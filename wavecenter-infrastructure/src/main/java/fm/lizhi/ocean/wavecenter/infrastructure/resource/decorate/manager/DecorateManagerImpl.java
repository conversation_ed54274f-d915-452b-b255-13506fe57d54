package fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.manager;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.DecorateInfoBean;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.DecorateRemote;
import fm.lizhi.ocean.wavecenter.service.resource.decorate.manager.DecorateManager;

@Component
public class DecorateManagerImpl implements DecorateManager{

    @Autowired
    private DecorateRemote decorateRemote;

    @Override
    public PageBean<DecorateInfoBean> getDecorates(PlatformDecorateTypeEnum decorateType, Long decorateId, String decorateName, int pageNum, int pageSize) {
        return decorateRemote.getDecorateInfoList(decorateType, decorateId, decorateName, pageNum, pageSize);
    }

    @Override
    public DecorateInfoBean getDecorateInfo(PlatformDecorateTypeEnum decorateType, Long decorateId) {
        return decorateRemote.getDecorateInfo(decorateType, decorateId);
    }

    @Override
    public List<DecorateInfoBean> batchGetDecorates(PlatformDecorateTypeEnum decorateType, List<Long> decorateIds) {
        return decorateRemote.batchGetDecorateInfo(decorateType, decorateIds);
    }
}