package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.AutoConfigResourceEnum;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityLevelConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseActivityResource;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityResourceConfig;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityResourceSimpleInfoDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActivityResourceConfigConvert {
    ActivityResourceConfigConvert I = Mappers.getMapper(ActivityResourceConfigConvert.class);

    @Mappings(
            @Mapping(source = "levels", target = "relationLevels")
    )
    ResponseActivityResource buildResponseActivityResource(ActivityResourceConfig config, List<ActivityLevelConfigBean> levels);



    List<ResponseActivityResource> buildResponseActivityResources(PageList<ActivityResourceConfig> list);

    List<ActivityResourceSimpleInfoDTO> buildSimpleInfoListDTOs(List<ActivityResourceConfig> list);


    @Mappings({
            @Mapping(target = "supportUpload", source = "resourceCode", qualifiedByName = "mapResourceCode"),
            @Mapping(target = "resourceCode", source = "resourceCode")
    })
    ResponseActivityResource buildResponseActivityResource(ActivityResourceConfig config);

    List<ResponseActivityResource> buildResponseActivityResources(List<ActivityResourceConfig> resourceList);

    default Long dateToLong(Date date) {
        return date.getTime();
    }

    @Named("mapResourceCode")
    default boolean mapResourceCode(String resourceCode) {

        AutoConfigResourceEnum autoConfigResourceEnum = AutoConfigResourceEnum.getByResourceCode(resourceCode);
        if (autoConfigResourceEnum != null) {
            return autoConfigResourceEnum.isSupportUpload();
        }
        return false;
    }


}

