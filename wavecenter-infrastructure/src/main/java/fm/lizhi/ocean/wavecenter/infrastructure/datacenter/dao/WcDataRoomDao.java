package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.dao;

import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataRoomFamilyDayExample;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataRoomFamilyMonthExample;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataRoomFamilyWeekExample;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.mapper.WcDataRoomFamilyDayMapper;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.mapper.WcDataRoomFamilyMonthMapper;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.mapper.WcDataRoomFamilyWeekMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
public class WcDataRoomDao {

    @Resource
    private WcDataRoomFamilyDayMapper wcDataRoomDayMapper;

    @Resource
    private WcDataRoomFamilyWeekMapper wcDataRoomFamilyWeekMapper;

    @Resource
    private WcDataRoomFamilyMonthMapper wcDataRoomFamilyMonthMapper;


    /**
     * 计算总条数
     *
     * @param appId    业务ID
     * @param familyId 公会ID
     * @return 总条数
     */
    public long countRoomDay(int appId, Long familyId, Long roomId) {
        WcDataRoomFamilyDayExample example = new WcDataRoomFamilyDayExample();
        example.createCriteria().andAppIdEqualTo(appId).andFamilyIdEqualTo(familyId).andRoomIdEqualTo(roomId);
        return wcDataRoomDayMapper.countByExample(example);
    }

    /**
     * 计算周统计总条数
     *
     * @param appId    业务ID
     * @param familyId 公会ID
     * @return 总条数
     */
    public long countRoomWeek(int appId, Long familyId, Long roomId) {
        WcDataRoomFamilyWeekExample example = new WcDataRoomFamilyWeekExample();
        example.createCriteria().andAppIdEqualTo(appId).andFamilyIdEqualTo(familyId).andRoomIdEqualTo(roomId);
        return wcDataRoomFamilyWeekMapper.countByExample(example);
    }

    /**
     * 计算月统计总条数
     *
     * @param appId    业务ID
     * @param familyId 公会ID
     * @return 总条数
     */
    public long countRoomMonth(int appId, Long familyId, Long roomId) {
        WcDataRoomFamilyMonthExample example = new WcDataRoomFamilyMonthExample();
        example.createCriteria().andAppIdEqualTo(appId).andFamilyIdEqualTo(familyId).andRoomIdEqualTo(roomId);
        return wcDataRoomFamilyMonthMapper.countByExample(example);
    }
}
