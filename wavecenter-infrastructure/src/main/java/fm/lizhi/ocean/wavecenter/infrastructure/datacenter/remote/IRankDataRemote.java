package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote;

import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RankBean;
import fm.lizhi.ocean.wavecenter.base.remote.IRemote;

import java.util.Date;
import java.util.List;

/**
 * 榜单数据
 * 查询差异化+第三方数据调用
 *
 * <AUTHOR>
 * @date 2024/5/23 10:23
 */
public interface IRankDataRemote extends IRemote {

    int BATCH_QUERY_PLAYER_PAGE_SIZE = 200;

    /**
     * 公会龙虎榜
     *
     * @param familyId 公会ID
     * @param date     日期
     * @param rankType 排序类型
     * @return 榜单数据
     */
    List<RankBean> guildPlayer(long familyId, List<Long> roomIds, Date date, OrderType rankType);

    /**
     * 签约厅龙虎榜
     *
     * @param njId     厅主ID
     * @param familyId 公会ID
     * @param date     日期
     * @param rankType 排序类型
     * @return 榜单数据
     */
    List<RankBean> roomPlayer(long njId, long familyId, Date date, OrderType rankType);

}
