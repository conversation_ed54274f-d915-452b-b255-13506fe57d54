package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.convert;

import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowPlayerMetricValue;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.PlayerDataForGrowDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2025/6/10 16:19
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface GrowPlayerMetricConvert {

    GrowPlayerMetricConvert I = Mappers.getMapper(GrowPlayerMetricConvert.class);

    @Mapping(target = "startWeekDate", ignore = true)
    @Mapping(target = "roomId", ignore = true)
    @Mapping(target = "modifyTime", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "familyId", ignore = true)
    @Mapping(target = "endWeekDate", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "appId", ignore = true)
    WcGrowPlayerMetricValue playerDataForGrowDTO2WcGrowPlayerMetricValue(PlayerDataForGrowDTO dto);

}
