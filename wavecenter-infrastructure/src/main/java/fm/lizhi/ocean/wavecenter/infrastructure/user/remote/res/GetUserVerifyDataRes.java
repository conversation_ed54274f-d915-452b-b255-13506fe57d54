package fm.lizhi.ocean.wavecenter.infrastructure.user.remote.res;

import lombok.Data;

/**
 * 获取用户认证信息结果
 * <AUTHOR>
 */
@Data
public class GetUserVerifyDataRes {
    /**
     * 认证id
     */
    private Long id;
    /**
     * 认证记录id
     */
    private Long recordId;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 应用id
     */
    private Long appId;
    /**
     * 子应用id
     */
    private Long subAppId;
    /**
     * 业务id
     */
    private Long bizId;
    /**
     * 证件类型
     */
    private Integer idCardType;
    /**
     * 姓名
     */
    private String name;
    /**
     * 证件号码
     */
    private String idCardNumber;
    /**
     * 认证类型
     */
    private Integer verifyType;
    /**
     * 认证状态
     */
    private Integer verifyStatus;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 修改时间
     */
    private Long modifyTime;
    /**
     * 身份证正面
     */
    private String idCardFront;
    /**
     * 身份证反面
     */
    private String idCardBack;
    /**
     * 手持身份证
     */
    private String idCardPerson;
    /**
     * 业务名称
     */
    private String bizName;
    /**
     * 手机号码
     */
    private String phoneNum;
}
