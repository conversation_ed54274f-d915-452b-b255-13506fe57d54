package fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.common.datastore.mysql.constant.ParamContants;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilyAwardLevelData;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.param.ListFamilyAwardLevelDataParam;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.param.ListFamilyAwardLevelFamilyDataParam;
import fm.lizhi.ocean.wavecenter.service.award.family.dto.FamilyAwardLevelDataDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcFamilyAwardLevelDataExtMapper {

    @Update("UPDATE `wavecenter_family_award_level_data`\n" +
            "SET `level_id` = #{param.levelId},\n" +
            "  `modify_time` = NOW()\n" +
            "WHERE `deploy_env` = #{deployEnv}\n" +
            "  AND `app_id` = #{param.appId}\n" +
            "  AND `family_id` = #{param.familyId}\n" +
            "  AND `start_time` = #{param.startTime}\n" +
            "  AND `level_id` != #{param.levelId}")
    int updateData(@Param("param") FamilyAwardLevelDataDTO param, @Param("deployEnv") String deployEnv);

    @Select("SELECT * FROM `wavecenter_family_award_level_data`\n" +
            "WHERE `deploy_env` = #{deployEnv}\n" +
            "  AND `app_id` = #{param.appId}\n" +
            "  AND `start_time` = #{param.awardStartTime}\n" +
            "  AND `create_time` <= #{param.maxCreateTime}\n" +
            "ORDER BY `create_time` ASC, `id` ASC")
    PageList<WcFamilyAwardLevelData> listData(
            @Param("param") ListFamilyAwardLevelDataParam param, @Param("deployEnv") String deployEnv,
            @Param(ParamContants.PAGE_NUMBER) int pageNumber, @Param(ParamContants.PAGE_SIZE) int pageSize);

    @Select("SELECT * FROM `wavecenter_family_award_level_data`\n" +
            "WHERE `deploy_env` = #{deployEnv}\n" +
            "  AND `app_id` = #{param.appId}\n" +
            "  AND `family_id` = #{param.familyId}\n" +
            "  AND `start_time` = #{param.awardStartTime}\n" +
            "  AND `create_time` <= #{param.maxCreateTime}\n" +
            "ORDER BY `create_time` ASC, `id` ASC")
    PageList<WcFamilyAwardLevelData> listFamilyData(
            @Param("param") ListFamilyAwardLevelFamilyDataParam param, @Param("deployEnv") String deployEnv,
            @Param(ParamContants.PAGE_NUMBER) int pageNumber, @Param(ParamContants.PAGE_SIZE) int pageSize);
}
