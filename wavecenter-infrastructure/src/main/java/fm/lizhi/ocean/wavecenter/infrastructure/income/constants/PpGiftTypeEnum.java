package fm.lizhi.ocean.wavecenter.infrastructure.income.constants;

import java.util.HashMap;
import java.util.Map;

/**
 * 礼物类型
 * Created in 17-10-18 下午6:09.
 *
 * <AUTHOR>
 */
public enum PpGiftTypeEnum {

    /**
     * 旧版荔枝
     */
    OLD_LITCHI(1, "旧版荔枝"),

    /**
     * 新版礼物
     */
    NEW_GIFT(2, "新版礼物"),

    /**
     * 娱乐模式礼物
     */
    RECREATION_GIFT(3, "娱乐模式礼物"),

    /**
     * 宝箱礼物
     */
    BOX_GIFT(4, "宝箱礼物");
    private int value;
    private String msg;

    private static Map<Integer, PpGiftTypeEnum> map = new HashMap<>();

    static {
        for (PpGiftTypeEnum object : PpGiftTypeEnum.values()) {
            map.put(object.getValue(), object);
        }
    }

    PpGiftTypeEnum(int value, String msg) {
        this.value = value;
        this.msg = msg;
    }

    public int getValue() {
        return value;
    }

    public String getMsg() {
        return msg;
    }

    /**
     * 根据值类型找枚举
     *
     * @param value 值
     * @return
     */
    public static PpGiftTypeEnum from(int value) {
        return map.get(value);
    }
}