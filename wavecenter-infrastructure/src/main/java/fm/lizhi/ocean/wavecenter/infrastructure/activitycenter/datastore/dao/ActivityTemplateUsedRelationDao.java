package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.dao;

import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityTemplateUsedRelation;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityTemplateUsedRelationExample;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.mapper.ActivityTemplateUsedRelationMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class ActivityTemplateUsedRelationDao {


    @Autowired
    private ActivityTemplateUsedRelationMapper relationMapper;

    /**
     * 保存模板使用关系
     *
     * @param templateUsedRelation 模板使用关系
     * @return 结果
     */
    public boolean saveTemplateUsedRelation(ActivityTemplateUsedRelation templateUsedRelation) {
        return relationMapper.insert(templateUsedRelation) > 0;
    }

    /**
     * 根据活动ID获取模板使用关系
     *
     * @param activityId 活动ID
     * @return 结果
     */
    public ActivityTemplateUsedRelation getTemplateUsedRelationByActivityId(long activityId) {
        ActivityTemplateUsedRelation getByActivityId = new ActivityTemplateUsedRelation();
        getByActivityId.setActivityId(activityId);
        return relationMapper.selectOne(getByActivityId);
    }

    /**
     * 根据活动ID删除模板使用关系
     *
     * @param activityId 活动ID
     * @return 结果
     */
    public long deleteTemplateUsedRelationByActivityId(long activityId) {
        ActivityTemplateUsedRelationExample example = new ActivityTemplateUsedRelationExample();
        example.createCriteria().andActivityIdEqualTo(activityId);
        return relationMapper.deleteByExample(example);
    }

    public List<ActivityTemplateUsedRelation> getTemplateUsedRelationByTemplateId(long templateId){
        ActivityTemplateUsedRelationExample example = new ActivityTemplateUsedRelationExample();
        example.createCriteria().andTemplateIdEqualTo(templateId);
        return relationMapper.selectByExample(example);
    }
}
