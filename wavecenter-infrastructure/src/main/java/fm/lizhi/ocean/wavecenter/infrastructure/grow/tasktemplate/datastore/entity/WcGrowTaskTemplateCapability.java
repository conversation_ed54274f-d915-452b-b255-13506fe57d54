package fm.lizhi.ocean.wavecenter.infrastructure.grow.tasktemplate.datastore.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 成长体系任务模板能力分奖励表
 *
 * @date 2025-06-06 02:36:15
 */
@Table(name = "`wavecenter_grow_task_template_capability`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcGrowTaskTemplateCapability {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 任务模板ID
     */
    @Column(name= "`template_id`")
    private Long templateId;

    /**
     * 能力项code
     */
    @Column(name= "`capability_code`")
    private String capabilityCode;

    /**
     * 能力分
     */
    @Column(name= "`ability_value`")
    private BigDecimal abilityValue;

    /**
     * 业务 ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 服务部署环境, TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", templateId=").append(templateId);
        sb.append(", capabilityCode=").append(capabilityCode);
        sb.append(", abilityValue=").append(abilityValue);
        sb.append(", appId=").append(appId);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}