package fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager.deliver.other;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardResourceDeliverTypeEnum;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardResourceTypeEnum;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardTypeEnum;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.BatchSendUserResultBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.request.RequestBatchSend;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert.FamilyAwardRecommendCardConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager.deliver.DeliverResourceParam;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager.deliver.FamilyAwardDeliver;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager.deliver.GetResourceInfoResult;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.recommendcard.remote.RecommendCardRemote;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 公会其他奖励推荐卡发放实现
 */
@Component
@Slf4j
public class FamilyOtherAwardRecommendCardDeliver implements FamilyAwardDeliver {

    @Autowired
    private RecommendCardRemote recommendCardRemote;

    @Override
    public boolean supports(FamilyAwardTypeEnum awardType, FamilyAwardResourceDeliverTypeEnum deliverType) {
        return awardType == FamilyAwardTypeEnum.OTHER && deliverType == FamilyAwardResourceDeliverTypeEnum.RECOMMEND_CARD;
    }

    @Override
    public Result<GetResourceInfoResult> getResourceInfo(DeliverResourceParam param) {
        // 推荐卡没有信息
        return RpcResult.success(GetResourceInfoResult.emptyInfo());
    }

    @Override
    public Result<Void> deliverResource(DeliverResourceParam param) {
        try {
            String reason = param.getResourceTypes().contains(FamilyAwardResourceTypeEnum.PP_SPECIAL_RECOMMEND_CARD) ? "特殊奖励" : "公会其他奖励";
            RequestBatchSend request = FamilyAwardRecommendCardConvert.I.toRequestBatchSend(param, reason);
            log.info("Deliver recommend card, request={}", request);
            List<BatchSendUserResultBean> failureBeans = recommendCardRemote.batchSend(request);
            if (CollectionUtils.isNotEmpty(failureBeans)) {
                // 此处必定是发给一个人的，所以只取第一个
                BatchSendUserResultBean failureBean = failureBeans.get(0);
                Integer errorCode = ObjectUtils.defaultIfNull(failureBean.getResultCode(), CommonService.INTERNAL_ERROR);
                String errorText = StringUtils.defaultString(failureBean.getMsg());
                log.info("Deliver recommend card fail, param={}, errorCode={}, errorText={}", param, errorCode, errorText);
                return RpcResult.fail(errorCode, errorText);
            }
            log.info("Deliver recommend card success, param={}", param);
            return RpcResult.success();
        } catch (RuntimeException e) {
            log.error("Deliver recommend card error, param={}", param, e);
            return RpcResult.fail(CommonService.INTERNAL_ERROR, e.getMessage());
        }
    }
}
