package fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.constants;

import java.util.Arrays;

import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import fm.lizhi.xm.vip.bean.decorate.DecorateTypeEnum;

public enum XmDecorateTypeEnum {

    /**
     * 座驾
     */
    VEHICLE(DecorateTypeEnum.MOUNT.getType(), PlatformDecorateTypeEnum.VEHICLE),

    /**
     * 勋章
     */
    MEDAL(DecorateTypeEnum.MEDAL.getType(), PlatformDecorateTypeEnum.MEDAL),
    
    /**
     * 头像框
     */
    AVATAR(DecorateTypeEnum.AVATAR_WIDGET.getType(), PlatformDecorateTypeEnum.AVATAR),
    
    /**
     * 背景
     */
    BACKGROUND(DecorateTypeEnum.BACKGROUD.getType(), PlatformDecorateTypeEnum.BACKGROUND);


    private final int type;

    private final PlatformDecorateTypeEnum decorateTypeEnum;

    XmDecorateTypeEnum(int type, PlatformDecorateTypeEnum decorateTypeEnum) {
        this.type = type;
        this.decorateTypeEnum = decorateTypeEnum;
    }

    public int getType() {
        return type;
    }

    public PlatformDecorateTypeEnum getDecorateTypeEnum() {
        return decorateTypeEnum;
    }

    public static XmDecorateTypeEnum getByType(PlatformDecorateTypeEnum decorateTypeEnum) {
        return Arrays.stream(XmDecorateTypeEnum.values())
                .filter(xmDecorateEnum -> xmDecorateEnum.getDecorateTypeEnum().equals(decorateTypeEnum))
                .findFirst()
                .orElse(null);
    }
}
