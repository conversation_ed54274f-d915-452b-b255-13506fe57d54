package fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.dao;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.*;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert.FamilyAwardDeliverExecutionConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert.FamilyAwardDeliverItemConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert.FamilyAwardDeliverRecordConvert;
import fm.lizhi.ocean.wavecenter.datastore.award.family.mapper.WcFamilyAwardDeliverExecutionMapper;
import fm.lizhi.ocean.wavecenter.datastore.award.family.mapper.WcFamilyAwardDeliverItemMapper;
import fm.lizhi.ocean.wavecenter.datastore.award.family.mapper.WcFamilyAwardDeliverRecordMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.mapper.ext.WcFamilyAwardDeliverExecutionExtMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.mapper.ext.WcFamilyAwardDeliverItemExtMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.mapper.ext.WcFamilyAwardDeliverRecordExtMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.param.CreateFamilyAwardDeliverExecutionParam;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.param.CreateFamilyAwardDeliverItemParam;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.param.CreateFamilyAwardDeliverRecordParam;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.result.CreateDeliverRecordResult;
import fm.lizhi.ocean.wavecenter.service.award.family.dto.GetFamilyAwardDeliverRecordParamDTO;
import io.shardingsphere.core.routing.router.masterslave.MasterSlaveRouteOnceVisitedHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Slf4j
@Repository
public class FamilyAwardDeliverRecordDao {

    @Autowired
    private FamilyAwardDeliverItemConvert familyAwardDeliverItemConvert;

    @Autowired
    private WcFamilyAwardDeliverRecordExtMapper wcFamilyAwardDeliverRecordExtMapper;
    @Autowired
    private WcFamilyAwardDeliverRecordMapper wcFamilyAwardDeliverRecordMapper;

    @Autowired
    private WcFamilyAwardDeliverExecutionExtMapper wcFamilyAwardDeliverExecutionExtMapper;
    @Autowired
    private WcFamilyAwardDeliverExecutionMapper wcFamilyAwardDeliverExecutionMapper;

    @Autowired
    private WcFamilyAwardDeliverItemExtMapper wcFamilyAwardDeliverItemExtMapper;
    @Autowired
    private WcFamilyAwardDeliverItemMapper wcFamilyAwardDeliverItemMapper;

    /**
     * 根据参数分页查询发放记录
     * @param param
     * @param pageNum
     * @param pageSize
     * @return
     */
    public PageList<WcFamilyAwardDeliverRecord> getDeliverRecordPage(GetFamilyAwardDeliverRecordParamDTO param, int pageNum, int pageSize){
        String deployEnv = ConfigUtils.getEnvRequired().name();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();

        WcFamilyAwardDeliverRecordExample example = new WcFamilyAwardDeliverRecordExample();
        example.setOrderByClause("create_time desc");
        WcFamilyAwardDeliverRecordExample.Criteria criteria = example.createCriteria();
        criteria.andDeployEnvEqualTo(deployEnv).andAppIdEqualTo(appId);

        if (param.getFamilyUserId() != null) {
            criteria.andFamilyUserIdEqualTo(param.getFamilyUserId());
        }
        if (param.getFamilyId() != null) {
            criteria.andFamilyIdEqualTo(param.getFamilyId());
        }
        if (StringUtils.isNotBlank(param.getFamilyName())) {
            criteria.andFamilyNameLike("%" + param.getFamilyName() + "%");
        }
        if (param.getMinDeliverTime() != null) {
            criteria.andDeliverTimeGreaterThanOrEqualTo(new Date(param.getMinDeliverTime()));
        }
        if (param.getMaxDeliverTime() != null) {
            criteria.andDeliverTimeLessThanOrEqualTo(new Date(param.getMaxDeliverTime()));
        }

        return wcFamilyAwardDeliverRecordMapper.pageByExample(example, pageNum, pageSize);
    }

    /**
     * 查询发放记录对应的发放明细
     * @param recordIds
     * @return
     */
    public List<WcFamilyAwardDeliverItem> getDeliverItemByRecordIds(List<Long> recordIds){
        if (CollectionUtils.isEmpty(recordIds)) {
            return Collections.emptyList();
        }

        String deployEnv = ConfigUtils.getEnvRequired().name();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();

        WcFamilyAwardDeliverItemExample example = new WcFamilyAwardDeliverItemExample();
        example.createCriteria()
                .andDeployEnvEqualTo(deployEnv)
                .andAppIdEqualTo(appId)
                .andRecordIdIn(recordIds);

        return wcFamilyAwardDeliverItemMapper.selectByExample(example);
    }

    /**
     * 获取发放记录
     *
     * @param recordId 发放记录id
     * @return 发放记录
     */
    public WcFamilyAwardDeliverRecord getDeliverRecord(long recordId) {
        WcFamilyAwardDeliverRecord getById = new WcFamilyAwardDeliverRecord();
        getById.setId(recordId);
        return wcFamilyAwardDeliverRecordMapper.selectByPrimaryKey(getById);
    }

    /**
     * 获取发放记录
     *
     * @param appId          应用id
     * @param familyId       公会id
     * @param awardStartTime 奖励周期开始时间
     * @return 发放记录
     */
    public WcFamilyAwardDeliverRecord getDeliverRecord(int appId, long familyId, Date awardStartTime) {
        String deployEnv = ConfigUtils.getEnvRequired().name();
        WcFamilyAwardDeliverRecord selectOne = new WcFamilyAwardDeliverRecord();
        selectOne.setAppId(appId);
        selectOne.setFamilyId(familyId);
        selectOne.setAwardStartTime(awardStartTime);
        selectOne.setDeployEnv(deployEnv);
        return wcFamilyAwardDeliverRecordMapper.selectOne(selectOne);
    }

    /**
     * 创建发放记录. 返回的结果中包含创建好的发放记录id和执行id列表, 其中执行id列表与入参列的执行参数列表按顺序对应
     *
     * @param param 创建发放记录参数
     * @return 创建发放记录结果
     */
    @Transactional
    public CreateDeliverRecordResult createDeliverRecord(CreateFamilyAwardDeliverRecordParam param) {
        log.info("createDeliverRecord param={}", param);
        WcFamilyAwardDeliverRecord createRecord = FamilyAwardDeliverRecordConvert.I.toCreateEntity(param);
        int createRecordRows = wcFamilyAwardDeliverRecordMapper.insert(createRecord);
        Long recordId = createRecord.getId();
        log.info("createDeliverRecord createRecordRows={}, recordId={}", createRecordRows, recordId);
        int appId = param.getAppId();
        List<CreateFamilyAwardDeliverExecutionParam> deliverExecutions = param.getDeliverExecutions();
        List<WcFamilyAwardDeliverExecution> createExecutions = FamilyAwardDeliverExecutionConvert.I
                .toCreateEntities(deliverExecutions, appId, recordId);
        int createExecutionRows = wcFamilyAwardDeliverExecutionMapper.batchInsert(createExecutions);
        log.info("createDeliverRecord createExecutionRows={}", createExecutionRows);
        ArrayList<Long> executionIds = new ArrayList<>();
        ArrayList<WcFamilyAwardDeliverItem> createItems = new ArrayList<>();
        for (int i = 0; i < deliverExecutions.size(); i++) {
            Long executionId = createExecutions.get(i).getId();
            executionIds.add(executionId);
            for (CreateFamilyAwardDeliverItemParam deliverItem : deliverExecutions.get(i).getDeliverItems()) {
                WcFamilyAwardDeliverItem createItem = familyAwardDeliverItemConvert
                        .toCreateEntity(deliverItem, appId, recordId, executionId);
                createItems.add(createItem);
            }
        }
        int createItemRows = wcFamilyAwardDeliverItemMapper.batchInsert(createItems);
        log.info("createDeliverRecord createItemRows={}", createItemRows);
        return new CreateDeliverRecordResult(recordId, executionIds);
    }

    /**
     * 更新发放记录为成功状态
     *
     * @param recordId 记录id
     */
    public void updateDeliverRecordToSuccess(long recordId) {
        int updateRows = wcFamilyAwardDeliverRecordExtMapper.updateToSuccess(recordId);
        log.info("updateDeliverRecordToSuccess recordId={}, updateRows={}", recordId, updateRows);
    }

    /**
     * 更新重新发放记录为成功状态
     *
     * @param recordId 记录id
     */
    public void updateReDeliverRecordToSuccess(long recordId) {
        int updateRows = wcFamilyAwardDeliverRecordExtMapper.updateReDeliverToSuccess(recordId);
        log.info("updateReDeliverRecordToSuccess recordId={}, updateRows={}", recordId, updateRows);
    }

    /**
     * 更新发放记录为失败状态
     *
     * @param recordId 记录id
     */
    public void updateDeliverRecordToFailure(long recordId) {
        int updateRows = wcFamilyAwardDeliverRecordExtMapper.updateToFailure(recordId);
        log.info("updateDeliverRecordToFailure recordId={}, updateRows={}", recordId, updateRows);
    }

    /**
     * 更新重新发放记录为失败状态
     *
     * @param recordId 记录id
     */
    public void updateReDeliverRecordToFailure(long recordId) {
        int updateRows = wcFamilyAwardDeliverRecordExtMapper.updateReDeliverToFailure(recordId);
        log.info("updateReDeliverRecordToFailure recordId={}, updateRows={}", recordId, updateRows);
    }

    /**
     * 更新发放记录为部分失败状态
     *
     * @param recordId 记录id
     */
    public void updateDeliverRecordToPartialFailure(long recordId) {
        int updateRows = wcFamilyAwardDeliverRecordExtMapper.updateToPartialFailure(recordId);
        log.info("updateDeliverRecordToPartialFailure recordId={}, updateRows={}", recordId, updateRows);
    }

    /**
     * 更新重新发放记录为部分失败状态
     *
     * @param recordId 记录id
     */
    public void updateReDeliverRecordToPartialFailure(long recordId) {
        int updateRows = wcFamilyAwardDeliverRecordExtMapper.updateReDeliverToPartialFailure(recordId);
        log.info("updateReDeliverRecordToPartialFailure recordId={}, updateRows={}", recordId, updateRows);
    }

    /**
     * 更新重新发放记录为发放中状态
     *
     * @param recordId 记录id
     */
    public void updateReDeliverRecordToDelivering(long recordId) {
        int updateRows = wcFamilyAwardDeliverRecordExtMapper.updateReDeliverToDelivering(recordId);
        log.info("updateReDeliverRecordToDelivering recordId={}, updateRows={}", recordId, updateRows);
    }

    /**
     * 更新发放执行为成功状态
     *
     * @param executionId 执行id
     */
    @Transactional
    public void updateDeliverExecutionToSuccess(long executionId) {
        int updateExecutionRows = wcFamilyAwardDeliverExecutionExtMapper.updateToSuccess(executionId);
        int updateItemRows = wcFamilyAwardDeliverItemExtMapper.updateToSuccessByExecutionId(executionId);
        log.info("updateDeliverExecutionToSuccess executionId={}, updateExecutionRows={}, updateItemRows={}",
                executionId, updateExecutionRows, updateItemRows);
    }

    /**
     * 更新重新发放执行为成功状态
     *
     * @param executionId 执行id
     */
    @Transactional
    public void updateReDeliverExecutionToSuccess(long executionId) {
        int updateExecutionRows = wcFamilyAwardDeliverExecutionExtMapper.updateReDeliverToSuccess(executionId);
        int updateItemRows = wcFamilyAwardDeliverItemExtMapper.updateReDeliverToSuccessByExecutionId(executionId);
        log.info("updateReDeliverExecutionToSuccess executionId={}, updateExecutionRows={}, updateItemRows={}",
                executionId, updateExecutionRows, updateItemRows);
    }

    /**
     * 更新发放执行为失败状态
     *
     * @param executionId 执行id
     * @param errorCode   错误码
     * @param errorText   错误信息
     */
    @Transactional
    public void updateDeliverExecutionToFailure(long executionId, int errorCode, String errorText) {
        String truncatedErrorText = truncateErrorText(errorText);
        int updateExecutionRows = wcFamilyAwardDeliverExecutionExtMapper.updateToFailure(executionId, errorCode, truncatedErrorText);
        int updateItemRows = wcFamilyAwardDeliverItemExtMapper.updateToFailureByExecutionId(executionId, errorCode, truncatedErrorText);
        log.info("updateDeliverExecutionToFailure executionId={}, errorCode={}, errorText={}, updateExecutionRows={}, updateItemRows={}",
                executionId, errorCode, truncatedErrorText, updateExecutionRows, updateItemRows);
    }

    /**
     * 更新重新发放执行为失败状态
     *
     * @param executionId 执行id
     * @param errorCode   错误码
     * @param errorText   错误信息
     */
    @Transactional
    public void updateReDeliverExecutionToFailure(long executionId, int errorCode, String errorText) {
        String truncatedErrorText = truncateErrorText(errorText);
        int updateExecutionRows = wcFamilyAwardDeliverExecutionExtMapper.updateReDeliverToFailure(executionId, errorCode, truncatedErrorText);
        int updateItemRows = wcFamilyAwardDeliverItemExtMapper.updateReDeliverToFailureByExecutionId(executionId, errorCode, truncatedErrorText);
        log.info("updateReDeliverExecutionToFailure executionId={}, errorCode={}, errorText={}, updateExecutionRows={}, updateItemRows={}",
                executionId, errorCode, truncatedErrorText, updateExecutionRows, updateItemRows);
    }

    private String truncateErrorText(String errorText) {
        if (StringUtils.isBlank(errorText)) {
            return StringUtils.EMPTY;
        }
        return StringUtils.substring(errorText, 0, 65535);
    }

    /**
     * 根据发放记录id获取发放条目map. key为资源类型, value为发放条目
     *
     * @param recordId 发放记录id
     * @return 发放条目map
     */
    public Map<Integer, WcFamilyAwardDeliverItem> getDeliverItemsByRecordIdAsResourceTypeMap(long recordId) {
        WcFamilyAwardDeliverItem selectMany = new WcFamilyAwardDeliverItem();
        selectMany.setRecordId(recordId);
        List<WcFamilyAwardDeliverItem> deliverItems = wcFamilyAwardDeliverItemMapper.selectMany(selectMany);
        HashMap<Integer, WcFamilyAwardDeliverItem> resourceTypeToItemMap = new HashMap<>();
        for (WcFamilyAwardDeliverItem deliverItem : deliverItems) {
            resourceTypeToItemMap.put(deliverItem.getResourceType(), deliverItem);
        }
        return resourceTypeToItemMap;
    }

    /**
     * 获取发放执行记录
     *
     * @param executionId 执行id
     * @return 发放执行记录
     */
    public WcFamilyAwardDeliverExecution getDeliverExecution(long executionId) {
        WcFamilyAwardDeliverExecution getById = new WcFamilyAwardDeliverExecution();
        getById.setId(executionId);
        return wcFamilyAwardDeliverExecutionMapper.selectByPrimaryKey(getById);
    }

    /**
     * 根据执行id获取发放条目列表
     *
     * @param executionId 执行id
     * @return 发放条目列表
     */
    public List<WcFamilyAwardDeliverItem> getDeliverItemsByExecutionId(long executionId) {
        WcFamilyAwardDeliverItem selectMany = new WcFamilyAwardDeliverItem();
        selectMany.setExecutionId(executionId);
        return wcFamilyAwardDeliverItemMapper.selectMany(selectMany);
    }

    /**
     * 根据发放记录id获取发放执行记录列表, 从主库查询. 非必要情况不要使用
     *
     * @param recordId 发放记录id
     * @return 发放执行记录列表
     */
    public List<WcFamilyAwardDeliverExecution> getDeliverExecutionsByRecordIdFromMaster(long recordId) {
        MasterSlaveRouteOnceVisitedHolder.routeMaster();
        WcFamilyAwardDeliverExecution selectMany = new WcFamilyAwardDeliverExecution();
        selectMany.setRecordId(recordId);
        return wcFamilyAwardDeliverExecutionMapper.selectMany(selectMany);
    }
}
