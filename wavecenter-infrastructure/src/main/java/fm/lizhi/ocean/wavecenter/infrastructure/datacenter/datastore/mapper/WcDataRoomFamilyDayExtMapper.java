package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataRoomFamilyDay;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.WcDataRoomDayStatPo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcDataRoomFamilyDayExtMapper {

    @Select({
            "<script>"
            , "select "
            , "<foreach collection='metrics' item='m' separator=','>"
            , "sum(${m}) as ${m}"
            , "</foreach>"
            , "from wavecenter_data_room_family_day where app_id=#{appId}"
            , "and family_id=#{familyId}"
            , "and room_id in "
            , "<foreach collection='roomIds' item='roomId' open='(' close=')' separator=','>"
            , "#{roomId}"
            , "</foreach>"
            , "and stat_date_value=#{dayValue}"
            , "group by stat_date_value"
            , "</script>"
    })
    List<WcDataRoomFamilyDay> sum(@Param("metrics")List<String> metrics
            , @Param("appId") Integer appId
            , @Param("familyId") Long familyId
            , @Param("roomIds") List<Long> roomIds
            , @Param("dayValue") Integer dayValue
    );

    @Select({
            "<script>"
            , "select "
            , "stat_date_value,"
            , "<foreach collection='metrics' item='m' separator=','>"
            , "sum(${m}) as ${m}"
            , "</foreach>"
            , "from wavecenter_data_room_family_day where app_id=#{appId}"
            , "and family_id=#{familyId}"
            , "and room_id in "
            , "<foreach collection='roomIds' item='roomId' open='(' close=')' separator=','>"
            , "#{roomId}"
            , "</foreach>"
            , "and stat_date_value in "
            , "<foreach collection='dayValues' item='dayValue' open='(' close=')' separator=','>"
            , "#{dayValue}"
            , "</foreach>"
            , "group by stat_date_value"
            , "</script>"
    })
    List<WcDataRoomFamilyDay> sumDays(@Param("metrics")List<String> metrics
            , @Param("appId") Integer appId
            , @Param("familyId") Long familyId
            , @Param("roomIds") List<Long> roomIds
            , @Param("dayValues") List<Integer> dayValues
    );


    /**
     * 查询厅日统计数据
     *
     * @param njId     厅ID
     * @param appId    业务ID
     * @param familyId 公会ID
     * @param lastTime 上次查询的最小时间
     * @param pageSize 页面大小
     * @return 厅日统计数据列表
     */
    @Select("SELECT " +
            "    stat_date, " +
            "    stat_date_value, " +
            "    room_id, " +
            "    personal_noble_income AS playerVip, " +
            "    official_hall_income AS officialRoom, " +
            "    personal_hall_income AS player, " +
            "    all_income AS sum, " +
            "    sign_hall_income AS roomGift, " +
            "    noble_income AS roomVip " +
            "FROM wavecenter_data_room_family_day " +
            "WHERE room_id = #{njId} " +
            "    AND family_id = #{familyId} " +
            "    AND app_id = #{appId} " +
            "    AND stat_date_value < #{lastTime} " +
            " order by stat_date_value desc limit #{pageSize}")
    List<WcDataRoomDayStatPo> queryDayStatsByTime(@Param("njId") Long njId,
                                                  @Param("appId") Integer appId,
                                                  @Param("familyId") Long familyId,
                                                  @Param("lastTime") Integer lastTime,
                                                  @Param("pageSize") Integer pageSize);

}
