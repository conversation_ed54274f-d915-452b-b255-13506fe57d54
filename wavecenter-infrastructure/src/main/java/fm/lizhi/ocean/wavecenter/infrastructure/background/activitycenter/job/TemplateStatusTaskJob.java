package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.job;

import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.handler.TemplateStatusTaskHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 模板定期上下架任务
 * <AUTHOR>
 * @date 2025/5/8 17:41
 */
@Slf4j
@Component
public class TemplateStatusTaskJob implements JobHandler {

    @Autowired
    private TemplateStatusTaskHandler handler;

    @Override
    public void execute(JobExecuteContext jobExecuteContext) throws Exception {
        int defaultTaskNums = 50;
        if (StringUtils.isNotBlank(jobExecuteContext.getParam())) {
            defaultTaskNums = Integer.parseInt(jobExecuteContext.getParam());
        }
        handler.executeTask(defaultTaskNums);
    }

}
