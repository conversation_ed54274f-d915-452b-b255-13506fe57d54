package fm.lizhi.ocean.wavecenter.infrastructure.live.convert;

import fm.lizhi.ocean.wavecenter.api.live.bean.WaveCheckInUserBean;
import fm.lizhi.ocean.wavecenter.api.live.response.ResponseGetCheckInPlayerSum;
import fm.lizhi.ocean.wavecenter.api.live.response.ResponseGetCheckInRoomStatisticReport;
import fm.lizhi.ocean.wavecenter.api.live.response.ResponseGetCheckInRoomSum;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataPlayerDayRealTime;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataPlayerHourRealTime;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataPlayerWeek;
import fm.lizhi.ocean.wavecenter.infrastructure.live.dto.GetCheckInRoomStatisticReportDTO;
import fm.lizhi.ocean.wavecenter.infrastructure.live.dto.WaveCheckInIncomeSumDTO;
import fm.lizhi.ocean.wavecenter.infrastructure.live.dto.WaveCheckInPlayerSumDTO;
import fm.lizhi.ocean.wavecenter.infrastructure.live.manager.checkinstat.ChatStat;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

/**
 * 麦序福利数据转换器
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface WaveCheckInDataConverter {

    ResponseGetCheckInRoomSum toResponseGetCheckInRoomSum(WaveCheckInIncomeSumDTO incomeSumDTO, int scheduledPlayerCnt);

    @Mapping(target = "photo", source = "simpleUser.avatar")
    WaveCheckInUserBean toWaveCheckInUserBean(SimpleUserDto simpleUser);

    ResponseGetCheckInPlayerSum toResponseGetCheckInPlayerSum(WaveCheckInPlayerSumDTO playerSumDTO);

    ChatStat toHourChatStat(WcDataPlayerHourRealTime t);

    ChatStat toDayChatStat(WcDataPlayerDayRealTime t);

    @Mapping(target = "statDate", ignore = true)
    ChatStat toWeekChatStat(WcDataPlayerWeek t);


    ResponseGetCheckInRoomStatisticReport toResponseGetCheckInRoomStatisticReport(GetCheckInRoomStatisticReportDTO data);
}
