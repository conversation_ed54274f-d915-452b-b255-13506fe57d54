package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateFlowResourceImageBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateFlowResourceImageExtraBean;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityTemplateFlowResourceImage;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityTemplateFlowResourceImageDTO;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityTemplateFlowResourceImageExtraDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 活动模板流量资源图片转换器
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.ERROR,
        imports = {UrlUtils.class, JsonUtils.class, ActivityTemplateFlowResourceImageExtraBean.class,
                ActivityTemplateFlowResourceImageExtraDTO.class,
        })
public abstract class ActivityTemplateFlowResourceImageConverter {

    @Autowired
    protected CommonConfig commonConfig;

    /**
     * 转换为创建活动模板流量资源图片的实体列表
     *
     * @param beans          请求对象列表
     * @param flowResourceId 活动模板流量资源ID
     * @return 创建活动模板流量资源图片的实体列表
     */
    public List<ActivityTemplateFlowResourceImage> toCreateActivityTemplateFlowResourceImages(List<ActivityTemplateFlowResourceImageBean> beans, long flowResourceId) {
        if (CollectionUtils.isEmpty(beans)) {
            return Collections.emptyList();
        }
        List<ActivityTemplateFlowResourceImage> activityTemplateFlowResourceImages = new ArrayList<>(beans.size());
        for (int index = 0; index < beans.size(); index++) {
            activityTemplateFlowResourceImages.add(toCreateActivityTemplateFlowResourceImage(beans.get(index), flowResourceId, index));
        }
        return activityTemplateFlowResourceImages;
    }

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "imageUrl", expression = "java(UrlUtils.removeHostOrEmpty(bean.getImageUrl()))")
    @Mapping(target = "extra", expression = "java(JsonUtils.toJsonString(bean.getExtra()))")
    public abstract ActivityTemplateFlowResourceImage toCreateActivityTemplateFlowResourceImage(ActivityTemplateFlowResourceImageBean bean, long flowResourceId, int index);

    /**
     * 转换为API接口返回的活动模板流量资源图片的Bean
     *
     * @param entity 实体
     * @return API接口返回的活动模板流量资源图片的Bean
     */
    @Mapping(target = "imageUrl", expression = "java(UrlUtils.addHostOrEmpty(entity.getImageUrl(), commonConfig.getRomeFsDownloadCdn()))")
    @Mapping(target = "extra", expression = "java(JsonUtils.fromJsonString(entity.getExtra(), ActivityTemplateFlowResourceImageExtraBean.class))")
    public abstract ActivityTemplateFlowResourceImageBean toActivityTemplateFlowResourceImageBean(ActivityTemplateFlowResourceImage entity);

    /**
     * 转换为活动模板流量资源图片的DTO
     *
     * @param entity 实体
     * @return 活动模板流量资源图片的DTO
     */
    @Mapping(target = "imageUrl", expression = "java(UrlUtils.addHostOrEmpty(entity.getImageUrl(), commonConfig.getRomeFsDownloadCdn()))")
    @Mapping(target = "extra", expression = "java(JsonUtils.fromJsonString(entity.getExtra(), ActivityTemplateFlowResourceImageExtraDTO.class))")
    public abstract ActivityTemplateFlowResourceImageDTO toActivityTemplateFlowResourceImageDTO(ActivityTemplateFlowResourceImage entity);
}
