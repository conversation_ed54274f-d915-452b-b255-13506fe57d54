package fm.lizhi.ocean.wavecenter.infrastructure.live.convert;


import fm.lizhi.ocean.wavecenter.api.live.bean.RoomDayCalendarDetailRes;
import fm.lizhi.ocean.wavecenter.api.live.bean.RoomDayDetail;
import fm.lizhi.ocean.wavecenter.api.live.bean.RoomDayStats;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.RoomDayCalendarEntity;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.RoomDayStatsDetailEntity;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.RoomDayStatsEntity;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface LiveRoomCheckConvert {

    LiveRoomCheckConvert I = Mappers.getMapper(LiveRoomCheckConvert.class);

    @Mappings({
            @Mapping(source = "statDate", target = "time"),

    })
    RoomDayDetail convertRoomDayDetail(RoomDayStatsDetailEntity entity);


    RoomDayStats convertRoomDayStats(RoomDayStatsEntity entity);

    @Mappings({
            @Mapping(source = "statDate", target = "time"),
    })
    RoomDayCalendarDetailRes convertRoomDayCalendarDetailRes(RoomDayCalendarEntity entity);


}
