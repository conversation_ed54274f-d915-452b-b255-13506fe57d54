package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.convert;

import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowRoomAbilityWeekCapability;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.RoomAbilityWeekCapabilityDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/11 15:37
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface GrowRoomAbilityWeekCapabilityConvert {

    GrowRoomAbilityWeekCapabilityConvert I = Mappers.getMapper(GrowRoomAbilityWeekCapabilityConvert.class);

    RoomAbilityWeekCapabilityDTO roomAbilityWeekCapability2Dto(WcGrowRoomAbilityWeekCapability po);

    @Mapping(target = "startWeekDate", ignore = true)
    @Mapping(target = "modifyTime", ignore = true)
    @Mapping(target = "lastWeekAbilityValue", ignore = true)
    @Mapping(target = "endWeekDate", ignore = true)
    @Mapping(target = "deployEnv", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "compareWeekValue", ignore = true)
    @Mapping(target = "appId", ignore = true)
    WcGrowRoomAbilityWeekCapability roomAbilityWeekCapabilityDTO2Po(RoomAbilityWeekCapabilityDTO dto);

    List<RoomAbilityWeekCapabilityDTO> roomAbilityWeekCapabilitys2Dtos(List<WcGrowRoomAbilityWeekCapability> pos);

}
