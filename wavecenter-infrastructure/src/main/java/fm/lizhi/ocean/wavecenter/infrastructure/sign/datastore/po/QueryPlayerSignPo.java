package fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.po;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/17 14:31
 */
@Data
@Accessors(chain = true)
public class QueryPlayerSignPo {

    private boolean descCreateTime;

    /**
     * 签约ID
     */
    private Long contractId;

    /**
     * 主播ID
     */
    private List<Long> njIds;

    /**
     * 签约类型
     */
    private List<String> types;

    /**
     * 状态
     */
    private List<String> statuses;

    /**
     * 用户或者厅主ID
     */
    private Long userOrNjId;

    /**
     * 用户ID
     */
    private Long userId;

    private List<Long> contractIdLists;

    private Long parentId;

}
