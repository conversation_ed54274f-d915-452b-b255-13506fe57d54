package fm.lizhi.ocean.wavecenter.infrastructure.live.convert;

import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WaveCheckInRecordEntity;
import fm.lizhi.ocean.wavecenter.service.live.dto.PlayerCheckHourStatsDto;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/11 18:21
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface LivePlayerCheckInfraConvert {

    LivePlayerCheckInfraConvert I = Mappers.getMapper(LivePlayerCheckInfraConvert.class);

    @Mappings({
            @Mapping(source = "status", target = "checkInStatus"),
            @Mapping(source = "njId", target = "roomId"),
    })
    PlayerCheckHourStatsDto hourStatsPo2Dto(WaveCheckInRecordEntity po);

    List<PlayerCheckHourStatsDto> hourStatsPos2Dtos(List<WaveCheckInRecordEntity> pos);

}
