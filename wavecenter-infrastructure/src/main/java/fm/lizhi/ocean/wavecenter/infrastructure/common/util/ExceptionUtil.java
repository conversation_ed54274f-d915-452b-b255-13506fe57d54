package fm.lizhi.ocean.wavecenter.infrastructure.common.util;

import fm.lizhi.common.datastore.mysql.exception.DatastoreMysqlOperationException;

/**
 * <AUTHOR>
 * @date 2025/4/24 20:03
 */
public class ExceptionUtil {

    /**
     * 是否为键冲突异常
     * @param opE
     * @return
     */
    public static boolean isDuplicateKeyException(DatastoreMysqlOperationException opE){
        Throwable cause = opE.getCause();
        // DuplicateKeyException会被包装为DatastoreMysqlOperationException, 可查看 fm.lizhi.common.datastore.mysql.aop.MapperInterceptor.pushStackTraceMessageIfNeeded
        return (cause instanceof org.springframework.dao.DuplicateKeyException);
    }

}
