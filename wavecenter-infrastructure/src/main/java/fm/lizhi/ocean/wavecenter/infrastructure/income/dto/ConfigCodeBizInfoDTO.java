package fm.lizhi.ocean.wavecenter.infrastructure.income.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/22 15:16
 */
@Data
@Accessors(chain = true)
public class ConfigCodeBizInfoDTO {

    /**
     * 账户编码
     */
    private String accountCode;

    /**
     * 交易引擎账户编码
     */
    private String accountEngineCode;

    /**
     * bizId列表
     */
    private List<Integer> bizIdList;

}
