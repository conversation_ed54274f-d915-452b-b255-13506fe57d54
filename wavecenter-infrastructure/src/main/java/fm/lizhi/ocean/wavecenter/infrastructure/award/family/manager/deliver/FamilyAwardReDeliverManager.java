package fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager.deliver;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardDeliverExecutionStatusEnum;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardResourceDeliverTypeEnum;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardTypeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert.FamilyAwardDeliverConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.dao.FamilyAwardDeliverRecordDao;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilyAwardDeliverExecution;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilyAwardDeliverItem;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilyAwardDeliverRecord;
import fm.lizhi.ocean.wavecenter.service.award.family.config.FamilyAwardConfig;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.helper.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class FamilyAwardReDeliverManager {

    @Autowired
    private FamilyAwardConfig familyAwardConfig;

    @Autowired
    private FamilyAwardDeliverRecordDao familyAwardDeliverRecordDao;

    @Autowired
    private List<FamilyAwardDeliver> familyAwardDelivers;

    @Autowired
    private DeliverResultHandler deliverResultHandler;

    /**
     * 重新发放公会奖励
     *
     * @param executionId 执行id
     */
    public void reDeliverAward(Long executionId) {
        log.info("ReDelivering award, executionId: {}", executionId);
        WcFamilyAwardDeliverExecution execution = familyAwardDeliverRecordDao.getDeliverExecution(executionId);
        if (execution == null) {
            log.info("Skip reDelivering family award, execution not found, executionId: {}", executionId);
            return;
        }
        if (Objects.equals(execution.getStatus(), FamilyAwardDeliverExecutionStatusEnum.DELIVER_SUCCESS.getValue())
                && !familyAwardConfig.isReDeliverSuccessEnabled()) {
            log.info("Skip reDelivering family award, execution is success and reDeliverSuccessEnabled is false, executionId: {}", executionId);
            return;
        }
        BusinessEvnEnum businessEvnEnum = BusinessEvnEnum.from(execution.getAppId());
        Validate.notNull(businessEvnEnum, "Invalid appId: " + execution.getAppId());
        // 设置应用上下文, 后续代码无需ResultHandler
        ContextUtils.setBusinessEvnEnum(businessEvnEnum);
        // 查询关联记录, 拼装发放参数
        Long recordId = execution.getRecordId();
        WcFamilyAwardDeliverRecord record = familyAwardDeliverRecordDao.getDeliverRecord(recordId);
        List<WcFamilyAwardDeliverItem> items = familyAwardDeliverRecordDao.getDeliverItemsByExecutionId(executionId);
        DeliverResourceParam deliverResourceParam = FamilyAwardDeliverConvert.I.toDeliverResourceParam(record, execution, items);
        FamilyAwardTypeEnum awardType = deliverResourceParam.getResourceAwardType();
        FamilyAwardResourceDeliverTypeEnum deliverType = deliverResourceParam.getResourceDeliverType();
        // 获取发放实现
        FamilyAwardDeliver familyAwardDeliver = getFamilyAwardDeliver(awardType, deliverType);
        if (familyAwardDeliver == null) {
            log.warn("Skip reDelivering family award, no deliver found, executionId: {}", executionId);
            return;
        }
        // 执行发放动作
        Result<Void> deliverResult = familyAwardDeliver.deliverResource(deliverResourceParam);
        // 处理重新发放结果
        deliverResultHandler.handleReDeliverResult(recordId, executionId, deliverResourceParam, deliverResult);
        log.info("ReDelivered award, executionId: {}", executionId);
    }

    private FamilyAwardDeliver getFamilyAwardDeliver(FamilyAwardTypeEnum awardType, FamilyAwardResourceDeliverTypeEnum deliverType) {
        for (FamilyAwardDeliver familyAwardDeliver : familyAwardDelivers) {
            if (familyAwardDeliver.supports(awardType, deliverType)) {
                return familyAwardDeliver;
            }
        }
        return null;
    }
}
