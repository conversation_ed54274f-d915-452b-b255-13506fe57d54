package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.manager;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.CharmRatioBean;
import fm.lizhi.ocean.wavecenter.base.util.CalculateUtil;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote.ICharmStatRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.IContractRemote;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.AssessTimeDto;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.PlayerGetAssessmentDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/24 15:01
 */
@Slf4j
@Component
public class CharmStateManager {

    @Autowired
    private ICharmStatRemote iCharmStatRemote;
    @Autowired
    private IContractRemote iContractRemote;

    /**
     * 查询陪玩考核期间厅收礼魅力值
     * @param param
     * @return
     */
    public CharmRatioBean getPlayerAssessmentRoomCharm(PlayerGetAssessmentDto param) {
        List<Long> familySignRoomIds = param.getFamilySignRoomIds();
        Long playerId = param.getPlayerId();
        AssessTimeDto currentTime = param.getCurrentTime();
        AssessTimeDto preTime = param.getPreTime();

        //厅收礼魅力值 房间主播为本账号签约厅主ID&收礼人为本账号的魅力值
        CharmRatioBean roomCharm = new CharmRatioBean();
        Integer curRoomCharm = 0;
        Integer preRoomCharm = 0;
        if (CollectionUtils.isNotEmpty(familySignRoomIds)) {
            log.info("roomIds={}", JsonUtil.dumps(familySignRoomIds));
            curRoomCharm = iCharmStatRemote.getRoomIncomeCharm(familySignRoomIds, playerId, currentTime.getStartDate(), currentTime.getEndDate());
            if (preTime != null) {
                preRoomCharm = iCharmStatRemote.getRoomIncomeCharm(familySignRoomIds, playerId, preTime.getStartDate(), preTime.getEndDate());
            }
        }

        roomCharm.setCurrent(curRoomCharm);
        roomCharm.setPer(preRoomCharm);
        roomCharm.setRatio(CalculateUtil.relativeRatio(preRoomCharm, curRoomCharm));
        return roomCharm;
    }

    /**
     * 查询陪玩考核期间个播收礼魅力值
     * @param param
     * @return
     */
    public CharmRatioBean getPlayerAssessmentIndividualCharm(PlayerGetAssessmentDto param) {
        Long playerId = param.getPlayerId();
        AssessTimeDto currentTime = param.getCurrentTime();
        AssessTimeDto preTime = param.getPreTime();

        //个播魅力值 本账号作为房主的流水的魅力值
        CharmRatioBean individualCharm = new CharmRatioBean();
        Integer curIndividualCharm = 0;
        Integer preIndividualCharm = 0;
        curIndividualCharm = iCharmStatRemote.getPersonIncomeCharm(playerId, currentTime.getStartDate(), currentTime.getEndDate());
        if (preTime != null) {
            preIndividualCharm = iCharmStatRemote.getPersonIncomeCharm(playerId, preTime.getStartDate(), preTime.getEndDate());
        }
        individualCharm.setCurrent(curIndividualCharm);
        individualCharm.setPer(preIndividualCharm);
        individualCharm.setRatio(CalculateUtil.relativeRatio(preIndividualCharm, curIndividualCharm));
        return individualCharm;
    }

    /**
     * 查询官方厅收入魅力值
     * @param param
     * @return
     */
    public CharmRatioBean getPlayerAssessmentOfficialCharm(PlayerGetAssessmentDto param){
        CharmRatioBean officialCharm = new CharmRatioBean();
        List<Long> officialRoomIds = iContractRemote.getOfficialRoomIds();
        if (CollectionUtils.isEmpty(officialRoomIds)) {
            log.info("officialRoomIds is empty");
            return officialCharm;
        }
        log.info("getPlayerAssessmentOfficialCharm officialRoomIds={}", JsonUtil.dumps(officialRoomIds));

        Long playerId = param.getPlayerId();
        AssessTimeDto preTime = param.getPreTime();
        AssessTimeDto currentTime = param.getCurrentTime();

        Integer curRoomCharm = 0;
        Integer preRoomCharm = 0;
        curRoomCharm = iCharmStatRemote.getRoomIncomeCharm(officialRoomIds, playerId, currentTime.getStartDate(), currentTime.getEndDate());
        if (preTime != null) {
            preRoomCharm = iCharmStatRemote.getRoomIncomeCharm(officialRoomIds, playerId, preTime.getStartDate(), preTime.getEndDate());
        }

        officialCharm.setCurrent(curRoomCharm);
        officialCharm.setPer(preRoomCharm);
        officialCharm.setRatio(CalculateUtil.relativeRatio(preRoomCharm, curRoomCharm));
        return officialCharm;
    }

}
