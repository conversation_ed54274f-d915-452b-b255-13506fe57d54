package fm.lizhi.ocean.wavecenter.infrastructure.sign.convert;

import fm.lizhi.ocean.wavecenter.api.sign.bean.SignFlowBean;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity.WcSignFlow;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity.WcSignStatusSync;
import fm.lizhi.ocean.wavecenter.service.sign.dto.SignStatusSyncDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2024/10/10 14:51
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS
)
public interface SignFlowInfraConvert {

    SignFlowInfraConvert I = Mappers.getMapper(SignFlowInfraConvert.class);

    @Mappings({
            @Mapping(source = "id", target = "flowId"),
    })
    SignFlowBean po2Bean(WcSignFlow po);

    WcSignStatusSync statusSyncDto2Po(SignStatusSyncDTO dto);

}
