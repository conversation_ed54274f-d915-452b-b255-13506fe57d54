package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.manager;

import cn.hutool.core.collection.CollUtil;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityResourceDeployTypeConstants;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.AutoConfigResourceEnum;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityLevelConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestPageActivityResources;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityResource;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityResource;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseActivityResource;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityResourceConfigService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert.ActivityResourceConfigConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.dao.ActivityResourceDao;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityResourceConfig;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityResourceLevelRelation;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.processor.IResourceConfigProcess;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityLevelManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityResourceManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityResourceSimpleInfoDTO;
import fm.lizhi.ocean.wavecenter.base.processor.ProcessorFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 活动资源
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityResourceManagerImpl implements ActivityResourceManager {


    @Autowired
    private ActivityResourceDao activityResourceDao;

    @Autowired
    private ActivityLevelManager activityLevelManager;

    @Autowired
    private CommonConfig commonConfig;

    @Autowired
    private ProcessorFactory factory;


    @Override
    public Result<Void> saveActivityResource(RequestSaveActivityResource param) {
        // 校验是否重复
        boolean exist = activityResourceDao.existResource(param.getAppId(), param.getName(), param.getDeployType(), param.getResourceCode());
        if (exist) {
            log.warn("saveActivityResource is fail. has exist resource: {}, appId: {}", param.getName(), param.getAppId());
            return RpcResult.fail(ActivityResourceConfigService.SAVE_ACTIVITY_RESOURCE_RESOURCE_EXIST, "流量资源重复配置");
        }

        boolean existLevels = checkLevelsExist(param.getAppId(), param.getRelationLevelIds());
        if (!existLevels) {
            log.warn("saveActivityResource is fail. has not exist level. relationLevelIds: {}", param.getRelationLevelIds());
            return RpcResult.fail(ActivityResourceConfigService.SAVE_ACTIVITY_RESOURCE_NOT_FOUND_LEVEL, "活动等级不存在");
        }

        // 如果是自动配置资源，校验自动配置枚举是否存在
        if (param.getDeployType().equals(ActivityResourceDeployTypeConstants.AUTO_CONFIG)) {
            AutoConfigResourceEnum autoConfigResource = getAutoConfigResource(param.getDeployType(), param.getResourceCode());
            if (autoConfigResource == null) {
                log.warn("saveActivityResource is fail. has not exist auto config resource. resourceCode: {}", param.getResourceCode());
                return RpcResult.fail(ActivityResourceConfigService.SAVE_ACTIVITY_RESOURCE_NOT_FOUND_AUTO_RESOURCE, "自动配置资源不存在");
            }
            param.setName(autoConfigResource.getResourceName());
        }

        IResourceConfigProcess processor = factory.getProcessor(IResourceConfigProcess.class);
        // 校验资源是否重复
        Result<Void> result = processor.checkResourceLevelRepeat(param.getRelationLevelIds(), param.getResourceCode(), param.getDeployType());
        if (RpcResult.isFail(result)) {
            return result;
        }

        // 保存
        activityResourceDao.saveActivityResource(param, processor);
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }


    @Override
    public Result<Void> updateActivityResource(RequestUpdateActivityResource param) {

        // 校验资源是否存在
        ActivityResourceConfig resourceConfig = activityResourceDao.selectById(param.getId());
        if (resourceConfig == null) {
            log.warn("updateActivityResource is fail. cannot found activity resource. resourceId: {}", param.getId());
            return RpcResult.fail(ActivityResourceConfigService.UPDATE_ACTIVITY_RESOURCE_NOT_FOUND_RESOURCE, "资源不存在");
        }

        // 校验是否重复. 只有在更新资源名称时进行校验，其他更新可能只是更新状态之类的。
        if (checkIsUpdateResourceName(param, resourceConfig)) {
            boolean exist = activityResourceDao.existResource(param.getAppId(), param.getName(), param.getDeployType(), param.getResourceCode());
            if (exist) {
                log.warn("updateActivityResource is fail. has exist resource: {}, appId: {}", param.getName(), param.getAppId());
                return RpcResult.fail(ActivityResourceConfigService.UPDATE_ACTIVITY_RESOURCE_RESOURCE_EXIST, "流量资源重复配置");
            }
        }


        // 校验等级是否存在
        boolean existLevels = checkLevelsExist(param.getAppId(), param.getRelationLevelIds());
        if (!existLevels) {
            log.warn("updateActivityResource is fail. has not exist level. relationLevelIds: {}", param.getRelationLevelIds());
            return RpcResult.fail(ActivityResourceConfigService.UPDATE_ACTIVITY_RESOURCE_NOT_FOUND_LEVEL, "活动等级不存在");
        }

        // 如果是自动配置资源，校验自动配置枚举是否存在
        if (param.getDeployType().equals(ActivityResourceDeployTypeConstants.AUTO_CONFIG)) {
            AutoConfigResourceEnum autoConfigResource = getAutoConfigResource(param.getDeployType(), param.getResourceCode());
            if (autoConfigResource == null) {
                log.warn("updateActivityResource is fail. cannot found auto config resource. resourceCode: {}", param.getResourceCode());
                return RpcResult.fail(ActivityResourceConfigService.UPDATE_ACTIVITY_RESOURCE_NOT_FOUND_AUTO_RESOURCE, "自动配置资源不存在");
            }
            param.setName(autoConfigResource.getResourceName());
        }

        IResourceConfigProcess processor = factory.getProcessor(IResourceConfigProcess.class);
        // 校验资源是否重复
        Result<Void> result = processor.checkResourceLevelRepeat(param.getRelationLevelIds(), param.getResourceCode(), param.getDeployType());
        if (RpcResult.isFail(result)) {
            return result;
        }

        activityResourceDao.updateActivityResource(param, processor);
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    /**
     * 检查是否是更新资源名
     *
     * @param param
     * @param resourceConfig
     * @return true: 更新资源名， false: 不是更新资源名
     */
    private boolean checkIsUpdateResourceName(RequestUpdateActivityResource param, ActivityResourceConfig resourceConfig) {
        // 自动配置资源， 检查 resourceCode 是否重复
        if (param.getDeployType().equals(ActivityResourceDeployTypeConstants.AUTO_CONFIG)) {
            return !param.getResourceCode().equals(resourceConfig.getResourceCode());
        } else if (param.getDeployType().equals(ActivityResourceDeployTypeConstants.MANUAL_CONFIG)) {
            return !param.getName().equals(resourceConfig.getName());
        }
        return false;
    }


    @Override
    public Result<Void> deleteActivityResource(Long id, Integer appId, String operator) {

        if (null == id || id <= 0) {
            log.warn("deleteActivityResource is fail. id is null or id <= 0");
            return RpcResult.fail(ActivityResourceConfigService.DELETE_ACTIVITY_RESOURCE_FAIL, "删除活动资源失败");
        }

        ActivityResourceConfig activityResourceConfig = activityResourceDao.selectById(id);

        IResourceConfigProcess processor = factory.getProcessor(IResourceConfigProcess.class);
        activityResourceDao.deleteActivityResource(activityResourceConfig, operator, processor);
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public Result<PageBean<ResponseActivityResource>> listActivityResource(RequestPageActivityResources param) {

        if (param.getPageNo() <= 0) {
            param.setPageNo(1);
        }

        if (param.getPageSize() <= 0) {
            param.setPageSize(20);
        }

        // 查询资源，西米会过滤节目类型的资源
        PageList<ActivityResourceConfig> list = activityResourceDao.listActivityResource(param);
        if (CollUtil.isEmpty(list)) {
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, PageBean.empty());
        }

        // 获取关系
        List<ActivityResourceLevelRelation> levelRelations = activityResourceDao.getLevelRelationsBatch(
                list.stream().map(ActivityResourceConfig::getId).collect(Collectors.toList())
        );

        if (CollUtil.isEmpty(levelRelations)) {
            // 没有等级关联关系，直接返回数据
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, PageBean.of(list.getTotal(), ActivityResourceConfigConvert.I.buildResponseActivityResources(list)));
        }

        // 有关联关系，获取等级
        List<ActivityLevelConfigBean> levelConfigs = activityLevelManager.listByAppIdAndLevelIds(param.getAppId(),
                levelRelations.stream().map(ActivityResourceLevelRelation::getLevelId).collect(Collectors.toList())
        );

        List<ResponseActivityResource> responseList = list.stream().map(resource -> {

            List<Long> levelIds = levelRelations.stream().filter(relation -> relation.getResourceId().equals(resource.getId()))
                    .map(ActivityResourceLevelRelation::getLevelId)
                    .collect(Collectors.toList());

            resource.setImageUrl(UrlUtils.addHostOrEmpty(resource.getImageUrl(), commonConfig.getRomeFsDownloadCdn()));
            List<ActivityLevelConfigBean> levelList = levelConfigs.stream().filter(level -> levelIds.contains(level.getId())).collect(Collectors.toList());
            return ActivityResourceConfigConvert.I.buildResponseActivityResource(resource, levelList);
        }).collect(Collectors.toList());

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, PageBean.of(list.getTotal(), responseList));
    }

    @Override
    public List<ActivityResourceSimpleInfoDTO> batchResourceByIds(List<Long> ids) {
        List<ActivityResourceConfig> list = activityResourceDao.batchResourceByIds(ids);
        return ActivityResourceConfigConvert.I.buildSimpleInfoListDTOs(list);
    }

    @Override
    public List<ActivityResourceSimpleInfoDTO> batchValidateResourceByIdsAndStatus(List<Long> ids, int status) {
        List<ActivityResourceConfig> resourceConfigs = activityResourceDao.listValidateResourceByIdsAndStatus(ids, status);
        return ActivityResourceConfigConvert.I.buildSimpleInfoListDTOs(resourceConfigs);
    }

    @Override
    public Result<List<ResponseActivityResource>> listActivityResourceByLevelId(Long levelId, int appId) {
        List<ActivityResourceLevelRelation> resourceRelations = activityResourceDao.getResourceRelations(levelId);
        if (CollUtil.isEmpty(resourceRelations)) {
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, Collections.emptyList());
        }

        List<ActivityResourceConfig> resourceList = activityResourceDao.batchResourceByIds(resourceRelations.stream().map(ActivityResourceLevelRelation::getResourceId).collect(Collectors.toList()));
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, ActivityResourceConfigConvert.I.buildResponseActivityResources(resourceList));
    }


    /**
     * 获取自动配置资源枚举
     */
    private AutoConfigResourceEnum getAutoConfigResource(Integer deployType, String resourceCode) {
        if (deployType.equals(ActivityResourceDeployTypeConstants.AUTO_CONFIG)) {
            AutoConfigResourceEnum resourceEnum = AutoConfigResourceEnum.getByResourceCode(resourceCode);
            if (resourceEnum == null) {
                log.warn("getAutoConfigResource is fail. cannot found auto config resource. resourceCode: {}", resourceCode);
                return null;
            }
            return resourceEnum;
        }
        return null;
    }

    /**
     * 校验活动等级是否都存在
     *
     * @param appId
     * @param levelIds
     * @return false 不存在， true 都存在
     */
    private boolean checkLevelsExist(int appId, List<Long> levelIds) {
        // 校验等级是否存在
        List<Long> notExistLevelIds = activityLevelManager.batchExistLevel(appId, levelIds);
        if (CollUtil.isNotEmpty(notExistLevelIds)) {
            log.warn("checkLevelsExist is fail. has not exist level. relationLevelIds: {}, notExistLevelIds: {}", levelIds, notExistLevelIds);
            return false;
        }
        return true;
    }


    @Override
    public List<ResponseActivityResource> getActivityResourcesByIds(List<Long> ids) {
        List<ActivityResourceConfig> resourceConfigs = activityResourceDao.batchResourceByIds(ids);
        return ActivityResourceConfigConvert.I.buildResponseActivityResources(resourceConfigs);
    }


    @Override
    public Result<Void> checkResourceLevelRepeat(List<Long> levelIds, String targetResourceCode) {
        List<ActivityResourceConfig> resourceConfigs = activityResourceDao.getResourceConfigByResourceCode(targetResourceCode, BusinessEvnEnum.XIMI.appId());
        if (CollUtil.isEmpty(resourceConfigs)) {
            log.info("checkResourceLevelRepeat is not find resource. targetResourceCode: {}", targetResourceCode);
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
        }
        List<ActivityResourceLevelRelation> levelRelations = activityResourceDao.getLevelRelations(resourceConfigs.get(0).getId());
        if (CollUtil.isEmpty(levelRelations)) {
            log.info("checkResourceLevelRepeat is not find level. targetResourceCode: {}", targetResourceCode);
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
        }

        // 校验查询结果是否与入参的level存在重叠，并返回重复的等级名称
        Set<Long> sourceLevelIds = new HashSet<>(levelIds);
        List<Long> repeatLevelIds = levelRelations.stream()
                                 .map(ActivityResourceLevelRelation::getLevelId)
                                   .filter(sourceLevelIds::contains).collect(Collectors.toList());
        List<ActivityLevelConfigBean> repeatLevels = activityLevelManager.listByAppIdAndLevelIds(BusinessEvnEnum.XIMI.appId(), repeatLevelIds);
        if (CollUtil.isNotEmpty(repeatLevels)) {
            String repeatLevelNames = repeatLevels.stream().map(ActivityLevelConfigBean::getLevel).collect(Collectors.joining(","));
            return RpcResult.fail(ActivityResourceConfigService.SAVE_ACTIVITY_RESOURCE_RESOURCE_LEVEL_REPEAT, "官频位（含节目单）和节目单不可同时绑定相同等级，请重新修改以下等级: " + repeatLevelNames);
        }

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }
}
