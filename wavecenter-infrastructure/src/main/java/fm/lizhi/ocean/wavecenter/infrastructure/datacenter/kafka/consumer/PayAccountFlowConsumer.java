package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.kafka.consumer;

import fm.lizhi.common.kafka.common.strategy.AutoOffsetReset;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaHandler;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaListener;
import fm.lizhi.common.kafka.ioc.api.strategy.annotation.AutoOffsetResetStrategy;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.common.utils.KafkaMsgUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PayTenantCodeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.convert.PayAccountFlowConvert;
import fm.lizhi.ocean.wavecenter.service.datacenter.handler.PayAccountFlowCollector;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 支付账户流水消费
 * <AUTHOR>
 * @date 2025/4/22 17:46
 */
@Slf4j
@Component
@KafkaListener(clusterNamespace = "public-kafka250-bootstrap-server")
public class PayAccountFlowConsumer {

    @Autowired
    private PayAccountFlowCollector payAccountFlowCollector;

    /**
     * 支付账户流水消费
     * @param body
     */
    @KafkaHandler(topic = "accounting_engines_balance_change_t", group = "accounting_engines_balance_change_t_wave_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleAccountFlow(String body){
        String msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
        log.info("handleAccountFlow msg={}", msg);
        AccountFlowMsg accountFlowMsg = JsonUtil.loads(msg, AccountFlowMsg.class);
        // 数据太多了，查日志就行
//        try {
//            // 保存流水 流水ID有唯一索引 不会出现重复的情况
//            payAccountFlowDao.saveAccountFlowMsg(accountFlowMsg);
//        } catch (DatastoreMysqlOperationException opE) {
//            if (ExceptionUtil.isDuplicateKeyException(opE)) {
//                // 唯一索引冲突，说明已经有保存流水数据了，不用再消费了
//                log.warn("handleAccountFlow DuplicateKeyException: ", opE);
//                return;
//            }else {
//                throw opE;
//            }
//        }

        try {
            // 处理流水
            Integer appId = PayTenantCodeEnum.toAppId(accountFlowMsg.getTenantCode());
            BusinessEvnEnum businessEvnEnum = BusinessEvnEnum.from(appId);
            ContextUtils.reSetBusinessEvnEnum(businessEvnEnum);

            payAccountFlowCollector.collectPayAccountFlow(PayAccountFlowConvert.I.msgToDto(accountFlowMsg));

            // 更新流水消费次数
//            payAccountFlowDao.updateAccountFlowMsgConsumer(accountFlowMsg.getId());
        } catch (Exception e) {
            log.error("handleAccountFlow msg:{}, orgMsg:{}", msg, body, e);
        }
    }

}
