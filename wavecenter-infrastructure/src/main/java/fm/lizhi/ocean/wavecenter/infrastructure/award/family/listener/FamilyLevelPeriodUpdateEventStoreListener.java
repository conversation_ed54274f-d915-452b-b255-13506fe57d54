package fm.lizhi.ocean.wavecenter.infrastructure.award.family.listener;

import fm.lizhi.ocean.wavecenter.common.validation.BeanValidator;
import fm.lizhi.ocean.wavecenter.common.validation.ValidateResult;
import fm.lizhi.ocean.wavecenter.domain.grow.event.GrowFamilyLevelPeriodUpdateEvent;
import fm.lizhi.ocean.wavecenter.service.award.family.convert.FamilyLevelUpEventConvert;
import fm.lizhi.ocean.wavecenter.service.award.family.dto.FamilyAwardLevelDataDTO;
import fm.lizhi.ocean.wavecenter.service.award.family.manager.FamilyAwardLevelDataManager;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

/**
 * 公会等级周结算事件存储监听器
 */
@Component
@Slf4j
public class FamilyLevelPeriodUpdateEventStoreListener implements ApplicationListener<GrowFamilyLevelPeriodUpdateEvent> {

    @Autowired
    private FamilyAwardLevelDataManager familyAwardLevelDataManager;

    @Autowired
    private BeanValidator beanValidator;

    @Override
    public void onApplicationEvent(@NotNull GrowFamilyLevelPeriodUpdateEvent event) {
        try {
            FamilyAwardLevelDataDTO data = FamilyLevelUpEventConvert.I.eventToData(event);
            log.info("on family level up event, data={}", data);
            ValidateResult validateResult = beanValidator.validate(data);
            if (validateResult.isInvalid()) {
                log.warn("family award level data is invalid, message={}", validateResult.getMessage());
                return;
            }
            familyAwardLevelDataManager.save(data);
            log.info("save family award level data success");
        } catch (RuntimeException e) {
            log.error("save family award level data failed, event={}", event, e);
        }
    }
}
