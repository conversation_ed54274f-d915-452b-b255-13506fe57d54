package fm.lizhi.ocean.wavecenter.infrastructure.live.convert;


import fm.lizhi.ocean.wavecenter.api.live.bean.GuildRoomDayDetail;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcRoomCheckInDayStats;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface LiveGuildCheckConvert {

    LiveGuildCheckConvert I = Mappers.getMapper(LiveGuildCheckConvert.class);


    @Mappings({
            @Mapping(source = "statDate", target = "time"),
            @Mapping(source = "checkInPlayer", target = "checkPlayerNumber"),
    })
    GuildRoomDayDetail convertGuildRoomDayDetail(WcRoomCheckInDayStats stats);



}
