package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote.hy;

import com.google.common.collect.Lists;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.IncomeBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomAssessmentInfoBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.ThreadConstants;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PaySettleConfigCodeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.HyPlayerSignCharmStatMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.PlayerIncomeStatisticsBean;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.PlayerSignPerformancePo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote.IRoomDataRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.income.manager.PaymentManager;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.IContractRemote;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.AssessTimeDto;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.GetRoomPlayerPerformanceParamDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2024/5/23 16:14
 */
@Slf4j
@Component
public class HyRoomDataRemote implements IRoomDataRemote {

    @Autowired
    private PaymentManager paymentManager;

    @Autowired
    private HyPlayerSignCharmStatMapper hyPlayerSignCharmStatMapper;

    @Autowired
    private IContractRemote contractRemote;


    @Override
    public RoomAssessmentInfoBean queryAssessment(Long familyId, Long roomId) {
        RoomAssessmentInfoBean roomAssessmentInfoBean = new RoomAssessmentInfoBean();
        CompletableFuture.allOf(
            CompletableFuture.runAsync(() -> {
                IncomeBean sumIncome = paymentManager.getIncomeBeanByRoom(familyId, roomId, PaySettleConfigCodeEnum.HALL_INCOME_TOTAL_AMOUNT);
                roomAssessmentInfoBean.setSumIncome(sumIncome);
            }, ThreadConstants.roomDatePool),
            CompletableFuture.runAsync(() -> {
                //签约厅
                IncomeBean roomIncome = paymentManager.getIncomeBeanByRoom(familyId, roomId, PaySettleConfigCodeEnum.HALL_HALL_INCOME_TOTAL_AMOUNT);
                roomAssessmentInfoBean.setRoomIncome(roomIncome);
            }, ThreadConstants.roomDatePool),
            CompletableFuture.runAsync(() -> {
                //个播
                IncomeBean individualIncome = paymentManager.getIncomeBeanByRoom(familyId, roomId, PaySettleConfigCodeEnum.HALL_INDIVIDUAL_INCOME_TOTAL_AMOUNT);
                roomAssessmentInfoBean.setIndividualIncome(individualIncome);
            }, ThreadConstants.roomDatePool),
            CompletableFuture.runAsync(() -> {
                //官方厅
                IncomeBean officialIncome = paymentManager.getIncomeBeanByRoom(familyId, roomId, PaySettleConfigCodeEnum.HALL_OFFICIAL_HALL_INCOME_TOTAL_AMOUNT);
                roomAssessmentInfoBean.setOfficialIncome(officialIncome);
            }, ThreadConstants.roomDatePool)
        ).join();
        return roomAssessmentInfoBean;
    }

    @Override
    public PageList<PlayerSignPerformancePo> selectPlayerSignPerformance(GetRoomPlayerPerformanceParamDto paramDto) {
        AssessTimeDto assessTime = paramDto.getAssessTime();
        String startDate = DateUtil.formatDateToString(assessTime.getStartDate(), DateUtil.date_2);
        String endDate = DateUtil.formatDateToString(assessTime.getEndDate(), DateUtil.date_2);
        List<PlayerSignPerformancePo> poList = hyPlayerSignCharmStatMapper.selectPlayerSignCharmSum(paramDto.getRoomId()
                , startDate
                , endDate);
        PageList<PlayerSignPerformancePo> hyPlayerPerformances = new PageList<>();
        hyPlayerPerformances.addAll(poList);
        hyPlayerPerformances.setTotal(poList.size());

        //根据时间范围查询签约的主播
        List<Long> allContractUserIds = contractRemote.selectAllSignPlayerByDate(Lists.newArrayList(paramDto.getRoomId()), assessTime.getStartDate(), assessTime.getEndDate());

        List<PlayerIncomeStatisticsBean> playerIncomeStatisticsPos =
                paymentManager.getAllAnchorIncomeStatisticsValue(paramDto.getRoomId(), paramDto.getFamilyId(), paramDto.getAppId(), allContractUserIds);

        mergePlayerSignPerformance(hyPlayerPerformances, playerIncomeStatisticsPos);

        sortPlayerSignPerformance(hyPlayerPerformances, paramDto.getOrderType());

        return hyPlayerPerformances;
    }

    private void sortPlayerSignPerformance(PageList<PlayerSignPerformancePo> hyPlayerPerformances, OrderType orderType) {
        if (orderType.equals(OrderType.ASC)) {
            //按当期收入值升序排序
            hyPlayerPerformances.sort(Comparator.comparing(PlayerSignPerformancePo::getSignIncome));
        } else {
            //按当期收入值降序排序
            hyPlayerPerformances.sort((o1, o2) -> {
                Long signIncome1 = o1 != null ? o1.getSignIncome() : null;
                Long signIncome2 = o2 != null ? o2.getSignIncome() : null;

                if (signIncome1 == null && signIncome2 == null) {
                    return 0;
                } else if (signIncome1 == null) {
                    return 1;
                } else if (signIncome2 == null) {
                    return -1;
                } else {
                    return signIncome2.compareTo(signIncome1);
                }
            });
        }
    }

    /**
     * 以当期收入为基础，合并魅力值与收入
     * @param hyPlayerPerformances
     * @param playerIncomeStatisticsPos
     */
    private void mergePlayerSignPerformance(PageList<PlayerSignPerformancePo> hyPlayerPerformances, List<PlayerIncomeStatisticsBean> playerIncomeStatisticsPos) {
        playerIncomeStatisticsPos.stream()
                .filter(x -> x.getIncomeStatistics() > 0 || x.getPreIncomeStatistics() > 0)
                .forEach(playerIncomeStatisticsPo -> {
                    //分页，数量不多，这里先直接列表查询
                    PlayerSignPerformancePo playerSignPerformancePo = hyPlayerPerformances.stream().filter( hyPlayerPerformance -> hyPlayerPerformance.getUserId().equals(playerIncomeStatisticsPo.getUserId()))
                            .findAny().orElse(new PlayerSignPerformancePo());
                    playerSignPerformancePo.setSignIncome(playerIncomeStatisticsPo.getIncomeStatistics());
                    playerSignPerformancePo.setPreIncome(playerIncomeStatisticsPo.getPreIncomeStatistics());
                    if (playerSignPerformancePo.getUserId() == null) {
                        playerSignPerformancePo.setUserId(playerIncomeStatisticsPo.getUserId());
                        hyPlayerPerformances.add(playerSignPerformancePo);
                    }
        });
    }



    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.HEI_YE;
    }
}
