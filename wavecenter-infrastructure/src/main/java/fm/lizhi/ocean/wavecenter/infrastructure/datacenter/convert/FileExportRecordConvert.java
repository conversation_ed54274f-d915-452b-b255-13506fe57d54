package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.convert;

import fm.lizhi.ocean.wavecenter.api.datacenter.bean.FileExportRecordBean;
import fm.lizhi.ocean.wavecenter.datastore.platform.file.entity.WcFileExportRecord;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/19 18:28
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface FileExportRecordConvert {
    FileExportRecordConvert I = Mappers.getMapper(FileExportRecordConvert.class);


    FileExportRecordBean toDto(WcFileExportRecord entity);

    List<FileExportRecordBean> toBeanList(List<WcFileExportRecord> list);

}
