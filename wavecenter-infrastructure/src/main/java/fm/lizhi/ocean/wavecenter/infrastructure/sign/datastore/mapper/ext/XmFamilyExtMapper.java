package fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/19 16:10
 */
@DataStore(namespace = "mysql_xm_lzppfamily_r")
public interface XmFamilyExtMapper {

    @Select({
            "select id from family where status='ACTIVATION' and family_type='PGC' and id>#{lastFamilyId} order by id limit #{pageSize}"
    })
    List<Long> getFamilyIdsByPage(@Param("lastFamilyId") Long lastFamilyId, @Param("pageSize") Integer pageSize);

}
