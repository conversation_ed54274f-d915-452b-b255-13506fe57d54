package fm.lizhi.ocean.wavecenter.infrastructure.common.constants;

import com.google.common.net.MediaType;

/**
 * 文件扩展名对应的内容类型枚举
 */
public enum ExtensionContentTypeEnum {

    DEFAULT("", ""),
    GIF("gif", MediaType.GIF.toString()),
    JPEG("jpeg", MediaType.JPEG.toString()),
    PNG("png", MediaType.PNG.toString()),
    IPG("jpg", MediaType.JPEG.toString())
    ;

    private final String extension;
    private final String contentType;

    ExtensionContentTypeEnum(String extension, String contentType) {
        this.extension = extension;
        this.contentType = contentType;
    }

    public static String getContentType(String extension) {
        for (ExtensionContentTypeEnum value : values()) {
            if (value.extension.equalsIgnoreCase(extension)) {
                return value.contentType;
            }
        }
        return DEFAULT.contentType;
    }
}