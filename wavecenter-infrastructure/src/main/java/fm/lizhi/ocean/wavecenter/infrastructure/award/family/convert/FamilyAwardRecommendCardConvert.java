package fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.SendRecommendCardBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.request.RequestBatchSend;
import fm.lizhi.ocean.wavecenter.base.convert.CommonConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager.deliver.DeliverResourceParam;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.Collections;
import java.util.Date;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR,
        uses = {
                CommonConvert.class,
        },
        imports = {
                ConfigUtils.class,
                Date.class,
        }
)
public interface FamilyAwardRecommendCardConvert {

    FamilyAwardRecommendCardConvert I = Mappers.getMapper(FamilyAwardRecommendCardConvert.class);

    default RequestBatchSend toRequestBatchSend(DeliverResourceParam param, String reason) {
        RequestBatchSend requestBatchSend = new RequestBatchSend();
        requestBatchSend.setAppId(param.getAppId());
        requestBatchSend.setOperator(ConfigUtils.getServiceNameRequired());
        requestBatchSend.setSendRecommendCards(Collections.singletonList(toSendRecommendCardBean(param, reason)));
        return requestBatchSend;
    }

    @Mapping(target = "userId", source = "param.familyUserId")
    @Mapping(target = "sendNum", source = "param.resourceNumber")
    @Mapping(target = "expireDay", source = "param.resourceValidPeriod")
    SendRecommendCardBean toSendRecommendCardBean(DeliverResourceParam param, String reason);
}
