package fm.lizhi.ocean.wavecenter.infrastructure.award.singer.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeMappingEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.bean.SingerDecorateRuleBean;
import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateFlowOperateEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateFlowOperatorEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.anchor.singer.convert.SingerInfoConvert;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.mapper.SingerInfoMapper;
import fm.lizhi.ocean.wavecenter.module.api.award.singer.constants.SingerDecorateOperateReasonConstant;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerInfoDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerInfoManager;
import fm.lizhi.ocean.wavecenter.service.award.singer.config.SingerAwardConfig;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowDTO;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowInitParamDTO;
import fm.lizhi.ocean.wavecenter.service.award.singer.manager.SingerDecorateFlowManager;
import fm.lizhi.ocean.wavecenter.service.award.singer.manager.SingerDecorateManager;
import fm.lizhi.ocean.wavecenter.service.award.singer.manager.SingerDecorateRuleManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 操作歌手装扮
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class OperateSingerDecorateJob implements JobHandler {

    @Autowired
    private SingerAwardConfig singerAwardConfig;

    @Autowired
    private SingerInfoManager singerInfoManager;

    @Autowired
    private SingerDecorateManager singerDecorateManager;

    @Autowired
    private SingerDecorateRuleManager singerDecorateRuleManager;

    @Autowired
    private SingerDecorateFlowManager singerDecorateFlowManager;

    @Autowired
    private SingerInfoMapper singerInfoMapper;

    @Override
    public void execute(JobExecuteContext context) throws Exception {

        SingerDecorateFlowOperateEnum operateEnum = SingerDecorateFlowOperateEnum.GRANT;

        try {
            if (null != context && StrUtil.isNotBlank(context.getParam())){
                String param = context.getParam();
                SingerDecorateFlowOperateEnum paramOperate = SingerDecorateFlowOperateEnum.getEnumByCode(Integer.parseInt(param));
                if (null != paramOperate) {
                    operateEnum = paramOperate;
                }
            }
        }catch (Exception e){
            log.error("获取参数失败. context:{}", context, e);
        }


        Map<Integer, List<Long>> ppUidMap = singerAwardConfig.getPp().getGenerateSingerDecorateUidMap();
        Map<Integer, List<Long>> xmUidMap = singerAwardConfig.getXm().getGenerateSingerDecorateUidMap();
        Map<Integer, List<Long>> hyUidMap = singerAwardConfig.getHy().getGenerateSingerDecorateUidMap();


        if (CollUtil.isNotEmpty(ppUidMap)){
            try {
                BusinessEvnEnum evnEnum = BusinessEvnEnum.PP;
                ContextUtils.setBusinessEvnEnum(evnEnum);
                int count = operateSingerDecorate(ppUidMap, evnEnum, operateEnum);
                log.info("PP 生成歌手装扮流水完成，本次生成数量：{}, operate:{}", count, operateEnum.getCode());
            }catch (Exception e){
                log.error("PP 生成歌手装扮流水失败", e);
            }finally {
                ContextUtils.clearContext();
            }
        }

        if (CollUtil.isNotEmpty(xmUidMap)){
            try {
                BusinessEvnEnum evnEnum = BusinessEvnEnum.XIMI;
                ContextUtils.setBusinessEvnEnum(evnEnum);
                int count = operateSingerDecorate(xmUidMap, evnEnum, operateEnum);
                log.info("XM 生成歌手装扮流水完成，本次生成数量：{}, operate:{}", count, operateEnum.getCode());
            }catch (Exception e){
                log.error("XM 生成歌手装扮流水失败", e);
            } finally {
                ContextUtils.clearContext();
            }
        }

        if (CollUtil.isNotEmpty(hyUidMap)){
            try {
                BusinessEvnEnum evnEnum = BusinessEvnEnum.HEI_YE;
                ContextUtils.setBusinessEvnEnum(evnEnum);
                int count = operateSingerDecorate(hyUidMap, evnEnum, operateEnum);
                log.info("HY 生成歌手装扮流水完成，本次生成数量：{}, operate:{}", count, operateEnum.getCode());
            } catch (Exception e) {
                log.error("HY 生成歌手装扮流水失败", e);
            } finally {
                ContextUtils.clearContext();
            }
        }


    }

    /**
     * 操作歌手装扮流水
     */
    private int operateSingerDecorate(Map<Integer, List<Long>> uidMap, BusinessEvnEnum evnEnum, SingerDecorateFlowOperateEnum operateEnum) {
        boolean isGrant = SingerDecorateFlowOperateEnum.GRANT.equals(operateEnum);

        if (isGrant){
            return grant(uidMap, evnEnum);
        }else {
            return recover(uidMap, evnEnum);
        }
    }


    private int recover(Map<Integer, List<Long>> uidMap, BusinessEvnEnum evnEnum) {
        AtomicInteger count = new AtomicInteger(0);
        for (Map.Entry<Integer, List<Long>> entry : uidMap.entrySet()) {
            Integer singerType = entry.getKey();
            List<Long> userIds = entry.getValue();

            if (CollUtil.isEmpty(userIds)) {
                continue;
            }

            // 校验歌手等级是否存在
            if (isInvalidSingerType(singerType, evnEnum)) {
                log.warn("无效的歌手等级: {}, appId:{}", singerType, evnEnum.getAppId());
                continue;
            }

            // 分组处理，每个分组最多处理20个用户
            List<List<Long>> userGroups = CollUtil.split(userIds, 20);

            for (List<Long> group : userGroups) {
                // 批量查询已存在的歌手
                List<SingerInfoDTO> singerInfoList = singerInfoManager.getSingerInfoByUserIds(group, evnEnum.getAppId(), CollUtil.newArrayList(SingerStatusEnum.EFFECTIVE));
                if (CollUtil.isNotEmpty(singerInfoList)){
                    singerDecorateManager.generateAndInsertSingerDecorateFlowAndSendEvent(new SingerDecorateFlowInitParamDTO()
                            .setSingerInfoList(singerInfoList)
                            .setOperator(SingerDecorateFlowOperatorEnum.SYSTEM.getOperator())
                            .setAppId(evnEnum.getAppId())
                            .setReason(SingerDecorateOperateReasonConstant.MANUAL_RECOVER)
                            .setOperateType(SingerDecorateFlowOperateEnum.RECOVER)
                    );
                    count.addAndGet(singerInfoList.size());

                    singerInfoList.forEach(singer -> {
                        singer.setRewardsIssued(false);
                        singer.setModifyTime(new Date().getTime());
                        singerInfoMapper.updateByPrimaryKey(SingerInfoConvert.I.convertSingerInfo(singer));
                    });
                }
            }
        }

        return count.get();
    }

    /**
     * 发放
     */
    private int grant(Map<Integer, List<Long>> uidMap, BusinessEvnEnum evnEnum){

        AtomicInteger count = new AtomicInteger(0);

        for (Map.Entry<Integer, List<Long>> entry : uidMap.entrySet()) {
            Integer singerType = entry.getKey();
            List<Long> userIds = entry.getValue();

            if (CollUtil.isEmpty(userIds)) {
                continue;
            }

            // 校验歌手等级是否存在
            if (isInvalidSingerType(singerType, evnEnum)) {
                log.warn("无效的歌手等级: {}, appId:{}", singerType, evnEnum.getAppId());
                continue;
            }

            // 分组处理，每个分组最多处理20个用户
            List<List<Long>> userGroups = CollUtil.split(userIds, 20);

            for (List<Long> group : userGroups) {
                // 批量查询已存在的歌手
                List<SingerInfoDTO> unIssuedUserIds = singerInfoManager.getRewardsIssuedByUserIds(group, evnEnum.getAppId(), false);
                List<SingerInfoDTO> waitOperateDecorateSingerList = new ArrayList<>();

                for (SingerInfoDTO singer : unIssuedUserIds) {
                    List<SingerDecorateRuleBean> ruleList = singerDecorateRuleManager.getSingerDecorateRule(evnEnum.getAppId(),
                            SingerTypeEnum.getByType(singer.getSingerType()), singer.getSongStyle(), singer.getOriginalSinger());

                    if (CollUtil.isEmpty(ruleList)) {
                        log.warn("未找到对应的装饰规则, appId: {}, singerType: {}, songStyle: {}, userId:{}", evnEnum.getAppId(),
                                SingerTypeEnum.getByType(singer.getSingerType()), singer.getSongStyle(), singer.getUserId());
                        continue;
                    }

                    List<SingerDecorateFlowDTO> existFlowList = singerDecorateFlowManager.getDecorateFlowByUserIdAndRuleIds(singer.getUserId(), evnEnum.getAppId(),
                            ruleList.stream().map(SingerDecorateRuleBean::getId).collect(Collectors.toList()), SingerDecorateFlowOperateEnum.GRANT, false
                    );
                    // 只判断是否存在发放流水
                    if (CollUtil.isNotEmpty(existFlowList)) {
                        log.warn("已存在的装饰流水, appId: {}, singerType: {}, songStyle: {}, userId:{}", evnEnum.getAppId(),
                                SingerTypeEnum.getByType(singer.getSingerType()), singer.getSongStyle(), singer.getUserId());
                        continue;
                    }

                    // 加入待发放列表
                    waitOperateDecorateSingerList.add(singer);
                }


                if (CollUtil.isNotEmpty(waitOperateDecorateSingerList)){
                    singerDecorateManager.generateAndInsertSingerDecorateFlowAndSendEvent(new SingerDecorateFlowInitParamDTO()
                            .setSingerInfoList(waitOperateDecorateSingerList)
                            .setOperator(SingerDecorateFlowOperatorEnum.SYSTEM.getOperator())
                            .setAppId(evnEnum.getAppId())
                            .setReason(SingerDecorateOperateReasonConstant.MANUAL_GRANT)
                            .setOperateType(SingerDecorateFlowOperateEnum.GRANT)
                    );
                    count.addAndGet(waitOperateDecorateSingerList.size());
                }
            }
        }
        return count.get();
    }


    /**
     * 判断歌手等级是否正常
     */
    boolean isInvalidSingerType(Integer singerType, BusinessEvnEnum evnEnum) {
        String bizSingerType = SingerTypeMappingEnum.getBizSingerType(evnEnum, SingerTypeEnum.getByType(singerType));
        return StrUtil.isEmpty(bizSingerType);
    }

}
