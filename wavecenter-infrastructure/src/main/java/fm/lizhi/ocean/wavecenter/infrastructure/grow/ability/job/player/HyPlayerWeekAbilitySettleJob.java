package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.job.player;

import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.common.utils.BizClusterRouter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/6/6 11:48
 */
@Slf4j
@Component
public class HyPlayerWeekAbilitySettleJob extends AbstractPlayerWeekAbilitySettleJob implements JobHandler {

    @Override
    public void execute(JobExecuteContext jobExecuteContext) throws Exception {
        try {
            String paramJson = jobExecuteContext.getParam();
            log.info("HyPlayerWeekAbilitySettleJob start`param={}", paramJson);
            ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.HEI_YE);

            int shardingCnt = jobExecuteContext.getShardingCnt();
            int shardingId = jobExecuteContext.getShardingId();
            BizClusterRouter clusterRouter = new BizClusterRouter(shardingCnt, shardingId);
            if (!clusterRouter.route(BusinessEvnEnum.HEI_YE)) {
                log.info("HyPlayerWeekAbilitySettleJob not route");
                return;
            }
            log.info("sharding router pass");

            PlayerWeekAbilitySettleParam param = new PlayerWeekAbilitySettleParam();
            if (StringUtils.isNotBlank(paramJson)) {
                param = JsonUtil.loads(paramJson, PlayerWeekAbilitySettleParam.class);
            }

            super.doSettle(param);
        } catch (Exception e) {
            log.error("HyPlayerWeekAbilitySettleJob error", e);
        }
    }

}
