package fm.lizhi.ocean.wavecenter.infrastructure.sign.process;

import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataPlayerRoomWeek;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.WcDataRoomFamilyWeekIncomePo;
import fm.lizhi.ocean.wavecenter.base.processor.BusinessEnvAwareProcessor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface RoomProcessor extends BusinessEnvAwareProcessor {

    /**
     * 陪玩上周收入
     *
     * @param incomeList 收入列表
     * @return 结果
     */
    Map<Long, Long> playerWeekIncomeToMap(List<WcDataPlayerRoomWeek> incomeList);

    /**
     * 厅上周收入
     *
     * @param incomeList 收入列表
     * @return 结果
     */
    Map<Long, Long> roomsWeekIncomeToMap(List<WcDataRoomFamilyWeekIncomePo> incomeList);

    @Override
    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return RoomProcessor.class;
    }

}
