package fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 结算信息
 *
 * @date 2024-10-15 07:26:49
 */
@Table(name = "`settle`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PpSettle {
    /**
     * ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * contract表的id
     */
    @Column(name= "`contract_id`")
    private Long contractId;

    /**
     * 对公结算:PUBLIC  对私结算:PRIVATE
     */
    @Column(name= "`type`")
    private String type;

    /**
     * 家族抽成比例
     */
    @Column(name= "`percentage`")
    private Integer percentage;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", contractId=").append(contractId);
        sb.append(", type=").append(type);
        sb.append(", percentage=").append(percentage);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}