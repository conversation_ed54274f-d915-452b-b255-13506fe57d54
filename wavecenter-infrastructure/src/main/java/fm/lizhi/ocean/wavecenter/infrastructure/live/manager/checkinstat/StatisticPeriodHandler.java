package fm.lizhi.ocean.wavecenter.infrastructure.live.manager.checkinstat;

import fm.lizhi.ocean.wavecenter.api.live.constants.CheckInDateTypeEnum;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WaveCheckInDayMicRecord;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WaveCheckInUserTask;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 统计时间段处理器, 将时间段相关的操作封装到一个接口中, 以便于扩展.
 */
public interface StatisticPeriodHandler {

    /**
     * 是否支持处理指定的时间范围类型
     *
     * @param dateType 时间范围类型
     * @return 是否支持
     */
    boolean supports(CheckInDateTypeEnum dateType);

    /**
     * 根据开始时间和结束时间构造预设的统计时间段列表, 因为部分档期可能没有打卡记录, 但需要在后台展示, 因此需要构造本应有的时间段.
     *
     * @param startDate 开始时间, 包含
     * @param endDate   结束时间, 包含
     * @return 预设的统计时间段列表
     */
    List<StatisticPeriod> buildPresetStatisticPeriods(long startDate, long endDate);

    /**
     * 根据开始时间构造标准化的统计时间段, 用于统计数据的归类.
     *
     * @param startTime 开始时间
     * @return 标准化的统计时间段
     */
    StatisticPeriod buildNormalizedStatisticPeriod(long startTime);

    /**
     * 格式化统计时间段, 用于展示.
     *
     * @param period 统计时间段
     * @return 格式化后的时间段
     */
    String formatStatisticPeriod(StatisticPeriod period);

    /**
     * 根据日麦序奖励记录，按照主播维度进行统计
     *
     * @param dayMicRecords
     * @return key->主播用户ID, value->日麦序奖励统计结果
     */
    Map<Long, DayMicCounter> buildDayMicCounterMap(List<WaveCheckInDayMicRecord> dayMicRecords);


    /**
     * 根据大数据私信数据，获取对应的值
     *
     * @param playerIds
     * @return key->主播用户ID, value->私信统计结果(私信人数，私信回复率)
     */
    Map<Long, List<ChatStat>> buildPlayerChatStatMap(int appId, List<Long> playerIds, long statTime, long endDate);

    /**
     * 根据打卡记录获取用户任务明细
     *
     * @param recordIds
     * @return
     */
    List<WaveCheckInUserTask> buildWaveCheckInUserTaskList(List<Long> recordIds);

    Optional<SimpleUserDto> buildHostInfo(Map<Long, SimpleUserDto> simpleUserMap, List<Long> hostIds);
}
