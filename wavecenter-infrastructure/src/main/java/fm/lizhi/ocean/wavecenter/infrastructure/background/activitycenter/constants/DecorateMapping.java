package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.constants;

import fm.lizhi.hy.amusement.enm.DressUpType;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.DecorateEnum;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.xm.vip.bean.decorate.DecorateTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.EnumMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum DecorateMapping {

    /**
     * 头像框
     */
    AVATAR(DecorateEnum.AVATAR.getType(), new EnumMap<BusinessEvnEnum, Integer>(BusinessEvnEnum.class) {
        {
            put(BusinessEvnEnum.PP, fm.lizhi.pp.vip.constant.DecorateTypeEnum.AVATAR_WIDGET.getType());
            put(BusinessEvnEnum.XIMI, DecorateTypeEnum.AVATAR_WIDGET.getType());
            put(BusinessEvnEnum.HEI_YE, DressUpType.AVATAR_FRAME.getId());
        }
    }),

    /**
     * 背景
     */
    BACKGROUND(DecorateEnum.BACKGROUND.getType(), new EnumMap<BusinessEvnEnum, Integer>(BusinessEvnEnum.class) {
        {
            put(BusinessEvnEnum.PP, fm.lizhi.pp.vip.constant.DecorateTypeEnum.BACKGROUND.getType());
            put(BusinessEvnEnum.XIMI, DecorateTypeEnum.BACKGROUD.getType());
            put(BusinessEvnEnum.HEI_YE, DressUpType.ROOM_BG.getId());
        }
    }),

    /**
     * 勋章
     */
    MEDAL(DecorateEnum.MEDAL.getType(), new EnumMap<BusinessEvnEnum, Integer>(BusinessEvnEnum.class) {
        {
            // PP 不支持勋章
            put(BusinessEvnEnum.PP, -1);
            put(BusinessEvnEnum.XIMI, DecorateTypeEnum.MEDAL.getType());
            put(BusinessEvnEnum.HEI_YE, DressUpType.MEDAL.getId());
        }
    }),

    /**
     * 官方认证
     */
    USER_GLORY(DecorateEnum.USER_GLORY.getType(), new EnumMap<BusinessEvnEnum, Integer>(BusinessEvnEnum.class) {
        {
            put(BusinessEvnEnum.PP, fm.lizhi.pp.vip.constant.DecorateTypeEnum.USER_GLORY.getType());
            // xm 不支持官方认证
            put(BusinessEvnEnum.XIMI, -1);
            // hy 没有官方认证标签类型，直接用平台的转换即可
            put(BusinessEvnEnum.HEI_YE, DecorateEnum.USER_GLORY.getType());
        }
    }),

    /**
     * 气泡
     */
    BUBBLE(DecorateEnum.BUBBLE.getType(), new EnumMap<BusinessEvnEnum, Integer>(BusinessEvnEnum.class) {
        {
            put(BusinessEvnEnum.PP, fm.lizhi.pp.vip.constant.DecorateTypeEnum.BUBBLE.getType());
            // xm 暂不开放使用
            put(BusinessEvnEnum.XIMI, -1);
            // hy 暂不开放使用
            put(BusinessEvnEnum.HEI_YE, -1);
        }
    }),


    ;

    private final Integer waveType;

    private final EnumMap<BusinessEvnEnum, Integer> bizValueMap;

    public static Integer bizValue2WaveType(Integer bizType, BusinessEvnEnum businessEvnEnum) {
        DecorateMapping[] values = values();
        for (DecorateMapping value : values) {
            Map<BusinessEvnEnum, Integer> bizMap = value.getBizValueMap();
            Integer mBizValue = bizMap.get(businessEvnEnum);
            if (mBizValue != null && mBizValue.equals(bizType)) {
                return value.getWaveType();
            }
        }
        return -1;
    }


    public static Integer waveType2Biz(int waveType, BusinessEvnEnum businessEvnEnum) {
        for (DecorateMapping enumItem : values()) {
            if (enumItem.getWaveType() == waveType) {
                return enumItem.bizValueMap.get(businessEvnEnum);
            }
        }
        return -1;
    }
}
