package fm.lizhi.ocean.wavecenter.infrastructure.user.convert;

import fm.lizhi.hy.user.account.user.protocol.HyUserBaseProto;
import fm.lizhi.hy.user.account.verify.protocol.HYUserVerifyProxyProto;
import fm.lizhi.ocean.wave.user.export.api.model.UserInfo;
import fm.lizhi.ocean.wave.user.export.protocol.UserServiceProto;
import fm.lizhi.ocean.wavecenter.api.user.bean.PlayerSignBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.RoomSignBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity.*;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.req.GetUserWithdrawStatusReq;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.res.GetUserVerifyDataRes;
import fm.lizhi.ocean.wavecenter.api.user.constants.SingStatusEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.res.GetUserWithdrawStatusRes;
import fm.lizhi.ocean.wavecenter.service.user.dto.*;
import fm.lizhi.pp.user.account.user.protocol.PpUserBaseProto;
import fm.lizhi.pp.user.account.verify.protocol.PpUserVerifyProxyProto;
import fm.lizhi.trade.contract.constant.ContractType;
import fm.lizhi.xm.user.account.tag.protocol.UserVerifyProxyProto;
import fm.lizhi.xm.user.account.user.protocol.XmUserBaseProto;
import fm.pp.family.bean.ContractInfo;
import fm.pp.family.bean.player.sign.PlayerSignInfo;
import fm.pp.family.constants.FamilyConstant;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/10 15:30
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface UserConvert {

    UserConvert I = Mappers.getMapper(UserConvert.class);

    GetUserVerifyDataRes verifyPb2Res(PpUserVerifyProxyProto.UserVerifyResult pb);

    UserInfoDto waveUserPb2UserInfoDto(UserServiceProto.UserInfo pb);

    UserInfoDto waveUserPojo2UserInfoDto(UserInfo pojo);

    @Mappings({
            @Mapping(source = "avatar", target = "photo")
    })
    UserBean simpleDto2Bean(SimpleUserDto dto);

    @Mappings({
            @Mapping(source = "portrait", target = "avatar")
    })
    SimpleUserDto userPb2SimpleDto(PpUserBaseProto.User pb);

    @Mappings({
            @Mapping(source = "portrait", target = "avatar")
    })
    SimpleUserDto hyUserPb2SimpleDto(HyUserBaseProto.User pb);


    @Mappings({
            @Mapping(source = "portrait", target = "avatar")
    })
    SimpleUserDto xmUserPb2SimpleDto(XmUserBaseProto.User pb);

    List<SimpleUserDto> userPbs2SimpleDtos(List<PpUserBaseProto.User> pbs);

    @Mappings({
            @Mapping(source = "njId", target = "id"),
            @Mapping(source = "po", target = "signStatus", qualifiedByName = "ppTransNjSignStatus"),
    })
    RoomSignBean ppContractPo2RoomSignBean(PpContract po);


    @Mappings({
            @Mapping(source = "njId", target = "id"),
            @Mapping(source = "po", target = "signStatus", qualifiedByName = "xmTransNjSignStatus"),
    })
    RoomSignBean xmContractPo2RoomSignBean(XmContract po);

    @Mappings({
            @Mapping(source = "njId", target = "id"),
            @Mapping(source = "po", target = "signStatus", qualifiedByName = "hyTransNjSignStatus"),
    })
    RoomSignBean hyContractPo2RoomSignBean(HyContract po);

    List<RoomSignBean> ppContractPos2RoomSignBeans(List<PpContract> pos);

    List<RoomSignBean> hyContractPos2RoomSignBeans(List<HyContract> pos);

    List<RoomSignBean> xmContractPos2RoomSignBeans(List<XmContract> pos);

    @Mappings({
            @Mapping(source = "njId", target = "id"),
            @Mapping(source = "pb", target = "signStatus", qualifiedByName = "transNjSignStatus"),
    })
    RoomSignBean contractPb2RoomSignBean(ContractInfo pb);

    List<RoomSignBean> contractPbs2RoomSignBeans(List<ContractInfo> pbs);

    @Mappings({
            @Mapping(source = "userId", target = "id"),
            @Mapping(source = "njId", target = "roomId"),
            @Mapping(source = "po", target = "signStatus", qualifiedByName = "ppTransPlayerSignStatus"),
    })
    PlayerSignBean ppPlayerSignPo2PlayerSignBean(PpPlayerSign po);

    @Mappings({
            @Mapping(source = "userId", target = "id"),
            @Mapping(source = "njId", target = "roomId"),
            @Mapping(source = "po", target = "signStatus", qualifiedByName = "hyTransPlayerSignStatus"),
    })
    PlayerSignBean hyPlayerSignPo2PlayerSignBean(HyPlayerSign po);

    @Mappings({
            @Mapping(source = "userId", target = "id"),
            @Mapping(source = "njId", target = "roomId"),
            @Mapping(source = "po", target = "signStatus", qualifiedByName = "xmTransPlayerSignStatus"),
    })
    PlayerSignBean xmPlayerSignPo2PlayerSignBean(XmPlayerSign po);


    List<PlayerSignBean> ppPlayerSignPos2PlayerSignBeans(List<PpPlayerSign> pos);

    List<PlayerSignBean> hyPlayerSignPos2PlayerSignBeans(List<HyPlayerSign> pos);

    List<PlayerSignBean> xmPlayerSignPos2PlayerSignBeans(List<XmPlayerSign> pos);

    @Mappings({
            @Mapping(source = "userId", target = "id"),
            @Mapping(source = "pb", target = "signStatus", qualifiedByName = "transPlayerSignStatus"),
    })
    PlayerSignBean playerSignInfo2PlayerSignBean(PlayerSignInfo pb);

    List<PlayerSignBean> playerSignInfos2PlayerSignBeans(List<PlayerSignInfo> pbs);

    @Named("transNjSignStatus")
    default Integer transNjSignStatus(ContractInfo pb) {
        if (pb == null) {
            return null;
        }
        String type = pb.getType();
        String njSignStatus = pb.getStatus();
        //类型(签约、续约、主体变更)+状态（SIGN_SUCCEED）
        if ((StringUtils.equals(type, ContractType.SIGN.getCode())
                || StringUtils.equals(type, ContractType.RENEW.getCode())
                || StringUtils.equals(type, ContractType.SUBJECT_CHANGE.getCode()))
                && StringUtils.equals(njSignStatus, FamilyConstant.ContractStatus.SIGN_SUCCEED.getCode())
        ) {
            return SingStatusEnum.SING.getValue();
        }
        return SingStatusEnum.STOP.getValue();
    }


    @Named("hyTransNjSignStatus")
    default Integer hyTransNjSignStatus(HyContract po) {
        if (po == null) {
            return null;
        }
        String type = po.getType();
        String njSignStatus = po.getStatus();
        //类型(签约、续约、主体变更)+状态（SIGN_SUCCEED）
        if ((StringUtils.equals(type, ContractType.SIGN.getCode())
                || StringUtils.equals(type, ContractType.RENEW.getCode())
                || StringUtils.equals(type, ContractType.SUBJECT_CHANGE.getCode()))
                && StringUtils.equals(njSignStatus, FamilyConstant.ContractStatus.SIGN_SUCCEED.getCode())
        ) {
            return SingStatusEnum.SING.getValue();
        }
        return SingStatusEnum.STOP.getValue();
    }

    @Named("xmTransNjSignStatus")
    default Integer xmTransNjSignStatus(XmContract po) {
        if (po == null) {
            return null;
        }
        String type = po.getType();
        String njSignStatus = po.getStatus();
        //类型(签约、续约、主体变更)+状态（SIGN_SUCCEED）
        if ((StringUtils.equals(type, ContractType.SIGN.getCode())
                || StringUtils.equals(type, ContractType.RENEW.getCode())
                || StringUtils.equals(type, ContractType.SUBJECT_CHANGE.getCode()))
                && StringUtils.equals(njSignStatus, FamilyConstant.ContractStatus.SIGN_SUCCEED.getCode())
        ) {
            return SingStatusEnum.SING.getValue();
        }
        return SingStatusEnum.STOP.getValue();
    }


    @Named("ppTransNjSignStatus")
    default Integer ppTransNjSignStatus(PpContract po) {
        if (po == null) {
            return null;
        }
        String type = po.getType();
        String njSignStatus = po.getStatus();
        //类型(签约、续约、主体变更)+状态（SIGN_SUCCEED）
        if ((StringUtils.equals(type, ContractType.SIGN.getCode())
                || StringUtils.equals(type, ContractType.RENEW.getCode())
                || StringUtils.equals(type, ContractType.SUBJECT_CHANGE.getCode()))
                && StringUtils.equals(njSignStatus, FamilyConstant.ContractStatus.SIGN_SUCCEED.getCode())
        ) {
            return SingStatusEnum.SING.getValue();
        }
        return SingStatusEnum.STOP.getValue();
    }

    @Named("transPlayerSignStatus")
    default Integer transPlayerSignStatus(PlayerSignInfo pb) {
        if (pb == null) {
            return null;
        }
        String type = pb.getType();
        String status = pb.getStatus();
        if (StringUtils.equals(type, "SIGN") && StringUtils.equals(status, "SIGN_SUCCEED")
        ) {
            return SingStatusEnum.SING.getValue();
        }
        return SingStatusEnum.STOP.getValue();
    }

    @Named("ppTransPlayerSignStatus")
    default Integer ppTransPlayerSignStatus(PpPlayerSign po) {
        if (po == null) {
            return null;
        }
        String type = po.getType();
        String status = po.getStatus();
        if (StringUtils.equals(type, "SIGN") && StringUtils.equals(status, "SIGN_SUCCEED")
        ) {
            return SingStatusEnum.SING.getValue();
        }
        return SingStatusEnum.STOP.getValue();
    }

    @Named("xmTransPlayerSignStatus")
    default Integer xmTransPlayerSignStatus(XmPlayerSign po) {
        if (po == null) {
            return null;
        }
        String type = po.getType();
        String status = po.getStatus();
        if (StringUtils.equals(type, "SIGN") && StringUtils.equals(status, "SIGN_SUCCEED")
        ) {
            return SingStatusEnum.SING.getValue();
        }
        return SingStatusEnum.STOP.getValue();
    }

    @Named("hyTransPlayerSignStatus")
    default Integer hyTransPlayerSignStatus(HyPlayerSign po) {
        if (po == null) {
            return null;
        }
        String type = po.getType();
        String status = po.getStatus();
        if (StringUtils.equals(type, "SIGN") && StringUtils.equals(status, "SIGN_SUCCEED")
        ) {
            return SingStatusEnum.SING.getValue();
        }
        return SingStatusEnum.STOP.getValue();
    }


    PlayerSignInfoDto ppPlayerSign2PlayerSignInfoDto(PpPlayerSign po);

    PlayerSignInfoDto xmPlayerSign2PlayerSignInfoDto(XmPlayerSign po);

    PlayerSignInfoDto hyPlayerSign2PlayerSignInfoDto(HyPlayerSign po);

    GetUserVerifyDataRes xmVerifyPb2Res(UserVerifyProxyProto.UserVerifyResult pb);

    GetUserVerifyDataRes hyVerifyPb2Res(HYUserVerifyProxyProto.UserVerifyResult pb);

    UserInfoDto xmUserInfoPb2InfoDTO(XmUserBaseProto.User pb);

    UserVerifyDataDTO verifyRes2DTO(GetUserVerifyDataRes res);

    UserWithdrawStatusDTO convertWithdrawStatusRes2DTO(GetUserWithdrawStatusRes res);

}
