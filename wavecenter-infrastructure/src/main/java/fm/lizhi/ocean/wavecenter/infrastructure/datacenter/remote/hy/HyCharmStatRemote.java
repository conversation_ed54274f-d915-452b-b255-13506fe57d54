package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote.hy;

import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.LiveGiveGiftActionPo;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.PlayerSignCharmSumPo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.HyLiveGiveGiftActionMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.HyPlayerSignCharmStatMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.AdminPlayerIncomeInfoPo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.CharmStatPo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote.ICharmStatRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.live.dto.LiveDto;
import fm.lizhi.ocean.wavecenter.infrastructure.live.remote.ILiveRemote;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/24 16:05
 */
@Slf4j
@Component
public class HyCharmStatRemote implements ICharmStatRemote {

    @Autowired
    private HyLiveGiveGiftActionMapper liveGiveGiftActionMapper;
    @Autowired
    private ILiveRemote iLiveRemote;
    @Autowired
    private HyPlayerSignCharmStatMapper playerSignCharmStatMapper;

    @Override
    public List<PlayerSignCharmSumPo> selectPlayerSignCharmSumByUsers(long njId, long familyId, List<Long> userIds, Date startDate, Date endDate) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        String startDateStr = DateUtil.formatDateToString(startDate, DateUtil.date_2);
        String endDateStr = DateUtil.formatDateToString(endDate, DateUtil.date_2);
        return playerSignCharmStatMapper.selectPlayerSignCharmSumByUsers(njId, userIds, startDateStr, endDateStr);
    }

    @Override
    public Integer getRoomIncomeCharm(List<Long> roomIds, long playerId, Date startDate, Date endDate) {
        return liveGiveGiftActionMapper.getRoomIncomeCharm(roomIds, playerId, startDate, endDate);
    }

    @Override
    public Integer getPersonIncomeCharm(long playerId, Date startDate, Date endDate) {
        //个人开播记录
        List<LiveGiveGiftActionPo> liveGiftList = liveGiveGiftActionMapper.getPersonIncomeCharmList(playerId, startDate, endDate);
        if (CollectionUtils.isEmpty(liveGiftList)) {
            return 0;
        }

        //过滤掉非个播类型的记录
        Set<Long> liveIds = liveGiftList.stream().map(LiveGiveGiftActionPo::getLiveId).collect(Collectors.toSet());
        List<LiveDto> lives = iLiveRemote.getLiveByIds(new ArrayList<>(liveIds));
        Map<Long, Integer> roomTypeMap = lives.stream().collect(Collectors.toMap(LiveDto::getId, LiveDto::getRoomType));
        return liveGiftList.stream().filter(v->{
            Integer roomType = roomTypeMap.get(v.getLiveId());
            if (roomType == null) {
                return false;
            }
            //黑叶4表示个播类型
            return roomType.equals(4);
        }).map(LiveGiveGiftActionPo::getValue).reduce(0, Integer::sum);
    }

    @Override
    public Map<Long, AdminPlayerIncomeInfoPo> getAdminPlayerIncomeInfoMap(List<Long> njIds, Date startDate, Date endDate) {
        String startDateStr = DateUtil.formatDateToString(startDate, DateUtil.date_2);
        String endDateStr = DateUtil.formatDateToString(endDate, DateUtil.date_2);
        List<CharmStatPo> countList = playerSignCharmStatMapper.getRoomPlayerCharmByRooms(njIds, startDateStr, endDateStr);
        if (CollectionUtils.isEmpty(countList)) {
            log.info("hy getAdminPlayerIncomeInfoMap countList is empty");
            return Collections.emptyMap();
        }

        Map<Long, List<CharmStatPo>> groupMap = countList.stream().collect(Collectors.groupingBy(CharmStatPo::getNjId));
        //按照厅汇总魅力值返回
        Map<Long, AdminPlayerIncomeInfoPo> result = new HashMap<>();
        for (Map.Entry<Long, List<CharmStatPo>> entry : groupMap.entrySet()) {
            List<CharmStatPo> value = entry.getValue();
            if (CollectionUtils.isEmpty(value)) {
                continue;
            }
            Long sumValue = value.stream().map(CharmStatPo::getValue).reduce(0L, Long::sum);
            result.put(entry.getKey(), new AdminPlayerIncomeInfoPo().setNjId(entry.getKey()).setTotalCharmValue(sumValue));
        }
        return result;
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.HEI_YE;
    }
}
