package fm.lizhi.ocean.wavecenter.infrastructure.live.manager;

import fm.lizhi.ocean.wavecenter.api.live.bean.GuildRoomDayStats;
import fm.lizhi.ocean.wavecenter.api.live.bean.GuildRoomDayStatsSummaryRes;
import fm.lizhi.ocean.wavecenter.api.live.bean.RoomDayStatsSummaryRes;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.RoomDayCalendarEntity;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.RoomDayStatsDetailEntity;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.RoomDayStatsEntity;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcRoomCheckInDayStats;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.po.RoomCheckPlayerPo;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/21 17:51
 */
@Deprecated
public interface ICheckInDayRouter {

    /**
     * 获取时间范围中 所有的厅的id
     * @param appId
     * @param familyId
     * @param njId
     * @param startDate
     * @param endDate
     * @return
     */
    List<Long> guildRoomDayAllNjId(Integer appId, Long familyId, Long njId, Date startDate, Date endDate);

    /**
     * 厅的日历汇总
     * @param appId
     * @param njId
     * @param startDate
     * @param endDate
     * @return
     */
    List<RoomDayCalendarEntity> roomCalendar(Integer appId, Long njId, Date startDate, Date endDate);

    /**
     * 厅的日汇总
     * @param appId
     * @param njId
     * @param startDate
     * @param endDate
     * @return
     */
    RoomDayStatsSummaryRes roomDaySummary(Integer appId, Long njId, Date startDate, Date endDate);

    /**
     * 厅的日统计
     * @param appId
     * @param familyId
     * @param njId
     * @param startDate
     * @param endDate
     * @return
     */
    List<WcRoomCheckInDayStats> guildRoomDayDetail(Integer appId, Long familyId, Long njId, Date startDate, Date endDate);

    /**
     * 厅的日统计
     * @param appId
     * @param familyId
     * @param njId
     * @param startDate
     * @param endDate
     * @return
     */
    List<GuildRoomDayStats> guildRoomDayStats(Integer appId, Long familyId, Long njId, Date startDate, Date endDate);

    /**
     * 查询打卡主播列表
     * @param appId
     * @param familyId
     * @param njId
     * @param startDate
     * @param endDate
     * @return
     */
    List<RoomCheckPlayerPo> guildRoomDayDetailCheckPlayer(Integer appId, Long familyId, Long njId, Date startDate, Date endDate);

    /**
     * 厅的日汇总
     * @param appId
     * @param familyId
     * @param njId
     * @param startDate
     * @param endDate
     * @return
     */
    GuildRoomDayStatsSummaryRes guildRoomDaySummary(Integer appId, Long familyId, Long njId, Date startDate, Date endDate);

    /**
     * 厅的日统计
     * @param appId
     * @param njId
     * @param userId
     * @param startDate
     * @param endDate
     * @return
     */
    List<Long> roomDayStatsUserId(Integer appId, Long njId, Long userId, Date startDate, Date endDate);

    /**
     * 厅的日统计
     * @param appId
     * @param njId
     * @param userId
     * @param startDate
     * @param endDate
     * @return
     */
    List<RoomDayStatsDetailEntity> roomDayStatsDetail(Integer appId, Long njId, Long userId, Date startDate, Date endDate);

    /**
     * 厅的日统计
     * @param appId
     * @param njId
     * @param userId
     * @param startDate
     * @param endDate
     * @return
     */
    List<RoomDayStatsEntity> roomDayStats(Integer appId, Long njId, Long userId, Date startDate, Date endDate);


}
