package fm.lizhi.ocean.wavecenter.infrastructure.kafka.consumer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import fm.lizhi.common.kafka.common.strategy.AutoOffsetReset;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaHandler;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaListener;
import fm.lizhi.common.kafka.ioc.api.strategy.annotation.AutoOffsetResetStrategy;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.message.constant.MessageTargetLinkEnum;
import fm.lizhi.ocean.wavecenter.api.message.constant.MessageTypeEnum;
import fm.lizhi.ocean.wavecenter.api.message.request.RequestSendMessage;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.bean.FamilyAndNjContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.common.utils.KafkaMsgUtils;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerInfoManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerVerifyApplyManager;
import fm.lizhi.ocean.wavecenter.service.message.manager.WaveCenterMessageManager;
import fm.lizhi.ocean.wavecenter.service.permissions.handler.RoleHandler;
import fm.lizhi.ocean.wavecenter.service.sign.dto.RequestFamilyAndNjContractDTO;
import fm.lizhi.ocean.wavecenter.service.sign.manager.ContractManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import fm.lizhi.xm.family.bean.player.sign.AdminSignMsg;
import fm.lizhi.xm.family.bean.player.sign.PlayerSignMsg;
import fm.lizhi.xm.family.bean.player.sign.PlayerSignMsgEvent;
import fm.lizhi.xm.family.constants.UserType;
import fm.lizhi.xm.family.constants.WaveSignConstant;
import fm.pp.family.constants.WaveSignType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/22 17:35
 */
@Slf4j
@Component
@KafkaListener(clusterNamespace = "xm-kafka250-bootstrap-server")
public class XmSignMsgConsumer {

    @Autowired
    private WaveCenterMessageManager waveCenterMessageManager;
    @Autowired
    private UserManager userManager;
    @Autowired
    private FamilyManager familyManager;
    @Autowired
    private RoleHandler roleHandler;
    @Autowired
    private ContractManager contractManager;
    @Autowired
    private SingerInfoManager singerInfoManager;
    @Autowired
    private SingerVerifyApplyManager singerVerifyApplyManager;


    private static final Map<String, String> ADMIN_SIGN_MSG_CONTENT = MapUtil.<String, String>builder()
            .put(WaveSignConstant.NJ_REC.name(), "你收到来自「#familyName」家族的签约邀请，请您于「#signDeadline」前完成合同签署。")
            .put(WaveSignConstant.NJ_OVERDUE.name(), "「#njName」未在有限期内完成签约合同签署，本次签约邀请已失效，请双方沟通后再发起邀请。")
            .put(WaveSignConstant.NJ_REJECT.name(), "「#njName」拒绝你的签约邀请，本次邀请已失效，请双方沟通后再发起邀请。")
            .put(WaveSignConstant.NJ_SUCC.name(), "「#njName」已完成签约合同签署，请您于「#signDeadline」前完成合同签署。")
            .put(WaveSignConstant.SUCC.name(), "「#njName」与「#familyName」家族已达成签约关系，签约期限为#year年，有效期至「#expireTime」")
            .put(WaveSignConstant.REC_CANCEL_NJ.name(), "您收到 【#familyName】 家族长的解约申请，请及时处理。")
            .put(WaveSignConstant.REC_CANCEL_FAMILY.name(), "您收到房间管理员 【#njName】 的解约申请，请及时处理。")
            .put(WaveSignConstant.SUCC_NJ_PLAYER.name(), "您已退出 【#njName】 房间， 【#familyName】 家族")
            .put(WaveSignConstant.REC_REJECT_NJ.name(), "您与 【#familyName】 家族的解约申请未被通过，请重新提交。")
            .put(WaveSignConstant.REC_REJECT_FAMILY.name(), "你与房间管理员 【#njName】 的解约申请未被通过，请重新提交。")
            .put(WaveSignConstant.REC_OVERDUE_NJ.name(), "您与 【#familyName】 家族的解约申请未被通过，请重新提交。")
            .put(WaveSignConstant.REC_OVERDUE_FAMILY.name(), "你与房间管理员 【管理员昵称】 的解约申请未被通过，请重新提交。")
            .put(WaveSignConstant.CANCEL_SUCC.name(), "您的家族签约已解除，目前您已退出 【#familyName】 家族")
            .put(WaveSignConstant.ADMIN_RECEIVE_APPLY.name(), "您收到了一条房间管理员加入申请，请及时处理。")
            .put(WaveSignConstant.ADMIN_PASS.name(), "您的申请已通过，您已成为家族的房间管理员。")
            .put(WaveSignConstant.ADMIN_NOT_PASS.name(), "您的申请未被接受，请重新提交申请。")
            .build();



    /**
     * 陪玩签约消息消费
     * @param body
     */
    @KafkaHandler(topic = "xm_topic_player_sign_event", group = "xm_topic_player_sign_event_wave_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handlePlayerSignMsg(String body){
        String msg = null;

        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            log.info("xm.handlePlayerSignMsg msg={}", msg);
            PlayerSignMsg playerSignMsg = JsonUtil.loads(msg, PlayerSignMsg.class);
            ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.XIMI);

            Pair<String, Long> contentAndTarget = genContentAndTarget(playerSignMsg);

            RequestSendMessage request = new RequestSendMessage()
                    .setType(MessageTypeEnum.NOTIFY.getType())
                    .setAppId(BusinessEvnEnum.XIMI.getAppId())
                    .setTitle(genTitle(playerSignMsg))
                    .setContent(contentAndTarget.getKey())
                    .setTargetUserId(contentAndTarget.getValue())
                    .setSendUserId(1L)
                    .setLinkType(MessageTargetLinkEnum.SIGN_MANAGEMENT.name())
                    .setBizId(playerSignMsg.getPlayerSignId());
            waveCenterMessageManager.sendMessage(request);

            //解约成功后置处理
            if (PlayerSignMsgEvent.DO_CANCEL.getCode().equals(playerSignMsg.getMsgEvent())) {
                //解除授权数据
                UserInFamilyBean userInFamily = familyManager.getUserInFamily(playerSignMsg.getNjId());
                Long familyId = userInFamily.getFamilyId();
                roleHandler.removePlayerWithFamilyAuth(familyId, playerSignMsg.getPlayerId());
                // 添加歌手淘汰标记
                singerInfoManager.addEliminateSingerTag(BusinessEvnEnum.XIMI.getAppId(), playerSignMsg.getPlayerId());
                singerVerifyApplyManager.updateSingerSignInfo(BusinessEvnEnum.XIMI.getAppId(), playerSignMsg.getPlayerId());
            } else if (PlayerSignMsgEvent.DO_SIGN.getCode().equals(playerSignMsg.getMsgEvent())) {
                singerVerifyApplyManager.updateSingerSignInfo(BusinessEvnEnum.XIMI.getAppId(), playerSignMsg.getPlayerId());
            }

        } catch (Exception e) {
            log.error("handlePlayerSignMsg error, msg:{}, orgMsg:{}", msg, body, e);
        }
    }

    private ContractTypeEnum genContractType(PlayerSignMsg playerSignMsg){
        String msgEvent = playerSignMsg.getMsgEvent();
        if (PlayerSignMsgEvent.DO_SIGN.getCode().equals(msgEvent)
                || PlayerSignMsgEvent.INVITE_SIGN.getCode().equals(msgEvent)) {
            return ContractTypeEnum.SIGN;
        }
        else {
            return ContractTypeEnum.CANCEL;
        }
    }

    private String genTitle(PlayerSignMsg playerSignMsg){
        ContractTypeEnum contractTypeEnum = genContractType(playerSignMsg);
        if (contractTypeEnum == ContractTypeEnum.SIGN) {
            return "签约申请";
        }
        else {
            return "解约申请";
        }
    }

    private Pair<String, Long> genContentAndTarget(PlayerSignMsg playerSignMsg){
        String msgEvent = playerSignMsg.getMsgEvent();
        RoleEnum userType = genUserType(playerSignMsg);

        if (PlayerSignMsgEvent.INVITE_SIGN.getCode().equals(msgEvent)) {
            if (userType == RoleEnum.ROOM) {
                return Pair.of("您收到了一条加入房间邀请，请及时处理", playerSignMsg.getPlayerId());
            } else {
                return Pair.of("您收到了一条加入房间申请，请及时处理", playerSignMsg.getNjId());
            }
        }

        if (PlayerSignMsgEvent.DO_SIGN.getCode().equals(msgEvent)) {
            if (userType == RoleEnum.ROOM) {
                String njName = getUserName(playerSignMsg.getNjId());
                return Pair.of(String.format("您已成功加入家族，您的管理员是%s", njName), playerSignMsg.getPlayerId());
            } else {
                String playerName = getUserName(playerSignMsg.getPlayerId());
                return Pair.of(String.format("您已成功邀请%s加入了家族", playerName), playerSignMsg.getNjId());
            }
        }

        if (PlayerSignMsgEvent.INVITE_CANCEL.getCode().equals(msgEvent)) {
            if (userType == RoleEnum.ROOM) {
                return Pair.of("您收到了一条成员解约申请，请及时处理", playerSignMsg.getNjId());
            } else {
                return Pair.of("您收到了一条解约申请，请及时处理", playerSignMsg.getPlayerId());
            }
        }

        if (PlayerSignMsgEvent.DO_CANCEL.getCode().equals(msgEvent)) {
            if (userType == RoleEnum.ROOM) {
                return Pair.of("您已成功退出家族", playerSignMsg.getPlayerId());
            } else {
                String name = getUserName(playerSignMsg.getPlayerId());
                return Pair.of(String.format("%s已退出家族", name), playerSignMsg.getNjId());
            }
        }

        return Pair.of("", 0L);
    }

    private String getUserName(Long userId){
        List<SimpleUserDto> userList = userManager.getSimpleUserByIds(Lists.newArrayList(userId));
        if (CollectionUtils.isEmpty(userList)) {
            return "";
        }
        return userList.get(0).getName();
    }

    private RoleEnum genUserType(PlayerSignMsg playerSignMsg){
        String userType = playerSignMsg.getUserType();
        if (UserType.PLAYER.name().equals(userType)) {
            return RoleEnum.PLAYER;
        }
        if (UserType.FAMILY.name().equals(userType)) {
            return RoleEnum.FAMILY;
        }
        if (UserType.ADMIN.name().equals(userType)) {
            return RoleEnum.ROOM;
        }
        return RoleEnum.USER;
    }

    /**
     * 管理员签约消息消费
     * @param body
     */
    @KafkaHandler(topic = "xm_topic_admin_sign_event", group = "xm_topic_admin_sign_event_wave_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleAdminSignMsg(String body){
        String msg = null;

        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            log.info("xm.handleAdminSignMsg msg={}", msg);

            AdminSignMsg adminSignMsg = JsonUtil.loads(msg, AdminSignMsg.class);
            ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.XIMI);

            waveCenterMessageManager.sendMessage(new RequestSendMessage()
                    .setTargetUserId(adminSignMsg.getTargetUserId())
                    .setBizId(adminSignMsg.getSignId())
                    .setContent(genAdminSignMessageContent(adminSignMsg))
                    .setTitle(genAdminSignMessageTitle(adminSignMsg))
                    .setAppId(BusinessEvnEnum.XIMI.getAppId())
                    .setType(MessageTypeEnum.NOTIFY.getType())
                    .setSendUserId(1L)
                    .setLinkType(MessageTargetLinkEnum.SIGN_MANAGEMENT.name())
            );
            //解约成功后置处理
            if (WaveSignConstant.CANCEL_SUCC.name().equals(adminSignMsg.getMsgType())) {
                //解除授权
                PageBean<FamilyAndNjContractBean> pageBean = contractManager.queryContract(RequestFamilyAndNjContractDTO.builder().contractId(adminSignMsg.getSignId()).build());
                if (CollectionUtils.isNotEmpty(pageBean.getList())) {
                    FamilyAndNjContractBean contract = pageBean.getList().get(0);
                    roleHandler.removeRoomWithFamilyAuth(contract.getFamilyId(), contract.getNjUserId());
                }
            }

        } catch (Exception e) {
            log.error("handleAdminSignMsg error, msg:{}, orgMsg:{}", msg, body, e);
        }
    }


    private String genAdminSignMessageTitle(AdminSignMsg adminSignMsg){
        String msgType = adminSignMsg.getMsgType();
        try {
            WaveSignConstant signConstant = WaveSignConstant.valueOf(msgType);
            return signConstant.getType() == WaveSignType.SIGN ? "签约通知" : "解约通知";
        }catch (Exception e){
            log.error("xm genAdminSignMessageTitle error, msgType:{}", msgType, e);
            return "";
        }
    }


    private String genAdminSignMessageContent(AdminSignMsg adminSignMsg){

        String msgContent = ADMIN_SIGN_MSG_CONTENT.get(adminSignMsg.getMsgType());
        if (StrUtil.isBlank(msgContent)){
            return "";
        }

        Map<String, String> msgParams = adminSignMsg.getMsgParams();
        if (CollUtil.isEmpty(msgParams)){
            return msgContent;
        }

        for (String param : msgParams.keySet()) {
            msgContent = msgContent.replace("#"+param, msgParams.get(param));
        }

        return msgContent;
    }



}
