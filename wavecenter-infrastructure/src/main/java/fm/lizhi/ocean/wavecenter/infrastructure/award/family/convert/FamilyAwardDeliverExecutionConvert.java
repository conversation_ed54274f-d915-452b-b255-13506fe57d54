package fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardDeliverExecutionStatusEnum;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardResourceDeliverTypeEnum;
import fm.lizhi.ocean.wavecenter.base.convert.CommonConvert;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilyAwardDeliverExecution;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.param.CreateFamilyAwardDeliverExecutionParam;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.param.CreateFamilyAwardDeliverItemParam;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR,
        uses = {
                CommonConvert.class,
        },
        imports = {
                ConfigUtils.class,
                Date.class,
                UrlUtils.class,
                FamilyAwardDeliverExecutionStatusEnum.class,
        }
)
public interface FamilyAwardDeliverExecutionConvert {

    FamilyAwardDeliverExecutionConvert I = Mappers.getMapper(FamilyAwardDeliverExecutionConvert.class);

    @Mapping(target = "resourceDeliverType", expression = "java(deliverItem.getResourceType().getDeliverType())")
    @Mapping(target = "deliverItems", source = "deliverItem", qualifiedByName = "singletonDeliverItems")
    CreateFamilyAwardDeliverExecutionParam toCreateParam(CreateFamilyAwardDeliverItemParam deliverItem);

    @Named("singletonDeliverItems")
    default List<CreateFamilyAwardDeliverItemParam> singletonDeliverItems(CreateFamilyAwardDeliverItemParam deliverItem) {
        return Collections.singletonList(deliverItem);
    }

    CreateFamilyAwardDeliverExecutionParam toCreateParam(
            FamilyAwardResourceDeliverTypeEnum resourceDeliverType, int resourceNumber, int resourceValidPeriod,
            long resourceId, String resourceName, String resourceImage, List<CreateFamilyAwardDeliverItemParam> deliverItems);

    default List<WcFamilyAwardDeliverExecution> toCreateEntities(
            List<CreateFamilyAwardDeliverExecutionParam> params, int appId, long recordId) {
        if (CollectionUtils.isEmpty(params)) {
            return Collections.emptyList();
        }
        ArrayList<WcFamilyAwardDeliverExecution> entities = new ArrayList<>(params.size());
        for (CreateFamilyAwardDeliverExecutionParam param : params) {
            entities.add(toCreateEntity(param, appId, recordId));
        }
        return entities;
    }

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "resourceDeliverType", expression = "java(param.getResourceDeliverType().getValue())")
    @Mapping(target = "resourceName", source = "param.resourceName", defaultValue = "")
    @Mapping(target = "resourceImagePath", expression = "java(UrlUtils.removeHostOrEmpty(param.getResourceImage()))")
    @Mapping(target = "status", expression = "java(FamilyAwardDeliverExecutionStatusEnum.DELIVERING.getValue())")
    @Mapping(target = "errorCode", constant = "0")
    @Mapping(target = "errorText", constant = "")
    @Mapping(target = "createTime", expression = "java(new Date())")
    @Mapping(target = "modifyTime", expression = "java(new Date())")
    WcFamilyAwardDeliverExecution toCreateEntity(
            CreateFamilyAwardDeliverExecutionParam param, int appId, long recordId);
}
