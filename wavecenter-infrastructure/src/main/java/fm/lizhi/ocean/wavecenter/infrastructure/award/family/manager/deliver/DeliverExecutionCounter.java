package fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager.deliver;

import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * 发放执行计数器
 */
@EqualsAndHashCode
@ToString
public class DeliverExecutionCounter {

    /**
     * 总数
     */
    private final int total;

    /**
     * 已执行数
     */
    private final AtomicInteger finish;

    /**
     * 成功数
     */
    private final AtomicInteger success;

    /**
     * 失败数
     */
    private final AtomicInteger failure;

    public DeliverExecutionCounter(int total) {
        this.total = total;
        this.finish = new AtomicInteger();
        this.success = new AtomicInteger();
        this.failure = new AtomicInteger();
    }

    public boolean incrementSuccessAndReturnIsAllFinish() {
        success.incrementAndGet();
        return finish.incrementAndGet() >= total;
    }

    public boolean incrementFailureAndReturnIsAllFinish() {
        failure.incrementAndGet();
        return finish.incrementAndGet() >= total;
    }

    public boolean hasSuccess() {
        return success.get() > 0;
    }

    public boolean hasFailure() {
        return failure.get() > 0;
    }
}
