package fm.lizhi.ocean.wavecenter.infrastructure.gift.convert;

import fm.lizhi.ocean.wavecenter.infrastructure.gift.datastore.entity.GiftPo;
import fm.lizhi.ocean.wavecenter.service.gift.dto.GiftDto;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/24 15:47
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface GiftInfraConvert {

    GiftInfraConvert I = Mappers.getMapper(GiftInfraConvert.class);

    @Mapping(target = "name", source = "description")
    GiftDto giftPo2Dto(GiftPo po);

    List<GiftDto> giftPo2Dto(List<GiftPo> pos);

}
