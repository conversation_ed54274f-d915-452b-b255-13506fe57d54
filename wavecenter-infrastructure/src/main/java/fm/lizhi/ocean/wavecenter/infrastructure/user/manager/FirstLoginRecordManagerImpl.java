package fm.lizhi.ocean.wavecenter.infrastructure.user.manager;

import fm.lizhi.ocean.wavecenter.infrastructure.user.convert.FirstLoginConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.user.datastore.dao.FirstLoginRecordDao;
import fm.lizhi.ocean.wavecenter.datastore.user.entity.WcUserFirstLoginRecord;
import fm.lizhi.ocean.wavecenter.service.user.dto.FirstLoginRecordDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.FirstLoginRecordParamDTO;
import fm.lizhi.ocean.wavecenter.service.user.manager.FirstLoginRecordManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 首次登录记录Manager实现
 *
 * <AUTHOR>
 */
@Service
public class FirstLoginRecordManagerImpl implements FirstLoginRecordManager {
    @Autowired
    private FirstLoginRecordDao firstLoginRecordDao;


    @Override
    public boolean addFirstLoginRecord(FirstLoginRecordParamDTO dto) {
        Optional<WcUserFirstLoginRecord> result = firstLoginRecordDao.getUserFirstLoginInfo(dto.getAppId(), dto.getUserId());
        if (result.isPresent()) {
            return true;
        }
        WcUserFirstLoginRecord entity = FirstLoginConvert.INSTANCE.toEntity(dto);
        return firstLoginRecordDao.insert(entity) > 0;
    }

    @Override
    public FirstLoginRecordDTO queryFirstLoginRecord(Integer appId, Long userId) {
        return firstLoginRecordDao.getUserFirstLoginInfo(appId, userId)
                .map(FirstLoginConvert.INSTANCE::toDTO)
                .orElse(null);
    }
} 