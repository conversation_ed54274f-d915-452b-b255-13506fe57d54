package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.manager;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcPayConfigCode;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.mapper.WcPayConfigCodeMapper;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.AccountFlowRuleDTO;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.AccountFlowRuleManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/23 20:12
 */
@Slf4j
@Component
public class AccountFlowRuleManagerImpl implements AccountFlowRuleManager {

    @Autowired
    private WcPayConfigCodeMapper payConfigCodeMapper;

    @Override
    public List<AccountFlowRuleDTO> getAccountEngineCodeRuleIgnoreBizId(String tenantCode, String accountEngineCode) {
        return getAccountEngineCodeRule(tenantCode, accountEngineCode, -1);
    }

    @Override
    public List<AccountFlowRuleDTO> getAccountEngineCodeRule(String tenantCode, String accountEngineCode, Integer bizId) {
        WcPayConfigCode param = new WcPayConfigCode();
        param.setTenantCode(tenantCode);
        param.setAccountEngineCode(accountEngineCode);
        param.setBizId(bizId);
        param.setDeleted(0);
        param.setDeployEnv(ConfigUtils.getEnvRequired().name());

        List<WcPayConfigCode> list = payConfigCodeMapper.selectMany(param);
        return list.stream()
                .map(v->new AccountFlowRuleDTO().setCode(v.getConfigCode()))
                .collect(Collectors.toList());
    }
}
