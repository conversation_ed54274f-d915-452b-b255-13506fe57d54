package fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.hy;

import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import fm.hy.family.api.FamilySignService;
import fm.hy.family.api.SignService;
import fm.hy.family.bean.InviteAnchorInfo;
import fm.hy.family.bean.Signinfo;
import fm.hy.family.protocol.FamilySignServiceProto;
import fm.hy.family.protocol.SignServiceProto;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.hy.usergroup.api.UserGroupService;
import fm.lizhi.hy.usergroup.protocol.UserGroupProto;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.bean.FamilyAndNjContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.RoomSignInfoBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.SignPlayerInfoBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestAdminApplyCancelFamily;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestFamilyInviteAdmin;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestUserApplyAdmin;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseAdminApplyCancelFamily;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseFamilyInviteAdmin;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseUserApplyAdmin;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.PlayerSignBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.RoomSignBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.api.user.constants.SingStatusEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.dto.PageDto;
import fm.lizhi.ocean.wavecenter.infrastructure.income.datastore.mapper.HyPlayerIncomeMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.convert.SignInfraConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity.*;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.mapper.*;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.po.HyPlayerIncomePo;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.IContractRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.IUserFamilyRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.request.RequestFamilyAdminCancel;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.request.RequestFamilyAndNjCancelApply;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.response.ResponseInviteAdmin;
import fm.lizhi.ocean.wavecenter.infrastructure.user.convert.UserConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.res.PlayerIncomeStatRes;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto.ContractInfoDto;
import fm.lizhi.ocean.wavecenter.service.sign.dto.*;
import fm.lizhi.ocean.wavecenter.service.user.config.UserConfig;
import fm.lizhi.ocean.wavecenter.service.user.convert.RoleConvert;
import fm.lizhi.ocean.wavecenter.service.user.dto.PlayerSignInfoDto;
import fm.lizhi.trade.contract.constant.ContractType;
import fm.pp.family.constants.FamilyConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class HyContractRemote implements IContractRemote {

    @Autowired
    private HyContractMapper contractMapper;

    @Autowired
    private HyPlayerSignMapper playerSignMapper;

    @Autowired
    private UserConfig userConfig;

    @Autowired
    private UserGroupService userGroupService;

    @Autowired
    private IUserFamilyRemote iUserFamilyRemote;

    @Autowired
    private HyPlayerIncomeMapper playerIncomeMapper;

    @Autowired
    private HyRoomWhitelistMapper roomWhitelistMapper;

    @Autowired
    private HyFamilyNjMapper familyNjMapper;

    @Autowired
    private SignService signService;

    @Autowired
    private FamilySignService familySignService;

    @Autowired
    private HyFamilySignMapper familySignMapper;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.HEI_YE;
    }

    private final LoadingCache<Long, Optional<Long>> FAMILY_ID_CACHE = CacheBuilder.newBuilder()
            .maximumSize(2000)
            .expireAfterAccess(10, TimeUnit.MINUTES)
            .recordStats()
            .build(new CacheLoader<Long, Optional<Long>>() {
                @Override
                public Optional<Long> load(Long roomId) throws Exception {
                    return getRoomBestFamily(roomId);
                }
            });


    @Override
    public PageBean<RoomSignBean> getAllSingGuildRooms(long familyId, int pageNo, int pageSize) {
        PageList<HyContract> hyContracts = contractMapper.selectList(0L, familyId, Arrays.asList(
                ContractType.SIGN.getCode(),
                ContractType.RENEW.getCode(),
                ContractType.SUBJECT_CHANGE.getCode()
                ),
                Arrays.asList(FamilyConstant.ContractStatus.SIGN_SUCCEED.getCode()),
                pageNo,
                pageSize
        );

        List<RoomSignBean> roomSignDtos = UserConvert.I.hyContractPos2RoomSignBeans(hyContracts);
        return PageBean.of(hyContracts.getTotal(), roomSignDtos);
    }

    @Override
    public PageBean<Long> getSingGuildRoomsByDate(long familyId, Date startDate, Date endDate, int pageNo, int pageSize) {
        PageList<Long> pageList = contractMapper.getSingGuildRoomsByDate(familyId,
                DateUtil.formatDateNormal(startDate),
                DateUtil.formatDateNormal(endDate),
                pageNo,
                pageSize);
        if (CollectionUtils.isEmpty(pageList)) {
            return PageBean.empty();
        }
        return PageBean.of(pageList.getTotal(), pageList);
    }

    @Override
    public List<RoomSignBean> getAllSingGuildRoomsList(long familyId) {
        HyContractExample example = new HyContractExample();
        example.createCriteria()
                .andFamilyIdEqualTo(familyId)
                .andTypeIn(Lists.newArrayList(ContractType.SIGN.getCode(), ContractType.RENEW.getCode(), ContractType.SUBJECT_CHANGE.getCode()))
                .andStatusEqualTo(FamilyConstant.ContractStatus.SIGN_SUCCEED.getCode())
        ;
        List<HyContract> poList = contractMapper.selectByExample(example);
        return UserConvert.I.hyContractPos2RoomSignBeans(poList);
    }

    public List<RoomSignBean> getAllSingGuildRoomsList(long familyId, List<Long> roomIds) {
        HyContractExample example = new HyContractExample();
        HyContractExample.Criteria criteria = example.createCriteria();
        criteria.andFamilyIdEqualTo(familyId)
                .andTypeIn(Lists.newArrayList(ContractType.SIGN.getCode(), ContractType.RENEW.getCode(), ContractType.SUBJECT_CHANGE.getCode()))
                .andStatusEqualTo(FamilyConstant.ContractStatus.SIGN_SUCCEED.getCode())
        ;

        if (CollectionUtils.isNotEmpty(roomIds)) {
            criteria.andNjIdIn(roomIds);
        }

        List<HyContract> poList = contractMapper.selectByExample(example);
        return UserConvert.I.hyContractPos2RoomSignBeans(poList);
    }

    @Override
    public PageBean<RoomSignBean> getAllGuildRooms(long familyId, List<Long> roomIds, int pageNo, int pageSize) {
        PageList<HyContract> pageList = contractMapper.pageFamilyNjBest(familyId, roomIds, pageNo, pageSize);
        if (CollectionUtils.isEmpty(pageList)) {
            return PageBean.empty();
        }

        List<RoomSignBean> roomSignDtos = UserConvert.I.hyContractPos2RoomSignBeans(pageList);
        return PageBean.of(pageList.getTotal(), roomSignDtos);
    }

    @Override
    public PageBean<PlayerSignBean> getAllRoomPlayers(long roomId, int pageNo, int pageSize) {
        PageList<HyPlayerSign> pageList = playerSignMapper.pagePlayerSignBest(Collections.singletonList(roomId), pageNo, pageSize);
        if (CollectionUtils.isEmpty(pageList)) {
            return PageBean.empty();
        }
        List<PlayerSignBean> beanList = UserConvert.I.hyPlayerSignPos2PlayerSignBeans(pageList);
        return PageBean.of(pageList.getTotal(), beanList);
    }

    @Override
    public List<Long> getAllSignRoomPlayerIds(long roomId) {
        return new ArrayList<>(playerSignMapper.selectAllSignPlayer(Collections.singletonList(roomId)));
    }

    @Override
    public Set<Long> getAllSignRoomPlayerIds(List<Long> roomIds) {
        if (CollectionUtils.isEmpty(roomIds)) {
            LogContext.addResLog("roomIds is empty");
            return Collections.emptySet();
        }
        return playerSignMapper.selectAllSignPlayer(roomIds);
    }

    @Override
    public List<Long> selectAllSignPlayerByDate(List<Long> njIds, Date startDate, Date endDate) {
        String startStr = DateUtil.formatDateNormal(startDate);
        String endStr = DateUtil.formatDateNormal(endDate);
        return playerSignMapper.selectAllSignPlayerByDate(njIds, startStr, endStr);
    }

    @Override
    public PageDto<PlayerSignBean> getAllGuildPlayer(long familyId, List<Long> scopeRoomIds, int status, int pageNo, int pageSize) {
        List<RoomSignBean> roomsList = getAllSingGuildRoomsList(familyId, scopeRoomIds);
        if (CollectionUtils.isEmpty(roomsList)) {
            return PageDto.empty();
        }
        List<Long> roomIds = roomsList.stream().map(UserBean::getId).collect(Collectors.toList());
        PageList<HyPlayerSign> pageList = playerSignMapper.pagePlayerSignBest(roomIds, pageNo, pageSize);
        //过滤掉重复的陪玩：不分陪玩在同一个公会下，存在转移签约厅的情况，在公会视角下，只关心当前签约的状态
        List<Long> removeIds = new ArrayList<>();
        Map<Long, List<HyPlayerSign>> beanMap = pageList.stream().collect(Collectors.groupingBy(HyPlayerSign::getUserId));
        for (Map.Entry<Long, List<HyPlayerSign>> longListEntry : beanMap.entrySet()) {
            if (longListEntry.getValue().size() > 1) {
                longListEntry.getValue().sort(Comparator.comparing(HyPlayerSign::getCreateTime).reversed());
                for (int i = 0; i < longListEntry.getValue().size(); i++) {
                    if (i != 0) {
                        removeIds.add(longListEntry.getValue().get(i).getId());
                    }
                }
            }
        }

        LogContext.addResLog("removeIds={}", JsonUtil.dumps(removeIds));
        List<HyPlayerSign> onlyList = pageList.stream().filter(v -> {
            return !removeIds.contains(v.getId());
        }).collect(Collectors.toList());

        List<PlayerSignBean> resultList = UserConvert.I.hyPlayerSignPos2PlayerSignBeans(onlyList);
        LogContext.addResLog("resultList={}", JsonUtil.dumps(resultList));
        return PageDto.of(pageList.getTotal(), resultList);
    }

    @Override
    public Optional<RoomSignBean> getRoomSign(long familyId, long njId) {
        RoomSignBean bean = new RoomSignBean();
        bean.setId(njId);

        HyContractExample example = new HyContractExample();
        example.createCriteria()
                .andNjIdEqualTo(njId)
                .andFamilyIdEqualTo(familyId)
                .andTypeIn(Lists.newArrayList("SIGN", "RENEW", "SUBJECT_CHANGE"))
                .andStatusEqualTo("SIGN_SUCCEED");
        List<HyContract> hyContracts = contractMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(hyContracts)) {
            bean.setSignStatus(SingStatusEnum.STOP.getValue());
        } else {
            bean.setSignStatus(SingStatusEnum.SING.getValue());
        }

        return Optional.of(bean);
    }

    @Override
    public Optional<PlayerSignBean> getPlayerSign(Long familyId, Long roomId, long playerId) {
        PlayerSignBean playerSignBean = new PlayerSignBean();
        playerSignBean.setId(playerId);

        HyPlayerSignExample example = new HyPlayerSignExample();
        HyPlayerSignExample.Criteria criteria = example.createCriteria();
        criteria.andUserIdEqualTo(playerId)
                .andTypeEqualTo("SIGN")
                .andStatusEqualTo("SIGN_SUCCEED")
        ;

        List<HyPlayerSign> list = new ArrayList<>();
        if (roomId != null) {
            criteria.andNjIdEqualTo(roomId);
            list = playerSignMapper.selectByExample(example);
        } else if (familyId != null) {
            //查询公会下所有厅
            List<Long> familyAllNjId = getFamilyAllNjId(familyId);
            if (CollectionUtils.isNotEmpty(familyAllNjId)) {
                criteria.andNjIdIn(familyAllNjId);
                list = playerSignMapper.selectByExample(example);
            }
        }

        if (CollectionUtils.isEmpty(list)) {
            playerSignBean.setSignStatus(SingStatusEnum.STOP.getValue());
        } else {
            playerSignBean.setSignStatus(SingStatusEnum.SING.getValue());
        }
        return Optional.of(playerSignBean);
    }

    /**
     * 查询公会下所有厅
     *
     * @param familyId
     * @return
     */
    public List<Long> getFamilyAllNjId(Long familyId) {
        if (familyId == null) {
            return Collections.emptyList();
        }
        HyContract param = new HyContract();
        param.setFamilyId(familyId);
        List<HyContract> list = contractMapper.selectMany(param);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(HyContract::getNjId).collect(Collectors.toList());
    }

    @Override
    public List<PlayerIncomeStatRes> queryAdminPlayerIncomeList(long userId, Date startDate, Date endDate) {
        return Collections.EMPTY_LIST;
    }

    @Override
    public Optional<PlayerSignInfoDto> getLatestSignRecord(long roomId, long userId) {
        return Optional.empty();
    }

    @Override
    public Optional<PlayerSignInfoDto> getLatestSignRecord(List<Long> roomIds, long userId) {
        HyPlayerSign latestSignRecord = playerSignMapper.getLatestSignRecord(roomIds, userId);
        return Optional.ofNullable(UserConvert.I.hyPlayerSign2PlayerSignInfoDto(latestSignRecord));
    }

    @Override
    public Optional<Long> getRoomBestFamily(long roomId) {
        HyContractExample example = new HyContractExample();
        example.createCriteria().andNjIdEqualTo(roomId);
        example.setOrderByClause("create_time desc");
        List<HyContract> hyContracts = contractMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(hyContracts)) {
            log.warn("getRoomBestFamily ppContracts empty roomId={}", roomId);
            return Optional.empty();
        }
        return Optional.ofNullable(hyContracts.get(0).getFamilyId());
    }

    @Override
    public Optional<Long> getPlayerBestFamily(long playerId) {
        Optional<Long> njIdOp = getUserBestNj(playerId);
        if (!njIdOp.isPresent()) {
            log.warn("getPlayerBestFamily sign empty playerId={}", playerId);
            return Optional.empty();
        }

        Long njId = njIdOp.get();
        return getRoomBestFamily(njId);
    }

    @Override
    public Optional<Long> getUserBestNj(long userId) {
        //优先查询签约
        HyPlayerSignExample example = new HyPlayerSignExample();
        example.createCriteria()
                .andTypeEqualTo("SIGN")
                .andStatusEqualTo("SIGN_SUCCEED")
                .andUserIdEqualTo(userId);
        example.setOrderByClause("create_time desc");
        List<HyPlayerSign> signList = playerSignMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(signList)) {
            return Optional.ofNullable(signList.get(0).getNjId());
        }

        HyPlayerSignExample example2 = new HyPlayerSignExample();
        example2.createCriteria()
                .andUserIdEqualTo(userId);
        example2.setOrderByClause("create_time desc");
        List<HyPlayerSign> signList2 = playerSignMapper.selectByExample(example2);
        if (CollectionUtils.isNotEmpty(signList2)) {
            return Optional.ofNullable(signList2.get(0).getNjId());
        }
        return Optional.empty();
    }

    @Override
    public Long getPlayerLastRoom(long familyId, long playerId) {
        List<Long> njIds = getFamilyAllNjId(familyId);
        if (CollectionUtils.isEmpty(njIds)) {
            return null;
        }

        //优先查询已签约
        HyPlayerSignExample example = new HyPlayerSignExample();
        example.createCriteria()
                .andTypeEqualTo("SIGN")
                .andStatusEqualTo("SIGN_SUCCEED")
                .andNjIdIn(njIds)
                .andUserIdEqualTo(playerId);
        example.setOrderByClause("create_time desc");
        List<HyPlayerSign> signList = playerSignMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(signList)) {
            log.info("has sign");
            return signList.get(0).getNjId();
        }

        //没有已签约就返回最近的旧签约
        HyPlayerSignExample example2 = new HyPlayerSignExample();
        example2.createCriteria()
                .andNjIdIn(njIds)
                .andUserIdEqualTo(playerId);
        example2.setOrderByClause("create_time desc");
        List<HyPlayerSign> noSignList = playerSignMapper.selectByExample(example2);
        if (CollectionUtils.isNotEmpty(noSignList)) {
            log.info("has no sign");
            return noSignList.get(0).getNjId();
        }
        return null;
    }

    @Override
    public List<Long> getOfficialRoomIds() {
        Long officialRoomGroupId = userConfig.getBizConfig().getOfficialRoomGroupId();
        if (officialRoomGroupId == null) {
            return Collections.emptyList();
        }

        log.info("officialRoomGroupId={}", officialRoomGroupId);
        Result<UserGroupProto.ResponseGetGroupUserIds> result = userGroupService.getGroupUserIds(officialRoomGroupId);
        if (RpcResult.isFail(result)) {
            log.warn("hy getOfficialRoomIds fail. rCode={}", result.rCode());
            return Collections.emptyList();
        }
        return result.target().getUserIdList();
    }

    @Override
    public Optional<Long> getRoomBestFamilyByCache(long roomId) {
        return FAMILY_ID_CACHE.getUnchecked(roomId);
    }

    @Override
    public Optional<Long> getRoomSignFamilyInDate(long roomId, Date date) {
        List<Long> list = contractMapper.getRoomSignFamilyInDate(roomId, DateUtil.formatDateNormal(date));
        if (CollectionUtils.isEmpty(list)) {
            return Optional.empty();
        }
        return Optional.ofNullable(list.get(0));
    }

    @Override
    public Optional<FamilyBean> getFamily(long familyId) {

        return Optional.empty();
    }

    @Override
    public PageBean<RoomSignInfoBean> guildSignRoomPageList(SMSignRoomPageListReqDto reqDto) {
        PageList<HyContract> pageList = contractMapper.guildSignRoomPageList(reqDto, reqDto.getPageNo(), reqDto.getPageSize());
        List<RoomSignInfoBean> beanList = SignInfraConvert.I.hyContracts2Beans(pageList);
        return PageBean.of(pageList.getTotal(), beanList);
    }

    @Override
    public PageBean<SignPlayerInfoBean> signPlayerPageList(SMSignPlayerPageListReqDto reqDto) {
        //结算比例参数
        if (reqDto.getSettleMax() != null || reqDto.getSettleMin() != null) {
            List<Long> filterRoomIds = playerIncomeMapper.selectSettleScopeNjIds(reqDto.getRoomIds(), reqDto.getSettleMax(), reqDto.getSettleMin());
            if (CollectionUtils.isEmpty(filterRoomIds)) {
                LogContext.addResLog("filterRoomIds is empty");
                return PageBean.empty();
            }
            reqDto.setRoomIds(filterRoomIds);
        }

        PageList<HyPlayerSign> poList = playerSignMapper.signPlayerPageList(reqDto, reqDto.getPageNo(), reqDto.getPageSize());
        if (CollectionUtils.isEmpty(poList)) {
            return PageBean.empty();
        }

        List<SignPlayerInfoBean> beanList = SignInfraConvert.I.hyPlayerSigns2Beans(poList);

        //查询家族长用户ID
        Optional<FamilyBean> family = iUserFamilyRemote.getFamily(reqDto.getFamilyId());
        if (!family.isPresent()) {
            LogContext.addResLog("family not found");
            return PageBean.of(poList.getTotal(), beanList);
        }

        //查询结算比例
        Set<Long> njIds = poList.stream().map(HyPlayerSign::getNjId).collect(Collectors.toSet());
        List<HyPlayerIncomePo> incomeList = playerIncomeMapper.selectBest(family.get().getUserId(), new ArrayList<>(njIds));
        Map<Long, Integer> incomeMap = incomeList.stream().collect(Collectors.toMap(HyPlayerIncomePo::getNjId, HyPlayerIncomePo::getPlayerIncome, (k1, k2) -> k2));

        for (SignPlayerInfoBean bean : beanList) {
            UserBean roomInfo = bean.getRoomInfo();
            if (roomInfo == null) {
                continue;
            }
            bean.setSettle(incomeMap.get(roomInfo.getId()));
        }

        return PageBean.of(poList.getTotal(), beanList);
    }

    @Override
    public Optional<FamilyAndNjContractBean> queryLastCancel(Long userId) {
        return queryNjLast(userId, ContractTypeEnum.CANCEL);
    }

    @Override
    public Optional<FamilyAndNjContractBean> queryLastSign(Long userId) {
        return queryNjLast(userId, ContractTypeEnum.SIGN);
    }

    private Optional<FamilyAndNjContractBean> queryNjLast(Long userId, ContractTypeEnum type){
        HyContractExample example = new HyContractExample();
        example.setOrderByClause("create_time desc");
        HyContractExample.Criteria criteria = example.createCriteria();
        criteria.andNjIdEqualTo(userId);
        criteria.andTypeEqualTo(type.getCode());

        PageList<HyContract> hyContracts = contractMapper.pageByExample(example, 1, 1);
        if (CollectionUtils.isEmpty(hyContracts)) {
            return Optional.empty();
        }

        HyContract contract = hyContracts.get(0);
        FamilyAndNjContractBean contractBean = new FamilyAndNjContractBean();
        contractBean.setId(contract.getId());
        contractBean.setContractId(contract.getId());
        contractBean.setSignId(contract.getSignId());
        contractBean.setType(type.getCode());
        contractBean.setNjUserId(contract.getNjId());
        contractBean.setFamilyId(contract.getFamilyId());

        contractBean.setStatus(
                SignInfraConvert.I.hySignStatusTrans(contract.getStatus())
        );

        return Optional.of(contractBean);
    }

    @Override
    public boolean isUserSignAsRoom(Long userId) {
        return roomWhitelistMapper.countUser(userId) > 0;
    }


    @Override
    public PageBean<FamilyAndNjContractBean> queryContract(RequestFamilyAndNjContractDTO request) {
        HyContractExample example = new HyContractExample();
        if (request.isDescCreateTime()) {
            example.setOrderByClause("create_time desc");
        }
        HyContractExample.Criteria criteria = example.createCriteria();
        if (request.isNoExpire()) {
            Date now = new Date();
            criteria.andBeginTimeLessThanOrEqualTo(now)
                    .andExpireTimeGreaterThan(now);
        }
        if (CollectionUtils.isNotEmpty(request.getTypes())) {
            criteria.andTypeIn(request.getTypes().stream().map(ContractTypeEnum::getCode).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(request.getRelations())) {
            criteria.andStatusIn(request.getRelations()
                    .stream().map(SignInfraConvert.I::waveSignStatus2hy)
                    .collect(Collectors.toList()));
        }
        if (request.getFamilyId() != null) {
            criteria.andFamilyIdEqualTo(request.getFamilyId());
        }
        if (request.getNjId() != null) {
            criteria.andNjIdEqualTo(request.getNjId());
        }
        if (request.getContractId() != null) {
            criteria.andIdEqualTo(request.getContractId());
        }
        if (StringUtils.isNotBlank(request.getSignId())) {
            criteria.andSignIdEqualTo(request.getSignId());
        }
        if (CollectionUtils.isNotEmpty(request.getContractLists())) {
            criteria.andIdIn(request.getContractLists());
        }
        if (request.getOtherFamilyId() != null) {
            criteria.andFamilyIdNotEqualTo(request.getOtherFamilyId());
        }

        PageList<HyContract> contracts = contractMapper.pageByExample(example, request.getPageNo(), request.getPageSize());
        return PageBean.of(contracts.getTotal()
                , SignInfraConvert.I.hyContracts2ContractBeans(contracts));
    }

    @Override
    public PageBean<FamilyAndNjContractBean> queryCancelApply(RequestFamilyAndNjCancelApply request) {
        return queryContract(RequestFamilyAndNjContractDTO.builder()
                .type(ContractTypeEnum.CANCEL)
                .relations(request.getRelations())
                .familyId(request.getFamilyId())
                .njId(request.getNjId())
                .build());
    }

    @Override
    public List<Long> queryAllSignNjId(Long familyId) {
        HyContractExample example = new HyContractExample();
        example.createCriteria()
                .andFamilyIdEqualTo(familyId)
                .andTypeIn(Lists.newArrayList(ContractTypeEnum.SIGN.getCode()
                        , ContractTypeEnum.SUBJECT_CHANGE.getCode()
                        , ContractTypeEnum.RENEW.getCode()
                ))
                .andStatusEqualTo("SIGN_SUCCEED");

        List<HyContract> list = contractMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return list.stream().map(HyContract::getNjId).collect(Collectors.toList());
    }

    @Override
    public Integer countSignRoomNum(long familyId) {

        HyContractExample example = new HyContractExample();
        example.createCriteria()
                .andFamilyIdEqualTo(familyId)
                .andTypeIn(Lists.newArrayList(ContractTypeEnum.SIGN.getCode()
                        , ContractTypeEnum.SUBJECT_CHANGE.getCode()
                        , ContractTypeEnum.RENEW.getCode()
                ))
                .andStatusEqualTo("SIGN_SUCCEED");

        return Math.toIntExact(contractMapper.countByExample(example));
    }

    @Override
    public ResponseUserApplyAdmin userApplyAdmin(RequestUserApplyAdmin request) {
        ResponseUserApplyAdmin res = new ResponseUserApplyAdmin().setCode(0);

        //发起申请
        ResponseInviteAdmin inviteRes = inviteAdmin(request.getCurUserId(), request.getFamilyId());
        if (inviteRes.getCode() != 0) {
            return res.setCode(inviteRes.getCode()).setMsg(inviteRes.getMsg());
        }
        long contractId = inviteRes.getContractId();

        //签署电子签
        //发送合同模板
        Optional<String> signOp = this.signContract(contractId);
        if (!signOp.isPresent()) {
            return res.setCode(-1).setMsg("签署合同失败");
        }

        return res.setSignId(signOp.get()).setContractId(contractId);
    }

    @Override
    public List<FamilyAndNjContractBean> queryIdentityNoJoinFamily(String identityNo) {
        HyFamilyNjExample example = new HyFamilyNjExample();
        example.createCriteria()
                .andStatusEqualTo(FamilyConstant.FamilyNjStatus.JOIN.getCode())
                .andIdentityNoEqualTo(identityNo);
        List<HyFamilyNj> list = familyNjMapper.selectByExample(example);
        return SignInfraConvert.I.hyFamilyNjs2ContractBeans(list);
    }

    @Override
    public ResponseFamilyInviteAdmin familyInviteAdmin(RequestFamilyInviteAdmin request) {
        ResponseFamilyInviteAdmin res = new ResponseFamilyInviteAdmin();

        //发起申请
        ResponseInviteAdmin inviteRes = inviteAdmin(request.getTargetUserId(), request.getFamilyId());
        if (inviteRes.getCode() != 0) {
            return res.setCode(inviteRes.getCode()).setMsg(inviteRes.getMsg());
        }
        long contractId = inviteRes.getContractId();

        //签署电子签
        //发送合同模板
        Optional<String> signOp = this.signContract(contractId);
        if (!signOp.isPresent()) {
            return res.setCode(-1).setMsg("签署合同失败");
        }

        return res.setSignId(signOp.get()).setContractId(contractId);
    }

    @Override
    public List<FamilyNjJoinRecordDTO> queryFamilyNjJoinRecord(QueryFamilyNjJoinRecordDTO request) {
        return Collections.emptyList();
    }

    @Override
    public Optional<String> doSignGenSignId(Long contractId) {
        return Optional.empty();
    }

    @Override
    public ResponseAdminApplyCancelFamily adminApplyCancelFamily(RequestAdminApplyCancelFamily request) {
        return null;
    }

    @Override
    public Optional<String> familyAdminCancel(RequestFamilyAdminCancel request) {
        //subjectId 如果当前用户为家族长 则为familyId，如果为管理员，则为管理员自己的uid
        SignServiceProto.CheckWaitSignCancelContractParam param = SignServiceProto.CheckWaitSignCancelContractParam.newBuilder()
                .setFamilyId(request.getFamilyId())
                .setNjId(request.getNjId())
                .setSubjectId(request.getFamilyId())
                .setUserId(request.getCurUserId())
                .build();
        Result<SignServiceProto.ResponseCheckWaitSignCancelContract> result = signService.checkWaitSignCancelContract(param);
        if(RpcResult.isSuccess(result) && StringUtils.isNotBlank(result.target().getSignId())) {
            String signId = result.target().getSignId();
            return Optional.of(signId);
        }

        //查询是否进行中的解约合同
        PageBean<FamilyAndNjContractBean> existPage = queryContract(RequestFamilyAndNjContractDTO.builder()
                .familyId(request.getFamilyId())
                .njId(request.getNjId())
                .type(ContractTypeEnum.CANCEL)
                .relation(SignRelationEnum.SIGNING)
                .relation(SignRelationEnum.WAIT_SIGN)
                .pageSize(1)
                .build());

        Long contractId = null;
        if (CollectionUtils.isNotEmpty(existPage.getList())) {
            contractId = existPage.getList().get(0).getContractId();
        } else {
            // 生成签约的数据
            SignServiceProto.InviteCancelParam inviteParam = SignServiceProto.InviteCancelParam.newBuilder()
                    .setFamilyId(request.getFamilyId()).setNjId(request.getNjId()).build();
            Result<SignServiceProto.ResponseInviteCancel> cancelResult = signService.inviteCancel(inviteParam);
            if(RpcResult.isFail(cancelResult)) {
                log.error("hy inviteCancel fail. familyId={},njId={},rCode={}", request.getFamilyId(), request.getNjId(), cancelResult.rCode());
                return Optional.empty();
            }
            contractId = cancelResult.target().getContractId();
        }

        // 更新签约表的signId（关联支付侧）用以查询对应的签约状态
        SignServiceProto.SignCancelContractParam cancelContractParam = SignServiceProto.SignCancelContractParam.newBuilder()
                .setContractId(contractId).build();
        Result<SignServiceProto.ResponseSignCancelContract> contractResult = signService.signCancelContract(cancelContractParam);
        if(RpcResult.isFail(contractResult)) {
            log.error("hy signCancelContract fail. contractId={},rCode={}", contractId, contractResult.rCode());
            return Optional.empty();
        }
        String signId = contractResult.target().getSignId();
        return Optional.ofNullable(signId);
    }

    @Override
    public Pair<Integer, String> familyAdminInviteConfirm(Long curUserId, Long targetUserId, RoleEnum operateRole) {
        String role = RoleConvert.I.waveRole2HyRoleCode(operateRole.getRoleCode());
        Result<FamilySignServiceProto.ResponseInviteSign> res = familySignService.inviteSign(curUserId, targetUserId, role);
        if (RpcResult.isFail(res)) {
            log.error("hy inviteSign fail. curUserId={},targetUserId={},operateRole={},rCode={}", curUserId, targetUserId, operateRole.getRoleCode(), res.rCode());
            return Pair.of(-1, "");
        }

        if (res.target().getCode() != 0) {
            return Pair.of(-1, res.target().getMessage());
        }

        return Pair.of(0, "");
    }

    @Override
    public Pair<Integer, String> familyAdminInviteCancelConfirm(Long familySignId, RoleEnum operateRole, Long curUserId) {
        String role = RoleConvert.I.waveRole2HyRoleCode(operateRole.getRoleCode());
        Result<FamilySignServiceProto.ResponseInviteCancel> res = familySignService.inviteCancel(familySignId, role, curUserId);
        if (RpcResult.isFail(res)) {
            log.error("hy familyAdminInviteCancelConfirm fail. familySignId={},curUserId={},operateRole={},rCode={}", familySignId, curUserId, operateRole.getRoleCode(), res.rCode());
            return Pair.of(-1, "");
        }

        if (res.target().getCode() != 0) {
            return Pair.of(-1, res.target().getMessage());
        }

        return Pair.of(0, "");
    }

    @Override
    public PageBean<FamilyNjSignRecordDTO> querySignRecord(QueryFamilyNjSignRecordDTO param) {
        HyFamilySignExample example = new HyFamilySignExample();
        example.setOrderByClause("create_time desc");
        HyFamilySignExample.Criteria criteria = example.createCriteria();

        if (CollectionUtils.isNotEmpty(param.getStatuses())) {
            List<String> status = param.getStatuses().stream().map(SignInfraConvert.I::waveSignStatus2hy).collect(Collectors.toList());
            criteria.andStatusIn(status);
        }

        if (CollectionUtils.isNotEmpty(param.getTypes())) {
            List<String> types = param.getTypes().stream().map(ContractTypeEnum::getCode).collect(Collectors.toList());
            criteria.andTypeIn(types);
        }

        if (param.getNjId() != null) {
            criteria.andNjIdEqualTo(param.getNjId());
        }

        if (param.getFamilyUserId() != null) {
            criteria.andFamilyUserIdEqualTo(param.getFamilyUserId());
        }

        if (param.getParentId() != null) {
            criteria.andParentIdEqualTo(param.getParentId());
        }

        if (param.getId() != null) {
            criteria.andIdEqualTo(param.getId());
        }

        PageList<HyFamilySign> pageList = familySignMapper.pageByExample(example, param.getPageNo(), param.getPageSize());
        List<FamilyNjSignRecordDTO> list = pageList.stream().map(v -> new FamilyNjSignRecordDTO()
                .setId(v.getId())
                .setFamilyUserId(v.getFamilyUserId())
                .setNjId(v.getNjId())
        ).collect(Collectors.toList());
        return PageBean.of(pageList.getTotal(), list);
    }

    @Override
    public Pair<Integer, String> doFamilyNjConfirmSign(Long familyNjSignId, Long curUserId, ContractTypeEnum type, RoleEnum operateRole) {
        String role = RoleConvert.I.waveRole2HyRoleCode(operateRole.getRoleCode());
        Result<FamilySignServiceProto.ResponseSign> res = familySignService.sign(familyNjSignId, curUserId, type.getCode(), role);
        if (RpcResult.isFail(res)) {
            log.error("hy doFamilyNjConfirmSign fail. familyNjSignId={},curUserId={},type={},operateRole={},rCode={}", familyNjSignId, curUserId, type.getCode(), operateRole.getRoleCode(), res.rCode());
            return Pair.of(-1, "");
        }

        if (res.target().getCode() != 0) {
            return Pair.of(-1, res.target().getMessage());
        }

        return Pair.of(0, "");
    }

    @Override
    public List<FamilyNjCancelRecordDTO> queryCancelRecordByContractId(List<Long> contractIds) {
        return Collections.emptyList();
    }

    private Optional<String> signContract(Long contractId){
        Signinfo signinfo = new Signinfo().setContractId(contractId);
        Result<SignServiceProto.ResponseSignContract> signRes = signService.signContract(JSONObject.toJSONString(signinfo));
        if (RpcResult.isFail(signRes)) {
            log.error("signContract fail. rCode={},param={}", signRes.rCode(), JsonUtil.dumps(signinfo));
            return Optional.empty();
        }

        return Optional.of(signRes.target().getSignId());
    }

    private ResponseInviteAdmin inviteAdmin(Long njId, Long familyId){
        ResponseInviteAdmin res = new ResponseInviteAdmin();
        InviteAnchorInfo inviteAnchor = new InviteAnchorInfo();
        inviteAnchor.setFamilyId(familyId);
        inviteAnchor.setNjId(njId);
        inviteAnchor.setDuration(36);
        inviteAnchor.setPercentage(0);
        inviteAnchor.setSettlyType("PUBLIC");
        Result<SignServiceProto.ResponseInviteAnchor> inviteRes = signService.inviteAnchor(JSONObject.toJSONString(inviteAnchor));
        if (inviteRes.rCode() == 2) {
            return res.setCode(-1).setMsg("家族管理员数量已满");
        }
        if (inviteRes.rCode() != 0) {
            log.error("inviteAnchor fail. rCode={},param={}", inviteRes.rCode(), JsonUtil.dumps(inviteAnchor));
            return res.setCode(-1).setMsg("参数错误");
        }
        long contractId = inviteRes.target().getContractId();
        return res.setContractId(contractId);
    }

    @Override
    public List<ContractInfoDto> queryContractInfoByTime(long familyId, Date startDate, Date endDate) {
        // HY暂不实现
        return Collections.emptyList();
    }

    @Override
    public List<Long> getHistoryNjIds(Long familyId, Long minNjId, Integer pageSize) {
        return contractMapper.getHistoryNjIds(familyId, minNjId, pageSize);
    }
}
