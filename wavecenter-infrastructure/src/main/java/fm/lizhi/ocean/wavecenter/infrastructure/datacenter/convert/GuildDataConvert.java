package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.convert;

import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.*;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.IncomeSummaryDTO;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface GuildDataConvert {

    GuildDataConvert I = Mappers.getMapper(GuildDataConvert.class);

    /**
     * 转换公会收入信息统计
     *
     * @param statPo 统计结果
     * @return 结果
     */
    IncomeSummaryDTO convertGuildInfoStat(WcDataPayRoomDayIncomeStatPo statPo);

    /**
     * 转换公会收入信息统计
     *
     * @param statPo 统计结果
     * @return 结果
     */
    IncomeSummaryDTO convertGuildDayInfoStat(WcDataFamilyDayStatPo statPo);

      /**
     * 转换公会收入信息统计
     *
     * @param statPo 统计结果
     * @return 结果
     */
    IncomeSummaryDTO convertGuildWeekInfoStat(WcDataFamilyWeekStatPo statPo);

    /**
     * 转换公会收入信息统计
     *
     * @param statPo 统计结果
     * @return 结果
     */
    IncomeSummaryDTO convertGuildMonthInfoStat(WcDataFamilyMonthStatPo statPo);
}
