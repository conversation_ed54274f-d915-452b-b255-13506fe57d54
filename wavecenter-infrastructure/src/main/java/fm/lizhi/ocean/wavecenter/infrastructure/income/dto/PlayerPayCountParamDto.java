package fm.lizhi.ocean.wavecenter.infrastructure.income.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 查询有收入主播数的参数
 * <AUTHOR>
 * @date 2024/5/23 14:45
 */
@Data
@Accessors(chain = true)
public class PlayerPayCountParamDto {

    /**
     * 家族ID
     */
    private Long familyId;

    /**
     * 厅ID
     */
    private Long roomId;

    /**
     * 批量参数
     */
    private List<Long> roomIds;

    private List<Long> playerIds;

}
