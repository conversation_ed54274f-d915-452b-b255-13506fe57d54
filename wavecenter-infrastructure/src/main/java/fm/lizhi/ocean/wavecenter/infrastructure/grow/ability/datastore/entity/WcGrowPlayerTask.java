package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 成长体系主播完成任务
 *
 * @date 2025-06-16 08:02:20
 */
@Table(name = "`wavecenter_grow_player_task`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcGrowPlayerTask {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 任务模板ID
     */
    @Column(name= "`template_id`")
    private Long templateId;

    /**
     * 主播ID
     */
    @Column(name= "`player_id`")
    private Long playerId;

    /**
     * 签约厅ID
     */
    @Column(name= "`room_id`")
    private Long roomId;

    /**
     * 签约公会ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 完成时间
     */
    @Column(name= "`finish_time`")
    private Date finishTime;

    /**
     * 周期开始时间
     */
    @Column(name= "`start_week_date`")
    private Date startWeekDate;

    /**
     * 周期结束时间
     */
    @Column(name= "`end_week_date`")
    private Date endWeekDate;

    /**
     * 业务 ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 服务部署环境, TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 0=未删除，1=已删除
     */
    @Column(name= "`deleted`")
    private Integer deleted;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", templateId=").append(templateId);
        sb.append(", playerId=").append(playerId);
        sb.append(", roomId=").append(roomId);
        sb.append(", familyId=").append(familyId);
        sb.append(", finishTime=").append(finishTime);
        sb.append(", startWeekDate=").append(startWeekDate);
        sb.append(", endWeekDate=").append(endWeekDate);
        sb.append(", appId=").append(appId);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", deleted=").append(deleted);
        sb.append("]");
        return sb.toString();
    }
}