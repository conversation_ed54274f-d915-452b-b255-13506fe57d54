package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.ext;

import java.util.List;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataPayRoomDay;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.WcDataPayRoomDayIncomeStatPo;

/**
 * <AUTHOR>
 * @date 2025/4/24 14:59
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcDataPayRoomDayExtMapper {

    /**
     * 保存收入数据
     * @param entity
     * @param columnNames 列名称
     * @param amount 负数需要拼接负号
     * @return
     */
    @Insert({
            "<script>"
            , "insert into wavecenter_data_pay_room_day (id, app_id, stat_date, stat_date_value, room_id, family_id, create_time, modify_time, deploy_env, "
                , "<foreach collection='columnNames' item='c' separator=','>"
                , "${c}"
                , "</foreach>"
            , ") VALUES (#{entity.id}, #{entity.appId}, #{entity.statDate}, #{entity.statDateValue}, #{entity.roomId}, #{entity.familyId}, #{entity.createTime}, #{entity.modifyTime}, #{entity.deployEnv}, "
                , "<foreach collection='columnNames' item='c' separator=','>"
                , "${amount}"
                , "</foreach>"
            , ")"
            , "</script>"
    })
    int insertForColumn(@Param("entity") WcDataPayRoomDay entity
            , @Param("columnNames") List<String> columnNames
            , @Param("amount") String amount);

    /**
     * 更新收入数据
     * @param entity
     * @param columnNames 列名
     * @param amount 负数需要拼接负号
     * @return
     */
    @Update({
            "<script>"
            , "update wavecenter_data_pay_room_day set modify_time=#{entity.modifyTime}, "
            , "<foreach collection='columnNames' item='c' separator=','>"
            , "${c} = ${c}${amount}"
            , "</foreach>"
            , "where app_id=#{entity.appId} and stat_date_value=#{entity.statDateValue} and deploy_env=#{entity.deployEnv} and room_id=#{entity.roomId}"
            , "</script>"
    })
    int updateForColumn(@Param("entity") WcDataPayRoomDay entity
            , @Param("columnNames") List<String> columnNames
            , @Param("amount") String amount);


//  toDO 检查一下
    @Select("<script>" +
            "select DISTINCT(room_id) " +
            "FROM wavecenter_data_pay_room_day " +
            "WHERE app_id = #{appId} AND stat_date_value BETWEEN #{start} AND #{end} AND all_income > 0 AND deploy_env = #{deployEnv}" +
            "</script>")
    List<Long> findHasIncomeRoomIds(@Param("appId") Integer appId, @Param("start") Integer startDateValue,
                                    @Param("end") Integer endDateValue, @Param("deployEnv") String deployEnv);

                                     /**
         * 查询公会某天的收入统计数据
         * @param familyId 公会ID
         * @param appId 业务ID
         * @param statDateValue 统计日期值
         * @return 收入统计数据
         */
        @Select("SELECT " +
                "    SUM(personal_noble_income) as playerVip, " +
                "    SUM(official_hall_income) as officialRoom, " +
                "    SUM(all_income) as sum, " +
                "    SUM(sign_hall_income) as roomGift, " +
                "    SUM(personal_hall_income) as player, " +
                "    SUM(room_noble_income) as roomVip " +
                "FROM wavecenter_data_pay_room_day " +
                "WHERE family_id = #{familyId} " +
                "    AND app_id = #{appId} " +
                "    AND stat_date_value = #{statDateValue}")
        WcDataPayRoomDayIncomeStatPo queryIncomeStatByDay(@Param("familyId") Long familyId,
                                                          @Param("appId") Integer appId,
                                                          @Param("statDateValue") Integer statDateValue);

        /**
         * 根据周开始时间和周结束时间，统计公会周收入统计数据
         */
        @Select("SELECT " +
                "    SUM(personal_noble_income) as playerVip, " +
                "    SUM(official_hall_income) as officialRoom, " +
                "    SUM(all_income) as sum, " +
                "    SUM(sign_hall_income) as roomGift, " +
                "    SUM(personal_hall_income) as player, " +
                "    SUM(room_noble_income) as roomVip " +
                "FROM wavecenter_data_pay_room_day " +
                "WHERE family_id = #{familyId} " +
                "    AND app_id = #{appId} " +
                "    AND stat_date_value BETWEEN #{startDateValue} AND #{endDateValue}")
        WcDataPayRoomDayIncomeStatPo queryIncomeStatByWeek(@Param("familyId") Long familyId,
                                                          @Param("appId") Integer appId,
                                                          @Param("startDateValue") Integer startDateValue,
                                                          @Param("endDateValue") Integer endDateValue);

        /**
         * 根据月开始时间和月结束时间，统计公会月收入统计数据
         */
        @Select("SELECT " +
                "    SUM(personal_noble_income) as playerVip, " +
                "    SUM(official_hall_income) as officialRoom, " +
                "    SUM(all_income) as sum, " +
                "    SUM(sign_hall_income) as roomGift, " +
                "    SUM(personal_hall_income) as player, " +
                "    SUM(room_noble_income) as roomVip " +
                "FROM wavecenter_data_pay_room_day " +
                "WHERE family_id = #{familyId} " +
                "    AND app_id = #{appId} " +
                "    AND stat_date_value BETWEEN #{startDateValue} AND #{endDateValue}")
        WcDataPayRoomDayIncomeStatPo queryIncomeStatByMonth(@Param("familyId") Long familyId, 
                                                          @Param("appId") Integer appId, 
                                                          @Param("startDateValue") Integer startDateValue, 
                                                          @Param("endDateValue") Integer endDateValue);

        /**
         * 查询厅某天的收入统计数据
         * @param njId 厅ID
         * @param appId 业务ID
         * @param statDateValue 统计日期值
         * @return 收入统计数据
         */
        @Select("SELECT " +
                "    personal_noble_income as playerVip, " +
                "    official_hall_income as officialRoom, " +
                "    all_income as sum, " +
                "    sign_hall_income as roomGift, " +
                "    personal_hall_income as player, " +
                "    room_noble_income as roomVip " +
                "FROM wavecenter_data_pay_room_day " +
                "WHERE room_id = #{njId} " +
                "    AND app_id = #{appId} " +
                "    AND stat_date_value = #{statDateValue}")
        WcDataPayRoomDayIncomeStatPo queryRoomIncomeStatByDay(@Param("njId") Long njId, 
                                                             @Param("appId") Integer appId, 
                                                             @Param("statDateValue") Integer statDateValue);

        /**
         * 根据周开始时间和周结束时间，统计厅周收入统计数据
         */
        @Select("SELECT " +
                "    SUM(personal_noble_income) as playerVip, " +
                "    SUM(official_hall_income) as officialRoom, " +
                "    SUM(all_income) as sum, " +
                "    SUM(personal_hall_income) as player, " +
                "    SUM(sign_hall_income) as roomGift, " +
                "    SUM(room_noble_income) as roomVip " +
                "FROM wavecenter_data_pay_room_day " +
                "WHERE room_id = #{njId} " +
                "    AND app_id = #{appId} " +
                "    AND stat_date_value BETWEEN #{startDateValue} AND #{endDateValue}")
        WcDataPayRoomDayIncomeStatPo queryRoomIncomeStatByWeek(@Param("njId") Long njId, 
                                                              @Param("appId") Integer appId, 
                                                              @Param("startDateValue") Integer startDateValue, 
                                                              @Param("endDateValue") Integer endDateValue);

        /**
         * 根据月开始时间和月结束时间，统计厅月收入统计数据
         */
        @Select("SELECT " +
                "    SUM(personal_noble_income) as playerVip, " +
                "    SUM(official_hall_income) as officialRoom, " +
                "    SUM(all_income) as sum, " +
                "    SUM(sign_hall_income) as roomGift, " +
                "    SUM(personal_hall_income) as player, " +
                "    SUM(room_noble_income) as roomVip " +
                "FROM wavecenter_data_pay_room_day " +
                "WHERE room_id = #{njId} " +
                "    AND app_id = #{appId} " +
                "    AND stat_date_value BETWEEN #{startDateValue} AND #{endDateValue}")
        WcDataPayRoomDayIncomeStatPo queryRoomIncomeStatByMonth(@Param("njId") Long njId, 
                                                               @Param("appId") Integer appId, 
                                                               @Param("startDateValue") Integer startDateValue, 
                                                               @Param("endDateValue") Integer endDateValue);

}
