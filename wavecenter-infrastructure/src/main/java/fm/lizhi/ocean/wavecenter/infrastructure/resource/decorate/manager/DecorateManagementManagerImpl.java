package fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestCreateAvatarWidget;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestCreateRoomBackground;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.response.ResponseCreateAvatarWidget;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.response.ResponseCreateRoomBackground;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.DecorateManagementRemote;
import fm.lizhi.ocean.wavecenter.service.resource.decorate.manager.DecorateManagementManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class DecorateManagementManagerImpl implements DecorateManagementManager {

    @Autowired
    private DecorateManagementRemote decorateManagementRemote;

    @Override
    public Result<ResponseCreateRoomBackground> createRoomBackground(RequestCreateRoomBackground request) {
        log.info("createRoomBackground request={}", JsonUtils.toJsonString(request));
        Result<ResponseCreateRoomBackground> result = decorateManagementRemote.createRoomBackground(request);
        if (RpcResult.isFail(result)) {
            log.info("createRoomBackground fail, rCode={}, message={}", result.rCode(), result.getMessage());
        } else {
            log.info("createRoomBackground success, id={}", result.target());
        }
        return result;
    }

    @Override
    public Result<ResponseCreateAvatarWidget> createAvatarWidget(RequestCreateAvatarWidget request) {
        log.info("createAvatarWidget request={}", JsonUtils.toJsonString(request));
        Result<ResponseCreateAvatarWidget> result = decorateManagementRemote.createAvatarWidget(request);
        if (RpcResult.isFail(result)) {
            log.info("createAvatarWidget fail, rCode={}, message={}", result.rCode(), result.getMessage());
        } else {
            log.info("createAvatarWidget success, id={}", result.target());
        }
        return result;
    }
}
