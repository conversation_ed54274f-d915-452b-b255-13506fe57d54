package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.constants;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityBigClassTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import xm.fm.lizhi.live.pp.enums.officialseat.PrimaryActivityConfigType;

import java.util.EnumMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum ActivityBigClassTypeMapping {

    /**
     * 常规活动
     */
    NORMAL(ActivityBigClassTypeEnum.NORMAL.getType(), new EnumMap<BusinessEvnEnum, Integer>(BusinessEvnEnum.class) {
        {
            put(BusinessEvnEnum.XIMI, PrimaryActivityConfigType.TYPE_NORMAL.getCode());
        }
    }),

    /**
     * 厅战
     */
    HALL_BATTLE(ActivityBigClassTypeEnum.ROOM_WAR.getType(),  new EnumMap<BusinessEvnEnum, Integer>(BusinessEvnEnum.class) {
        {
            put(BusinessEvnEnum.XIMI, PrimaryActivityConfigType.TYPE_ROOM_WAR.getCode());
        }
    }),

    ;

    private final Integer waveType;

    private final EnumMap<BusinessEvnEnum, Integer> bizValueMap;

    public static Integer bizValue2WaveType(Integer bizType) {
        BusinessEvnEnum businessEvnEnum = ContextUtils.getBusinessEvnEnum();
        ActivityBigClassTypeMapping[] values = values();
        for (ActivityBigClassTypeMapping value : values) {
            Map<BusinessEvnEnum, Integer> bizMap = value.getBizValueMap();
            Integer mBizValue = bizMap.get(businessEvnEnum);
            if (mBizValue != null && mBizValue.equals(bizType)) {
                return value.getWaveType();
            }
        }
        return -1;
    }


    public static Integer waveType2Biz(int waveType) {
        BusinessEvnEnum businessEvnEnum = ContextUtils.getBusinessEvnEnum();
        for (ActivityBigClassTypeMapping enumItem : values()) {
            if (enumItem.getWaveType() == waveType) {
                return enumItem.bizValueMap.get(businessEvnEnum);
            }
        }
        return -1;
    }

}
