package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.dao;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityAnnouncementDeployRecord;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityAnnouncementDeployRecordExample;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.mapper.ActivityAnnouncementDeployRecordMapper;
import joptsimple.internal.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public class ActivityRoomAnnouncementDeployDao {

    @Autowired
    private ActivityAnnouncementDeployRecordMapper activityAnnouncementDeployRecordMapper;

    public boolean updateAnnouncementDeployStatus(long id, int originalStatus, int targetStatus) {
        ActivityAnnouncementDeployRecord record = new ActivityAnnouncementDeployRecord();
        record.setStatus(targetStatus);

        ActivityAnnouncementDeployRecordExample example = new ActivityAnnouncementDeployRecordExample();
        example.createCriteria()
                .andStatusEqualTo(originalStatus)
                .andIdEqualTo(id);

        return activityAnnouncementDeployRecordMapper.updateByExample(record, example) > 0;
    }

    /**
     * 获取房间公告配置列表
     *
     * @param time           时间
     * @param statusList     状态列表
     * @param activityStatus 活动状态
     * @return 结果列表
     */
    public List<ActivityAnnouncementDeployRecord> getAnnouncementDeployList(Long time, List<Integer> statusList, int activityStatus) {
        ActivityAnnouncementDeployRecordExample example = new ActivityAnnouncementDeployRecordExample();
        ActivityAnnouncementDeployRecordExample.Criteria criteria = example.createCriteria()
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andStatusIn(statusList);

        //活动未开始
        if (activityStatus == 1) {
            criteria.andStartTimeGreaterThanOrEqualTo(new Date(time));
        } else if (activityStatus == 2) {
            criteria.andEndTimeLessThanOrEqualTo(new Date(time));
        }
        return activityAnnouncementDeployRecordMapper.selectByExample(example);
    }

    /**
     * 保存原始房间公告
     *
     * @param id                   主键ID
     * @param originalAnnouncement 原始房间公告
     * @param originalImageUrls    原始房间公告图片
     * @return 结果
     */
    public boolean saveOriginalAnnouncement(Long id, String originalAnnouncement, List<String> originalImageUrls) {
        ActivityAnnouncementDeployRecordExample example = new ActivityAnnouncementDeployRecordExample();
        example.createCriteria().andIdEqualTo(id);
        ActivityAnnouncementDeployRecord record = new ActivityAnnouncementDeployRecord();
        record.setModifyTime(new Date());
        if (originalAnnouncement != null) {
            record.setOriginalAnnouncement(originalAnnouncement);
        }
        if (originalImageUrls != null) {
            record.setOriginalAnnouncementImgUrl(Strings.join(originalImageUrls, ","));
        }

        return activityAnnouncementDeployRecordMapper.updateByExample(record, example) > 0;

    }

    public boolean updateOriginalAnnouncementByActivityId(Long activityId, String announcement, String announcementImgUrls) {
        ActivityAnnouncementDeployRecordExample example = new ActivityAnnouncementDeployRecordExample();
        example.createCriteria().andActivityIdEqualTo(activityId);
        ActivityAnnouncementDeployRecord record = new ActivityAnnouncementDeployRecord();
        record.setModifyTime(new Date());
        record.setActivityId(activityId);
        if (announcement == null) {
            announcement = "";
        }
        if (announcementImgUrls == null) {
            announcementImgUrls = "";
        }
        record.setOriginalAnnouncementImgUrl(announcementImgUrls);
        record.setOriginalAnnouncement(announcement);

        return activityAnnouncementDeployRecordMapper.updateByExample(record, example) > 0;
    }

    /**
     * 查询活动对应的房间公告配置
     *
     * @param activityId 活动ID
     * @return 结果
     */
    public ActivityAnnouncementDeployRecord getAnnouncementDeployRecord(Long activityId) {
        ActivityAnnouncementDeployRecord record = new ActivityAnnouncementDeployRecord();
        record.setActivityId(activityId);
        return activityAnnouncementDeployRecordMapper.selectOne(record);
    }

    /**
     * 删除活动对应的房间公告配置
     *
     * @param activityId 活动ID
     * @return true：成功
     */
    public boolean deleteAnnouncementByActivityId(Long activityId) {
        ActivityAnnouncementDeployRecordExample example = new ActivityAnnouncementDeployRecordExample();
        example.createCriteria().andActivityIdEqualTo(activityId);
        return activityAnnouncementDeployRecordMapper.deleteByExample(example) > 0;
    }
}
