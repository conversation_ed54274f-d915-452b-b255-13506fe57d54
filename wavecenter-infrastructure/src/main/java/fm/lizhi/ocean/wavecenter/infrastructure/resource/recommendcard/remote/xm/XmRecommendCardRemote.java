package fm.lizhi.ocean.wavecenter.infrastructure.resource.recommendcard.remote.xm;

import com.google.common.collect.Lists;
import com.google.protobuf.InvalidProtocolBufferException;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.live.room.xm.api.RecommendationCardManageService;
import fm.lizhi.live.room.xm.dto.PageDto;
import fm.lizhi.live.room.xm.dto.recommendationcard.RecommendationCardUseRecordDto;
import fm.lizhi.live.room.xm.dto.recommendationcard.UserRecommendationCardRecordDto;
import fm.lizhi.live.room.xm.dto.req.GetRecommendationCardUseRecordReq;
import fm.lizhi.live.room.xm.dto.req.GetUserRecommendationCardRecordReq;
import fm.lizhi.live.room.xm.dto.req.SumUserRecommendationCardExpireByTimeReq;
import fm.lizhi.live.room.xm.protocol.RecommendationCardManageServiceProto;
import fm.lizhi.live.room.xm.services.RecommendationCardSpringService;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.resource.constants.ResourceDeliverStatusEnum;
import fm.lizhi.ocean.wavecenter.api.resource.constants.ResourceSendTypeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.*;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.request.*;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.response.RewardResultBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.CountDownLatchWrapper;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.ThreadConstants;
import fm.lizhi.ocean.wavecenter.common.utils.WcAssert;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.recommendcard.remote.RecommendCardRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.IUserRemote;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto.GetUseRecordParamDTO;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto.RecommendAllocationRecordDTO;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto.RecommendCardUseRecordDTO;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto.UserRecommendCardStockDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/21 15:27
 */
@Slf4j
@Component
public class XmRecommendCardRemote implements RecommendCardRemote {

    @Autowired
    private RecommendationCardSpringService recommendationCardService;
    @Autowired
    private RecommendationCardManageService recommendationCardManageService;


    private static final int SEND_RECORD_STATUS_REMOVE = 3;

    @Autowired
    private IUserRemote userRemote;

    @Override
    public List<UserRecommendCardStockDTO> getUserStock(List<Long> userIds) {
        return getUserRecommendCardStockCore(userIds);
    }

    @Override
    public Integer getExpireNum(Long userId, Date startDate, Date endDate) {
        Result<Integer> result = recommendationCardService.sumUserExpireByTime(new SumUserRecommendationCardExpireByTimeReq()
                .setUserId(userId)
                .setEndTime(endDate)
                .setStartTime(startDate));
        if (RpcResult.isFail(result)) {
            log.warn("sumUserExpireByTime fail. rCode={},userId={},startDate={},endDate={}", result.rCode(), userId, startDate, endDate);
            return 0;
        }
        return result.target();
    }

    @Override
    public PageBean<RecommendCardUseRecordBean> getUseRecordForManagement(RequestGetUseRecord request) {
        RecommendationCardManageServiceProto.RecommendationCardUseRecordListRequest.Builder builder = RecommendationCardManageServiceProto.RecommendationCardUseRecordListRequest.newBuilder();
        if (request.getUserId() != null) {
            builder.setUserId(request.getUserId());
        }
        builder.setPageNumber(request.getPageNo()).setPageSize(request.getPageSize());
        Result<RecommendationCardManageServiceProto.ResponseGetRecommendationCardUseRecordList> result = recommendationCardManageService.getRecommendationCardUseRecordList(builder.build());
        if (RpcResult.isFail(result)) {
            log.warn("getRecommendationCardUseRecordList fail. rCode={},request={}", result.rCode(), JsonUtil.dumps(request));
            return PageBean.empty();
        }

        List<RecommendCardUseRecordBean> beanList = new ArrayList<>();
        for (RecommendationCardManageServiceProto.UseRecord useRecord : result.target().getRecordList()) {
            RecommendCardUseRecordBean bean = new RecommendCardUseRecordBean();
            bean.setId(useRecord.getId());
            bean.setNjId(useRecord.getUserId());
            bean.setNjName(useRecord.getUserName());
            bean.setUseTime(DateUtil.formatStrToNormalDate(useRecord.getUseTime()));
            bean.setRecommendationTime(useRecord.getRecommendationTime());
            bean.setUseNum(useRecord.getNums());
            bean.setPosition(String.valueOf(useRecord.getRecommendationIndex()));
            beanList.add(bean);
        }

        return PageBean.of((int) result.target().getTotal(), beanList);
    }

    @Override
    public PageBean<RecommendCardSendRecordBean> getSendRecord(RequestGetSendRecord request) {
        RecommendationCardManageServiceProto.GetRecommendationCardSendRecordListRequest.Builder builder = RecommendationCardManageServiceProto.GetRecommendationCardSendRecordListRequest.newBuilder();
        builder.setPageNumber(request.getPageNo());
        builder.setPageSize(request.getPageSize());
        builder.setStartTime(DateUtil.formatDateNormal(DateUtil.getDayStart(request.getStartTime())));
        builder.setEndTime(DateUtil.formatDateNormal(DateUtil.getDayEnd(request.getEndTime())));
        builder.setUserId(0);
        if (request.getUserId() != null) {
            builder.setUserId(request.getUserId());
        }

        Result<RecommendationCardManageServiceProto.ResponseGetRecommendationCardSendRecordList> result = recommendationCardManageService.getRecommendationCardSendRecordList(builder.build());
        if (RpcResult.isFail(result)) {
            log.warn("getRecommendationCardSendRecordList fail. rCode={},request={}", result.rCode(), JsonUtil.dumps(request));
            return PageBean.empty();
        }

        List<RecommendCardSendRecordBean> beanList = new ArrayList<>();
        for (RecommendationCardManageServiceProto.SendRecord sendRecord : result.target().getRecordList()) {
            beanList.add(buildSendRecordBean(sendRecord));
        }

        return PageBean.of((int) result.target().getTotal(), beanList);
    }

    private RecommendCardSendRecordBean buildSendRecordBean(RecommendationCardManageServiceProto.SendRecord sendRecord){
        RecommendCardSendRecordBean bean = new RecommendCardSendRecordBean();
        bean.setExpireTime(new Date(sendRecord.getEndTime()));
        if (sendRecord.getRewardType() == 0) {
            bean.setSendType(ResourceSendTypeEnum.OFFICIAL.getValue());
        } else if (sendRecord.getRewardType() == 1) {
            bean.setSendType(ResourceSendTypeEnum.FAMILY.getValue());
        } else {
            bean.setSendType(ResourceSendTypeEnum.UNKNOWN.getValue());
        }
        bean.setId(sendRecord.getSendRecordId());
        bean.setUserId(sendRecord.getUserId());
        bean.setUserBand(sendRecord.getBand());
        bean.setUserName(sendRecord.getUserName());
        bean.setSendTime(DateUtil.formatStrToNormalDate(sendRecord.getSendTime()));
        bean.setSendNum(sendRecord.getNums());
        bean.setReason(sendRecord.getReason());
        if (sendRecord.getStatus() == 1) {
            bean.setStatus(ResourceDeliverStatusEnum.DELIVER_SUCCESS.getValue());
        } else if (sendRecord.getStatus() == 2) {
            bean.setStatus(ResourceDeliverStatusEnum.RECYCLE.getValue());
        } else if (sendRecord.getStatus() == 3) {
            bean.setStatus(ResourceDeliverStatusEnum.DEDUCTION.getValue());
        } else if (sendRecord.getStatus() == 4) {
            bean.setStatus(ResourceDeliverStatusEnum.EXPIRATION.getValue());
        } else {
            bean.setStatus(ResourceDeliverStatusEnum.UNKNOWN.getValue());
        }
        return bean;
    }

    @Override
    public List<BatchSendUserResultBean> batchSend(RequestBatchSend request) {
        List<SendRecommendCardBean> sendRecommendCards = request.getSendRecommendCards();
        if (CollectionUtils.isEmpty(sendRecommendCards)) {
            return Collections.emptyList();
        }

        List<BatchSendUserResultBean> resultList = new CopyOnWriteArrayList<>();
        List<List<SendRecommendCardBean>> partition = Lists.partition(sendRecommendCards, 20);
        CountDownLatchWrapper downLatchWrapper = new CountDownLatchWrapper(ThreadConstants.batchSendRecommendCardPool, 4, partition.size());

        for (List<SendRecommendCardBean> cardBeans : partition) {
            downLatchWrapper.submit(()->{
                for (SendRecommendCardBean sendRecommendCard : cardBeans) {
                    RecommendationCardManageServiceProto.SendUserRecommendationCardRequest.Builder builder = RecommendationCardManageServiceProto.SendUserRecommendationCardRequest.newBuilder();
                    builder.setNums(sendRecommendCard.getSendNum())
                            .setOperator(request.getOperator())
                            .setValid(sendRecommendCard.getExpireDay())
                            .setUserId(sendRecommendCard.getUserId())
                            .setReason(sendRecommendCard.getReason());
                    Result<RecommendationCardManageServiceProto.ResponseSendUserRecommendationCard> result = recommendationCardManageService.sendUserRecommendationCard(builder.build());
                    log.info("batchSendRecommendCard rCode={}, request={}", result.rCode(), sendRecommendCard);
                    if (RpcResult.isFail(result)) {
                        resultList.add(new BatchSendUserResultBean().setUserId(sendRecommendCard.getUserId())
                                .setResultCode(result.rCode())
                                .setMsg("服务异常"));
                    }
                }
            });
        }

        downLatchWrapper.await();
        return resultList;
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.XIMI;
    }

    @Override
    public RewardResultBean rewardRecommendCard(Long operatorUserId, List<RecommendCardRewardBean> rewardBeans) {

        //计算总发放数量
        int totalRewardNums = rewardBeans.stream().mapToInt(RecommendCardRewardBean::getNum).sum();

        //获取总库存数量
        int totalStockNums = getUserRecommendCardStockCore(Collections.singletonList(operatorUserId)).stream().mapToInt(UserRecommendCardStockDTO::getStock).sum();
        log.info("operatorUserId: {}, totalRewardNums: {}, totalStockNums: {}", operatorUserId, totalRewardNums, totalStockNums);

        if (totalRewardNums > totalStockNums) {
            return new RewardResultBean().setCode(REWARD_RECOMMEND_CARD_NOT_ENOUGH).setRewardResult("库存不足");
        }

        List<SimpleUserDto> simpleUserDtos = userRemote.getSimpleUserByIds(rewardBeans.stream().map(RecommendCardRewardBean::getTargetUserId).collect(Collectors.toList()));
        Map<Long, String> userMap = simpleUserDtos.stream().collect(Collectors.toMap(SimpleUserDto::getId, SimpleUserDto::getName, (a, b) -> a));


        for (RecommendCardRewardBean rewardRecommendationCard : rewardBeans) {
            Result<RecommendationCardManageServiceProto.ResponseRemoveUserRecommendationCard> removeUserRecommendationCardResult =
                    recommendationCardManageService.removeUserRecommendationCard(
                            operatorUserId,
                            rewardRecommendationCard.getNum(),
                            String.format("分配给房间[%s]", userMap.get(rewardRecommendationCard.getTargetUserId())),
                            SEND_RECORD_STATUS_REMOVE,
                            rewardRecommendationCard.getTargetUserId());
            if (RpcResult.isFail(removeUserRecommendationCardResult)) {
                return buildErrorMsg(removeUserRecommendationCardResult);
            }
        }

        return new RewardResultBean();
    }

    private RewardResultBean buildErrorMsg(
            Result<RecommendationCardManageServiceProto.ResponseRemoveUserRecommendationCard> removeUserRecommendationCardResult) {
        try {
            RecommendationCardManageServiceProto.ResponseRemoveUserRecommendationCard response =
                        RecommendationCardManageServiceProto.ResponseRemoveUserRecommendationCard.parseFrom(removeUserRecommendationCardResult.rawData());
            log.warn("removeUserRecommendationCardResult: {}", response.getMsg());
            return new RewardResultBean().setCode(REWARD_RECOMMEND_CARD_ERROR).setRewardResult(response.getMsg());
        } catch (InvalidProtocolBufferException e) {
            log.error("removeUserRecommendationCardResult: {}", e.getMessage());
        }
        return new RewardResultBean().setCode(REWARD_RECOMMEND_CARD_ERROR).setRewardResult("发放失败");
    }

    @NotNull
    private List<UserRecommendCardStockDTO> getUserRecommendCardStockCore(List<Long> userIds) {
        Result<List<fm.lizhi.live.room.xm.dto.recommendationcard.UserRecommendCardStockDTO>> result = recommendationCardService.batchGetUserStock(userIds);
        if (RpcResult.isFail(result)) {
            log.warn("batchGetUserStock fail. rCode={},userIds={}", result.rCode(), JsonUtil.dumps(userIds));
            return Collections.emptyList();
        }

        return result.target().stream().map(v -> new UserRecommendCardStockDTO()
                        .setUserId(v.getUserId())
                        .setStock(v.getStock()))
                .collect(Collectors.toList());
    }

    @Override
    public boolean recycle(RequestRecycle request) {
        WcAssert.notNull(request.getSendRecordId(), "sendRecordId is null");
        RecommendationCardManageServiceProto.RecycleUserRecommendationCardRequest.Builder builder = RecommendationCardManageServiceProto.RecycleUserRecommendationCardRequest.newBuilder();
        builder.setSendRecordId(request.getSendRecordId());
        builder.setOperator(request.getOperator());
        Result<RecommendationCardManageServiceProto.ResponseRecycleUserRecommendationCard> result = recommendationCardManageService.recycleUserRecommendationCard(builder.build());
        if (RpcResult.isFail(result)) {
            log.warn("recycle error, rCode={},sendRecordId={}", result.rCode(), request.getSendRecordId());
            return false;
        }
        return true;
    }

    @Override
    public PageBean<RecommendAllocationRecordDTO> getAllocationRecord(RequestGetAllocationRecord request) {
        GetUserRecommendationCardRecordReq req = new GetUserRecommendationCardRecordReq();
        req.setUserId(request.getFamilyUserId());
        req.setStartTime(request.getStartDate());
        req.setEndTime(request.getEndDate());
        req.setPageNumber(request.getPageNum());
        req.setPageSize(request.getPageSize());

        Result<PageDto<UserRecommendationCardRecordDto>> result = recommendationCardService.getUserRecommendationCardRecord(req);
        if (RpcResult.isFail(result)) {
            log.warn("getAllocationRecord error, rCode={},request={}", result.rCode(), request);
            return PageBean.empty();
        }

        List<RecommendAllocationRecordDTO> dtos = new ArrayList<>();
        for (UserRecommendationCardRecordDto pb : result.target().getList()) {
            RecommendAllocationRecordDTO dto = new RecommendAllocationRecordDTO();
            dto.setAllocationTime(new Date(pb.getTime()));
            dto.setNums(pb.getNums());
            dto.setDetail(pb.getDesc());
            dtos.add(dto);
        }

        return PageBean.of(result.target().getTotal(), dtos);
    }

    @Override
    public PageBean<RecommendCardUseRecordDTO> getUseRecord(GetUseRecordParamDTO paramDTO) {
        GetRecommendationCardUseRecordReq req = new GetRecommendationCardUseRecordReq();
        req.setUserIds(paramDTO.getUserIds());
        req.setCreateTimeAsc(paramDTO.isCreateTimeAsc()?1:0);
        req.setPageNumber(paramDTO.getPageNumber());
        req.setPageSize(paramDTO.getPageSize());

        Result<PageDto<RecommendationCardUseRecordDto>> result = recommendationCardService.getRecommendationCardUseRecord(req);
        if (RpcResult.isFail(result)) {
            log.warn("getUseRecord error, rCode={},request={}", result.rCode(), req);
            return PageBean.empty();
        }

        List<RecommendCardUseRecordDTO> dtos = new ArrayList<>();
        for (RecommendationCardUseRecordDto pb : result.target().getList()) {
            RecommendCardUseRecordDTO dto = new RecommendCardUseRecordDTO();
            dto.setId(pb.getId());
            dto.setNjId(pb.getUserId());
            dto.setNums(pb.getNums());
            dto.setRecommendIndex(pb.getRecommendIndex());
            dto.setPosition("热推"+pb.getRecommendIndex());
            dto.setRecommendTime(pb.getRecommendTime());
            dto.setUseTime(pb.getCreateTime());
            dtos.add(dto);
        }

        return PageBean.of(result.target().getTotal(), dtos);
    }
}
