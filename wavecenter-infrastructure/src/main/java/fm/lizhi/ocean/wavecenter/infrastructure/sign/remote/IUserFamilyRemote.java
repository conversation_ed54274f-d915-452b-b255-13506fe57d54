package fm.lizhi.ocean.wavecenter.infrastructure.sign.remote;

import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.base.remote.IRemote;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/4/10 17:36
 */
public interface IUserFamilyRemote extends IRemote {

    UserInFamilyBean getUserInFamily(long userId);

    Optional<FamilyBean> getUserFamily(long userId);

    Optional<FamilyBean> getFamily(long familyId);

    Integer countCanOpenRoomNum(long familyId);

    Optional<FamilyBean> getFamilyByUserId(long userId);

    /**
     * 根据家族ID获取家族信息，带缓存
     * @param familyId
     * @return
     */
    Optional<FamilyBean> getFamilyByCache(long familyId);

    /**
     * 获取当前陪玩签约的厅主id
     *
     * @param userId 用户id
     * @return 厅主id
     */
    Optional<Long> playerCurSignNj(long userId);

}
