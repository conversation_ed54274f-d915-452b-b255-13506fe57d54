package fm.lizhi.ocean.wavecenter.infrastructure.grow.datastore.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WcFamilyLevelConfigExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_family_level_config
     *
     * @mbg.generated Mon Apr 07 14:42:29 CST 2025
     */
    public WcFamilyLevelConfigExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_family_level_config
     *
     * @mbg.generated Mon Apr 07 14:42:29 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_family_level_config
     *
     * @mbg.generated Mon Apr 07 14:42:29 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_family_level_config
     *
     * @mbg.generated Mon Apr 07 14:42:29 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_family_level_config
     *
     * @mbg.generated Mon Apr 07 14:42:29 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_family_level_config
     *
     * @mbg.generated Mon Apr 07 14:42:29 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_family_level_config
     *
     * @mbg.generated Mon Apr 07 14:42:29 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_family_level_config
     *
     * @mbg.generated Mon Apr 07 14:42:29 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_family_level_config
     *
     * @mbg.generated Mon Apr 07 14:42:29 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_family_level_config
     *
     * @mbg.generated Mon Apr 07 14:42:29 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_family_level_config
     *
     * @mbg.generated Mon Apr 07 14:42:29 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Class getEntityClass() {
        return fm.lizhi.ocean.wavecenter.infrastructure.grow.datastore.entity.WcFamilyLevelConfig.class;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wavecenter_family_level_config
     *
     * @mbg.generated Mon Apr 07 14:42:29 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andLevelNameIsNull() {
            addCriterion("level_name is null");
            return (Criteria) this;
        }

        public Criteria andLevelNameIsNotNull() {
            addCriterion("level_name is not null");
            return (Criteria) this;
        }

        public Criteria andLevelNameEqualTo(String value) {
            addCriterion("level_name =", value, "levelName");
            return (Criteria) this;
        }

        public Criteria andLevelNameNotEqualTo(String value) {
            addCriterion("level_name <>", value, "levelName");
            return (Criteria) this;
        }

        public Criteria andLevelNameGreaterThan(String value) {
            addCriterion("level_name >", value, "levelName");
            return (Criteria) this;
        }

        public Criteria andLevelNameGreaterThanOrEqualTo(String value) {
            addCriterion("level_name >=", value, "levelName");
            return (Criteria) this;
        }

        public Criteria andLevelNameLessThan(String value) {
            addCriterion("level_name <", value, "levelName");
            return (Criteria) this;
        }

        public Criteria andLevelNameLessThanOrEqualTo(String value) {
            addCriterion("level_name <=", value, "levelName");
            return (Criteria) this;
        }

        public Criteria andLevelNameLike(String value) {
            addCriterion("level_name like", value, "levelName");
            return (Criteria) this;
        }

        public Criteria andLevelNameNotLike(String value) {
            addCriterion("level_name not like", value, "levelName");
            return (Criteria) this;
        }

        public Criteria andLevelNameIn(List<String> values) {
            addCriterion("level_name in", values, "levelName");
            return (Criteria) this;
        }

        public Criteria andLevelNameNotIn(List<String> values) {
            addCriterion("level_name not in", values, "levelName");
            return (Criteria) this;
        }

        public Criteria andLevelNameBetween(String value1, String value2) {
            addCriterion("level_name between", value1, value2, "levelName");
            return (Criteria) this;
        }

        public Criteria andLevelNameNotBetween(String value1, String value2) {
            addCriterion("level_name not between", value1, value2, "levelName");
            return (Criteria) this;
        }

        public Criteria andLevelValueIsNull() {
            addCriterion("level_value is null");
            return (Criteria) this;
        }

        public Criteria andLevelValueIsNotNull() {
            addCriterion("level_value is not null");
            return (Criteria) this;
        }

        public Criteria andLevelValueEqualTo(Integer value) {
            addCriterion("level_value =", value, "levelValue");
            return (Criteria) this;
        }

        public Criteria andLevelValueNotEqualTo(Integer value) {
            addCriterion("level_value <>", value, "levelValue");
            return (Criteria) this;
        }

        public Criteria andLevelValueGreaterThan(Integer value) {
            addCriterion("level_value >", value, "levelValue");
            return (Criteria) this;
        }

        public Criteria andLevelValueGreaterThanOrEqualTo(Integer value) {
            addCriterion("level_value >=", value, "levelValue");
            return (Criteria) this;
        }

        public Criteria andLevelValueLessThan(Integer value) {
            addCriterion("level_value <", value, "levelValue");
            return (Criteria) this;
        }

        public Criteria andLevelValueLessThanOrEqualTo(Integer value) {
            addCriterion("level_value <=", value, "levelValue");
            return (Criteria) this;
        }

        public Criteria andLevelValueIn(List<Integer> values) {
            addCriterion("level_value in", values, "levelValue");
            return (Criteria) this;
        }

        public Criteria andLevelValueNotIn(List<Integer> values) {
            addCriterion("level_value not in", values, "levelValue");
            return (Criteria) this;
        }

        public Criteria andLevelValueBetween(Integer value1, Integer value2) {
            addCriterion("level_value between", value1, value2, "levelValue");
            return (Criteria) this;
        }

        public Criteria andLevelValueNotBetween(Integer value1, Integer value2) {
            addCriterion("level_value not between", value1, value2, "levelValue");
            return (Criteria) this;
        }

        public Criteria andMinExpIsNull() {
            addCriterion("min_exp is null");
            return (Criteria) this;
        }

        public Criteria andMinExpIsNotNull() {
            addCriterion("min_exp is not null");
            return (Criteria) this;
        }

        public Criteria andMinExpEqualTo(Integer value) {
            addCriterion("min_exp =", value, "minExp");
            return (Criteria) this;
        }

        public Criteria andMinExpNotEqualTo(Integer value) {
            addCriterion("min_exp <>", value, "minExp");
            return (Criteria) this;
        }

        public Criteria andMinExpGreaterThan(Integer value) {
            addCriterion("min_exp >", value, "minExp");
            return (Criteria) this;
        }

        public Criteria andMinExpGreaterThanOrEqualTo(Integer value) {
            addCriterion("min_exp >=", value, "minExp");
            return (Criteria) this;
        }

        public Criteria andMinExpLessThan(Integer value) {
            addCriterion("min_exp <", value, "minExp");
            return (Criteria) this;
        }

        public Criteria andMinExpLessThanOrEqualTo(Integer value) {
            addCriterion("min_exp <=", value, "minExp");
            return (Criteria) this;
        }

        public Criteria andMinExpIn(List<Integer> values) {
            addCriterion("min_exp in", values, "minExp");
            return (Criteria) this;
        }

        public Criteria andMinExpNotIn(List<Integer> values) {
            addCriterion("min_exp not in", values, "minExp");
            return (Criteria) this;
        }

        public Criteria andMinExpBetween(Integer value1, Integer value2) {
            addCriterion("min_exp between", value1, value2, "minExp");
            return (Criteria) this;
        }

        public Criteria andMinExpNotBetween(Integer value1, Integer value2) {
            addCriterion("min_exp not between", value1, value2, "minExp");
            return (Criteria) this;
        }

        public Criteria andLevelIconIsNull() {
            addCriterion("level_icon is null");
            return (Criteria) this;
        }

        public Criteria andLevelIconIsNotNull() {
            addCriterion("level_icon is not null");
            return (Criteria) this;
        }

        public Criteria andLevelIconEqualTo(String value) {
            addCriterion("level_icon =", value, "levelIcon");
            return (Criteria) this;
        }

        public Criteria andLevelIconNotEqualTo(String value) {
            addCriterion("level_icon <>", value, "levelIcon");
            return (Criteria) this;
        }

        public Criteria andLevelIconGreaterThan(String value) {
            addCriterion("level_icon >", value, "levelIcon");
            return (Criteria) this;
        }

        public Criteria andLevelIconGreaterThanOrEqualTo(String value) {
            addCriterion("level_icon >=", value, "levelIcon");
            return (Criteria) this;
        }

        public Criteria andLevelIconLessThan(String value) {
            addCriterion("level_icon <", value, "levelIcon");
            return (Criteria) this;
        }

        public Criteria andLevelIconLessThanOrEqualTo(String value) {
            addCriterion("level_icon <=", value, "levelIcon");
            return (Criteria) this;
        }

        public Criteria andLevelIconLike(String value) {
            addCriterion("level_icon like", value, "levelIcon");
            return (Criteria) this;
        }

        public Criteria andLevelIconNotLike(String value) {
            addCriterion("level_icon not like", value, "levelIcon");
            return (Criteria) this;
        }

        public Criteria andLevelIconIn(List<String> values) {
            addCriterion("level_icon in", values, "levelIcon");
            return (Criteria) this;
        }

        public Criteria andLevelIconNotIn(List<String> values) {
            addCriterion("level_icon not in", values, "levelIcon");
            return (Criteria) this;
        }

        public Criteria andLevelIconBetween(String value1, String value2) {
            addCriterion("level_icon between", value1, value2, "levelIcon");
            return (Criteria) this;
        }

        public Criteria andLevelIconNotBetween(String value1, String value2) {
            addCriterion("level_icon not between", value1, value2, "levelIcon");
            return (Criteria) this;
        }

        public Criteria andLevelMedalIsNull() {
            addCriterion("level_medal is null");
            return (Criteria) this;
        }

        public Criteria andLevelMedalIsNotNull() {
            addCriterion("level_medal is not null");
            return (Criteria) this;
        }

        public Criteria andLevelMedalEqualTo(String value) {
            addCriterion("level_medal =", value, "levelMedal");
            return (Criteria) this;
        }

        public Criteria andLevelMedalNotEqualTo(String value) {
            addCriterion("level_medal <>", value, "levelMedal");
            return (Criteria) this;
        }

        public Criteria andLevelMedalGreaterThan(String value) {
            addCriterion("level_medal >", value, "levelMedal");
            return (Criteria) this;
        }

        public Criteria andLevelMedalGreaterThanOrEqualTo(String value) {
            addCriterion("level_medal >=", value, "levelMedal");
            return (Criteria) this;
        }

        public Criteria andLevelMedalLessThan(String value) {
            addCriterion("level_medal <", value, "levelMedal");
            return (Criteria) this;
        }

        public Criteria andLevelMedalLessThanOrEqualTo(String value) {
            addCriterion("level_medal <=", value, "levelMedal");
            return (Criteria) this;
        }

        public Criteria andLevelMedalLike(String value) {
            addCriterion("level_medal like", value, "levelMedal");
            return (Criteria) this;
        }

        public Criteria andLevelMedalNotLike(String value) {
            addCriterion("level_medal not like", value, "levelMedal");
            return (Criteria) this;
        }

        public Criteria andLevelMedalIn(List<String> values) {
            addCriterion("level_medal in", values, "levelMedal");
            return (Criteria) this;
        }

        public Criteria andLevelMedalNotIn(List<String> values) {
            addCriterion("level_medal not in", values, "levelMedal");
            return (Criteria) this;
        }

        public Criteria andLevelMedalBetween(String value1, String value2) {
            addCriterion("level_medal between", value1, value2, "levelMedal");
            return (Criteria) this;
        }

        public Criteria andLevelMedalNotBetween(String value1, String value2) {
            addCriterion("level_medal not between", value1, value2, "levelMedal");
            return (Criteria) this;
        }

        public Criteria andThemColorIsNull() {
            addCriterion("them_color is null");
            return (Criteria) this;
        }

        public Criteria andThemColorIsNotNull() {
            addCriterion("them_color is not null");
            return (Criteria) this;
        }

        public Criteria andThemColorEqualTo(String value) {
            addCriterion("them_color =", value, "themColor");
            return (Criteria) this;
        }

        public Criteria andThemColorNotEqualTo(String value) {
            addCriterion("them_color <>", value, "themColor");
            return (Criteria) this;
        }

        public Criteria andThemColorGreaterThan(String value) {
            addCriterion("them_color >", value, "themColor");
            return (Criteria) this;
        }

        public Criteria andThemColorGreaterThanOrEqualTo(String value) {
            addCriterion("them_color >=", value, "themColor");
            return (Criteria) this;
        }

        public Criteria andThemColorLessThan(String value) {
            addCriterion("them_color <", value, "themColor");
            return (Criteria) this;
        }

        public Criteria andThemColorLessThanOrEqualTo(String value) {
            addCriterion("them_color <=", value, "themColor");
            return (Criteria) this;
        }

        public Criteria andThemColorLike(String value) {
            addCriterion("them_color like", value, "themColor");
            return (Criteria) this;
        }

        public Criteria andThemColorNotLike(String value) {
            addCriterion("them_color not like", value, "themColor");
            return (Criteria) this;
        }

        public Criteria andThemColorIn(List<String> values) {
            addCriterion("them_color in", values, "themColor");
            return (Criteria) this;
        }

        public Criteria andThemColorNotIn(List<String> values) {
            addCriterion("them_color not in", values, "themColor");
            return (Criteria) this;
        }

        public Criteria andThemColorBetween(String value1, String value2) {
            addCriterion("them_color between", value1, value2, "themColor");
            return (Criteria) this;
        }

        public Criteria andThemColorNotBetween(String value1, String value2) {
            addCriterion("them_color not between", value1, value2, "themColor");
            return (Criteria) this;
        }

        public Criteria andBackgroundColorIsNull() {
            addCriterion("background_color is null");
            return (Criteria) this;
        }

        public Criteria andBackgroundColorIsNotNull() {
            addCriterion("background_color is not null");
            return (Criteria) this;
        }

        public Criteria andBackgroundColorEqualTo(String value) {
            addCriterion("background_color =", value, "backgroundColor");
            return (Criteria) this;
        }

        public Criteria andBackgroundColorNotEqualTo(String value) {
            addCriterion("background_color <>", value, "backgroundColor");
            return (Criteria) this;
        }

        public Criteria andBackgroundColorGreaterThan(String value) {
            addCriterion("background_color >", value, "backgroundColor");
            return (Criteria) this;
        }

        public Criteria andBackgroundColorGreaterThanOrEqualTo(String value) {
            addCriterion("background_color >=", value, "backgroundColor");
            return (Criteria) this;
        }

        public Criteria andBackgroundColorLessThan(String value) {
            addCriterion("background_color <", value, "backgroundColor");
            return (Criteria) this;
        }

        public Criteria andBackgroundColorLessThanOrEqualTo(String value) {
            addCriterion("background_color <=", value, "backgroundColor");
            return (Criteria) this;
        }

        public Criteria andBackgroundColorLike(String value) {
            addCriterion("background_color like", value, "backgroundColor");
            return (Criteria) this;
        }

        public Criteria andBackgroundColorNotLike(String value) {
            addCriterion("background_color not like", value, "backgroundColor");
            return (Criteria) this;
        }

        public Criteria andBackgroundColorIn(List<String> values) {
            addCriterion("background_color in", values, "backgroundColor");
            return (Criteria) this;
        }

        public Criteria andBackgroundColorNotIn(List<String> values) {
            addCriterion("background_color not in", values, "backgroundColor");
            return (Criteria) this;
        }

        public Criteria andBackgroundColorBetween(String value1, String value2) {
            addCriterion("background_color between", value1, value2, "backgroundColor");
            return (Criteria) this;
        }

        public Criteria andBackgroundColorNotBetween(String value1, String value2) {
            addCriterion("background_color not between", value1, value2, "backgroundColor");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(Integer value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(Integer value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(Integer value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(Integer value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(Integer value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<Integer> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<Integer> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(Integer value1, Integer value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(Integer value1, Integer value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIsNull() {
            addCriterion("deploy_env is null");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIsNotNull() {
            addCriterion("deploy_env is not null");
            return (Criteria) this;
        }

        public Criteria andDeployEnvEqualTo(String value) {
            addCriterion("deploy_env =", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotEqualTo(String value) {
            addCriterion("deploy_env <>", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvGreaterThan(String value) {
            addCriterion("deploy_env >", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvGreaterThanOrEqualTo(String value) {
            addCriterion("deploy_env >=", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLessThan(String value) {
            addCriterion("deploy_env <", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLessThanOrEqualTo(String value) {
            addCriterion("deploy_env <=", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLike(String value) {
            addCriterion("deploy_env like", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotLike(String value) {
            addCriterion("deploy_env not like", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIn(List<String> values) {
            addCriterion("deploy_env in", values, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotIn(List<String> values) {
            addCriterion("deploy_env not in", values, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvBetween(String value1, String value2) {
            addCriterion("deploy_env between", value1, value2, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotBetween(String value1, String value2) {
            addCriterion("deploy_env not between", value1, value2, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andModifyUserIsNull() {
            addCriterion("modify_user is null");
            return (Criteria) this;
        }

        public Criteria andModifyUserIsNotNull() {
            addCriterion("modify_user is not null");
            return (Criteria) this;
        }

        public Criteria andModifyUserEqualTo(String value) {
            addCriterion("modify_user =", value, "modifyUser");
            return (Criteria) this;
        }

        public Criteria andModifyUserNotEqualTo(String value) {
            addCriterion("modify_user <>", value, "modifyUser");
            return (Criteria) this;
        }

        public Criteria andModifyUserGreaterThan(String value) {
            addCriterion("modify_user >", value, "modifyUser");
            return (Criteria) this;
        }

        public Criteria andModifyUserGreaterThanOrEqualTo(String value) {
            addCriterion("modify_user >=", value, "modifyUser");
            return (Criteria) this;
        }

        public Criteria andModifyUserLessThan(String value) {
            addCriterion("modify_user <", value, "modifyUser");
            return (Criteria) this;
        }

        public Criteria andModifyUserLessThanOrEqualTo(String value) {
            addCriterion("modify_user <=", value, "modifyUser");
            return (Criteria) this;
        }

        public Criteria andModifyUserLike(String value) {
            addCriterion("modify_user like", value, "modifyUser");
            return (Criteria) this;
        }

        public Criteria andModifyUserNotLike(String value) {
            addCriterion("modify_user not like", value, "modifyUser");
            return (Criteria) this;
        }

        public Criteria andModifyUserIn(List<String> values) {
            addCriterion("modify_user in", values, "modifyUser");
            return (Criteria) this;
        }

        public Criteria andModifyUserNotIn(List<String> values) {
            addCriterion("modify_user not in", values, "modifyUser");
            return (Criteria) this;
        }

        public Criteria andModifyUserBetween(String value1, String value2) {
            addCriterion("modify_user between", value1, value2, "modifyUser");
            return (Criteria) this;
        }

        public Criteria andModifyUserNotBetween(String value1, String value2) {
            addCriterion("modify_user not between", value1, value2, "modifyUser");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNull() {
            addCriterion("deleted is null");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNotNull() {
            addCriterion("deleted is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedEqualTo(Integer value) {
            addCriterion("deleted =", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotEqualTo(Integer value) {
            addCriterion("deleted <>", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThan(Integer value) {
            addCriterion("deleted >", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("deleted >=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThan(Integer value) {
            addCriterion("deleted <", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("deleted <=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedIn(List<Integer> values) {
            addCriterion("deleted in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotIn(List<Integer> values) {
            addCriterion("deleted not in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedBetween(Integer value1, Integer value2) {
            addCriterion("deleted between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("deleted not between", value1, value2, "deleted");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wavecenter_family_level_config
     *
     * @mbg.generated do_not_delete_during_merge Mon Apr 07 14:42:29 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wavecenter_family_level_config
     *
     * @mbg.generated Mon Apr 07 14:42:29 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}