package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.remote.hy;

import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.live.pp.live.constant.SaveTopRecommendResultEnum;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.remote.IRecommendCardServiceRemote;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.SendRecommendCardParamDTO;
import hy.fm.lizhi.live.pp.live.api.TopRecommendService;
import hy.fm.lizhi.live.pp.live.constant.TopRecommendCardOperationTypeEnum;
import hy.fm.lizhi.live.pp.live.protocol.TopRecommendProto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
public class HyRecommendCardServiceRemote implements IRecommendCardServiceRemote {

    @Autowired
    private TopRecommendService topRecommendService;

    @Override
    public Result<Void> sendRecommendCard(SendRecommendCardParamDTO param) {
        TopRecommendProto.TopRecommendCardOperationParam cardOperationParam = TopRecommendProto.TopRecommendCardOperationParam.newBuilder()
                .setType(TopRecommendCardOperationTypeEnum.GRANT.getValue())
                .setValidityDays(param.getValid())
                .setReason(param.getReason())
                .setUserId(param.getUserId())
                .setOperationAmount(param.getCount())
                .build();

        Result<TopRecommendProto.ResponseBatchSaveUserTopRecommendCard> result = topRecommendService.batchSaveUserTopRecommendCard(Lists.newArrayList(cardOperationParam));
        if (RpcResult.isFail(result)) {
            log.warn("hy.sendRecommendCard fail. rCode={}, userId={}, count={}", result.rCode(), param.getUserId(), param.getCount());
            return RpcResult.fail(SEND_RECOMMEND_CARD_FAIL);
        }
        for (TopRecommendProto.TopRecommendCardOperationResult operationResult : result.target().getResultList()) {
            int resultCode = operationResult.getResultCode();
            if (!Objects.equals(resultCode, SaveTopRecommendResultEnum.SUCCESS_FLAG.getValue())) {
                SaveTopRecommendResultEnum resultEnum = SaveTopRecommendResultEnum.get(resultCode);
                String message = resultEnum != null ? resultEnum.getName() : null;
                log.warn("hy.sendRecommendCard fail. userId={}, count={}, resultCode={}, message={}", param.getUserId(), param.getCount(), resultCode, message);
                return RpcResult.fail(SEND_RECOMMEND_CARD_FAIL, message);
            }
        }
        return RpcResult.success();
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.HEI_YE;
    }
}
