package fm.lizhi.ocean.wavecenter.infrastructure.file.job;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.google.common.collect.Lists;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.util.CountDownLatchWrapper;
import fm.lizhi.ocean.wavecenter.base.util.ThreadUtils;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.entity.XmLiveGiveGiftActionPo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.XmLiveGiveGiftActionMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.income.manager.GiftIncomeStatManager;
import fm.lizhi.ocean.wavecenter.infrastructure.kafka.message.GiftMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;

@Slf4j
@Component
public class XmLiveGiveGiftActionJob implements JobHandler {


    @Autowired
    private XmLiveGiveGiftActionMapper xmLiveGiveGiftActionMapper;

    @Autowired
    private GiftIncomeStatManager giftIncomeStatManager;


    @Override
    public void execute(JobExecuteContext jobExecuteContext) throws Exception {
        log.info("XmLiveGiveGiftActionJob start");
        String param = jobExecuteContext.getParam();
        if (StringUtils.isBlank(param)) {
            log.info("XmLiveGiveGiftActionJob param is empty");
            return;
        }
        String[] split = param.split(",");
        if (split.length != 3) {
            log.info("XmLiveGiveGiftActionJob split length error");
            return;
        }
        String startDateStr = split[0];
        String endDateStr = split[1];
        String pageSize = split[2];

        Date startDate = DateUtil.getDayStart(DateUtil.formatStrToDate(startDateStr, DateUtil.date));
        Date endDate = DateUtil.getDayEnd(DateUtil.formatStrToDate(endDateStr, DateUtil.date));

        List<DateTime> dayList = cn.hutool.core.date.DateUtil.rangeToList(startDate, endDate, DateField.DAY_OF_YEAR);

        ExecutorService executorService = ThreadUtils.getTtlExecutors("xmLiveGiveGiftActionJob", 30, 30);

        List<List<DateTime>> dayPart = Lists.partition(dayList, 7);
        for (List<DateTime> dateTimes : dayPart) {
            CountDownLatchWrapper countDownLatchWrapper = new CountDownLatchWrapper(executorService, dateTimes.size());
            for (DateTime dateTime : dateTimes) {
                countDownLatchWrapper.submit(()->{
                    Date subStart = DateUtil.getDayStart(dateTime);
                    Date subEnd = DateUtil.getDayEnd(dateTime);
                    addPlaySignCharm(subStart, subEnd, pageSize, executorService);
                    log.info("XmLiveGiveGiftActionJob finish date={}", dateTime.toDateStr());
                });
            }
            countDownLatchWrapper.await(60 * 60 * 4);
        }
        log.info("XmLiveGiveGiftActionJob end");
    }

    @Transactional
    public void addPlaySignCharm(Date startDate, Date endDate, String pageSizeStr, ExecutorService executorService){
        Integer pageSize = Integer.valueOf(pageSizeStr);
        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.XIMI);
        PageList<XmLiveGiveGiftActionPo> pageList = xmLiveGiveGiftActionMapper.pageList(startDate, endDate, 1, pageSize);
        log.info("pageList total={}", pageList.getTotal());
        saveCharm(pageList, executorService);

        while (pageList.getNextPageNumber() > 0) {
            int nextPageNumber = pageList.getNextPageNumber();
            pageList = xmLiveGiveGiftActionMapper.pageList(startDate, endDate, nextPageNumber, pageSize);
            log.info("pageList total={}, nextPageNumber={}", pageList.getTotal(), nextPageNumber);
            saveCharm(pageList, executorService);
        }
    }

    private void saveCharm(PageList<XmLiveGiveGiftActionPo> xmLiveGiveGiftActionPos, ExecutorService executorService){
        if (CollectionUtils.isEmpty(xmLiveGiveGiftActionPos)) {
            log.info("pageList is empty");
            return;
        }
        List<List<XmLiveGiveGiftActionPo>> partition = Lists.partition(xmLiveGiveGiftActionPos, 100);
        CountDownLatchWrapper countDownLatchWrapper = new CountDownLatchWrapper(executorService, partition.size());
        for (List<XmLiveGiveGiftActionPo> list : partition) {
            countDownLatchWrapper.submit(()->{
                for (XmLiveGiveGiftActionPo xmLiveGiveGiftActionPo : list) {

                    GiftMsg giftMsg = new GiftMsg();
                    giftMsg.setTransactionId(xmLiveGiveGiftActionPo.getId());
                    giftMsg.setCreateTime(xmLiveGiveGiftActionPo.getCreateTime().getTime());
                    giftMsg.setRecUserId(xmLiveGiveGiftActionPo.getNjId());
                    giftMsg.setRecTargetUserId(xmLiveGiveGiftActionPo.getRecUserId());
                    giftMsg.setValue(Math.toIntExact(xmLiveGiveGiftActionPo.getValue()));

                    boolean res = giftIncomeStatManager.statGiftCharm(giftMsg);
                    if (!res) {
                        log.info("addPlaySignCharm xmLiveGiveGiftActionPo={}", JsonUtil.dumps(xmLiveGiveGiftActionPo));
                    }
                }
            });
        }
        countDownLatchWrapper.await(60 * 60 * 4);
    }

}
