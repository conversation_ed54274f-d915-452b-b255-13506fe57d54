package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.dao;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityTemplateGeneralFlowResourceBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityTemplateGeneralPageBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityTemplateGeneralProcessBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityTemplateHotPageBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.*;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestCountGeneralActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestPageGeneralActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestPageHotActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseGetGeneralActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.*;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.*;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseGetActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseGetActivityTemplateShelfStatus;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.*;
import fm.lizhi.ocean.wavecenter.datastore.background.mapper.*;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert.*;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.mapper.ext.*;
import fm.lizhi.ocean.wavecenter.infrastructure.common.util.PageListUtils;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.ActivityConfig;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.CommonActivityConfig;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.constants.TemplateNjListType;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.constants.TemplateStatusTaskStatusEnum;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityTemplateFlowResourceDTO;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityTemplateFlowResourceExtraDTO;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityTemplateFlowResourceImageDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 活动模板数据访问对象
 */
@Repository
@Slf4j
public class ActivityTemplateDao {

    @Autowired
    private ActivityConfig activityConfig;

    @Autowired
    private ActivityClassificationDao activityClassificationDao;
    @Autowired
    private ActivityLevelDao activityLevelDao;
    @Autowired
    private ActivityResourceDao activityResourceDao;

    @Autowired
    private ActivityTemplateFlowResourceConverter activityTemplateFlowResourceConverter;
    @Autowired
    private ActivityTemplateFlowResourceExtMapper activityTemplateFlowResourceExtMapper;
    @Autowired
    private ActivityTemplateFlowResourceMapper activityTemplateFlowResourceMapper;

    @Autowired
    private ActivityTemplateFlowResourceImageConverter activityTemplateFlowResourceImageConverter;
    @Autowired
    private ActivityTemplateFlowResourceImageExtMapper activityTemplateFlowResourceImageExtMapper;
    @Autowired
    private ActivityTemplateFlowResourceImageMapper activityTemplateFlowResourceImageMapper;

    @Autowired
    private ActivityTemplateHighlightConverter activityTemplateHighlightConverter;
    @Autowired
    private ActivityTemplateHighlightExtMapper activityTemplateHighlightExtMapper;
    @Autowired
    private ActivityTemplateHighlightMapper activityTemplateHighlightMapper;

    @Autowired
    private ActivityTemplateInfoConverter activityTemplateInfoConverter;
    @Autowired
    private ActivityTemplateInfoExtMapper activityTemplateInfoExtMapper;
    @Autowired
    private ActivityTemplateInfoMapper activityTemplateInfoMapper;

    @Autowired
    private ActivityTemplateProcessConverter activityTemplateProcessConverter;
    @Autowired
    private ActivityTemplateProcessExtMapper activityTemplateProcessExtMapper;
    @Autowired
    private ActivityTemplateProcessMapper activityTemplateProcessMapper;

    @Autowired
    private ActivityTemplateStatusTaskMapper templateStatusTaskMapper;
    @Autowired
    private ActivityTemplateNjListMapper templateNjListMapper;

    /**
     * 创建活动模板
     *
     * @param req 请求
     * @return 活动模板ID
     */
    @Transactional
    public long createTemplate(RequestCreateActivityTemplate req) {
        // 活动信息表
        ActivityTemplateInfo activityTemplateInfo = activityTemplateInfoConverter.toCreateActivityTemplateInfo(req);
        activityTemplateInfoMapper.insert(activityTemplateInfo);
        Long templateId = activityTemplateInfo.getId();
        log.info("created templateId={}", templateId);
        // 活动流程表
        List<ActivityTemplateProcess> processes = batchInsertProcesses(req.getProcesses(), templateId);
        log.info("insertProcessCount={}", processes.size());
        // 活动流量资源表
        List<ActivityTemplateFlowResourceBean> flowResourceBeans = req.getFlowResources();
        List<ActivityTemplateFlowResource> flowResources = batchInsertFlowResources(flowResourceBeans, templateId);
        log.info("insertFlowResourceCount={}", flowResources.size());
        // 活动流量资源图片表
        ArrayList<ActivityTemplateFlowResourceImage> allFlowResourceImages = new ArrayList<>();
        for (int index = 0; index < flowResources.size(); index++) {
            List<ActivityTemplateFlowResourceImageBean> imageBeans = flowResourceBeans.get(index).getImages();
            Long flowResourceId = flowResources.get(index).getId();
            List<ActivityTemplateFlowResourceImage> flowResourceImages = activityTemplateFlowResourceImageConverter
                    .toCreateActivityTemplateFlowResourceImages(imageBeans, flowResourceId);
            allFlowResourceImages.addAll(flowResourceImages);
        }
        if (CollectionUtils.isNotEmpty(allFlowResourceImages)) {
            activityTemplateFlowResourceImageMapper.batchInsert(allFlowResourceImages);
        }
        log.info("insertFlowResourceImageCount={}", allFlowResourceImages.size());
        // 最后返回活动模板ID
        return templateId;
    }

    private List<ActivityTemplateProcess> batchInsertProcesses(
            List<ActivityTemplateProcessBean> processBeans, long templateId) {
        if (CollectionUtils.isEmpty(processBeans)) {
            return Collections.emptyList();
        }
        List<ActivityTemplateProcess> activityTemplateProcesses = activityTemplateProcessConverter
                .toCreateActivityTemplateProcesses(processBeans, templateId);
        activityTemplateProcessMapper.batchInsert(activityTemplateProcesses);
        return activityTemplateProcesses;
    }

    private List<ActivityTemplateFlowResource> batchInsertFlowResources(
            List<ActivityTemplateFlowResourceBean> flowResourceBeans, long templateId) {
        if (CollectionUtils.isEmpty(flowResourceBeans)) {
            return Collections.emptyList();
        }
        List<ActivityTemplateFlowResource> activityTemplateFlowResources = activityTemplateFlowResourceConverter
                .toCreateActivityTemplateFlowResources(flowResourceBeans, templateId);
        activityTemplateFlowResourceMapper.batchInsert(activityTemplateFlowResources);
        return activityTemplateFlowResources;
    }

    /**
     * 活动模板是否不存在
     *
     * @param templateId 活动模板ID
     * @return 是否不存在
     */
    public boolean isTemplateAbsent(long templateId) {
        return getTemplateInfoById(templateId) == null;
    }

    /**
     * 根据活动模板ID获取活动模板信息
     *
     * @param templateId 活动模板ID
     * @return 活动模板信息
     */
    public ActivityTemplateInfo getTemplateInfoById(long templateId) {
        ActivityTemplateInfo getById = new ActivityTemplateInfo();
        getById.setId(templateId);
        return activityTemplateInfoMapper.selectByPrimaryKey(getById);
    }

    /**
     * 更新活动模板. 对于关联表, 如果新旧数据一致, 则不更新; 否则删除旧数据, 插入新数据.
     *
     * @param req 请求
     */
    @Transactional
    public void updateTemplate(RequestUpdateActivityTemplate req) {
        Long templateId = req.getId();
        // 活动信息表
        ActivityTemplateInfo activityTemplateInfo = activityTemplateInfoConverter.toUpdateActivityTemplateInfo(req);
        int updatedTemplateCount = activityTemplateInfoMapper.updateByPrimaryKey(activityTemplateInfo);
        Validate.isTrue(updatedTemplateCount == 1, String.format("更新活动模板失败, templateId=%s", templateId));
        log.info("updated templateId={}", templateId);
        // 需要置空的字段处理
        int updateTimeLimit = activityTemplateInfoExtMapper.updateTimeLimit(activityTemplateInfo.getId()
                , activityTemplateInfo.getActivityDurationLimit()
                , activityTemplateInfo.getActivityStartTimeLimit()
                , activityTemplateInfo.getActivityEndTimeLimit());
        log.info("updateTimeLimit={}", updateTimeLimit);

        // 活动流程表
        List<ActivityTemplateProcessBean> oldProcessBeans = activityTemplateProcessConverter
                .toActivityTemplateProcessBeans(activityTemplateProcessExtMapper.getProcessesByTemplateId(templateId));
        List<ActivityTemplateProcessBean> newProcessBeans = req.getProcesses();
        boolean shouldUpdateProcesses = CollectionUtils.size(oldProcessBeans) != CollectionUtils.size(newProcessBeans)
                || !CollectionUtils.isEqualCollection(oldProcessBeans, newProcessBeans);
        if (shouldUpdateProcesses) {
            int deletedProcessCount = activityTemplateProcessExtMapper.deleteProcessesByTemplateId(templateId);
            List<ActivityTemplateProcess> processes = batchInsertProcesses(newProcessBeans, templateId);
            log.info("deletedProcessCount={}, insertProcessCount={}", deletedProcessCount, processes.size());
        } else {
            log.info("no need to update processes");
        }
        // 活动流量资源表和活动流量资源图片表
        List<ActivityTemplateFlowResource> oldFlowResources = activityTemplateFlowResourceExtMapper
                .getFlowResourcesByTemplateId(templateId);
        ArrayList<Long> oldFlowResourceIds = new ArrayList<>();
        for (ActivityTemplateFlowResource flowResource : oldFlowResources) {
            oldFlowResourceIds.add(flowResource.getId());
        }
        LinkedHashMap<Long, List<ActivityTemplateFlowResourceImageBean>> flowResourceImageBeansMap =
                getFlowResourceImageBeansMap(oldFlowResourceIds);
        List<ActivityTemplateFlowResourceBean> oldFlowResourceBeans = activityTemplateFlowResourceConverter
                .toActivityTemplateFlowResourceBeans(oldFlowResources, flowResourceImageBeansMap);
        List<ActivityTemplateFlowResourceBean> newFlowResourceBeans = req.getFlowResources();
        boolean shouldUpdateFlowResources =
                CollectionUtils.size(oldFlowResourceBeans) != CollectionUtils.size(newFlowResourceBeans)
                        || !CollectionUtils.isEqualCollection(oldFlowResourceBeans, newFlowResourceBeans);
        if (shouldUpdateFlowResources) {
            // 活动流量资源表
            int deletedFlowResourceCount = activityTemplateFlowResourceExtMapper.deleteFlowResourcesByTemplateId(templateId);
            List<ActivityTemplateFlowResource> newFlowResources = batchInsertFlowResources(newFlowResourceBeans, templateId);
            // 活动流量资源图片表
            int deletedFlowResourceImageCount;
            if (CollectionUtils.isNotEmpty(oldFlowResourceIds)) {
                deletedFlowResourceImageCount = activityTemplateFlowResourceImageExtMapper
                        .deleteFlowResourceImagesByFlowResourceIds(oldFlowResourceIds);
            } else {
                deletedFlowResourceImageCount = 0;
            }
            ArrayList<ActivityTemplateFlowResourceImage> allNewFlowResourceImages = new ArrayList<>();
            for (int index = 0; index < newFlowResourceBeans.size(); index++) {
                List<ActivityTemplateFlowResourceImageBean> imageBeans = newFlowResourceBeans.get(index).getImages();
                Long flowResourceId = newFlowResources.get(index).getId();
                List<ActivityTemplateFlowResourceImage> newFlowResourceImages = activityTemplateFlowResourceImageConverter
                        .toCreateActivityTemplateFlowResourceImages(imageBeans, flowResourceId);
                allNewFlowResourceImages.addAll(newFlowResourceImages);
            }
            if (CollectionUtils.isNotEmpty(allNewFlowResourceImages)) {
                activityTemplateFlowResourceImageMapper.batchInsert(allNewFlowResourceImages);
            }
            log.info("deletedFlowResourceCount={}, insertFlowResourceCount={}," +
                            " deletedFlowResourceImageCount={}, insertFlowResourceImageCount={}",
                    deletedFlowResourceCount, newFlowResources.size(),
                    deletedFlowResourceImageCount, allNewFlowResourceImages.size());
        } else {
            log.info("no need to update flowResources");
        }
    }

    private LinkedHashMap<Long, List<ActivityTemplateFlowResourceImageBean>> getFlowResourceImageBeansMap(
            List<Long> flowResourceIds) {
        LinkedHashMap<Long, List<ActivityTemplateFlowResourceImageBean>> flowResourceImageBeansMap = new LinkedHashMap<>();
        if (CollectionUtils.isEmpty(flowResourceIds)) {
            return flowResourceImageBeansMap;
        }
        List<ActivityTemplateFlowResourceImage> flowResourceImages = getFlowResourceImagesByFlowResourceIds(flowResourceIds);
        for (ActivityTemplateFlowResourceImage flowResourceImage : flowResourceImages) {
            Long flowResourceId = flowResourceImage.getFlowResourceId();
            ActivityTemplateFlowResourceImageBean flowResourceImageBean = activityTemplateFlowResourceImageConverter
                    .toActivityTemplateFlowResourceImageBean(flowResourceImage);
            flowResourceImageBeansMap.computeIfAbsent(flowResourceId, k -> new ArrayList<>()).add(flowResourceImageBean);
        }
        return flowResourceImageBeansMap;
    }

    private List<ActivityTemplateFlowResourceImage> getFlowResourceImagesByFlowResourceIds(List<Long> flowResourceIds) {
        if (CollectionUtils.isEmpty(flowResourceIds)) {
            return Collections.emptyList();
        }
        return activityTemplateFlowResourceImageExtMapper.getFlowResourceImagesByFlowResourceIds(flowResourceIds);
    }

    /**
     * 删除活动模板
     *
     * @param req 请求
     */
    @Transactional
    public void deleteTemplate(RequestDeleteActivityTemplate req) {
        Long templateId = req.getId();
        Validate.notNull(templateId, "templateId cannot be null");
        ActivityTemplateInfo deleteById = new ActivityTemplateInfo();
        deleteById.setId(templateId);
        deleteById.setDeleted(true);
        deleteById.setOperator(req.getOperator());
        deleteById.setModifyTime(new Date());
        int deletedTemplateCount = activityTemplateInfoMapper.updateByPrimaryKey(deleteById);
        Validate.isTrue(deletedTemplateCount == 1, String.format(
                "删除活动模板失败, templateId=%s, deletedTemplateCount=%s", templateId, deletedTemplateCount));
        log.info("deleted templateId={}", templateId);
    }

    /**
     * 更新活动模板上下架状态
     *
     * @param req 请求
     */
    @Transactional
    public void updateShelfStatus(RequestUpdateActivityTemplateShelfStatus req) {
        Long templateId = req.getId();
        ActivityTemplateInfo updateTemplate = activityTemplateInfoConverter.toUpdateActivityTemplateInfoShelfStatus(req);
        int updatedTemplateCount = activityTemplateInfoMapper.updateByPrimaryKey(updateTemplate);
        Validate.isTrue(updatedTemplateCount == 1, String.format(
                "更新活动模板上下架状态失败, templateId=%s, updatedTemplateCount=%s", templateId, updatedTemplateCount));

        int updateUpTimeLimit = activityTemplateInfoExtMapper.updateUpTimeLimit(updateTemplate.getId(), updateTemplate.getUpStartTime(), updateTemplate.getUpEndTime());
        log.info("updateUpTimeLimit={}", updateUpTimeLimit);

        List<ActivityTemplateHighlightBean> oldHighlightBeans = activityTemplateHighlightConverter
                .toActivityTemplateHighlightBeans(activityTemplateHighlightExtMapper.getHighlightsByTemplateId(templateId));
        List<ActivityTemplateHighlightBean> newHighlightBeans = req.getHighlights();
        boolean shouldUpdateHighlights = CollectionUtils.size(oldHighlightBeans) != CollectionUtils.size(newHighlightBeans)
                || !CollectionUtils.isEqualCollection(oldHighlightBeans, newHighlightBeans);
        if (shouldUpdateHighlights) {
            int deletedHighlightCount = activityTemplateHighlightExtMapper.deleteHighlightsByTemplateId(templateId);
            List<ActivityTemplateHighlightBean> highlightBeans = req.getHighlights();
            List<ActivityTemplateHighlight> highlights = activityTemplateHighlightConverter
                    .toCreateActivityTemplateHighlights(highlightBeans, templateId);
            activityTemplateHighlightMapper.batchInsert(highlights);
            log.info("deletedHighlightCount={}, insertHighlightCount={}", deletedHighlightCount, highlights.size());
        } else {
            log.info("no need to update highlights");
        }

        // 定期上架任务更新
        if (Objects.equals(req.getStatus(), ActivityTemplateStatusEnum.ON_SHELF_AUTO.getStatus())) {
            updateTemplateAutoUpTask(templateId, req.getUpStartTime(), req.getUpEndTime(), req.getAppId());
        }

        // 白名单更新
        updateTemplateWhitelist(templateId, req.getNjWhiteList(), req.getAppId());
    }

    /**
     * 更新活动模板定时上架任务
     *
     * @param templateId
     * @param startTime
     * @param endTime
     */
    private void updateTemplateAutoUpTask(Long templateId, Long startTime, Long endTime, Integer appId) {
        // 删除旧任务
        ActivityTemplateStatusTaskExample deleteExample = new ActivityTemplateStatusTaskExample();
        deleteExample.createCriteria()
                .andTaskStatusEqualTo(TemplateStatusTaskStatusEnum.WAITING.getValue())
                .andTemplateIdEqualTo(templateId)
                .andDeletedEqualTo(0)
                .andAppIdEqualTo(appId);
        ActivityTemplateStatusTask deleteEntity = ActivityTemplateStatusTask.builder().deleted(1).build();
        templateStatusTaskMapper.updateByExample(deleteEntity, deleteExample);

        // 创建上架任务
        ActivityTemplateStatusTask.ActivityTemplateStatusTaskBuilder upEntity = ActivityTemplateStatusTask.builder()
                .templateId(templateId)
                .templateTargetStatus(ActivityTemplateStatusEnum.ON_SHELF.getStatus())
                .executeTime(MyDateUtil.resetSecond(startTime))
                .taskStatus(TemplateStatusTaskStatusEnum.WAITING.getValue())
                .createTime(new Date())
                .updateTime(new Date())
                .appId(appId)
                .deleted(0);
        templateStatusTaskMapper.insert(upEntity.build());

        // 创建下架任务
        ActivityTemplateStatusTask.ActivityTemplateStatusTaskBuilder downEntity = ActivityTemplateStatusTask.builder()
                .templateId(templateId)
                .templateTargetStatus(ActivityTemplateStatusEnum.OFF_SHELF.getStatus())
                .executeTime(MyDateUtil.resetSecond(endTime))
                .taskStatus(TemplateStatusTaskStatusEnum.WAITING.getValue())
                .createTime(new Date())
                .updateTime(new Date())
                .appId(appId)
                .deleted(0);
        templateStatusTaskMapper.insert(downEntity.build());
    }

    /**
     * 更新模板白名单
     *
     * @param templateId
     * @param njIds
     */
    private void updateTemplateWhitelist(Long templateId, List<Long> njIds, Integer appId) {
        // 删除旧的白名单
        ActivityTemplateNjListExample deleteExample = new ActivityTemplateNjListExample();
        deleteExample.createCriteria()
                .andListTypeEqualTo(TemplateNjListType.WHITELIST.getValue())
                .andAppIdEqualTo(appId)
                .andTemplateIdEqualTo(templateId);
        templateNjListMapper.deleteByExample(deleteExample);

        // 创建新的白名单
        if (CollectionUtils.isNotEmpty(njIds)) {
            List<ActivityTemplateNjList> njLists = new ArrayList<>();
            for (Long njId : njIds) {
                ActivityTemplateNjList njList = ActivityTemplateNjList.builder()
                        .templateId(templateId)
                        .njId(njId)
                        .listType(TemplateNjListType.WHITELIST.getValue())
                        .appId(appId)
                        .createTime(new Date())
                        .updateTime(new Date())
                        .build();
                njLists.add(njList);
            }
            templateNjListMapper.batchInsert(njLists);
        }
    }

    /**
     * 获取活动模板上下架状态
     *
     * @param templateId 活动模板ID
     * @return 活动模板上下架状态响应
     */
    public ResponseGetActivityTemplateShelfStatus getShelfStatus(long templateId) {
        ActivityTemplateInfo activityTemplateInfo = getTemplateInfoById(templateId);
        Validate.notNull(activityTemplateInfo, "查询活动模板上下架状态不存在, templateId=" + templateId);
        List<ActivityTemplateHighlight> highlights = activityTemplateHighlightExtMapper.getHighlightsByTemplateId(templateId);
        // 查询白名单
        List<Long> templateWhitelist = getTemplateWhitelist(templateId);
        return activityTemplateInfoConverter.toResponseGetActivityTemplateShelfStatus(activityTemplateInfo, highlights, templateWhitelist);
    }

    /**
     * 查询模板的厅白名单
     *
     * @param templateId
     * @return
     */
    public List<Long> getTemplateWhitelist(long templateId) {
        ActivityTemplateNjListExample example = new ActivityTemplateNjListExample();
        example.createCriteria()
                .andListTypeEqualTo(TemplateNjListType.WHITELIST.getValue())
                .andTemplateIdEqualTo(templateId);
        List<ActivityTemplateNjList> list = templateNjListMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(ActivityTemplateNjList::getNjId).collect(Collectors.toList());
    }

    /**
     * 分页查询活动模板信息
     *
     * @param req 请求
     * @return 活动模板信息分页列表
     */
    public PageList<ActivityTemplatePageBean> pageTemplate(RequestPageActivityTemplate req) {
        // 查询出大分类下的小分类
        if (CollectionUtils.isNotEmpty(req.getClassIds())) {
            List<ActivityClassConfig> allClassIds = activityClassificationDao.listClassificationByClassOrBigClass(req.getClassIds());
            req.setClassIds(allClassIds.stream().map(ActivityClassConfig::getId).collect(Collectors.toList()));
        }

        PageList<ActivityTemplateInfo> pageList = activityTemplateInfoExtMapper.pageTemplate(
                req, ConfigUtils.getEnvRequired().name(), req.getPageNo(), req.getPageSize());
        LinkedHashSet<Long> classIds = new LinkedHashSet<>();
        for (ActivityTemplateInfo activityTemplateInfo : pageList) {
            classIds.add(activityTemplateInfo.getClassId());
        }
        Map<Long, ActivityClassConfig> classConfigMap = activityClassificationDao.getClassIdToClassConfigMapByClassIds(
                new ArrayList<>(classIds));
        LinkedHashSet<Long> bigClassIds = new LinkedHashSet<>();
        for (ActivityClassConfig classConfig : classConfigMap.values()) {
            bigClassIds.add(classConfig.getBigClassId());
        }
        Map<Long, ActivityBigClass> bigClassMap = activityClassificationDao.getBigClassIdToBigClassMapByBigClassIds(
                new ArrayList<>(bigClassIds));
        PageList<ActivityTemplatePageBean> resultPageList = PageListUtils.copyWithoutElement(pageList);
        for (ActivityTemplateInfo activityTemplateInfo : pageList) {
            ActivityClassConfig classConfig = classConfigMap.get(activityTemplateInfo.getClassId());
            String className = classConfig != null ? classConfig.getName() : null;
            Long bigClassId = classConfig != null ? classConfig.getBigClassId() : null;
            ActivityBigClass bigClass = bigClassMap.get(bigClassId);
            String bigClassName = bigClass != null ? bigClass.getName() : null;
            ActivityTemplatePageBean activityTemplatePageBean = activityTemplateInfoConverter
                    .toActivityTemplatePageBean(activityTemplateInfo, className, bigClassId, bigClassName);
            resultPageList.add(activityTemplatePageBean);
        }
        return resultPageList;
    }

    /**
     * 获取活动模板
     *
     * @param templateId 活动模板ID
     * @return 活动模板
     */
    public ResponseGetActivityTemplate getTemplate(long templateId) {
        // 活动信息表
        ActivityTemplateInfo templateInfo = getTemplateInfoById(templateId);
        Validate.notNull(templateInfo, "查询活动模板不存在, templateId=" + templateId);
        // 活动大类表
        ActivityClassConfig classConfig = activityClassificationDao.getActivityClassConfig(templateInfo.getClassId());
        String className = classConfig != null ? classConfig.getName() : null;
        Long bigClassId = classConfig != null ? classConfig.getBigClassId() : null;
        ActivityBigClass activityBigClass = activityClassificationDao.getActivityBigClass(bigClassId);
        String bigClassName = activityBigClass != null ? activityBigClass.getName() : null;
        Integer bigClassType = activityBigClass != null ? activityBigClass.getType() : 0;
        // 活动流程表
        List<ActivityTemplateProcess> processes = activityTemplateProcessExtMapper
                .getProcessesByTemplateId(templateId);
        List<ActivityTemplateDetailProcessBean> processBeans = activityTemplateProcessConverter
                .toActivityTemplateDetailProcessBeans(processes);
        // 活动流量资源
        List<ActivityTemplateFlowResourceDTO> flowResourceDTOS = getFlowResourceDTOS(templateId);
        List<ActivityTemplateDetailFlowResourceBean> flowResourceBeans = activityTemplateFlowResourceConverter
                .toActivityTemplateDetailFlowResourceBeans(flowResourceDTOS);

        // 活动限时旧数据兼容处理
        getActivityDurationLimit(templateInfo, flowResourceDTOS).ifPresent(templateInfo::setActivityDurationLimit);

        // 合并结果
        return activityTemplateInfoConverter.toResponseGetActivityTemplate(templateInfo, className, bigClassId,
                bigClassName, processBeans, flowResourceBeans, bigClassType);
    }

    /**
     * 获取活动时长限制
     *
     * @param templateInfo
     * @param flowResourceDTOS
     * @return
     */
    public Optional<Integer> getActivityDurationLimit(ActivityTemplateInfo templateInfo, List<ActivityTemplateFlowResourceDTO> flowResourceDTOS) {
        if (templateInfo.getActivityDurationLimit() != null) {
            return Optional.of(templateInfo.getActivityDurationLimit());
        }
        if (CollectionUtils.isEmpty(flowResourceDTOS)) {
            return Optional.empty();
        }
        for (ActivityTemplateFlowResourceDTO flowResourceDTO : flowResourceDTOS) {
            if (flowResourceDTO.getExtra() != null && flowResourceDTO.getExtra().getDurationLimit() != null) {
                return Optional.of(flowResourceDTO.getExtra().getDurationLimit());
            }
        }
        return Optional.empty();
    }

    private List<ActivityTemplateFlowResourceDTO> getFlowResourceDTOS(long templateId) {
        // 活动信息表
        ActivityTemplateInfo templateInfo = getTemplateInfoById(templateId);
        if (templateInfo == null) {
            log.warn("getTemplateFlowResources failed, could not found template, templateId={}", templateId);
            return Collections.emptyList();
        }
        // 流量资源表
        List<ActivityTemplateFlowResource> flowResources = activityTemplateFlowResourceExtMapper
                .getFlowResourcesByTemplateId(templateId);
        if (CollectionUtils.isEmpty(flowResources)) {
            return Collections.emptyList();
        }
        ArrayList<Long> resourceConfigIds = new ArrayList<>();
        for (ActivityTemplateFlowResource flowResource : flowResources) {
            resourceConfigIds.add(flowResource.getResourceConfigId());
        }
        Map<Long, ActivityResourceConfig> resourceConfigIdToEntityMap = activityResourceDao
                .getResourceByIdsAsMap(resourceConfigIds);
        // 流量资源图片表
        ArrayList<Long> flowResourceIds = new ArrayList<>();
        for (ActivityTemplateFlowResource flowResource : flowResources) {
            flowResourceIds.add(flowResource.getId());
        }
        List<ActivityTemplateFlowResourceImage> flowResourceImages = getFlowResourceImagesByFlowResourceIds(flowResourceIds);
        // 合并流量资源图片DTO
        LinkedHashMap<Long, List<ActivityTemplateFlowResourceImageDTO>> flowResourceImageDTOSMap = new LinkedHashMap<>();
        for (ActivityTemplateFlowResourceImage flowResourceImage : flowResourceImages) {
            Long flowResourceId = flowResourceImage.getFlowResourceId();
            ActivityTemplateFlowResourceImageDTO flowResourceImageDTO = activityTemplateFlowResourceImageConverter
                    .toActivityTemplateFlowResourceImageDTO(flowResourceImage);
            flowResourceImageDTOSMap.computeIfAbsent(flowResourceId, k -> new ArrayList<>()).add(flowResourceImageDTO);
        }
        // 合并流量资源DTO
        ArrayList<ActivityTemplateFlowResourceDTO> flowResourceDTOS = new ArrayList<>();
        for (ActivityTemplateFlowResource flowResource : flowResources) {
            Long flowResourceId = flowResource.getId();
            Long resourceConfigId = flowResource.getResourceConfigId();
            ActivityResourceConfig resourceConfig = resourceConfigIdToEntityMap.get(resourceConfigId);
            if (resourceConfig == null) {
                log.warn("getTemplateFlowResources failed, could not found resourceConfig, flowResourceId={}",
                        flowResourceId);
                continue;
            }
            List<ActivityTemplateFlowResourceImageDTO> imageDTOS = flowResourceImageDTOSMap.get(flowResourceId);
            ActivityTemplateFlowResourceExtraDTO extraDTO = toFlowResourceExtraDTO(
                    templateInfo.getAppId(), flowResource.getExtra());
            ActivityTemplateFlowResourceDTO flowResourceDTO = activityTemplateFlowResourceConverter
                    .toActivityTemplateFlowResourceDTO(resourceConfig, imageDTOS, extraDTO);
            flowResourceDTOS.add(flowResourceDTO);
        }
        return flowResourceDTOS;
    }

    private ActivityTemplateFlowResourceExtraDTO toFlowResourceExtraDTO(Integer appId, String extraJson) {
        ActivityTemplateFlowResourceExtraDTO extraDTO = StringUtils.isNotBlank(extraJson)
                ? JsonUtils.fromJsonString(extraJson, ActivityTemplateFlowResourceExtraDTO.class)
                : new ActivityTemplateFlowResourceExtraDTO();
        // 兼容旧数据官频位座位号为空的情况
        if (CollectionUtils.isEmpty(extraDTO.getOfficialSeatNumbers())) {
            CommonActivityConfig bizConfig = activityConfig.getBizConfig(appId);
            if (bizConfig != null) {
                extraDTO.setOfficialSeatNumbers(new ArrayList<>(bizConfig.getOfficialSeatDefaultNumbers()));
            }
        }
        return extraDTO;
    }

    /**
     * 分页查询热门活动模板, 用于web站
     *
     * @param req 请求
     * @return 热门活动模板分页列表
     */
    public PageList<ActivityTemplateHotPageBean> pageHotTemplate(RequestPageHotActivityTemplate req, List<Long> njList) {
        PageList<ActivityTemplateInfo> pageList = activityTemplateInfoExtMapper.pageHotTemplate(
                req, ConfigUtils.getEnvRequired().name(), njList, req.getPageNo(), req.getPageSize());
        ArrayList<Long> templateIds = new ArrayList<>();
        for (ActivityTemplateInfo templateInfo : pageList) {
            templateIds.add(templateInfo.getId());
        }
        List<ActivityTemplateHighlight> highlights = getHighlightsByTemplateIds(templateIds);
        LinkedHashMap<Long, String> templateIdToUsageCountMap = new LinkedHashMap<>();
        LinkedHashMap<Long, List<ActivityTemplateHighlight>> templateIdToHighlightMap = new LinkedHashMap<>();
        for (ActivityTemplateHighlight highlight : highlights) {
            if (highlight.getType() == ActivityHighlightTypeEnum.HIGHLIGHT.getType()
                    && Objects.equals(highlight.getHighlightKey(), ActivityTemplateHighlightEnum.TEMPLATE_USAGE_COUNT.getCode())) {
                templateIdToUsageCountMap.put(highlight.getTemplateId(), highlight.getHighlightValue());
            }
            List<ActivityTemplateHighlight> highlightList = templateIdToHighlightMap.get(highlight.getTemplateId());
            highlightList = highlightList == null ? new ArrayList<>() : highlightList;
            highlightList.add(highlight);
            templateIdToHighlightMap.put(highlight.getTemplateId(), highlightList);
        }

        // 查询流量资源信息
        Map<Long, List<ActivityTemplateFlowResourceBean>> flowResourceMap = getFlowResourceMap(templateIds);

        PageList<ActivityTemplateHotPageBean> resultPageList = PageListUtils.copyWithoutElement(pageList);
        for (ActivityTemplateInfo templateInfo : pageList) {
            String usageCount = templateIdToUsageCountMap.get(templateInfo.getId());
            List<ActivityTemplateHighlight> highlightList = templateIdToHighlightMap.get(templateInfo.getId());
            List<ActivityTemplateFlowResourceBean> flowResourceBeans = flowResourceMap.get(templateInfo.getId());
            ActivityTemplateHotPageBean activityTemplateHotPageBean = activityTemplateInfoConverter
                    .toActivityTemplateHotPageBean(templateInfo, usageCount, flowResourceBeans, highlightList);
            resultPageList.add(activityTemplateHotPageBean);
        }
        return resultPageList;
    }

    private List<ActivityTemplateHighlight> getHighlightsByTemplateIds(List<Long> templateIds) {
        if (CollectionUtils.isEmpty(templateIds)) {
            return Collections.emptyList();
        }
        return activityTemplateHighlightExtMapper.getHighlightsByTemplateIds(templateIds);
    }

    /**
     * 分页查询通用活动模板, 用于web站
     *
     * @param req 请求
     * @return 通用活动模板分页列表
     */
    public PageList<ActivityTemplateGeneralPageBean> pageGeneralTemplate(RequestPageGeneralActivityTemplate req) {
        PageList<ActivityTemplateInfo> pageList = activityTemplateInfoExtMapper.pageGeneralTemplate(
                req, ConfigUtils.getEnvRequired().name(), req.getPageNo(), req.getPageSize());
        LinkedHashSet<Long> classIds = new LinkedHashSet<>();
        List<Long> templateIds = new ArrayList<>();
        for (ActivityTemplateInfo activityTemplateInfo : pageList) {
            templateIds.add(activityTemplateInfo.getId());
            classIds.add(activityTemplateInfo.getClassId());
        }
        Map<Long, ActivityClassConfig> classConfigMap = activityClassificationDao.getClassIdToClassConfigMapByClassIds(
                new ArrayList<>(classIds));
        PageList<ActivityTemplateGeneralPageBean> resultPageList = PageListUtils.copyWithoutElement(pageList);
        // 查询流量资源信息
        List<ActivityTemplateHighlight> highlightList = getHighlightsByTemplateIds(templateIds);
        LinkedHashMap<Long, List<ActivityTemplateHighlight>> templateIdToUsageCountMap = new LinkedHashMap<>();
        for (ActivityTemplateHighlight highlight : highlightList) {
            List<ActivityTemplateHighlight> highlights = templateIdToUsageCountMap.get(highlight.getTemplateId());
            highlights = highlights == null ? new ArrayList<>() : highlights;
            highlights.add(highlight);
            templateIdToUsageCountMap.put(highlight.getTemplateId(), highlights);
        }

        Map<Long, List<ActivityTemplateFlowResourceBean>> flowResourceMap = getFlowResourceMap(templateIds);
        for (ActivityTemplateInfo templateInfo : pageList) {
            ActivityClassConfig classConfig = classConfigMap.get(templateInfo.getClassId());
            Long bigClassId = classConfig != null ? classConfig.getBigClassId() : null;
            List<ActivityTemplateFlowResourceBean> flowResourceBeans = flowResourceMap.get(templateInfo.getId());
            List<ActivityTemplateHighlight> highlights = templateIdToUsageCountMap.get(templateInfo.getId());
            ActivityTemplateGeneralPageBean activityTemplateGeneralPageBean = activityTemplateInfoConverter
                    .toActivityTemplateGeneralPageBean(templateInfo, bigClassId, flowResourceBeans, highlights);
            resultPageList.add(activityTemplateGeneralPageBean);
        }
        return resultPageList;
    }

    /**
     * 统计通用活动模板数量, 用于web站
     *
     * @param req 请求
     * @return 通用活动模板数量
     */
    public Long countGeneralTemplate(RequestCountGeneralActivityTemplate req) {
        return activityTemplateInfoExtMapper.countGeneralTemplate(req, ConfigUtils.getEnvRequired().name());
    }

    /**
     * 查询活动模板资源信息
     *
     * @param templateIds
     * @return key=templateId value=流量资源信息
     */
    private Map<Long, List<ActivityTemplateFlowResourceBean>> getFlowResourceMap(List<Long> templateIds) {
        if (CollectionUtils.isEmpty(templateIds)) {
            return Collections.emptyMap();
        }
        ActivityTemplateFlowResourceExample resourceExample = new ActivityTemplateFlowResourceExample();
        resourceExample.createCriteria().andTemplateIdIn(templateIds);
        List<ActivityTemplateFlowResource> resourceList = activityTemplateFlowResourceMapper.selectByExample(resourceExample);
        if (CollectionUtils.isEmpty(resourceList)) {
            return Collections.emptyMap();
        }

        // key=templateId  模板的流量资源
        Map<Long, List<ActivityTemplateFlowResource>> templateMap = resourceList.stream().collect(Collectors.groupingBy(ActivityTemplateFlowResource::getTemplateId));

        List<Long> configIds = resourceList.stream().map(ActivityTemplateFlowResource::getResourceConfigId).distinct().collect(Collectors.toList());
        List<ActivityResourceConfig> activityResourceConfigs = activityResourceDao.batchResourceByIds(configIds);

        // key=configId value=resourceCode 模板流量资源的详细配置信息
        Map<Long, ActivityResourceConfig> configMap = activityResourceConfigs.stream().collect(Collectors.toMap(ActivityResourceConfig::getId, v -> v));

        Map<Long, List<ActivityTemplateFlowResourceBean>> resultMap = new HashMap<>();
        for (Long templateId : templateIds) {
            List<ActivityTemplateFlowResource> rList = templateMap.get(templateId);
            if (CollectionUtils.isEmpty(rList)) {
                continue;
            }
            List<ActivityTemplateFlowResourceBean> beanList = new ArrayList<>();
            for (ActivityTemplateFlowResource r : rList) {
                ActivityResourceConfig activityResourceConfig = configMap.get(r.getResourceConfigId());
                if (activityResourceConfig == null) {
                    continue;
                }
                ActivityTemplateFlowResourceBean bean = new ActivityTemplateFlowResourceBean()
                        .setResourceConfigId(r.getResourceConfigId())
                        .setName(activityResourceConfig.getName())
                        .setResourceCode(activityResourceConfig.getResourceCode());
                beanList.add(bean);
            }
            resultMap.put(templateId, beanList);
        }
        return resultMap;
    }

    /**
     * 获取通用活动模板详情, 用于web站
     *
     * @param templateId 模板ID
     * @return 通用活动模板详情
     */
    public ResponseGetGeneralActivityTemplate getGeneralTemplate(long templateId) {
        // 活动信息表
        ActivityTemplateInfo templateInfo = getTemplateInfoById(templateId);
        Validate.notNull(templateInfo, "查询活动模板不存在, templateId=" + templateId);
        // 活动大类表
        ActivityClassConfig classConfig = activityClassificationDao.getActivityClassConfig(templateInfo.getClassId());
        String className = classConfig != null ? classConfig.getName() : null;
        Long bigClassId = classConfig != null ? classConfig.getBigClassId() : null;
        ActivityBigClass bigClass = activityClassificationDao.getActivityBigClass(bigClassId);
        String bigClassName = bigClass != null ? bigClass.getName() : null;
        Integer bigClassType = bigClass != null ? bigClass.getType() : 0;
        Long levelId = classConfig != null ? classConfig.getLevelId() : null;
        ActivityLevelConfig levelConfig = levelId != null ? activityLevelDao.getActivityLevelConfig(levelId) : null;
        String level = levelConfig != null ? levelConfig.getLevel() : null;
        // 活动流程表
        List<ActivityTemplateProcess> processes = activityTemplateProcessExtMapper
                .getProcessesByTemplateId(templateId);
        List<ActivityTemplateGeneralProcessBean> processBeans = activityTemplateProcessConverter
                .toActivityTemplateGeneralProcessBeans(processes);
        // 活动流量资源
        List<ActivityTemplateFlowResourceDTO> flowResourceDTOS = getFlowResourceDTOS(templateId);
        List<ActivityTemplateGeneralFlowResourceBean> allFlowResourceBeans = activityTemplateFlowResourceConverter
                .toActivityTemplateGeneralFlowResourceBeans(flowResourceDTOS);
        ArrayList<ActivityTemplateGeneralFlowResourceBean> flowResourceBeans = new ArrayList<>();
        for (ActivityTemplateGeneralFlowResourceBean flowResourceBean : allFlowResourceBeans) {
            // web站只保留未删除且未禁用的资源
            if (flowResourceBean != null
                    && !flowResourceBean.getResourceDeleted()
                    && !Objects.equals(flowResourceBean.getResourceStatus(), ActivityResourceStatusEnum.DISABLED.getValue())) {
                flowResourceBeans.add(flowResourceBean);
            }
        }
        // 活动模板亮点标签表
        List<ActivityTemplateHighlight> highlights = activityTemplateHighlightExtMapper.getHighlightsByTemplateId(templateId);

        // 活动限时旧数据兼容处理
        getActivityDurationLimit(templateInfo, flowResourceDTOS).ifPresent(templateInfo::setActivityDurationLimit);

        // 合并结果
        return activityTemplateInfoConverter.toResponseGetGeneralActivityTemplate(templateInfo, bigClassId, bigClassName,
                className, level, processBeans, flowResourceBeans, highlights, bigClassType, levelId);
    }

    /**
     * 获取活动模板官频位资源, 如果没有则返回null
     *
     * @param templateId 活动模板id
     * @return 活动模板官频位资源
     */
    public ActivityTemplateFlowResourceDTO getTemplateOfficialSeat(long templateId) {
        ActivityTemplateInfo templateInfo = getTemplateInfoById(templateId);
        if (templateInfo == null) {
            return null;
        }
        List<ActivityTemplateFlowResourceDTO> flowResourceDTOS = getFlowResourceDTOS(templateId);
        for (ActivityTemplateFlowResourceDTO flowResourceDTO : flowResourceDTOS) {
            if (Objects.equals(flowResourceDTO.getResourceCode(), AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode())) {
                // 官频位目前只能配一个, 因此取第一个
                return flowResourceDTO;
            }
        }
        log.info("getTemplateOfficialSeat failed, could not found officialSeat, templateId={}", templateId);
        return null;
    }

    public void updateStatus(Long templateId, ActivityTemplateStatusEnum status) {
        ActivityTemplateInfoExample example = new ActivityTemplateInfoExample();
        example.createCriteria().andIdEqualTo(templateId);
        ActivityTemplateInfo record = new ActivityTemplateInfo();
        record.setStatus(status.getStatus());
        record.setModifyTime(new Date());
        activityTemplateInfoMapper.updateByExample(record, example);
    }

    /**
     * 查询活动模板白名单
     *
     * @param templateId
     * @param appId
     * @return
     */
    public List<Long> getTemplateNjWhitelist(long templateId, int appId) {
        ActivityTemplateNjListExample example = new ActivityTemplateNjListExample();
        example.createCriteria()
                .andListTypeEqualTo(TemplateNjListType.WHITELIST.getValue())
                .andTemplateIdEqualTo(templateId)
                .andAppIdEqualTo(appId);
        List<ActivityTemplateNjList> activityTemplateNjLists = templateNjListMapper.selectByExample(example);
        return activityTemplateNjLists.stream().map(ActivityTemplateNjList::getNjId).collect(Collectors.toList());
    }

    /**
     * 获取活动模板节目单资源, 如果没有则返回null
     *
     * @param templateId 活动模板id
     * @return 活动模板节目单资源
     */
    public ActivityTemplateFlowResourceDTO getTemplateProgramme(long templateId) {
        List<ActivityTemplateFlowResourceDTO> flowResources = getFlowResourceDTOS(templateId);
        for (ActivityTemplateFlowResourceDTO flowResourceDTO : flowResources) {
            if (Objects.equals(flowResourceDTO.getResourceCode(), AutoConfigResourceEnum.PROGRAMME.getResourceCode())) {
                return flowResourceDTO;
            }
        }
        log.info("getTemplateProgramme failed, could not found programme, templateId={}", templateId);
        return null;
    }
}
