package fm.lizhi.ocean.wavecenter.infrastructure.income.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.IncomeStatPo;
import fm.lizhi.ocean.wavecenter.infrastructure.income.datastore.po.PlayerPayCountPo;
import fm.lizhi.ocean.wavecenter.infrastructure.income.dto.PlayerPayCountParamDto;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/23 14:21
 */
@DataStore(namespace = "mysql_xm_lzppfamily_r")
public interface XmIncomeRecordMapper {

    /**
     * 查询有收入主播数
     * @param param
     * @param stateDayStart yyyyMMdd
     * @param stateDayEnd  yyyyMMdd
     * @return
     */
    @Select({
        "<script>"
            , "select count(distinct player_id) from income_record where coin>0 "
            , "<if test=' null != param.familyId and param.familyId > 0 '>"
            , "and family_id=#{param.familyId} "
            , "</if>"
            , "<if test=' null != param.roomId and param.roomId > 0 '>"
            , "and nj_id=#{param.roomId} "
            , "</if>"
            , "and stat_date&gt;=#{stateDayStart} "
            , "and stat_date&lt;=#{stateDayEnd} "
        , "</script>"
    })
    Integer getPlayerPayCount(@Param("param") PlayerPayCountParamDto param
            , @Param("stateDayStart") int stateDayStart
            , @Param("stateDayEnd") int stateDayEnd
    );

    @Select({
            "<script>"
            , "select nj_id, count(distinct player_id) totalCount from income_record where coin>0 "
            , "<if test=' null != param.familyId and param.familyId > 0 '>"
            , "and family_id=#{param.familyId} "
            , "</if>"
            , "<if test=' null != param.roomIds and param.roomIds.size > 0 '>"
            , "and nj_id in "
            , "<foreach collection='param.roomIds' item='rId' open='(' separator=',' close=')'>"
            , "#{rId}"
            , "</foreach>"
            , "</if>"
            , "<if test=' null != param.playerIds and param.playerIds.size > 0 '>"
            , "and player_id in "
            , "<foreach collection='param.playerIds' item='pId' open='(' separator=',' close=')'>"
            , "#{pId}"
            , "</foreach>"
            , "</if>"
            , "and stat_date&gt;=#{stateDayStart} "
            , "and stat_date&lt;=#{stateDayEnd} "
            , "group by nj_id"
            , "</script>"
    })
    List<PlayerPayCountPo> getPlayerPayCountByRooms(@Param("param") PlayerPayCountParamDto param
            , @Param("stateDayStart") int stateDayStart
            , @Param("stateDayEnd") int stateDayEnd
    );

    @Select({
            "<script>"
            , "select s.player_id as userId, s.nj_id as njId, sum(s.coin) income"
            , "from income_record s"
            , "where s.family_id = #{familyId}"
            , "and s.stat_date=#{date}"
            , "group by s.user_id, s.nj_id"
            , "order by income ${orderType}"
            , "limit 10"
            ,"</script>"
    })
    List<IncomeStatPo> guildPlayerIncomeInfo(@Param("familyId") long familyId, @Param("date") String date, @Param("orderType") String orderType);

    @Select({
            "<script>"
            , "select s.player_id as userId, s.nj_id, sum(s.coin) income"
            , "from income_record s"
            , "where s.nj_id = #{njId}"
            , "and s.stat_date=#{date}"
            , "and s.family_id = #{familyId}"
            , "group by s.player_id, s.nj_id"
            , "order by income ${orderType}"
            , "limit 10"
            ,"</script>"
    })
    List<IncomeStatPo> roomPlayerIncomeList(@Param("njId") long njId, @Param("familyId") long familyId, @Param("date") String date, @Param("orderType") String orderType);

    @Select({
            "<script>"
            , "select count(distinct player_id) totalCount from income_record where coin>0 "
            , "<if test=' null != param.familyId and param.familyId > 0 '>"
            , "and family_id=#{param.familyId} "
            , "</if>"
            , "<if test=' null != param.roomIds and param.roomIds.size > 0 '>"
            , "and nj_id in "
            , "<foreach collection='param.roomIds' item='rId' open='(' separator=',' close=')'>"
            , "#{rId}"
            , "</foreach>"
            , "</if>"
            , "<if test=' null != param.playerIds and param.playerIds.size > 0 '>"
            , "and player_id in "
            , "<foreach collection='param.playerIds' item='pId' open='(' separator=',' close=')'>"
            , "#{pId}"
            , "</foreach>"
            , "</if>"
            , "and stat_date&gt;=#{stateDayStart} "
            , "and stat_date&lt;=#{stateDayEnd} "
            , "group by family_id"
            , "</script>"
    })
    Integer getPlayerPayCountForFamily(@Param("param") PlayerPayCountParamDto param
            , @Param("stateDayStart") int stateDayStart
            , @Param("stateDayEnd") int stateDayEnd
    );

}
