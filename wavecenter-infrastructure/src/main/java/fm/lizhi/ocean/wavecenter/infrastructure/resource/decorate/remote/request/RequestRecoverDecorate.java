package fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.request;

import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import lombok.Data;

/**
 * 单次回收装扮的请求
 */
@Data
public class RequestRecoverDecorate {

    /**
     * 装扮类型
     */
    private PlatformDecorateTypeEnum decorateType;

    /**
     * 装扮ID
     */
    private Long decorateId;

    /**
     * 用户ID
     */
    private Long userId;
}
