package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.manager;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.common.constants.MetricsEnum;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.CharmRatioBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.CountDataBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.IncomeBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.PlayerAssessmentInfoBean;
import fm.lizhi.ocean.wavecenter.base.util.CalculateUtil;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.common.utils.WcAssert;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.*;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.mapper.*;
import fm.lizhi.ocean.wavecenter.infrastructure.common.MetricsUtil;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PaySettleConfigCodeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PaySettlePeriodNumberEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.common.manager.CreatorDataQueryCommonManager;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.constants.MetricsConstants;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.constants.MetricsMeta;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.convert.DataCenterInfraConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.entity.*;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.*;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.CountPlayerPo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote.ICharmStatRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote.IPlayerDataRemote;
import fm.lizhi.ocean.wavecenter.service.common.dto.PaySettlePeriodDto;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.*;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.PlayerDataManager;
import fm.lizhi.ocean.wavecenter.service.live.manager.LiveAuditManager;
import fm.lizhi.ocean.wavecenter.service.live.manager.WaveCheckInDataManager;
import fm.lizhi.pay.settle.settleenum.creativecenter.PeriodTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/21 20:21
 */
@Slf4j
@Component
public class PlayerDataManagerImpl implements PlayerDataManager {

    @Autowired
    private WcDataPlayerDayMapper playerDayMapper;
    @Autowired
    private WcDataPlayerRoomDayMapper wcDataPlayerRoomDayMapper;
    @Autowired
    private WcDataPlayerWeekMapper playerWeekMapper;
    @Autowired
    private WcDataPlayerRoomWeekMapper wcDataPlayerRoomWeekMapper;
    @Autowired
    private WcDataPlayerMonthMapper playerMonthMapper;
    @Autowired
    private WcDataPlayerRoomMonthMapper wcDataPlayerRoomMonthMapper;
    @Autowired
    private ICharmStatRemote iCharmStatRemote;
    @Autowired
    private CreatorDataQueryCommonManager creatorDataQueryCommonManager;
    @Autowired
    private IPlayerDataRemote iPlayerDataRemote;
    @Autowired
    private WcDataPlayerRoomDayExtMapper playerRoomDayExtMapper;
    @Autowired
    private WcDataPlayerRoomWeekExtMapper playerRoomWeekExtMapper;
    @Autowired
    private WcDataPlayerRoomMonthExtMapper playerRoomMonthExtMapper;
    @Autowired
    private WcDataRealPlayerWeekMapper realPlayerWeekMapper;
    @Autowired
    private WcDataRealPlayerWeekExtMapper realPlayerWeekExtMapper;
    @Autowired
    private LiveAuditManager liveAuditManager;
    @Autowired
    private WaveCheckInDataManager checkInDataManager;

    @Override
    public List<CountDataBean> getIndicatorTrend(long playerId, Long familyId, Long roomId, String metric, int days) {
        List<Integer> dates = MyDateUtil.getDateDayValueList(days);

        Map<Integer, Object> valueMap = new HashMap<>();

        if (familyId == null && roomId == null) {
            WcDataPlayerDayExample example = new WcDataPlayerDayExample();
            WcDataPlayerDayExample.Criteria criteria = example.createCriteria();
            criteria.andPlayerIdEqualTo(playerId)
                    .andAppIdEqualTo(ContextUtils.getBusinessEvnEnum().getAppId())
                    .andStatDateValueIn(dates);

            List<WcDataPlayerDay> list = playerDayMapper.selectByExample(example);
            valueMap = list.stream().collect(Collectors.toMap(WcDataPlayerDay::getStatDateValue, v -> v, (k1, k2) -> k2));
        } else {
            WcDataPlayerRoomDayExample example = new WcDataPlayerRoomDayExample();
            WcDataPlayerRoomDayExample.Criteria criteria = example.createCriteria();
            criteria.andPlayerIdEqualTo(playerId)
                    .andAppIdEqualTo(ContextUtils.getBusinessEvnEnum().getAppId())
                    .andStatDateValueIn(dates);
            if (familyId != null) {
                criteria.andFamilyIdEqualTo(familyId);
            }
            if (roomId != null) {
                criteria.andRoomIdEqualTo(roomId);
            }

            List<WcDataPlayerRoomDay> list = wcDataPlayerRoomDayMapper.selectByExample(example);
            valueMap = list.stream().collect(Collectors.toMap(WcDataPlayerRoomDay::getStatDateValue, v -> v, (k1, k2) -> k2));
        }

        List<CountDataBean> result = new ArrayList<>(days);
        for (Integer date : dates) {
            CountDataBean countDataBean = new CountDataBean();
            countDataBean.setDate(MyDateUtil.getDayValueDate(date));
            Object wcDataDay = valueMap.get(date);
            if (wcDataDay != null) {
                ReflectionUtils.doWithLocalFields(wcDataDay.getClass(), field -> {
                    String name = field.getName();
                    if (!name.equals(metric)) {
                        return;
                    }

                    Optional<MetricsMeta> metricsMeta = MetricsConstants.getMetricsMeta(name);
                    if (!metricsMeta.isPresent()) {
                        return;
                    }

                    ReflectionUtils.makeAccessible(field);
                    Object value = ReflectionUtils.getField(field, wcDataDay);

                    MetricsMeta.ValueFactory valueFactory = metricsMeta.get().getValueFactory();
                    String formatValue = valueFactory.calculateValue(metricsMeta.get(), name, String.valueOf(value));
                    countDataBean.setValue(formatValue);
                });
            }
            result.add(countDataBean);
        }

        return result;
    }

    @Override
    public Map<String, String> getDayKeyIndicators(int appId, Long familyId, Long roomId, long playerId, Date date, List<String> valueMetrics) {
        if (familyId == null && roomId == null) {
            WcDataPlayerDay param = new WcDataPlayerDay();
            param.setAppId(appId);
            param.setPlayerId(playerId);
            param.setStatDateValue(Integer.valueOf(DateUtil.formatDateToString(date, DateUtil.date)));

            log.info("playerDayMapper selectMany. param={}", JsonUtil.dumps(param));
            List<WcDataPlayerDay> wcDataDays = playerDayMapper.selectMany(param);
            Map<String, String> baseValueMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(wcDataDays)) {
                WcDataPlayerDay wcDataDay = wcDataDays.get(0);
                baseValueMap = MetricsUtil.convertToMap(WcDataPlayerDay.class, wcDataDay);
            }

            return MetricsUtil.convertToResultMap(valueMetrics, baseValueMap);
        }

        WcDataPlayerRoomDay param = new WcDataPlayerRoomDay();
        param.setAppId(appId);
        param.setPlayerId(playerId);
        param.setStatDateValue(Integer.valueOf(DateUtil.formatDateToString(date, DateUtil.date)));

        if (familyId != null) {
            param.setFamilyId(familyId);
        }
        if (roomId != null) {
            param.setRoomId(roomId);
        }

        log.info("getDayKeyIndicators selectMany. param={}", JsonUtil.dumps(param));
        List<WcDataPlayerRoomDay> wcDataDays = wcDataPlayerRoomDayMapper.selectMany(param);
        Map<String, String> baseValueMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(wcDataDays)) {
            WcDataPlayerRoomDay wcDataDay = wcDataDays.get(0);
            baseValueMap = MetricsUtil.convertToMap(WcDataPlayerRoomDay.class, wcDataDay);
        }

        return MetricsUtil.convertToResultMap(valueMetrics, baseValueMap);
    }

    @Override
    public Map<String, String> getWeekKeyIndicators(int appId, Long familyId, Long roomId, long playerId, Date startDate, Date endDate, List<String> valueMetrics) {
        if (familyId == null && roomId == null) {
            WcDataPlayerWeek param = new WcDataPlayerWeek();
            param.setAppId(appId);
            param.setPlayerId(playerId);
            param.setStartWeekDate(startDate);
            param.setEndWeekDate(endDate);

            log.info("playerWeekMapper selectMany. param={}", JsonUtil.dumps(param));
            List<WcDataPlayerWeek> list = playerWeekMapper.selectMany(param);
            Map<String, String> baseValueMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(list)) {
                WcDataPlayerWeek wcDataWeek = list.get(0);
                baseValueMap = MetricsUtil.convertToMap(WcDataPlayerWeek.class, wcDataWeek);
            }

            return MetricsUtil.convertToResultMap(valueMetrics, baseValueMap);
        }

        WcDataPlayerRoomWeek param = new WcDataPlayerRoomWeek();
        param.setAppId(appId);
        param.setPlayerId(playerId);
        param.setStartWeekDate(startDate);
        param.setEndWeekDate(endDate);
        if (roomId != null) {
            param.setRoomId(roomId);
        }
        if (familyId != null) {
            param.setFamilyId(familyId);
        }

        log.info("getWeekKeyIndicators selectMany. param={}", JsonUtil.dumps(param));
        List<WcDataPlayerRoomWeek> list = wcDataPlayerRoomWeekMapper.selectMany(param);
        Map<String, String> baseValueMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(list)) {
            WcDataPlayerRoomWeek wcDataWeek = list.get(0);
            baseValueMap = MetricsUtil.convertToMap(WcDataPlayerRoomWeek.class, wcDataWeek);
        }

        return MetricsUtil.convertToResultMap(valueMetrics, baseValueMap);
    }

    @Override
    public Map<String, String> getMonthKeyIndicators(int appId, Long familyId, Long roomId, long playerId, Date monthDate, List<String> valueMetrics) {
        if (familyId == null && roomId == null) {
            WcDataPlayerMonth param = new WcDataPlayerMonth();
            param.setAppId(appId);
            param.setPlayerId(playerId);
            param.setStatMonth(Integer.valueOf(DateUtil.formatDateToString(monthDate, "yyyyMM")));

            log.info("playerMonthMapper selectMany. param={}", JsonUtil.dumps(param));
            List<WcDataPlayerMonth> list = playerMonthMapper.selectMany(param);
            Map<String, String> baseValueMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(list)) {
                WcDataPlayerMonth wcDataMonth = list.get(0);
                baseValueMap = MetricsUtil.convertToMap(WcDataPlayerMonth.class, wcDataMonth);
            }

            return MetricsUtil.convertToResultMap(valueMetrics, baseValueMap);
        }

        WcDataPlayerRoomMonth param = new WcDataPlayerRoomMonth();
        param.setAppId(appId);
        param.setPlayerId(playerId);
        param.setStatMonth(Integer.valueOf(DateUtil.formatDateToString(monthDate, "yyyyMM")));
        if (roomId != null) {
            param.setRoomId(roomId);
        }
        if (familyId != null) {
            param.setFamilyId(familyId);
        }

        log.info("getMonthKeyIndicators selectMany. param={}", JsonUtil.dumps(param));
        List<WcDataPlayerRoomMonth> list = wcDataPlayerRoomMonthMapper.selectMany(param);
        Map<String, String> baseValueMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(list)) {
            WcDataPlayerRoomMonth wcDataMonth = list.get(0);
            baseValueMap = MetricsUtil.convertToMap(WcDataPlayerRoomMonth.class, wcDataMonth);
        }

        return MetricsUtil.convertToResultMap(valueMetrics, baseValueMap);
    }

    @Override
    public Integer getRoomIncomeCharm(List<Long> roomIds, long playerId, Date startDate, Date endDate) {
        return iCharmStatRemote.getRoomIncomeCharm(roomIds, playerId, startDate, endDate);
    }

    @Override
    public Integer getPersonIncomeCharm(long playerId, Date startDate, Date endDate) {
        return iCharmStatRemote.getPersonIncomeCharm(playerId, startDate, endDate);
    }

    @Override
    public PlayerAssessmentInfoBean getAssessmentInfo(PlayerGetAssessmentDto param) {
        Long familyId = param.getFamilyId();
        List<Long> familySignRoomIds = param.getFamilySignRoomIds();
        Long playerId = param.getPlayerId();

        WcAssert.notNull(familyId, "familyId is null");
        WcAssert.notNull(playerId, "playerId is null");
        WcAssert.notEmpty(familySignRoomIds, "roomIds is empty");

        PlayerAssessmentInfoBean assessmentInfo = iPlayerDataRemote.getAssessmentInfo(param);
        fillSum(assessmentInfo);
        return assessmentInfo;
    }

    /**
     * 计算合计
     */
    private void fillSum(PlayerAssessmentInfoBean assessmentInfo){
        IncomeBean roomIncome = assessmentInfo.getRoomIncome();
        IncomeBean individualIncome = assessmentInfo.getIndividualIncome();
        IncomeBean officialIncome = assessmentInfo.getOfficialIncome();
        CharmRatioBean roomCharm = assessmentInfo.getRoomCharm();
        CharmRatioBean individualCharm = assessmentInfo.getIndividualCharm();
        CharmRatioBean officialCharm = assessmentInfo.getOfficialCharm();

        BigDecimal currentIncomeSum = BigDecimal.ZERO;
        BigDecimal preIncomeSum = BigDecimal.ZERO;
        Integer currentCharmSum = 0;
        Integer preCharmSum = 0;

        if (roomIncome != null) {
            if (roomIncome.getCurrent() != null) {
                currentIncomeSum = currentIncomeSum.add(new BigDecimal(roomIncome.getCurrent()));
            }
            if (roomIncome.getPer() != null) {
                preIncomeSum = preIncomeSum.add(new BigDecimal(roomIncome.getPer()));
            }
        }
        if (roomCharm != null) {
            if (roomCharm.getCurrent() != null) {
                currentCharmSum += roomCharm.getCurrent();
            }
            if (roomCharm.getPer() != null) {
                preCharmSum += roomCharm.getPer();
            }
        }

        if (individualIncome != null) {
            if (individualIncome.getCurrent() != null) {
                currentIncomeSum = currentIncomeSum.add(new BigDecimal(individualIncome.getCurrent()));
            }
            if (individualIncome.getPer() != null) {
                preIncomeSum = preIncomeSum.add(new BigDecimal(individualIncome.getPer()));
            }
        }
        if (individualCharm != null) {
            if (individualCharm.getCurrent() != null) {
                currentCharmSum += individualCharm.getCurrent();
            }
            if (individualCharm.getPer() != null) {
                preCharmSum += individualCharm.getPer();
            }
        }

        if (officialIncome != null) {
            if (officialIncome.getCurrent() != null) {
                currentIncomeSum = currentIncomeSum.add(new BigDecimal(officialIncome.getCurrent()));
            }
            if (officialIncome.getPer() != null) {
                preIncomeSum = preIncomeSum.add(new BigDecimal(officialIncome.getPer()));
            }
        }
        if (officialCharm != null) {
            if (officialCharm.getCurrent() != null) {
                currentCharmSum += officialCharm.getCurrent();
            }
            if (officialCharm.getPer() != null) {
                preCharmSum += officialCharm.getPer();
            }
        }

        assessmentInfo.setSumIncome(new IncomeBean()
                .setCurrent(currentIncomeSum.toString())
                .setPer(preIncomeSum.toString())
                .setRatio(CalculateUtil.relativeRatio(preIncomeSum.toString(), currentIncomeSum.toString())));
        assessmentInfo.setSumCharm(new CharmRatioBean()
                .setCurrent(currentCharmSum)
                .setPer(preCharmSum)
                .setRatio(CalculateUtil.relativeRatio(preCharmSum, currentCharmSum)));
    }

    @Override
    public PaySettlePeriodDto getPaySettlePeriod(int appId, long familyId) {
        return creatorDataQueryCommonManager.getSettlePeriod(appId, familyId, PaySettlePeriodNumberEnum.CURRENT_PERIOD);
    }

    @Override
    public PaySettlePeriodDto getPaySettlePeriodPre(int appId, long familyId) {
        return creatorDataQueryCommonManager.getSettlePeriod(appId, familyId, PaySettlePeriodNumberEnum.PRE_PERIOD);
    }

    @Override
    public Map<String, String> countPlayerDay(Long family, List<Long> roomIds, List<String> valueMetrics, Date day) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Integer dateDayValue = MyDateUtil.getDateDayValue(day);
        Map<String, String> result = new HashMap<>();

        //签约主播数
        if (valueMetrics.contains(MetricsEnum.SIGN_PLAYER_CNT.getValue())) {
            int signPlayerCnt = playerRoomDayExtMapper.countPlayer(new CountPlayerEntity()
                    .setAppId(appId)
                    .setFamilyId(family)
                    .setRoomIds(roomIds)
                    , dateDayValue);
            result.put(MetricsEnum.SIGN_PLAYER_CNT.getValue(), String.valueOf(signPlayerCnt));
        }

        return result;
    }

    @Override
    public Map<Integer, Map<String, Integer>> countPlayerDays(Long familyId, List<Long> roomIds, List<String> valueMetrics, List<Integer> dayValues) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Map<Integer, Map<String, Integer>> result = new HashMap<>();

        //签约主播数
        if (valueMetrics.contains(MetricsEnum.SIGN_PLAYER_CNT.getValue())) {
            List<CountPlayerPo> countPlayerPos = playerRoomDayExtMapper.countPlayerDays(new CountPlayerEntity()
                    .setAppId(appId)
                    .setFamilyId(familyId)
                    .setRoomIds(roomIds), dayValues);
            if (CollectionUtils.isNotEmpty(countPlayerPos)) {
                for (CountPlayerPo countPlayerPo : countPlayerPos) {
                    Map<String, Integer> dayMap = result.computeIfAbsent(countPlayerPo.getStatDateValue(), k -> new HashMap<>());
                    dayMap.put(MetricsEnum.SIGN_PLAYER_CNT.getValue(), countPlayerPo.getCnt());
                }
            }
        }

        return result;
    }

    @Override
    public Map<String, String> countPlayerWeek(Long familyId, List<Long> roomIds, Date startDate, Date endDate, List<String> valueMetrics) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        String startDay = DateUtil.formatDateToString(startDate, DateUtil.date_2);
        String endDay = DateUtil.formatDateToString(endDate, DateUtil.date_2);
        Map<String, String> result = new HashMap<>();

        //签约主播数
        if (valueMetrics.contains(MetricsEnum.SIGN_PLAYER_CNT.getValue())) {
            int signPlayerCnt = playerRoomWeekExtMapper.countPlayer(new CountPlayerEntity()
                            .setAppId(appId)
                            .setFamilyId(familyId)
                            .setRoomIds(roomIds)
                    , startDay, endDay);
            result.put(MetricsEnum.SIGN_PLAYER_CNT.getValue(), String.valueOf(signPlayerCnt));
        }

        return result;
    }

    @Override
    public Map<String, String> countPlayerMonth(Long familyId, List<Long> roomIds, Date monthDate, List<String> valueMetrics) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Integer dateMonthValue = MyDateUtil.getDateMonthValue(monthDate);
        Map<String, String> result = new HashMap<>();

        //签约主播数
        if (valueMetrics.contains(MetricsEnum.SIGN_PLAYER_CNT.getValue())) {
            int signPlayerCnt = playerRoomMonthExtMapper.countPlayer(new CountPlayerEntity()
                            .setAppId(appId)
                            .setFamilyId(familyId)
                            .setRoomIds(roomIds)
                    , dateMonthValue);
            result.put(MetricsEnum.SIGN_PLAYER_CNT.getValue(), String.valueOf(signPlayerCnt));
        }

        return result;
    }

    @Override
    public List<DataPlayerDayDTO> getPlayerDayList(GetPlayerRoomDayParam param) {
        WcDataPlayerDayExample example = new WcDataPlayerDayExample();
        example.setOrderByClause("stat_date_value desc");

        WcDataPlayerDayExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(ContextUtils.getBusinessEvnEnum().getAppId());
        if (param.getPlayerId() != null) {
            criteria.andPlayerIdEqualTo(param.getPlayerId());
        }
        if (CollectionUtils.isNotEmpty(param.getDayValues())) {
            criteria.andStatDateValueIn(param.getDayValues());
        }

        List<WcDataPlayerDay> poList = playerDayMapper.selectByExample(example);
        return DataCenterInfraConvert.I.playerDays2DTOs(poList);
    }

    @Override
    public List<DataPlayerRoomDayDTO> getPlayerRoomDayList(GetPlayerRoomDayParam param) {
        WcDataPlayerRoomDayExample example = new WcDataPlayerRoomDayExample();
        example.setOrderByClause("stat_date_value desc");

        WcDataPlayerRoomDayExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(ContextUtils.getBusinessEvnEnum().getAppId());
        if (param.getPlayerId() != null) {
            criteria.andPlayerIdEqualTo(param.getPlayerId());
        }
        if (CollectionUtils.isNotEmpty(param.getDayValues())) {
            criteria.andStatDateValueIn(param.getDayValues());
        }
        if (param.getRoomId() != null) {
            criteria.andRoomIdEqualTo(param.getRoomId());
        }
        if (param.getFamilyId() != null) {
            criteria.andFamilyIdEqualTo(param.getFamilyId());
        }

        List<WcDataPlayerRoomDay> poList = wcDataPlayerRoomDayMapper.selectByExample(example);
        return DataCenterInfraConvert.I.playerRoomDays2DTOs(poList);
    }

    @Override
    public List<Long> getPlayerIdsWeekHasIncomeByMinPlayerId(Date weekStartDay, Date weekEndDay, Long minPlayerId, Integer pageSize) {
        return realPlayerWeekExtMapper.getPlayerIdsWeekHasIncomeByMinPlayerId(weekStartDay, weekEndDay, minPlayerId, pageSize, ContextUtils.getBusinessEvnEnum().getAppId());
    }

    @Override
    public PlayerDataForGrowDTO getPlayerGrowDataInWeek(Long playerId, Date weekStartDay, Date weekEndDay) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();

        WcDataRealPlayerWeekExample example = new WcDataRealPlayerWeekExample();
        example.createCriteria()
                .andStartWeekDateEqualTo(weekStartDay)
                .andEndWeekDateEqualTo(weekEndDay)
                .andAppIdEqualTo(appId)
                .andPlayerIdEqualTo(playerId);
        List<WcDataRealPlayerWeek> realList = realPlayerWeekMapper.selectByExample(example);

        PlayerDataForGrowDTO dto = new PlayerDataForGrowDTO();
        dto.setPlayerId(playerId);
        if (CollectionUtils.isNotEmpty(realList)) {
            WcDataRealPlayerWeek week = realList.get(0);
            dto.setChatUserCnt(week.getChatUserCnt() == null ? 0 : week.getChatUserCnt());
            dto.setReplyChatUserCnt(week.getReplyChatUserCnt() == null ? 0 : week.getReplyChatUserCnt());
            dto.setReplyChatNewUserCnt(week.getReplyChatNewUserCnt() == null ? 0 : week.getReplyChatNewUserCnt());
            dto.setGiftUserCnt(week.getGiftUserCnt() == null ? 0 : week.getGiftUserCnt());
            dto.setGiftNewUserCnt(week.getGiftNewUserCnt() == null ? 0 : week.getGiftNewUserCnt());
            dto.setAllIncome(week.getAllIncome() == null ? BigDecimal.ZERO : week.getAllIncome());
            dto.setUpGuestDur(week.getUpGuestDur() == null ? BigDecimal.ZERO : week.getUpGuestDur());
            dto.setNewFansUserCnt(week.getNewFansUserCnt() == null ? 0 : week.getNewFansUserCnt());
        }

        // 违规
        Long violationCnt = liveAuditManager.countPlayerAudit(playerId, weekStartDay, weekEndDay);
        dto.setViolationCnt(violationCnt == null ? 0 : violationCnt.intValue());

        // 麦序
        try {
            Long checkInCnt = checkInDataManager.countUserCheckIn(playerId, weekStartDay, weekEndDay);
            dto.setCheckInCnt(checkInCnt == null ? 0 : checkInCnt.intValue());
        } catch (Exception e) {
            log.error("countUserCheckIn playerId={} error:", playerId, e);
        }

        return dto;
    }

    private IncomeBean getIncomeBeanByPlayer(long familyId, Long roomId, long player, PaySettleConfigCodeEnum configCodeEnum ){
        long currentIncome = creatorDataQueryCommonManager.queryTradeStatisticsValue(familyId, roomId, player, configCodeEnum.getConfigCode(), PeriodTypeEnum.CURRENT_PERIOD);
        long preIncome = creatorDataQueryCommonManager.queryTradeStatisticsValue(familyId, roomId, player, configCodeEnum.getConfigCode(), PeriodTypeEnum.PRE_PERIOD);
        String currentIncomeStr = String.valueOf(currentIncome);
        String preIncomeStr = String.valueOf(preIncome);
        return new IncomeBean()
                .setCurrent(currentIncomeStr)
                .setPer(preIncomeStr)
                .setRatio(CalculateUtil.relativeRatio(preIncomeStr, currentIncomeStr));

    }

}
