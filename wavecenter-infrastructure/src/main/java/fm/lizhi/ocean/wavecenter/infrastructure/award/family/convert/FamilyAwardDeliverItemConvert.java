package fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardDeliverExecutionStatusEnum;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardResourceTypeEnum;
import fm.lizhi.ocean.wavecenter.common.config.BizCommonConfig;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wavecenter.base.convert.CommonConvert;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilyAwardDeliverItem;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilyLevelAwardItem;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.param.CreateFamilyAwardDeliverItemParam;
import fm.lizhi.ocean.wavecenter.service.award.family.dto.FamilyAwardDeliverItemDTO;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        uses = {
                CommonConvert.class,
        },
        imports = {
                ConfigUtils.class,
                Date.class,
                UrlUtils.class,
                FamilyAwardResourceTypeEnum.class,
                FamilyAwardDeliverExecutionStatusEnum.class,
        }
)
public abstract class FamilyAwardDeliverItemConvert {

    @Autowired
    private CommonConfig commonConfig;

    @Mapping(target = "resourceType", expression = "java(FamilyAwardResourceTypeEnum.fromValue(awardItem.getResourceType()))")
    public abstract CreateFamilyAwardDeliverItemParam toCreateParam(
            WcFamilyLevelAwardItem awardItem, String resourceName, String resourceImage);

    @Mapping(target = "resourceType", expression = "java(FamilyAwardResourceTypeEnum.PP_LEVEL_RECOMMEND_CARD)")
    @Mapping(target = "resourceNumber", source = "levelRecommendCardNumber")
    @Mapping(target = "resourceValidPeriod", constant = "0")
    @Mapping(target = "resourceId", constant = "0L")
    @Mapping(target = "resourceName", constant = "")
    @Mapping(target = "resourceImage", constant = "")
    public abstract CreateFamilyAwardDeliverItemParam toOtherAwardLevelRecommendCardCreateParam(Integer levelRecommendCardNumber);

    @Mapping(target = "resourceType", expression = "java(FamilyAwardResourceTypeEnum.PP_FLOW_GROWTH_RECOMMEND_CARD)")
    @Mapping(target = "resourceNumber", source = "flowGrowthRecommendCardNumber")
    @Mapping(target = "resourceValidPeriod", constant = "0")
    @Mapping(target = "resourceId", constant = "0L")
    @Mapping(target = "resourceName", constant = "")
    @Mapping(target = "resourceImage", constant = "")
    public abstract CreateFamilyAwardDeliverItemParam toOtherAwardFlowGrowthRecommendCardCreateParam(Integer flowGrowthRecommendCardNumber);

    @Mapping(target = "resourceType", expression = "java(FamilyAwardResourceTypeEnum.PP_NEW_ROOM_RETAIN_RECOMMEND_CARD)")
    @Mapping(target = "resourceNumber", source = "newRoomRetainRecommendCardNumber")
    @Mapping(target = "resourceValidPeriod", constant = "0")
    @Mapping(target = "resourceId", constant = "0L")
    @Mapping(target = "resourceName", constant = "")
    @Mapping(target = "resourceImage", constant = "")
    public abstract CreateFamilyAwardDeliverItemParam toOtherAwardNewRoomRetainRecommendCardCreateParam(Integer newRoomRetainRecommendCardNumber);

    @Mapping(target = "resourceType", expression = "java(FamilyAwardResourceTypeEnum.PP_ZERO_LOST_ROOM_RECOMMEND_CARD)")
    @Mapping(target = "resourceNumber", source = "zeroLostRoomRecommendCardNumber")
    @Mapping(target = "resourceValidPeriod", constant = "0")
    @Mapping(target = "resourceId", constant = "0L")
    @Mapping(target = "resourceName", constant = "")
    @Mapping(target = "resourceImage", constant = "")
    public abstract CreateFamilyAwardDeliverItemParam toOtherAwardZeroLostRoomRecommendCardCreateParam(Integer zeroLostRoomRecommendCardNumber);

    @Mapping(target = "resourceType", expression = "java(FamilyAwardResourceTypeEnum.PP_SPECIAL_RECOMMEND_CARD)")
    @Mapping(target = "resourceNumber", source = "specialRecommendCardNumber")
    @Mapping(target = "resourceValidPeriod", constant = "0")
    @Mapping(target = "resourceId", constant = "0L")
    @Mapping(target = "resourceName", constant = "")
    @Mapping(target = "resourceImage", constant = "")
    public abstract CreateFamilyAwardDeliverItemParam toOtherAwardSpecialRecommendCardCreateParam(Integer specialRecommendCardNumber);

    @Mapping(target = "resourceType", expression = "java(FamilyAwardResourceTypeEnum.PP_LEVEL_NEW_ROOM)")
    @Mapping(target = "resourceNumber", source = "levelNewRoomNumber")
    @Mapping(target = "resourceValidPeriod", constant = "0")
    @Mapping(target = "resourceId", constant = "0L")
    @Mapping(target = "resourceName", constant = "")
    @Mapping(target = "resourceImage", constant = "")
    public abstract CreateFamilyAwardDeliverItemParam toOtherAwardLevelNewRoomCreateParam(Integer levelNewRoomNumber);

    @Mapping(target = "resourceType", expression = "java(FamilyAwardResourceTypeEnum.PP_FLOW_GROWTH_NEW_ROOM)")
    @Mapping(target = "resourceNumber", source = "flowGrowthNewRoomNumber")
    @Mapping(target = "resourceValidPeriod", constant = "0")
    @Mapping(target = "resourceId", constant = "0L")
    @Mapping(target = "resourceName", constant = "")
    @Mapping(target = "resourceImage", constant = "")
    public abstract CreateFamilyAwardDeliverItemParam toOtherAwardFlowGrowthNewRoomCreateParam(Integer flowGrowthNewRoomNumber);

    @Mapping(target = "resourceType", expression = "java(FamilyAwardResourceTypeEnum.PP_LOST_ROOM_NEW_ROOM)")
    @Mapping(target = "resourceNumber", source = "lostRoomNewRoomNumber")
    @Mapping(target = "resourceValidPeriod", constant = "0")
    @Mapping(target = "resourceId", constant = "0L")
    @Mapping(target = "resourceName", constant = "")
    @Mapping(target = "resourceImage", constant = "")
    public abstract CreateFamilyAwardDeliverItemParam toOtherAwardLostRoomNewRoomCreateParam(Integer lostRoomNewRoomNumber);

    @Mapping(target = "resourceType", expression = "java(FamilyAwardResourceTypeEnum.PP_NEW_ROOM_RETAIN_NEW_ROOM)")
    @Mapping(target = "resourceNumber", source = "newRoomRetainNewRoomNumber")
    @Mapping(target = "resourceValidPeriod", constant = "0")
    @Mapping(target = "resourceId", constant = "0L")
    @Mapping(target = "resourceName", constant = "")
    @Mapping(target = "resourceImage", constant = "")
    public abstract CreateFamilyAwardDeliverItemParam toOtherAwardNewRoomRetainNewRoomCreateParam(Integer newRoomRetainNewRoomNumber);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "resourceDeliverType", expression = "java(param.getResourceType().getDeliverType().getValue())")
    @Mapping(target = "resourceType", expression = "java(param.getResourceType().getValue())")
    @Mapping(target = "resourceName", source = "param.resourceName", defaultValue = "")
    @Mapping(target = "resourceImagePath", expression = "java(UrlUtils.removeHostOrEmpty(param.getResourceImage()))")
    @Mapping(target = "status", expression = "java(FamilyAwardDeliverExecutionStatusEnum.DELIVERING.getValue())")
    @Mapping(target = "errorCode", constant = "0")
    @Mapping(target = "errorText", constant = "")
    @Mapping(target = "createTime", expression = "java(new Date())")
    @Mapping(target = "modifyTime", expression = "java(new Date())")
    public abstract WcFamilyAwardDeliverItem toCreateEntity(
            CreateFamilyAwardDeliverItemParam param, int appId, long recordId, long executionId);

    public abstract List<FamilyAwardDeliverItemDTO> entitiesToDTOS(List<WcFamilyAwardDeliverItem> items);

    @Mapping(target = "resourceImage", source = "item", qualifiedByName = "resourceImagePathToUrl")
    protected abstract FamilyAwardDeliverItemDTO entityToDTO(WcFamilyAwardDeliverItem item);

    @Named("resourceImagePathToUrl")
    protected String resourceImagePathToUrl(WcFamilyAwardDeliverItem item) {
        if (item == null) {
            return StringUtils.EMPTY;
        }
        BizCommonConfig bizConfig = commonConfig.getBizConfig(item.getAppId());
        String resourceImagePath = item.getResourceImagePath();
        if (bizConfig == null) {
            return StringUtils.defaultString(resourceImagePath);
        }
        String cdnHost = bizConfig.getCdnHost();
        return UrlUtils.addHostOrEmpty(resourceImagePath, cdnHost);
    }
}
