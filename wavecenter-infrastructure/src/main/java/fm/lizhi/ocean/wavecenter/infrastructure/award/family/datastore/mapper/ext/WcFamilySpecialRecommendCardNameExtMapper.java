package fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.common.datastore.mysql.constant.ParamContants;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilySpecialRecommendCardName;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcFamilySpecialRecommendCardNameExtMapper {

    @Delete("DELETE FROM `wavecenter_family_special_recommend_card_name`\n" +
            "WHERE `deploy_env` = #{deployEnv} AND `app_id` = #{appId}")
    int deleteByAppId(@Param("appId") int appId, @Param("deployEnv") String deployEnv);

    @Select("SELECT * FROM `wavecenter_family_special_recommend_card_name`\n" +
            "WHERE `deploy_env` = #{deployEnv} AND `app_id` = #{appId}\n" +
            "ORDER BY `id` DESC")
    PageList<WcFamilySpecialRecommendCardName> list(
            @Param("appId") int appId, @Param("deployEnv") String deployEnv,
            @Param(ParamContants.PAGE_NUMBER) int pageNumber, @Param(ParamContants.PAGE_SIZE) int pageSize);
}
