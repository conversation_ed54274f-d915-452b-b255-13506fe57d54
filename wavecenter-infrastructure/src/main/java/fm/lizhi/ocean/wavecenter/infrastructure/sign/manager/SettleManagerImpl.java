package fm.lizhi.ocean.wavecenter.infrastructure.sign.manager;

import fm.lizhi.ocean.wavecenter.api.sign.bean.FamilyAndNjContractBean;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.ISettleRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.request.RequestContractSettle;
import fm.lizhi.ocean.wavecenter.service.sign.dto.SignSettleDTO;
import fm.lizhi.ocean.wavecenter.service.sign.manager.SettleManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/10/17 18:26
 */
@Component
public class SettleManagerImpl implements SettleManager {

    @Autowired
    private ISettleRemote iSettleRemote;

    @Override
    public Map<Long, SignSettleDTO> querySettle(List<FamilyAndNjContractBean> contractList) {
        return iSettleRemote.querySettle(RequestContractSettle.builder().contracts(contractList).build());
    }

    @Override
    public Optional<SignSettleDTO> querySettleByNj(Long njId) {
        return iSettleRemote.querySettleByNj(njId);
    }
}
