package fm.lizhi.ocean.wavecenter.infrastructure.home.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.common.constants.DateType;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.IncomeBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomPerformanceBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomPerformanceStatisticsBean;
import fm.lizhi.ocean.wavecenter.api.home.bean.MetricsDataBean;
import fm.lizhi.ocean.wavecenter.api.home.bean.RoomTrendChartBean;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetGuildKeyDataSummary;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetGuildKeyDataTrendChart;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetGuildMarketMonitorSummary;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetGuildMarketMonitorTrendChart;
import fm.lizhi.ocean.wavecenter.base.util.CalculateUtil;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.infrastructure.common.MetricsUtil;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PaySettleConfigCodeEnum;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataFamilyDay;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataFamilyWeek;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataRoomFamilyDay;
import fm.lizhi.ocean.wavecenter.infrastructure.income.manager.PaymentManager;
import fm.lizhi.ocean.wavecenter.service.datacenter.config.DataCenterConfig;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.DataFamilyDayDTO;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.DataRoomFamilyDayDTO;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.GetRoomDayListParam;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.GuildDataManager;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.RoomDataManager;
import fm.lizhi.ocean.wavecenter.service.home.convert.GuildHomeConvert;
import fm.lizhi.ocean.wavecenter.service.home.dto.GuildKeyDataTrendChartDTO;
import fm.lizhi.ocean.wavecenter.service.home.dto.GuildMarketMonitorSummaryDTO;
import fm.lizhi.ocean.wavecenter.service.home.dto.GuildMarketMonitorTrendChartDTO;
import fm.lizhi.ocean.wavecenter.service.home.manager.GuildHomeManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 公会首页
 * <AUTHOR>
 */
@Component
@Slf4j
public class GuildHomeManagerImpl implements GuildHomeManager {

    @Autowired
    private PaymentManager paymentManager;

    @Autowired
    private GuildDataManager guildDataManager;

    @Autowired
    private RoomDataManager roomDataManager;

    @Autowired
    private DataCenterConfig dataCenterConfig;

    @Autowired
    private UserManager userManager;


    @Override
    public MetricsDataBean getKeyDataSummary(RequestGetGuildKeyDataSummary request) {
        Date startDate = DateUtil.formatStrToDate(request.getStartDate(), DateUtil.date_2);
        Date endDate = DateUtil.formatStrToDate(request.getEndDate(), DateUtil.date_2);

        // 公会收入汇总
        IncomeBean sumIncome = paymentManager.getIncomeBeanByFamily(request.getFamilyId(), request.getRoomIds(), startDate, endDate, PaySettleConfigCodeEnum.FAMILY_INCOME_TOTAL_AMOUNT, DateType.WEEK);
        return GuildHomeConvert.I.buildMetricsDataBean(sumIncome, startDate, endDate);
    }

    @Override
    public List<GuildKeyDataTrendChartDTO> getKeyDataTrendChart(RequestGetGuildKeyDataTrendChart request) {

        Date startDate = DateUtil.formatStrToDate(request.getStartDate(), DateUtil.date_2);
        Date endDate = DateUtil.formatStrToDate(request.getEndDate(), DateUtil.date_2);
        Date startBefore = DateUtil.getDayBefore(startDate, 1);
        Date endBefore = DateUtil.getDayBefore(endDate, 1);

        String allIncomeKey = WcDataFamilyDay.Fields.allIncome;
        String roomAvgIncomeKey = WcDataFamilyDay.Fields.roomAvgIncome;
        String upGuestPlayerCnt = WcDataFamilyDay.Fields.upGuestPlayerCnt;
        String playerAvgIncomeKey = WcDataFamilyDay.Fields.playerAvgIncome;

        Double ratioWarning = dataCenterConfig.getRatioWarning();
        List<String> valueMetrics = CollUtil.newArrayList(allIncomeKey, roomAvgIncomeKey, upGuestPlayerCnt, playerAvgIncomeKey);

        // 获取当前
        List<DataFamilyDayDTO> listCurrent = guildDataManager.getFamilyDayList(request.getFamilyId(), request.getAppId(), startDate, endDate);
        if (CollUtil.isEmpty(listCurrent)){
            log.info("getKeyDataTrendChart is empty. appId={}, familyId={}, startDate={}, endDate={}", request.getAppId(), request.getFamilyId(), startDate.getTime(), endDate.getTime());
            return Collections.emptyList();
        }

        // 获取上个周期
        List<DataFamilyDayDTO> listBefore = guildDataManager.getFamilyDayList(request.getFamilyId(), request.getAppId(), startBefore, endBefore);
        if (CollUtil.isEmpty(listBefore)){
            log.info("getKeyDataTrendChart listBefore is empty. appId={}, familyId={}, startDate={}, endDate={}", request.getAppId(), request.getFamilyId(), startBefore.getTime(), endBefore.getTime());
        }
        Map<Integer, DataFamilyDayDTO> mapBefore = listBefore.stream().collect(Collectors.toMap(DataFamilyDayDTO::getStatDateValue, data -> data));


        return listCurrent.stream().map(data -> {
            Map<String, String> baseValueMap = MetricsUtil.convertToMap(DataFamilyDayDTO.class, data);
            Integer key = MyDateUtil.getDateDayValue(DateUtil.getDayBefore(data.getStatDate(), 1));
            DataFamilyDayDTO beforeDate = mapBefore.get(key);
            Map<String, String> beforeValueMap = new HashMap<>();
            if (null != beforeDate){
                beforeValueMap = MetricsUtil.convertToMap(DataFamilyDayDTO.class, beforeDate);
                beforeValueMap = MetricsUtil.convertToResultMap(valueMetrics, beforeValueMap);
            }

            baseValueMap = MetricsUtil.convertToResultMap(valueMetrics, baseValueMap);

            GuildKeyDataTrendChartDTO dto = new GuildKeyDataTrendChartDTO();
            dto.setDate(data.getStatDate());
            dto.setSumIncome(GuildHomeConvert.I.buildTrendChartBean(MapUtil.getStr(baseValueMap, allIncomeKey), MapUtil.getStr(beforeValueMap, allIncomeKey), ratioWarning));
            dto.setRoomAvgIncome(GuildHomeConvert.I.buildTrendChartBean(MapUtil.getStr(baseValueMap, roomAvgIncomeKey), MapUtil.getStr(beforeValueMap, roomAvgIncomeKey), ratioWarning ));
            dto.setSignUpGuestPlayerCnt(GuildHomeConvert.I.buildTrendChartBean(MapUtil.getStr(baseValueMap, upGuestPlayerCnt), MapUtil.getStr(beforeValueMap, upGuestPlayerCnt), ratioWarning));
            dto.setPlayerAvgIncome(GuildHomeConvert.I.buildTrendChartBean(MapUtil.getStr(baseValueMap, playerAvgIncomeKey), MapUtil.getStr(beforeValueMap, playerAvgIncomeKey), ratioWarning ));

            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public GuildMarketMonitorSummaryDTO getMarketMonitorSummary(RequestGetGuildMarketMonitorSummary request) {
        Date startDate = DateUtil.formatStrToDate(request.getStartDate(), DateUtil.date_2);
        Date endDate = DateUtil.formatStrToDate(request.getEndDate(), DateUtil.date_2);

        String incomeRoomCntMetrics = WcDataFamilyWeek.Fields.incomeRoomCnt;
        Map<String, IncomeBean> indicatorMap = getGuildWeekKeyIndicator(request.getAppId(), request.getFamilyId(), startDate, endDate, Collections.singletonList(incomeRoomCntMetrics));

        //有收入厅数
        IncomeBean incomeRoomCnt = indicatorMap.get(incomeRoomCntMetrics);

        return new GuildMarketMonitorSummaryDTO()
                .setIncomeRoomCnt(GuildHomeConvert.I.buildMetricsDataBean(incomeRoomCnt, startDate, endDate));
    }

    @Override
    public List<GuildMarketMonitorTrendChartDTO> getMarketMonitorTrendChartByRoomIncome(RequestGetGuildMarketMonitorTrendChart request) {
        Date startDate = DateUtil.formatStrToDate(request.getStartDate(), DateUtil.date_2);
        Date endDate = DateUtil.formatStrToDate(request.getEndDate(), DateUtil.date_2);
        Date startDateBefore = DateUtil.getDayBefore(startDate, 1);
        Date endDateBefore = DateUtil.getDayBefore(endDate, 1);

        // 指标
        String allIncomeMetrics = WcDataRoomFamilyDay.Fields.allIncome;

        // 先查离线数据 - 根据日期分组
        Map<Integer, List<DataRoomFamilyDayDTO>> roomDataByStatDate = getRoomDayDateMap(request.getAppId(), request.getFamilyId(), request.getRoomIds(), startDate, endDate);
        Map<Integer, List<DataRoomFamilyDayDTO>> roomDataByStatDateBefore = getRoomDayDateMap(request.getAppId(), request.getFamilyId(), request.getRoomIds(), startDateBefore, endDateBefore);

        // 同一周，需要查询今天的实时数据并添加到离线数据中
        boolean sameWeek = cn.hutool.core.date.DateUtil.isSameWeek(startDate, new Date(), true);
        if (sameWeek){
            List<RoomPerformanceStatisticsBean> list = paymentManager.queryHallPerformanceStatistics(request.getFamilyId(), request.getRoomIds());
            if (CollUtil.isNotEmpty(list)){
                roomDataByStatDate.put(MyDateUtil.getDateDayValue(new Date()), GuildHomeConvert.I.buildDataRoomFamilyDays(list));
            }else {
                log.info("getMarketMonitorTrendChartByRoomIncome roomPerformance is empty. familyId={}, appId={}", request.getFamilyId(), request.getAppId());
            }

        }

        return buildTrendChartResult(roomDataByStatDateBefore, roomDataByStatDate, allIncomeMetrics);

    }


    @Override
    public List<GuildMarketMonitorTrendChartDTO> getMarketMonitorTrendChartByRoomSumIncome(RequestGetGuildMarketMonitorTrendChart request) {

        Date startDate = DateUtil.formatStrToDate(request.getStartDate(), DateUtil.date_2);
        Date endDate = DateUtil.formatStrToDate(request.getEndDate(), DateUtil.date_2);
        Date startDateBefore = DateUtil.getDayBefore(startDate, 1);
        Date endDateBefore = DateUtil.getDayBefore(endDate, 1);

        // 指标
        String allIncomeMetrics = WcDataRoomFamilyDay.Fields.weekAllIncome;

        // 先查离线数据 - 根据日期分组
        Map<Integer, List<DataRoomFamilyDayDTO>> roomDataByStatDate = getRoomDayDateMap(request.getAppId(), request.getFamilyId(), request.getRoomIds(), startDate, endDate);
        Map<Integer, List<DataRoomFamilyDayDTO>> roomDataByStatDateBefore = getRoomDayDateMap(request.getAppId(), request.getFamilyId(), request.getRoomIds(), startDateBefore, endDateBefore);

        // 同一周，需要查询今天的实时数据并添加到离线数据中
        boolean sameWeek = cn.hutool.core.date.DateUtil.isSameWeek(startDate, new Date(), true);
        if (sameWeek) {
            Map<Long, RoomPerformanceStatisticsBean> todayPerformanceMap = paymentManager.queryHallPerformanceStatistics(request.getFamilyId(), request.getRoomIds())
                    .stream().collect(Collectors.toMap(RoomPerformanceStatisticsBean::getRoomId, resource -> resource, (existing, replacement) -> replacement));
            List<DataRoomFamilyDayDTO> todayData = new ArrayList<>();

            Integer today = MyDateUtil.getDateDayValue(new Date());
            List<DataRoomFamilyDayDTO> yesterdayDataList = roomDataByStatDate.get(MyDateUtil.getDateDayValueBefore(today, 1));
            if (CollUtil.isEmpty(yesterdayDataList)){
                // 昨天没数据，代表今天是周一，只查实时数据就好了
                todayData = todayPerformanceMap.keySet().stream().map(e -> {
                    RoomPerformanceBean roomPerformanceBean = MapUtil.get(todayPerformanceMap, e, RoomPerformanceBean.class);
                    DataRoomFamilyDayDTO dto = new DataRoomFamilyDayDTO();
                    dto.setWeekAllIncome(new BigDecimal(
                            Optional.ofNullable(roomPerformanceBean).map(RoomPerformanceBean::getSumIncome).orElse("0")
                    ));
                    dto.setRoomId(e);
                    return dto;
                }).collect(Collectors.toList());
            }else {
                // 累计历史数据
                todayData = yesterdayDataList.stream().map(e -> {
                    DataRoomFamilyDayDTO dto = new DataRoomFamilyDayDTO();
                    dto.setWeekAllIncome(new BigDecimal(
                            Optional.ofNullable(MapUtil.get(todayPerformanceMap, e.getRoomId(), RoomPerformanceBean.class)).map(RoomPerformanceBean::getSumIncome).orElse("0")
                    ).add(e.getWeekAllIncome()));
                    dto.setRoomId(e.getRoomId());
                    return dto;

                }).collect(Collectors.toList());
            }

            roomDataByStatDate.put(today, todayData);
        }

        return buildTrendChartResult(roomDataByStatDateBefore, roomDataByStatDate, allIncomeMetrics);
    }


    private List<RoomTrendChartBean> buildRoomTrendChartBeanList(String metrics, Integer date, List<DataRoomFamilyDayDTO> dataList, Map<Long, SimpleUserDto> userInfoMap, Table<Integer, Long, DataRoomFamilyDayDTO> roomDataTableBefore) {
        Double ratioWarning = dataCenterConfig.getRatioWarning();
        Integer before = MyDateUtil.getDateDayValueBefore(date, 1);

        List<RoomTrendChartBean> resultList = new ArrayList<>();

        for (DataRoomFamilyDayDTO dto : dataList) {
            Optional<SimpleUserDto> njUserInfoOpt = Optional.ofNullable(userInfoMap.get(dto.getRoomId()));

            Map<String, String> dataMap = MetricsUtil.convertToMap(DataRoomFamilyDayDTO.class, dto);
            Map<String, String> dataBeforeMap = MetricsUtil.convertToMap(DataRoomFamilyDayDTO.class, roomDataTableBefore.get(before, dto.getRoomId()));

            RoomTrendChartBean result = new RoomTrendChartBean()
                    .setBand(njUserInfoOpt.map(SimpleUserDto::getBand).orElse(""))
                    .setName(njUserInfoOpt.map(SimpleUserDto::getName).orElse(""))
                    .setValue(MapUtil.getDouble(dataMap, metrics, 0.0))
                    .setRatio(CalculateUtil.relativeRatio(dataBeforeMap.get(metrics), dataMap.get(metrics)))
                    .setPre(MapUtil.getDouble(dataBeforeMap, metrics, 0.0))
                    .setWarn(new BigDecimal(String.valueOf(ratioWarning)).compareTo(CalculateUtil.relativeRatioNotFormat(dataBeforeMap.get(metrics), dataMap.get(metrics))) > 0);

            resultList.add(result);
        }
        return resultList;
    }


    /**
     * 获取厅数据-天
     * @return key=日期，value=数据列表
     */
    private Map<Integer, List<DataRoomFamilyDayDTO>> getRoomDayDateMap(int appId, Long familyId, List<Long> roomIds, Date startDate, Date endDate){
        // 直接查离线数据
        GetRoomDayListParam queryParam = new GetRoomDayListParam()
                .setFamilyId(familyId).setRoomIds(roomIds)
                .setStartDay(startDate).setEndDay(endDate);

        List<DataRoomFamilyDayDTO> roomDayDataList = roomDataManager.getRoomFamilyDayList(queryParam);

        if (CollUtil.isEmpty(roomDayDataList)){
            log.info("getRoomDayDateMap is empty. appId={}, familyId={}, startDate={}, endDate={}", appId, familyId, startDate.getTime(), endDate.getTime());
            return new HashMap<>();
        }

        // 根据日期分组
        return roomDayDataList.stream()
                .collect(Collectors.groupingBy(
                        DataRoomFamilyDayDTO::getStatDateValue,
                        Collectors.mapping(dto -> dto, Collectors.toList())
                ));
    }

    /**
     * 获取周指标
     */
    private Map<String, IncomeBean> getGuildWeekKeyIndicator(int appId, Long familyId, Date startDate, Date endDate, List<String> valueMetrics){

        // 获取本周
        Map<String, String> indicators = guildDataManager.getWeekKeyIndicators(
                appId, familyId, startDate, endDate, valueMetrics);

        // 获取上周
        Date startBefore = DateUtil.getDayBefore(startDate, 7);
        Date endBefore = DateUtil.getDayBefore(endDate, 7);
        Map<String, String> indicatorsBefore = guildDataManager.getWeekKeyIndicators(
                appId, familyId, startBefore, endBefore, valueMetrics);

        Map<String, IncomeBean> result = new HashMap<>();
        for (String metric : valueMetrics) {
            String pre = indicatorsBefore.get(metric);
            String current = indicators.get(metric);
            String ratio = CalculateUtil.relativeRatio(pre, current);
            result.put(metric, new IncomeBean()
                    .setCurrent(current)
                    .setPer(pre)
                    .setRatio(ratio)
            );
        }

        return result;
    }

    /**
     * 构建趋势图结果列表
     */
    private @NotNull List<GuildMarketMonitorTrendChartDTO> buildTrendChartResult(Map<Integer, List<DataRoomFamilyDayDTO>> roomDataByStatDateBefore, Map<Integer, List<DataRoomFamilyDayDTO>> roomDataByStatDate, String allIncomeMetrics) {
        // row = 日期，column = 房间ID, value = 数据
        Table<Integer, Long, DataRoomFamilyDayDTO> roomDataTableBefore = HashBasedTable.create();
        roomDataByStatDateBefore.forEach((statDate, dataRoomList) -> {
            for (DataRoomFamilyDayDTO dataRoom : dataRoomList) {
                Long roomId = dataRoom.getRoomId();
                // 将数据添加到 Table 中
                roomDataTableBefore.put(statDate, roomId, dataRoom);
            }
        });

        // 批量获取厅主信息
        List<Long> njIds = roomDataByStatDate.values().stream()
                .flatMap(List::stream)
                .map(DataRoomFamilyDayDTO::getRoomId)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, SimpleUserDto> userInfoMap = userManager.getSimpleUserMapByIds(njIds);

        return roomDataByStatDate.keySet().stream().map(date -> new GuildMarketMonitorTrendChartDTO()
                        .setDate(MyDateUtil.getDayValueDate(date))
                        .setValues(buildRoomTrendChartBeanList(allIncomeMetrics, date, roomDataByStatDate.get(date), userInfoMap, roomDataTableBefore))
                )
                .sorted(Comparator.comparing(GuildMarketMonitorTrendChartDTO::getDate))
                .collect(Collectors.toList());
    }
}
