package fm.lizhi.ocean.wavecenter.infrastructure.live.remote.xm;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.live.constants.RoomCategoryEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.live.remote.ILiveRoomRemote;
import fm.lizhi.ocean.wavecenter.service.live.config.LiveConfig;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserGroupManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/5/9 15:50
 */
@Slf4j
@Component
public class XmLiveRoomRemote implements ILiveRoomRemote {

    @Autowired
    private UserGroupManager userGroupManager;
    @Autowired
    private LiveConfig liveConfig;

    @Override
    public Optional<RoomCategoryEnum> getUserRoomCategory(Long userId) {
        Long signRoomGroupId = liveConfig.getXm().getSignRoomGroupId();
        boolean userInGroup = userGroupManager.isUserInGroup(userId, signRoomGroupId);
        if (userInGroup) {
            return Optional.of(RoomCategoryEnum.SING);
        }
        return Optional.of(RoomCategoryEnum.AMUSEMENT);
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.XIMI;
    }
}
