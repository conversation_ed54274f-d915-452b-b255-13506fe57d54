package fm.lizhi.ocean.wavecenter.infrastructure.award.singer.convert;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.api.award.singer.bean.SingerDecorateFlowBean;
import fm.lizhi.ocean.wavecenter.api.award.singer.bean.SingerDecorateRuleBean;
import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateOperateStatusEnum;
import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import fm.lizhi.ocean.wavecenter.base.convert.CommonConvert;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.entity.SingerDecorateFlow;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.entity.SingerDecorateRule;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.request.RequestRecoverDecorate;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.request.RequestSendDecorate;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerInfoDTO;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowDTO;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowInitParamDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR,
        imports = {
                ConfigUtils.class, Date.class, SingerDecorateOperateStatusEnum.class, PlatformDecorateTypeEnum.class
        },
        uses = {CommonConvert.class}
)
public interface SingerDecorateConvert {

    SingerDecorateConvert I = Mappers.getMapper(SingerDecorateConvert.class);

    List<SingerDecorateRuleBean> convertSingerDecorateRuleBeanList(Collection<SingerDecorateRule> pageList);




    List<SingerDecorateFlowDTO> convertSingerDecorateFlowBeanList(Collection<SingerDecorateFlow> pageList);


    @Mapping(target = "retryCount", constant = "0")
    @Mapping(target = "recycled", constant = "false")
    @Mapping(target = "status", expression = "java(SingerDecorateOperateStatusEnum.UNPROCESSED.getStatus())")
    @Mapping(target = "ruleId", source = "rule.id")
    @Mapping(target = "appId", source = "param.appId")
    @Mapping(target = "operateType", source = "param.operateType.code")
    @Mapping(target = "singerType", source = "singer.singerType")
    @Mapping(target = "operator", source = "param.operator")
    // todo duration 和 expireTime先暂定，后续不需要的话，可以从表中删除该字段
    @Mapping(target = "operateTime",  expression = "java(new Date())")
    @Mapping(target = "duration", constant = "-1L")
    @Mapping(target = "expireTime", expression = "java(new Date())")
    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "createTime", expression = "java(new Date())")
    @Mapping(target = "modifyTime", expression = "java(new Date())")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "transactionId", source = "transactionId")
    SingerDecorateFlow buildSingerDecorateFlowByGrant(SingerInfoDTO singer, SingerDecorateRuleBean rule, Long transactionId, SingerDecorateFlowInitParamDTO param);


    @Mapping(target = "retryCount", constant = "0")
    @Mapping(target = "recycled", constant = "true")
    @Mapping(target = "operator", source = "param.operator")
    @Mapping(target = "operateType", source = "param.operateType.code")
    @Mapping(target = "reason", source = "param.reason")
    @Mapping(target = "appId", source = "param.appId")
    @Mapping(target = "transactionId", source = "transactionId")
    @Mapping(target = "operateTime",  expression = "java(new Date())")
    @Mapping(target = "status", expression = "java(SingerDecorateOperateStatusEnum.UNPROCESSED.getStatus())")
    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "createTime", expression = "java(new Date())")
    @Mapping(target = "modifyTime", expression = "java(new Date())")
    @Mapping(target = "id", ignore = true)
    SingerDecorateFlow buildSingerDecorateFlowByRecover(SingerDecorateFlowDTO grantFlow, SingerDecorateFlowInitParamDTO param, Long transactionId);

    SingerDecorateFlow convertSingerDecorateFlow(SingerDecorateFlowBean flow);
    SingerDecorateFlow convertSingerDecorateFlow(SingerDecorateFlowDTO flow);

    @Mapping(target = "decorateNumber", constant = "1")
    @Mapping(target = "decorateExpireTime", expression = "java((int) (flow.getDuration() * 60))")
    @Mapping(target = "decorateType", expression = "java(PlatformDecorateTypeEnum.getByType(flow.getDecorateType()))")
    RequestSendDecorate buildRequestSendDecorate(SingerDecorateFlowDTO flow);

    @Mapping(target = "decorateType", expression = "java(PlatformDecorateTypeEnum.getByType(flow.getDecorateType()))")
    RequestRecoverDecorate buildRequestRecoverDecorate(SingerDecorateFlowDTO flow);

    List<SingerDecorateFlowDTO> toSingerDecorateFlowDTOList(List<SingerDecorateFlow> singerDecorateFlowList);

    List<SingerDecorateFlow> toSingerDecorateFlowList(List<SingerDecorateFlowDTO> dtoList);
}
