package fm.lizhi.ocean.wavecenter.infrastructure.income.remote.pp;

import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.IncomeSummaryBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.IncomeSummaryThreadBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.RoomSignBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PaySettleConfigCodeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.PpPlayerSignCharmStatMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.NjIncomeSignPlayerNumPo;
import fm.lizhi.ocean.wavecenter.infrastructure.income.dto.PlayerPayCountParamDto;
import fm.lizhi.ocean.wavecenter.infrastructure.income.manager.PaymentManager;
import fm.lizhi.ocean.wavecenter.infrastructure.income.remote.IIncomeStateRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.IContractRemote;
import fm.lizhi.pay.settle.settleenum.creativecenter.PeriodTypeEnum;
import fm.pp.family.api.FamilyService;
import fm.pp.family.protocol.FamilyServiceProto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/23 11:55
 */
@Slf4j
@Component
public class PpIncomeStateRemote implements IIncomeStateRemote {

    @Autowired
    private IContractRemote iContractRemote;
    @Autowired
    private PaymentManager paymentManager;
    @Autowired
    private FamilyService familyService;
    @Autowired
    private PpPlayerSignCharmStatMapper playerSignCharmStatMapper;

    @Override
    public int getPlayerPayCountByFamily(Long familyId, Date startDate, Date endDate) {
        List<RoomSignBean> list = iContractRemote.getAllSingGuildRoomsList(familyId);
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }

        List<Long> njIds = list.stream().map(UserBean::getId).collect(Collectors.toList());
        Map<Long, Integer> njIncomeSignPlayerNumMap = getNjIncomeSignPlayerNumMap(njIds, startDate, endDate);
        return njIncomeSignPlayerNumMap.values().stream().reduce(0, Integer::sum);
    }

    @Override
    public int getPlayerPayCountByRoom(Long familyId, Long roomId, Date startDate, Date endDate) {
        Map<Long, Integer> njIncomeSignPlayerNumMap = getNjIncomeSignPlayerNumMap(Collections.singletonList(roomId), startDate, endDate);
        return njIncomeSignPlayerNumMap.values().stream().reduce(0, Integer::sum);
    }

    /**
     * 获取厅有收入签约主播数
     *
     * @param njIds     厅主id列表
     * @param beginDate 开始时间
     * @param endDate   结束时间
     * @return 厅有收入签约主播数 key=userId value=收入人数
     */
    private Map<Long, Integer> getNjIncomeSignPlayerNumMap(
            List<Long> njIds, Date beginDate, Date endDate) {
        Result<FamilyServiceProto.ResponseGetNjIncomeSignPlayerNum> result =
                familyService.getNjIncomeSignPlayerNum(
                        njIds, beginDate.getTime(), endDate.getTime());
        if (RpcResult.isFail(result)) {
            log.error("getNjIncomeSignPlayerNum error, rCode={}", result.rCode());
            return Collections.emptyMap();
        }
        String njIncomeSignPlayerNumDtoListStr =
                result.target().getNjIncomeSignPlayerNumDtoListStr();
        List<NjIncomeSignPlayerNumPo> njIncomeSignPlayerNumPos =
                JSONArray.parseArray(njIncomeSignPlayerNumDtoListStr, NjIncomeSignPlayerNumPo.class);
        return njIncomeSignPlayerNumPos.stream().collect(
                Collectors.toMap(NjIncomeSignPlayerNumPo::getUserId, NjIncomeSignPlayerNumPo::getIncomeNum, (o, n) -> n));
    }

    @Override
    public Map<Long, Integer> getPlayerPayCountByRooms(Long familyId, List<Long> roomIds, Date startDate, Date endDate) {
        return getNjIncomeSignPlayerNumMap(roomIds, startDate, endDate);
    }

    @Override
    public IncomeSummaryBean queryTradeValueByFamily(Long familyId, List<Long> roomIds, PeriodTypeEnum periodType) {
        List<PaySettleConfigCodeEnum> configList = Lists.newArrayList(
                PaySettleConfigCodeEnum.FAMILY_INCOME_TOTAL_AMOUNT,
                PaySettleConfigCodeEnum.FAMILY_HALL_INCOME_TOTAL_AMOUNT,
                PaySettleConfigCodeEnum.FAMILY_INDIVIDUAL_INCOME_TOTAL_AMOUNT,
                PaySettleConfigCodeEnum.FAMILY_NOBILITY_HALL_ROYALTY_INCOME_TOTAL_AMOUNT,
                PaySettleConfigCodeEnum.FAMILY_NOBILITY_PERSONAL_ROYALTY_INCOME_TOTAL_AMOUNT,
                PaySettleConfigCodeEnum.FAMILY_OFFICIAL_HALL_INCOME_TOTAL_AMOUNT
                );
        //批量查询
        IncomeSummaryThreadBean summaryThreadBean = paymentManager.asyncBatchQueryTrade(familyId, roomIds, periodType, configList);
        return new IncomeSummaryBean()
                .setSum(Optional.ofNullable(summaryThreadBean.getSum()).orElse(0L))
                .setRoomGift(Optional.ofNullable(summaryThreadBean.getRoomGift()).orElse(0L))
                .setPlayer(Optional.ofNullable(summaryThreadBean.getPlayer()).orElse(0L))
                .setRoomVip(Optional.ofNullable(summaryThreadBean.getRoomVip()).orElse(0L))
                .setPlayerVip(Optional.ofNullable(summaryThreadBean.getPlayerVip()).orElse(0L))
                .setOfficialRoom(Optional.ofNullable(summaryThreadBean.getOfficialRoom()).orElse(0L))
                ;
    }

    @Override
    public IncomeSummaryBean queryTradeValueByRoom(Long familyId, Long roomId, PeriodTypeEnum periodType) {
        List<PaySettleConfigCodeEnum> configList = Lists.newArrayList(
                PaySettleConfigCodeEnum.HALL_INCOME_TOTAL_AMOUNT,
                PaySettleConfigCodeEnum.HALL_HALL_INCOME_TOTAL_AMOUNT,
                PaySettleConfigCodeEnum.HALL_INDIVIDUAL_INCOME_TOTAL_AMOUNT,
                PaySettleConfigCodeEnum.HALL_NOBILITY_HALL_ROYALTY_INCOME_TOTAL_AMOUNT,
                PaySettleConfigCodeEnum.HALL_NOBILITY_PERSONAL_ROYALTY_INCOME_TOTAL_AMOUNT,
                PaySettleConfigCodeEnum.HALL_OFFICIAL_HALL_INCOME_TOTAL_AMOUNT
        );
        //批量查询
        IncomeSummaryThreadBean summaryThreadBean = paymentManager.asyncBatchQueryTrade(familyId, roomId, periodType, configList);
        return new IncomeSummaryBean()
                .setSum(Optional.ofNullable(summaryThreadBean.getSum()).orElse(0L))
                .setRoomGift(Optional.ofNullable(summaryThreadBean.getRoomGift()).orElse(0L))
                .setPlayer(Optional.ofNullable(summaryThreadBean.getPlayer()).orElse(0L))
                .setRoomVip(Optional.ofNullable(summaryThreadBean.getRoomVip()).orElse(0L))
                .setPlayerVip(Optional.ofNullable(summaryThreadBean.getPlayerVip()).orElse(0L))
                .setOfficialRoom(Optional.ofNullable(summaryThreadBean.getOfficialRoom()).orElse(0L))
                ;
    }

    @Override
    public int getPlayerPayCountForFamily(PlayerPayCountParamDto paramDto, Date startDate, Date endDate) {
        String startDateStr = DateUtil.formatDateToString(startDate, DateUtil.date_2);
        String endDateStr = DateUtil.formatDateToString(endDate, DateUtil.date_2);

        //根据签约的主播去重
        Integer count = playerSignCharmStatMapper.getPlayerPayCountForFamily(paramDto, startDateStr, endDateStr);
        return count == null ? 0 : count;
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }
}
