package fm.lizhi.ocean.wavecenter.infrastructure.resource.recommendcard.remote;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.BatchSendUserResultBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.RecommendCardRewardBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.RecommendCardSendRecordBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.RecommendCardUseRecordBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.request.*;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.response.RewardResultBean;
import fm.lizhi.ocean.wavecenter.base.remote.IRemote;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto.GetUseRecordParamDTO;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto.RecommendAllocationRecordDTO;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto.RecommendCardUseRecordDTO;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto.UserRecommendCardStockDTO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/21 15:23
 */
public interface RecommendCardRemote extends IRemote {

    /**
     * 查询用户库存
     * @param userIds
     * @return
     */
    List<UserRecommendCardStockDTO> getUserStock(List<Long> userIds);

    /**
     * 查询过期数
     * @param userId
     * @param startDate
     * @param endDate
     * @return
     */
    Integer getExpireNum(Long userId, Date startDate, Date endDate);

    /**
     * 查询推荐卡使用记录
     * @param request
     * @return
     */
    PageBean<RecommendCardUseRecordBean> getUseRecordForManagement(RequestGetUseRecord request);

    /**
     * 奖励推荐卡
     * @param operatorUserId
     * @param rewardBeans
     * @return
     */
    RewardResultBean rewardRecommendCard(Long operatorUserId, List<RecommendCardRewardBean> rewardBeans);

    int REWARD_RECOMMEND_CARD_SUCCESS = 0;
    int REWARD_RECOMMEND_CARD_ERROR = 1;
    int REWARD_RECOMMEND_CARD_NOT_SUCCESS = 2;
    int REWARD_RECOMMEND_CARD_NOT_ENOUGH = 3;
    /**
     * 查询推荐卡发放记录
     * @param request
     * @return
     */
    PageBean<RecommendCardSendRecordBean> getSendRecord(RequestGetSendRecord request);

    /**
     * 批量发放推荐
     * @param request
     */
    List<BatchSendUserResultBean> batchSend(RequestBatchSend request);

    /**
     * 回收推荐卡
     * @param request
     */
    boolean recycle(RequestRecycle request);

    /**
     * 查询推荐卡分配记录
     * @param request
     * @return
     */
    PageBean<RecommendAllocationRecordDTO> getAllocationRecord(RequestGetAllocationRecord request);

    /**
     * 查询推荐卡使用记录
     * @param paramDTO
     * @return
     */
    PageBean<RecommendCardUseRecordDTO> getUseRecord(GetUseRecordParamDTO paramDTO);

}
