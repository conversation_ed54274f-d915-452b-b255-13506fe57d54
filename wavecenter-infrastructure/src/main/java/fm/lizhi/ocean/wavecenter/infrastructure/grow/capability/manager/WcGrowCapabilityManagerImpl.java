package fm.lizhi.ocean.wavecenter.infrastructure.grow.capability.manager;

import fm.lizhi.ocean.wavecenter.datastore.grow.capability.entity.WcGrowCapability;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.capability.convert.WcGrowCapabilityConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.capability.datastore.dao.WcGrowCapabilityDao;
import fm.lizhi.ocean.wavecenter.service.grow.capability.manager.WcGrowCapabilityManager;
import fm.lizhi.ocean.wavecenter.service.grow.dto.WcGrowCapabilityDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 能力项Manager实现
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@Component
public class WcGrowCapabilityManagerImpl implements WcGrowCapabilityManager {
    @Autowired
    private WcGrowCapabilityDao growCapabilityDao;

    /**
     * 查询全部能力项（按appId和环境）
     *
     * @param appId 业务ID
     * @return 能力项列表
     */
    @Override
    public List<WcGrowCapabilityDTO> queryAll(Integer appId) {
        List<WcGrowCapability> entities = growCapabilityDao.selectAll(appId);
        return entities.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    /**
     * 新增能力项，参数校验
     *
     * @param code     能力项code
     * @param name     能力项名称
     * @param operator 操作人
     * @param appId    业务ID
     */
    @Override
    public boolean addCapability(String code, String name, String operator, Integer appId) {
        if (StringUtils.isEmpty(code) || StringUtils.isEmpty(name) || StringUtils.isEmpty(operator) || appId == null) {
            return false;
        }
        WcGrowCapability entity = WcGrowCapability.builder()
                .capabilityCode(code)
                .name(name)
                .createUser(operator)
                .modifyUser(operator)
                .appId(appId)
                .build();
        return growCapabilityDao.insert(entity) > 0;
    }

    @Override
    public boolean updateCapability(Long id, String code, String name, String operator, Integer appId) {
        WcGrowCapability entity = growCapabilityDao.getById(id);
        if (entity != null) {
            if (StringUtils.isNotBlank(code)) {
                entity.setCapabilityCode(code);
            }
            if (StringUtils.isNotBlank(name)) {
                entity.setName(name);
            }
            entity.setModifyUser(operator);
            return growCapabilityDao.updateById(entity) > 0;
        }
        return false;
    }

    /**
     * 实体转换为DTO
     *
     * @param entity 实体对象
     * @return DTO对象
     */
    private WcGrowCapabilityDTO convertToDTO(WcGrowCapability entity) {
        return WcGrowCapabilityConvert.INSTANCE.entityToDto(entity);
    }
} 