package fm.lizhi.ocean.wavecenter.infrastructure.live.manager;

import fm.lizhi.ocean.wavecenter.api.live.bean.GuildRoomDayStats;
import fm.lizhi.ocean.wavecenter.api.live.bean.GuildRoomDayStatsSummaryRes;
import fm.lizhi.ocean.wavecenter.api.live.bean.RoomDayStatsSummaryRes;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.RoomDayCalendarEntity;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.RoomDayStatsDetailEntity;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.RoomDayStatsEntity;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcRoomCheckInDayStats;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.mapper.WaveRoomCheckInRecordMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.po.RoomCheckPlayerPo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 根据小时数据统计日数据
 * <AUTHOR>
 * @date 2024/6/21 18:03
 */
@Component
@Deprecated
public class LatestCheckDayDataManager implements ICheckInDayRouter{

    @Autowired
    private WaveRoomCheckInRecordMapper checkInRecordMapper;

    @Override
    public List<Long> guildRoomDayAllNjId(Integer appId, Long familyId, Long njId, Date startDate, Date endDate) {
        return checkInRecordMapper.guildRoomDayAllNjId(appId, familyId, njId, startDate, endDate);
    }

    @Override
    public List<WcRoomCheckInDayStats> guildRoomDayDetail(Integer appId, Long familyId, Long njId, Date startDate, Date endDate) {
        return checkInRecordMapper.guildRoomDayDetail(appId, familyId, njId, startDate, endDate);
    }

    @Override
    public List<RoomCheckPlayerPo> guildRoomDayDetailCheckPlayer(Integer appId, Long familyId, Long njId, Date startDate, Date endDate) {
        return checkInRecordMapper.guildRoomDayDetailCheckPlayer(appId, familyId, njId, startDate, endDate);
    }

    @Override
    public List<GuildRoomDayStats> guildRoomDayStats(Integer appId, Long familyId, Long njId, Date startDate, Date endDate) {
        return checkInRecordMapper.guildRoomDayStats(appId, familyId, njId, startDate, endDate);
    }

    @Override
    public GuildRoomDayStatsSummaryRes guildRoomDaySummary(Integer appId, Long familyId, Long njId, Date startDate, Date endDate) {
        return checkInRecordMapper.guildRoomDaySummary(appId, familyId, njId, startDate, endDate);
    }

    @Override
    public List<Long> roomDayStatsUserId(Integer appId, Long njId, Long userId, Date startDate, Date endDate) {
        return checkInRecordMapper.roomDayStatsUserId(appId, njId, userId, startDate, endDate);
    }

    @Override
    public List<RoomDayStatsDetailEntity> roomDayStatsDetail(Integer appId, Long njId, Long userId, Date startDate, Date endDate) {
        return checkInRecordMapper.roomDayStatsDetail(appId, njId, userId, startDate, endDate);
    }

    @Override
    public List<RoomDayStatsEntity> roomDayStats(Integer appId, Long njId, Long userId, Date startDate, Date endDate) {
        return checkInRecordMapper.roomDayStats(appId, njId, userId, startDate, endDate);
    }

    @Override
    public List<RoomDayCalendarEntity> roomCalendar(Integer appId, Long njId, Date startDate, Date endDate) {
        return checkInRecordMapper.roomCalendar(appId, njId, startDate, endDate);
    }

    @Override
    public RoomDayStatsSummaryRes roomDaySummary(Integer appId, Long njId, Date startDate, Date endDate) {
        return checkInRecordMapper.roomDaySummary(appId, njId, startDate, endDate);
    }
}
