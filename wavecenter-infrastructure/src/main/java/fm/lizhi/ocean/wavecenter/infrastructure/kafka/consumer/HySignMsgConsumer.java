package fm.lizhi.ocean.wavecenter.infrastructure.kafka.consumer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import fm.hy.family.bean.player.sign.AdminSignMsg;
import fm.hy.family.bean.player.sign.PlayerSignMsg;
import fm.hy.family.bean.player.sign.PlayerSignMsgEvent;
import fm.hy.family.bean.player.sign.UserType;
import fm.hy.family.constants.WaveSignConstant;
import fm.lizhi.common.kafka.common.strategy.AutoOffsetReset;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaHandler;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaListener;
import fm.lizhi.common.kafka.ioc.api.strategy.annotation.AutoOffsetResetStrategy;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.message.constant.MessageTargetLinkEnum;
import fm.lizhi.ocean.wavecenter.api.message.constant.MessageTypeEnum;
import fm.lizhi.ocean.wavecenter.api.message.request.RequestSendMessage;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.common.utils.KafkaMsgUtils;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerInfoManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerVerifyApplyManager;
import fm.lizhi.ocean.wavecenter.service.message.manager.WaveCenterMessageManager;
import fm.lizhi.ocean.wavecenter.service.permissions.handler.RoleHandler;
import fm.lizhi.ocean.wavecenter.service.sign.dto.FamilyNjSignRecordDTO;
import fm.lizhi.ocean.wavecenter.service.sign.dto.QueryFamilyNjSignRecordDTO;
import fm.lizhi.ocean.wavecenter.service.sign.manager.ContractManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/10/22 17:35
 */
@Slf4j
@Component
@KafkaListener(clusterNamespace = "hy-kafka250-bootstrap-server")
public class HySignMsgConsumer {

    @Autowired
    private WaveCenterMessageManager waveCenterMessageManager;
    @Autowired
    private UserManager userManager;
    @Autowired
    private ContractManager contractManager;
    @Autowired
    private RoleHandler roleHandler;
    @Autowired
    private FamilyManager familyManager;
    @Autowired
    private SingerInfoManager singerInfoManager;

    @Autowired
    private SingerVerifyApplyManager singerVerifyApplyManager;

    private static final Map<String, String> ADMIN_SIGN_MSG_CONTENT = MapUtil.<String, String>builder()
            .put(WaveSignConstant.ADMIN_RECEIVE_APPLY.getCode(), "您收到了一条房间管理员加入申请，请及时处理")
            .put(WaveSignConstant.ADMIN_PASS.getCode(), "您的申请已通过，您已成为#familyName家族的房间管理员")
            .put(WaveSignConstant.FAMILY_PASS.getCode(), "您的邀请已被接受，#njName已成为您的家族管理员")
            .put(WaveSignConstant.CANCEL_SIGN_BY_FAMILY.getCode(), "您收到了一条家族长解约申请，请及时处理")
            .put(WaveSignConstant.CANCEL_SIGN.getCode(), "您收到了一条解约申请，请及时处理")
            .put(WaveSignConstant.EXIT_FAMILY.getCode(), "您的家族绑定已解除，目前您已退出#familyName家族")
            .build();

    /**
     * 陪玩签约消息消费
     * @param body
     */
    @KafkaHandler(topic = "hy_topic_player_sign_msg", group = "hy_topic_player_sign_msg_wave_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handlePlayerSignMsg(String body){
        String msg = null;

        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            log.info("hy.handlePlayerSignMsg msg={}", msg);

            PlayerSignMsg playerSignMsg = JsonUtil.loads(msg, PlayerSignMsg.class);
            ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.HEI_YE);

            if (PlayerSignMsgEvent.REVIEW_CANCEL_REJECT.getCode().equals(playerSignMsg.getMsgEvent())) {
                String njName = getUserName(playerSignMsg.getNjId());
                String playerName = getUserName(playerSignMsg.getPlayerId());

                RequestSendMessage requestNJ = new RequestSendMessage()
                        .setType(MessageTypeEnum.NOTIFY.getType())
                        .setAppId(BusinessEvnEnum.HEI_YE.getAppId())
                        .setTitle(genTitle(playerSignMsg))
                        .setContent(String.format("暂时无法与%s解除绑定，请与家族长联系", playerName))
                        .setTargetUserId(playerSignMsg.getNjId())
                        .setSendUserId(1L)
                        .setLinkType(MessageTargetLinkEnum.SIGN_MANAGEMENT.name())
                        .setBizId(playerSignMsg.getPlayerSignId());
                waveCenterMessageManager.sendMessage(requestNJ);

                RequestSendMessage request = new RequestSendMessage()
                        .setType(MessageTypeEnum.NOTIFY.getType())
                        .setAppId(BusinessEvnEnum.HEI_YE.getAppId())
                        .setTitle(genTitle(playerSignMsg))
                        .setContent(String.format("暂时无法与%s解除绑定，请与家族长联系", njName))
                        .setTargetUserId(playerSignMsg.getPlayerId())
                        .setSendUserId(1L)
                        .setLinkType(MessageTargetLinkEnum.SIGN_MANAGEMENT.name())
                        .setBizId(playerSignMsg.getPlayerSignId());
                waveCenterMessageManager.sendMessage(request);

                return;
            }

            Pair<String, Long> contentAndTarget = genContentAndTarget(playerSignMsg);

            RequestSendMessage request = new RequestSendMessage()
                    .setType(MessageTypeEnum.NOTIFY.getType())
                    .setAppId(BusinessEvnEnum.HEI_YE.getAppId())
                    .setTitle(genTitle(playerSignMsg))
                    .setContent(contentAndTarget.getKey())
                    .setTargetUserId(contentAndTarget.getValue())
                    .setSendUserId(1L)
                    .setLinkType(MessageTargetLinkEnum.SIGN_MANAGEMENT.name())
                    .setBizId(playerSignMsg.getPlayerSignId());
            waveCenterMessageManager.sendMessage(request);

            //解约成功后置处理
            if (PlayerSignMsgEvent.DO_CANCEL.getCode().equals(playerSignMsg.getMsgEvent())) {
                //解除授权数据
                UserInFamilyBean userInFamily = familyManager.getUserInFamily(playerSignMsg.getNjId());
                Long familyId = userInFamily.getFamilyId();
                roleHandler.removePlayerWithFamilyAuth(familyId, playerSignMsg.getPlayerId());
                // 添加歌手淘汰标记
                singerInfoManager.addEliminateSingerTag(BusinessEvnEnum.HEI_YE.getAppId(), playerSignMsg.getPlayerId());
                singerVerifyApplyManager.updateSingerSignInfo(BusinessEvnEnum.HEI_YE.getAppId(), playerSignMsg.getPlayerId());
            } else if (PlayerSignMsgEvent.DO_SIGN.getCode().equals(playerSignMsg.getMsgEvent())) {
                singerVerifyApplyManager.updateSingerSignInfo(BusinessEvnEnum.HEI_YE.getAppId(), playerSignMsg.getPlayerId());
            }

        } catch (Exception e) {
            log.error("handlePlayerSignMsg error, msg:{}, orgMsg:{}", msg, body, e);
        }
    }

    private ContractTypeEnum genContractType(PlayerSignMsg playerSignMsg){
        String msgEvent = playerSignMsg.getMsgEvent();
        if (PlayerSignMsgEvent.DO_SIGN.getCode().equals(msgEvent)
                || PlayerSignMsgEvent.INVITE_SIGN.getCode().equals(msgEvent)) {
            return ContractTypeEnum.SIGN;
        }
        else {
            return ContractTypeEnum.CANCEL;
        }
    }

    private String genTitle(PlayerSignMsg playerSignMsg){
        ContractTypeEnum contractTypeEnum = genContractType(playerSignMsg);
        if (contractTypeEnum == ContractTypeEnum.SIGN) {
            return "签约申请";
        }
        else {
            return "解约申请";
        }
    }

    private Pair<String, Long> genContentAndTarget(PlayerSignMsg playerSignMsg){
        String msgEvent = playerSignMsg.getMsgEvent();
        RoleEnum userType = genUserType(playerSignMsg);

        if (PlayerSignMsgEvent.INVITE_SIGN.getCode().equals(msgEvent)) {
            if (userType == RoleEnum.ROOM) {
                return Pair.of("您收到了一条加入房间邀请，请及时处理", playerSignMsg.getPlayerId());
            } else {
                return Pair.of("您收到了一条加入房间申请，请及时处理", playerSignMsg.getNjId());
            }
        }

        if (PlayerSignMsgEvent.DO_SIGN.getCode().equals(msgEvent)) {
            if (userType == RoleEnum.ROOM) {
                String njName = getUserName(playerSignMsg.getNjId());
                return Pair.of(String.format("您已成功加入家族，您的管理员是%s", njName), playerSignMsg.getPlayerId());
            } else {
                String playerName = getUserName(playerSignMsg.getPlayerId());
                return Pair.of(String.format("您已成功邀请%s加入了家族", playerName), playerSignMsg.getNjId());
            }
        }

        if (PlayerSignMsgEvent.INVITE_CANCEL.getCode().equals(msgEvent)) {
            if (userType == RoleEnum.ROOM) {
                return Pair.of("您收到了一条解约申请，请及时处理", playerSignMsg.getPlayerId());
            } else {
                return Pair.of("您收到了一条成员解约申请，请及时处理", playerSignMsg.getNjId());
            }
        }

        if (PlayerSignMsgEvent.DO_CANCEL.getCode().equals(msgEvent)) {
            if (userType == RoleEnum.ROOM) {
                return Pair.of("您已成功退出家族", playerSignMsg.getPlayerId());
            } else {
                String name = getUserName(playerSignMsg.getPlayerId());
                return Pair.of(String.format("%s已退出家族", name), playerSignMsg.getNjId());
            }
        }

        if (PlayerSignMsgEvent.DO_CANCEL_CONFIRM.getCode().equals(msgEvent)) {
            if (userType == RoleEnum.ROOM) {
                return Pair.of("您的解约申请管理员已同意，待家族长审批完成后，将会完成解约", playerSignMsg.getPlayerId());
            } else {
                return Pair.of("您的解约申请成员已同意，待家族长完成审批后，将会完成解约", playerSignMsg.getNjId());
            }
        }

        if (PlayerSignMsgEvent.WITHDRAW_CANCEL.getCode().equals(msgEvent)) {
            if (userType == RoleEnum.ROOM) {
                String njName = getUserName(playerSignMsg.getNjId());
                return Pair.of(String.format("%s已撤销解除签约流程", njName), playerSignMsg.getPlayerId());
            } else {
                String playerName = getUserName(playerSignMsg.getPlayerId());
                return Pair.of(String.format("%s已撤销解除成员签约流程", playerName), playerSignMsg.getNjId());
            }
        }

        return Pair.of("", 0L);
    }

    private String getUserName(Long userId){
        List<SimpleUserDto> userList = userManager.getSimpleUserByIds(Lists.newArrayList(userId));
        if (CollectionUtils.isEmpty(userList)) {
            return "";
        }
        return userList.get(0).getName();
    }

    private RoleEnum genUserType(PlayerSignMsg playerSignMsg){
        String userType = playerSignMsg.getUserType();
        if (UserType.PLAYER.name().equals(userType)) {
            return RoleEnum.PLAYER;
        }
        if (UserType.FAMILY.name().equals(userType)) {
            return RoleEnum.FAMILY;
        }
        if (UserType.ADMIN.name().equals(userType)) {
            return RoleEnum.ROOM;
        }
        return RoleEnum.USER;
    }

    /**
     * 管理员签约消息消费
     * @param body
     */
    @KafkaHandler(topic = "hy_topic_admin_sign_msg", group = "hy_topic_admin_sign_msg_wave_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleAdminSignMsg(String body){
        String msg = null;

        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            log.info("hy.handleAdminSignMsg msg={}", msg);

            AdminSignMsg adminSignMsg = JsonUtil.loads(msg, AdminSignMsg.class);
            ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.HEI_YE);

            waveCenterMessageManager.sendMessage(new RequestSendMessage()
                    .setTargetUserId(adminSignMsg.getTargetUserId())
                    .setBizId(adminSignMsg.getSignId())
                    .setTitle(genAdminSignMessageTitle(adminSignMsg))
                    .setContent(genAdminSignMessageContent(adminSignMsg))
                    .setAppId(BusinessEvnEnum.HEI_YE.getAppId())
                    .setType(MessageTypeEnum.NOTIFY.getType())
                    .setSendUserId(1L)
                    .setLinkType(MessageTargetLinkEnum.SIGN_MANAGEMENT.name())
            );

            //解约成功后置处理
            if (WaveSignConstant.EXIT_FAMILY.getCode().equals(adminSignMsg.getMsgType())) {
                //解除授权
                PageBean<FamilyNjSignRecordDTO> signRecord = contractManager.querySignRecord(QueryFamilyNjSignRecordDTO.builder()
                        .id(adminSignMsg.getSignId())
                        .build());
                if (CollectionUtils.isNotEmpty(signRecord.getList())) {
                    FamilyNjSignRecordDTO recordDTO = signRecord.getList().get(0);
                    Optional<FamilyBean> userFamily = familyManager.getUserFamily(recordDTO.getFamilyUserId());
                    userFamily.ifPresent(familyBean -> roleHandler.removeRoomWithFamilyAuth(familyBean.getId(), recordDTO.getNjId()));
                }
            }

        } catch (Exception e) {
            log.error("handleAdminSignMsg error, msg:{}, orgMsg:{}", msg, body, e);
        }
    }



    private String genAdminSignMessageTitle(AdminSignMsg adminSignMsg){

        String msgType = adminSignMsg.getMsgType();

        if (WaveSignConstant.ADMIN_RECEIVE_APPLY.getCode().equals(msgType)
                || WaveSignConstant.ADMIN_PASS.getCode().equals(msgType)
                || WaveSignConstant.FAMILY_PASS.getCode().equals(msgType)
                || WaveSignConstant.ADMIN_NOT_PASS.getCode().equals(msgType)
        ){
            return "签约通知";
        }

        if (WaveSignConstant.CANCEL_SIGN_BY_FAMILY.getCode().equals(msgType)
                || WaveSignConstant.CANCEL_SIGN.getCode().equals(msgType)
                || WaveSignConstant.EXIT_FAMILY.getCode().equals(msgType)
        ){
            return "解约通知";
        }

        return "";
    }


    private String genAdminSignMessageContent(AdminSignMsg adminSignMsg){

        String msgContent = ADMIN_SIGN_MSG_CONTENT.get(adminSignMsg.getMsgType());
        if (StrUtil.isBlank(msgContent)){
            return "";
        }

        Map<String, String> msgParams = adminSignMsg.getMsgParams();
        if (CollUtil.isEmpty(msgParams)){
            return msgContent;
        }

        for (String param : msgParams.keySet()) {
            msgContent = msgContent.replace("#"+param, msgParams.get(param));
        }

        return msgContent;
    }

}
