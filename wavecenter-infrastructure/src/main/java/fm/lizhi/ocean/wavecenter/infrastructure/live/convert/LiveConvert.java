package fm.lizhi.ocean.wavecenter.infrastructure.live.convert;


import fm.lizhi.ocean.wavecenter.infrastructure.live.request.GetRoomInfoByNjIdResponse;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.GetRoomInfoByNjIdDTO;
import fm.lizhi.ocean.wavecenter.service.live.dto.EditRoomNoticeDTO;
import fm.lizhi.ocean.wavecenter.service.live.dto.GetRoomNoticeDTO;
import fm.lizhi.ocean.wavecenter.service.live.dto.GetRoomNoticeParamDTO;
import fm.lizhi.ocean.wavecenter.infrastructure.live.request.EditRoomNoticeRequest;
import fm.lizhi.ocean.wavecenter.infrastructure.live.request.GetRoomNoticeRequest;
import fm.lizhi.ocean.wavecenter.infrastructure.live.request.GetRoomNoticeResponse;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface LiveConvert {

    LiveConvert I = Mappers.getMapper(LiveConvert.class);

    GetRoomNoticeRequest buildGetRoomNoticeRequest(GetRoomNoticeParamDTO dto);

    EditRoomNoticeRequest buildEditRoomNoticeRequest(EditRoomNoticeDTO dto);

    GetRoomNoticeDTO convertRoomDayStats(GetRoomNoticeResponse response);

    GetRoomInfoByNjIdDTO convertRoomInfo(GetRoomInfoByNjIdResponse response);

}
