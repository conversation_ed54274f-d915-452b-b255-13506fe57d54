package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.manager;

import fm.lizhi.common.datastore.redis.client.RedisClient;
import fm.lizhi.ocean.wavecenter.base.constants.TimeConstant;
import fm.lizhi.ocean.wavecenter.common.utils.RedisLock;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.constants.ActivityRedisKey;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityRedisManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class ActivityRedisManagerImpl implements ActivityRedisManager {

    @Resource(name = "redis_ocean_wavecenter")
    private RedisClient redisClient;

    @Override
    public RedisLock getApplyLock(Integer appId, Long userId) {
        int timeout = TimeConstant.ONE_MINUTE * 1000;
        return new RedisLock(redisClient, ActivityRedisKey.APPLY_LOCK_STR.getKey(appId, userId), timeout / 3, timeout);
    }

    @Override
    public RedisLock getResourceGiveLock(Integer appId, Long resourceId) {
        int timeout = TimeConstant.ONE_MINUTE * 1000;
        return new RedisLock(redisClient, getResourceGiveLockKey(appId, resourceId), timeout / 3, timeout);
    }

    @Override
    public RedisLock getActivityOperateLock(Integer appId, Long activityId) {
        int timeout = TimeConstant.ONE_MINUTE * 1000;
        return new RedisLock(redisClient, getActivityAgreeLockKey(appId, activityId), timeout / 3, timeout);
    }

    private String getResourceGiveLockKey(Integer appId, Long resourceId) {
        return ActivityRedisKey.RESOURCE_GIVE_LOCK_STR.getKey(appId, resourceId);
    }

    private String getActivityAgreeLockKey(Integer appId, Long activityId) {
        return ActivityRedisKey.ACTIVITY_AGREE_LOCK_STR.getKey(appId, activityId);
    }

    @Override
    public RedisLock getModifyLock(Integer appId, Long activityId) {
        int timeout = TimeConstant.ONE_MINUTE * 1000;
        return new RedisLock(redisClient, getModifyLockKey(appId, activityId), timeout / 3, timeout);
    }

    private String getModifyLockKey(Integer appId, Long activityId) {
        return ActivityRedisKey.ACTIVITY_MODIFY_LOCK_STR.getKey(appId, activityId);
    }
}
