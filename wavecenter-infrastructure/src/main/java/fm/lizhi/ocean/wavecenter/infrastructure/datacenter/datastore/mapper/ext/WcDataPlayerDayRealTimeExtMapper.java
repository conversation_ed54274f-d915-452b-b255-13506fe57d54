package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataPlayerDayRealTime;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcDataPlayerDayRealTimeExtMapper {

    @Select("<script>\n" +
            "  SELECT * FROM\n" +
            "    wavecenter_data_player_day_real_time\n" +
            "  WHERE\n" +
            "    player_id IN\n" +
            "      <foreach collection=\"playerIds\" item=\"playerId\" open=\"(\" separator=\",\" close=\")\">\n" +
            "        #{playerId}\n" +
            "      </foreach>\n" +
            "    AND app_id = #{appId} " +
            "   <if test = 'start == end '>" +
            "       AND stat_date_value = #{start} " +
            "   </if>" +
            "   <if test = 'start != end'>" +
            "       AND stat_date_value BETWEEN #{start} AND #{end}" +
            "   </if>" +
            "</script>")
    List<WcDataPlayerDayRealTime> getWcDataPlayerDayRealTimes(@Param("appId") Integer appId, @Param("playerIds") List<Long> playerIds,
                                                                @Param("start") Integer start, @Param("end") Integer end);



}
