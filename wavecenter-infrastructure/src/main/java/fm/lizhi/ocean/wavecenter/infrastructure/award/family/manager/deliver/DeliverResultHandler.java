package fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager.deliver;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardDeliverExecutionStatusEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.dao.FamilyAwardDeliverRecordDao;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilyAwardDeliverExecution;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 发放结果处理器. 用于实现发放结果的保存或者通知等操作
 */
@Component
@Slf4j
public class DeliverResultHandler {

    @Autowired
    private FamilyAwardDeliverRecordDao familyAwardDeliverRecordDao;

    /**
     * 处理发放结果
     *
     * @param recordId    发放记录id
     * @param executionId 发放执行id
     * @param counter     发放执行计数器
     * @param param       发放资源参数
     * @param result      发放结果
     */
    public void handleDeliverResult(long recordId, long executionId, DeliverExecutionCounter counter,
                                    DeliverResourceParam param, Result<Void> result) {
        int rCode = result.rCode();
        String message = result.getMessage();
        log.info("handle deliver result, param={}, rCode={}, message={}", param, rCode, message);
        if (RpcResult.isSuccess(result)) {
            // 更新执行状态为成功
            familyAwardDeliverRecordDao.updateDeliverExecutionToSuccess(executionId);
            boolean allFinish = counter.incrementSuccessAndReturnIsAllFinish();
            if (allFinish) {
                if (counter.hasFailure()) {
                    // 如果全部执行完成, 且有失败的, 则更新发放记录为部分失败
                    familyAwardDeliverRecordDao.updateDeliverRecordToPartialFailure(recordId);
                } else {
                    // 如果全部执行完成, 且没有失败的, 则更新发放记录为成功
                    familyAwardDeliverRecordDao.updateDeliverRecordToSuccess(recordId);
                }
            }
        } else {
            // 更新执行状态为失败
            familyAwardDeliverRecordDao.updateDeliverExecutionToFailure(executionId, rCode, message);
            boolean allFinish = counter.incrementFailureAndReturnIsAllFinish();
            if (allFinish) {
                if (counter.hasSuccess()) {
                    // 如果全部执行完成, 且有成功的, 则更新发放记录为部分失败
                    familyAwardDeliverRecordDao.updateDeliverRecordToPartialFailure(recordId);
                } else {
                    // 如果全部执行完成, 且没有成功的, 则更新发放记录为失败
                    familyAwardDeliverRecordDao.updateDeliverRecordToFailure(recordId);
                }
            }
        }
        log.info("handle deliver result finish, param={}, rCode={}, message={}", param, rCode, message);
    }

    /**
     * 处理重新发放结果
     *
     * @param recordId    发放记录id
     * @param executionId 发放执行id
     * @param param       发放资源参数
     * @param result      发放结果
     */
    public void handleReDeliverResult(long recordId, long executionId, DeliverResourceParam param, Result<Void> result) {
        int rCode = result.rCode();
        String message = result.getMessage();
        log.info("handle reDeliver result, param={}, rCode={}, message={}", param, rCode, message);
        if (RpcResult.isSuccess(result)) {
            // 更新执行状态为成功
            familyAwardDeliverRecordDao.updateReDeliverExecutionToSuccess(executionId);
            List<WcFamilyAwardDeliverExecution> executions = familyAwardDeliverRecordDao.getDeliverExecutionsByRecordIdFromMaster(recordId);
            if (containsDelivering(executions)) {
                // 如果包含执行中的, 则更新为发放中
                familyAwardDeliverRecordDao.updateReDeliverRecordToDelivering(recordId);
            } else {
                if (containsDeliverFail(executions)) {
                    // 如果全部执行完成, 且有失败的, 则更新发放记录为部分失败
                    familyAwardDeliverRecordDao.updateReDeliverRecordToPartialFailure(recordId);
                } else {
                    // 如果全部执行完成, 且没有失败的, 则更新发放记录为成功
                    familyAwardDeliverRecordDao.updateReDeliverRecordToSuccess(recordId);
                }
            }
        } else {
            // 更新执行状态为失败
            familyAwardDeliverRecordDao.updateReDeliverExecutionToFailure(executionId, rCode, message);
            List<WcFamilyAwardDeliverExecution> executions = familyAwardDeliverRecordDao.getDeliverExecutionsByRecordIdFromMaster(recordId);
            if (containsDelivering(executions)) {
                // 如果包含执行中的, 则更新为发放中
                familyAwardDeliverRecordDao.updateReDeliverRecordToDelivering(recordId);
            } else {
                if (containsDeliverSuccess(executions)) {
                    // 如果全部执行完成, 且有成功的, 则更新发放记录为部分失败
                    familyAwardDeliverRecordDao.updateReDeliverRecordToPartialFailure(recordId);
                } else {
                    // 如果全部执行完成, 且没有成功的, 则更新发放记录为失败
                    familyAwardDeliverRecordDao.updateReDeliverRecordToFailure(recordId);
                }
            }
        }
    }

    private boolean containsDelivering(List<WcFamilyAwardDeliverExecution> executions) {
        for (WcFamilyAwardDeliverExecution execution : executions) {
            if (Objects.equals(execution.getStatus(), FamilyAwardDeliverExecutionStatusEnum.DELIVERING.getValue())) {
                return true;
            }
        }
        return false;
    }

    private boolean containsDeliverSuccess(List<WcFamilyAwardDeliverExecution> executions) {
        for (WcFamilyAwardDeliverExecution execution : executions) {
            if (Objects.equals(execution.getStatus(), FamilyAwardDeliverExecutionStatusEnum.DELIVER_SUCCESS.getValue())) {
                return true;
            }
        }
        return false;
    }

    private boolean containsDeliverFail(List<WcFamilyAwardDeliverExecution> executions) {
        for (WcFamilyAwardDeliverExecution execution : executions) {
            if (Objects.equals(execution.getStatus(), FamilyAwardDeliverExecutionStatusEnum.DELIVER_FAIL.getValue())) {
                return true;
            }
        }
        return false;
    }
}
