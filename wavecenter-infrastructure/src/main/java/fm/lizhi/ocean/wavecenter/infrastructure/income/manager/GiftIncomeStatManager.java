package fm.lizhi.ocean.wavecenter.infrastructure.income.manager;

import fm.lizhi.common.datastore.redis.client.RedisClient;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.kafka.message.GiftMsg;
import fm.lizhi.ocean.wavecenter.infrastructure.user.constants.IncomeRedisKey;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.pp.util.utils.EnvUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Optional;

@Slf4j
@Component
public class GiftIncomeStatManager {

    @Autowired
    private WcPlayerSignCharmStatManager wcPlayerSignCharmStatManager;

    @Resource(name = "redis_ocean_wavecenter")
    private RedisClient redisClient;

    @Autowired
    private FamilyManager familyManager;

    @Autowired
    private CommonConfig commonConfig;

    /**
     * 统计礼物魅力值
     *
     * @param giftMsg 礼物信息
     * @return 结果
     */
    public boolean statGiftCharm(GiftMsg giftMsg) {
        //先做幂等校验
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        //日期
        String date = DateUtil.formatDateToString(new Date(giftMsg.getCreateTime()), DateUtil.date_2);

        //已经消费过了就结束
        if (hasConsumeIdempotent(appId, giftMsg.getTransactionId())) {
            log.info("statGiftCharm hasConsumeIdempotent appId={}, transactionId={}", appId, giftMsg.getTransactionId());
            return true;
        }

        //是否初始化
        boolean hasInit = hasInitCharmStatRecord(appId, giftMsg.isOfficialRoom() ? giftMsg.getFamilyNjId() : giftMsg.getRecUserId(), giftMsg.getRecTargetUserId(), date);
        try {
            Optional<Long> family = familyManager.getRoomSignFamilyInDate(giftMsg.isOfficialRoom() ? giftMsg.getFamilyNjId() : giftMsg.getRecUserId(), new Date(giftMsg.getCreateTime()));
            return wcPlayerSignCharmStatManager.saveGiftIncome(hasInit, date, giftMsg, family.orElse(0L));
        } catch (Exception e) {
            log.error("saveGiftIncome error, giftMsg:{}", giftMsg, e);
        }
        return false;
    }


    /**
     * 是否已经消费过
     *
     * @param appId         应用ID
     * @param transactionId 事务ID
     * @return 结果
     */
    private boolean hasConsumeIdempotent(int appId, long transactionId) {
        if (EnvUtils.isPre()) {
            String key = IncomeRedisKey.GIFT_MSG_IDEMPOTENT_INCOME_STR.getKey(appId, transactionId, commonConfig.getConsumeIdempotentTag());
            String value = redisClient.get(key);
            return StringUtils.isNotBlank(value);
        }

        String key = IncomeRedisKey.GIFT_MSG_IDEMPOTENT_INCOME_STR.getKey(appId, transactionId);
        String value = redisClient.get(key);
        return StringUtils.isNotBlank(value);
    }

    /**
     * 是否已经初始化过魅力值统计信息记录
     *
     * @param appId 应用ID
     * @param njId  主播ID
     * @param date  日期
     * @return 结果： true-已经初始化过，false-未初始化过
     */
    private boolean hasInitCharmStatRecord(int appId, long njId, long userId, String date) {
        if (EnvUtils.isPre()) {
            String key = IncomeRedisKey.INIT_CHARM_STAT_HASH.getKey(appId, njId, date, commonConfig.getConsumeIdempotentTag());
            String value = redisClient.hget(key, String.valueOf(userId));
            return StringUtils.isNotBlank(value);
        }

        String key = IncomeRedisKey.INIT_CHARM_STAT_HASH.getKey(appId, njId, date);
        String value = redisClient.hget(key, String.valueOf(userId));
        return StringUtils.isNotBlank(value);
    }
}
