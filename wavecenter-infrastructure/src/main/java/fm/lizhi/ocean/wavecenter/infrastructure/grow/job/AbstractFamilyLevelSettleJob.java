package fm.lizhi.ocean.wavecenter.infrastructure.grow.job;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.Week;
import com.ctrip.framework.apollo.core.enums.Env;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.manager.FamilyLevelRecordManagerImpl;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.manager.GrowRedisManager;
import fm.lizhi.ocean.wavecenter.service.grow.config.GrowConfig;
import fm.lizhi.ocean.wavecenter.service.grow.handler.FamilyLevelSettleHandler;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/19 16:24
 */
@Slf4j
public abstract class AbstractFamilyLevelSettleJob {

    @Autowired
    protected FamilyLevelSettleHandler familyLevelSettleHandler;
    @Autowired
    protected GrowConfig growConfig;
    @Autowired
    protected FamilyManager familyManager;
    @Autowired
    protected GrowRedisManager growRedisManager;
    @Autowired
    protected FamilyLevelRecordManagerImpl familyLevelRecordManager;

    protected void doSettle(JobExecuteContext jobExecuteContext){
        String paramJsonStr = jobExecuteContext.getParam();
        log.info("paramJsonStr={}", paramJsonStr);

        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        log.info("appId={}", appId);

        if (StringUtils.isBlank(paramJsonStr)) {
            Date startTime = getStartTime();
            Date endTime = getEndTime();
            log.info("startTime={},endTime={}", startTime.getTime(), endTime.getTime());

            //全量结算
            if (ConfigUtils.getEnv() == Env.PRE) {
                //环境区分名单 预发环境只发放白名单中的用户 防止误发资源给线上用户
                List<Long> familyIds = growConfig.getBizConfig().getPreSettleFamilyIds();
                log.info("familyIds={}", familyIds);

                settle(familyIds, startTime, endTime);
            } else {
                // 线上空参数执行，必须保证当前是周一，否则收入数据结算会缺失
                Week week = DateUtil.thisDayOfWeekEnum();
                if (week != Week.MONDAY) {
                    log.info("not monday. week={}", week);
                    return;
                }
                doAllFamilySettle(appId, startTime, endTime);
            }
        } else {
            FamilyLevelSettleParam param = JsonUtil.loads(paramJsonStr, FamilyLevelSettleParam.class);
            Date startTime = getStartTime(param);
            Date endTime = getEndTime(param);
            log.info("startTime={},endTime={}", startTime.getTime(), endTime.getTime());

            if ("resetApp".equals(param.getCmd())) {
                growRedisManager.setLastSettleFamilyId(appId, startTime, endTime, 0L);
            }
            if ("all".equals(param.getCmd())) {
                doAllFamilySettle(appId, startTime, endTime);
            }

            //指定结算
            List<Long> familyIds = param.getFamilyIds();
            if (CollectionUtils.isNotEmpty(familyIds)) {
                settle(familyIds, startTime, endTime);
            }
            if (MapUtils.isNotEmpty(param.getFamilyLevelMap())) {
                for (Map.Entry<Long, Long> entry : param.getFamilyLevelMap().entrySet()) {
                    familyLevelSettleHandler.settleFamily(entry.getKey(), startTime, endTime, entry.getValue());
                }
            }
        }
    }

    private void doAllFamilySettle(Integer appId, Date startTime, Date endTime){
        //分页获取全部familyId
        Long lastFamilyId = growRedisManager.getLastSettleFamilyId(appId, startTime, endTime);
        log.info("start lastFamilyId={}", lastFamilyId);

        List<Long> pageFamilyIds = familyManager.getFamilyIdsByPage(lastFamilyId, 100);
        log.info("pageFamilyIds page=1.size={}", JsonUtil.dumps(pageFamilyIds));

        while (CollectionUtils.isNotEmpty(pageFamilyIds)) {
            //过滤掉结算过的familyId
            Set<Long> existFamily = familyLevelRecordManager.getWeekExitRecord(pageFamilyIds, appId, startTime);
            log.info("existFamily={}", JsonUtil.dumps(existFamily));

            List<Long> notSettleFamilyIds = pageFamilyIds.stream().filter(v -> !existFamily.contains(v)).collect(Collectors.toList());
            settle(notSettleFamilyIds, startTime, endTime);

            lastFamilyId = pageFamilyIds.get(pageFamilyIds.size() - 1);
            log.info("lastFamilyId={}", lastFamilyId);
            growRedisManager.setLastSettleFamilyId(appId, startTime, endTime, lastFamilyId);

            //下一页
            pageFamilyIds = familyManager.getFamilyIdsByPage(lastFamilyId, 100);
            log.info("pageFamilyIds={}", JsonUtil.dumps(pageFamilyIds));
        }
    }

    private void settle(List<Long> familyIds, Date startTime, Date endTime){
        familyLevelSettleHandler.settleFamilyByList(familyIds, startTime, endTime);
    }

    /**
     * 获取结算周期开始时间
     * @return
     */
    private Date getStartTime(){
        DateTime dateTime = DateUtil.offsetDay(new Date(), -7);
        return DateUtil.beginOfWeek(dateTime);
    }

    private Date getStartTime(FamilyLevelSettleParam param){
        if (param.getStartDay() == null) {
            return getStartTime();
        }

        return DateUtil.beginOfDay(MyDateUtil.getDayValueDate(param.getStartDay()));
    }

    /**
     * 获取结算周期结束时间
     * @return
     */
    private Date getEndTime(){
        DateTime dateTime = DateUtil.offsetDay(new Date(), -7);
        return DateUtil.endOfWeek(dateTime);
    }

    private Date getEndTime(FamilyLevelSettleParam param){
        if (param.getEndDay() == null) {
            return getEndTime();
        }

        return DateUtil.endOfDay(MyDateUtil.getDayValueDate(param.getEndDay()));
    }

}
