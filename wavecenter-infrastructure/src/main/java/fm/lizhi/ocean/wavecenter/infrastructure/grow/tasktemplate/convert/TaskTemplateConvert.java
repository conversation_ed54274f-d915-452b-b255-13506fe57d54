package fm.lizhi.ocean.wavecenter.infrastructure.grow.tasktemplate.convert;

import com.lizhi.commons.config.core.util.ConfigUtils;

import fm.lizhi.ocean.wavecenter.infrastructure.grow.tasktemplate.datastore.entity.WcGrowTaskTemplate;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.tasktemplate.datastore.entity.WcGrowTaskTemplateCapability;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto.TaskTemplateCapabilityDTO;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto.TaskTemplateConditionDTO;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto.TaskTemplateDTO;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto.TaskTemplateQueryItemDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 任务模版转换器
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR, imports = {ConfigUtils.class, Date.class})
public interface TaskTemplateConvert {

    TaskTemplateConvert I = Mappers.getMapper(TaskTemplateConvert.class);

    /**
     * 实体转DTO
     *
     * @param taskTemplate 任务模版实体
     * @return TaskTemplateDTO
     */
    @Mapping(target = "capabilityList", ignore = true)
    TaskTemplateDTO entityToDto(WcGrowTaskTemplate taskTemplate);

    /**
     * 能力实体转DTO
     *
     * @param capability 能力实体
     * @return TaskTemplateCapabilityDTO
     */
    @Mapping(target = "capabilityScore", source = "abilityValue")
    TaskTemplateCapabilityDTO capabilityEntityToDto(WcGrowTaskTemplateCapability capability);

    /**
     * 能力实体列表转DTO列表
     *
     * @param capabilities 能力实体列表
     * @return TaskTemplateCapabilityDTO列表
     */
    List<TaskTemplateCapabilityDTO> capabilityEntitiesToDtos(List<WcGrowTaskTemplateCapability> capabilities);

    /**
     * 将PageList<WcGrowTaskTemplateCapability>和Map<Long, WcGrowTaskTemplate>转换为List<TaskTemplateQueryItemDTO>
     *
     * @param capabilityList 能力项分页数据
     * @param templateMap    主表数据Map
     * @return 查询结果项列表
     */
    default List<TaskTemplateQueryItemDTO> toQueryItemDTOList(List<WcGrowTaskTemplateCapability> capabilityList, Map<Long, WcGrowTaskTemplate> templateMap) {
        List<TaskTemplateQueryItemDTO> result = new ArrayList<>();
        if (capabilityList == null || templateMap == null) {
            return result;
        }
        for (WcGrowTaskTemplateCapability cap : capabilityList) {
            WcGrowTaskTemplate template = templateMap.get(cap.getTemplateId());
            if (template == null) {
                continue;
            }
            TaskTemplateQueryItemDTO item = TaskTemplateQueryItemDTO.builder().id(String.valueOf(template.getId())).capabilityCode(cap.getCapabilityCode()).capabilityScore(cap.getAbilityValue() == null ? null : cap.getAbilityValue().intValue()).conditionJson(template.getConditionJson()).capabilityName(null).build();
            result.add(item);
        }
        return result;
    }

} 