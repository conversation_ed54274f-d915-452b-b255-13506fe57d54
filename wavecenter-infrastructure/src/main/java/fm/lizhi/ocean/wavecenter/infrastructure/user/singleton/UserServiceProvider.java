package fm.lizhi.ocean.wavecenter.infrastructure.user.singleton;

import fm.hy.family.api.FamilyService;
import fm.lizhi.account.security.api.AntiCheatService;
import fm.lizhi.accountcenter.api.AccountAuthService;
import fm.lizhi.common.dubbo.DubboClientBuilder;
import fm.lizhi.common.verify.api.UserVerifyService;
import fm.lizhi.hy.account.innermap.api.PpUserInnerMapService;
import fm.lizhi.hy.user.account.user.api.HyNewUserService;
import fm.lizhi.hy.user.account.user.api.HyWithdrawUserService;
import fm.lizhi.hy.usergroup.api.UserGroupService;
import fm.lizhi.ocean.lamp.common.generic.annotation.ScanBusinessProviderAPI;
import fm.lizhi.ocean.lamp.common.generic.annotation.ScanOceanStandardAPI;
import fm.lizhi.ocean.wave.user.export.api.service.UserService;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.ServiceProviderConstants;
import fm.lizhi.pp.security.api.ban.PpUserBanService;
import fm.lizhi.pp.security.api.sms.H5TokenService;
import fm.lizhi.pp.user.account.user.api.PpNewUserService;
import fm.lizhi.pp.user.account.user.api.PpWithdrawUserService;
import fm.lizhi.trade.contract.api.AuthService;
import fm.lizhi.trade.contract.api.BankCardService;
import fm.lizhi.trade.query.center.account.api.AccountQueryService;
import fm.lizhi.xm.user.account.user.api.XmNewUserService;
import fm.lizhi.xm.user.account.user.api.XmWithdrawUserService;
import fm.pp.family.api.ContractService;
import fm.pp.family.api.PlayerSignService;
import hy.fm.lizhi.live.pp.copywriting.api.PpCopyWritingService;
import hy.fm.lizhi.live.pp.push.api.PpDatePlayService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import pp.fm.lizhi.live.pp.player.api.PpPlayerAuthService;
import pp.fm.lizhi.live.pp.player.api.SingerAuthService;
import pp.fm.lizhi.live.pp.whitelist.api.PpWhiteListCategoryService;

/**
 * <AUTHOR>
 * @date 2024/4/10 14:51
 * UserVerifyService多个业务都有部署，使用泛化调用
 */
@ScanOceanStandardAPI(values = {
        @ScanOceanStandardAPI.RegisterProvider(interfaceClass = UserService.class)
})
@ScanBusinessProviderAPI(values = {
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = PpPlayerAuthService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = SingerAuthService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = xm.fm.lizhi.live.usergroup.api.UserGroupService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = PpCopyWritingService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = PpDatePlayService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = xm.fm.lizhi.live.pp.push.api.PpDatePlayService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = UserVerifyService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = PpWhiteListCategoryService.class),
})
@Configuration
public class UserServiceProvider {

    @Bean
    public UserGroupService hyUserGroupService() {
        return new DubboClientBuilder<>(UserGroupService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public AccountAuthService accountAuthService() {
        return new DubboClientBuilder<>(AccountAuthService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public ContractService ppContractService() {
        return new DubboClientBuilder<>(ContractService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public fm.hy.family.api.ContractService hyContractService() {
        return new DubboClientBuilder<>(fm.hy.family.api.ContractService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public fm.lizhi.xm.family.api.ContractService xmContractService() {
        return new DubboClientBuilder<>(fm.lizhi.xm.family.api.ContractService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public PpNewUserService ppNewUserService() {
        return new DubboClientBuilder<>(PpNewUserService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public fm.pp.family.api.FamilyService ppFamilyService() {
        return new DubboClientBuilder<>(fm.pp.family.api.FamilyService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public PlayerSignService ppPlayerSignService() {
        return new DubboClientBuilder<>(PlayerSignService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public fm.lizhi.pp.user.account.verify.api.UserVerifyProxyService ppUserVerifyProxyService() {
        return new DubboClientBuilder<>(fm.lizhi.pp.user.account.verify.api.UserVerifyProxyService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public PpUserBanService ppUserBanService() {
        return new DubboClientBuilder<>(PpUserBanService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public AntiCheatService antiCheatService() {
        return new DubboClientBuilder<>(AntiCheatService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public H5TokenService ppH5TokenService() {
        return new DubboClientBuilder<>(H5TokenService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public AuthService authService() {
        return new DubboClientBuilder<>(AuthService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public BankCardService bankCardService() {
        return new DubboClientBuilder<>(BankCardService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public PlayerSignService playerSignService() {
        return new DubboClientBuilder<>(PlayerSignService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public AccountQueryService accountQueryService() {
        return new DubboClientBuilder<>(AccountQueryService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public fm.lizhi.xm.user.account.tag.api.UserVerifyProxyService xmUserVerifyProxyService() {
        return new DubboClientBuilder<>(fm.lizhi.xm.user.account.tag.api.UserVerifyProxyService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public fm.lizhi.hy.user.account.verify.api.HYUserVerifyProxyService hyUserVerifyProxyService() {
        return new DubboClientBuilder<>(fm.lizhi.hy.user.account.verify.api.HYUserVerifyProxyService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }


    @Bean
    public HyNewUserService hyNewUserService() {
        return new DubboClientBuilder<>(HyNewUserService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }


    @Bean
    public XmNewUserService xmNewUserService() {
        return new DubboClientBuilder<>(XmNewUserService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }


    @Bean
    public FamilyService hyFamilyService() {
        return new DubboClientBuilder<>(FamilyService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public fm.lizhi.xm.family.api.FamilyService xmFamilyService() {
        return new DubboClientBuilder<>(fm.lizhi.xm.family.api.FamilyService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public fm.lizhi.hy.security.ban.api.HyUserBanService hyUserBanService() {
        return new DubboClientBuilder<>(fm.lizhi.hy.security.ban.api.HyUserBanService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public fm.lizhi.xm.security.api.XmUserBanService xmUserBanService() {
        return new DubboClientBuilder<>(fm.lizhi.xm.security.api.XmUserBanService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public fm.lizhi.security.sms.api.H5TokenService hyH5TokenService() {
        return new DubboClientBuilder<>(fm.lizhi.security.sms.api.H5TokenService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public fm.lizhi.xm.security.api.H5TokenService xmH5TokenService() {
        return new DubboClientBuilder<>(fm.lizhi.xm.security.api.H5TokenService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public PpUserInnerMapService hyPpUserInnerMapService() {
        return new DubboClientBuilder<>(PpUserInnerMapService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public PpWithdrawUserService ppWithdrawUserService() {
        return new DubboClientBuilder<>(PpWithdrawUserService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public XmWithdrawUserService xmWithdrawUserService() {
        return new DubboClientBuilder<>(XmWithdrawUserService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public HyWithdrawUserService hyWithdrawUserService() {
        return new DubboClientBuilder<>(HyWithdrawUserService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }
}
