package fm.lizhi.ocean.wavecenter.infrastructure.user.manager;

import fm.lizhi.ocean.wavecenter.service.user.constants.VerifyStatusConstant;
import fm.lizhi.ocean.wavecenter.service.user.constants.SearchType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.user.convert.UserVerifyManagerConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.UserVerifyServiceRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.req.GetUserVerifyResultReq;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.req.SearchUserVerifyResultReq;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.res.GetUserVerifyResultRes;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.res.SearchUserVerifyResultRes;
import fm.lizhi.ocean.wavecenter.service.user.dto.GetUserVerifyResultDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.GetUserVerifyResultParamDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.SearchUserVerifyResultDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.SearchUserVerifyResultParamDTO;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserVerifyManager;

/**
 * 用户认证管理实现
 */
@Component
public class UserVerifyManagerImpl implements UserVerifyManager {

    @Autowired
    private UserVerifyServiceRemote userVerifyServiceRemote;

    @Override
    public Result<GetUserVerifyResultDTO> getUserVerifyResult(GetUserVerifyResultParamDTO param) {
        GetUserVerifyResultReq req = UserVerifyManagerConvert.I.getUserVerifyResultParamDTO2Pb(param);
        Result<GetUserVerifyResultRes> result = userVerifyServiceRemote.getUserVerifyResult(req);
        if (RpcResult.isFail(result)) {
            return RpcResult.fail(result.rCode());
        }
        return RpcResult.success(UserVerifyManagerConvert.I.verifyResultRes2DTO(result.target()));
    }

    @Override
    public Result<SearchUserVerifyResultDTO> searchUserVerifyResult(SearchUserVerifyResultParamDTO param) {
        SearchUserVerifyResultReq req = UserVerifyManagerConvert.I.searchUserVerifyResultParamDTO2Pb(param);
        Result<SearchUserVerifyResultRes> result = userVerifyServiceRemote.searchUserVerifyResult(req);
        if (RpcResult.isFail(result)) {
            return RpcResult.fail(result.rCode());
        }
        return RpcResult.success(UserVerifyManagerConvert.I.searchVerifyResultRes2DTO(result.target()));
    }

}
