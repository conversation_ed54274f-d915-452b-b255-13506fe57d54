package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityRecommendCardConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityRecommendCard;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityRecommendCard;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityRecommendCardConfig;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActivityRecommendCardConfigConvert {
    ActivityRecommendCardConfigConvert I = Mappers.getMapper(ActivityRecommendCardConfigConvert.class);

    ActivityRecommendCardConfigBean recommendCardConfig2RecommendCardConfigBean(ActivityRecommendCardConfig activityRecommendCardConfig);

    ActivityRecommendCardConfig buildRecommendCardUpdateParam(RequestUpdateActivityRecommendCard param);

    ActivityRecommendCardConfig buildRecommendCardSaveParam(RequestSaveActivityRecommendCard param);

    ActivityRecommendCardConfigBean buildResponseActivityRecommendCard(ActivityRecommendCardConfig recommendCard);
}
