package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.ext;

import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityFlowResourceGiveRecord;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityApplyFlowResource;

import java.util.List;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface ActivityFlowResourceGiveRecordExtMapper {

    /**
     * 查询可用的流量资源
     * @param activityId 活动ID
     * @param appId 应用ID
     * @param deployEnv 部署环境
     * @param resourceCode 资源编码
     * @return 流量资源
     */
    @Select("select r.* " +
            "from activity_apply_flow_resource r " +
            "LEFT JOIN activity_resource_config c on r.resource_config_id = c.id " +
            "where r.activity_id = #{activityId} " +
            "and c.app_id = #{appId} " +
            "and c.deleted = 0 " +
            "and c.deploy_env = #{deployEnv} " +
            "and c.resource_code = #{resourceCode} limit 1")
    ActivityApplyFlowResource getActivityFlowResource(@Param("activityId") Long activityId, @Param("appId") Integer appId, @Param("deployEnv") String deployEnv, @Param("resourceCode") String resourceCode);

    /**
     * 查询流量资源记录
     * @param activityId
     * @param status
     * @return
     */
    @Select("select f.* from activity_resource_give_record g left join activity_flow_resource_give_record f on g.id= f.give_id where g.activity_id = #{activityId} and f.status = #{status}")
    List<ActivityFlowResourceGiveRecord> getFlowResourcesGiveRecordList(@Param("activityId") Long activityId, @Param("status") Integer status);


}
