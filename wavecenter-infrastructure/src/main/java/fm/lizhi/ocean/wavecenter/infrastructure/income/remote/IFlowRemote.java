package fm.lizhi.ocean.wavecenter.infrastructure.income.remote;

import fm.lizhi.ocean.wavecenter.api.income.bean.GetRoomSignRoomParamBean;
import fm.lizhi.ocean.wavecenter.common.dto.PageDto;
import fm.lizhi.ocean.wavecenter.base.remote.IRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.income.remote.req.PersonalGiftflowReq;
import fm.lizhi.ocean.wavecenter.infrastructure.income.remote.req.RoomGiftflowReq;
import fm.lizhi.ocean.wavecenter.service.income.dto.GiveGiftFlowDto;
import fm.lizhi.ocean.wavecenter.service.income.dto.RoomRecFlowSumDto;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/4/24 15:13
 */
public interface IFlowRemote extends IRemote {

    PageDto<GiveGiftFlowDto> getRoomRecFlow(GetRoomSignRoomParamBean paramBean);

    RoomRecFlowSumDto getRoomRecFlowSum(GetRoomSignRoomParamBean paramBean);

    PageDto<GiveGiftFlowDto> getPersonalGiftflow(PersonalGiftflowReq req);

    GiveGiftFlowDto getPersonalGiftflowSum(PersonalGiftflowReq req);

    PageDto<GiveGiftFlowDto> getRoomGiftflow(RoomGiftflowReq req);

    GiveGiftFlowDto getRoomGiftflowSum(RoomGiftflowReq req);

    /**
     * 查询流水的内容
     * @return
     */
    Map<Long, String> getFlowRemark(Set<Long> flowIds);

}
