package fm.lizhi.ocean.wavecenter.infrastructure.live.remote.xm;

import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.live.room.xm.api.LiveNewService;
import fm.lizhi.live.room.xm.api.LiveRoomService;
import fm.lizhi.live.room.xm.dto.liveroom.roomNotify.*;
import fm.lizhi.live.room.xm.protocol.LiveNewProto;
import fm.lizhi.live.room.xm.protocol.LiveRoomProto;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.live.dto.LiveDto;
import fm.lizhi.ocean.wavecenter.infrastructure.live.remote.ILiveRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.live.request.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/8/26 14:37
 */
@Slf4j
@Component
public class XmLiveRemote implements ILiveRemote {

    @Autowired
    private LiveNewService liveNewService;

    @Autowired
    private LiveRoomService liveRoomService;

    @Override
    public List<LiveDto> getLiveByIds(List<Long> ids) {
        return Collections.emptyList();
    }

    @Override
    public Optional<LiveDto> getLiveInfo(Long liveId) {
        Result<LiveNewProto.ResponseGetLiveNoCache> result = liveNewService.getLiveNoCache(LiveNewProto.GetLiveParams.newBuilder()
                .setLiveId(liveId)
                .build());
        if (RpcResult.isFail(result)) {
            log.warn("xm getLiveInfo fail. rCode={},liveId={}", result.rCode(), liveId);
            return Optional.empty();
        }

        LiveNewProto.Live livePb = result.target().getLive();
        return Optional.of(new LiveDto()
                .setId(liveId)
                .setRoomType(livePb.getType())
                .setUserId(livePb.getUserId())
                .setOnAir(livePb.getStatus() == 1)
        );
    }

    @Override
    public Optional<Long> getLatestLiveIdByUserId(Long userId) {
        Result<LiveNewProto.ResponseGetUpToDateLiveIdNotCache> result = liveNewService.getUpToDateLiveIdNotCache(LiveNewProto.GetUpToDateLiveIdParams.newBuilder().setUserId(userId).build());
        if (RpcResult.isFail(result)) {
            log.warn("xm getLatestLiveIdByUserId fail. rCode={},userId={}", result.rCode(), userId);
            return Optional.empty();
        }
        return Optional.of(result.target().getLiveId());
    }

    @Override
    public Result<GetRoomNoticeResponse> getRoomNotice(GetRoomNoticeRequest request) {
        ReqGetRoomNoticeDto dto = new ReqGetRoomNoticeDto();
        dto.setNjId(request.getNjId());
        Result<LiveRoomProto.ResponseGetRoomNotice> result = liveRoomService.getRoomNotice(JsonUtils.toJsonStringLegacy(dto));
        if (RpcResult.isFail(result)) {
            log.warn("xm getRoomNotice fail. rCode={},request={}", result.rCode(), request);
            return RpcResult.fail(GER_ROOM_NOTICE_FAIL);
        }
        GetRoomNoticeResponse response = new GetRoomNoticeResponse();
        String roomNoticeDto = result.target().getResGetRoomNoticeDto();
        ResGetRoomNoticeDto noticeDto = JSONObject.parseObject(roomNoticeDto, ResGetRoomNoticeDto.class);
        if (noticeDto == null || noticeDto.getRoomNoticeDto() == null || StringUtils.isBlank(noticeDto.getRoomNoticeDto().getContent())) {
            response.setContent("");
            return RpcResult.success(response);
        }
        response.setContent(noticeDto.getRoomNoticeDto().getContent());
        response.setImageUrls(noticeDto.getRoomNoticeDto().getImageUrls());
        return RpcResult.success(response);
    }

    @Override
    public Result<Void> editRoomNotice(EditRoomNoticeRequest request) {
        //需要先查询出完整的房间公告信息
        ReqSaveRoomNoticeDto dto = new ReqSaveRoomNoticeDto();
        RoomNoticeDto roomNoticeDto = new RoomNoticeDto();
        if (StringUtils.isNotBlank(request.getContent())) {
            roomNoticeDto.setContent(request.getContent());
        }
        if (CollectionUtils.isNotEmpty(request.getImageUrl())) {
            roomNoticeDto.setImageUrls(request.getImageUrl());
        }
        roomNoticeDto.setOperatorId(request.getNjId());
        roomNoticeDto.setNjId(request.getNjId());
        dto.setRoomNoticeDto(roomNoticeDto);
        Result<LiveRoomProto.ResponseSaveRoomNotice> result = liveRoomService.saveRoomNotice(JsonUtils.toJsonStringLegacy(dto));
        if (RpcResult.isFail(result)) {
            log.warn("xm editRoomNotice fail. rCode={},request={}", result.rCode(), request);
            return RpcResult.fail(EDIT_ROOM_NOTICE_FAIL);
        }
        LiveRoomProto.ResponseSaveRoomNotice notice = result.target();
        String resSaveRoomNoticeDto = notice.getResSaveRoomNoticeDto();

        ResSaveRoomNoticeDto noticeDto = JSONObject.parseObject(resSaveRoomNoticeDto, ResSaveRoomNoticeDto.class);
        log.info("xm editRoomNotice result={},request={}", noticeDto, JsonUtils.toJsonStringLegacy(request));
        if (noticeDto.getCode() != 0) {
            return RpcResult.fail(EDIT_ROOM_NOTICE_FAIL, noticeDto.getMessage());
        }
        return RpcResult.success();
    }

    @Override
    public Result<GetRoomInfoByNjIdResponse> getRoomInfoByNjId(Long userId) {
        Result<LiveRoomProto.ResponseGetLiveRoomByUserId> result = liveRoomService.getLiveRoomByUserId(userId);
        if (RpcResult.isFail(result)) {
            log.warn("xm getRoomInfoByNjId fail. rCode={},userId={}", result.rCode(), userId);
            if (result.rCode() == LiveRoomService.GET_LIVE_ROOM_BY_USER_ID_NO_CACHE_ERROR_NOT_FOUND) {
                return RpcResult.fail(GET_ROOM_INFO_BY_NJ_ID_NO_EXIST);
            }
            return RpcResult.fail(GET_ROOM_INFO_BY_NJ_ID_FAIL);
        }
        LiveRoomProto.LiveRoom liveRoom = result.target().getLiveRoom();
        GetRoomInfoByNjIdResponse info = new GetRoomInfoByNjIdResponse().setId(liveRoom.getId())
                .setName(liveRoom.getName())
                .setRadioId(liveRoom.getRadioId())
                .setStatus(liveRoom.getStatus())
                .setUserId(liveRoom.getUserId());
        return RpcResult.success(info);
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.XIMI;
    }
}
