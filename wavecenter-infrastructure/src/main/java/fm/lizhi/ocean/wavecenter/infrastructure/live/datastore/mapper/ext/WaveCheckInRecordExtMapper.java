package fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WaveCheckInRecord;
import fm.lizhi.ocean.wavecenter.infrastructure.live.dto.WaveCheckInIncomeSumDTO;
import fm.lizhi.ocean.wavecenter.infrastructure.live.dto.WaveCheckInPlayerSumDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * 麦序福利只读库扩展mapper, 用于web站数据统计
 */
@DataStore(namespace = "mysql_ocean_wave_r")
public interface WaveCheckInRecordExtMapper {

    @Select("<script>\n" +
            "  SELECT SUM(`income`) AS `income`, SUM(`charm_value`) AS `charm`\n" +
            "  FROM `wave_check_in_record`\n" +
            "  WHERE `schedule_id` IN\n" +
            "    <foreach collection=\"scheduleIds\" item=\"scheduleId\" open=\"(\" separator=\",\" close=\")\">\n" +
            "      #{scheduleId}\n" +
            "    </foreach>\n" +
            "</script>")
    WaveCheckInIncomeSumDTO getCheckInIncomeSum(@Param("scheduleIds") List<Long> scheduleIds);

    @Select("<script>\n" +
            "    SELECT COUNT(DISTINCT `user_id`)\n" +
            "    FROM `wave_check_in_record`\n" +
            "    WHERE `schedule_id` IN\n" +
            "      <foreach collection=\"scheduleIds\" item=\"scheduleId\" open=\"(\" separator=\",\" close=\")\">\n" +
            "        #{scheduleId}\n" +
            "      </foreach>\n" +
            "      AND scheduled = 1\n" +
            "</script>")
    int getCheckInScheduledPlayerCnt(@Param("scheduleIds") List<Long> scheduleIds);

    @Select("<script>\n" +
            "  SELECT * \n" +
            "  FROM wave_check_in_record\n" +
            "  WHERE\n" +
            "    schedule_id IN\n" +
            "      <foreach collection=\"scheduleIds\" item=\"scheduleId\" open=\"(\" separator=\",\" close=\")\">\n" +
            "        #{scheduleId}\n" +
            "      </foreach>\n" +
            "</script>")
    List<WaveCheckInRecord> getCheckInRecordsByScheduleIds(@Param("scheduleIds") List<Long> scheduleIds);

    @Select("<script>\n" +
            "  SELECT\n" +
            "    SUM(wave_check_in_record.charm_value) AS charm,\n" +
            "    SUM(wave_check_in_record.income) AS income,\n" +
            "    SUM(CASE WHEN wave_check_in_record.`status` = 1 THEN 1 ELSE 0 END) AS seatCnt,\n" +
            "    SUM(CASE WHEN wave_check_in_schedule.`host_id` = #{playerId} THEN 1 ELSE 0 END) AS hostCnt\n" +
            "  FROM\n" +
            "    wave_check_in_schedule\n" +
            "  LEFT JOIN\n" +
            "    wave_check_in_record ON wave_check_in_schedule.id = wave_check_in_record.schedule_id\n" +
            "  WHERE\n" +
            "    wave_check_in_schedule.`start_time` &gt;= #{startDate}\n" +
            "    AND wave_check_in_schedule.`start_time` &lt;= #{endDate}\n" +
            "    AND wave_check_in_record.user_id = #{playerId}\n" +
            "    <if test=\"familyId != null\">\n" +
            "      AND wave_check_in_schedule.`family_id` = #{familyId}\n" +
            "    </if>\n" +
            "    <if test=\"roomId != null\">\n" +
            "      AND wave_check_in_schedule.`room_id` = #{roomId}\n" +
            "    </if>\n" +
            "</script>")
    WaveCheckInPlayerSumDTO getCheckInPlayerSum(@Param("playerId") long playerId, @Param("startDate") Date startDate,
                                                @Param("endDate") Date endDate, @Param("familyId") Long familyId,
                                                @Param("roomId") Long roomId);

    @Select("<script>\n" +
            "  SELECT * \n" +
            "  FROM wave_check_in_record\n" +
            "  WHERE\n" +
            "    schedule_id IN\n" +
            "      <foreach collection=\"scheduleIds\" item=\"scheduleId\" open=\"(\" separator=\",\" close=\")\">\n" +
            "        #{scheduleId}\n" +
            "      </foreach>\n" +
            "    AND user_id = #{userId}\n" +
            "</script>")
    List<WaveCheckInRecord> getCheckInPlayerRecords(@Param("scheduleIds") List<Long> scheduleIds, @Param("userId") long userId);
}
