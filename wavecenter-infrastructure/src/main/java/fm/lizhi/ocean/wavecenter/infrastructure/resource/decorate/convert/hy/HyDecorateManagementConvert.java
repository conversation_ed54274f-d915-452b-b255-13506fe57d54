package fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.convert.hy;

import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.hy.HyCreateAvatarWidgetBean;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.hy.HyCreateRoomBackgroundBean;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestCreateAvatarWidget;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestCreateRoomBackground;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface HyDecorateManagementConvert {

    HyDecorateManagementConvert I = Mappers.getMapper(HyDecorateManagementConvert.class);

    HyCreateRoomBackgroundBean toHyCreateRoomBackgroundBean(RequestCreateRoomBackground request);

    HyCreateAvatarWidgetBean toHyCreateAvatarWidgetBean(RequestCreateAvatarWidget request);
}
