package fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilyLevelAwardItem;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcFamilyLevelAwardItemExtMapper {

    @Update("<script>\n" +
            "  UPDATE `wavecenter_family_level_award_item`\n" +
            "  SET `deleted` = 1, `modify_time` = NOW(), `modifier` = #{operator}\n" +
            "  WHERE `id` IN\n" +
            "    <foreach collection=\"ids\" item=\"id\" open=\"(\" separator=\",\" close=\")\">\n" +
            "      #{id}\n" +
            "    </foreach>\n" +
            "</script>")
    int deleteByIds(@Param("ids") List<Long> ids, @Param("operator") String operator);

    @Select("<script>\n" +
            "  SELECT * FROM `wavecenter_family_level_award_item`\n" +
            "  WHERE `rule_id` IN\n" +
            "    <foreach collection=\"ruleIds\" item=\"ruleId\" open=\"(\" separator=\",\" close=\")\">\n" +
            "        #{ruleId}\n" +
            "    </foreach>\n" +
            "    AND `deleted` = 0\n" +
            "  ORDER BY `rule_id` ASC, `resource_type` ASC\n" +
            "</script>")
    List<WcFamilyLevelAwardItem> getRulesItems(@Param("ruleIds") List<Long> ruleIds);
}
