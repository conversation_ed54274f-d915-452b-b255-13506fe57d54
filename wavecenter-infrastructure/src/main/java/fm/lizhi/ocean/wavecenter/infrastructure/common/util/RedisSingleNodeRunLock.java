package fm.lizhi.ocean.wavecenter.infrastructure.common.util;

import fm.lizhi.common.datastore.redis.client.RedisClient;
import fm.lizhi.ocean.wavecenter.common.utils.RedisLock;
import fm.lizhi.ocean.wavecenter.common.utils.SingleNodeRunLock;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.CommonRedisKey;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * @date 2025/7/1 15:29
 */
@Slf4j
public class RedisSingleNodeRunLock implements SingleNodeRunLock {

    private final AtomicBoolean nodeRunning;

    private final RedisLock redisLock;

    private final RedisLock appRedisLock;

    private static final Map<String, RedisSingleNodeRunLock> INSTANCE_MAP = new ConcurrentHashMap<>();

    public static SingleNodeRunLock getInstance(RedisClient redisClient, String caseName, int appId, int expireSeconds, int retryTimeoutSeconds) {
        return INSTANCE_MAP.computeIfAbsent(caseName, k -> new RedisSingleNodeRunLock(redisClient, caseName, appId, expireSeconds, retryTimeoutSeconds));
    }

    private RedisSingleNodeRunLock(RedisClient redisClient, String caseName, int appId, int expireSeconds, int retryTimeoutSeconds) {
        this.redisLock = new RedisLock(redisClient
                , CommonRedisKey.SINGLE_NODE_RUN_LOCK.getKey(caseName)
                , retryTimeoutSeconds * 1000
                , 10 * 1000
        );
        this.appRedisLock = new RedisLock(redisClient
                , CommonRedisKey.SINGLE_NODE_RUN_BIZ_LOCK.getKey(caseName, appId)
                , retryTimeoutSeconds * 1000
                , expireSeconds * 1000
        );
        this.nodeRunning = new AtomicBoolean(false);
    }

    @Override
    public boolean lock() {
        // 抢全局锁，抢不到重试
        boolean lock = this.redisLock.lock();
        if (!lock) {
            log.warn("RedisSingleNodeRunLock lock error.");
            return false;
        }

        try {
            // 抢到全局锁占用节点 防止一个节点同时运行多个任务
            log.info("beforeRunningResult={}", nodeRunning.get());
            boolean runningResult = nodeRunning.compareAndSet(false, true);
            if (!runningResult) {
                log.warn("RedisSingleNodeRunLock node is running.");
                return false;
            }
            log.info("afterRunningResult={}", nodeRunning.get());

            // 业务锁
            boolean appLock = this.appRedisLock.tryLock();
            if (!appLock) {
                log.warn("RedisSingleNodeRunLock app lock error.");
                unlockNode();
                return false;
            }

            return true;
        } catch (Exception e) {
            log.error("RedisSingleNodeRunLock error.", e);
            unlockNode();
            return false;
        } finally {
            // 释放全局锁
            this.redisLock.unlock();
        }
    }

    @Override
    public void unlock() {
        unlockNode();
        this.appRedisLock.unlock();
    }

    private void unlockNode(){
        log.info("unlockNode={}", nodeRunning.get());
        boolean result = nodeRunning.compareAndSet(true, false);
        log.info("unlockNode={}, result={}", nodeRunning.get(), result);
    }
}
