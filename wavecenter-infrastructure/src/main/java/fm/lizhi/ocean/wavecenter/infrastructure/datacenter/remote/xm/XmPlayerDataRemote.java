package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote.xm;

import fm.lizhi.ocean.wavecenter.api.datacenter.bean.IncomeBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.PlayerAssessmentInfoBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.utils.WcAssert;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PaySettleConfigCodeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.manager.CharmStateManager;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote.IPlayerDataRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.income.manager.PaymentManager;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.PlayerGetAssessmentDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/24 11:21
 */
@Component
public class XmPlayerDataRemote implements IPlayerDataRemote {

    @Autowired
    private PaymentManager paymentManager;
    @Autowired
    private CharmStateManager charmStateManager;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.XIMI;
    }

    @Override
    public PlayerAssessmentInfoBean getAssessmentInfo(PlayerGetAssessmentDto param) {
        Long familyId = param.getFamilyId();
        List<Long> familySignRoomIds = param.getFamilySignRoomIds();
        Long signRoomId = param.getSignRoomId();
        Long playerId = param.getPlayerId();

        WcAssert.notNull(familyId, "familyId is null");
        WcAssert.notNull(playerId, "playerId is null");
        WcAssert.notEmpty(familySignRoomIds, "roomIds is empty");

        //签约厅收礼收入
        IncomeBean roomIncome = paymentManager.getIncomeBeanByPlayer(familyId, signRoomId, playerId, PaySettleConfigCodeEnum.ANCHOR_HALL_INCOME_TOTAL_AMOUNT);
        //官方厅收礼收入
        IncomeBean officialIncome = paymentManager.getIncomeBeanByPlayer(familyId, signRoomId, playerId, PaySettleConfigCodeEnum.ANCHOR_OFFICIAL_HALL_INCOME_TOTAL_AMOUNT);

        PlayerAssessmentInfoBean playerAssessmentInfoBean = new PlayerAssessmentInfoBean()
                .setFlushTime(System.currentTimeMillis())
                .setRoomIncome(roomIncome)
                .setOfficialIncome(officialIncome);

        //魅力值
        //厅收礼魅力值
        playerAssessmentInfoBean.setRoomCharm(charmStateManager.getPlayerAssessmentRoomCharm(param));
        //官方厅魅力值
        playerAssessmentInfoBean.setOfficialCharm(charmStateManager.getPlayerAssessmentOfficialCharm(param));

        return playerAssessmentInfoBean;
    }
}
