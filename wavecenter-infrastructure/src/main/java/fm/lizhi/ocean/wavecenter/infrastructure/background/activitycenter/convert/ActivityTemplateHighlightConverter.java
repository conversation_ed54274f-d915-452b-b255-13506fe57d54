package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateHighlightBean;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityTemplateHighlight;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 活动模板亮点标签转换器
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface ActivityTemplateHighlightConverter {

    /**
     * 转换为活动模板亮点标签Bean列表
     *
     * @param highlights 活动模板亮点标签实体列表
     * @return 活动模板亮点标签Bean
     */
    List<ActivityTemplateHighlightBean> toActivityTemplateHighlightBeans(List<ActivityTemplateHighlight> highlights);

    /**
     * 转换为活动模板亮点标签实体列表
     *
     * @param beans 活动模板亮点标签Bean
     * @return 活动模板亮点标签实体列表
     */
    default List<ActivityTemplateHighlight> toCreateActivityTemplateHighlights(List<ActivityTemplateHighlightBean> beans, long templateId) {
        if (CollectionUtils.isEmpty(beans)) {
            return Collections.emptyList();
        }
        ArrayList<ActivityTemplateHighlight> highlights = new ArrayList<>();
        for (int index = 0; index < beans.size(); index++) {
            highlights.add(toCreateActivityTemplateHighlight(beans.get(index), templateId, index));
        }
        return highlights;
    }

    @Mapping(target = "id", ignore = true)
    ActivityTemplateHighlight toCreateActivityTemplateHighlight(ActivityTemplateHighlightBean bean, long templateId, int index);
}
