package fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 家族签约的直播
 *
 * @date 2024-11-19 03:54:21
 */
@Table(name = "`family_nj`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PpFamilyNj {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 家族ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 主播ID
     */
    @Column(name= "`nj_id`")
    private Long njId;

    /**
     * 加入家族:JOIN   离开家族:LEAVE
     */
    @Column(name= "`status`")
    private String status;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 身份证号码
     */
    @Column(name= "`identity_no`")
    private String identityNo;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", familyId=").append(familyId);
        sb.append(", njId=").append(njId);
        sb.append(", status=").append(status);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", identityNo=").append(identityNo);
        sb.append("]");
        return sb.toString();
    }
}