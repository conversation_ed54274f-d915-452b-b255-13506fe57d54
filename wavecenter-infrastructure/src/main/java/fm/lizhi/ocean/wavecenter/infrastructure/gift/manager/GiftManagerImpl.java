package fm.lizhi.ocean.wavecenter.infrastructure.gift.manager;

import fm.lizhi.ocean.wavecenter.infrastructure.gift.remote.IGiftRemote;
import fm.lizhi.ocean.wavecenter.service.gift.dto.GiftDto;
import fm.lizhi.ocean.wavecenter.service.gift.manager.GiftManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/24 15:31
 */
@Component
public class GiftManagerImpl implements GiftManager {

    @Autowired
    private IGiftRemote iGiftRemote;

    @Override
    public List<GiftDto> getByIds(List<Long> ids) {
        return iGiftRemote.getByIds(ids);
    }

}
