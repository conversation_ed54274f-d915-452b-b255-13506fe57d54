package fm.lizhi.ocean.wavecenter.infrastructure.resource.recommendcard.remote.hy;

import com.google.common.collect.Lists;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.*;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.request.*;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.response.RewardResultBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.CountDownLatchWrapper;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.ThreadConstants;
import fm.lizhi.ocean.wavecenter.common.utils.WcAssert;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.recommendcard.remote.RecommendCardRemote;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto.GetUseRecordParamDTO;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto.RecommendAllocationRecordDTO;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto.RecommendCardUseRecordDTO;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto.UserRecommendCardStockDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import hy.fm.lizhi.live.pp.live.api.TopRecommendH5Service;
import hy.fm.lizhi.live.pp.live.api.TopRecommendService;
import hy.fm.lizhi.live.pp.live.api.TopRecommendSpringService;
import hy.fm.lizhi.live.pp.live.bean.CardRecordOrderBean;
import hy.fm.lizhi.live.pp.live.bean.TopRecommendCardOperationRecordBean;
import hy.fm.lizhi.live.pp.live.bean.UserTopRecommendCardAmountBean;
import hy.fm.lizhi.live.pp.live.constant.SaveTopRecommendResultEnum;
import hy.fm.lizhi.live.pp.live.constant.TopRecommendCardOperationTypeEnum;
import hy.fm.lizhi.live.pp.live.protocol.TopRecommendH5Proto;
import hy.fm.lizhi.live.pp.live.protocol.TopRecommendProto;
import hy.fm.lizhi.live.pp.live.request.RequestCountUserExpire;
import hy.fm.lizhi.live.pp.live.request.RequestGetMyCardRecord;
import hy.fm.lizhi.live.pp.live.request.RequestGetOperationRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/21 15:26
 */
@Slf4j
@Component
public class HyRecommendCardRemote implements RecommendCardRemote {

    @Autowired
    private TopRecommendSpringService topRecommendSpringService;
    @Autowired
    private TopRecommendService topRecommendService;

    @Autowired
    private TopRecommendH5Service topRecommendH5Service;
    @Autowired
    private UserManager userManager;

    @Override
    public List<UserRecommendCardStockDTO> getUserStock(List<Long> userIds) {
        Result<List<UserTopRecommendCardAmountBean>> result = topRecommendSpringService.getUserTopRecommendCardAmount(userIds);
        if (RpcResult.isFail(result)) {
            log.warn("getUserTopRecommendCardAmount fail. rCode={},userIds={}", result.rCode(), JsonUtil.dumps(userIds));
            return Collections.emptyList();
        }

        List<UserTopRecommendCardAmountBean> list = result.target();
        return list.stream().map(v->new UserRecommendCardStockDTO()
                .setUserId(v.getUserId())
                .setStock(v.getAmount()))
                .collect(Collectors.toList());
    }

    @Override
    public Integer getExpireNum(Long userId, Date startDate, Date endDate) {
        RequestCountUserExpire req = new RequestCountUserExpire()
                .setUserId(userId);
        if (startDate != null) {
            req.setExpireTimeStamp(startDate.getTime());
        }

        Result<Integer> result = topRecommendSpringService.countUserExpireByTime(req);
        if (RpcResult.isFail(result)) {
            log.warn("countUserExpireByTime fail. rCode={},userId={},startDate={},endDate={}", result.rCode(), userId, startDate, endDate);
            return 0;
        }

        return result.target();
    }

    @Override
    public PageBean<RecommendCardUseRecordBean> getUseRecordForManagement(RequestGetUseRecord request) {
        TopRecommendProto.TopRecommendRecordListParam.Builder paramBuilder = TopRecommendProto.TopRecommendRecordListParam.newBuilder();
        if (request.getUserId() != null) {
            paramBuilder.setUserId(request.getUserId());
        }
        paramBuilder.addType(TopRecommendCardOperationTypeEnum.USE.getValue());
        TopRecommendProto.TopRecommendRecordPageListParam.Builder builder = TopRecommendProto.TopRecommendRecordPageListParam.newBuilder();
        builder.setSearchParam(paramBuilder);
        builder.setPageNumber(request.getPageNo()).setPageSize(request.getPageSize());
        Result<TopRecommendProto.ResponseGetTopRecommendRecordPageList> result = topRecommendService.getTopRecommendRecordPageList(builder.build());
        if (RpcResult.isFail(result)) {
            log.warn("getTopRecommendRecordPageList fail. rCode={},request={}", result.rCode(), JsonUtil.dumps(request));
            return PageBean.empty();
        }

        List<RecommendCardUseRecordBean> beanList = new ArrayList<>();
        for (TopRecommendProto.TopRecommendOperationRecord useRecord : result.target().getTopRecommendOperationRecordList()) {
            RecommendCardUseRecordBean bean = new RecommendCardUseRecordBean();
            bean.setId(useRecord.getId());
            if (NumberUtils.isCreatable(useRecord.getRecNjUserId())) {
                bean.setNjId(Long.valueOf(useRecord.getRecNjUserId()));
            }
            bean.setNjName(useRecord.getRecNjName());
            bean.setUseTime(DateUtil.formatStrToNormalDate(useRecord.getOperationTime()));
            bean.setRecommendationTime(useRecord.getTime());
            bean.setUseNum(useRecord.getAmount());
            bean.setPosition(useRecord.getPosition());
            bean.setCategory(useRecord.getCategory());
            beanList.add(bean);
        }

        return PageBean.of(result.target().getTotal(), beanList);
    }

    @Override
    public RewardResultBean rewardRecommendCard(Long operatorUserId, List<RecommendCardRewardBean> rewardBeans) {

        List<TopRecommendH5Proto.AllocationDetail> rewardDetails = rewardBeans.stream().map(x->TopRecommendH5Proto.AllocationDetail.newBuilder()
                        .setUserId(x.getTargetUserId()).setAmount(x.getNum()).build()).collect(Collectors.toList());

        Result<TopRecommendH5Proto.ResponseAllocationTopCard> result = topRecommendH5Service.allocationTopCard(operatorUserId, rewardDetails);
        if (!RpcResult.isSuccess(result)) {
            log.warn("hy rewardRecommendCard error, operatorUserId: {}, rewardBeans: {}", operatorUserId, rewardBeans);
            return new RewardResultBean().setCode(REWARD_RECOMMEND_CARD_ERROR).setRewardResult("请求失败，请联系管理员");
        }

        int code = result.target().getCode();
        String desc = result.target().getDesc();

        if (code != 0) {
            return new RewardResultBean().setCode(REWARD_RECOMMEND_CARD_NOT_SUCCESS).setRewardResult(desc);
        }

        return new RewardResultBean().setCode(REWARD_RECOMMEND_CARD_SUCCESS).setRewardResult("success");
    }

    @Override
    public PageBean<RecommendCardSendRecordBean> getSendRecord(RequestGetSendRecord request) {
        TopRecommendProto.TopRecommendRecordListParam.Builder paramBuilder = TopRecommendProto.TopRecommendRecordListParam.newBuilder();
        paramBuilder.addAllType(Lists.newArrayList(
                TopRecommendCardOperationTypeEnum.GRANT.getValue(),
                TopRecommendCardOperationTypeEnum.RECOVER.getValue(),
                TopRecommendCardOperationTypeEnum.AMUSE_AWARD.getValue(),
                TopRecommendCardOperationTypeEnum.EXPIRE.getValue(),
                TopRecommendCardOperationTypeEnum.FREE_ANNOTATION.getValue()));
        if (request.getUserId() != null) {
            paramBuilder.setUserId(request.getUserId());
        }
        paramBuilder.setStartTime(DateUtil.formatDateNormal(request.getStartTime()));
        paramBuilder.setEndTime(DateUtil.formatDateNormal(request.getEndTime()));

        TopRecommendProto.TopRecommendRecordPageListParam.Builder builder = TopRecommendProto.TopRecommendRecordPageListParam.newBuilder();
        builder.setSearchParam(paramBuilder);
        builder.setPageNumber(request.getPageNo());
        builder.setPageSize(request.getPageSize());
        Result<TopRecommendProto.ResponseGetTopRecommendRecordPageList> result = topRecommendService.getTopRecommendRecordPageList(builder.build());
        if (RpcResult.isFail(result)) {
            log.warn("getTopRecommendRecordPageList fail. rCode={},request={}", result.rCode(), JsonUtil.dumps(request));
            return PageBean.empty();
        }

        List<RecommendCardSendRecordBean> beanList = new ArrayList<>();
        for (TopRecommendProto.TopRecommendOperationRecord sendRecord : result.target().getTopRecommendOperationRecordList()) {
            beanList.add(buildSendRecordBean(sendRecord));
        }

        return PageBean.of(result.target().getTotal(), beanList);
    }

    private RecommendCardSendRecordBean buildSendRecordBean(TopRecommendProto.TopRecommendOperationRecord sendRecord){
        RecommendCardSendRecordBean bean = new RecommendCardSendRecordBean();
        bean.setId(sendRecord.getId());
        bean.setUserId(Long.valueOf(sendRecord.getUserId()));
        bean.setUserBand(sendRecord.getBand());
        bean.setUserName(sendRecord.getName());
        bean.setSendTime(DateUtil.formatStrToNormalDate(sendRecord.getOperationTime()));
        bean.setSendNum(sendRecord.getAmount());
        bean.setReason(sendRecord.getReason());
        bean.setExpireTime(DateUtil.formatStrToNormalDate(sendRecord.getExpireTime()));
        return bean;
    }

    @Override
    public List<BatchSendUserResultBean> batchSend(RequestBatchSend request) {
        List<SendRecommendCardBean> sendRecommendCards = request.getSendRecommendCards();
        if (CollectionUtils.isEmpty(sendRecommendCards)) {
            return Collections.emptyList();
        }

        List<BatchSendUserResultBean> result = new CopyOnWriteArrayList<>();
        // 有效期校验, 最小必须为2
        for (SendRecommendCardBean sendRecommendCard : sendRecommendCards) {
            if (sendRecommendCard.getExpireDay() == null) {
                result.add(new BatchSendUserResultBean()
                        .setUserId(sendRecommendCard.getUserId())
                        .setMsg("有效期为空")
                        .setResultCode(-1)
                );
                continue;
            }
            if (sendRecommendCard.getExpireDay() < 2) {
                result.add(new BatchSendUserResultBean()
                        .setUserId(sendRecommendCard.getUserId())
                        .setMsg("有效期最少为2天")
                        .setResultCode(-1)
                );
                continue;
            }
            if (sendRecommendCard.getExpireDay() > 365) {
                result.add(new BatchSendUserResultBean()
                        .setUserId(sendRecommendCard.getUserId())
                        .setMsg("有效期最多为365天")
                        .setResultCode(-1)
                );
                continue;
            }
        }
        if (CollectionUtils.isNotEmpty(result)) {
            LogContext.addResLog("result={}", JsonUtil.dumps(result));
            return result;
        }

        List<List<SendRecommendCardBean>> partition = Lists.partition(sendRecommendCards, 20);
        CountDownLatchWrapper downLatchWrapper = new CountDownLatchWrapper(ThreadConstants.batchSendRecommendCardPool, 4, partition.size());
        for (List<SendRecommendCardBean> sendRecommendCardBeans : partition) {
            downLatchWrapper.submit(()->{

                List<TopRecommendProto.TopRecommendCardOperationParam> params = new ArrayList<>();
                for (SendRecommendCardBean sendRecommendCardBean : sendRecommendCardBeans) {
                    TopRecommendProto.TopRecommendCardOperationParam.Builder builder = TopRecommendProto.TopRecommendCardOperationParam.newBuilder();
                    builder.setUserId(sendRecommendCardBean.getUserId())
                            .setOperationAmount(sendRecommendCardBean.getSendNum())
                            .setValidityDays(sendRecommendCardBean.getExpireDay())
                            .setType(TopRecommendCardOperationTypeEnum.GRANT.getValue())
                            .setReason(sendRecommendCardBean.getReason());
                    params.add(builder.build());
                }

                Result<TopRecommendProto.ResponseBatchSaveUserTopRecommendCard> rpcResult = topRecommendService.batchSaveUserTopRecommendCard(params);
                if (RpcResult.isFail(rpcResult)) {
                    log.warn("hy.batchSendRecommendCard fail. rCode={}", rpcResult.rCode());

                    result.addAll(sendRecommendCardBeans.stream().map(v->new BatchSendUserResultBean()
                            .setUserId(v.getUserId())
                            .setResultCode(rpcResult.rCode())
                            .setMsg("服务异常")
                    ).collect(Collectors.toList()));

                    return;
                }
                List<TopRecommendProto.TopRecommendCardOperationResult> resultList = rpcResult.target().getResultList();
                for (TopRecommendProto.TopRecommendCardOperationResult r : resultList) {
                    if (r.getResultCode() != SaveTopRecommendResultEnum.SUCCESS_FLAG.getValue()) {
                        log.info("batchSaveUserTopRecommendCard fail. userId={},resultCode={}", r.getUserId(), r.getResultCode());
                        result.add(new BatchSendUserResultBean()
                                .setResultCode(r.getResultCode())
                                .setUserId(r.getUserId())
                                .setMsg(SaveTopRecommendResultEnum.get(r.getResultCode()).getName()));
                    }
                }

            });
        }

        downLatchWrapper.await();

        return result;
    }

    @Override
    public boolean recycle(RequestRecycle request) {
        WcAssert.notNull(request.getUserId(), "userId is null");
        WcAssert.notNull(request.getNum(), "num is null");

        List<SimpleUserDto> userList = userManager.getSimpleUserByIds(Lists.newArrayList(request.getUserId()));
        if (CollectionUtils.isEmpty(userList)) {
            LogContext.addResLog("userList is empty");
            return false;
        }

        List<TopRecommendProto.TopRecommendCardOperationParam> params = new ArrayList<>();
        TopRecommendProto.TopRecommendCardOperationParam.Builder builder = TopRecommendProto.TopRecommendCardOperationParam.newBuilder();
        builder.setUserId(request.getUserId())
                .setOperationAmount(request.getNum())
                .setType(TopRecommendCardOperationTypeEnum.RECOVER.getValue())
                .setReason(request.getReason());
        params.add(builder.build());
        Result<TopRecommendProto.ResponseBatchSaveUserTopRecommendCard> result = topRecommendService.batchSaveUserTopRecommendCard(params);
        if (RpcResult.isFail(result)) {
            log.warn("hy.recycle fail. rCode={},id={}", result.rCode(), request.getSendRecordId());
            return false;
        }
        return true;
    }

    @Override
    public PageBean<RecommendAllocationRecordDTO> getAllocationRecord(RequestGetAllocationRecord request) {
        RequestGetMyCardRecord record = new RequestGetMyCardRecord();
        record.setUserId(request.getFamilyUserId());
        record.setRecNjUserId(request.getNjId());
        if (request.getStartDate() != null) {
            record.setStartTime(request.getStartDate().getTime());
        }
        if (request.getEndDate() != null) {
            record.setEndTime(request.getEndDate().getTime());
        }
        record.setPageNumber(request.getPageNum());
        record.setPageSize(request.getPageSize());

        Result<hy.fm.lizhi.live.pp.live.bean.PageBean<CardRecordOrderBean>> result = topRecommendSpringService.getMyCardRecord(record);
        if (RpcResult.isFail(result)) {
            log.warn("getMyCardRecord fail. rCode={},request={}", result.rCode(), request);
            return PageBean.empty();
        }

        if (CollectionUtils.isEmpty(result.target().getList())) {
            return PageBean.empty();
        }

        List<RecommendAllocationRecordDTO> dtos = new ArrayList<>();
        for (CardRecordOrderBean pb : result.target().getList()) {
            RecommendAllocationRecordDTO dto = new RecommendAllocationRecordDTO();
            if (pb.getTime() != null) {
                dto.setAllocationTime(new Date(pb.getTime()));
            }
            dto.setNums(pb.getAmount());
            dto.setDetail(pb.getDetail());
            dtos.add(dto);
        }

        return PageBean.of(result.target().getTotal(), dtos);
    }

    @Override
    public PageBean<RecommendCardUseRecordDTO> getUseRecord(GetUseRecordParamDTO paramDTO) {
        RequestGetOperationRecord request = new RequestGetOperationRecord();
        request.setRecNjUserIds(paramDTO.getUserIds());
        request.setType(TopRecommendCardOperationTypeEnum.USE.getValue());
        request.setOperatorTimeAsc(paramDTO.isCreateTimeAsc()?1:0);
        request.setPageNum(paramDTO.getPageNumber());
        request.setPageSize(paramDTO.getPageSize());

        Result<hy.fm.lizhi.live.pp.live.bean.PageBean<TopRecommendCardOperationRecordBean>> result = topRecommendSpringService.getOperationRecord(request);
        if (RpcResult.isFail(result)) {
            log.warn("getUseRecord fail. rCode={},request={}", result.rCode(), request);
            return PageBean.empty();
        }

        List<RecommendCardUseRecordDTO> dtos = new ArrayList<>();
        for (TopRecommendCardOperationRecordBean pb : result.target().getList()) {
            RecommendCardUseRecordDTO dto = new RecommendCardUseRecordDTO();
            dto.setId(pb.getId());
            dto.setNjId(pb.getRecNjUserId());
            dto.setNums(Math.abs(pb.getOperationAmount()));
            dto.setCategory(pb.getCategory());
            dto.setRecommendIndex(pb.getPosition());
            dto.setPosition(pb.getPositionName());
            if (pb.getOperationTime() != null) {
                dto.setUseTime(new Date(pb.getOperationTime()));
            }
            if (pb.getStartDate() != null && pb.getEndDate() != null) {
                dto.setRecommendTime(DateUtil.formatDateNormal(pb.getStartDate()) + "~" + DateUtil.formatDateNormal(pb.getEndDate()));
            }
            dtos.add(dto);
        }

        return PageBean.of(result.target().getTotal(), dtos);
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.HEI_YE;
    }
}
