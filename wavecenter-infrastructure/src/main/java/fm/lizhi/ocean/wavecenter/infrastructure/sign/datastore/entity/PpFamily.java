package fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 家族
 *
 * @date 2024-05-08 04:54:10
 */
@Table(name = "`family`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PpFamily {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 个人家族:P_FAMILY 公司家族:C_FAMILY
     */
    @Column(name= "`family_type`")
    private String familyType;

    /**
     * 家族名称
     */
    @Column(name= "`family_name`")
    private String familyName;

    /**
     * 用户ID
     */
    @Column(name= "`user_id`")
    private Long userId;

    /**
     * 公会头像
     */
    @Column(name= "`portrait`")
    private String portrait;

    /**
     * 冻结:FREEZE  可用:ACTIVATION
     */
    @Column(name= "`status`")
    private String status;

    /**
     * 0:未同步给清结算 1:已同步给清结算
     */
    @Column(name= "`sync_status`")
    private Integer syncStatus;

    /**
     * 家族简介
     */
    @Column(name= "`family_note`")
    private String familyNote;

    /**
     * 联系人手机号,默认创建家族的手机号
     */
    @Column(name= "`phone`")
    private String phone;

    /**
     * 结算周期
     */
    @Column(name= "`settle_period`")
    private String settlePeriod;

    /**
     * 税率
     */
    @Column(name= "`tax_rate`")
    private Integer taxRate;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", familyType=").append(familyType);
        sb.append(", familyName=").append(familyName);
        sb.append(", userId=").append(userId);
        sb.append(", portrait=").append(portrait);
        sb.append(", status=").append(status);
        sb.append(", syncStatus=").append(syncStatus);
        sb.append(", familyNote=").append(familyNote);
        sb.append(", phone=").append(phone);
        sb.append(", settlePeriod=").append(settlePeriod);
        sb.append(", taxRate=").append(taxRate);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}