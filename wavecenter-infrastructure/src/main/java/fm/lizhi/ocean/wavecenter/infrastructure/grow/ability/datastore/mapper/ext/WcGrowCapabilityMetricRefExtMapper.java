package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowCapabilityMetricRef;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcGrowCapabilityMetricRefExtMapper {

    @Select("<script>\n" +
            "  SELECT *FROM `wavecenter_grow_capability_metric_ref`\n" +
            "  WHERE `deploy_env` = #{deployEnv}\n" +
            "    AND `app_id` = #{appId}\n" +
            "    AND `start_week_date` &gt;= #{minStartWeekDate}\n" +
            "    AND `start_week_date` &lt;= #{maxStartWeekDate}\n" +
            "</script>")
    List<WcGrowCapabilityMetricRef> getWeeksCapabilityMetricRefs(
            @Param("deployEnv") String deployEnv,
            @Param("appId") Integer appId,
            @Param("minStartWeekDate") Date minStartWeekDate,
            @Param("maxStartWeekDate") Date maxStartWeekDate);
}
