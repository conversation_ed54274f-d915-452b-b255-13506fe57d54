package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.kafka.consumer;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/4/22 17:53
 */
@Data
public class AccountFlowMsg {

    private String accountCode;
    /**
     * minus - 减 plus - 加  frozen -冻结 unfrozen-解冻
     */
    private String accountOpType;
    private String amount;  //金额变化
    private Integer bizId;

    /**
     * 支付流水的appId
     */
    private String appId;

    /**
     * 记账日
     * "2024-01-08"
     */
    private String bookkeepingDate;
    private String businessNo;
    private Long id;
    /**
     * 用户标识或组织标识
     */
    private String identity;
    /**
     * lizhi_ppapp_live - PP约玩  lizhi_heiye_common-黑叶 lizhi_ximi_common-西米
     */
    private String tenantCode;

    /**
     * 流水时间
     */
    private String tradeTime;

}
