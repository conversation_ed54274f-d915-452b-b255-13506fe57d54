package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.dao;

import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityLevelConfig;
import fm.lizhi.ocean.wavecenter.datastore.background.mapper.ActivityLevelConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityLevelDao {

    @Autowired
    public ActivityLevelConfigMapper activityLevelConfigMapper;

    /**
     * 获取活动等级配置
     *
     * @param id 活动等级配置id
     * @return 活动等级配置
     */
    public ActivityLevelConfig getActivityLevelConfig(Long id) {
        if (id == null) {
            return null;
        }
        ActivityLevelConfig getById = new ActivityLevelConfig();
        getById.setId(id);
        return activityLevelConfigMapper.selectByPrimaryKey(getById);
    }
}
