package fm.lizhi.ocean.wavecenter.infrastructure.sign.manager;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.common.datastore.redis.client.RedisClient;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.SignFlowBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.FlowConfirmStatusEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.constants.SignRedisKey;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.convert.SignFlowInfraConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.convert.SignInfraConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity.WcSignFlow;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity.WcSignFlowExample;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity.WcSignStatusSync;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity.WcSignStatusSyncExample;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.mapper.WcSignFlowMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.mapper.WcSignStatusSyncMapper;
import fm.lizhi.ocean.wavecenter.service.sign.dto.QueryFlowParamDTO;
import fm.lizhi.ocean.wavecenter.service.sign.dto.QuerySignStatusSyncDTO;
import fm.lizhi.ocean.wavecenter.service.sign.dto.SignStatusSyncDTO;
import fm.lizhi.ocean.wavecenter.service.sign.manager.SignFlowManager;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import redis.clients.jedis.params.SetParams;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/10 14:44
 */
@Component
public class SignFlowManagerImpl implements SignFlowManager {

    @Autowired
    private WcSignFlowMapper signFlowMapper;
    @Autowired
    private WcSignStatusSyncMapper signStatusSyncMapper;

    @Resource(name = "redis_ocean_wavecenter")
    private RedisClient redisClient;

    @Override
    public Optional<SignFlowBean> queryContractFlowBySignId(Long contractId, String type, Integer hasContract) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        WcSignFlowExample example = new WcSignFlowExample();
        example.createCriteria()
                .andTypeEqualTo(type)
                .andAppIdEqualTo(appId)
                .andHasContractEqualTo(hasContract)
                .andContractIdEqualTo(contractId);

        List<WcSignFlow> list = signFlowMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return Optional.empty();
        }

        return Optional.ofNullable(SignFlowInfraConvert.I.po2Bean(list.get(0)));
    }

    @Override
    public Long addSignFlow(SignFlowBean signFlowBean) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        WcSignFlow entity = new WcSignFlow();
        entity.setContractId(signFlowBean.getContractId());
        entity.setStatus(signFlowBean.getStatus());
        entity.setType(signFlowBean.getType());
        entity.setHasContract(signFlowBean.getHasContract());
        entity.setCreateTime(new Date());
        entity.setAppId(appId);

        signFlowMapper.insert(entity);
        return entity.getId();
    }

    @Override
    public void changeStatus(Long flowId, String status) {
        WcSignFlow entity = new WcSignFlow();
        entity.setId(flowId);
        entity.setStatus(status);
        signFlowMapper.updateByPrimaryKey(entity);
    }

    @Override
    public void changeConfirmStatus(Long flowId, String confirmStatus) {
        WcSignStatusSync entity = new WcSignStatusSync();
        entity.setId(flowId);
        entity.setConfirmStatus(confirmStatus);
        entity.setModifyTime(new Date());
        signStatusSyncMapper.updateByPrimaryKey(entity);
    }

    @Override
    public PageBean<SignFlowBean> queryFlow(QueryFlowParamDTO param) {
        return null;
    }

    @Override
    public PageBean<SignStatusSyncDTO> querySignStatusSync(QuerySignStatusSyncDTO param) {
        WcSignStatusSyncExample example = new WcSignStatusSyncExample();
        WcSignStatusSyncExample.Criteria criteria = example.createCriteria();

        if (CollectionUtils.isNotEmpty(param.getConfirmStatuses())) {
            List<String> valueList = param.getConfirmStatuses().stream().map(FlowConfirmStatusEnum::getCode).collect(Collectors.toList());
            criteria.andConfirmStatusIn(valueList);
        }

        if (param.getType() != null) {
            criteria.andTypeEqualTo(param.getType().getCode());
        }

        if (param.getAppId() != null) {
            criteria.andAppIdEqualTo(param.getAppId());
        }

        if (param.getContractId() != null) {
            criteria.andContractIdEqualTo(param.getContractId());
        }

        if (param.getRole() != null) {
            criteria.andCreateRoleEqualTo(param.getRole().getRoleCode());
        }

        PageList<WcSignStatusSync> pageList = signStatusSyncMapper.pageByExample(example, param.getPageNo(), param.getPageSize());
        return PageBean.of(pageList.getTotal(), SignInfraConvert.I.signStatusSyncPos2Dtos(pageList));
    }

    @Override
    public void addSignStatusSync(SignStatusSyncDTO dto) {
        WcSignStatusSyncExample example = new WcSignStatusSyncExample();
        example.createCriteria()
                .andAppIdEqualTo(dto.getAppId())
                .andCreateRoleEqualTo(dto.getCreateRole())
                .andContractIdEqualTo(dto.getContractId());
        if (signStatusSyncMapper.countByExample(example) > 0) {
            LogContext.addResLog("sign status is exist");
            return;
        }

        signStatusSyncMapper.insert(SignFlowInfraConvert.I.statusSyncDto2Po(dto));
    }

    @Override
    public boolean lockFlow(Long flowId) {
        String key = SignRedisKey.SYNC_FLOW.getKey(flowId);
        String result = redisClient.set(key
                , String.valueOf(System.currentTimeMillis())
                , SetParams.setParams().ex(5).nx());
        return "OK".equals(result);
    }

    @Override
    public void unlockFlow(Long flowId) {
        redisClient.del(SignRedisKey.SYNC_FLOW.getKey(flowId));
    }
}
