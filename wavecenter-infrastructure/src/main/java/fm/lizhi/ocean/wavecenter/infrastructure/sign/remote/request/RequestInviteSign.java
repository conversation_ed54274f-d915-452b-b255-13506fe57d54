package fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.request;

import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

/**
 * <AUTHOR>
 * @date 2024/10/12 17:40
 */
@Getter
@Builder
public class RequestInviteSign {

    /**
     * 申请用户ID
     */
    @NonNull
    private Long curUserId;

    /**
     * 目标用户ID
     */
    @NonNull
    private Long targetUserId;

    /**
     * 发起角色
     */
    @NonNull
    private RoleEnum opRole;

}
