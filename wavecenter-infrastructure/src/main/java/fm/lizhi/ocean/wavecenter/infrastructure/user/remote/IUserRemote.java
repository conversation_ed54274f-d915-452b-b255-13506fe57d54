package fm.lizhi.ocean.wavecenter.infrastructure.user.remote;

import fm.lizhi.ocean.wavecenter.base.remote.IRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.req.GetUserVerifyDataReq;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.req.GetUserWithdrawStatusReq;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.res.GetUserVerifyDataRes;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.res.GetUserWithdrawStatusRes;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserInfoDto;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserMediaDto;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/4/10 15:06
 */
public interface IUserRemote extends IRemote {

    List<SimpleUserDto> getSimpleUserByIds(List<Long> ids);

    /**
     * 获取用户实名状态
     *
     * @param param
     * @return
     */
    Optional<GetUserVerifyDataRes> getUserVerifyDataResult(GetUserVerifyDataReq param);

    /**
     * 获取用户封禁状态
     *
     * @param userId
     * @return
     */
    boolean getUserBanStatus(Long userId);

    /**
     * 通过业务token获取用户ID
     *
     * @param businessToken
     * @return
     */
    Optional<Long> getUserIdByBusinessToken(String businessToken);

    /**
     * 通过业务波段号获取用户信息
     *
     * @param band
     * @return
     */
    Optional<SimpleUserDto> getUserInfoByBand(String band);

    /**
     * 查询用户信息
     * @param userIds
     * @return
     */
    List<UserInfoDto> getUserInfoByIds(List<Long> userIds);

    /**
     * 查询用户信息
     * @param userId
     * @return
     */
    Optional<UserInfoDto> getUserInfoById(long userId);

    /**
     * 查询用户媒体信息
     * @param userId
     * @return
     */
    Optional<UserMediaDto> getUserMediaById(long userId);

    /**
     * 查询用户封禁状态
     * @param userId
     * @return
     */
    int getUserBandStatus(Long userId);

    /**
     * 是否完成主播中心认证
     * @param userId
     * @return
     */
    boolean finishPlayerCenterAuth(Long userId);

    /**
     * 根据关键词搜索用户
     * @param userId
     * @param userName
     * @return
     */
    Optional<SimpleUserDto> getUserByKeyWord(Long userId, String userName);

    /**
     * 查询用户注销状态
     * @param param 查询参数
     * @return 注销状态响应体
     *   userId: 用户ID
     *   note: 提示语
     *   withdraw: 是否处于注销申请中
     */
    Optional<GetUserWithdrawStatusRes> getUserWithdrawStatus(GetUserWithdrawStatusReq param);

}
