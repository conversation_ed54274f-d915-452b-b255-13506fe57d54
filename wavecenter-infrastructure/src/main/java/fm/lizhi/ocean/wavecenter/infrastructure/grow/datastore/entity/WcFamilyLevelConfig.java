package fm.lizhi.ocean.wavecenter.infrastructure.grow.datastore.entity;

import lombok.*;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 公会等级配置表
 *
 * @date 2025-04-07 02:42:29
 */
@Table(name = "`wavecenter_family_level_config`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcFamilyLevelConfig {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 等级名称
     */
    @Column(name= "`level_name`")
    private String levelName;

    /**
     * 等级值
     */
    @Column(name= "`level_value`")
    private Integer levelValue;

    /**
     * 最小经验值
     */
    @Column(name= "`min_exp`")
    private Integer minExp;

    /**
     * 角标
     */
    @Column(name= "`level_icon`")
    private String levelIcon;

    /**
     * 勋章
     */
    @Column(name= "`level_medal`")
    private String levelMedal;

    /**
     * 主题色
     */
    @Column(name= "`them_color`")
    private String themColor;

    /**
     * 底色
     */
    @Column(name= "`background_color`")
    private String backgroundColor;

    /**
     * 业务 ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 服务部署环境, TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 最近操作人
     */
    @Column(name= "`modify_user`")
    private String modifyUser;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`update_time`")
    private Date updateTime;

    /**
     * 0=未删除，1=已删除
     */
    @Column(name= "`deleted`")
    private Integer deleted;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", levelName=").append(levelName);
        sb.append(", levelValue=").append(levelValue);
        sb.append(", minExp=").append(minExp);
        sb.append(", levelIcon=").append(levelIcon);
        sb.append(", levelMedal=").append(levelMedal);
        sb.append(", themColor=").append(themColor);
        sb.append(", backgroundColor=").append(backgroundColor);
        sb.append(", appId=").append(appId);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", modifyUser=").append(modifyUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", deleted=").append(deleted);
        sb.append("]");
        return sb.toString();
    }
}