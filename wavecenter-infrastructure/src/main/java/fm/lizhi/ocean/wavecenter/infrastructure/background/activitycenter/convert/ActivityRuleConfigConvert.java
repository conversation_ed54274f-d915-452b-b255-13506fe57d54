package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityRuleBaseAbstractBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityRuleConfigBean;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityRuleConfig;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        imports = {ActivityRuleBaseAbstractBean.class}
)
public interface ActivityRuleConfigConvert {
    ActivityRuleConfigConvert I = Mappers.getMapper(ActivityRuleConfigConvert.class);


    ActivityRuleConfigBean convertActivityRuleConfigBean(ActivityRuleConfig config);
    List<ActivityRuleConfigBean> convertActivityRuleConfigBeans(List<ActivityRuleConfig> configs);


    default Long dateToLong(Date date) {
        return date.getTime();
    }
}
