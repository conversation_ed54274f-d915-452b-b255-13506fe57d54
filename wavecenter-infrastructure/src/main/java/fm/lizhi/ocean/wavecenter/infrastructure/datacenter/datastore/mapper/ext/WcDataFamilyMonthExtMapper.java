package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.WcDataFamilyMonthStatPo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcDataFamilyMonthExtMapper {

    /**
     * 查询公会月统计数据
     *
     * @param familyId   公会ID
     * @param appId      业务ID
     * @param startMonth 开始月份
     * @param pageSize   每页条数
     * @return 公会月统计数据列表
     */
    @Select("SELECT stat_year, stat_month, " +
            "personal_noble_income as playerVip, " +
            "official_hall_income as officialRoom, " +
            "personal_hall_income as player, " +
            "all_income as sum, " +
            "sign_hall_income as roomGift, " +
            "noble_income as roomVip " +
            "FROM wavecenter_data_family_month " +
            "WHERE family_id = #{familyId} AND app_id = #{appId} " +
            "AND stat_month < #{startMonth} " +
            " order by stat_month desc limit #{pageSize}")
    List<WcDataFamilyMonthStatPo> queryMonthStatsByTime(@Param("familyId") Long familyId,
                                                        @Param("appId") Integer appId,
                                                        @Param("startMonth") Integer startMonth,
                                                        @Param("pageSize") Integer pageSize);

}
