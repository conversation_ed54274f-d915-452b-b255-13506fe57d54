package fm.lizhi.ocean.wavecenter.infrastructure.income.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.infrastructure.income.datastore.po.PlayerPayCountPo;
import fm.lizhi.ocean.wavecenter.infrastructure.income.dto.PlayerPayCountParamDto;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/23 14:59
 */
@DataStore(namespace = "mysql_heiye_ppdata_r")
public interface HyFamilyFlowStatMapper {

    /**
     * 查询有收入主播数
     * @param param
     * @param stateDayStart yyyy-MM-dd
     * @param stateDayEnd yyyy-MM-dd
     * @return
     */
    @Select({
            "<script>"
            , "select count(distinct user_id) from family_flow_stat where flow>0 "
            , "<if test=' null != param.familyId and param.familyId > 0 '>"
            , "and family_id=#{param.familyId} "
            , "</if>"
            , "<if test=' null != param.roomId and param.roomId > 0 '>"
            , "and nj_id=#{param.roomId} "
            , "</if>"
            , "and stat_day&gt;=#{stateDayStart} "
            , "and stat_day&lt;=#{stateDayEnd} "
            , "</script>"
    })
    Integer getPlayerPayCount(@Param("param") PlayerPayCountParamDto param
            , @Param("stateDayStart") String stateDayStart
            , @Param("stateDayEnd") String stateDayEnd
    );

    @Select({
            "<script>"
            , "select nj_id, count(distinct user_id) totalCount from family_flow_stat where flow>0 "
            , "<if test=' null != param.familyId and param.familyId > 0 '>"
            , "and family_id=#{param.familyId} "
            , "</if>"
            , "<if test=' null != param.roomIds and param.roomIds.size > 0 '>"
            , "and nj_id in "
            , "<foreach collection='param.roomIds' item='rId' open='(' separator=',' close=')'>"
            , "#{rId}"
            , "</foreach>"
            , "</if>"
            , "<if test=' null != param.playerIds and param.playerIds.size > 0 '>"
            , "and user_id in "
            , "<foreach collection='param.playerIds' item='pId' open='(' separator=',' close=')'>"
            , "#{pId}"
            , "</foreach>"
            , "</if>"
            , "and stat_day&gt;=#{stateDayStart} "
            , "and stat_day&lt;=#{stateDayEnd} "
            , "group by nj_id "
            , "</script>"
    })
    List<PlayerPayCountPo> getPlayerPayCountByRooms(@Param("param") PlayerPayCountParamDto param
            , @Param("stateDayStart") String stateDayStart
            , @Param("stateDayEnd") String stateDayEnd
    );


    @Select({
            "<script>"
            , "select count(distinct user_id) totalCount from family_flow_stat where flow>0 "
            , "<if test=' null != param.familyId and param.familyId > 0 '>"
            , "and family_id=#{param.familyId} "
            , "</if>"
            , "<if test=' null != param.roomIds and param.roomIds.size > 0 '>"
            , "and nj_id in "
            , "<foreach collection='param.roomIds' item='rId' open='(' separator=',' close=')'>"
            , "#{rId}"
            , "</foreach>"
            , "</if>"
            , "<if test=' null != param.playerIds and param.playerIds.size > 0 '>"
            , "and user_id in "
            , "<foreach collection='param.playerIds' item='pId' open='(' separator=',' close=')'>"
            , "#{pId}"
            , "</foreach>"
            , "</if>"
            , "and stat_day&gt;=#{stateDayStart} "
            , "and stat_day&lt;=#{stateDayEnd} "
            , "group by family_id "
            , "</script>"
    })
    Integer getPlayerPayCountForFamily(@Param("param") PlayerPayCountParamDto param
            , @Param("stateDayStart") String stateDayStart
            , @Param("stateDayEnd") String stateDayEnd
    );
}
