package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class FamilyDateDataPo {

    /**
     * 公会ID
     */
    private Long familyId;

    /**
     * 公会收入,公会考核期间总收入，单位：钻（结算币)
     */
    private BigDecimal income;

    /**
     * 公会魅力值	公会考核期间总魅力值，单位：魅力值
     */
    private Integer charm;

    /**
     * 签约厅数	公会签约厅数
     */
    private Integer signRoomCnt;

    /**
     * 开播厅数	公会开播厅数
     */
    private Integer openRoomCnt;

    /**
     * 厅均收入 公会收入/开播厅数
     */
    private BigDecimal roomAvgIncome;

    /**
     * 厅均魅力值 公会魅力值/开播厅数
     */
    private BigDecimal roomAvgCharm;

    /**
     * 签约主播数
     */
    private Integer signPlayerCnt;

    /**
     * 上麦主播数 直播间上麦人数
     */
    private Integer upGuestPlayerCnt;

    /**
     * 有收入主播数 直播间有收入签约主播人数
     */
    private Integer incomePlayerCnt;

    /**
     * 人均收入 公会收入/有收入主播数
     */
    private BigDecimal playerAvgIncome;

    /**
     * 人均魅力值 公会魅力值/有收入主播数
     */
    private BigDecimal playerAvgCharm;
}
