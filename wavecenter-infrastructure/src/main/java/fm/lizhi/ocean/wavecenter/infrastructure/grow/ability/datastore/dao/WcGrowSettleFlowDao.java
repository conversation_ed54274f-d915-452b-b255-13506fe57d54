package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.dao;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.convert.GrowSettleFlowConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowSettleFlow;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowSettleFlowExample;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.mapper.WcGrowSettleFlowMapper;
import fm.lizhi.ocean.wavecenter.service.grow.ability.constants.AbilityConstant;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.PlayerSettleFlowDTO;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.SettlePeriodDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/10 10:51
 */
@Repository
public class WcGrowSettleFlowDao {

    @Autowired
    private WcGrowSettleFlowMapper settleFlowMapper;

    public WcGrowSettleFlow getPlayerSettleFlow(SettlePeriodDTO dto, Integer appId, Integer type) {
        WcGrowSettleFlowExample example = new WcGrowSettleFlowExample();
        example.createCriteria()
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andTypeEqualTo(type)
                .andStartWeekDateEqualTo(dto.getStartDate())
                .andEndWeekDateEqualTo(dto.getEndDate())
                .andAppIdEqualTo(appId);

        List<WcGrowSettleFlow> list = settleFlowMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    public WcGrowSettleFlow getOrInitPlayerSettleFlow(SettlePeriodDTO dto, Integer appId, Integer flowType) {
        WcGrowSettleFlowExample example = new WcGrowSettleFlowExample();
        example.createCriteria()
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andTypeEqualTo(flowType)
                .andStartWeekDateEqualTo(dto.getStartDate())
                .andEndWeekDateEqualTo(dto.getEndDate())
                .andAppIdEqualTo(appId);

        List<WcGrowSettleFlow> list = settleFlowMapper.selectByExample(example);

        WcGrowSettleFlow flow;
        if (CollectionUtils.isEmpty(list)) {
            // 不存在则初始化新记录
            flow = WcGrowSettleFlow.builder()
                    .deployEnv(ConfigUtils.getEnvRequired().name())
                    .type(flowType)
                    .status(AbilityConstant.SETTLE_FLOW_STATUS_DOING)
                    .startWeekDate(dto.getStartDate())
                    .endWeekDate(dto.getEndDate())
                    .lastSettleId(0L)
                    .appId(appId)
                    .build();
            settleFlowMapper.insert(flow);
        } else {
            flow = list.get(0);
        }
        return flow;
    }

    public void updateSettleFlow(WcGrowSettleFlow po) {
        po.setModifyTime(new Date());
        settleFlowMapper.updateByPrimaryKey(po);
    }

}
