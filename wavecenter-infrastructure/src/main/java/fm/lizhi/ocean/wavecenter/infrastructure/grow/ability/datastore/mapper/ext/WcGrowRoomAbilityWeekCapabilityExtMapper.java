package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowRoomAbilityWeekCapability;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcGrowRoomAbilityWeekCapabilityExtMapper {

    @Select({
        "<script>",
        "SELECT *",
        "FROM wavecenter_grow_room_ability_week_capability",
        "WHERE deploy_env = #{deployEnv}",
        "  AND app_id = #{appId}",
        "  AND start_week_date = #{startDate}",
        "  AND end_week_date = #{endDate}",
        "<if test='familyId != null'>",
        "  AND family_id = #{familyId}",
        "</if>",
        "  AND id &gt; #{minId}",
        "ORDER BY id ASC",
        "LIMIT #{pageSize}",
        "</script>"
    })
    List<WcGrowRoomAbilityWeekCapability> getRoomAbilityWeekCapability(
        @Param("familyId") Long familyId,
        @Param("startDate") Date startDate,
        @Param("endDate") Date endDate,
        @Param("minId") Long minId,
        @Param("pageSize") Integer pageSize,
        @Param("appId") Integer appId,
        @Param("deployEnv") String deployEnv
    );
}