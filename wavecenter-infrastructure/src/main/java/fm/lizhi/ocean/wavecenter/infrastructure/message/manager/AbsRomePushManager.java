package fm.lizhi.ocean.wavecenter.infrastructure.message.manager;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.rome.push.api.RomePushService;
import fm.lizhi.commons.rome.push.dto.BroadcastResult;
import fm.lizhi.commons.rome.push.dto.RomeBroadcastMessage;
import fm.lizhi.commons.rome.push.dto.RomePushMessage;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.infrastructure.message.dto.PushDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2024/4/17 16:02
 */
@Slf4j
public abstract class AbsRomePushManager {

    public static final String businessEnv = "creatorCenter";

    public static final String businessWaveEnv = "creator";

    @Autowired
    protected RomePushService romePushService;

    public RomePushMessage buildDevicePushMessage() {
        RomePushMessage message = new RomePushMessage();
        message.setBusinessEnv(businessEnv);
        message.setTargetType(RomePushMessage.RomePushTargetType.DEVICE_ID);
        message.setPayloadId(String.valueOf(System.currentTimeMillis()));
        return message;
    }

    /**
     * 推送到web站
     *
     * @param topic   topic
     * @param pushDTO 数据结构
     */
    public void pushWaveCenterMessage(String topic, PushDTO<?> pushDTO) {
        basePushMessage(topic, pushDTO, businessEnv);
    }

    /**
     * 推送到PC助手
     *
     * @param topic   topic
     * @param pushDTO 推送的数据结构
     */
    public void pushWaveMessage(String topic, PushDTO<?> pushDTO) {
        basePushMessage(topic, pushDTO, businessWaveEnv);
    }

    private void basePushMessage(String topic, PushDTO<?> pushDTO, String businessEnv) {
        long payloadId = System.currentTimeMillis();
        String payload = JsonUtil.dumps(pushDTO);
        byte[] bytes = payload.getBytes(StandardCharsets.UTF_8);
        RomeBroadcastMessage broadcastMessage = new RomeBroadcastMessage();
        broadcastMessage.setBusinessEnv(businessEnv);
        broadcastMessage.setTopic(topic);
        broadcastMessage.setPayloadId(String.valueOf(payloadId));
        broadcastMessage.setPayload(bytes);

        Result<BroadcastResult> resp = romePushService.broadcastMessage(broadcastMessage);
        if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("pushMessage failed to request - topic:{}, payload:{}, payloadId:{}, rCode:{}",
                    topic, payload, payloadId, resp.rCode());
            return;
        }

        long total = resp.target().getTotal();
        long successCnt = resp.target().getSuccessCnt();
        log.info("pushMessage successful request - topic:{}, payload:{}, payloadId:{}, total:{}, successCnt:{}",
                topic, payload, payloadId, total, successCnt);
    }
}
