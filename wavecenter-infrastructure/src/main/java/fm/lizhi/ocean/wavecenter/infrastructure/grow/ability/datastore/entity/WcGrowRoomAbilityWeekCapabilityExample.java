package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class WcGrowRoomAbilityWeekCapabilityExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_grow_room_ability_week_capability
     *
     * @mbg.generated Tue Jun 10 17:31:13 CST 2025
     */
    public WcGrowRoomAbilityWeekCapabilityExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_grow_room_ability_week_capability
     *
     * @mbg.generated Tue Jun 10 17:31:13 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_grow_room_ability_week_capability
     *
     * @mbg.generated Tue Jun 10 17:31:13 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_grow_room_ability_week_capability
     *
     * @mbg.generated Tue Jun 10 17:31:13 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_grow_room_ability_week_capability
     *
     * @mbg.generated Tue Jun 10 17:31:13 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_grow_room_ability_week_capability
     *
     * @mbg.generated Tue Jun 10 17:31:13 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_grow_room_ability_week_capability
     *
     * @mbg.generated Tue Jun 10 17:31:13 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_grow_room_ability_week_capability
     *
     * @mbg.generated Tue Jun 10 17:31:13 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_grow_room_ability_week_capability
     *
     * @mbg.generated Tue Jun 10 17:31:13 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_grow_room_ability_week_capability
     *
     * @mbg.generated Tue Jun 10 17:31:13 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_grow_room_ability_week_capability
     *
     * @mbg.generated Tue Jun 10 17:31:13 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Class getEntityClass() {
        return fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowRoomAbilityWeekCapability.class;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wavecenter_grow_room_ability_week_capability
     *
     * @mbg.generated Tue Jun 10 17:31:13 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andRoomIdIsNull() {
            addCriterion("room_id is null");
            return (Criteria) this;
        }

        public Criteria andRoomIdIsNotNull() {
            addCriterion("room_id is not null");
            return (Criteria) this;
        }

        public Criteria andRoomIdEqualTo(Long value) {
            addCriterion("room_id =", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotEqualTo(Long value) {
            addCriterion("room_id <>", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdGreaterThan(Long value) {
            addCriterion("room_id >", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdGreaterThanOrEqualTo(Long value) {
            addCriterion("room_id >=", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdLessThan(Long value) {
            addCriterion("room_id <", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdLessThanOrEqualTo(Long value) {
            addCriterion("room_id <=", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdIn(List<Long> values) {
            addCriterion("room_id in", values, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotIn(List<Long> values) {
            addCriterion("room_id not in", values, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdBetween(Long value1, Long value2) {
            addCriterion("room_id between", value1, value2, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotBetween(Long value1, Long value2) {
            addCriterion("room_id not between", value1, value2, "roomId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIsNull() {
            addCriterion("family_id is null");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIsNotNull() {
            addCriterion("family_id is not null");
            return (Criteria) this;
        }

        public Criteria andFamilyIdEqualTo(Long value) {
            addCriterion("family_id =", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotEqualTo(Long value) {
            addCriterion("family_id <>", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdGreaterThan(Long value) {
            addCriterion("family_id >", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("family_id >=", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdLessThan(Long value) {
            addCriterion("family_id <", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdLessThanOrEqualTo(Long value) {
            addCriterion("family_id <=", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIn(List<Long> values) {
            addCriterion("family_id in", values, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotIn(List<Long> values) {
            addCriterion("family_id not in", values, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdBetween(Long value1, Long value2) {
            addCriterion("family_id between", value1, value2, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotBetween(Long value1, Long value2) {
            addCriterion("family_id not between", value1, value2, "familyId");
            return (Criteria) this;
        }

        public Criteria andCapabilityCodeIsNull() {
            addCriterion("capability_code is null");
            return (Criteria) this;
        }

        public Criteria andCapabilityCodeIsNotNull() {
            addCriterion("capability_code is not null");
            return (Criteria) this;
        }

        public Criteria andCapabilityCodeEqualTo(String value) {
            addCriterion("capability_code =", value, "capabilityCode");
            return (Criteria) this;
        }

        public Criteria andCapabilityCodeNotEqualTo(String value) {
            addCriterion("capability_code <>", value, "capabilityCode");
            return (Criteria) this;
        }

        public Criteria andCapabilityCodeGreaterThan(String value) {
            addCriterion("capability_code >", value, "capabilityCode");
            return (Criteria) this;
        }

        public Criteria andCapabilityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("capability_code >=", value, "capabilityCode");
            return (Criteria) this;
        }

        public Criteria andCapabilityCodeLessThan(String value) {
            addCriterion("capability_code <", value, "capabilityCode");
            return (Criteria) this;
        }

        public Criteria andCapabilityCodeLessThanOrEqualTo(String value) {
            addCriterion("capability_code <=", value, "capabilityCode");
            return (Criteria) this;
        }

        public Criteria andCapabilityCodeLike(String value) {
            addCriterion("capability_code like", value, "capabilityCode");
            return (Criteria) this;
        }

        public Criteria andCapabilityCodeNotLike(String value) {
            addCriterion("capability_code not like", value, "capabilityCode");
            return (Criteria) this;
        }

        public Criteria andCapabilityCodeIn(List<String> values) {
            addCriterion("capability_code in", values, "capabilityCode");
            return (Criteria) this;
        }

        public Criteria andCapabilityCodeNotIn(List<String> values) {
            addCriterion("capability_code not in", values, "capabilityCode");
            return (Criteria) this;
        }

        public Criteria andCapabilityCodeBetween(String value1, String value2) {
            addCriterion("capability_code between", value1, value2, "capabilityCode");
            return (Criteria) this;
        }

        public Criteria andCapabilityCodeNotBetween(String value1, String value2) {
            addCriterion("capability_code not between", value1, value2, "capabilityCode");
            return (Criteria) this;
        }

        public Criteria andAbilityValueIsNull() {
            addCriterion("ability_value is null");
            return (Criteria) this;
        }

        public Criteria andAbilityValueIsNotNull() {
            addCriterion("ability_value is not null");
            return (Criteria) this;
        }

        public Criteria andAbilityValueEqualTo(BigDecimal value) {
            addCriterion("ability_value =", value, "abilityValue");
            return (Criteria) this;
        }

        public Criteria andAbilityValueNotEqualTo(BigDecimal value) {
            addCriterion("ability_value <>", value, "abilityValue");
            return (Criteria) this;
        }

        public Criteria andAbilityValueGreaterThan(BigDecimal value) {
            addCriterion("ability_value >", value, "abilityValue");
            return (Criteria) this;
        }

        public Criteria andAbilityValueGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("ability_value >=", value, "abilityValue");
            return (Criteria) this;
        }

        public Criteria andAbilityValueLessThan(BigDecimal value) {
            addCriterion("ability_value <", value, "abilityValue");
            return (Criteria) this;
        }

        public Criteria andAbilityValueLessThanOrEqualTo(BigDecimal value) {
            addCriterion("ability_value <=", value, "abilityValue");
            return (Criteria) this;
        }

        public Criteria andAbilityValueIn(List<BigDecimal> values) {
            addCriterion("ability_value in", values, "abilityValue");
            return (Criteria) this;
        }

        public Criteria andAbilityValueNotIn(List<BigDecimal> values) {
            addCriterion("ability_value not in", values, "abilityValue");
            return (Criteria) this;
        }

        public Criteria andAbilityValueBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ability_value between", value1, value2, "abilityValue");
            return (Criteria) this;
        }

        public Criteria andAbilityValueNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ability_value not between", value1, value2, "abilityValue");
            return (Criteria) this;
        }

        public Criteria andLastWeekAbilityValueIsNull() {
            addCriterion("last_week_ability_value is null");
            return (Criteria) this;
        }

        public Criteria andLastWeekAbilityValueIsNotNull() {
            addCriterion("last_week_ability_value is not null");
            return (Criteria) this;
        }

        public Criteria andLastWeekAbilityValueEqualTo(BigDecimal value) {
            addCriterion("last_week_ability_value =", value, "lastWeekAbilityValue");
            return (Criteria) this;
        }

        public Criteria andLastWeekAbilityValueNotEqualTo(BigDecimal value) {
            addCriterion("last_week_ability_value <>", value, "lastWeekAbilityValue");
            return (Criteria) this;
        }

        public Criteria andLastWeekAbilityValueGreaterThan(BigDecimal value) {
            addCriterion("last_week_ability_value >", value, "lastWeekAbilityValue");
            return (Criteria) this;
        }

        public Criteria andLastWeekAbilityValueGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("last_week_ability_value >=", value, "lastWeekAbilityValue");
            return (Criteria) this;
        }

        public Criteria andLastWeekAbilityValueLessThan(BigDecimal value) {
            addCriterion("last_week_ability_value <", value, "lastWeekAbilityValue");
            return (Criteria) this;
        }

        public Criteria andLastWeekAbilityValueLessThanOrEqualTo(BigDecimal value) {
            addCriterion("last_week_ability_value <=", value, "lastWeekAbilityValue");
            return (Criteria) this;
        }

        public Criteria andLastWeekAbilityValueIn(List<BigDecimal> values) {
            addCriterion("last_week_ability_value in", values, "lastWeekAbilityValue");
            return (Criteria) this;
        }

        public Criteria andLastWeekAbilityValueNotIn(List<BigDecimal> values) {
            addCriterion("last_week_ability_value not in", values, "lastWeekAbilityValue");
            return (Criteria) this;
        }

        public Criteria andLastWeekAbilityValueBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("last_week_ability_value between", value1, value2, "lastWeekAbilityValue");
            return (Criteria) this;
        }

        public Criteria andLastWeekAbilityValueNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("last_week_ability_value not between", value1, value2, "lastWeekAbilityValue");
            return (Criteria) this;
        }

        public Criteria andCompareWeekValueIsNull() {
            addCriterion("compare_week_value is null");
            return (Criteria) this;
        }

        public Criteria andCompareWeekValueIsNotNull() {
            addCriterion("compare_week_value is not null");
            return (Criteria) this;
        }

        public Criteria andCompareWeekValueEqualTo(BigDecimal value) {
            addCriterion("compare_week_value =", value, "compareWeekValue");
            return (Criteria) this;
        }

        public Criteria andCompareWeekValueNotEqualTo(BigDecimal value) {
            addCriterion("compare_week_value <>", value, "compareWeekValue");
            return (Criteria) this;
        }

        public Criteria andCompareWeekValueGreaterThan(BigDecimal value) {
            addCriterion("compare_week_value >", value, "compareWeekValue");
            return (Criteria) this;
        }

        public Criteria andCompareWeekValueGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("compare_week_value >=", value, "compareWeekValue");
            return (Criteria) this;
        }

        public Criteria andCompareWeekValueLessThan(BigDecimal value) {
            addCriterion("compare_week_value <", value, "compareWeekValue");
            return (Criteria) this;
        }

        public Criteria andCompareWeekValueLessThanOrEqualTo(BigDecimal value) {
            addCriterion("compare_week_value <=", value, "compareWeekValue");
            return (Criteria) this;
        }

        public Criteria andCompareWeekValueIn(List<BigDecimal> values) {
            addCriterion("compare_week_value in", values, "compareWeekValue");
            return (Criteria) this;
        }

        public Criteria andCompareWeekValueNotIn(List<BigDecimal> values) {
            addCriterion("compare_week_value not in", values, "compareWeekValue");
            return (Criteria) this;
        }

        public Criteria andCompareWeekValueBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("compare_week_value between", value1, value2, "compareWeekValue");
            return (Criteria) this;
        }

        public Criteria andCompareWeekValueNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("compare_week_value not between", value1, value2, "compareWeekValue");
            return (Criteria) this;
        }

        public Criteria andRoomInFamilyRankIsNull() {
            addCriterion("room_in_family_rank is null");
            return (Criteria) this;
        }

        public Criteria andRoomInFamilyRankIsNotNull() {
            addCriterion("room_in_family_rank is not null");
            return (Criteria) this;
        }

        public Criteria andRoomInFamilyRankEqualTo(Integer value) {
            addCriterion("room_in_family_rank =", value, "roomInFamilyRank");
            return (Criteria) this;
        }

        public Criteria andRoomInFamilyRankNotEqualTo(Integer value) {
            addCriterion("room_in_family_rank <>", value, "roomInFamilyRank");
            return (Criteria) this;
        }

        public Criteria andRoomInFamilyRankGreaterThan(Integer value) {
            addCriterion("room_in_family_rank >", value, "roomInFamilyRank");
            return (Criteria) this;
        }

        public Criteria andRoomInFamilyRankGreaterThanOrEqualTo(Integer value) {
            addCriterion("room_in_family_rank >=", value, "roomInFamilyRank");
            return (Criteria) this;
        }

        public Criteria andRoomInFamilyRankLessThan(Integer value) {
            addCriterion("room_in_family_rank <", value, "roomInFamilyRank");
            return (Criteria) this;
        }

        public Criteria andRoomInFamilyRankLessThanOrEqualTo(Integer value) {
            addCriterion("room_in_family_rank <=", value, "roomInFamilyRank");
            return (Criteria) this;
        }

        public Criteria andRoomInFamilyRankIn(List<Integer> values) {
            addCriterion("room_in_family_rank in", values, "roomInFamilyRank");
            return (Criteria) this;
        }

        public Criteria andRoomInFamilyRankNotIn(List<Integer> values) {
            addCriterion("room_in_family_rank not in", values, "roomInFamilyRank");
            return (Criteria) this;
        }

        public Criteria andRoomInFamilyRankBetween(Integer value1, Integer value2) {
            addCriterion("room_in_family_rank between", value1, value2, "roomInFamilyRank");
            return (Criteria) this;
        }

        public Criteria andRoomInFamilyRankNotBetween(Integer value1, Integer value2) {
            addCriterion("room_in_family_rank not between", value1, value2, "roomInFamilyRank");
            return (Criteria) this;
        }

        public Criteria andRoomInFamilyCompareWeekRankIsNull() {
            addCriterion("room_in_family_compare_week_rank is null");
            return (Criteria) this;
        }

        public Criteria andRoomInFamilyCompareWeekRankIsNotNull() {
            addCriterion("room_in_family_compare_week_rank is not null");
            return (Criteria) this;
        }

        public Criteria andRoomInFamilyCompareWeekRankEqualTo(Integer value) {
            addCriterion("room_in_family_compare_week_rank =", value, "roomInFamilyCompareWeekRank");
            return (Criteria) this;
        }

        public Criteria andRoomInFamilyCompareWeekRankNotEqualTo(Integer value) {
            addCriterion("room_in_family_compare_week_rank <>", value, "roomInFamilyCompareWeekRank");
            return (Criteria) this;
        }

        public Criteria andRoomInFamilyCompareWeekRankGreaterThan(Integer value) {
            addCriterion("room_in_family_compare_week_rank >", value, "roomInFamilyCompareWeekRank");
            return (Criteria) this;
        }

        public Criteria andRoomInFamilyCompareWeekRankGreaterThanOrEqualTo(Integer value) {
            addCriterion("room_in_family_compare_week_rank >=", value, "roomInFamilyCompareWeekRank");
            return (Criteria) this;
        }

        public Criteria andRoomInFamilyCompareWeekRankLessThan(Integer value) {
            addCriterion("room_in_family_compare_week_rank <", value, "roomInFamilyCompareWeekRank");
            return (Criteria) this;
        }

        public Criteria andRoomInFamilyCompareWeekRankLessThanOrEqualTo(Integer value) {
            addCriterion("room_in_family_compare_week_rank <=", value, "roomInFamilyCompareWeekRank");
            return (Criteria) this;
        }

        public Criteria andRoomInFamilyCompareWeekRankIn(List<Integer> values) {
            addCriterion("room_in_family_compare_week_rank in", values, "roomInFamilyCompareWeekRank");
            return (Criteria) this;
        }

        public Criteria andRoomInFamilyCompareWeekRankNotIn(List<Integer> values) {
            addCriterion("room_in_family_compare_week_rank not in", values, "roomInFamilyCompareWeekRank");
            return (Criteria) this;
        }

        public Criteria andRoomInFamilyCompareWeekRankBetween(Integer value1, Integer value2) {
            addCriterion("room_in_family_compare_week_rank between", value1, value2, "roomInFamilyCompareWeekRank");
            return (Criteria) this;
        }

        public Criteria andRoomInFamilyCompareWeekRankNotBetween(Integer value1, Integer value2) {
            addCriterion("room_in_family_compare_week_rank not between", value1, value2, "roomInFamilyCompareWeekRank");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateIsNull() {
            addCriterion("start_week_date is null");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateIsNotNull() {
            addCriterion("start_week_date is not null");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateEqualTo(Date value) {
            addCriterionForJDBCDate("start_week_date =", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("start_week_date <>", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateGreaterThan(Date value) {
            addCriterionForJDBCDate("start_week_date >", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("start_week_date >=", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateLessThan(Date value) {
            addCriterionForJDBCDate("start_week_date <", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("start_week_date <=", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateIn(List<Date> values) {
            addCriterionForJDBCDate("start_week_date in", values, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("start_week_date not in", values, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("start_week_date between", value1, value2, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("start_week_date not between", value1, value2, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateIsNull() {
            addCriterion("end_week_date is null");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateIsNotNull() {
            addCriterion("end_week_date is not null");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateEqualTo(Date value) {
            addCriterionForJDBCDate("end_week_date =", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("end_week_date <>", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateGreaterThan(Date value) {
            addCriterionForJDBCDate("end_week_date >", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("end_week_date >=", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateLessThan(Date value) {
            addCriterionForJDBCDate("end_week_date <", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("end_week_date <=", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateIn(List<Date> values) {
            addCriterionForJDBCDate("end_week_date in", values, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("end_week_date not in", values, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("end_week_date between", value1, value2, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("end_week_date not between", value1, value2, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(Integer value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(Integer value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(Integer value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(Integer value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(Integer value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<Integer> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<Integer> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(Integer value1, Integer value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(Integer value1, Integer value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIsNull() {
            addCriterion("deploy_env is null");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIsNotNull() {
            addCriterion("deploy_env is not null");
            return (Criteria) this;
        }

        public Criteria andDeployEnvEqualTo(String value) {
            addCriterion("deploy_env =", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotEqualTo(String value) {
            addCriterion("deploy_env <>", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvGreaterThan(String value) {
            addCriterion("deploy_env >", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvGreaterThanOrEqualTo(String value) {
            addCriterion("deploy_env >=", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLessThan(String value) {
            addCriterion("deploy_env <", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLessThanOrEqualTo(String value) {
            addCriterion("deploy_env <=", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLike(String value) {
            addCriterion("deploy_env like", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotLike(String value) {
            addCriterion("deploy_env not like", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIn(List<String> values) {
            addCriterion("deploy_env in", values, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotIn(List<String> values) {
            addCriterion("deploy_env not in", values, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvBetween(String value1, String value2) {
            addCriterion("deploy_env between", value1, value2, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotBetween(String value1, String value2) {
            addCriterion("deploy_env not between", value1, value2, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wavecenter_grow_room_ability_week_capability
     *
     * @mbg.generated do_not_delete_during_merge Tue Jun 10 17:31:13 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wavecenter_grow_room_ability_week_capability
     *
     * @mbg.generated Tue Jun 10 17:31:13 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}