package fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager.deliver.level;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardResourceDeliverTypeEnum;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardTypeEnum;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.DecorateInfoBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert.FamilyAwardDecorateConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager.deliver.DeliverResourceParam;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager.deliver.FamilyAwardDeliver;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager.deliver.GetResourceInfoResult;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.DecorateRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.request.RequestSendDecorate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 公会等级奖励座驾发放实现
 */
@Component
@Slf4j
public class FamilyLevelAwardVehicleDeliver implements FamilyAwardDeliver {

    @Autowired
    private DecorateRemote decorateRemote;

    @Override
    public boolean supports(FamilyAwardTypeEnum awardType, FamilyAwardResourceDeliverTypeEnum deliverType) {
        return awardType == FamilyAwardTypeEnum.LEVEL && deliverType == FamilyAwardResourceDeliverTypeEnum.VEHICLE;
    }

    @Override
    public Result<GetResourceInfoResult> getResourceInfo(DeliverResourceParam param) {
        try {
            Long resourceId = param.getResourceId();
            DecorateInfoBean infoBean = decorateRemote.getDecorateInfo(PlatformDecorateTypeEnum.VEHICLE, resourceId);
            if (infoBean == null) {
                return RpcResult.fail(CommonService.PARAM_ERROR, StringUtils.EMPTY);
            }
            return RpcResult.success(GetResourceInfoResult.of(infoBean.getDecorateName(), infoBean.getDecorateImage()));
        } catch (RuntimeException e) {
            log.info("Failed to get vehicle info, resourceId={}", param.getResourceId(), e);
            return RpcResult.fail(CommonService.INTERNAL_ERROR, e.getMessage());
        }
    }

    @Override
    public Result<Void> deliverResource(DeliverResourceParam param) {
        try {
            RequestSendDecorate request = FamilyAwardDecorateConvert.I.toRequestSendVehicle(param);
            log.info("Deliver vehicle, request={}", request);
            Result<Void> result = decorateRemote.sendDecorate(request);
            if (RpcResult.isFail(result)) {
                log.info("Deliver vehicle fail, request={}, rCode={}, message={}", request, result.rCode(), result.getMessage());
                return RpcResult.fail(result.rCode(), result.getMessage());
            }
            log.info("Deliver vehicle success, param={}", param);
            return RpcResult.success();
        } catch (RuntimeException e) {
            log.error("Deliver vehicle error, param={}", param, e);
            return RpcResult.fail(CommonService.INTERNAL_ERROR, e.getMessage());
        }
    }
}
