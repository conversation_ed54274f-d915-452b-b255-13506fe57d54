package fm.lizhi.ocean.wavecenter.infrastructure.income.singleton;

import fm.lizhi.common.dubbo.DubboClientBuilder;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.ServiceProviderConstants;
import fm.lizhi.trade.query.center.account.api.AccountQueryService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/4/24 18:16
 */
@Configuration
public class IncomeServiceProvider {

    @Bean
    public AccountQueryService accountQueryService(){
        return new DubboClientBuilder<>(AccountQueryService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

}
