package fm.lizhi.ocean.wavecenter.infrastructure.common;

import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.constants.MetricsConstants;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.constants.MetricsMeta;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.constants.MetricsNs;
import org.springframework.util.ReflectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/4/25 20:18
 */
public class MetricsUtil {

    public static Map<String, String> convertToResultMap(List<String> valueMetrics, Map<String, String> baseValueMap){
        Map<String, String> result = new HashMap<>();
        for (String valueMetric : valueMetrics) {
            Optional<MetricsMeta> metricsMetaOp = MetricsConstants.getMetricsMeta(valueMetric);
            if (!metricsMetaOp.isPresent()) {
                String valueStr = baseValueMap.get(valueMetric);
                result.put(valueMetric, valueStr==null ? "0" : valueStr);
                continue;
            }
            MetricsMeta metricsMeta = metricsMetaOp.get();
            String valueStr = metricsMeta.getValueFactory().calculateValue(metricsMeta, baseValueMap);
            result.put(metricsMeta.getAliasName(), valueStr==null ? "0" : valueStr);
        }
        return result;
    }

    public static Map<String, String> convertToResultMap(MetricsNs ns, List<String> valueMetrics, Map<String, String> baseValueMap){
        Map<String, String> result = new HashMap<>();
        for (String valueMetric : valueMetrics) {
            Optional<MetricsMeta> nsMetricsMetaOp = MetricsConstants.getMetricsMeta(ns, valueMetric);
            if (!nsMetricsMetaOp.isPresent()) {
                String valueStr = baseValueMap.get(valueMetric);
                result.put(valueMetric, valueStr==null ? "0" : valueStr);
                continue;
            }
            MetricsMeta meta = nsMetricsMetaOp.get();
            String valueStr = meta.getValueFactory().calculateValue(meta, baseValueMap);
            //使用别名
            result.put(meta.getName(), valueStr==null ? "0" : valueStr);
        }
        return result;
    }

    public static Map<String, String> convertToMap(Class<?> clazz, Object value){
        if (value == null) {
            return new HashMap<>();
        }

        Map<String, String> valueMap = new HashMap<>();
        ReflectionUtils.doWithLocalFields(clazz, field -> {
            ReflectionUtils.makeAccessible(field);
            String name = field.getName();
            Object valueObj = ReflectionUtils.getField(field, value);
            valueMap.put(name, String.valueOf(valueObj));
        });
        return valueMap;
    }

}
