package fm.lizhi.ocean.wavecenter.infrastructure.user.manager;

import cn.hutool.core.util.NumberUtil;
import fm.lizhi.common.datastore.redis.client.RedisClient;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.user.export.api.model.RiskInfo;
import fm.lizhi.ocean.wave.user.export.api.param.LoginOutPostProcessorParam;
import fm.lizhi.ocean.wave.user.export.api.param.LoginPostProcessorParam;
import fm.lizhi.ocean.wave.user.export.api.service.UserService;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserTokenBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.EnvUtil;
import fm.lizhi.ocean.wavecenter.common.utils.WcAssert;
import fm.lizhi.ocean.wavecenter.infrastructure.user.constants.UserRedisKey;
import fm.lizhi.ocean.wavecenter.infrastructure.user.po.AccessTokenInfoPo;
import fm.lizhi.ocean.wavecenter.infrastructure.user.po.RefreshTokenInfoPo;
import fm.lizhi.ocean.wavecenter.service.user.constants.UserConstant;
import fm.lizhi.ocean.wavecenter.service.user.dto.*;
import fm.lizhi.ocean.wavecenter.service.user.manager.FirstLoginRecordManager;
import fm.lizhi.ocean.wavecenter.service.user.manager.LoginManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/4/12 17:40
 */
@Slf4j
@Component
public class LoginManagerImpl implements LoginManager {

    @Resource(name = "redis_ocean_wavecenter")
    private RedisClient redisClient;

    @Autowired
    private UserService userService;

    @Autowired
    private UserRedisManager userRedisManager;

    @Autowired
    private FirstLoginRecordManager firstLoginRecordManager;

    @Override
    public void resetLoginRole(int appId, long userId, String deviceId) {
        WcAssert.hasText(deviceId, "deviceId is empty");
        Optional<String> oldAccessTokenOp = userRedisManager.getAccessToken(appId, userId, deviceId);
        if (!oldAccessTokenOp.isPresent()) {
            return;
        }

        //查询出旧的信息
        String oldAccessToken = oldAccessTokenOp.get();
        RefreshTokenInfoPo rePo = userRedisManager.getRefreshTokenInfoByAc(oldAccessToken);
        Long roleConfigId = rePo.getRoleConfigId();

        if (roleConfigId != null) {
            log.info("clearn roleConfigId");
            String key = UserRedisKey.USER_ROLE_ACCESS_TOKEN.getKey(appId, userId, deviceId, roleConfigId);
            redisClient.del(key);
        }

        AccessTokenInfoPo accessTokenInfo = userRedisManager.getAccessTokenInfo(oldAccessToken);
        if (StringUtils.isNotBlank(accessTokenInfo.getRefreshToken())) {
            log.info("clearn role");
            Integer ttl = userRedisManager.getRefreshTokenTtl(accessTokenInfo.getRefreshToken());
            rePo.setLoginType(null);
            rePo.setRoleConfigId(null);
            rePo.setRoleCode(null);
            rePo.setSubjectId(null);
            redisClient.setex(UserRedisKey.REFRESH_TOKEN_REF_INFO.getKey(accessTokenInfo.getRefreshToken())
                    , ttl, rePo.toJson());
        }

    }

    @Override
    public void deleteAllLoginInfo(int appId, long userId, String deviceId) {
        WcAssert.hasText(deviceId, "deviceId is empty");
        Optional<String> oldAccessTokenOp = userRedisManager.getAccessToken(appId, userId, deviceId);
        if (!oldAccessTokenOp.isPresent()) {
            return;
        }

        //查询出旧的信息
        String oldAccessToken = oldAccessTokenOp.get();
        AccessTokenInfoPo acPo = userRedisManager.getAccessTokenInfo(oldAccessToken);

        //获取refreshToken信息
        String oldRefreshToken = acPo.getRefreshToken();
        RefreshTokenInfoPo refreshTokenInfoPo = userRedisManager.getRefreshTokenInfo(oldRefreshToken);

        //删除用户关联角色
        if (refreshTokenInfoPo.getRoleConfigId() != null) {
            userRedisManager.deleteRole(appId, userId, deviceId, refreshTokenInfoPo.getRoleConfigId());
        }
        //删除设备
        userRedisManager.removeDevice(appId, userId, deviceId);
        //删除refreshTokenInfo
        userRedisManager.deleteRefreshTokenInfo(oldRefreshToken);
        //删除accessTokenInfo
        userRedisManager.deleteAccessTokenInfo(oldAccessToken);
        //删除用户token
        userRedisManager.deleteAccessToken(appId, userId, deviceId);
    }

    @Override
    public void deleteAllLoginInfo(int appId, long userId) {
        Set<String> userDevices = this.getUserDevice(appId, userId);
        if (CollectionUtils.isEmpty(userDevices)) {
            return;
        }

        log.info("userDevices={}", JsonUtil.dumps(userDevices));
        for (String userDevice : userDevices) {
            this.deleteAllLoginInfo(appId, userId, userDevice);
        }
    }

    @Override
    public void deleteToken(String accessToken, String refreshToken) {
        WcAssert.hasText(refreshToken, "refreshToken is empty");
        WcAssert.hasText(accessToken, "accessToken is empty");
        //删除refreshToken
        userRedisManager.deleteRefreshTokenInfo(refreshToken);
        //删除accessToken
        userRedisManager.deleteAccessTokenInfo(accessToken);
    }

    @Override
    public void saveToken(int appId, long userId, String deviceId, UserTokenBean userTokenBean) {
        WcAssert.hasText(deviceId, "deviceId is empty");

        String accessToken = userTokenBean.getAccessToken();
        WcAssert.hasText(accessToken, "accessToken is empty");

        Integer accessTokenExpiresIn = userTokenBean.getAccessTokenExpiresIn();
        WcAssert.notNull(accessTokenExpiresIn, "accessTokenExpiresIn is null");

        String refreshToken = userTokenBean.getRefreshToken();
        WcAssert.hasText(refreshToken, "refreshToken is empty");

        Integer refreshTokenExpiresIn = userTokenBean.getRefreshTokenExpiresIn();
        WcAssert.notNull(refreshTokenExpiresIn,  "refreshTokenExpiresIn is null");

        //保存accessToken
        redisClient.setex(UserRedisKey.USER_ADVICE_ACCESS_TOKEN.getKey(appId, userId, deviceId)
                , accessTokenExpiresIn, accessToken);

        String deviceKey = UserRedisKey.LOGIN_USER_DEVICE.getKey(appId, userId);
        redisClient.sadd(deviceKey, deviceId);
        //明确的key,过期时间单独设置也没有关系
        redisClient.expire(deviceKey, refreshTokenExpiresIn);

        //accessToken关联信息
        AccessTokenInfoPo accessTokenInfoPo = new AccessTokenInfoPo().setRefreshToken(refreshToken);
        redisClient.setex(UserRedisKey.ACCESS_TOKEN_REF_INFO.getKey(accessToken)
                , accessTokenExpiresIn, accessTokenInfoPo.toJson());

        //refreshToken关联信息
        RefreshTokenInfoPo refreshTokenInfoPo = new RefreshTokenInfoPo()
                .setAccessToken(accessToken)
                .setAppId(appId)
                .setUserId(userId)
                .setDeviceId(deviceId);
        redisClient.setex(UserRedisKey.REFRESH_TOKEN_REF_INFO.getKey(refreshToken)
                , refreshTokenExpiresIn, refreshTokenInfoPo.toJson());

    }

    @Override
    public void loginPostProcessor(UserInfoDto userInfoDto, LoginContextDto loginContextDto) {
        firstLoginRecordManager.addFirstLoginRecord(new FirstLoginRecordParamDTO().setAppId(Integer.valueOf(loginContextDto.getAppId())).setUserId(userInfoDto.getId()));
        LoginPostProcessorParam param = new LoginPostProcessorParam();
        param.setUserId(userInfoDto.getId());
        param.setRiskInfo(RiskInfo.builder()
                .register(loginContextDto.getRegister())
                .appId(loginContextDto.getAppId())
                .subAppId(loginContextDto.getSubAppId())
                .deviceId(loginContextDto.getDeviceId())
                .deviceType(loginContextDto.getDeviceType())
                .clientIp(loginContextDto.getClientIp())
                .clientVersion(loginContextDto.getClientVersion())
                .channelId(loginContextDto.getChannelId())
                .smId(loginContextDto.getSmId())
                .phoneNum(loginContextDto.getPhoneNum())
                .network(loginContextDto.getNetwork())
                .authAccountId(loginContextDto.getAuthAccountId())
                .riskAppId(loginContextDto.getRiskAppId())
                .build());
        Result<Void> result = userService.loginPostProcessor(param);
        if (RpcResult.isFail(result)) {
            log.warn("loginPostProcessor fail rCode={}, userInfoDto={}, loginContextDto={}"
                    , result.rCode(), JsonUtil.dumps(userInfoDto), JsonUtil.dumps(loginContextDto));
        }
    }

    @Override
    public void logoutPostProcessor(int appId, long userId) {
        LoginOutPostProcessorParam param = new LoginOutPostProcessorParam();
        param.setUserId(userId);
        Result<Void> result = userService.loginOutPostProcessor(param);
        if (RpcResult.isFail(result)) {
            log.warn("logoutPostProcessor fail rCode={}, userId={}", result.rCode(), userId);
        }
    }

    @Override
    public Optional<String> getAccessTokenByRefreshToken(String refreshToken) {
        if (StringUtils.isBlank(refreshToken)) {
            return Optional.empty();
        }
        RefreshTokenInfoPo refreshTokenInfoPo = userRedisManager.getRefreshTokenInfo(refreshToken);
        return Optional.ofNullable(refreshTokenInfoPo.getAccessToken());
    }

    @Override
    public Optional<TokenUserInfoDto> getUserByRefreshToken(String refreshToken) {
        if (StringUtils.isBlank(refreshToken)) {
            return Optional.empty();
        }
        RefreshTokenInfoPo po = userRedisManager.getRefreshTokenInfo(refreshToken);
        if (po.getUserId() == null || StringUtils.isBlank(po.getDeviceId())) {
            return Optional.empty();
        }
        return Optional.of(new TokenUserInfoDto().setUserId(po.getUserId()).setDeviceId(po.getDeviceId()));
    }

    @Override
    public void deleteQrCodeKey(int appId, String qrCodeKey) {
        WcAssert.hasText(qrCodeKey, "qrCodeKey is empty");
        String key = UserRedisKey.LOGIN_QRCODE_KEY.getKey(appId, qrCodeKey);
        Long result = redisClient.del(key);
        if (result == null) {
            log.error("deleteQrCodeKey error, appId={}, qrCodeKey={}", appId, qrCodeKey);
        }
    }

    @Override
    public void saveQrCodeKey(int appId, String qrCodeKey, int expireSeconds, int status) {
        WcAssert.hasText(qrCodeKey, "qrCodeKey is empty");
        String key = UserRedisKey.LOGIN_QRCODE_KEY.getKey(appId, qrCodeKey);
        String result = redisClient.setex(key, expireSeconds, String.valueOf(status));
        if (result == null) {
            log.error("saveQrCodeKey error, appId={}, qrCodeKey={}, expireSeconds={}, status={}", appId, qrCodeKey, expireSeconds, status);
        }
    }

    @Override
    public void refQrCodeKeyDevice(int appId, String qrCodeKey, int expireSeconds, String deviceId) {
        WcAssert.hasText(qrCodeKey, "qrCodeKey is empty");
        String key = UserRedisKey.QRCODE_REF_DEVICE.getKey(appId, qrCodeKey);
        String result = redisClient.setex(key, expireSeconds, deviceId);
        if (result == null) {
            log.error("refQrCodeKeyDevice error, appId={}, qrCodeKey={}, expireSeconds={}, deviceId={}", appId, qrCodeKey, expireSeconds, deviceId);
        }
    }

    @Override
    public Optional<String> getQrCodeKeyDevice(int appId, String qrCodeKey) {
        WcAssert.hasText(qrCodeKey, "qrCodeKey is empty");
        String key = UserRedisKey.QRCODE_REF_DEVICE.getKey(appId, qrCodeKey);
        return Optional.ofNullable(redisClient.get(key));
    }

    @Override
    public int qrCodeExpireSeconds(int appId, String qrCodeKey) {
        WcAssert.hasText(qrCodeKey, "qrCodeKey is empty");
        String key = UserRedisKey.LOGIN_QRCODE_KEY.getKey(appId, qrCodeKey);
        Long ttl = redisClient.ttl(key);
        return Math.toIntExact(ttl);
    }

    @Override
    public void refQrCodeAndToken(int appId, String qrCodeKey, UserTokenBean userTokenBean) {
        WcAssert.hasText(qrCodeKey, "qrCodeKey is empty");
        String key = UserRedisKey.QRCODE_REF_ACCESS_TOKEN.getKey(appId, qrCodeKey);
        redisClient.setex(key, userTokenBean.getAccessTokenExpiresIn(), userTokenBean.getAccessToken());
    }

    @Override
    public Optional<Integer> getQrCodeStatus(int appId, String qrCodeKey) {
        WcAssert.hasText(qrCodeKey, "qrCodeKey is empty");
        String key = UserRedisKey.LOGIN_QRCODE_KEY.getKey(appId, qrCodeKey);
        String status = redisClient.get(key);
        if (StringUtils.isBlank(status)) {
            return Optional.empty();
        }
        return Optional.of(Integer.valueOf(status));
    }

    @Override
    public Optional<UserTokenBean> getTokenByQrCodeKey(int appId, String qrCodeKey) {
        WcAssert.hasText(qrCodeKey, "qrCodeKey is empty");
        //查询accessToken
        String key = UserRedisKey.QRCODE_REF_ACCESS_TOKEN.getKey(appId, qrCodeKey);
        String accessToken = redisClient.get(key);
        if (StringUtils.isBlank(accessToken)) {
            LogContext.addResLog("accessToken is empty key={}", key);
            return Optional.empty();
        }

        //查询refreshToken
        AccessTokenInfoPo acPo = userRedisManager.getAccessTokenInfo(accessToken);
        if (StringUtils.isBlank(acPo.getRefreshToken())) {
            LogContext.addResLog("refreshToken is empty appId={},qrCodeKey={}", appId, qrCodeKey);
            return Optional.empty();
        }

        //查询token用户ID
        RefreshTokenInfoPo rePo = userRedisManager.getRefreshTokenInfo(acPo.getRefreshToken());
        if (rePo.getUserId() == null) {
            LogContext.addResLog("userId is empty appId={},qrCodeKey", appId, qrCodeKey);
            return Optional.empty();
        }

        return Optional.of(new UserTokenBean()
                .setAccessToken(accessToken)
                .setUserId(rePo.getUserId())
                .setRefreshToken(acPo.getRefreshToken())
        );
    }

    @Override
    public Optional<UserTokenBean> getToken(int appId, long userId, String deviceId) {
        WcAssert.hasText(deviceId, "deviceId is empty");
        String accessTokenKey = UserRedisKey.USER_ADVICE_ACCESS_TOKEN.getKey(appId, userId, deviceId);
        String accessToken = redisClient.get(accessTokenKey);
        if (StringUtils.isBlank(accessToken)) {
            return Optional.empty();
        }
        Long accessTokenTime = redisClient.ttl(accessTokenKey);

        AccessTokenInfoPo acPo = userRedisManager.getAccessTokenInfo(accessToken);
        if (StringUtils.isBlank(acPo.getRefreshToken())) {
            return Optional.empty();
        }

        Integer refreshTokenTime = userRedisManager.getRefreshTokenTtl(acPo.getRefreshToken());

        return Optional.of(new UserTokenBean()
                .setUserId(userId)
                .setAccessToken(accessToken)
                .setAccessTokenExpiresIn(Math.toIntExact(accessTokenTime))
                .setRefreshToken(acPo.getRefreshToken())
                .setRefreshTokenExpiresIn(refreshTokenTime)
        );
    }

    @Override
    public void refTokenRoleConfigId(int appId, UserTokenBean token, Long roleConfigId) {
        String refreshToken = token.getRefreshToken();
        WcAssert.hasText(refreshToken, "refreshToken is empty");
        Integer refreshTokenExpiresIn = token.getRefreshTokenExpiresIn();
        WcAssert.notNull(refreshTokenExpiresIn, "refreshTokenExpiresIn is empty");

        RefreshTokenInfoPo rePo = userRedisManager.getRefreshTokenInfo(refreshToken);
        rePo.setRoleConfigId(roleConfigId);
        String refreshRefKey = UserRedisKey.REFRESH_TOKEN_REF_INFO.getKey(refreshToken);
        redisClient.setex(refreshRefKey, refreshTokenExpiresIn, rePo.toJson());
    }

    @Override
    public void saveAccessTokenRole(int appId, long userId, String deviceId, Long roleConfigId, String accessToken, int accessTokenExpireSeconds) {
        WcAssert.hasText(deviceId, "deviceId is empty");
        WcAssert.notNull(roleConfigId, "roleConfigId is empty");
        String key = UserRedisKey.USER_ROLE_ACCESS_TOKEN.getKey(appId, userId, deviceId, roleConfigId);
        redisClient.setex(key, accessTokenExpireSeconds, accessToken);
    }

    @Override
    public Set<String> getUserDevice(int appId, long userId) {
        String deviceKey = UserRedisKey.LOGIN_USER_DEVICE.getKey(appId, userId);
        return redisClient.smembers(deviceKey);
    }

    @Override
    public Optional<Long> getUserRoleConfigId(int appId, long userId, String deviceId) {
        WcAssert.hasText(deviceId, "deviceId is empty");
        String key = UserRedisKey.USER_ADVICE_ACCESS_TOKEN.getKey(appId, userId, deviceId);
        String accessToken = redisClient.get(key);
        if (StringUtils.isBlank(accessToken)) {
            return Optional.empty();
        }

        RefreshTokenInfoPo rePo = userRedisManager.getRefreshTokenInfoByAc(accessToken);
        if (rePo.getRoleConfigId() == null) {
            return Optional.empty();
        }

        return Optional.of(rePo.getRoleConfigId());
    }

    @Override
    public Optional<Long> getUserIdByAccessToken(String accessToken) {
        if (StringUtils.isBlank(accessToken)) {
            return Optional.empty();
        }
        RefreshTokenInfoPo rePo = userRedisManager.getRefreshTokenInfoByAc(accessToken);
        if (rePo.getUserId() == null) {
            LogContext.addResLog("userId is empty accessToken={}", accessToken);
            return Optional.empty();
        }
        LogContext.addResLog("userId={}", rePo.getUserId());
        return Optional.of(rePo.getUserId());
    }

    @Override
    public Optional<LoginUserInfoDto> getUserByAccessToken(String accessToken) {
        if (StringUtils.isBlank(accessToken)) {
            return Optional.empty();
        }
        RefreshTokenInfoPo rePo = userRedisManager.getRefreshTokenInfoByAc(accessToken);
        if (rePo.getUserId() == null) {
            if (EnvUtil.isTestEnv() && NumberUtil.isNumber(accessToken)) {
                //测试环境,如果accessToken是数字，说明是测试环境的用户ID,直接当场userid使用
                return Optional.of(new LoginUserInfoDto()
                        .setAppId(rePo.getAppId())
                        .setUserId(Long.parseLong(accessToken))
                        .setDeviceId(rePo.getDeviceId()));
            }
            return Optional.empty();
        }
        return Optional.of(new LoginUserInfoDto()
                .setAppId(rePo.getAppId())
                .setUserId(rePo.getUserId())
                .setDeviceId(rePo.getDeviceId()));
    }

    @Override
    public void refTokenRole(LoginRoleExpiresInfoDto loginRoleExpiresInfoDto, String accessToken, String refreshToken) {
        WcAssert.hasText(accessToken, "accessToken is empty");
        WcAssert.hasText(refreshToken, "refreshToken is empty");
        RefreshTokenInfoPo rePo = userRedisManager.getRefreshTokenInfoByAc(accessToken);
        rePo.setRoleCode(loginRoleExpiresInfoDto.getRoleCode());
        rePo.setSubjectId(loginRoleExpiresInfoDto.getSubjectId());
        rePo.setRoleConfigId(loginRoleExpiresInfoDto.getRoleConfigId());
        rePo.setLoginType(loginRoleExpiresInfoDto.getLoginType());

        String key = UserRedisKey.REFRESH_TOKEN_REF_INFO.getKey(refreshToken);
        redisClient.setex(key, loginRoleExpiresInfoDto.getRefreshTokenExpiresIn(), rePo.toJson());
    }

    @Override
    public Optional<LoginRoleInfoDto> getAccessTokenRole(String accessToken) {
        WcAssert.hasText(accessToken, "accessToken is empty");
        RefreshTokenInfoPo rePo = userRedisManager.getRefreshTokenInfoByAc(accessToken);
        if (StringUtils.isBlank(rePo.getRoleCode()) || rePo.getSubjectId() == null || rePo.getRoleConfigId() == null) {
            return Optional.empty();
        }
        LoginRoleInfoDto loginRoleInfoDto = new LoginRoleInfoDto();
        loginRoleInfoDto.setRoleCode(rePo.getRoleCode());
        loginRoleInfoDto.setSubjectId(rePo.getSubjectId());
        loginRoleInfoDto.setRoleConfigId(rePo.getRoleConfigId());
        loginRoleInfoDto.setLoginType(rePo.getLoginType());
        return Optional.of(loginRoleInfoDto);
    }

    @Override
    public Optional<LoginRoleInfoDto> getRefreshTokenRole(String refreshToken) {
        WcAssert.hasText(refreshToken, "refreshToken is empty");
        RefreshTokenInfoPo rePo = userRedisManager.getRefreshTokenInfo(refreshToken);
        if (StringUtils.isBlank(rePo.getRoleCode()) || rePo.getSubjectId() == null || rePo.getRoleConfigId()==null) {
            log.info("getRefreshTokenRole rePo empty refreshToken={}", refreshToken);
            return Optional.empty();
        }

        LoginRoleInfoDto loginRoleInfoDto = new LoginRoleInfoDto();
        loginRoleInfoDto.setRoleCode(rePo.getRoleCode());
        loginRoleInfoDto.setSubjectId(rePo.getSubjectId());
        loginRoleInfoDto.setRoleConfigId(rePo.getRoleConfigId());
        loginRoleInfoDto.setLoginType(rePo.getLoginType());
        return Optional.of(loginRoleInfoDto);
    }

    @Override
    public Optional<String> getUserRoleCode(int appId, long userId, String deviceId) {
        Optional<String> accessTokenOp = userRedisManager.getAccessToken(appId, userId, deviceId);
        if (!accessTokenOp.isPresent()) {
            return Optional.empty();
        }
        RefreshTokenInfoPo rePo = userRedisManager.getRefreshTokenInfoByAc(accessTokenOp.get());
        return Optional.ofNullable(rePo.getRoleCode());
    }

    public int getLoginType(int appId, long userId, String deviceId){
        Optional<String> accessTokenOp = userRedisManager.getAccessToken(appId, userId, deviceId);
        if (!accessTokenOp.isPresent()) {
            return UserConstant.LoginType.SELF;
        }
        RefreshTokenInfoPo rePo = userRedisManager.getRefreshTokenInfoByAc(accessTokenOp.get());
        if (rePo.getLoginType() != null) {
            return rePo.getLoginType();
        }
        return UserConstant.LoginType.SELF;
    }
}
