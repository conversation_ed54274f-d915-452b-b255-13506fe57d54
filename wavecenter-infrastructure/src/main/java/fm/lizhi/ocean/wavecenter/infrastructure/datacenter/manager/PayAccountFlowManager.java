package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.manager;

import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.dao.WcPayAccountFlowDao;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.kafka.consumer.AccountFlowMsg;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @date 2025/4/22 18:17
 */
@Component
public class PayAccountFlowManager {

    @Autowired
    private WcPayAccountFlowDao payAccountFlowDao;

    /**
     * 保存支付流水消息
     * @param msg
     */
    public void saveAccountFlowMsg(AccountFlowMsg msg){
        payAccountFlowDao.saveAccountFlowMsg(msg);
    }

}
