package fm.lizhi.ocean.wavecenter.infrastructure.message.singleton;

import fm.lizhi.common.dubbo.DubboClientBuilder;
import fm.lizhi.commons.rome.push.api.RomePushService;
import fm.lizhi.live.room.hy.api.LiveNewService;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.ServiceProviderConstants;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/5/24 19:10
 */
@Configuration
public class MessageServiceProvider {

    @Bean
    public RomePushService romePushService(){
        return new DubboClientBuilder<>(RomePushService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

}
