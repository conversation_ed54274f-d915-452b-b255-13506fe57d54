package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.convert;

import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowSettleFlow;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.PlayerSettleFlowDTO;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.RoomSettleFlowDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2025/6/10 11:49
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface GrowSettleFlowConvert {

    GrowSettleFlowConvert I = Mappers.getMapper(GrowSettleFlowConvert.class);

    PlayerSettleFlowDTO po2DTO(WcGrowSettleFlow po);

    RoomSettleFlowDTO po2roomDTO(WcGrowSettleFlow po);

    @Mapping(target = "type", ignore = true)
    @Mapping(target = "modifyTime", ignore = true)
    @Mapping(target = "deployEnv", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    WcGrowSettleFlow dto2Po(PlayerSettleFlowDTO dto);

    @Mapping(target = "type", ignore = true)
    @Mapping(target = "modifyTime", ignore = true)
    @Mapping(target = "deployEnv", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    WcGrowSettleFlow roomDTO2Po(RoomSettleFlowDTO dto);

}
