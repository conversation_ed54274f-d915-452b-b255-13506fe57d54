package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote.pp;

import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.PlayerSignCharmSumPo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.PpLiveGiveGiftActionMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.PpPlayerSignCharmStatMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.AdminPlayerIncomeInfoPo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote.ICharmStatRemote;
import fm.lizhi.trade.query.center.account.api.AccountQueryService;
import fm.pp.family.api.FamilyService;
import fm.pp.family.api.PlayerSignService;
import fm.pp.family.protocol.PlayerSignServiceProto;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/18 20:31
 */
@Component
public class PpCharmStatRemote implements ICharmStatRemote {

    private static final Logger log = LoggerFactory.getLogger(PpCharmStatRemote.class);
    @Autowired
    private PpPlayerSignCharmStatMapper ppPlayerSignCharmStatMapper;

    @Autowired
    private PpLiveGiveGiftActionMapper ppLiveGiveGiftActionMapper;

    @Autowired
    private PlayerSignService playerSignService;
    @Autowired
    private FamilyService familyService;
    @Autowired
    private AccountQueryService accountQueryService;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public List<PlayerSignCharmSumPo> selectPlayerSignCharmSumByUsers(long njId, long familyId, List<Long> userIds, Date startDate, Date endDate) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        String startDateStr = DateUtil.formatDateToString(startDate, DateUtil.date_2);
        String endDateStr = DateUtil.formatDateToString(endDate, DateUtil.date_2);
        return ppPlayerSignCharmStatMapper.selectPlayerSignCharmSumByUsers(njId, familyId, userIds, startDateStr, endDateStr);
    }

    @Override
    public Integer getRoomIncomeCharm(List<Long> roomIds, long playerId, Date startDate, Date endDate) {
        Integer roomIncomeCharm = ppLiveGiveGiftActionMapper.getRoomIncomeCharm(roomIds, playerId, startDate, endDate);
        if (roomIncomeCharm == null) {
            return 0;
        }
        return roomIncomeCharm;
    }

    @Override
    public Integer getPersonIncomeCharm(long playerId, Date startDate, Date endDate) {
        Integer personIncomeCharm = ppLiveGiveGiftActionMapper.getPersonIncomeCharm(playerId, startDate, endDate);
        if (personIncomeCharm == null) {
            return 0;
        }
        return personIncomeCharm;
    }

    /**
     * 获取厅收入信息
     * @param njIds 厅主id列表
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 厅收入信息 key=njId value=厅收入信息
     */
    public Map<Long, AdminPlayerIncomeInfoPo> getAdminPlayerIncomeInfoMap(List<Long> njIds, Date startDate, Date endDate) {
        Result<PlayerSignServiceProto.ResponseBatchAdminPlayerIncomeList> result =
                playerSignService.batchAdminPlayerIncomeList(njIds, startDate.getTime(), endDate.getTime());
        if (RpcResult.isFail(result)) {
            log.warn("queryAdminPlayerIncomeList fail, njIds={}`startDate={}`endDate={}`rCode={}",
                    njIds, startDate, endDate, result.rCode());
            return Collections.emptyMap();
        }

        String adminPlayerIncomeInfoStr = result.target().getAdminPlayerIncomeInfo();
        List<AdminPlayerIncomeInfoPo> adminPlayerIncomeInfoPos = JSONObject.parseArray(
                adminPlayerIncomeInfoStr, AdminPlayerIncomeInfoPo.class);
        log.info("getAdminPlayerIncomeInfoMap[batchAdminPlayerIncomeList] `startDate={}`endDate={}` adminPlayerIncomeInfoPos={} ",startDate, endDate,JSONObject.toJSONString(adminPlayerIncomeInfoPos));

        // list转换成map, key=njId
        return adminPlayerIncomeInfoPos.stream().collect(
                Collectors.toMap(AdminPlayerIncomeInfoPo::getNjId, Function.identity(), (o, n) -> n));
    }
}
