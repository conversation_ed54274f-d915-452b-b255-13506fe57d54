package fm.lizhi.ocean.wavecenter.infrastructure.live.manager;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.live.constants.CheckInStatus;
import fm.lizhi.ocean.wavecenter.api.live.constants.HostStatus;
import fm.lizhi.ocean.wavecenter.infrastructure.live.convert.LivePlayerCheckInfraConvert;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WaveCheckInRecordEntity;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.mapper.WaveCheckInRecordEntityMapper;
import fm.lizhi.ocean.wavecenter.service.live.dto.*;
import fm.lizhi.ocean.wavecenter.service.live.manager.LivePlayerCheckInManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/11 16:27
 */
@Slf4j
@Component
public class LivePlayerCheckInManagerImpl implements LivePlayerCheckInManager {

    @Autowired
    private WaveCheckInRecordEntityMapper waveCheckInRecordMapper;

    @Override
    public List<PlayerCheckDayStatsDto> queryDayStats(PlayerCheckDayStatsReqDto reqDto) {
        //查询所有相关记录
        List<WaveCheckInRecordEntity> poList = waveCheckInRecordMapper.hourDetail(
                reqDto.getAppId(), reqDto.getPlayerId(), reqDto.getStartDay(),
                reqDto.getEndDay(), reqDto.getRoomId(), reqDto.getFamilyId(),
                reqDto.getSingStatus().getValue());
        if (CollectionUtils.isEmpty(poList)) {
            LogContext.addResLog("poList is empty");
            return Collections.emptyList();
        }

        //数据量不多，直接内存计算
        //按天分组
        Map<String, List<WaveCheckInRecordEntity>> dayGroup = poListToDayGroup(poList);

        //按天累加
        List<PlayerCheckDayStatsDto> dtoList = new ArrayList<>();
        for (Map.Entry<String, List<WaveCheckInRecordEntity>> entry : dayGroup.entrySet()) {
            String dayStr = entry.getKey();
            List<WaveCheckInRecordEntity> records = entry.getValue();

            PlayerCheckDayStatsDto dto = new PlayerCheckDayStatsDto().setDay(DateUtil.formatStrToDate(dayStr, DateUtil.date_2));
            BigDecimal income = BigDecimal.ZERO;
            long charm = 0L;
            int seatOrder = 0;
            int hostCnt = 0;
            for (WaveCheckInRecordEntity record : records) {
                income = income.add(record.getIncome());
                charm += record.getCharmValue();
                if (!Objects.equals(CheckInStatus.UN_CHECKED.getValue(), record.getStatus())) {
                    seatOrder++;
                }

                if (Objects.equals(HostStatus.HOST.getValue(), record.getIsHost())) {
                    hostCnt++;
                }
            }

            dto.setIncome(income)
                    .setCharm(charm)
                    .setHostCnt(hostCnt)
                    .setSeatOrder(seatOrder);

            dtoList.add(dto);
        }

        //按天排序
        dtoList.sort(Comparator.comparing(PlayerCheckDayStatsDto::getDay));
        return dtoList;
    }

    @Override
    public List<PlayerCheckHourStatsDayDto> queryHourStats(PlayerCheckHourStatsReqDto reqDto) {
        //查询所有相关记录
        log.info("waveCheckInRecordMapper.hourDetail reqDto={}", JsonUtil.dumps(reqDto));
        List<WaveCheckInRecordEntity> poList = waveCheckInRecordMapper.hourDetail(
                reqDto.getAppId(), reqDto.getPlayerId(), reqDto.getStartDay(),
                reqDto.getEndDay(), reqDto.getRoomId(), reqDto.getFamilyId(),
                reqDto.getSingStatus().getValue());
        if (CollectionUtils.isEmpty(poList)) {
            LogContext.addResLog("poList is empty");
            return Collections.emptyList();
        }

        //数据量不多，直接内存计算
        //按天分组
        Map<String, List<WaveCheckInRecordEntity>> dayGroup = poListToDayGroup(poList);
        List<PlayerCheckHourStatsDayDto> dtoList = new ArrayList<>();
        for (Map.Entry<String, List<WaveCheckInRecordEntity>> entry : dayGroup.entrySet()) {
            String dayStr = entry.getKey();
            List<WaveCheckInRecordEntity> records = entry.getValue();
            if (CollectionUtils.isEmpty(records)) {
                continue;
            }

            PlayerCheckHourStatsDayDto dto = new PlayerCheckHourStatsDayDto();
            dto.setDay(DateUtil.formatStrToDate(dayStr, DateUtil.date_2));
            List<PlayerCheckHourStatsDto> detail = LivePlayerCheckInfraConvert.I.hourStatsPos2Dtos(records);
            detail.sort(Comparator.comparing(PlayerCheckHourStatsDto::getStartTime));
            dto.setDetail(detail);

            BigDecimal income = BigDecimal.ZERO;
            long charm = 0L;
            int seatOrder = 0;
            int hostCnt = 0;
            for (WaveCheckInRecordEntity record : records) {
                income = income.add(record.getIncome());
                charm += record.getCharmValue();
                if (!Objects.equals(CheckInStatus.UN_CHECKED.getValue(), record.getStatus())) {
                    seatOrder++;
                }
                if (Objects.equals(HostStatus.HOST.getValue(), record.getIsHost())) {
                    hostCnt++;
                }
            }

            dto.setIncome(income)
                    .setCharm(charm)
                    .setHostCnt(hostCnt)
                    .setSeatOrder(seatOrder);
            dtoList.add(dto);
        }
        dtoList.sort(Comparator.comparing(PlayerCheckHourStatsDayDto::getDay));
        return dtoList;
    }

    @Override
    public PlayerCheckStatsSumDto sum(PlayerCheckHourStatsReqDto reqDto) {
        //查询所有相关记录
        List<WaveCheckInRecordEntity> poList = waveCheckInRecordMapper.hourDetail(
                reqDto.getAppId(), reqDto.getPlayerId(), reqDto.getStartDay(),
                reqDto.getEndDay(), reqDto.getRoomId(), reqDto.getFamilyId(),
                reqDto.getSingStatus().getValue());
        if (CollectionUtils.isEmpty(poList)) {
            LogContext.addResLog("poList is empty");
            return PlayerCheckStatsSumDto.zero();
        }

        BigDecimal income = BigDecimal.ZERO;
        long charm = 0L;
        int seatOrder = 0;
        int hostCnt = 0;
        for (WaveCheckInRecordEntity record : poList) {
            income = income.add(record.getIncome());
            charm += record.getCharmValue();
            if (!Objects.equals(CheckInStatus.UN_CHECKED.getValue(), record.getStatus())) {
                seatOrder++;
            }
            if (Objects.equals(HostStatus.HOST.getValue(), record.getIsHost())) {
                hostCnt++;
            }
        }

        return new PlayerCheckStatsSumDto()
                .setIncome(income)
                .setCharm(charm)
                .setSeatOrder(seatOrder)
                .setHostCnt(hostCnt);
    }

    private Map<String, List<WaveCheckInRecordEntity>> poListToDayGroup(List<WaveCheckInRecordEntity> poList) {
        return poList.stream().collect(Collectors.groupingBy(v -> DateUtil.formatDateToString(v.getStartTime(), DateUtil.date_2)));
    }
}
