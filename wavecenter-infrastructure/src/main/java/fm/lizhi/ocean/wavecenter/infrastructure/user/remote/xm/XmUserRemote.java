package fm.lizhi.ocean.wavecenter.infrastructure.user.remote.xm;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.CacheStats;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.lamp.common.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.user.convert.UserConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.IUserRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.req.GetUserVerifyDataReq;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.req.GetUserWithdrawStatusReq;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.res.GetUserVerifyDataRes;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.res.GetUserWithdrawStatusRes;
import fm.lizhi.ocean.wavecenter.service.user.config.UserConfig;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserInfoDto;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserMediaDto;
import fm.lizhi.pp.security.constant.ban.BanStatus;
import fm.lizhi.xm.security.api.H5TokenService;
import fm.lizhi.xm.security.api.XmUserBanService;
import fm.lizhi.xm.security.protocol.SmsProto;
import fm.lizhi.xm.security.protocol.XmUserBanProto;
import fm.lizhi.xm.user.account.tag.api.UserVerifyProxyService;
import fm.lizhi.xm.user.account.tag.protocol.UserVerifyProxyProto;
import fm.lizhi.xm.user.account.user.api.XmNewUserService;
import fm.lizhi.xm.user.account.user.api.XmWithdrawUserService;
import fm.lizhi.xm.user.account.user.protocol.XmUserBaseProto;
import fm.lizhi.xm.user.account.user.protocol.XmUserProto;
import fm.lizhi.xm.user.account.user.protocol.XmUserRequestProto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xm.fm.lizhi.live.pp.push.api.PpDatePlayService;
import xm.fm.lizhi.live.pp.push.protocol.PpDatePlayProto;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class XmUserRemote implements IUserRemote {

    @Autowired
    private UserVerifyProxyService userVerifyProxyService;

    @Autowired
    private XmNewUserService xmNewUserService;

    @Autowired
    private UserConfig userConfig;

    @Autowired
    private CommonConfig commonConfig;

    @Autowired
    private XmUserBanService xmUserBanService;

    @Autowired
    private H5TokenService h5TokenService;

    @Autowired
    private PpDatePlayService ppDatePlayService;

    @Autowired
    private XmWithdrawUserService xmWithdrawUserService;

    /**
     * 用户信息缓存
     */
    private final LoadingCache<Long, Optional<SimpleUserDto>> SIMPLE_USER_CACHE = CacheBuilder.newBuilder()
            .maximumSize(20000)
            .expireAfterAccess(10, TimeUnit.MINUTES)
            .recordStats()
            .build(new CacheLoader<Long, Optional<SimpleUserDto>>() {
                @Override
                public Optional<SimpleUserDto> load(Long userId) throws Exception {
                    // @what：直接调批量接口查询 @why：PP服务的单个查询接口会查询标签信息，这里不需要
                    Map<Long, Optional<SimpleUserDto>> userMap = remoteLoadSimpleUserMap(Lists.newArrayList(userId));
                    if (userMap.get(userId) == null) {
                        return Optional.empty();
                    }
                    return userMap.get(userId);
                }

                @Override
                public Map<Long, Optional<SimpleUserDto>> loadAll(Iterable<? extends Long> userIds) throws Exception {
                    //批量查询，调用getAll方法时，除去缓存中存在的，剩下的调用此方法批量查询，例如：[1,2,3,4]，缓存中存在2,3，那么调用此方法查询1,4
                    return remoteLoadSimpleUserMap(Lists.newArrayList(userIds));
                }
            });

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.XIMI;
    }

    @Override
    public List<SimpleUserDto> getSimpleUserByIds(List<Long> ids) {
        try {
            ImmutableMap<Long, Optional<SimpleUserDto>> userCacheMap = SIMPLE_USER_CACHE.getAll(ids);

            if (userConfig.getXm().isPrintCacheStatLogSwitch()) {
                CacheStats stats = SIMPLE_USER_CACHE.stats();
                long size = SIMPLE_USER_CACHE.size();
                log.info("ximi getSimpleUserByIds cache size={}, stats:{}", size, stats.toString());
            }

            List<SimpleUserDto> simpleUserList = new ArrayList<>(ids.size());
            for (Optional<SimpleUserDto> userOptional : userCacheMap.values()) {
                userOptional.ifPresent(simpleUserList::add);
            }
            return simpleUserList;
        } catch (Exception e) {
            log.warn("ximi,getSimpleUserByIds exception:", e);
            return Collections.emptyList();
        }
    }

    @Override
    public Optional<GetUserVerifyDataRes> getUserVerifyDataResult(GetUserVerifyDataReq param) {
        Result<UserVerifyProxyProto.ResponseSearchUserVerifyResult> result = userVerifyProxyService.searchUserVerifyResult(UserVerifyProxyProto.SearchUserVerifyResultParams.newBuilder()
                .setAppId(param.getAppId())
                .setUserId(param.getUserId())
                .build());
        if (RpcResult.isFail(result)) {
            return Optional.empty();
        }
        List<UserVerifyProxyProto.UserVerifyResult> resultList = result.target().getResultList();
        if (CollectionUtils.isEmpty(resultList)) {
            return Optional.empty();
        }

        return Optional.ofNullable(UserConvert.I.xmVerifyPb2Res(result.target().getResult(0)));
    }

    @Override
    public boolean getUserBanStatus(Long userId) {
        Result<XmUserBanProto.ResponseGetUserBanStatus> result = xmUserBanService.getUserBanStatus(userId);
        if (RpcResult.isFail(result)) {
            log.warn("xm.getUserBanStatus fail userId={},rCode={}", userId, result.rCode());
            return true;
        }
        return !Objects.equals(result.target().getStatus(), BanStatus.NORMAL.getValue());
    }

    @Override
    public Optional<Long> getUserIdByBusinessToken(String businessToken) {
        Result<SmsProto.ResponseGetH5UserIdByToken> result = h5TokenService.getH5UserIdByToken(businessToken);
        if (RpcResult.isFail(result)) {
            log.warn("xm.getUserIdByBusinessToken fail businessToken={},rCode={}", businessToken, result.rCode());
            return Optional.empty();
        }
        return Optional.of(result.target().getUserId());
    }

    @Override
    public Optional<SimpleUserDto> getUserInfoByBand(String band) {
        XmUserRequestProto.GetUserRequest request = XmUserRequestProto.GetUserRequest.newBuilder()
                .setBand(band).build();
        Result<XmUserProto.ResponseGetUser> result = xmNewUserService.getUser(request);
        if (RpcResult.isFail(result)) {
            log.warn("xm getUserInfoByBand fail band={},rCode={}", band, result.rCode());
            return Optional.empty();
        }
        XmUserProto.ResponseGetUser user = result.target();
        return Optional.of(
                convertToSimple(user.getUser())
        );
    }


    private SimpleUserDto convertToSimple(XmUserBaseProto.User user) {
        long userId = user.getId();
        SimpleUserDto simpleUserDto = new SimpleUserDto();
        simpleUserDto.setId(userId);
        simpleUserDto.setBand(user.getBand());
        simpleUserDto.setName(user.getName());
        simpleUserDto.setAvatar(UrlUtils.getAvatarUrl(commonConfig.getXm().getCdnHost(), user.getPortrait()));
        return simpleUserDto;
    }

    private Map<Long, Optional<SimpleUserDto>> remoteLoadSimpleUserMap(List<Long> userIdList) {
        Map<Long, Optional<SimpleUserDto>> result = new HashMap<>();
        try {
            Result<XmUserProto.ResponseGetUsers> resp = xmNewUserService.getUsers(userIdList, false);
            if (RpcResult.isFail(resp)) {
                log.warn("xm,remoteLoadSimpleUserMap,rCode={},userIdList={}", resp.rCode(), JsonUtils.toJsonString(userIdList));
                return Collections.emptyMap();
            }

            List<XmUserBaseProto.User> userList = resp.target().getUserList();
            Map<Long, XmUserBaseProto.User> userMap = userList.stream().collect(Collectors.toMap(XmUserBaseProto.User::getId, v -> v, (k1, k2) -> k1));
            for (Long userId : userIdList) {
                XmUserBaseProto.User user = userMap.get(userId);
                if (user == null) {
                    LogContext.addReqLog("userId={} not found", userId);
                    result.put(userId, Optional.empty());
                    continue;
                }

                SimpleUserDto simpleUserDto = UserConvert.I.xmUserPb2SimpleDto(user);
                simpleUserDto.setAvatar(UrlUtils.getAvatarUrl(commonConfig.getXm().getCdnHost(), user.getPortrait()));
                result.put(userId, Optional.of(simpleUserDto));
            }
        } catch (Exception e) {
            log.warn("xm,remoteLoadSimpleUserMap,exception", e);
        }
        return result;
    }

    @Override
    public List<UserInfoDto> getUserInfoByIds(List<Long> userIds) {
        Result<XmUserProto.ResponseGetUsers> result = xmNewUserService.getUsers(userIds, false);
        if (RpcResult.isFail(result)) {
            log.warn("xm,getUserInfoByIds,rCode={},userIds={}", result.rCode(), JsonUtil.dumps(userIds));
            return Collections.emptyList();
        }

        List<XmUserBaseProto.User> userList = result.target().getUserList();
        List<UserInfoDto> list = new ArrayList<>();
        for (XmUserBaseProto.User user : userList) {
            list.add(convertToUserInfoDto(user));
        }
        return list;
    }

    @Override
    public Optional<UserInfoDto> getUserInfoById(long userId) {
        Result<XmUserProto.ResponseGetUser> result = xmNewUserService.getUser(XmUserRequestProto.GetUserRequest.newBuilder()
                .setUserId(userId)
                .build());
        if (RpcResult.isFail(result)) {
            log.error("xm getUserInfoById fail. rCode={},userId={}", result.rCode(), userId);
            return Optional.empty();
        }

        XmUserBaseProto.User user = result.target().getUser();
        return Optional.of(UserConvert.I.xmUserInfoPb2InfoDTO(user));
    }

    @Override
    public Optional<UserMediaDto> getUserMediaById(long userId) {
        Result<PpDatePlayProto.ResponsePlayerMediaList> result = ppDatePlayService.playerMediaList(0L, userId);
        if (RpcResult.isFail(result)) {
            log.error("xm playerMediaList fail. userId={},rCode={}", userId, result.rCode());
            return Optional.empty();
        }

        return Optional.of(new UserMediaDto()
                .setAlbumListJSON(result.target().getAlbumListJSON())
                .setVoiceListJSON(result.target().getVoiceListJSON())
                .setVideoListJSON(result.target().getVideoListJSON())
        );
    }

    @Override
    public int getUserBandStatus(Long userId) {
        return 0;
    }

    private UserInfoDto convertToUserInfoDto(XmUserBaseProto.User user) {
        UserInfoDto userInfoDto = new UserInfoDto();
        userInfoDto.setId(user.getId());
        userInfoDto.setName(user.getName());
        userInfoDto.setBand(user.getBand());
        userInfoDto.setPhoneNum(user.getPhoneNum());
        return userInfoDto;
    }

    @Override
    public boolean finishPlayerCenterAuth(Long userId) {
        return false;
    }

    @Override
    public Optional<SimpleUserDto> getUserByKeyWord(Long userId, String userName) {
        XmUserRequestProto.GetUserRequest request = XmUserRequestProto.GetUserRequest.newBuilder()
                .setUserId(userId)
                .setName(userName)
                .build();
        Result<XmUserProto.ResponseGetUser> result = xmNewUserService.getUser(request);
        if (RpcResult.isFail(result)) {
            log.error("xm getUserByKeyWord fail. userId={},userName={},rCode={}", userId, userName, result.rCode());
            return Optional.empty();
        }
        XmUserBaseProto.User user = result.target().getUser();
        return Optional.of(UserConvert.I.xmUserPb2SimpleDto(user));
    }

    @Override
    public Optional<GetUserWithdrawStatusRes> getUserWithdrawStatus(GetUserWithdrawStatusReq param) {
        XmUserRequestProto.CheckUserWithdrawStatusRequest request = XmUserRequestProto.CheckUserWithdrawStatusRequest.newBuilder().setUserId(param.getUserId()).build();
        Result<XmUserProto.ResponseCheckUserWithdrawStatus> result = xmWithdrawUserService.checkUserWithdrawStatus(request);
        if (RpcResult.isFail(result)) {
            return Optional.empty();
        }
        GetUserWithdrawStatusRes withdrawStatus = GetUserWithdrawStatusRes.builder()
                .userId(result.target().getUserId())
                .note(result.target().getNote())
                .withdraw(result.target().getIsWithdraw())
                .build();
        return Optional.ofNullable(withdrawStatus);
    }
}
