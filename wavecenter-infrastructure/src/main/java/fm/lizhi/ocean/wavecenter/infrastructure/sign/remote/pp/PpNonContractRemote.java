package fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.pp;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.bean.NjAndPlayerContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.OperateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseWithdrawCancel;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.convert.SignInfraConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity.PpPlayerSign;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity.PpPlayerSignExample;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.mapper.PpPlayerSignMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.po.CountSignPlayerPo;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.po.QueryPlayerSignPo;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.INonContractRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.request.RequestInviteCancel;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.request.RequestInviteSign;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.response.ResponseInviteCancel;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.response.ResponseInviteSign;
import fm.lizhi.ocean.wavecenter.service.sign.dto.OperateSignDTO;
import fm.lizhi.ocean.wavecenter.service.sign.dto.QueryNonContractDTO;
import fm.lizhi.ocean.wavecenter.service.user.convert.RoleConvert;
import fm.pp.family.api.PlayerSignService;
import fm.pp.family.protocol.PlayerSignServiceProto;
import org.apache.commons.lang3.tuple.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/10 14:18
 */
@Slf4j
@Component
public class PpNonContractRemote implements INonContractRemote {

    @Autowired
    private PpPlayerSignMapper playerSignMapper;
    @Autowired
    private PlayerSignService playerSignService;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public Optional<NjAndPlayerContractBean> queryUserLast(Long userId, String type) {
        PpPlayerSignExample example = new PpPlayerSignExample();
        example.setOrderByClause("create_time desc");
        PpPlayerSignExample.Criteria criteria = example.createCriteria();
        criteria.andUserIdEqualTo(userId);
        criteria.andTypeEqualTo(type);

        PageList<PpPlayerSign> pageList = playerSignMapper.pageByExample(example, 1, 1);
        if (CollectionUtils.isEmpty(pageList)) {
            return Optional.empty();
        }

        PpPlayerSign ppPlayerSign = pageList.get(0);
        NjAndPlayerContractBean njAndPlayerContractBean = new NjAndPlayerContractBean();
        njAndPlayerContractBean.setContractId(ppPlayerSign.getId());
        njAndPlayerContractBean.setStatus(SignInfraConvert.I.ppSignStatusTrans(ppPlayerSign.getStatus()));
        njAndPlayerContractBean.setType(type);
        njAndPlayerContractBean.setCreateUser("ADMIN".equals(ppPlayerSign.getUserType()) ? RoleEnum.ROOM.getRoleCode() : RoleEnum.PLAYER.getRoleCode());

        return Optional.of(njAndPlayerContractBean);
    }

    @Override
    public boolean isInChangeCompany(Long curUserId) {
        return false;
    }

    @Override
    public boolean isInChangeCompanyPreparedStage(Long curUserId) {
        return false;
    }

    @Override
    public Pair<Integer, String> checkInviteSignLimit(Long userId, Long njId, RoleEnum userType, RoleEnum checkType) {
        return null;
    }

    @Override
    public ResponseInviteSign inviteSign(RequestInviteSign request) {
        Result<PlayerSignServiceProto.ResponseInviteSign> result = playerSignService.inviteSign(request.getCurUserId(), request.getTargetUserId(), RoleConvert.I.waveRole2PpRoleCode(request.getOpRole().getRoleCode()));
        if (RpcResult.isFail(result)) {
            log.error("pp inviteSign fail. curUserId={},targetUserId={},rCode={}", request.getCurUserId(), request.getTargetUserId(), result.rCode());
            return new ResponseInviteSign().setCode(-1);
        }
        return new ResponseInviteSign()
                .setCode(result.target().getCode())
                .setMsg(result.target().getMessage());
    }

    @Override
    public ResponseInviteCancel inviteCancel(RequestInviteCancel request) {
        Result<PlayerSignServiceProto.ResponseInviteCancel> result = playerSignService.inviteCancel(request.getPlaySignId()
                , RoleConvert.I.waveRole2PpRoleCode(request.getOpRole().getRoleCode()), request.getCurUserId());
        if (RpcResult.isFail(result)) {
            log.error("pp inviteCancel fail. playSignId={},curUserId={},role={},rCode={}", request.getPlaySignId()
                    , request.getCurUserId(), request.getOpRole().getRoleCode(), result.rCode());
            return new ResponseInviteCancel().setCode(-1);
        }
        return new ResponseInviteCancel().setCode(result.target().getCode()).setMsg(result.target().getMessage());
    }

    @Override
    public List<NjAndPlayerContractBean> queryUserSign(Long userId, List<ContractTypeEnum> types, List<SignRelationEnum> status) {
        return Collections.emptyList();
    }

    @Override
    public OperateSignDTO operateSign(Long playSignId, Long curUserId, ContractTypeEnum type, RoleEnum role, OperateTypeEnum operateType) {
        OperateSignDTO res = new OperateSignDTO().setCode(0);

        Result<PlayerSignServiceProto.ResponseSign> result = playerSignService.sign(playSignId, curUserId, type.getCode(), RoleConvert.I.waveRole2PpRoleCode(role.getRoleCode()));
        int rCode = result.rCode();
        if (rCode == PlayerSignService.SIGN_EXIST) {
            if (type == ContractTypeEnum.SIGN) {
                if (role == RoleEnum.ROOM) {
                    return res.setCode(-1).setMsg("该陪玩已签约其他厅");
                }
                return res.setCode(-1).setMsg("你已有签约的厅");
            } else {
                return res.setCode(-1).setMsg("已解约");
            }
        }

        if (rCode == PlayerSignService.SIGN_RESTRICTED_PERIOD && type == ContractTypeEnum.SIGN) {
            if (role == RoleEnum.ROOM) {
                return res.setCode(-1).setMsg("对方解约未超过14天不得签约");
            }
            return res.setCode(-1).setMsg("解约内14天不得签约");
        }

        if (RpcResult.isFail(result)) {
            log.error("pp sign fail. playSignId={},curUserId={},type={},role={}", playSignId, curUserId, type.getCode(), role.getRoleCode());
            return new OperateSignDTO().setCode(-1);
        }
        return res;
    }

    @Override
    public Pair<Integer, String> checkCanSignForConfirm(Long playSignId, Long curUserId, RoleEnum opRole, RoleEnum checkRole) {
        return null;
    }

    @Override
    public PageBean<NjAndPlayerContractBean> queryList(QueryNonContractDTO paramDTO) {
        QueryPlayerSignPo entityParam = new QueryPlayerSignPo();
        if (paramDTO.getContractId() != null) {
            entityParam.setContractId(paramDTO.getContractId());
        }
        if (CollectionUtils.isNotEmpty(paramDTO.getNjIds())) {
            entityParam.setNjIds(paramDTO.getNjIds());
        }
        if (CollectionUtils.isNotEmpty(paramDTO.getTypes())) {
            entityParam.setTypes(paramDTO.getTypes().stream().map(ContractTypeEnum::getCode).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(paramDTO.getStatuses())) {
            entityParam.setStatuses(paramDTO.getStatuses().stream().map(SignInfraConvert.I::waveSignStatus2hy).collect(Collectors.toList()));
        }
        if (paramDTO.getUserOrNjId() != null) {
            entityParam.setUserOrNjId(paramDTO.getUserOrNjId());
        }
        if (paramDTO.getUserId() != null) {
            entityParam.setUserId(paramDTO.getUserId());
        }
        if (CollectionUtils.isNotEmpty(paramDTO.getContractIdLists())) {
            entityParam.setContractIdLists(paramDTO.getContractIdLists());
        }
        if (paramDTO.getParentId() != null) {
            entityParam.setParentId(paramDTO.getParentId());
        }

        entityParam.setDescCreateTime(paramDTO.isDescCreateTime());

        PageList<PpPlayerSign> list = playerSignMapper.pageByEntity(entityParam, paramDTO.getPageNo(), paramDTO.getPageSize());
        return PageBean.of(list.getTotal(), SignInfraConvert.I.ppPlayerSignPos2ContractBeans(list));
    }

    @Override
    public Pair<Integer, String> reviewCancel(Long playSignId, Long curUserId, OperateTypeEnum operateType) {
        return null;
    }

    @Override
    public ResponseWithdrawCancel withdrawCancel(Long playerSignId, Long curUserId, RoleEnum opUserRole) {
        return null;
    }

    @Override
    public Map<Long, Integer> countSignPlayerByRooms(List<Long> njIds) {
        if (CollectionUtils.isEmpty(njIds)) {
            return Collections.emptyMap();
        }

        List<CountSignPlayerPo> countSignPlayerPos = playerSignMapper.countSignPlayerByRooms(njIds);
        return countSignPlayerPos.stream().collect(Collectors.toMap(CountSignPlayerPo::getNjId, CountSignPlayerPo::getUserCount));
    }

    @Override
    public Integer countPlayerSignNum(Long njId) {
        Result<PlayerSignServiceProto.ResponsePlayerSignNum> result = playerSignService.playerSignNum(njId);
        if (RpcResult.isFail(result)) {
            log.error("pp playerSignNum fail. njId={},rCode={}", njId, result.rCode());
            return 0;
        }
        return result.target().getNum();
    }

    @Override
    public Optional<Long> getPlayerCurSignNj(Long userId) {
        Result<PlayerSignServiceProto.ResponsePlayerCurSignNj> result = playerSignService.playerCurSignNj(userId);
        if (RpcResult.isFail(result)) {
            return Optional.empty();
        }
        long njId = result.target().getNjId();
        if (-1 == njId) {
            return Optional.empty();
        }
        return Optional.of(njId);
    }

    @Override
    public Optional<Long> getPlayerLastCancelSign(Long userId) {
        Result<PlayerSignServiceProto.ResponsePlayerLastCancelSign> result =
                playerSignService.playerLastCancelSign(userId);
        if (RpcResult.isFail(result)) {
            log.error("playerSignService.playerLastCancelSign error, rCode={}, userId={}", result.rCode(), userId);
            return Optional.empty();
        }
        return Optional.of(result.target().getNjId());
    }
}
