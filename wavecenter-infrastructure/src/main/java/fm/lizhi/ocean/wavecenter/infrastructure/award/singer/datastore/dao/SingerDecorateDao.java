package fm.lizhi.ocean.wavecenter.infrastructure.award.singer.datastore.dao;

import java.util.*;
import java.util.stream.Collectors;

import fm.lizhi.ocean.wavecenter.infrastructure.award.singer.convert.SingerDecorateConvert;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowDTO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.lizhi.commons.config.core.util.ConfigUtils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateFlowOperateEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateFlowOperatorEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateOperateStatusEnum;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.entity.SingerDecorateFlow;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.entity.SingerDecorateFlowExample;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.entity.SingerDecorateRule;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.entity.SingerDecorateRuleExample;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.mapper.SingerDecorateFlowMapper;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.mapper.SingerDecorateRuleMapper;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowPageParamDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class SingerDecorateDao {


    @Autowired
    private SingerDecorateRuleMapper singerDecorateRuleMapper;

    @Autowired
    private SingerDecorateFlowMapper singerDecorateFlowMapper;

    /**
     * 分页查询歌手装扮规则
     */
    public PageList<SingerDecorateRule> pageSingerDecorateRule(int appId, int pageNo, int pageSize) {

        SingerDecorateRuleExample example = new SingerDecorateRuleExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andDeletedEqualTo(Boolean.FALSE)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
        ;

        example.setOrderByClause("modify_time desc");

        return singerDecorateRuleMapper.pageByExample(example, pageNo, pageSize);
    }

    /**
     * 分页查询歌手装扮流水
     */
    public PageList<SingerDecorateFlow> pageSingerDecorateFlow(SingerDecorateFlowPageParamDTO param, int pageNo, int pageSize) {
        SingerDecorateFlowExample example = new SingerDecorateFlowExample();
        SingerDecorateFlowExample.Criteria criteria = example.createCriteria()
                .andAppIdEqualTo(param.getAppId())
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                ;

        if (param.getUserId() != null){
            criteria.andUserIdEqualTo(param.getUserId());
        }

        if (StrUtil.isNotBlank(param.getOperator())){
            if (param.getOperator().equals(SingerDecorateFlowOperatorEnum.SYSTEM.getOperator())){
                criteria.andOperatorEqualTo(param.getOperator());
            }else {
                criteria.andOperatorNotEqualTo(SingerDecorateFlowOperatorEnum.SYSTEM.getOperator());
            }
        }

        if (param.getDecorateType() != null){
            criteria.andDecorateTypeEqualTo(param.getDecorateType());
        }

        if (param.getOperateType() != null){
            criteria.andOperateTypeEqualTo(param.getOperateType());
        }

        if (param.getStartOperateTime() != null){
            criteria.andOperateTimeGreaterThanOrEqualTo(new Date(param.getStartOperateTime()));
        }
        if (param.getEndOperateTime() != null){
            criteria.andOperateTimeLessThanOrEqualTo(new Date(param.getEndOperateTime()));
        }

        example.setOrderByClause("create_time desc");

        return singerDecorateFlowMapper.pageByExample(example, pageNo, pageSize);
    }

    /**
     * 获取歌手装扮规则
     */
    public List<SingerDecorateRule> getSingerDecorateRule(int appId, SingerTypeEnum singerType, Set<String> combineIndices) {
        SingerDecorateRuleExample example = new SingerDecorateRuleExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andDeletedEqualTo(Boolean.FALSE)
                .andEnabledEqualTo(Boolean.TRUE)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andSingerTypeEqualTo(singerType.getType())
                .andCombineConditionIndexIn(new ArrayList<>(combineIndices));
        return singerDecorateRuleMapper.selectByExample(example);
    }

    /**
     * 获取歌手装扮流水
     */
    public List<SingerDecorateFlow> getDecorateFlowByUserIdAndSingerType(Long userId, int singerType, SingerDecorateFlowOperateEnum singerDecorateFlowOperateEnum) {
        SingerDecorateFlowExample example = new SingerDecorateFlowExample();
        SingerDecorateFlowExample.Criteria criteria = example.createCriteria()
                .andUserIdEqualTo(userId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andSingerTypeEqualTo(singerType)
                ;

        if (singerDecorateFlowOperateEnum != null){
            criteria.andOperateTypeEqualTo(singerDecorateFlowOperateEnum.getCode());
        }

        return singerDecorateFlowMapper.selectByExample(example);
    }

    /**
     * 获取歌手装扮流水
     */
    public List<SingerDecorateFlow> getDecorateFlowByUserIdAndSingerType(int appId, Long userId, Long ruleId,
                                                                         int singerType,
                                                                         SingerDecorateFlowOperateEnum singerDecorateFlowOperateEnum) {
        SingerDecorateFlowExample example = new SingerDecorateFlowExample();
        SingerDecorateFlowExample.Criteria criteria = example.createCriteria()
                .andUserIdEqualTo(userId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andSingerTypeEqualTo(singerType)
                .andRuleIdEqualTo(ruleId)
                .andAppIdEqualTo(appId)
                ;

        if (singerDecorateFlowOperateEnum != null){
            criteria.andOperateTypeEqualTo(singerDecorateFlowOperateEnum.getCode());
        }

        return singerDecorateFlowMapper.selectByExample(example);
    }

    /**
     * 获取歌手装扮流水
     */
    public List<SingerDecorateFlow> getDecorateFlowByTransactionIdAndLteRetryCount(Long transactionId, List<SingerDecorateOperateStatusEnum> statusList) {
        SingerDecorateFlowExample example = new SingerDecorateFlowExample();
        example.createCriteria()
                .andTransactionIdEqualTo(transactionId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andStatusIn(statusList.stream().map(SingerDecorateOperateStatusEnum::getStatus).collect(Collectors.toList()))
                .andRetryCountLessThanOrEqualTo(3)
        ;

        return singerDecorateFlowMapper.selectByExample(example);
    }

    /**
     * 批量插入歌手装扮流水
     * 
     * @param decorateFlowList 歌手装扮流水列表
     * @return 是否插入成功
     */
    public boolean batchInsert(List<SingerDecorateFlow> decorateFlowList) {
        if (CollectionUtils.isEmpty(decorateFlowList)) {
            return true;
        }
        return singerDecorateFlowMapper.batchInsert(decorateFlowList) == decorateFlowList.size();
    }

    /**
     * 获取可回收的歌手装扮流水
     */
    public List<SingerDecorateFlow> getCanRecoverDecorateFlowByUserIdAndSingerType(int appId, Long userId, Integer singerType) {
        SingerDecorateFlowExample example = new SingerDecorateFlowExample();
        example.createCriteria()
                .andUserIdEqualTo(userId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andAppIdEqualTo(appId)
                .andRecycledEqualTo(false)
                .andStatusEqualTo(SingerDecorateOperateStatusEnum.SUCCESS.getStatus())
                .andSingerTypeEqualTo(singerType)
                .andOperateTypeEqualTo(SingerDecorateFlowOperateEnum.GRANT.getCode())
                ;

        return singerDecorateFlowMapper.selectByExample(example);

    }

    /**
     * 更新歌手流水
     */
    @Transactional
    public boolean updateDecorateFlowStatus(SingerDecorateFlowDTO flow, SingerDecorateOperateStatusEnum status) {
        flow.setModifyTime(new Date());
        flow.setOperateTime(new Date());
        flow.setStatus(status.getStatus());

        if (SingerDecorateOperateStatusEnum.FAIL.equals(status)){
            flow.setRetryCount(flow.getRetryCount()+1);
        }

        return singerDecorateFlowMapper.updateByPrimaryKey(SingerDecorateConvert.I.convertSingerDecorateFlow(flow)) > 0;

    }

    /**
     * 更新歌手装扮流水回收状态
     */
    public void updateSingerDecorateFlowRecycled(Long userId, Integer singerType, Long decorateId, Integer decorateType, Long ruleId, boolean recycled) {
        SingerDecorateFlowExample example = new SingerDecorateFlowExample();
        example.createCriteria()
                .andUserIdEqualTo(userId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andRecycledEqualTo(false)
                .andDecorateTypeEqualTo(decorateType)
                .andSingerTypeEqualTo(singerType)
                .andDecorateIdEqualTo(decorateId)
                .andRuleIdEqualTo(ruleId)
        ;

        SingerDecorateFlow singerDecorateFlow = new SingerDecorateFlow();
        singerDecorateFlow.setModifyTime(new Date());
        singerDecorateFlow.setRecycled(recycled);
        singerDecorateFlowMapper.updateByExample(singerDecorateFlow, example);
    }

    /**
     * 根据状态获取歌手装扮流水
     */
    public PageList<SingerDecorateFlow> getDecorateFlowByStatus(List<SingerDecorateOperateStatusEnum> statusList, int pageSize, int pageNo) {
        SingerDecorateFlowExample example = new SingerDecorateFlowExample();
        example.createCriteria()
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andStatusIn(statusList.stream().map(SingerDecorateOperateStatusEnum::getStatus).collect(Collectors.toList()));

        return singerDecorateFlowMapper.pageByExample(example, pageNo, pageSize);

    }

    /**
     * 根据用户ID和规则ID列表查询歌手装扮流水
     */
    public List<SingerDecorateFlow> getDecorateFlowByUserIdAndRuleIds(Long userId, int appId, List<Long> ruleIds, SingerDecorateFlowOperateEnum operate, boolean recycled) {
        SingerDecorateFlowExample example = new SingerDecorateFlowExample();
        example.createCriteria()
                .andUserIdEqualTo(userId)
                .andAppIdEqualTo(appId)
                .andRuleIdIn(ruleIds)
                .andRecycledEqualTo(recycled)
                .andOperateTypeEqualTo(operate.getCode())
               .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());

        return singerDecorateFlowMapper.selectByExample(example);
    }

    /**
     * 根据transactionId获取歌手装扮流水，增加appId
     */
    public List<SingerDecorateFlow> getDecorateFlowByTransactionId(Long transactionId, int appId) {
        SingerDecorateFlowExample example = new SingerDecorateFlowExample();
        example.createCriteria()
                .andTransactionIdEqualTo(transactionId)
                .andAppIdEqualTo(appId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());

        return singerDecorateFlowMapper.selectByExample(example);
    }
}
