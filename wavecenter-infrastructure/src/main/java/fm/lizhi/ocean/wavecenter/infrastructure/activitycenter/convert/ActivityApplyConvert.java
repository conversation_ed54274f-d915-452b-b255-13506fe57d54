package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityFlowResourceAuditBean;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityApplyFlowResource;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityApplyInfo;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityApplyProcess;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.*;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActivityApplyConvert {

    ActivityApplyConvert I = Mappers.getMapper(ActivityApplyConvert.class);

    @Mappings({
            @Mapping(target = "id", source = "activityId"),
            @Mapping(target = "deleted", ignore = true),
            @Mapping(target = "roomBackgroundId", constant = "0l"),
            @Mapping(target = "avatarWidgetId", constant = "0l"),
    })
    ActivityApplyInfo applyInfoDto2Bean(ActivityApplyParamDTO param);

    /**
     * 流量资源DTO转流量资源实体
     * @param param 参数
     * @return 结果
     */
    @Mappings({
            @Mapping(target = "status", constant = "0"),
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "activityId", ignore = true)
    })
    ActivityApplyFlowResource flowResourceDto2Bean(ActivityFlowResourceDTO param);

    List<ActivityApplyFlowResource> flowResourceDtoList2Beans(List<ActivityFlowResourceDTO> params);

    @Mappings({
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "activityId", ignore = true)
    })
    ActivityApplyProcess processDto2Bean(ActivityProcessDTO param);

    List<ActivityApplyProcess> processDtoList2BeanList(List<ActivityProcessDTO> params);

    ActivitySimpleInfoDT0 simpleBean2DTO (ActivityApplyInfo param);

    List<ActivitySimpleInfoDT0> simpleBeans2DTOs (List<ActivityApplyInfo> params);

    @Mapping(target = "activityId", source = "id")
    SendReportDataInfoDTO convertSendReportDataInfo(ActivityApplyInfo info);

    List<SendReportDataInfoDTO> convertSendReportDataInfo(List<ActivityApplyInfo> pageList);

    ActivityInfoDTO activityInfo2DTO(ActivityApplyInfo bean);
    List<ActivityInfoDTO> activityInfos2DTOs(List<ActivityApplyInfo> pageList);

    List<ActivityFlowResourceDTO> convertFlowResourceBean2DTO(List<ActivityApplyFlowResource> flowResources);

    List<ActivityProcessDTO> activityApplyProcess2ActivityProcessDTO(List<ActivityApplyProcess> processes);

    @Mappings({
            @Mapping(target = "resourceConfigId", source = "flowResourceId")
    })
    ActivityApplyFlowResource convertFlowResourceAuditBean2Bean(ActivityFlowResourceAuditBean resourceAudit);


    List<ActivityApplyFlowResource> convertFlowResourceAuditsBean2Bean(List<ActivityFlowResourceAuditBean> resourceAuditList);

    ActivityApplyInfoSimpleDTO convertActivityApplyInfoSimpleDTO(ActivityApplyInfo entity);

    List<ActivityApplyInfoSimpleDTO> convertActivityApplyInfoSimpleDTOs(List<ActivityApplyInfo> entitys);
}
