package fm.lizhi.ocean.wavecenter.infrastructure.user.constants;

import fm.lizhi.ocean.wavecenter.base.constants.IRedisKey;

/**
 * <AUTHOR>
 * @date 2024/4/12 17:42
 */
public enum UserRedisKey implements IRedisKey {

    /**
     * 用户设备对应的accessToken
     * USER_ADVICE_ACCESS_TOKEN_[appId]_[userId]_[deviceId]
     * value->accessToken
     */
    USER_ADVICE_ACCESS_TOKEN

    /**
     * accessToken信息
     * ACCESS_TOKEN_REF_INFO_[accessToken]
     * string结构
     * value=accessTokenInfoPo的json字符串
     * @see fm.lizhi.ocean.wavecenter.infrastructure.user.po.AccessTokenInfoPo
     */
    , ACCESS_TOKEN_REF_INFO

    /**
     * refreshToken信息
     * REFRESH_TOKEN_REF_INFO_[refreshToken]
     * string结构
     * value=refreshTokenInfoPo的json字符串
     * @see fm.lizhi.ocean.wavecenter.infrastructure.user.po.RefreshTokenInfoPo
     */
    , REFRESH_TOKEN_REF_INFO

    /**
     * 用户角色对应的accessToken
     * 用户角色配置禁用的时候删除token
     * USER_ROLE_ACCESS_TOKEN_[appId]_[userId]_[deviceId]_[roleConfigId]
     * value->accessToken
     */
    , USER_ROLE_ACCESS_TOKEN

    /**
     * 用户登录设备集合
     * LOGIN_USER_DEVICE_[appId]_[userId]
     * value=设备ID集合
     */
    , LOGIN_USER_DEVICE

    /**
     * 登录二维码key
     * string结构
     * LOGIN_QRCODE_KEY_[appId]_[qrCodeKey]
     * value->二维码状态
     */
    , LOGIN_QRCODE_KEY

    /**
     * 二维码key关联的设备
     * string结构
     * LOGIN_QRCODE_KEY_[appId]_[qrCodeKey]
     * value->设备ID
     */
    , QRCODE_REF_DEVICE

    /**
     * 登录二维码key和accessToken的关联
     * string结构
     * QRCODE_REF_ACCESS_TOKEN_[appId]_[qrCodeKey]
     * value->accessToken
     */
    , QRCODE_REF_ACCESS_TOKEN

    /**
     * 每日下载文件数量
     * DOWN_FILE_NUM_[appId]_[userId]_[20240101]
     */
    , DOWN_FILE_NUM

    ;

    @Override
    public String getPrefix() {
        return "WC_USER";
    }

    @Override
    public String getName() {
        return this.name();
    }


}
