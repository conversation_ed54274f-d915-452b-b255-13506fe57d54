package fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.infrastructure.live.dto.WaveCheckInRoomUserLightGiftGroupDTO;
import fm.lizhi.ocean.wavecenter.infrastructure.live.dto.WaveCheckInRoomUserRewardAmountGroupDTO;
import fm.lizhi.ocean.wavecenter.infrastructure.live.dto.WaveCheckInUserLightGiftGroupDTO;
import fm.lizhi.ocean.wavecenter.infrastructure.live.dto.WaveCheckInUserRewardAmountGroupDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 麦序福利只读库扩展mapper, 用于web站数据统计
 */
@DataStore(namespace = "mysql_ocean_wave_r")
public interface WaveCheckInLightGiftRecordExtMapper {

    @Select("<script>\n" +
            "  SELECT\n" +
            "    rec_user_id AS recUserId,\n" +
            "    reward_ladder AS rewardLadder,\n" +
            "    SUM(gift_amount) AS giftAmountSum\n" +
            "  FROM\n" +
            "    wave_check_in_light_gift_record\n" +
            "  WHERE\n" +
            "    schedule_id IN\n" +
            "      <foreach collection=\"scheduleIds\" item=\"scheduleId\" open=\"(\" separator=\",\" close=\")\">\n" +
            "        #{scheduleId}\n" +
            "      </foreach>\n" +
            "  GROUP BY rec_user_id, reward_ladder\n" +
            "</script>")
    List<WaveCheckInRoomUserLightGiftGroupDTO> getCheckInRoomUserLightGiftGroups(@Param("scheduleIds") List<Long> scheduleIds);

    @Select("<script>\n" +
            "  SELECT\n" +
            "    rec_user_id AS recUserId,\n" +
            "    SUM(reward_amount) AS rewardAmountSum\n" +
            "  FROM\n" +
            "    wave_check_in_light_gift_record\n" +
            "  WHERE\n" +
            "    schedule_id IN\n" +
            "      <foreach collection=\"scheduleIds\" item=\"scheduleId\" open=\"(\" separator=\",\" close=\")\">\n" +
            "        #{scheduleId}\n" +
            "      </foreach>\n" +
            "  GROUP BY rec_user_id\n" +
            "</script>")
    List<WaveCheckInRoomUserRewardAmountGroupDTO> getCheckInRoomUserRewardAmountGroups(@Param("scheduleIds") List<Long> scheduleIds);

    @Select("<script>\n" +
            "  SELECT\n" +
            "    schedule_id AS scheduleId,\n" +
            "    reward_ladder AS rewardLadder,\n" +
            "    SUM(gift_amount) AS giftAmountSum\n" +
            "  FROM\n" +
            "    wave_check_in_light_gift_record\n" +
            "  WHERE\n" +
            "    schedule_id IN\n" +
            "      <foreach collection=\"scheduleIds\" item=\"scheduleId\" open=\"(\" separator=\",\" close=\")\">\n" +
            "        #{scheduleId}\n" +
            "      </foreach>\n" +
            "    AND rec_user_id = #{recUserId}\n" +
            "  GROUP BY schedule_id, reward_ladder\n" +
            "</script>")
    List<WaveCheckInUserLightGiftGroupDTO> getCheckInUserLightGiftGroups(@Param("scheduleIds") List<Long> scheduleIds, @Param("recUserId") long recUserId);

    @Select("<script>\n" +
            "  SELECT\n" +
            "    schedule_id AS scheduleId,\n" +
            "    SUM(reward_amount) AS rewardAmountSum\n" +
            "  FROM\n" +
            "    wave_check_in_light_gift_record\n" +
            "  WHERE\n" +
            "    schedule_id IN\n" +
            "      <foreach collection=\"scheduleIds\" item=\"scheduleId\" open=\"(\" separator=\",\" close=\")\">\n" +
            "        #{scheduleId}\n" +
            "      </foreach>\n" +
            "    AND rec_user_id = #{recUserId}\n" +
            "    GROUP BY schedule_id\n" +
            "</script>")
    List<WaveCheckInUserRewardAmountGroupDTO> getCheckInUserRewardAmountGroups(@Param("scheduleIds") List<Long> scheduleIds, @Param("recUserId") long recUserId);
}
