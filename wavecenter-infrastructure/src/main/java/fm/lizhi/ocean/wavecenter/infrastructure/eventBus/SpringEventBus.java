package fm.lizhi.ocean.wavecenter.infrastructure.eventBus;

import fm.lizhi.ocean.wavecenter.domain.eventBus.EventBus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEvent;
import org.springframework.stereotype.Component;

/**
 * spring事务总线
 * <AUTHOR>
 * @date 2025/3/19 20:52
 */
@Component
public class SpringEventBus implements EventBus {

    @Autowired
    private ApplicationContext applicationContext;

    @Override
    public void publishEvent(ApplicationEvent event) {
        this.applicationContext.publishEvent(event);
    }

}
