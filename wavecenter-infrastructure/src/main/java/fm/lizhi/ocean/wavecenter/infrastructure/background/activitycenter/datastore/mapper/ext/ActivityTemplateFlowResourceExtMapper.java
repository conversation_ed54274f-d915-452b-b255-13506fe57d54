package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityTemplateFlowResource;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface ActivityTemplateFlowResourceExtMapper {

    @Select("SELECT * FROM `activity_template_flow_resource` WHERE `template_id` = #{templateId} ORDER BY `index` ASC")
    List<ActivityTemplateFlowResource> getFlowResourcesByTemplateId(@Param("templateId") long templateId);

    @Update("DELETE FROM `activity_template_flow_resource` WHERE `template_id` = #{templateId}")
    int deleteFlowResourcesByTemplateId(@Param("templateId") long templateId);
}
