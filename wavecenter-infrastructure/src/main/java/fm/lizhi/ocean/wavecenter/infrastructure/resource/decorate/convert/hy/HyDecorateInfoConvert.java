package fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.convert.hy;

import cn.hutool.core.collection.CollUtil;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto;
import fm.lizhi.hy.amusement.protocol.DressUpInfoProto;
import fm.lizhi.hy.vip.protocol.HyMedalBaseV2Proto;
import fm.lizhi.live.pp.idl.officialCertifiedTag.dto.tag.OfficialCertifiedTagDTO;
import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.DecorateInfoBean;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: guoyibin
 * @create: 2025/03/31 12:11
 */
public class HyDecorateInfoConvert {

    private static final int HY_DECORATE_TIME_NO_LIMIT = 10;

    private static final int MEDAL_TIME_NO_LIMIT = 0;

    private static final int OFFICIAL_CERTIFIED_TAG_TIME_NO_LIMIT = 2;

    public static DecorateInfoBean convertToDecorateInfoBean(DressUpGoodsProto.DressUpGoods dressUpGood, String cdnHost, PlatformDecorateTypeEnum platformDecorateTypeEnum) {
        DecorateInfoBean decorateInfoBean = new DecorateInfoBean();
        decorateInfoBean.setDecorateId(dressUpGood.getDressUpInfo().getId());
        decorateInfoBean.setDecorateName(dressUpGood.getDressUpInfo().getDressUpName());
        decorateInfoBean.setDecorateTypeEnum(PlatformDecorateTypeEnum.VEHICLE);
        //TODO:后续黑叶支持webp格式后，需要优先取webp格式字段
        decorateInfoBean.setDecorateImage(getDecorateImageUrl(cdnHost, dressUpGood.getDressUpInfo().getMaterialUrl(),
                                dressUpGood.getDressUpInfo().getThumbUrl(), dressUpGood.getDressUpInfo().getSvgaMaterialUrl()));
        decorateInfoBean.setDecorateExpireTime(dressUpGood.getDressUpInfo().getTimeType() == HY_DECORATE_TIME_NO_LIMIT ? 0 : dressUpGood.getDressUpInfo().getTimeLimit());
        decorateInfoBean.setDecorateTypeEnum(platformDecorateTypeEnum);
        decorateInfoBean.setPreviewUrl(getPreviewUrl(cdnHost, dressUpGood.getDressUpInfo().getMaterialUrl(),
                               dressUpGood.getDressUpInfo().getThumbUrl(), dressUpGood.getDressUpInfo().getSvgaMaterialUrl()));
        return decorateInfoBean;
    }

    public static DecorateInfoBean convertMedalToDecorateInfoBean(HyMedalBaseV2Proto.MedalInfoV2 medalInfo, String cdnHost, PlatformDecorateTypeEnum platformDecorateTypeEnum) {
        DecorateInfoBean decorateInfoBean = new DecorateInfoBean();
        decorateInfoBean.setDecorateId(medalInfo.getId());
        decorateInfoBean.setDecorateName(medalInfo.getMedalName());
        //TODO:后续黑叶支持webp格式后，需要优先取webp格式字段
        decorateInfoBean.setDecorateImage(UrlUtils.addCdnHost(cdnHost, medalInfo.getImageUrl()));
        decorateInfoBean.setDecorateExpireTime(medalInfo.getVaildType() == MEDAL_TIME_NO_LIMIT ? 0 : caculateMinutes(medalInfo.getVaildDay(), medalInfo.getVaildHour(), medalInfo.getVaildMinutes()));
        decorateInfoBean.setDecorateTypeEnum(platformDecorateTypeEnum);
        decorateInfoBean.setPreviewUrl(UrlUtils.addCdnHost(cdnHost, medalInfo.getImageUrl()));
        return decorateInfoBean;
    }

    public static DecorateInfoBean convertToDecorateInfoBean(DressUpInfoProto.DressUpInfo dressUpInfo, String cdnHost, PlatformDecorateTypeEnum platformDecorateTypeEnum) {
        //TODO:后续黑叶支持webp格式后，需要优先取webp格式字段
        DecorateInfoBean decorateInfoBean = new DecorateInfoBean();
        decorateInfoBean.setDecorateId(dressUpInfo.getId());
        decorateInfoBean.setDecorateName(dressUpInfo.getDressUpName());
        decorateInfoBean.setDecorateImage(getDecorateImageUrl(cdnHost, dressUpInfo.getMaterialUrl(),
                                                     dressUpInfo.getThumbUrl(), dressUpInfo.getSvgaMaterialUrl()));
        decorateInfoBean.setDecorateExpireTime(dressUpInfo.getTimeType() == HY_DECORATE_TIME_NO_LIMIT ? 0 : dressUpInfo.getTimeLimit());
        decorateInfoBean.setPreviewUrl(getPreviewUrl(cdnHost, dressUpInfo.getMaterialUrl(),
                                               dressUpInfo.getThumbUrl(), dressUpInfo.getSvgaMaterialUrl()));
        decorateInfoBean.setDecorateTypeEnum(platformDecorateTypeEnum);
        return decorateInfoBean;
    }

    public static DecorateInfoBean convertUserGloryToDecorateInfoBean(OfficialCertifiedTagDTO officialCertifiedTag, String cdnHost, PlatformDecorateTypeEnum platformDecorateTypeEnum) {
        if (officialCertifiedTag == null) {
            return null;
        }
        DecorateInfoBean decorateInfoBean = new DecorateInfoBean();
        decorateInfoBean.setDecorateId(officialCertifiedTag.getId());
        decorateInfoBean.setDecorateName(officialCertifiedTag.getName());
        decorateInfoBean.setDecorateImage(officialCertifiedTag.getTagImage());
        decorateInfoBean.setDecorateExpireTime(OFFICIAL_CERTIFIED_TAG_TIME_NO_LIMIT == officialCertifiedTag.getTimeType() ? 0 : officialCertifiedTag.getEffectiveDuration());
        decorateInfoBean.setPreviewUrl(officialCertifiedTag.getTagImage());
        decorateInfoBean.setDecorateTypeEnum(platformDecorateTypeEnum);
        return decorateInfoBean;
    }

    public static List<DecorateInfoBean> hyResponseOfficialCertifiedList2DecorateDTO(List<OfficialCertifiedTagDTO> list, String cdnHost, PlatformDecorateTypeEnum platformDecorateTypeEnum) {
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(dto -> convertUserGloryToDecorateInfoBean(dto, cdnHost, platformDecorateTypeEnum))
                .collect(Collectors.toList());
    }

    private static Integer caculateMinutes(int vaildDay, int vaildHour, int vaildMinutes) {
        return vaildDay * 24 * 60 + vaildHour * 60 + vaildMinutes;
    }

    private static String getPreviewUrl(String cdnHost, String materialUrl, String thumbUrl, String svgaMaterialUrl) {
        if (StringUtils.isNotBlank(materialUrl) && !materialUrl.toLowerCase().endsWith(".svga") 
            && !materialUrl.toLowerCase().endsWith(".pag") && !materialUrl.toLowerCase().endsWith(".zip")) {
            return UrlUtils.addCdnHost(cdnHost, materialUrl);
        }
        if (StringUtils.isNotBlank(thumbUrl)) {
            return UrlUtils.addCdnHost(cdnHost, thumbUrl);
        }
        if (StringUtils.isNotBlank(svgaMaterialUrl)) {
            return UrlUtils.addCdnHost(cdnHost, svgaMaterialUrl);
        }
        return null;
    }

    private static String getDecorateImageUrl(String cdnHost, String materialUrl, String thumbUrl, String svgaMaterialUrl) {
        if (StringUtils.isNotBlank(materialUrl)) {
            return UrlUtils.addCdnHost(cdnHost, materialUrl);
        }
        if (StringUtils.isNotBlank(thumbUrl)) {
            return UrlUtils.addCdnHost(cdnHost, thumbUrl);
        }
        if (StringUtils.isNotBlank(svgaMaterialUrl)) {
            return UrlUtils.addCdnHost(cdnHost, svgaMaterialUrl);
        }
        return null;
    }
}
