package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.repository;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.Period;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.PlayerAbility;
import fm.lizhi.ocean.wavecenter.domain.grow.repository.GrowPlayerAbilityRepository;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowPlayerAbilityWeek;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowPlayerAbilityWeekCapability;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowPlayerAbilityWeekCapabilityExample;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowPlayerAbilityWeekExample;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.mapper.WcGrowPlayerAbilityWeekCapabilityMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.mapper.WcGrowPlayerAbilityWeekMapper;
import fm.lizhi.ocean.wavecenter.service.grow.ability.manager.GrowPlayerAbilityManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.NonContractManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/9 20:41
 */
@Slf4j
@Component
public class GrowPlayerAbilityRepositoryImpl implements GrowPlayerAbilityRepository {

    @Autowired
    private WcGrowPlayerAbilityWeekMapper abilityWeekMapper;
    @Autowired
    private WcGrowPlayerAbilityWeekCapabilityMapper abilityWeekCapabilityMapper;
    @Autowired
    private FamilyManager familyManager;
    @Autowired
    private NonContractManager nonContractManager;
    @Autowired
    private GrowPlayerAbilityManager growPlayerAbilityManager;


    @Override
    public PlayerAbility getPlayerAbility(Long playerId, Period period) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();

        WcGrowPlayerAbilityWeekExample abilityWeekExample = new WcGrowPlayerAbilityWeekExample();
        abilityWeekExample.createCriteria()
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andPlayerIdEqualTo(playerId)
                .andStartWeekDateEqualTo(period.getStart())
                .andEndWeekDateEqualTo(period.getEnd())
                .andAppIdEqualTo(appId);

        List<WcGrowPlayerAbilityWeek> abilityWeeks = abilityWeekMapper.selectByExample(abilityWeekExample);
        if (CollectionUtils.isEmpty(abilityWeeks)) {
            return null;
        }

        WcGrowPlayerAbilityWeek abilityWeek = abilityWeeks.get(0);
        PlayerAbility playerAbility = new PlayerAbility(abilityWeek.getId(), appId, playerId, period);

        WcGrowPlayerAbilityWeekCapabilityExample capabilityExample = new WcGrowPlayerAbilityWeekCapabilityExample();
        capabilityExample.createCriteria()
                .andPlayerIdEqualTo(playerId)
                .andStartWeekDateEqualTo(period.getStart())
                .andEndWeekDateEqualTo(period.getEnd())
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andAppIdEqualTo(appId);
        List<WcGrowPlayerAbilityWeekCapability> capabilities = abilityWeekCapabilityMapper.selectByExample(capabilityExample);
        for (WcGrowPlayerAbilityWeekCapability capability : capabilities) {
            playerAbility.addCapabilityScore(capability.getCapabilityCode(), capability.getAbilityValue());
        }
        return playerAbility;
    }


    @Override
    public void savePlayerAbility(PlayerAbility playerAbility) {
        UserInFamilyBean userInFamily = familyManager.getUserInFamily(playerAbility.getPlayerId());
        long familyId = userInFamily.getFamilyId() != null ? userInFamily.getFamilyId() : 0;
        long roomId = userInFamily.getNjId() != null ? userInFamily.getNjId() : 0;

        Date playerSignTime = new Date();
        boolean playerFirstSignNjFamily = false;
        try {
            Date signTime = nonContractManager.getPlayerSignTime(playerAbility.getPlayerId(), roomId);
            if (signTime != null) {
                playerSignTime = signTime;
            }
            playerFirstSignNjFamily = nonContractManager.isPlayerFirstSignNjFamily(playerAbility.getPlayerId(), roomId, familyId);
        } catch (Exception e) {
            log.error("savePlayerAbility playerId={} error:", playerAbility.getPlayerId(), e);
        }

        growPlayerAbilityManager.savePlayerAbility(playerAbility, roomId, familyId, playerSignTime, playerFirstSignNjFamily);
    }

}
