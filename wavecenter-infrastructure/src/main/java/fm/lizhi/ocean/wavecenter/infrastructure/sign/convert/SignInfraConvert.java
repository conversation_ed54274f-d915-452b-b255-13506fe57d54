package fm.lizhi.ocean.wavecenter.infrastructure.sign.convert;

import fm.hy.family.bean.player.sign.PlaySignStatus;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.bean.*;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity.*;
import fm.lizhi.ocean.wavecenter.infrastructure.user.convert.UserConvert;
import fm.lizhi.ocean.wavecenter.service.sign.dto.FamilyNjJoinRecordDTO;
import fm.lizhi.ocean.wavecenter.service.sign.dto.SignStatusSyncDTO;
import fm.lizhi.trade.contract.constant.ContractStatus;
import fm.pp.family.constants.FamilyConstant;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/13 16:08
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS
        , uses = UserConvert.class
)
public interface SignInfraConvert {

    SignInfraConvert I = Mappers.getMapper(SignInfraConvert.class);

    @Mappings({
            @Mapping(source = "njId", target = "roomInfo.id"),
            @Mapping(source = "id", target = "contractId"),
            @Mapping(source = "signFinishTime", target = "signDate"),
            @Mapping(source = "expireTime", target = "expireDate"),
            @Mapping(source = "stopTime", target = "stopDate"),
            @Mapping(source = "ppContract", target = "signStatus", qualifiedByName = "ppTransNjSignStatus"),
    })
    RoomSignInfoBean ppContract2Bean(PpContract ppContract);

    List<RoomSignInfoBean> ppContracts2Beans(List<PpContract> ppContracts);

    @Mappings({
            @Mapping(source = "njId", target = "roomInfo.id"),
            @Mapping(source = "id", target = "contractId"),
            @Mapping(source = "signFinishTime", target = "signDate"),
            @Mapping(source = "expireTime", target = "expireDate"),
            @Mapping(source = "stopTime", target = "stopDate"),
            @Mapping(source = "po", target = "signStatus", qualifiedByName = "hyTransNjSignStatus"),
    })
    RoomSignInfoBean hyContract2Bean(HyContract po);

    List<RoomSignInfoBean> hyContracts2Beans(List<HyContract> pos);

    @Mappings({
            @Mapping(source = "njId", target = "roomInfo.id"),
            @Mapping(source = "id", target = "contractId"),
            @Mapping(source = "signFinishTime", target = "signDate"),
            @Mapping(source = "expireTime", target = "expireDate"),
            @Mapping(source = "stopTime", target = "stopDate"),
            @Mapping(source = "po", target = "signStatus", qualifiedByName = "xmTransNjSignStatus"),
    })
    RoomSignInfoBean xmContract2Bean(XmContract po);

    List<RoomSignInfoBean> xmContracts2Beans(List<XmContract> pos);

    @Mappings({
            @Mapping(source = "njId", target = "roomInfo.id"),
            @Mapping(source = "id", target = "contractId"),
            @Mapping(source = "userId", target = "playerInfo.id"),
            @Mapping(source = "startTime", target = "signDate"),
            @Mapping(source = "endTime", target = "expireDate"),
            @Mapping(source = "stopTime", target = "stopDate"),
            @Mapping(source = "po", target = "signStatus", qualifiedByName = "hyTransPlayerSignStatus"),
    })
    SignPlayerInfoBean hyPlayerSign2Bean(HyPlayerSign po);

    List<SignPlayerInfoBean> hyPlayerSigns2Beans(List<HyPlayerSign> pos);

    @Mappings({
            @Mapping(source = "njId", target = "roomInfo.id"),
            @Mapping(source = "id", target = "contractId"),
            @Mapping(source = "userId", target = "playerInfo.id"),
            @Mapping(source = "startTime", target = "signDate"),
            @Mapping(source = "endTime", target = "expireDate"),
            @Mapping(source = "stopTime", target = "stopDate"),
            @Mapping(source = "po", target = "signStatus", qualifiedByName = "ppTransPlayerSignStatus"),
    })
    SignPlayerInfoBean ppPlayerSign2Bean(PpPlayerSign po);

    List<SignPlayerInfoBean> ppPlayerSigns2Beans(List<PpPlayerSign> pos);

    @Mappings({
            @Mapping(source = "njId", target = "roomInfo.id"),
            @Mapping(source = "id", target = "contractId"),
            @Mapping(source = "userId", target = "playerInfo.id"),
            @Mapping(source = "startTime", target = "signDate"),
            @Mapping(source = "endTime", target = "expireDate"),
            @Mapping(source = "stopTime", target = "stopDate"),
            @Mapping(source = "po", target = "signStatus", qualifiedByName = "xmTransPlayerSignStatus"),
    })
    SignPlayerInfoBean xmPlayerSign2Bean(XmPlayerSign po);

    List<SignPlayerInfoBean> xmPlayerSigns2Beans(List<XmPlayerSign> pos);

    @Mappings({
            @Mapping(source = "id", target = "contractId"),
            @Mapping(source = "njId", target = "njUserId"),
            @Mapping(source = "status", target = "status", qualifiedByName = "ppSignStatusTrans"),
    })
    FamilyAndNjContractBean ppContract2ContractBean(PpContract contract);

    List<FamilyAndNjContractBean> ppContracts2ContractBeans(List<PpContract> contracts);

    @Mappings({
            @Mapping(source = "id", target = "contractId"),
            @Mapping(source = "njId", target = "njUserId"),
            @Mapping(source = "status", target = "status", qualifiedByName = "xmSignStatusTrans"),
    })
    FamilyAndNjContractBean xmContract2ContractBean(XmContract contract);

    List<FamilyAndNjContractBean> xmContracts2ContractBeans(List<XmContract> contract);

    @Mappings({
            @Mapping(source = "id", target = "contractId"),
            @Mapping(source = "njId", target = "njUserId"),
            @Mapping(source = "status", target = "status", qualifiedByName = "hySignStatusTrans"),
    })
    FamilyAndNjContractBean hyContract2ContractBean(HyContract contract);

    List<FamilyAndNjContractBean> hyContracts2ContractBeans(List<HyContract> contracts);

    @Mappings({
            @Mapping(source = "unwindContractId", target = "contractId"),
            @Mapping(source = "unwindSignId", target = "signId"),
            @Mapping(source = "njId", target = "njUserId"),
            @Mapping(source = "status", target = "status", qualifiedByName = "ppSignStatusTrans"),
    })
    FamilyAndNjContractBean ppUnwindApply2ContractBean(PpUnwindApply po);

    List<FamilyAndNjContractBean> ppUnwindApplys2ContractBeans(List<PpUnwindApply> pos);

    @Mappings({
            @Mapping(source = "unwindContractId", target = "contractId"),
            @Mapping(source = "unwindSignId", target = "signId"),
            @Mapping(source = "njId", target = "njUserId"),
            @Mapping(source = "status", target = "status", qualifiedByName = "xmSignStatusTrans"),
    })
    FamilyAndNjContractBean xmUnwindApply2ContractBean(XmUnwindApply po);

    List<FamilyAndNjContractBean> xmUnwindApplys2ContractBeans(List<XmUnwindApply> pos);

    @Mappings({
            @Mapping(source = "id", target = "contractId"),
            @Mapping(source = "njId", target = "njUserId"),
            @Mapping(source = "userId", target = "playerUserId"),
            @Mapping(source = "parentId", target = "oldContractId"),
            @Mapping(source = "status", target = "status", qualifiedByName = "xmSignStatusTrans"),
            @Mapping(source = "userType", target = "createUser", qualifiedByName = "transUserType2Role"),
    })
    NjAndPlayerContractBean xmPlayerSignPo2ContractBean(XmPlayerSign po);

    List<NjAndPlayerContractBean> xmPlayerSignPos2ContractBeans(List<XmPlayerSign> po);

    @Mappings({
            @Mapping(source = "id", target = "contractId"),
            @Mapping(source = "njId", target = "njUserId"),
            @Mapping(source = "userId", target = "playerUserId"),
            @Mapping(source = "parentId", target = "oldContractId"),
            @Mapping(source = "status", target = "status", qualifiedByName = "hySignStatusTrans"),
            @Mapping(source = "userType", target = "createUser", qualifiedByName = "transUserType2Role"),
    })
    NjAndPlayerContractBean hyPlayerSignPo2ContractBean(HyPlayerSign po);

    List<NjAndPlayerContractBean> hyPlayerSignPos2ContractBeans(List<HyPlayerSign> pos);

    @Mappings({
            @Mapping(source = "id", target = "contractId"),
            @Mapping(source = "njId", target = "njUserId"),
            @Mapping(source = "userId", target = "playerUserId"),
            @Mapping(source = "parentId", target = "oldContractId"),
            @Mapping(source = "status", target = "status", qualifiedByName = "hySignStatusTrans"),
            @Mapping(source = "userType", target = "createUser", qualifiedByName = "transUserType2Role"),
    })
    NjAndPlayerContractBean ppPlayerSignPo2ContractBean(PpPlayerSign po);

    List<NjAndPlayerContractBean> ppPlayerSignPos2ContractBeans(List<PpPlayerSign> pos);

    @Mappings({
            @Mapping(source = "type", target = "signType"),
            @Mapping(source = "njUserId", target = "roomInfo.id"),
    })
    TodoSignBean familyNjSign2TodoBean(FamilyAndNjContractBean bean);

    List<TodoSignBean> familyNjSigns2TodoBeans(List<FamilyAndNjContractBean> beans);

    @Mappings({
            @Mapping(source = "njUserId", target = "roomInfo.id"),
    })
    RoomSignRecordBean familyNjSign2RecordBean(FamilyAndNjContractBean bean);

    @Mappings({
            @Mapping(source = "playerUserId", target = "playerInfo.id"),
            @Mapping(source = "njUserId", target = "roomInfo.id"),
            @Mapping(source = "type", target = "signType"),
    })
    TodoSignPlayerBean njPlayerContractBean2TodoBean(NjAndPlayerContractBean bean);

    List<TodoSignPlayerBean> njPlayerContractBeans2TodoBeans(List<NjAndPlayerContractBean> beans);

    @Mappings({
            @Mapping(source = "playerUserId", target = "playerInfo.id"),
            @Mapping(source = "njUserId", target = "roomInfo.id"),
    })
    CancelPlayerRecordBean njPlayerContractBean2RecordBean(NjAndPlayerContractBean bean);

    List<CancelPlayerRecordBean> njPlayerContractBeans2RecordBeans(List<NjAndPlayerContractBean> beans);

    @Mappings({
            @Mapping(source = "playerUserId", target = "playerInfo.id"),
    })
    AdminSignPlayerRecordBean njPlayerContractBean2AdminRecordBean(NjAndPlayerContractBean bean);

    List<AdminSignPlayerRecordBean> njPlayerContractBeans2AdminRecordBeans(List<NjAndPlayerContractBean> beans);

    @Mappings({
            @Mapping(source = "njId", target = "njUserId"),
    })
    FamilyAndNjContractBean hyFamilyNj2ContractBean(HyFamilyNj familyNj);

    List<FamilyAndNjContractBean> hyFamilyNjs2ContractBeans(List<HyFamilyNj> familyNj);

    FamilyNjJoinRecordDTO ppFamilyNjPO2DTO(PpFamilyNj po);

    List<FamilyNjJoinRecordDTO> ppFamilyNjPOs2DTOs(List<PpFamilyNj> pos);

    SignStatusSyncDTO signStatusSyncPo2Dto(WcSignStatusSync po);

    List<SignStatusSyncDTO> signStatusSyncPos2Dtos(List<WcSignStatusSync> pos);

    @Named("transUserType2Role")
    default String transUserType2Role(String userType){
        if ("ADMIN".equals(userType)) {
            return RoleEnum.ROOM.getRoleCode();
        }
        if ("PLAYER".equals(userType)) {
            return RoleEnum.PLAYER.getRoleCode();
        }
        if ("FAMILY".equals(userType)) {
            return RoleEnum.FAMILY.getRoleCode();
        }
        return "";
    }

    /**
     * PP签约状态转换
     * @param status
     * @return
     */
    @Named("ppSignStatusTrans")
    default String ppSignStatusTrans(String status){
        if (FamilyConstant.ContractStatus.WAIT_SIGN.getCode().equals(status)) {
            return SignRelationEnum.WAIT_SIGN.getCode();
        } else if (FamilyConstant.ContractStatus.SIGNING.getCode().equals(status)) {
            return SignRelationEnum.SIGNING.getCode();
        } else if (FamilyConstant.ContractStatus.SIGN_SUCCEED.getCode().equals(status)) {
            return SignRelationEnum.SIGN_SUCCESS.getCode();
        } else if (FamilyConstant.ContractStatus.SIGN_FAILED.getCode().equals(status)) {
            return SignRelationEnum.SIGN_FAILED.getCode();
        } else if (FamilyConstant.ContractStatus.REJECT.getCode().equals(status)) {
            return SignRelationEnum.REJECT.getCode();
        } else if (FamilyConstant.ContractStatus.OVERDUE.getCode().equals(status)) {
            return SignRelationEnum.OVERDUE.getCode();
        } else if (FamilyConstant.ContractStatus.STOP_CONTRACT.getCode().equals(status)) {
            return SignRelationEnum.STOP_CONTRACT.getCode();
        }
        else if (FamilyConstant.ContractStatus.OVERDUE_CONTRACT.getCode().equals(status)) {
            return SignRelationEnum.OVERDUE_CONTRACT.getCode();
        }
        else {
            return SignRelationEnum.UNKNOWN.getCode();
        }
    }

    /**
     * PP签约状态转换
     * @param status
     * @return
     */
    @Named("waveSignStatus2pp")
    default String waveSignStatus2pp(SignRelationEnum status){
        switch (status) {
            case WAIT_SIGN:
                return FamilyConstant.ContractStatus.WAIT_SIGN.getCode();
            case SIGNING:
                return FamilyConstant.ContractStatus.SIGNING.getCode();
            case SIGN_SUCCESS:
                return FamilyConstant.ContractStatus.SIGN_SUCCEED.getCode();
            case SIGN_FAILED:
                return FamilyConstant.ContractStatus.SIGN_FAILED.getCode();
            case REJECT:
                return FamilyConstant.ContractStatus.REJECT.getCode();
            case OVERDUE:
                return FamilyConstant.ContractStatus.OVERDUE.getCode();
            case STOP_CONTRACT:
                return FamilyConstant.ContractStatus.STOP_CONTRACT.getCode();
            case OVERDUE_CONTRACT:
                return FamilyConstant.ContractStatus.OVERDUE_CONTRACT.getCode();
            default:
                return null;
        }
    }

    /**
     * xm签约状态转换
     * @param status
     * @return
     */
    @Named("waveSignStatus2xm")
    default String waveSignStatus2xm(SignRelationEnum status){
        switch (status) {
            case SIGNING:
                return ContractStatus.SIGNING.getCode();
            case WAIT_SIGN:
                return fm.lizhi.xm.family.constants.FamilyConstant.ContractStatus.WAIT_SIGN.getCode();
            case SIGN_SUCCESS:
                return fm.lizhi.xm.family.constants.FamilyConstant.ContractStatus.SIGN_SUCCEED.getCode();
            case SIGN_FAILED:
                return fm.lizhi.xm.family.constants.FamilyConstant.ContractStatus.SIGN_FAILED.getCode();
            case REJECT:
                return fm.lizhi.xm.family.constants.FamilyConstant.ContractStatus.REJECT.getCode();
            case OVERDUE:
                return fm.lizhi.xm.family.constants.FamilyConstant.ContractStatus.OVERDUE.getCode();
            case STOP_CONTRACT:
                return fm.lizhi.xm.family.constants.FamilyConstant.ContractStatus.STOP_CONTRACT.getCode();
            default:
                return null;
        }
    }

    /**
     * 西米签约状态转换
     * @param status
     * @return
     */
    @Named("xmSignStatusTrans")
    default String xmSignStatusTrans(String status){
        if (fm.lizhi.xm.family.constants.FamilyConstant.ContractStatus.WAIT_SIGN.getCode().equals(status)
            || ContractStatus.SIGNING.getCode().equals(status)
        ) {
            return SignRelationEnum.WAIT_SIGN.getCode();
        } else if (fm.lizhi.xm.family.constants.FamilyConstant.ContractStatus.SIGN_SUCCEED.getCode().equals(status)) {
            return SignRelationEnum.SIGN_SUCCESS.getCode();
        } else if (fm.lizhi.xm.family.constants.FamilyConstant.ContractStatus.SIGN_FAILED.getCode().equals(status)) {
            return SignRelationEnum.SIGN_FAILED.getCode();
        } else if (fm.lizhi.xm.family.constants.FamilyConstant.ContractStatus.REJECT.getCode().equals(status)) {
            return SignRelationEnum.REJECT.getCode();
        } else if (fm.lizhi.xm.family.constants.FamilyConstant.ContractStatus.OVERDUE.getCode().equals(status)) {
            return SignRelationEnum.OVERDUE.getCode();
        } else if (fm.lizhi.xm.family.constants.FamilyConstant.ContractStatus.STOP_CONTRACT.getCode().equals(status)) {
            return SignRelationEnum.STOP_CONTRACT.getCode();
        } else {
            return SignRelationEnum.UNKNOWN.getCode();
        }
    }

    /**
     * 黑叶签约状态转换
     * @param status
     * @return
     */
    @Named("hySignStatusTrans")
    default String hySignStatusTrans(String status){
        if (fm.hy.family.constants.FamilyConstant.ContractStatus.WAIT_SIGN.getCode().equals(status)
        || fm.hy.family.constants.FamilyConstant.ContractStatus.SIGNING.getCode().equals(status)) {
            return SignRelationEnum.WAIT_SIGN.getCode();
        }
        else if (fm.hy.family.constants.FamilyConstant.ContractStatus.SIGN_SUCCEED.getCode().equals(status)) {
            return SignRelationEnum.SIGN_SUCCESS.getCode();
        }
        else if (fm.hy.family.constants.FamilyConstant.ContractStatus.SIGN_FAILED.getCode().equals(status)) {
            return SignRelationEnum.SIGN_FAILED.getCode();
        }
        else if (fm.hy.family.constants.FamilyConstant.ContractStatus.REJECT.getCode().equals(status)) {
            return SignRelationEnum.REJECT.getCode();
        }
        else if (fm.hy.family.constants.FamilyConstant.ContractStatus.OVERDUE.getCode().equals(status)) {
            return SignRelationEnum.OVERDUE.getCode();
        }
        else if (fm.hy.family.constants.FamilyConstant.ContractStatus.STOP_CONTRACT.getCode().equals(status)) {
            return SignRelationEnum.STOP_CONTRACT.getCode();
        }
        else if (PlaySignStatus.SIGN_CONFIRM.getCode().equals(status)){
            return SignRelationEnum.SIGN_CONFIRM.getCode();
        }
        else if (PlaySignStatus.REVIEW_REJECTED.getCode().equals(status)){
            return SignRelationEnum.REVIEW_REJECTED.getCode();
        }
        else if (PlaySignStatus.CANCEL_CONTRACT.getCode().equals(status)){
            return SignRelationEnum.CANCEL_CONTRACT.getCode();
        }
        else if (PlaySignStatus.SIGN_FAIL.getCode().equals(status)) {
            return SignRelationEnum.SIGN_FAILED.getCode();
        }
        else {
            return SignRelationEnum.UNKNOWN.getCode();
        }
    }

    /**
     * hy签约状态转换
     * @param status
     * @return
     */
    @Named("waveSignStatus2hy")
    default String waveSignStatus2hy(SignRelationEnum status){
        switch (status) {
            case WAIT_SIGN:
                return FamilyConstant.ContractStatus.WAIT_SIGN.getCode();
            case SIGNING:
                return FamilyConstant.ContractStatus.SIGNING.getCode();
            case SIGN_SUCCESS:
                return FamilyConstant.ContractStatus.SIGN_SUCCEED.getCode();
            case SIGN_FAILED:
                return FamilyConstant.ContractStatus.SIGN_FAILED.getCode();
            case REJECT:
                return FamilyConstant.ContractStatus.REJECT.getCode();
            case OVERDUE:
                return FamilyConstant.ContractStatus.OVERDUE.getCode();
            case STOP_CONTRACT:
                return FamilyConstant.ContractStatus.STOP_CONTRACT.getCode();
            case SIGN_CONFIRM:
                return PlaySignStatus.SIGN_CONFIRM.getCode();
            case REVIEW_REJECTED:
                return PlaySignStatus.REVIEW_REJECTED.getCode();
            case CANCEL_CONTRACT:
                return PlaySignStatus.CANCEL_CONTRACT.getCode();
            default:
                return null;
        }
    }

}
