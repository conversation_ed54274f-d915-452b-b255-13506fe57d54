package fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardResourceTypeEnum;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestGetFamilyWeekAwardV1;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestGetFamilyWeekAwardV2;
import fm.lizhi.ocean.wavecenter.api.award.family.response.ResponseGetFamilyWeekAwardV1;
import fm.lizhi.ocean.wavecenter.api.award.family.response.ResponseGetFamilyWeekAwardV2;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.config.BizCommonConfig;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.dao.FamilyAwardDeliverRecordDao;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilyAwardDeliverItem;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilyAwardDeliverRecord;
import fm.lizhi.ocean.wavecenter.service.award.family.manager.FamilyAwardManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

@Slf4j
@Component
public class FamilyAwardManagerImpl implements FamilyAwardManager {

    @Autowired
    private CommonConfig commonConfig;

    @Autowired
    private FamilyAwardDeliverRecordDao familyAwardDeliverRecordDao;

    @Override
    public Result<ResponseGetFamilyWeekAwardV1> getFamilyWeekAwardV1(RequestGetFamilyWeekAwardV1 request) {
        Integer appId = request.getAppId();
        Long familyId = request.getFamilyId();
        Date awardStartTime = new Date(request.getAwardStartTime());
        WcFamilyAwardDeliverRecord deliverRecord = familyAwardDeliverRecordDao.getDeliverRecord(appId, familyId, awardStartTime);
        if (deliverRecord == null) {
            log.info("No deliver record V1 found for appId={}, familyId={}, awardStartTime={}", appId, familyId, awardStartTime);
            return RpcResult.success(null);
        }
        Long recordId = deliverRecord.getId();
        Map<Integer, WcFamilyAwardDeliverItem> itemMap = familyAwardDeliverRecordDao.getDeliverItemsByRecordIdAsResourceTypeMap(recordId);
        ResponseGetFamilyWeekAwardV1 response = new ResponseGetFamilyWeekAwardV1();
        // 推荐卡
        WcFamilyAwardDeliverItem recommendCardItem = itemMap.get(FamilyAwardResourceTypeEnum.RECOMMEND_CARD.getValue());
        Integer recommendCardNumber = recommendCardItem != null ? recommendCardItem.getResourceNumber() : 0;
        response.setRecommendCardNumber(recommendCardNumber);
        // 座驾
        WcFamilyAwardDeliverItem vehicleItem = itemMap.get(FamilyAwardResourceTypeEnum.VEHICLE.getValue());
        Long vehicleId = vehicleItem != null ? vehicleItem.getResourceId() : null;
        String vehicleName = vehicleItem != null ? vehicleItem.getResourceName() : null;
        String vehicleImagePath = vehicleItem != null ? vehicleItem.getResourceImagePath() : null;
        response.setVehicleId(vehicleId);
        response.setVehicleName(vehicleName);
        response.setVehicleImage(pathToUrlOrNull(appId, vehicleImagePath));
        // 勋章
        WcFamilyAwardDeliverItem medalItem = itemMap.get(FamilyAwardResourceTypeEnum.MEDAL.getValue());
        Long medalId = medalItem != null ? medalItem.getResourceId() : null;
        String medalName = medalItem != null ? medalItem.getResourceName() : null;
        String medalImagePath = medalItem != null ? medalItem.getResourceImagePath() : null;
        response.setMedalId(medalId);
        response.setMedalName(medalName);
        response.setMedalImage(pathToUrlOrNull(appId, medalImagePath));
        // 短号
        WcFamilyAwardDeliverItem shortNumberItem = itemMap.get(FamilyAwardResourceTypeEnum.SHORT_NUMBER.getValue());
        Long shortNumberId = shortNumberItem != null ? shortNumberItem.getResourceId() : null;
        String shortNumberName = shortNumberItem != null ? shortNumberItem.getResourceName() : null;
        response.setShortNumberId(shortNumberId);
        response.setShortNumberName(shortNumberName);
        return RpcResult.success(response);
    }

    private String pathToUrlOrNull(Integer appId, String path) {
        if (StringUtils.isBlank(path)) {
            return null;
        }
        BizCommonConfig bizConfig = commonConfig.getBizConfig(appId);
        if (bizConfig == null) {
            return null;
        }
        return UrlUtils.addHostOrEmpty(path, bizConfig.getCdnHost());
    }

    @Override
    public Result<ResponseGetFamilyWeekAwardV2> getFamilyWeekAwardV2(RequestGetFamilyWeekAwardV2 request) {
        Integer appId = request.getAppId();
        Long familyId = request.getFamilyId();
        Date awardStartTime = new Date(request.getAwardStartTime());
        WcFamilyAwardDeliverRecord deliverRecord = familyAwardDeliverRecordDao.getDeliverRecord(appId, familyId, awardStartTime);
        if (deliverRecord == null) {
            log.info("No deliver record V2 found for appId={}, familyId={}, awardStartTime={}", appId, familyId, awardStartTime);
            return RpcResult.success(null);
        }
        Long recordId = deliverRecord.getId();
        Map<Integer, WcFamilyAwardDeliverItem> itemMap = familyAwardDeliverRecordDao.getDeliverItemsByRecordIdAsResourceTypeMap(recordId);
        ResponseGetFamilyWeekAwardV2 response = new ResponseGetFamilyWeekAwardV2();
        // 等级推荐卡
        WcFamilyAwardDeliverItem levelRecommendCardItem = itemMap.get(FamilyAwardResourceTypeEnum.PP_LEVEL_RECOMMEND_CARD.getValue());
        Integer levelRecommendCardNumber = levelRecommendCardItem != null ? levelRecommendCardItem.getResourceNumber() : 0;
        response.setLevelRecommendCardNumber(levelRecommendCardNumber);
        // 流水增长推荐卡
        WcFamilyAwardDeliverItem flowRecommendCardItem = itemMap.get(FamilyAwardResourceTypeEnum.PP_FLOW_GROWTH_RECOMMEND_CARD.getValue());
        Integer flowGrowthRecommendCardNumber = flowRecommendCardItem != null ? flowRecommendCardItem.getResourceNumber() : 0;
        response.setFlowGrowthRecommendCardNumber(flowGrowthRecommendCardNumber);
        // 新厅留存推荐卡
        WcFamilyAwardDeliverItem newRoomRetainRecommendCardItem = itemMap.get(FamilyAwardResourceTypeEnum.PP_NEW_ROOM_RETAIN_RECOMMEND_CARD.getValue());
        Integer newRoomRetainRecommendCardNumber = newRoomRetainRecommendCardItem != null ? newRoomRetainRecommendCardItem.getResourceNumber() : 0;
        response.setNewRoomRetainRecommendCardNumber(newRoomRetainRecommendCardNumber);
        // 0流失厅推荐卡
        WcFamilyAwardDeliverItem zeroLostRoomRecommendCardItem = itemMap.get(FamilyAwardResourceTypeEnum.PP_ZERO_LOST_ROOM_RECOMMEND_CARD.getValue());
        Integer zeroLostRoomRecommendCardNumber = zeroLostRoomRecommendCardItem != null ? zeroLostRoomRecommendCardItem.getResourceNumber() : 0;
        response.setZeroLostRoomRecommendCardNumber(zeroLostRoomRecommendCardNumber);
        // 特殊推荐卡
        WcFamilyAwardDeliverItem specialRecommendCardItem = itemMap.get(FamilyAwardResourceTypeEnum.PP_SPECIAL_RECOMMEND_CARD.getValue());
        Integer specialRecommendCardNumber = specialRecommendCardItem != null ? specialRecommendCardItem.getResourceNumber() : 0;
        response.setSpecialRecommendCardNumber(specialRecommendCardNumber);
        // 总推荐卡数
        Integer totalRecommendCardNumber = levelRecommendCardNumber + flowGrowthRecommendCardNumber + newRoomRetainRecommendCardNumber + zeroLostRoomRecommendCardNumber + specialRecommendCardNumber;
        response.setTotalRecommendCardNumber(totalRecommendCardNumber);
        // 等级新厅名额
        WcFamilyAwardDeliverItem levelNewRoomItem = itemMap.get(FamilyAwardResourceTypeEnum.PP_LEVEL_NEW_ROOM.getValue());
        Integer levelNewRoomNumber = levelNewRoomItem != null ? levelNewRoomItem.getResourceNumber() : 0;
        response.setLevelNewRoomNumber(levelNewRoomNumber);
        // 流水涨幅新厅名额
        WcFamilyAwardDeliverItem flowGrowthNewRoomItem = itemMap.get(FamilyAwardResourceTypeEnum.PP_FLOW_GROWTH_NEW_ROOM.getValue());
        Integer flowGrowthNewRoomNumber = flowGrowthNewRoomItem != null ? flowGrowthNewRoomItem.getResourceNumber() : 0;
        response.setFlowGrowthNewRoomNumber(flowGrowthNewRoomNumber);
        // 流失厅新厅名额
        WcFamilyAwardDeliverItem lostRoomNewRoomItem = itemMap.get(FamilyAwardResourceTypeEnum.PP_LOST_ROOM_NEW_ROOM.getValue());
        Integer lostRoomNewRoomNumber = lostRoomNewRoomItem != null ? lostRoomNewRoomItem.getResourceNumber() : 0;
        response.setLostRoomNewRoomNumber(lostRoomNewRoomNumber);
        // 新厅留存新厅名额
        WcFamilyAwardDeliverItem newRoomRetainNewRoomItem = itemMap.get(FamilyAwardResourceTypeEnum.PP_NEW_ROOM_RETAIN_NEW_ROOM.getValue());
        Integer newRoomRetainNewRoomNumber = newRoomRetainNewRoomItem != null ? newRoomRetainNewRoomItem.getResourceNumber() : 0;
        response.setNewRoomRetainNewRoomNumber(newRoomRetainNewRoomNumber);
        // 总新厅名额数, 如果总和小于0则置为0. 后续改为直接查execution表, 里面存的总数是调整过的.
        Integer totalNewRoomNumber = Math.max(levelNewRoomNumber + flowGrowthNewRoomNumber + lostRoomNewRoomNumber + newRoomRetainNewRoomNumber, 0);
        response.setTotalNewRoomNumber(totalNewRoomNumber);
        return RpcResult.success(response);
    }
}
