package fm.lizhi.ocean.wavecenter.infrastructure.live.manager;

import fm.lizhi.ocean.wavecenter.api.live.bean.GuildRoomDayStats;
import fm.lizhi.ocean.wavecenter.api.live.bean.GuildRoomDayStatsSummaryRes;
import fm.lizhi.ocean.wavecenter.api.live.bean.RoomDayStatsSummaryRes;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.RoomDayCalendarEntity;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.RoomDayStatsDetailEntity;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.RoomDayStatsEntity;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcRoomCheckInDayStats;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.mapper.WcPlayerCheckInDayStatsMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.mapper.WcRoomCheckInDayStatsMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.po.RoomCheckPlayerPo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/21 18:02
 */
@Component
@Deprecated
public class HistoryCheckDayDataManager implements ICheckInDayRouter{

    @Autowired
    private WcRoomCheckInDayStatsMapper wcRoomCheckInDayStatsMapper;
    @Autowired
    private WcPlayerCheckInDayStatsMapper wcPlayerCheckInDayStatsMapper;

    @Override
    public List<Long> guildRoomDayAllNjId(Integer appId, Long familyId, Long njId, Date startDate, Date endDate) {
        return wcRoomCheckInDayStatsMapper.guildRoomDayAllNjId(appId, familyId, njId, startDate, endDate);
    }

    @Override
    public List<WcRoomCheckInDayStats> guildRoomDayDetail(Integer appId, Long familyId, Long njId, Date startDate, Date endDate) {
        return wcRoomCheckInDayStatsMapper.guildRoomDayDetail(appId, familyId, njId, startDate, endDate);
    }

    @Override
    public List<RoomCheckPlayerPo> guildRoomDayDetailCheckPlayer(Integer appId, Long familyId, Long njId, Date startDate, Date endDate) {
        return Collections.emptyList();
    }

    @Override
    public List<RoomDayCalendarEntity> roomCalendar(Integer appId, Long njId, Date startDate, Date endDate) {
        return wcRoomCheckInDayStatsMapper.roomCalendar(appId, njId, startDate, endDate);
    }

    @Override
    public RoomDayStatsSummaryRes roomDaySummary(Integer appId, Long njId, Date startDate, Date endDate) {
        return wcRoomCheckInDayStatsMapper.roomDaySummary(appId, njId, startDate, endDate);
    }

    @Override
    public List<GuildRoomDayStats> guildRoomDayStats(Integer appId, Long familyId, Long njId, Date startDate, Date endDate) {
        return wcRoomCheckInDayStatsMapper.guildRoomDayStats(appId, familyId, njId, startDate, endDate);
    }

    @Override
    public GuildRoomDayStatsSummaryRes guildRoomDaySummary(Integer appId, Long familyId, Long njId, Date startDate, Date endDate) {
        return wcRoomCheckInDayStatsMapper.guildRoomDaySummary(appId, familyId, njId, startDate, endDate);
    }

    @Override
    public List<Long> roomDayStatsUserId(Integer appId, Long njId, Long userId, Date startDate, Date endDate) {
        return wcPlayerCheckInDayStatsMapper.roomDayStatsUserId(appId, njId, userId, startDate, endDate);
    }

    @Override
    public List<RoomDayStatsDetailEntity> roomDayStatsDetail(Integer appId, Long njId, Long userId, Date startDate, Date endDate) {
        return wcPlayerCheckInDayStatsMapper.roomDayStatsDetail(appId, njId, userId, startDate, endDate);
    }

    @Override
    public List<RoomDayStatsEntity> roomDayStats(Integer appId, Long njId, Long userId, Date startDate, Date endDate) {
        return wcPlayerCheckInDayStatsMapper.roomDayStats(appId, njId, userId, startDate, endDate);
    }
}
