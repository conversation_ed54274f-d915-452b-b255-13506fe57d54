package fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.dao;

import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcDataFamilyWeekData;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.mapper.ext.WcDataFamilyWeekDataExtMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;

@Slf4j
@Repository
public class FamilyWeekDataDao {

    @Autowired
    private WcDataFamilyWeekDataExtMapper wcDataFamilyWeekDataExtMapper;

    /**
     * 获取公会周数据
     *
     * @param appId     应用id
     * @param familyId  公会id
     * @param startTime 开始时间, 自然周的开始时间, 周一的00:00:00.000
     * @return 公会周数据
     */
    public WcDataFamilyWeekData getFamilyWeekData(int appId, long familyId, Date startTime) {
        return wcDataFamilyWeekDataExtMapper.getFamilyWeekData(appId, familyId, startTime);
    }
}
