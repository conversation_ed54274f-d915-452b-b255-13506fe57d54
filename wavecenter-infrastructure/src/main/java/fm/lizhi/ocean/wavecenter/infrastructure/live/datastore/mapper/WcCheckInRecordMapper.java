package fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.common.datastore.mysql.constant.ParamContants;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.common.datastore.mysql.mybatis.sqlprovider.BaseDeleteProvider;
import fm.lizhi.common.datastore.mysql.mybatis.sqlprovider.BaseInsertProvider;
import fm.lizhi.common.datastore.mysql.mybatis.sqlprovider.BaseSelectProvider;
import fm.lizhi.common.datastore.mysql.mybatis.sqlprovider.BaseUpdateProvider;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.*;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcCheckInRecordMapper {

    @Select({
            "<script>" +
                    "select sum(income) as income , sum(charm) as charm ,   sum(up_guest_dur) as upGuestDur ,sum(`status`) as seatOrder , user_id  from wavecenter_check_in_record where app_id = #{appId} and  roomId = #{room_id} and  start_time &gt;= #{startDate} and start_time &lt;= #{endDate}  GROUP BY user_id " +
                    "</script>"
    })
    List<RoomHourStatsEntity> roomHourStats(@Param("appId") Integer appId, @Param("roomId") Long roomId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);


    @Select({
            "<script>" +
                    " select *  from wavecenter_check_in_record  where app_id = #{appId}    " +
                    "<if test=' null != playerId and playerId > 0 '>" +
                    " and  user_id  = #{playerId} " +
                    "</if>" +
                    "<if test=' null != roomId and roomId > 0 '>" +
                    " and  nj_id  = #{roomId} " +
                    "</if>" +
                    "and  start_time = #{startDate} and end_time = #{endDate}  " +
                    "</script>"
    })
    List<WcCheckInRecord> hourDetail(@Param("appId") Integer appId, @Param("roomId") Long roomId, @Param("playerId") Long playerId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);


    @Select({
            "<script>" +
                    " select *  from wavecenter_check_in_record  where app_id = #{appId}  and  nj_id = #{roomId} and  start_time &gt;= #{startDate} and start_time &lt;= #{endDate}  " +
                    "</script>"
    })
    List<WcCheckInRecord> roomHourDetail(@Param("appId") Integer appId, @Param("roomId") Long roomId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);


    @Select({
            "<script>" +
                    "select sum(income) as income , sum(charm) as charm , sum(status) as seatOrder ,  nj_id  from wavecenter_check_in_record where app_id = #{appId} and  family_id = #{familyId} and  start_time &gt;= #{startDate} and start_time &lt;= #{endDate}  GROUP BY nj_id  " +
                    "</script>"
    })
    List<GuildRoomHourStatsEntity> familyRoomHourStats(@Param("appId") Integer appId, @Param("familyId") Long familyId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);


    @Select({
            "<script>" +
                    "select *  from wavecenter_check_in_record  where app_id = #{appId}  and  family_id = #{familyId} and  start_time &gt;=  #{startDate} and start_time &lt;= #{endDate}  " +
                    "</script>"
    })
    List<WcCheckInRecord> guildRoomHourDetail(@Param("appId") Integer appId, @Param("familyId") Long familyId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);


    @Select({
            "<script>" +
                    " select sum(income) as income , sum(charm) as charm ,   sum(status) as  seatOrder ,  sum( CASE WHEN status = 1 THEN 1 ELSE 0 END ) as checkPlayerNumber from wavecenter_check_in_record  where  family_id = #{familyId} and  app_id = #{appId}  and income > 0  and  `start_time` &gt;= #{startDate} and `start_time` &lt;= #{endDate}    " +
                    "</script>"
    })
    GuildRoomHourSummaryEntity guildRoomHourSummary(@Param("appId") Integer appId, @Param("familyId") Long familyId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);


    @Select({
            "<script>" +
                    " select sum(income) as income , sum(charm) as charm ,   sum(status) as  seatOrder , sum( is_host ) as hostCnt from wavecenter_check_in_record  where  nj_id = #{njId} and  app_id = #{appId}  and income > 0  and  `start_time` &gt;= #{startDate} and `start_time` &lt;= #{endDate}    " +
                    "</script>"
    })
    RoomHourSummaryEntity roomHourSummary(@Param("appId") Integer appId, @Param("njId") Long njId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);


    @Select({
            "<script>" +
                    " select count(DISTINCT(user_id))   from wavecenter_check_in_record  where  family_id = #{familyId} and  app_id = #{appId}  and   `start_time` &gt;= #{startDate} and `start_time` &lt;= #{endDate}    " +
                    "</script>"
    })
    Integer guildRoomHourIncomePlayerSummary(@Param("appId") Integer appId, @Param("familyId") Long familyId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);


    @Select({
            "<script>" +
                    " select count(DISTINCT(user_id))   from wavecenter_check_in_record  where  nj_id = #{njId} and  app_id = #{appId}  and   `start_time` &gt;= #{startDate} and `start_time` &lt;= #{endDate}    " +
                    "</script>"
    })
    Integer roomHourIncomePlayerSummary(@Param("appId") Integer appId, @Param("njId") Long njId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);


    /**
     * 根据实体对象的字段值查询多条记录，查询条件会跳过NULL值的字段。
     *
     * @param entity 实体对象
     * @return 实体对象列表
     */
    @SelectProvider(type = BaseSelectProvider.class, method = "select")
    List<WcCheckInRecord> selectMany(WcCheckInRecord entity);

    /**
     * 根据实体对象的字段值查询单条记录，查询条件会跳过NULL值的字段。
     *
     * @param entity 实体对象
     * @return 单个实体对象
     */
    @SelectProvider(type = BaseSelectProvider.class, method = "select")
    WcCheckInRecord selectOne(WcCheckInRecord entity);

    /**
     * 根据实体对象中主键（{@link javax.persistence.Id}标注）字段查询单条数据。
     *
     * @param entity 实体对象
     * @return 单个实体对象
     */
    @SelectProvider(type = BaseSelectProvider.class, method = "selectByPrimaryKey")
    WcCheckInRecord selectByPrimaryKey(WcCheckInRecord entity);

    /**
     * 根据实体对象的字段值查询分页记录，查询条件会跳过NULL值的字段。
     *
     * @param entity 实体对象
     * @return 分页数据
     */
    @SelectProvider(type = BaseSelectProvider.class, method = "selectPage")
    PageList<WcCheckInRecord> selectPage(@Param(ParamContants.ENTITIE) WcCheckInRecord entity, @Param(ParamContants.PAGE_NUMBER) int pageNumber, @Param(ParamContants.PAGE_SIZE) int pageSize);

    /**
     * 根据实体对象中主键字段（{@link javax.persistence.Id}标注）删除数据。
     *
     * @param entity 实体对象
     * @return 影响行数
     */
    @DeleteProvider(type = BaseDeleteProvider.class, method = "deleteByPrimaryKey")
    int deleteByPrimaryKey(WcCheckInRecord entity);

    /**
     * 将实体对象写入数据库，会跳过NULL值的字段。<br/>
     * 如果主键字段（{@link javax.persistence.Id}标注）有设置{@link javax.persistence.GeneratedValue}时，会自动生成主键并设置到实体对象中。
     *
     * @param entity 实体对象
     * @return 影响行数
     */
    @InsertProvider(type = BaseInsertProvider.class, method = "insert")
    int insert(WcCheckInRecord entity);

    /**
     * 批量将实体对象写入数据库（不会跳过NULL值)。<br/>
     * 如果字段有设置{@link javax.persistence.Id}和{@link javax.persistence.GeneratedValue}时，会自动生成值，并设置到实体类实例中。
     *
     * @param entities 实体对象列表
     * @return 影响行数
     */
    @InsertProvider(type = BaseInsertProvider.class, method = "batchInsert")
    int batchInsert(@Param(ParamContants.ENTITIES) List<WcCheckInRecord> entities);

    /**
     * 根据实体类中主键（{@link javax.persistence.Id}标注的字段）更新数据，会跳过NULL值的字段。
     *
     * @param entity 实体对象
     * @return 影响行数
     */
    @UpdateProvider(type = BaseUpdateProvider.class, method = "updateByPrimaryKey")
    int updateByPrimaryKey(WcCheckInRecord entity);

    /**
     * 根据example类生成WHERE条件查询总记录条数
     *
     * @param example
     * @return
     */
    @SelectProvider(type = BaseSelectProvider.class, method = "countByExample")
    long countByExample(WcCheckInRecordExample example);

    /**
     * 根据example类生成WHERE条件删除记录
     *
     * @param example
     * @return
     */
    @DeleteProvider(type = BaseDeleteProvider.class, method = "deleteByExample")
    long deleteByExample(WcCheckInRecordExample example);

    /**
     * 根据example类生成WHERE条件查询记录数
     *
     * @param example
     * @return
     */
    @SelectProvider(type = BaseSelectProvider.class, method = "selectByExample")
    List<WcCheckInRecord> selectByExample(WcCheckInRecordExample example);

    /**
     * 根据example类生成WHERE条件查询分页记录数
     *
     * @param example
     * @param pageNumber 页码
     * @param pageSize   每页数据大小
     * @return
     */
    @SelectProvider(type = BaseSelectProvider.class, method = "pageByExample")
    PageList<WcCheckInRecord> pageByExample(@Param(ParamContants.EXAMPLE) WcCheckInRecordExample example, @Param(ParamContants.PAGE_NUMBER) int pageNumber, @Param(ParamContants.PAGE_SIZE) int pageSize);

    /**
     * 根据example类生成WHERE条件更新记录数，会跳过实体类对象中的NULL值的字段。<br/>
     *
     * @param entity  实体类对象
     * @param example
     * @return
     */
    @UpdateProvider(type = BaseUpdateProvider.class, method = "updateByExample")
    int updateByExample(@Param(ParamContants.ENTITIE) WcCheckInRecord entity, @Param(ParamContants.EXAMPLE) WcCheckInRecordExample example);


}
