package fm.lizhi.ocean.wavecenter.infrastructure.sign.manager;

import cn.hutool.core.util.DesensitizedUtil;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.grow.level.bean.FamilyLevelConfigBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.EnterpriseInfoBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.GuildFullInfoBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignAuthStatusEnum;
import fm.lizhi.ocean.wavecenter.api.user.bean.*;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PayTenantCodeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.IContractRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.IFamilyRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.IUserFamilyRemote;
import fm.lizhi.ocean.wavecenter.service.grow.dto.FamilyLevelDTO;
import fm.lizhi.ocean.wavecenter.service.grow.manager.FamilyLevelConfigManager;
import fm.lizhi.ocean.wavecenter.service.grow.manager.FamilyLevelManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.PlayerSignInfoDto;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import fm.lizhi.trade.contract.api.AuthService;
import fm.lizhi.trade.contract.api.BankCardService;
import fm.lizhi.trade.contract.constant.AuthStatus;
import fm.lizhi.trade.contract.protocol.AuthProto;
import fm.lizhi.trade.contract.protocol.BankCardProto;
import fm.lizhi.trade.contract.protocol.CommonProto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/10 17:36
 */
@Slf4j
@Component
public class FamilyManagerImpl implements FamilyManager {

    @Autowired
    private IUserFamilyRemote iUserFamilyRemote;
    @Autowired
    private IContractRemote iContractRemote;
    @Autowired
    private UserManager userManager;
    @Autowired
    private AuthService authService;
    @Autowired
    private BankCardService bankCardService;
    @Autowired
    private IFamilyRemote familyRemote;
    @Autowired
    private FamilyLevelManager familyLevelRecordManager;
    @Autowired
    private FamilyLevelConfigManager familyLevelConfigManager;

    @Override
    public Optional<Long> playerCurSignNj(long userId) {
        return iUserFamilyRemote.playerCurSignNj(userId);
    }

    @Override
    public Optional<FamilyBean> getFamilyByUserId(long userId) {
        return iUserFamilyRemote.getFamilyByUserId(userId);
    }

    @Override
    public Optional<FamilyBean> getUserFamily(long userId) {
        return iUserFamilyRemote.getUserFamily(userId);
    }

    @Override
    public UserInFamilyBean getUserInFamily(long userId) {
        return iUserFamilyRemote.getUserInFamily(userId);
    }

    @Override
    public Optional<RoomSignBean> getRoomSign(long familyId, long njId) {
        Optional<RoomSignBean> roomSign = iContractRemote.getRoomSign(familyId, njId);
        if (!roomSign.isPresent()) {
            log.warn("getRoomSign error familyId={},njId={}",familyId,njId);
            return Optional.empty();
        }
        RoomSignBean roomSignBean = roomSign.get();
        List<SimpleUserDto> simpleUserList = userManager.getSimpleUserByIds(Collections.singletonList(roomSignBean.getId()));
        if (CollectionUtils.isEmpty(simpleUserList)) {
            return Optional.empty();
        }
        SimpleUserDto simpleUserDto = simpleUserList.get(0);
        roomSignBean.setName(simpleUserDto.getName());
        roomSignBean.setBand(simpleUserDto.getBand());
        roomSignBean.setPhoto(simpleUserDto.getAvatar());
        return Optional.of(roomSignBean);
    }

    @Override
    public Optional<Long> getRoomBestFamily(long roomId) {
        return iContractRemote.getRoomBestFamily(roomId);
    }

    @Override
    public Optional<Long> getRoomSignFamilyInDate(long roomId, Date date) {
        return iContractRemote.getRoomSignFamilyInDate(roomId, date);
    }

    @Override
    public Optional<FamilyAuthBean> getUserFamilyAuth(long familyId) {

        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Optional<FamilyBean> familyOp = iUserFamilyRemote.getFamily(familyId);
        if (!familyOp.isPresent()) {
            return Optional.empty();
        }

        FamilyAuthBean bean = new FamilyAuthBean();
        bean.setFamilyId(familyId);
        bean.setFamilyName(familyOp.get().getFamilyName());
        bean.setFamilyPhotoUrl(familyOp.get().getFamilyIconUrl());
        bean.setCreateTime(familyOp.get().getCreateTime().getTime());

        String contractTenantCode = PayTenantCodeEnum.getPayTenantCode(appId);
        Result<AuthProto.ResponseGetEnterpriseInfo> res = authService.getEnterpriseInfo(contractTenantCode, familyId);
        if (RpcResult.isFail(res)) {
            log.warn("getEnterpriseInfo fail familyId={},code={},rCode={}", familyId, contractTenantCode, res.rCode());
        } else {
            CommonProto.EnterpriseInfo enterpriseInfo = res.target().getEnterpriseInfo();
            bean.setAuthType(transAuthType(enterpriseInfo.getStatus()));
            bean.setAuthCompany(enterpriseInfo.getEnterpriseName());
        }

        //家族长波段号
        Long familyUserId = familyOp.get().getUserId();
        List<SimpleUserDto> users = userManager.getSimpleUserByIds(Collections.singletonList(familyUserId));
        if (CollectionUtils.isNotEmpty(users)) {
            log.info("user found success");
            SimpleUserDto userDto = users.get(0);
            bean.setFamilyUserBand(userDto.getBand());
            bean.setFamilyUserName(userDto.getName());
        }

        // 家族等级
        Optional<FamilyLevelDTO> familyLevel = familyLevelRecordManager.getFamilyLevel(familyId, MyDateUtil.getLastWeekStartDay());
        if (familyLevel.isPresent()) {
            bean.setFamilyLevelId(familyLevel.get().getLevelId());
            FamilyLevelConfigBean levelConfig = familyLevelConfigManager.getLevelConfig(familyLevel.get().getLevelId());
            if (levelConfig != null) {
                FamilyLevelInfoBean familyLevelInfo = new FamilyLevelInfoBean();
                familyLevelInfo.setLevelName(levelConfig.getLevelName());
                familyLevelInfo.setLevelIcon(levelConfig.getLevelIcon());
                familyLevelInfo.setLevelMedal(levelConfig.getLevelMedal());
                familyLevelInfo.setThemColor(levelConfig.getThemColor());
                familyLevelInfo.setBackgroundColor(levelConfig.getBackgroundColor());
                bean.setFamilyLevelInfo(familyLevelInfo);
            }
        }

        return Optional.of(bean);
    }

    @Override
    public Optional<PlayerSignInfoDto> getLatestSignRecord(List<Long> roomIds, long userId) {
        return iContractRemote.getLatestSignRecord(roomIds, userId);
    }

    private Integer transAuthType(String code) {
        AuthStatus authStatus = AuthStatus.getByCode(code);
        if (authStatus == null) {
            return null;
        }
        return authStatus.getOutCode();
    }

    @Override
    public Optional<Long> getPlayerBestFamily(long playerId) {
        return iContractRemote.getPlayerBestFamily(playerId);
    }

    @Override
    public Optional<Long> getUserBestNj(long userId) {
        return iContractRemote.getUserBestNj(userId);
    }

    @Override
    public Long getPlayerLastRoom(int appId, long familyId, long playerId) {
        return iContractRemote.getPlayerLastRoom(familyId, playerId);
    }

    @Override
    public Optional<FamilyBean> getFamily(int appId, long familyId) {
        return iUserFamilyRemote.getFamily(familyId);
    }

    @Override
    public Optional<FamilyBean> getFamilyByCache(long familyId) {
        return iUserFamilyRemote.getFamilyByCache(familyId);
    }

    @Override
    public Optional<Long> getRoomBestFamilyByCache(long roomId) {
        return iContractRemote.getRoomBestFamilyByCache(roomId);
    }

    @Override
    public Set<Long> getSignNjIds(Long familyId) {
        if (familyId == null) {
            return Collections.emptySet();
        }

        List<RoomSignBean> beanList = iContractRemote.getAllSingGuildRoomsList(familyId);
        if (CollectionUtils.isEmpty(beanList)) {
            LogContext.addResLog("signNjList is empty");
            return Collections.emptySet();
        }

        return beanList.stream().map(UserBean::getId).collect(Collectors.toSet());
    }

    @Override
    public GuildFullInfoBean getFullInfo(long familyId) {
        GuildFullInfoBean guildFullInfoBean = new GuildFullInfoBean();

        //公会名称
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Optional<FamilyBean> familyOp = iUserFamilyRemote.getFamily(familyId);
        if (!familyOp.isPresent()) {
            return guildFullInfoBean;
        }
        FamilyBean familyBean = familyOp.get();
        guildFullInfoBean.setFamilyName(familyBean.getFamilyName());

        //波段号
        Long familyUserId = familyBean.getUserId();
        List<SimpleUserDto> users = userManager.getSimpleUserByIds(Collections.singletonList(familyUserId));
        if (CollectionUtils.isNotEmpty(users)) {
            log.info("user found success");
            SimpleUserDto userDto = users.get(0);
            guildFullInfoBean.setFamilyBand(userDto.getBand());
        }

        //公会信息
        String contractTenantCode = PayTenantCodeEnum.getPayTenantCode(appId);
        Result<AuthProto.ResponseGetEnterpriseInfo> res = authService.getEnterpriseInfo(contractTenantCode, familyId);
        if (RpcResult.isSuccess(res)) {
            CommonProto.EnterpriseInfo enterpriseInfo = res.target().getEnterpriseInfo();
            //认证信息
            guildFullInfoBean.setAuthType(transAuthType(enterpriseInfo.getStatus()));
            guildFullInfoBean.setAuthCompany(enterpriseInfo.getEnterpriseName());

            //联系人
            int isLegalPerson = enterpriseInfo.getIsLegalPerson();
            if (isLegalPerson == 1) {
                //法人
                guildFullInfoBean.setBusinessEntity(enterpriseInfo.getName());
                guildFullInfoBean.setBusinessEntityIdCard(DesensitizedUtil.idCardNum(enterpriseInfo.getIdentityNo(), 2, 4));
            } else {
                //经办人
                guildFullInfoBean.setLinkUserName(enterpriseInfo.getName());
                guildFullInfoBean.setLinkPhone(DesensitizedUtil.mobilePhone(enterpriseInfo.getPhone()));
            }

            //营业执照
            guildFullInfoBean.setBusinessLicense(enterpriseInfo.getUnifiedCreditCode());
        } else {
            log.warn("getEnterpriseInfo fail. familyId={},contractTenantCode={}", familyBean, contractTenantCode);
        }

        //银行信息
        Result<BankCardProto.ResponseGetBankCardInfo> bankRes = bankCardService.getBankCardInfo(contractTenantCode, familyId);
        if (RpcResult.isSuccess(bankRes)) {
            CommonProto.BankInfo bankInfo = bankRes.target().getBankInfo();
            guildFullInfoBean.setSettleBank(bankInfo.getBranchBankName());
            guildFullInfoBean.setBankAccountName(bankInfo.getAccountName());
            guildFullInfoBean.setBankAccountCardId(bankInfo.getCardNo());
        } else {
            log.warn("getBankCardInfo fail. familyId={},contractTenantCode={}", familyBean, contractTenantCode);
        }

        return guildFullInfoBean;
    }

    @Override
    public Integer countCanOpenRoomNum(long familyId) {
        return iUserFamilyRemote.countCanOpenRoomNum(familyId);
    }

    @Override
    public Integer countSignRoomNum(long familyId) {
        return 0;
    }

    @Override
    public boolean isFamilyVerifyPass(long familyId) {
        boolean flag = false;
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        String code = PayTenantCodeEnum.getPayTenantCode(appId);
        Result<AuthProto.ResponseGetEnterpriseInfo> getEnterpriseInfoResult = authService.getEnterpriseInfo(code, familyId);
        if (getEnterpriseInfoResult.rCode() == 0) {
            String status = getEnterpriseInfoResult.target().getEnterpriseInfo().getStatus();
            flag = StringUtils.equalsIgnoreCase(AuthStatus.AUTO_AUTH_PASS.getCode(), status);
        }
        if (flag) {
            Result<BankCardProto.ResponseGetBankCardInfo> bankCardResult = bankCardService.getBankCardInfo(code, familyId);
            if (bankCardResult.rCode() == 0) {
                String status = bankCardResult.target().getBankInfo().getStatus();
                flag = StringUtils.equalsIgnoreCase(AuthStatus.AUTO_AUTH_PASS.getCode(), status);
            }
        }
        return flag;
    }

    @Override
    public boolean isBankCardVerifyPass(long familyId) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        String code = PayTenantCodeEnum.getPayTenantCode(appId);
        Result<BankCardProto.ResponseGetBankCardInfo> bankCardResult = bankCardService.getBankCardInfo(code, familyId);
        if (bankCardResult.rCode() == 0) {
            String status = bankCardResult.target().getBankInfo().getStatus();
            return StringUtils.equalsIgnoreCase(AuthStatus.AUTO_AUTH_PASS.getCode(), status);
        }
        return false;
    }

    @Override
    public Optional<EnterpriseInfoBean> getFamilyEnterpriseInfo(long familyId) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        String contractTenantCode = PayTenantCodeEnum.getPayTenantCode(appId);
        Result<AuthProto.ResponseGetEnterpriseInfo> res = authService.getEnterpriseInfo(contractTenantCode, familyId);
        if (RpcResult.isFail(res)) {
            log.error("getFamilyEnterpriseInfo fail. familyId={},code={},rCode={}", familyId, contractTenantCode, res.rCode());
            return Optional.empty();
        }

        return Optional.of(new EnterpriseInfoBean()
                .setEnterpriseName(res.target().getEnterpriseInfo().getEnterpriseName())
                .setAuthStatus(SignAuthStatusEnum.getByCode(res.target().getEnterpriseInfo().getStatus()).getCode())
        );
    }

    @Override
    public List<Long> getFamilyIdsByPage(Long lastFamilyId, Integer pageSize) {
        return familyRemote.getFamilyIdsByPage(lastFamilyId, pageSize);
    }

    @Override
    public Optional<String> getSocietyCode(Long familyId) {
        return familyRemote.getSocietyCode(familyId);
    }
}
