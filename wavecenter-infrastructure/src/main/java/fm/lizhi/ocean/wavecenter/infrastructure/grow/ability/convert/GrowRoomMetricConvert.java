package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.convert;

import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowRoomMetricValue;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.GrowRoomMetricValueDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface GrowRoomMetricConvert {

    GrowRoomMetricConvert I = Mappers.getMapper(GrowRoomMetricConvert.class);

    @Mapping(target = "modifyTime", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "appId", ignore = true)
    WcGrowRoomMetricValue dto2Entity(GrowRoomMetricValueDTO dto);
}