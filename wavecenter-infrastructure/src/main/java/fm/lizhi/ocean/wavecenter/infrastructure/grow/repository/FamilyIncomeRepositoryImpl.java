package fm.lizhi.ocean.wavecenter.infrastructure.grow.repository;

import fm.lizhi.ocean.wavecenter.domain.grow.repository.FamilyIncomeRepository;
import fm.lizhi.ocean.wavecenter.service.income.manager.IncomeManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/22 11:40
 */
@Component
public class FamilyIncomeRepositoryImpl implements FamilyIncomeRepository {

    @Autowired
    private IncomeManager incomeManager;

    @Override
    public Integer getFamilyWeekIncome(Integer appId, Long familyId, Date monDay, Date sunDay) {
        return incomeManager.getFamilyWeekIncome(familyId, monDay, sunDay);
    }
}
