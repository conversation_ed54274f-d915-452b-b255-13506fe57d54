package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.dao;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityResourceDeployTypeConstants;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.AutoConfigResourceEnum;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestPageActivityResources;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityResource;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityResource;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityResourceConfig;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityResourceConfigExample;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityResourceLevelRelation;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityResourceLevelRelationExample;
import fm.lizhi.ocean.wavecenter.datastore.background.mapper.ActivityResourceConfigMapper;
import fm.lizhi.ocean.wavecenter.datastore.background.mapper.ActivityResourceLevelRelationMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.processor.IResourceConfigProcess;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.LogicDeleteConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 活动资源
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityResourceDao {

    private static final int DISABLE_STATUS = 0;

    @Autowired
    private ActivityResourceConfigMapper activityResourceConfigMapper;

    @Autowired
    private ActivityResourceLevelRelationMapper activityResourceLevelRelationMapper;

    @Transactional(rollbackFor = Exception.class)
    public void saveActivityResource(RequestSaveActivityResource resourceInfo, IResourceConfigProcess processor) {
        boolean saveResourceResult = saveSingleActivityResource(resourceInfo);
        if (!saveResourceResult) {
            return;
        }

        processor.bindSaveProGramme(resourceInfo);
    }

    /**
     * 保存活动资源
     */

    public boolean saveSingleActivityResource(RequestSaveActivityResource param) {

        ActivityResourceConfig resource = new ActivityResourceConfig();
        resource.setName(param.getName());
        resource.setAppId(param.getAppId());
        resource.setResourceCode(param.getResourceCode());
        resource.setIntroduction(param.getIntroduction());
        resource.setImageUrl(UrlUtils.removeHostOrEmpty(param.getImageUrl()));
        resource.setDeployType(param.getDeployType());
        resource.setStatus(param.getStatus());
        resource.setRequired(param.getRequired());
        resource.setDeployEnv(ConfigUtils.getEnvRequired().name());
        resource.setDeleted(LogicDeleteConstants.NOT_DELETED);
        resource.setOperator(param.getOperator());

        boolean success = activityResourceConfigMapper.insert(resource) > 0;
        if (success) {
            // 先删除关联关系
            deleteActivityResourceRelation(resource.getId());

            // 再保存关联关系
            List<ActivityResourceLevelRelation> levelRelations = param.getRelationLevelIds().stream().map(levelId -> {
                ActivityResourceLevelRelation relation = new ActivityResourceLevelRelation();
                relation.setResourceId(resource.getId());
                relation.setLevelId(levelId);
                return relation;
            }).collect(Collectors.toList());
            activityResourceLevelRelationMapper.batchInsert(levelRelations);
        }

        return success;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateActivityResource(RequestUpdateActivityResource param, IResourceConfigProcess processor) {
        boolean updateResult = updateSingleActivityResource(param);
        if (!updateResult) {
            return;
        }

        processor.bindUpdateProGramme(param);
    }

    public List<ActivityResourceConfig> getProgrammeInfo(Integer appId) {
        ActivityResourceConfigExample example = new ActivityResourceConfigExample();
        example.createCriteria().andResourceCodeEqualTo(AutoConfigResourceEnum.PROGRAMME.getResourceCode())
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andAppIdEqualTo(appId).andDeletedEqualTo(LogicDeleteConstants.NOT_DELETED);
        List<ActivityResourceConfig> activityResourceConfigs = activityResourceConfigMapper.selectByExample(example);
        //官频只有一个节目，直接取第一条
        if (CollUtil.isEmpty(activityResourceConfigs)) {
            return null;
        }
        return activityResourceConfigs;
    }


    /**
     * 更新活动资源
     */
    public boolean updateSingleActivityResource(RequestUpdateActivityResource param) {

        ActivityResourceConfig resource = new ActivityResourceConfig();
        resource.setId(param.getId());
        resource.setName(param.getName());
        resource.setAppId(param.getAppId());
        resource.setResourceCode(param.getResourceCode());
        resource.setIntroduction(param.getIntroduction());
        resource.setImageUrl(UrlUtils.removeHostOrEmpty(param.getImageUrl()));
        resource.setDeployType(param.getDeployType());
        resource.setStatus(param.getStatus());
        resource.setRequired(param.getRequired());
        resource.setDeployEnv(ConfigUtils.getEnvRequired().name());
        resource.setOperator(param.getOperator());
        resource.setModifyTime(new Date());

        boolean success = activityResourceConfigMapper.updateByPrimaryKey(resource) > 0;
        if (success) {
            if (CollUtil.isEmpty(param.getRelationLevelIds())) {
                return true;
            }
            // 先删除关联关系
            deleteActivityResourceRelation(resource.getId());

            // 再保存关联关系
            List<ActivityResourceLevelRelation> levelRelations = param.getRelationLevelIds().stream().map(levelId -> {
                ActivityResourceLevelRelation relation = new ActivityResourceLevelRelation();
                relation.setResourceId(resource.getId());
                relation.setLevelId(levelId);
                return relation;
            }).collect(Collectors.toList());
            activityResourceLevelRelationMapper.batchInsert(levelRelations);
        }

        return success;
    }

    /**
     * 删除资源关联关系
     */
    private void deleteActivityResourceRelation(Long resourceId) {
        ActivityResourceLevelRelationExample example = new ActivityResourceLevelRelationExample();
        example.createCriteria().andResourceIdEqualTo(resourceId);
        activityResourceLevelRelationMapper.deleteByExample(example);
    }


    public ActivityResourceConfig selectById(Long id) {
        return activityResourceConfigMapper.selectByPrimaryKey(ActivityResourceConfig.builder().id(id).build());
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteActivityResource(ActivityResourceConfig activityResourceConfig, String operator, IResourceConfigProcess processor) {
        boolean deleteResult = deleteSingleActivityResource(activityResourceConfig.getId(), operator);
        if (!deleteResult) {
            return;
        }

        processor.bindDeleteProGramme(activityResourceConfig, operator);
    }


    /**
     * 删除活动资源
     *
     * @param id
     * @param operator
     */
    public boolean deleteSingleActivityResource(Long id, String operator) {

        boolean success = activityResourceConfigMapper.updateByPrimaryKey(
                ActivityResourceConfig.builder()
                        .id(id).deleted(LogicDeleteConstants.DELETED)
                        .status(DISABLE_STATUS)
                        .operator(operator).modifyTime(new Date())
                        .build()
        ) > 0;

        if (success) {
            deleteActivityResourceRelation(id);
        }
        return success;
    }

    /**
     * 获取资源列表
     *
     * @param param
     * @return
     */
    public PageList<ActivityResourceConfig> listActivityResource(RequestPageActivityResources param) {
        ActivityResourceConfigExample example = new ActivityResourceConfigExample();
        ActivityResourceConfigExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(param.getAppId())
                .andDeletedEqualTo(LogicDeleteConstants.NOT_DELETED)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());

        if (BusinessEvnEnum.XIMI.getAppId() == param.getAppId()) {
            criteria.andResourceCodeNotIn(Lists.newArrayList(AutoConfigResourceEnum.PROGRAMME.getResourceCode()));
        }

        if (param.getDeployType() != null) {
            criteria.andDeployTypeEqualTo(param.getDeployType());
        }

        // 分页查询
        return activityResourceConfigMapper.pageByExample(example, param.getPageNo(), param.getPageSize());
    }


    /**
     * 获取资源关联等级
     *
     * @param resourceId
     * @return
     */
    public List<ActivityResourceLevelRelation> getLevelRelations(Long resourceId) {
        ActivityResourceLevelRelationExample example = new ActivityResourceLevelRelationExample();
        ActivityResourceLevelRelationExample.Criteria criteria = example.createCriteria();
        criteria.andResourceIdEqualTo(resourceId);
        return activityResourceLevelRelationMapper.selectByExample(example);
    }

    /**
     * 获取等级关联资源
     *
     * @param levelId
     * @return
     */
    public List<ActivityResourceLevelRelation> getResourceRelations(Long levelId) {
        ActivityResourceLevelRelationExample example = new ActivityResourceLevelRelationExample();
        ActivityResourceLevelRelationExample.Criteria criteria = example.createCriteria();
        criteria.andLevelIdEqualTo(levelId);
        return activityResourceLevelRelationMapper.selectByExample(example);
    }

    /**
     * 批量获取资源关联等级
     */
    public List<ActivityResourceLevelRelation> getLevelRelationsBatch(List<Long> resourceIds) {
        if (CollUtil.isEmpty(resourceIds)) {
            return Collections.emptyList();
        }

        ActivityResourceLevelRelationExample example = new ActivityResourceLevelRelationExample();
        ActivityResourceLevelRelationExample.Criteria criteria = example.createCriteria();
        criteria.andResourceIdIn(resourceIds);
        return activityResourceLevelRelationMapper.selectByExample(example);
    }

    /**
     * 判断资源是否存在
     */
    public boolean existResource(Integer appId, String name, Integer deployType, String resourceCode) {
        ActivityResourceConfigExample example = new ActivityResourceConfigExample();
        ActivityResourceConfigExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(appId)
                .andDeletedEqualTo(LogicDeleteConstants.NOT_DELETED)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andDeployTypeEqualTo(deployType);

        if (deployType.equals(ActivityResourceDeployTypeConstants.AUTO_CONFIG)) {
            criteria.andResourceCodeEqualTo(resourceCode);
        } else {
            criteria.andNameEqualTo(name);
        }
        return activityResourceConfigMapper.countByExample(example) > 0;
    }

    /**
     * 根据ID列表批量查询
     *
     * @param ids id列表
     * @return 结果
     */
    public List<ActivityResourceConfig> batchResourceByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        ActivityResourceConfigExample example = new ActivityResourceConfigExample();
        ActivityResourceConfigExample.Criteria criteria = example.createCriteria();
        criteria.andIdIn(ids);
        return activityResourceConfigMapper.selectByExample(example);
    }

    /**
     * 根据id列表批量查询资源作为map返回, 不区分状态, 包含已删除的资源和禁用的资源
     *
     * @param ids 资源id列表
     * @return 资源map, key: 资源id, value: 资源配置对象
     */
    public Map<Long, ActivityResourceConfig> getResourceByIdsAsMap(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        ArrayList<Long> distinctIds = new ArrayList<>(new LinkedHashSet<>(ids));
        ActivityResourceConfigExample example = new ActivityResourceConfigExample();
        ActivityResourceConfigExample.Criteria criteria = example.createCriteria();
        criteria.andIdIn(distinctIds);
        List<ActivityResourceConfig> resourceConfigs = activityResourceConfigMapper.selectByExample(example);
        HashMap<Long, ActivityResourceConfig> resourceConfigMap = new HashMap<>();
        for (ActivityResourceConfig resourceConfig : resourceConfigs) {
            resourceConfigMap.put(resourceConfig.getId(), resourceConfig);
        }
        return resourceConfigMap;
    }

    /**
     * 根据资源ID列表和状态查询资源
     *
     * @param resourceIds 资源ID列表
     * @return 资源列表
     */
    public List<ActivityResourceConfig> listValidateResourceByIdsAndStatus(List<Long> resourceIds, Integer status) {
        if (CollUtil.isEmpty(resourceIds)) {
            return Collections.emptyList();
        }
        ActivityResourceConfigExample example = new ActivityResourceConfigExample();
        ActivityResourceConfigExample.Criteria criteria = example.createCriteria();
        criteria.andIdIn(resourceIds)
                .andStatusEqualTo(status)
                .andDeletedEqualTo(LogicDeleteConstants.NOT_DELETED)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        return activityResourceConfigMapper.selectByExample(example);
    }

    /**
     * 根据资源code查询自动资源，手动资源不可查询
     *
     * @param resourceCode 资源code
     * @return 资源列表
     */
    public List<ActivityResourceConfig> getResourceConfigByResourceCode(String resourceCode, Integer appId) {
        ActivityResourceConfigExample example = new ActivityResourceConfigExample();
        ActivityResourceConfigExample.Criteria criteria = example.createCriteria();
        criteria.andResourceCodeEqualTo(resourceCode)
                .andDeployTypeEqualTo(ActivityResourceDeployTypeConstants.AUTO_CONFIG)
                .andDeletedEqualTo(LogicDeleteConstants.NOT_DELETED)
                .andAppIdEqualTo(appId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        return activityResourceConfigMapper.selectByExample(example);
    }
}
