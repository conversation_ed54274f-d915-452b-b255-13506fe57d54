package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.constants;

import fm.lizhi.ocean.wavecenter.base.constants.IRedisKey;

public enum ActivityRedisKey implements IRedisKey {

    /**
     * 活动申请锁
     * 类型：STRING
     * 参数：APPLY_LOCK_STR_#{appId}_#{userId}
     * 描述：用户申请活动时，对用户进行全局锁，防止重复申请
     * 值：时间戳
     */
    APPLY_LOCK_STR,

    /**
     * 资源发放锁
     * 类型：STRING
     * 参数：RESOURCE_GIVE_LOCK_STR_#{appId}_#{resourceId}
     * 描述：资源发放锁，防止重复申请
     * 值：时间戳
     */
    RESOURCE_GIVE_LOCK_STR,

    /**
     * 活动审核通过锁
     * 类型：STRING
     * 参数：ACTIVITY_AGREE_LOCK_STR_#{appId}_#{activityId}
     * 描述：活动审核通过锁，防止重复审核
     * 值：时间戳
     */
    ACTIVITY_AGREE_LOCK_STR,

    /**
     * 活动修改锁
     */
    ACTIVITY_MODIFY_LOCK_STR;

    @Override
    public String getPrefix() {
        return "ACTIVITY_CENTER";
    }

    @Override
    public String getName() {
        return this.name();
    }
}
