package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.manager;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityRuleConfigBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.OfficialCountRuleBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyRuleEnum;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateFlowResourceExtraBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseGetActivityTemplate;
import fm.lizhi.ocean.wavecenter.base.constants.TimeConstant;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.convert.ActivityOfficialSeatConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.dao.ActivityOfficialSeatTimeDao;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityOfficialSeatTime;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityOfficialSeatTimeExample;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.mapper.ActivityOfficialSeatTimeMapper;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityOfficialSeatTimeDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityOptionalOfficialTimeDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityOfficialSeatManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityRuleManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ActivityOfficialSeatManagerImpl implements ActivityOfficialSeatManager {

    @Autowired
    private ActivityOfficialSeatTimeMapper activityOfficialSeatTimeMapper;

    @Autowired
    private ActivityOfficialSeatTimeDao activityOfficialSeatTimeDao;

    @Autowired
    private ActivityRuleManager activityRulesManager;

    @Override
    public List<ActivityOfficialSeatTimeDTO> getOfficialSeatList(Integer appId, Date startTime, Date endTime, Integer seatNo) {
        ActivityOfficialSeatTimeExample seatTimeExample = new ActivityOfficialSeatTimeExample();
        seatTimeExample.createCriteria()
                .andAppIdEqualTo(appId)
                .andSeatEqualTo(seatNo)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andStartTimeGreaterThanOrEqualTo(startTime)
                .andEndTimeLessThanOrEqualTo(endTime);

        List<ActivityOfficialSeatTime> list = activityOfficialSeatTimeMapper.selectByExample(seatTimeExample);
        return ActivityOfficialSeatConvert.I.officialSeatBeans2DTOs(list);
    }

    @Override
    public ActivityOptionalOfficialTimeDTO getOptionalOfficialTime(Integer appId, Long activityStartTime, Long activityEndTime,
                                                                   ActivityTemplateFlowResourceExtraBean flowResourceExtraBean, ResponseGetActivityTemplate template) {
        //配置模板时，限制了不能为空
        List<Integer> officialSeatNumbers = flowResourceExtraBean.getOfficialSeatNumbers();
        //官频位限制的分钟数，实际上可能为空
        Integer durationLimit = template.getActivityDurationLimit() == null ? flowResourceExtraBean.getDurationLimit() : template.getActivityDurationLimit();
        //查询出设置的最大官频位数量
        int maxOfficialSeatHallCount = getMaxOfficialSeatHallCount(appId);
        log.info("getOptionalOfficialTime appId={}, activityStartTime={}, activityEndTime={}, durationLimit={},maxOfficialSeatHallCount={}",
                appId, activityStartTime, activityEndTime, durationLimit, maxOfficialSeatHallCount);

        //根据活动时间和官频位限制分钟数，选择可选的官频位时间
        for (Integer seat : officialSeatNumbers) {
            //查询出官频位
            List<ActivityOfficialSeatTimeDTO> seatList = getOfficialSeatList(appId, new Date(activityStartTime), new Date(activityEndTime), seat);
            //过滤掉某个时间段内官频位使用数量大于等于maxOfficialSeatHallCount的记录
            seatList = CollectionUtils.isEmpty(seatList) ? null : seatList.stream().filter(seatInfo -> seatInfo.getCount() < maxOfficialSeatHallCount).collect(Collectors.toList());
            //如果官频位记录不存在，就跳过
            if (CollectionUtils.isEmpty(seatList)) {
                continue;
            }
            //根据官频位限制分钟数，选择可选的官频位时间
            durationLimit = (int) (durationLimit == null ? (activityEndTime - activityStartTime) / TimeConstant.ONE_MINUTE_MILLISECOND : durationLimit);


            long officialStartTime = seatList.get(0).getStartTime().getTime();
            long officialEndTime = seatList.get(0).getEndTime().getTime();
            for (ActivityOfficialSeatTimeDTO timeDTO : seatList) {
                //先检查时间连贯性，不能调换顺序。如果出现时间不连贯了，重新赋值开始时间
                if (timeDTO.getStartTime().getTime() > officialEndTime) {
                    //正常情况，下半个小时的开始时间和上半个小时的结束结束一样
                    officialStartTime = timeDTO.getStartTime().getTime();
                    log.info("getOptionalOfficialTime 官频位时间连续性中断，appId={}, activityStartTime={}, activityEndTime={}, seat={}, preEndTime={}, currentStartTime={}",
                            appId, activityStartTime, activityEndTime, seat, officialEndTime, timeDTO.getStartTime().getTime());
                }

                long endTime = timeDTO.getEndTime().getTime();
                //如果官频位开始时间和结束时间差等于官频位限制分钟数，就返回可选的官频位时间
                if ((endTime - officialStartTime) / TimeConstant.ONE_MINUTE_MILLISECOND == durationLimit) {
                    //可选的官频位时间
                    return new ActivityOptionalOfficialTimeDTO().setSeat(seat).setOfficialStartTime(officialStartTime).setOfficialEndTime(endTime);
                }
                officialEndTime = timeDTO.getEndTime().getTime();
            }
        }
        return null;
    }

    @Override
    public boolean initOfficialSeatTime(List<ActivityOfficialSeatTimeDTO> list) {
        List<ActivityOfficialSeatTime> timeList = new ArrayList<>();
        for (ActivityOfficialSeatTimeDTO seatTime : list) {
            ActivityOfficialSeatTime build = ActivityOfficialSeatTime.builder()
                    .seat(seatTime.getSeat())
                    .startTime(seatTime.getStartTime())
                    .endTime(seatTime.getEndTime())
                    .appId(seatTime.getAppId())
                    .count(seatTime.getCount())
                    .deployEnv(ConfigUtils.getEnvRequired().name())
                    .showDate(DateUtil.getDayStart(seatTime.getStartTime()))
                    .build();
            timeList.add(build);
        }
        return activityOfficialSeatTimeDao.batchSaveOfficialSeatTime(timeList);
    }

    /**
     * 获取最大官频位厅数量
     *
     * @param appId 应用ID
     * @return 最大官频位厅数量
     */
    private int getMaxOfficialSeatHallCount(Integer appId) {
        ActivityRuleConfigBean rule = activityRulesManager.getActivityRuleByRuleTypeAndAppId(appId, ActivityApplyRuleEnum.OFFICIAL_COUNT);
        int maxOfficialSeatHallCount = 5;
        if (rule != null) {
            OfficialCountRuleBean ruleBean = activityRulesManager.getRuleBean(ActivityApplyRuleEnum.OFFICIAL_COUNT, rule.getRuleJson());
            if (ruleBean != null) {
                maxOfficialSeatHallCount = ruleBean.getCount();
            }
        }
        return maxOfficialSeatHallCount;
    }
}
