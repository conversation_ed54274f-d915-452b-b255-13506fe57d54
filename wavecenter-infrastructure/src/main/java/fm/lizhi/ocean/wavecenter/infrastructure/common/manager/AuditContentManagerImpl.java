package fm.lizhi.ocean.wavecenter.infrastructure.common.manager;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.content.review.api.CheckDataService;
import fm.lizhi.content.review.constant.CheckDataResultCodeEnum;
import fm.lizhi.content.review.protocol.CheckServiceProto;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.common.convert.AuditContentConvert;
import fm.lizhi.ocean.wavecenter.service.common.dto.ActivityApplyDataAuditParamDTO;
import fm.lizhi.ocean.wavecenter.service.common.dto.ActivityApplyDataAuditResultDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.AuditContentManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class AuditContentManagerImpl implements AuditContentManager {

    @Autowired
    private CheckDataService checkDataService;

    @Autowired
    private AuditContentConvert auditContentConvert;

    /**
     * 活动申请数据送审
     *
     * @param paramDTO 参数
     * @return 结果
     */
    public Result<ActivityApplyDataAuditResultDTO> auditActivityApplyData(ActivityApplyDataAuditParamDTO paramDTO) {
        ActivityApplyDataAuditResultDTO auditResultDTO = new ActivityApplyDataAuditResultDTO();
        if (paramDTO == null) {
            auditResultDTO.setResult(true);
            return RpcResult.success(auditResultDTO);
        }
        CheckServiceProto.RequestCheckMultiData param = auditContentConvert.buildAuditActivityApplyDataParam(paramDTO);
        Result<CheckServiceProto.ResponseCheckData> result = checkDataService.checkMultiData(param);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("auditActivityApplyData error params={} rCode={}", paramDTO, result.rCode());
            return RpcResult.fail(result.rCode());
        }
        int resultCode = result.target().getResultCode();
        auditResultDTO.setResult(resultCode == CheckDataResultCodeEnum.PASS.getResultCode());
        //如果审核结果不通过，可以遍历查询结果
        if (resultCode == CheckDataResultCodeEnum.REJECT.getResultCode()) {
            List<ActivityApplyDataAuditResultDTO.DiscernResult> discernResults = auditContentConvert.convertApplyDataAuditResult(result.target().getDiscernResultsList());
            auditResultDTO.setDiscernResults(discernResults);
            log.info("auditActivityApplyData error checkDataList={} discernResults={}",
                    JsonUtil.dumps(paramDTO.getCheckDataList()), JsonUtil.dumps(discernResults));
            return RpcResult.success(auditResultDTO);
        }
        return RpcResult.success(auditResultDTO);
    }
}
