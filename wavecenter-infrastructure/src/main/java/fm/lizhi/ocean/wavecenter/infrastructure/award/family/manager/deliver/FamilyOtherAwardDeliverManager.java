package fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager.deliver;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardResourceDeliverTypeEnum;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardTypeEnum;
import fm.lizhi.ocean.wavecenter.api.grow.level.bean.FamilyLevelConfigBean;
import fm.lizhi.ocean.wavecenter.api.grow.level.request.RequestGetFamilyLevelConfigList;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.common.utils.DateTimeUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert.FamilyAwardDeliverConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert.FamilyAwardDeliverExecutionConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert.FamilyAwardDeliverItemConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert.FamilyAwardDeliverRecordConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.dao.FamilyAwardDeliverRecordDao;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.dao.FamilyAwardLevelDataDao;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.dao.FamilyOtherAwardRuleDao;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.dao.FamilyWeekDataDao;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcDataFamilyWeekData;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilyAwardDeliverRecord;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilyAwardLevelData;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.param.*;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.result.CreateDeliverRecordResult;
import fm.lizhi.ocean.wavecenter.service.award.family.config.*;
import fm.lizhi.ocean.wavecenter.service.grow.manager.FamilyLevelConfigManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Component
public class FamilyOtherAwardDeliverManager {

    @Autowired
    private FamilyAwardConfig familyAwardConfig;

    @Autowired
    private FamilyAwardDeliverItemConvert familyAwardDeliverItemConvert;

    @Autowired
    private FamilyAwardLevelDataDao familyAwardLevelDataDao;

    @Autowired
    private FamilyWeekDataDao familyWeekDataDao;

    @Autowired
    private FamilyOtherAwardRuleDao familyOtherAwardRuleDao;

    @Autowired
    private FamilyAwardDeliverRecordDao familyAwardDeliverRecordDao;

    @Autowired
    private FamilyLevelConfigManager familyLevelConfigManager;

    @Autowired
    private FamilyManager familyManager;

    @Autowired
    private UserManager userManager;

    @Autowired
    private List<FamilyAwardDeliver> familyAwardDelivers;

    @Autowired
    private DeliverResultHandler deliverResultHandler;

    /**
     * 发放奖励. 暂不支持自动重发, 如果有失败的记录, 需要人工干预.
     *
     * @param appIds         应用id列表
     * @param awardStartTime 奖励周期开始时间
     */
    public void deliverAward(List<Integer> appIds, Date awardStartTime) {
        for (Integer appId : appIds) {
            deliverAward(appId, awardStartTime);
        }
    }

    public void deliverAward(Integer appId, Date awardStartTime, Long familyId){
        try {
            log.info("FamilyDelivering family other award, familyId={},awardStartTime={}", familyId, awardStartTime);
            BusinessEvnEnum businessEvnEnum = BusinessEvnEnum.from(appId);
            if (businessEvnEnum == null) {
                log.info("Skip delivering family other award, invalid appId={}", appId);
                return;
            }
            if (!DateTimeUtils.isMondayStartTime(awardStartTime)) {
                log.info("Skip delivering family other award, invalid awardStartTime={}", awardStartTime);
                return;
            }

            // 设置应用上下文, 后续代码无需ResultHandler
            ContextUtils.setBusinessEvnEnum(businessEvnEnum);
            // 查询所有有效的公会等级id
            Set<Long> effectiveLevelIds = getEffectiveLevelIds(appId);
            log.info("effectiveLevelIds: {}", effectiveLevelIds);
            // 查询所有特殊推荐卡名单数量
            Map<Long, Integer> specialRecommendCardUserNumberMap = new TreeMap<>(familyOtherAwardRuleDao.getAppSpecialRecommendCardNameAsUserNumberMap(appId));
            log.info("specialRecommendCardUserNumberMap: {}", specialRecommendCardUserNumberMap);
            // 分页查询公会等级数据
            Date maxCreateTime = new Date();
            Date deliverTime = new Date();
            int pageNumber = 1;
            int pageSize = 1000;
            boolean hasNextPage = true;
            while (hasNextPage) {
                ListFamilyAwardLevelFamilyDataParam param = new ListFamilyAwardLevelFamilyDataParam(appId, familyId, awardStartTime, maxCreateTime, pageNumber, pageSize);
                PageList<WcFamilyAwardLevelData> pageList = familyAwardLevelDataDao.listData(param);
                for (WcFamilyAwardLevelData levelData : pageList) {
                    Long levelId = levelData.getLevelId();
                    FamilyBean familyBean = getFamilyByCache(appId, familyId);
                    if (familyBean == null) {
                        log.info("Skip delivering family other award, family not found, appId={}, familyId={}", appId, familyId);
                        continue;
                    }
                    if (!effectiveLevelIds.contains(levelId)) {
                        log.info("Skip delivering family other award, level not valid, appId={}, familyId={}, levelId={}", appId, familyId, levelId);
                        continue;
                    }
                    // 获取特殊推荐卡数量, 并从名单中移除
                    Long familyUserId = familyBean.getUserId();
                    Integer specialRecommendCardNumber = specialRecommendCardUserNumberMap.remove(familyUserId);
                    // 根据每条公会等级数据执行奖励发放
                    try {
                        doDeliver(appId, awardStartTime, deliverTime, familyBean, levelId, specialRecommendCardNumber);
                    } catch (RuntimeException e) {
                        log.error("Deliver family other award error, appId={}, familyId={}, awardStartTime={}", appId, familyId, awardStartTime, e);
                    }
                }
                if (pageList.size() < pageSize) {
                    hasNextPage = false;
                } else {
                    pageNumber++;
                }
            }
            // 处理剩余特殊推荐卡名单
//        for (Map.Entry<Long, Integer> entry : specialRecommendCardUserNumberMap.entrySet()) {
//            Long familyUserId = entry.getKey();
//            Integer specialRecommendCardNumber = entry.getValue();
//            FamilyBean familyBean = getFamilyByUserId(appId, familyUserId);
//            if (familyBean == null) {
//                log.warn("Skip delivering family other award, family not found, appId={}, familyUserId={}", appId, familyUserId);
//                continue;
//            }
//            // 此处无需传入levelId, 因为有等级数据的公会在上面的循环中已经发放过奖励
//            try {
//                doDeliver(appId, awardStartTime, deliverTime, familyBean, null, specialRecommendCardNumber);
//            } catch (RuntimeException e) {
//                log.error("Deliver family other award error, appId={}, familyUserId={}, awardStartTime={}", appId, familyUserId, awardStartTime, e);
//            }
//        }
            log.info("Delivered family other award, appId={}, awardStartTime={}", appId, awardStartTime);
        } finally {
            ContextUtils.clearContext();
        }
    }

    private void deliverAward(Integer appId, Date awardStartTime) {
        try {
            log.info("Delivering family other award, appId={}, awardStartTime={}", appId, awardStartTime);
            BusinessEvnEnum businessEvnEnum = BusinessEvnEnum.from(appId);
            if (businessEvnEnum == null) {
                log.info("Skip delivering family other award, invalid appId={}", appId);
                return;
            }
            if (!DateTimeUtils.isMondayStartTime(awardStartTime)) {
                log.info("Skip delivering family other award, invalid awardStartTime={}", awardStartTime);
                return;
            }
            // 设置应用上下文, 后续代码无需ResultHandler
            ContextUtils.setBusinessEvnEnum(businessEvnEnum);
            // 查询所有有效的公会等级id
            Set<Long> effectiveLevelIds = getEffectiveLevelIds(appId);
            log.info("effectiveLevelIds: {}", effectiveLevelIds);
            // 查询所有特殊推荐卡名单数量
            Map<Long, Integer> specialRecommendCardUserNumberMap = new TreeMap<>(familyOtherAwardRuleDao.getAppSpecialRecommendCardNameAsUserNumberMap(appId));
            log.info("specialRecommendCardUserNumberMap: {}", specialRecommendCardUserNumberMap);
            // 分页查询公会等级数据
            Date maxCreateTime = new Date();
            Date deliverTime = new Date();
            int pageNumber = 1;
            int pageSize = 1000;
            boolean hasNextPage = true;
            while (hasNextPage) {
                ListFamilyAwardLevelDataParam param = new ListFamilyAwardLevelDataParam(appId, awardStartTime, maxCreateTime, pageNumber, pageSize);
                PageList<WcFamilyAwardLevelData> pageList = familyAwardLevelDataDao.listData(param);
                for (WcFamilyAwardLevelData levelData : pageList) {
                    Long familyId = levelData.getFamilyId();
                    Long levelId = levelData.getLevelId();
                    FamilyBean familyBean = getFamilyByCache(appId, familyId);
                    if (familyBean == null) {
                        log.info("Skip delivering family other award, family not found, appId={}, familyId={}", appId, familyId);
                        continue;
                    }
                    if (!effectiveLevelIds.contains(levelId)) {
                        log.info("Skip delivering family other award, level not valid, appId={}, familyId={}, levelId={}", appId, familyId, levelId);
                        continue;
                    }
                    // 获取特殊推荐卡数量, 并从名单中移除
                    Long familyUserId = familyBean.getUserId();
                    Integer specialRecommendCardNumber = specialRecommendCardUserNumberMap.remove(familyUserId);
                    // 根据每条公会等级数据执行奖励发放
                    try {
                        doDeliver(appId, awardStartTime, deliverTime, familyBean, levelId, specialRecommendCardNumber);
                    } catch (RuntimeException e) {
                        log.error("Deliver family other award error, appId={}, familyId={}, awardStartTime={}", appId, familyId, awardStartTime, e);
                    }
                }
                if (pageList.size() < pageSize) {
                    hasNextPage = false;
                } else {
                    pageNumber++;
                }
            }
            // 处理剩余特殊推荐卡名单
            for (Map.Entry<Long, Integer> entry : specialRecommendCardUserNumberMap.entrySet()) {
                Long familyUserId = entry.getKey();
                Integer specialRecommendCardNumber = entry.getValue();
                FamilyBean familyBean = getFamilyByUserId(appId, familyUserId);
                if (familyBean == null) {
                    log.warn("Skip delivering family other award, family not found, appId={}, familyUserId={}", appId, familyUserId);
                    continue;
                }
                // 此处无需传入levelId, 因为有等级数据的公会在上面的循环中已经发放过奖励
                try {
                    doDeliver(appId, awardStartTime, deliverTime, familyBean, null, specialRecommendCardNumber);
                } catch (RuntimeException e) {
                    log.error("Deliver family other award error, appId={}, familyUserId={}, awardStartTime={}", appId, familyUserId, awardStartTime, e);
                }
            }
            log.info("Delivered family other award, appId={}, awardStartTime={}", appId, awardStartTime);
        } finally {
            ContextUtils.clearContext();
        }
    }

    private void doDeliver(Integer appId, Date awardStartTime, Date deliverTime, FamilyBean familyBean, Long levelId, Integer specialRecommendCardNumber) {
        Long familyId = familyBean.getId();
        String familyName = familyBean.getFamilyName();
        Long familyUserId = familyBean.getUserId();
        WcFamilyAwardDeliverRecord oldDeliverRecord = familyAwardDeliverRecordDao.getDeliverRecord(appId, familyId, awardStartTime);
        if (oldDeliverRecord != null) {
            log.info("Skip delivering family other award, record is present, appId={}, familyId={}, awardStartTime={}", appId, familyId, awardStartTime);
            return;
        }
        if (levelId == null && specialRecommendCardNumber == null) {
            log.info("Skip delivering family other award, no award to deliver, appId={}, familyId={}, awardStartTime={}", appId, familyId, awardStartTime);
            return;
        }
        WcDataFamilyWeekData familyWeekData = familyWeekDataDao.getFamilyWeekData(appId, familyId, awardStartTime);
        log.info("familyWeekData={}", familyWeekData);
        String familyUserName = getUserNameOrEmpty(appId, familyUserId);
        // 构造发放资源参数
        ArrayList<CreateFamilyAwardDeliverExecutionParam> deliverExecutions = new ArrayList<>();
        ArrayList<DeliverResourceParam> deliverResourceParams = new ArrayList<>();
        if (levelId != null) {
            // 推荐卡计算
            ArrayList<CreateFamilyAwardDeliverItemParam> recommendCardItems = new ArrayList<>();
            // 公会等级推荐卡
            int levelRecommendCardNumber = computeLevelRecommendCardNumber(levelId, familyWeekData);
            log.info("levelRecommendCardNumber={}", levelRecommendCardNumber);
            recommendCardItems.add(familyAwardDeliverItemConvert.toOtherAwardLevelRecommendCardCreateParam(levelRecommendCardNumber));
            // 流水增长推荐卡
            int flowGrowthRecommendCardNumber = computeFlowGrowthRecommendCardNumber(levelId, familyWeekData);
            log.info("flowGrowthRecommendCardNumber={}", flowGrowthRecommendCardNumber);
            recommendCardItems.add(familyAwardDeliverItemConvert.toOtherAwardFlowGrowthRecommendCardCreateParam(flowGrowthRecommendCardNumber));
            // 新厅留存推荐卡
            int newRoomRetainRecommendCardNumber = computeNewRoomRetainRecommendCardNumber(familyWeekData);
            log.info("newRoomRetainRecommendCardNumber={}", newRoomRetainRecommendCardNumber);
            recommendCardItems.add(familyAwardDeliverItemConvert.toOtherAwardNewRoomRetainRecommendCardCreateParam(newRoomRetainRecommendCardNumber));
            // 0流失厅推荐卡
            int zeroLostRoomRecommendCardNumber = computeZeroLostRoomRecommendCardNumber(levelId, familyWeekData);
            log.info("zeroLostRoomRecommendCardNumber={}", zeroLostRoomRecommendCardNumber);
            recommendCardItems.add(familyAwardDeliverItemConvert.toOtherAwardZeroLostRoomRecommendCardCreateParam(zeroLostRoomRecommendCardNumber));
            // 推荐卡总数, 注意这里是等级计算的推荐卡总数, 不包括特殊推荐卡名单的推荐卡数
            int totalRecommendCardNumber = levelRecommendCardNumber + flowGrowthRecommendCardNumber + newRoomRetainRecommendCardNumber + zeroLostRoomRecommendCardNumber;
            log.info("totalRecommendCardNumber={}", totalRecommendCardNumber);
            CreateFamilyAwardDeliverExecutionParam deliverRecommendCardExecution = FamilyAwardDeliverExecutionConvert.I.toCreateParam(FamilyAwardResourceDeliverTypeEnum.RECOMMEND_CARD, totalRecommendCardNumber, 0, 0L, StringUtils.EMPTY, StringUtils.EMPTY, recommendCardItems);
            deliverExecutions.add(deliverRecommendCardExecution);
            DeliverResourceParam deliverRecommendCardParam = FamilyAwardDeliverConvert.I.toDeliverResourceParam(deliverRecommendCardExecution, appId, FamilyAwardTypeEnum.OTHER, familyId, familyName, familyUserId, familyUserName);
            deliverResourceParams.add(deliverRecommendCardParam);

            // 新厅名额计算
            ArrayList<CreateFamilyAwardDeliverItemParam> newRoomItems = new ArrayList<>();
            // 公会等级新厅名额
            int levelNewRoomNumber = computeLevelNewRoomNumber(levelId);
            log.info("levelNewRoomNumber={}", levelNewRoomNumber);
            newRoomItems.add(familyAwardDeliverItemConvert.toOtherAwardLevelNewRoomCreateParam(levelNewRoomNumber));
            // 流水增长新厅名额
            int flowGrowthNewRoomNumber = computeFlowGrowthNewRoomNumber(familyWeekData);
            log.info("flowGrowthNewRoomNumber={}", flowGrowthNewRoomNumber);
            newRoomItems.add(familyAwardDeliverItemConvert.toOtherAwardFlowGrowthNewRoomCreateParam(flowGrowthNewRoomNumber));
            // 流失厅数新厅名额
            int lostRoomNewRoomNumber = computeLostRoomNewRoomNumber(familyWeekData);
            log.info("lostRoomNewRoomNumber={}", lostRoomNewRoomNumber);
            newRoomItems.add(familyAwardDeliverItemConvert.toOtherAwardLostRoomNewRoomCreateParam(lostRoomNewRoomNumber));
            // 新厅留存新厅名额
            int newRoomRetainNewRoomNumber = computeNewRoomRetainNewRoomNumber(familyWeekData);
            log.info("newRoomRetainNewRoomNumber={}", newRoomRetainNewRoomNumber);
            newRoomItems.add(familyAwardDeliverItemConvert.toOtherAwardNewRoomRetainNewRoomCreateParam(newRoomRetainNewRoomNumber));
            // 新厅名额总数, 如果总和小于0则置为0
            int totalNewRoomNumber = Math.max(levelNewRoomNumber + flowGrowthNewRoomNumber + lostRoomNewRoomNumber + newRoomRetainNewRoomNumber, 0);
            log.info("totalNewRoomNumber={}", totalNewRoomNumber);
            CreateFamilyAwardDeliverExecutionParam deliverNewRoomExecution = FamilyAwardDeliverExecutionConvert.I.toCreateParam(FamilyAwardResourceDeliverTypeEnum.NEW_ROOM, totalNewRoomNumber, 0, 0L, StringUtils.EMPTY, StringUtils.EMPTY, newRoomItems);
            deliverExecutions.add(deliverNewRoomExecution);
            DeliverResourceParam deliverNewRoomParam = FamilyAwardDeliverConvert.I.toDeliverResourceParam(deliverNewRoomExecution, appId, FamilyAwardTypeEnum.OTHER, familyId, familyName, familyUserId, familyUserName);
            deliverResourceParams.add(deliverNewRoomParam);
        }
        if (specialRecommendCardNumber != null) {
            // 特殊推荐卡单独调用一次发放
            CreateFamilyAwardDeliverItemParam specialRecommendCardItem = familyAwardDeliverItemConvert.toOtherAwardSpecialRecommendCardCreateParam(specialRecommendCardNumber);
            CreateFamilyAwardDeliverExecutionParam specialRecommendCardExecution = FamilyAwardDeliverExecutionConvert.I.toCreateParam(specialRecommendCardItem);
            deliverExecutions.add(specialRecommendCardExecution);
            deliverResourceParams.add(FamilyAwardDeliverConvert.I.toDeliverResourceParam(specialRecommendCardExecution, appId, FamilyAwardTypeEnum.OTHER, familyId, familyName, familyUserId, familyUserName));
        }
        int executionSize = deliverExecutions.size();
        // 发放前先初始化发放记录
        CreateFamilyAwardDeliverRecordParam createRecordParam = FamilyAwardDeliverRecordConvert.I.toCreateParam(appId, familyId, familyName, familyUserId, familyUserName, awardStartTime, deliverTime, deliverExecutions);
        CreateDeliverRecordResult createRecordResult = familyAwardDeliverRecordDao.createDeliverRecord(createRecordParam);
        // 依次发放资源
        long recordId = createRecordResult.getRecordId();
        DeliverExecutionCounter counter = new DeliverExecutionCounter(executionSize);
        for (int i = 0; i < executionSize; i++) {
            Long executionId = createRecordResult.getExecutionIds().get(i);
            DeliverResourceParam deliverResourceParam = deliverResourceParams.get(i);
            FamilyAwardDeliver familyAwardDeliver = getFamilyAwardDeliver(deliverResourceParam.getResourceAwardType(), deliverResourceParam.getResourceDeliverType());
            Validate.notNull(familyAwardDeliver);
            // 执行发放动作
            Result<Void> deliverResult = familyAwardDeliver.deliverResource(deliverResourceParam);
            // 执行发放结果处理入库
            deliverResultHandler.handleDeliverResult(recordId, executionId, counter, deliverResourceParam, deliverResult);
        }
    }

    private int computeLevelRecommendCardNumber(long levelId, WcDataFamilyWeekData familyWeekData) {
        if (familyWeekData != null) {
            Integer effectiveRoomNumber = familyWeekData.getEffectiveRoomNumber();
            List<LevelRecommendCardConfig> configs = familyAwardConfig.getOtherAwardConfig().getLevelRecommendCardConfigs();
            for (LevelRecommendCardConfig config : configs) {
                if (Objects.equals(levelId, config.getLevelId())) {
                    for (LevelRecommendCardLadder ladder : config.getLadders()) {
                        if (effectiveRoomNumber >= ladder.getEffectiveRoomNumber()) {
                            return ladder.getRecommendCardNumber();
                        }
                    }
                }
            }
        }
        return 0;
    }

    private int computeFlowGrowthRecommendCardNumber(long levelId, WcDataFamilyWeekData familyWeekData) {
        if (familyWeekData != null) {
            BigDecimal flowWeekOnWeek = familyWeekData.getFlowWeekOnWeek();
            List<FlowRecommendCardConfig> configs = familyAwardConfig.getOtherAwardConfig().getFlowRecommendCardConfigs();
            for (FlowRecommendCardConfig config : configs) {
                if (Objects.equals(levelId, config.getLevelId())) {
                    for (FlowRecommendCardLadder ladder : config.getLadders()) {
                        if (flowWeekOnWeek.compareTo(ladder.getFlowWeekOnWeek()) >= 0) {
                            return ladder.getRecommendCardNumber();
                        }
                    }
                }
            }
        }
        return 0;
    }

    private int computeNewRoomRetainRecommendCardNumber(WcDataFamilyWeekData familyWeekData) {
        if (familyWeekData != null) {
            Integer retainedNewRoomNumber = familyWeekData.getRetainedNewRoomNumber();
            for (NewRoomRecommendCardConfig config : familyAwardConfig.getOtherAwardConfig().getNewRoomRecommendCardConfigs()) {
                if (retainedNewRoomNumber >= config.getRetainedNewRoomNumber()) {
                    return config.getRecommendCardNumber();
                }
            }
        }
        return 0;
    }

    private int computeZeroLostRoomRecommendCardNumber(long levelId, WcDataFamilyWeekData familyWeekData) {
        if (familyWeekData != null && familyWeekData.getLostRoomNumber() <= 0) {
            for (ZeroLostRoomRecommendCardConfig config : familyAwardConfig.getOtherAwardConfig().getZeroLostRoomRecommendCardConfigs()) {
                if (Objects.equals(levelId, config.getLevelId())) {
                    return config.getRecommendCardNumber();
                }
            }
        }
        return 0;
    }

    private int computeLevelNewRoomNumber(long levelId) {
        List<LevelNewRoomConfig> configs = familyAwardConfig.getOtherAwardConfig().getLevelNewRoomConfigs();
        for (LevelNewRoomConfig config : configs) {
            if (Objects.equals(levelId, config.getLevelId())) {
                return config.getNewRoomNumber();
            }
        }
        return 0;
    }

    private int computeFlowGrowthNewRoomNumber(WcDataFamilyWeekData familyWeekData) {
        if (familyWeekData != null) {
            BigDecimal flowWeekOnWeek = familyWeekData.getFlowWeekOnWeek();
            for (FlowGrowthNewRoomConfig config : familyAwardConfig.getOtherAwardConfig().getFlowGrowthNewRoomConfigs()) {
                if (flowWeekOnWeek.compareTo(config.getFlowWeekOnWeek()) >= 0) {
                    return config.getNewRoomNumber();
                }
            }
        }
        return 0;
    }

    private int computeLostRoomNewRoomNumber(WcDataFamilyWeekData familyWeekData) {
        if (familyWeekData != null) {
            Integer lostRoomNumber = familyWeekData.getLostRoomNumber();
            for (LostRoomNewRoomConfig config : familyAwardConfig.getOtherAwardConfig().getLostRoomNewRoomConfigs()) {
                if (lostRoomNumber >= config.getLostRoomNumber()) {
                    return config.getNewRoomNumber();
                }
            }
        }
        return 0;
    }

    private int computeNewRoomRetainNewRoomNumber(WcDataFamilyWeekData familyWeekData) {
        if (familyWeekData != null) {
            Integer retainedNewRoomNumber = familyWeekData.getRetainedNewRoomNumber();
            for (NewRoomRetainNewRoomConfig config : familyAwardConfig.getOtherAwardConfig().getNewRoomRetainNewRoomConfigs()) {
                if (retainedNewRoomNumber >= config.getRetainedNewRoomNumber()) {
                    return config.getNewRoomNumber();
                }
            }
        }
        return 0;
    }

    private Set<Long> getEffectiveLevelIds(int appId) {
        RequestGetFamilyLevelConfigList request = new RequestGetFamilyLevelConfigList();
        request.setAppId(appId);
        List<FamilyLevelConfigBean> levels = familyLevelConfigManager.getList(request);
        HashSet<Long> levelIds = new HashSet<>();
        for (FamilyLevelConfigBean level : CollectionUtils.emptyIfNull(levels)) {
            if (!level.getDeleted()) {
                levelIds.add(level.getId());
            }
        }
        return levelIds;
    }

    private FamilyBean getFamilyByCache(int appId, long familyId) {
        try {
            Optional<FamilyBean> familyByCache = familyManager.getFamilyByCache(familyId);
            return familyByCache.orElse(null);
        } catch (RuntimeException e) {
            log.error("Failed to get family by cache, appId={}, familyId={}", appId, familyId, e);
            return null;
        }
    }

    private FamilyBean getFamilyByUserId(int appId, long userId) {
        try {
            Optional<FamilyBean> familyByUserId = familyManager.getFamilyByUserId(userId);
            return familyByUserId.orElse(null);
        } catch (RuntimeException e) {
            log.error("Failed to get family by userId, appId={}, userId={}", appId, userId, e);
            return null;
        }
    }

    private String getUserNameOrEmpty(int appId, long userId) {
        try {
            List<SimpleUserDto> simpleUsers = userManager.getSimpleUserByIds(Collections.singletonList(userId));
            if (CollectionUtils.isEmpty(simpleUsers) || simpleUsers.get(0) == null) {
                return StringUtils.EMPTY;
            }
            return StringUtils.defaultString(simpleUsers.get(0).getName());
        } catch (RuntimeException e) {
            log.warn("Failed to get user name by cache, appId={}, userId={}", appId, userId, e);
            return StringUtils.EMPTY;
        }
    }

    private FamilyAwardDeliver getFamilyAwardDeliver(FamilyAwardTypeEnum awardType, FamilyAwardResourceDeliverTypeEnum deliverType) {
        for (FamilyAwardDeliver familyAwardDeliver : familyAwardDelivers) {
            if (familyAwardDeliver.supports(awardType, deliverType)) {
                return familyAwardDeliver;
            }
        }
        return null;
    }
}
