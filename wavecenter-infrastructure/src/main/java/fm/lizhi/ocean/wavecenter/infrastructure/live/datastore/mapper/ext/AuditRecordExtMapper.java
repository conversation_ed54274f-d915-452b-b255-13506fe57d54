package fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.common.datastore.mysql.constant.ParamContants;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.api.live.bean.AuditRecordSearchParamBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.GuildAuditStatsParamBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.RoomAuditStatsParamBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.RoomPushParamBean;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcAuditRecordFull;
import fm.lizhi.ocean.wavecenter.service.live.dto.GuildAuditRecordDto;
import fm.lizhi.ocean.wavecenter.service.live.dto.RoomAuditRecordDto;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @date 2025/7/14 17:08
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface AuditRecordExtMapper {

    @Select({
            "<script>"
            ,"SELECT DISTINCT(user_id)  "
            ,"from wavecenter_audit_record_full where (sign_nj_id = #{paramBean.roomId} or biz_nj_id=#{paramBean.roomId}) and app_id = #{paramBean.appId} "
            ,"<if test=' null != paramBean.familyId and paramBean.familyId > 0 '>"
            ," and (sign_family_id = #{paramBean.familyId} or biz_family_id=#{paramBean.familyId})"
            ,"</if>"
            ," and audit_start_time &gt;= #{paramBean.startTime}"
            ," and audit_start_time &lt;= #{paramBean.endTime}"
            , "</script>"
    })
    PageList<Long> roomAuditPlayer(@Param("paramBean") RoomPushParamBean paramBean
            , @Param(ParamContants.PAGE_NUMBER)int pageNumber
            , @Param(ParamContants.PAGE_SIZE)int pageSize);

    @Select({
            "<script>"
            , "select a.nj_id as roomId, count(DISTINCT (a.id)) as pushNumber, count(DISTINCT (a.user_id)) as pushPeopleNumber"
            , "from (select ts.sign_nj_id nj_id, ts.id, ts.user_id"
            , "      from wavecenter_audit_record_full ts"
            , "      where ts.sign_family_id = #{paramBean.familyId} and ts.sign_nj_id &gt; 0 and ts.app_id=#{paramBean.appId} "
            ,"<if test=' null != paramBean.roomId and paramBean.roomId > 0 '>"
            ," and ts.sign_nj_id = #{paramBean.roomId} "
            ,"</if>"

            , "<if test='null != paramBean.roomIds and paramBean.roomIds.size > 0'>"
            , "and ts.sign_nj_id in "
            , "<foreach collection='paramBean.roomIds' item='rId' open='(' separator=',' close=')'>"
            , "#{rId}"
            , "</foreach>"
            , "</if>"

            ," and ts.audit_start_time &gt;= #{paramBean.startTime} "
            ," and ts.reason != '空流超时' "
            ," and ts.audit_start_time &lt; #{paramBean.endTime} "
            , "union all"
            , "select tb.biz_nj_id nj_id, tb.id, tb.user_id"
            , "from wavecenter_audit_record_full tb"
            , "where tb.biz_family_id = #{paramBean.familyId} and tb.biz_nj_id &gt; 0 and tb.app_id=#{paramBean.appId} "
            ,"<if test=' null != paramBean.roomId and paramBean.roomId > 0 '>"
            ," and tb.biz_nj_id = #{paramBean.roomId} "
            ,"</if>"

            , "<if test='null != paramBean.roomIds and paramBean.roomIds.size > 0'>"
            , "and tb.biz_nj_id in "
            , "<foreach collection='paramBean.roomIds' item='rId' open='(' separator=',' close=')'>"
            , "#{rId}"
            , "</foreach>"
            , "</if>"

            ," and tb.audit_start_time &gt;= #{paramBean.startTime} "
            , " and tb.reason != '空流超时' "
            ," and tb.audit_start_time &lt; #{paramBean.endTime} "
            , ") a"
            , "group by a.nj_id order by ${order} "
            ,"</script>"
    })
    PageList<GuildAuditRecordDto> pageAuditRecordStats(@Param("paramBean") GuildAuditStatsParamBean paramBean
            , @Param("order") String order
            , @Param(ParamContants.PAGE_NUMBER)int pageNumber
            , @Param(ParamContants.PAGE_SIZE)int pageSize);

    @Select({
            "<script>"
            , "SELECT count(1) as pushNumber, count( DISTINCT(user_id) ) as pushPeopleNumber"
            , "from wavecenter_audit_record_full where (sign_nj_id = #{roomId} or biz_nj_id=#{roomId}) and app_id = #{appId}"
            , " and audit_start_time &gt;= #{startTime} "
            , " and audit_start_time &lt;= #{endTime} "
            , " and reason != '空流超时' "
            , "</script>"
    })
    RoomAuditRecordDto roomAuditRecordStats(RoomAuditStatsParamBean paramBean);

    @Select({
            "<script>"
            , "select  *  "
            , "from wavecenter_audit_record_full s "
            , "where s.app_id = #{paramBean.appId}"
            , "<if test='null != paramBean.familyId and paramBean.familyId > 0'>"
            , " and (s.sign_family_id = #{paramBean.familyId} or s.biz_family_id = #{paramBean.familyId}) "
            , "</if>"
            , "<if test='null != paramBean.roomId and paramBean.roomId > 0'>"
            , " and (s.sign_nj_id = #{paramBean.roomId} or s.biz_nj_id = #{paramBean.roomId}) "
            , "</if>"

            , "<if test='null != paramBean.roomIds and paramBean.roomIds.size > 0'>"
            , "and (s.sign_nj_id in "
            , "<foreach collection='paramBean.roomIds' item='rId' open='(' separator=',' close=')'>"
            , "#{rId}"
            , "</foreach>"
            , "or s.biz_nj_id in"
            , "<foreach collection='paramBean.roomIds' item='rId' open='(' separator=',' close=')'>"
            , "#{rId}"
            , "</foreach>"
            , ")"
            , "</if>"

            , "<if test='null != paramBean.userId and paramBean.userId > 0'>"
            , " and s.user_id = #{paramBean.userId}  "
            , "</if>"
            , "<if test='null != paramBean.op'>"
            , " and s.op = #{paramBean.op}  "
            , "</if>"
            , " and s.audit_start_time &gt;= #{paramBean.startTime}"
            , " and s.reason != '空流超时'"
            , " and s.audit_start_time &lt;= #{paramBean.endTime} order by s.audit_start_time desc"
            ,"</script>"
    })
    PageList<WcAuditRecordFull> selectAuditRecordFull(@Param("paramBean") AuditRecordSearchParamBean paramBean
            , @Param(ParamContants.PAGE_NUMBER)int pageNumber
            , @Param(ParamContants.PAGE_SIZE)int pageSize);

}
