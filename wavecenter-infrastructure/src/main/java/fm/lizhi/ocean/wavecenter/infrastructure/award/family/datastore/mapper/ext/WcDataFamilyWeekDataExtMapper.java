package fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcDataFamilyWeekData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcDataFamilyWeekDataExtMapper {

    @Select("SELECT * FROM `wavecenter_data_family_week_data`\n" +
            "WHERE `app_id` = #{appId} AND `family_id` = #{familyId} AND `start_time` = #{startTime}\n" +
            "ORDER BY `create_time` DESC\n" +
            "LIMIT 1")
    WcDataFamilyWeekData getFamilyWeekData(@Param("appId") int appId, @Param("familyId") long familyId, @Param("startTime") Date startTime);
}
