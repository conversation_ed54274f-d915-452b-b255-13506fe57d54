package fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.ListFamilySpecialRecommendCardNameBean;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.SaveFamilySpecialRecommendCardNameBean;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestUploadFamilySpecialRecommendCardName;
import fm.lizhi.ocean.wavecenter.base.convert.CommonConvert;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilySpecialRecommendCardName;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR,
        uses = {
                CommonConvert.class,
        },
        imports = {
                ConfigUtils.class,
                Date.class,
        }
)
public interface FamilySpecialRecommendCardNameConvert {

    FamilySpecialRecommendCardNameConvert I = Mappers.getMapper(FamilySpecialRecommendCardNameConvert.class);

    default List<WcFamilySpecialRecommendCardName> toCreateEntities(RequestUploadFamilySpecialRecommendCardName request) {
        if (request == null || CollectionUtils.isEmpty(request.getList())) {
            return Collections.emptyList();
        }
        ArrayList<WcFamilySpecialRecommendCardName> entities = new ArrayList<>(request.getList().size());
        for (SaveFamilySpecialRecommendCardNameBean bean : request.getList()) {
            WcFamilySpecialRecommendCardName entity = toCreateEntity(bean, request.getAppId(), request.getOperator());
            entities.add(entity);
        }
        return entities;
    }

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "createTime", expression = "java(new Date())")
    @Mapping(target = "creator", source = "operator")
    @Mapping(target = "modifyTime", expression = "java(new Date())")
    @Mapping(target = "modifier", source = "operator")
    WcFamilySpecialRecommendCardName toCreateEntity(SaveFamilySpecialRecommendCardNameBean bean, Integer appId, String operator);

    List<ListFamilySpecialRecommendCardNameBean> toListBeans(List<WcFamilySpecialRecommendCardName> entities);
}
