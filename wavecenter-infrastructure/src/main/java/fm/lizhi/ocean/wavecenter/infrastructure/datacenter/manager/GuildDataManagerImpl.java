package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.manager;

import cn.hutool.core.collection.CollUtil;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.CountDataBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.GuildAssessmentInfoBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.GuildRoomPerformanceResBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomPerformanceBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.constants.GuildDataMetricsConstant;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.*;
import fm.lizhi.ocean.wavecenter.infrastructure.common.MetricsUtil;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PaySettlePeriodNumberEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PayTenantCodeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.common.manager.CreatorDataQueryCommonManager;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.constants.MetricsConstants;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.constants.MetricsMeta;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.constants.MetricsNs;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.constants.StatPeriodConstants;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.convert.DataCenterInfraConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.convert.GuildDataConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.dao.WcDataFamilyDao;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.mapper.WcDataFamilyDayMapper;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.mapper.WcDataFamilyMonthMapper;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.mapper.WcDataFamilyWeekMapper;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.mapper.WcDataRoomFamilyDayMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.ext.WcDataFamilyDayExtMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.ext.WcDataFamilyMonthExtMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.ext.WcDataFamilyWeekExtMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.ext.WcDataPayRoomDayExtMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.*;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote.IGuildDataRemote;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.*;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.service.common.dto.PaySettlePeriodDto;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.GuildDataManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import fm.lizhi.pay.settle.api.creativecenter.CreatorDataQueryService;
import fm.lizhi.pay.settle.protocol.creativecenter.CreatorDataQueryProto;
import fm.lizhi.pay.settle.settleenum.creativecenter.PeriodTypeEnum;
import groovy.util.logging.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/17 17:15
 */
@Slf4j
@Component
public class GuildDataManagerImpl implements GuildDataManager {

    private static final Logger log = LoggerFactory.getLogger(GuildDataManagerImpl.class);
    @Resource
    private WcDataFamilyDayMapper familyDayMapper;
    @Resource
    private WcDataFamilyWeekMapper familyWeekMapper;
    @Resource
    private WcDataFamilyMonthMapper familyMonthMapper;
    @Autowired
    private UserManager userManager;
    @Autowired
    private CreatorDataQueryService creatorDataQueryService;
    @Autowired
    private CreatorDataQueryCommonManager creatorDataQueryCommonManager;
    @Autowired
    private IGuildDataRemote iGuildDataRemote;
    @Resource
    private WcDataRoomFamilyDayMapper roomFamilyDayMapper;
    @Resource
    private WcDataPayRoomDayExtMapper payRoomDayMapper;
    @Resource
    private WcDataFamilyDayExtMapper familyDayExtMapper;
    @Resource
    private WcDataFamilyWeekExtMapper familyWeekExtMapper;
    @Resource
    private WcDataFamilyMonthExtMapper familyMonthExtMapper;

    @Autowired
    private WcDataFamilyDao wcDataFamilyDao;

    @Override
    public Optional<GuildAssessmentInfoBean> getAssessmentInfo(int appId, long familyId) {
        return getAssessmentInfo(familyId, null);
    }

    @Override
    public Optional<GuildAssessmentInfoBean> getAssessmentInfo(long familyId, List<Long> roomIds) {
        GuildAssessmentInfoBean guildAssessmentInfoBean = iGuildDataRemote.queryAssessment(familyId, roomIds);
        guildAssessmentInfoBean.setFlushTime(System.currentTimeMillis());
        return Optional.of(guildAssessmentInfoBean);
    }

    @Override
    public GuildRoomPerformanceResBean roomPerformance(long familyId, List<Long> roomIds) {
        Date flushTime = new Date();
        String requestTime = DateUtil.formatDateToString(flushTime, DateUtil.NORMAL_DATE_FORMAT);
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        log.info("queryHallPerformance appId={}, familyId={}, roomIds={}", appId, familyId, JsonUtil.dumps(roomIds));

        CreatorDataQueryProto.QueryHallPerformanceRequest.Builder builder = CreatorDataQueryProto.QueryHallPerformanceRequest.newBuilder()
                .setTenantCode(PayTenantCodeEnum.getPayTenantCode(appId))
                .setFamilyId(familyId)
                .setPeriodType(PeriodTypeEnum.CURRENT_PERIOD.getPeriodType());
        if (CollectionUtils.isNotEmpty(roomIds)) {
            builder.addAllManageHallId(roomIds);
        }
        Result<CreatorDataQueryProto.ResponseQueryHallPerformance> result = creatorDataQueryService.queryHallPerformance(builder
                .build(), requestTime);

        if (RpcResult.isFail(result)) {
            log.warn("queryHallPerformance fail. familyId={}, appId={} ,rCode={}", familyId, appId, result.rCode());
            return null;
        }

        List<RoomPerformanceBean> performanceBeanList = DataCenterInfraConvert.I.hallPerformances2roomPerformanceBeans(result.target().getDataList());
        log.info("performanceBeanListSize={}", performanceBeanList.size());
        Map<Long, UserBean> roomInfoMap = performanceBeanList.stream().collect(Collectors.toMap(k -> k.getRoomInfo().getId(), RoomPerformanceBean::getRoomInfo, (k1, k2) -> k2));

        //厅主信息
        List<SimpleUserDto> userInfoList = userManager.getSimpleUserByIds(new ArrayList<>(roomInfoMap.keySet()));
        Map<Long, SimpleUserDto> userInfoMap = userInfoList.stream().collect(Collectors.toMap(SimpleUserDto::getId, v -> v));
        for (Map.Entry<Long, UserBean> entry : roomInfoMap.entrySet()) {
            SimpleUserDto userDto = userInfoMap.get(entry.getKey());
            if (userDto == null) {
                continue;
            }
            UserBean roomInfoBean = entry.getValue();
            roomInfoBean.setPhoto(userDto.getAvatar());
            roomInfoBean.setBand(userDto.getBand());
            roomInfoBean.setName(userDto.getName());
        }

        performanceBeanList.parallelStream().forEach(bean -> {
            bean.setRoomInfo(roomInfoMap.get(bean.getRoomInfo().getId()));
        });

        // 获取考核周期
        PaySettlePeriodDto period = creatorDataQueryCommonManager.getSettlePeriod(appId, familyId, PaySettlePeriodNumberEnum.CURRENT_PERIOD);

        return new GuildRoomPerformanceResBean().setPerformances(performanceBeanList)
                .setFlushTime(flushTime)
                .setStartDate(period.getStartDate())
                .setEndDate(period.getEndDate());
    }

    @Override
    public List<CountDataBean> getIndicatorTrend(long familyId, List<Long> roomIds, String metric, int days) {
        Date yesterday = DateUtil.getDayBefore(1);
        List<Integer> dates = new ArrayList<>(days);
        dates.add(MyDateUtil.getDateDayValue(yesterday));
        for (int i = 1; i < days; i++) {
            Date dayBefore = DateUtil.getDayBefore(yesterday, i);
            dates.add(MyDateUtil.getDateDayValue(dayBefore));
        }

        WcDataRoomFamilyDayExample example = new WcDataRoomFamilyDayExample();
        example.createCriteria()
                .andFamilyIdEqualTo(familyId)
                .andRoomIdIn(roomIds)
                .andStatDateValueIn(dates)
                .andAppIdEqualTo(ContextUtils.getBusinessEvnEnum().getAppId());

        List<WcDataRoomFamilyDay> dataList = roomFamilyDayMapper.selectByExample(example);
        // <dayValue, <roomId, data>>
        Map<Integer, Map<Long, WcDataRoomFamilyDay>> dayRoomDataMap = dataList.stream()
                .collect(Collectors.groupingBy(WcDataRoomFamilyDay::getStatDateValue, Collectors.toMap(WcDataRoomFamilyDay::getRoomId, v -> v, (k1, k2) -> k2)));

        List<CountDataBean> result = new ArrayList<>(days);
        for (Map.Entry<Integer, Map<Long, WcDataRoomFamilyDay>> entry : dayRoomDataMap.entrySet()) {
            Integer dayValue = entry.getKey();
            Map<Long, WcDataRoomFamilyDay> roomDataMap = entry.getValue();


        }

        return Collections.emptyList();
    }

    @Override
    public List<CountDataBean> getIndicatorTrend(long familyId, String metric, int days) {
        Date yesterday = DateUtil.getDayBefore(1);
        List<Integer> dates = new ArrayList<>(days);
        dates.add(MyDateUtil.getDateDayValue(yesterday));
        for (int i = 1; i < days; i++) {
            Date dayBefore = DateUtil.getDayBefore(yesterday, i);
            dates.add(MyDateUtil.getDateDayValue(dayBefore));
        }

        WcDataFamilyDayExample example = new WcDataFamilyDayExample();
        example.createCriteria()
                .andFamilyIdEqualTo(familyId)
                .andAppIdEqualTo(ContextUtils.getBusinessEvnEnum().getAppId())
                .andStatDateValueIn(dates);
        List<WcDataFamilyDay> list = familyDayMapper.selectByExample(example);
        Map<Integer, WcDataFamilyDay> valueMap = list.stream().collect(Collectors.toMap(WcDataFamilyDay::getStatDateValue, v -> v, (k1, k2) -> k2));

        List<CountDataBean> result = new ArrayList<>(days);
        for (Integer date : dates) {
            CountDataBean countDataBean = new CountDataBean();
            countDataBean.setDate(MyDateUtil.getDayValueDate(date));
            WcDataFamilyDay wcDataFamilyDay = valueMap.get(date);
            if (wcDataFamilyDay != null) {
                ReflectionUtils.doWithLocalFields(WcDataFamilyDay.class, field -> {
                    Optional<MetricsMeta> metricsMeta = MetricsConstants.getMetricsMeta(MetricsNs.FAMILY, metric);
                    if (!metricsMeta.isPresent()) {
                        return;
                    }

                    String name = field.getName();
                    if (!name.equals(metricsMeta.get().getAliasName())) {
                        return;
                    }

                    ReflectionUtils.makeAccessible(field);
                    Object value = ReflectionUtils.getField(field, wcDataFamilyDay);

                    MetricsMeta.ValueFactory valueFactory = metricsMeta.get().getValueFactory();
                    String formatValue = valueFactory.calculateValue(metricsMeta.get(), name, String.valueOf(value));
                    countDataBean.setValue(formatValue);
                });
            }
            result.add(countDataBean);
        }

        return result;
    }

    @Override
    public Map<String, String> getDayKeyIndicators(int appId, long familyId, Date date, List<String> valueMetrics) {
        WcDataFamilyDay param = new WcDataFamilyDay();
        param.setAppId(appId);
        param.setFamilyId(familyId);
        param.setStatDateValue(Integer.valueOf(DateUtil.formatDateToString(date, DateUtil.date)));

        log.info("getDayKeyIndicators selectMany. param={}", JsonUtil.dumps(param));
        List<WcDataFamilyDay> wcDataDays = familyDayMapper.selectMany(param);
        Map<String, String> baseValueMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(wcDataDays)) {
            WcDataFamilyDay wcDataDay = wcDataDays.get(0);
            baseValueMap = MetricsUtil.convertToMap(WcDataFamilyDay.class, wcDataDay);
        }

        return MetricsUtil.convertToResultMap(MetricsNs.FAMILY, valueMetrics, baseValueMap);
    }

    @Override
    public Map<String, String> getWeekKeyIndicators(int appId, long familyId, Date startDate, Date endDate, List<String> valueMetrics) {
        WcDataFamilyWeek param = new WcDataFamilyWeek();
        param.setAppId(appId);
        param.setFamilyId(familyId);
        param.setStartWeekDate(startDate);
        param.setEndWeekDate(endDate);

        log.info("getWeekKeyIndicators selectMany. param={}", JsonUtil.dumps(param));
        List<WcDataFamilyWeek> list = familyWeekMapper.selectMany(param);
        Map<String, String> baseValueMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(list)) {
            WcDataFamilyWeek wcDataWeek = list.get(0);
            baseValueMap = MetricsUtil.convertToMap(WcDataFamilyWeek.class, wcDataWeek);
        }

        return MetricsUtil.convertToResultMap(MetricsNs.FAMILY, valueMetrics, baseValueMap);
    }

    @Override
    public Map<String, String> getMonthKeyIndicators(int appId, long familyId, Date monthDate, List<String> valueMetrics) {
        WcDataFamilyMonth param = new WcDataFamilyMonth();
        param.setAppId(appId);
        param.setFamilyId(familyId);
        param.setStatMonth(Integer.valueOf(DateUtil.formatDateToString(monthDate, "yyyyMM")));

        log.info("getMonthKeyIndicators selectMany. param={}", JsonUtil.dumps(param));
        List<WcDataFamilyMonth> list = familyMonthMapper.selectMany(param);
        Map<String, String> baseValueMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(list)) {
            WcDataFamilyMonth wcDataMonth = list.get(0);
            baseValueMap = MetricsUtil.convertToMap(WcDataFamilyMonth.class, wcDataMonth);
        }

        return MetricsUtil.convertToResultMap(MetricsNs.FAMILY, valueMetrics, baseValueMap);
    }

    @Override
    public List<DataFamilyDayDTO> getFamilyDayList(long familyId, int appId, Date startDate, Date endDate) {
        WcDataFamilyDayExample example = new WcDataFamilyDayExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andFamilyIdEqualTo(familyId)
                .andStatDateValueBetween(Integer.valueOf(DateUtil.formatDateToString(startDate, DateUtil.date)), Integer.valueOf(DateUtil.formatDateToString(endDate, DateUtil.date)));
        example.setOrderByClause("stat_date_value asc");

        List<WcDataFamilyDay> list = familyDayMapper.selectByExample(example);
        if (CollUtil.isEmpty(list)) {
            log.info("getFamilyDayList is empty. appId={}, familyId={}, startDate={}, endDate={}", appId, familyId, startDate.getTime(), endDate.getTime());
            return Collections.emptyList();
        }
        return DataCenterInfraConvert.I.toDataFamilyDayDTOs(list);
    }

    @Override
    public List<DataFamilyDayDTO> getFamilyDayList(GetFamilyDayListParam param) {
        WcDataFamilyDayExample example = new WcDataFamilyDayExample();
        example.setOrderByClause("stat_date_value desc");
        WcDataFamilyDayExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(ContextUtils.getBusinessEvnEnum().getAppId());
        if (param.getFamilyId() != null) {
            criteria.andFamilyIdEqualTo(param.getFamilyId());
        }
        if (CollectionUtils.isNotEmpty(param.getDayValues())) {
            criteria.andStatDateValueIn(param.getDayValues());
        }
        List<WcDataFamilyDay> list = familyDayMapper.selectByExample(example);
        return DataCenterInfraConvert.I.toDataFamilyDayDTOs(list);
    }

    @Override
    public Integer getFamilyDayIncomeSum(Long familyId, Date startDate, Date endDate) {
        WcDataFamilyDayExample example = new WcDataFamilyDayExample();
        example.createCriteria()
                .andStatDateValueBetween(MyDateUtil.getDateDayValue(startDate), MyDateUtil.getDateDayValue(endDate))
                .andFamilyIdEqualTo(familyId)
                .andAppIdEqualTo(ContextUtils.getBusinessEvnEnum().getAppId());
        List<WcDataFamilyDay> list = familyDayMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }

        BigDecimal sum = list.stream().map(WcDataFamilyDay::getAllIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
        return sum.intValue();
    }

    /**
     * 获取公会数据关键指标
     *
     * @param familyId  公会id
     * @param appId     应用id
     * @param dateType  查询时间类型：day=日，week=周，month=月
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return 公会数据关键指标
     */
    private Map<String, String> getGuildDataKeyIndicatorsMap(long familyId, int appId, String dateType, Date startDate, Date endDate) {
        Map<String, String> po = null;
        if ("day".equals(dateType)) {
            List<WcDataFamilyDay> familyDays = familyDayMapper.selectByFamilyIdAndAppId(
                    familyId, appId, startDate, endDate);
            if (familyDays != null && !familyDays.isEmpty()) {
                // 把列表的数据累加
                WcDataFamilyDay familyDay = new WcDataFamilyDay();
                familyDay = familyDays.stream().reduce(familyDay, (a, b) -> {
                    a.setIncome(a.getIncome().add(b.getIncome()));
                    a.setCharm(a.getCharm() + b.getCharm());
                    a.setSignRoomCnt(a.getSignRoomCnt() + b.getSignRoomCnt());
                    a.setOpenRoomCnt(a.getOpenRoomCnt() + b.getOpenRoomCnt());
                    a.setRoomAvgIncome(a.getRoomAvgIncome().add(b.getRoomAvgIncome()));
                    a.setRoomAvgCharm(a.getRoomAvgCharm().add(b.getRoomAvgCharm()));
                    a.setSignPlayerCnt(a.getSignPlayerCnt() + b.getSignPlayerCnt());
                    a.setUpGuestPlayerCnt(a.getUpGuestPlayerCnt() + b.getUpGuestPlayerCnt());
                    a.setIncomePlayerCnt(a.getIncomePlayerCnt() + b.getIncomePlayerCnt());
                    a.setPlayerAvgIncome(a.getPlayerAvgIncome().add(b.getPlayerAvgIncome()));
                    a.setPlayerAvgCharm(a.getPlayerAvgCharm().add(b.getPlayerAvgCharm()));
                    return a;
                });
                po = convertToFamilyDateDataMap(familyDay);
            }
        }
        if ("week".equals(dateType)) {
            List<WcDataFamilyWeek> familyWeeks = familyWeekMapper.selectByFamilyIdAndAppId(
                    familyId, appId, startDate, endDate);
            if (familyWeeks != null && !familyWeeks.isEmpty()) {
                // 把列表的数据累加
                WcDataFamilyWeek familyWeek = new WcDataFamilyWeek();
                familyWeek = familyWeeks.stream().reduce(familyWeek, (a, b) -> {
                    a.setIncome(a.getIncome().add(b.getIncome()));
                    a.setCharm(a.getCharm() + b.getCharm());
                    a.setSignRoomCnt(a.getSignRoomCnt() + b.getSignRoomCnt());
                    a.setOpenRoomCnt(a.getOpenRoomCnt() + b.getOpenRoomCnt());
                    a.setRoomAvgIncome(a.getRoomAvgIncome().add(b.getRoomAvgIncome()));
                    a.setRoomAvgCharm(a.getRoomAvgCharm().add(b.getRoomAvgCharm()));
                    a.setSignPlayerCnt(a.getSignPlayerCnt() + b.getSignPlayerCnt());
                    a.setUpGuestPlayerCnt(a.getUpGuestPlayerCnt() + b.getUpGuestPlayerCnt());
                    a.setIncomePlayerCnt(a.getIncomePlayerCnt() + b.getIncomePlayerCnt());
                    a.setPlayerAvgIncome(a.getPlayerAvgIncome().add(b.getPlayerAvgIncome()));
                    a.setPlayerAvgCharm(a.getPlayerAvgCharm().add(b.getPlayerAvgCharm()));
                    return a;
                });
                po = convertToFamilyDateDataMap(familyWeek);
            }
        }
        if ("month".equals(dateType)) {
            List<WcDataFamilyMonth> familyMonths = familyMonthMapper.selectByFamilyIdAndAppId(
                    familyId, appId, startDate, endDate);
            if (familyMonths != null && !familyMonths.isEmpty()) {
                // 把列表的数据累加
                WcDataFamilyMonth familyMonth = new WcDataFamilyMonth();
                familyMonth = familyMonths.stream().reduce(familyMonth, (a, b) -> {
                    a.setIncome(a.getIncome().add(b.getIncome()));
                    a.setCharm(a.getCharm() + b.getCharm());
                    a.setSignRoomCnt(a.getSignRoomCnt() + b.getSignRoomCnt());
                    a.setOpenRoomCnt(a.getOpenRoomCnt() + b.getOpenRoomCnt());
                    a.setRoomAvgIncome(a.getRoomAvgIncome().add(b.getRoomAvgIncome()));
                    a.setRoomAvgCharm(a.getRoomAvgCharm().add(b.getRoomAvgCharm()));
                    a.setSignPlayerCnt(a.getSignPlayerCnt() + b.getSignPlayerCnt());
                    a.setUpGuestPlayerCnt(a.getUpGuestPlayerCnt() + b.getUpGuestPlayerCnt());
                    a.setIncomePlayerCnt(a.getIncomePlayerCnt() + b.getIncomePlayerCnt());
                    a.setPlayerAvgIncome(a.getPlayerAvgIncome().add(b.getPlayerAvgIncome()));
                    a.setPlayerAvgCharm(a.getPlayerAvgCharm().add(b.getPlayerAvgCharm()));
                    return a;
                });
                po = convertToFamilyDateDataMap(familyMonth);
            }
        }
        return po;
    }

    /**
     * 转换po对象
     *
     * @param familyData 公会数据对象
     * @return po对象
     */
    private Map<String, String> convertToFamilyDateDataMap(Object familyData) {
        if (familyData == null) {
            return null;
        }

        try {
            Map<String, String> metricHandlers = new HashMap<>(14);
            metricHandlers.put(GuildDataMetricsConstant.INCOME_TOTAL, (String) familyData.getClass().getMethod("getIncome").invoke(familyData));
            metricHandlers.put(GuildDataMetricsConstant.ROOM_GIFT_INCOME, "0");
            metricHandlers.put(GuildDataMetricsConstant.PERSONAL_LIVE_INCOME, "0");
            metricHandlers.put(GuildDataMetricsConstant.NOBILITY_UNIT_INCOME, "0");
            metricHandlers.put(GuildDataMetricsConstant.CHARM, (String) familyData.getClass().getMethod("getCharm").invoke(familyData));
            metricHandlers.put(GuildDataMetricsConstant.SIGN_ROOM_CNT, (String) familyData.getClass().getMethod("getSignRoomCnt").invoke(familyData));
            metricHandlers.put(GuildDataMetricsConstant.OPEN_ROOM_CNT, (String) familyData.getClass().getMethod("getOpenRoomCnt").invoke(familyData));
            metricHandlers.put(GuildDataMetricsConstant.ROOM_AVG_INCOME, (String) familyData.getClass().getMethod("getRoomAvgIncome").invoke(familyData));
            metricHandlers.put(GuildDataMetricsConstant.ROOM_AVG_CHARM, (String) familyData.getClass().getMethod("getRoomAvgCharm").invoke(familyData));
            metricHandlers.put(GuildDataMetricsConstant.SIGN_PLAYER_CNT, (String) familyData.getClass().getMethod("getSignPlayerCnt").invoke(familyData));
            metricHandlers.put(GuildDataMetricsConstant.UP_GUEST_PLAYER_CNT, (String) familyData.getClass().getMethod("getUpGuestPlayerCnt").invoke(familyData));
            metricHandlers.put(GuildDataMetricsConstant.INCOME_PLAYER_CNT, (String) familyData.getClass().getMethod("getIncomePlayerCnt").invoke(familyData));
            metricHandlers.put(GuildDataMetricsConstant.PLAYER_AVG_INCOME, (String) familyData.getClass().getMethod("getPlayerAvgIncome").invoke(familyData));
            metricHandlers.put(GuildDataMetricsConstant.PLAYER_AVG_CHARM, (String) familyData.getClass().getMethod("getPlayerAvgCharm").invoke(familyData));
            return metricHandlers;
        } catch (Exception e) {
            log.error("convertToFamilyDateDataPo ex.", e);
            return null;
        }
    }

    @Override
    public PageBean<GuildIncomeStatDTO> queryGuildIncomeStats(GuildIncomeStatParamDTO param) {
        QueryGuildIncomeResDTO resDTO = null;
        try {
            // 根据统计周期查询数据
            switch (param.getStatPeriod().toLowerCase()) {
                case StatPeriodConstants.DAY:
                    resDTO = queryDayStats(param);
                    break;
                case StatPeriodConstants.WEEK:
                    resDTO = queryWeekStats(param);
                    break;
                case StatPeriodConstants.MONTH:
                    resDTO = queryMonthStats(param);
                    break;
                default:
                    break;
            }
            return resDTO == null ? PageBean.empty() : PageBean.of(resDTO.getTotal(), resDTO.getResultList());
        } catch (Exception e) {
            log.error("Query guild income overview stats failed", e);
            return PageBean.empty();
        }
    }

    /**
     * 查询日统计数据
     */
    private QueryGuildIncomeResDTO queryDayStats(GuildIncomeStatParamDTO param) {
        List<GuildIncomeStatDTO> resultList = new ArrayList<>();
        //计算出是否需要从queryIncomeStatByDay查询数据
        Date lastMinTime = param.getLastMinTime() == null ? new Date() : new Date(param.getLastMinTime());
        Integer lastMinDayValue = MyDateUtil.getDateDayValue(lastMinTime);

        // 查询其他日期数据（从实时表获取）
        int total = (int) wcDataFamilyDao.countFamilyDay(param.getAppId(), param.getFamilyId());
        List<WcDataFamilyDayStatPo> dayStats = familyDayExtMapper.queryDayStatsByTime(param.getFamilyId(), param.getAppId(), lastMinDayValue, param.getPageSize());

        int lastMinDayValueBefore = MyDateUtil.getDateDayValue(DateUtil.getDayBefore(lastMinTime, 1));
        //如果缺少日统计的首天数据，说明大数据还没有洗出来，重实时数据中获取
        boolean hasLastMinDayData = dayStats.stream().anyMatch(stat -> stat.getStatDateValue().equals(lastMinDayValueBefore));
        if (!hasLastMinDayData) {
            //从实时表获取
            WcDataPayRoomDayIncomeStatPo firstDayStat = payRoomDayMapper.queryIncomeStatByDay(param.getFamilyId(), param.getAppId(), lastMinDayValueBefore);
            Date dayBefore = DateUtil.getDayBefore(lastMinTime, 1);
            IncomeSummaryDTO infoStat = GuildDataConvert.I.convertGuildInfoStat(firstDayStat);
            GuildIncomeStatDTO incomeStatDTO = GuildIncomeStatDTO.of(DateUtil.getDayStart(dayBefore).getTime(), DateUtil.getDayEnd(dayBefore).getTime(), infoStat);
            resultList.add(incomeStatDTO);
            total += infoStat == null ? 0 : 1;
        }

        for (WcDataFamilyDayStatPo stat : dayStats) {
            IncomeSummaryDTO infoStat = GuildDataConvert.I.convertGuildDayInfoStat(stat);
            GuildIncomeStatDTO incomeStatDTO = GuildIncomeStatDTO.of(stat.getStatDate().getTime(), DateUtil.getDayEnd(stat.getStatDate()).getTime(), infoStat);
            resultList.add(incomeStatDTO);
        }

        //按开始时间倒序排序
        resultList.sort(Comparator.comparing(GuildIncomeStatDTO::getStartTime).reversed());
        if (resultList.size() > param.getPageSize()) {
            resultList = resultList.subList(0, param.getPageSize());
        }
        return QueryGuildIncomeResDTO.of(total, resultList);
    }

    /**
     * 查询周统计数据
     */
    private QueryGuildIncomeResDTO queryWeekStats(GuildIncomeStatParamDTO param) {
        List<GuildIncomeStatDTO> resultList = new ArrayList<>();
        Date lastWeekTime = param.getLastMinTime() == null ? MyDateUtil.getCurrentWeekStart() : new Date(param.getLastMinTime());
        Date lastDate = DateUtil.formatStrToDate(DateUtil.formatDateToString(lastWeekTime, DateUtil.date_2), DateUtil.date_2);

        List<WcDataFamilyWeekStatPo> weekStats = familyWeekExtMapper.queryWeekStatsByTime(param.getFamilyId(), param.getAppId(), lastDate, param.getPageSize());
        int total = (int) wcDataFamilyDao.countFamilyWeek(param.getAppId(), param.getFamilyId());

        //判断是否存在缺少周统计的首周数据
        Date firstWeekDate = MyDateUtil.getLastWeekStart(lastWeekTime);
        boolean hasFirstWeekData = weekStats.stream().anyMatch(stat -> stat.getStartWeekDate().equals(firstWeekDate));
        if (!hasFirstWeekData) {
            //从实时表获取计算
            Date lastWeekEnd = MyDateUtil.getLastWeekEnd(lastWeekTime);
            WcDataPayRoomDayIncomeStatPo firstWeekStat = payRoomDayMapper.queryIncomeStatByWeek(param.getFamilyId(),
                    param.getAppId(), MyDateUtil.getDateDayValue(firstWeekDate), MyDateUtil.getDateDayValue(lastWeekEnd));
            IncomeSummaryDTO infoStat = GuildDataConvert.I.convertGuildInfoStat(firstWeekStat);
            GuildIncomeStatDTO incomeStatDTO = GuildIncomeStatDTO.of(firstWeekDate.getTime(), lastWeekEnd.getTime(), infoStat);
            resultList.add(incomeStatDTO);
            total += infoStat == null ? 0 : 1;
        }
        for (WcDataFamilyWeekStatPo stat : weekStats) {
            IncomeSummaryDTO infoStat = GuildDataConvert.I.convertGuildWeekInfoStat(stat);
            GuildIncomeStatDTO incomeStatDTO = GuildIncomeStatDTO.of(stat.getStartWeekDate().getTime(), stat.getEndWeekDate().getTime(), infoStat);
            resultList.add(incomeStatDTO);
        }

        //按周开始时间倒序排序
        resultList.sort(Comparator.comparing(GuildIncomeStatDTO::getStartTime).reversed());
        if (resultList.size() > param.getPageSize()) {
            resultList = resultList.subList(0, param.getPageSize());
        }
        return QueryGuildIncomeResDTO.of(total, resultList);
    }

    /**
     * 查询月统计数据
     */
    private QueryGuildIncomeResDTO queryMonthStats(GuildIncomeStatParamDTO param) {
        List<GuildIncomeStatDTO> resultList = new ArrayList<>();

        Date lastMinTime = param.getLastMinTime() == null ? DateUtil.getMonthStart(new Date()) : new Date(param.getLastMinTime());
        int startMonth = MyDateUtil.getDateMonthValue(lastMinTime);

        List<WcDataFamilyMonthStatPo> monthStats = familyMonthExtMapper.queryMonthStatsByTime(param.getFamilyId(), param.getAppId(), startMonth, param.getPageSize());
        int total = (int) wcDataFamilyDao.countFamilyMonth(param.getAppId(), param.getFamilyId());

        //判断是否存在缺少月统计的首月数据
        Date lastMonthDate = DateUtil.getMonthBefore(lastMinTime, 1);
        Integer firstMonth = MyDateUtil.getDateMonthValue(lastMonthDate);
        boolean hasFirstMonthData = monthStats.stream().anyMatch(stat -> stat.getStatMonth().equals(firstMonth));
        if (!hasFirstMonthData) {
            //从实时表获取
            WcDataPayRoomDayIncomeStatPo firstMonthStat = payRoomDayMapper.queryIncomeStatByMonth(param.getFamilyId(), param.getAppId(), firstMonth, firstMonth);
            IncomeSummaryDTO infoStat = GuildDataConvert.I.convertGuildInfoStat(firstMonthStat);
            GuildIncomeStatDTO incomeStatDTO = GuildIncomeStatDTO.of(lastMonthDate.getTime(), DateUtil.getMonthEnd(lastMonthDate).getTime(), infoStat);
            resultList.add(incomeStatDTO);
            total += infoStat == null ? 0 : 1;
        }

        for (WcDataFamilyMonthStatPo stat : monthStats) {
            Date monthDate = DateUtil.formatStrToDate(String.valueOf(stat.getStatMonth()), "yyyyMM");
            IncomeSummaryDTO infoStat = GuildDataConvert.I.convertGuildMonthInfoStat(stat);
            GuildIncomeStatDTO incomeStatDTO = GuildIncomeStatDTO.of(DateUtil.getMonthStart(monthDate).getTime(), DateUtil.getMonthEnd(monthDate).getTime(), infoStat);
            resultList.add(incomeStatDTO);
        }

        //按月开始时间倒序排序
        resultList.sort(Comparator.comparing(GuildIncomeStatDTO::getStartTime).reversed());
        if (resultList.size() > param.getPageSize()) {
            resultList = resultList.subList(0, param.getPageSize());
        }
        return QueryGuildIncomeResDTO.of(total, resultList);
    }
}