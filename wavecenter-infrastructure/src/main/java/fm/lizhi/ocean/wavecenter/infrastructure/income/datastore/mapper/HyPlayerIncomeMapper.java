package fm.lizhi.ocean.wavecenter.infrastructure.income.datastore.mapper;


import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.po.HyPlayerIncomePo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@DataStore(namespace = "mysql_heiye_lzppfamily_r")
public interface HyPlayerIncomeMapper {

    @Select({"<script>"
            , "select nj_id, nj_income, player_income, create_time"
            , "from player_income"
            , "where status = 70 and sign_status=1 and nj_id in "
            , "<foreach collection='njIds' item='njId' open='(' separator=',' close=')'>"
            , "#{njId}"
            , "</foreach>"
            , "</script>"})
    List<HyPlayerIncomePo> getNjIncome(@Param("njIds")List<Long> njIds);

    @Select(" select player_income from player_income " +
            "where nj_id = #{njId}  " +
            "  and sign_status = 1  " +
            "  and status = 70 order by create_time desc limit 1 ")
    Long getPlayerIncome(@Param("njId") long njId);

    @Select({"<script>"
            , "select nj_id, player_income"
            , "from player_income"
            , "where family_user_id = #{familyUserId}"

            , "and nj_id in "
            , "<foreach collection='njIds' item='njId' open='(' separator=',' close=')'>"
            , "#{njId}"
            , "</foreach>"

            , "  and (nj_id, create_time) in (select nj_id, MAX(create_time)"
            , "     from player_income"
            , "     where family_user_id = #{familyUserId}"
            , "       and nj_id in "
            , "<foreach collection='njIds' item='njId' open='(' separator=',' close=')'>"
            , "#{njId}"
            , "</foreach>"
            , "       and status = 70 and sign_status=1"
            , "     group by nj_id)"
            , "</script>"})
    List<HyPlayerIncomePo> selectBest(@Param("familyUserId") long familyUserId, @Param("njIds") List<Long> njIds);

    @Select({"<script>"
            , "select distinct nj_id"
            , "from player_income"
            , "where status = 70 and sign_status=1 and nj_id in "
            , "<foreach collection='njIds' item='njId' open='(' separator=',' close=')'>"
            , "#{njId}"
            , "</foreach>"
            , "<if test='null != max'>"
            , " and player_income &lt;=#{max}"
            , "</if>"
            , "<if test='null != min'>"
            , " and player_income &gt;=#{min}"
            , "</if>"
            , "</script>"})
    List<Long> selectSettleScopeNjIds(@Param("njIds") List<Long> njIds, @Param("max")Integer max, @Param("min") Integer min);

}
