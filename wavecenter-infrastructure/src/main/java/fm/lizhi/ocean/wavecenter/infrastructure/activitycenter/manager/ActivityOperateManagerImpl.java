package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.OfficialSeatExtraBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityResourceAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityResourceTypeEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.AutoConfigResourceEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ResourceExtraMapping;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.*;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.convert.ActivityMaterielConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.dao.*;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityUserCancelParamDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityUserModifyParamDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.DeleteOfficialSeatParamDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.handler.FlowResourceGiveHandler;
import fm.lizhi.ocean.wavecenter.service.activitycenter.handler.FlowResourceHandlerFactory;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityOperateManager;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityRedisManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 活动操作管理
 */
@Slf4j
@Component
public class ActivityOperateManagerImpl implements ActivityOperateManager {

    @Autowired
    private ActivityApplyDao applyDao;

    @Autowired
    private ActivityResourceGiveDao resourceGiveDao;

    @Autowired
    private ActivityRoomAnnouncementDeployDao roomAnnouncementDeployDao;

    @Autowired
    private ActivityMaterielConvert activityMaterielConvert;

    @Autowired
    private ActivityApplyFlowResourceDao flowResourceDao;

    @Autowired
    private ActivityOfficialSeatTimeDao officialSeatTimeDao;

    @Autowired
    private ActivityRedisManager activityRedisManager;

    @Autowired
    private ActivityFlowResourceGiveDao flowResourceGiveDao;

    @Autowired
    private FlowResourceHandlerFactory factory;

    @Autowired
    private ActivityApplyDecorateDao activityApplyDecorateDao;

    @Override
    public Result<String> cancelActivityAfterAudit(ActivityUserCancelParamDTO cancelParamDTO) {
        List<ActivityResourceGiveRecord> resourceGiveRecordList = resourceGiveDao.getResourceGiveRecordList(cancelParamDTO.getActivityId());
        ActivityAnnouncementDeployRecord announcementDeployRecord = roomAnnouncementDeployDao.getAnnouncementDeployRecord(cancelParamDTO.getActivityId());
        List<ActivityOfficialSeatTime> officialSeatTimeList = getOfficialSeatTimeList(cancelParamDTO.getActivityId(), cancelParamDTO.getAppId());
        //过滤出流量资源的id
        List<Long> giveIds = resourceGiveRecordList.stream()
                .filter(record -> record.getType() == ActivityResourceTypeEnum.FLOW_RESOURCE.getType())
                .map(ActivityResourceGiveRecord::getId).collect(Collectors.toList());
        //查询出流量资源发放记录ID
        List<ActivityFlowResourceGiveRecord> flowResourceGiveList = flowResourceGiveDao.getFlowResourceGiveRecordByGiveIds(giveIds);
        try {
            //调用RPC接口调用下游取消操作，失败了提示
            applyDao.cancelActivityAfterAudit(cancelParamDTO, officialSeatTimeList, resourceGiveRecordList, announcementDeployRecord, cancelParamDTO.getVersion());
            String errorMsg = cancelBizResource(flowResourceGiveList);
            return RpcResult.success(errorMsg);
        } catch (Exception e) {
            log.error("cancelActivityAfterAudit error, activityId:{}", cancelParamDTO.getActivityId(), e);
            return RpcResult.fail(CANCEL_ACTIVITY_FAIL, "取消活动失败，请稍候重试");
        }
    }


    /**
     * 用户取消活动申请，活动处于审批前状态
     *
     * @param cancelParamDTO 上下文信息
     * @return 结果
     */
    @Override
    public Result<Void> cancelActivityBeforeAudit(ActivityUserCancelParamDTO cancelParamDTO) {
        List<ActivityOfficialSeatTime> officialSeatTimeList = getOfficialSeatTimeList(cancelParamDTO.getActivityId(), cancelParamDTO.getAppId());
        try {
            // 未审批前取消，只需要修改活动状态即可
            applyDao.cancelActivityBeforeAudit(cancelParamDTO, officialSeatTimeList);
        } catch (Exception e) {
            log.error("cancelActivityBeforeAudit error, activityId:{}", cancelParamDTO.getActivityId(), e);
            return RpcResult.fail(CANCEL_ACTIVITY_FAIL, "取消活动失败，请稍候重试");
        }
        return RpcResult.success();
    }

    @Override
    public Result<Void> userModifyActivityAfterAudit(ActivityUserModifyParamDTO paramDTO) {
        ActivityApplyInfo activityInfo = applyDao.getActivityInfoById(paramDTO.getActivityId());
        //查询出之前的装扮发放记录
        List<ActivityResourceGiveRecord> oldDressUpGiveRecordList = resourceGiveDao.getDressUpResourceGiveRecordList(paramDTO.getActivityId());
        List<Long> giveIds = CollectionUtils.isNotEmpty(oldDressUpGiveRecordList) ? oldDressUpGiveRecordList.stream().map(ActivityResourceGiveRecord::getId).collect(Collectors.toList()) : Collections.emptyList();

        //设置主持人id和陪档主播id
        activityInfo.setAccompanyNjIds(paramDTO.getAccompanyNjIds());
        activityInfo.setHostId(paramDTO.getHostId());

        // 查询活动装扮资源
        List<ActivityApplyDecorate> decorates = activityApplyDecorateDao.getDecorates(paramDTO.getActivityId());

        //构建装扮发放记录
        Pair<List<ActivityResourceGiveRecord>, List<ActivityDressUpGiveRecord>> dressRecordPair = activityMaterielConvert.buildDressUpResources(activityInfo, decorates);

        // 设置活动信息
        try {
            ActivityApplyInfo applyInfo = buildApplyInfo(paramDTO);
            applyDao.updateActivityAndDressUp(applyInfo, giveIds, dressRecordPair.getLeft(), dressRecordPair.getRight());
        } catch (Exception e) {
            log.error("userModifyActivityAfterAudit error，activityId:{}: ", paramDTO.getActivityId(), e);
            return RpcResult.fail(MODIFY_ACTIVITY_FAIL, "修改活动失败，请稍候重试");
        }

        return RpcResult.success();
    }

    /**
     * 取消活动资源发放
     *
     * @param resourceGiveRecordList 资源发放记录
     * @return 结果
     */
    private String cancelBizResource(List<ActivityFlowResourceGiveRecord> resourceGiveRecordList) {
        StringBuilder errorBuilder = new StringBuilder();
        for (ActivityFlowResourceGiveRecord record : resourceGiveRecordList) {
            FlowResourceGiveHandler handler = factory.getHandler(record.getResourceCode());
            DeleteOfficialSeatParamDTO dto = new DeleteOfficialSeatParamDTO().setBizRecordId(record.getBizRecordId()).setNjId(record.getUserId());
            Result<Void> result = handler.cancelGiveFlowResource(dto);
            if (RpcResult.isFail(result)) {
                errorBuilder.append(record.getResourceName()).append("取消业务流量资源，原因：").append(result.getMessage()).append("\n");
            }
        }
        return errorBuilder.toString();
    }

    /**
     * 构建待修改的活动信息
     *
     * @param paramDTO 参数信息
     * @return 活动信息
     */
    private ActivityApplyInfo buildApplyInfo(ActivityUserModifyParamDTO paramDTO) {
        ActivityApplyInfo applyInfo = new ActivityApplyInfo();
        applyInfo.setId(paramDTO.getActivityId());
        applyInfo.setAccompanyNjIds(Optional.ofNullable(paramDTO.getAccompanyNjIds()).orElse(""));
        applyInfo.setHostId(Optional.ofNullable(paramDTO.getHostId()).orElse(0L));
        applyInfo.setVersion(paramDTO.getVersion());
        return applyInfo;
    }

    /**
     * 根据活动ID和appId查询出官频位时间列表
     *
     * @param activityId 活动ID
     * @param appId      appId
     * @return 官频位时间列表
     */
    private List<ActivityOfficialSeatTime> getOfficialSeatTimeList(Long activityId, Integer appId) {
        Optional<ActivityApplyFlowResource> flowResource = flowResourceDao.getFlowResourceByActivityIdAndCode(activityId, appId, AutoConfigResourceEnum.OFFICIAL_SEAT);
        if (!flowResource.isPresent() || flowResource.get().getStatus().equals(ActivityResourceAuditStatusEnum.DISABLE_GIVE.getStatus())) {
            return Collections.emptyList();
        }

        //查询出官频位时间列表
        String extra = flowResource.get().getExtra();
        //判空
        if (StringUtils.isBlank(extra)) {
            return Collections.emptyList();
        }
        OfficialSeatExtraBean bean = ResourceExtraMapping.convertJsonToExtra(extra, AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode());
        if (bean == null) {
            return Collections.emptyList();
        }

        //将扩展字段反序列化成对象
        return officialSeatTimeDao.getOfficialSeatTimeList(appId, bean.getStartTime(), bean.getEndTime(), bean.getSeat());
    }
}
