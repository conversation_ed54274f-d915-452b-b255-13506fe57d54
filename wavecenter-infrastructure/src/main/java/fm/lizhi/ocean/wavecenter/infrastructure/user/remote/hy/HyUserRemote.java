package fm.lizhi.ocean.wavecenter.infrastructure.user.remote.hy;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.CacheStats;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.hy.account.innermap.api.PpUserInnerMapService;
import fm.lizhi.hy.account.innermap.protocol.PpUserInnerMapProto;
import fm.lizhi.hy.security.ban.api.HyUserBanService;
import fm.lizhi.hy.security.ban.protocol.HyUserBanProto;
import fm.lizhi.hy.user.account.user.api.HyNewUserService;
import fm.lizhi.hy.user.account.user.api.HyWithdrawUserService;
import fm.lizhi.hy.user.account.user.protocol.HyUserBaseProto;
import fm.lizhi.hy.user.account.user.protocol.HyUserProto;
import fm.lizhi.hy.user.account.user.protocol.HyUserRequestProto;
import fm.lizhi.hy.user.account.verify.api.HYUserVerifyProxyService;
import fm.lizhi.hy.user.account.verify.protocol.HYUserVerifyProxyProto;
import fm.lizhi.ocean.lamp.common.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.user.convert.UserConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.IUserRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.req.GetUserVerifyDataReq;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.req.GetUserWithdrawStatusReq;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.res.GetUserVerifyDataRes;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.res.GetUserWithdrawStatusRes;
import fm.lizhi.ocean.wavecenter.service.user.config.UserConfig;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserInfoDto;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserMediaDto;
import fm.lizhi.pp.security.constant.ban.BanStatus;
import fm.lizhi.security.sms.api.H5TokenService;
import fm.lizhi.security.sms.protocol.SmsProto;
import hy.fm.lizhi.live.pp.push.api.PpDatePlayService;
import hy.fm.lizhi.live.pp.push.protocol.PpDatePlayProto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class HyUserRemote implements IUserRemote {

    @Autowired
    private HYUserVerifyProxyService userVerifyProxyService;

    @Autowired
    private HyNewUserService hyNewUserService;

    @Autowired
    private UserConfig userConfig;

    @Autowired
    private CommonConfig commonConfig;

    @Autowired
    private HyUserBanService hyUserBanService;

    @Autowired
    private H5TokenService h5TokenService;

    @Autowired
    private PpDatePlayService ppDatePlayService;

    @Autowired
    private HyUserBanService userBanService;

    @Autowired
    private PpUserInnerMapService ppUserInnerMapService;

    @Autowired
    private HyWithdrawUserService hyWithdrawUserService;

    /**
     * 用户信息缓存
     */
    private final LoadingCache<Long, Optional<SimpleUserDto>> SIMPLE_USER_CACHE = CacheBuilder.newBuilder()
            .maximumSize(20000)
            .expireAfterAccess(10, TimeUnit.MINUTES)
            .recordStats()
            .build(new CacheLoader<Long, Optional<SimpleUserDto>>() {
                @Override
                public Optional<SimpleUserDto> load(Long userId) throws Exception {
                    // @what：直接调批量接口查询 @why：PP服务的单个查询接口会查询标签信息，这里不需要
                    Map<Long, Optional<SimpleUserDto>> userMap = remoteLoadSimpleUserMap(Lists.newArrayList(userId));
                    if (userMap.get(userId) == null) {
                        return Optional.empty();
                    }
                    return userMap.get(userId);
                }

                @Override
                public Map<Long, Optional<SimpleUserDto>> loadAll(Iterable<? extends Long> userIds) throws Exception {
                    //批量查询，调用getAll方法时，除去缓存中存在的，剩下的调用此方法批量查询，例如：[1,2,3,4]，缓存中存在2,3，那么调用此方法查询1,4
                    return remoteLoadSimpleUserMap(Lists.newArrayList(userIds));
                }
            });

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.HEI_YE;
    }

    @Override
    public List<SimpleUserDto> getSimpleUserByIds(List<Long> ids) {
        try {
            ImmutableMap<Long, Optional<SimpleUserDto>> userCacheMap = SIMPLE_USER_CACHE.getAll(ids);

            if (userConfig.getHy().isPrintCacheStatLogSwitch()) {
                CacheStats stats = SIMPLE_USER_CACHE.stats();
                long size = SIMPLE_USER_CACHE.size();
                log.info("hy getSimpleUserByIds cache size={}, stats:{}", size, stats.toString());
            }

            List<SimpleUserDto> simpleUserList = new ArrayList<>(ids.size());
            for (Optional<SimpleUserDto> userOptional : userCacheMap.values()) {
                userOptional.ifPresent(simpleUserList::add);
            }
            return simpleUserList;
        } catch (Exception e) {
            log.warn("hy,getSimpleUserByIds exception:", e);
            return Collections.emptyList();
        }
    }

    @Override
    public Optional<GetUserVerifyDataRes> getUserVerifyDataResult(GetUserVerifyDataReq param) {
        Result<HYUserVerifyProxyProto.ResponseGetUserVerifyResult> result = userVerifyProxyService.getUserVerifyResult(HYUserVerifyProxyProto.GetUserVerifyResultParams.newBuilder()
                .setAppId(param.getAppId())
                .setUserId(param.getUserId())
                .build());
        if (RpcResult.isFail(result)) {
            return Optional.empty();
        }
        return Optional.ofNullable(UserConvert.I.hyVerifyPb2Res(result.target().getResult()));
    }

    @Override
    public boolean getUserBanStatus(Long userId) {
        Result<HyUserBanProto.ResponseGetUserBanStatus> result = hyUserBanService.getUserBanStatus(userId);
        if (RpcResult.isFail(result)) {
            log.warn("hy.getUserBanStatus fail userId={},rCode={}", userId, result.rCode());
            return true;
        }
        return !Objects.equals(result.target().getStatus(), BanStatus.NORMAL.getValue());
    }

    @Override
    public Optional<Long> getUserIdByBusinessToken(String businessToken) {
        Result<SmsProto.ResponseGetH5UserIdByToken> result = h5TokenService.getH5UserIdByToken(businessToken);
        if (RpcResult.isFail(result)) {
            log.warn("hy.getUserIdByBusinessToken fail businessToken={},rCode={}", businessToken, result.rCode());
            return Optional.empty();
        }
        return Optional.of(result.target().getUserId());
    }

    @Override
    public Optional<SimpleUserDto> getUserInfoByBand(String band) {
        HyUserRequestProto.GetUserRequest request = HyUserRequestProto.GetUserRequest.newBuilder()
                .setBand(band).build();
        Result<HyUserProto.ResponseGetUser> result = hyNewUserService.getUser(request);
        if (RpcResult.isFail(result)) {
            log.warn("hy getUserInfoByBand fail band={},rCode={}", band, result.rCode());
            return Optional.empty();
        }
        HyUserProto.ResponseGetUser user = result.target();
        return Optional.of(
                convertToSimple(user.getUser())
        );
    }


    private SimpleUserDto convertToSimple(HyUserBaseProto.User user) {
        long userId = user.getId();
        SimpleUserDto simpleUserDto = new SimpleUserDto();
        simpleUserDto.setId(userId);
        simpleUserDto.setBand(user.getBand());
        simpleUserDto.setName(user.getName());
        simpleUserDto.setAvatar(UrlUtils.getAvatarUrl(commonConfig.getHy().getCdnHost(), user.getPortrait()));
        return simpleUserDto;
    }


    private Map<Long, Optional<SimpleUserDto>> remoteLoadSimpleUserMap(List<Long> userIdList) {
        Map<Long, Optional<SimpleUserDto>> result = new HashMap<>();
        try {
            Result<HyUserProto.ResponseGetUsers> resp = hyNewUserService.getUsers(userIdList);
            if (RpcResult.isFail(resp)) {
                log.warn("hy,remoteLoadSimpleUserMap,rCode={},userIdList={}", resp.rCode(), JsonUtils.toJsonString(userIdList));
                return Collections.emptyMap();
            }

            List<HyUserBaseProto.User> userList = resp.target().getUserList();
            Map<Long, HyUserBaseProto.User> userMap = userList.stream().collect(Collectors.toMap(HyUserBaseProto.User::getId, v -> v, (k1, k2) -> k1));
            for (Long userId : userIdList) {
                HyUserBaseProto.User user = userMap.get(userId);
                if (user == null) {
                    LogContext.addReqLog("userId={} not found", userId);
                    result.put(userId, Optional.empty());
                    continue;
                }

                SimpleUserDto simpleUserDto = UserConvert.I.hyUserPb2SimpleDto(user);
                simpleUserDto.setAvatar(UrlUtils.getAvatarUrl(commonConfig.getHy().getCdnHost(), user.getPortrait()));
                result.put(userId, Optional.of(simpleUserDto));
            }
        } catch (Exception e) {
            log.warn("hy,remoteLoadSimpleUserMap,exception", e);
        }
        return result;
    }

    @Override
    public List<UserInfoDto> getUserInfoByIds(List<Long> userIds) {
        Result<HyUserProto.ResponseGetUsers> result = hyNewUserService.getUsers(userIds);
        if (RpcResult.isFail(result)) {
            log.warn("hy,getUserInfoByIds,rCode={},userIds={}", result.rCode(), JsonUtil.dumps(userIds));
            return Collections.emptyList();
        }

        List<HyUserBaseProto.User> userList = result.target().getUserList();
        List<UserInfoDto> list = new ArrayList<>();
        for (HyUserBaseProto.User user : userList) {
            list.add(convertToUserInfoDto(user));
        }
        return list;
    }

    @Override
    public Optional<UserInfoDto> getUserInfoById(long userId) {
        return Optional.empty();
    }

    private UserInfoDto convertToUserInfoDto(HyUserBaseProto.User user) {
        UserInfoDto userInfoDto = new UserInfoDto();
        userInfoDto.setId(user.getId());
        userInfoDto.setName(user.getName());
        userInfoDto.setBand(user.getBand());
        userInfoDto.setPhoneNum(user.getPhoneNum());
        return userInfoDto;
    }

    @Override
    public Optional<UserMediaDto> getUserMediaById(long userId) {
        Result<PpDatePlayProto.ResponsePlayerMediaList> result = ppDatePlayService.playerMediaList(0L, userId);
        if (RpcResult.isFail(result)) {
            log.error("hy playerMediaList fail. userId={},rCode={}", userId, result.rCode());
            return Optional.empty();
        }

        return Optional.of(new UserMediaDto()
                .setAlbumListJSON(result.target().getAlbumListJSON())
                .setVoiceListJSON(result.target().getVoiceListJSON())
                .setVideoListJSON(result.target().getVideoListJSON())
        );
    }

    @Override
    public int getUserBandStatus(Long uid) {
        Result<HyUserBanProto.ResponseGetUserBanStatus> result = hyUserBanService.getUserBanStatus(uid);
        if (result.rCode() == 0) {
            HyUserBanProto.ResponseGetUserBanStatus status = result.target();
            // 1-正常 2-临时封禁 3-永久封禁
            return status.getStatus();
        }
        // 默认返回正常
        log.warn("getUserBanStatus fail rCode={},uid={}", result.rCode(), uid);
        return 1;
    }

    @Override
    public boolean finishPlayerCenterAuth(Long userId) {
        Result<PpUserInnerMapProto.ResponseUserInnerMapList> result = ppUserInnerMapService.userInnerMapList(userId, "PLAYER_BASIC_INDEX_FIN");
        int rCode = result.rCode();
        if (rCode != 0) {
            log.warn("hy userInnerMapList fail. rCode={}, uid={}", rCode, userId);
            return false;
        }
        List<PpUserInnerMapProto.UserInnerMap> list = result.target().getUserInnerMapList();
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        return "1".equals(list.get(0).getValue());
    }

    @Override
    public Optional<SimpleUserDto> getUserByKeyWord(Long userId, String userName) {
        HyUserRequestProto.GetUserRequest request = HyUserRequestProto.GetUserRequest.newBuilder()
                .setUserId(userId)
                .setName(userName)
                .build();
        Result<HyUserProto.ResponseGetUser> result = hyNewUserService.getUser(request);
        if (RpcResult.isFail(result)) {
            log.error("hy getUserByKeyWord fail. userId={},userName={},rCode={}", userId, userName, result.rCode());
            return Optional.empty();
        }
        HyUserBaseProto.User user = result.target().getUser();
        return Optional.of(UserConvert.I.hyUserPb2SimpleDto(user));
    }

    @Override
    public Optional<GetUserWithdrawStatusRes> getUserWithdrawStatus(GetUserWithdrawStatusReq param) {
        HyUserRequestProto.CheckUserWithdrawStatusRequest request = HyUserRequestProto.CheckUserWithdrawStatusRequest.newBuilder().setUserId(param.getUserId()).build();
        Result<HyUserProto.ResponseCheckUserWithdrawStatus> result = hyWithdrawUserService.checkUserWithdrawStatus(request);
        if (RpcResult.isFail(result)) {
            return Optional.empty();
        }
        GetUserWithdrawStatusRes withdrawStatus = GetUserWithdrawStatusRes.builder()
                .userId(result.target().getUserId())
                .note(result.target().getNote())
                .withdraw(result.target().getIsWithdraw())
                .build();
        return Optional.ofNullable(withdrawStatus);
    }
}
