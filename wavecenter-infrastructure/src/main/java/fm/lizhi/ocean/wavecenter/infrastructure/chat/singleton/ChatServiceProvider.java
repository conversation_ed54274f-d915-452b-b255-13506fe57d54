package fm.lizhi.ocean.wavecenter.infrastructure.chat.singleton;

import fm.lizhi.common.dubbo.DubboClientBuilder;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.ServiceProviderConstants;
import fm.lizhi.pp.social.api.chat.PpChatService;
import fm.lizhi.xm.common.flow.services.chat.XmChatFlowService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/4/10 14:51
 */
@Configuration
public class ChatServiceProvider {

    @Bean
    public PpChatService ppChatService() {
        return new DubboClientBuilder<>(PpChatService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public fm.lizhi.hy.chat.api.PpChatService hyChatService() {
        return new DubboClientBuilder<>(fm.lizhi.hy.chat.api.PpChatService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public XmChatFlowService xmChatService() {
        return new DubboClientBuilder<>(XmChatFlowService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }


}
