package fm.lizhi.ocean.wavecenter.infrastructure.grow.datastore.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 公会等级结算记录表
 *
 * @date 2025-03-20 05:32:04
 */
@Table(name = "`wavecenter_family_level_week_record`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcFamilyLevelWeekRecord {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 等级ID
     */
    @Column(name= "`level_id`")
    private Long levelId;

    /**
     * 公会ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 经验值
     */
    @Column(name= "`exp`")
    private Integer exp;

    /**
     * 业务 ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 最近结算周期开始时间
     */
    @Column(name= "`settle_start_time`")
    private Date settleStartTime;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`update_time`")
    private Date updateTime;

    /**
     * 0=未删除，1=已删除
     */
    @Column(name= "`deleted`")
    private Integer deleted;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", levelId=").append(levelId);
        sb.append(", familyId=").append(familyId);
        sb.append(", exp=").append(exp);
        sb.append(", appId=").append(appId);
        sb.append(", settleStartTime=").append(settleStartTime);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", deleted=").append(deleted);
        sb.append("]");
        return sb.toString();
    }
}