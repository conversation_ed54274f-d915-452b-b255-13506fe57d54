package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.dao;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityResourceGiveStatusEnum;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityFlowResourceGiveRecord;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityFlowResourceGiveRecordExample;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.mapper.ActivityFlowResourceGiveRecordMapper;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.UpdateFlowResourceStatusDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class ActivityFlowResourceGiveDao {

    @Autowired
    private ActivityFlowResourceGiveRecordMapper activityFlowResourcesGiveRecordMapper;

    /**
     * 批量保存
     *
     * @param records 记录列表
     * @return 结果
     */
    public boolean batchSaveRecords(List<ActivityFlowResourceGiveRecord> records) {
        if (CollectionUtils.isEmpty(records)) {
            return true;
        }

        for (ActivityFlowResourceGiveRecord record : records) {
            if (record.getCreateTime() == null) {
                record.setCreateTime(new Date());
            }
            if (record.getModifyTime() == null) {
                record.setModifyTime(new Date());
            }
        }

        return activityFlowResourcesGiveRecordMapper.batchInsert(records) == records.size();
    }

    public boolean batchUpdateFlowResourceStatus(List<UpdateFlowResourceStatusDTO> records) {
        if (CollectionUtils.isEmpty(records)) {
            return true;
        }
        for (UpdateFlowResourceStatusDTO record : records) {
            ActivityFlowResourceGiveRecordExample example = new ActivityFlowResourceGiveRecordExample();
            example.createCriteria().andIdEqualTo(record.getId()).andStatusEqualTo(record.getOriginalStatus());
            ActivityFlowResourceGiveRecord giveRecord = ActivityFlowResourceGiveRecord.builder().id(record.getId()).bizRecordId(record.getBizRecordId()).status(record.getStatus()).modifyTime(new Date()).build();
            boolean res = activityFlowResourcesGiveRecordMapper.updateByExample(giveRecord, example) > 0;
            if (!res) {
                return false;
            }
        }
        return true;
    }

    /**
     * 根据giveIds查询记录
     *
     * @param giveIds 发放记录ID
     * @return 记录列表
     */
    public List<ActivityFlowResourceGiveRecord> getFlowResourceGiveRecordByGiveIds(List<Long> giveIds) {
        if (CollectionUtils.isEmpty(giveIds)) {
            return new ArrayList<>();
        }
        ActivityFlowResourceGiveRecordExample example = new ActivityFlowResourceGiveRecordExample();
        example.createCriteria().andGiveIdIn(giveIds).andStatusEqualTo(ActivityResourceGiveStatusEnum.SUCCESS.getStatus());
        return activityFlowResourcesGiveRecordMapper.selectByExample(example);
    }

    /**
     * 批量删除流量资源
     *
     * @param giveIds 发放ID
     * @return 结果
     */
    public boolean batchDeleteFlowResource(List<Long> giveIds) {
        if (CollectionUtils.isEmpty(giveIds)) {
            return true;
        }
        ActivityFlowResourceGiveRecordExample example = new ActivityFlowResourceGiveRecordExample();
        example.createCriteria().andGiveIdIn(giveIds);
        return activityFlowResourcesGiveRecordMapper.deleteByExample(example) > 0;
    }
}
