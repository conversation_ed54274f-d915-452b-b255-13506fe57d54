package fm.lizhi.ocean.wavecenter.infrastructure.kafka;

import fm.lizhi.common.kafka.ioc.api.KafkaTemplate;
import fm.lizhi.common.kafka.ioc.spring.EnableKafkaClients;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/5/19
 */
@Configuration
@EnableKafkaClients(basePackages = "fm.lizhi.ocean.wavecenter.infrastructure.kafka.consumer")
public class KafkaClientConfiguration {

    @Bean("xmKafkaTemplate")
    @ConditionalOnMissingBean(name = "xmKafkaTemplate")
    public KafkaTemplate xmKafkaTemplate() {
        return new KafkaTemplate("xm-kafka250-bootstrap-server");
    }

    @Bean("hyKafkaTemplate")
    @ConditionalOnMissingBean(name = "hyKafkaTemplate")
    public KafkaTemplate hyKafkaTemplate() {
        return new KafkaTemplate("hy-kafka250-bootstrap-server");
    }

    @Bean("ppKafkaTemplate")
    @ConditionalOnMissingBean(name = "ppKafkaTemplate")
    public KafkaTemplate ppKafkaTemplate() {
        return new KafkaTemplate("pp-kafka250-bootstrap-server");
    }

}
