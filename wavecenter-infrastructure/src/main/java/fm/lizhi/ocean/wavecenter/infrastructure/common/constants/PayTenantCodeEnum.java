package fm.lizhi.ocean.wavecenter.infrastructure.common.constants;


import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * pp lizhi_ppapp_live
 * 黑叶 lizhi_heiye_common
 * 小西米 lizhi_ximi_common
 */
public enum PayTenantCodeEnum {

    pp(BusinessEvnEnum.PP.getAppId(), "lizhi_ppapp_live"),
    ximi(BusinessEvnEnum.XIMI.getAppId(), "lizhi_ximi_common"),
    heiye(BusinessEvnEnum.HEI_YE.getAppId(), "lizhi_heiye_common")
    ;


    private int appId;
    private String payTenantCode;

    PayTenantCodeEnum(int appId, String payTenantCode) {
        this.appId = appId;
        this.payTenantCode = payTenantCode;
    }



    private static Map<Integer,String> tenantCodeMap = new ConcurrentHashMap<>(10,1);

    static {
        for (PayTenantCodeEnum value : PayTenantCodeEnum.values()) {
            tenantCodeMap.put(value.getAppId(),value.payTenantCode);
        }
    }

    /**
     * 获取 支付的 租户code
     *
     * @param appId
     * @return
     */
    public static String getPayTenantCode(int appId){
        return tenantCodeMap.getOrDefault(appId, pp.payTenantCode);
    }

    public static Optional<String> getPayTenantCodeNullable(int appId){
        return Optional.ofNullable(tenantCodeMap.get(appId));
    }

    public static Integer toAppId(String payTenantCode){
        for (PayTenantCodeEnum value : values()) {
            if (value.payTenantCode.equals(payTenantCode)) {
                return value.appId;
            }
        }
        return null;
    }

    public int getAppId() {
        return appId;
    }

    public String getPayTenantCode() {
        return payTenantCode;
    }
}
