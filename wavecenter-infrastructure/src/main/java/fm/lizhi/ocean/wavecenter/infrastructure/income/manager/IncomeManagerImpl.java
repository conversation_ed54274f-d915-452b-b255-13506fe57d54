package fm.lizhi.ocean.wavecenter.infrastructure.income.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.GuildIncomeSummaryBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.GuildIncomeSummaryThreadBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomIncomeDetailBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomIncomeSummaryBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.*;
import fm.lizhi.ocean.wavecenter.api.income.constants.IncomeType;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.ThreadUtils;
import fm.lizhi.ocean.wavecenter.common.dto.PageDto;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PaySettleConfigCodeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PayTenantCodeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.AdminPlayerIncomeInfoPo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote.ICharmStatRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.income.convert.IncomeInfraConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.income.dto.PlayerPayCountParamDto;
import fm.lizhi.ocean.wavecenter.infrastructure.income.remote.*;
import fm.lizhi.ocean.wavecenter.infrastructure.income.remote.req.GetSignRoomIncomeDetailReq;
import fm.lizhi.ocean.wavecenter.infrastructure.income.remote.req.PersonalGiftflowReq;
import fm.lizhi.ocean.wavecenter.infrastructure.income.remote.req.RoomGiftflowReq;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.IContractRemote;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.GuildDataManager;
import fm.lizhi.ocean.wavecenter.service.gift.dto.GiftDto;
import fm.lizhi.ocean.wavecenter.service.gift.manager.GiftManager;
import fm.lizhi.ocean.wavecenter.service.income.dto.GiveGiftFlowDto;
import fm.lizhi.ocean.wavecenter.service.income.manager.IncomeManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import fm.lizhi.pay.settle.api.creativecenter.CreatorDataQueryService;
import fm.lizhi.pay.settle.protocol.creativecenter.CreatorDataQueryProto;
import fm.lizhi.pay.settle.settleenum.creativecenter.DirectionEnum;
import fm.lizhi.pay.settle.settleenum.creativecenter.PeriodTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import fm.lizhi.ocean.wavecenter.service.datacenter.dto.GuildIncomeStatParamDTO;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.GuildIncomeStatDTO;

/**
 * <AUTHOR>
 * @date 2024/4/23 15:23
 */
@Slf4j
@Component
public class IncomeManagerImpl implements IncomeManager {

    /**
     * 多线程调用线程池
     */
    private final ExecutorService executorService = ThreadUtils.getTtlExecutors("income-manager", 50, 50);

    @Autowired
    private IContractRemote iContractRemote;
    @Autowired
    private PaymentManager paymentManager;
    @Autowired
    private IFlowRemote iFlowRemote;
    @Autowired
    private GiftManager giftManager;
    @Autowired
    private UserManager userManager;
    @Autowired
    private ICharmStatRemote iCharmStatRemote;

    @Autowired
    private CreatorDataQueryService creatorDataQueryService;

    @Autowired
    private AnchorIncomeManager anchorIncomeManager;

    @Autowired
    private GuildDataManager guildDataManager;


    @Autowired
    private PersonalIncomeRemote personalIncomeRemote;

    @Autowired
    private IIncomeStateRemote iIncomeStateRemote;

    @Autowired
    private IGuildIncomeRemote iGuildIncomeRemote;

    @Autowired
    private IRoomIncomeRemote iRoomIncomeRemote;

    @Override
    public int getPlayerPayCount(long familyId, Date startDate, Date endDate) {
        return iIncomeStateRemote.getPlayerPayCountByFamily(familyId, startDate, endDate);
    }

    @Override
    public int getPlayerPayCount(long familyId, List<Long> roomIds, Date startDate, Date endDate) {
        List<Long> queryRoomIds = roomIds;
        if (CollectionUtils.isEmpty(queryRoomIds)) {
            //roomIds为空，则查询出时间范围内签约的厅
            PageBean<Long> roomPage = iContractRemote.getSingGuildRoomsByDate(familyId, startDate, endDate, 1, 500);
            if (CollectionUtils.isEmpty(roomPage.getList())) {
                LogContext.addResLog("roomList is empty");
                return 0;
            }

            queryRoomIds = roomPage.getList();
        }

        //查询每个厅的签约主播 主播的签约跟着厅走，所以不用根据时间范围查询
        Set<Long> playerIds = iContractRemote.getAllSignRoomPlayerIds(queryRoomIds);
        if (CollectionUtils.isEmpty(playerIds)) {
            LogContext.addResLog("playerIds is empty");
            return 0;
        }

        return iIncomeStateRemote.getPlayerPayCountForFamily(new PlayerPayCountParamDto()
                .setFamilyId(familyId)
                .setRoomIds(queryRoomIds)
                .setPlayerIds(new ArrayList<>(playerIds)), startDate, endDate);
    }

    @Override
    public int getPlayerPayCountByRoom(Long familyId, Long roomId, Date startDate, Date endDate) {
        return iIncomeStateRemote.getPlayerPayCountByRoom(familyId, roomId, startDate, endDate);
    }

    @Override
    public PageBean<RoomIncomeDetailBean> signRoomIncomeDetail(SignRoomIncomeDetailParamBean paramBean) {
        Long familyId = paramBean.getFamilyId();
        Long njId = paramBean.getNjId();
        Date startDate = paramBean.getStartDate();
        Date endDate = paramBean.getEndDate();
        Integer page = paramBean.getPage();
        Integer pageSize = paramBean.getPageSize();
        List<Long> roomIds = paramBean.getRoomIds();

        List<Long> njIds = new ArrayList<>();
        if (njId == null) {
            PageBean<Long> allSingGuildRooms = iContractRemote.getSingGuildRoomsByDate(familyId, startDate, endDate, page, pageSize);
            njIds = allSingGuildRooms.getList();

            if (CollectionUtils.isNotEmpty(roomIds)) {
                //交集
                njIds = njIds.stream().filter(roomIds::contains).collect(Collectors.toList());
            }

        } else {
            njIds.add(njId);
        }

        if (CollectionUtils.isEmpty(njIds)) {
            LogContext.addResLog("njIds is empty");
            return PageBean.empty();
        }

        Map<Long, AdminPlayerIncomeInfoPo> njIncomeInfoMap =
                iCharmStatRemote.getAdminPlayerIncomeInfoMap(njIds, startDate, endDate);
        //查询有收入主播数
        Map<Long, Integer> njIncomeSignPlayerNumMap =
                iIncomeStateRemote.getPlayerPayCountByRooms(familyId, njIds, startDate, endDate);

        List<RoomIncomeDetailBean> beanList = iGuildIncomeRemote.getSignRoomIncomeDetail(GetSignRoomIncomeDetailReq.builder()
                .roomIds(njIds)
                .familyId(familyId)
                .startDate(startDate)
                .endDate(endDate)
                .build());

        for (RoomIncomeDetailBean bean : beanList) {
            UserBean roomInfo = bean.getRoomInfo();
            if (roomInfo == null) {
                continue;
            }
            Long roomId = roomInfo.getId();
            AdminPlayerIncomeInfoPo adminPlayerIncomeInfoPo = njIncomeInfoMap.get(roomId);
            bean.setCharm(adminPlayerIncomeInfoPo != null ? adminPlayerIncomeInfoPo.getTotalCharmValue() : 0L);
            bean.setPlayerPayCount(njIncomeSignPlayerNumMap.getOrDefault(roomId, 0));
        }
        return PageBean.of(beanList.size(), beanList);
    }

    @Override
    public PageBean<GuildIncomeDetailBean> getGuildIncomeDetail(GetGuildIncomeDetailParamBean paramBean) {
        CreatorDataQueryProto.Page page = CreatorDataQueryProto.Page.newBuilder()
                .setPageNumber(paramBean.getPageNo())
                .setPageSize(paramBean.getPageSize())
                .build();

        log.info("getGuildIncomeDetail req paramBean={}", JSONObject.toJSONString(paramBean));

        CreatorDataQueryProto.QueryHallIncomeDetailRequest.Builder builder = CreatorDataQueryProto.QueryHallIncomeDetailRequest.newBuilder()
                .setFamilyId(paramBean.getFamilyId())
                .setHallId(paramBean.getRoomId())
                .setTenantCode(PayTenantCodeEnum.getPayTenantCode(paramBean.getAppId()))
                .setTimeStart(fm.lizhi.commons.util.DateUtil.formatDateNormal(paramBean.getStartDate()))
                .setTimeEnd(fm.lizhi.commons.util.DateUtil.formatDateNormal(paramBean.getEndDate()))
                .setDirection(DirectionEnum.ALL.getCode())
                .setPage(page);

        if (CollUtil.isNotEmpty(paramBean.getIncomeType())) {
            builder.addAllBizId(paramBean.getIncomeType().stream().map(IncomeType::getValue).map(String::valueOf).collect(Collectors.toSet()));
        }

        Result<CreatorDataQueryProto.ResponseQueryHallIncomeDetail> result = creatorDataQueryService.queryHallIncomeDetail(builder.build(), fm.lizhi.commons.util.DateUtil.formatDateToString(new Date(paramBean.getFlushTime()), fm.lizhi.commons.util.DateUtil.NORMAL_DATE_FORMAT));
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("getGuildIncomeDetail fail  req paramBean={} rCode={}", JSONObject.toJSONString(paramBean), result.rCode());
            return PageBean.empty();
        }
        CreatorDataQueryProto.ResponseQueryHallIncomeDetail hallIncomeDetail = result.target();
        List<CreatorDataQueryProto.QueryHallIncomeDetailResponse> dataList = hallIncomeDetail.getDataList();
        List<GuildIncomeDetailBean> detailBeans = dataList.stream().map(e -> GuildIncomeDetailBean.builder()
                        .content(e.getRemark())
                        .date(fm.lizhi.commons.util.DateUtil.formatStrToDate(e.getHappenedTime(), "yyyy-MM-dd HH:mm:ss"))
                        .income(e.getAmount())
                        .roomInfo(new UserBean().setId(paramBean.getRoomId()))
                        .incomeType(e.getBizId())
                        .build())
                .collect(Collectors.toList());

        log.info("getGuildIncomeDetail response  paramBean={}, total={}, detailBeans={} ", JSONObject.toJSONString(paramBean), hallIncomeDetail.getTotalCount(), JSONObject.toJSONString(detailBeans));
        return PageBean.of(hallIncomeDetail.getTotalCount(), detailBeans, paramBean.getFlushTime());
    }


    @Override
    public GuildIncomeDetailSumBean getGuildIncomeDetailSum(GetGuildIncomeDetailSumParamBean paramBean) {
        String requestTime = fm.lizhi.commons.util.DateUtil.formatDateToString(new Date(), fm.lizhi.commons.util.DateUtil.NORMAL_DATE_FORMAT);
        String startDate = fm.lizhi.commons.util.DateUtil.formatDateToString(paramBean.getStartDate(), fm.lizhi.commons.util.DateUtil.date_2) + " 00:00:00";
        String endDate = fm.lizhi.commons.util.DateUtil.formatDateToString(paramBean.getEndDate(), fm.lizhi.commons.util.DateUtil.date_2) + " 23:59:59";
        String tenantCode = PayTenantCodeEnum.getPayTenantCode(ContextUtils.getBusinessEvnEnum().getAppId());

        log.info("getGuildIncomeDetailSum req request={} ", JSONObject.toJSONString(paramBean));
        CreatorDataQueryProto.QueryHallIncomeDetailRequest.Builder builder = CreatorDataQueryProto.QueryHallIncomeDetailRequest.newBuilder()
                .setTenantCode(tenantCode)
                .setFamilyId(paramBean.getFamilyId())
                .setHallId(paramBean.getRoomId())
                .setTimeStart(startDate)
                .setTimeEnd(endDate)
                .setDirection(DirectionEnum.ALL.getCode())
                .setPage(CreatorDataQueryProto.Page.newBuilder().setPageSize(10).setPageNumber(1).build());
        if (CollUtil.isNotEmpty(paramBean.getIncomeType())) {
            builder.addAllBizId(paramBean.getIncomeType().stream().map(IncomeType::getValue).map(String::valueOf).collect(Collectors.toSet()));
        }

        Result<CreatorDataQueryProto.ResponseQueryHallIncomeDetailTotal> result = creatorDataQueryService.queryHallIncomeDetailTotal(builder.build(), requestTime);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("getGuildIncomeDetailSum error params={} rCode={}", JSONObject.toJSONString(paramBean), result.rCode());
            return GuildIncomeDetailSumBean.builder().income(0).build();
        }
        GuildIncomeDetailSumBean detailSumBean = GuildIncomeDetailSumBean.builder().income(Integer.parseInt(result.target().getStatisticsValue())).build();
        log.info("getGuildIncomeDetailSum response request={} detailSumBean={}", JSONObject.toJSONString(paramBean), JSONObject.toJSONString(detailSumBean));
        return detailSumBean;
    }

    @Override
    public GuildIncomeSummaryBean guildIncomeSummary(long familyId, List<Long> roomIds) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        GuildIncomeSummaryThreadBean tempRes = new GuildIncomeSummaryThreadBean();
        CompletableFuture.allOf(
                CompletableFuture.runAsync(() -> {
                    //历史待确认收入
                    tempRes.setCanSettlement(paymentManager.getHallSettleAbleAmount(appId, familyId, roomIds));
                }, executorService),
                CompletableFuture.runAsync(() -> {
                    //本周待结算收入
                    tempRes.setTobeSettlement(paymentManager.queryTradeStatisticsValue(familyId, roomIds, PaySettleConfigCodeEnum.FAMILY_WAIT_SETTLEMENT_INCOME_TOTAL_AMOUNT, PeriodTypeEnum.CURRENT_PERIOD));
                }, executorService),
                CompletableFuture.runAsync(() -> {
                    //查询当天数据
                    tempRes.setDay(iIncomeStateRemote.queryTradeValueByFamily(familyId, roomIds, PeriodTypeEnum.TODAY));
                }, executorService),
                CompletableFuture.runAsync(() -> {
                    //查询当周数据
                    tempRes.setWeek(iIncomeStateRemote.queryTradeValueByFamily(familyId, roomIds, PeriodTypeEnum.CURRENT_WEEK));
                }, executorService),
                CompletableFuture.runAsync(() -> {
                    //查询当月数据
                    tempRes.setMonth(iIncomeStateRemote.queryTradeValueByFamily(familyId, roomIds, PeriodTypeEnum.CURRENT_MONTH));
                }, executorService)
        ).join();

        return new GuildIncomeSummaryBean()
                .setCanSettlement(tempRes.getCanSettlement())
                .setTobeSettlement(tempRes.getTobeSettlement())
                .setDay(tempRes.getDay())
                .setWeek(tempRes.getWeek())
                .setMonth(tempRes.getMonth());
    }

    @Override
    public RoomIncomeSummaryBean roomIncomeSummary(long familyId, long roomId, int appId) {
        String tenantCode = PayTenantCodeEnum.getPayTenantCode(appId);

        GuildIncomeSummaryThreadBean tempRes = new GuildIncomeSummaryThreadBean();
        CompletableFuture.allOf(
                CompletableFuture.runAsync(() -> {
                    log.info("roomIncomeSummary[getHallSettleableAmount] req familyId={}, tenantCode={}", familyId, tenantCode);
                    tempRes.setCanSettlement(paymentManager.getHallSettleAbleAmount(appId, familyId, roomId));
                }, executorService),
                CompletableFuture.runAsync(() -> {
                    log.info("roomIncomeSummary[queryTradeStatisticsValue] req familyId={}, tenantCode={},PeriodType={}", familyId, tenantCode, PeriodTypeEnum.CURRENT_PERIOD.getPeriodType());
                    tempRes.setTobeSettlement(paymentManager.queryTradeStatisticsValue(familyId, roomId, null, PaySettleConfigCodeEnum.HALL_WAIT_SETTLEMENT_INCOME_TOTAL_AMOUNT, PeriodTypeEnum.CURRENT_PERIOD));
                }, executorService),
                CompletableFuture.runAsync(() -> {
                    //查询当天数据
                    tempRes.setDay(iIncomeStateRemote.queryTradeValueByRoom(familyId, roomId, PeriodTypeEnum.TODAY));
                }, executorService),
                CompletableFuture.runAsync(() -> {
                    //查询当周数据
                    tempRes.setWeek(iIncomeStateRemote.queryTradeValueByRoom(familyId, roomId, PeriodTypeEnum.CURRENT_WEEK));
                }, executorService),
                CompletableFuture.runAsync(() -> {
                    //查询当月数据
                    tempRes.setMonth(iIncomeStateRemote.queryTradeValueByRoom(familyId, roomId, PeriodTypeEnum.CURRENT_MONTH));
                }, executorService)
        ).join();

        return new RoomIncomeSummaryBean()
                .setCanSettlement(tempRes.getCanSettlement())
                .setTobeSettlement(tempRes.getTobeSettlement())
                .setDay(tempRes.getDay())
                .setWeek(tempRes.getWeek())
                .setMonth(tempRes.getMonth());
    }


    @Override
    public PageBean<PersonalIncomeDetailBean> getPersonalIncomeDetail(GetPersonalIncomeDetailParamBean paramBean) {
        CreatorDataQueryProto.ResponseQueryAnchorIncomeDetail result = anchorIncomeManager.queryAnchorIncomeDetail(paramBean.getAppId(), paramBean.getUserId(),
                DirectionEnum.ALL, null, paramBean.getStartDate(), paramBean.getEndDate(), PaySettleConfigCodeEnum.QUERY_ANCHOR_PERSONAL_INCOME_DETAIL, paramBean.getPageNo(), paramBean.getPageSize(), paramBean.getFlushTime());
        if (null == result) {
            return PageBean.empty();
        }
        List<PersonalIncomeDetailBean> beans = IncomeInfraConvert.I.queryAnchorIncomeDetailResponses2personalIncomeDetailBeans(result.getDataList());

        log.info("getPersonalIncomeDetail req={},total={},beans={}", result.getTotalCount(), JSONObject.toJSONString(paramBean), JSONObject.toJSONString(beans));
        return PageBean.of(result.getTotalCount(), beans);
    }

    @Override
    public PageBean<PlayerIncomeDetailBean> getPlayerIncomeDetail(GetPlayerIncomeDetailParamBean paramBean) {
        CreatorDataQueryProto.ResponseQueryAnchorIncomeDetail result = anchorIncomeManager.queryAnchorIncomeDetail(paramBean.getAppId(), paramBean.getUserId(),
                DirectionEnum.IN, paramBean.getIncomeType(), paramBean.getStartDate(), paramBean.getEndDate(), PaySettleConfigCodeEnum.QUERY_ANCHOR_INDIVIDUAL_INCOME_DETAIL, paramBean.getPageNo(), paramBean.getPageSize(), paramBean.getFlushTime());

        if (null == result) {
            return PageBean.empty();
        }

        List<CreatorDataQueryProto.QueryAnchorIncomeDetailResponse> list = result.getDataList();
        List<PlayerIncomeDetailBean> beans = IncomeInfraConvert.I.queryAnchorIncomeDetailResponses2PlayerIncomeDetailBeans(list);
        log.info("getPlayerIncomeDetail response total={}, beans={} ", result.getTotalCount(), JSONObject.toJSONString(beans));
        return PageBean.of(result.getTotalCount(), beans);
    }


    @Override
    public PersonalIncomeDetailSumBean getPersonalIncomeDetailSum(GetPersonalIncomeDetailSumParamBean paramBean) {
        Integer income = anchorIncomeManager.queryAnchorIncomeDetailTotal(paramBean.getAppId(), paramBean.getUserId(), paramBean.getStartDate(), paramBean.getEndDate(),
                DirectionEnum.ALL, PaySettleConfigCodeEnum.QUERY_ANCHOR_PERSONAL_INCOME_DETAIL, null);
        return new PersonalIncomeDetailSumBean()
                .setIncome(income);
    }

    @Override
    public PlayerIncomeDetailSumBean getPlayerIncomeDetailSum(GetPlayerIncomeDetailSumParamBean paramBean) {
        Integer income = anchorIncomeManager.queryAnchorIncomeDetailTotal(paramBean.getAppId(), paramBean.getUserId(), paramBean.getStartDate(), paramBean.getEndDate(),
                DirectionEnum.IN, PaySettleConfigCodeEnum.QUERY_ANCHOR_INDIVIDUAL_INCOME_DETAIL, paramBean.getIncomeType());
        return new PlayerIncomeDetailSumBean()
                .setIncome(income);
    }


    @Override
    public PageBean<RoomSignPlayerIncomeBean> getRoomSignPlayerIncome(GetRoomSignPlayerIncomeParamBean paramBean) {
        //差异化查询
        return iRoomIncomeRemote.getRoomSignPlayerIncome(paramBean);
    }

    @Override
    public PlayerSumResBean playerSum(long userId) {

        PlayerSumResBean playerSumResBean = new PlayerSumResBean();

        //时间
        Date today = new Date();
        DateTime dayStart = DateUtil.beginOfDay(today);
        DateTime dayEnd = DateUtil.endOfDay(today);

        DateTime weekStart = DateUtil.beginOfWeek(today);
        DateTime weekEnd = DateUtil.endOfWeek(today);

        DateTime monthStart = DateUtil.beginOfMonth(today);
        DateTime monthEnd = DateUtil.endOfMonth(today);

        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        String tenantCode = PayTenantCodeEnum.getPayTenantCode(appId);
        // 可提现余额
        CreatorDataQueryProto.GetAnchorAccountBalanceRequest anchorCanAccountBalanceRequest = CreatorDataQueryProto.GetAnchorAccountBalanceRequest.newBuilder()
                .setTenantCode(tenantCode)
                .setConfigCode(PaySettleConfigCodeEnum.PERSONAL_GET_CANITHDRAW_TOTAL_AMOUNT.getConfigCode())
                .setAnchorId(userId).build();
        Result<CreatorDataQueryProto.ResponseGetAnchorAccountBalance> anchorCanAccountBalanceResult = creatorDataQueryService.getAnchorAccountBalance(anchorCanAccountBalanceRequest, fm.lizhi.commons.util.DateUtil.formatDateToString(new Date(), fm.lizhi.commons.util.DateUtil.NORMAL_DATE_FORMAT));
        if (anchorCanAccountBalanceResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("playerSum getAnchorAccountBalance[PERSONAL_GET_CANITHDRAW_TOTAL_AMOUNT]  error appId={} userId={} rCode={} ", appId, userId, anchorCanAccountBalanceResult.rCode());
        } else {
            long canAccountBalance = anchorCanAccountBalanceResult.target().getBalance();
            playerSumResBean.setCanSettlement(String.valueOf(canAccountBalance));
        }

        // 待结算余额
        CreatorDataQueryProto.GetAnchorAccountBalanceRequest anchorToBeSettledAccountBalanceRequest = CreatorDataQueryProto.GetAnchorAccountBalanceRequest.newBuilder()
                .setTenantCode(tenantCode)
                .setConfigCode(PaySettleConfigCodeEnum.PERSONAL_GET_TO_BETTLED_TOTAL_AMOUNT.getConfigCode())
                .setAnchorId(userId).build();
        Result<CreatorDataQueryProto.ResponseGetAnchorAccountBalance> anchorToBeSettledAccountBalanceResult = creatorDataQueryService.getAnchorAccountBalance(anchorToBeSettledAccountBalanceRequest, fm.lizhi.commons.util.DateUtil.formatDateToString(new Date(), fm.lizhi.commons.util.DateUtil.NORMAL_DATE_FORMAT));
        if (anchorToBeSettledAccountBalanceResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("playerSum getAnchorAccountBalance[PERSONAL_GET_TO_BETTLED_TOTAL_AMOUNT]  error appId={} userId={} rCode={} ", appId, userId, anchorToBeSettledAccountBalanceResult.rCode());
        } else {
            long toBeSettledAccountBalance = anchorToBeSettledAccountBalanceResult.target().getBalance();
            playerSumResBean.setTobeSettlement(String.valueOf(toBeSettledAccountBalance));
        }

        //TODO 是否需要考虑解约情况
        Long familyId = 0L;
        PlayerSumDataBean personal = new PlayerSumDataBean();

        // 个人收入
        long todayIndividualBalance = paymentManager.queryTradeStatisticsValue(familyId, userId, PaySettleConfigCodeEnum.PERSONAL_ANCHOR_PERSONAL_INCOME_TOTAL_AMOUNT, PeriodTypeEnum.TODAY);
        PlayerSumTimeBean day = new PlayerSumTimeBean();
        day.setSumIncome(String.valueOf(todayIndividualBalance));
        personal.setDay(day);

        long weekIndividualBalance = paymentManager.queryTradeStatisticsValue(familyId, userId, PaySettleConfigCodeEnum.PERSONAL_ANCHOR_PERSONAL_INCOME_TOTAL_AMOUNT, PeriodTypeEnum.CURRENT_WEEK);
        PlayerSumTimeBean week = new PlayerSumTimeBean();
        week.setSumIncome(String.valueOf(weekIndividualBalance));
        personal.setWeek(week);

        long monthIndividualBalance = paymentManager.queryTradeStatisticsValue(familyId, userId, PaySettleConfigCodeEnum.PERSONAL_ANCHOR_PERSONAL_INCOME_TOTAL_AMOUNT, PeriodTypeEnum.CURRENT_MONTH);
        PlayerSumTimeBean month = new PlayerSumTimeBean();
        month.setSumIncome(String.valueOf(monthIndividualBalance));
        personal.setMonth(month);

        // 个人收入
        playerSumResBean.setPersonal(personal);

        // 个播收入
        PlayerSumDataBean personalHall = personalIncomeRemote.getPlayerSumData(tenantCode, familyId, userId);
        playerSumResBean.setPersonalHall(personalHall);

        // 考核收入
        PlayerRevenueSumDataBean playerRevenueSumData = personalIncomeRemote.getPlayerRevenueSumData(tenantCode, familyId, userId);
        playerSumResBean.setRevenue(playerRevenueSumData);

        // 考核比例
        String incomeRatio = personalIncomeRemote.getIncomeRatio(userId);
        playerSumResBean.setIncomeRatio(incomeRatio);

        //个人收礼流水魅力值
        PlayerSumDataBean giftFlow = new PlayerSumDataBean();
        //今天
        GiveGiftFlowDto dayPersonalFlow = iFlowRemote.getPersonalGiftflowSum(new PersonalGiftflowReq()
                .setRecUserId(userId)
                .setStartDate(dayStart)
                .setEndDate(dayEnd)
        );

        Integer dayCharm = Optional.ofNullable(dayPersonalFlow).map(GiveGiftFlowDto::getCharm).orElse(0);
        Integer dayIncome = Optional.ofNullable(dayPersonalFlow).map(GiveGiftFlowDto::getIncome).orElse(0);
        giftFlow.setDay(new PlayerSumTimeBean().setSumCharm(String.valueOf(dayCharm)).setSumIncome(String.valueOf(dayIncome)));

        //本周
        GiveGiftFlowDto weekPersonalFlow = iFlowRemote.getPersonalGiftflowSum(new PersonalGiftflowReq()
                .setRecUserId(userId)
                .setStartDate(weekStart)
                .setEndDate(weekEnd)
        );

        Integer weekCharm = Optional.ofNullable(weekPersonalFlow).map(GiveGiftFlowDto::getCharm).orElse(0);
        Integer weekIncome = Optional.ofNullable(weekPersonalFlow).map(GiveGiftFlowDto::getIncome).orElse(0);
        giftFlow.setWeek(new PlayerSumTimeBean().setSumCharm(String.valueOf(weekCharm)).setSumIncome(String.valueOf(weekIncome)));

        //本月
        GiveGiftFlowDto monthPersonalFlow = iFlowRemote.getPersonalGiftflowSum(new PersonalGiftflowReq()
                .setRecUserId(userId)
                .setStartDate(monthStart)
                .setEndDate(monthEnd)
        );

        Integer monthCharm = Optional.ofNullable(monthPersonalFlow).map(GiveGiftFlowDto::getCharm).orElse(0);
        Integer monthIncome = Optional.ofNullable(monthPersonalFlow).map(GiveGiftFlowDto::getIncome).orElse(0);
        giftFlow.setMonth(new PlayerSumTimeBean().setSumCharm(String.valueOf(monthCharm)).setSumIncome(String.valueOf(monthIncome)));


        //房间收礼流水魅力值
        PlayerSumDataBean roomFlow = new PlayerSumDataBean();
        //今天
        GiveGiftFlowDto dayRoomFlow = iFlowRemote.getRoomGiftflowSum(new RoomGiftflowReq()
                .setRecRoomId(userId)
                .setStartDate(dayStart)
                .setEndDate(dayEnd)
        );
        roomFlow.setDay(new PlayerSumTimeBean()
                .setSumCharm(String.valueOf(Optional.ofNullable(dayRoomFlow).map(GiveGiftFlowDto::getCharm).orElse(0)))
                .setSumIncome(String.valueOf(Optional.ofNullable(dayRoomFlow).map(GiveGiftFlowDto::getIncome).orElse(0)))
        );

        //本周
        GiveGiftFlowDto weekRoomFlow = iFlowRemote.getRoomGiftflowSum(new RoomGiftflowReq()
                .setRecRoomId(userId)
                .setStartDate(weekStart)
                .setEndDate(weekEnd)
        );
        roomFlow.setWeek(new PlayerSumTimeBean()
                .setSumCharm(String.valueOf(Optional.ofNullable(weekRoomFlow).map(GiveGiftFlowDto::getCharm).orElse(0)))
                .setSumIncome(String.valueOf(Optional.ofNullable(weekRoomFlow).map(GiveGiftFlowDto::getIncome).orElse(0)))
        );

        //本月
        GiveGiftFlowDto monthRoomFlow = iFlowRemote.getRoomGiftflowSum(new RoomGiftflowReq()
                .setRecRoomId(userId)
                .setStartDate(monthStart)
                .setEndDate(monthEnd)
        );
        roomFlow.setMonth(new PlayerSumTimeBean()
                .setSumCharm(String.valueOf(Optional.ofNullable(monthRoomFlow).map(GiveGiftFlowDto::getCharm).orElse(0)))
                .setSumIncome(String.valueOf(Optional.ofNullable(monthRoomFlow).map(GiveGiftFlowDto::getIncome).orElse(0)))
        );

        return playerSumResBean.setGiftFlow(giftFlow).setRoomFlow(roomFlow);
    }


    @Override
    public PageBean<PersonalGiftflowBean> playerPersonalGiftflow(PlayerPersonalGiftflowParamBean paramBean) {
        Long sendUserId = userManager.getUserIdByBand(paramBean.getSendUserBand());
        Long recRoomId = userManager.getUserIdByBand(paramBean.getRecRoomBand());

        PageDto<GiveGiftFlowDto> pageDto = iFlowRemote.getPersonalGiftflow(new PersonalGiftflowReq()
                .setPageSize(paramBean.getPageSize())
                .setPageNumber(paramBean.getPageNo())
                .setEndDate(paramBean.getEndDate())
                .setStartDate(paramBean.getStartDate())
                .setSendUserId(sendUserId)
                .setRecUserId(paramBean.getUserId())
                .setRecRoomId(recRoomId)
        );
        List<GiveGiftFlowDto> flowList = pageDto.getList();

        List<Long> giftIds = new ArrayList<>();
        List<Long> requestId = new ArrayList<>();
        List<Long> userIds = new ArrayList<>();
        for (GiveGiftFlowDto flowDto : flowList) {
            giftIds.add(flowDto.getGiftId());
            requestId.add(flowDto.getRequestId());
            userIds.add(flowDto.getRoomId());
            userIds.add(flowDto.getSendUserId());
        }

        //查询礼物名称
        List<GiftDto> giftList = giftManager.getByIds(giftIds);
        Map<Long, String> giftNameMap = giftList.stream().collect(Collectors.toMap(GiftDto::getId, GiftDto::getName));

        //调用支付查询流水内容
        Map<Long, String> flowContentMap = getAccountHistoryRemark(flowList.stream().map(GiveGiftFlowDto::getRequestId).collect(Collectors.toSet()));

        //查询用户信息
        Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(userIds);

        List<PersonalGiftflowBean> beanList = new ArrayList<>(flowList.size());
        for (GiveGiftFlowDto dto : flowList) {
            PersonalGiftflowBean bean = IncomeInfraConvert.I.giveGiftFlowDto2PersonalGiftflowBean(dto);
            bean.setGiftName(giftNameMap.get(dto.getGiftId()));
            bean.setContent(flowContentMap.get(dto.getRequestId()));

            UserBean recRoomInfo = bean.getRecRoomInfo();
            SimpleUserDto roomUserDto = userMap.get(recRoomInfo.getId());
            if (roomUserDto != null) {
                recRoomInfo.setBand(roomUserDto.getBand());
                recRoomInfo.setName(roomUserDto.getName());
            }

            UserBean sendUserInfo = bean.getSendUserInfo();
            SimpleUserDto sendUserDto = userMap.get(sendUserInfo.getId());
            if (sendUserDto != null) {
                sendUserInfo.setBand(sendUserDto.getBand());
                sendUserInfo.setName(sendUserDto.getName());
            }
            beanList.add(bean);
        }

        return PageBean.of(pageDto.getTotal(), beanList);
    }

    @Override
    public PersonalGiftflowBean playerPersonalGiftflowSum(PlayerPersonalGiftflowParamBean paramBean) {
        return Optional.ofNullable(iFlowRemote.getPersonalGiftflowSum(new PersonalGiftflowReq()
                        .setEndDate(paramBean.getEndDate())
                        .setStartDate(paramBean.getStartDate())
                        .setSendUserId(userManager.getUserIdByBand(paramBean.getSendUserBand()))
                        .setRecUserId(paramBean.getUserId())
                        .setRecRoomId(userManager.getUserIdByBand(paramBean.getRecRoomBand()))))
                .map(dto -> {
                    PersonalGiftflowBean bean = new PersonalGiftflowBean();
                    bean.setCharm(dto.getCharm());
                    bean.setIncome(dto.getIncome());
                    return bean;
                }).orElse(null);
    }

    @Override
    public PageBean<PlayerRoomGiftflowBean> playerRoomGiftflow(PlayerRoomGiftflowParamBean paramBean) {
        PageDto<GiveGiftFlowDto> pageDto = iFlowRemote.getRoomGiftflow(new RoomGiftflowReq()
                .setPageSize(paramBean.getPageSize())
                .setPageNumber(paramBean.getPageNo())
                .setEndDate(paramBean.getEndDate())
                .setStartDate(paramBean.getStartDate())
                .setSendUserId(userManager.getUserIdByBand(paramBean.getSendUserBand()))
                .setRecUserId(userManager.getUserIdByBand(paramBean.getRecUserBand()))
                .setRecRoomId(paramBean.getUserId())
        );
        List<GiveGiftFlowDto> flowList = pageDto.getList();

        List<Long> giftIds = new ArrayList<>();
        List<Long> requestId = new ArrayList<>();
        List<Long> userIds = new ArrayList<>();
        for (GiveGiftFlowDto flowDto : flowList) {
            giftIds.add(flowDto.getGiftId());
            requestId.add(flowDto.getRequestId());
            userIds.add(flowDto.getRoomId());
            userIds.add(flowDto.getSendUserId());
            userIds.add(flowDto.getRecUserId());
        }

        //查询礼物名称
        List<GiftDto> giftList = giftManager.getByIds(giftIds);
        Map<Long, String> giftNameMap = giftList.stream().collect(Collectors.toMap(GiftDto::getId, GiftDto::getName));

        //调用支付查询流水内容
        Map<Long, String> flowContentMap = getAccountHistoryRemark(flowList.stream().map(GiveGiftFlowDto::getRequestId).collect(Collectors.toSet()));

        //查询用户信息
        Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(userIds);

        List<PlayerRoomGiftflowBean> beanList = new ArrayList<>(flowList.size());
        for (GiveGiftFlowDto dto : flowList) {
            PlayerRoomGiftflowBean bean = IncomeInfraConvert.I.giveGiftFlowDto2PlayerRoomGiftflowBean(dto);
            bean.setGiftName(giftNameMap.get(dto.getGiftId()));
            bean.setContent(flowContentMap.get(dto.getRequestId()));

            UserBean recRoomInfo = bean.getRecRoomInfo();
            SimpleUserDto roomUserDto = userMap.get(recRoomInfo.getId());
            if (roomUserDto != null) {
                recRoomInfo.setBand(roomUserDto.getBand());
                recRoomInfo.setName(roomUserDto.getName());
            }

            UserBean sendUserInfo = bean.getSendUserInfo();
            SimpleUserDto sendUserDto = userMap.get(sendUserInfo.getId());
            if (sendUserDto != null) {
                sendUserInfo.setBand(sendUserDto.getBand());
                sendUserInfo.setName(sendUserDto.getName());
            }

            UserBean recUserInfo = bean.getRecUserInfo();
            SimpleUserDto recUserDto = userMap.get(recUserInfo.getId());
            if (recUserDto != null) {
                recUserInfo.setBand(recUserDto.getBand());
                recUserInfo.setName(recUserDto.getName());
            }

            beanList.add(bean);
        }

        return PageBean.of(pageDto.getTotal(), beanList);
    }

    @Override
    public PlayerRoomGiftflowBean playerRoomGiftflowSum(PlayerRoomGiftflowParamBean paramBean) {

        return Optional.ofNullable(iFlowRemote.getRoomGiftflowSum(new RoomGiftflowReq()
                        .setEndDate(paramBean.getEndDate())
                        .setStartDate(paramBean.getStartDate())
                        .setSendUserId(userManager.getUserIdByBand(paramBean.getSendUserBand()))
                        .setRecUserId(userManager.getUserIdByBand(paramBean.getRecUserBand()))
                        .setRecRoomId(paramBean.getUserId())))
                .map(dto -> {
                    PlayerRoomGiftflowBean bean = new PlayerRoomGiftflowBean();
                    bean.setIncome(String.valueOf(dto.getIncome()));
                    bean.setCharm(dto.getCharm());
                    return bean;
                }).orElse(null);
    }

    @Override
    public PageBean<fm.lizhi.ocean.wavecenter.api.income.bean.RoomIncomeDetailBean> getRoomIncomeDetail(GetRoomIncomeDetailParamBean paramBean) {

        CreatorDataQueryProto.Page page = CreatorDataQueryProto.Page.newBuilder()
                .setPageNumber(paramBean.getPageNo())
                .setPageSize(paramBean.getPageSize())
                .build();

        CreatorDataQueryProto.QueryHallIncomeDetailRequest.Builder builder = CreatorDataQueryProto.QueryHallIncomeDetailRequest.newBuilder()
                .setFamilyId(paramBean.getFamilyId())
                .setHallId(paramBean.getRoomId())
                .setTenantCode(PayTenantCodeEnum.getPayTenantCode(paramBean.getAppId()))
                .setTimeStart(fm.lizhi.commons.util.DateUtil.formatDateToString(paramBean.getStartDate(), fm.lizhi.commons.util.DateUtil.date_2) + " 00:00:00")
                .setTimeEnd(fm.lizhi.commons.util.DateUtil.formatDateToString(paramBean.getEndDate(), fm.lizhi.commons.util.DateUtil.date_2) + " 23:59:59")
                .setDirection(DirectionEnum.ALL.getCode())
                .setPage(page);

        if (CollUtil.isNotEmpty(paramBean.getIncomeType())) {
            builder.addAllBizId(paramBean.getIncomeType().stream().map(IncomeType::getValue).map(String::valueOf).collect(Collectors.toSet()));
        }

        Result<CreatorDataQueryProto.ResponseQueryHallIncomeDetail> result = creatorDataQueryService.queryHallIncomeDetail(builder.build(),
                fm.lizhi.commons.util.DateUtil.formatDateToString(new Date(paramBean.getFlushTime()), fm.lizhi.commons.util.DateUtil.NORMAL_DATE_FORMAT));
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            return PageBean.empty();
        }
        CreatorDataQueryProto.ResponseQueryHallIncomeDetail hallIncomeDetail = result.target();
        List<CreatorDataQueryProto.QueryHallIncomeDetailResponse> dataList = hallIncomeDetail.getDataList();
        List<fm.lizhi.ocean.wavecenter.api.income.bean.RoomIncomeDetailBean> detailBeans = dataList.stream().map(e -> fm.lizhi.ocean.wavecenter.api.income.bean.RoomIncomeDetailBean.builder()
                        .content(e.getRemark())
                        .date(fm.lizhi.commons.util.DateUtil.formatStrToDate(e.getHappenedTime(), fm.lizhi.commons.util.DateUtil.NORMAL_DATE_FORMAT))
                        .income(e.getAmount())
                        .incomeType(e.getBizId())
                        .build())
                .collect(Collectors.toList());
        return PageBean.of(hallIncomeDetail.getTotalCount(), detailBeans);

    }

    @Override
    public RoomIncomeDetailSumBean getRoomIncomeDetailSum(GetRoomIncomeDetailSumParamBean paramBean) {

        CreatorDataQueryProto.QueryHallIncomeDetailRequest.Builder builder = CreatorDataQueryProto.QueryHallIncomeDetailRequest.newBuilder()
                .setTenantCode(PayTenantCodeEnum.getPayTenantCode(paramBean.getAppId()))
                .setFamilyId(paramBean.getFamilyId())
                .setHallId(paramBean.getRoomId())
                .setTimeStart(fm.lizhi.commons.util.DateUtil.formatDateToString(paramBean.getStartDate(), fm.lizhi.commons.util.DateUtil.date_2) + " 00:00:00")
                .setTimeEnd(fm.lizhi.commons.util.DateUtil.formatDateToString(paramBean.getEndDate(), fm.lizhi.commons.util.DateUtil.date_2) + " 23:59:59")
                .setDirection(DirectionEnum.ALL.getCode())
                // 分页参数必传，汇总值不影响.
                .setPage(CreatorDataQueryProto.Page.newBuilder()
                        .setPageNumber(1)
                        .setPageSize(20)
                        .build());

        if (CollUtil.isNotEmpty(paramBean.getIncomeType())) {
            builder.addAllBizId(paramBean.getIncomeType().stream().map(IncomeType::getValue).map(String::valueOf).collect(Collectors.toSet()));
        }

        Result<CreatorDataQueryProto.ResponseQueryHallIncomeDetailTotal> result = creatorDataQueryService.queryHallIncomeDetailTotal(builder.build(),
                fm.lizhi.commons.util.DateUtil.formatDateToString(new Date(), fm.lizhi.commons.util.DateUtil.NORMAL_DATE_FORMAT));

        if (RpcResult.isFail(result)) {
            return new RoomIncomeDetailSumBean().setIncome("0");
        }
        return new RoomIncomeDetailSumBean()
                .setIncome(result.target().getStatisticsValue());

    }

    @Override
    public Map<Long, String> getAccountHistoryRemark(Set<Long> flowIds) {
        return iFlowRemote.getFlowRemark(flowIds);
    }

    @Override
    public Map<Long, String> getAccountHistoryRemark(List<Long> flowIds) {
        return getAccountHistoryRemark(new HashSet<>(flowIds));
    }


    @Override
    public PageBean<PersonalIncomeDetailBean> getRevenueIncomeDetail(GetPersonalRevenueIncomeDetailParamBean paramBean) {
        return personalIncomeRemote.getRevenueIncomeDetail(paramBean);
    }


    @Override
    public PersonalIncomeDetailSumBean getRevenueIncomeDetailSum(GetPersonalRevenueIncomeDetailParamBean paramBean) {
        return personalIncomeRemote.getRevenueIncomeDetailSum(paramBean);
    }

    @Override
    public Integer getFamilyWeekIncome(Long familyId, Date monDay, Date sunDay) {
        //周一至周六查询数据中心
        DateTime saturday = DateUtil.offsetDay(sunDay, -1);
        Integer partOne = guildDataManager.getFamilyDayIncomeSum(familyId, monDay, saturday);
        log.info("familyId={},monday={},saturday={},partOne={}", familyId, monDay, saturday, partOne);

        //周日查询支付
        DateTime begin = DateUtil.beginOfDay(sunDay);
        DateTime end = DateUtil.endOfDay(sunDay);
        long partTwo = paymentManager.queryFamilyIncomeByDateScope(familyId, begin, end, PaySettleConfigCodeEnum.FAMILY_INCOME_TOTAL_AMOUNT);

        return Math.toIntExact(partTwo) + partOne;
    }

    @Override
    public PageBean<GuildIncomeStatDTO> queryGuildIncomeStats(GuildIncomeStatParamDTO param) {
        return guildDataManager.queryGuildIncomeStats(param);
    }
}
