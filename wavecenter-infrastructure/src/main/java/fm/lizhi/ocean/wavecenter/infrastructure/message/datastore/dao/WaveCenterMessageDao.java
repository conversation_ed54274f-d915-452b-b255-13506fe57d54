package fm.lizhi.ocean.wavecenter.infrastructure.message.datastore.dao;

import cn.hutool.core.collection.CollUtil;
import fm.lizhi.ocean.wavecenter.api.message.bean.MessageBean;
import fm.lizhi.ocean.wavecenter.api.message.request.RequestGetMessageList;
import fm.lizhi.ocean.wavecenter.datastore.platform.message.entity.*;
import fm.lizhi.ocean.wavecenter.infrastructure.message.convert.MessageConvert;
import fm.lizhi.ocean.wavecenter.datastore.platform.message.mapper.WcMessageContentMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.message.datastore.mapper.WcMessageExtMapper;
import fm.lizhi.ocean.wavecenter.datastore.platform.message.mapper.WcMessageMapper;
import fm.lizhi.ocean.wavecenter.datastore.platform.message.mapper.WcMessageReadRecordMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.message.param.BatchSaveMessageParam;
import fm.lizhi.ocean.wavecenter.infrastructure.message.param.SaveMessageParam;
import fm.lizhi.ocean.wavecenter.infrastructure.message.param.SaveMessageToRoleParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class WaveCenterMessageDao {

    @Autowired
    private WcMessageMapper messageMapper;

    @Autowired
    private WcMessageContentMapper messageContentMapper;

    @Autowired
    private WcMessageReadRecordMapper messageReadRecordMapper;

    @Autowired
    private WcMessageExtMapper messageExtMapper;


    @Transactional
    public WcMessage saveMessage(SaveMessageParam param) {
        WcMessage message = MessageConvert.I.buildWcMessage(param);
        int success = messageMapper.insert(message);
        if (success <= 0) {
            log.warn("save message failed, content:{}, sendUserId: {}, targetUserId: {}", param.getContent(), param.getSendUserId(), param.getTargetUserId());
            return null;
        }

        WcMessageContent content = new WcMessageContent();
        content.setMessageId(message.getId());
        content.setContent(param.getContent());
        messageContentMapper.insert(content);

        return message;
    }

    @Transactional
    public List<WcMessage> saveMessageByRole(SaveMessageToRoleParam param) {

        if (CollUtil.isEmpty(param.getRoleCodes())) {
            log.warn("saveMessageByRole roles is empty. content:{}", param.getRoleCodes());
            return Collections.emptyList();
        }

        return param.getRoleCodes().stream()
                .map(roleCode -> saveMessage(MessageConvert.I.convertSaveMessageParam(param, roleCode)))
                .collect(Collectors.toList());

    }

    @Transactional
    public List<WcMessage> batchSaveMessages(BatchSaveMessageParam param) {
        if (CollUtil.isEmpty(param.getTargetUserIds())) {
            log.warn("batchSaveMessages targetUserIds is empty. content:{}", param.getContent());
            return Collections.emptyList();
        }

        List<WcMessage> messages = param.getTargetUserIds().
                stream()
                .map(uid -> MessageConvert.I.buildWcMessage(param, uid))
                .collect(Collectors.toList());

        boolean success = messageMapper.batchInsert(messages) == messages.size();
        if (!success) {
            log.warn("batch save message failed, content:{}", param.getContent());
            return Collections.emptyList();
        }

        List<WcMessageContent> messageContents = messages.stream().map(message -> {
            WcMessageContent content = new WcMessageContent();
            content.setMessageId(message.getId());
            content.setContent(param.getContent());
            content.setCreateTime(new Date());
            content.setUpdateTime(new Date());
            return content;
        }).collect(Collectors.toList());

        messageContentMapper.batchInsert(messageContents);
        return messages;
    }

    public List<MessageBean> getMessageList(RequestGetMessageList param) {

        List<WcMessage> messageList = messageExtMapper.selectMessage(
                param.getType(), param.getAppId(), param.getPerformanceId(), param.getRoleCode(), param.getUserId(), param.getSize()
        );

        List<Long> messageIds = messageList.stream().map(WcMessage::getId).collect(Collectors.toList());
        if (CollUtil.isEmpty(messageIds)) {
            return Collections.emptyList();
        }

        // 获取消息内容
        WcMessageContentExample example = new WcMessageContentExample();
        example.createCriteria().andMessageIdIn(messageIds);
        List<WcMessageContent> contentList = messageContentMapper.selectByExample(example);
        Map<Long, String> contentIdMap = contentList.stream().collect(Collectors.toMap(WcMessageContent::getMessageId, WcMessageContent::getContent, (a, b) -> a));


        // 获取已读
        WcMessageReadRecordExample readRecordExample = new WcMessageReadRecordExample();
        readRecordExample.createCriteria().andMessageIdIn(messageIds).andUserIdEqualTo(param.getUserId());
        List<WcMessageReadRecord> readRecordList = messageReadRecordMapper.selectByExample(readRecordExample);

        Map<Long, WcMessageReadRecord> readRecordMap = readRecordList.stream()
                .collect(Collectors.toMap(WcMessageReadRecord::getMessageId, v -> v, (a, b) -> a));

        return messageList.stream()
                .map(message -> MessageConvert.I.convertMessageBean(message,
                        contentIdMap.get(message.getId()),
                        readRecordMap.containsKey(message.getId())
                ))
                .collect(Collectors.toList());
    }


    public Long getUnReadCount(RequestGetMessageList param, Long showNoticeTimeStamp) {

        return messageExtMapper.countMessageByNotRead(
                param.getType(), param.getAppId(),showNoticeTimeStamp, param.getRoleCode(), param.getUserId()
        );
    }

    /**
     * 一键已读
     */
    @Transactional
    public void batchReadAll(Integer type, Long userId, String roleCode, int appId) {

        List<Long> unReadIds = messageExtMapper.getMessageIdByNotRead(type, appId, roleCode, userId);
        if (CollUtil.isEmpty(unReadIds)) {
            return;
        }

        List<WcMessageReadRecord> readRecordList = unReadIds.stream().map(id -> WcMessageReadRecord.builder()
                        .messageId(id)
                        .userId(userId)
                        .createTime(new Date())
                        .updateTime(new Date())
                        .build())
                .collect(Collectors.toList());

        messageReadRecordMapper.batchInsert(readRecordList);
    }

    @Transactional
    public void batchRead(Long userId, List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }

        WcMessageExample example = new WcMessageExample();
        example.createCriteria().andIdIn(ids)
                .andDeletedEqualTo(false);

        List<WcMessage> messageList = messageMapper.selectByExample(example);
        if (CollUtil.isEmpty(messageList)) {
            return;
        }
        List<Long> messageIds = messageList.stream().map(WcMessage::getId).collect(Collectors.toList());

        WcMessageReadRecordExample recordExample = new WcMessageReadRecordExample();
        recordExample.createCriteria()
                .andUserIdEqualTo(userId)
                .andMessageIdIn(messageIds);

        List<WcMessageReadRecord> readRecordList = messageReadRecordMapper.selectByExample(recordExample);

        // 去除已读的消息
        messageIds.removeAll(readRecordList.stream().map(WcMessageReadRecord::getMessageId).collect(Collectors.toList()));
        if (CollUtil.isEmpty(messageIds)) {
            return;
        }

        readRecordList = messageIds.stream().map(id -> WcMessageReadRecord.builder()
                        .messageId(id)
                        .userId(userId)
                        .createTime(new Date())
                        .updateTime(new Date())
                        .build())
                .collect(Collectors.toList());

        messageReadRecordMapper.batchInsert(readRecordList);

    }

    /**
     * 批量保存已读记录
     *
     * @param userId     用户ID
     * @param messageIds 消息ID列表
     * @return 是否保存成功
     */
    public void batchSaveReadRecord(Long userId, List<Long> messageIds) {
        if (CollUtil.isEmpty(messageIds)) {
            return;
        }

        //查出已读的消息
        WcMessageReadRecordExample example = new WcMessageReadRecordExample();
        example.createCriteria().andUserIdEqualTo(userId).andMessageIdIn(messageIds);
        List<WcMessageReadRecord> readRecordList = messageReadRecordMapper.selectByExample(example);
        //过滤掉已读的消息id
        if (CollUtil.isNotEmpty(readRecordList)) {
            messageIds.removeAll(readRecordList.stream().map(WcMessageReadRecord::getMessageId).collect(Collectors.toList()));
            if (CollUtil.isEmpty(messageIds)) {
                return;
            }
        }

        List<WcMessageReadRecord> insertList = messageIds.stream().map(id -> WcMessageReadRecord.builder()
                        .messageId(id)
                        .userId(userId)
                        .createTime(new Date())
                        .updateTime(new Date())
                        .build())
                .collect(Collectors.toList());

        messageReadRecordMapper.batchInsert(insertList);
    }

    /**
     * 查询用户最近消息
     *
     * @param userId 用户ID
     * @param size   条数
     * @return 消息列表
     */
    public List<WcMessage> queryRecentMessages(int appId, Long userId, int size, String roleCode) {
        WcMessageExample example = new WcMessageExample();
        example.setOrderByClause("create_time desc");
        WcMessageExample.Criteria criteria = example.createCriteria();
        criteria.andTargetUserIdEqualTo(userId)
                .andAppIdEqualTo(appId)
                .andVisibleRoleCodeEqualTo(roleCode)
                .andDeletedEqualTo(false);
        return messageMapper.pageByExample(example, 1, size);
    }
}
