package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityTemplateInfo;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityTemplateBaseInfoDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityTemplateInfoDTO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface ActivityTemplateConvert {

    ActivityTemplateConvert I = Mappers.getMapper(ActivityTemplateConvert.class);

    ActivityTemplateInfoDTO bean2Dto(ActivityTemplateInfo activityTemplateInfo, Long activityId);

    ActivityTemplateBaseInfoDTO entity2BaseDTO(ActivityTemplateInfo entity);
}
