package fm.lizhi.ocean.wavecenter.infrastructure.income.remote.pp;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.GetRoomSignPlayerIncomeParamBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.RoomSignPlayerIncomeBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.income.convert.IncomeInfraConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.income.manager.PaymentManager;
import fm.lizhi.ocean.wavecenter.infrastructure.income.remote.IRoomIncomeRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.IContractRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.res.PlayerIncomeStatRes;
import fm.lizhi.ocean.wavecenter.service.user.dto.PlayerSignInfoDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/5/27 17:54
 */
@Slf4j
@Component
public class PpRoomIncomeRemote implements IRoomIncomeRemote {

    @Autowired
    private IContractRemote iContractRemote;
    @Autowired
    private PaymentManager paymentManager;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public PageBean<RoomSignPlayerIncomeBean> getRoomSignPlayerIncome(GetRoomSignPlayerIncomeParamBean paramBean) {
        Long roomId = paramBean.getUserId();
        List<PlayerIncomeStatRes> incomeList = iContractRemote.queryAdminPlayerIncomeList(roomId, paramBean.getStartDate(), paramBean.getEndDate());
        if (CollectionUtils.isEmpty(incomeList)) {
            return PageBean.empty();
        }

        //数据量不多
        for (PlayerIncomeStatRes t : incomeList) {
            Optional<PlayerSignInfoDto> signInfoOp = iContractRemote.getLatestSignRecord(roomId, t.getId());
            if (signInfoOp.isPresent()) {
                Date start = paramBean.getStartDate();
                Date end = paramBean.getEndDate();
                PlayerSignInfoDto info = signInfoOp.get();
                Date signStartTime = info.getStartTime();
                Date signEndTime = info.getEndTime();
                Date queryStartTime = signStartTime.getTime() >= start.getTime() ? signStartTime : start;
                Date queryEndTime = end;
                if (null != signEndTime) {
                    queryEndTime = signEndTime.getTime() <= end.getTime() ? signEndTime : end;
                }
                Long total = paymentManager.queryAssetSumInRange(t.getId(), queryStartTime.getTime(),
                        queryEndTime.getTime(), 2, "");
                t.setPlayerPersonalLiveIncome(String.valueOf(null == total ? 0 : total));
            } else {
                t.setPlayerPersonalLiveIncome("0");
            }
        }

        return PageBean.of(incomeList.size(), IncomeInfraConvert.I.playerIncomeStatRess2Beans(incomeList));
    }
}
