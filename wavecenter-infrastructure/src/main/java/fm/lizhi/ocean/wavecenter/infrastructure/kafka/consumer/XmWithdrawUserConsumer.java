package fm.lizhi.ocean.wavecenter.infrastructure.kafka.consumer;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import fm.lizhi.common.kafka.common.strategy.AutoOffsetReset;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaHandler;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaListener;
import fm.lizhi.common.kafka.ioc.api.strategy.annotation.AutoOffsetResetStrategy;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.common.utils.KafkaMsgUtils;
import fm.lizhi.ocean.wavecenter.service.user.manager.LoginManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@KafkaListener(clusterNamespace = "xm-kafka250-bootstrap-server")
public class XmWithdrawUserConsumer {

    @Autowired
    private LoginManager loginManager;

    @KafkaHandler(topic = "xm_topic_user_apply_with_draw", group = "xm_topic_withdraw_user_wavecenter_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleWithdrawUser(String body) {
        String msg = null;
        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.XIMI);
            log.info("xm.handleWithdrawUser msg:{}", msg);
            JSONObject object = JSON.parseObject(msg);
            if (object == null) {
                return;
            }

            long userId = object.getLongValue("userId");
            if (userId > 0) {
                //调用退登接口
                loginManager.deleteAllLoginInfo(BusinessEvnEnum.XIMI.getAppId(), userId);
            }
        } catch (Exception e) {
            log.error("xm.handleWithdrawUser", e);
        }
    }


}
