package fm.lizhi.ocean.wavecenter.infrastructure.income.remote.hy;

import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.GetRoomSignPlayerIncomeParamBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.RoomSignPlayerIncomeBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PaySettleConfigCodeEnum;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.PlayerSignCharmSumPo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.HyPlayerSignCharmStatMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.income.manager.PaymentManager;
import fm.lizhi.ocean.wavecenter.infrastructure.income.remote.IRoomIncomeRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.IContractRemote;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/27 17:53
 */
@Slf4j
@Component
public class HyRoomIncomeRemote implements IRoomIncomeRemote {

    @Autowired
    private IContractRemote iContractRemote;
    @Autowired
    private HyPlayerSignCharmStatMapper playerSignCharmStatMapper;
    @Autowired
    private FamilyManager familyManager;
    @Autowired
    private PaymentManager paymentManager;
    @Autowired
    private UserManager userManager;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.HEI_YE;
    }

    @Override
    public PageBean<RoomSignPlayerIncomeBean> getRoomSignPlayerIncome(GetRoomSignPlayerIncomeParamBean paramBean) {
        Long roomId = paramBean.getUserId();
        Date startDate = paramBean.getStartDate();
        Date endDate = paramBean.getEndDate();

        UserInFamilyBean userInFamily = familyManager.getUserInFamily(roomId);
        Long familyId = userInFamily.getFamilyId();
        if (familyId == null) {
            log.info("familyId is null");
            return PageBean.empty();
        }

        //查询厅所有签约主播
        List<Long> playerIds = iContractRemote.getAllSignRoomPlayerIds(roomId);
        if (CollectionUtils.isEmpty(playerIds)) {
            log.info("hy playerIds is empty");
            return PageBean.empty();
        }

        String startDateStr = DateUtil.formatDateToString(startDate, DateUtil.date_2);
        String endDateStr = DateUtil.formatDateToString(endDate, DateUtil.date_2);
        List<PlayerSignCharmSumPo> charmList = playerSignCharmStatMapper.selectPlayerSignCharmSumByUsers(roomId, playerIds, startDateStr, endDateStr);
        Map<Long, Integer> charmMap;
        try {
            charmMap = charmList.stream().collect(Collectors.toMap(PlayerSignCharmSumPo::getUserId, PlayerSignCharmSumPo::getTotalValue, (k1, k2) -> k2));
        } catch (Exception e) {
            log.warn("xm.get charmMap error,roomId={}", roomId, e);
            charmMap = new HashMap<>();
        }

        Map<Long, Long> roomMap = paymentManager.batchGetIncomeByPlayer(familyId, roomId, playerIds, startDate, endDate, PaySettleConfigCodeEnum.ANCHOR_HALL_INCOME_TOTAL_AMOUNT);
        Map<Long, Long> playerMap = paymentManager.batchGetIncomeByPlayer(familyId, roomId, playerIds, startDate, endDate, PaySettleConfigCodeEnum.PERSONAL_ANCHOR_INDIVIDUAL_INCOME_TOTAL_AMOUNT);
        Map<Long, Long> officialMap = paymentManager.batchGetIncomeByPlayer(familyId, roomId, playerIds, startDate, endDate, PaySettleConfigCodeEnum.ANCHOR_OFFICIAL_HALL_INCOME_TOTAL_AMOUNT);

        Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(playerIds);

        List<RoomSignPlayerIncomeBean> result = new ArrayList<>();
        for (Long playerId : playerIds) {
            SimpleUserDto userDto = userMap.get(playerId);
            if (userDto == null) {
                continue;
            }

            RoomSignPlayerIncomeBean bean = new RoomSignPlayerIncomeBean();
            UserBean player = new UserBean();
            player.setId(userDto.getId());
            player.setBand(userDto.getBand());
            player.setName(userDto.getName());
            bean.setPlayerInfo(player);
            bean.setCharm(charmMap.getOrDefault(playerId, 0));

            Long playerIncome = playerMap.getOrDefault(playerId, 0L);
            Long roomIncome = roomMap.getOrDefault(playerId, 0L);
            Long officialIncome = officialMap.getOrDefault(playerId, 0L);

            bean.setPersonalHallIncome(String.valueOf(playerIncome));
            bean.setSignHallIncome(String.valueOf(roomIncome));
            bean.setOfficialHallIncome(String.valueOf(officialIncome));
            bean.setCheckIncome(String.valueOf(playerIncome + roomIncome + officialIncome));
            result.add(bean);
        }
        return PageBean.of(result.size(), result);
    }
}
