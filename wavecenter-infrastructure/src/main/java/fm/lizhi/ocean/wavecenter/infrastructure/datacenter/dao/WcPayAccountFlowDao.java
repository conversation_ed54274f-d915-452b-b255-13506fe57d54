package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.dao;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.convert.PayAccountFlowConvert;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcPayAccountFlow;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.mapper.WcPayAccountFlowMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.ext.WcPayAccountFlowExtMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.kafka.consumer.AccountFlowMsg;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/4/24 11:55
 */
@Repository
public class WcPayAccountFlowDao {

    @Autowired
    private WcPayAccountFlowMapper payAccountFlowMapper;
    @Autowired
    private WcPayAccountFlowExtMapper payAccountFlowExtMapper;

    /**
     * 保存支付流水消息
     * @param msg
     */
    public void saveAccountFlowMsg(AccountFlowMsg msg){
        WcPayAccountFlow entity = PayAccountFlowConvert.I.msgToEntity(msg);
        entity.setDeployEnv(ConfigUtils.getEnvRequired().name());
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        entity.setDeleted(0);
        entity.setConsumerTimes(0);

        payAccountFlowMapper.insert(entity);
    }

    /**
     * 更新消费信息
     * @param flowId
     */
    public void updateAccountFlowMsgConsumer(Long flowId){
        payAccountFlowExtMapper.updateConsumer(flowId);
    }

}
