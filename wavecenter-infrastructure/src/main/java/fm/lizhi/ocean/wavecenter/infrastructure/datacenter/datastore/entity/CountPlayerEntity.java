package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/12 20:12
 */
@Data
@Accessors(chain = true)
public class CountPlayerEntity {

    private Integer appId;

    private Long familyId;

    private List<Long> roomIds;

    /**
     * 主播上麦时长最小值
     */
    private Integer upGuestDurMin;

    /**
     * 主播收入魅力值最小值
     */
    private Integer charmMin;

}
