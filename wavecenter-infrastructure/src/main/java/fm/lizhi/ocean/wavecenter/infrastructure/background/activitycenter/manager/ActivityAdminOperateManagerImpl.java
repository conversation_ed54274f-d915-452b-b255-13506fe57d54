package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityFlowResourceAuditBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.OfficialSeatExtraBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.AutoConfigResourceEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ResourceExtraMapping;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.dto.DateDTO;
import fm.lizhi.ocean.wavecenter.common.utils.DateTimeUtils;
import fm.lizhi.ocean.wavecenter.common.utils.RedisLock;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.convert.ActivityApplyConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.dao.ActivityApplyDao;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityApplyFlowResource;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityApplyFlowResourceExample;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityOfficialSeatTime;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.mapper.ActivityApplyFlowResourceMapper;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ResourceGiveResultDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityMaterielGiveManager;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityRedisManager;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.manager.ActivityResourceGiveManagerImpl;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityAdminOperateManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityResourceManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityResourceSimpleInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ActivityAdminOperateManagerImpl implements ActivityAdminOperateManager {

    @Autowired
    private ActivityRedisManager activityRedisManager;

    @Autowired
    private ActivityApplyDao applyDao;

    @Autowired
    private ActivityMaterielGiveManager activityMaterielGiveManager;

    @Autowired
    private ActivityResourceGiveManagerImpl activityResourceGiveManager;

    @Autowired
    private ActivityApplyFlowResourceMapper activityApplyFlowResourceMapper;

    @Autowired
    private ActivityResourceManager activityResourceManager;


    @Override
    public boolean rejectActivityApply(Long activityId, String reason, String operator, Integer version) {
        try {
            reason = reason == null ? "" : reason;
            ActivityApplyFlowResourceExample example = new ActivityApplyFlowResourceExample();
            example.createCriteria().andActivityIdEqualTo(activityId);
            List<ActivityApplyFlowResource> flowResources = activityApplyFlowResourceMapper.selectByExample(example);
            List<ActivityOfficialSeatTime> seatTimeList = buildSeatTimeList(flowResources);
            applyDao.rejectActivityApply(activityId, seatTimeList, reason, operator, version);
            return true;
        } catch (Exception e) {
            log.warn("rejectActivityApply happen error:activityId={}", activityId, e);
            return false;
        }
    }

    @Override
    public Result<String> agreeActivityApply(Integer appId, Long activityId, String operator, List<ActivityFlowResourceAuditBean> resourceAuditList, Integer version) {
        // 获取不到锁直接返回,这里跟修改和审批通过后运营修改加的是同一把锁
        try (RedisLock lock = activityRedisManager.getModifyLock(appId, activityId)) {
            if (!lock.tryLock()) {
                return RpcResult.fail(AGREE_ACTIVITY_APPLY_REPEAT, "请刷新页面重新操作");
            }

            // 初始化资源（这一步会简单判断数据是否已存在，没有做严格的并发控制）
            Result<Void> initResult = activityMaterielGiveManager.initActivityMateriel(activityId, resourceAuditList);
            if (RpcResult.isFail(initResult)) {
                log.error("agreeActivityApply initActivityMateriel failed. activityId:{}", activityId);
                return RpcResult.fail(initResult.rCode(), initResult.getMessage());
            }

            // 更新活动审批状态，严格更新，更新数据不一致直接回滚，强制人工检查
            List<ActivityApplyFlowResource> activityApplyFlowResources = ActivityApplyConvert.I
                    .convertFlowResourceAuditsBean2Bean(resourceAuditList);
            applyDao.agreeActivityApply(activityId, operator, activityApplyFlowResources, version);

            // 资源发放，不影响主流程，可失败
            Result<List<ResourceGiveResultDTO>> resourceGiveResult = activityResourceGiveManager
                    .realTimeGiveResource(appId, activityId);
            if (RpcResult.isFail(resourceGiveResult)) {
                log.warn("agreeActivityApply realTimeGiveResource failed. activityId:{},appId:{}", activityId, appId);
                return RpcResult.fail(resourceGiveResult.rCode(), resourceGiveResult.getMessage());
            }
            // 如果整体是成功的，则判断列表里面是否有error信息，有则返回失败
            String sendMessage = resourceGiveResult.target().stream().filter(dto -> dto.getErrorList() != null)
                    .flatMap(dto -> dto.getErrorList().stream())
                    .map(dto -> dto.getName() + "发放错误，错误原因:" + dto.getErrorMessage())
                    .collect(Collectors.joining(", "));
            if (StringUtils.isNotBlank(sendMessage)) {
                return RpcResult.success(sendMessage);
            }
        }

        return RpcResult.success();
    }

    private List<ActivityOfficialSeatTime> buildSeatTimeList(List<ActivityApplyFlowResource> flowResources) {
        if (CollectionUtils.isEmpty(flowResources)) {
            return Collections.emptyList();
        }

        List<Long> ids = flowResources.stream().map(ActivityApplyFlowResource::getResourceConfigId)
                .collect(Collectors.toList());
        List<ActivityResourceSimpleInfoDTO> resources = activityResourceManager.batchResourceByIds(ids);

        Optional<ActivityResourceSimpleInfoDTO> result = resources.stream().filter(resource -> Objects
                        .equals(resource.getResourceCode(), AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode()))
                .findFirst();
        if (!result.isPresent()) {
            return Collections.emptyList();
        }

        ActivityResourceSimpleInfoDTO infoDTO = result.get();
        for (ActivityApplyFlowResource flowResource : flowResources) {
            if (!flowResource.getResourceConfigId().equals(infoDTO.getId())) {
                continue;
            }
            OfficialSeatExtraBean bean = ResourceExtraMapping.convertJsonToExtra(flowResource.getExtra(),
                    AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode());
            if (bean == null) {
                continue;
            }
            List<DateDTO> timeSlots = DateTimeUtils.divideTimeSlots(bean.getStartTime(), bean.getEndTime());
            List<ActivityOfficialSeatTime> list = new ArrayList<>();
            for (DateDTO timeSlot : timeSlots) {
                ActivityOfficialSeatTime seatTime = ActivityOfficialSeatTime.builder()
                        .appId(infoDTO.getAppId())
                        .seat(bean.getSeat())
                        .startTime(timeSlot.getStartTime())
                        .endTime(timeSlot.getEndTime())
                        .build();
                list.add(seatTime);
            }
            return list;
        }
        return Collections.emptyList();
    }
}
