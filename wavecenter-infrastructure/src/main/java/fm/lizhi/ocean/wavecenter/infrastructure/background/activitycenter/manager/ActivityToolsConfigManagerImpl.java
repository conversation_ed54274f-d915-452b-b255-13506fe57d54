package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.manager;

import cn.hutool.core.collection.CollUtil;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityToolsInfoBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityToolStatusEnum;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityTools;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityTools;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityToolsConfigService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert.ActivityToolsInfoConvert;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityToolsInfo;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityToolsInfoExample;
import fm.lizhi.ocean.wavecenter.datastore.background.mapper.ActivityToolsInfoMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.mapper.ext.ActivityToolsInfoExtMapper;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityToolsConfigManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ActivityToolsConfigManagerImpl implements ActivityToolsConfigManager {

    @Autowired
    private ActivityToolsInfoExtMapper activityToolsInfoExtMapper;

    @Autowired
    private ActivityToolsInfoMapper activityToolsInfoMapper;

    @Override
    public Result<Boolean> saveTools(RequestSaveActivityTools param) {

        // 先查出当前业务的最新toolValue
        int toolValue = activityToolsInfoExtMapper.getToolValueByAppId(param.getAppId(), ConfigUtils.getEnvRequired().name());

        ActivityToolsInfo toolsInfo = ActivityToolsInfoConvert.I.buildActivityToolsInfo(param, toolValue);
        return activityToolsInfoMapper.insert(toolsInfo) > 0
                ? RpcResult.success(true)
                : RpcResult.fail(ActivityToolsConfigService.SAVE_TOOLS_FAIL, "保存活动工具失败");
    }

    @Override
    public Result<Boolean> updateTools(RequestUpdateActivityTools param) {
        ActivityToolsInfo toolsInfo = activityToolsInfoMapper.selectByPrimaryKey(ActivityToolsInfo.builder().id(param.getId()).build());
        if (null == toolsInfo) {
            return RpcResult.fail(ActivityToolsConfigService.UPDATE_TOOLS_FAIL, "记录不存在");
        }

        toolsInfo = ActivityToolsInfoConvert.I.convertUpdateActivityToolsInfo(param);
        return activityToolsInfoMapper.updateByPrimaryKey(toolsInfo) > 0
               ? RpcResult.success(true)
               : RpcResult.fail(ActivityToolsConfigService.UPDATE_TOOLS_FAIL, "更新活动工具失败");
    }

    @Override
    public Result<List<ActivityToolsInfoBean>> listByAppId(Integer appId, ActivityToolStatusEnum status) {

        ActivityToolsInfoExample example = new ActivityToolsInfoExample();
        ActivityToolsInfoExample.Criteria criteria = example.createCriteria()
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andAppIdEqualTo(appId);

        if (status != null) {
            criteria.andStatusEqualTo(status.getStatus());
        }

        List<ActivityToolsInfo> list = activityToolsInfoMapper.selectByExample(example);

        return RpcResult.success(ActivityToolsInfoConvert.I.convertActivityToolsInfoBeanList(list));
    }


    @Override
    public boolean isActivityToolsIllegal(int appId, List<Integer> activityTools) {
        if (CollectionUtils.isEmpty(activityTools)) {
            return false;
        }
        Result<List<ActivityToolsInfoBean>> result = listByAppId(appId, ActivityToolStatusEnum.ENABLE);
        if (RpcResult.isFail(result) || CollUtil.isEmpty(result.target())) {
            return false;
        }
        List<Integer> appIdActivityTools = result.target().stream().map(ActivityToolsInfoBean::getToolValue).collect(Collectors.toList());
        return !CollectionUtils.containsAll(appIdActivityTools, activityTools);
    }
}
