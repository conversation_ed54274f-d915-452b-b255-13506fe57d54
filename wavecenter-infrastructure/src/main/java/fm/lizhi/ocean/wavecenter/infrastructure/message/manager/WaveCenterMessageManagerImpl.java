package fm.lizhi.ocean.wavecenter.infrastructure.message.manager;

import cn.hutool.core.collection.CollUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.message.bean.MessageBean;
import fm.lizhi.ocean.wavecenter.api.message.constant.WcNoticeConfigEnum;
import fm.lizhi.ocean.wavecenter.api.message.request.*;
import fm.lizhi.ocean.wavecenter.api.message.response.ResponseGetMessageList;
import fm.lizhi.ocean.wavecenter.api.message.response.ResponseQueryRecentMessages;
import fm.lizhi.ocean.wavecenter.infrastructure.message.constants.PushConstants;
import fm.lizhi.ocean.wavecenter.infrastructure.message.constants.PushTopicEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.message.convert.MessageConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.message.datastore.dao.WaveCenterMessageDao;
import fm.lizhi.ocean.wavecenter.datastore.platform.message.entity.WcMessage;
import fm.lizhi.ocean.wavecenter.datastore.platform.message.entity.WcNoticeConfig;
import fm.lizhi.ocean.wavecenter.infrastructure.message.param.BatchSaveMessageParam;
import fm.lizhi.ocean.wavecenter.infrastructure.message.dto.MessagePushDTO;
import fm.lizhi.ocean.wavecenter.infrastructure.message.dto.PushDTO;
import fm.lizhi.ocean.wavecenter.infrastructure.message.param.SaveMessageToRoleParam;
import fm.lizhi.ocean.wavecenter.service.message.manager.WaveCenterMessageManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import fm.lizhi.ocean.wavecenter.infrastructure.message.datastore.dao.WcNoticeConfigDao;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static fm.lizhi.ocean.wavecenter.api.message.service.WaveCenterMessageService.SEND_MESSAGE_BATCH_TARGET_UID_EMPTY;
import static fm.lizhi.ocean.wavecenter.api.message.service.WaveCenterMessageService.SEND_MESSAGE_ROLE_TARGET_ROLE_EMPTY;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class WaveCenterMessageManagerImpl implements WaveCenterMessageManager {

    @Autowired
    private CommonPushManager commonPushManager;

    @Autowired
    private WaveCenterMessageDao waveCenterMessageDao;

    @Autowired
    private WcNoticeConfigDao wcNoticeConfigDao;

    @Override
    public Long sendMessage(RequestSendMessage param) {
        WcMessage message = waveCenterMessageDao.saveMessage(MessageConvert.I.convertSaveMessageParam(param));

        // 发送新消息罗马推送
        pushNewMessage(param.getAppId(), param.getTargetUserId(), message.getId(), message.getType());
        return message.getId();
    }


    @Override
    public Result<List<Long>> sendMessageBatch(RequestSendMessageBatch param) {
        if (CollUtil.isEmpty(param.getTargetUserIds())) {
            log.warn("send message batch failed, targetUserIds is empty, content:{}, sendUserId: {}", param.getContent(), param.getSendUserId());
            return new Result<>(SEND_MESSAGE_BATCH_TARGET_UID_EMPTY, Collections.emptyList());
        }

        BatchSaveMessageParam batchSaveMessageParam = MessageConvert.I.convertSaveMessageBatchParam(param);
        List<WcMessage> messages = waveCenterMessageDao.batchSaveMessages(batchSaveMessageParam);

        // 发送新消息罗马推送
        messages.forEach(message -> pushNewMessage(param.getAppId(), message.getTargetUserId(), message.getId(), message.getType()));
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, messages.stream().map(WcMessage::getId).collect(Collectors.toList()));
    }

    @Override
    public Result<List<Long>> sendMessage2Role(RequestSendMessageToRole param) {
        if (CollUtil.isEmpty(param.getRoleCodes())) {
            log.warn("send message to role failed, roleCodes is empty, content:{}, sendUserId: {}", param.getContent(), param.getSendUserId());
            return new Result<>(SEND_MESSAGE_ROLE_TARGET_ROLE_EMPTY, Collections.emptyList());
        }


        SaveMessageToRoleParam saveMessageToRoleParam = MessageConvert.I.convertSaveMessageToRoleParam(param);
        List<WcMessage> messages = waveCenterMessageDao.saveMessageByRole(saveMessageToRoleParam);

        // 发送新消息罗马推送
        messages.forEach(message -> pushNewMessageByRole(param.getAppId(), message.getVisibleRoleCode(), message.getId(), message.getType()));
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, messages.stream().map(WcMessage::getId).collect(Collectors.toList()));
    }

    @Override
    public ResponseGetMessageList getMessageList(RequestGetMessageList param, Long showNoticeTimeStamp) {
        param.setType(null);
        List<MessageBean> messageList = waveCenterMessageDao.getMessageList(param);
        Long unRead = waveCenterMessageDao.getUnReadCount(param, showNoticeTimeStamp);

        return new ResponseGetMessageList()
                .setMessageList(messageList)
                .setUnRead(unRead)
                .setPerformanceId(Optional.ofNullable(CollUtil.getLast(messageList)).map(MessageBean::getCreateTime).orElse(0L) / 1000);
    }

    @Override
    public void batchRead(RequestBatchReadMessage param) {
        if (param.getAllRead()) {
            waveCenterMessageDao.batchReadAll(param.getType(), param.getUserId(), param.getRoleCode(), param.getAppId());
        } else if (param.getType() != null && param.getType() == WcNoticeConfigEnum.SIGN_APPLY.getCode()) {
            waveCenterMessageDao.batchRead(param.getUserId(), param.getIds());
        } else {
            waveCenterMessageDao.batchSaveReadRecord(param.getUserId(), param.getIds());
        }
    }

    @Override
    public ResponseQueryRecentMessages queryRecentMessages(Integer appId, Long userId, Integer size, String roleCode) {
        // 查询消息
        List<WcMessage> messageList = waveCenterMessageDao.queryRecentMessages(appId, userId, size, roleCode);
        // 查询公告
        List<WcNoticeConfig> noticeList = wcNoticeConfigDao.queryRecentNotices(appId, size);
        // 转换为 MessageBean
        List<MessageBean> msgBeans = messageList.stream()
                .map(MessageConvert.I::convertMessageBeanSimple)
                .collect(Collectors.toList());
        List<MessageBean> noticeBeans = noticeList.stream()
                .map(MessageConvert.I::convertNoticeConfigToMessageBean)
                .collect(Collectors.toList());
        // 合并并按 createTime 倒序
        List<MessageBean> all = new ArrayList<>();
        all.addAll(msgBeans);
        all.addAll(noticeBeans);
        //all按时间倒序排序
        all.sort((o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime()));
        if (all.size() > size) {
            all = all.subList(0, size);
        }
        return new ResponseQueryRecentMessages()
                .setMessageList(all);
    }

    private void pushNewMessage(int appId, Long targetUserId, Long messageId, Integer type) {
        // 发送罗马消息
        PushDTO<MessagePushDTO> pushDTO = new PushDTO<>();
        pushDTO.setBiz(PushConstants.Biz.NEW_MESSAGE);
        pushDTO.setData(new MessagePushDTO()
                .setMessageId(String.valueOf(messageId))
                .setType(type)
        );

        commonPushManager.pushWaveCenterMessage(
                PushTopicEnum.NEW_MESSAGE.getKey(appId, targetUserId),
                pushDTO
        );
    }

    private void pushNewMessageByRole(int appId, String roleCode, Long messageId, Integer type) {
        // 发送罗马消息
        PushDTO<MessagePushDTO> pushDTO = new PushDTO<>();
        pushDTO.setBiz(PushConstants.Biz.NEW_MESSAGE_BY_ROLE);
        pushDTO.setData(new MessagePushDTO()
                .setMessageId(String.valueOf(messageId))
                .setType(type)
        );

        commonPushManager.pushWaveCenterMessage(
                PushTopicEnum.NEW_MESSAGE_ROLE.getKey(appId, roleCode),
                pushDTO
        );
    }


}
