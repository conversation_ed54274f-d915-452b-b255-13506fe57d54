package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.job;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/22 11:41
 */
@Data
public class PaySettleConfigCodeSyncParam {

    /**
     * 需要同步的配置编码
     * <a href="https://lizhi2021.feishu.cn/wiki/ZCe8w8UuUicJ2WkU78RcHsLTn2g?sheet=b017f7">编码文档</a>
     * @see fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PaySettleConfigCodeEnum
     */
    private List<String> configCodes;

}
