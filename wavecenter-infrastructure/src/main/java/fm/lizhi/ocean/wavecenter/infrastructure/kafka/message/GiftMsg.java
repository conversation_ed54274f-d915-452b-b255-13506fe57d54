package fm.lizhi.ocean.wavecenter.infrastructure.kafka.message;


import lombok.Data;

/**
 * 送礼消息
 */
@Data
public class GiftMsg {

    /**
     * 版本号
     */
    private long version;
    /**
     * 事务ID
     */
    private long transactionId;

    /**
     * 赠送者ID
     */
    private long sendUserId;

    /**
     * 接收者ID，主播
     */
    private long recUserId;

    /**
     * 接收者ID，普通用户。场景是用户给用户送礼，产生的荔枝属于主播
     */
    private long recTargetUserId;

    /**
     * 礼物ID
     */
    private long giftId;

    /**
     * 礼物数量
     */
    private int giftAmount;

    /**
     * 玩法（普通送礼、一键抢头条等）
     *
     */
    private Integer giftPlayType;


    /**
     * 收入的荔枝总数
     */
    private int litchiAmount;

    /**
     * 创建时间，毫秒级时间戳
     */
    private long createTime;

    /**
     * 修改时间，毫秒级时间戳
     */
    private long modifyTime;

    /**
     * 送礼来源
     *
     */
    private int source;

    /**
     * 礼物来源
     */
    private String giftSource;

    /**
     * 礼物类型
     *
     */
    private int giftType;

    /**
     * 类型为 GiftType.RECREATION_GIFT 时（娱乐模式礼物），该值为魅力值，可正可负
     */
    private int value;

    /**
     * 直播场次ID
     */
    private long liveId;

    /**
     * 礼物价值。本次送礼折算的金币数。有可能为0，例如宝箱礼物开出空礼物
     */
    private int giftCoin;

    /**
     * 经验加成比例
     */

    private double proportion;

    /**
     * 实际的礼物Id
     * <p>
     * (当送礼礼物为宝箱时，actualGiftId与giftId 不一致，否则一致)
     */
    private long actualGiftId;


    /**
     * 送礼时 直播类型
     *
     */
    private int liveType;

    /**
     * 是否是赏金分成 true :是 , false:不是
     */
    private boolean isReward;

    /**
     * 分层交易信息
     */
    private RewardTradeLitchiAccount rewardTradeLitchiAccount;

    /**
     * 是否是空礼物
     */
    private boolean isGiftVoid;

    /**
     * 宝箱是否赠送金币
     */
    private int isGiveCoin;

    /**
     * 宝箱返还金币数量
     */
    private int giveCoinAmount;

    /**
     * 礼物开出道具信息
     */
    private long propId;

    /**
     * 特性类型
     *
     */
    private int featureType;

    /**
     * 礼物子类型
     */
    private int biz;

    /**
     * 礼物名
     */
    private String giftName;

    /**
     * 礼物描述
     */
    private String descr;


    private long appId;

    private long subAppId;

    /**
     * 是否为幸运礼物
     */
    private boolean lucky = false;

    /**
     * 收益人（PP）
     */
    private long incomeUserId;

    /**
     * 物品单价
     */
    private int unitPrice;

    /**
     * 请求id 唯一
     */
    private Long requestId;

    /**
     * 官频厅-收礼者的签约家族管理员ID
     */
    private long familyNjId;

    /**
     * 是否是官方厅
     */
    private boolean isOfficialRoom = false;

}
