package fm.lizhi.ocean.wavecenter.infrastructure.user.singleton;

import fm.lizhi.common.kafka.common.strategy.ConsumeStrategy;
import fm.lizhi.common.kafka.common.strategy.OffsetCommit;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class KafkaConsumeStrategy {

    @Bean
    public ConsumeStrategy syncConsumeStrategy() {
        return new ConsumeStrategy.Builder()
                .offsetCommit(OffsetCommit.Sync)
                .build();
    }
}
