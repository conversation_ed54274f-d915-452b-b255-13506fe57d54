package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.convert;

import fm.lizhi.hy.content.protocol.LiveActivityResourceProto;
import fm.lizhi.live.pp.dto.homeSeatManual.ConfigDto;
import fm.lizhi.live.pp.dto.homeSeatManual.SaveConfigReq;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityOfficialSeatTime;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.*;
import fm.lizhi.pp.content.artrcmd.bean.offact.OfficialActDetailDto;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.List;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActivityOfficialSeatConvert {

    ActivityOfficialSeatConvert I = Mappers.getMapper(ActivityOfficialSeatConvert.class);

    /**
     * 官方频道位置信息实体转DTO
     *
     * @param bean 官方频道位置信息实体
     * @return DTO
     */
    ActivityOfficialSeatTimeDTO officialSeatBean2DTO(ActivityOfficialSeatTime bean);

    List<ActivityOfficialSeatTimeDTO> officialSeatBeans2DTOs(List<ActivityOfficialSeatTime> list);

    PpOfficialSeatDetailDTO convertPpSeatDto2WaveDTO(OfficialActDetailDto detailDto);


    HyOfficialSeatDetailDTO convertHySeatProto2WaveDTO(LiveActivityResourceProto.LiveActivityResource source);

    default Date map(long value) {
        return new Date(value);
    }

    XmOfficialSeatDetailDTO convertXmSeatDto2WaveDTO(ConfigDto dto);

    @Mappings({
            @Mapping(target = "note", source = "remark"),
            @Mapping(target = "seat", source = "position"),
    })
    SaveConfigReq buildXmConfigReq(XmOfficialSeatDetailDTO officialSeatDetailDTO);

    OfficialActDetailDto buildPpConfigReq(PpOfficialSeatDetailDTO officialSeatDetailDTO);

}
