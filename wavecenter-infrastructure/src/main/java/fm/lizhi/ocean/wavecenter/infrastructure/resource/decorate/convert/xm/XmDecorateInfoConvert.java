package fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.convert.xm;

import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.DecorateInfoBean;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.xm.vip.bean.decorate.DecorateDto;
import fm.lizhi.xm.vip.bean.decorate.resp.PageDecorateDto;
import org.apache.commons.lang3.StringUtils;

/**
 * @description:
 * @author: guoyibin
 * @create: 2025/03/31 12:11
 */
public class XmDecorateInfoConvert {

    public static DecorateInfoBean convertToDecorateInfoBean(PageDecorateDto pageDecorateDto, String cdnHost, PlatformDecorateTypeEnum platformDecorateTypeEnum) {
        DecorateInfoBean decorateInfoBean = new DecorateInfoBean();
        decorateInfoBean.setDecorateId(Long.valueOf(pageDecorateDto.getId()));
        decorateInfoBean.setDecorateName(pageDecorateDto.getName());
        decorateInfoBean.setDecorateImage(UrlUtils.addCdnHost(cdnHost, StringUtils.isNotBlank(pageDecorateDto.getPcAniUrl())
                ? pageDecorateDto.getPcAniUrl()
                : StringUtils.isNotBlank(pageDecorateDto.getThumbUrl()) ? pageDecorateDto.getThumbUrl() : pageDecorateDto.getMaterialUrl()));
        decorateInfoBean.setDecorateExpireTime(pageDecorateDto.getValidMin());
        decorateInfoBean.setPreviewUrl(pageDecorateDto.getThumbUrl());
        decorateInfoBean.setDecorateTypeEnum(platformDecorateTypeEnum);
        return decorateInfoBean;
    }

    public static DecorateInfoBean convertDtoToDecorateInfoBean(DecorateDto decorateDto, String cdnHost, PlatformDecorateTypeEnum platformDecorateTypeEnum) {
        DecorateInfoBean decorateInfoBean = new DecorateInfoBean();
        decorateInfoBean.setDecorateId(decorateDto.getId());
        decorateInfoBean.setDecorateName(decorateDto.getName());
        decorateInfoBean.setDecorateImage(UrlUtils.addCdnHost(cdnHost,
                StringUtils.isNotBlank(decorateDto.getPcAniUrl()) ? decorateDto.getPcAniUrl() :
                        StringUtils.isNotBlank(decorateDto.getThumbUrl()) ? decorateDto.getThumbUrl() : decorateDto.getMaterialUrl()));
        decorateInfoBean.setDecorateExpireTime(decorateDto.getValidMin());
        decorateInfoBean.setDecorateTypeEnum(platformDecorateTypeEnum);
        decorateInfoBean.setPreviewUrl(decorateDto.getThumbUrl());
        return decorateInfoBean;
    }
}
