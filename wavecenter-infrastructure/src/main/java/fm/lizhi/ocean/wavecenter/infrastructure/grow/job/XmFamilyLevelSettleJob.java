package fm.lizhi.ocean.wavecenter.infrastructure.grow.job;

import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 公会等级结算job
 * <AUTHOR>
 * @date 2025/3/19 11:48
 */
@Slf4j
@Component
public class XmFamilyLevelSettleJob extends AbstractFamilyLevelSettleJob implements JobHandler {

    @Override
    public void execute(JobExecuteContext jobExecuteContext) throws Exception {
        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.XIMI);
        doSettle(jobExecuteContext);
    }


}
