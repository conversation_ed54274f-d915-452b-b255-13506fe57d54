package fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.common.datastore.mysql.constant.ParamContants;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * 已废弃, 麦序福利1.0的web站打卡明细接口用到, 从麦序福利2.0开始废弃.
 * <p>
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 */
@DataStore(namespace = "mysql_ocean_wave_r")
public interface WaveCheckInRecordEntityMapper {

    @Select({
            "<script>" +
                    "select sum(income) as income , sum(charm) as charm ,   sum(up_guest_dur) as upGuestDur , sum( CASE WHEN  `status` > 0  THEN 1 ELSE 0 END ) as seatOrder  , user_id  from wavecenter_check_in_record where app_id = #{appId} and  roomId = #{room_id} and  start_time &gt;= #{startDate} and start_time &lt;= #{endDate}  GROUP BY user_id " +
                    "</script>"
    })
    List<RoomHourStatsEntity> roomHourStats(@Param("appId") Integer appId, @Param("roomId") Long roomId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);


    @Select({
            "<script>" +
                    " select *  from wavecenter_check_in_record  where app_id = #{appId}  and  nj_id = #{roomId} and  start_time &gt;= #{startDate} and start_time &lt;= #{endDate}  " +
                    "</script>"
    })
    List<WcCheckInRecord> roomHourDetail(@Param("appId") Integer appId, @Param("roomId") Long roomId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);


    @Select({
            "<script>" +
                    "select sum(income) as income , sum(charm) as charm , sum( CASE WHEN  `status` &gt; 0  THEN 1 ELSE 0 END ) as seatOrder ,  nj_id  from wavecenter_check_in_record where app_id = #{appId} and  family_id = #{familyId} and  start_time &gt;= #{startDate} and start_time &lt;= #{endDate}  GROUP BY nj_id  " +
                    "</script>"
    })
    List<GuildRoomHourStatsEntity> familyRoomHourStats(@Param("appId") Integer appId, @Param("familyId") Long familyId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Select({
            "<script>"
            , "select sum(a.income)                               as income,"
            ,"       sum(a.charm_value)                          as charm,"
            ,"       sum(CASE WHEN a.status &gt; 0 THEN 1 ELSE 0 END) as seatOrder,"
            ,"       sum(CASE WHEN a.status = 1 THEN 1 ELSE 0 END) as checkPlayerNumber"
            ,"from wave_check_in_record a"
            ,"         left join"
            ,"     wave_check_in_schedule b on a.schedule_id = b.id"
            ,"where family_id = #{familyId}"
            ,"  and app_id = #{appId}"
            ,"  and income &gt; 0"
            ,"  and start_time &gt;=#{startDate} "
            ,"  and start_time &lt;=#{endDate}"
            ,"</script>"
    })
    GuildRoomHourSummaryEntity guildRoomHourSummary(@Param("appId") Integer appId, @Param("familyId") Long familyId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);


    @Select({
            "<script>"
            , "select sum(a.income)  as income,"
            , "       sum(a.charm_value)   as charm,"
            , "       sum(CASE WHEN a.status &gt; 0 THEN 1 ELSE 0 END) as seatOrder,"
            , "       sum(CASE WHEN a.user_id = b.host_id THEN 1 ELSE 0 END) as hostCnt"
            , "from wave_check_in_record a"
            , "         left join"
            , "     wave_check_in_schedule b on a.schedule_id = b.id"
            , "where nj_id = #{njId}"
            , "  and app_id = #{appId}"
            , "  and income &gt; 0"
            , "  and start_time &gt;= #{startDate} and start_time &lt;= #{endDate}"
            , "</script>"
    })
    RoomHourSummaryEntity roomHourSummary(@Param("appId") Integer appId, @Param("njId") Long njId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);


    @Select({
            "<script>" +
                    " select count(DISTINCT(user_id))   from wavecenter_check_in_record  where  family_id = #{familyId} and  app_id = #{appId}  and   `start_time` &gt;= #{startDate} and `start_time` &lt;= #{endDate}    " +
                    "</script>"
    })
    Integer guildRoomHourIncomePlayerSummary(@Param("appId") Integer appId, @Param("familyId") Long familyId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Select({
            "<script>" +
                    "select app_id, family_id, host_id, a.room_id, user_id, nj_id, income, " +
                    "a.`status`, charm_value, original_value as charm , start_time, end_time, " +
                    " (CASE WHEN user_id = host_id THEN 1 ELSE 0 END) as is_host, " +
                    " b.remark,a.create_time, a.modify_time" +
                    " from wave_check_in_record a left join " +
                    " wave_check_in_schedule b on a.schedule_id = b.id " +
                    " where a.user_id =#{userId} and  b.start_time &gt;= #{startDate} and  b.start_time &lt;= #{endDate} and b.app_id=#{appId} and b.family_id = #{familyId} " +
                    "<if test = 'signType != null and signType == 1 '>" +
                        "<if test = 'njId != null and njId > 0 '>" +
                        " and b.nj_id = #{njId} " +
                        "</if>" +
                    "</if>" +
                    "<if test = 'signType != null and signType == 0 '>" +
                        "<if test = 'njId != null and njId > 0 '>" +
                        " and b.nj_id != #{njId} " +
                        "</if>" +
                    "</if>" +
            "</script>"
    })
    List<WaveCheckInRecordEntity> hourDetail(@Param("appId") Integer appId, @Param("userId") Long userId,
                                             @Param("startDate") Date startDate, @Param("endDate")Date endDate,
                                             @Param("njId") Long njId, @Param("familyId") Long familyId,
                                             @Param("signType") Integer signType);

    @Select({
            "<script>"
            , "select app_id, family_id, host_id, a.room_id, user_id, nj_id, income, "
            , "a.`status`, charm_value, original_value as charm , start_time, end_time, "
            , " (CASE WHEN user_id = host_id THEN 1 ELSE 0 END) as is_host, "
            , " b.remark,a.create_time, a.modify_time"
            , " from wave_check_in_record a left join "
            , " wave_check_in_schedule b on a.schedule_id = b.id "
            , " where b.start_time = #{startDate} and  b.end_time = #{endDate} and b.app_id=#{appId}   "
            , "<if test=' null != playerId and playerId > 0 '>"
            ," and  a.user_id  = #{playerId} "
            ,"</if>"
            ,"<if test=' null != roomId and roomId > 0 '>"
            ," and  b.nj_id  = #{roomId} "
            ,"</if>"
            , "</script>"
    })
    List<WcCheckInRecord> hourDetail2(@Param("appId") Integer appId
            , @Param("roomId") Long roomId, @Param("playerId") Long playerId
            , @Param("startDate") Date startDate, @Param("endDate") Date endDate);


    @Select({
            "<script>"
            , "select count(DISTINCT (user_id))"
            , "from wave_check_in_record a"
            , "         left join"
            , "     wave_check_in_schedule b on a.schedule_id = b.id"
            , "where nj_id = #{njId} and  app_id = #{appId}  and  start_time &gt;= #{startDate} and start_time &lt;= #{endDate}"
            , "</script>"
    })
    Integer roomHourIncomePlayerSummary(@Param("appId") Integer appId, @Param("njId") Long njId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);


    @Select({
            "<script>" +
                    "select app_id, family_id, host_id, a.room_id, user_id, nj_id, income, " +
                    "a.`status`, charm_value, original_value as charm , start_time, end_time, " +
                    " (CASE WHEN user_id = host_id THEN 1 ELSE 0 END) as is_host, " +
                    " b.remark,a.create_time, a.modify_time" +
                    " from wave_check_in_schedule b inner join " +
                    " wave_check_in_record a ON a.schedule_id = b.id " +
                    " where b.family_id =#{familyId} and  b.start_time &gt;= #{startDate} and  b.start_time &lt;= #{endDate} and b.app_id=#{appId} " +
                    " order by a.create_time" +
                    "</script>"
    })
    PageList<WaveCheckInRecordEntity> guildRoomHourDetail(@Param("appId") Integer appId, @Param("familyId") Long familyId,
                                                          @Param("startDate") Date startDate, @Param("endDate") Date endDate,
                                                          @Param(ParamContants.PAGE_NUMBER)int pageNumber,
                                                          @Param(ParamContants.PAGE_SIZE)int pageSize);
}
