package fm.lizhi.ocean.wavecenter.infrastructure.common.convert;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageConfigBean;
import fm.lizhi.ocean.wavecenter.datastore.platform.common.entity.WcUserPageConfig;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/20 14:51
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ConfigInfraConvert {

    ConfigInfraConvert I = Mappers.getMapper(ConfigInfraConvert.class);

    PageConfigBean pageConfigPo2Bean(WcUserPageConfig po);

    List<PageConfigBean> pageConfigPos2Beans(List<WcUserPageConfig> pos);

}
