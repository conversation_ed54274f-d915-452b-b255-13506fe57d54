package fm.lizhi.ocean.wavecenter.infrastructure.user.remote.pp;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.IUserGroupRemote;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/5/9 16:47
 */
@Slf4j
@Component
public class PpUserGroupRemote implements IUserGroupRemote {
    @Override
    public boolean isUserInGroup(Long userId, Long groupId) {
        return false;
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }
}
