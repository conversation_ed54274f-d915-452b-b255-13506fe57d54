package fm.lizhi.ocean.wavecenter.infrastructure.user.po;

import fm.lizhi.commons.config.util.JsonUtil;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/4/17 22:38
 */
@Data
@Accessors(chain = true)
public class AccessTokenInfoPo {

    private String refreshToken;

    public static AccessTokenInfoPo of(String valueJsonStr){
        return JsonUtil.loads(valueJsonStr, AccessTokenInfoPo.class);
    }

    public String toJson(){
        return JsonUtil.dumps(this);
    }

}
