package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.manager;

import com.alibaba.fastjson.JSON;
import com.lark.oapi.core.utils.Lists;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.RequestQueryEffectActivitiesByTemplateIdBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityStatusEnum;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityTemplateUsedRelation;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.convert.ActivityApplyConvert;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityApplyInfo;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityApplyInfoExample;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.mapper.ActivityApplyInfoMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.dao.ActivityTemplateUsedRelationDao;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.dao.ActivityClassificationDao;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityClassConfig;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.LogicDeleteConstants;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivitySimpleInfoDT0;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivitySimpleQueryParamDTO;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityInfoQueryManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ActivityInfoQueryManagerImpl implements ActivityInfoQueryManager {

    @Autowired
    private ActivityApplyInfoMapper activityApplyInfoMapper;
    @Autowired
    private ActivityClassificationDao activityClassificationDao;
    @Autowired
    private ActivityTemplateUsedRelationDao relationDao;

    @Override
    public PageBean<ActivitySimpleInfoDT0> queryActivityInfoList(ActivitySimpleQueryParamDTO param) {
        ActivityApplyInfoExample example = new ActivityApplyInfoExample();
        ActivityApplyInfoExample.Criteria criteria = example.createCriteria();
        criteria.andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andAppIdEqualTo(param.getAppId());
        //拼接create time降序排序条件
        example.setOrderByClause("create_time desc");
        if (param.getDeleted() != null) {
            criteria.andDeletedEqualTo(param.getDeleted());
        }
        if (param.getNjId() != null) {
            criteria.andNjIdEqualTo(param.getNjId());
        }
        if (param.getFamilyId() != null) {
            criteria.andFamilyIdEqualTo(param.getFamilyId());
        }
        if (param.getApplyUserId() != null) {
            criteria.andApplicantUidEqualTo(param.getApplyUserId());
        }
        if (param.getName() != null) {
            criteria.andNameLike("%" + param.getName() + "%");
        }

        if (param.getMinStartTime() != null) {
            criteria.andStartTimeGreaterThanOrEqualTo(param.getMinStartTime());
        }

        if (param.getMaxStartTime() != null) {
            criteria.andStartTimeLessThanOrEqualTo(param.getMaxStartTime());
        }

        if (param.getApplyStartTime() != null) {
            criteria.andCreateTimeGreaterThanOrEqualTo(param.getApplyStartTime());
        }

        if (param.getApplyEndTime() != null) {
            criteria.andCreateTimeLessThanOrEqualTo(param.getApplyEndTime());
        }

        //未开始，则补充开始时间大于等于当前时间的条件
        if (param.getActivityStatus() != null && param.getActivityStatus().equals(ActivityStatusEnum.UN_START.getStatus())) {
            criteria.andStartTimeGreaterThan(new Date());
        }

        //已结束，则补充开始时间小于当前时间的条件
        if (param.getActivityStatus() != null && param.getActivityStatus().equals(ActivityStatusEnum.END.getStatus())) {
            criteria.andEndTimeLessThan(new Date());
        }

        if (param.getActivityStatus() != null && param.getActivityStatus().equals(ActivityStatusEnum.START.getStatus())) {
            //进行中，则补充结束时间大于当前时间的条件
            criteria.andStartTimeLessThan(new Date());
            criteria.andEndTimeGreaterThan(new Date());
        }
        if (param.getAuditStatus() != null && param.getAuditStatus().size() > 0) {
            criteria.andAuditStatusIn(param.getAuditStatus());
        }

        if (param.getApplyType() != null) {
            criteria.andApplyTypeEqualTo(param.getApplyType());
        }

        if (param.getClassIds() != null && param.getClassIds().size() > 0) {
            List<ActivityClassConfig> activityClassConfigs = activityClassificationDao.listClassificationByClassOrBigClass(param.getClassIds());
            criteria.andClassIdIn(activityClassConfigs.stream().map(ActivityClassConfig::getId).collect(Collectors.toList()));
        }

        if (param.getActivityId() != null) {
            criteria.andIdEqualTo(param.getActivityId());
        }

        PageList<ActivityApplyInfo> infoList = activityApplyInfoMapper.pageByExample(example, param.getPageNo(), param.getPageSize());
        return PageBean.of(infoList.getTotal(), ActivityApplyConvert.I.simpleBeans2DTOs(infoList));
    }

    @Override
    public List<ActivitySimpleInfoDT0> queryEffectActivityInfoListByTemplateId(RequestQueryEffectActivitiesByTemplateIdBean param) {
        log.info("queryEffectActivityInfoListByTemplateId , param :{}", JSON.toJSONString(param));
        Long templateId = param.getTemplateId();

        List<ActivityTemplateUsedRelation> relations = relationDao.getTemplateUsedRelationByTemplateId(templateId);

        if(CollectionUtils.isEmpty(relations)){
            return Lists.newArrayList();
        }
        List<Long> activityIds = relations.stream().map(ActivityTemplateUsedRelation::getActivityId).distinct().collect(Collectors.toList());

        ActivityApplyInfoExample example = new ActivityApplyInfoExample();
        example.createCriteria()
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andAppIdEqualTo(param.getAppId())
                .andDeletedEqualTo(LogicDeleteConstants.NOT_DELETED)
                .andAuditStatusEqualTo(ActivityAuditStatusEnum.AUDIT_PASS.getStatus())
                .andStartTimeLessThan(new Date())
                .andEndTimeGreaterThan(new Date())
                .andIdIn(activityIds);

        List<ActivityApplyInfo> activityApplyInfos = activityApplyInfoMapper.selectByExample(example);
        if (activityApplyInfos.isEmpty()) {
            return Collections.emptyList();
        }

        return ActivityApplyConvert.I.simpleBeans2DTOs(activityApplyInfos);
    }
}
