package fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.pp;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.DecorateInfoBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.constants.DecorateConstant;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.constants.PpDecorateTypeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.convert.pp.PpDecorateInfoConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.DecorateRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.request.RequestRecoverDecorate;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.request.RequestSendDecorate;
import fm.lizhi.pp.util.json.JsonUtil;
import fm.lizhi.pp.vip.api.PpDecorateService;
import fm.lizhi.pp.vip.bean.DecoratePreviewBo;
import fm.lizhi.pp.vip.bean.req.GetDecorateStockListReq;
import fm.lizhi.pp.vip.bean.resp.PageDecorateDto;
import fm.lizhi.pp.vip.bean.resp.PageDecorateStockDto;
import fm.lizhi.pp.vip.protocol.PpDecorateProto;
import fm.lizhi.pp.vip.bean.req.SendDecorateManagerReq;
import fm.lizhi.pp.vip.bean.enums.DecorateSendTypeEnum;
import fm.lizhi.pp.vip.api.DecorateUserService;
import fm.lizhi.pp.vip.protocol.DecorateUserProto;
import fm.lizhi.pp.vip.bean.req.DelDecorateStockByIdsReq;

import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class PpDecorateRemote implements DecorateRemote {

    private static final int USE_IMMEDIATELY = 1;

    private static final int DEFAULT_GLORY_VALID_TIME = 20990101;

    @Autowired
    private PpDecorateService decorateService;

    @Autowired
    private DecorateUserService decorateUserService;

    @Autowired
    private CommonConfig commonConfig;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public DecorateInfoBean getDecorateInfo(PlatformDecorateTypeEnum decorateType, Long decorateId) {

        if (decorateType == PlatformDecorateTypeEnum.MEDAL) {
            throw new IllegalArgumentException("pp.getDecorateInfo not support medal");
        }

        Result<PpDecorateProto.ResponseGetDecorateInfoById> result = decorateService.getDecorateInfoById(decorateId);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("pp.getDecorateInfo fail. getDecorateInfoById fail, decorateId={},rCode={}", decorateId, result.rCode());
            return null;
        }

        String decorateDtoStr = result.target().getDecorateDtoStr();
        return PpDecorateInfoConvert.convertToDecorateInfoBean(decorateDtoStr, commonConfig.getBizConfig().getCdnHost(), decorateType);
    }

    @Override
    public PageBean<DecorateInfoBean> getDecorateInfoList(PlatformDecorateTypeEnum decorateType, Long decorateId, String decorateName, int pageNum, int pageSize) {

        if (decorateType == PlatformDecorateTypeEnum.MEDAL) {
            throw new IllegalArgumentException("pp.getDecorateInfoList not support medal");
        }

        RequestGetDecorateList param = RequestGetDecorateList.builder()
                                        .pageNum(pageNum).pageSize(pageSize)
                                        .type(PpDecorateTypeEnum.getByType(decorateType).getType())
                                        .build();

        if (decorateId != null && decorateId > 0) {
            param.setId(decorateId);
        }
        if (StringUtils.isNotBlank(decorateName)) {
            param.setName(decorateName);
        }

        Result<PpDecorateProto.ResponseGetDecorateList> result = decorateService.getDecorateList(JsonUtils.toJsonStringLegacy(param));

        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("pp.getDecorateInfoList fail. getDecorateList fail, decorateType={},decorateName={},rCode={}", decorateType, decorateName, result.rCode());
            return new PageBean<DecorateInfoBean>(0, Collections.emptyList());
        }
        
        PpDecorateProto.ResponseGetDecorateList target = result.target();
        List<PageDecorateDto> decorateDTOList = JSONObject.parseArray(target.getDecorateListStr(), PageDecorateDto.class);
        List<DecorateInfoBean> decorateInfoBeanList = decorateDTOList.stream()
                .map(x -> PpDecorateInfoConvert.convertToDecorateInfoBean(x, commonConfig.getBizConfig().getCdnHost(), decorateType))
                    .collect(Collectors.toList());
        return new PageBean<DecorateInfoBean>(result.target().getTotalPage(), decorateInfoBeanList);
        
    }

    @Override
    public List<DecorateInfoBean> batchGetDecorateInfo(PlatformDecorateTypeEnum decorateType, List<Long> decorateIds) {
        List<DecorateInfoBean> res = new ArrayList<>();
        for (Long id : decorateIds) {
            Result<PpDecorateProto.ResponseGetDecorateInfoById> result = decorateService.getDecorateInfoById(id);
            if (RpcResult.isFail(result)) {
                log.warn("pp batch decorate result fail. type:{}, id:{}", decorateType, id);
                return Collections.emptyList();
            }
            String decorateDtoStr = result.target().getDecorateDtoStr();
            DecorateInfoBean decorateInfoBean =
                    PpDecorateInfoConvert.convertToDecorateInfoBean(JSONObject.parseObject(decorateDtoStr, PageDecorateDto.class), commonConfig.getBizConfig().getCdnHost(), decorateType);
            res.add(decorateInfoBean);
        }
        
        return res;
    }

    @Override
    public Result<Void> sendDecorate(RequestSendDecorate request) {
        PlatformDecorateTypeEnum decorateType = request.getDecorateType();
        switch (decorateType) {
            case USER_GLORY:
                return sendUserGlory(request);
            case AVATAR:
            case BUBBLE:
                return sendDecorateManager(request);
            default:
                return RpcResult.fail(CommonService.PARAM_ERROR, "pp暂未实现该装扮类型发放: " + decorateType);
        }
    }

    private Result<Void> sendUserGlory(RequestSendDecorate request) {
        //获取用户官方认证装扮的有效期
        PpDecorateTypeEnum decorateType = PpDecorateTypeEnum.getByType(request.getDecorateType());
        Result<DecorateUserProto.ResponseGetUserValidDecorates> decorates = decorateUserService.getUserValidDecorates(decorateType.getType(), request.getUserId());
        //查得到用户的库存，则返回
        if (RpcResult.isSuccess(decorates) && StringUtils.isNotBlank(decorates.target().getValidDecorateListStr())) {
            log.info("pp.sendUserGlory. getUserValidDecorates,decorateType={},userId={}", decorateType, request.getUserId());
            List<DecoratePreviewBo> decoratePreviewBos = JsonUtil.fromJson2List(decorates.target().getValidDecorateListStr(), DecoratePreviewBo.class);
            List<DecoratePreviewBo> collect = decoratePreviewBos.stream()
                    .filter(decoratePreviewBo -> decoratePreviewBo.getDecorateId().equals(request.getDecorateId()))
                    .collect(Collectors.toList());
            //如果用户存在超过2099年有效期的装扮库存，就不发了。。看看这份代码有无必要
            for (DecoratePreviewBo previewBo : collect) {
                int yearFromTimestamp = MyDateUtil.getDateDayValue(new Date(previewBo.getEndTime()));
                if(yearFromTimestamp >= DecorateConstant.DEFAULT_GLORY_VALID_TIME) {
                    log.warn("pp.sendUserGlory ignore because valid time too long, decorateType={},userId={},previewBo={}",decorateType, request.getUserId(), JsonUtils.toJsonString(previewBo));
                    return RpcResult.success();
                }
            }
        }
        //@why pp的官方认证不能配置有效期 需要手动传
        if(request.getDecorateExpireTime() != null) {
            request.setDecorateExpireTime(PpDecorateTypeEnum.USER_GLORY.calValidMin(request.getDecorateExpireTime()));
        }
        return sendDecorateManager(request);
    }

    private Result<Void> sendDecorateManager(RequestSendDecorate request) {
        SendDecorateManagerReq sendDecorateManagerReq = new SendDecorateManagerReq();
        sendDecorateManagerReq.setDecorateId(request.getDecorateId());
        sendDecorateManagerReq.setOwnerIds(String.valueOf(request.getUserId()));
        sendDecorateManagerReq.setCount(request.getDecorateNumber());
        sendDecorateManagerReq.setUseImmediately(USE_IMMEDIATELY);
        sendDecorateManagerReq.setValidMin(request.getDecorateExpireTime());
        sendDecorateManagerReq.setSendType(DecorateSendTypeEnum.SEND_NOW.getType());
        Result<DecorateUserProto.ResponseSendDecorateManager> result = decorateUserService.sendDecorateManager(JsonUtils.toJsonString(sendDecorateManagerReq));
        if (RpcResult.isFail(result)) {
            log.warn("pp.sendDecorateManager fail. decorateId={}, ownerId={}, rCode={}", request.getDecorateId(), request.getUserId(), result.rCode());
            return RpcResult.fail(result.rCode(), result.getMessage());
        }
        List<Long> failIdList = result.target().getFailIdListList();
        if (!failIdList.isEmpty()) {
            log.warn("pp.sendDecorateManager fail. failIdList={}", failIdList);
            return RpcResult.fail(CommonService.BUSINESS_ERROR, "pp发放装扮失败");
        }
        return RpcResult.success();
    }



    @Override
    public Result<Void> recoverDecorate(RequestRecoverDecorate request) {
        PlatformDecorateTypeEnum decorateType = request.getDecorateType();
        switch (decorateType) {
            case USER_GLORY:
            case AVATAR:
            case BUBBLE:
                return recoverDecorateManager(request);
            default:
                return RpcResult.fail(CommonService.PARAM_ERROR, "pp暂未实现该装扮类型恢复: " + decorateType);
        }
    }

    private Result<Void> recoverDecorateManager(RequestRecoverDecorate request) {

        GetDecorateStockListReq req = new GetDecorateStockListReq();
        req.setDecorateId(request.getDecorateId());
        req.setOwnerId(request.getUserId());

        Result<DecorateUserProto.ResponseGetDecorateStockList> stockResult = decorateUserService.getDecorateStockList(JsonUtils.toJsonString(req));
        if (RpcResult.isFail(stockResult)) {
            log.warn("pp.recoverDecorateManager fail. decorateId={}, ownerId={}, rCode={}", request.getDecorateId(), request.getUserId(), stockResult.rCode());
            return RpcResult.fail(stockResult.rCode(), stockResult.getMessage());
        }
        String stockListStr = stockResult.target().getStockListStr();
        if (StringUtils.isBlank(stockListStr)) {
            log.warn("pp.recoverDecorateManager stockListStr is blank. decorateId={}, ownerId={}, rCode={}",
                    request.getDecorateId(), request.getUserId(), stockResult.rCode());
            return RpcResult.success();
        }

        List<PageDecorateStockDto> stockList = JSONArray.parseArray(stockListStr, PageDecorateStockDto.class);
        if (CollectionUtils.isEmpty(stockList)) {
            log.warn("pp.recoverDecorateManager stockList is empty. decorateId={}, ownerId={}, rCode={}",
                    request.getDecorateId(), request.getUserId(), stockResult.rCode());
            return RpcResult.success();
        }

        List<Long> stockIds = stockList.stream().map(PageDecorateStockDto::getId).collect(Collectors.toList());
        DelDecorateStockByIdsReq recoverDecorateManagerReq = new DelDecorateStockByIdsReq();
        recoverDecorateManagerReq.setIds(stockIds);
        Result<DecorateUserProto.ResponseBatchDelDecorateStockByIds> result = decorateUserService.batchDelDecorateStockByIds(JsonUtils.toJsonString(recoverDecorateManagerReq));
        if (RpcResult.isFail(result)) {
            log.warn("pp.recoverDecorateManager fail. decorateId={}, ownerId={}, rCode={}", request.getDecorateId(), request.getUserId(), result.rCode());
            return RpcResult.fail(result.rCode(), result.getMessage());
        }
        return RpcResult.success();
    }

    @Data
    @Builder
    static class RequestGetDecorateList {

        private Long id;

        private Integer type;
        /**
         * 物品名称
         */
        private String name;
        /**
         * 第几页
         */
        private int pageNum = 1;
        /**
         * 每页大小
         */
        private int pageSize = 20;
    }
}

