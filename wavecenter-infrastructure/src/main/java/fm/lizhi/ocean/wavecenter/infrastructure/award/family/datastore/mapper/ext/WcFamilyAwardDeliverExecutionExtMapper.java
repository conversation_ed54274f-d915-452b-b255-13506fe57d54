package fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcFamilyAwardDeliverExecutionExtMapper {

    @Update("UPDATE `wavecenter_family_award_deliver_execution`\n" +
            "SET `status` = 2, `modify_time` = NOW()\n" +
            "WHERE `id` = #{id} AND `status` = 1")
    int updateToSuccess(@Param("id") long id);

    @Update("UPDATE `wavecenter_family_award_deliver_execution`\n" +
            "SET `status` = 2, `error_code` = 0, `error_text` = '', `modify_time` = NOW()\n" +
            "WHERE `id` = #{id}")
    int updateReDeliverToSuccess(@Param("id") long id);

    @Update("UPDATE `wavecenter_family_award_deliver_execution`\n" +
            "SET `status` = 3, `error_code` = #{errorCode}, `error_text` = #{errorText}, `modify_time` = NOW()\n" +
            "WHERE `id` = #{id} AND `status` = 1")
    int updateToFailure(@Param("id") long id, @Param("errorCode") int errorCode, @Param("errorText") String errorText);

    @Update("UPDATE `wavecenter_family_award_deliver_execution`\n" +
            "SET `status` = 3, `error_code` = #{errorCode}, `error_text` = #{errorText}, `modify_time` = NOW()\n" +
            "WHERE `id` = #{id}")
    int updateReDeliverToFailure(@Param("id") long id, @Param("errorCode") int errorCode, @Param("errorText") String errorText);
}
