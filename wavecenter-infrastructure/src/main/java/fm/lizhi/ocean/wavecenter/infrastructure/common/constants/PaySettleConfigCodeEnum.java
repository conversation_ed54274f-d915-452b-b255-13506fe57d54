package fm.lizhi.ocean.wavecenter.infrastructure.common.constants;

import lombok.Getter;

/**
 * 结算项规则名称
 * <a href="https://lizhi2021.feishu.cn/wiki/ZCe8w8UuUicJ2WkU78RcHsLTn2g?sheet=b017f7">编码文档</a>
 * <AUTHOR>
 */
public enum PaySettleConfigCodeEnum {


    FAMILY_INCOME_TOTAL_AMOUNT("familyIncomeTotalAmount","公会-总收入"),

    FAMILY_HALL_INCOME_TOTAL_AMOUNT("familyHallIncomeTotalAmount", "公会-厅收礼收入"),

    FAMILY_INDIVIDUAL_INCOME_TOTAL_AMOUNT("familyIndividualIncomeTotalAmount", "公会-个播收礼收入"),

    FAMILY_NOBILITY_ROYALTY_INCOME_TOTAL_AMOUNT("familyNobilityRoyaltyIncomeTotalAmount", "公会-贵族提成收入"),

    FAMILY_NOBILITY_PERSONAL_ROYALTY_INCOME_TOTAL_AMOUNT("familyNobilityPersonalRoyaltyIncomeTotalAmount", "公会-贵族提成(个播)"),

    FAMILY_NOBILITY_HALL_ROYALTY_INCOME_TOTAL_AMOUNT("familyNobilityHallRoyaltyIncomeTotalAmount", "公会-贵族提成(厅)"),

    FAMILY_OFFICIAL_HALL_INCOME_TOTAL_AMOUNT("familyOfficialHallIncomeTotalAmount", "公会-官方厅收礼收入"),

    HALL_SETTLEABLE_AMOUNT("getHallSettleableAmount", "可结算余额"),

    FAMILY_WAIT_SETTLEMENT_INCOME_TOTAL_AMOUNT("familyWaitSettlementIncomeTotalAmount", "公会-待结算余额"),

    HALL_WAIT_SETTLEMENT_INCOME_TOTAL_AMOUNT("hallWaitSettlementIncomeTotalAmount", "厅-待结算余额"),

    HALL_HALL_INCOME_TOTAL_AMOUNT("hallHallIncomeTotalAmount", "厅-厅收礼收入"),

    HALL_INDIVIDUAL_INCOME_TOTAL_AMOUNT("hallIndividualIncomeTotalAmount", "厅-个播收礼收入"),

    HALL_NOBILITY_ROYALTY_INCOME_TOTAL_AMOUNT("hallNobilityRoyaltyIncomeTotalAmount", "厅-厅贵族提成"),

    HALL_NOBILITY_PERSONAL_ROYALTY_INCOME_TOTAL_AMOUNT("hallNobilityPersonalRoyaltyIncomeTotalAmount", "厅-贵族提成（个播）"),

    HALL_NOBILITY_HALL_ROYALTY_INCOME_TOTAL_AMOUNT("hallNobilityHallRoyaltyIncomeTotalAmount", "厅-贵族提成（厅）"),


    HALL_INCOME_TOTAL_AMOUNT("hallIncomeTotalAmount", "厅-总收入"),


    HALL_OFFICIAL_HALL_INCOME_TOTAL_AMOUNT("hallOfficialHallIncomeTotalAmount", "厅-官方厅收入"),

    HALL_FEEDBACK_PERFORMANCE_TOTAL_AMOUNT("hallFeedbackPerformanceTotalAmount", "厅-考核流水"),

    PERSONAL_GET_CANITHDRAW_TOTAL_AMOUNT("personalGetCanWithdrawTotalAmount", "个人-可提现余额"),

    ANCHOR_INCOME_TOTAL_AMOUNT("anchorIncomeTotalAmount", "主播-总收入"),

    ANCHOR_HALL_INCOME_TOTAL_AMOUNT("anchorHallIncomeTotalAmount", "主播-厅收礼收入"),

    ANCHOR_OFFICIAL_HALL_INCOME_TOTAL_AMOUNT("anchorOfficialHallIncomeTotalAmount", "主播-官方厅收礼收入"),

    PERSONAL_GET_TO_BETTLED_TOTAL_AMOUNT("personalGetToBeSettledTotalAmount", "个人-待结算余额"),

    PERSONAL_ANCHOR_INDIVIDUAL_INCOME_TOTAL_AMOUNT("anchorIndividualIncomeTotalAmount","个人-个播收入"),

    PERSONAL_ANCHOR_NOBILITY_ROYALTY_INCOME_TOTAL_AMOUNT("anchorNobilityRoyaltyIncomeTotalAmount","个人-贵族收入"),

    PERSONAL_ANCHOR_PERSONAL_INCOME_TOTAL_AMOUNT("anchorPersonalIncomeTotal","个人-个人收入"),

    QUERY_ANCHOR_PERSONAL_INCOME_DETAIL("queryAnchorPersonalIncomeDetail", "个人-收入账户明细列表-个人收入"),

    QUERY_ANCHOR_INDIVIDUAL_INCOME_DETAIL("queryAnchorIndividualIncomeDetail", "个人-收入账户明细-个播/贵族汇总"),

    ANCHOR_HALL_PROFIT_TOTAL_AMOUNT("anchorHallProfitTotalAmount","个人-考核收入-签约厅收礼收益"),

    ANCHOR_INDIVIDUAL_PROFIT_TOTAL_AMOUNT("anchorIndividualProfitTotalAmount","个人-考核收入-个播收礼收益"),

    ANCHOR_OFFICIAL_HALL_PROFIT_TOTAL_AMOUNT("anchorOfficialHallProfitTotalAmount","个人-考核收入-官方厅收礼收益"),

    ANCHOR_EXAM_INCOME_TOTAL_AMOUNT("anchorExamIncomeTotalAmount","个人-考核收入-收入统计"),

    ANCHOR_EXAM_PROFIT_TOTAL_AMOUNT("anchorExamProfitTotalAmount","个人-考核收入-收益统计"),

    QUERY_ANCHOR_INDIVIDUAL_INCOME_WITH_EARNING_DETAIL("queryAnchorIndividualIncomeWithEarningDetail","个人-个人收入账号明细-带收益"),

    QUERY_ANCHOR_FEEDBACK_INCOME_WITH_EARNING_DETAIL("queryAnchorFeedbackIncomeWithEarningDetail","个人-收入账户明细列表-考核收入"),

    QUERY_HALL_INCOME_DETAIL("queryHallIncomeDetail", "厅-厅收入账户明细汇总"),

    ANCHOR_HALL_INCOME_EARNING_TOTAL_AMOUNT("anchorHallIncomeEarningTotalAmount", "个人-考核收入-签约厅收礼收益"),

    ANCHOR_OFFICIAL_HALL_INCOME_EARNING_TOTAL_AMOUNT("anchorOfficialHallIncomeEarningTotalAmount", "个人-考核收入-官方厅收礼收益")
    ;

    @Getter
    private final String configCode;

    private final String desc;

    PaySettleConfigCodeEnum(String configCode, String desc) {
        this.configCode = configCode;
        this.desc = desc;
    }
}
