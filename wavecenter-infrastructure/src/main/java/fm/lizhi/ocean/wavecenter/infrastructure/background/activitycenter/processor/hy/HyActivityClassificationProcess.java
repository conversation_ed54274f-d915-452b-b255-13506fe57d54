package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.processor.hy;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import org.springframework.stereotype.Component;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityBigClass;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityClassConfig;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.processor.IActivityClassificationProcess;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class HyActivityClassificationProcess implements IActivityClassificationProcess {

    @Override
    public Result<Void> syncBigClassToBiz(ActivityBigClass entity) {
        return RpcResult.success();
    }
    

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.HEI_YE;
    }


    @Override
    public Result<Void> syncClassToBiz(ActivityClassConfig entity) {
        return RpcResult.success();
    }

    @Override
    public Result<Void> deleteBigClass(Long id, String operator) {
        return RpcResult.success();
    }

    @Override
    public Result<Void> deleteClass(Long id, String operator) {
        return RpcResult.success();
    }

}
