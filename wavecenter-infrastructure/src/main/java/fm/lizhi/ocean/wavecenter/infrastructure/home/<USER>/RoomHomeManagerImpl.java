package fm.lizhi.ocean.wavecenter.infrastructure.home.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.common.constants.DateType;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.EchelonBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.IncomeBean;
import fm.lizhi.ocean.wavecenter.api.home.bean.MetricsDataBean;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetRoomKeyDataSummary;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetRoomKeyDataTrendChart;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetRoomMsgAnalysisPerformance;
import fm.lizhi.ocean.wavecenter.base.util.CalculateUtil;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.ThreadConstants;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.infrastructure.common.MetricsUtil;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PaySettleConfigCodeEnum;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataRoomFamilyDay;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataRoomWeek;
import fm.lizhi.ocean.wavecenter.infrastructure.income.manager.PaymentManager;
import fm.lizhi.ocean.wavecenter.service.datacenter.config.DataCenterConfig;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.DataRoomFamilyDayDTO;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.PerformanceInfoDto;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.AssessmentManager;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.RoomDataManager;
import fm.lizhi.ocean.wavecenter.service.home.convert.GuildHomeConvert;
import fm.lizhi.ocean.wavecenter.service.home.convert.RoomHomeConvert;
import fm.lizhi.ocean.wavecenter.service.home.dto.RoomKeyDataSummaryDTO;
import fm.lizhi.ocean.wavecenter.service.home.dto.RoomKeyDataTrendChartDTO;
import fm.lizhi.ocean.wavecenter.service.home.dto.RoomMsgAnalysisPerformanceDTO;
import fm.lizhi.ocean.wavecenter.service.home.manager.RoomHomeManager;
import fm.lizhi.ocean.wavecenter.service.income.manager.IncomeManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 厅管理-首页
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class RoomHomeManagerImpl implements RoomHomeManager {

    @Autowired
    private PaymentManager paymentManager;

    @Autowired
    private AssessmentManager assessmentManager;

    @Autowired
    private RoomDataManager roomDataManager;

    @Autowired
    private DataCenterConfig dataCenterConfig;

    @Autowired
    private IncomeManager incomeManager;


    @Override
    public RoomKeyDataSummaryDTO getRoomKeyDataSummary(RequestGetRoomKeyDataSummary request) {
        Date startDate = DateUtil.formatStrToDate(request.getStartDate(), DateUtil.date_2);
        Date endDate = DateUtil.formatStrToDate(request.getEndDate(), DateUtil.date_2);
        RoomKeyDataSummaryDTO result = new RoomKeyDataSummaryDTO();

        CompletableFuture.allOf(CompletableFuture.runAsync(() -> {
                    // 厅总收入
                    IncomeBean sumIncome = paymentManager.getIncomeBeanByRoom(request.getFamilyId(), request.getRoomId(), startDate, endDate, PaySettleConfigCodeEnum.HALL_INCOME_TOTAL_AMOUNT, DateType.WEEK);
                    result.setSumIncome(RoomHomeConvert.I.buildMetricsDataBean(sumIncome, startDate, endDate));

                }, ThreadConstants.roomHomePool),

                CompletableFuture.runAsync(() -> {
                    // 距离下一梯度&结算比例
                    Optional<PerformanceInfoDto> performanceInfoOpt = assessmentManager.getPerformanceInfo(request.getFamilyId(), request.getRoomId());
                    result.setNextEchelon(performanceInfoOpt.map(e -> EchelonBean.of(e.getNextLevelAmount(), e.getCurrentLevel(), e.getCurrentLevelBonus())).orElse(null));
                }, ThreadConstants.roomHomePool),

                CompletableFuture.runAsync(() -> {
                    // 厅考核收入
                    IncomeBean performanceInfoOpt = paymentManager.getIncomeBeanByRoom(request.getFamilyId(), request.getRoomId(), startDate, endDate, PaySettleConfigCodeEnum.HALL_FEEDBACK_PERFORMANCE_TOTAL_AMOUNT, DateType.WEEK);
                    result.setExaminationFlow(new EchelonBean().setCurrent(performanceInfoOpt.getCurrent()));
                }, ThreadConstants.roomHomePool),

                CompletableFuture.runAsync(() -> {
                    // 厅上麦主播数
                    String signUpGuestPlayerCntMetrics = WcDataRoomWeek.Fields.signUpGuestPlayerCnt;
                    Map<String, IncomeBean> roomWeekKeyIndicators = getRoomWeekKeyIndicator(request.getAppId(), request.getFamilyId(), request.getRoomId(), startDate, endDate, CollUtil.newArrayList(signUpGuestPlayerCntMetrics));
                    result.setSignUpGuestPlayerCnt(RoomHomeConvert.I.buildMetricsDataBean(MapUtil.get(roomWeekKeyIndicators, signUpGuestPlayerCntMetrics, IncomeBean.class), startDate, endDate));
                }, ThreadConstants.roomHomePool), CompletableFuture.runAsync(() -> {
                    //有收入主播数
                    MetricsDataBean incomePlayerCnt = getIncomePlayerCnt(request.getFamilyId(), request.getRoomId(), startDate, endDate);
                    result.setIncomePlayerCnt(incomePlayerCnt);
                }, ThreadConstants.roomHomePool)).join();

        return result;
    }

    @Override
    public List<RoomKeyDataTrendChartDTO> getRoomKeyDataTrendChart(RequestGetRoomKeyDataTrendChart request) {
        Date startDate = DateUtil.formatStrToDate(request.getStartDate(), DateUtil.date_2);
        Date endDate = DateUtil.formatStrToDate(request.getEndDate(), DateUtil.date_2);
        Date startBefore = DateUtil.getDayBefore(startDate, 1);
        Date endBefore = DateUtil.getDayBefore(endDate, 1);
        String allIncomeKey = WcDataRoomFamilyDay.Fields.allIncome;
        String incomeKey = WcDataRoomFamilyDay.Fields.income;
        String signUpGuestPlayerCnt = WcDataRoomFamilyDay.Fields.signUpGuestPlayerCnt;
        String playerAvgIncomeKey = WcDataRoomFamilyDay.Fields.playerAvgIncome;
        String incomePlayerCnt = WcDataRoomFamilyDay.Fields.incomePlayerCnt;
        Double ratioWarning = dataCenterConfig.getRatioWarning();

        // 获取当前
        List<DataRoomFamilyDayDTO> listCurrent = roomDataManager.getRoomDayData(request.getAppId(), request.getFamilyId(), request.getRoomId(), startDate, endDate);
        if (CollUtil.isEmpty(listCurrent)) {
            log.info("getRoomKeyDataTrendChart current is empty, appId={}, familyId={}, roomId={}", request.getAppId(), request.getFamilyId(), request.getRoomId());
            return Collections.emptyList();
        }

        // 获取上周
        List<DataRoomFamilyDayDTO> listBefore = roomDataManager.getRoomDayData(request.getAppId(), request.getFamilyId(), request.getRoomId(), startBefore, endBefore);
        if (CollUtil.isEmpty(listBefore)) {
            log.info("getRoomKeyDataTrendChart before is empty, appId={}, familyId={}, roomId={}", request.getAppId(), request.getFamilyId(), request.getRoomId());
        }
        Map<Integer, DataRoomFamilyDayDTO> mapBefore = listBefore.stream().collect(Collectors.toMap(DataRoomFamilyDayDTO::getStatDateValue, data -> data));

        return listCurrent.stream().map(data -> {
            Map<String, String> baseValueMap = MetricsUtil.convertToMap(DataRoomFamilyDayDTO.class, data);
            Integer key = MyDateUtil.getDateDayValue(DateUtil.getDayBefore(data.getStatDate(), 1));
            DataRoomFamilyDayDTO beforeDate = mapBefore.get(key);
            Map<String, String> beforeValueMap = new HashMap<>();
            if (null != beforeDate) {
                beforeValueMap = MetricsUtil.convertToMap(DataRoomFamilyDayDTO.class, beforeDate);
            }

            RoomKeyDataTrendChartDTO dto = new RoomKeyDataTrendChartDTO();
            dto.setDate(data.getStatDate());
            dto.setSumIncome(GuildHomeConvert.I.buildTrendChartBean(MapUtil.getStr(baseValueMap, allIncomeKey), MapUtil.getStr(beforeValueMap, allIncomeKey), ratioWarning));
            dto.setIncome(GuildHomeConvert.I.buildTrendChartBean(MapUtil.getStr(baseValueMap, incomeKey), MapUtil.getStr(beforeValueMap, incomeKey), ratioWarning));
            dto.setSignUpGuestPlayerCnt(GuildHomeConvert.I.buildTrendChartBean(MapUtil.getStr(baseValueMap, signUpGuestPlayerCnt), MapUtil.getStr(beforeValueMap, signUpGuestPlayerCnt), ratioWarning));
            dto.setPlayerAvgIncome(GuildHomeConvert.I.buildTrendChartBean(MapUtil.getStr(baseValueMap, playerAvgIncomeKey), MapUtil.getStr(beforeValueMap, playerAvgIncomeKey), ratioWarning));
            dto.setIncomePlayerCnt(GuildHomeConvert.I.buildTrendChartBean(MapUtil.getStr(baseValueMap, incomePlayerCnt), MapUtil.getStr(beforeValueMap, incomePlayerCnt), ratioWarning));

            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public RoomMsgAnalysisPerformanceDTO getRoomMsgAnalysisPerformance(RequestGetRoomMsgAnalysisPerformance request) {

        ArrayList<String> valueMetrics = CollUtil.newArrayList(WcDataRoomFamilyDay.Fields.chatUserCnt, WcDataRoomFamilyDay.Fields.replyChatUserCnt, WcDataRoomFamilyDay.Fields.replyChatRate, WcDataRoomFamilyDay.Fields.replyChatUserPerformance, WcDataRoomFamilyDay.Fields.chatEnterRoomUserCnt, WcDataRoomFamilyDay.Fields.chatEnterRoomRate, WcDataRoomFamilyDay.Fields.chatEnterRoomUserPerformance, WcDataRoomFamilyDay.Fields.chatGiftUserCnt, WcDataRoomFamilyDay.Fields.chatGiftRate, WcDataRoomFamilyDay.Fields.chatGiftUserPerformance);

        Date startDate = DateUtil.formatStrToDate(request.getStartDate(), DateUtil.date_2);
        Date endDate = DateUtil.formatStrToDate(request.getEndDate(), DateUtil.date_2);
        Map<String, String> roomDataPre = new HashMap<>();
        Map<String, String> roomDataCurrent = new HashMap<>();

        Integer appId = request.getAppId();
        Long familyId = request.getFamilyId();
        Long roomId = request.getRoomId();

        if (DateType.DAY.getValue().equals(request.getDateType())) {
            Date startBefore = DateUtil.getDayBefore(startDate, 1);
            roomDataPre = roomDataManager.getRoomDayKeyIndicators(appId, familyId, roomId, startBefore, valueMetrics);
            roomDataCurrent = roomDataManager.getRoomDayKeyIndicators(appId, familyId, roomId, startDate, valueMetrics);
        } else if (DateType.WEEK.getValue().equals(request.getDateType())) {
            Date startBefore = DateUtil.getDayBefore(startDate, 7);
            Date endBefore = DateUtil.getDayBefore(endDate, 7);
            roomDataPre = roomDataManager.getRoomWeekKeyIndicators(appId, familyId, roomId, startBefore, endBefore, valueMetrics);
            roomDataCurrent = roomDataManager.getRoomWeekKeyIndicators(appId, familyId, roomId, startDate, endDate, valueMetrics);
        }

        RoomMsgAnalysisPerformanceDTO dto = new RoomMsgAnalysisPerformanceDTO();
        dto.setChatUserCnt(RoomHomeConvert.I.buildMetricsPerformanceBean(MapUtil.getStr(roomDataPre, WcDataRoomFamilyDay.Fields.chatUserCnt), MapUtil.getStr(roomDataCurrent, WcDataRoomFamilyDay.Fields.chatUserCnt), "0", "0", dataCenterConfig.getMaxPerformance()));

        dto.setReplyChatUserCnt(RoomHomeConvert.I.buildMetricsPerformanceBean(MapUtil.getStr(roomDataPre, WcDataRoomFamilyDay.Fields.replyChatUserCnt), MapUtil.getStr(roomDataCurrent, WcDataRoomFamilyDay.Fields.replyChatUserCnt), MapUtil.getStr(roomDataCurrent, WcDataRoomFamilyDay.Fields.replyChatRate), MapUtil.getStr(roomDataCurrent, WcDataRoomFamilyDay.Fields.replyChatUserPerformance), dataCenterConfig.getMaxPerformance()));

        dto.setChatGiftUserCnt(RoomHomeConvert.I.buildMetricsPerformanceBean(MapUtil.getStr(roomDataPre, WcDataRoomFamilyDay.Fields.chatGiftUserCnt), MapUtil.getStr(roomDataCurrent, WcDataRoomFamilyDay.Fields.chatGiftUserCnt), MapUtil.getStr(roomDataCurrent, WcDataRoomFamilyDay.Fields.chatGiftRate), MapUtil.getStr(roomDataCurrent, WcDataRoomFamilyDay.Fields.chatGiftUserPerformance), dataCenterConfig.getMaxPerformance()));

        dto.setChatEnterRoomUserCnt(RoomHomeConvert.I.buildMetricsPerformanceBean(MapUtil.getStr(roomDataPre, WcDataRoomFamilyDay.Fields.chatEnterRoomUserCnt), MapUtil.getStr(roomDataCurrent, WcDataRoomFamilyDay.Fields.chatEnterRoomUserCnt), MapUtil.getStr(roomDataCurrent, WcDataRoomFamilyDay.Fields.chatEnterRoomRate), MapUtil.getStr(roomDataCurrent, WcDataRoomFamilyDay.Fields.chatEnterRoomUserPerformance), dataCenterConfig.getMaxPerformance()));

        return dto;
    }

    /**
     * 获取周指标
     */
    private Map<String, IncomeBean> getRoomWeekKeyIndicator(int appId, Long familyId, Long roomId, Date startDate, Date endDate, List<String> valueMetrics) {

        // 获取本周
        Map<String, String> indicators = roomDataManager.getRoomWeekKeyIndicators(appId, familyId, roomId, startDate, endDate, valueMetrics);

        // 获取上周
        Date startBefore = DateUtil.getDayBefore(startDate, 7);
        Date endBefore = DateUtil.getDayBefore(endDate, 7);
        Map<String, String> indicatorsBefore = roomDataManager.getRoomWeekKeyIndicators(appId, familyId, roomId, startBefore, endBefore, valueMetrics);

        Map<String, IncomeBean> result = new HashMap<>();
        for (String metric : valueMetrics) {
            String pre = indicatorsBefore.get(metric);
            String current = indicators.get(metric);
            String ratio = CalculateUtil.relativeRatio(pre, current);
            result.put(metric, new IncomeBean().setCurrent(current).setPer(pre).setRatio(ratio));
        }

        return result;
    }

    /**
     * 查询有收入主播数当前值和上周值以及环比
     *
     * @param familyId  家族ID
     * @param roomId    厅ID
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return 结果
     */
    private MetricsDataBean getIncomePlayerCnt(Long familyId, Long roomId, Date startDate, Date endDate) {
        int playerPayCount = incomeManager.getPlayerPayCountByRoom(familyId, roomId, startDate, endDate);
        int playerPayCountBefore = incomeManager.getPlayerPayCountByRoom(familyId, roomId, DateUtil.getDayBefore(startDate, 7), DateUtil.getDayBefore(endDate, 7));
        String current = String.valueOf(playerPayCount);
        String pre = String.valueOf(playerPayCountBefore);
        String ratio = CalculateUtil.relativeRatio(pre, current);

        return new MetricsDataBean()
                .setCurrent(Double.valueOf(current))
                .setPre(Double.valueOf(pre))
                .setRatio(ratio)
                .setStartTime(startDate.getTime())
                .setEndTime(endDate.getTime());
    }
}
