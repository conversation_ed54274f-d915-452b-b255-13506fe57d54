package fm.lizhi.ocean.wavecenter.infrastructure.income.remote.hy;


import com.alibaba.fastjson.JSONObject;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.*;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.util.ThreadUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PaySettleConfigCodeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.income.convert.IncomeInfraConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.income.datastore.mapper.HyPlayerIncomeMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.income.manager.AnchorIncomeManager;
import fm.lizhi.ocean.wavecenter.infrastructure.income.remote.PersonalIncomeRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.hy.HyUserFamilyRemote;
import fm.lizhi.pay.settle.protocol.creativecenter.CreatorDataQueryProto;
import fm.lizhi.pay.settle.settleenum.creativecenter.DirectionEnum;
import fm.lizhi.pay.settle.settleenum.creativecenter.PeriodTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.Supplier;

@Slf4j
@Component
public class HyPersonalIncomeRemote implements PersonalIncomeRemote {


    @Autowired
    private AnchorIncomeManager anchorIncomeManager;

    @Autowired
    private HyPlayerIncomeMapper hyPlayerIncomeMapper;


    @Autowired
    private HyUserFamilyRemote hyUserFamilyRemote;

    /**
     * 多线程调用线程池
     */
    private final ExecutorService executorService = ThreadUtils.getTtlExecutors("hy-personal-payment", 50, 50);


    @Override
    public PersonalIncomeDetailSumBean getRevenueIncomeDetailSum(GetPersonalRevenueIncomeDetailParamBean paramBean) {
        Pair<Integer, Integer> incomePair = anchorIncomeManager.queryAnchorIncomeWithEarningDetailTotal(paramBean.getAppId(), paramBean.getUserId(), paramBean.getStartDate(), paramBean.getEndDate(),
                DirectionEnum.ALL, PaySettleConfigCodeEnum.QUERY_ANCHOR_FEEDBACK_INCOME_WITH_EARNING_DETAIL, null);
        return new PersonalIncomeDetailSumBean()
                .setIncome(incomePair.getKey()).setRevenueAmount(incomePair.getValue());
    }

    @Override
    public PageBean<PersonalIncomeDetailBean> getRevenueIncomeDetail(GetPersonalRevenueIncomeDetailParamBean paramBean) {
        CreatorDataQueryProto.ResponseQueryAnchorIncomeWithEarningDetail result = anchorIncomeManager.queryAnchorIncomeDetailWithEarningDetail(paramBean.getAppId(), paramBean.getUserId(),
                DirectionEnum.ALL, paramBean.getIncomeType(), paramBean.getStartDate(), paramBean.getEndDate(), PaySettleConfigCodeEnum.QUERY_ANCHOR_FEEDBACK_INCOME_WITH_EARNING_DETAIL, paramBean.getPageNo(), paramBean.getPageSize(), paramBean.getFlushTime());
        if (null == result) {
            return PageBean.empty();
        }
        List<PersonalIncomeDetailBean> beans = IncomeInfraConvert.I.queryAnchorIncomeDetailWithEarningResponses2personalIncomeDetailBeans(result.getDataList());

        log.info("hy getRevenueIncomeDetail req={},total={},beans={}", result.getTotalCount(), JSONObject.toJSONString(paramBean), JSONObject.toJSONString(beans));
        return PageBean.of(result.getTotalCount(), beans);
    }


    @Override
    public PlayerSumDataBean getPlayerSumData(String tenantCode, long familyId, long userId) {
        return new PlayerSumDataBean();
    }


    List<PaySettleConfigCodeEnum> PlayerRevenueConfigCodeList = Arrays.asList(
            PaySettleConfigCodeEnum.ANCHOR_EXAM_INCOME_TOTAL_AMOUNT,
            PaySettleConfigCodeEnum.ANCHOR_EXAM_PROFIT_TOTAL_AMOUNT,
            PaySettleConfigCodeEnum.ANCHOR_OFFICIAL_HALL_INCOME_TOTAL_AMOUNT,
            PaySettleConfigCodeEnum.ANCHOR_HALL_INCOME_TOTAL_AMOUNT,
            PaySettleConfigCodeEnum.PERSONAL_ANCHOR_INDIVIDUAL_INCOME_TOTAL_AMOUNT,
            PaySettleConfigCodeEnum.ANCHOR_OFFICIAL_HALL_PROFIT_TOTAL_AMOUNT,
            PaySettleConfigCodeEnum.ANCHOR_HALL_PROFIT_TOTAL_AMOUNT,
            PaySettleConfigCodeEnum.ANCHOR_INDIVIDUAL_PROFIT_TOTAL_AMOUNT
    );


    @Override
    public PlayerRevenueSumDataBean getPlayerRevenueSumData(String tenantCode, long familyId, long userId) {
        PlayerRevenueSumDataBean playerRevenueSumDataBean = new PlayerRevenueSumDataBean();
        Supplier<PlayerRevenueSumTimeBean> dayBalanceSupplier = () -> anchorIncomeManager.getPlayerRevenueSumTimeBean(PlayerRevenueConfigCodeList,tenantCode,familyId,userId,PeriodTypeEnum.TODAY);
        Supplier<PlayerRevenueSumTimeBean> weekBalanceSupplier = () -> anchorIncomeManager.getPlayerRevenueSumTimeBean(PlayerRevenueConfigCodeList,tenantCode,familyId,userId,PeriodTypeEnum.CURRENT_WEEK);
        Supplier<PlayerRevenueSumTimeBean> monthBalanceSupplier = () -> anchorIncomeManager.getPlayerRevenueSumTimeBean(PlayerRevenueConfigCodeList,tenantCode,familyId,userId,PeriodTypeEnum.CURRENT_MONTH);
        // 异步运行所有余额的获取并处理结果
        CompletableFuture.allOf(
                CompletableFuture.supplyAsync(dayBalanceSupplier,executorService).thenAccept(playerRevenueSumDataBean::setDay),
                CompletableFuture.supplyAsync(weekBalanceSupplier,executorService).thenAccept(playerRevenueSumDataBean::setWeek),
                CompletableFuture.supplyAsync(monthBalanceSupplier,executorService).thenAccept(playerRevenueSumDataBean::setMonth)
        ).join();

        return playerRevenueSumDataBean;
    }

    @Override
    public String getIncomeRatio(long njId) {
        UserInFamilyBean userInFamily = hyUserFamilyRemote.getUserInFamily(njId);
        if (userInFamily.getNjId() == null || userInFamily.getNjId() <= 0) {
            return "0";
        }
        Long playerIncome = hyPlayerIncomeMapper.getPlayerIncome(userInFamily.getNjId());
        return playerIncome != null ? String.valueOf(playerIncome) : String.valueOf(0);
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.HEI_YE;
    }
}
