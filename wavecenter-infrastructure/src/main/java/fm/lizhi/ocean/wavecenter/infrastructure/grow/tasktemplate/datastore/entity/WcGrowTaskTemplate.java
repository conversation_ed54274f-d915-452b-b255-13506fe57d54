package fm.lizhi.ocean.wavecenter.infrastructure.grow.tasktemplate.datastore.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 成长体系任务模板
 *
 * @date 2025-06-09 06:30:46
 */
@Table(name = "`wavecenter_grow_task_template`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcGrowTaskTemplate {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 模板code
     */
    @Column(name= "`template_code`")
    private String templateCode;

    /**
     * 版本号
     */
    @Column(name= "`version`")
    private Long version;

    /**
     * 状态: 1=启用,0=禁用
     */
    @Column(name= "`status`")
    private Integer status;

    /**
     * 业务 ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 服务部署环境, TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 创建人
     */
    @Column(name= "`create_user`")
    private String createUser;

    /**
     * 是否删除，0：未删除，1：删除
     */
    @Column(name= "`deleted`")
    private Boolean deleted;

    /**
     * 条件json
     */
    @Column(name= "`condition_json`")
    private String conditionJson;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", templateCode=").append(templateCode);
        sb.append(", version=").append(version);
        sb.append(", status=").append(status);
        sb.append(", appId=").append(appId);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", createUser=").append(createUser);
        sb.append(", deleted=").append(deleted);
        sb.append(", conditionJson=").append(conditionJson);
        sb.append("]");
        return sb.toString();
    }
}