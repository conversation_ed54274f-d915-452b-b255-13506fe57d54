package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.dao;

import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityAiResultRate;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.mapper.ActivityAiResultRateMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
@Slf4j
public class ActivityAiResultRateDao {

    @Autowired
    private ActivityAiResultRateMapper activityAiResultRateMapper;

    /**
     * 保存AI结果评分
     *
     * @param entity ActivityAiResultRate 实体
     * @return 是否保存成功
     */
    public boolean saveActivityAiResultRate(ActivityAiResultRate entity) {
        if (entity == null) {
            log.warn("保存失败，参数为空");
            return false;
        }
        return activityAiResultRateMapper.insert(entity) > 0;
    }
} 