package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.manager;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert.ActivityTemplateStatusTaskConvert;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityTemplateStatusTask;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityTemplateStatusTaskExample;
import fm.lizhi.ocean.wavecenter.datastore.background.mapper.ActivityTemplateStatusTaskMapper;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.constants.TemplateStatusTaskStatusEnum;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityTemplateStatusTaskManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityTemplateStatusTaskDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/8 17:59
 */
@Component
public class ActivityTemplateStatusTaskManagerImpl implements ActivityTemplateStatusTaskManager {

    @Autowired
    private ActivityTemplateStatusTaskMapper statusTaskMapper;

    @Override
    public List<ActivityTemplateStatusTaskDTO> getWaitingTask(Integer taskNums) {
        ActivityTemplateStatusTaskExample example = new ActivityTemplateStatusTaskExample();
        example.setOrderByClause("create_time");
        example.createCriteria()
                .andExecuteTimeLessThanOrEqualTo(new Date())
                .andDeletedEqualTo(0)
                .andTaskStatusEqualTo(TemplateStatusTaskStatusEnum.WAITING.getValue());

        PageList<ActivityTemplateStatusTask> list = statusTaskMapper.pageByExample(example, 1, taskNums);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return ActivityTemplateStatusTaskConvert.I.entitys2DTOs(list);
    }

    @Override
    public void updateTaskStatus(Long taskId, TemplateStatusTaskStatusEnum taskStatus) {
        ActivityTemplateStatusTaskExample example = new ActivityTemplateStatusTaskExample();
        example.createCriteria().andIdEqualTo(taskId);
        ActivityTemplateStatusTask entity = new ActivityTemplateStatusTask();
        entity.setTaskStatus(taskStatus.getValue());
        entity.setUpdateTime(new Date());
        statusTaskMapper.updateByExample(entity, example);
    }
}
