package fm.lizhi.ocean.wavecenter.infrastructure.sign.manager;

import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.bean.AdminSignPlayerRecordBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.CancelPlayerRecordBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.NjAndPlayerContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.TodoSignPlayerBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.OperateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import fm.lizhi.ocean.wavecenter.api.sign.request.*;
import fm.lizhi.ocean.wavecenter.api.sign.response.*;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.convert.SignInfraConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.IContractRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.INonContractRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.request.RequestInviteCancel;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.request.RequestInviteSign;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.response.ResponseInviteCancel;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.response.ResponseInviteSign;
import fm.lizhi.ocean.wavecenter.service.sign.dto.OperateSignDTO;
import fm.lizhi.ocean.wavecenter.service.sign.dto.QueryNonContractDTO;
import fm.lizhi.ocean.wavecenter.service.sign.manager.NonContractManager;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/10/10 14:12
 */
@Component
public class NonContractManagerImpl implements NonContractManager {

    @Autowired
    private INonContractRemote iNonContractRemote;
    @Autowired
    private IContractRemote iContractRemote;
    @Autowired
    private UserManager userManager;

    @Override
    public Optional<NjAndPlayerContractBean> queryLastCancel(Long userId) {
        return iNonContractRemote.queryUserLast(userId, ContractTypeEnum.CANCEL.getCode());
    }

    @Override
    public Optional<NjAndPlayerContractBean> queryLastSign(Long userId) {
        return iNonContractRemote.queryUserLast(userId, ContractTypeEnum.SIGN.getCode());
    }

    @Override
    public ResponseUserApplyPlayer userApplyPlayer(RequestUserApplyPlayer request) {
        ResponseInviteSign res = iNonContractRemote.inviteSign(RequestInviteSign.builder()
                .targetUserId(request.getTargetUserId())
                .curUserId(request.getCurUserId())
                .opRole(RoleEnum.PLAYER)
                .build());

        ResponseUserApplyPlayer response = new ResponseUserApplyPlayer().setMsg(res.getMsg()).setCode(res.getCode());

        //查询发起的签约记录
        PageBean<NjAndPlayerContractBean> pageBean = iNonContractRemote.queryList(QueryNonContractDTO.builder()
                .njId(request.getTargetUserId())
                .userId(request.getCurUserId())
                .type(ContractTypeEnum.SIGN)
                .status(SignRelationEnum.WAIT_SIGN)
                .build());
        if (CollectionUtils.isNotEmpty(pageBean.getList())) {
            response.setContractId(pageBean.getList().get(0).getContractId());
        }

        return response;
    }

    @Override
    public ResponseAdminInviteUser adminInviteUser(RequestAdminInviteUser request) {
        ResponseInviteSign res = iNonContractRemote.inviteSign(RequestInviteSign.builder()
                .targetUserId(request.getTargetUserId())
                .curUserId(request.getCurUserId())
                .opRole(RoleEnum.ROOM)
                .build());

        ResponseAdminInviteUser response = new ResponseAdminInviteUser()
                .setMsg(res.getMsg()).setCode(res.getCode());

        //查询发起的签约记录
        PageBean<NjAndPlayerContractBean> pageBean = iNonContractRemote.queryList(QueryNonContractDTO.builder()
                .njId(request.getCurUserId())
                .userId(request.getTargetUserId())
                .type(ContractTypeEnum.SIGN)
                .status(SignRelationEnum.WAIT_SIGN)
                .build());
        if (CollectionUtils.isNotEmpty(pageBean.getList())) {
            response.setContractId(pageBean.getList().get(0).getContractId());
        }

        return response;
    }

    @Override
    public boolean isInChangeCompany(Long curUserId) {
        return iNonContractRemote.isInChangeCompany(curUserId);
    }

    @Override
    public boolean isInChangeCompanyPreparedStage(Long curUserId) {
        return iNonContractRemote.isInChangeCompanyPreparedStage(curUserId);
    }

    @Override
    public Pair<Integer, String> checkInviteSignLimit(Long userId, Long njId, RoleEnum userType, RoleEnum checkType) {
        return iNonContractRemote.checkInviteSignLimit(userId, njId, userType, checkType);
    }

    @Override
    public List<NjAndPlayerContractBean> queryUserSign(Long userId, List<ContractTypeEnum> types, List<SignRelationEnum> status) {
        return iNonContractRemote.queryUserSign(userId, types, status);
    }

    @Override
    public OperateSignDTO operateSign(Long playSignId, Long curUserId, ContractTypeEnum type, RoleEnum role, OperateTypeEnum operateType) {
        return iNonContractRemote.operateSign(playSignId, curUserId, type, role, operateType);
    }

    @Override
    public Pair<Integer, String> checkCanSignForConfirm(Long playSignId, Long curUserId, RoleEnum opRole, RoleEnum checkRole) {
        return iNonContractRemote.checkCanSignForConfirm(playSignId, curUserId, opRole, checkRole);
    }

    @Override
    public PageBean<NjAndPlayerContractBean> queryList(QueryNonContractDTO paramDTO) {
        return iNonContractRemote.queryList(paramDTO);
    }

    @Override
    public ResponsePlayerApplyCancel playerApplyCancel(RequestPlayerApplyCancel request) {
        ResponseInviteCancel result = iNonContractRemote.inviteCancel(RequestInviteCancel.builder()
                .curUserId(request.getCurUserId())
                .opRole(RoleEnum.PLAYER)
                .playSignId(request.getPlayerSignId())
                .build());

        ResponsePlayerApplyCancel response = new ResponsePlayerApplyCancel()
                .setCode(result.getCode()).setMsg(result.getMsg());

        PageBean<NjAndPlayerContractBean> pageBean = iNonContractRemote.queryList(QueryNonContractDTO.builder()
                .parentId(request.getPlayerSignId())
                .build());
        if (CollectionUtils.isNotEmpty(pageBean.getList())) {
            response.setContractId(pageBean.getList().get(0).getContractId());
        }

        return response;
    }

    @Override
    public ResponseAdminApplyCancelPlayer adminApplyCancelPlayer(RequestAdminApplyCancelPlayer request) {
        ResponseInviteCancel result = iNonContractRemote.inviteCancel(RequestInviteCancel.builder()
                .curUserId(request.getCurUserId())
                .opRole(RoleEnum.ROOM)
                .playSignId(request.getPlayerSignId())
                .build());

        ResponseAdminApplyCancelPlayer response = new ResponseAdminApplyCancelPlayer()
                .setCode(result.getCode()).setMsg(result.getMsg());

        PageBean<NjAndPlayerContractBean> pageBean = iNonContractRemote.queryList(QueryNonContractDTO.builder()
                .parentId(request.getPlayerSignId())
                .build());
        if (CollectionUtils.isNotEmpty(pageBean.getList())) {
            response.setContractId(pageBean.getList().get(0).getContractId());
        }

        return response;
    }

    @Override
    public Pair<Integer, String> reviewCancel(Long playSignId, Long curUserId, OperateTypeEnum operateType) {
        return iNonContractRemote.reviewCancel(playSignId, curUserId, operateType);
    }

    @Override
    public ResponseWithdrawCancel withdrawCancel(Long playerSignId, Long curUserId, RoleEnum opUserRole) {
        return iNonContractRemote.withdrawCancel(playerSignId, curUserId, opUserRole);
    }

    @Override
    public Map<Long, Integer> countNjSign(List<Long> njIds) {
        return iNonContractRemote.countSignPlayerByRooms(njIds);
    }

    @Override
    public List<TodoSignPlayerBean> todoPlayerList(RequestFamilyTodoPlayerList request) {
        //查询所有厅主
        List<Long> njIds = iContractRemote.queryAllSignNjId(request.getFamilyId());
        if (CollectionUtils.isEmpty(njIds)) {
            return Collections.emptyList();
        }

        //查询待审批列表
        PageBean<NjAndPlayerContractBean> pageList = iNonContractRemote.queryList(QueryNonContractDTO.builder()
                .type(ContractTypeEnum.CANCEL)
                .njIds(njIds)
                .status(SignRelationEnum.SIGN_CONFIRM)
                .pageNo(1)
                .pageSize(50)
                .build()
        );
        List<NjAndPlayerContractBean> list = pageList.getList();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return SignInfraConvert.I.njPlayerContractBeans2TodoBeans(list);
    }

    @Override
    public List<TodoSignPlayerBean> adminTodoList(RequestAdminTodoList request) {
        PageBean<NjAndPlayerContractBean> pageList = iNonContractRemote.queryList(QueryNonContractDTO.builder()
                .njId(request.getNjId())
                .status(SignRelationEnum.WAIT_SIGN)
                .status(SignRelationEnum.SIGN_CONFIRM)
                .pageNo(1).pageSize(50)
                .build()
        );
        return SignInfraConvert.I.njPlayerContractBeans2TodoBeans(pageList.getList());
    }

    @Override
    public PageBean<CancelPlayerRecordBean> queryFamilyCancelPlayerList(RequestQueryRoomSignList request) {

        //查询所有厅主
        List<Long> njIds = iContractRemote.queryAllSignNjId(request.getFamilyId());
        if (CollectionUtils.isEmpty(njIds)) {
            return PageBean.empty();
        }

        QueryNonContractDTO.QueryNonContractDTOBuilder paramBuilder = QueryNonContractDTO.builder()
                .type(ContractTypeEnum.CANCEL)
                .njIds(njIds)
                .status(request.getStatus())
                .pageNo(request.getPageNo())
                .pageSize(request.getPageSize());

        if (StringUtils.isNotBlank(request.getUserBand())) {
            //所有厅或者用户
            Long userId = userManager.getUserIdByBand(request.getUserBand());
            if (userId == null) {
                LogContext.addResLog("userId is null");
                return PageBean.empty();
            }
            paramBuilder.userOrNjId(userId);
        }

        //查询待审批列表
        PageBean<NjAndPlayerContractBean> pageList = iNonContractRemote.queryList(paramBuilder.build());
        if (CollectionUtils.isEmpty(pageList.getList())) {
            return PageBean.empty();
        }

        return PageBean.of(pageList.getTotal(), SignInfraConvert.I.njPlayerContractBeans2RecordBeans(pageList.getList()));
    }

    @Override
    public PageBean<AdminSignPlayerRecordBean> querySignPlayerList(RequestQuerySignPlayerList request) {
        QueryNonContractDTO.QueryNonContractDTOBuilder paramBuilder = QueryNonContractDTO.builder()
                .type(request.getType())
                .njId(request.getNjId())
                .status(request.getStatus())
                .pageNo(request.getPageNo())
                .pageSize(request.getPageSize());

        if (StringUtils.isNotBlank(request.getUserBand())) {
            //所有厅或者用户
            Long userId = userManager.getUserIdByBand(request.getUserBand());
            if (userId == null) {
                LogContext.addResLog("search user not exist. userBand={}", request.getUserBand());
                return PageBean.empty();
            }
            paramBuilder.userId(userId);
        }

        if (request.getStatus() == SignRelationEnum.WAIT_SIGN) {
            //待签署需要包括签署中的
            paramBuilder.status(SignRelationEnum.SIGNING);
        }

        //查询待审批列表
        PageBean<NjAndPlayerContractBean> pageList = iNonContractRemote.queryList(paramBuilder.build());
        if (CollectionUtils.isEmpty(pageList.getList())) {
            return PageBean.empty();
        }

        return PageBean.of(pageList.getTotal(), SignInfraConvert.I.njPlayerContractBeans2AdminRecordBeans(pageList.getList()));
    }

    @Override
    public Integer countPlayerSignNum(Long njId) {
        return iNonContractRemote.countPlayerSignNum(njId);
    }

    @Override
    public Optional<Long> getPlayerCurSignNj(Long userId) {
        return iNonContractRemote.getPlayerCurSignNj(userId);
    }

    @Override
    public Optional<Long> getPlayerLastCancelSign(Long userId) {
        return iNonContractRemote.getPlayerLastCancelSign(userId);
    }

    @Override
    public Date getPlayerSignTime(Long playerId, Long njId) {
        QueryNonContractDTO.QueryNonContractDTOBuilder builder = QueryNonContractDTO.builder()
                .userId(playerId)
                .njId(njId)
                .status(SignRelationEnum.SIGN_SUCCESS)
                .type(ContractTypeEnum.SIGN);
        PageBean<NjAndPlayerContractBean> pageBean = queryList(builder.build());
        if (CollectionUtils.isEmpty(pageBean.getList())) {
            return null;
        }
        return pageBean.getList().get(0).getStartTime();
    }

    @Override
    public boolean isPlayerFirstSignNjFamily(Long playerId, Long njId, Long familyId) {
        // 查询公会下所有历史签约厅
        List<Long> historyNjIds = iContractRemote.getHistoryNjIds(familyId, 0L, 100);
        while (CollectionUtils.isNotEmpty(historyNjIds)) {
            for (Long historyNjId : historyNjIds) {
                if (historyNjId.equals(njId)) {
                    continue;
                }
                // 是否签约过
                if (isHasHistoryNjSign(playerId, historyNjId)) {
                    return false;
                }
            }
            historyNjIds = iContractRemote.getHistoryNjIds(familyId, historyNjIds.get(historyNjIds.size() - 1), 100);
        }
        return true;
    }

    /**
     * 主播和厅是否签约过
     * @param playerId
     * @param njId
     * @return
     */
    private boolean isHasHistoryNjSign(Long playerId, Long njId) {
        QueryNonContractDTO.QueryNonContractDTOBuilder builder = QueryNonContractDTO.builder()
                .userId(playerId)
                .njId(njId)
                .status(SignRelationEnum.SIGN_SUCCESS)
                .status(SignRelationEnum.STOP_CONTRACT)
                .type(ContractTypeEnum.SIGN);
        PageBean<NjAndPlayerContractBean> pageBean = queryList(builder.build());
        return CollectionUtils.isNotEmpty(pageBean.getList());
    }

}
