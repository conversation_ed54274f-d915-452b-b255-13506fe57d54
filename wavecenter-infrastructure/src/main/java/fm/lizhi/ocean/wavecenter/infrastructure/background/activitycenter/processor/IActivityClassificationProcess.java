package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.processor;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityBigClass;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityClassConfig;
import fm.lizhi.ocean.wavecenter.base.processor.BusinessEnvAwareProcessor;

public interface IActivityClassificationProcess extends BusinessEnvAwareProcessor {

    /**
     * 同步大类到业务
     *
     * @return 结果
     */
    Result<Void> syncBigClassToBiz(ActivityBigClass entity);

    /**
     * 同步二级分类到业务
     *
     * @return 结果
     */
    Result<Void> syncClassToBiz(ActivityClassConfig entity);

    /**
     * 删除一级分类
     * @param id 一级分类id
     * @param operator 操作人
     * @return 结果
     */
    Result<Void> deleteBigClass(Long id, String operator);

    /**
     * 删除二级分类
     * @param id 二级分类id
     * @param operator 操作人
     * @return 结果
     */
    Result<Void> deleteClass(Long id, String operator);

    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return IActivityClassificationProcess.class;
    }

}
