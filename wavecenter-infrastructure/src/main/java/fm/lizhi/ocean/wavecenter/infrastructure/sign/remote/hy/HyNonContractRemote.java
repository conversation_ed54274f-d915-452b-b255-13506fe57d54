package fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.hy;

import fm.hy.family.api.FamilySignService;
import fm.hy.family.api.PlayerSignService;
import fm.hy.family.bean.player.sign.PlaySignStatus;
import fm.hy.family.protocol.FamilySignServiceProto;
import fm.hy.family.protocol.PlayerSignServiceProto;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.bean.NjAndPlayerContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.OperateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseWithdrawCancel;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.convert.SignInfraConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity.HyPlayerSign;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity.HyPlayerSignExample;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.mapper.HyPlayerSignMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.po.CountSignPlayerPo;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.po.QueryPlayerSignPo;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.INonContractRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.request.RequestInviteCancel;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.request.RequestInviteSign;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.response.ResponseInviteCancel;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.response.ResponseInviteSign;
import fm.lizhi.ocean.wavecenter.service.sign.dto.OperateSignDTO;
import fm.lizhi.ocean.wavecenter.service.sign.dto.QueryNonContractDTO;
import fm.lizhi.ocean.wavecenter.service.user.convert.RoleConvert;
import hy.fm.lizhi.pp.util.utils.ChangeCompanyObjectUtil;
import org.apache.commons.lang3.tuple.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/10 14:16
 */
@Slf4j
@Component
public class HyNonContractRemote implements INonContractRemote {

    @Autowired
    private HyPlayerSignMapper playerSignMapper;
    @Autowired
    private PlayerSignService playerSignService;
    @Autowired
    private FamilySignService familySignService;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.HEI_YE;
    }

    @Override
    public Optional<NjAndPlayerContractBean> queryUserLast(Long userId, String type) {
        HyPlayerSignExample example = new HyPlayerSignExample();
        example.setOrderByClause("create_time desc");
        HyPlayerSignExample.Criteria criteria = example.createCriteria();
        criteria.andUserIdEqualTo(userId);
        criteria.andTypeEqualTo(type);

        PageList<HyPlayerSign> pageList = playerSignMapper.pageByExample(example, 1, 1);
        if (CollectionUtils.isEmpty(pageList)) {
            return Optional.empty();
        }

        HyPlayerSign hyPlayerSign = pageList.get(0);
        NjAndPlayerContractBean njAndPlayerContractBean = new NjAndPlayerContractBean();
        njAndPlayerContractBean.setContractId(hyPlayerSign.getId());
        njAndPlayerContractBean.setStatus(SignInfraConvert.I.hySignStatusTrans(hyPlayerSign.getStatus()));
        njAndPlayerContractBean.setType(type);
        njAndPlayerContractBean.setCreateUser("ADMIN".equals(hyPlayerSign.getUserType()) ? RoleEnum.ROOM.getRoleCode() : RoleEnum.PLAYER.getRoleCode());

        return Optional.of(njAndPlayerContractBean);
    }

    @Override
    public boolean isInChangeCompany(Long curUserId) {
        return ChangeCompanyObjectUtil.isInChangeCompanyObjectStopLiveStage(new Date(), curUserId);
    }

    @Override
    public boolean isInChangeCompanyPreparedStage(Long curUserId) {
        return ChangeCompanyObjectUtil.isInChangeCompanyObjectPreparedStage(new Date(), curUserId);
    }

    @Override
    public Pair<Integer, String> checkInviteSignLimit(Long userId, Long njId, RoleEnum userType, RoleEnum checkType) {
        Result<PlayerSignServiceProto.ResponseCheckInviteSignLimit> result = playerSignService.checkInviteSignLimit(njId, userId
                , RoleConvert.I.waveRole2HyRoleCode(userType.getRoleCode())
                , RoleConvert.I.waveRole2HyRoleCode(checkType.getRoleCode())
        );
        if (RpcResult.isFail(result)) {
            log.error("hy checkInviteSignLimit fail. targetUserId={},curUserId={},rCode={}", njId, userId, result.rCode());
            //下游不管成功还是失败都是返回rCode=0，除非服务或者网路问题，所以返回-1就好，直接忽略
            return Pair.of(-1, "");
        }

        PlayerSignServiceProto.ResponseCheckInviteSignLimit target = result.target();
        //如果检查通过，下游返回code=0
        return Pair.of(target.getCode(), target.getMessage());
    }

    @Override
    public ResponseInviteSign inviteSign(RequestInviteSign request) {
        Result<PlayerSignServiceProto.ResponseInviteSign> result = playerSignService.inviteSign(request.getCurUserId(), request.getTargetUserId(), RoleConvert.I.waveRole2HyRoleCode(request.getOpRole().getRoleCode()));
        if (RpcResult.isFail(result)) {
            log.error("hy inviteSign fail. curUserId={},targetUserId={},rCode={}", request.getCurUserId(), request.getTargetUserId(), result.rCode());
            return new ResponseInviteSign().setCode(-1);
        }
        return new ResponseInviteSign()
                .setCode(result.target().getCode())
                .setMsg(result.target().getMessage());
    }

    @Override
    public ResponseInviteCancel inviteCancel(RequestInviteCancel request) {
        Result<PlayerSignServiceProto.ResponseInviteCancel> result = playerSignService.inviteCancel(request.getPlaySignId(), RoleConvert.I.waveRole2HyRoleCode(request.getOpRole().getRoleCode()), request.getCurUserId());
        if (RpcResult.isFail(result)) {
            log.error("hy inviteCancel fail. rCode={},request={}", result.rCode(), JsonUtil.dumps(request));
            return new ResponseInviteCancel().setCode(-1);
        }

        return new ResponseInviteCancel().setCode(result.target().getCode()).setMsg(result.target().getMessage());
    }

    @Override
    public List<NjAndPlayerContractBean> queryUserSign(Long userId, List<ContractTypeEnum> types, List<SignRelationEnum> status) {
        return Collections.emptyList();
    }

    @Override
    public OperateSignDTO operateSign(Long playSignId, Long curUserId, ContractTypeEnum type, RoleEnum role, OperateTypeEnum operateType) {
        OperateSignDTO res = new OperateSignDTO().setCode(0);

        Result<PlayerSignServiceProto.ResponseSign> result = playerSignService.sign(playSignId, curUserId, type.getCode(), RoleConvert.I.waveRole2HyRoleCode(role.getRoleCode()));
        int rCode = result.rCode();
        LogContext.addResLog("playerSignService.sign rCode={}", rCode);
        if (rCode == PlayerSignService.SIGN_EXIST) {
            if (type == ContractTypeEnum.SIGN) {
                if (role == RoleEnum.ROOM) {
                    return res.setCode(-1).setMsg("该用户已有签约身份，无法操作哦~");
                }
                return res.setCode(-1).setMsg("你已有签约身份，无法操作哦~");
            } else {
                return res.setCode(-1).setMsg("已解约");
            }
        }

        if (rCode == PlayerSignService.SIGN_ILLEGAL_PARAMS) {
            return res.setCode(-1).setMsg("申请已取消或不存在，请刷新重试");
        }

        if (RpcResult.isFail(result)) {
            log.error("hy sign fail. playSignId={},curUserId={},type={},role={},rCode={}", playSignId, curUserId, type.getCode(), role.getRoleCode(), rCode);
            return new OperateSignDTO().setCode(-1);
        }

        return res;
    }

    @Override
    public Pair<Integer, String> checkCanSignForConfirm(Long playSignId, Long curUserId, RoleEnum opRole, RoleEnum checkRole) {
        Result<PlayerSignServiceProto.ResponseCheckCanSignForComfirm> result = playerSignService.checkCanSignForComfirm(playSignId, curUserId
                , RoleConvert.I.waveRole2HyRoleCode(opRole.getRoleCode())
                , RoleConvert.I.waveRole2HyRoleCode(checkRole.getRoleCode()));
        if (RpcResult.isFail(result)) {
            log.error("hy checkCanSignForConfirm fail. playSignId={},curUserId={},opRole={},checkRole={}", playSignId, curUserId, opRole.getRoleCode(), checkRole.getRoleCode());
            return Pair.of(-1, "检验签约限制失败");
        }
        return Pair.of(result.target().getCode(), result.target().getMessage());
    }

    @Override
    public PageBean<NjAndPlayerContractBean> queryList(QueryNonContractDTO paramDTO) {
        QueryPlayerSignPo entityParam = new QueryPlayerSignPo();
        if (paramDTO.getContractId() != null) {
            entityParam.setContractId(paramDTO.getContractId());
        }
        if (CollectionUtils.isNotEmpty(paramDTO.getNjIds())) {
            entityParam.setNjIds(paramDTO.getNjIds());
        }
        if (CollectionUtils.isNotEmpty(paramDTO.getTypes())) {
            entityParam.setTypes(paramDTO.getTypes().stream().map(ContractTypeEnum::getCode).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(paramDTO.getStatuses())) {
            entityParam.setStatuses(paramDTO.getStatuses().stream().map(SignInfraConvert.I::waveSignStatus2hy).collect(Collectors.toList()));
            if (paramDTO.getStatuses().contains(SignRelationEnum.SIGN_FAILED)) {
                entityParam.getStatuses().add(PlaySignStatus.SIGN_FAIL.getCode());
            }
        }
        if (paramDTO.getUserOrNjId() != null) {
            entityParam.setUserOrNjId(paramDTO.getUserOrNjId());
        }
        if (paramDTO.getUserId() != null) {
            entityParam.setUserId(paramDTO.getUserId());
        }
        if (CollectionUtils.isNotEmpty(paramDTO.getContractIdLists())) {
            entityParam.setContractIdLists(paramDTO.getContractIdLists());
        }
        if (paramDTO.getParentId() != null) {
            entityParam.setParentId(paramDTO.getParentId());
        }
        entityParam.setDescCreateTime(paramDTO.isDescCreateTime());

        PageList<HyPlayerSign> pageList = playerSignMapper.pageByEntity(entityParam, paramDTO.getPageNo(), paramDTO.getPageSize());
        return PageBean.of(pageList.getTotal(), SignInfraConvert.I.hyPlayerSignPos2ContractBeans(pageList));
    }

    @Override
    public Pair<Integer, String> reviewCancel(Long playSignId, Long curUserId, OperateTypeEnum operateType) {
        Result<FamilySignServiceProto.ResponseReviewCancel> result = familySignService.reviewCancel(playSignId
                , operateType.getCode()
                , curUserId);
        if (RpcResult.isFail(result)) {
            log.error("hy reviewCancel fail. playSignId={},curUserId={},opType={},rCode={}"
                    , playSignId, curUserId, operateType.getCode(), result.rCode());
            return Pair.of(-1, "");
        }
        return Pair.of(result.target().getCode(), result.target().getMessage());
    }

    @Override
    public ResponseWithdrawCancel withdrawCancel(Long playerSignId, Long curUserId, RoleEnum opUserRole) {
        Result<PlayerSignServiceProto.ResponseTermination> result = playerSignService.termination(playerSignId, RoleConvert.I.waveRole2HyRoleCode(opUserRole.getRoleCode()), curUserId);
        if (RpcResult.isFail(result)) {
            log.error("hy withdrawCancel fail. playerSignId={},curUserId={},opRole={},rCode={}",
                    playerSignId, curUserId, opUserRole.getRoleCode(), result.rCode());
            return new ResponseWithdrawCancel().setCode(-1);
        }
        return new ResponseWithdrawCancel().setCode(result.target().getCode()).setMsg(result.target().getMessage());
    }

    @Override
    public Map<Long, Integer> countSignPlayerByRooms(List<Long> njIds) {
        if (CollectionUtils.isEmpty(njIds)) {
            return Collections.emptyMap();
        }

        List<CountSignPlayerPo> countSignPlayerPos = playerSignMapper.countSignPlayerByRooms(njIds);
        return countSignPlayerPos.stream().collect(Collectors.toMap(CountSignPlayerPo::getNjId, CountSignPlayerPo::getUserCount));
    }

    @Override
    public Integer countPlayerSignNum(Long njId) {
        Result<PlayerSignServiceProto.ResponsePlayerSignNum> result = playerSignService.playerSignNum(njId);
        if (RpcResult.isFail(result)) {
            log.error("hy playerSignNum fail. njId={},rCode={}", njId, result.rCode());
            return 0;
        }

        return result.target().getNum();
    }

    @Override
    public Optional<Long> getPlayerCurSignNj(Long userId) {
        return Optional.empty();
    }

    @Override
    public Optional<Long> getPlayerLastCancelSign(Long userId) {
        return Optional.empty();
    }
}
