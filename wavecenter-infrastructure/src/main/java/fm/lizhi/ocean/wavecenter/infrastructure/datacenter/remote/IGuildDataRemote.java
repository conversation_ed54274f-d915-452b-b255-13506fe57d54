package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote;

import fm.lizhi.ocean.wavecenter.api.datacenter.bean.GuildAssessmentInfoBean;
import fm.lizhi.ocean.wavecenter.base.remote.IRemote;

import java.util.List;

/**
 * 公会数据
 * 查询差异化+第三方数据调用
 * <AUTHOR>
 * @date 2024/5/23 10:23
 */
public interface IGuildDataRemote extends IRemote {

    /**
     * 查询考核业绩
     * @param familyId
     * @return
     */
    GuildAssessmentInfoBean queryAssessment(Long familyId, List<Long> roomIds);

}
