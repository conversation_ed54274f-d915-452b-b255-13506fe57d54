package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.constants;

import fm.lizhi.ocean.wavecenter.api.grow.ability.constants.AssessMetricEnum;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashSet;

/**
 * 能力指标枚举的校验, 用于避免修改了{@link AssessMetricEnum}之后忘记同步修改{@link CapabilityMetricEnum}.
 */
@Component
class CapabilityMetricEnumValidation {

    /**
     * 在postConstruct方法中进行校验, 确保{@link AssessMetricEnum}中的所有code都在{@link CapabilityMetricEnum}中存在.
     */
    @PostConstruct
    public void postConstruct() {
        HashSet<String> presentCodes = new HashSet<>();
        for (CapabilityMetricEnum capabilityMetricEnum : CapabilityMetricEnum.values()) {
            presentCodes.add(capabilityMetricEnum.getCode());
        }
        HashSet<String> requiredCodes = new HashSet<>();
        for (AssessMetricEnum assessMetricEnum : AssessMetricEnum.values()) {
            requiredCodes.add(assessMetricEnum.getCode());
        }
        if (!presentCodes.containsAll(requiredCodes)) {
            throw new IllegalStateException("AssessMetricEnum新增了code但CapabilityMetricEnum未同步修改, 请检查代码");
        }
    }
}
