package fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.hy;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import fm.hy.family.api.FamilyService;
import fm.hy.family.api.PlayerSignService;
import fm.hy.family.bean.FamilyInfo;
import fm.hy.family.protocol.FamilyServiceProto;
import fm.hy.family.protocol.PlayerSignServiceProto;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.constants.FamilyTypeMapping;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.IUserFamilyRemote;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class HyUserFamilyRemote implements IUserFamilyRemote {

    @Autowired
    private FamilyService familyService;
    @Autowired
    private CommonConfig commonConfig;
    @Autowired
    private PlayerSignService playerSignService;

    private final LoadingCache<Long, Optional<FamilyBean>> FAMILY_INFO_CACHE = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterAccess(10, TimeUnit.MINUTES)
            .build(new CacheLoader<Long, Optional<FamilyBean>>() {
                @Override
                public Optional<FamilyBean> load(Long familyId) {
                    return getFamily(familyId);
                }
            });

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.HEI_YE;
    }

    @Override
    public UserInFamilyBean getUserInFamily(long userId) {
        Result<FamilyServiceProto.ResponseGetUserFamily> res = familyService.getUserFamily(userId, true);
        if (RpcResult.isFail(res)) {
            log.warn("hy getUserInFamily fail userId={},rCode={}", userId, res.rCode());
            return UserInFamilyBean.builder().build();
        }

        FamilyServiceProto.UserFamilyInfo userFamilyInfo = res.target().getUserFamilyInfo();
        return UserInFamilyBean.builder()
                .familyId(userFamilyInfo.getFamilyId())
                .njId(userFamilyInfo.getNjId())
                .isFamily(userFamilyInfo.getIsFamily())
                .isRoom(userFamilyInfo.getIsRoom())
                .isPlayer(userFamilyInfo.getIsPlayer())
                .build();
    }

    @Override
    public Optional<FamilyBean> getUserFamily(long userId) {
        Result<FamilyServiceProto.ResponseGetUserFamily> result = familyService.getUserFamily(userId, true);
        if (RpcResult.isFail(result)) {
            log.warn("hy getUserFamily fail userId={},rCode={}", userId, result.rCode());
            return Optional.empty();
        }

        FamilyServiceProto.UserFamilyInfo userFamilyInfo = result.target().getUserFamilyInfo();
        return Optional.of(new FamilyBean()
                .setUserId(userFamilyInfo.getUserId())
                .setId(userFamilyInfo.getFamilyId())
                .setFamilyType(FamilyTypeMapping.bizValue2WaveType(userFamilyInfo.getFamilyType()))
                .setFamilyName(userFamilyInfo.getFamilyName()));
    }

    @Override
    public Optional<FamilyBean> getFamily(long familyId) {
        Result<FamilyServiceProto.ResponseGetFamilyInfo> familyInfoRes = familyService.getFamilyInfo(FamilyServiceProto.FamilyParam.newBuilder()
                .setFamilyId(familyId)
                .build());
        if (RpcResult.isFail(familyInfoRes)) {
            log.warn("hy,getFamily,familyId={},rCode={}", familyId, familyInfoRes.rCode());
            return Optional.empty();
        }
        String familyJsonStr = familyInfoRes.target().getFamily();
        if (StringUtils.isBlank(familyJsonStr)) {
            return Optional.empty();
        }
        FamilyInfo familyInfo = JsonUtil.loads(familyJsonStr, FamilyInfo.class);
        FamilyBean bean = new FamilyBean();
        bean.setId(familyId);
        bean.setFamilyName(familyInfo.getFamilyName());
        bean.setUserId(familyInfo.getUserId());
        bean.setFamilyNote(familyInfo.getFamilyNote());
        bean.setFamilyIconUrl(UrlUtils.getImageUrl(commonConfig.getHy().getCdnHost(), familyInfo.getFamilyIconUrl()));
        bean.setCreateTime(familyInfo.getCreateTime());
        bean.setFamilyType(FamilyTypeMapping.bizValue2WaveType(familyInfo.getFamilyType()));
        return Optional.of(bean);
    }

    @Override
    public Optional<FamilyBean> getFamilyByCache(long familyId) {
        try {
            return FAMILY_INFO_CACHE.get(familyId);
        } catch (ExecutionException e) {
            log.warn("hy getFamilyByCache error familyId={}", familyId, e);
            return Optional.empty();
        }
    }

    @Override
    public Optional<Long> playerCurSignNj(long userId) {
        Result<PlayerSignServiceProto.ResponsePlayerCurSignNj> res
                = playerSignService.playerCurSignNj(userId);
        if (res.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("hy playerCurSignNj fail, userId={}`rCode={}", userId, res.rCode());
            return Optional.empty();
        }
        return Optional.of(res.target().getNjId());
    }

    @Override
    public Integer countCanOpenRoomNum(long familyId) {
        Result<FamilyServiceProto.ResponseGetFamilyInfo> result = familyService.getFamilyInfo(FamilyServiceProto.FamilyParam.newBuilder().setFamilyId(familyId).build());
        if (RpcResult.isFail(result)) {
            log.error("hy getFamilyInfo fail. familyId={},rCode={}", familyId, result.rCode());
            return 0;
        }
        String familyStr = result.target().getFamily();
        FamilyInfo familyInfo = JsonUtil.loads(familyStr, FamilyInfo.class);
        return familyInfo.getOpenRoomNum() == null ? 0 : familyInfo.getOpenRoomNum();
    }

    @Override
    public Optional<FamilyBean> getFamilyByUserId(long userId) {
        return Optional.empty();
    }
}
