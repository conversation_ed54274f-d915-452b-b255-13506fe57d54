package fm.lizhi.ocean.wavecenter.infrastructure.message.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.api.message.bean.UnReadMessageCountBean;
import fm.lizhi.ocean.wavecenter.datastore.platform.message.entity.WcNoticeConfig;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 通知配置扩展Mapper
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcNoticeConfigExtMapper {

    /**
     * 统计未读消息数
     *
     * @param type 消息类型
     * @param appId 应用ID
     * @param effectTimeStamp 生效时间戳
     * @param showNoticeTimeStamp 显示通知时间戳
     * @param deployEnv 部署环境
     * @param targetUserId 目标用户ID
     * @return 未读消息数
     */
    @Select({
            "<script>",
            "SELECT wnc.type, count(1) as count",
            "FROM wavecenter_notice_config wnc",
            "WHERE wnc.app_id = #{appId}",
            "  AND wnc.deleted = 0",
            "  AND wnc.status = 1",
            "  AND unix_timestamp(wnc.effect_time) * 1000 &lt; #{effectTimeStamp}",
            "<if test='null != showNoticeTimeStamp'>",
            "  AND unix_timestamp(wnc.effect_time) * 1000 &gt; #{showNoticeTimeStamp}",
            "</if>",
            "<if test='null != type and type > 0'>",
            "  AND wnc.type = #{type}",
            "</if>",
            "  AND (wnc.deploy_env = #{deployEnv})",
            "  AND NOT EXISTS (",
            "    SELECT wncrr.user_id",
            "    FROM wavecenter_message_read_record wncrr",
            "    WHERE wncrr.message_id = wnc.id and wncrr.user_id = #{targetUserId}",
            "  ) GROUP BY wnc.type"
            ,"</script>"
    })
    List<UnReadMessageCountBean> countUnreadByType(@Param("type") Integer type, @Param("appId") int appId,
                          @Param("effectTimeStamp") Long effectTimeStamp,
                          @Param("showNoticeTimeStamp") Long showNoticeTimeStamp,
                          @Param("deployEnv") String deployEnv,
                          @Param("targetUserId") Long targetUserId);

/**
     * 分页查询公告配置
     * @param type 公告类型，可选
     * @param lastMaxEffectTime 生效时间下限，可选
     * @param status 公告状态，必填
     * @param deployEnv 部署环境，必填
     * @param limit 查询条数，必填
     * @return 公告配置列表
     */
    @Select({
        "<script>",
        "SELECT * FROM wavecenter_notice_config",
        "WHERE deleted = 0",
        "AND status = #{status}",
        "AND deploy_env = #{deployEnv}",
        "AND app_id = #{appId}",
        "<if test='type != null'>",
        "  AND type = #{type}",
        "</if>",
        "<if test='lastMaxEffectTime != null'>",
        "  AND create_time &gt; #{lastMaxEffectTime}",
        "</if>",
        " AND effect_time &lt; #{effectTime}",
        "ORDER BY create_time DESC",
        "LIMIT #{limit}",
        "</script>"
    })
    List<WcNoticeConfig> selectByPage(
            @Param("appId") Integer appId,
            @Param("type") Integer type,
            @Param("lastMaxEffectTime") Date lastMaxEffectTime,
            @Param("effectTime") Date effectTime,
            @Param("status") Integer status,
            @Param("deployEnv") String deployEnv,
            @Param("limit") int limit
    );




}
