package fm.lizhi.ocean.wavecenter.infrastructure.income.remote;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.*;
import fm.lizhi.ocean.wavecenter.base.remote.IRemote;

public interface PersonalIncomeRemote extends IRemote {


    /**
     * 个人-考核收入合计
     * @param paramBean
     * @return
     */
    PersonalIncomeDetailSumBean getRevenueIncomeDetailSum(GetPersonalRevenueIncomeDetailParamBean paramBean);

    /**
     * 个人-考核收入明细
     *
     * @param paramBean
     * @return
     */
    PageBean<PersonalIncomeDetailBean> getRevenueIncomeDetail(GetPersonalRevenueIncomeDetailParamBean paramBean);


    /**
     * 个人收益-获取个播收入的统计
     *
     * @return 日 周 月的统计值
     */
    PlayerSumDataBean getPlayerSumData(String tenantCode, long familyId, long userId);


    /**
     * 考核收入
     *
     * @return 日 周 月的统计值
     */
    PlayerRevenueSumDataBean getPlayerRevenueSumData(String tenantCode, long familyId, long userId);


    /**
     * 获取考核比例
     * @param njId
     * @return
     */
    String getIncomeRatio(long njId);

}
