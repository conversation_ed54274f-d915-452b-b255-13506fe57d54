package fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.dao;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.SaveFamilyLevelAwardItemBean;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestCreateFamilyLevelAwardRule;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestDeleteFamilyLevelAwardRule;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestUpdateFamilyLevelAwardRule;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert.FamilyLevelAwardItemConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert.FamilyLevelAwardRuleConvert;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilyLevelAwardItem;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilyLevelAwardRule;
import fm.lizhi.ocean.wavecenter.datastore.award.family.mapper.WcFamilyLevelAwardItemMapper;
import fm.lizhi.ocean.wavecenter.datastore.award.family.mapper.WcFamilyLevelAwardRuleMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.mapper.ext.WcFamilyLevelAwardItemExtMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.mapper.ext.WcFamilyLevelAwardRuleExtMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListValuedMap;
import org.apache.commons.collections4.multimap.ArrayListValuedHashMap;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Slf4j
@Repository
public class FamilyLevelAwardRuleDao {

    @Autowired
    private WcFamilyLevelAwardRuleExtMapper wcFamilyLevelAwardRuleExtMapper;
    @Autowired
    private WcFamilyLevelAwardRuleMapper wcFamilyLevelAwardRuleMapper;

    @Autowired
    private WcFamilyLevelAwardItemExtMapper wcFamilyLevelAwardItemExtMapper;
    @Autowired
    private WcFamilyLevelAwardItemMapper wcFamilyLevelAwardItemMapper;

    /**
     * 创建公会等级奖励规则
     *
     * @param request 请求
     */
    @Transactional
    public void createRule(RequestCreateFamilyLevelAwardRule request) {
        log.info("createRule request={}", request);
        WcFamilyLevelAwardRule createRule = FamilyLevelAwardRuleConvert.I.toCreateEntity(request);
        int createRuleRows = wcFamilyLevelAwardRuleMapper.insert(createRule);
        Long ruleId = createRule.getId();
        log.info("createRule createRule={}, createRuleRows={}, ruleId={}", createRule, createRuleRows, ruleId);
        List<WcFamilyLevelAwardItem> createItems = FamilyLevelAwardItemConvert.I.toCreateEntities(request, ruleId);
        int createItemRows = wcFamilyLevelAwardItemMapper.batchInsert(createItems);
        log.info("createRule createItems={}, createItemRows={}", createItems, createItemRows);
    }

    /**
     * 更新公会等级奖励规则
     *
     * @param request 请求
     */
    @Transactional
    public void updateRule(RequestUpdateFamilyLevelAwardRule request) {
        log.info("updateRule request={}", request);
        WcFamilyLevelAwardRule updateRule = FamilyLevelAwardRuleConvert.I.toUpdateEntity(request);
        int updateRuleRows = wcFamilyLevelAwardRuleMapper.updateByPrimaryKey(updateRule);
        log.info("updateRule updateRule={}, updateRuleRows={}", updateRule, updateRuleRows);
        Validate.validState(updateRuleRows == 1, "更新公会等级奖励规则失败");
        Long ruleId = request.getId();
        Integer appId = request.getAppId();
        String operator = request.getOperator();
        List<SaveFamilyLevelAwardItemBean> newItems = request.getItems();
        Map<Integer, WcFamilyLevelAwardItem> oldItemsMap = new HashMap<>(getRuleAllItemsAsTypeMap(ruleId));
        ArrayList<WcFamilyLevelAwardItem> updateItems = new ArrayList<>();
        ArrayList<WcFamilyLevelAwardItem> createItems = new ArrayList<>();
        ArrayList<Long> deleteItemIds = new ArrayList<>();
        for (SaveFamilyLevelAwardItemBean newItem : newItems) {
            WcFamilyLevelAwardItem oldItem = oldItemsMap.remove(newItem.getResourceType());
            if (oldItem != null) {
                WcFamilyLevelAwardItem updateItem = FamilyLevelAwardItemConvert.I.toUpdateEntity(newItem, oldItem.getId(), operator);
                updateItems.add(updateItem);
            } else {
                WcFamilyLevelAwardItem createItem = FamilyLevelAwardItemConvert.I.toCreateEntity(newItem, appId, ruleId, operator);
                createItems.add(createItem);
            }
        }
        for (WcFamilyLevelAwardItem remainItem : oldItemsMap.values()) {
            if (!remainItem.getDeleted()) {
                deleteItemIds.add(remainItem.getId());
            }
        }
        if (CollectionUtils.isNotEmpty(updateItems)) {
            for (WcFamilyLevelAwardItem updateItem : updateItems) {
                wcFamilyLevelAwardItemMapper.updateByPrimaryKey(updateItem);
            }
            log.info("updateRule updateRows={}", updateItems.size());
        }
        if (CollectionUtils.isNotEmpty(deleteItemIds)) {
            int deleteRows = wcFamilyLevelAwardItemExtMapper.deleteByIds(deleteItemIds, operator);
            log.info("updateRule deleteRows={}", deleteRows);
        }
        if (CollectionUtils.isNotEmpty(createItems)) {
            int createRows = wcFamilyLevelAwardItemMapper.batchInsert(createItems);
            log.info("updateRule createRows={}", createRows);
        }
        log.info("updateRule updateItems={}, createItems={}, deleteItemIds={}", updateItems, createItems, deleteItemIds);
    }

    /**
     * 删除公会等级奖励规则
     *
     * @param request 请求
     */
    public void deleteRule(RequestDeleteFamilyLevelAwardRule request) {
        log.info("deleteRule request={}", request);
        Long ruleId = request.getId();
        String operator = request.getOperator();
        int deleteRuleRows = wcFamilyLevelAwardRuleExtMapper.deleteById(ruleId, operator);
        log.info("deleteRule deleteRuleRows={}", deleteRuleRows);
    }

    /**
     * 获取应用的公会等级奖励规则列表
     *
     * @param appId 应用id
     * @return 公会等级奖励规则列表
     */
    public List<WcFamilyLevelAwardRule> getRulesByAppId(int appId) {
        String deployEnv = ConfigUtils.getEnvRequired().name();
        return wcFamilyLevelAwardRuleExtMapper.getRulesByAppId(appId, deployEnv);
    }

    /**
     * 获取应用的公会等级奖励规则map. key: 等级id, value: 规则
     *
     * @param appId 应用id
     * @return 公会等级奖励规则map
     */
    public Map<Long, WcFamilyLevelAwardRule> getRulesByAppIdAsLevelMap(int appId) {
        List<WcFamilyLevelAwardRule> rules = getRulesByAppId(appId);
        LinkedHashMap<Long, WcFamilyLevelAwardRule> ruleMap = new LinkedHashMap<>();
        for (WcFamilyLevelAwardRule rule : CollectionUtils.emptyIfNull(rules)) {
            ruleMap.put(rule.getLevelId(), rule);
        }
        return ruleMap;
    }

    /**
     * 获取公会等级奖励规则
     *
     * @param ruleId 规则id
     * @return 公会等级奖励规则
     */
    public WcFamilyLevelAwardRule getRule(long ruleId) {
        WcFamilyLevelAwardRule getById = new WcFamilyLevelAwardRule();
        getById.setId(ruleId);
        return wcFamilyLevelAwardRuleMapper.selectByPrimaryKey(getById);
    }

    /**
     * 获取未删除的公会等级奖励规则
     *
     * @param appId   应用id
     * @param levelId 等级id
     * @return 公会等级奖励规则
     */
    public WcFamilyLevelAwardRule getUndeletedRule(int appId, long levelId) {
        String deployEnv = ConfigUtils.getEnvRequired().name();
        WcFamilyLevelAwardRule selectOne = new WcFamilyLevelAwardRule();
        selectOne.setDeployEnv(deployEnv);
        selectOne.setAppId(appId);
        selectOne.setLevelId(levelId);
        selectOne.setDeleted(false);
        return wcFamilyLevelAwardRuleMapper.selectOne(selectOne);
    }

    /**
     * 获取公会等级奖励规则条目map, key: 规则id, value: 规则条目列表
     *
     * @param ruleIds 规则id列表
     * @return 公会等级奖励规则条目map
     */
    public ListValuedMap<Long, WcFamilyLevelAwardItem> getRulesItemsMap(List<Long> ruleIds) {
        ArrayListValuedHashMap<Long, WcFamilyLevelAwardItem> ruleItemsMap = new ArrayListValuedHashMap<>();
        if (CollectionUtils.isEmpty(ruleIds)) {
            return ruleItemsMap;
        }
        List<WcFamilyLevelAwardItem> items = getRulesItems(ruleIds);
        for (WcFamilyLevelAwardItem item : items) {
            ruleItemsMap.put(item.getRuleId(), item);
        }
        return ruleItemsMap;
    }

    private List<WcFamilyLevelAwardItem> getRulesItems(List<Long> ruleIds) {
        if (CollectionUtils.isEmpty(ruleIds)) {
            return Collections.emptyList();
        }
        return wcFamilyLevelAwardItemExtMapper.getRulesItems(ruleIds);
    }

    public List<WcFamilyLevelAwardItem> getRuleItems(long ruleId) {
        WcFamilyLevelAwardItem selectMany = new WcFamilyLevelAwardItem();
        selectMany.setRuleId(ruleId);
        selectMany.setDeleted(false);
        List<WcFamilyLevelAwardItem> items = wcFamilyLevelAwardItemMapper.selectMany(selectMany);
        ArrayList<WcFamilyLevelAwardItem> sortedItems = new ArrayList<>(items);
        sortedItems.sort(Comparator.comparing(WcFamilyLevelAwardItem::getResourceType));
        return sortedItems;
    }

    private List<WcFamilyLevelAwardItem> getRuleAllItems(long ruleId) {
        WcFamilyLevelAwardItem selectByRuleId = new WcFamilyLevelAwardItem();
        selectByRuleId.setRuleId(ruleId);
        List<WcFamilyLevelAwardItem> items = wcFamilyLevelAwardItemMapper.selectMany(selectByRuleId);
        ArrayList<WcFamilyLevelAwardItem> sortedItems = new ArrayList<>(items);
        sortedItems.sort(Comparator.comparing(WcFamilyLevelAwardItem::getResourceType));
        return sortedItems;
    }

    private Map<Integer, WcFamilyLevelAwardItem> getRuleAllItemsAsTypeMap(long ruleId) {
        List<WcFamilyLevelAwardItem> items = getRuleAllItems(ruleId);
        LinkedHashMap<Integer, WcFamilyLevelAwardItem> itemMap = new LinkedHashMap<>();
        for (WcFamilyLevelAwardItem item : items) {
            itemMap.put(item.getResourceType(), item);
        }
        return itemMap;
    }

    /**
     * 获取公会等级奖励规则条目类型map. 嵌套结构{@code <规则id, <资源类型, 规则条目>>}.
     *
     * @param ruleIds 规则id列表
     * @return 公会等级奖励规则条目map
     */
    public Map<Long, Map<Integer, WcFamilyLevelAwardItem>> getRulesResourceTypeItemMap(List<Long> ruleIds) {
        if (CollectionUtils.isEmpty(ruleIds)) {
            return Collections.emptyMap();
        }
        ListValuedMap<Long, WcFamilyLevelAwardItem> rulesItemsMap = getRulesItemsMap(ruleIds);
        HashMap<Long, Map<Integer, WcFamilyLevelAwardItem>> rulesResourceTypeItemMap = new HashMap<>();
        for (Long ruleId : ruleIds) {
            List<WcFamilyLevelAwardItem> items = rulesItemsMap.get(ruleId);
            HashMap<Integer, WcFamilyLevelAwardItem> resourceTypeItemMap = new HashMap<>();
            for (WcFamilyLevelAwardItem item : items) {
                resourceTypeItemMap.put(item.getResourceType(), item);
            }
            rulesResourceTypeItemMap.put(ruleId, resourceTypeItemMap);
        }
        return rulesResourceTypeItemMap;
    }
}
