package fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.xm;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.bean.NjAndPlayerContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.OperateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseWithdrawCancel;
import fm.lizhi.ocean.wavecenter.api.sign.service.SignUserService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.utils.WcAssert;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.convert.SignInfraConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity.XmPlayerSign;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity.XmPlayerSignExample;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.mapper.XmPlayerSignMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.po.CountSignPlayerPo;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.po.QueryPlayerSignPo;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.INonContractRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.request.RequestInviteCancel;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.request.RequestInviteSign;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.response.ResponseInviteCancel;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.response.ResponseInviteSign;
import fm.lizhi.ocean.wavecenter.service.sign.dto.OperateSignDTO;
import fm.lizhi.ocean.wavecenter.service.sign.dto.QueryNonContractDTO;
import fm.lizhi.ocean.wavecenter.service.user.convert.RoleConvert;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserVerifyDataDTO;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import fm.lizhi.xm.family.api.PlayerSignService;
import fm.lizhi.xm.family.bean.JoinFamilyParam;
import fm.lizhi.xm.family.constants.UserType;
import fm.lizhi.xm.family.protocol.PlayerSignServiceProto;
import org.apache.commons.lang3.tuple.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/10 14:18
 */
@Slf4j
@Component
public class XmNonContractRemote implements INonContractRemote {

    @Autowired
    private XmPlayerSignMapper playerSignMapper;
    @Autowired
    private PlayerSignService playerSignService;
    @Autowired
    private UserManager userManager;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.XIMI;
    }

    @Override
    public Optional<NjAndPlayerContractBean> queryUserLast(Long userId, String type) {
        XmPlayerSignExample example = new XmPlayerSignExample();
        example.setOrderByClause("create_time desc");
        XmPlayerSignExample.Criteria criteria = example.createCriteria();
        criteria.andUserIdEqualTo(userId);
        criteria.andTypeEqualTo(type);

        PageList<XmPlayerSign> pageList = playerSignMapper.pageByExample(example, 1, 1);
        if (CollectionUtils.isEmpty(pageList)) {
            return Optional.empty();
        }

        XmPlayerSign ppPlayerSign = pageList.get(0);
        NjAndPlayerContractBean njAndPlayerContractBean = new NjAndPlayerContractBean();
        njAndPlayerContractBean.setContractId(ppPlayerSign.getId());
        njAndPlayerContractBean.setStatus(SignInfraConvert.I.xmSignStatusTrans(ppPlayerSign.getStatus()));
        njAndPlayerContractBean.setType(type);
        njAndPlayerContractBean.setCreateUser("ADMIN".equals(ppPlayerSign.getUserType()) ? RoleEnum.ROOM.getRoleCode() : RoleEnum.PLAYER.getRoleCode());

        return Optional.of(njAndPlayerContractBean);
    }

    @Override
    public boolean isInChangeCompany(Long curUserId) {
        return false;
    }

    @Override
    public boolean isInChangeCompanyPreparedStage(Long curUserId) {
        return false;
    }

    @Override
    public Pair<Integer, String> checkInviteSignLimit(Long userId, Long njId, RoleEnum userType, RoleEnum checkType) {
        return null;
    }

    @Override
    public ResponseInviteSign inviteSign(RequestInviteSign request) {
        ResponseInviteSign res = new ResponseInviteSign().setCode(0);

        // 查询用户所有实名账号用户ID
        List<Long> verifyUserIds = userManager.getUserIdFromVerifyResult(request.getCurUserId());
        if (CollectionUtils.isEmpty(verifyUserIds)) {
            log.warn("getUserIdFromVerifyResult fail.");
            return res.setCode(-1);
        }

        log.info("verifyUserIds={}", JsonUtil.dumps(verifyUserIds));
        JoinFamilyParam joinFamilyParam = new JoinFamilyParam()
                .setUserId(request.getCurUserId())
                .setTargetId(request.getTargetUserId())
                .setUserIdList(verifyUserIds)
                .setUserType(getUserType(request.getOpRole()));
        Result<PlayerSignServiceProto.ResponseJoinFamily> result = playerSignService.joinFamily(JsonUtil.dumps(joinFamilyParam));
        if (RpcResult.isFail(result)) {
            log.error("xm joinFamily fail. userId={},targetUserId={},rCode={}", request.getCurUserId(), request.getTargetUserId());
            return res.setCode(-1);
        }
        int code = result.target().getCode();
        if (code != 0) {
            return res.setCode(-1).setMsg(result.target().getMsg());
        }
        return res;
    }

    @Override
    public ResponseInviteCancel inviteCancel(RequestInviteCancel request) {
        Result<PlayerSignServiceProto.ResponseInviteCancel> result = playerSignService.inviteCancel(request.getPlaySignId(), RoleConvert.I.waveRole2XmRoleCode(request.getOpRole().getRoleCode()), request.getCurUserId());
        if (RpcResult.isFail(result)) {
            log.error("xm inviteCancel fail. rCode={},request={}", result.rCode(), JsonUtil.dumps(request));
            return new ResponseInviteCancel().setCode(-1);
        }
        return new ResponseInviteCancel().setCode(result.target().getCode()).setMsg(result.target().getMessage());
    }

    private UserType getUserType(RoleEnum role){
        if (RoleEnum.ROOM.getRoleCode().equals(role.getRoleCode())) {
            return UserType.ADMIN;
        }
        if (RoleEnum.PLAYER.getRoleCode().equals(role.getRoleCode()) || RoleEnum.USER.getRoleCode().equals(role.getRoleCode())) {
            return UserType.PLAYER;
        }
        if (RoleEnum.FAMILY.getRoleCode().equals(role.getRoleCode())) {
            return UserType.FAMILY;
        }
        return null;
    }

    @Override
    public List<NjAndPlayerContractBean> queryUserSign(Long userId, List<ContractTypeEnum> types, List<SignRelationEnum> status) {
        List<String> xmStatus = new ArrayList<>();
        for (SignRelationEnum signRelationEnum : status) {
            String value = SignInfraConvert.I.waveSignStatus2xm(signRelationEnum);
            if (StringUtils.isNotBlank(value)) {
                xmStatus.add(value);
            }
        }
        WcAssert.notEmpty(xmStatus, "status is empty");

        List<String> xmTypes = types.stream().map(ContractTypeEnum::getCode).collect(Collectors.toList());

        XmPlayerSignExample example = new XmPlayerSignExample();
        XmPlayerSignExample.Criteria criteria = example.createCriteria();
        criteria.andUserIdEqualTo(userId);
        criteria.andStatusIn(xmStatus);
        criteria.andTypeIn(xmTypes);

        List<XmPlayerSign> xmPlayerSigns = playerSignMapper.selectByExample(example);
        return SignInfraConvert.I.xmPlayerSignPos2ContractBeans(xmPlayerSigns);
    }

    @Override
    public OperateSignDTO operateSign(Long playSignId, Long curUserId, ContractTypeEnum type, RoleEnum role, OperateTypeEnum operateType) {
        OperateSignDTO res = new OperateSignDTO().setCode(0);

        PlayerSignServiceProto.OperateSignParam.Builder param = PlayerSignServiceProto.OperateSignParam.newBuilder();
        param.setPlaySignId(playSignId);
        param.setUid(curUserId);
        param.setUserType(RoleConvert.I.waveRole2XmRoleCode(role.getRoleCode()));
        param.setSignType(type.getCode());
        param.setOperateType(operateType.getCode());

        Result<PlayerSignServiceProto.ResponseOperateSign> result = playerSignService.operateSign(param.build());

        if (RpcResult.isFail(result)) {
            log.error("xm sign fail. playSignId={},curUserId={},type={},role={}", playSignId, curUserId, type.getCode(), role.getRoleCode());
            return new OperateSignDTO().setCode(-1);
        }
        if (result.target().getCode() != 0) {
            return res.setCode(-1).setMsg(result.target().getMsg());
        }
        return res;
    }

    @Override
    public Pair<Integer, String> checkCanSignForConfirm(Long playSignId, Long curUserId, RoleEnum opRole, RoleEnum checkRole) {
        return null;
    }

    @Override
    public PageBean<NjAndPlayerContractBean> queryList(QueryNonContractDTO paramDTO) {
        QueryPlayerSignPo entityParam = new QueryPlayerSignPo();
        if (paramDTO.getContractId() != null) {
            entityParam.setContractId(paramDTO.getContractId());
        }
        if (CollectionUtils.isNotEmpty(paramDTO.getNjIds())) {
            entityParam.setNjIds(paramDTO.getNjIds());
        }
        if (CollectionUtils.isNotEmpty(paramDTO.getTypes())) {
            entityParam.setTypes(paramDTO.getTypes().stream().map(ContractTypeEnum::getCode).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(paramDTO.getStatuses())) {
            entityParam.setStatuses(paramDTO.getStatuses().stream().map(SignInfraConvert.I::waveSignStatus2hy).collect(Collectors.toList()));
        }
        if (paramDTO.getUserOrNjId() != null) {
            entityParam.setUserOrNjId(paramDTO.getUserOrNjId());
        }
        if (paramDTO.getUserId() != null) {
            entityParam.setUserId(paramDTO.getUserId());
        }
        if (CollectionUtils.isNotEmpty(paramDTO.getContractIdLists())) {
            entityParam.setContractIdLists(paramDTO.getContractIdLists());
        }
        if (paramDTO.getParentId() != null) {
            entityParam.setParentId(paramDTO.getParentId());
        }

        entityParam.setDescCreateTime(paramDTO.isDescCreateTime());

        PageList<XmPlayerSign> list = playerSignMapper.pageByEntity(entityParam, paramDTO.getPageNo(), paramDTO.getPageSize());
        List<NjAndPlayerContractBean> resList = SignInfraConvert.I.xmPlayerSignPos2ContractBeans(list);
        for (NjAndPlayerContractBean contractBean : resList) {
            if (ContractTypeEnum.SIGN.getCode().equals(contractBean.getType())
                    && (
                            SignRelationEnum.REJECT.getCode().equals(contractBean.getStatus())
                                    || SignRelationEnum.SIGN_FAILED.getCode().equals(contractBean.getStatus())
                                    || SignRelationEnum.OVERDUE.getCode().equals(contractBean.getStatus())
                    )
            ) {
                //签约不成功应该没有签约时间
                contractBean.setStartTime(null);
            }
        }
        return PageBean.of(list.getTotal(), resList);
    }

    @Override
    public Pair<Integer, String> reviewCancel(Long playSignId, Long curUserId, OperateTypeEnum operateType) {
        return null;
    }

    @Override
    public ResponseWithdrawCancel withdrawCancel(Long playerSignId, Long curUserId, RoleEnum opUserRole) {
        return null;
    }

    @Override
    public Map<Long, Integer> countSignPlayerByRooms(List<Long> njIds) {
        if (CollectionUtils.isEmpty(njIds)) {
            return Collections.emptyMap();
        }

        List<CountSignPlayerPo> countSignPlayerPos = playerSignMapper.countSignPlayerByRooms(njIds);
        return countSignPlayerPos.stream().collect(Collectors.toMap(CountSignPlayerPo::getNjId, CountSignPlayerPo::getUserCount));
    }

    @Override
    public Integer countPlayerSignNum(Long njId) {
        Result<PlayerSignServiceProto.ResponsePlayerSignNum> result = playerSignService.playerSignNum(njId);
        if (RpcResult.isFail(result)) {
            log.error("xm playerSignNum fail. njId={},rCode={}", njId, result.rCode());
            return 0;
        }

        return result.target().getNum();
    }

    @Override
    public Optional<Long> getPlayerCurSignNj(Long userId) {
        return Optional.empty();
    }

    @Override
    public Optional<Long> getPlayerLastCancelSign(Long userId) {
        return Optional.empty();
    }
}
