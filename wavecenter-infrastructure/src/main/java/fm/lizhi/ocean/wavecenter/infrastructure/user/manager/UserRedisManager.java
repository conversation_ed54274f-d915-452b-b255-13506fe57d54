package fm.lizhi.ocean.wavecenter.infrastructure.user.manager;

import fm.lizhi.common.datastore.redis.client.RedisClient;
import fm.lizhi.ocean.wavecenter.infrastructure.user.constants.UserRedisKey;
import fm.lizhi.ocean.wavecenter.infrastructure.user.po.AccessTokenInfoPo;
import fm.lizhi.ocean.wavecenter.infrastructure.user.po.RefreshTokenInfoPo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/4/17 22:41
 */
@Component
public class UserRedisManager {

    @Resource(name = "redis_ocean_wavecenter")
    private RedisClient redisClient;

    public void removeDevice(int appId, long userId, String deviceId){
        String deviceKey = UserRedisKey.LOGIN_USER_DEVICE.getKey(appId, userId);
        redisClient.srem(deviceKey, deviceId);
    }

    public void deleteRole(int appId, long userId, String deviceId, Long roleConfigId){
        String roleKey = UserRedisKey.USER_ROLE_ACCESS_TOKEN.getKey(appId, userId, deviceId, roleConfigId);
        redisClient.del(roleKey);
    }

    public void deleteAccessToken(int appId, long userId, String deviceId){
        String key = UserRedisKey.USER_ADVICE_ACCESS_TOKEN.getKey(appId, userId, deviceId);
        redisClient.del(key);
    }

    public void deleteAccessTokenInfo(String oldAccessToken){
        redisClient.del(UserRedisKey.ACCESS_TOKEN_REF_INFO.getKey(oldAccessToken));
    }

    public void deleteRefreshTokenInfo(String oldRefreshToken){
        redisClient.del(UserRedisKey.REFRESH_TOKEN_REF_INFO.getKey(oldRefreshToken));
    }

    public Optional<String> getAccessToken(int appId, long userId, String deviceId){
        String key = UserRedisKey.USER_ADVICE_ACCESS_TOKEN.getKey(appId, userId, deviceId);
        return Optional.ofNullable(redisClient.get(key));
    }

    public RefreshTokenInfoPo getRefreshTokenInfoByAc(String accessToken) {
        AccessTokenInfoPo acPo = getAccessTokenInfo(accessToken);
        if (acPo == null || StringUtils.isBlank(acPo.getRefreshToken())) {
            return new RefreshTokenInfoPo();
        }
        return getRefreshTokenInfo(acPo.getRefreshToken());
    }

    public RefreshTokenInfoPo getRefreshTokenInfo(String refreshToken) {
        String key = UserRedisKey.REFRESH_TOKEN_REF_INFO.getKey(refreshToken);
        String value = redisClient.get(key);
        if (StringUtils.isBlank(value)) {
            return new RefreshTokenInfoPo();
        }
        return RefreshTokenInfoPo.of(value);
    }

    public Integer getRefreshTokenTtl(String refreshToken){
        String key = UserRedisKey.REFRESH_TOKEN_REF_INFO.getKey(refreshToken);
        Long ttl = redisClient.ttl(key);
        return Math.toIntExact(ttl);
    }

    public AccessTokenInfoPo getAccessTokenInfo(String accessToken) {
        String key = UserRedisKey.ACCESS_TOKEN_REF_INFO.getKey(accessToken);
        String value = redisClient.get(key);
        if (StringUtils.isBlank(value)) {
            return new AccessTokenInfoPo();
        }
        return AccessTokenInfoPo.of(value);
    }

}
