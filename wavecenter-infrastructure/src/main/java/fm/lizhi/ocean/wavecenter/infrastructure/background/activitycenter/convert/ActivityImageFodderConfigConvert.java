package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityImageFodderBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityRuleBaseAbstractBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityImageFodder;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityImageFodder;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseSaveActivityImageFodder;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityImageFodder;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        imports = {ActivityRuleBaseAbstractBean.class}
)
public interface ActivityImageFodderConfigConvert {
    ActivityImageFodderConfigConvert I = Mappers.getMapper(ActivityImageFodderConfigConvert.class);


    @Mappings({
            @Mapping(target = "deleted", expression = "java(fm.lizhi.ocean.wavecenter.infrastructure.common.constants.LogicDeleteConstants.NOT_DELETED)"),
            @Mapping(target = "deployEnv", expression = "java(com.lizhi.commons.config.core.util.ConfigUtils.getEnvRequired().name())"),
    })
    ActivityImageFodder buildActivityImageFodderForSave(RequestSaveActivityImageFodder param);

    @Mappings({
            @Mapping(target = "modifyTime", expression = "java(new java.util.Date())"),
    })
    ActivityImageFodder buildActivityImageFodderForUpdate(RequestUpdateActivityImageFodder param);

    List<ActivityImageFodderBean> convertPageImageFodder(PageList<ActivityImageFodder> pageList);

    @Mappings({
            @Mapping(target = "imageUrl", expression = "java(fm.lizhi.ocean.wavecenter.common.utils.UrlUtils.addCdnHost(cdnHost, imageFodder.getImageUrl()))"),
    })
    ResponseSaveActivityImageFodder buildResponseSaveActivityImageFodder(ActivityImageFodder imageFodder, String cdnHost);

    default Long dateToLong(Date date) {
        return date.getTime();
    }
}
