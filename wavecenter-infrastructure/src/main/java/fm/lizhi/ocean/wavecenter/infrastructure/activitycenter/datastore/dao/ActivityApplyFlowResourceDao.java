package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.dao;

import java.util.List;
import java.util.Optional;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.ext.ActivityFlowResourceGiveRecordExtMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.AutoConfigResourceEnum;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityApplyFlowResource;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityApplyFlowResourceExample;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.mapper.ActivityApplyFlowResourceMapper;
import fm.lizhi.ocean.wavecenter.datastore.background.mapper.ActivityResourceConfigMapper;

@Repository
public class ActivityApplyFlowResourceDao {

    @Autowired
    private ActivityApplyFlowResourceMapper flowResourceMapper;

    @Autowired
    private ActivityResourceConfigMapper resourceConfigMapper;

    @Autowired
    private ActivityFlowResourceGiveRecordExtMapper flowResourceGiveRecordExtMapper;

    

    /**
     * 批量保存
     *
     * @param flowResources 流量资源列表
     * @return 结果
     */
    public boolean batchSaveRecord(List<ActivityApplyFlowResource> flowResources) {
        if (CollectionUtils.isEmpty(flowResources)) {
            return true;
        }
        return flowResourceMapper.batchInsert(flowResources) == flowResources.size();
    }

    public boolean batchUpdateRecord(Long activityId, List<ActivityApplyFlowResource> flowResources) {
        if (CollectionUtils.isEmpty(flowResources)) {
            return true;
        }

        int updateCount = 0;
        for (ActivityApplyFlowResource flowResource : flowResources) {
            ActivityApplyFlowResourceExample activityApplyFlowResourceExample = new ActivityApplyFlowResourceExample();
            activityApplyFlowResourceExample.createCriteria().andActivityIdEqualTo(activityId).andResourceConfigIdEqualTo(flowResource.getResourceConfigId());
            updateCount = updateCount + flowResourceMapper.updateByExample(flowResource, activityApplyFlowResourceExample);
        }
        return updateCount == flowResources.size();
    }

    /**
     * 修改流量资源状态
     *
     * @param activityId 活动ID
     * @param status     状态
     */
    public void updateFlowResourceStatus(Long activityId, Integer status) {
        ActivityApplyFlowResourceExample example = new ActivityApplyFlowResourceExample();
        example.createCriteria().andActivityIdEqualTo(activityId);
        ActivityApplyFlowResource flowResource = new ActivityApplyFlowResource();
        flowResource.setStatus(status);
        flowResourceMapper.updateByExample(flowResource, example);
    }

    public long batchDeleteRecord(Long id) {
        ActivityApplyFlowResourceExample example = new ActivityApplyFlowResourceExample();
        example.createCriteria().andActivityIdEqualTo(id);
        return flowResourceMapper.deleteByExample(example);
    }

    /**
     * 根据活动ID和资源配置code枚举，查询活动申请流量资源
     * @param activityId 活动ID
     * @param appId 应用ID
     * @param resourceCodeEnum 资源配置code枚举
     * @return 活动申请流量资源
     */
    public Optional<ActivityApplyFlowResource> getFlowResourceByActivityIdAndCode(Long activityId, Integer appId, AutoConfigResourceEnum resourceCodeEnum) {
        ActivityApplyFlowResource resource = flowResourceGiveRecordExtMapper.getActivityFlowResource(activityId, appId, ConfigUtils.getEnvRequired().name(), resourceCodeEnum.getResourceCode());
        return Optional.ofNullable(resource);
    }

}
