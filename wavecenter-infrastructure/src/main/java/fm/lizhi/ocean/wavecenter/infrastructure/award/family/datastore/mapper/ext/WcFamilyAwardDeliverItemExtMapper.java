package fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcFamilyAwardDeliverItemExtMapper {

    @Update("UPDATE `wavecenter_family_award_deliver_item`\n" +
            "SET `status` = 2, `modify_time` = NOW()\n" +
            "WHERE `execution_id` = #{executionId} AND `status` = 1")
    int updateToSuccessByExecutionId(@Param("executionId") long executionId);

    @Update("UPDATE `wavecenter_family_award_deliver_item`\n" +
            "SET `status` = 2,  `error_code` = 0, `error_text` = '', `modify_time` = NOW()\n" +
            "WHERE `execution_id` = #{executionId}")
    int updateReDeliverToSuccessByExecutionId(@Param("executionId") long executionId);

    @Update("UPDATE `wavecenter_family_award_deliver_item`\n" +
            "SET `status` = 3, `error_code` = #{errorCode}, `error_text` = #{errorText}, `modify_time` = NOW()\n" +
            "WHERE `execution_id` = #{executionId} AND `status` = 1")
    int updateToFailureByExecutionId(@Param("executionId") long executionId, @Param("errorCode") int errorCode, @Param("errorText") String errorText);

    @Update("UPDATE `wavecenter_family_award_deliver_item`\n" +
            "SET `status` = 3, `error_code` = #{errorCode}, `error_text` = #{errorText}, `modify_time` = NOW()\n" +
            "WHERE `execution_id` = #{executionId}")
    int updateReDeliverToFailureByExecutionId(@Param("executionId") long executionId, @Param("errorCode") int errorCode, @Param("errorText") String errorText);
}
