package fm.lizhi.ocean.wavecenter.infrastructure.income.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.pay.settle.protocol.creativecenter.CreatorDataQueryProto;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/12/20 14:57
 */
@Slf4j
public class CreatorDataQueryServiceHandler {

    public Result<CreatorDataQueryProto.ResponseQueryTradeStatisticsValue> queryTradeStatisticsValue(CreatorDataQueryProto.QueryTradeStatisticsValueRequest request, String requestTime){
        log.warn("CreatorDataQueryServiceHandler queryTradeStatisticsValue block");
        //降级返回0
        CreatorDataQueryProto.ResponseQueryTradeStatisticsValue value = CreatorDataQueryProto.ResponseQueryTradeStatisticsValue.newBuilder()
                .setStatisticsValue(0L)
                .setRequestTime(requestTime)
                .build();
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, value);
    }

}
