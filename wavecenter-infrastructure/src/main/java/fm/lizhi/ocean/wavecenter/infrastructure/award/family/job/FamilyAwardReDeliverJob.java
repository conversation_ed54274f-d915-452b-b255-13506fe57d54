package fm.lizhi.ocean.wavecenter.infrastructure.award.family.job;

import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager.deliver.FamilyAwardReDeliverManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 公会奖励重新发放任务
 */
@Component
@Slf4j
public class FamilyAwardReDeliverJob implements JobHandler {

    @Autowired
    private FamilyAwardReDeliverManager familyAwardReDeliverManager;

    @Override
    public void execute(JobExecuteContext context) throws Exception {
        FamilyAwardReDeliverParam param = parseParam(context);
        if (param == null || param.getExecutionId() == null) {
            log.info("FamilyAwardReDeliverJob skip due to empty param");
            return;
        }
        log.info("FamilyAwardReDeliverJob start, param: {}", param);
        Long executionId = param.getExecutionId();
        familyAwardReDeliverManager.reDeliverAward(executionId);
        log.info("FamilyAwardReDeliverJob end");
    }

    private FamilyAwardReDeliverParam parseParam(JobExecuteContext context) {
        if (StringUtils.isBlank(context.getParam())) {
            return null;
        }
        try {
            return JsonUtils.fromJsonString(context.getParam(), FamilyAwardReDeliverParam.class);
        } catch (RuntimeException e) {
            log.info("Failed to parse param: {}", context.getParam(), e);
            return null;
        }
    }
}
