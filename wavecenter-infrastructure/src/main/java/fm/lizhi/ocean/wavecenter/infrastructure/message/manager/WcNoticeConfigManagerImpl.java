package fm.lizhi.ocean.wavecenter.infrastructure.message.manager;

import cn.hutool.core.collection.CollUtil;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.api.message.bean.UnReadMessageCountBean;
import fm.lizhi.ocean.wavecenter.api.message.constant.WcNoticeConfigEnum;
import fm.lizhi.ocean.wavecenter.api.message.request.RequestGetMessageList;
import fm.lizhi.ocean.wavecenter.infrastructure.message.convert.WcNoticeConfigConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.message.datastore.dao.WaveCenterMessageDao;
import fm.lizhi.ocean.wavecenter.infrastructure.message.datastore.dao.WaveCenterMessageReadDao;
import fm.lizhi.ocean.wavecenter.infrastructure.message.datastore.dao.WcNoticeConfigDao;
import fm.lizhi.ocean.wavecenter.datastore.platform.message.entity.WcMessageReadRecord;
import fm.lizhi.ocean.wavecenter.datastore.platform.message.entity.WcNoticeConfig;
import fm.lizhi.ocean.wavecenter.service.message.dto.*;
import fm.lizhi.ocean.wavecenter.service.message.manager.WcNoticeConfigManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.FirstLoginRecordDTO;
import fm.lizhi.ocean.wavecenter.service.user.manager.FirstLoginRecordManager;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class WcNoticeConfigManagerImpl implements WcNoticeConfigManager {

    @Autowired
    private WcNoticeConfigDao wcNoticeConfigDao;

    @Autowired
    private FirstLoginRecordManager firstLoginRecordManager;

    @Autowired
    private WaveCenterMessageReadDao waveCenterMessageReadDao;

    @Autowired
    private WaveCenterMessageDao waveCenterMessageDao;

    @Override
    public boolean saveWcNoticeConfig(WcNoticeConfigSaveParamDTO configDTO) {
        WcNoticeConfig entity = WcNoticeConfigConvert.I.dtoToEntity(configDTO);
        if (configDTO.getId() == null) {
            return wcNoticeConfigDao.saveWcNoticeConfig(entity) > 0;
        } else {
            return wcNoticeConfigDao.updateWcNoticeConfig(entity) > 0;
        }
    }

    @Override
    public boolean deleteWcNoticeConfigById(Long id) {
        return wcNoticeConfigDao.deleteWcNoticeConfigById(id) > 0;
    }

    @Override
    public WcNoticeConfigPageResultDTO queryWcNoticeConfigPage(WcNoticeConfigQueryDTO queryDTO) {
        PageList<WcNoticeConfig> page = wcNoticeConfigDao.queryWcNoticeConfigPage(queryDTO);
        List<WcNoticeConfigDTO> dtoList = WcNoticeConfigConvert.I.entityToDTOList(page);
        return new WcNoticeConfigPageResultDTO().setList(dtoList).setTotal(page.getTotal());
    }

    @Override
    public WcNoticeConfigResultDTO queryWcNoticeConfig(WcNoticeConfigQueryParamDTO queryDTO) {
        List<WcNoticeConfig> page = wcNoticeConfigDao.queryWcNoticeConfigByEffectTimePage(queryDTO);
        List<WcNoticeConfigQueryResDTO> dtoList = WcNoticeConfigConvert.I.queryResEntityToDTOList(page);
        //过滤出消息ID列表
        List<Long> messageIds = dtoList.stream().map(WcNoticeConfigQueryResDTO::getId).collect(Collectors.toList());
        Map<Long, WcMessageReadRecord> readMap = waveCenterMessageReadDao.batchQueryReadRecord(queryDTO.getUserId(), messageIds);
        for (WcNoticeConfigQueryResDTO dto : dtoList) {
            dto.setRead(readMap.containsKey(dto.getId()));
        }
        return new WcNoticeConfigResultDTO().setList(dtoList).setPerformanceId(Optional.ofNullable(CollUtil.getLast(dtoList)).map(e -> e.getEffectTime().getTime()).orElse(0L) / 1000);
    }

    @Override
    public List<UnReadMessageCountBean> getUnReadMessageCount(Long targetUserId, Integer appId, Integer type, String roleCode) {

        List<UnReadMessageCountBean> unReadMessageCountBeans = new ArrayList<>();

        Long showNoticeTimeStamp = null;
        FirstLoginRecordDTO firstLoginRecord = firstLoginRecordManager.queryFirstLoginRecord(appId, targetUserId);
        if (firstLoginRecord != null) {
            showNoticeTimeStamp = firstLoginRecord.getCreateTime().getTime();
        }
        if (type == null || WcNoticeConfigEnum.SIGN_APPLY.getCode() != type) {
            Long effectTimeStamp = System.currentTimeMillis();
            unReadMessageCountBeans = wcNoticeConfigDao.countUnreadByType(type, appId, effectTimeStamp, showNoticeTimeStamp, targetUserId);
        }

        if (type == null || WcNoticeConfigEnum.SIGN_APPLY.getCode() == type) {
            RequestGetMessageList request = new RequestGetMessageList().setAppId(appId).setUserId(targetUserId).setType(type).setRoleCode(roleCode);
            Long singnUnReadCount = waveCenterMessageDao.getUnReadCount(request, showNoticeTimeStamp);
            if (singnUnReadCount > 0) {
                unReadMessageCountBeans.add(new UnReadMessageCountBean().setType(WcNoticeConfigEnum.SIGN_APPLY.getCode()).setCount(singnUnReadCount.intValue()));
            }
        }
        return unReadMessageCountBeans;
    }

    @Override
    public boolean updateNoticeStatus(WcNoticeUpdateStatusDTO configDTO) {
        return wcNoticeConfigDao.updateNoticeStatus(configDTO);
    }

}
