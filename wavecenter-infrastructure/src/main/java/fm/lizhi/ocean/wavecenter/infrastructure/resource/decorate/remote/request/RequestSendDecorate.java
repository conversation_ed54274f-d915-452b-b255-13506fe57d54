package fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.request;

import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import lombok.Data;

/**
 * 单次发放装扮的请求
 */
@Data
public class RequestSendDecorate {

    /**
     * 装扮类型
     */
    private PlatformDecorateTypeEnum decorateType;

    /**
     * 装扮ID
     */
    private Long decorateId;

    /**
     * 装扮发放数量
     */
    private Integer decorateNumber;

    /**
     * 装扮过期时间, 分钟. 部分业务和装扮类型可能无有效期.
     */
    private Integer decorateExpireTime;

    /**
     * 用户ID
     */
    private Long userId;
}
