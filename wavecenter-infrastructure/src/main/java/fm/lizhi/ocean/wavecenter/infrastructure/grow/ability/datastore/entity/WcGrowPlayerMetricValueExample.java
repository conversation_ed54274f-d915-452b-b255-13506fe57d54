package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class WcGrowPlayerMetricValueExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_grow_player_metric_value
     *
     * @mbg.generated Tue Jun 10 19:44:21 CST 2025
     */
    public WcGrowPlayerMetricValueExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_grow_player_metric_value
     *
     * @mbg.generated Tue Jun 10 19:44:21 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_grow_player_metric_value
     *
     * @mbg.generated Tue Jun 10 19:44:21 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_grow_player_metric_value
     *
     * @mbg.generated Tue Jun 10 19:44:21 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_grow_player_metric_value
     *
     * @mbg.generated Tue Jun 10 19:44:21 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_grow_player_metric_value
     *
     * @mbg.generated Tue Jun 10 19:44:21 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_grow_player_metric_value
     *
     * @mbg.generated Tue Jun 10 19:44:21 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_grow_player_metric_value
     *
     * @mbg.generated Tue Jun 10 19:44:21 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_grow_player_metric_value
     *
     * @mbg.generated Tue Jun 10 19:44:21 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_grow_player_metric_value
     *
     * @mbg.generated Tue Jun 10 19:44:21 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_grow_player_metric_value
     *
     * @mbg.generated Tue Jun 10 19:44:21 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Class getEntityClass() {
        return fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowPlayerMetricValue.class;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wavecenter_grow_player_metric_value
     *
     * @mbg.generated Tue Jun 10 19:44:21 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(Integer value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(Integer value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(Integer value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(Integer value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(Integer value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<Integer> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<Integer> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(Integer value1, Integer value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(Integer value1, Integer value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdIsNull() {
            addCriterion("player_id is null");
            return (Criteria) this;
        }

        public Criteria andPlayerIdIsNotNull() {
            addCriterion("player_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlayerIdEqualTo(Long value) {
            addCriterion("player_id =", value, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdNotEqualTo(Long value) {
            addCriterion("player_id <>", value, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdGreaterThan(Long value) {
            addCriterion("player_id >", value, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdGreaterThanOrEqualTo(Long value) {
            addCriterion("player_id >=", value, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdLessThan(Long value) {
            addCriterion("player_id <", value, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdLessThanOrEqualTo(Long value) {
            addCriterion("player_id <=", value, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdIn(List<Long> values) {
            addCriterion("player_id in", values, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdNotIn(List<Long> values) {
            addCriterion("player_id not in", values, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdBetween(Long value1, Long value2) {
            addCriterion("player_id between", value1, value2, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdNotBetween(Long value1, Long value2) {
            addCriterion("player_id not between", value1, value2, "playerId");
            return (Criteria) this;
        }

        public Criteria andRoomIdIsNull() {
            addCriterion("room_id is null");
            return (Criteria) this;
        }

        public Criteria andRoomIdIsNotNull() {
            addCriterion("room_id is not null");
            return (Criteria) this;
        }

        public Criteria andRoomIdEqualTo(Long value) {
            addCriterion("room_id =", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotEqualTo(Long value) {
            addCriterion("room_id <>", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdGreaterThan(Long value) {
            addCriterion("room_id >", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdGreaterThanOrEqualTo(Long value) {
            addCriterion("room_id >=", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdLessThan(Long value) {
            addCriterion("room_id <", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdLessThanOrEqualTo(Long value) {
            addCriterion("room_id <=", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdIn(List<Long> values) {
            addCriterion("room_id in", values, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotIn(List<Long> values) {
            addCriterion("room_id not in", values, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdBetween(Long value1, Long value2) {
            addCriterion("room_id between", value1, value2, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotBetween(Long value1, Long value2) {
            addCriterion("room_id not between", value1, value2, "roomId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIsNull() {
            addCriterion("family_id is null");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIsNotNull() {
            addCriterion("family_id is not null");
            return (Criteria) this;
        }

        public Criteria andFamilyIdEqualTo(Long value) {
            addCriterion("family_id =", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotEqualTo(Long value) {
            addCriterion("family_id <>", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdGreaterThan(Long value) {
            addCriterion("family_id >", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("family_id >=", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdLessThan(Long value) {
            addCriterion("family_id <", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdLessThanOrEqualTo(Long value) {
            addCriterion("family_id <=", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIn(List<Long> values) {
            addCriterion("family_id in", values, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotIn(List<Long> values) {
            addCriterion("family_id not in", values, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdBetween(Long value1, Long value2) {
            addCriterion("family_id between", value1, value2, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotBetween(Long value1, Long value2) {
            addCriterion("family_id not between", value1, value2, "familyId");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateIsNull() {
            addCriterion("start_week_date is null");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateIsNotNull() {
            addCriterion("start_week_date is not null");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateEqualTo(Date value) {
            addCriterionForJDBCDate("start_week_date =", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("start_week_date <>", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateGreaterThan(Date value) {
            addCriterionForJDBCDate("start_week_date >", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("start_week_date >=", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateLessThan(Date value) {
            addCriterionForJDBCDate("start_week_date <", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("start_week_date <=", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateIn(List<Date> values) {
            addCriterionForJDBCDate("start_week_date in", values, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("start_week_date not in", values, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("start_week_date between", value1, value2, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("start_week_date not between", value1, value2, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateIsNull() {
            addCriterion("end_week_date is null");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateIsNotNull() {
            addCriterion("end_week_date is not null");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateEqualTo(Date value) {
            addCriterionForJDBCDate("end_week_date =", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("end_week_date <>", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateGreaterThan(Date value) {
            addCriterionForJDBCDate("end_week_date >", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("end_week_date >=", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateLessThan(Date value) {
            addCriterionForJDBCDate("end_week_date <", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("end_week_date <=", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateIn(List<Date> values) {
            addCriterionForJDBCDate("end_week_date in", values, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("end_week_date not in", values, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("end_week_date between", value1, value2, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("end_week_date not between", value1, value2, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andChatUserCntIsNull() {
            addCriterion("chat_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andChatUserCntIsNotNull() {
            addCriterion("chat_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andChatUserCntEqualTo(Integer value) {
            addCriterion("chat_user_cnt =", value, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntNotEqualTo(Integer value) {
            addCriterion("chat_user_cnt <>", value, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntGreaterThan(Integer value) {
            addCriterion("chat_user_cnt >", value, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("chat_user_cnt >=", value, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntLessThan(Integer value) {
            addCriterion("chat_user_cnt <", value, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("chat_user_cnt <=", value, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntIn(List<Integer> values) {
            addCriterion("chat_user_cnt in", values, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntNotIn(List<Integer> values) {
            addCriterion("chat_user_cnt not in", values, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntBetween(Integer value1, Integer value2) {
            addCriterion("chat_user_cnt between", value1, value2, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("chat_user_cnt not between", value1, value2, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntIsNull() {
            addCriterion("reply_chat_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntIsNotNull() {
            addCriterion("reply_chat_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntEqualTo(Integer value) {
            addCriterion("reply_chat_user_cnt =", value, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntNotEqualTo(Integer value) {
            addCriterion("reply_chat_user_cnt <>", value, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntGreaterThan(Integer value) {
            addCriterion("reply_chat_user_cnt >", value, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("reply_chat_user_cnt >=", value, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntLessThan(Integer value) {
            addCriterion("reply_chat_user_cnt <", value, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("reply_chat_user_cnt <=", value, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntIn(List<Integer> values) {
            addCriterion("reply_chat_user_cnt in", values, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntNotIn(List<Integer> values) {
            addCriterion("reply_chat_user_cnt not in", values, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntBetween(Integer value1, Integer value2) {
            addCriterion("reply_chat_user_cnt between", value1, value2, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("reply_chat_user_cnt not between", value1, value2, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatNewUserCntIsNull() {
            addCriterion("reply_chat_new_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andReplyChatNewUserCntIsNotNull() {
            addCriterion("reply_chat_new_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andReplyChatNewUserCntEqualTo(Integer value) {
            addCriterion("reply_chat_new_user_cnt =", value, "replyChatNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatNewUserCntNotEqualTo(Integer value) {
            addCriterion("reply_chat_new_user_cnt <>", value, "replyChatNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatNewUserCntGreaterThan(Integer value) {
            addCriterion("reply_chat_new_user_cnt >", value, "replyChatNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatNewUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("reply_chat_new_user_cnt >=", value, "replyChatNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatNewUserCntLessThan(Integer value) {
            addCriterion("reply_chat_new_user_cnt <", value, "replyChatNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatNewUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("reply_chat_new_user_cnt <=", value, "replyChatNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatNewUserCntIn(List<Integer> values) {
            addCriterion("reply_chat_new_user_cnt in", values, "replyChatNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatNewUserCntNotIn(List<Integer> values) {
            addCriterion("reply_chat_new_user_cnt not in", values, "replyChatNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatNewUserCntBetween(Integer value1, Integer value2) {
            addCriterion("reply_chat_new_user_cnt between", value1, value2, "replyChatNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatNewUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("reply_chat_new_user_cnt not between", value1, value2, "replyChatNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntIsNull() {
            addCriterion("gift_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntIsNotNull() {
            addCriterion("gift_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntEqualTo(Integer value) {
            addCriterion("gift_user_cnt =", value, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntNotEqualTo(Integer value) {
            addCriterion("gift_user_cnt <>", value, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntGreaterThan(Integer value) {
            addCriterion("gift_user_cnt >", value, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("gift_user_cnt >=", value, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntLessThan(Integer value) {
            addCriterion("gift_user_cnt <", value, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("gift_user_cnt <=", value, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntIn(List<Integer> values) {
            addCriterion("gift_user_cnt in", values, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntNotIn(List<Integer> values) {
            addCriterion("gift_user_cnt not in", values, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntBetween(Integer value1, Integer value2) {
            addCriterion("gift_user_cnt between", value1, value2, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("gift_user_cnt not between", value1, value2, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftNewUserCntIsNull() {
            addCriterion("gift_new_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andGiftNewUserCntIsNotNull() {
            addCriterion("gift_new_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andGiftNewUserCntEqualTo(Integer value) {
            addCriterion("gift_new_user_cnt =", value, "giftNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftNewUserCntNotEqualTo(Integer value) {
            addCriterion("gift_new_user_cnt <>", value, "giftNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftNewUserCntGreaterThan(Integer value) {
            addCriterion("gift_new_user_cnt >", value, "giftNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftNewUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("gift_new_user_cnt >=", value, "giftNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftNewUserCntLessThan(Integer value) {
            addCriterion("gift_new_user_cnt <", value, "giftNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftNewUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("gift_new_user_cnt <=", value, "giftNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftNewUserCntIn(List<Integer> values) {
            addCriterion("gift_new_user_cnt in", values, "giftNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftNewUserCntNotIn(List<Integer> values) {
            addCriterion("gift_new_user_cnt not in", values, "giftNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftNewUserCntBetween(Integer value1, Integer value2) {
            addCriterion("gift_new_user_cnt between", value1, value2, "giftNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftNewUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("gift_new_user_cnt not between", value1, value2, "giftNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andAllIncomeIsNull() {
            addCriterion("all_income is null");
            return (Criteria) this;
        }

        public Criteria andAllIncomeIsNotNull() {
            addCriterion("all_income is not null");
            return (Criteria) this;
        }

        public Criteria andAllIncomeEqualTo(BigDecimal value) {
            addCriterion("all_income =", value, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeNotEqualTo(BigDecimal value) {
            addCriterion("all_income <>", value, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeGreaterThan(BigDecimal value) {
            addCriterion("all_income >", value, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("all_income >=", value, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeLessThan(BigDecimal value) {
            addCriterion("all_income <", value, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("all_income <=", value, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeIn(List<BigDecimal> values) {
            addCriterion("all_income in", values, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeNotIn(List<BigDecimal> values) {
            addCriterion("all_income not in", values, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("all_income between", value1, value2, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("all_income not between", value1, value2, "allIncome");
            return (Criteria) this;
        }

        public Criteria andUpGuestDurIsNull() {
            addCriterion("up_guest_dur is null");
            return (Criteria) this;
        }

        public Criteria andUpGuestDurIsNotNull() {
            addCriterion("up_guest_dur is not null");
            return (Criteria) this;
        }

        public Criteria andUpGuestDurEqualTo(BigDecimal value) {
            addCriterion("up_guest_dur =", value, "upGuestDur");
            return (Criteria) this;
        }

        public Criteria andUpGuestDurNotEqualTo(BigDecimal value) {
            addCriterion("up_guest_dur <>", value, "upGuestDur");
            return (Criteria) this;
        }

        public Criteria andUpGuestDurGreaterThan(BigDecimal value) {
            addCriterion("up_guest_dur >", value, "upGuestDur");
            return (Criteria) this;
        }

        public Criteria andUpGuestDurGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("up_guest_dur >=", value, "upGuestDur");
            return (Criteria) this;
        }

        public Criteria andUpGuestDurLessThan(BigDecimal value) {
            addCriterion("up_guest_dur <", value, "upGuestDur");
            return (Criteria) this;
        }

        public Criteria andUpGuestDurLessThanOrEqualTo(BigDecimal value) {
            addCriterion("up_guest_dur <=", value, "upGuestDur");
            return (Criteria) this;
        }

        public Criteria andUpGuestDurIn(List<BigDecimal> values) {
            addCriterion("up_guest_dur in", values, "upGuestDur");
            return (Criteria) this;
        }

        public Criteria andUpGuestDurNotIn(List<BigDecimal> values) {
            addCriterion("up_guest_dur not in", values, "upGuestDur");
            return (Criteria) this;
        }

        public Criteria andUpGuestDurBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("up_guest_dur between", value1, value2, "upGuestDur");
            return (Criteria) this;
        }

        public Criteria andUpGuestDurNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("up_guest_dur not between", value1, value2, "upGuestDur");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntIsNull() {
            addCriterion("new_fans_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntIsNotNull() {
            addCriterion("new_fans_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntEqualTo(Integer value) {
            addCriterion("new_fans_user_cnt =", value, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntNotEqualTo(Integer value) {
            addCriterion("new_fans_user_cnt <>", value, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntGreaterThan(Integer value) {
            addCriterion("new_fans_user_cnt >", value, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("new_fans_user_cnt >=", value, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntLessThan(Integer value) {
            addCriterion("new_fans_user_cnt <", value, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("new_fans_user_cnt <=", value, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntIn(List<Integer> values) {
            addCriterion("new_fans_user_cnt in", values, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntNotIn(List<Integer> values) {
            addCriterion("new_fans_user_cnt not in", values, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntBetween(Integer value1, Integer value2) {
            addCriterion("new_fans_user_cnt between", value1, value2, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("new_fans_user_cnt not between", value1, value2, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andViolationCntIsNull() {
            addCriterion("violation_cnt is null");
            return (Criteria) this;
        }

        public Criteria andViolationCntIsNotNull() {
            addCriterion("violation_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andViolationCntEqualTo(Integer value) {
            addCriterion("violation_cnt =", value, "violationCnt");
            return (Criteria) this;
        }

        public Criteria andViolationCntNotEqualTo(Integer value) {
            addCriterion("violation_cnt <>", value, "violationCnt");
            return (Criteria) this;
        }

        public Criteria andViolationCntGreaterThan(Integer value) {
            addCriterion("violation_cnt >", value, "violationCnt");
            return (Criteria) this;
        }

        public Criteria andViolationCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("violation_cnt >=", value, "violationCnt");
            return (Criteria) this;
        }

        public Criteria andViolationCntLessThan(Integer value) {
            addCriterion("violation_cnt <", value, "violationCnt");
            return (Criteria) this;
        }

        public Criteria andViolationCntLessThanOrEqualTo(Integer value) {
            addCriterion("violation_cnt <=", value, "violationCnt");
            return (Criteria) this;
        }

        public Criteria andViolationCntIn(List<Integer> values) {
            addCriterion("violation_cnt in", values, "violationCnt");
            return (Criteria) this;
        }

        public Criteria andViolationCntNotIn(List<Integer> values) {
            addCriterion("violation_cnt not in", values, "violationCnt");
            return (Criteria) this;
        }

        public Criteria andViolationCntBetween(Integer value1, Integer value2) {
            addCriterion("violation_cnt between", value1, value2, "violationCnt");
            return (Criteria) this;
        }

        public Criteria andViolationCntNotBetween(Integer value1, Integer value2) {
            addCriterion("violation_cnt not between", value1, value2, "violationCnt");
            return (Criteria) this;
        }

        public Criteria andCheckInCntIsNull() {
            addCriterion("check_in_cnt is null");
            return (Criteria) this;
        }

        public Criteria andCheckInCntIsNotNull() {
            addCriterion("check_in_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andCheckInCntEqualTo(Integer value) {
            addCriterion("check_in_cnt =", value, "checkInCnt");
            return (Criteria) this;
        }

        public Criteria andCheckInCntNotEqualTo(Integer value) {
            addCriterion("check_in_cnt <>", value, "checkInCnt");
            return (Criteria) this;
        }

        public Criteria andCheckInCntGreaterThan(Integer value) {
            addCriterion("check_in_cnt >", value, "checkInCnt");
            return (Criteria) this;
        }

        public Criteria andCheckInCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("check_in_cnt >=", value, "checkInCnt");
            return (Criteria) this;
        }

        public Criteria andCheckInCntLessThan(Integer value) {
            addCriterion("check_in_cnt <", value, "checkInCnt");
            return (Criteria) this;
        }

        public Criteria andCheckInCntLessThanOrEqualTo(Integer value) {
            addCriterion("check_in_cnt <=", value, "checkInCnt");
            return (Criteria) this;
        }

        public Criteria andCheckInCntIn(List<Integer> values) {
            addCriterion("check_in_cnt in", values, "checkInCnt");
            return (Criteria) this;
        }

        public Criteria andCheckInCntNotIn(List<Integer> values) {
            addCriterion("check_in_cnt not in", values, "checkInCnt");
            return (Criteria) this;
        }

        public Criteria andCheckInCntBetween(Integer value1, Integer value2) {
            addCriterion("check_in_cnt between", value1, value2, "checkInCnt");
            return (Criteria) this;
        }

        public Criteria andCheckInCntNotBetween(Integer value1, Integer value2) {
            addCriterion("check_in_cnt not between", value1, value2, "checkInCnt");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wavecenter_grow_player_metric_value
     *
     * @mbg.generated do_not_delete_during_merge Tue Jun 10 19:44:21 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wavecenter_grow_player_metric_value
     *
     * @mbg.generated Tue Jun 10 19:44:21 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}