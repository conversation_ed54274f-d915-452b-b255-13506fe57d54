package fm.lizhi.ocean.wavecenter.infrastructure.award.singer.manager;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.csp.sentinel.util.AssertUtil;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateFlowOperateEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateOperateStatusEnum;
import fm.lizhi.ocean.wavecenter.common.dto.PageDto;
import fm.lizhi.ocean.wavecenter.infrastructure.anchor.singer.datastore.dao.SingerInfoV1Dao;
import fm.lizhi.ocean.wavecenter.infrastructure.award.singer.convert.SingerDecorateConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.award.singer.datastore.dao.SingerDecorateDao;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.entity.SingerDecorateFlow;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowDTO;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowGenerateDTO;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowPageParamDTO;
import fm.lizhi.ocean.wavecenter.service.award.singer.manager.SingerDecorateFlowManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 歌手装扮流水
 * <AUTHOR>
 */
@Component
@Slf4j
public class SingerDecorateFlowManagerImpl implements SingerDecorateFlowManager {

    @Autowired
    private SingerDecorateDao singerDecorateDao;

    @Autowired
    private SingerInfoV1Dao singerInfoV1Dao;


    @Override
    public PageDto<SingerDecorateFlowDTO> pageSingerDecorateFlow(SingerDecorateFlowPageParamDTO param, int pageNo, int pageSize) {
        PageList<SingerDecorateFlow> pageList = singerDecorateDao.pageSingerDecorateFlow(param, pageNo, pageSize);
        if (CollUtil.isEmpty(pageList)){
            return PageDto.empty();
        }

        return PageDto.of(pageList.getTotal(), SingerDecorateConvert.I.convertSingerDecorateFlowBeanList(pageList));
    }

    @Override
    public List<SingerDecorateFlowDTO> getDecorateFlowByUserIdAndSingerType(Long userId, int singerType, SingerDecorateFlowOperateEnum flowOperateEnum) {
        List<SingerDecorateFlow> singerDecorateFlowList = singerDecorateDao.getDecorateFlowByUserIdAndSingerType(userId, singerType, flowOperateEnum);
        return SingerDecorateConvert.I.convertSingerDecorateFlowBeanList(singerDecorateFlowList);
    }

    @Override
    public List<SingerDecorateFlowDTO> getCanRecoverDecorateFlowByUserId(int appId, Long userId, Integer singerType) {
        List<SingerDecorateFlow> singerDecorateFlowList = singerDecorateDao.getCanRecoverDecorateFlowByUserIdAndSingerType(appId, userId, singerType);
        return SingerDecorateConvert.I.convertSingerDecorateFlowBeanList(singerDecorateFlowList);
    }

    @Override
    public List<SingerDecorateFlowDTO> getDecorateFlowByTransactionIdAndLteRetryCount(Long transactionId, List<SingerDecorateOperateStatusEnum> statusList) {
        List<SingerDecorateFlow> list = singerDecorateDao.getDecorateFlowByTransactionIdAndLteRetryCount(transactionId, statusList);
        if (CollUtil.isEmpty(list)){
            return Collections.emptyList();
        }

        return SingerDecorateConvert.I.convertSingerDecorateFlowBeanList(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDecorateFlowStatus(SingerDecorateFlowDTO flow, SingerDecorateOperateStatusEnum status) {

        boolean updateRes = singerDecorateDao.updateDecorateFlowStatus(flow, status);
        AssertUtil.assertState(updateRes, "更新歌手装扮流水状态失败");

        if (flow.getOperateType().equals(SingerDecorateFlowOperateEnum.GRANT.getCode()) && status.equals(SingerDecorateOperateStatusEnum.SUCCESS)){
            // 发放类型，需要更新歌手信息
            singerInfoV1Dao.updateSingerRewardsIssued(flow.getUserId(), flow.getSingerType(), true);
        }

        if (flow.getOperateType().equals(SingerDecorateFlowOperateEnum.RECOVER.getCode()) && status.equals(SingerDecorateOperateStatusEnum.SUCCESS)){
            // 回收类型，需要更新之前的发放记录
            singerDecorateDao.updateSingerDecorateFlowRecycled(flow.getUserId(), flow.getSingerType(), flow.getDecorateId(), flow.getDecorateType(), flow.getRuleId(), true);
        }
        return true;
    }

    @Override
    public List<SingerDecorateFlowDTO> getDecorateFlowByUserIdAndRuleIds(Long userId, int appId, List<Long> ruleIds, SingerDecorateFlowOperateEnum operate, boolean recycled) {
        List<SingerDecorateFlow> list = singerDecorateDao.getDecorateFlowByUserIdAndRuleIds(userId, appId, ruleIds, operate, recycled);
        return SingerDecorateConvert.I.convertSingerDecorateFlowBeanList(list);
    }

    @Override
    public boolean batchInsert(SingerDecorateFlowGenerateDTO decorateFlowDTO) {
        List<SingerDecorateFlow> flowList = decorateFlowDTO.getDecorateFlowList()
                .stream().map(SingerDecorateConvert.I::convertSingerDecorateFlow).collect(Collectors.toList());
        return singerDecorateDao.batchInsert(flowList);
    }

    @Override
    public List<SingerDecorateFlowDTO> getDecorateFlowByTransactionId(int appId, Long transactionId) {
        List<SingerDecorateFlow> list = singerDecorateDao.getDecorateFlowByTransactionId(transactionId, appId);
        if (CollUtil.isNotEmpty(list)){
            return SingerDecorateConvert.I.convertSingerDecorateFlowBeanList(list);
        }
        return Collections.emptyList();
    }


}
