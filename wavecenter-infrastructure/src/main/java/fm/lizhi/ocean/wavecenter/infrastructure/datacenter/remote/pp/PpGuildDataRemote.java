package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote.pp;

import fm.lizhi.ocean.wavecenter.api.datacenter.bean.GuildAssessmentInfoBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.IncomeBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.ThreadConstants;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PaySettleConfigCodeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote.IGuildDataRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.income.manager.PaymentManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2024/5/23 10:25
 */
@Component
public class PpGuildDataRemote implements IGuildDataRemote {

    @Autowired
    private PaymentManager paymentManager;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public GuildAssessmentInfoBean queryAssessment(Long familyId, List<Long> roomIds) {
        GuildAssessmentInfoBean result = new GuildAssessmentInfoBean();
        CompletableFuture.allOf(
                CompletableFuture.runAsync(() -> {
                    // 总收入
                    IncomeBean incomeBean = paymentManager.getIncomeBeanByFamily(familyId, roomIds, PaySettleConfigCodeEnum.FAMILY_INCOME_TOTAL_AMOUNT);
                    result.setSumIncome(incomeBean);
                }, ThreadConstants.guildDatePool),
                CompletableFuture.runAsync(() -> {
                    // 厅收礼
                    IncomeBean roomIncome = paymentManager.getIncomeBeanByFamily(familyId, roomIds, PaySettleConfigCodeEnum.FAMILY_HALL_INCOME_TOTAL_AMOUNT);
                    result.setRoomIncome(roomIncome);
                }, ThreadConstants.guildDatePool),
                CompletableFuture.runAsync(() -> {
                    // 主播个播收礼收入
                    IncomeBean individualIncome = paymentManager.getIncomeBeanByFamily(familyId, roomIds, PaySettleConfigCodeEnum.FAMILY_INDIVIDUAL_INCOME_TOTAL_AMOUNT);
                    result.setIndividualIncome(individualIncome);
                }, ThreadConstants.guildDatePool),
                CompletableFuture.runAsync(() -> {
                    // 贵族提成
                    IncomeBean vipIncome = paymentManager.getIncomeBeanByFamily(familyId, roomIds, PaySettleConfigCodeEnum.FAMILY_NOBILITY_ROYALTY_INCOME_TOTAL_AMOUNT);
                    result.setVipIncome(vipIncome);
                }, ThreadConstants.guildDatePool),
                CompletableFuture.runAsync(() -> {
                    // 官方厅收礼收入
                    IncomeBean officialIncome = paymentManager.getIncomeBeanByFamily(familyId, roomIds, PaySettleConfigCodeEnum.FAMILY_OFFICIAL_HALL_INCOME_TOTAL_AMOUNT);
                    result.setOfficialIncome(officialIncome);
                }, ThreadConstants.guildDatePool)
        ).join();
        return result;
    }
}
