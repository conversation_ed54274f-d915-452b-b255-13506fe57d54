package fm.lizhi.ocean.wavecenter.infrastructure.chat.manager;

import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.config.BizCommonConfig;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wavecenter.service.common.constants.WaveCenterChatQueueEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.chat.remote.IChatServiceRemote;
import fm.lizhi.ocean.wavecenter.service.common.manager.ChatManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ChatManagerImpl implements ChatManager {

    @Autowired
    private IChatServiceRemote chatServiceRemote;

    @Autowired
    private CommonConfig commonConfig;

    /**
     * 发私信
     */
    @Override
    public void sendChatAsync(long userId, String content) {

        try {
            BizCommonConfig bizCommonConfig = commonConfig.getBizConfig();
            if (bizCommonConfig == null) {
                log.error("bis common config is null. userId: {}", userId);
                return;
            }

            Result<Void> result = chatServiceRemote.sendChatAsync(bizCommonConfig.getSendChatUid(), userId, content);
            if (RpcResult.isFail(result)) {
                log.warn("send chat fail. userId: {}, content: {}", userId, content);
                return;
            }
            log.info("sendChat rCode={}, userId={}, content={}", result.rCode(), userId, content);
        } catch (Exception e) {
            log.error("sendChat error. userId={}, content={}", userId, content, e);
        }
    }

    @Override
    public void batchSendCardChatAsync(Long sender, Collection<Long> receiverUserIdList, String content) {
        try {
            if (sender == null || sender <= 0) {
                log.error("batchSendChatAsync sender is null");
                return;
            }
            int success = 0;
            for (Long receiverUserId : receiverUserIdList) {
                Result<Void> result = chatServiceRemote.sendCardChatAsync(sender, receiverUserId, content);
                if (RpcResult.isFail(result)) {
                    log.warn("batchSendChatAsync fail.receiverUserId: {}, content: {}", receiverUserId, content);
                }
                success += RpcResult.isSuccess(result) ? 1 : 0;
                //睡一把，别太频繁
                Thread.sleep(20);
            }

            log.info("batchSendChatAsync.finish, totalSize={}, success={}, content={}", receiverUserIdList.size(), success, content);
        } catch (Exception e) {
            log.error("batchSendChatAsync error.  receiverUserIdList={}, content={}", receiverUserIdList, content, e);
        }
    }

    @Override
    public void batchSendRichTextChatAsync(Long sender, Collection<Long> receiverUserIdList, String content, WaveCenterChatQueueEnum queueEnum) {
        try {
            if (sender == null || sender <= 0) {
                log.error("batchSendChatAsync sender is null");
                return;
            }
            int success = 0;
            for (Long receiverUserId : receiverUserIdList) {
                Result<Void> result;
                if(queueEnum == WaveCenterChatQueueEnum.HIGH_PRIORITY) {
                    result = chatServiceRemote.sendRichTextChatAsync(sender, receiverUserId, content);
                } else {
                    result = chatServiceRemote.sendRichTextChatAsyncLowPriority(sender, receiverUserId, content);
                }
                if (RpcResult.isFail(result)) {
                    log.warn("batchSendChatAsync fail.receiverUserId: {}, content: {}", receiverUserId, content);
                }
                success += RpcResult.isSuccess(result) ? 1 : 0;
                //睡一把，别太频繁
                Thread.sleep(20);
            }

            log.info("batchSendChatAsync.finish, totalSize={}, success={}, content={}", receiverUserIdList.size(), success, content);
        } catch (Exception e) {
            log.error("batchSendChatAsync error.  receiverUserIdList={}, content={}", receiverUserIdList, content, e);
        }
    }

    @Override
    public void sendChatAsyncWithSkipWeb(long userId, String content, String url) {
        try {
            BizCommonConfig bizCommonConfig = commonConfig.getBizConfig();
            if (bizCommonConfig == null) {
                log.error("bis common config is null");
                return;
            }

            // 构建私信内容
            String chatContent = buildChatContent(content, url);
            Result<Void> result = chatServiceRemote.sendRichTextChatAsync(bizCommonConfig.getSendChatUid(), userId, chatContent);
            if (RpcResult.isFail(result)) {
                log.warn("sendRichTextChatAsync fail. userId: {}, content: {}", userId, content);
                return;
            }
            log.info("sendRichTextChatAsync rCode={}, userId={}, content={}", result.rCode(), userId, content);
        } catch (Exception e) {
            log.error("sendRichTextChatAsync error. userId={}, content={}", userId, content, e);
        }
    }

    /**
     * 构建带跳转链接的私信内容
     *
     * @param content 文案
     * @param url     跳转链接
     * @return 私信内容
     */
    private String buildChatContent(String content, String url) {
        String action = commonConfig.getActionWebJson();
        action = action.replace("#{url}", url);

        // 构建跳转action
        JSONObject clickAction = new JSONObject();
        clickAction.put("bizType", 0);
        clickAction.put("action", action);

        JSONObject chatContent = new JSONObject();
        chatContent.put("text", content);
        chatContent.put("clickActionType", 2);
        chatContent.put("clickAction", clickAction.toJSONString());
        return chatContent.toJSONString();
    }

}

