package fm.lizhi.ocean.wavecenter.infrastructure.grow.capability.datastore.dao;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.datastore.grow.capability.entity.WcGrowCapability;
import fm.lizhi.ocean.wavecenter.datastore.grow.capability.mapper.WcGrowCapabilityMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 能力项DAO
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@Repository
public class WcGrowCapabilityDao {

    @Autowired
    private WcGrowCapabilityMapper mapper;

    /**
     * 查询全部能力项（按appId和环境）
     *
     * @param appId 业务ID
     * @return 能力项列表
     */
    public List<WcGrowCapability> selectAll(Integer appId) {
        WcGrowCapability query = new WcGrowCapability();
        query.setAppId(appId);
        query.setDeployEnv(ConfigUtils.getEnvRequired().name());
        return mapper.selectMany(query);
    }

    /**
     * 插入能力项
     *
     * @param entity 能力项实体
     * @return 插入行数
     */
    public int insert(WcGrowCapability entity) {
        entity.setDeployEnv(ConfigUtils.getEnvRequired().name());
        return mapper.insert(entity);
    }

    /**
     * 根据id查询配置
     */
    public WcGrowCapability getById(Long id) {
        WcGrowCapability query = new WcGrowCapability();
        query.setId(id);
        return mapper.selectByPrimaryKey(query);
    }

    /**
     * 修改配置
     *
     * @param entity 配置实体
     * @return 影响行数
     */
    public int updateById(WcGrowCapability entity) {
        return mapper.updateByPrimaryKey(entity);
    }

    /**
     * 根据应用id获取所有能力项, 包括停用状态的能力项
     *
     * @param appId 应用id
     * @return 能力项列表
     */
    public List<WcGrowCapability> getAllCapabilities(int appId) {
        WcGrowCapability selectMany = new WcGrowCapability();
        selectMany.setDeployEnv(ConfigUtils.getEnvRequired().name());
        selectMany.setAppId(appId);
        return mapper.selectMany(selectMany);
    }

    /**
     * 根据应用id获取所有能力项名称映射, 包括停用状态的能力项, key: 能力项code, value: 能力项名称
     *
     * @param appId           应用id
     * @return 能力项code到名称的映射
     */
    public Map<String, String> getAllCapabilityCodeNameMap(int appId) {
        List<WcGrowCapability> capabilities = getAllCapabilities(appId);
        HashMap<String, String> codeNameMap = new HashMap<>();
        for (WcGrowCapability capability : capabilities) {
            codeNameMap.put(capability.getCapabilityCode(), capability.getName());
        }
        return codeNameMap;
    }
} 
