package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataRoomFamilyWeek;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.WcDataRoomFamilyWeekIncomePo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.WcDataRoomWeekStatPo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcDataRoomFamilyWeekExtMapper {

    @Select({
            "<script>"
            , "select "
            , "<foreach collection='metrics' item='m' separator=','>"
            , "sum(${m}) as ${m}"
            , "</foreach>"
            , "from wavecenter_data_room_family_week where app_id=#{appId}"
            , "and family_id=#{familyId}"
            , "and room_id in "
            , "<foreach collection='roomIds' item='roomId' open='(' close=')' separator=','>"
            , "#{roomId}"
            , "</foreach>"
            , "and start_week_date=#{startDay}"
            , "and end_week_date=#{endDay}"
            , "group by start_week_date"
            , "</script>"
    })
    List<WcDataRoomFamilyWeek> sum(@Param("metrics")List<String> metrics
            , @Param("appId") Integer appId
            , @Param("familyId") Long familyId
            , @Param("roomIds") List<Long> roomIds
            , @Param("startDay") String startDay
            , @Param("endDay") String endDay
    );

    /**
     * 批量查询厅上周收入数据
     * @param roomIds 厅ID列表
     * @param appId 业务ID
     * @param startWeekDate 上周开始日期
     * @param endWeekDate 上周结束日期
     * @return 厅收入数据列表
     */
    @Select({
            "<script>",
            "SELECT room_id, income , charm ",
            "FROM wavecenter_data_room_family_week ",
            "WHERE room_id IN ",
            "<foreach collection='roomIds' item='roomId' open='(' separator=',' close=')'>",
            "#{roomId}",
            "</foreach>",
            "AND app_id = #{appId} ",
            "AND start_week_date = #{startWeekDate} ",
            "AND end_week_date = #{endWeekDate}",
            "</script>"
    })
    List<WcDataRoomFamilyWeekIncomePo> queryRoomFamilyWeekIncomeByIds(@Param("roomIds") List<Long> roomIds,
                                                                      @Param("appId") Integer appId,
                                                                      @Param("startWeekDate") Date startWeekDate,
                                                                      @Param("endWeekDate") Date endWeekDate);

    /**
     * 查询厅周统计数据
     *
     * @param njId          厅ID
     * @param appId         业务ID
     * @param familyId      公会ID
     * @param startWeekDate 开始周日期
     * @param pageSize      条数
     * @return 厅周统计数据列表
     */
    @Select("SELECT " +
            "    start_week_date, " +
            "    end_week_date, " +
            "    room_id, " +
            "    personal_noble_income AS playerVip, " +
            "    official_hall_income AS officialRoom, " +
            "    personal_hall_income as player, " +
            "    all_income AS sum, " +
            "    sign_hall_income AS roomGift, " +
            "    noble_income AS roomVip " +
            "FROM wavecenter_data_room_family_week " +
            "WHERE room_id = #{njId} " +
            "    AND family_id = #{familyId} " +
            "    AND app_id = #{appId} " +
            "    AND start_week_date < #{startWeekDate} " +
            " order by start_week_date desc limit #{pageSize} ")
    List<WcDataRoomWeekStatPo> queryWeekStatsByTime(@Param("njId") Long njId,
                                                    @Param("appId") Integer appId,
                                                    @Param("familyId") Long familyId,
                                                    @Param("startWeekDate") Date startWeekDate,
                                                    @Param("pageSize") Integer pageSize);

}
