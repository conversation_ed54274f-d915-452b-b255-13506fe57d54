package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.dao;

import fm.lizhi.ocean.wavecenter.api.background.activitycenter.constants.ActivityResourceTransferStatusEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.convert.ActivityResourceTransferConvert;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityResourceTransfer;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityResourceTransferExample;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.ext.ActivityResourceTransferExtMapper;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.mapper.ActivityResourceTransferMapper;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityResourceTransferResultDTO;
import fm.lizhi.ocean.wavecenter.service.common.manager.IdManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ActivityResourceTransferDao {

    @Autowired
    private ActivityResourceTransferMapper activityResourceTransferMapper;

    @Autowired
    private ActivityResourceTransferExtMapper activityResourceTransferExtMapper;

    @Autowired
    private IdManager idManager;


    /**
     * 查询转存成功
     *
     * @param appId      应用ID
     * @param sourceUrls 资源url列表
     * @return 查询出来的资源url
     */
    public List<ActivityResourceTransfer> getTransferSuccessUrls(int appId, List<String> sourceUrls) {
        ActivityResourceTransferExample example = new ActivityResourceTransferExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andStatusEqualTo(ActivityResourceTransferStatusEnum.SUCCESS.getStatus())
                .andSourceUriIn(sourceUrls);
        return activityResourceTransferMapper.selectByExample(example);
    }

    /**
     * 查询转存失败的资源url
     *
     * @param appId       应用ID
     * @param sourceUrls  资源url列表
     * @param maxTryCount 最大重试次数
     * @return 查询出来的资源url
     */
    public List<ActivityResourceTransfer> getTransferMaxFailUrls(int appId, List<String> sourceUrls, int maxTryCount) {
        ActivityResourceTransferExample example = new ActivityResourceTransferExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andStatusEqualTo(ActivityResourceTransferStatusEnum.FAIL.getStatus())
                .andTryCountGreaterThan(maxTryCount)
                .andSourceUriIn(sourceUrls);
        return activityResourceTransferMapper.selectByExample(example);
    }

    /**
     * 批量保存转存结果
     *
     * @param transferList 转存结果列表
     * @return 成功行数
     */
    public int batchSaveTransferResult(List<ActivityResourceTransfer> transferList) {
        int totalSuccess = 0;
        for (ActivityResourceTransfer transfer : transferList) {
            transfer.setId(idManager.genId());
            int row = activityResourceTransferExtMapper.insertOrUpdate(transfer);
            totalSuccess += row == 1 ? 1 : 0;
        }
        return totalSuccess;
    }

    public ActivityResourceTransferResultDTO getResourceTransfer(int appId, String sourceUri) {
        ActivityResourceTransfer param = ActivityResourceTransfer.builder()
                .appId(appId)
                .sourceUri(sourceUri)
                .status(ActivityResourceTransferStatusEnum.SUCCESS.getStatus())
                .build();
        return ActivityResourceTransferConvert.I.convert(activityResourceTransferMapper.selectOne(param));
    }
}
