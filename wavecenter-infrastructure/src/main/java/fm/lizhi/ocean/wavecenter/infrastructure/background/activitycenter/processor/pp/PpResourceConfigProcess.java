package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.processor.pp;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityResource;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityResource;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityResourceConfig;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.processor.IResourceConfigProcess;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class PpResourceConfigProcess implements IResourceConfigProcess {


    @Override
    public void bindSaveProGramme(RequestSaveActivityResource resourceInfo) {

    }

    @Override
    public void bindUpdateProGramme(RequestUpdateActivityResource resourceInfo) {

    }

    @Override
    public void bindDeleteProGramme(ActivityResourceConfig activityResourceConfig, String operator) {

    }

    @Override
    public Result<Void> checkResourceLevelRepeat(List<Long> levelIds, String sourceResourceCode, Integer resourceType) {
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.PP;
    }
}
