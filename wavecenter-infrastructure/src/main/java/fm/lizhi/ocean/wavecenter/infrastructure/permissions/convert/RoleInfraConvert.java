package fm.lizhi.ocean.wavecenter.infrastructure.permissions.convert;

import fm.lizhi.ocean.wavecenter.api.permissions.bean.RoleAuthRefBean;
import fm.lizhi.ocean.wavecenter.api.permissions.bean.RoleInfoAuthRefBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.datastore.platform.premission.entity.WcRole;
import fm.lizhi.ocean.wavecenter.datastore.platform.premission.entity.WcRoleAuthRef;
import fm.lizhi.ocean.wavecenter.service.permissions.dto.RoleAuthRefDto;
import fm.lizhi.ocean.wavecenter.service.permissions.dto.RoleDto;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/4/11 11:20
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface RoleInfraConvert {

    RoleInfraConvert I = Mappers.getMapper(RoleInfraConvert.class);

    RoleInfoAuthRefBean roleAuthRefBean2RoleInfoBean(RoleAuthRefBean roleAuthRefBean);

    List<RoleInfoAuthRefBean> roleAuthRefBeans2RoleInfoBeans(List<RoleAuthRefBean> roleAuthRefBeans);

    RoleDto rolePo2Dto(WcRole po);

    List<RoleDto> rolePos2Dtos(List<WcRole> pos);

    RoleAuthRefBean authRefPo2Bean(WcRoleAuthRef po);

    RoleAuthRefDto authRefPo2Dto(WcRoleAuthRef po);

    @Named("convertUserInfo")
    default UserBean convertUserInfo(WcRoleAuthRef po, Map<Long, SimpleUserDto> userMap){
        if (po == null) {
            return null;
        }

        Long userId = po.getUserId();
        SimpleUserDto userDto = userMap.get(userId);
        if (userDto == null) {
            return null;
        }

        return new UserBean()
                .setId(userDto.getId())
                .setName(userDto.getName())
                .setBand(userDto.getBand());
    }

    @Named("convertSubject")
    default UserBean convertSubject(WcRoleAuthRef po, Map<Long, SimpleUserDto> userMap){
        if (po == null) {
            return null;
        }

        Long userId = po.getSubjectUserId();
        SimpleUserDto userDto = userMap.get(userId);
        if (userDto == null) {
            return null;
        }

        return new UserBean()
                .setId(userDto.getId())
                .setName(userDto.getName())
                .setBand(userDto.getBand());
    }

}
