package fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.request;

import fm.lizhi.ocean.wavecenter.api.sign.constant.AuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.Singular;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/15 17:08
 */
@Getter
@Builder
public class RequestFamilyAndNjCancelApply {

    private Long familyId;

    /**
     * 申请审批状态
     */
    @Singular
    private List<AuditStatusEnum> audits;

    /**
     * 签署状态
     */
    @Singular
    private List<SignRelationEnum> relations;

    @Builder.Default
    private Integer pageNo = 1;

    @Builder.Default
    private Integer pageSize = 50;

    private Long njId;

}
