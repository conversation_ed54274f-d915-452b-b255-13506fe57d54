package fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.hy;

import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.ocean.wavecenter.api.sign.bean.FamilyAndNjContractBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.income.datastore.mapper.HyPlayerIncomeMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.po.HyPlayerIncomePo;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.ISettleRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.request.RequestContractSettle;
import fm.lizhi.ocean.wavecenter.service.sign.dto.SignSettleDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/15 19:20
 */
@Component
public class HySettleRemote implements ISettleRemote {

    @Autowired
    private HyPlayerIncomeMapper playerIncomeMapper;

    @Override
    public Optional<SignSettleDTO> querySettleByNj(Long njId) {
        //默认值
        SignSettleDTO signSettleDTO = new SignSettleDTO().setSettlePercentage(48);
        List<HyPlayerIncomePo> list = playerIncomeMapper.getNjIncome(Lists.newArrayList(njId));
        if (CollectionUtils.isEmpty(list)) {
            LogContext.addResLog("njIncome list is empty");
            return Optional.of(signSettleDTO);
        }
        HyPlayerIncomePo po = list.get(0);
        return Optional.of(signSettleDTO.setSettlePercentage(po.getPlayerIncome()));
    }

    @Override
    public Map<Long, SignSettleDTO> querySettle(RequestContractSettle request) {

        List<FamilyAndNjContractBean> contracts = request.getContracts();

        if (CollectionUtils.isEmpty(contracts)) {
            LogContext.addResLog("contracts is empty");
            return Collections.emptyMap();
        }

        List<Long> njIds = contracts.stream()
                .map(FamilyAndNjContractBean::getNjUserId)
                .collect(Collectors.toList());

        List<HyPlayerIncomePo> list = playerIncomeMapper.getNjIncome(njIds);

        Map<Long, HyPlayerIncomePo> valueMapping = new HashMap<>();
        if (CollectionUtils.isNotEmpty(list)) {
            for (HyPlayerIncomePo hyPlayerIncomePo : list) {
                HyPlayerIncomePo existValue = valueMapping.get(hyPlayerIncomePo.getNjId());
                if (existValue == null) {
                    valueMapping.put(hyPlayerIncomePo.getNjId(), hyPlayerIncomePo);
                    continue;
                }

                if (hyPlayerIncomePo.getCreateTime().getTime() > existValue.getCreateTime().getTime()) {
                    valueMapping.put(hyPlayerIncomePo.getNjId(), hyPlayerIncomePo);
                }
            }
        }

        Map<Long, SignSettleDTO> result = new HashMap<>();
        for (FamilyAndNjContractBean contract : contracts) {
            Long njId = contract.getNjUserId();
            HyPlayerIncomePo po = valueMapping.get(njId);
            if (po != null) {
                result.put(contract.getContractId(), new SignSettleDTO().setSettlePercentage(po.getPlayerIncome()));
            } else {
                //业务默认值48
                result.put(contract.getContractId(), new SignSettleDTO().setSettlePercentage(48));
            }
        }

        return result;
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.HEI_YE;
    }
}
