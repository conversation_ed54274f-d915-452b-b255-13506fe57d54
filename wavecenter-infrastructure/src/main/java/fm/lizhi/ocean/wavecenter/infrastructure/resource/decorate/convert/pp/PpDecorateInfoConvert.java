package fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.convert.pp;

import com.alibaba.fastjson.JSONObject;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto;
import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.DecorateInfoBean;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.pp.vip.bean.resp.PageDecorateDto;
import org.apache.commons.lang3.StringUtils;

/**
 * @description:
 * @author: guoyibin
 * @create: 2025/03/31 12:11
 */
public class PpDecorateInfoConvert {

    public static DecorateInfoBean convertToDecorateInfoBean(PageDecorateDto pageDecoratedto, String cdnHost, PlatformDecorateTypeEnum platformDecorateTypeEnum) {
        DecorateInfoBean decorateInfoBean = new DecorateInfoBean();
        decorateInfoBean.setDecorateId(Long.parseLong(pageDecoratedto.getId()));
        decorateInfoBean.setDecorateName(pageDecoratedto.getName());
        decorateInfoBean.setDecorateTypeEnum(platformDecorateTypeEnum);
        // TODO:后续如果PP实现webp格式，需要优先取webp格式地址
        decorateInfoBean.setDecorateImage(UrlUtils.addCdnHost(cdnHost, pageDecoratedto.getMaterialUrl()));
        decorateInfoBean.setDecorateExpireTime(pageDecoratedto.getValidMin());
        //@why 官方认证 运营不配置thumbUrl
        decorateInfoBean.setPreviewUrl(StringUtils.isBlank(pageDecoratedto.getThumbUrl()) ? pageDecoratedto.getIconUrl() : pageDecoratedto.getThumbUrl());
        return decorateInfoBean;
    }

    public static DecorateInfoBean convertToDecorateInfoBean(String decorateDtoStr, String cdnHost, PlatformDecorateTypeEnum decorateType) {
        JSONObject decorateDTO = JSONObject.parseObject(decorateDtoStr);
        // TODO:后续如果PP实现webp格式，需要优先取webp格式地址
        String showImageUrl = UrlUtils.addCdnHost(cdnHost, getDecorateImage(decorateDTO));

        DecorateInfoBean decorateInfoBean = new DecorateInfoBean();
        decorateInfoBean.setDecorateId(decorateDTO.getLong("id"));
        decorateInfoBean.setDecorateName(decorateDTO.getString("name"));
        decorateInfoBean.setDecorateTypeEnum(decorateType);
        decorateInfoBean.setDecorateImage(showImageUrl);
        decorateInfoBean.setDecorateExpireTime(decorateDTO.getInteger("validMin"));
        decorateInfoBean.setPreviewUrl(showImageUrl);
        return decorateInfoBean;
    }

    private static String getDecorateImage(JSONObject decorateDTO) {
        // TODO:后续如果PP实现webp格式，需要优先取webp格式地址
        String thumbUrl = decorateDTO.getString("thumbUrl");
        if(StringUtils.isNotBlank(thumbUrl)) {
            return thumbUrl;
        }
        String materialUrl = decorateDTO.getString("materialUrl");
        if(StringUtils.isNotBlank(materialUrl)) {
            return materialUrl;
        }
        //@why 官方认证 运营不配置thumbUrl materialUrl
        String iconUrl = decorateDTO.getString("iconUrl");
        if(StringUtils.isNotBlank(iconUrl)) {
            return iconUrl;
        }
        return StringUtils.EMPTY;
    }
}
