package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.dao;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.common.utils.WcAssert;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PayTenantCodeEnum;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcPayConfigCode;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcPayConfigCodeExample;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.mapper.WcPayConfigCodeMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/22 15:51
 */
@Slf4j
@Repository
public class PayConfigCodeDao {

    @Autowired
    private WcPayConfigCodeMapper payConfigCodeMapper;

    /**
     * 新增或者更新
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveConfigCode(int appId, String configCode
            , String accountCode, String accountSubjectType, String accountEngineCode
            , List<Integer> bizIdList) {
        List<Integer> bizIds = new ArrayList<>();
        if (CollectionUtils.isEmpty(bizIdList)) {
            // 用来区分没有bizId关联的配置
            bizIds.add(-1);
        } else {
            bizIds = bizIdList;
        }

        // 查询配置是否存在
        List<WcPayConfigCode> configs = getConfigs(appId, configCode, accountCode);
        if (CollectionUtils.isEmpty(configs)) {
            // 不存在，插入新配置
            List<WcPayConfigCode> configEntity = buildConfigs(appId, configCode, accountCode, accountSubjectType, accountEngineCode, bizIds);
            int row = payConfigCodeMapper.batchInsert(configEntity);
            log.info("batchInsert row={}", row);
            return;
        }

        // 如果已存在，对比bizId是否有变更
        List<Integer> existBizIds = configs.stream().map(WcPayConfigCode::getBizId).collect(Collectors.toList());
        if (existBizIds.equals(bizIds)) {
            // 没有变更，忽略
            log.info("config no change");
            return;
        }

        // 有变更，删除配置，重新插入
        int deleteRow = deleteConfigs(appId, configCode, accountCode);
        log.info("deleteConfigs deleteRow={}", deleteRow);

        List<WcPayConfigCode> configEntity = buildConfigs(appId, configCode, accountCode, accountSubjectType, accountEngineCode, bizIds);
        int row = payConfigCodeMapper.batchInsert(configEntity);
        log.info("batchInsert row={}", row);
    }

    /**
     * 逻辑删除配置
     * @param appId
     * @param configCode
     * @param accountCode
     * @return
     */
    private int deleteConfigs(int appId, String configCode, String accountCode){
        WcPayConfigCodeExample example = new WcPayConfigCodeExample();
        example.createCriteria()
                .andConfigCodeEqualTo(configCode)
                .andAccountCodeEqualTo(accountCode)
                .andDeletedEqualTo(0)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andAppIdEqualTo(appId);

        WcPayConfigCode entity = new WcPayConfigCode();
        entity.setDeleted(1);
        entity.setUpdateTime(new Date());
        return payConfigCodeMapper.updateByExample(entity, example);
    }

    private List<WcPayConfigCode> buildConfigs(int appId, String configCode, String accountCode, String accountSubjectType, String accountEngineCode
            , List<Integer> bizIds){
        Optional<String> payTenantCodeOp = PayTenantCodeEnum.getPayTenantCodeNullable(appId);
        WcAssert.isTrue(payTenantCodeOp.isPresent(), "payTenantCode not exist");

        List<WcPayConfigCode> configs = new ArrayList<>();
        for (Integer bizId : bizIds) {
            WcPayConfigCode config = new WcPayConfigCode();
            config.setAppId(appId);
            config.setTenantCode(payTenantCodeOp.get());
            config.setConfigCode(configCode);
            config.setAccountCode(accountCode);
            config.setAccountSubjectType(accountSubjectType);
            config.setAccountEngineCode(accountEngineCode);
            config.setBizId(bizId);
            config.setDeployEnv(ConfigUtils.getEnvRequired().name());
            config.setCreateTime(new Date());
            config.setUpdateTime(new Date());
            config.setDeleted(0);
            configs.add(config);
        }
        return configs;
    }

    /**
     * 查询配置
     * @param appId
     * @param configCode
     * @param accountCode
     * @return
     */
    public List<WcPayConfigCode> getConfigs(int appId, String configCode, String accountCode){
        WcPayConfigCode param = new WcPayConfigCode();
        param.setAppId(appId);
        param.setConfigCode(configCode);
        param.setAccountCode(accountCode);
        param.setDeployEnv(ConfigUtils.getEnvRequired().name());
        param.setDeleted(0);
        return payConfigCodeMapper.selectMany(param);
    }

}
