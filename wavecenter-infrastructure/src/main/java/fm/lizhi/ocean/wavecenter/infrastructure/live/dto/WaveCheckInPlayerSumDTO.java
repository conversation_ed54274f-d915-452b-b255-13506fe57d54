package fm.lizhi.ocean.wavecenter.infrastructure.live.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 麦序福利主播汇总数据DTO
 */
@Data
@Accessors(chain = true)
public class WaveCheckInPlayerSumDTO {

    /**
     * 魅力值
     */
    private Long charm;

    /**
     * 收入(钻石)
     */
    private Long income;

    /**
     * 总有效麦序数
     */
    private Integer seatCnt;

    /**
     * 总主持档数
     */
    private Integer hostCnt;
}
