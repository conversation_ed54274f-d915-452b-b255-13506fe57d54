package fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.common.dto.PageDto;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert.FamilyAwardDeliverItemConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert.FamilyAwardDeliverRecordConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.dao.FamilyAwardDeliverRecordDao;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilyAwardDeliverItem;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilyAwardDeliverRecord;
import fm.lizhi.ocean.wavecenter.service.award.family.dto.FamilyAwardDeliverItemDTO;
import fm.lizhi.ocean.wavecenter.service.award.family.dto.FamilyAwardDeliverRecordDTO;
import fm.lizhi.ocean.wavecenter.service.award.family.dto.GetFamilyAwardDeliverRecordParamDTO;
import fm.lizhi.ocean.wavecenter.service.award.family.manager.FamilyAwardDeliverRecordManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class FamilyAwardDeliverRecordManagerImpl implements FamilyAwardDeliverRecordManager {

    @Autowired
    private FamilyAwardDeliverItemConvert familyAwardDeliverItemConvert;

    @Autowired
    private FamilyAwardDeliverRecordDao recordDao;

    @Override
    public PageDto<FamilyAwardDeliverRecordDTO> getFamilyRecord(GetFamilyAwardDeliverRecordParamDTO param, int pageNum, int pageSize) {
        PageList<WcFamilyAwardDeliverRecord> pageList = recordDao.getDeliverRecordPage(param, pageNum, pageSize);
        List<FamilyAwardDeliverRecordDTO> dtoList = FamilyAwardDeliverRecordConvert.I.entitysToDTOs(pageList);
        return PageDto.of(pageList.getTotal(), dtoList);
    }

    @Override
    public List<FamilyAwardDeliverItemDTO> getFamilyAwardDeliverItem(List<Long> awardDeliverRecordIds) {
        List<WcFamilyAwardDeliverItem> itemList = recordDao.getDeliverItemByRecordIds(awardDeliverRecordIds);
        return familyAwardDeliverItemConvert.entitiesToDTOS(itemList);
    }

}
