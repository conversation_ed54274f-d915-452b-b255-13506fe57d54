package fm.lizhi.ocean.wavecenter.infrastructure.message.job;

import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.message.request.RequestSendMessageBatch;
import fm.lizhi.ocean.wavecenter.api.message.request.RequestSendMessageToRole;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.message.convert.MessageConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.message.dto.SendMessageJobDTO;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.FileExportRecordManager;
import fm.lizhi.ocean.wavecenter.service.message.manager.WaveCenterMessageManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 手动发送消息
 * <AUTHOR>
 */
@Slf4j
@Component
public class SendMessageJob implements JobHandler {

    @Autowired
    private WaveCenterMessageManager waveCenterMessageManager;

    @Override
    public void execute(JobExecuteContext jobExecuteContext) throws Exception {

        String param = jobExecuteContext.getParam();
        if (StringUtils.isBlank(param)) {
            log.info("param is blank");
            return;
        }
        log.info("SendMessageJob execute. param={}", param);

        SendMessageJobDTO dto  = JsonUtil.loads(param, SendMessageJobDTO.class);
        if (dto == null) {
            log.info("dto is null. param={}", param);
            return;
        }

        Result<List<Long>> result = null;
        if (dto.getSendType() == 1) {
            // 面向用户
            RequestSendMessageBatch request = MessageConvert.I.sendMessageJobDTO2RequestSendMessageBatch(dto);
            result = waveCenterMessageManager.sendMessageBatch(request);
        } else if (dto.getSendType() == 2){
            // 面向角色
            RequestSendMessageToRole request = MessageConvert.I.sendMessageJobDTO2RequestSendMessageToRole(dto);
            result = waveCenterMessageManager.sendMessage2Role(request);
        }

        if (null == result || RpcResult.isFail(result)){
            log.info("SendMessageJob is fail. param={}, result={}", param, result);
        }
    }
}