package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.remote.xm;

import cn.hutool.core.util.StrUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestBatchGetDecorate;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.constants.DecorateMapping;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert.ActivityDecorateConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.remote.IDecorateRemote;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.DecorateDTO;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.request.RequestGetDecorate;
import fm.lizhi.xm.vip.bean.decorate.req.GetDecorateListReq;
import fm.lizhi.xm.vip.bean.decorate.resp.GetDecorateListResp;
import fm.lizhi.xm.vip.bean.decorate.resp.PageDecorateDto;
import fm.lizhi.xm.vip.services.DecorateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class XmDecorateRemoteImpl implements IDecorateRemote {

    @Autowired
    private DecorateService decorateService;


    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.XIMI;
    }

    @Override
    public Result<PageBean<DecorateDTO>> getDecorateList(RequestGetDecorate request) {

        GetDecorateListReq req = new GetDecorateListReq();
        req.setType(DecorateMapping.waveType2Biz(request.getType(), BusinessEvnEnum.XIMI));
        req.setId(request.getDressUpId());
        req.setPageNum(request.getPageNo());
        req.setPageSize(request.getPageSize());
        

        if (StrUtil.isNotBlank(request.getName())) {
            req.setName(request.getName());
        }

        Result<GetDecorateListResp> result = decorateService.getDecorateList(req);
        if (RpcResult.isFail(result)) {
            log.warn("xm decorate result fail. type:{}, name:{}, pageNo:{}, pageSize:{}", request.getType(), request.getName(), request.getPageNo(), request.getPageSize());
            return new Result<>(result.rCode(), PageBean.empty());
        }

        GetDecorateListResp target = result.target();
        List<DecorateDTO> list = ActivityDecorateConvert.I.xmGetDecorateListResp2DecorateDTO(target.getDecorateList(), BusinessEvnEnum.XIMI);
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, PageBean.of((int) target.getTotal(), list));
    }

    @Override
    public Result<List<DecorateDTO>> batchGetDecorateList(RequestBatchGetDecorate request) {
        List<Long> ids = request.getIds();
        Result<List<PageDecorateDto>> decorateList = decorateService.getDecorateInfosByIds(ids);
        if (RpcResult.isFail(decorateList)) {
            log.warn("xm batch decorate result fail. type:{}, id:{}", request.getType(), ids);
            return RpcResult.fail(BATCH_GET_DECORATE_LIST_FAIL);
        }

        List<PageDecorateDto> target = decorateList.target();
        List<DecorateDTO> list = ActivityDecorateConvert.I.xmGetDecorateListResp2DecorateDTO(target, BusinessEvnEnum.XIMI);
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, list);
    }
}
