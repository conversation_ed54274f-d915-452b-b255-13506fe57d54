package fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @date 2024/10/10 18:18
 */
@DataStore(namespace = "mysql_heiye_lzppfamily_r")
public interface HyRoomWhitelistMapper {

    @Select("select count(*) from room_whitelist where user_id=#{userId} and status=1")
    int countUser(@Param("userId") long userId);

}
