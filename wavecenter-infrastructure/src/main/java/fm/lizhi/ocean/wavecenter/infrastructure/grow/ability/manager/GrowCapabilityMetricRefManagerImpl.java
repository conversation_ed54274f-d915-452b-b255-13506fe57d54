package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.manager;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowCapabilityMetricRef;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowCapabilityMetricRefExample;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.mapper.WcGrowCapabilityMetricRefMapper;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.GrowCapabilityMetricRefDTO;
import fm.lizhi.ocean.wavecenter.service.grow.ability.manager.GrowCapabilityMetricRefManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/10 20:14
 */
@Component
public class GrowCapabilityMetricRefManagerImpl implements GrowCapabilityMetricRefManager {

    @Autowired
    private WcGrowCapabilityMetricRefMapper capabilityMetricRefMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveRef(List<GrowCapabilityMetricRefDTO> dtos) {
        WcGrowCapabilityMetricRefExample example = new WcGrowCapabilityMetricRefExample();
        example.createCriteria()
                .andStartWeekDateEqualTo(dtos.get(0).getStartWeekDate())
                .andAppIdEqualTo(ContextUtils.getBusinessEvnEnum().getAppId())
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andEndWeekDateEqualTo(dtos.get(0).getEndWeekDate());
        capabilityMetricRefMapper.deleteByExample(example);

        List<WcGrowCapabilityMetricRef> pos = new ArrayList<>();
        for (GrowCapabilityMetricRefDTO dto : dtos) {
            WcGrowCapabilityMetricRef po = new WcGrowCapabilityMetricRef();
            po.setCapabilityCode(dto.getCapabilityCode());
            po.setMetricCode(dto.getMetricCode());
            po.setStartWeekDate(dto.getStartWeekDate());
            po.setEndWeekDate(dto.getEndWeekDate());
            po.setAppId(ContextUtils.getBusinessEvnEnum().getAppId());
            po.setDeployEnv(ConfigUtils.getEnvRequired().name());
            po.setCreateTime(new Date());
            po.setModifyTime(new Date());
            pos.add(po);
        }
        capabilityMetricRefMapper.batchInsert(pos);
    }
}
