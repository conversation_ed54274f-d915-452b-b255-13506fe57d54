package fm.lizhi.ocean.wavecenter.infrastructure.message.manager;

import fm.lizhi.ocean.wavecenter.infrastructure.message.constants.PushConstants;
import fm.lizhi.ocean.wavecenter.infrastructure.message.constants.SingerPushTopicEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.message.dto.PushDTO;
import fm.lizhi.ocean.wavecenter.infrastructure.message.dto.SingerStatusPushDTO;
import fm.lizhi.ocean.wavecenter.service.message.manager.SingerPushManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/25 14:28
 */
@Slf4j
@Component
public class SingerPushManagerImpl extends AbsRomePushManager implements SingerPushManager {

    /**
     * 推送歌手审核状态变更
     *
     * @param singerId 歌手id
     */
    @Override
    public void pushVerifyStatusChange(int appId, long singerId) {
        try {
            String key = SingerPushTopicEnum.SINGER_VERIFY_STATUS_CHANGE.getKey(appId, singerId);
            // 发送罗马消息
            PushDTO<SingerStatusPushDTO> pushDTO = new PushDTO<>();
            pushDTO.setBiz(PushConstants.Biz.SINGER_VERIFY_STATUS_CHANGE);
            pushDTO.setData(new SingerStatusPushDTO().setUserId(String.valueOf(singerId)));
            super.pushWaveMessage(key, pushDTO);
        } catch (Exception e) {
            log.error("发送消息失败, userId={}", singerId, e);
        }
    }

    @Override
    public void batchPushVerifyStatusChange(int appId, List<Long> singerIds) {
        for (Long singerId : singerIds) {
            pushVerifyStatusChange(appId, singerId);
        }
    }

}
