package fm.lizhi.ocean.wavecenter.infrastructure.resource.recommendcard.manager;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.BatchSendUserResultBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.RecommendCardRewardBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.RecommendCardSendRecordBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.RecommendCardUseRecordBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.request.*;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.response.RewardResultBean;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.recommendcard.remote.RecommendCardRemote;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto.GetUseRecordParamDTO;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto.RecommendAllocationRecordDTO;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto.RecommendCardUseRecordDTO;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto.UserRecommendCardStockDTO;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.manager.RecommendCardManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/21 15:21
 */
@Slf4j
@Component
public class RecommendCardManagerImpl implements RecommendCardManager {

    @Autowired
    private RecommendCardRemote recommendCardRemote;

    @Override
    public List<UserRecommendCardStockDTO> getUserStock(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        return recommendCardRemote.getUserStock(userIds);
    }

    @Override
    public Integer getExpireNum(Long userId, Date startDate, Date endDate) {
        log.info("userId={},startDate={},endDate={}", userId, startDate, endDate);
        if (userId == null || endDate == null) {
            return 0;
        }
        return recommendCardRemote.getExpireNum(userId, startDate, endDate);
    }

    @Override
    public RewardResultBean rewardRecommendCard(Long operatorUserId, List<RecommendCardRewardBean> rewardBeans) {
        if (CollectionUtils.isEmpty(rewardBeans)) {
            return null;
        }

        return recommendCardRemote.rewardRecommendCard(operatorUserId, rewardBeans);
    }

    @Override
    public PageBean<RecommendCardUseRecordBean> getUseRecordForManagement(RequestGetUseRecord request) {
        return recommendCardRemote.getUseRecordForManagement(request);
    }

    @Override
    public PageBean<RecommendCardSendRecordBean> getSendRecord(RequestGetSendRecord request) {
        return recommendCardRemote.getSendRecord(request);
    }

    @Override
    public List<BatchSendUserResultBean> batchSend(RequestBatchSend request) {
        return recommendCardRemote.batchSend(request);
    }

    @Override
    public boolean recycle(RequestRecycle request) {
        return recommendCardRemote.recycle(request);
    }

    @Override
    public PageBean<RecommendAllocationRecordDTO> getAllocationRecord(RequestGetAllocationRecord request) {
        return recommendCardRemote.getAllocationRecord(request);
    }

    @Override
    public PageBean<RecommendCardUseRecordDTO> getUseRecord(GetUseRecordParamDTO paramDTO) {
        return recommendCardRemote.getUseRecord(paramDTO);
    }
}
