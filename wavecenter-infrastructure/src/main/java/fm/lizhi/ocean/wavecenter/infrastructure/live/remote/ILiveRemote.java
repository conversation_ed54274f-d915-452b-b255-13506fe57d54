package fm.lizhi.ocean.wavecenter.infrastructure.live.remote;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.base.remote.IRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.live.dto.LiveDto;
import fm.lizhi.ocean.wavecenter.infrastructure.live.request.*;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/5/24 19:20
 */
public interface ILiveRemote extends IRemote {

    /**
     * 查询直播内容
     *
     * @param ids
     * @return
     */
    List<LiveDto> getLiveByIds(List<Long> ids);

    /**
     * 通过直播ID查询直播信息
     *
     * @param liveId
     * @return
     */
    Optional<LiveDto> getLiveInfo(Long liveId);

    /**
     * 获取最新的直播间信息
     *
     * @param userId 用户ID
     * @return 直播间信息
     */
    Optional<Long> getLatestLiveIdByUserId(Long userId);

    /**
     * 查询房间公告
     *
     * @param request 请求参数
     * @return 结果
     */
    Result<GetRoomNoticeResponse> getRoomNotice(GetRoomNoticeRequest request);

    /**
     * 编辑房间公告
     *
     * @param request 请求参数
     * @return 结果
     */
    Result<Void> editRoomNotice(EditRoomNoticeRequest request);

    /**
     * 根据主播ID查询房间信息
     *
     * @param userId 主播ID
     * @return 结果
     */
    Result<GetRoomInfoByNjIdResponse> getRoomInfoByNjId(Long userId);

    int GER_ROOM_NOTICE_FAIL = 1;


    /**
     * 编辑房间公告失败
     */
    int EDIT_ROOM_NOTICE_FAIL = 1;

    /**
     * 根据主播ID查询房间信息失败
     */
    int GET_ROOM_INFO_BY_NJ_ID_FAIL = 1;

    /**
     * 直播间不存在
     */
    int GET_ROOM_INFO_BY_NJ_ID_NO_EXIST = 2;


}
