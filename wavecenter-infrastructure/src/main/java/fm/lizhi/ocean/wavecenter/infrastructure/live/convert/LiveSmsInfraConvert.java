package fm.lizhi.ocean.wavecenter.infrastructure.live.convert;

import fm.lizhi.ocean.wavecenter.api.live.bean.PlayerSmsStatBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.RoomSmsStatBean;
import fm.lizhi.ocean.wavecenter.base.util.CalculateUtil;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.*;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/20 17:48
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface LiveSmsInfraConvert {

    LiveSmsInfraConvert I = Mappers.getMapper(LiveSmsInfraConvert.class);

    @Mappings({
            @Mapping(source = "roomId", target = "roomInfo.id"),

    })
    RoomSmsStatBean roomFamilyDayPo2SmsBean(WcDataRoomFamilyDay po);

    List<RoomSmsStatBean> roomFamilyDayPos2SmsBeans(List<WcDataRoomFamilyDay> pos);

    @Mappings({
            @Mapping(source = "roomId", target = "roomInfo.id"),
            @Mapping(source = "playerId", target = "playerInfo.id"),

    })
    PlayerSmsStatBean playerRoomDayPo2SmsBean(WcDataPlayerRoomDay po);

    List<PlayerSmsStatBean> playerRoomDayPos2SmsBeans(List<WcDataPlayerRoomDay> pos);

    @Mappings({
            @Mapping(source = "playerId", target = "playerInfo.id"),

    })
    PlayerSmsStatBean playerDayPo2SmsBean(WcDataPlayerDay po);

    List<PlayerSmsStatBean> playerDayPos2SmsBeans(List<WcDataPlayerDay> pos);

    @Mappings({
            @Mapping(source = "roomId", target = "roomInfo.id"),

    })
    RoomSmsStatBean roomFamilyWeekPo2SmsBean(WcDataRoomFamilyWeek po);

    List<RoomSmsStatBean> roomFamilyWeekPos2SmsBeans(List<WcDataRoomFamilyWeek> pos);

    @Mappings({
            @Mapping(source = "roomId", target = "roomInfo.id"),
            @Mapping(source = "playerId", target = "playerInfo.id"),

    })
    PlayerSmsStatBean playerRoomWeekPo2SmsBean(WcDataPlayerRoomWeek po);

    List<PlayerSmsStatBean> playerRoomWeekPos2SmsBeans(List<WcDataPlayerRoomWeek> pos);

    @Mappings({
            @Mapping(source = "playerId", target = "playerInfo.id"),

    })
    PlayerSmsStatBean playerWeekPo2SmsBean(WcDataPlayerWeek po);

    List<PlayerSmsStatBean> playerWeekPos2SmsBeans(List<WcDataPlayerWeek> pos);

    @Mappings({
            @Mapping(source = "roomId", target = "roomInfo.id"),

    })
    RoomSmsStatBean roomFamilyMonthPo2SmsBean(WcDataRoomFamilyMonth po);

    List<RoomSmsStatBean> roomFamilyMonthPos2SmsBeans(List<WcDataRoomFamilyMonth> pos);

    @Mappings({
            @Mapping(source = "roomId", target = "roomInfo.id"),
            @Mapping(source = "playerId", target = "playerInfo.id"),

    })
    PlayerSmsStatBean playerRoomMonthPo2SmsBean(WcDataPlayerRoomMonth po);

    List<PlayerSmsStatBean> playerRoomMonthPos2SmsBeans(List<WcDataPlayerRoomMonth> pos);

    @Mappings({
            @Mapping(source = "playerId", target = "playerInfo.id"),

    })
    PlayerSmsStatBean playerMonthPo2SmsBean(WcDataPlayerMonth po);

    List<PlayerSmsStatBean> playerMonthPos2SmsBeans(List<WcDataPlayerMonth> pos);

    @Named("convertRate")
    default String convertRate(BigDecimal bigDecimal){
        if (bigDecimal == null) {
            return CalculateUtil.formatPercent("0");
        }
        return CalculateUtil.formatPercent(bigDecimal);
    }

}
