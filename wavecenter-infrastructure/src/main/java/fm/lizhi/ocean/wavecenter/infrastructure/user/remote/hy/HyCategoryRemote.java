package fm.lizhi.ocean.wavecenter.infrastructure.user.remote.hy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wavecenter.infrastructure.user.datastore.mapper.HyRoomWhitelistCategoryMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.user.po.RoomWhitelistCategoryPo;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.ICategoryRemote;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/27 16:59
 */
@Slf4j
@Component
public class HyCategoryRemote implements ICategoryRemote {

    @Autowired
    private HyRoomWhitelistCategoryMapper roomWhitelistCategoryMapper;
    @Autowired
    private CommonConfig commonConfig;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.HEI_YE;
    }

    @Override
    public Map<Long, String> getCategoryNameByRoomIds(List<Long> roomIds) {
        if (CollectionUtils.isEmpty(roomIds)) {
            return Collections.emptyMap();
        }

        List<RoomWhitelistCategoryPo> list = roomWhitelistCategoryMapper.getCategoryIdByUserIds(roomIds);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        List<Long> categoryIds = list.stream().map(RoomWhitelistCategoryPo::getCategoryId).collect(Collectors.toList());
        Map<Long, List<RoomWhitelistCategoryPo>> roomMap = list.stream().collect(Collectors.groupingBy(RoomWhitelistCategoryPo::getUserId));

        Map<Long, String> nameMap = new HashMap<>();
        try {
            log.info("hy getCategoryNameByIds categoryIds={}", JsonUtil.dumps(categoryIds));

            String roomTypeNameStr = commonConfig.getHy().getRoomTypeName();
            if (StringUtils.isNotBlank(roomTypeNameStr)) {
                JSONArray array = JSON.parseArray(roomTypeNameStr);
                for (int i = 0; i < array.size(); i++) {
                    JSONObject jsonObject = array.getJSONObject(i);
                    String tabId = jsonObject.getString("tabId");
                    String tabName = jsonObject.getString("tabName");
                    nameMap.put(Long.valueOf(tabId), tabName);
                }
            }
        } catch (Exception e) {
            log.warn("getCategoryNameByIds error:", e);
        }
        log.info("hy getCategoryNameByIds nameMap={}", JsonUtil.dumps(nameMap));

        Map<Long, String> resultMap = new HashMap<>();
        for (Map.Entry<Long, List<RoomWhitelistCategoryPo>> entry : roomMap.entrySet()) {
            Long roomId = entry.getKey();
            List<RoomWhitelistCategoryPo> cateList = entry.getValue();
            if (CollectionUtils.isEmpty(cateList)) {
                continue;
            }
            for (RoomWhitelistCategoryPo roomWhitelistCategoryPo : cateList) {
                Long categoryId = roomWhitelistCategoryPo.getCategoryId();
                String name = nameMap.get(categoryId);
                if (name != null) {
                    resultMap.put(roomId, name);
                }
            }
        }

        return resultMap;
    }
}
