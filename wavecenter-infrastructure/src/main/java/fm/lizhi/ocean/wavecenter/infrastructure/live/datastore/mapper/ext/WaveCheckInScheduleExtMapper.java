package fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WaveCheckInSchedule;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * 麦序福利只读库扩展mapper, 用于web站数据统计
 */
@DataStore(namespace = "mysql_ocean_wave_r")
public interface WaveCheckInScheduleExtMapper {

    @Select("<script>\n" +
            "  SELECT * FROM wave_check_in_schedule\n" +
            "  WHERE start_time &gt;= #{startDate} \n" +
            "    AND start_time &lt;= #{endDate}\n" +
            "    AND room_id = #{roomId}\n" +
            "    <if test=\"familyId != null\">\n" +
            "      AND family_id = #{familyId}\n" +
            "    </if>\n" +
            "</script>")
    List<WaveCheckInSchedule> getCheckInRoomSchedules(@Param("roomId") long roomId, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("familyId") Long familyId);

    @Select("<script>\n" +
            "  SELECT\n" +
            "    wave_check_in_schedule.*\n" +
            "  FROM\n" +
            "    wave_check_in_schedule\n" +
            "  LEFT JOIN\n" +
            "    wave_check_in_record ON wave_check_in_schedule.id = wave_check_in_record.schedule_id\n" +
            "  WHERE\n" +
            "    wave_check_in_record.user_id = #{playerId}\n" +
            "    AND wave_check_in_schedule.start_time &gt;= #{startDate}\n" +
            "    AND wave_check_in_schedule.start_time &lt;= #{endDate}\n" +
            "    <if test=\"familyId != null\">\n" +
            "      AND wave_check_in_schedule.`family_id` = #{familyId}\n" +
            "    </if>\n" +
            "    <if test=\"roomId != null\">\n" +
            "      AND wave_check_in_schedule.`room_id` = #{roomId}\n" +
            "    </if>\n" +
            "</script>")
    List<WaveCheckInSchedule> getCheckInPlayerSchedules(@Param("playerId") long playerId, @Param("startDate") Date startDate,
                                                        @Param("endDate") Date endDate, @Param("familyId") Long familyId,
                                                        @Param("roomId") Long roomId);

    @Select("<script>\n" +
            "  SELECT DISTINCT(host_id) FROM wave_check_in_schedule\n" +
            "  WHERE start_time &gt;= #{startDate} \n" +
            "    AND start_time &lt;= #{endDate}\n" +
            "    AND nj_id = #{njId}\n" +
        "        AND app_id = #{appId} AND host_id > 0\n" +
            "</script>")
    List<Long> getHostUserIds(@Param("appId")Integer appId, @Param("njId")Long njId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);
}
