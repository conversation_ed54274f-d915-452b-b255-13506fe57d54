package fm.lizhi.ocean.wavecenter.infrastructure.income.remote.xm;

import com.google.common.collect.Lists;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomIncomeDetailBean;
import fm.lizhi.ocean.wavecenter.api.income.constants.IncomeType;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.income.manager.PaymentManager;
import fm.lizhi.ocean.wavecenter.infrastructure.income.remote.IGuildIncomeRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.income.remote.req.GetSignRoomIncomeDetailReq;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/5/27 14:39
 */
@Slf4j
@Component
public class XmGuildIncomeRemote implements IGuildIncomeRemote {

    @Autowired
    private UserManager userManager;
    @Autowired
    private PaymentManager paymentManager;

    @Override
    public List<RoomIncomeDetailBean> getSignRoomIncomeDetail(GetSignRoomIncomeDetailReq req) {
        Date startDate = req.getStartDate();
        Date endDate = req.getEndDate();
        List<Long> roomIds = req.getRoomIds();
        Long familyId = req.getFamilyId();
        Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(roomIds);
        List<RoomIncomeDetailBean> beanList = new ArrayList<>();

        //签约厅
        Map<Long, Long> roomMap = paymentManager.batchGetIncomeByRoom(familyId, roomIds, startDate, endDate
                , Lists.newArrayList(IncomeType.PGC_UN_SIGN_HALL_INCOME.getValue(), IncomeType.SIGN_HALL_INCOME.getValue()));
        //官方
        Map<Long, Long> officialMap = paymentManager.batchGetIncomeByRoom(familyId, roomIds, startDate, endDate
                , Lists.newArrayList(IncomeType.OFFICIAL_INCOME.getValue()));

        for (Long id : roomIds) {
            SimpleUserDto simpleUserDto = userMap.get(id);
            if (simpleUserDto == null) {
                continue;
            }
            UserBean njUser = new UserBean();
            njUser.setId(simpleUserDto.getId());
            njUser.setBand(simpleUserDto.getBand());
            njUser.setName(simpleUserDto.getName());

            RoomIncomeDetailBean bean = new RoomIncomeDetailBean();
            bean.setRoomInfo(njUser);
            bean.setIncome(roomMap.getOrDefault(id, 0L)
                    + officialMap.getOrDefault(id, 0L));

            //过滤总收入 为0的
            if(bean.getIncome()<=0){
                continue;
            }

            bean.setSignRoomIncome(roomMap.getOrDefault(id, 0L));
            bean.setOfficialIncome(officialMap.getOrDefault(id, 0L));
            //西米默认娱乐
            bean.setCateName("娱乐");
            beanList.add(bean);
        }
        return beanList;
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.XIMI;
    }
}
