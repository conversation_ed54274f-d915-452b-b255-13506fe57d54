package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.processor.xm;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityResourceDeployTypeConstants;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.AutoConfigResourceEnum;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityResource;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityResource;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.dao.ActivityResourceDao;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityResourceConfig;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.processor.IResourceConfigProcess;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityResourceManager;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class XmResourceConfigProcess implements IResourceConfigProcess {

    @Autowired
    private ActivityResourceDao activityResourceDao;

    @Autowired
    private ActivityResourceManager activityResourceManager;

    @Override
    public void bindSaveProGramme(RequestSaveActivityResource resourceInfo) {
        if (resourceInfo.getResourceCode() == null || !resourceInfo.getResourceCode().equals(AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode())) {
            return;
        }

        //如果是官频，自动保存节目单
        resourceInfo.setResourceCode(AutoConfigResourceEnum.PROGRAMME.getResourceCode());
        resourceInfo.setName(AutoConfigResourceEnum.PROGRAMME.getResourceName());
        boolean programmeSaveResult = activityResourceDao.saveSingleActivityResource(resourceInfo);
        if (!programmeSaveResult) {
            log.warn("save programme resource failed, appId: {}, resourceCode:{}", resourceInfo.getAppId(), resourceInfo.getResourceCode());
            throw new RuntimeException("资源保存失败");
        }
    }

    @Override
    public void bindUpdateProGramme(RequestUpdateActivityResource param) {
        //如果是官频，自动更新节目单
        if (!AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode().equals(param.getResourceCode())) {
            return;
        }
        //获取关联的节目单信息
        List<ActivityResourceConfig> activityResourceConfigs = activityResourceDao.getProgrammeInfo(param.getAppId());
        if (CollectionUtils.isEmpty(activityResourceConfigs)) {
            return;
        }
        
        for (ActivityResourceConfig activityResourceConfig : activityResourceConfigs) {
            param.setId(activityResourceConfig.getId());
            param.setResourceCode(AutoConfigResourceEnum.PROGRAMME.getResourceCode());
            param.setName(AutoConfigResourceEnum.PROGRAMME.getResourceName());
            boolean updateProgrammeResult = activityResourceDao.updateSingleActivityResource(param);
            if (!updateProgrammeResult) {
                log.warn("update programme resource failed, appId: {}, resourceCode:{}", param.getAppId(), param.getResourceCode());
                throw new RuntimeException("资源更新失败");
            }
        }
    }

    @Override
    public void bindDeleteProGramme(ActivityResourceConfig activityResourceConfig, String operator) {
        if (!AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode().equals(activityResourceConfig.getResourceCode())) {
            return;
        }

        List<ActivityResourceConfig> programmeResourceConfigs = activityResourceDao.getProgrammeInfo(activityResourceConfig.getAppId());
        if (CollectionUtils.isEmpty(programmeResourceConfigs)) {
            return;
        }

        for (ActivityResourceConfig programmeResourceConfig : programmeResourceConfigs) {
            boolean deleteProgrammeResult = activityResourceDao.deleteSingleActivityResource(programmeResourceConfig.getId(), operator);
            if (!deleteProgrammeResult) {
                log.warn("delete programme resource failed, appId: {}, resourceCode:{}", activityResourceConfig.getAppId(), AutoConfigResourceEnum.PROGRAMME.getResourceCode());
                throw new RuntimeException("资源删除失败");
            }
        }
    }

    /**
     * 仅西米存在检查逻辑，其他业务环境不检查
     * 主要检查官频位(含节目单)与节目单的互斥关心
     */
    @Override
    public Result<Void> checkResourceLevelRepeat(List<Long> levelIds, String sourceResourceCode, Integer resourceType) {
        //如果传入的是官频位，检查的则是节目单,如果传入的是节目单，检查的则是官频位
        String targetResouceCode = null;
        // 手动资源和非官频位、节目单的资源不检查
        if (ActivityResourceDeployTypeConstants.AUTO_CONFIG != resourceType
                || !(AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode().equals(sourceResourceCode) || AutoConfigResourceEnum.HALL_PROGRAMME.getResourceCode().equals(sourceResourceCode))) {
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
        }

        if (AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode().equals(sourceResourceCode)) {
            targetResouceCode = AutoConfigResourceEnum.HALL_PROGRAMME.getResourceCode();
        } else if (AutoConfigResourceEnum.HALL_PROGRAMME.getResourceCode().equals(sourceResourceCode)) {
            targetResouceCode = AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode();
        }
        return activityResourceManager.checkResourceLevelRepeat(levelIds, targetResouceCode);
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.XIMI;
    }
}
