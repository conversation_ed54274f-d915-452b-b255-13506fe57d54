package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.manager;

import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import fm.lizhi.ocean.wavecenter.api.grow.ability.bean.*;
import fm.lizhi.ocean.wavecenter.api.grow.ability.request.RequestGetPlayerPerformance;
import fm.lizhi.ocean.wavecenter.api.grow.ability.request.RequestGetRoomPerformance;
import fm.lizhi.ocean.wavecenter.api.grow.ability.request.RequestGetRoomPlayerRank;
import fm.lizhi.ocean.wavecenter.api.grow.ability.response.ResponseGetPlayerPerformance;
import fm.lizhi.ocean.wavecenter.api.grow.ability.response.ResponseGetRoomPerformance;
import fm.lizhi.ocean.wavecenter.api.grow.ability.response.ResponseGetRoomPlayerRank;
import fm.lizhi.ocean.wavecenter.common.utils.DateTimeUtils;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.constants.CapabilityMetricEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.convert.AbilityPerformanceConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.dao.WcGrowAbilityDao;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.*;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.capability.datastore.dao.WcGrowCapabilityDao;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.GrowPlayerTaskDTO;
import fm.lizhi.ocean.wavecenter.service.grow.ability.manager.AbilityPerformanceManager;
import fm.lizhi.ocean.wavecenter.service.grow.ability.manager.GrowPlayerTaskManager;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto.ConditionDTO;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto.TaskTemplateConditionDTO;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.manager.TaskTemplateManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListValuedMap;
import org.apache.commons.collections4.SetValuedMap;
import org.apache.commons.collections4.multimap.ArrayListValuedHashMap;
import org.apache.commons.collections4.multimap.HashSetValuedHashMap;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class AbilityPerformanceManagerImpl implements AbilityPerformanceManager {

    @Autowired
    private AbilityPerformanceConvert abilityPerformanceConvert;

    @Autowired
    private WcGrowAbilityDao wcGrowAbilityDao;

    @Autowired
    private WcGrowCapabilityDao wcGrowCapabilityDao;

    @Autowired
    private UserManager userManager;

    @Autowired
    private GrowPlayerTaskManager growPlayerTaskManager;

    @Autowired
    private TaskTemplateManager taskTemplateManager;

    @Override
    public ResponseGetRoomPerformance getRoomPerformance(RequestGetRoomPerformance request) {
        Integer appId = request.getAppId();
        Long roomId = request.getRoomId();
        Date startWeekDate = DateTimeUtils.parseDate(request.getStartWeekDate());
        List<WcGrowRoomAbilityWeekCapability> capabilities = wcGrowAbilityDao.getRoomWeekCapabilities(appId, roomId, startWeekDate);
        // 如果没有能力项, 可能为历史周期没有结算, 或者本周期该厅没有收入
        if (CollectionUtils.isEmpty(capabilities)) {
            return ResponseGetRoomPerformance.ofEmpty();
        }
        List<String> capabilityCodes = capabilities.stream().map(WcGrowRoomAbilityWeekCapability::getCapabilityCode).collect(Collectors.toList());
        Map<String, String> capabilityCodeNameMap = wcGrowCapabilityDao.getAllCapabilityCodeNameMap(appId);
        ListValuedMap<String, String> capabilityMetricCodesMap = wcGrowAbilityDao.getCapabilityMetricCodesMap(appId, startWeekDate);
        Date lastWeekStartDate = DateTimeUtils.minus1Week(startWeekDate);
        Map<Date, WcGrowRoomMetricValue> metricMap = wcGrowAbilityDao.getWeeksRoomMetricValueMap(appId, roomId, lastWeekStartDate, startWeekDate);
        WcGrowRoomMetricValue thisWeekMetric = metricMap.get(startWeekDate);
        WcGrowRoomMetricValue lastWeekMetric = metricMap.get(lastWeekStartDate);
        // key: 能力项code, value: 指标bean列表
        ListValuedMap<String, RoomAbilityMetricBean> metricBeanMap = new ArrayListValuedHashMap<>();
        for (String capabilityCode : capabilityCodes) {
            List<String> metricCodes = capabilityMetricCodesMap.get(capabilityCode);
            for (String metricCode : metricCodes) {
                CapabilityMetricEnum metricEnum = CapabilityMetricEnum.from(metricCode);
                if (metricEnum != null) {
                    String metricName = metricEnum.getName();
                    BigDecimal thisWeekValue = thisWeekMetric != null ? metricEnum.getRoomMetricExtractor().apply(thisWeekMetric) : null;
                    BigDecimal lastWeekValue = lastWeekMetric != null ? metricEnum.getRoomMetricExtractor().apply(lastWeekMetric) : null;
                    BigDecimal thisWeekValueChange = thisWeekValue != null && lastWeekValue != null ? thisWeekValue.subtract(lastWeekValue) : null;
                    metricBeanMap.put(capabilityCode, RoomAbilityMetricBean.of(metricCode, metricName, thisWeekValue, thisWeekValueChange));
                }
            }
        }
        List<RoomAbilityBean> abilities = abilityPerformanceConvert.toRoomAbilityBeans(capabilities, capabilityCodeNameMap, metricBeanMap);
        return ResponseGetRoomPerformance.of(abilities);
    }

    @Override
    public ResponseGetRoomPlayerRank getRoomPlayerRank(RequestGetRoomPlayerRank request) {
        Integer appId = request.getAppId();
        Long roomId = request.getRoomId();
        Date startWeekDate = DateTimeUtils.parseDate(request.getStartWeekDate());
        String orderByCapabilityCode = request.getOrderByCapabilityCode();
        String orderByDirection = request.getOrderByDirection();
        Boolean onlyNewPlayer = request.getOnlyNewPlayer();
        Boolean firstSignInFamily = BooleanUtils.isTrue(onlyNewPlayer) ? Boolean.TRUE : null;
        OrderType orderType = ObjectUtils.defaultIfNull(OrderType.fromValue(orderByDirection), OrderType.ASC);
        // 新主播定义: 与所在公会首次签约且签约时间<=30天
        Date minSignDate = BooleanUtils.isTrue(onlyNewPlayer) ? Date.from(LocalDateTime.now().minusDays(30).atZone(ZoneId.systemDefault()).toInstant()) : null;
        if (StringUtils.isBlank(orderByCapabilityCode)) {
            // 如果没有指定排序的能力项, 则默认查的是综合分且按综合分排序
            List<WcGrowPlayerAbilityWeek> weeks = wcGrowAbilityDao.getPlayerAbilityWeekRanks(appId, roomId, startWeekDate, firstSignInFamily, minSignDate, orderType);
            if (CollectionUtils.isEmpty(weeks)) {
                return ResponseGetRoomPlayerRank.ofEmpty();
            }
            List<Long> playerIds = weeks.stream().map(WcGrowPlayerAbilityWeek::getPlayerId).distinct().collect(Collectors.toList());
            Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(playerIds);
            List<RoomPlayerRankBean> rankBeans = abilityPerformanceConvert.playerAbilityWeeksToBeans(weeks, userMap);
            return ResponseGetRoomPlayerRank.of(rankBeans);
        } else {
            // 如果指定了排序的能力项, 则查询该能力项分且按该能力项分排序
            List<WcGrowPlayerAbilityWeekCapability> capabilities = wcGrowAbilityDao.getPlayerAbilityWeekCapabilityRanks(appId, roomId, startWeekDate, orderByCapabilityCode, firstSignInFamily, minSignDate, orderType);
            if (CollectionUtils.isEmpty(capabilities)) {
                return ResponseGetRoomPlayerRank.ofEmpty();
            }
            List<Long> playerIds = capabilities.stream().map(WcGrowPlayerAbilityWeekCapability::getPlayerId).distinct().collect(Collectors.toList());
            Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(playerIds);
            List<RoomPlayerRankBean> rankBeans = abilityPerformanceConvert.playerAbilityWeekCapabilitiesToBeans(capabilities, userMap);
            return ResponseGetRoomPlayerRank.of(rankBeans);
        }
    }

    @Override
    public ResponseGetPlayerPerformance getPlayerPerformance(RequestGetPlayerPerformance request) {
        Integer appId = request.getAppId();
        Long roomId = request.getRoomId();
        Long playerId = request.getPlayerId();
        Date startWeekDate = DateTimeUtils.parseDate(request.getStartWeekDate());
        // 查询全部能力项code和名称映射
        Map<String, String> capabilityCodeNameMap = wcGrowCapabilityDao.getAllCapabilityCodeNameMap(appId);

        // 查询指定周和前后N周的能力项
        int aroundWeeks = 2;
        Date minStartWeekDate = DateTimeUtils.minusWeeks(startWeekDate, aroundWeeks);
        Date maxStartWeekDate = DateTimeUtils.plusWeeks(startWeekDate, aroundWeeks);
        ListValuedMap<Date, WcGrowPlayerAbilityWeekCapability> capabilitiesMap = wcGrowAbilityDao
                .getPlayerAbilityWeekCapabilitiesMap(appId, roomId, playerId, minStartWeekDate, maxStartWeekDate);

        // 查询指定周和前后N周的指标项, key: 周开始日期, value: 能力项code -> 指标code列表
        // 主播考核的指标项
        Map<Date, SetValuedMap<String, String>> assessMetricMap = getPlayerWeeksCapabilityMetricCodesMap(playerId, minStartWeekDate, maxStartWeekDate);
        // 考核周期能力相关的所有指标项
        Map<Date, SetValuedMap<String, String>> metricCodesMap = wcGrowAbilityDao.getWeeksCapabilityMetricCodesMap(appId, minStartWeekDate, maxStartWeekDate);

        // 查询指定周、后退N+1周和前进N周的指标值
        int backwardWeeks = aroundWeeks + 1;
        Date metricMinStartWeekDate = DateTimeUtils.minusWeeks(startWeekDate, backwardWeeks);
        Date metricMaxStartWeekDate = DateTimeUtils.plusWeeks(startWeekDate, aroundWeeks);

        // key=考核周期开始时间 value=考核周期指标值
        Map<Date, WcGrowPlayerMetricValue> metricsMap = wcGrowAbilityDao.getWeeksPlayerMetricValueMap(appId, roomId, playerId, metricMinStartWeekDate, metricMaxStartWeekDate);
        // 厅的考核周期指标值
        Map<Date, WcGrowRoomMetricValue> roomMetricMap = wcGrowAbilityDao.getWeeksRoomMetricValueMap(appId, roomId, metricMinStartWeekDate, metricMaxStartWeekDate);

        // 构建结果数据, 最多返回至上一周的数据
        Date LastWeekStartDate = DateTimeUtils.getLastWeekStartTime();
        ArrayList<PlayerAbilityWeekBean> weekBeans = new ArrayList<>();
        for (Date thisWeekStartDate = minStartWeekDate; !thisWeekStartDate.after(maxStartWeekDate) && !thisWeekStartDate.after(LastWeekStartDate); thisWeekStartDate = DateTimeUtils.plus1Week(thisWeekStartDate)) {
            // 查询当前遍历周期的 能力项和指标的关联map  key=能力项code value=指标code列表  Map<String, Set<String>
            SetValuedMap<String, String> capabilityMetricCodesMap = metricCodesMap.getOrDefault(thisWeekStartDate, new HashSetValuedHashMap<>());
            // 查询当前遍历周期的 指标值
            WcGrowPlayerMetricValue thisWeekMetric = metricsMap.get(thisWeekStartDate);
            WcGrowRoomMetricValue roomThisWeekMetric = roomMetricMap.get(thisWeekStartDate);
            // 查询当前遍历周期的 上周指标值
            WcGrowPlayerMetricValue lastWeekMetric = metricsMap.get(DateTimeUtils.minus1Week(thisWeekStartDate));
            // 查询当前遍历周期的 考核的能力项和指标的关联map key=能力项code value=指标code列表  Map<String, Set<String>
            SetValuedMap<String, String> assessCapabilityMetricMap = assessMetricMap.getOrDefault(thisWeekStartDate, new HashSetValuedHashMap<>());

            ArrayList<PlayerAbilityBean> abilityBeans = new ArrayList<>();
            for (WcGrowPlayerAbilityWeekCapability capability : capabilitiesMap.get(thisWeekStartDate)) {
                // 构造周指标bean列表
                ArrayList<PlayerAbilityMetricBean> metricBeans = new ArrayList<>();
                ArrayList<RoomAbilityMetricBean> roomMetrics = new ArrayList<>();
                for (String metricCode : capabilityMetricCodesMap.get(capability.getCapabilityCode())) {
                    CapabilityMetricEnum metricEnum = CapabilityMetricEnum.from(metricCode);
                    if (metricEnum != null) {
                        // 主播指标
                        String metricName = metricEnum.getName();
                        BigDecimal thisWeekMetricValue = thisWeekMetric != null ? metricEnum.getPlayerMetricExtractor().apply(thisWeekMetric) : null;
                        BigDecimal lastWeekMetricValue = lastWeekMetric != null ? metricEnum.getPlayerMetricExtractor().apply(lastWeekMetric) : null;
                        BigDecimal thisWeekValueChange = thisWeekMetricValue != null && lastWeekMetricValue != null ? thisWeekMetricValue.subtract(lastWeekMetricValue) : null;
                        PlayerAbilityMetricBean metricBean = PlayerAbilityMetricBean.of(metricCode, metricName, thisWeekMetricValue, thisWeekValueChange);
                        metricBeans.add(metricBean);
                        // 厅指标
                        BigDecimal roomThisWeekValue = roomThisWeekMetric != null ? metricEnum.getRoomMetricExtractor().apply(roomThisWeekMetric) : null;
                        RoomAbilityMetricBean roomMetricBean = RoomAbilityMetricBean.of(metricCode, roomThisWeekValue);
                        roomMetrics.add(roomMetricBean);
                    }
                }

                // 考核指标
                Set<String> assessMetrics = assessCapabilityMetricMap.get(capability.getCapabilityCode());

                // 构造周能力bean
                PlayerAbilityBean abilityBean = abilityPerformanceConvert.toPlayerAbilityBean(capability, metricBeans, new ArrayList<>(assessMetrics), roomMetrics, capabilityCodeNameMap);
                abilityBeans.add(abilityBean);
            }
            PlayerAbilityWeekBean weekBean = abilityPerformanceConvert.toPlayerAbilityWeekBean(thisWeekStartDate, abilityBeans);
            weekBeans.add(weekBean);
        }
        return ResponseGetPlayerPerformance.of(weekBeans);
    }

    /**
     * 查询主播在时间周期内，每周完成的任务对应的指标
     *
     * @param playerId
     * @param minStartWeekDate
     * @param maxStartWeekDate
     * @return key=周开始时间, value=能力项code -> 指标项code列表
     */
    private Map<Date, SetValuedMap<String, String>> getPlayerWeeksCapabilityMetricCodesMap(Long playerId, Date minStartWeekDate, Date maxStartWeekDate) {
        List<GrowPlayerTaskDTO> tasks = growPlayerTaskManager.getPlayerFinishByTime(playerId, DateUtil.getDayStart(minStartWeekDate), DateUtil.getDayEnd(maxStartWeekDate));
        List<Long> templateIds = tasks.stream().map(GrowPlayerTaskDTO::getTemplateId).distinct().collect(Collectors.toList());

        // 每周完成的任务列表 key: 周开始时间, value: 完成的任务列表
        Map<Date, List<GrowPlayerTaskDTO>> weeksFinishTaskList = tasks.stream().collect(Collectors.groupingBy(GrowPlayerTaskDTO::getStartWeekDate));

        List<TaskTemplateConditionDTO> templates = taskTemplateManager.queryTaskTemplateConditionByIds(templateIds);
        Map<Long, TaskTemplateConditionDTO> templateMap = templates.stream().collect(Collectors.toMap(TaskTemplateConditionDTO::getId, v -> v));

        // Map<Date,Map<String,Set<String>>> key=周开始时间, value=能力项code -> 指标项code列表
        Map<Date, SetValuedMap<String, String>> result = new HashMap<>();
        for (Map.Entry<Date, List<GrowPlayerTaskDTO>> weekTaskEntry : weeksFinishTaskList.entrySet()) {
            Date weekStart = weekTaskEntry.getKey();
            List<GrowPlayerTaskDTO> weekTasks = weekTaskEntry.getValue();

            // Map<String,Set<String>> value=能力项code -> 指标项code列表
            SetValuedMap<String, String> capabilityMetricMap = result.computeIfAbsent(weekStart, k -> new HashSetValuedHashMap<>());
            for (GrowPlayerTaskDTO weekTask : weekTasks) {
                TaskTemplateConditionDTO template = templateMap.get(weekTask.getTemplateId());
                if (template == null) {
                    continue;
                }

                Set<String> codeList = capabilityMetricMap.get(template.getCapability().getCapabilityCode());
                codeList.addAll(template.getConditionGroup().getConditionList().stream()
                        .map(ConditionDTO::getMetricCode)
                        .collect(Collectors.toSet())
                );
            }
        }

        return result;
    }
}
