package fm.lizhi.ocean.wavecenter.infrastructure.user.remote.req;

import lombok.Data;

@Data
public class SearchUserVerifyResultReq {

     /**
      * 应用ID
      */
     private Long appId;
     /**
      * 用户ID
      */
     private Long userId;
     /**
      * 证件号
      */
     private String idCardNumber;
     /**
      * 认证状态
      */
     private Integer verifyStatus;
     /**
      * 认证类型
      */
     private Integer verifyType;
     /**
      * 开始时间
      */
     private Long beginDate;
     /**
      * 结束时间
      */
     private Long endDate;

     /**
      * 查询类型
      */
     private Integer searchType;

}
