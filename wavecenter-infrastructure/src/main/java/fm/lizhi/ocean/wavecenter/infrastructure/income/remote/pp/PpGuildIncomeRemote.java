package fm.lizhi.ocean.wavecenter.infrastructure.income.remote.pp;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomIncomeDetailBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.income.manager.PaymentManager;
import fm.lizhi.ocean.wavecenter.infrastructure.income.remote.IGuildIncomeRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.income.remote.req.GetSignRoomIncomeDetailReq;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.ICategoryServiceRemote;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import fm.lizhi.pp.util.bean.LiveCategoryObject;
import fm.lizhi.pp.util.utils.ConfigUtil;
import fm.lizhi.trade.query.center.account.protocol.QueryAccountProto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/27 14:25
 */
@Slf4j
@Component
public class PpGuildIncomeRemote implements IGuildIncomeRemote {

    @Autowired
    private PaymentManager paymentManager;
    @Autowired
    private ICategoryServiceRemote iCategoryServiceRemote;
    @Autowired
    private UserManager userManager;

    @Override
    public List<RoomIncomeDetailBean> getSignRoomIncomeDetail(GetSignRoomIncomeDetailReq req) {
        Date startDate = req.getStartDate();
        Date endDate = req.getEndDate();
        List<Long> roomIds = req.getRoomIds();

        //跟业务的差异： 418, 364,530 （多了）
        List<QueryAccountProto.HistorySummary> historySummaries = paymentManager.batchQueryHistorySumInRange(
                startDate.getTime(), endDate.getTime(), 2,
                roomIds, Lists.newArrayList(2, 528, 376, 529, 2010008));
        // 厅贵族提成
        Map<Long, Long> userRoomNobilitySummaryAmount = getUserSummaryAmount(historySummaries, "376");
        log.info("userRoomNobilitySummaryAmount={}", JsonUtil.dumps(userRoomNobilitySummaryAmount));
        // 个播贵族提成
        Map<Long, Long> userPersonalNobilitySummaryAmount = getUserSummaryAmount(historySummaries, "529");
        log.info("userPersonalNobilitySummaryAmount={}", JsonUtil.dumps(userPersonalNobilitySummaryAmount));
        // 签约厅收礼
        Map<Long, Long> userAmusementHallSummaryAmount = getUserSummaryAmount(historySummaries, "2");
        log.info("userAmusementHallSummaryAmount={}", userAmusementHallSummaryAmount);
        // 个播收礼收入
        Map<Long, Long> userPersonalLiveSummaryAmount = getUserSummaryAmount(historySummaries, "528");
        log.info("userPersonalLiveSummaryAmount={}", userPersonalLiveSummaryAmount);
        // 官方厅收入
        Map<Long, Long> userOfficialSummaryAmount = getUserSummaryAmount(historySummaries, "2010008");
        log.info("userOfficialSummaryAmount={}", JsonUtil.dumps(userOfficialSummaryAmount));

        Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(roomIds);
        List<RoomIncomeDetailBean> roomIncomeDetailDtos = new ArrayList<>();
        for (Long id : roomIds) {
            SimpleUserDto simpleUserDto = userMap.get(id);
            if (simpleUserDto == null) {
                continue;
            }
            UserBean njUser = new UserBean();
            njUser.setId(simpleUserDto.getId());
            njUser.setBand(simpleUserDto.getBand());
            njUser.setName(simpleUserDto.getName());
            Long cateId = iCategoryServiceRemote.getUserCategoryByTime(id, startDate, endDate);

            RoomIncomeDetailBean roomIncomeDetailDto = new RoomIncomeDetailBean();
            roomIncomeDetailDto.setRoomInfo(njUser);
            roomIncomeDetailDto.setIncome(userRoomNobilitySummaryAmount.getOrDefault(id, 0L)
                    + userPersonalNobilitySummaryAmount.getOrDefault(id, 0L)
                    + userAmusementHallSummaryAmount.getOrDefault(id, 0L)
                    + userOfficialSummaryAmount.getOrDefault(id, 0L)
                    + userPersonalLiveSummaryAmount.getOrDefault(id, 0L));
//            AdminPlayerIncomeInfoPo adminPlayerIncomeInfoPo = njIncomeInfoMap.get(id);
//            roomIncomeDetailDto.setCharm(adminPlayerIncomeInfoPo != null ? adminPlayerIncomeInfoPo.getTotalCharmValue() : 0L);
            roomIncomeDetailDto.setSignRoomIncome(userAmusementHallSummaryAmount.getOrDefault(id, 0L));
            roomIncomeDetailDto.setPersonalRoomIncome(userPersonalLiveSummaryAmount.getOrDefault(id, 0L));
            roomIncomeDetailDto.setSignRoomVipIncome(userRoomNobilitySummaryAmount.getOrDefault(id, 0L));
            roomIncomeDetailDto.setPersonalRoomVipIncome(userPersonalNobilitySummaryAmount.getOrDefault(id, 0L));
            roomIncomeDetailDto.setOfficialIncome(userOfficialSummaryAmount.getOrDefault(id, 0L));
//            roomIncomeDetailDto.setPlayerPayCount(njIncomeSignPlayerNumMap.getOrDefault(id, 0));
            roomIncomeDetailDto.setCateName(Optional.ofNullable(ConfigUtil.getLiveHallCategoryById(cateId)).map(LiveCategoryObject::getName).orElse(""));
            roomIncomeDetailDtos.add(roomIncomeDetailDto);
        }

        return roomIncomeDetailDtos;
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }

    /**
     * 汇总指定bizId的总收入，区分主播
     *
     * @param specificBizIdConfig 待汇总的bizId分组配置
     * @param historySummaries    历史汇总数据
     * @return 每个主播指定bizIds的收入
     */
    private Map<Long, Long> getUserSummaryAmount(List<QueryAccountProto.HistorySummary> historySummaries, String specificBizIdConfig) {
        Map<Long, Long> result = Maps.newHashMap();
        List<Integer> specificBizIdConfigs = Splitter.on(",").trimResults().omitEmptyStrings().splitToList(specificBizIdConfig)
                .stream().map(Integer::parseInt).collect(Collectors.toList());
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(historySummaries)) {
            return result;
        }
        // 汇总
        for (QueryAccountProto.HistorySummary historySummary : historySummaries) {
            if (!specificBizIdConfigs.contains(historySummary.getBizId())) {
                // 不再统计的bizId配置中
                continue;
            }
            String identity = historySummary.getIdentity();
            if (!StringUtils.isNumeric(identity)) {
                // 异常数据
                continue;
            }
            long userId = Long.parseLong(identity);
            Long summaryAmount = result.get(userId);
            if (null == summaryAmount) {
                summaryAmount = 0L;
            }
            // 将汇总结果加入到sumResult中
            result.put(userId, summaryAmount + (long) historySummary.getSummaryAmount());
        }
        // 返回汇总结果
        return result;
    }
}
