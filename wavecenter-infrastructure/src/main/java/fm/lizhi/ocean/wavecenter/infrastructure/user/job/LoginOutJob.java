package fm.lizhi.ocean.wavecenter.infrastructure.user.job;

import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wavecenter.service.user.manager.LoginManager;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/2/11 16:55
 */
@Slf4j
@Component
public class LoginOutJob implements JobHandler {

    @Autowired
    private LoginManager loginManager;

    @Override
    public void execute(JobExecuteContext jobExecuteContext) throws Exception {
        String paramStr = jobExecuteContext.getParam();
        if (StringUtils.isBlank(paramStr)) {
            log.info("param is blank");
            return;
        }

        Param param = JsonUtil.loads(paramStr, Param.class);
        Integer appId = param.getAppId();
        Long userId = param.getUserId();
        if (appId == null || userId == null) {
            log.info("param is null. appId={}, userId={}", appId, userId);
            return;
        }

        Set<String> userDevices = loginManager.getUserDevice(appId, userId);
        if (CollectionUtils.isEmpty(userDevices)) {
            return;
        }

        log.info("userDevices={}", JsonUtil.dumps(userDevices));
        for (String userDevice : userDevices) {
            loginManager.deleteAllLoginInfo(appId, userId, userDevice);
        }
        log.info("loginOut finish.userId={},appId={}", userId, appId);
    }

    @Data
    public static class Param{
        private Integer appId;
        private Long userId;
    }


}
