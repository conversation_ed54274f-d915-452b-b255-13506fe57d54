package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.constants;

import fm.lizhi.ocean.wavecenter.api.grow.ability.constants.AssessMetricEnum;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.PlayerSupportMetric;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowPlayerMetricValue;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowRoomMetricValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.function.Function;

/**
 * 能力指标枚举, 使用{@link PlayerSupportMetric}的字段名作为指标code, 根据指标code从{@link WcGrowRoomMetricValue}中读取不同的字段.
 */
@AllArgsConstructor
@Getter
public enum CapabilityMetricEnum {

    /**
     * 周私信用户数
     */
    CHAT_USER_CNT(AssessMetricEnum.CHAT_USER_CNT, WcGrowRoomMetricValue::getChatUserCnt, new PlayerMetricIntegerExtractor(WcGrowPlayerMetricValue::getChatUserCnt)),
    /**
     * 周私信回复用户数
     */
    REPLY_CHAT_USER_CNT(AssessMetricEnum.REPLY_CHAT_USER_CNT, WcGrowRoomMetricValue::getReplyChatUserCnt, new PlayerMetricIntegerExtractor(WcGrowPlayerMetricValue::getReplyChatUserCnt)),
    /**
     * 周私信回复新用户数
     */
    REPLY_CHAT_NEW_USER_CNT(AssessMetricEnum.REPLY_CHAT_NEW_USER_CNT, WcGrowRoomMetricValue::getReplyChatNewUserCnt, new PlayerMetricIntegerExtractor(WcGrowPlayerMetricValue::getReplyChatNewUserCnt)),
    /**
     * 周付费用户数
     */
    GIFT_USER_CNT(AssessMetricEnum.GIFT_USER_CNT, WcGrowRoomMetricValue::getGiftUserCnt, new PlayerMetricIntegerExtractor(WcGrowPlayerMetricValue::getGiftUserCnt)),
    /**
     * 周付费新用户数
     */
    GIFT_NEW_USER_CNT(AssessMetricEnum.GIFT_NEW_USER_CNT, WcGrowRoomMetricValue::getGiftNewUserCnt, new PlayerMetricIntegerExtractor(WcGrowPlayerMetricValue::getGiftNewUserCnt)),
    /**
     * 周收礼金币/钻石数
     */
    ALL_INCOME(AssessMetricEnum.ALL_INCOME, WcGrowRoomMetricValue::getAllIncome, WcGrowPlayerMetricValue::getAllIncome),
    /**
     * 周上麦时长
     */
    UP_GUEST_DUR(AssessMetricEnum.UP_GUEST_DUR, WcGrowRoomMetricValue::getUpGuestDur, WcGrowPlayerMetricValue::getUpGuestDur),
    /**
     * 周新增粉丝数
     */
    NEW_FANS_USER_CNT(AssessMetricEnum.NEW_FANS_USER_CNT, WcGrowRoomMetricValue::getNewFansUserCnt, new PlayerMetricIntegerExtractor(WcGrowPlayerMetricValue::getNewFansUserCnt)),
    /**
     * 周违规次数
     */
    VIOLATION_CNT(AssessMetricEnum.VIOLATION_CNT, WcGrowRoomMetricValue::getViolationCnt, new PlayerMetricIntegerExtractor(WcGrowPlayerMetricValue::getViolationCnt)),
    /**
     * 周有效麦序
     */
    CHECK_IN_CNT(AssessMetricEnum.CHECK_IN_CNT, WcGrowRoomMetricValue::getCheckInCnt, new PlayerMetricIntegerExtractor(WcGrowPlayerMetricValue::getCheckInCnt)),
    ;

    /**
     * 对应的考核指标枚举
     */
    private final AssessMetricEnum assessMetricEnum;
    /**
     * 厅指标提取器
     */
    private final RoomMetricExtractor roomMetricExtractor;
    /**
     * 主播指标提取器
     */
    private final PlayerMetricExtractor playerMetricExtractor;

    /**
     * 获取指标code, 即对应的考核指标枚举的code.
     *
     * @return 指标code
     */
    public String getCode() {
        return assessMetricEnum.getCode();
    }

    /**
     * 获取指标名称, 即对应的考核指标枚举的名称.
     *
     * @return 指标名称
     */
    public String getName() {
        return assessMetricEnum.getName();
    }

    /**
     * 根据指标code获取对应的指标枚举
     *
     * @param code 指标code
     * @return 指标枚举, 如果不存在则返回null
     */
    public static CapabilityMetricEnum from(String code) {
        for (CapabilityMetricEnum metricEnum : CapabilityMetricEnum.values()) {
            if (Objects.equals(metricEnum.getCode(), code)) {
                return metricEnum;
            }
        }
        return null;
    }

    /**
     * 厅指标提取器接口
     */
    public interface RoomMetricExtractor {

        /**
         * 从厅指标中提取对应的指标值
         *
         * @param roomMetricValue 厅指标值实体, 不能为null
         * @return 提取的指标值
         */
        BigDecimal apply(WcGrowRoomMetricValue roomMetricValue);
    }

    /**
     * 主播指标提取器接口
     */
    public interface PlayerMetricExtractor {

        /**
         * 从主播指标中提取对应的指标值
         *
         * @param playerMetricValue 主播指标值实体, 不能为null
         * @return 提取的指标值
         */
        BigDecimal apply(WcGrowPlayerMetricValue playerMetricValue);
    }

    /**
     * 主播指标整形值提取器, 将整型指标值转换为BigDecimal, 最终适配{@link PlayerMetricExtractor}接口
     */
    private static class PlayerMetricIntegerExtractor implements PlayerMetricExtractor {

        private final Function<WcGrowPlayerMetricValue, Integer> integerExtractor;

        public PlayerMetricIntegerExtractor(Function<WcGrowPlayerMetricValue, Integer> integerExtractor) {
            this.integerExtractor = integerExtractor;
        }

        @Override
        public BigDecimal apply(WcGrowPlayerMetricValue playerMetricValue) {
            Integer integerValue = integerExtractor.apply(playerMetricValue);
            if (integerValue == null) {
                return null;
            }
            return BigDecimal.valueOf(integerValue);
        }
    }
}
