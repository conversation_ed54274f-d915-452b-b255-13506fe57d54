package fm.lizhi.ocean.wavecenter.infrastructure.live.remote.pp;

import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.live.constants.RoomCategoryEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.live.remote.ILiveRoomRemote;
import fm.lizhi.ocean.wavecenter.service.live.config.LiveConfig;
import fm.lizhi.pp.util.bean.LiveCategoryObject;
import fm.lizhi.pp.util.utils.ConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pp.fm.lizhi.live.pp.core.api.LivePpUserService;
import pp.fm.lizhi.live.pp.core.protocol.LivePpUserProto;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/9 15:50
 */
@Slf4j
@Component
public class PpLiveRoomRemote implements ILiveRoomRemote {

    @Autowired
    private LivePpUserService livePpUserService;
    @Autowired
    private LiveConfig liveConfig;

    @Override
    public Optional<RoomCategoryEnum> getUserRoomCategory(Long userId) {
        log.info("userId={}", userId);
        Result<LivePpUserProto.ResponseGetUserCategoryList> result = livePpUserService.getUserCategoryList(LivePpUserProto.GetUserCategoryListParams.newBuilder()
                .setUserId(userId)
                .build());
        if (RpcResult.isFail(result)) {
            log.warn("getUserCategoryList fail. rCode={}", result.rCode());
        }

        List<LivePpUserProto.WhitelistCategory> categoryListList = result.target().getUserCategory().getCategoryListList();
        if (CollectionUtils.isEmpty(categoryListList)) {
            return Optional.empty();
        }

        // 所有的大类
        List<Long> allCategoryIds = ConfigUtil.getLiveHallCategoryList().stream().map(LiveCategoryObject::getId).collect(Collectors.toList());
        Long categoryId = null;
        for (LivePpUserProto.WhitelistCategory whitelistCategory : categoryListList) {
            // 匹配出品类列表中的大类
            if (allCategoryIds.contains(whitelistCategory.getId())) {
                categoryId = whitelistCategory.getId();
                break;
            }
        }
        log.info("categoryId={}", categoryId);

        if (categoryId == null) {
            return Optional.empty();
        }

        JSONObject categoryMapping = liveConfig.getPp().getCategoryMapping();
        Integer roomCategoryValue = categoryMapping.getInteger(categoryId.toString());
        if (roomCategoryValue == null) {
            return Optional.empty();
        }

        RoomCategoryEnum categoryEnum = RoomCategoryEnum.getByValue(roomCategoryValue);
        return Optional.ofNullable(categoryEnum);
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }
}
