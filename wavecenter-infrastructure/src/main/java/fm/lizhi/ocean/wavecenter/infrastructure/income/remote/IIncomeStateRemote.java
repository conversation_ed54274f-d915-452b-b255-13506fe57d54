package fm.lizhi.ocean.wavecenter.infrastructure.income.remote;

import fm.lizhi.ocean.wavecenter.api.datacenter.bean.IncomeSummaryBean;
import fm.lizhi.ocean.wavecenter.base.remote.IRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.income.dto.PlayerPayCountParamDto;
import fm.lizhi.pay.settle.settleenum.creativecenter.PeriodTypeEnum;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 收入统计
 *
 * <AUTHOR>
 * @date 2024/5/23 11:54
 */
public interface IIncomeStateRemote extends IRemote {

    /**
     * 查询公会有收入主播数
     *
     * @param familyId
     * @param startDate
     * @param endDate
     * @return
     */
    int getPlayerPayCountByFamily(Long familyId, Date startDate, Date endDate);

    /**
     * 查询厅有收入主播数
     *
     * @param roomId
     * @param startDate
     * @param endDate
     * @return
     */
    int getPlayerPayCountByRoom(Long familyId, Long roomId, Date startDate, Date endDate);

    /**
     * 批量查询厅的有收入主播数
     *
     * @param familyId
     * @param roomIds
     * @param startDate
     * @param endDate
     * @return key=厅id, value=有收入主播数
     */
    Map<Long, Integer> getPlayerPayCountByRooms(Long familyId, List<Long> roomIds, Date startDate, Date endDate);

    /**
     * 查询公会有收入主播数，支持通过roomIds进行过滤
     * @param startDate
     * @param endDate
     * @return
     */
    int getPlayerPayCountForFamily(PlayerPayCountParamDto paramDto, Date startDate, Date endDate);

    /**
     * 查询公会收入数据
     *
     * @param familyId   家族ID
     * @param periodType 时间周期
     * @return 收入
     */
    IncomeSummaryBean queryTradeValueByFamily(Long familyId, List<Long> roomIds, PeriodTypeEnum periodType);

    /**
     * 查询签约厅收入数据
     *
     * @param familyId   家族ID
     * @param roomId     房间ID
     * @param periodType 时间周期
     * @return 收入
     */
    IncomeSummaryBean queryTradeValueByRoom(Long familyId, Long roomId, PeriodTypeEnum periodType);

}
