package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote.pp;

import fm.lizhi.ocean.wavecenter.api.datacenter.bean.IncomeBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.PlayerAssessmentInfoBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.utils.WcAssert;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PaySettleConfigCodeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.manager.CharmStateManager;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote.IPlayerDataRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.income.manager.PaymentManager;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.PlayerGetAssessmentDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/24 11:20
 */
@Slf4j
@Component
public class PpPlayerDataRemote implements IPlayerDataRemote {

    @Autowired
    private PaymentManager paymentManager;
    @Autowired
    private CharmStateManager charmStateManager;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public PlayerAssessmentInfoBean getAssessmentInfo(PlayerGetAssessmentDto param) {
        Long familyId = param.getFamilyId();
        List<Long> familySignRoomIds = param.getFamilySignRoomIds();
        Long signRoomId = param.getSignRoomId();
        Long playerId = param.getPlayerId();

        WcAssert.notNull(familyId, "familyId is null");
        WcAssert.notNull(playerId, "playerId is null");
        WcAssert.notEmpty(familySignRoomIds, "roomIds is empty");

        //收入
        IncomeBean individualIncome = paymentManager.getIncomeBeanByPlayer(familyId, signRoomId, playerId, PaySettleConfigCodeEnum.PERSONAL_ANCHOR_INDIVIDUAL_INCOME_TOTAL_AMOUNT);
        PlayerAssessmentInfoBean playerAssessmentInfoBean = new PlayerAssessmentInfoBean()
                .setFlushTime(System.currentTimeMillis())
                .setIndividualIncome(individualIncome);

        //魅力值
        //厅收礼魅力值
        playerAssessmentInfoBean.setRoomCharm(charmStateManager.getPlayerAssessmentRoomCharm(param));
        //个播魅力值
        playerAssessmentInfoBean.setIndividualCharm(charmStateManager.getPlayerAssessmentIndividualCharm(param));
        return playerAssessmentInfoBean;
    }
}
