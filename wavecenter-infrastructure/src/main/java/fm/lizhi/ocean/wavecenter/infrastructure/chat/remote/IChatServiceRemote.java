package fm.lizhi.ocean.wavecenter.infrastructure.chat.remote;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.base.remote.IRemote;


/**
 * <AUTHOR>
 */
public interface IChatServiceRemote extends IRemote {

    Result<Void> sendChatAsync(long senderUid, long receiverUid, String content);


    Result<Void> sendRichTextChatAsync(long senderUid, long receiverUid, String content);

    Result<Void> sendCardChatAsync(long senderUid, long receiverUid, String content);

    Result<Void> sendRichTextChatAsyncLowPriority(long senderUid, long receiverUid, String content);

    int BATCH_SEND_CHAT_ASYNC_FAIL = 1;
}
