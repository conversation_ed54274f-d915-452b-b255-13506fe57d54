package fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.request;

import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/10/26 14:48
 */
@Getter
@Builder
public class RequestFamilyAdminCancel {

    private Long familyId;

    private Long njId;

    private Long contractId;

    //subjectId 如果当前用户为家族长 则为familyId，如果为管理员，则为管理员自己的uid
    private Long subjectId;

    private Long curUserId;

}
