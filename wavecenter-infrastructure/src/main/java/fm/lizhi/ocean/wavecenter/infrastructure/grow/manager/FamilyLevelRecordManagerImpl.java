package fm.lizhi.ocean.wavecenter.infrastructure.grow.manager;

import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.datastore.entity.WcFamilyLevelWeekRecord;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.datastore.entity.WcFamilyLevelWeekRecordExample;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.datastore.mapper.WcFamilyLevelWeekRecordMapper;
import fm.lizhi.ocean.wavecenter.service.grow.dto.FamilyLevelDTO;
import fm.lizhi.ocean.wavecenter.service.grow.manager.FamilyLevelManager;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/20 11:53
 */
@Component
public class FamilyLevelRecordManagerImpl implements FamilyLevelManager {

    @Autowired
    private WcFamilyLevelWeekRecordMapper familyLevelWeekRecordMapper;

    /**
     * 查询存在的周记录
     * @param familyIds
     * @param appId
     * @param startTime
     * @return
     */
    public Set<Long> getWeekExitRecord(List<Long> familyIds, Integer appId, Date startTime) {
        WcFamilyLevelWeekRecordExample example = new WcFamilyLevelWeekRecordExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andFamilyIdIn(familyIds)
                .andDeletedEqualTo(0)
                .andSettleStartTimeEqualTo(startTime);
        List<WcFamilyLevelWeekRecord> list = familyLevelWeekRecordMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptySet();
        }
        return list.stream().map(WcFamilyLevelWeekRecord::getFamilyId).collect(Collectors.toSet());
    }

    /**
     * 查询公会周期等级
     * @param familyId
     * @param startTime
     * @return
     */
    @Override
    public Optional<FamilyLevelDTO> getFamilyLevel(Long familyId, Date startTime){
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        WcFamilyLevelWeekRecordExample example = new WcFamilyLevelWeekRecordExample();
        example.setOrderByClause("create_time desc");
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andFamilyIdEqualTo(familyId)
                .andDeletedEqualTo(0)
                .andSettleStartTimeEqualTo(startTime);
        List<WcFamilyLevelWeekRecord> list = familyLevelWeekRecordMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return Optional.empty();
        }
        return Optional.ofNullable(new FamilyLevelDTO()
                .setExp(list.get(0).getExp())
                .setLevelId(list.get(0).getLevelId())
        );
    }

}
