package fm.lizhi.ocean.wavecenter.infrastructure.anchor.singer.datastore.dao;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.csp.sentinel.util.AssertUtil;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.*;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.mapper.SingerInfoMapper;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.mapper.SingerOperateRecordMapper;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.mapper.SingerVerifyRecordMapper;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.mapper.SingerWhiteListConfigMapper;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.entity.SingerDecorateFlow;
import fm.lizhi.ocean.wavecenter.infrastructure.award.singer.datastore.dao.SingerDecorateDao;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerCountInHallDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class SingerInfoV1Dao {

    @Autowired
    private SingerInfoMapper singerInfoMapper;

    @Autowired
    private SingerVerifyRecordMapper singerVerifyRecordMapper;

    @Autowired
    private SingerOperateRecordMapper singerOperateRecordMapper;

    @Autowired
    private SingerDecorateDao singerDecorateDao;

    @Resource
    private SingerWhiteListConfigMapper singerWhiteListConfigMapper;

    /**
     * 批量获取厅下的歌手信息
     *
     * @param njId         厅ID
     * @param singerType   歌手类型, null 的话查全部
     * @param singerStatus 歌手状态, null 的话查全部
     * @return 歌手信息列表
     */
    public List<SingerInfo> selectSingerInfoByNjId(int appId, Long njId, SingerTypeEnum singerType,
                                                   List<SingerStatusEnum> singerStatus) {
        SingerInfoExample example = new SingerInfoExample();
        SingerInfoExample.Criteria criteria = example.createCriteria()
                .andNjIdEqualTo(njId)
                .andAppIdEqualTo(appId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());

        if (singerType != null) {
            criteria.andSingerTypeEqualTo(singerType.getType());
        }

        if (CollUtil.isNotEmpty(singerStatus)) {
            criteria.andSingerStatusIn(
                    singerStatus.stream().map(SingerStatusEnum::getStatus).collect(Collectors.toList()));
        }

        return singerInfoMapper.selectByExample(example);
    }


    /**
     * 批量删除歌手
     *
     * @return
     */
    @Transactional
    public Boolean deleteSinger(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return true;
        }
        SingerInfoExample example = new SingerInfoExample();
        example.createCriteria().andIdIn(ids);
        return singerInfoMapper.deleteByExample(example) == ids.size();
    }

    /**
     * 通过歌手
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean passSinger(List<SingerInfo> singerInfo, List<SingerDecorateFlow> flowList, List<SingerOperateRecord> singerOperateRecords) {
        if (CollUtil.isEmpty(singerInfo)) {
            return true;
        }

        boolean success = singerInfo.stream().mapToInt(singer -> singerInfoMapper.updateByPrimaryKey(singer))
                .sum() == singerInfo.size();
        AssertUtil.assertState(success, "修改歌手状态失败");

        if (CollUtil.isNotEmpty(flowList)) {
            boolean decorateRes = singerDecorateDao.batchInsert(flowList);
            AssertUtil.assertState(decorateRes, "发放装扮失败");
        }
        if (CollUtil.isNotEmpty(singerOperateRecords)) {
            singerOperateRecordMapper.batchInsert(singerOperateRecords);
        }
        return true;

    }

    /**
     * 根据用户ID和应用ID查询歌手信息列表
     *
     * @param appId  应用ID
     * @param userId 用户ID
     * @return 歌手信息列表
     */
    public List<SingerInfo> getSingerInfoByUserId(int appId, Long userId, SingerStatusEnum singerStatus) {
        SingerInfoExample example = new SingerInfoExample();
        example.createCriteria()
                .andUserIdEqualTo(userId)
                .andAppIdEqualTo(appId)
                .andSingerStatusEqualTo(singerStatus.getStatus())
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        return singerInfoMapper.selectByExample(example);
    }

    public List<SingerInfo> getSingerInfoByUserId(int appId, Long userId, List<SingerStatusEnum> singerStatusList) {
        SingerInfoExample example = new SingerInfoExample();
        example.createCriteria()
                .andUserIdEqualTo(userId)
                .andAppIdEqualTo(appId)
                .andSingerStatusIn(singerStatusList.stream().map(SingerStatusEnum::getStatus).collect(Collectors.toList()))
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        return singerInfoMapper.selectByExample(example);
    }

    /**
     * 根据用户ID和应用ID以及歌手类型查询歌手信息
     *
     * @param appId      应用ID
     * @param userId     用户ID
     * @param singerType 歌手类型
     * @return 歌手信息列表
     */
    public SingerInfo getSingerInfo(int appId, Long userId, Integer singerType) {
        SingerInfo entity = new SingerInfo();
        entity.setUserId(userId);
        entity.setAppId(appId);
        entity.setSingerType(singerType);
        entity.setDeployEnv(ConfigUtils.getEnvRequired().name());
        return singerInfoMapper.selectOne(entity);
    }

    /**
     * 根据用户ID和应用ID查询用户是否是歌手
     *
     * @param appId  应用ID
     * @param userId 用户ID
     * @return 是否是歌手
     */
    public boolean isSinger(Integer appId, Long userId) {
        SingerInfoExample example = new SingerInfoExample();
        SingerInfoExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(appId)
                .andUserIdEqualTo(userId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andSingerStatusEqualTo(SingerStatusEnum.EFFECTIVE.getStatus());
        return singerInfoMapper.countByExample(example) > 0;
    }

    /**
     * 统计厅内歌手总数
     *
     * @param njId 厅主ID
     * @return 返回生效中和审核中的歌手数量
     */
    public SingerCountInHallDTO countSingerInHall(Long njId) {
        // 统计生效中的歌手
        SingerInfoExample effectiveExample = new SingerInfoExample();
        SingerInfoExample.Criteria effectiveCriteria = effectiveExample.createCriteria();
        effectiveCriteria.andNjIdEqualTo(njId)
                .andSingerStatusIn(Arrays.asList(SingerStatusEnum.EFFECTIVE.getStatus(),
                        SingerStatusEnum.AUTHENTICATING.getStatus()))
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        List<SingerInfo> effectiveSingers = singerInfoMapper.selectByExample(effectiveExample);
        Set<Long> effectiveUserIds = effectiveSingers.stream()
                .map(SingerInfo::getUserId).collect(Collectors.toSet());

        // 统计审核中的歌手（从 singer_verify_record 表中查询）
        SingerVerifyRecordExample authenticatingExample = new SingerVerifyRecordExample();
        SingerVerifyRecordExample.Criteria authenticatingCriteria = authenticatingExample.createCriteria();
        authenticatingCriteria.andNjIdEqualTo(njId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andAuditStatusIn(Arrays.asList(SingerAuditStatusEnum.WAIT_AUDIT.getStatus(),
                        SingerAuditStatusEnum.WAIT_DECIDE.getStatus(),
                        SingerAuditStatusEnum.SELECTED.getStatus()));
        List<SingerVerifyRecord> auditSingers = singerVerifyRecordMapper.selectByExample(authenticatingExample);
        Set<Long> auditUserIds = auditSingers.stream().map(SingerVerifyRecord::getUserId).collect(Collectors.toSet());

        //拿出认证中的歌手
        Set<Long> authenticatingUserIds = effectiveSingers.stream()
                .filter(singerInfo -> singerInfo.getSingerStatus() == SingerStatusEnum.AUTHENTICATING.getStatus())
                .map(SingerInfo::getUserId).collect(Collectors.toSet());

        return new SingerCountInHallDTO()
                .setEffectiveCount(effectiveUserIds.size())
                .setAuditCount(auditUserIds.size())
                .setAuthenticationCount(authenticatingUserIds.size());
    }

    /**
     * 根据ID列表获取
     *
     * @param appId
     * @param ids
     * @return
     */
    public List<SingerInfo> getSingerInfoByIds(int appId, List<Long> ids, List<SingerStatusEnum> singerStatus) {
        SingerInfoExample example = new SingerInfoExample();
        SingerInfoExample.Criteria criteria = example.createCriteria()
                .andIdIn(ids)
                .andAppIdEqualTo(appId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());

        if (CollUtil.isNotEmpty(singerStatus)) {
            criteria.andSingerStatusIn(singerStatus.stream().map(SingerStatusEnum::getStatus).collect(Collectors.toList()));
        }

        return singerInfoMapper.selectByExample(example);
    }


    /**
     * 根据ID列表获取
     */
    public List<SingerInfo> getSingerInfoByIds(int appId, List<Long> ids) {
        return getSingerInfoByIds(appId, ids, null);
    }

    /**
     * 根据userIds列表获取
     *
     * @param appId
     * @param userIds
     * @return
     */
    public List<SingerInfo> getSingerInfoByUserIds(int appId, List<Long> userIds, List<SingerStatusEnum> singerStatus, SingerTypeEnum singerType) {
        SingerInfoExample example = new SingerInfoExample();
        SingerInfoExample.Criteria criteria = example.createCriteria()
                .andUserIdIn(userIds)
                .andAppIdEqualTo(appId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());

        if (CollUtil.isNotEmpty(singerStatus)) {
            criteria.andSingerStatusIn(singerStatus.stream().map(SingerStatusEnum::getStatus).collect(Collectors.toList()));
        }

        if (singerType != null) {
            criteria.andSingerTypeEqualTo(singerType.getType());
        }

        return singerInfoMapper.selectByExample(example);
    }

    /**
     * 根据userIds列表获取
     *
     * @param appId
     * @param userIds
     * @return
     */
    public List<SingerInfo> getSingerInfoByUserIds(int appId, List<Long> userIds, List<SingerStatusEnum> singerStatus) {
        return getSingerInfoByUserIds(appId, userIds, singerStatus, null);
    }

    /**
     * 更新歌手奖励装扮发放状态
     */
    public boolean updateSingerRewardsIssued(Long userId, Integer singerType, boolean rewardsIssued) {
        SingerInfoExample example = new SingerInfoExample();
        SingerInfoExample.Criteria criteria = example.createCriteria();
        criteria.andUserIdEqualTo(userId)
                .andSingerTypeEqualTo(singerType)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());

        SingerInfo singerInfo = new SingerInfo();
        singerInfo.setRewardsIssued(rewardsIssued);
        singerInfo.setModifyTime(new Date());
        return singerInfoMapper.updateByExample(singerInfo, example) > 0;
    }


    /**
     * 根据奖励发放状态批量获取歌手
     *
     * @param rewardsIssued 奖励发放状态
     */
    public List<SingerInfo> getRewardsIssuedByUserIds(int appId, List<Long> userIds, boolean rewardsIssued) {
        SingerInfoExample example = new SingerInfoExample();
        SingerInfoExample.Criteria criteria = example.createCriteria();
        criteria.andUserIdIn(userIds)
                .andAppIdEqualTo(appId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andRewardsIssuedEqualTo(rewardsIssued)
                .andSingerStatusEqualTo(SingerStatusEnum.EFFECTIVE.getStatus())
        ;

        return singerInfoMapper.selectByExample(example);
    }

    /**
     * 删除用户指定歌手类型的数据
     *
     * @param appId          appId
     * @param userId         用户ID
     * @param singerTypeList 歌手类型列表
     * @return 结果
     */
    public boolean deleteSingerInfo(int appId, long userId, List<Integer> singerTypeList) {
        SingerInfoExample example = new SingerInfoExample();
        SingerInfoExample.Criteria criteria = example.createCriteria();
        criteria.andUserIdEqualTo(userId)
                .andAppIdEqualTo(appId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andSingerTypeIn(singerTypeList);
        return singerInfoMapper.deleteByExample(example) > 0;
    }

    /**
     * 根据歌手类型获取歌手信息
     *
     * @param appId          应用ID
     * @param userId         用户ID
     * @param singerTypeList 歌手类型列表
     * @return 结果
     */
    public List<SingerInfo> getSingerInfoListByType(int appId, Long userId, List<Integer> singerTypeList) {
        SingerInfoExample singerInfoExample = new SingerInfoExample();
        singerInfoExample.createCriteria()
                .andAppIdEqualTo(appId)
                .andUserIdEqualTo(userId)
                .andSingerTypeIn(singerTypeList)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        return singerInfoMapper.selectByExample(singerInfoExample);
    }

    /**
     * 更新歌手库签约信息
     *
     * @param appId    应用ID
     * @param singerId 歌手ID
     * @param familyId 家族ID
     * @param njId     厅主ID
     * @return 结果
     */
    public boolean updateSingerSignInfo(Integer appId, Long singerId, Long familyId, Long njId) {
        SingerInfo info = new SingerInfo();
        info.setFamilyId(familyId);
        info.setNjId(njId);
        SingerInfoExample example = new SingerInfoExample();
        SingerInfoExample.Criteria criteria = example.createCriteria();
        criteria.andUserIdEqualTo(singerId)
                .andAppIdEqualTo(appId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        return singerInfoMapper.updateByExample(info, example) > 0;
    }

    /**
     * 根据歌手状态、歌手类型分页查询出歌手信息
     *
     * @param appId        应用ID
     * @param singerStatus 歌手状态
     * @param singerType   歌手类型
     * @param pageNo       页码
     * @param pageSize     每页大小
     * @return 分页结果
     */
    public PageList<SingerInfo> pageSingerInfoByStatusAndType(int appId, SingerStatusEnum singerStatus, SingerTypeEnum singerType, int pageNo, int pageSize) {
        SingerInfoExample example = new SingerInfoExample();
        SingerInfoExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(appId)
                .andSingerStatusEqualTo(singerStatus.getStatus())
                .andSingerTypeEqualTo(singerType.getType())
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        return singerInfoMapper.pageByExample(example, pageNo, pageSize);
    }

    /**
     * 批量修改歌手状态
     *
     * @param appId               应用ID
     * @param ids                 歌手记录Id
     * @param currentSingerStatus 当前歌手状态
     * @param targetSingerStatus  目标歌手状态
     * @param operator            操作人
     * @return 结果
     */
    public boolean batchUpdateSingerStatus(int appId, List<Long> ids, SingerStatusEnum currentSingerStatus,
                                           SingerStatusEnum targetSingerStatus, String operator) {
        SingerInfoExample example = new SingerInfoExample();
        SingerInfoExample.Criteria criteria = example.createCriteria();
        criteria.andIdIn(ids)
                .andAppIdEqualTo(appId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andSingerStatusEqualTo(currentSingerStatus.getStatus());

        SingerInfo singerInfo = new SingerInfo();
        singerInfo.setSingerStatus(targetSingerStatus.getStatus());
        singerInfo.setOperator(operator);
        singerInfo.setModifyTime(new Date());
        return singerInfoMapper.updateByExample(singerInfo, example) > 0;
    }

    /**
     * 批量删除白名单用户
     *
     * @param singerInfoList 歌手列表
     * @return 结果
     */
    public boolean batchDeleteWhiteList(List<SingerInfo> singerInfoList) {
        if (CollectionUtils.isEmpty(singerInfoList)) {
            return true;
        }
        for (SingerInfo singerInfo : singerInfoList) {
            SingerWhiteListConfigExample example = new SingerWhiteListConfigExample();
            example.createCriteria()
                    .andAppIdEqualTo(singerInfo.getAppId())
                    .andSingerIdEqualTo(singerInfo.getUserId())
                    .andSingerTypeEqualTo(singerInfo.getSingerType())
                    .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
            long row = singerWhiteListConfigMapper.deleteByExample(example);
            if (row <= 0) {
                return false;
            }
        }
        return true;
    }
}
