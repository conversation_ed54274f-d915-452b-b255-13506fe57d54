package fm.lizhi.ocean.wavecenter.infrastructure.grow.tasktemplate.datastore.dao;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.tasktemplate.datastore.entity.WcGrowTaskTemplateCapability;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.tasktemplate.datastore.entity.WcGrowTaskTemplateCapabilityExample;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.tasktemplate.datastore.mapper.WcGrowTaskTemplateCapabilityMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;

import java.util.Collections;
import java.util.List;

/**
 * 任务模版能力表DAO
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Slf4j
@Repository
public class GrowTaskTemplateCapabilityDao {

    @Autowired
    private WcGrowTaskTemplateCapabilityMapper capabilityMapper;

    /**
     * 根据appId和模版ID列表批量查询能力记录
     *
     * @param appId 业务ID
     * @param templateIds 模版ID列表
     * @return 能力记录列表
     */
    public List<WcGrowTaskTemplateCapability> queryCapabilityByByTemplateIds(Integer appId, List<Long> templateIds) {
        if (appId == null || CollectionUtils.isEmpty(templateIds)) {
            return Collections.emptyList();
        }
        String deployEnv = ConfigUtils.getEnvRequired().name();
        WcGrowTaskTemplateCapabilityExample example = new WcGrowTaskTemplateCapabilityExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andTemplateIdIn(templateIds)
                .andDeployEnvEqualTo(deployEnv);
        return capabilityMapper.selectByExample(example);
    }

    /**
     * 根据capabilityCode和appId查询能力记录
     *
     * @param capabilityCode 能力code
     * @param appId 业务ID
     * @return 能力记录列表
     */
    public List<WcGrowTaskTemplateCapability> queryCapabilityByCapabilityCode(String capabilityCode, Integer appId) {
        String deployEnv = ConfigUtils.getEnvRequired().name();
        WcGrowTaskTemplateCapabilityExample example = new WcGrowTaskTemplateCapabilityExample();
        example.createCriteria()
                .andCapabilityCodeEqualTo(capabilityCode)
                .andAppIdEqualTo(appId)
                .andDeployEnvEqualTo(deployEnv);
        return capabilityMapper.selectByExample(example);
    }


} 