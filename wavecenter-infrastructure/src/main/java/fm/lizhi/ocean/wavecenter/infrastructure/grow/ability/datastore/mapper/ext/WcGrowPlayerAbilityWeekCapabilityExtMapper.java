package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowPlayerAbilityWeekCapability;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcGrowPlayerAbilityWeekCapabilityExtMapper {

    @Select({
            "<script>"
            , "select *"
            , "from wavecenter_grow_player_ability_week_capability"
            , "where room_id = #{roomId}"
            , "  and app_id = #{appId}"
            , "  and deploy_env = #{deployEnv}"
            , "  and start_week_date = #{weekStartDay}"
            , "  and end_week_date = #{weekEndDay}"
            , "  and id &gt; #{minId}"
            , "order by id"
            , "limit #{pageSize}"
            , "</script>"
    })
    List<WcGrowPlayerAbilityWeekCapability> getRoomPlayerWeekCapabilityPage(@Param("roomId") Long roomId
            , @Param("weekStartDay") Date weekStartDay
            , @Param("weekEndDay") Date weekEndDay
            , @Param("minId") Long minId
            , @Param("pageSize") Integer pageSize
            , @Param("appId") Integer appId
            , @Param("deployEnv") String deployEnv
    );

    @Select("<script>\n" +
            "  SELECT * FROM `wavecenter_grow_player_ability_week_capability`\n" +
            "  WHERE `deploy_env` = #{deployEnv}\n" +
            "    AND `app_id` = #{appId}\n" +
            "    AND `room_id` = #{roomId}\n" +
            "    AND `start_week_date` = #{startWeekDate}\n" +
            "    AND `capability_code` = #{capabilityCode}\n" +
            "    <if test=\"firstSignInFamily != null\">\n" +
            "      AND `first_sign_in_family` = #{firstSignInFamily}\n" +
            "    </if>\n" +
            "    <if test=\"minSignDate != null\">\n" +
            "      AND `sign_date` &gt;= #{minSignDate}\n" +
            "    </if>\n" +
            "  ORDER BY `ability_value` ${orderType.value}\n" +
            "</script>")
    List<WcGrowPlayerAbilityWeekCapability> getPlayerAbilityWeekCapabilityRanks(
            @Param("deployEnv") String deployEnv,
            @Param("appId") int appId,
            @Param("roomId") long roomId,
            @Param("startWeekDate") Date startWeekDate,
            @Param("capabilityCode") String capabilityCode,
            @Param("firstSignInFamily") Boolean firstSignInFamily,
            @Param("minSignDate") Date minSignDate,
            @Param("orderType") OrderType orderType);

    @Select("<script>\n" +
            "  SELECT * FROM `wavecenter_grow_player_ability_week_capability`\n" +
            "  WHERE `deploy_env` = #{deployEnv}\n" +
            "    AND `app_id` = #{appId}\n" +
            "    AND `room_id` = #{roomId}\n" +
            "    AND `player_id` = #{playerId}\n" +
            "    AND `start_week_date` &gt;= #{minStartWeekDate}\n" +
            "    AND `start_week_date` &lt;= #{maxStartWeekDate}\n" +
            "</script>")
    List<WcGrowPlayerAbilityWeekCapability> getPlayerAbilityWeekCapabilities(
            @Param("deployEnv") String deployEnv,
            @Param("appId") int appId,
            @Param("roomId") long roomId,
            @Param("playerId") long playerId,
            @Param("minStartWeekDate") Date minStartWeekDate,
            @Param("maxStartWeekDate") Date maxStartWeekDate);
}
