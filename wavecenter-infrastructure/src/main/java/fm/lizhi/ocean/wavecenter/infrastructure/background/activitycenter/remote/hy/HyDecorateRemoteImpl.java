package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.remote.hy;

import cn.hutool.core.util.StrUtil;
import fm.lizhi.amusement.commons.utils.EnvUtils;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.hy.amusement.api.DressUpInfoService;
import fm.lizhi.hy.amusement.enm.DressUpType;
import fm.lizhi.hy.amusement.protocol.DressUpInfoProto;
import fm.lizhi.hy.vip.api.HyMedalInfoV2Service;
import fm.lizhi.hy.vip.protocol.HyMedalBaseV2Proto;
import fm.lizhi.hy.vip.protocol.HyMedalBaseV2Proto.MedalInfoV2;
import fm.lizhi.hy.vip.protocol.HyMedalInfoV2Proto.ResponseMedalInfoList;
import fm.lizhi.live.pp.idl.officialCertifiedTag.api.OfficialCertifiedTagIDLService;
import fm.lizhi.live.pp.idl.officialCertifiedTag.dto.tag.ListOfficialCertifiedTagRequest;
import fm.lizhi.live.pp.idl.officialCertifiedTag.dto.tag.ListOfficialCertifiedTagResponse;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.DecorateEnum;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestBatchGetDecorate;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.constants.DecorateMapping;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert.ActivityDecorateConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.remote.IDecorateRemote;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.DecorateDTO;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.request.RequestGetDecorate;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class HyDecorateRemoteImpl implements IDecorateRemote {

    @Autowired
    private DressUpInfoService dressUpInfoService;

    @Autowired
    private HyMedalInfoV2Service hyMedalInfoV2Service;

    @Autowired
    private CommonConfig commonConfig;

    @Autowired
    private OfficialCertifiedTagIDLService officialCertifiedTagIdlService;


    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.HEI_YE;
    }

    @Override
    public Result<PageBean<DecorateDTO>> getDecorateList(RequestGetDecorate request) {
        if (request.getType().equals(DecorateEnum.USER_GLORY.getType())){
            // hy 官方认证，没有装扮类型，直接比对平台的类型即可
            return listOfficialCertifiedTags(request);
        }

        Integer decorateType = DecorateMapping.waveType2Biz(request.getType(), BusinessEvnEnum.HEI_YE);
        if (decorateType == -1) {
            return new Result<>(GET_DECORATE_LIST_ILLEGAL_PARAM, PageBean.empty());
        }

        if (decorateType == DressUpType.MEDAL.getId()) {
            return getMedalDecorateInfoList(request.getDressUpId(), request.getName(), request.getPageNo(), request.getPageSize());
        }
        
        return getDressUpList(request);
    }

    

    @Override
    public Result<List<DecorateDTO>> batchGetDecorateList(RequestBatchGetDecorate request) {
        if (request.getType().equals(DecorateEnum.USER_GLORY.getType())){
            // hy 官方认证，没有装扮类型，直接比对平台的类型即可
            return new Result<>(BATCH_GET_DECORATE_LIST_ILLEGAL_PARAM, new ArrayList<>());
        }

        Result<DressUpInfoProto.ResponseGetDressUpListByIds> result = dressUpInfoService.getDressUpListByIds(request.getIds());
        if (RpcResult.isFail(result)) {
            log.warn("hy batch decorate result fail. type:{}, id:{}", request.getType(), request.getIds());
            return RpcResult.fail(BATCH_GET_DECORATE_LIST_FAIL);
        }

        DressUpInfoProto.ResponseGetDressUpListByIds target = result.target();
        List<DecorateDTO> list = ActivityDecorateConvert.I.hyResponseGetDressUpList2DecorateDTO(target.getDressUpInfoList(), BusinessEvnEnum.HEI_YE);
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, list);

    }

    private Result<PageBean<DecorateDTO>> getMedalDecorateInfoList(Long decorateId, String decorateName, int pageNum, int pageSize) {
        HyMedalBaseV2Proto.QueryParams.Builder builder = HyMedalBaseV2Proto.QueryParams.newBuilder()
                //  上架状态 0-未上架 1-已上架
                .setStatus(1)
                .setMedalInfoId(Optional.ofNullable(decorateId).orElse(0L))
                .setPageNo(pageNum)
                .setPageSize(pageSize);
        if (StringUtils.isNotBlank(decorateName)) {
            builder.setMedalInfoName(decorateName);
        }
        Result<ResponseMedalInfoList> result = hyMedalInfoV2Service.medalInfoList(builder.build());
        if (RpcResult.isFail(result)) {
            log.warn("hy.getMedalDecorateInfoList fail. medalInfoList fail, decorateName={},pageNum={},pageSize={}", decorateName, pageNum, pageSize);
            return new Result<>(result.rCode(), PageBean.empty());
        }
        List<MedalInfoV2> medalInfoList = result.target().getMedalInfosList();
        List<DecorateDTO> decorateDTOList = ActivityDecorateConvert.I.convertToDecorateInfoBean(medalInfoList, commonConfig.getBizConfig().getCdnHost());
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, PageBean.of(result.target().getTotalCount(), decorateDTOList));
    }

    private Result<PageBean<DecorateDTO>> getDressUpList(RequestGetDecorate request) {
        DressUpInfoProto.QueryDressUpParam.Builder param = DressUpInfoProto.QueryDressUpParam.newBuilder();
        param.setDressUpType(DecorateMapping.waveType2Biz(request.getType(), BusinessEvnEnum.HEI_YE));
        param.setDressUpId(Optional.ofNullable(request.getDressUpId()).orElse(0L));
        int visibility = !EnvUtils.isProduct() ? -1 : EnvUtils.isPro() ? 1: 0;
        param.setVisibility(visibility);
        param.setPageNum(request.getPageNo());
        param.setPageSize(request.getPageSize());

        if (StrUtil.isNotBlank(request.getName())) {
            param.setDressUpName(request.getName());
        }

        Result<DressUpInfoProto.ResponseGetDressUpList> result = dressUpInfoService.getDressUpList(param.build());
        if (RpcResult.isFail(result)) {
            log.warn("hy decorate result fail. type:{}, name:{}, pageNo:{}, pageSize:{}", request.getType(), request.getName(), request.getPageNo(), request.getPageSize());
            return new Result<>(result.rCode(), PageBean.empty());
        }

        DressUpInfoProto.ResponseGetDressUpList target = result.target();
        List<DecorateDTO> list = ActivityDecorateConvert.I.hyResponseGetDressUpList2DecorateDTO(target.getDressUpInfoList(), BusinessEvnEnum.HEI_YE);
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, PageBean.of((int) target.getTotal(), list));
    }


    /**
     * 获取官方认证标签
     */
    private Result<PageBean<DecorateDTO>> listOfficialCertifiedTags(RequestGetDecorate param) {

        ListOfficialCertifiedTagRequest request = new ListOfficialCertifiedTagRequest();
        request.setId(param.getDressUpId());
        request.setName(param.getName());
        request.setPageNum(param.getPageNo());
        request.setPageSize(param.getPageSize());

        Result<ListOfficialCertifiedTagResponse> result = officialCertifiedTagIdlService.listOfficialCertifiedTags(request);

        if (RpcResult.isFail(result)) {
            log.warn("hy listOfficialCertifiedTags fail. id:{}, name:{}, pageNo:{}, pageSize:{}",
                    param.getDressUpId(), param.getName(), param.getPageNo(), param.getPageSize());
        }
        ListOfficialCertifiedTagResponse target = result.target();
        List<DecorateDTO> list = ActivityDecorateConvert.I.hyResponseOfficialCertifiedList2DecorateDTO(target.getList(), commonConfig.getBizConfig().getCdnHost());
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, PageBean.of(Math.toIntExact(target.getTotal()), list));
    }
}
