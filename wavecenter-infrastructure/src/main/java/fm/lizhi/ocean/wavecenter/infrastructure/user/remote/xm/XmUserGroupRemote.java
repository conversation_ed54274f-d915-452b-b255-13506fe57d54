package fm.lizhi.ocean.wavecenter.infrastructure.user.remote.xm;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.IUserGroupRemote;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xm.fm.lizhi.live.usergroup.api.UserGroupService;
import xm.fm.lizhi.live.usergroup.protocol.UserGroupProto;

/**
 * <AUTHOR>
 * @date 2025/5/9 16:46
 */
@Slf4j
@Component
public class XmUserGroupRemote implements IUserGroupRemote {

    @Autowired
    private UserGroupService userGroupService;

    @Override
    public boolean isUserInGroup(Long userId, Long groupId) {
        log.info("userId={}, groupId={}", userId, groupId);
        Result<UserGroupProto.ResponseIsUserInGroup> result = userGroupService.isUserInGroup(groupId, userId, 0);
        if (RpcResult.isFail(result)) {
            log.warn("isUserInGroup fail. rCode={}", result.rCode());
            return false;
        }
        boolean isUserInGroup = result.target().getIsUserInGroup();
        log.info("isUserInGroup={}", isUserInGroup);
        return isUserInGroup;
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.XIMI;
    }
}
