package fm.lizhi.ocean.wavecenter.infrastructure.income.remote.xm;

import com.google.common.collect.Lists;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.IncomeSummaryBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.IncomeSummaryThreadBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.RoomSignBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.util.CountDownLatchWrapper;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.ThreadConstants;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PaySettleConfigCodeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.income.datastore.mapper.XmIncomeRecordMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.income.datastore.po.PlayerPayCountPo;
import fm.lizhi.ocean.wavecenter.infrastructure.income.dto.PlayerPayCountParamDto;
import fm.lizhi.ocean.wavecenter.infrastructure.income.manager.PaymentManager;
import fm.lizhi.ocean.wavecenter.infrastructure.income.remote.IIncomeStateRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.xm.XmContractRemote;
import fm.lizhi.pay.settle.settleenum.creativecenter.PeriodTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/23 12:07
 */
@Component
public class XmIncomeStateRemote implements IIncomeStateRemote {

    @Autowired
    private XmIncomeRecordMapper incomeRecordMapper;

    @Autowired
    private PaymentManager paymentManager;

    @Autowired
    private XmContractRemote contractRemote;

    @Override
    public int getPlayerPayCountByFamily(Long familyId, Date startDate, Date endDate) {
        List<RoomSignBean> roomList = contractRemote.getAllSingGuildRoomsList(familyId);
        if (CollectionUtils.isEmpty(roomList)) {
            return 0;
        }

        List<Long> roomIds = roomList.stream().map(UserBean::getId).collect(Collectors.toList());
        Map<Long, Integer> roomCountMap = getPlayerPayCountByRooms(familyId, roomIds, startDate, endDate);
        if (MapUtils.isEmpty(roomCountMap)) {
            return 0;
        }

        return roomCountMap.values().stream().filter(Objects::nonNull).reduce(0, Integer::sum);
    }

    @Override
    public int getPlayerPayCountByRoom(Long familyId, Long roomId, Date startDate, Date endDate) {
        Map<Long, Integer> map = getPlayerPayCountByRooms(familyId, Collections.singletonList(roomId), startDate, endDate);
        return MapUtils.isEmpty(map) ? 0 : map.get(roomId);
    }

    @Override
    public IncomeSummaryBean queryTradeValueByFamily(Long familyId, List<Long> roomIds, PeriodTypeEnum periodType) {
        List<PaySettleConfigCodeEnum> configList = Lists.newArrayList(
                PaySettleConfigCodeEnum.FAMILY_HALL_INCOME_TOTAL_AMOUNT,
                PaySettleConfigCodeEnum.FAMILY_OFFICIAL_HALL_INCOME_TOTAL_AMOUNT);
        //批量查询
        IncomeSummaryThreadBean summaryThreadBean = paymentManager.asyncBatchQueryTrade(familyId, roomIds, periodType, configList);
        return IncomeSummaryBean.buildRoomAndOfficial(
                Optional.ofNullable(summaryThreadBean.getRoomGift()).orElse(0L),
                Optional.ofNullable(summaryThreadBean.getOfficialRoom()).orElse(0L)
        );
    }

    @Override
    public IncomeSummaryBean queryTradeValueByRoom(Long familyId, Long roomId, PeriodTypeEnum periodType) {
        List<PaySettleConfigCodeEnum> configList = Lists.newArrayList(
                PaySettleConfigCodeEnum.HALL_HALL_INCOME_TOTAL_AMOUNT,
                PaySettleConfigCodeEnum.HALL_OFFICIAL_HALL_INCOME_TOTAL_AMOUNT);
        //批量查询
        IncomeSummaryThreadBean summaryThreadBean = paymentManager.asyncBatchQueryTrade(familyId, roomId, periodType, configList);
        return IncomeSummaryBean.buildRoomAndOfficial(
                Optional.ofNullable(summaryThreadBean.getRoomGift()).orElse(0L),
                Optional.ofNullable(summaryThreadBean.getOfficialRoom()).orElse(0L)
        );
    }

    @Override
    public Map<Long, Integer> getPlayerPayCountByRooms(Long familyId, List<Long> roomIds, Date startDate, Date endDate) {
        int startValue = MyDateUtil.getDateDayValue(startDate);
        int endValue = MyDateUtil.getDateDayValue(endDate);

        Map<Long, Integer> resultMap = new ConcurrentHashMap<>();
        CountDownLatchWrapper latchWrapper = new CountDownLatchWrapper(ThreadConstants.guildIncomePool, roomIds.size());
        for (Long roomId : roomIds) {
            latchWrapper.submit(()->{
                //查询每个厅签约的主播
                List<Long> playerIds = contractRemote.getAllSignRoomPlayerIds(roomId);

                List<PlayerPayCountPo> countList = new ArrayList<>();
                List<List<Long>> partition = Lists.partition(playerIds, 1000);

                for (List<Long> playerGroup : partition) {
                    //查询每个厅的有收入主播数
                    List<PlayerPayCountPo> countGroup = incomeRecordMapper.getPlayerPayCountByRooms(new PlayerPayCountParamDto()
                                    .setFamilyId(familyId)
                                    .setRoomIds(Collections.singletonList(roomId))
                                    .setPlayerIds(playerGroup)
                            , startValue, endValue);
                    countList.addAll(countGroup);
                }

                if (CollectionUtils.isNotEmpty(countList)) {
                    Map<Long, List<PlayerPayCountPo>> collect = countList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(PlayerPayCountPo::getNjId));
                    List<PlayerPayCountPo> valueList = collect.get(roomId);
                    if (CollectionUtils.isNotEmpty(valueList)) {
                        Integer value = valueList.stream().map(PlayerPayCountPo::getTotalCount).filter(Objects::nonNull).reduce(0, Integer::sum);
                        resultMap.put(roomId, value);
                    }
                }
            });
        }
        latchWrapper.await();

        return resultMap;
    }

    @Override
    public int getPlayerPayCountForFamily(PlayerPayCountParamDto paramDto, Date startDate, Date endDate) {
        int startValue = MyDateUtil.getDateDayValue(startDate);
        int endValue = MyDateUtil.getDateDayValue(endDate);

        //根据签约的主播去重
        Integer count = incomeRecordMapper.getPlayerPayCountForFamily(paramDto, startValue, endValue);
        return count == null ? 0 : count;
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.XIMI;
    }
}