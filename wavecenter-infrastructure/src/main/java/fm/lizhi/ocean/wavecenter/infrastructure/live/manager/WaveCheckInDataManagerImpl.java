package fm.lizhi.ocean.wavecenter.infrastructure.live.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.live.bean.*;
import fm.lizhi.ocean.wavecenter.api.live.constants.CheckInDateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.live.constants.CheckInTaskRuleTypeEnum;
import fm.lizhi.ocean.wavecenter.api.live.request.*;
import fm.lizhi.ocean.wavecenter.api.live.response.*;
import fm.lizhi.ocean.wavecenter.common.utils.RedisLock;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.*;
import fm.lizhi.ocean.wavecenter.infrastructure.live.constant.CheckInScheduledConstants;
import fm.lizhi.ocean.wavecenter.infrastructure.live.constant.CheckInStatusConstants;
import fm.lizhi.ocean.wavecenter.infrastructure.live.convert.WaveCheckInDataConverter;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.dao.WaveCheckInDataDao;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.mapper.WaveRoomCheckInRecordMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.mapper.ext.WaveCheckInScheduleExtMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.live.dto.*;
import fm.lizhi.ocean.wavecenter.infrastructure.live.manager.checkinstat.*;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.GetRoomInfoByNjIdDTO;
import fm.lizhi.ocean.wavecenter.service.live.manager.LiveManager;
import fm.lizhi.ocean.wavecenter.service.live.manager.WaveCheckInDataManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 麦序福利数据管理器实现
 */
@Component
@Slf4j
public class WaveCheckInDataManagerImpl implements WaveCheckInDataManager {

    private static final DateTimeFormatter PLAYER_DATE_FORMATTER = DateTimeFormatter.ofPattern("MM/dd");

    @Autowired
    private UserManager userManager;

    @Autowired
    private WaveCheckInDataConverter waveCheckInDataConverter;

    @Autowired
    private WaveCheckInDataDao waveCheckInDataDao;

    @Autowired
    private WaveCheckInScheduleExtMapper waveCheckInScheduleExtMapper;

    @Autowired
    private Collection<StatisticPeriodHandler> statisticPeriodHandlers;

    @Autowired
    private CheckInRedisManagerImpl redisManager;

    @Autowired
    private LiveManager liveManager;


    @Autowired
    private WaveRoomCheckInRecordMapper waveRoomCheckInRecordMapper;

    @Override
    public ResponseGetCheckInRoomSum getCheckInRoomSum(RequestGetCheckInRoomSum req) {
        Long roomId = req.getRoomId();
        Long startDate = req.getStartDate();
        Long endDate = req.getEndDate();
        Long familyId = req.getFamilyId();
        CheckInDateTypeEnum dateType = req.getDateType();
        Integer appId = req.getAppId();
        List<WaveCheckInSchedule> schedules = waveCheckInDataDao.getCheckInRoomSchedules(roomId, startDate, endDate, familyId);
        List<Long> scheduleIds = schedules.stream().map(WaveCheckInSchedule::getId).collect(Collectors.toList());
        WaveCheckInIncomeSumDTO incomeSumDTO = waveCheckInDataDao.getCheckInIncomeSum(scheduleIds);
        int scheduledPlayerCnt = waveCheckInDataDao.getCheckInScheduledPlayerCnt(scheduleIds);
        return waveCheckInDataConverter.toResponseGetCheckInRoomSum(incomeSumDTO, scheduledPlayerCnt);
    }

    @Override
    public ResponseGetCheckInRoomStatistic getCheckInRoomStatistic(RequestGetCheckInRoomStatistic req) {
        Long roomId = req.getRoomId();
        CheckInDateTypeEnum dateType = req.getDateType();
        Long startDate = req.getStartDate();
        Long endDate = req.getEndDate();
        Long familyId = req.getFamilyId();
        Integer appId = req.getAppId();
        List<WaveCheckInSchedule> schedules = waveCheckInDataDao.getCheckInRoomSchedules(roomId, startDate, endDate, familyId);
        List<Long> scheduleIds = schedules.stream().map(WaveCheckInSchedule::getId).collect(Collectors.toList());
        List<WaveCheckInRecord> records = waveCheckInDataDao.getCheckInRecordsByScheduleIds(scheduleIds);
        List<Long> playerIds = records.stream().map(WaveCheckInRecord::getUserId).distinct().sorted(Comparator.naturalOrder()).collect(Collectors.toList());
        List<Long> recordIds = records.stream().map(WaveCheckInRecord::getId).collect(Collectors.toList());

        // 获取主持人 ID 并加入 playerIds
        List<Long> hostIds = schedules.stream().map(WaveCheckInSchedule::getHostId).filter(Objects::nonNull)
                .filter(uid -> 0L != uid)
                .distinct().collect(Collectors.toList());
        CollUtil.addAllIfNotContains(playerIds, hostIds);

        List<WaveCheckInUnDone> unDoneList = waveCheckInDataDao.getCheckInRoomUserUnDoneList(playerIds, roomId, startDate, endDate);
        List<WaveCheckInRoomUserLightGiftGroupDTO> lightGiftGroups = waveCheckInDataDao.getCheckInRoomUserLightGiftGroups(scheduleIds);
        List<WaveCheckInRoomUserRewardAmountGroupDTO> rewardAmountGroups = waveCheckInDataDao.getCheckInRoomUserRewardAmountGroups(scheduleIds);
        List<WaveCheckInAllMicGiftRecord> allMicGiftRecords = waveCheckInDataDao.getAllMicGiftRecords(scheduleIds);
        List<WaveCheckInDayMicRecord> dayMicRecords = waveCheckInDataDao.getDayMicRecords(appId, familyId, roomId, playerIds, startDate, endDate);

        // 添加全麦的用户记录进来
        CollUtil.addAllIfNotContains(playerIds, allMicGiftRecords.stream().map(WaveCheckInAllMicGiftRecord::getAllocationUserId).collect(Collectors.toList()));
        Map<Long, SimpleUserDto> simpleUserMap = getSimpleUserMap(playerIds);
        StatisticPeriodHandler periodHandler = getStatisticPeriodHandler(dateType);
        // 排列所有时间段
        List<StatisticPeriod> presetPeriods = periodHandler.buildPresetStatisticPeriods(startDate, endDate);
        // 日麦序奖励
        Map<Long, DayMicCounter> dayMicCounterMap = periodHandler.buildDayMicCounterMap(dayMicRecords);
        //获取私信人数和私信回复率 <userId, List<ChatStat>>
        Map<Long, List<ChatStat>> chatStats = periodHandler.buildPlayerChatStatMap(appId, playerIds, startDate, endDate);
        //获取今日未完成任务
        List<WaveCheckInUserTask> userTasks = periodHandler.buildWaveCheckInUserTaskList(recordIds);

        // userId -> (period -> sum)
        Map<Long, Map<StatisticPeriod, WaveCheckInUserRecordSumBean>> userRecordSumMap = buildRoomUserRecordSumMap(periodHandler, schedules, records);
        // userId -> sum
        Map<Long, WaveCheckInUserSumBean> roomUserSumMap = buildRoomUserSumMap(schedules, records
                , unDoneList, lightGiftGroups, rewardAmountGroups
                , allMicGiftRecords, dayMicCounterMap, chatStats, userTasks);


        // 构造结果列表
        List<WaveCheckInRoomStatisticBean> list = new ArrayList<>(playerIds.size());
        for (Long playerId : playerIds) {
            // 用户信息
            WaveCheckInUserBean player = waveCheckInDataConverter.toWaveCheckInUserBean(simpleUserMap.get(playerId));
            // 明细
            Map<StatisticPeriod, WaveCheckInUserRecordSumBean> periodMap = userRecordSumMap.computeIfAbsent(playerId, k -> new HashMap<>());
            List<WaveCheckInUserRecordSumBean> detail = new ArrayList<>(presetPeriods.size());
            for (StatisticPeriod presetPeriod : presetPeriods) {
                WaveCheckInUserRecordSumBean sumBean = periodMap.computeIfAbsent(presetPeriod, k -> initUserRecordSumBean(presetPeriod, periodHandler));
                detail.add(sumBean);
            }
            // 汇总
            WaveCheckInUserSumBean sum = roomUserSumMap.computeIfAbsent(playerId, k -> new WaveCheckInUserSumBean());
            // 合并成统计bean
            WaveCheckInRoomStatisticBean statisticBean = new WaveCheckInRoomStatisticBean();
            statisticBean.setPlayer(player);
            statisticBean.setDetail(detail);
            statisticBean.setSum(sum);
            list.add(statisticBean);
        }
        ResponseGetCheckInRoomStatistic resp = new ResponseGetCheckInRoomStatistic();
        resp.setList(list);
        //主持人信息
        Optional<SimpleUserDto> simpleUserDto = periodHandler.buildHostInfo(simpleUserMap, hostIds);
        simpleUserDto.ifPresent(dto -> resp.setHost(waveCheckInDataConverter.toWaveCheckInUserBean(dto)));
        return resp;
    }

    private Map<Long, SimpleUserDto> getSimpleUserMap(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        return userManager.getSimpleUserMapByIds(userIds);
    }

    private SimpleUserDto getSimpleUser(long userId) {
        Map<Long, SimpleUserDto> simpleUserMap = getSimpleUserMap(Collections.singletonList(userId));
        return simpleUserMap.get(userId);
    }

    private StatisticPeriodHandler getStatisticPeriodHandler(CheckInDateTypeEnum dateType) {
        for (StatisticPeriodHandler handler : statisticPeriodHandlers) {
            if (handler.supports(dateType)) {
                return handler;
            }
        }
        throw new IllegalArgumentException("Unsupported date type: " + dateType);
    }

    private Map<Long, Map<StatisticPeriod, WaveCheckInUserRecordSumBean>> buildRoomUserRecordSumMap(
            StatisticPeriodHandler periodHandler, List<WaveCheckInSchedule> schedules, List<WaveCheckInRecord> records) {
        // scheduleId -> schedule
        Map<Long, WaveCheckInSchedule> scheduleMap = new HashMap<>();
        for (WaveCheckInSchedule schedule : schedules) {
            scheduleMap.put(schedule.getId(), schedule);
        }
        // userId -> (period -> sum)
        Map<Long, Map<StatisticPeriod, WaveCheckInUserRecordSumBean>> userRecordSumMap = new HashMap<>();
        for (WaveCheckInRecord record : records) {
            WaveCheckInSchedule schedule = scheduleMap.get(record.getScheduleId());
            if (schedule != null) {
                Long userId = record.getUserId();
                StatisticPeriod period = periodHandler.buildNormalizedStatisticPeriod(schedule.getStartTime().getTime());
                WaveCheckInUserRecordSumBean sumBean = userRecordSumMap
                        .computeIfAbsent(userId, k -> new HashMap<>())
                        .computeIfAbsent(period, k -> initUserRecordSumBean(period, periodHandler));
                // 累加数据
                sumBean.setCharm(sumBean.getCharm() + record.getCharmValue());
                sumBean.setIncome(sumBean.getIncome() + record.getIncome());
            }
        }
        return userRecordSumMap;
    }

    private WaveCheckInUserRecordSumBean initUserRecordSumBean(StatisticPeriod period, StatisticPeriodHandler periodHandler) {
        String time = periodHandler.formatStatisticPeriod(period);
        WaveCheckInUserRecordSumBean sumBean = new WaveCheckInUserRecordSumBean();
        sumBean.setTime(time);
        return sumBean;
    }

    private Map<Long, WaveCheckInUserSumBean> buildRoomUserSumMap(List<WaveCheckInSchedule> schedules,
                                                                  List<WaveCheckInRecord> records,
                                                                  List<WaveCheckInUnDone> unDoneList,
                                                                  List<WaveCheckInRoomUserLightGiftGroupDTO> lightGiftGroups,
                                                                  List<WaveCheckInRoomUserRewardAmountGroupDTO> rewardAmountGroups,
                                                                  List<WaveCheckInAllMicGiftRecord> allMicGiftRecords,
                                                                  Map<Long, DayMicCounter> dayMicCounterMap,
                                                                  Map<Long, List<ChatStat>> chatStats,
                                                                  List<WaveCheckInUserTask> userTasks) {
        // scheduleId -> schedule
        Map<Long, WaveCheckInSchedule> scheduleMap = new HashMap<>();
        for (WaveCheckInSchedule schedule : schedules) {
            scheduleMap.put(schedule.getId(), schedule);
        }
        // userId -> (rewardLadder -> giftAmountSum)
        Map<Long, TreeMap<Long, Integer>> lightGiftSumMap = buildRoomUserLightGiftSumMap(lightGiftGroups);
        // userId -> (rewardLadder -> 全麦记录数)
        Map<Long, TreeMap<Long, Integer>> allMicGiftMap = buildAllMicGiftRecordMap(allMicGiftRecords);
        // userId -> sum
        Map<Long, WaveCheckInUserSumBean> roomUserSumMap = new HashMap<>();
        for (WaveCheckInRecord record : records) {
            Long userId = record.getUserId();
            WaveCheckInUserSumBean sumBean = roomUserSumMap.computeIfAbsent(userId, k -> new WaveCheckInUserSumBean());
            // 排档数
            if (Objects.equals(record.getScheduled(), CheckInScheduledConstants.SCHEDULER)) {
                sumBean.setScheduledCnt(sumBean.getScheduledCnt() + 1);
            }
            if (Objects.equals(record.getStatus(), CheckInStatusConstants.CHECK_IN)) {
                // 有效麦序
                sumBean.setSeatCnt(sumBean.getSeatCnt() + 1);
                // 有效麦序魅力值
                sumBean.setSeatCharm(sumBean.getSeatCharm() + record.getCharmValue());
            }
            // 主持档魅力值合计
            WaveCheckInSchedule schedule = scheduleMap.get(record.getScheduleId());
            if (schedule != null && Objects.equals(schedule.getHostId(), userId)) {
                sumBean.setHostCharmSum(sumBean.getHostCharmSum() + record.getCharmValue());
            }
            // 合计魅力值
            sumBean.setSumCharm(sumBean.getSumCharm() + record.getCharmValue());
            // 合计钻石值
            sumBean.setSumIncome(sumBean.getSumIncome() + record.getIncome());
            //进账
            if(record.getCharmDiffValue() >= 0) {
                sumBean.setCharmDiffIn(sumBean.getCharmDiffIn() + record.getCharmDiffValue());
            } else {
                sumBean.setCharmDiffOut(sumBean.getCharmDiffIn() + record.getCharmDiffValue());
            }
            // 备注
            sumBean.setRemark(record.getRemark());
        }
        for (WaveCheckInSchedule schedule : schedules) {
            Long hostId = schedule.getHostId();
            WaveCheckInUserSumBean hostSumBean = roomUserSumMap.computeIfAbsent(hostId, k -> new WaveCheckInUserSumBean());
            // 主持档数
            hostSumBean.setHostCnt(hostSumBean.getHostCnt() + 1);
        }
        for (WaveCheckInUnDone unDone : unDoneList) {
            Long userId = unDone.getUserId();
            WaveCheckInUserSumBean sumBean = roomUserSumMap.computeIfAbsent(userId, k -> new WaveCheckInUserSumBean());
            // 未完成任务分
            sumBean.setNotDoneScore(sumBean.getNotDoneScore() + unDone.getUnDoneScore());
            // 把总分塞进去
            sumBean.setNotDoneScoreDetail(String.valueOf(sumBean.getNotDoneScore()));
        }
        //填充未完成任务明细
        for (WaveCheckInUserTask userTask : userTasks) {
            WaveCheckInUserSumBean sumBean = roomUserSumMap.computeIfAbsent(userTask.getUserId(), k -> new WaveCheckInUserSumBean());
            sumBean.setNotDoneScoreDetail(convertTaskDetail(userTask.getTaskDailyUnDoneDetail(), userTask.getTaskRule()));
        }

        for (Map.Entry<Long, TreeMap<Long, Integer>> entry : lightGiftSumMap.entrySet()) {
            Long recUserId = entry.getKey();
            WaveCheckInUserSumBean sumBean = roomUserSumMap.computeIfAbsent(recUserId, k -> new WaveCheckInUserSumBean());
            // 收光记录
            sumBean.setLightGift(formatLightGiftSum(entry.getValue()));
        }
        for (WaveCheckInRoomUserRewardAmountGroupDTO rewardAmountGroup : rewardAmountGroups) {
            Long recUserId = rewardAmountGroup.getRecUserId();
            WaveCheckInUserSumBean sumBean = roomUserSumMap.computeIfAbsent(recUserId, k -> new WaveCheckInUserSumBean());
            // 收光奖励
            sumBean.setLightGiftAmount(sumBean.getLightGiftAmount() + rewardAmountGroup.getRewardAmountSum());
        }
        // 全麦记录统计
        for (WaveCheckInAllMicGiftRecord allMicGiftRecord : allMicGiftRecords) {
            Long userId = allMicGiftRecord.getAllocationUserId();
            if (userId == null) {
                continue;
            }
            WaveCheckInUserSumBean sumBean = roomUserSumMap.computeIfAbsent(userId, k -> new WaveCheckInUserSumBean());
            // 全麦奖励
            sumBean.setAllMicGiftAmount(sumBean.getAllMicGiftAmount() + allMicGiftRecord.getRewardAmount());
        }
        for (Map.Entry<Long, TreeMap<Long, Integer>> entry : allMicGiftMap.entrySet()) {
            Long userId = entry.getKey();
            WaveCheckInUserSumBean sumBean = roomUserSumMap.computeIfAbsent(userId, k -> new WaveCheckInUserSumBean());
            // 全麦记录
            sumBean.setAllMicGift(formatLightGiftSum(entry.getValue()));
        }
        for (Map.Entry<Long, DayMicCounter> entry : dayMicCounterMap.entrySet()) {
            Long userId = entry.getKey();
            WaveCheckInUserSumBean sumBean = roomUserSumMap.computeIfAbsent(userId, k -> new WaveCheckInUserSumBean());
            //日麦序奖励
            sumBean.setDayMicAmount(entry.getValue().getRewardAmountSum());
        }
        for (Map.Entry<Long, List<ChatStat>> entry : chatStats.entrySet()) {
            Long userId = entry.getKey();
            WaveCheckInUserSumBean sumBean = roomUserSumMap.computeIfAbsent(userId, k -> new WaveCheckInUserSumBean());
            //私信人数
            sumBean.setChatUserCnt(entry.getValue().get(0).getChatUserCnt());
            sumBean.setChatUserReplyRate(entry.getValue().get(0).getReplyChatRate());
        }
        return roomUserSumMap;
    }

    private String convertTaskDetail(String taskDetail, Integer taskRule) {
        if (taskDetail.equals("-1")){
            return "正在结算中...";
        }

        if (!taskRule.equals(CheckInTaskRuleTypeEnum.FULL_TASK_COMPLETION.getType())){
            return taskDetail;
        }

        return formatNumberListWithCounts(StrUtil.split(taskDetail, ",").stream().map(Long::parseLong).collect(Collectors.toList()), false);
    }
    /**
     * 将数字列表转换成字符串
     * @param numberList 100,200,100,300
     * @param keepSymbols 保留符号，比如 "+100*2,+200*1,+300*1 -100*2"
     * @return "+100*2,+200*1,+300*1 -100*2"
     */
    private String formatNumberListWithCounts(List<Long> numberList, Boolean keepSymbols) {
        if (CollUtil.isEmpty(numberList)) {
            return "";
        }

        if(numberList.size() == 1 && numberList.get(0) == 0) {
            return "0";
        }

        return numberList.stream()
                .collect(Collectors.groupingBy(n -> n, Collectors.counting()))
                .entrySet()
                .stream()
                .sorted(Map.Entry.comparingByKey())
                .map(entry -> {
                    Long number = entry.getKey();
                    if (keepSymbols){
                        return (number > 0 ? "+" + number : number) + "*" + entry.getValue();
                    }else {
                        return number + "*" + entry.getValue();
                    }
                })
                .collect(Collectors.joining(","));
    }

    private Map<Long, TreeMap<Long, Integer>> buildRoomUserLightGiftSumMap(
            List<WaveCheckInRoomUserLightGiftGroupDTO> lightGiftGroups) {
        // userId -> (rewardLadder -> giftAmountSum)
        Map<Long, TreeMap<Long, Integer>> lightGiftSumMap = new HashMap<>();
        for (WaveCheckInRoomUserLightGiftGroupDTO lightGiftGroup : lightGiftGroups) {
            Long recUserId = lightGiftGroup.getRecUserId();
            Map<Long, Integer> rewardLadderMap = lightGiftSumMap.computeIfAbsent(recUserId, k -> new TreeMap<>());
            Long rewardLadder = lightGiftGroup.getRewardLadder();
            Integer oldSum = rewardLadderMap.computeIfAbsent(rewardLadder, k -> 0);
            int newSum = oldSum + lightGiftGroup.getGiftAmountSum();
            rewardLadderMap.put(rewardLadder, newSum);
        }
        return lightGiftSumMap;
    }

    public Map<Long, TreeMap<Long, Integer>> buildAllMicGiftRecordMap(List<WaveCheckInAllMicGiftRecord> allMicGiftRecords){
        if (CollectionUtils.isEmpty(allMicGiftRecords)) {
            return Collections.emptyMap();
        }

        // userId -> (rewardLadder -> 用户对应记录数)
        Map<Long, TreeMap<Long, Integer>> allGiftMap = new HashMap<>();
        for (WaveCheckInAllMicGiftRecord allMicGiftRecord : allMicGiftRecords) {
            Long userId = allMicGiftRecord.getAllocationUserId();
            if (userId == null) {
                continue;
            }
            Map<Long, Integer> rewardLadderMap = allGiftMap.computeIfAbsent(userId, k -> new TreeMap<>());
            Long rewardLadder = allMicGiftRecord.getRewardLadder();
            rewardLadderMap.merge(rewardLadder, 1, Integer::sum);
        }
        return allGiftMap;
    }

    private String formatLightGiftSum(TreeMap<Long, Integer> rewardLadderToAmountSumMap) {
        if (MapUtils.isEmpty(rewardLadderToAmountSumMap)) {
            return StringUtils.EMPTY;
        }
        StringJoiner stringJoiner = new StringJoiner(",");
        for (Map.Entry<Long, Integer> entry : rewardLadderToAmountSumMap.entrySet()) {
            stringJoiner.add(entry.getKey() + "*" + entry.getValue());
        }
        return stringJoiner.toString();
    }

    @Override
    public ResponseGetCheckInPlayerSum getCheckInPlayerSum(RequestGetCheckInPlayerSum req) {
        Long playerId = req.getPlayerId();
        Long startDate = req.getStartDate();
        Long endDate = req.getEndDate();
        Long familyId = req.getFamilyId();
        Long roomId = req.getRoomId();
        WaveCheckInPlayerSumDTO playerSumDTO = waveCheckInDataDao.getCheckInPlayerSum(playerId, startDate, endDate, familyId, roomId);
        return waveCheckInDataConverter.toResponseGetCheckInPlayerSum(playerSumDTO);
    }

    @Override
    public ResponseGetCheckInPlayerStatistic getCheckInPlayerStatistic(RequestGetCheckInPlayerStatistic req) {
        Long playerId = req.getPlayerId();
        Long startDate = req.getStartDate();
        Long endDate = req.getEndDate();
        Long familyId = req.getFamilyId();
        Long roomId = req.getRoomId();
        Integer appId = req.getAppId();
        List<WaveCheckInSchedule> schedules = waveCheckInDataDao.getCheckInPlayerAndHostSchedules(playerId, startDate, endDate, familyId, roomId);
        List<Long> scheduleIds = schedules.stream().map(WaveCheckInSchedule::getId).collect(Collectors.toList());
        List<WaveCheckInRecord> records = waveCheckInDataDao.getCheckInPlayerRecords(scheduleIds, playerId);
        List<Long> roomIds = roomId != null ? Collections.singletonList(roomId) : schedules.stream().map(WaveCheckInSchedule::getRoomId).distinct().collect(Collectors.toList());
        List<WaveCheckInUnDone> unDoneList = waveCheckInDataDao.getCheckInUserUnDoneList(playerId, roomIds, startDate, endDate, familyId);
        List<WaveCheckInUserLightGiftGroupDTO> lightGiftGroups = waveCheckInDataDao.getCheckInUserLightGiftGroups(scheduleIds, playerId);
        List<WaveCheckInUserRewardAmountGroupDTO> rewardAmountGroups = waveCheckInDataDao.getCheckInUserRewardAmountGroups(scheduleIds, playerId);
        List<WaveCheckInAllMicGiftRecord> allMicGiftRecords = waveCheckInDataDao.getAllMicGiftRecordsByUserId(scheduleIds, playerId);
        List<WaveCheckInDayMicRecord> dayMicRecords = waveCheckInDataDao.getDayMicRecords(appId, familyId, roomId, Lists.newArrayList(playerId), startDate, endDate);

        SimpleUserDto simpleUser = getSimpleUser(playerId);
        // 主播视角明细是列出一天24个档期的数据. 但每行对应一天.
        StatisticPeriodHandler periodHandler = getStatisticPeriodHandler(CheckInDateTypeEnum.DAY);
        // 排列所有时间段
        TreeMap<Long, List<StatisticPeriod>> statisticPeriodsMap = buildPlayerPresetStatisticPeriodsMap(startDate, endDate, periodHandler);
        // startTimeOfDay -> (period -> sum)
        Map<Long, Map<StatisticPeriod, WaveCheckInUserRecordSumBean>> playerRecordSumMap = buildPlayerRecordSumMap(periodHandler, schedules, records, playerId);
        //私信人数、回复率 user -> 私信记录
        Map<Long, List<ChatStat>> chatStatMap = periodHandler.buildPlayerChatStatMap(appId, Collections.singletonList(playerId), startDate, endDate);
        //获取用户私信记录
        List<ChatStat> playerChatStats = chatStatMap.getOrDefault(playerId, Collections.emptyList());
        // startTimeOfDay -> sum
        Map<Long, WaveCheckInUserSumBean> playerSumMap = buildPlayerSumMap(playerId, schedules
                , records, unDoneList, lightGiftGroups, rewardAmountGroups
                , allMicGiftRecords, dayMicRecords, playerChatStats
        );
        // 用户信息
        WaveCheckInUserBean player = waveCheckInDataConverter.toWaveCheckInUserBean(simpleUser);
        // 构造结果列表
        ArrayList<WaveCheckInPlayerStatisticBean> list = new ArrayList<>(statisticPeriodsMap.size());
        for (Map.Entry<Long, List<StatisticPeriod>> entry : statisticPeriodsMap.entrySet()) {
            Long startTimeOfDay = entry.getKey();
            List<StatisticPeriod> presetPeriods = entry.getValue();
            // 日期
            String date = formatPlayerDate(startTimeOfDay);
            // 明细
            Map<StatisticPeriod, WaveCheckInUserRecordSumBean> periodMap = playerRecordSumMap.computeIfAbsent(startTimeOfDay, k -> new HashMap<>());
            List<WaveCheckInUserRecordSumBean> detail = new ArrayList<>(presetPeriods.size());
            for (StatisticPeriod presetPeriod : presetPeriods) {
                WaveCheckInUserRecordSumBean sumBean = periodMap.computeIfAbsent(presetPeriod, k -> initUserRecordSumBean(presetPeriod, periodHandler));
                detail.add(sumBean);
            }
            // 汇总
            WaveCheckInUserSumBean sum = playerSumMap.computeIfAbsent(startTimeOfDay, k -> new WaveCheckInUserSumBean());
            // 合并成统计bean
            WaveCheckInPlayerStatisticBean statisticBean = new WaveCheckInPlayerStatisticBean();
            statisticBean.setDate(date);
            statisticBean.setDetail(detail);
            statisticBean.setSum(sum);
            list.add(statisticBean);
        }
        ResponseGetCheckInPlayerStatistic resp = new ResponseGetCheckInPlayerStatistic();
        resp.setPlayer(player);
        resp.setList(list);
        return resp;
    }

    @Override
    public List<Long> getHostUserIds(Integer appId, Long njId, Date startTime, Date endTime) {
        return waveCheckInScheduleExtMapper.getHostUserIds(appId, njId, startTime, endTime);
    }

    @Override
    public List<Long> getManagerIds(Integer appId, Long njId) {
        return waveCheckInDataDao.getManagerIds(appId, njId);
    }

    @Override
    public ResponseGetCheckInRoomStatisticReport getCheckInRoomStatisticReport(RequestGetCheckInRoomStatisticReport req) {
        Long njId = req.getNjId();
        CheckInDateTypeEnum dateType = req.getDateType();
        Long startDate = req.getStartDate();
        Long endDate = req.getEndDate();
        Integer appId = req.getAppId();
        ResponseGetCheckInRoomStatisticReport result = new ResponseGetCheckInRoomStatisticReport();
        GetCheckInRoomStatisticReportDTO cache = redisManager.getCheckInRoomStatisticReport(appId, dateType, njId, startDate);
        if(cache != null) {
            return waveCheckInDataConverter.toResponseGetCheckInRoomStatisticReport(cache);
        }
        //加锁执行
        try (RedisLock lock = redisManager.getCheckInRoomStatisticReportLock(appId, dateType, njId, startDate)) {
            if (!lock.tryLock()) {
                //尝试获取锁，如果获取不到就不等了
                log.info("getCheckInRoomStatisticReport failed;appId:{}, dateType:{}, roomId:{}, startDate={}", appId, dateType, njId, startDate);
                result.setList(Collections.emptyList());
                return result;
            }
            //double check
            cache = redisManager.getCheckInRoomStatisticReport(appId, dateType, njId, startDate);
            if(cache != null) {
                return waveCheckInDataConverter.toResponseGetCheckInRoomStatisticReport(cache);
            }
            GetCheckInRoomStatisticReportDTO data = new GetCheckInRoomStatisticReportDTO();
            Result<GetRoomInfoByNjIdDTO> roomInfoByNjId = liveManager.getRoomInfoByNjId(njId);
            if(roomInfoByNjId.rCode() != 0) {
                //穿透风险
                log.error("getCheckInRoomStatisticReport failed;appId:{}, njId:{}, dateType:{}, startDate={}", appId, njId, dateType, startDate);
                result.setList(Collections.emptyList());
                return result;
            }
            //构造数据
            List<WaveCheckInRoomStatisticReportBean> list = getWaveCheckInRoomStatisticReportFromDB(roomInfoByNjId.target().getId(), dateType, startDate, endDate, appId);
            data.setList(list);
            List<SimpleUserDto> simpleUserByIds = userManager.getSimpleUserByIds(Collections.singletonList(njId));
            if(CollectionUtils.isNotEmpty(simpleUserByIds)) {
                data.setRoomInfo(waveCheckInDataConverter.toWaveCheckInUserBean(simpleUserByIds.get(0)));
            }
            //缓存起来
            redisManager.setCheckInRoomStatisticReport(appId, dateType, njId, startDate, data);
            return waveCheckInDataConverter.toResponseGetCheckInRoomStatisticReport(data);
        } catch (Exception e) {
            log.error("getCheckInRoomStatisticReport ex;appId:{}, dateType:{}, roomId:{}, startDate={}", appId, dateType, njId, startDate, e);
            result.setList(Collections.emptyList());
            return result;
        }
    }

    @NotNull
    private List<WaveCheckInRoomStatisticReportBean> getWaveCheckInRoomStatisticReportFromDB(Long roomId, CheckInDateTypeEnum dateType, Long startDate, Long endDate, Integer appId) {
        // 获取打卡
        List<WaveCheckInSchedule> schedules = waveCheckInDataDao.getCheckInRoomSchedules(roomId, startDate, endDate, null);
        List<Long> scheduleIds = schedules.stream().map(WaveCheckInSchedule::getId).collect(Collectors.toList());
        List<WaveCheckInRecord> records = waveCheckInDataDao.getCheckInRecordsByScheduleIds(scheduleIds);
        //过滤出 定排主播 + 有收入的非定排主播记录
        records = records.stream()
                .filter(record -> record.getScheduled() == CheckInScheduledConstants.SCHEDULER || record.getCharmValue() > 0)
                .collect(Collectors.toList());
        //获取用户
        List<Long> playerIds = records.stream()
                .map(WaveCheckInRecord::getUserId)
                .distinct()
                .sorted(Comparator.naturalOrder())
                .collect(Collectors.toList());
        // 获取主持人 ID 并加入 playerIds
        List<Long> hostIds = schedules.stream()
                .map(WaveCheckInSchedule::getHostId)
                .filter(Objects::nonNull)
                .filter(uid -> 0L != uid)
                .distinct()
                .collect(Collectors.toList());
        CollUtil.addAllIfNotContains(playerIds, hostIds);

        StatisticPeriodHandler periodHandler = getStatisticPeriodHandler(dateType);
        //获取私信人数和私信回复率 <userId, List<ChatStat>>
        Map<Long, List<ChatStat>> chatStats = periodHandler.buildPlayerChatStatMap(appId, playerIds, startDate, endDate);
        // userId -> sum
        Map<Long, WaveCheckInUserReportSumBean> roomUserSumMap = buildRoomUserReportSumMap(records, chatStats);
        Map<Long, SimpleUserDto> simpleUserMap = getSimpleUserMap(playerIds);
        // 构造结果列表
        List<WaveCheckInRoomStatisticReportBean> list = new ArrayList<>(playerIds.size());
        for (Long playerId : playerIds) {
            // 用户信息
            WaveCheckInUserBean player = waveCheckInDataConverter.toWaveCheckInUserBean(simpleUserMap.get(playerId));
            // 汇总
            WaveCheckInUserReportSumBean sum = roomUserSumMap.computeIfAbsent(playerId, k -> new WaveCheckInUserReportSumBean());
            // 合并成统计bean
            WaveCheckInRoomStatisticReportBean statisticBean = new WaveCheckInRoomStatisticReportBean();
            statisticBean.setPlayer(player);
            statisticBean.setSum(sum);
            list.add(statisticBean);
        }
        return list;
    }


    private Map<Long, WaveCheckInUserReportSumBean> buildRoomUserReportSumMap(List<WaveCheckInRecord> records,
                                                                  Map<Long, List<ChatStat>> chatStatsMap) {

        // userId -> sum
        Map<Long, WaveCheckInUserReportSumBean> roomUserSumMap = new HashMap<>();
        for (WaveCheckInRecord record : records) {
            Long userId = record.getUserId();
            WaveCheckInUserReportSumBean sumBean = roomUserSumMap.computeIfAbsent(userId, k -> new WaveCheckInUserReportSumBean());
            if (Objects.equals(record.getStatus(), CheckInStatusConstants.CHECK_IN)) {
                // 有效麦序
                sumBean.setSeatCnt(sumBean.getSeatCnt() + 1);
            }
            // 合计魅力值
            sumBean.setSumCharm(sumBean.getSumCharm() + record.getCharmValue());
            log.debug("buildRoomUserReportSumMap userId={};charm={}", userId, sumBean.getSumCharm());
        }
        for (Map.Entry<Long, List<ChatStat>> entry : chatStatsMap.entrySet()) {
            Long userId = entry.getKey();
            List<ChatStat> chatStatList = entry.getValue();
            if(CollectionUtils.isEmpty(chatStatList)) {
                continue;
            }
            WaveCheckInUserReportSumBean sumBean = roomUserSumMap.computeIfAbsent(userId, k -> new WaveCheckInUserReportSumBean());
            //私信人数
            sumBean.setChatUserCnt(chatStatList.get(0).getChatUserCnt());
        }
        return roomUserSumMap;
    }


    private TreeMap<Long, List<StatisticPeriod>> buildPlayerPresetStatisticPeriodsMap(long startDate, long endDate,
                                                                                      StatisticPeriodHandler periodHandler) {
        // startTimeOfDay -> hour periods
        TreeMap<Long, List<StatisticPeriod>> statisticPeriodsMap = new TreeMap<>();
        long startTimeOfDay = getStartTimeOfDay(startDate);
        while (startTimeOfDay <= endDate) {
            long endTime = startTimeOfDay + 24 * 60 * 60 * 1000L - 1;
            List<StatisticPeriod> periods = periodHandler.buildPresetStatisticPeriods(startTimeOfDay, endTime);
            statisticPeriodsMap.put(startTimeOfDay, periods);
            startTimeOfDay += 24 * 60 * 60 * 1000L;
        }
        return statisticPeriodsMap;
    }

    private Map<Long, Map<StatisticPeriod, WaveCheckInUserRecordSumBean>> buildPlayerRecordSumMap(
            StatisticPeriodHandler periodHandler, List<WaveCheckInSchedule> schedules, List<WaveCheckInRecord> records, Long playerId) {
        // scheduleId -> schedule
        Map<Long, WaveCheckInSchedule> scheduleMap = new HashMap<>();
        for (WaveCheckInSchedule schedule : schedules) {
            scheduleMap.put(schedule.getId(), schedule);
        }
        // startTimeOfDay -> (period -> sum)
        Map<Long, Map<StatisticPeriod, WaveCheckInUserRecordSumBean>> playerRecordSumMap = new HashMap<>();
        for (WaveCheckInRecord record : records) {
            WaveCheckInSchedule schedule = scheduleMap.get(record.getScheduleId());
            if (schedule != null && record.getUserId().equals(playerId)) {
                long startTime = schedule.getStartTime().getTime();
                StatisticPeriod period = periodHandler.buildNormalizedStatisticPeriod(startTime);
                long startTimeOfDay = getStartTimeOfDay(startTime);
                WaveCheckInUserRecordSumBean sumBean = playerRecordSumMap
                        .computeIfAbsent(startTimeOfDay, k -> new HashMap<>())
                        .computeIfAbsent(period, k -> initUserRecordSumBean(period, periodHandler));
                // 累加数据
                sumBean.setCharm(sumBean.getCharm() + record.getCharmValue());
                sumBean.setIncome(sumBean.getIncome() + record.getIncome());
            }
        }
        return playerRecordSumMap;
    }

    @Override
    public Long countUserCheckIn(Long userId, Date startDay, Date endTime) {
        return waveRoomCheckInRecordMapper.countUserCheckIn(ContextUtils.getBusinessEvnEnum().getAppId()
                , userId
                , DateUtil.getDayStart(startDay)
                , DateUtil.getDayEnd(endTime));
    }

    private Map<Long, WaveCheckInUserSumBean> buildPlayerSumMap(Long playerId,
                                                                List<WaveCheckInSchedule> schedules,
                                                                List<WaveCheckInRecord> records,
                                                                List<WaveCheckInUnDone> unDoneList,
                                                                List<WaveCheckInUserLightGiftGroupDTO> lightGiftGroups,
                                                                List<WaveCheckInUserRewardAmountGroupDTO> rewardAmountGroups,
                                                                List<WaveCheckInAllMicGiftRecord> allMicGiftRecords,
                                                                List<WaveCheckInDayMicRecord> dayMicRecords,
                                                                List<ChatStat> chatStats) {
        // scheduleId -> schedule
        Map<Long, WaveCheckInSchedule> scheduleMap = new HashMap<>();
        for (WaveCheckInSchedule schedule : schedules) {
            scheduleMap.put(schedule.getId(), schedule);
        }
        // startTimeOfDay -> (rewardLadder -> giftAmountSum)
        Map<Long, TreeMap<Long, Integer>> lightGiftSumMap = buildPlayerLightGiftSumMap(scheduleMap, lightGiftGroups);
        // startTimeOfDay -> sum
        Map<Long, WaveCheckInUserSumBean> playerSumMap = new HashMap<>();
        for (WaveCheckInRecord record : records) {
            WaveCheckInSchedule schedule = scheduleMap.get(record.getScheduleId());
            if (schedule == null || !record.getUserId().equals(playerId)) {
                continue;
            }
            long startTimeOfDay = getStartTimeOfDay(schedule.getStartTime().getTime());
            WaveCheckInUserSumBean sumBean = playerSumMap.computeIfAbsent(startTimeOfDay, k -> new WaveCheckInUserSumBean());
            // 排档数
            if (Objects.equals(record.getScheduled(), CheckInScheduledConstants.SCHEDULER)) {
                sumBean.setScheduledCnt(sumBean.getScheduledCnt() + 1);
            }
            if (Objects.equals(record.getStatus(), CheckInStatusConstants.CHECK_IN)) {
                // 有效麦序
                sumBean.setSeatCnt(sumBean.getSeatCnt() + 1);
                // 有效麦序魅力值
                sumBean.setSeatCharm(sumBean.getSeatCharm() + record.getCharmValue());
            }
            // 主持档魅力值合计
            if (Objects.equals(schedule.getHostId(), record.getUserId())) {
                sumBean.setHostCharmSum(sumBean.getHostCharmSum() + record.getCharmValue());
            }
            // 合计魅力值
            sumBean.setSumCharm(sumBean.getSumCharm() + record.getCharmValue());
            // 合计钻石值
            sumBean.setSumIncome(sumBean.getSumIncome() + record.getIncome());

            //进账
            if(record.getCharmDiffValue() >= 0) {
                sumBean.setCharmDiffIn(sumBean.getCharmDiffIn() + record.getCharmDiffValue());
            } else {
                sumBean.setCharmDiffOut(sumBean.getCharmDiffIn() + record.getCharmDiffValue());
            }
        }
        for (WaveCheckInSchedule schedule : schedules) {
            if (Objects.equals(schedule.getHostId(), playerId)) {
                long startTimeOfDay = getStartTimeOfDay(schedule.getStartTime().getTime());
                WaveCheckInUserSumBean hostSumBean = playerSumMap.computeIfAbsent(startTimeOfDay, k -> new WaveCheckInUserSumBean());
                // 主持档数
                hostSumBean.setHostCnt(hostSumBean.getHostCnt() + 1);
            }
        }
        for (WaveCheckInUnDone unDone : unDoneList) {
            long startTimeOfDay = getStartTimeOfDay(unDone.getCurrDate().getTime());
            WaveCheckInUserSumBean sumBean = playerSumMap.computeIfAbsent(startTimeOfDay, k -> new WaveCheckInUserSumBean());
            // 未完成任务分
            sumBean.setNotDoneScore(sumBean.getNotDoneScore() + unDone.getUnDoneScore());
        }
        for (Map.Entry<Long, TreeMap<Long, Integer>> entry : lightGiftSumMap.entrySet()) {
            Long startTimeOfDay = entry.getKey();
            WaveCheckInUserSumBean sumBean = playerSumMap.computeIfAbsent(startTimeOfDay, k -> new WaveCheckInUserSumBean());
            // 收光记录
            sumBean.setLightGift(formatLightGiftSum(entry.getValue()));
        }
        for (WaveCheckInUserRewardAmountGroupDTO rewardAmountGroup : rewardAmountGroups) {
            WaveCheckInSchedule schedule = scheduleMap.get(rewardAmountGroup.getScheduleId());
            if (schedule == null) {
                continue;
            }
            long startTimeOfDay = getStartTimeOfDay(schedule.getStartTime().getTime());
            WaveCheckInUserSumBean sumBean = playerSumMap.computeIfAbsent(startTimeOfDay, k -> new WaveCheckInUserSumBean());
            // 收光奖励
            sumBean.setLightGiftAmount(sumBean.getLightGiftAmount() + rewardAmountGroup.getRewardAmountSum());
        }
        for (WaveCheckInAllMicGiftRecord allMicGiftRecord : allMicGiftRecords) {
            WaveCheckInSchedule schedule = scheduleMap.get(allMicGiftRecord.getScheduleId());
            if (schedule == null) {
                continue;
            }
            long startTimeOfDay = getStartTimeOfDay(schedule.getStartTime().getTime());
            WaveCheckInUserSumBean sumBean = playerSumMap.computeIfAbsent(startTimeOfDay, k -> new WaveCheckInUserSumBean());
            // 全麦奖励
            sumBean.setAllMicGiftAmount(sumBean.getAllMicGiftAmount() + allMicGiftRecord.getRewardAmount());
        }
        Map<Long, TreeMap<Long, Integer>> allMicGiftTreeMap = buildAllMicGiftRecordMap(scheduleMap, allMicGiftRecords);
        for (Map.Entry<Long, TreeMap<Long, Integer>> entry : allMicGiftTreeMap.entrySet()) {
            Long startTimeOfDay = entry.getKey();
            WaveCheckInUserSumBean sumBean = playerSumMap.computeIfAbsent(startTimeOfDay, k -> new WaveCheckInUserSumBean());
            // 全麦记录
            sumBean.setAllMicGift(formatLightGiftSum(entry.getValue()));
        }
        for (WaveCheckInDayMicRecord dayMicRecord : dayMicRecords) {
            long startTimeOfDay = getStartTimeOfDay(dayMicRecord.getCalcDate().getTime());
            WaveCheckInUserSumBean sumBean = playerSumMap.computeIfAbsent(startTimeOfDay, k -> new WaveCheckInUserSumBean());
            //日麦序奖励
            sumBean.setDayMicAmount(sumBean.getDayMicAmount() + dayMicRecord.getRewardAmountSum());
        }
       for (ChatStat chatStat : chatStats) {
            long startTimeOfDay = getStartTimeOfDay(chatStat.getStatDate().getTime());
            WaveCheckInUserSumBean sumBean = playerSumMap.computeIfAbsent(startTimeOfDay, k -> new WaveCheckInUserSumBean());
           //私信人数
           sumBean.setChatUserCnt(chatStat.getChatUserCnt());
           sumBean.setChatUserReplyRate(chatStat.getReplyChatRate());
        }
        return playerSumMap;
    }

    private Map<Long, TreeMap<Long, Integer>> buildPlayerLightGiftSumMap(Map<Long, WaveCheckInSchedule> scheduleMap,
                                                                         List<WaveCheckInUserLightGiftGroupDTO> lightGiftGroups) {
        // startTimeOfDay -> (rewardLadder -> giftAmountSum)
        Map<Long, TreeMap<Long, Integer>> lightGiftSumMap = new HashMap<>();
        for (WaveCheckInUserLightGiftGroupDTO lightGiftGroup : lightGiftGroups) {
            Long scheduleId = lightGiftGroup.getScheduleId();
            WaveCheckInSchedule schedule = scheduleMap.get(scheduleId);
            if (schedule != null) {
                long startTimeOfDay = getStartTimeOfDay(schedule.getStartTime().getTime());
                Map<Long, Integer> rewardLadderMap = lightGiftSumMap.computeIfAbsent(startTimeOfDay, k -> new TreeMap<>());
                Long rewardLadder = lightGiftGroup.getRewardLadder();
                Integer oldSum = rewardLadderMap.computeIfAbsent(rewardLadder, k -> 0);
                int newSum = oldSum + lightGiftGroup.getGiftAmountSum();
                rewardLadderMap.put(rewardLadder, newSum);
            }
        }
        return lightGiftSumMap;
    }

    public Map<Long, TreeMap<Long, Integer>> buildAllMicGiftRecordMap(Map<Long, WaveCheckInSchedule> scheduleMap, List<WaveCheckInAllMicGiftRecord> allMicGiftRecords){
        if (CollectionUtils.isEmpty(allMicGiftRecords)) {
            return Collections.emptyMap();
        }

        // startTimeOfDay -> (rewardLadder -> 用户对应记录数)
        Map<Long, TreeMap<Long, Integer>> allGiftMap = new HashMap<>();
        for (WaveCheckInAllMicGiftRecord allMicGiftRecord : allMicGiftRecords) {
            Long scheduleId = allMicGiftRecord.getScheduleId();
            WaveCheckInSchedule schedule = scheduleMap.get(scheduleId);
            if (schedule == null) {
                continue;
            }
            long startTimeOfDay = getStartTimeOfDay(schedule.getStartTime().getTime());
            Map<Long, Integer> rewardLadderMap = allGiftMap.computeIfAbsent(startTimeOfDay, k -> new TreeMap<>());
            Long rewardLadder = allMicGiftRecord.getRewardLadder();
            rewardLadderMap.merge(rewardLadder, 1, Integer::sum);
        }
        return allGiftMap;
    }

    private String formatPlayerDate(long startTimeOfDay) {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(startTimeOfDay), ZoneId.systemDefault())
                .format(PLAYER_DATE_FORMATTER);
    }

    private static long getStartTimeOfDay(long time) {
        LocalDateTime t = LocalDateTime.ofInstant(Instant.ofEpochMilli(time), ZoneId.systemDefault());
        if (t.getHour() == 0 && t.getMinute() == 0 && t.getSecond() == 0 && t.getNano() == 0) {
            return time;
        }
        return t.withHour(0).withMinute(0).withSecond(0).withNano(0)
                .atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }


}
