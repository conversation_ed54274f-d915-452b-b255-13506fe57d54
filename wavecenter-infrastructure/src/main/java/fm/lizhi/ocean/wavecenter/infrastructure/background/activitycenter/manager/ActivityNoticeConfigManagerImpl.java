package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.manager;

import cn.hutool.core.collection.CollUtil;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestDeleteActivityNoticeConfig;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.dao.ActivityNoticeDao;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityNoticeCategoryRelation;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityNoticeCategoryRelationExample;
import fm.lizhi.ocean.wavecenter.datastore.background.mapper.ActivityNoticeCategoryRelationMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert.ActivityNoticeConfigConvert;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityNoticeConfig;
import fm.lizhi.ocean.wavecenter.datastore.background.mapper.ActivityNoticeConfigMapper;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityNoticeConfigManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityNoticeConfigDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ActivityNoticeConfigManagerImpl implements ActivityNoticeConfigManager {

    @Autowired
    private ActivityNoticeConfigMapper activityNoticeConfigMapper;

    @Autowired
    private ActivityNoticeCategoryRelationMapper activityNoticeCategoryRelationMapper;

    @Autowired
    private ActivityNoticeDao activityNoticeDao;


    @Override
    public List<ActivityNoticeConfigDTO> getAllNoticeConfig(Integer appId) {

        List<ActivityNoticeConfig> configList = activityNoticeDao.selectAllNoticeConfigList(appId);
        List<ActivityNoticeConfigDTO> dtoList = ActivityNoticeConfigConvert.I.activityNoticeConfig2ActivityNoticeConfigDTOs(configList);
        if (CollUtil.isEmpty(dtoList)) {
            log.info("get activity notice config is empty. return default value, appId:{}", appId);
            return CollUtil.newArrayList(new ActivityNoticeConfigDTO().setAppId(appId).setId(-1L).setContent(""));
        }

        Map<Long, List<ActivityNoticeCategoryRelation>> categoryMap = activityNoticeDao.selectNoticeCategoryRelation(appId,
                        dtoList.stream().map(ActivityNoticeConfigDTO::getId).collect(Collectors.toList())
                )
                .stream()
                .collect(Collectors.groupingBy(ActivityNoticeCategoryRelation::getNoticeId));

        if (CollUtil.isNotEmpty(categoryMap)) {
            dtoList.forEach(dto -> {
                List<ActivityNoticeCategoryRelation> relations = categoryMap.get(dto.getId());
                if (CollUtil.isNotEmpty(relations)) {
                    List<Integer> categoryList = relations.stream()
                            .map(ActivityNoticeCategoryRelation::getCategoryValue)
                            .collect(Collectors.toList());
                    dto.setCategoryList(categoryList);
                }
            });
        }

        return dtoList;
    }

    @Override
    public Optional<ActivityNoticeConfigDTO> getNoticeConfig(Integer appId, Integer category) {
        List<ActivityNoticeCategoryRelation> relationList = activityNoticeDao.selectNoticeConfigRelation(appId, category);
        if (CollUtil.isEmpty(relationList)) {
            log.info("get activity notice relation is empty, appId:{}, category:{}", appId, category);
            return Optional.empty();
        }

        ActivityNoticeConfigDTO configDto = null;
        Long noticeId = null;

        if (CollUtil.size(relationList) == 1) {
            ActivityNoticeCategoryRelation relation = relationList.get(0);
            noticeId = relation.getNoticeId();
        }

        // 大于 1，代表存在多个配置，需要根据 category 来获取对应的配置
        if (CollUtil.size(relationList) > 1) {
            noticeId = relationList.stream().filter(relation -> relation.getCategoryValue().equals(category))
                    .findFirst().map(ActivityNoticeCategoryRelation::getNoticeId).orElse(null);
        }

        if (noticeId != null) {
            ActivityNoticeConfig config = activityNoticeDao.selectNoticeConfigById(noticeId);
            List<ActivityNoticeCategoryRelation> relations = activityNoticeDao.selectNoticeCategoryRelation(appId, noticeId);
            configDto = ActivityNoticeConfigConvert.I.buildActivityNoticeConfigDTO(config,
                    relations.stream().map(ActivityNoticeCategoryRelation::getCategoryValue).collect(Collectors.toList())
            );
        }

        return Optional.ofNullable(configDto);
    }

    @Override
    public Result<Long> updateNoticeConfig(ActivityNoticeConfigDTO noticeConfigDTO) {
        ActivityNoticeConfig config = ActivityNoticeConfigConvert.I.activityNoticeConfigDTO2ActivityNoticeConfig(noticeConfigDTO);
        Long configId = activityNoticeDao.insertOrUpdate(config, noticeConfigDTO.getCategoryList());
        if (configId == null) {
            return RpcResult.fail(UPDATE_NOTICE_CONFIG_FAIL, "更新公告配置失败");
        }
        return RpcResult.success(configId);
    }

    @Override
    public List<Integer> hasCategoryByAppId(Integer appId, List<Integer> categoryList) {
        if(CollUtil.isEmpty(categoryList)) {
            return Collections.emptyList();
        }
        ActivityNoticeCategoryRelationExample example = new ActivityNoticeCategoryRelationExample();
        example.createCriteria().andAppIdEqualTo(appId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andCategoryValueIn(categoryList);

        return activityNoticeCategoryRelationMapper.selectByExample(example)
                .stream()
                .map(ActivityNoticeCategoryRelation::getCategoryValue)
                .collect(Collectors.toList());
    }

    @Override
    public List<Integer> getCategoryListByNoticeId(Integer appId, Long noticeId) {
        if (noticeId == null || noticeId < 0) {
            return Collections.emptyList();
        }

        ActivityNoticeCategoryRelationExample example = new ActivityNoticeCategoryRelationExample();
        example.createCriteria().andAppIdEqualTo(appId)
               .andNoticeIdEqualTo(noticeId);

        return activityNoticeCategoryRelationMapper.selectByExample(example)
               .stream()
               .map(ActivityNoticeCategoryRelation::getCategoryValue)
               .collect(Collectors.toList());
    }

    @Override
    public Result<Void> deleteNoticeConfig(RequestDeleteActivityNoticeConfig config) {
        boolean success = activityNoticeDao.deleteNoticeConfig(config.getId());
        return success ? RpcResult.success() : RpcResult.fail(DELETE_NOTICE_CONFIG_FAIL);
    }

}
