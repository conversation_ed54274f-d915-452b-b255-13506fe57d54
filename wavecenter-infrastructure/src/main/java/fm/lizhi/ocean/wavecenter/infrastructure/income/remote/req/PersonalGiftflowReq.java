package fm.lizhi.ocean.wavecenter.infrastructure.income.remote.req;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/4/24 20:27
 */
@Data
@Accessors(chain = true)
public class PersonalGiftflowReq {
    private long recUserId;
    private Date startDate;
    private Date endDate;
    private int pageNumber;
    private int pageSize;
    private Long recRoomId;
    private Long sendUserId;
}
