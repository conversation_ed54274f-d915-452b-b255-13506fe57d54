package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowRoomMetricValue;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcGrowRoomMetricValueExtMapper {

    @Select("<script>\n" +
            "  SELECT * FROM `wavecenter_grow_room_metric_value`\n" +
            "  WHERE `app_id` = #{appId}\n" +
            "    AND `room_id` = #{roomId}\n" +
            "    AND `start_week_date` &gt;= #{minStartWeekDate}\n" +
            "    AND `start_week_date` &lt;= #{maxStartWeekDate}\n" +
            "</script>")
    List<WcGrowRoomMetricValue> getWeeksRoomMetricValues(
            @Param("appId") int appId,
            @Param("roomId") long roomId,
            @Param("minStartWeekDate") Date minStartWeekDate,
            @Param("maxStartWeekDate") Date maxStartWeekDate);
}
