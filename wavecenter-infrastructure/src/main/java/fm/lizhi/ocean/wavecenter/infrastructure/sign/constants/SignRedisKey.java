package fm.lizhi.ocean.wavecenter.infrastructure.sign.constants;

import fm.lizhi.ocean.wavecenter.base.constants.IRedisKey;

/**
 * <AUTHOR>
 * @date 2024/11/12 11:34
 */
public enum SignRedisKey implements IRedisKey {

    /**
     * 流程同步分布式锁
     * WC_SIGN_SYNC_FLOW_[flowId]
     */
    SYNC_FLOW;

    @Override
    public String getPrefix() {
        return "WC_SIGN";
    }

    @Override
    public String getName() {
        return this.name();
    }
}
