package fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.GetFamilyLevelAwardItemBean;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestCreateFamilyLevelAwardRule;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestUpdateFamilyLevelAwardRule;
import fm.lizhi.ocean.wavecenter.api.award.family.response.ResponseGetFamilyLevelAwardRule;
import fm.lizhi.ocean.wavecenter.base.convert.CommonConvert;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilyLevelAwardRule;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR,
        uses = {
                CommonConvert.class,
        },
        imports = {
                ConfigUtils.class,
                Date.class,
        }
)
public interface FamilyLevelAwardRuleConvert {

    FamilyLevelAwardRuleConvert I = Mappers.getMapper(FamilyLevelAwardRuleConvert.class);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "deleted", constant = "false")
    @Mapping(target = "deleteTime", ignore = true)
    @Mapping(target = "createTime", expression = "java(new Date())")
    @Mapping(target = "creator", source = "operator")
    @Mapping(target = "modifyTime", expression = "java(new Date())")
    @Mapping(target = "modifier", source = "operator")
    WcFamilyLevelAwardRule toCreateEntity(RequestCreateFamilyLevelAwardRule request);

    @BeanMapping(unmappedTargetPolicy = ReportingPolicy.IGNORE, unmappedSourcePolicy = ReportingPolicy.ERROR,
            ignoreUnmappedSourceProperties = {"items"})
    @Mapping(target = "modifyTime", expression = "java(new Date())")
    @Mapping(target = "modifier", source = "operator")
    WcFamilyLevelAwardRule toUpdateEntity(RequestUpdateFamilyLevelAwardRule request);

    ResponseGetFamilyLevelAwardRule toResponseGetFamilyLevelAwardRule(
            WcFamilyLevelAwardRule rule, List<GetFamilyLevelAwardItemBean> items,
            Long levelId, String levelName, Boolean levelDeleted);
}
