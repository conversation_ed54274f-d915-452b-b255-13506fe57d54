package fm.lizhi.ocean.wavecenter.infrastructure.common.manager;

import fm.lizhi.ocean.wavecenter.api.common.bean.AddEvaluateRecordReq;
import fm.lizhi.ocean.wavecenter.api.common.bean.GetEvaluateRecordCountReq;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wavecenter.datastore.platform.common.entity.WcEvaluateRecord;
import fm.lizhi.ocean.wavecenter.datastore.platform.common.entity.WcEvaluateRecordExample;
import fm.lizhi.ocean.wavecenter.datastore.platform.common.mapper.WcEvaluateRecordMapper;
import fm.lizhi.ocean.wavecenter.service.common.manager.FeedbackManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/6/14 20:44
 */
@Component
public class FeedbackManagerImpl implements FeedbackManager {

    @Autowired
    private WcEvaluateRecordMapper wcEvaluateRecordMapper;
    @Autowired
    private CommonConfig config;

    @Override
    public void addEvaluateRecord(AddEvaluateRecordReq req) {
        Long cnt = getEvaluateRecordCount(GetEvaluateRecordCountReq.builder().appId(req.getAppId()).userId(req.getUserId()).build());
        if (cnt != null && cnt > 0) {
            return;
        }

        WcEvaluateRecord wcEvaluateRecord = new WcEvaluateRecord();
        wcEvaluateRecord.setAppId(req.getAppId());
        wcEvaluateRecord.setUserId(req.getUserId());
        wcEvaluateRecord.setEvaluateTag(config.getEvaluateTag());
        wcEvaluateRecordMapper.insert(wcEvaluateRecord);
    }

    @Override
    public Long getEvaluateRecordCount(GetEvaluateRecordCountReq req) {
        WcEvaluateRecordExample example = new WcEvaluateRecordExample();
        WcEvaluateRecordExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(req.getAppId())
                .andEvaluateTagEqualTo(config.getEvaluateTag())
                .andUserIdEqualTo(req.getUserId());
        return wcEvaluateRecordMapper.countByExample(example);
    }
}
