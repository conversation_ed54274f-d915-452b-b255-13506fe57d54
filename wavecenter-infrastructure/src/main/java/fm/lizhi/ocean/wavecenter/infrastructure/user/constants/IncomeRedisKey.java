package fm.lizhi.ocean.wavecenter.infrastructure.user.constants;

import fm.lizhi.ocean.wavecenter.base.constants.IRedisKey;

/**
 * 收入RedisKey
 */
public enum IncomeRedisKey implements IRedisKey {

    /**
     * 送礼消息幂等收入
     * WC_INCOME_GIFT_MSG_IDEMPOTENT_INCOME_STR_#{appId}_#{transactionId}
     * 过期时间：7天
     */
    GIFT_MSG_IDEMPOTENT_INCOME_STR,

    /**
     * 是否初始化过魅力值统计信息记录
     * WC_INCOME_INIT_CHARM_STAT_HASH_#{appId}_#{njId}_#{date}
     * field: userId
     * value: 次数
     * 过期时间：1天
     */
    INIT_CHARM_STAT_HASH,

    ;


    @Override
    public String getPrefix() {
        return "WC_INCOME";
    }

    @Override
    public String getName() {
        return this.name();
    }
}
