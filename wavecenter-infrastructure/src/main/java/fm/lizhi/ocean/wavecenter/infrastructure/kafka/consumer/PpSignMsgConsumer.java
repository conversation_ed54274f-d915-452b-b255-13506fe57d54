package fm.lizhi.ocean.wavecenter.infrastructure.kafka.consumer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import fm.lizhi.common.kafka.common.strategy.AutoOffsetReset;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaHandler;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaListener;
import fm.lizhi.common.kafka.ioc.api.strategy.annotation.AutoOffsetResetStrategy;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.message.constant.MessageTargetLinkEnum;
import fm.lizhi.ocean.wavecenter.api.message.constant.MessageTypeEnum;
import fm.lizhi.ocean.wavecenter.api.message.request.RequestSendMessage;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.bean.FamilyAndNjContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.common.utils.KafkaMsgUtils;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.constant.SingerEliminationReasonConstant;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.constant.SingerOperatorConstant;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerInfoDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerInfoManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerVerifyApplyManager;
import fm.lizhi.ocean.wavecenter.service.message.manager.WaveCenterMessageManager;
import fm.lizhi.ocean.wavecenter.service.permissions.handler.RoleHandler;
import fm.lizhi.ocean.wavecenter.service.sign.dto.RequestFamilyAndNjContractDTO;
import fm.lizhi.ocean.wavecenter.service.sign.manager.ContractManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import fm.pp.family.bean.player.sign.AdminSignMsg;
import fm.pp.family.bean.player.sign.PlayerSignMsg;
import fm.pp.family.bean.player.sign.PlayerSignMsgEvent;
import fm.pp.family.bean.player.sign.UserType;
import fm.pp.family.constants.WaveSignConstant;
import fm.pp.family.constants.WaveSignType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/22 17:35
 */
@Slf4j
@Component
@KafkaListener(clusterNamespace = "pp-kafka250-bootstrap-server")
public class PpSignMsgConsumer {

    @Autowired
    private WaveCenterMessageManager waveCenterMessageManager;
    @Autowired
    private UserManager userManager;
    @Autowired
    private FamilyManager familyManager;
    @Autowired
    private RoleHandler roleHandler;
    @Autowired
    private ContractManager contractManager;
    @Autowired
    private SingerInfoManager singerInfoManager;

    @Autowired
    private SingerVerifyApplyManager singerVerifyApplyManager;

    private static final Map<String, String> ADMIN_SIGN_MSG_CONTENT = MapUtil.<String, String>builder()
            .put(WaveSignConstant.NJ_REC.name(), "你收到来自「#familyName」家族的签约邀请，请您于「#signDeadline」前完成合同签署。")
            .put(WaveSignConstant.NJ_OVERDUE.name(), "「#njName」未在有限期内完成签约合同签署，本次签约邀请已失效，请双方沟通后再发起邀请。")
            .put(WaveSignConstant.NJ_REJECT.name(), "「#njName」拒绝你的签约邀请，本次邀请已失效，请双方沟通后再发起邀请。")
            .put(WaveSignConstant.NJ_SUCC.name(), "「#njName」已完成签约合同签署，请您于「#signDeadline」前完成合同签署。")
            .put(WaveSignConstant.SUCC.name(), "「#njName」与「#familyName」家族已达成签约关系，签约期限为#year年，有效期至「#expireTime」")
            .put(WaveSignConstant.APPLY_REFUSED.name(), "你发起的解约申请审核未通过，有疑问请联系对接小编。")
            .put(WaveSignConstant.APPLY_AGREED.name(), "你发起的解约申请已审核通过，请您于「#signDeadline」前完成合同签署。")
            .put(WaveSignConstant.REC_CANCEL.name(), "「#njName」向您发起解约邀请，请您于「#signDeadline」前完成合同签署。")
            .put(WaveSignConstant.REJECT.name(), "「#rejectName」已拒绝解约合同签署，本次解约合同已失效，请沟通后再重新签署。")
            .put(WaveSignConstant.OVERDUE.name(), "「#overdueName」未在有限期内完成解约合同签署，本次解约合同已失效，请沟通后再重新签署。")
            .put(WaveSignConstant.CANCEL_SUCC.name(), "「#njName」与「#familyName」家族已完成解约签署，合同生效时间为「#beginTime」。")
            .build();


    /**
     * 陪玩签约消息消费
     *
     * @param body
     */
    @KafkaHandler(topic = "pp_topic_player_sign_msg", group = "pp_topic_player_sign_msg_wave_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handlePlayerSignMsg(String body) {
        String msg = null;

        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            log.info("pp.handlePlayerSignMsg msg={}", msg);
            PlayerSignMsg playerSignMsg = JsonUtil.loads(msg, PlayerSignMsg.class);
            ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.PP);

            Pair<String, Long> contentAndTarget = genContentAndTarget(playerSignMsg);

            RequestSendMessage request = new RequestSendMessage()
                    .setType(MessageTypeEnum.NOTIFY.getType())
                    .setAppId(BusinessEvnEnum.PP.getAppId())
                    .setTitle(genTitle(playerSignMsg))
                    .setContent(contentAndTarget.getKey())
                    .setTargetUserId(contentAndTarget.getValue())
                    .setSendUserId(1L)
                    .setLinkType(MessageTargetLinkEnum.SIGN_MANAGEMENT.name())
                    .setBizId(playerSignMsg.getPlayerSignId());
            waveCenterMessageManager.sendMessage(request);

            //解约成功后置处理
            if (PlayerSignMsgEvent.DO_CANCEL.getCode().equals(playerSignMsg.getMsgEvent())) {
                //解除授权数据
                UserInFamilyBean userInFamily = familyManager.getUserInFamily(playerSignMsg.getNjId());
                Long familyId = userInFamily.getFamilyId();
                roleHandler.removePlayerWithFamilyAuth(familyId, playerSignMsg.getPlayerId());
                // 添加歌手淘汰标记
                singerInfoManager.addEliminateSingerTag(BusinessEvnEnum.PP.getAppId(), playerSignMsg.getPlayerId());
                singerVerifyApplyManager.updateSingerSignInfo(BusinessEvnEnum.PP.getAppId(), playerSignMsg.getPlayerId());
            } else if (PlayerSignMsgEvent.DO_SIGN.getCode().equals(playerSignMsg.getMsgEvent())) {
                singerVerifyApplyManager.updateSingerSignInfo(BusinessEvnEnum.PP.getAppId(), playerSignMsg.getPlayerId());
            }

        } catch (Exception e) {
            log.error("handlePlayerSignMsg error, msg:{}, orgMsg:{}", msg, body, e);
        }
    }

    private ContractTypeEnum genContractType(PlayerSignMsg playerSignMsg) {
        String msgEvent = playerSignMsg.getMsgEvent();
        if (PlayerSignMsgEvent.DO_SIGN.getCode().equals(msgEvent)
                || PlayerSignMsgEvent.INVITE_SIGN.getCode().equals(msgEvent)) {
            return ContractTypeEnum.SIGN;
        } else {
            return ContractTypeEnum.CANCEL;
        }
    }

    private String genTitle(PlayerSignMsg playerSignMsg) {
        ContractTypeEnum contractTypeEnum = genContractType(playerSignMsg);
        if (contractTypeEnum == ContractTypeEnum.SIGN) {
            return "签约申请";
        } else {
            return "解约申请";
        }
    }

    private Pair<String, Long> genContentAndTarget(PlayerSignMsg playerSignMsg) {
        String msgEvent = playerSignMsg.getMsgEvent();
        RoleEnum userType = genUserType(playerSignMsg);

        if (PlayerSignMsgEvent.INVITE_SIGN.getCode().equals(msgEvent)) {
            if (userType == RoleEnum.ROOM) {
                return Pair.of("您收到了一条加入房间邀请，请及时处理", playerSignMsg.getPlayerId());
            } else {
                return Pair.of("您收到了一条加入房间申请，请及时处理", playerSignMsg.getNjId());
            }
        }

        if (PlayerSignMsgEvent.DO_SIGN.getCode().equals(msgEvent)) {
            if (userType == RoleEnum.ROOM) {
                String njName = getUserName(playerSignMsg.getNjId());
                return Pair.of(String.format("您已成功加入家族，您的管理员是%s", njName), playerSignMsg.getPlayerId());
            } else {
                String playerName = getUserName(playerSignMsg.getPlayerId());
                return Pair.of(String.format("您已成功邀请%s加入了家族", playerName), playerSignMsg.getNjId());
            }
        }

        if (PlayerSignMsgEvent.INVITE_CANCEL.getCode().equals(msgEvent)) {
            if (userType == RoleEnum.ROOM) {
                return Pair.of("您收到了一条解约申请，请及时处理", playerSignMsg.getPlayerId());
            } else {
                return Pair.of("您收到了一条成员解约申请，请及时处理", playerSignMsg.getNjId());
            }
        }

        if (PlayerSignMsgEvent.DO_CANCEL.getCode().equals(msgEvent)) {
            if (userType == RoleEnum.ROOM) {
                return Pair.of("您已成功退出家族", playerSignMsg.getPlayerId());
            } else {
                String name = getUserName(playerSignMsg.getPlayerId());
                return Pair.of(String.format("%s已退出家族", name), playerSignMsg.getNjId());
            }
        }

        return Pair.of("", 0L);
    }

    private String getUserName(Long userId) {
        List<SimpleUserDto> userList = userManager.getSimpleUserByIds(Lists.newArrayList(userId));
        if (CollectionUtils.isEmpty(userList)) {
            return "";
        }
        return userList.get(0).getName();
    }

    private RoleEnum genUserType(PlayerSignMsg playerSignMsg) {
        String userType = playerSignMsg.getUserType();
        if (UserType.PLAYER.name().equals(userType)) {
            return RoleEnum.PLAYER;
        }
        if (UserType.FAMILY.name().equals(userType)) {
            return RoleEnum.FAMILY;
        }
        if (UserType.ADMIN.name().equals(userType)) {
            return RoleEnum.ROOM;
        }
        return RoleEnum.USER;
    }

    /**
     * 管理员签约消息消费
     *
     * @param body
     */
    @KafkaHandler(topic = "pp_topic_admin_sign_msg", group = "pp_topic_admin_sign_msg_wave_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleAdminSignMsg(String body) {
        String msg = null;

        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            log.info("pp.handleAdminSignMsg msg={}", msg);

            AdminSignMsg adminSignMsg = JsonUtil.loads(msg, AdminSignMsg.class);
            ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.PP);

            waveCenterMessageManager.sendMessage(new RequestSendMessage()
                    .setTargetUserId(adminSignMsg.getTargetUserId())
                    .setBizId(adminSignMsg.getSignId())
                    .setContent(genAdminSignMessageContent(adminSignMsg))
                    .setTitle(genAdminSignMessageTitle(adminSignMsg))
                    .setAppId(BusinessEvnEnum.PP.getAppId())
                    .setType(MessageTypeEnum.NOTIFY.getType())
                    .setSendUserId(1L)
                    .setLinkType(MessageTargetLinkEnum.SIGN_MANAGEMENT.name())
            );

            //解约成功后置处理
            if (WaveSignConstant.CANCEL_SUCC.name().equals(adminSignMsg.getMsgType())) {
                //解除授权
                PageBean<FamilyAndNjContractBean> pageBean = contractManager.queryContract(RequestFamilyAndNjContractDTO.builder().contractId(adminSignMsg.getSignId()).build());
                if (CollectionUtils.isNotEmpty(pageBean.getList())) {
                    FamilyAndNjContractBean contract = pageBean.getList().get(0);
                    roleHandler.removeRoomWithFamilyAuth(contract.getFamilyId(), contract.getNjUserId());
                }
            }

        } catch (Exception e) {
            log.error("handleAdminSignMsg error, msg:{}, orgMsg:{}", msg, body, e);
        }
    }


    private String genAdminSignMessageTitle(AdminSignMsg adminSignMsg) {
        String msgType = adminSignMsg.getMsgType();
        try {
            WaveSignConstant signConstant = WaveSignConstant.valueOf(msgType);
            return signConstant.getType() == WaveSignType.SIGN ? "签约通知" : "解约通知";
        } catch (Exception e) {
            log.error("genAdminSignMessageTitle error, msgType:{}", msgType, e);
            return "";
        }
    }


    private String genAdminSignMessageContent(AdminSignMsg adminSignMsg) {

        String msgContent = ADMIN_SIGN_MSG_CONTENT.get(adminSignMsg.getMsgType());
        if (StrUtil.isBlank(msgContent)) {
            return "";
        }

        Map<String, String> msgParams = adminSignMsg.getMsgParams();
        if (CollUtil.isEmpty(msgParams)) {
            return msgContent;
        }

        for (String param : msgParams.keySet()) {
            msgContent = msgContent.replace("#" + param, msgParams.get(param));
        }

        return msgContent;
    }


}
