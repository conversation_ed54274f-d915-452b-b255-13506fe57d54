package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.job.room;

import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.job.player.PlayerWeekAbilitySettleParam;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.SettlePeriodDTO;
import fm.lizhi.ocean.wavecenter.service.grow.ability.handler.RoomAbilitySettleHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/10 16:35
 */
@Slf4j
public abstract class AbstractRoomWeekAbilitySettleJob {

    @Autowired
    private RoomAbilitySettleHandler roomAbilitySettleHandler;

    /**
     * 结算入口
     * @param param
     */
    protected void doSettle(RoomWeekAbilitySettleParam param) {
        // 获取结算周期
        SettlePeriodDTO settlePeriod = getSettlePeriod(param);
        log.info("settlePeriod={}", settlePeriod);

        // 是否指定结算
        List<Long> roomIds = settleRoomIds(param);
        if (CollectionUtils.isEmpty(roomIds)) {
            roomAbilitySettleHandler.settleByPeriod(settlePeriod);
        } else {
            roomAbilitySettleHandler.settleByPeriod(settlePeriod, roomIds);
        }
    }

    /**
     * 获取结算周期
     * @param param
     * @return
     */
    private SettlePeriodDTO getSettlePeriod(RoomWeekAbilitySettleParam param){
        // 优先获取参数
        if (param != null && param.getWeekStartDay() != null) {
            Date weekStart = MyDateUtil.getDayValueDate(param.getWeekStartDay());
            Date weekEnd = MyDateUtil.getLastWeekEndDayByStartDay(weekStart);
            return new SettlePeriodDTO(weekStart, weekEnd);
        }

        // 否则获取上一个自然周的周期
        Date lastWeekStartDay = MyDateUtil.getLastWeekStartDay();
        Date lastWeekEndDay = MyDateUtil.getLastWeekEndDay();
        return new SettlePeriodDTO(lastWeekStartDay, lastWeekEndDay);
    }

    /**
     * 查询指定结算名单
     * @param param
     * @return
     */
    private List<Long> settleRoomIds(RoomWeekAbilitySettleParam param){
        // 优先获取参数
        if (param != null && CollectionUtils.isNotEmpty(param.getRoomIds())) {
            return param.getRoomIds();
        }
        return Collections.emptyList();
    }

}
