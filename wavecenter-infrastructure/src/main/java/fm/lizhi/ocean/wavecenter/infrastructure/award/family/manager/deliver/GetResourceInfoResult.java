package fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager.deliver;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 获取资源信息结果
 */
@Data
public class GetResourceInfoResult {

    /**
     * 资源名称
     */
    private String resourceName;

    /**
     * 资源图片
     */
    private String resourceImage;

    /**
     * 构造空的资源信息, 适用于非从业务侧选中特定资源的情况, 比如推荐卡只关心数量和有效期, 这些在通用信息中已经有了
     *
     * @return 空的资源信息
     */
    public static GetResourceInfoResult emptyInfo() {
        GetResourceInfoResult result = new GetResourceInfoResult();
        result.setResourceImage(StringUtils.EMPTY);
        result.setResourceName(StringUtils.EMPTY);
        return result;
    }

    /**
     * 构造资源信息
     *
     * @param resourceName  资源名称
     * @param resourceImage 资源图片
     * @return 资源信息
     */
    public static GetResourceInfoResult of(String resourceName, String resourceImage) {
        GetResourceInfoResult result = new GetResourceInfoResult();
        result.setResourceName(resourceName);
        result.setResourceImage(resourceImage);
        return result;
    }
}
