package fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.singleton;

import fm.lizhi.common.dubbo.DubboClientBuilder;
import fm.lizhi.hy.vip.api.HyMedalInfoV2Service;
import fm.lizhi.hy.vip.api.HyMedalSendScheduledV2Service;
import fm.lizhi.live.pp.idl.officialCertifiedTag.api.OfficialCertifiedTagRecordIDLService;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.ServiceProviderConstants;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class DecorateServiceProvider {

    @Bean
    public HyMedalInfoV2Service hyMedalInfoV2Service(){
        return new DubboClientBuilder<>(HyMedalInfoV2Service.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public HyMedalSendScheduledV2Service hyMedalSendScheduledV2Service(){
        return new DubboClientBuilder<>(HyMedalSendScheduledV2Service.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }


    @Bean
    public OfficialCertifiedTagRecordIDLService officialCertifiedTagRecordIdlService(){
        return new DubboClientBuilder<>(OfficialCertifiedTagRecordIDLService.class)
               .connections(ServiceProviderConstants.connections)
               .timeoutInMillis(ServiceProviderConstants.timeout)
               .retries(ServiceProviderConstants.readRetries)
               .build();
    }
}
