package fm.lizhi.ocean.wavecenter.infrastructure.sign.remote;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.bean.NjAndPlayerContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.OperateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseWithdrawCancel;
import fm.lizhi.ocean.wavecenter.base.remote.IRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.request.RequestInviteCancel;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.request.RequestInviteSign;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.response.ResponseInviteCancel;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.response.ResponseInviteSign;
import fm.lizhi.ocean.wavecenter.service.sign.dto.OperateSignDTO;
import fm.lizhi.ocean.wavecenter.service.sign.dto.QueryNonContractDTO;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 非电子签签约远程接口
 * <AUTHOR>
 * @date 2024/10/10 14:15
 */
public interface INonContractRemote extends IRemote {

    /**
     * 查询最近一次签约记录
     * @param userId
     * @return
     */
    Optional<NjAndPlayerContractBean> queryUserLast(Long userId, String type);

    /**
     * 查询当前公司是否在主体变更阶段
     * @param curUserId
     * @return
     */
    boolean isInChangeCompany(Long curUserId);

    /**
     * 查询当前公司是否在主体变更准备阶段
     * @param curUserId
     * @return
     */
    boolean isInChangeCompanyPreparedStage(Long curUserId);

    /**
     * 签约限制检查
     * @return
     */
    Pair<Integer, String> checkInviteSignLimit(Long userId, Long njId, RoleEnum userType, RoleEnum checkType);

    /**
     * 用户申请为主播
     * @param request
     * @return
     */
    ResponseInviteSign inviteSign(RequestInviteSign request);

    /**
     * 申请解约
     * @param request
     * @return
     */
    ResponseInviteCancel inviteCancel(RequestInviteCancel request);

    /**
     * 查询用户签约数据
     * @param userId
     * @param types
     * @param status
     * @return
     */
    List<NjAndPlayerContractBean> queryUserSign(Long userId, List<ContractTypeEnum> types, List<SignRelationEnum> status);

    /**
     * 操作签约
     * @param playSignId
     * @param curUserId
     * @param type
     * @param role
     * @param operateType
     * @return
     */
    OperateSignDTO operateSign(Long playSignId, Long curUserId, ContractTypeEnum type, RoleEnum role, OperateTypeEnum operateType);

    /**
     * 检查是否可以签约
     * @param playSignId
     * @param curUserId
     * @param opRole
     * @param checkRole
     * @return
     */
    Pair<Integer, String> checkCanSignForConfirm(Long playSignId, Long curUserId, RoleEnum opRole, RoleEnum checkRole);

    /**
     * 查询合同
     * @param paramDTO
     * @return
     */
    PageBean<NjAndPlayerContractBean> queryList(QueryNonContractDTO paramDTO);

    /**
     * 审批解约
     * @param playSignId
     * @param curUserId
     * @param operateType
     * @return
     */
    Pair<Integer, String> reviewCancel(Long playSignId, Long curUserId, OperateTypeEnum operateType);

    /**
     * 取消解约
     * @param playerSignId
     * @param curUserId
     * @param opUserRole
     * @return
     */
    ResponseWithdrawCancel withdrawCancel(Long playerSignId, Long curUserId, RoleEnum opUserRole);

    /**
     * 查询厅的签约主播数
     * @param njIds
     * @return
     */
    Map<Long, Integer> countSignPlayerByRooms(List<Long> njIds);

    /**
     * 统计厅下签约主播数据
     * @param njId
     * @return
     */
    Integer countPlayerSignNum(Long njId);

    /**
     * 查询陪玩当前签约厅主
     * @param userId
     * @return
     */
    Optional<Long> getPlayerCurSignNj(Long userId);

    /**
     * 查询陪玩最近一次解约厅主
     * @param userId
     * @return
     */
    Optional<Long> getPlayerLastCancelSign(Long userId);


}
