package fm.lizhi.ocean.wavecenter.infrastructure.user.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.infrastructure.user.po.RoomWhitelistCategoryPo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/27 16:46
 */
@DataStore(namespace = "mysql_heiye_lzppfamily_r")
public interface HyRoomWhitelistCategoryMapper {

    @Select({
            "<script>"
            , "SELECT *"
            ,"FROM room_whitelist_category"
            ,"WHERE status=1 and user_id IN "
            , "<foreach collection='userIds' item='uId' open='(' separator=',' close=')'>"
            , "#{uId}"
            , "</foreach>"
            ,"</script>"
    })
    List<RoomWhitelistCategoryPo> getCategoryIdByUserIds(@Param("userIds") List<Long> userIds);

}
