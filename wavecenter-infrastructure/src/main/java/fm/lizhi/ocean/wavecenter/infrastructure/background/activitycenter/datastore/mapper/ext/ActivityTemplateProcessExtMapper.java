package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityTemplateProcess;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface ActivityTemplateProcessExtMapper {

    @Select("SELECT * FROM `activity_template_process` WHERE `template_id` = #{templateId} ORDER BY `index` ASC")
    List<ActivityTemplateProcess> getProcessesByTemplateId(@Param("templateId") long templateId);

    @Update("DELETE FROM `activity_template_process` WHERE `template_id` = #{templateId}")
    int deleteProcessesByTemplateId(@Param("templateId") long templateId);
}
