package fm.lizhi.ocean.wavecenter.infrastructure.live.manager;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import fm.lizhi.ocean.wavecenter.api.live.bean.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/6/25 19:14
 */
@Component
public class CheckGroupManager {

    /**
     * 时间合计相加
     * @return
     */
    public <T extends IDetailList> TimeStatsSumBean sumTimeStats(List<T> dataList){
        BigDecimal income = BigDecimal.ZERO;
        int charm = 0;
        int seatOrder = 0;
        int hostCnt = 0;
        int checkPlayerNumber = 0;

        for (T data : dataList) {
            IStatsEle ele = data.foundStats();
            if (ele == null) {
                continue;
            }

            String incomeStr = ele.foundIncome();
            if (StringUtils.isNotBlank(incomeStr)) {
                income = income.add(new BigDecimal(incomeStr));
            }

            if (ele.foundCharm() != null) {
                charm += ele.foundCharm();
            }

            if (ele.foundSeatOrder() != null) {
                seatOrder += ele.foundSeatOrder();
            }

            if (ele.foundHostCnt() != null) {
                hostCnt += ele.foundHostCnt();
            }

            if (ele.foundCheckPlayerNumber() != null) {
                checkPlayerNumber += ele.foundCheckPlayerNumber();
            }
        }
        return new TimeStatsSumBean()
                .setIncome(income)
                .setSeatOrder(seatOrder)
                .setHostCnt(hostCnt)
                .setCheckPlayerNumber(checkPlayerNumber)
                .setCharm(charm);
    }

    /**
     * 按照时间合计
     * @param dataList
     * @return
     * @param <T>
     */
    public <T extends IDetailList> List<TimeStatsBean> groupTimeStats(List<T> dataList){

        //分组
        Map<String, List<IDetailEle>> timeMap = new HashMap<>();
        for (T data : dataList) {
            List<IDetailEle> eleList = data.foundDetail();
            if (CollectionUtils.isEmpty(eleList)) {
                continue;
            }
            for (IDetailEle ele : eleList) {
                Date date = ele.foundTime();
                if (date == null) {
                    continue;
                }
                String dateStr = DateUtil.formatDateTime(date);
                timeMap.computeIfAbsent(dateStr, k -> new ArrayList<>()).add(ele);
            }
        }

        //合计
        List<TimeStatsBean> result = new ArrayList<>();
        for (Map.Entry<String, List<IDetailEle>> entry : timeMap.entrySet()) {
            TimeStatsBean vo = new TimeStatsBean();

            String dateStr = entry.getKey();
            DateTime dateTime = DateUtil.parseDateTime(dateStr);
            vo.setTime(dateTime);

            List<IDetailEle> eleList = entry.getValue();
            BigDecimal income = BigDecimal.ZERO;
            int charm = 0;
            int seatOrder = 0;
            int hostCnt = 0;
            int checkPlayerNumber = 0;
            for (IDetailEle ele : eleList) {
                String incomeStr = ele.foundIncome();
                if (StringUtils.isNotBlank(incomeStr)) {
                    income = income.add(new BigDecimal(incomeStr));
                }

                if (ele.foundCharm() != null) {
                    charm += ele.foundCharm();
                }

                if (ele.foundSeatOrder() != null) {
                    seatOrder += ele.foundSeatOrder();
                }

                if (ele.foundHostCnt() != null) {
                    hostCnt += ele.foundHostCnt();
                }

                if (ele.foundCheckPlayerNumber() != null) {
                    checkPlayerNumber += ele.foundCheckPlayerNumber();
                }
            }

            vo.setIncome(income)
                    .setCharm(charm)
                    .setSeatOrder(seatOrder)
                    .setHostCnt(hostCnt)
                    .setCheckPlayerNumber(checkPlayerNumber);

            result.add(vo);
        }

        return result;
    }
}
