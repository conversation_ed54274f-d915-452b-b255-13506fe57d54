package fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcFamilyAwardDeliverRecordExtMapper {

    @Update("UPDATE `wavecenter_family_award_deliver_record`\n" +
            "SET `status` = 2, `deliver_time` = NOW(), `deliver_date` = CURDATE(), `modify_time` = NOW()\n" +
            "WHERE `id` = #{id} AND `status` = 1")
    int updateToSuccess(@Param("id") long id);

    @Update("UPDATE `wavecenter_family_award_deliver_record`\n" +
            "SET `status` = 2, `deliver_time` = NOW(), `deliver_date` = CURDATE(), `modify_time` = NOW()\n" +
            "WHERE `id` = #{id}")
    int updateReDeliverToSuccess(@Param("id") long id);

    @Update("UPDATE `wavecenter_family_award_deliver_record`\n" +
            "SET `status` = 3, `deliver_time` = NOW(), `deliver_date` = CURDATE(), `modify_time` = NOW()\n" +
            "WHERE `id` = #{id} AND `status` = 1")
    int updateToFailure(@Param("id") long id);

    @Update("UPDATE `wavecenter_family_award_deliver_record`\n" +
            "SET `status` = 3, `deliver_time` = NOW(), `deliver_date` = CURDATE(), `modify_time` = NOW()\n" +
            "WHERE `id` = #{id}")
    int updateReDeliverToFailure(@Param("id") long id);

    @Update("UPDATE `wavecenter_family_award_deliver_record`\n" +
            "SET `status` = 4, `deliver_time` = NOW(), `deliver_date` = CURDATE(), `modify_time` = NOW()\n" +
            "WHERE `id` = #{id} AND `status` = 1")
    int updateToPartialFailure(@Param("id") long id);

    @Update("UPDATE `wavecenter_family_award_deliver_record`\n" +
            "SET `status` = 4, `deliver_time` = NOW(), `deliver_date` = CURDATE(), `modify_time` = NOW()\n" +
            "WHERE `id` = #{id}")
    int updateReDeliverToPartialFailure(@Param("id") long id);

    @Update("UPDATE `wavecenter_family_award_deliver_record`\n" +
            "SET `status` = 1, `deliver_time` = NOW(), `deliver_date` = CURDATE(), `modify_time` = NOW()\n" +
            "WHERE `id` = #{id}")
    int updateReDeliverToDelivering(@Param("id") long id);
}
