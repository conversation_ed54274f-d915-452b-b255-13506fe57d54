package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.repository;

import com.alibaba.dubbo.common.extension.Activate;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.Period;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.RoomAbility;
import fm.lizhi.ocean.wavecenter.domain.grow.repository.GrowRoomAbilityRepository;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowRoomAbilityWeek;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowRoomAbilityWeekCapability;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowRoomAbilityWeekCapabilityExample;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowRoomAbilityWeekExample;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.mapper.WcGrowRoomAbilityWeekCapabilityMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.mapper.WcGrowRoomAbilityWeekMapper;
import fm.lizhi.ocean.wavecenter.service.grow.ability.manager.GrowPlayerAbilityManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/10 19:30
 */
@Slf4j
@Component
public class GrowRoomAbilityRepositoryImpl implements GrowRoomAbilityRepository {

    @Autowired
    private WcGrowRoomAbilityWeekMapper abilityWeekMapper;
    @Autowired
    private WcGrowRoomAbilityWeekCapabilityMapper abilityWeekCapabilityMapper;
    @Autowired
    private GrowPlayerAbilityManager growPlayerAbilityManager;
    @Autowired
    private FamilyManager familyManager;

    @Override
    public RoomAbility getRoomAbility(Long roomId, Period period) {

        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        String deployEnv = ConfigUtils.getEnvRequired().name();

        WcGrowRoomAbilityWeekExample abilityExm = new WcGrowRoomAbilityWeekExample();
        abilityExm.createCriteria()
                .andAppIdEqualTo(appId)
                .andDeployEnvEqualTo(deployEnv)
                .andRoomIdEqualTo(roomId)
                .andStartWeekDateEqualTo(period.getStart())
                .andEndWeekDateEqualTo(period.getEnd());

        List<WcGrowRoomAbilityWeek> abilityList = abilityWeekMapper.selectByExample(abilityExm);
        if (CollectionUtils.isEmpty(abilityList)) {
            log.info("abilityList is empty. roomId={},startDate={}", roomId, period.getStart());
            return null;
        }

        WcGrowRoomAbilityWeek ability = abilityList.get(0);

        WcGrowRoomAbilityWeekCapabilityExample capExm = new WcGrowRoomAbilityWeekCapabilityExample();
        capExm.createCriteria()
                .andAppIdEqualTo(appId)
                .andDeployEnvEqualTo(deployEnv)
                .andRoomIdEqualTo(roomId)
                .andStartWeekDateEqualTo(period.getStart())
                .andEndWeekDateEqualTo(period.getEnd());

        List<WcGrowRoomAbilityWeekCapability> capList = abilityWeekCapabilityMapper.selectByExample(capExm);
        Map<String, BigDecimal> capabilityMap = capList.stream().collect(Collectors.toMap(WcGrowRoomAbilityWeekCapability::getCapabilityCode, WcGrowRoomAbilityWeekCapability::getAbilityValue, (k1, k2) -> k2));

        Integer playerCount = growPlayerAbilityManager.countRoomAbilityPlayer(roomId, period.getStart(), period.getEnd());

        return new RoomAbility(ability.getId()
                , ability.getAppId()
                , ability.getRoomId()
                , period
                , playerCount
                , capabilityMap);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveRoomAbility(RoomAbility roomAbility) {
        UserInFamilyBean userInFamily = familyManager.getUserInFamily(roomAbility.getRoomId());
        Long familyId = userInFamily.getFamilyId() != null ? userInFamily.getFamilyId() : 0;

        String deployEnv = ConfigUtils.getEnvRequired().name();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Period period = roomAbility.getPeriod();

        // 新增查询逻辑
        WcGrowRoomAbilityWeekExample example = new WcGrowRoomAbilityWeekExample();
        example.createCriteria()
            .andRoomIdEqualTo(roomAbility.getRoomId())
            .andStartWeekDateEqualTo(period.getStart())
            .andEndWeekDateEqualTo(period.getEnd())
            .andAppIdEqualTo(appId)
            .andDeployEnvEqualTo(deployEnv);

        List<WcGrowRoomAbilityWeek> existingWeeks = abilityWeekMapper.selectByExample(example);
        boolean exists = CollectionUtils.isNotEmpty(existingWeeks);

        WcGrowRoomAbilityWeek abilityWeek = new WcGrowRoomAbilityWeek();
        abilityWeek.setRoomId(roomAbility.getRoomId());
        abilityWeek.setTotalScore(roomAbility.settleCompositeScore());
        abilityWeek.setStartWeekDate(period.getStart());
        abilityWeek.setEndWeekDate(period.getEnd());
        abilityWeek.setAppId(appId);
        abilityWeek.setDeployEnv(deployEnv);
        abilityWeek.setModifyTime(new Date());

        if (exists) {
            // 更新逻辑
            abilityWeek.setId(existingWeeks.get(0).getId());
            abilityWeek.setCreateTime(existingWeeks.get(0).getCreateTime());
            abilityWeekMapper.updateByPrimaryKey(abilityWeek);
        } else {
            // 新增逻辑
            abilityWeek.setCreateTime(new Date());
            abilityWeek.setFamilyId(familyId);
            abilityWeekMapper.insert(abilityWeek);
        }

        // 删除旧能力细分记录
        WcGrowRoomAbilityWeekCapabilityExample capabilityExample = new WcGrowRoomAbilityWeekCapabilityExample();
        capabilityExample.createCriteria()
            .andRoomIdEqualTo(roomAbility.getRoomId())
            .andStartWeekDateEqualTo(period.getStart())
            .andEndWeekDateEqualTo(period.getEnd())
            .andAppIdEqualTo(appId)
            .andDeployEnvEqualTo(deployEnv);
        abilityWeekCapabilityMapper.deleteByExample(capabilityExample);

        List<WcGrowRoomAbilityWeekCapability> capabilities = new ArrayList<>();
        for (Map.Entry<String, BigDecimal> entry : roomAbility.getCapabilityScore().entrySet()) {
            WcGrowRoomAbilityWeekCapability capability = new WcGrowRoomAbilityWeekCapability();
            capability.setRoomId(roomAbility.getRoomId());
            capability.setFamilyId(familyId);
            capability.setCapabilityCode(entry.getKey());
            capability.setAbilityValue(entry.getValue());
            capability.setRoomInFamilyRank(0);
            capability.setStartWeekDate(period.getStart());
            capability.setEndWeekDate(period.getEnd());
            capability.setAppId(appId);
            capability.setDeployEnv(deployEnv);
            capability.setCreateTime(new Date());
            capability.setModifyTime(new Date());
            capability.setLastWeekAbilityValue(roomAbility.getPrePeriodScore().get(entry.getKey()));
            capability.setCompareWeekValue(roomAbility.getPrePeriodScoreCompare().get(entry.getKey()));
            capabilities.add(capability);
        }
        abilityWeekCapabilityMapper.batchInsert(capabilities);

    }
}
