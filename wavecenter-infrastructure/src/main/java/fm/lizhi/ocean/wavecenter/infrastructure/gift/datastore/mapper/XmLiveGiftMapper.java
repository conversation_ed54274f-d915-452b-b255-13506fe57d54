package fm.lizhi.ocean.wavecenter.infrastructure.gift.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.infrastructure.gift.datastore.entity.GiftPo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/24 15:34
 */
@DataStore(namespace = "mysql_xm_lizhilivegift_r")
public interface XmLiveGiftMapper {

    @Select({
            "<script>"
            , "select *"
            , "from live_gift g"
            , "where 1=1"
            , "<if test='null != ids and ids.size > 0'>"
            , "and g.id in "
            , "<foreach collection='ids' item='id' open='(' separator=',' close=')'>"
            , "#{id}"
            , "</foreach>"
            , "</if>"
            ,"</script>"
    })
    List<GiftPo> getByIds(@Param("ids") List<Long> ids);

}
