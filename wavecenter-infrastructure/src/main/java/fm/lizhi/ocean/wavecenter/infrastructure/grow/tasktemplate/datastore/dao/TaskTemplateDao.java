package fm.lizhi.ocean.wavecenter.infrastructure.grow.tasktemplate.datastore.dao;

import java.util.*;
import java.util.stream.Collectors;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.tasktemplate.convert.TaskTemplateConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.tasktemplate.datastore.entity.WcGrowTaskTemplateCapabilityExample;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto.ConditionGroupDTO;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto.TaskTemplateConditionDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.lizhi.commons.config.core.util.ConfigUtils;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.tasktemplate.datastore.entity.WcGrowTaskTemplate;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.tasktemplate.datastore.entity.WcGrowTaskTemplateCapability;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.tasktemplate.datastore.entity.WcGrowTaskTemplateExample;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.tasktemplate.datastore.mapper.WcGrowTaskTemplateCapabilityMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.tasktemplate.datastore.mapper.WcGrowTaskTemplateMapper;
import lombok.extern.slf4j.Slf4j;

/**
 * 任务模版数据访问层
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Slf4j
@Repository
public class TaskTemplateDao {

    @Autowired
    private WcGrowTaskTemplateMapper taskTemplateMapper;

    @Autowired
    private WcGrowTaskTemplateCapabilityMapper taskTemplateCapabilityMapper;

    /**
     * 保存任务模版和能力配置（事务操作）
     *
     * @param taskTemplate       任务模版实体
     * @param capabilityEntities 能力配置实体列表
     * @param oldVersion         旧版本号
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveTaskTemplate(WcGrowTaskTemplate taskTemplate, List<WcGrowTaskTemplateCapability> capabilityEntities, Long oldVersion) {
        // 禁用旧版本数据
        if (oldVersion != null) {
            boolean delRes = this.disableOldVersionsById(taskTemplate);
            ApiAssert.isTrue(delRes, "saveTaskTemplate disable old versions failed");
        }

        // 插入任务模版记录
        int insertRows = taskTemplateMapper.insert(taskTemplate);
        ApiAssert.isTrue(insertRows > 0, "saveTaskTemplate insert taskTemplate failed");

        // 插入能力配置数据
        if (CollectionUtils.isNotEmpty(capabilityEntities)) {
            // 设置所有能力配置的模版ID
            capabilityEntities.forEach(capability -> capability.setTemplateId(taskTemplate.getId()));
            // 批量插入能力配置数据
            int capabilityInsertRows = taskTemplateCapabilityMapper.batchInsert(capabilityEntities);
            log.info("saveTaskTemplate batch insert capability result={}, size={}",
                    capabilityInsertRows, capabilityEntities.size());
            ApiAssert.isTrue(capabilityInsertRows == capabilityEntities.size(), "saveTaskTemplate batch insert capability failed");
        }

    }

    /**
     * 根据模版代码和应用ID查询最大版本号
     *
     * @param templateCode 模版代码
     * @param appId        应用ID
     * @return 最大版本号
     */
    public Long getMaxVersionByTemplateCode(String templateCode, Integer appId) {
        WcGrowTaskTemplateExample example = new WcGrowTaskTemplateExample();
        example.createCriteria()
                .andTemplateCodeEqualTo(templateCode)
                .andAppIdEqualTo(appId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());

        List<WcGrowTaskTemplate> templates = taskTemplateMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(templates)) {
            return templates.stream()
                    .map(WcGrowTaskTemplate::getVersion)
                    .max(Long::compareTo)
                    .orElse(null);
        }
        return null;
    }

    /**
     * 根据ID获取同模版代码的最大版本号
     *
     * @param templateId 模版ID
     * @return 最大版本号
     */
    public Long getMaxVersionByTemplateId(Long templateId) {
        WcGrowTaskTemplate template = getTaskTemplateById(templateId);
        if (template == null) {
            return null;
        }
        return getMaxVersionByTemplateCode(template.getTemplateCode(), template.getAppId());
    }

    /**
     * 禁用旧版本的模版数据（根据模版代码）
     *
     */
    public boolean disableOldVersionsById(WcGrowTaskTemplate taskTemplate) {
        WcGrowTaskTemplate updateEntity = new WcGrowTaskTemplate();
        updateEntity.setStatus(0);
        updateEntity.setModifyTime(new Date());

        WcGrowTaskTemplateExample updateExample = new WcGrowTaskTemplateExample();
        updateExample.createCriteria()
                .andTemplateCodeEqualTo(taskTemplate.getTemplateCode())
                .andDeployEnvEqualTo(taskTemplate.getDeployEnv())
                .andAppIdEqualTo(taskTemplate.getAppId());

        int updateRows = taskTemplateMapper.updateByExample(updateEntity, updateExample);
        return updateRows > 0;
    }

    /**
     * 根据ID查询任务模版
     *
     * @param id 模版ID
     * @return 任务模版
     */
    public WcGrowTaskTemplate getTaskTemplateById(Long id) {
        if (id == null) {
            return null;
        }

        WcGrowTaskTemplate query = new WcGrowTaskTemplate();
        query.setId(id);
        return taskTemplateMapper.selectByPrimaryKey(query);
    }

    /**
     * 分页查询任务模版表
     * @param appId 业务ID
     * @param pageNum 页码
     * @param pageSize 页面大小
     * @return 任务模版列表
     */
    public PageList<WcGrowTaskTemplate> queryTaskTemplatePage(Integer appId, int pageNum, int pageSize) {
        String deployEnv = ConfigUtils.getEnvRequired().name();
        WcGrowTaskTemplate template = new WcGrowTaskTemplate();
        template.setDeployEnv(deployEnv);
        template.setAppId(appId);
        template.setStatus(1);
        template.setDeleted(false);
        return taskTemplateMapper.selectPage(template, pageNum, pageSize);
    }

    /**
     * 批量查主表
     */
    public List<WcGrowTaskTemplate> getTemplatesByIds(List<Long> templateIds) {
        if (CollectionUtils.isEmpty(templateIds)) {
            return new ArrayList<>();
        }
        WcGrowTaskTemplateExample example = new WcGrowTaskTemplateExample();
        example.createCriteria()
                .andIdIn(templateIds);
        return taskTemplateMapper.selectByExample(example);
    }

    /**
     * 过滤掉逻辑删除的模版
     * @param templateIds
     * @return
     */
    public List<WcGrowTaskTemplate> getTemplatesByIdsFilterStatus(List<Long> templateIds) {
        if (CollectionUtils.isEmpty(templateIds)) {
            return new ArrayList<>();
        }
        WcGrowTaskTemplateExample example = new WcGrowTaskTemplateExample();
        example.createCriteria()
                .andDeletedEqualTo(false)
                .andStatusEqualTo(1)
                .andIdIn(templateIds);
        return taskTemplateMapper.selectByExample(example);
    }

    /**
     * 根据ID逻辑删除任务模版（将 deleted 字段置为 true）
     *
     * @return 删除结果
     */
    public boolean deleteGrowTaskTemplateById(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }

        WcGrowTaskTemplateExample example = new WcGrowTaskTemplateExample();
        example.createCriteria().andIdIn(ids);
        WcGrowTaskTemplate updateEntity = new WcGrowTaskTemplate();
        updateEntity.setDeleted(true);
        updateEntity.setModifyTime(new Date());
        int updateRows = taskTemplateMapper.updateByExample(updateEntity, example);
        return updateRows > 0;
    }

    public List<TaskTemplateConditionDTO> queryEnableTaskTemplateConditionList(Integer appId) {
        WcGrowTaskTemplateExample example = new WcGrowTaskTemplateExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andStatusEqualTo(1)
                .andDeletedEqualTo(false)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());

        List<WcGrowTaskTemplate> templates = taskTemplateMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(templates)) {
            return Collections.emptyList();
        }

        List<Long> templateIds = templates.stream().map(WcGrowTaskTemplate::getId).collect(Collectors.toList());
        WcGrowTaskTemplateCapabilityExample capabilityExample = new WcGrowTaskTemplateCapabilityExample();
        capabilityExample.createCriteria()
                .andAppIdEqualTo(appId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andTemplateIdIn(templateIds);
        List<WcGrowTaskTemplateCapability> capabilities = taskTemplateCapabilityMapper.selectByExample(capabilityExample);
        Map<Long, List<WcGrowTaskTemplateCapability>> capabilityMap = capabilities.stream().collect(Collectors.groupingBy(WcGrowTaskTemplateCapability::getTemplateId));

        List<TaskTemplateConditionDTO> dtos = new ArrayList<>();
        for (WcGrowTaskTemplate template : templates) {
            List<WcGrowTaskTemplateCapability> capabilityList = capabilityMap.get(template.getId());
            if (CollectionUtils.isEmpty(capabilityList)) {
                continue;
            }

            TaskTemplateConditionDTO dto = new TaskTemplateConditionDTO();
            dto.setId(template.getId());
            dto.setCapability(TaskTemplateConvert.I.capabilityEntityToDto(capabilityList.get(0)));
            dto.setConditionGroup(JsonUtil.loads(template.getConditionJson(), ConditionGroupDTO.class));
            dtos.add(dto);
        }

        return dtos;
    }

} 