package fm.lizhi.ocean.wavecenter.infrastructure.sign.remote;

import fm.lizhi.ocean.wavecenter.base.remote.IRemote;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/3/19 16:09
 */
public interface IFamilyRemote extends IRemote {

    /**
     * 分页获取家族ID列表
     * @param lastFamilyId
     * @param pageSize
     * @return
     */
    List<Long> getFamilyIdsByPage(Long lastFamilyId, Integer pageSize);

    /**
     * 查询家族公会编码
     * @param familyId
     * @return
     */
    Optional<String> getSocietyCode(Long familyId);

}
