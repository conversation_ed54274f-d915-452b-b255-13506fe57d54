package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface ActivityToolsInfoExtMapper {

    @Select("select  tool_value + 1 as tool_value  from  activity_tools_info where app_id = #{appId} and deploy_env = #{deployEnv}  order by tool_value desc limit 1")
    int getToolValueByAppId(@Param("appId") int appId, @Param("deployEnv") String deployEnv);
}
