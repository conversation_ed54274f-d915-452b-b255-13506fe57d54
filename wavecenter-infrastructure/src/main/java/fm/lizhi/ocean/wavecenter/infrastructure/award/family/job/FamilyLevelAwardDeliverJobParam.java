package fm.lizhi.ocean.wavecenter.infrastructure.award.family.job;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 公会等级奖励发放任务参数, 用于手动触发任务时指定参数
 */
@Data
public class FamilyLevelAwardDeliverJobParam {

    /**
     * 忽略时间检查
     * 不为空则忽略
     */
    private Integer ignoreDayCheck;

    /**
     * 应用id列表
     */
    private List<Integer> appIds;

    /**
     * 奖励周期开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date awardStartTime;
}
