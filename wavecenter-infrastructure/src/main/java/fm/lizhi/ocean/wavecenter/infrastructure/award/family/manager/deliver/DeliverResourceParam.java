package fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager.deliver;

import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardResourceDeliverTypeEnum;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardResourceTypeEnum;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * 发放资源参数
 */
@Data
public class DeliverResourceParam {

    /**
     * 应用id
     */
    private Integer appId;

    /**
     * 公会id
     */
    private Long familyId;

    /**
     * 公会名称
     */
    private String familyName;

    /**
     * 公会长id
     */
    private Long familyUserId;

    /**
     * 公会长名称
     */
    private String familyUserName;

    /**
     * 资源奖励类型
     */
    private FamilyAwardTypeEnum resourceAwardType;

    /**
     * 资源发放类型
     */
    private FamilyAwardResourceDeliverTypeEnum resourceDeliverType;

    /**
     * 资源类型列表, 必须是同一种奖励类型和发放类型
     */
    private List<FamilyAwardResourceTypeEnum> resourceTypes;

    /**
     * 资源数量, 可能为null或0
     */
    private Integer resourceNumber;

    /**
     * 资源有效期, 默认单位为天, 可能为null或0
     */
    private Integer resourceValidPeriod;

    /**
     * 资源id, 可能为null或0
     */
    private Long resourceId;
}
