package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.job;

import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 支付结算规则同步job
 * <AUTHOR>
 * @date 2025/4/22 11:27
 */
@Slf4j
@Component
public class XmPaySettleConfigCodeSyncJob extends AbstractPaySettleConfigCodeSyncJob implements JobHandler {

    @Override
    public void execute(JobExecuteContext jobExecuteContext) throws Exception {
        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.XIMI);
        doSync(jobExecuteContext);
    }
}
