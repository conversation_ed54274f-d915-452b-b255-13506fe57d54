package fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager.deliver;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardResourceDeliverTypeEnum;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardResourceTypeEnum;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardTypeEnum;
import fm.lizhi.ocean.wavecenter.api.grow.level.bean.FamilyLevelConfigBean;
import fm.lizhi.ocean.wavecenter.api.grow.level.request.RequestGetFamilyLevelConfigList;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.DateTimeUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert.FamilyAwardDeliverConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert.FamilyAwardDeliverExecutionConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert.FamilyAwardDeliverItemConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert.FamilyAwardDeliverRecordConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.dao.FamilyAwardDeliverRecordDao;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.dao.FamilyAwardLevelDataDao;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.dao.FamilyLevelAwardRuleDao;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilyAwardDeliverRecord;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilyAwardLevelData;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilyLevelAwardItem;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilyLevelAwardRule;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.param.CreateFamilyAwardDeliverExecutionParam;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.param.CreateFamilyAwardDeliverItemParam;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.param.CreateFamilyAwardDeliverRecordParam;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.param.ListFamilyAwardLevelDataParam;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.result.CreateDeliverRecordResult;
import fm.lizhi.ocean.wavecenter.service.grow.manager.FamilyLevelConfigManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListValuedMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class FamilyLevelAwardDeliverManager {

    @Autowired
    private FamilyAwardDeliverItemConvert familyAwardDeliverItemConvert;

    @Autowired
    private FamilyAwardLevelDataDao familyAwardLevelDataDao;

    @Autowired
    private FamilyLevelAwardRuleDao familyLevelAwardRuleDao;

    @Autowired
    private FamilyAwardDeliverRecordDao familyAwardDeliverRecordDao;

    @Autowired
    private FamilyLevelConfigManager familyLevelConfigManager;

    @Autowired
    private FamilyManager familyManager;

    @Autowired
    private UserManager userManager;

    @Autowired
    private List<FamilyAwardDeliver> familyAwardDelivers;

    @Autowired
    private DeliverResultHandler deliverResultHandler;

    /**
     * 发放奖励. 暂不支持自动重发, 如果有失败的记录, 需要人工干预.
     *
     * @param appIds         应用id列表
     * @param awardStartTime 奖励周期开始时间
     */
    public void deliverAward(List<Integer> appIds, Date awardStartTime) {
        for (Integer appId : appIds) {
            deliverAward(appId, awardStartTime);
        }
    }

    private void deliverAward(Integer appId, Date awardStartTime) {
        try {
            log.info("Delivering family level award, appId={}, awardStartTime={}", appId, awardStartTime);
            BusinessEvnEnum businessEvnEnum = BusinessEvnEnum.from(appId);
            if (businessEvnEnum == null) {
                log.info("Skip delivering family level award, invalid appId={}", appId);
                return;
            }
            if (!DateTimeUtils.isMondayStartTime(awardStartTime)) {
                log.info("Skip delivering family level award, invalid awardStartTime={}", awardStartTime);
                return;
            }
            // 设置应用上下文, 后续代码无需ResultHandler
            ContextUtils.setBusinessEvnEnum(businessEvnEnum);
            // 查询所有有效的公会等级id
            Set<Long> effectiveLevelIds = getEffectiveLevelIds(appId);
            log.info("effectiveLevelIds: {}", effectiveLevelIds);
            // 查询奖励规则和条目
            Map<Long, WcFamilyLevelAwardRule> ruleMap = familyLevelAwardRuleDao.getRulesByAppIdAsLevelMap(appId);
            List<Long> ruleIds = ruleMap.values().stream().map(WcFamilyLevelAwardRule::getId).collect(Collectors.toList());
            ListValuedMap<Long, WcFamilyLevelAwardItem> rulesItemsMap = familyLevelAwardRuleDao.getRulesItemsMap(ruleIds);
            log.info("ruleMap={}, rulesItemsMap={}", ruleMap, rulesItemsMap);
            // 分页查询公会等级数据
            Date maxCreateTime = new Date();
            Date deliverTime = new Date();
            int pageNumber = 1;
            int pageSize = 1000;
            boolean hasNextPage = true;
            while (hasNextPage) {
                ListFamilyAwardLevelDataParam param = new ListFamilyAwardLevelDataParam(appId, awardStartTime, maxCreateTime, pageNumber, pageSize);
                PageList<WcFamilyAwardLevelData> pageList = familyAwardLevelDataDao.listData(param);
                for (WcFamilyAwardLevelData levelData : pageList) {
                    Long familyId = levelData.getFamilyId();
                    Long levelId = levelData.getLevelId();
                    // 根据每条公会等级数据执行奖励发放
                    try {
                        doDeliver(appId, awardStartTime, deliverTime, familyId, levelId, ruleMap, rulesItemsMap, effectiveLevelIds);
                    } catch (RuntimeException e) {
                        log.error("Deliver family level award error, appId={}, awardStartTime={}, familyId={}, levelId={}", appId, awardStartTime, familyId, levelId, e);
                    }
                }
                if (pageList.size() < pageSize) {
                    hasNextPage = false;
                } else {
                    pageNumber++;
                }
            }
            log.info("Delivered family level award, appId={}, awardStartTime={}", appId, awardStartTime);
        } finally {
            // 清空业务上下文
            ContextUtils.clearContext();
        }
    }

    private void doDeliver(Integer appId, Date awardStartTime, Date deliverTime, Long familyId, Long levelId, Map<Long, WcFamilyLevelAwardRule> ruleMap, ListValuedMap<Long, WcFamilyLevelAwardItem> rulesItemsMap, Set<Long> effectiveLevelIds) {
        if (!effectiveLevelIds.contains(levelId)) {
            log.info("Skip delivering family level award, level not valid, appId={}, familyId={}, levelId={}", appId, familyId, levelId);
            return;
        }
        WcFamilyLevelAwardRule rule = ruleMap.get(levelId);
        if (rule == null) {
            log.info("Skip delivering family level award, rule not found, appId={}, familyId={}, levelId={}", appId, familyId, levelId);
            return;
        }
        List<WcFamilyLevelAwardItem> ruleItems = rulesItemsMap.get(rule.getId());
        if (CollectionUtils.isEmpty(ruleItems)) {
            log.info("Skip delivering family level award, items is empty, appId={}, familyId={}, levelId={}", appId, familyId, levelId);
            return;
        }
        WcFamilyAwardDeliverRecord oldDeliverRecord = familyAwardDeliverRecordDao.getDeliverRecord(appId, familyId, awardStartTime);
        if (oldDeliverRecord != null) {
            log.info("Skip delivering family level award, record is present, appId={}, familyId={}, awardStartTime={}", appId, familyId, awardStartTime);
            return;
        }
        FamilyBean familyBean = getFamily(appId, familyId);
        if (familyBean == null) {
            log.warn("Skip delivering family level award, family not found, appId={}, familyId={}", appId, familyId);
            return;
        }
        String familyName = familyBean.getFamilyName();
        Long familyUserId = familyBean.getUserId();
        String familyUserName = getUserNameOrEmpty(appId, familyUserId);
        ArrayList<CreateFamilyAwardDeliverExecutionParam> deliverExecutions = new ArrayList<>();
        ArrayList<DeliverResourceParam> deliverResourceParams = new ArrayList<>();
        for (WcFamilyLevelAwardItem ruleItem : ruleItems) {
            FamilyAwardResourceTypeEnum resourceType = FamilyAwardResourceTypeEnum.fromValue(ruleItem.getResourceType());
            if (resourceType == null) {
                log.warn("Skip delivering family level award item, invalid resourceType, appId={}, familyId={}, levelId={}, itemId={}, resourceType={}", appId, familyId, levelId, ruleItem.getId(), ruleItem.getResourceType());
                continue;
            }
            FamilyAwardDeliver familyAwardDeliver = getFamilyAwardDeliver(resourceType.getAwardType(), resourceType.getDeliverType());
            Validate.notNull(familyAwardDeliver);
            // 构造发放资源参数
            DeliverResourceParam deliverResourceParam = FamilyAwardDeliverConvert.I.toDeliverResourceParam(ruleItem, familyId, familyName, familyUserId, familyUserName);
            deliverResourceParams.add(deliverResourceParam);
            // 获取资源信息, 构造发放条目
            Result<GetResourceInfoResult> resourceInfoResult = familyAwardDeliver.getResourceInfo(deliverResourceParam);
            String resourceName = RpcResult.isSuccess(resourceInfoResult) ? resourceInfoResult.target().getResourceName() : StringUtils.EMPTY;
            String resourceImage = RpcResult.isSuccess(resourceInfoResult) ? resourceInfoResult.target().getResourceImage() : StringUtils.EMPTY;
            CreateFamilyAwardDeliverItemParam deliverItem = familyAwardDeliverItemConvert.toCreateParam(ruleItem, resourceName, resourceImage);
            // 公会等级奖励规则目前必定是每个资源类型只有一个奖励条目, 因此一个条目对应一次执行
            CreateFamilyAwardDeliverExecutionParam deliverExecution = FamilyAwardDeliverExecutionConvert.I.toCreateParam(deliverItem);
            deliverExecutions.add(deliverExecution);
        }
        if (CollectionUtils.isEmpty(deliverExecutions)) {
            log.warn("Skip delivering family level award, no deliver executions, appId={}, familyId={}, levelId={}", appId, familyId, levelId);
            return;
        }
        int executionSize = deliverExecutions.size();
        // 发放前先初始化发放记录
        CreateFamilyAwardDeliverRecordParam createRecordParam = FamilyAwardDeliverRecordConvert.I.toCreateParam(appId, familyId, familyName, familyUserId, familyUserName, awardStartTime, deliverTime, deliverExecutions);
        CreateDeliverRecordResult createRecordResult = familyAwardDeliverRecordDao.createDeliverRecord(createRecordParam);
        // 依次发放资源
        long recordId = createRecordResult.getRecordId();
        DeliverExecutionCounter counter = new DeliverExecutionCounter(executionSize);
        for (int i = 0; i < executionSize; i++) {
            Long executionId = createRecordResult.getExecutionIds().get(i);
            DeliverResourceParam deliverResourceParam = deliverResourceParams.get(i);
            FamilyAwardDeliver familyAwardDeliver = getFamilyAwardDeliver(deliverResourceParam.getResourceAwardType(), deliverResourceParam.getResourceDeliverType());
            Validate.notNull(familyAwardDeliver);
            // 执行发放动作
            Result<Void> deliverResult = familyAwardDeliver.deliverResource(deliverResourceParam);
            // 执行发放结果处理入库
            deliverResultHandler.handleDeliverResult(recordId, executionId, counter, deliverResourceParam, deliverResult);
        }
    }

    private Set<Long> getEffectiveLevelIds(int appId) {
        RequestGetFamilyLevelConfigList request = new RequestGetFamilyLevelConfigList();
        request.setAppId(appId);
        List<FamilyLevelConfigBean> levels = familyLevelConfigManager.getList(request);
        HashSet<Long> levelIds = new HashSet<>();
        for (FamilyLevelConfigBean level : CollectionUtils.emptyIfNull(levels)) {
            if (!level.getDeleted()) {
                levelIds.add(level.getId());
            }
        }
        return levelIds;
    }

    private FamilyBean getFamily(int appId, long familyId) {
        try {
            Optional<FamilyBean> familyBeanOp = familyManager.getFamily(appId, familyId);
            return familyBeanOp.orElse(null);
        } catch (RuntimeException e) {
            log.error("Failed to get family by cache, appId={}, familyId={}", appId, familyId, e);
            return null;
        }
    }

    private String getUserNameOrEmpty(int appId, long userId) {
        try {
            List<SimpleUserDto> simpleUsers = userManager.getSimpleUserByIds(Collections.singletonList(userId));
            if (CollectionUtils.isEmpty(simpleUsers) || simpleUsers.get(0) == null) {
                return StringUtils.EMPTY;
            }
            return StringUtils.defaultString(simpleUsers.get(0).getName());
        } catch (RuntimeException e) {
            log.warn("Failed to get user name by cache, appId={}, userId={}", appId, userId, e);
            return StringUtils.EMPTY;
        }
    }

    private FamilyAwardDeliver getFamilyAwardDeliver(FamilyAwardTypeEnum awardType, FamilyAwardResourceDeliverTypeEnum deliverType) {
        for (FamilyAwardDeliver familyAwardDeliver : familyAwardDelivers) {
            if (familyAwardDeliver.supports(awardType, deliverType)) {
                return familyAwardDeliver;
            }
        }
        return null;
    }
}
