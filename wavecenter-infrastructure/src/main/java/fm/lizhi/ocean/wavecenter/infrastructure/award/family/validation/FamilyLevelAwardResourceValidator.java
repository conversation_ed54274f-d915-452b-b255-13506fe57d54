package fm.lizhi.ocean.wavecenter.infrastructure.award.family.validation;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.SaveFamilyLevelAwardItemBean;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardResourceTypeEnum;
import fm.lizhi.ocean.wavecenter.common.validation.ValidateResult;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 家族等级奖励资源校验器
 */
@Component
public class FamilyLevelAwardResourceValidator {

    /**
     * key1=appId或0(0表示通用)  key2=resourceType
     */
    private static final Table<Integer, Integer, InternalValidator> VALIDATORS;

    private static final int commonAppId = 0;

    static {
        Table<Integer, Integer, InternalValidator> validatorMap = HashBasedTable.create();
        validatorMap.put(commonAppId, FamilyAwardResourceTypeEnum.RECOMMEND_CARD.getValue(), new RecommendCardValidator());
        validatorMap.put(commonAppId, FamilyAwardResourceTypeEnum.VEHICLE.getValue(), new VehicleValidator());
        validatorMap.put(commonAppId, FamilyAwardResourceTypeEnum.MEDAL.getValue(), new MedalValidator());
        validatorMap.put(commonAppId, FamilyAwardResourceTypeEnum.SHORT_NUMBER.getValue(), new ShortNumberValidator());
        validatorMap.put(BusinessEvnEnum.HEI_YE.getAppId(), FamilyAwardResourceTypeEnum.VEHICLE.getValue(), new VehicleNotPeriodValidator());
        VALIDATORS = validatorMap;
    }

    public InternalValidator getValidator(Integer appId, Integer resourceType) {
        InternalValidator appValidator = VALIDATORS.get(appId, resourceType);
        if (appValidator != null) {
            return appValidator;
        }
        return VALIDATORS.get(commonAppId, resourceType);
    }

    /**
     * 校验家族等级奖励资源列表. 只包含资源类型相关参数的基础校验, 不包含资源id的校验和数据库有效性的校验
     *
     * @param beans 家族等级奖励资源列表
     * @return 校验结果
     */
    public ValidateResult validate(List<SaveFamilyLevelAwardItemBean> beans) {
        if (CollectionUtils.isEmpty(beans)) {
            return ValidateResult.valid();
        }
        for (SaveFamilyLevelAwardItemBean bean : beans) {
            ValidateResult validateResult = validate(bean);
            if (validateResult.isInvalid()) {
                return validateResult;
            }
        }
        return ValidateResult.valid();
    }

    private ValidateResult validate(SaveFamilyLevelAwardItemBean bean) {
        if (bean == null) {
            return ValidateResult.generalError("家族等级奖励资源不能为空");
        }
        Integer resourceType = bean.getResourceType();
        if (resourceType == null) {
            return ValidateResult.generalError("家族等级奖励资源类型不能为空");
        }
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        InternalValidator internalValidator = getValidator(appId, resourceType);
        if (internalValidator == null) {
            return ValidateResult.generalError("家族等级奖励资源类型不支持校验, resourceType=" + resourceType);
        }
        return internalValidator.validate(bean);
    }

    private interface InternalValidator {

        ValidateResult validate(SaveFamilyLevelAwardItemBean bean);
    }

    private static class RecommendCardValidator implements InternalValidator {

        @Override
        public ValidateResult validate(SaveFamilyLevelAwardItemBean bean) {
            Integer resourceNumber = bean.getResourceNumber();
            if (resourceNumber == null || resourceNumber <= 0) {
                return ValidateResult.generalError("推荐卡数量必须大于0");
            }
            Integer resourceValidPeriod = bean.getResourceValidPeriod();
            if (resourceValidPeriod == null || resourceValidPeriod <= 0) {
                return ValidateResult.generalError("推荐卡有效期必须大于0");
            }
            return ValidateResult.valid();
        }
    }

    private static class VehicleValidator implements InternalValidator {

        @Override
        public ValidateResult validate(SaveFamilyLevelAwardItemBean bean) {
            Long resourceId = bean.getResourceId();
            if (resourceId == null || resourceId <= 0) {
                return ValidateResult.generalError("座驾id不能为空");
            }
            Integer resourceValidPeriod = bean.getResourceValidPeriod();
            if (resourceValidPeriod == null || resourceValidPeriod <= 0) {
                return ValidateResult.generalError("座驾有效期必须大于0");
            }
            return ValidateResult.valid();
        }
    }

    /**
     * 座驾不存在有效期的校验器
     */
    private static class VehicleNotPeriodValidator implements InternalValidator {

        @Override
        public ValidateResult validate(SaveFamilyLevelAwardItemBean bean) {
            Long resourceId = bean.getResourceId();
            if (resourceId == null || resourceId <= 0) {
                return ValidateResult.generalError("座驾id不能为空");
            }
            return ValidateResult.valid();
        }
    }

    private static class MedalValidator implements InternalValidator {

        @Override
        public ValidateResult validate(SaveFamilyLevelAwardItemBean bean) {
            Long resourceId = bean.getResourceId();
            if (resourceId == null || resourceId <= 0) {
                return ValidateResult.generalError("勋章id不能为空");
            }
            Integer resourceValidPeriod = bean.getResourceValidPeriod();
            if (resourceValidPeriod == null || resourceValidPeriod <= 0) {
                return ValidateResult.generalError("勋章有效期必须大于0");
            }
            return ValidateResult.valid();
        }
    }

    private static class ShortNumberValidator implements InternalValidator {

        @Override
        public ValidateResult validate(SaveFamilyLevelAwardItemBean bean) {
            Long resourceId = bean.getResourceId();
            if (resourceId == null || resourceId <= 0) {
                return ValidateResult.generalError("短号id不能为空");
            }
            return ValidateResult.valid();
        }
    }
}
