package fm.lizhi.ocean.wavecenter.infrastructure.sign.remote;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.bean.FamilyAndNjContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.RoomSignInfoBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.SignPlayerInfoBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestAdminApplyCancelFamily;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestFamilyInviteAdmin;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestUserApplyAdmin;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseAdminApplyCancelFamily;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseFamilyInviteAdmin;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseUserApplyAdmin;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.PlayerSignBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.RoomSignBean;
import fm.lizhi.ocean.wavecenter.common.dto.PageDto;
import fm.lizhi.ocean.wavecenter.base.remote.IRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.request.RequestFamilyAdminCancel;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.request.RequestFamilyAndNjCancelApply;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.res.PlayerIncomeStatRes;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto.ContractInfoDto;
import fm.lizhi.ocean.wavecenter.service.sign.dto.*;
import fm.lizhi.ocean.wavecenter.service.user.dto.PlayerSignInfoDto;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/4/10 14:45
 */
public interface IContractRemote extends IRemote {

    /**
     * 查询公会下所有签约厅
     *
     * @param familyId
     * @param pageNo
     * @param pageSize
     * @return
     */
    PageBean<RoomSignBean> getAllSingGuildRooms(long familyId, int pageNo, int pageSize);

    /**
     * 查询指定时间段内签约的厅ID
     * @return
     */
    PageBean<Long> getSingGuildRoomsByDate(long familyId, Date startDate, Date endDate, int pageNo, int pageSize);

    /**
     * 查询公会下所有签约厅
     *
     * @param familyId
     * @return
     */
    List<RoomSignBean> getAllSingGuildRoomsList(long familyId);


    /**
     * 查询公会下所有厅
     * 只返回厅ID和签约状态
     *
     * @param familyId
     * @param pageNo
     * @param pageSize
     * @return
     */
    PageBean<RoomSignBean> getAllGuildRooms(long familyId, List<Long> roomIds, int pageNo, int pageSize);

    /**
     * 查询厅下所有主播
     * 只返回主播ID和签约状态
     *
     * @param roomId
     * @param pageNo
     * @param pageSize
     * @return
     */
    PageBean<PlayerSignBean> getAllRoomPlayers(long roomId, int pageNo, int pageSize);

    /**
     * 查询厅下已签约的主播ID列表
     * @param roomId
     * @return
     */
    List<Long> getAllSignRoomPlayerIds(long roomId);

    /**
     * 查询厅下的所有签约主播
     * @param roomIds
     * @return
     */
    Set<Long> getAllSignRoomPlayerIds(List<Long> roomIds);

    /**
     * 查询指定时间范围内签约的主播
     * @param njIds
     * @param startDate
     * @param endDate
     * @return
     */
    List<Long> selectAllSignPlayerByDate(List<Long> njIds, Date startDate, Date endDate);


    PageDto<PlayerSignBean> getAllGuildPlayer(long familyId, List<Long> scopeRoomIds, int status, int pageNo, int pageSize);

    /**
     * 查询厅主最新签约信息
     *
     * @param njId
     * @return
     */
    Optional<RoomSignBean> getRoomSign(long familyId, long njId);

    /**
     * 查询陪玩签约信息
     *
     * @param playerId
     * @return
     */
    Optional<PlayerSignBean> getPlayerSign(Long familyId, Long roomId, long playerId);

    /**
     * 厅管侧签约陪玩收入数据
     *
     * @param userId
     * @param startDate
     * @param endDate
     * @return
     */
    List<PlayerIncomeStatRes> queryAdminPlayerIncomeList(long userId, Date startDate, Date endDate);

    /**
     * 获取陪玩最新签约信息
     *
     * @param roomId
     * @param userId
     * @return
     */
    Optional<PlayerSignInfoDto> getLatestSignRecord(long roomId, long userId);

    /**
     * 获取陪玩最新签约信息
     *
     * @param userId
     * @return
     */
    Optional<PlayerSignInfoDto> getLatestSignRecord(List<Long> roomIds, long userId);


    Optional<Long> getRoomBestFamily(long roomId);

    Optional<Long> getPlayerBestFamily(long playerId);

    Optional<Long> getUserBestNj(long userId);

    Optional<FamilyBean> getFamily(long familyId);

    List<Long> getFamilyAllNjId(Long familyId);

    Long getPlayerLastRoom(long familyId, long playerId);

    /**
     * 获取官方厅ID
     * @return
     */
    List<Long> getOfficialRoomIds();

    Optional<Long> getRoomBestFamilyByCache(long roomId);


    /**
     * 查询厅指定时间签约的公会
     * @param roomId
     * @return
     */
    Optional<Long> getRoomSignFamilyInDate(long roomId, Date date);

    /**
     * 查询公会签约厅列表
     * 场景：公会管理
     * @return
     */
    PageBean<RoomSignInfoBean> guildSignRoomPageList(SMSignRoomPageListReqDto reqDto);

    /**
     * 查询签约主播列表
     * @param reqDto
     * @return
     */
    PageBean<SignPlayerInfoBean> signPlayerPageList(SMSignPlayerPageListReqDto reqDto);

    /**
     * 查询用户最近一条解约流程
     * @param userId
     * @return
     */
    Optional<FamilyAndNjContractBean> queryLastCancel(Long userId);

    /**
     * 查询最近一条签约记录
     * @param userId
     * @return
     */
    Optional<FamilyAndNjContractBean> queryLastSign(Long userId);

    /**
     * 用户是否签约为管理员
     * @param userId
     * @return
     */
    boolean isUserSignAsRoom(Long userId);

    /**
     * 查询合同
     * @return
     */
    PageBean<FamilyAndNjContractBean> queryContract(RequestFamilyAndNjContractDTO request);

    /**
     * 查询解约申请
     * @param request
     * @return
     */
    PageBean<FamilyAndNjContractBean> queryCancelApply(RequestFamilyAndNjCancelApply request);

    /**
     * 获取所有签约的厅ID
     * @param familyId
     * @return
     */
    List<Long> queryAllSignNjId(Long familyId);

    /**
     * 统计已签约厅数
     * @param familyId
     * @return
     */
    Integer countSignRoomNum(long familyId);

    /**
     * 用户申请成为厅主
     * @param request
     * @return
     */
    ResponseUserApplyAdmin userApplyAdmin(RequestUserApplyAdmin request);

    /**
     * 查询身份证加入的家族
     * @param identityNo
     * @return
     */
    List<FamilyAndNjContractBean> queryIdentityNoJoinFamily(String identityNo);

    /**
     * 家族长邀请用户成为管理员
     * @param request
     * @return
     */
    ResponseFamilyInviteAdmin familyInviteAdmin(RequestFamilyInviteAdmin request);

    /**
     * 查询管理员加入家族的记录
     * @param request
     * @return
     */
    List<FamilyNjJoinRecordDTO> queryFamilyNjJoinRecord(QueryFamilyNjJoinRecordDTO request);

    /**
     * 生成签署合同ID
     * @param contractId
     * @return
     */
    Optional<String> doSignGenSignId(Long contractId);

    /**
     * 家族申请解约
     * @param request
     * @return
     */
    Optional<String> familyAdminCancel(RequestFamilyAdminCancel request);


    /**
     * 管理员申请解约家族
     * @param request
     * @return
     */
    ResponseAdminApplyCancelFamily adminApplyCancelFamily(RequestAdminApplyCancelFamily request);

    /**
     * 管理员家族长邀请确认
     * @param curUserId
     * @param targetUserId
     * @param operateRole
     * @return
     */
    Pair<Integer, String> familyAdminInviteConfirm(Long curUserId, Long targetUserId, RoleEnum operateRole);

    /**
     * 解约邀请确认
     * @param familySignId
     * @param operateRole
     * @param curUserId
     * @return
     */
    Pair<Integer, String> familyAdminInviteCancelConfirm(Long familySignId, RoleEnum operateRole, Long curUserId);

    /**
     * 查询签署记录
     * @param param
     * @return
     */
    PageBean<FamilyNjSignRecordDTO> querySignRecord(QueryFamilyNjSignRecordDTO param);

    /**
     * 家族长和厅管理确认签约
     * @param familyNjSignId
     * @param curUserId
     * @param type
     * @param operateRole
     * @return
     */
    Pair<Integer, String> doFamilyNjConfirmSign(Long familyNjSignId, Long curUserId, ContractTypeEnum type, RoleEnum operateRole);

    /**
     * 查询解约记录
     * @param contractIds 解约合同id
     * @return
     */
    List<FamilyNjCancelRecordDTO> queryCancelRecordByContractId(List<Long> contractIds);

    /**
     * 按时间顺序获取签约信息
     * @param familyId
     * @param startDate
     * @param endDate
     * @return
     */
    List<ContractInfoDto> queryContractInfoByTime(long familyId, Date startDate, Date endDate);

    /**
     * 查询公会签约过的历史厅
     * @param familyId
     * @param minNjId
     * @param pageSize
     * @return
     */
    List<Long> getHistoryNjIds(Long familyId, Long minNjId, Integer pageSize);


}
