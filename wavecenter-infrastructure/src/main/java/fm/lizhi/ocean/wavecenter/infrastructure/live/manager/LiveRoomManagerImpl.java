package fm.lizhi.ocean.wavecenter.infrastructure.live.manager;

import fm.lizhi.ocean.wavecenter.api.live.constants.RoomCategoryEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.live.remote.ILiveRoomRemote;
import fm.lizhi.ocean.wavecenter.service.live.manager.LiveRoomManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/5/9 15:44
 */
@Component
public class LiveRoomManagerImpl implements LiveRoomManager {

    @Autowired
    private ILiveRoomRemote iLiveRoomRemote;

    @Override
    public Optional<RoomCategoryEnum> getUserRoomCategory(Long userId) {
        return iLiveRoomRemote.getUserRoomCategory(userId);
    }

}
