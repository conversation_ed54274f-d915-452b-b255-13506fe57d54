package fm.lizhi.ocean.wavecenter.infrastructure.common.constants;

import fm.lizhi.ocean.wavecenter.base.constants.IRedisKey;

public enum CommonRedisKey implements IRedisKey {

    /**
     * 单节点运行锁
     * key=WAVECENTER_COMMON_SINGLE_NODE_RUN_LOCK_{caseName}
     * caseName=场景名称,自定义使用的场景后缀
     */
    SINGLE_NODE_RUN_LOCK,
    /**
     * 单节点运行业务层面锁
     * key=WAVECENTER_COMMON_SINGLE_NODE_RUN_LOCK_{caseName}_{appId}
     * caseName=场景名称,自定义使用的场景后缀
     */
    SINGLE_NODE_RUN_BIZ_LOCK
    ;

    @Override
    public String getPrefix() {
        return "WAVECENTER_COMMON";
    }

    @Override
    public String getName() {
        return this.name();
    }
}
