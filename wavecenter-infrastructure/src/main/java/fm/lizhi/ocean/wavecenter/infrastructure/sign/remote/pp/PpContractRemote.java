package fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.pp;

import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.bean.FamilyAndNjContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.RoomSignInfoBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.SignPlayerInfoBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.AuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestAdminApplyCancelFamily;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestFamilyInviteAdmin;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestUserApplyAdmin;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseAdminApplyCancelFamily;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseFamilyInviteAdmin;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseUserApplyAdmin;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.PlayerSignBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.RoomSignBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.api.user.constants.SingStatusEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.dto.PageDto;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.convert.SignInfraConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity.*;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.mapper.PpContractMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.mapper.PpFamilyNjMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.mapper.PpPlayerSignMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.mapper.PpUnwindApplyMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.IContractRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.request.RequestFamilyAdminCancel;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.request.RequestFamilyAndNjCancelApply;
import fm.lizhi.ocean.wavecenter.infrastructure.user.convert.UserConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.res.PlayerIncomeStatRes;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto.ContractInfoDto;
import fm.lizhi.ocean.wavecenter.service.sign.dto.*;
import fm.lizhi.ocean.wavecenter.service.user.dto.PlayerSignInfoDto;
import fm.lizhi.trade.contract.constant.ContractType;
import fm.pp.family.api.*;
import fm.pp.family.bean.ContractInfo;
import fm.pp.family.bean.InviteAnchorInfo;
import fm.pp.family.bean.Signinfo;
import fm.pp.family.bean.UnwindContractInfo;
import fm.pp.family.constants.FamilyConstant;
import fm.pp.family.protocol.*;
import org.apache.commons.lang3.tuple.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/10 14:46
 */
@Slf4j
@Component
public class PpContractRemote implements IContractRemote {

    @Autowired
    private ContractService contractService;
    @Autowired
    private PlayerSignService playerSignService;
    @Autowired
    private PpContractMapper contractMapper;
    @Autowired
    private PpPlayerSignMapper playerSignMapper;
    @Autowired
    private UnwindContractService unwindContractService;
    @Autowired
    private FamilyNjService familyNjService;
    @Autowired
    private PpUnwindApplyMapper unwindApplyMapper;
    @Autowired
    private PpFamilyNjMapper familyNjMapper;
    @Autowired
    private SignService signService;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }

    private final LoadingCache<Long, Optional<Long>> FAMILY_ID_CACHE = CacheBuilder.newBuilder()
            .maximumSize(2000)
            .expireAfterAccess(10, TimeUnit.MINUTES)
            .recordStats()
            .build(new CacheLoader<Long, Optional<Long>>() {
                @Override
                public Optional<Long> load(Long roomId) throws Exception {
                    return getRoomBestFamily(roomId);
                }
            });

    @Override
    public PageBean<RoomSignBean> getAllSingGuildRooms(long familyId, int pageNo, int pageSize) {
        ContractServiceProto.contractListReq req = ContractServiceProto.contractListReq.newBuilder()
                .setFamilyId(familyId)
                .addAllContractType(Arrays.asList(
                        ContractType.SIGN.getCode(),
                        ContractType.RENEW.getCode(),
                        ContractType.SUBJECT_CHANGE.getCode()
                ))
                .addStatus(FamilyConstant.ContractStatus.SIGN_SUCCEED.getCode())
                .setPageNum(pageNo)
                .setPageSize(pageSize)
                .build();
        Result<ContractServiceProto.ResponseContractListV2> result = contractService.contractListV2(req);
        if (RpcResult.isFail(result)) {
            log.warn("pp,getAllSingGuildRooms,familyId={},pageNo={},pageSize={},rCode={}", familyId, pageNo, pageSize, result.rCode());
            return PageBean.empty();
        }
        String contractsJsonArrayStr = result.target().getContracts();
        List<ContractInfo> contractInfos = JsonUtil.loadsArray(contractsJsonArrayStr, ContractInfo.class);
        if (CollectionUtils.isEmpty(contractInfos)) {
            return PageBean.empty();
        }
        List<RoomSignBean> roomSignDtos = UserConvert.I.contractPbs2RoomSignBeans(contractInfos);
        return PageBean.of(result.target().getTotal(), roomSignDtos);
    }

    @Override
    public PageBean<Long> getSingGuildRoomsByDate(long familyId, Date startDate, Date endDate, int pageNo, int pageSize) {
        PageList<Long> pageList = contractMapper.getSingGuildRoomsByDate(familyId,
                DateUtil.formatDateNormal(startDate),
                DateUtil.formatDateNormal(endDate),
                pageNo,
                pageSize);
        if (CollectionUtils.isEmpty(pageList)) {
            LogContext.addResLog("pageList is empty");
            return PageBean.empty();
        }
        return PageBean.of(pageList.getTotal(), pageList);
    }

    @Override
    public List<RoomSignBean> getAllSingGuildRoomsList(long familyId) {
        PpContractExample example = new PpContractExample();
        example.createCriteria()
                .andFamilyIdEqualTo(familyId)
                .andTypeIn(Lists.newArrayList(ContractType.SIGN.getCode(), ContractType.RENEW.getCode(), ContractType.SUBJECT_CHANGE.getCode()))
                .andStatusEqualTo(FamilyConstant.ContractStatus.SIGN_SUCCEED.getCode())
        ;
        List<PpContract> poList = contractMapper.selectByExample(example);
        return UserConvert.I.ppContractPos2RoomSignBeans(poList);
    }

    public List<RoomSignBean> getAllSingGuildRoomsList(long familyId, List<Long> roomIds) {
        PpContractExample example = new PpContractExample();
        PpContractExample.Criteria criteria = example.createCriteria();
        criteria.andFamilyIdEqualTo(familyId)
                .andTypeIn(Lists.newArrayList(ContractType.SIGN.getCode(), ContractType.RENEW.getCode(), ContractType.SUBJECT_CHANGE.getCode()))
                .andStatusEqualTo(FamilyConstant.ContractStatus.SIGN_SUCCEED.getCode())
        ;

        if (CollectionUtils.isNotEmpty(roomIds)) {
            criteria.andNjIdIn(roomIds);
        }

        List<PpContract> poList = contractMapper.selectByExample(example);
        return UserConvert.I.ppContractPos2RoomSignBeans(poList);
    }

    @Override
    public PageBean<RoomSignBean> getAllGuildRooms(long familyId, List<Long> roomIds, int pageNo, int pageSize) {
        PageList<PpContract> pageList = contractMapper.pageFamilyNjBest(familyId, roomIds, pageNo, pageSize);
        if (CollectionUtils.isEmpty(pageList)) {
            return PageBean.empty();
        }

        List<RoomSignBean> roomSignDtos = UserConvert.I.ppContractPos2RoomSignBeans(pageList);
        return PageBean.of(pageList.getTotal(), roomSignDtos);
    }

    @Override
    public PageBean<PlayerSignBean> getAllRoomPlayers(long roomId, int pageNo, int pageSize) {
        PageList<PpPlayerSign> pageList = playerSignMapper.pagePlayerSignBest(Collections.singletonList(roomId), pageNo, pageSize);
        if (CollectionUtils.isEmpty(pageList)) {
            return PageBean.empty();
        }
        List<PlayerSignBean> beanList = UserConvert.I.ppPlayerSignPos2PlayerSignBeans(pageList);
        return PageBean.of(pageList.getTotal(), beanList);
    }

    @Override
    public List<Long> getAllSignRoomPlayerIds(long roomId) {
        return new ArrayList<>(playerSignMapper.selectAllSignPlayer(Collections.singletonList(roomId)));
    }

    @Override
    public Set<Long> getAllSignRoomPlayerIds(List<Long> roomIds) {
        if (CollectionUtils.isEmpty(roomIds)) {
            LogContext.addResLog("roomIds is empty");
            return Collections.emptySet();
        }
        return playerSignMapper.selectAllSignPlayer(roomIds);
    }

    @Override
    public PageDto<PlayerSignBean> getAllGuildPlayer(long familyId, List<Long> scopeRoomIds, int status, int pageNo, int pageSize) {
        List<RoomSignBean> roomsList = getAllSingGuildRoomsList(familyId, scopeRoomIds);
        if (CollectionUtils.isEmpty(roomsList)) {
            return PageDto.empty();
        }
        List<Long> roomIds = roomsList.stream().map(UserBean::getId).collect(Collectors.toList());
        PageList<PpPlayerSign> pageList = playerSignMapper.pagePlayerSignBest(roomIds, pageNo, pageSize);
        //过滤掉重复的陪玩：不分陪玩在同一个公会下，存在转移签约厅的情况，在公会视角下，只关心当前签约的状态
        List<Long> removeIds = new ArrayList<>();
        Map<Long, List<PpPlayerSign>> beanMap = pageList.stream().collect(Collectors.groupingBy(PpPlayerSign::getUserId));
        for (Map.Entry<Long, List<PpPlayerSign>> longListEntry : beanMap.entrySet()) {
            if (longListEntry.getValue().size() > 1) {
                longListEntry.getValue().sort(Comparator.comparing(PpPlayerSign::getCreateTime).reversed());
                for (int i = 0; i < longListEntry.getValue().size(); i++) {
                    if (i != 0) {
                        removeIds.add(longListEntry.getValue().get(i).getId());
                    }
                }
            }
        }

        List<PpPlayerSign> onlyList = pageList.stream().filter(v -> {
            return !removeIds.contains(v.getId());
        }).collect(Collectors.toList());

        List<PlayerSignBean> resultList = UserConvert.I.ppPlayerSignPos2PlayerSignBeans(onlyList);
        return PageDto.of(pageList.getTotal(), resultList);
    }

    @Override
    public Optional<RoomSignBean> getRoomSign(long familyId, long njId) {
        RoomSignBean bean = new RoomSignBean();
        bean.setId(njId);

        PpContractExample example = new PpContractExample();
        example.createCriteria()
                .andNjIdEqualTo(njId)
                .andFamilyIdEqualTo(familyId)
                .andTypeIn(Lists.newArrayList("SIGN", "RENEW", "SUBJECT_CHANGE"))
                .andStatusEqualTo("SIGN_SUCCEED");

        List<PpContract> ppContracts = contractMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(ppContracts)) {
            bean.setSignStatus(SingStatusEnum.STOP.getValue());
        } else {
            bean.setSignStatus(SingStatusEnum.SING.getValue());
        }

        return Optional.of(bean);
    }

    @Override
    public Optional<PlayerSignBean> getPlayerSign(Long familyId, Long roomId, long playerId) {
        PlayerSignBean playerSignBean = new PlayerSignBean();
        playerSignBean.setId(playerId);

        PpPlayerSignExample example = new PpPlayerSignExample();
        PpPlayerSignExample.Criteria criteria = example.createCriteria();
        criteria.andUserIdEqualTo(playerId)
                .andTypeEqualTo("SIGN")
                .andStatusEqualTo("SIGN_SUCCEED")
        ;

        List<PpPlayerSign> list = new ArrayList<>();
        if (roomId != null) {
            criteria.andNjIdEqualTo(roomId);
            list = playerSignMapper.selectByExample(example);
        } else if (familyId != null) {
            //查询公会下所有厅
            List<Long> familyAllNjId = getFamilyAllNjId(familyId);
            if (CollectionUtils.isNotEmpty(familyAllNjId)) {
                criteria.andNjIdIn(familyAllNjId);
                list = playerSignMapper.selectByExample(example);
            }
        }

        if (CollectionUtils.isEmpty(list)) {
            playerSignBean.setSignStatus(SingStatusEnum.STOP.getValue());
        } else {
            playerSignBean.setSignStatus(SingStatusEnum.SING.getValue());
        }
        return Optional.of(playerSignBean);
    }

    /**
     * 查询公会下所有厅
     * @param familyId
     * @return
     */
    public List<Long> getFamilyAllNjId(Long familyId){
        if (familyId == null) {
            return Collections.emptyList();
        }
        PpContract param = new PpContract();
        param.setFamilyId(familyId);
        List<PpContract> list = contractMapper.selectMany(param);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(PpContract::getNjId).collect(Collectors.toList());
    }

    @Override
    public List<PlayerIncomeStatRes> queryAdminPlayerIncomeList(long userId, Date startDate, Date endDate) {
        Result<PlayerSignServiceProto.ResponseQueryAdminPlayerIncomeList> result = playerSignService.queryAdminPlayerIncomeList(userId, startDate.getTime(), endDate.getTime(), "");
        if (RpcResult.isFail(result)) {
            log.warn("queryAdminPlayerIncomeList fail userId={},startDate={},endDate={},rCode={}", userId, startDate, endDate, result.rCode());
            return Collections.emptyList();
        }
        PlayerSignServiceProto.ResponseQueryAdminPlayerIncomeList target = result.target();
        return JsonUtil.loadsArray(target.getIncomeListJson(), PlayerIncomeStatRes.class);
    }

    @Override
    public Optional<PlayerSignInfoDto> getLatestSignRecord(long roomId, long userId) {
        PlayerSignServiceProto.GetLatestSignRecordRequest.Builder request = PlayerSignServiceProto.GetLatestSignRecordRequest.newBuilder();
        request.setNjId(roomId);
        request.setUserId(userId);
        Result<PlayerSignServiceProto.ResponseGetLatestSignRecord> result = playerSignService.getLatestSignRecord(request.build());
        String signRecordJson = result.target().getSignRecordJson();
        if (StringUtils.isBlank(signRecordJson)) {
            return Optional.empty();
        }
        return Optional.ofNullable(JsonUtil.loads(signRecordJson, PlayerSignInfoDto.class));
    }

    @Override
    public Optional<PlayerSignInfoDto> getLatestSignRecord(List<Long> roomIds, long userId) {
        PpPlayerSign latestSignRecord = playerSignMapper.getLatestSignRecord(roomIds, userId);
        return Optional.ofNullable(UserConvert.I.ppPlayerSign2PlayerSignInfoDto(latestSignRecord));
    }

    @Override
    public Optional<Long> getRoomBestFamily(long roomId) {
        PpContractExample example = new PpContractExample();
        example.createCriteria().andNjIdEqualTo(roomId);
        example.setOrderByClause("create_time desc");
        List<PpContract> ppContracts = contractMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(ppContracts)) {
            log.warn("getRoomBestFamily ppContracts empty roomId={}", roomId);
            return Optional.empty();
        }
        return Optional.ofNullable(ppContracts.get(0).getFamilyId());
    }

    @Override
    public Optional<Long> getPlayerBestFamily(long playerId) {
        Optional<Long> njIdOp = getUserBestNj(playerId);
        if (!njIdOp.isPresent()) {
            log.warn("getPlayerBestFamily sign empty playerId={}", playerId);
            return Optional.empty();
        }

        Long njId = njIdOp.get();
        return getRoomBestFamily(njId);
    }

    @Override
    public Optional<Long> getUserBestNj(long userId) {
        //优先查询签约
        PpPlayerSignExample example = new PpPlayerSignExample();
        example.createCriteria()
                .andTypeEqualTo("SIGN")
                .andStatusEqualTo("SIGN_SUCCEED")
                .andUserIdEqualTo(userId);
        example.setOrderByClause("create_time desc");
        List<PpPlayerSign> signList = playerSignMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(signList)) {
            return Optional.ofNullable(signList.get(0).getNjId());
        }

        PpPlayerSignExample example2 = new PpPlayerSignExample();
        example2.createCriteria()
                .andUserIdEqualTo(userId);
        example2.setOrderByClause("create_time desc");
        List<PpPlayerSign> signList2 = playerSignMapper.selectByExample(example2);
        if (CollectionUtils.isNotEmpty(signList2)) {
            return Optional.ofNullable(signList2.get(0).getNjId());
        }
        return Optional.empty();
    }

    @Override
    public Long getPlayerLastRoom(long familyId, long playerId) {
        List<Long> njIds = getFamilyAllNjId(familyId);
        if (CollectionUtils.isEmpty(njIds)) {
            return null;
        }

        //优先查询已签约
        PpPlayerSignExample example = new PpPlayerSignExample();
        example.createCriteria()
                .andTypeEqualTo("SIGN")
                .andStatusEqualTo("SIGN_SUCCEED")
                .andNjIdIn(njIds)
                .andUserIdEqualTo(playerId);
        example.setOrderByClause("create_time desc");
        List<PpPlayerSign> signList = playerSignMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(signList)) {
            log.info("has sign");
            return signList.get(0).getNjId();
        }

        //没有已签约就返回最近的旧签约
        PpPlayerSignExample example2 = new PpPlayerSignExample();
        example2.createCriteria()
                .andNjIdIn(njIds)
                .andUserIdEqualTo(playerId);
        example2.setOrderByClause("create_time desc");
        List<PpPlayerSign> noSignList = playerSignMapper.selectByExample(example2);
        if (CollectionUtils.isNotEmpty(noSignList)) {
            log.info("has no sign");
            return noSignList.get(0).getNjId();
        }
        return null;
    }

    @Override
    public List<Long> getOfficialRoomIds() {
        //没有官方厅
        return Collections.emptyList();
    }

    @Override
    public Optional<Long> getRoomBestFamilyByCache(long roomId) {
        return FAMILY_ID_CACHE.getUnchecked(roomId);
    }

    @Override
    public Optional<Long> getRoomSignFamilyInDate(long roomId, Date date) {
        List<Long> list = contractMapper.getRoomSignFamilyInDate(roomId, DateUtil.formatDateNormal(date));
        if (CollectionUtils.isEmpty(list)) {
            return Optional.empty();
        }
        return Optional.ofNullable(list.get(0));
    }

    @Override
    public Optional<FamilyBean> getFamily(long familyId) {

        return Optional.empty();
    }

    @Override
    public PageBean<RoomSignInfoBean> guildSignRoomPageList(SMSignRoomPageListReqDto reqDto) {
        PageList<PpContract> pageList = contractMapper.guildSignRoomPageList(reqDto, reqDto.getPageNo(), reqDto.getPageSize());
        List<RoomSignInfoBean> beanList = SignInfraConvert.I.ppContracts2Beans(pageList);
        return PageBean.of(pageList.getTotal(), beanList);
    }

    @Override
    public PageBean<SignPlayerInfoBean> signPlayerPageList(SMSignPlayerPageListReqDto reqDto) {
        PageList<PpPlayerSign> poList = playerSignMapper.signPlayerPageList(reqDto, reqDto.getPageNo(), reqDto.getPageSize());
        if (CollectionUtils.isEmpty(poList)) {
            return PageBean.empty();
        }

        List<SignPlayerInfoBean> beanList = SignInfraConvert.I.ppPlayerSigns2Beans(poList);
        return PageBean.of(poList.getTotal(), beanList);
    }

    @Override
    public Optional<FamilyAndNjContractBean> queryLastCancel(Long userId) {
        Result<UnwindContractServiceProto.ResponseApplyUnwindList> result = unwindContractService.applyUnwindList(userId, 0, "", 1, 1, new ArrayList<>());
        if (RpcResult.isFail(result)) {
            log.error("pp applyUnwindList fail. userId={},rCode={}", userId, result.rCode());
            return Optional.empty();
        }
        List<UnwindContractInfo> unwindContracts = JSONObject.parseArray(result.target().getUnWinds(), UnwindContractInfo.class);
        if (CollectionUtils.isEmpty(unwindContracts)) {
            return Optional.empty();
        }

        UnwindContractInfo unwindContract = unwindContracts.get(0);
        FamilyAndNjContractBean contractBean = new FamilyAndNjContractBean();
        contractBean.setId(unwindContract.getId());
        contractBean.setContractId(unwindContract.getUnwindContract().getId());
        contractBean.setSignId(unwindContract.getUnwindContract().getSignId());
        contractBean.setType(ContractTypeEnum.CANCEL.getCode());
        //PP解约都是厅主发起
        contractBean.setCreateUser(RoleEnum.ROOM.getRoleCode());
        contractBean.setNjUserId(unwindContract.getNjId());
        contractBean.setFamilyId(unwindContract.getFamilyId());

        if (FamilyConstant.AuditStatus.REFUSED.getCode().equals(unwindContract.getAuditStatus())) {
            //审批拒绝
            contractBean.setStatus(SignRelationEnum.AUDIT_FAIL.getCode());
        } else if (FamilyConstant.AuditStatus.PENDING.getCode().equals(unwindContract.getAuditStatus())) {
            contractBean.setStatus(SignRelationEnum.WAIT_AUDIT.getCode());
        } else {
            //审批通过
            contractBean.setStatus(
                    SignInfraConvert.I.ppSignStatusTrans(unwindContract.getUnwindContract().getStatus())
            );
        }

        return Optional.of(contractBean);
    }

    @Override
    public List<Long> selectAllSignPlayerByDate(List<Long> njIds, Date startDate, Date endDate) {
        String startStr = DateUtil.formatDateNormal(startDate);
        String endStr = DateUtil.formatDateNormal(endDate);
        return playerSignMapper.selectAllSignPlayerByDate(njIds, startStr, endStr);
    }

    @Override
    public Optional<FamilyAndNjContractBean> queryLastSign(Long userId) {
        PpContractExample example = new PpContractExample();
        example.setOrderByClause("create_time desc");
        PpContractExample.Criteria criteria = example.createCriteria();
        criteria.andNjIdEqualTo(userId);
        criteria.andTypeEqualTo(ContractTypeEnum.SIGN.getCode());

        PageList<PpContract> ppContracts = contractMapper.pageByExample(example, 1, 1);
        if (CollectionUtils.isEmpty(ppContracts)) {
            return Optional.empty();
        }

        PpContract contract = ppContracts.get(0);
        FamilyAndNjContractBean contractBean = SignInfraConvert.I.ppContract2ContractBean(contract);
        return Optional.of(contractBean);
    }

    @Override
    public boolean isUserSignAsRoom(Long userId) {
        FamilyNjServiceProto.FamilyNjParam param = FamilyNjServiceProto.FamilyNjParam.newBuilder()
                .setUserId(userId)
                .setStatus(FamilyConstant.FamilyNjStatus.JOIN.getCode())
                .build();
        Result<FamilyNjServiceProto.ResponseGetFamilyNj> result = familyNjService.getFamilyNj(param);
        if (result.rCode() == FamilyNjService.GET_FAMILY_NJ_NO_DATA) {
            LogContext.addResLog("getFamilyNjrCode={}", result.rCode());
            return false;
        }
        //其他情况都是已签署
        return true;
    }

    @Override
    public PageBean<FamilyAndNjContractBean> queryContract(RequestFamilyAndNjContractDTO request) {
        PpContractExample example = new PpContractExample();
        if (request.isDescCreateTime()) {
            example.setOrderByClause("create_time desc");
        }
        PpContractExample.Criteria criteria = example.createCriteria();
        if (request.isNoExpire()) {
            Date now = new Date();
            criteria.andBeginTimeLessThanOrEqualTo(now)
                    .andExpireTimeGreaterThan(now);
        }
        if (CollectionUtils.isNotEmpty(request.getTypes())) {
            criteria.andTypeIn(request.getTypes().stream().map(ContractTypeEnum::getCode).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(request.getRelations())) {
            criteria.andStatusIn(request.getRelations()
                    .stream().map(SignInfraConvert.I::waveSignStatus2pp)
                    .collect(Collectors.toList()));
        }
        if (request.getFamilyId() != null) {
            criteria.andFamilyIdEqualTo(request.getFamilyId());
        }
        if (request.getNjId() != null) {
            criteria.andNjIdEqualTo(request.getNjId());
        }
        if (request.getContractId() != null) {
            criteria.andIdEqualTo(request.getContractId());
        }
        if (StringUtils.isNotBlank(request.getSignId())) {
            criteria.andSignIdEqualTo(request.getSignId());
        }
        if (CollectionUtils.isNotEmpty(request.getContractLists())) {
            criteria.andIdIn(request.getContractLists());
        }
        if (request.getOtherFamilyId() != null) {
            criteria.andFamilyIdNotEqualTo(request.getOtherFamilyId());
        }

        PageList<PpContract> ppContracts = contractMapper.pageByExample(example, request.getPageNo(), request.getPageSize());
        return PageBean.of(ppContracts.getTotal(), SignInfraConvert.I.ppContracts2ContractBeans(ppContracts));
    }

    @Override
    public PageBean<FamilyAndNjContractBean> queryCancelApply(RequestFamilyAndNjCancelApply request) {
        PpUnwindApplyExample example = new PpUnwindApplyExample();
        example.setOrderByClause("create_time desc");
        PpUnwindApplyExample.Criteria criteria = example.createCriteria();
        if (request.getFamilyId() != null) {
            criteria.andFamilyIdEqualTo(request.getFamilyId());
        }
        if (CollectionUtils.isNotEmpty(request.getAudits())) {
            criteria.andAuditStatusIn(request.getAudits().stream().map(AuditStatusEnum::getCode).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(request.getRelations())) {
            criteria.andStatusIn(request.getRelations()
                    .stream().map(SignInfraConvert.I::waveSignStatus2pp)
                    .collect(Collectors.toList()));
        }
        if (request.getNjId() != null) {
            criteria.andNjIdEqualTo(request.getNjId());
        }

        PageList<PpUnwindApply> pageList = unwindApplyMapper.pageByExample(example, request.getPageNo(), request.getPageSize());
        if (CollectionUtils.isEmpty(pageList)) {
            return PageBean.empty();
        }

        //查询解约电子合同，补充时间信息
        List<Long> unwindContractIds = pageList.stream().filter(v -> v.getUnwindContractId() != null && v.getUnwindContractId() > 0).map(PpUnwindApply::getUnwindContractId).collect(Collectors.toList());
        Map<Long, PpContract> contractMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(unwindContractIds)) {
            PpContractExample contractExample = new PpContractExample();
            contractExample.createCriteria().andIdIn(unwindContractIds);
            List<PpContract> ppContracts = contractMapper.selectByExample(contractExample);
            contractMap = ppContracts.stream().collect(Collectors.toMap(PpContract::getId, resource -> resource, (existing, replacement) -> replacement));
        }

        List<FamilyAndNjContractBean> list = new ArrayList<>();
        for (PpUnwindApply ppUnwindApply : pageList) {
            FamilyAndNjContractBean bean = SignInfraConvert.I.ppUnwindApply2ContractBean(ppUnwindApply);
            if (AuditStatusEnum.PENDING.getCode().equals(ppUnwindApply.getAuditStatus())) {
                bean.setStatus(SignRelationEnum.WAIT_AUDIT.getCode());
            }
            if (AuditStatusEnum.REFUSED.getCode().equals(ppUnwindApply.getAuditStatus())) {
                bean.setStatus(SignRelationEnum.AUDIT_FAIL.getCode());
            }
            bean.setType(ContractTypeEnum.CANCEL.getCode());

            if (contractMap.containsKey(ppUnwindApply.getUnwindContractId())) {
                PpContract ppContract = contractMap.get(ppUnwindApply.getUnwindContractId());
                bean.setBeginTime(ppContract.getBeginTime());
                bean.setExpireTime(ppContract.getExpireTime());
                bean.setSignDeadline(ppContract.getSignDeadline());
                bean.setSignFinishTime(ppContract.getSignFinishTime());
            }

            list.add(bean);
        }

        return PageBean.of(pageList.getTotal(), list);
    }

    @Override
    public List<Long> queryAllSignNjId(Long familyId) {
        PpFamilyNjExample example = new PpFamilyNjExample();
        example.createCriteria()
                .andFamilyIdEqualTo(familyId)
                .andStatusEqualTo("JOIN");

        List<PpFamilyNj> list = familyNjMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return list.stream().map(PpFamilyNj::getNjId).collect(Collectors.toList());
    }

    @Override
    public Integer countSignRoomNum(long familyId) {
        PpContractExample example = new PpContractExample();
        example.createCriteria()
                .andFamilyIdEqualTo(familyId)
                .andTypeIn(Lists.newArrayList(ContractTypeEnum.SIGN.getCode()
                        , ContractTypeEnum.SUBJECT_CHANGE.getCode()
                        , ContractTypeEnum.RENEW.getCode()
                ))
                .andStatusEqualTo("SIGN_SUCCEED");

        return Math.toIntExact(contractMapper.countByExample(example));
    }

    @Override
    public ResponseUserApplyAdmin userApplyAdmin(RequestUserApplyAdmin request) {
        //不支持
        return new ResponseUserApplyAdmin().setCode(-1);
    }

    @Override
    public List<FamilyAndNjContractBean> queryIdentityNoJoinFamily(String identityNo) {
        return Collections.emptyList();
    }

    @Override
    public ResponseFamilyInviteAdmin familyInviteAdmin(RequestFamilyInviteAdmin request) {
        ResponseFamilyInviteAdmin res = new ResponseFamilyInviteAdmin();

        InviteAnchorInfo inviteAnchor = new InviteAnchorInfo();
        inviteAnchor.setFamilyId(request.getFamilyId());
        inviteAnchor.setNjId(request.getTargetUserId());
        inviteAnchor.setDuration(request.getDuration());
        inviteAnchor.setPercentage(20);
        inviteAnchor.setSettlyType("PUBLIC");
        Result<SignServiceProto.ResponseInviteAnchor> result = signService.inviteAnchor(JSONObject.toJSONString(inviteAnchor));
        if (RpcResult.isFail(result)) {
            log.error("inviteAnchor fail. rCode={},inviteAnchor={}", result.rCode(), JsonUtil.dumps(inviteAnchor));
            return res.setCode(-1);
        }

        //查询合同id
        PageBean<FamilyAndNjContractBean> pageBean = queryContract(RequestFamilyAndNjContractDTO.builder()
                .familyId(request.getFamilyId())
                .njId(request.getTargetUserId())
                .type(ContractTypeEnum.SIGN)
                .relation(SignRelationEnum.WAIT_SIGN)
                .build());
        if (CollectionUtils.isNotEmpty(pageBean.getList())) {
            res.setContractId(pageBean.getList().get(0).getContractId());
        }

        return res;
    }

    @Override
    public List<FamilyNjJoinRecordDTO> queryFamilyNjJoinRecord(QueryFamilyNjJoinRecordDTO request) {
        PpFamilyNjExample example = new PpFamilyNjExample();
        PpFamilyNjExample.Criteria criteria = example.createCriteria();
        if (request.getFamilyId() != null) {
            criteria.andFamilyIdEqualTo(request.getFamilyId());
        }
        if (request.getNjId() != null) {
            criteria.andNjIdEqualTo(request.getNjId());
        }
        if (StringUtils.isNotBlank(request.getStatus())) {
            criteria.andStatusEqualTo(request.getStatus());
        }

        List<PpFamilyNj> list = familyNjMapper.selectByExample(example);
        return SignInfraConvert.I.ppFamilyNjPOs2DTOs(list);
    }

    @Override
    public Optional<String> doSignGenSignId(Long contractId) {
        Signinfo signinfo = new Signinfo().setContractId(contractId);
        Result<SignServiceProto.ResponseSignContract> res = signService.signContract(JSONObject.toJSONString(signinfo));
        if (RpcResult.isFail(res)) {
            log.error("signContract fail. contractId={},rCode={}", contractId, res.rCode());
            return Optional.empty();
        }
        return Optional.of(res.target().getSignId());
    }

    @Override
    public Optional<String> familyAdminCancel(RequestFamilyAdminCancel request) {
        return Optional.empty();
    }

    @Override
    public ResponseAdminApplyCancelFamily adminApplyCancelFamily(RequestAdminApplyCancelFamily request) {
        ResponseAdminApplyCancelFamily res = new ResponseAdminApplyCancelFamily();

        Result<UnwindContractServiceProto.ResponseApplyUnwind> result = unwindContractService.applyUnwind(request.getCurUserId(), request.getContractId());

        if (result.rCode() == 1) {
            return res.setCode(-1).setMsg("请先关播再申请解约");
        }
        if (RpcResult.isFail(result)) {
            return res.setCode(-1);
        }

        return res;
    }

    @Override
    public Pair<Integer, String> familyAdminInviteConfirm(Long curUserId, Long targetUserId, RoleEnum operateRole) {
        return null;
    }

    @Override
    public Pair<Integer, String> familyAdminInviteCancelConfirm(Long familySignId, RoleEnum operateRole, Long curUserId) {
        return null;
    }

    @Override
    public PageBean<FamilyNjSignRecordDTO> querySignRecord(QueryFamilyNjSignRecordDTO param) {
        return PageBean.empty();
    }

    @Override
    public Pair<Integer, String> doFamilyNjConfirmSign(Long familyNjSignId, Long curUserId, ContractTypeEnum type, RoleEnum operateRole) {
        return null;
    }

    @Override
    public List<FamilyNjCancelRecordDTO> queryCancelRecordByContractId(List<Long> contractIds) {
        if (CollectionUtils.isEmpty(contractIds)) {
            return Collections.emptyList();
        }

        PpUnwindApplyExample example = new PpUnwindApplyExample();
        example.createCriteria()
                .andUnwindContractIdIn(contractIds);

        List<PpUnwindApply> list = unwindApplyMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return list.stream().map(v->new FamilyNjCancelRecordDTO()
                .setCancelContractId(v.getUnwindContractId())
                .setOldContractId(v.getContractId())
        ).collect(Collectors.toList());
    }

    @Override
    public List<ContractInfoDto> queryContractInfoByTime(long familyId, Date startDate, Date endDate) {
        // PP暂不实现
        return Collections.emptyList();
    }

    @Override
    public List<Long> getHistoryNjIds(Long familyId, Long minNjId, Integer pageSize) {
        return contractMapper.getHistoryNjIds(familyId, minNjId, pageSize);
    }
}
