package fm.lizhi.ocean.wavecenter.infrastructure.live.manager.checkinstat;

import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.live.constants.CheckInDateTypeEnum;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataPlayerWeek;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.ext.WcDataPlayerWeekExtMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.live.convert.WaveCheckInDataConverter;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WaveCheckInDayMicRecord;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WaveCheckInUserTask;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 周统计时间段处理器实现
 */
@Component
public class WeekStatisticPeriodHandler implements StatisticPeriodHandler {

    @Autowired
    private WcDataPlayerWeekExtMapper playerWeekExtMapper;
    @Autowired
    private WaveCheckInDataConverter converter;

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("MM/dd");

    @Override
    public boolean supports(CheckInDateTypeEnum dateType) {
        return CheckInDateTypeEnum.WEEK.equals(dateType);
    }

    @Override
    public List<StatisticPeriod> buildPresetStatisticPeriods(long startDate, long endDate) {
        // 周统计的每段为1天
        List<StatisticPeriod> statisticPeriods = new ArrayList<>();
        long periodStart = startDate;
        while (periodStart <= endDate) {
            statisticPeriods.add(new StatisticPeriod(CheckInDateTypeEnum.WEEK, periodStart));
            periodStart += 24 * 60 * 60 * 1000L;
        }
        return statisticPeriods;
    }

    @Override
    public StatisticPeriod buildNormalizedStatisticPeriod(long startTime) {
        // 周统计使用开始时间所在当天的0点表示时间段
        long startTimeOfDay = getStartTimeOfDay(startTime);
        return new StatisticPeriod(CheckInDateTypeEnum.WEEK, startTimeOfDay);
    }

    @Override
    public String formatStatisticPeriod(StatisticPeriod period) {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(period.getStartTime()), ZoneId.systemDefault())
                .format(FORMATTER);
    }

    @Override
    public Map<Long, DayMicCounter> buildDayMicCounterMap(List<WaveCheckInDayMicRecord> dayMicRecords) {
        if (CollectionUtils.isEmpty(dayMicRecords)) {
            return Collections.emptyMap();
        }
        Map<Long, DayMicCounter> map = new HashMap<>();
        for (WaveCheckInDayMicRecord record : dayMicRecords) {
            DayMicCounter counter = map.computeIfAbsent(record.getUserId(), k -> new DayMicCounter());
            counter.setRewardAmountSum(counter.getRewardAmountSum() + record.getRewardAmountSum());
        }
        return map;
    }

    @Override
    public Map<Long, List<ChatStat>> buildPlayerChatStatMap(int appId, List<Long> playerIds, long startDate, long endDate) {
        if(CollectionUtils.isEmpty(playerIds)) {
            return Collections.emptyMap();
        }
        Date end = DateUtil.getDayStart(new Date(endDate));
        List<WcDataPlayerWeek> playerWeek = playerWeekExtMapper.getWcDataPlayerWeek(appId, playerIds, new Date(startDate), end);
        return playerWeek.stream().collect(Collectors.groupingBy(WcDataPlayerWeek::getPlayerId,
                Collectors.mapping(converter::toWeekChatStat, Collectors.toList())));
    }

    @Override
    public List<WaveCheckInUserTask> buildWaveCheckInUserTaskList(List<Long> recordIds) {
        return Collections.emptyList();
    }

    @Override
    public Optional<SimpleUserDto> buildHostInfo(Map<Long, SimpleUserDto> simpleUserMap, List<Long> hostIds) {
        return Optional.empty();
    }


    private long getStartTimeOfDay(long time) {
        LocalDateTime t = LocalDateTime.ofInstant(Instant.ofEpochMilli(time), ZoneId.systemDefault());
        if (t.getHour() == 0 && t.getMinute() == 0 && t.getSecond() == 0 && t.getNano() == 0) {
            return time;
        }
        return t.withHour(0).withMinute(0).withSecond(0).withNano(0)
                .atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }
}
