package fm.lizhi.ocean.wavecenter.infrastructure.sign.manager;

import cn.hutool.core.util.StrUtil;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.bean.FamilyAndNjContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.RoomSignRecordBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.TodoSignBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.*;
import fm.lizhi.ocean.wavecenter.api.sign.request.*;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseAdminApplyCancelFamily;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseFamilyInviteAdmin;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseUserApplyAdmin;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PayTenantCodeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.convert.SignInfraConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.IContractRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.ISettleRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.request.RequestContractSettle;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.request.RequestFamilyAdminCancel;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.request.RequestFamilyAndNjCancelApply;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto.ContractInfoDto;
import fm.lizhi.ocean.wavecenter.service.sign.dto.*;
import fm.lizhi.ocean.wavecenter.service.sign.manager.ContractManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import fm.lizhi.trade.contract.api.AuthService;
import fm.lizhi.trade.contract.api.SignContractService;
import fm.lizhi.trade.contract.constant.AuthStatus;
import fm.lizhi.trade.contract.constant.BestSignConstants;
import fm.lizhi.trade.contract.constant.FamilyConstants;
import fm.lizhi.trade.contract.constant.SignRoleEnum;
import fm.lizhi.trade.contract.protocol.AuthProto;
import fm.lizhi.trade.contract.protocol.CommonProto;
import fm.lizhi.trade.contract.protocol.ContractProto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/9 15:26
 */
@Slf4j
@Component
public class ContractManagerImpl implements ContractManager {

    @Autowired
    private IContractRemote iContractRemote;
    @Autowired
    private SignContractService signContractService;
    @Autowired
    private ISettleRemote settleRemote;
    @Autowired
    private UserManager userManager;
    @Autowired
    private AuthService authService;
    @Autowired
    private FamilyManager familyManager;

    @Override
    public Optional<FamilyAndNjContractBean> queryLastCancel(Long userId) {
        //查询最近一条解约流程
        Optional<FamilyAndNjContractBean> contractBeanOp = iContractRemote.queryLastCancel(userId);
        if (!contractBeanOp.isPresent()) {
            LogContext.addResLog("contractBeanOp is null");
            return Optional.empty();
        }

        //查询双方签约状态
        FamilyAndNjContractBean contractBean = contractBeanOp.get();
        if (StringUtils.isBlank(contractBean.getSignId())) {
            //未签署
            LogContext.addResLog("signId is null");
            return Optional.of(contractBean);
        }

        Map<Long, UserSignStatusEnum> userSignMap = getContractUserSignStatus(contractBean.getSignId());
        if (MapUtils.isEmpty(userSignMap)) {
            //不影响查询
            return Optional.of(contractBean);
        }

        UserSignStatusEnum njStatus = userSignMap.get(contractBean.getNjUserId());
        if (njStatus != null) {
            contractBean.setNjSignStatus(njStatus.getCode());
        }

        UserSignStatusEnum familyStatus = userSignMap.get(contractBean.getFamilyId());
        if (familyStatus != null) {
            contractBean.setFamilySignStatus(familyStatus.getCode());
        }

        return Optional.of(contractBean);
    }

    /**
     * 获取合同相关人员的签约状态
     * @return
     */
    public Map<Long, UserSignStatusEnum> getContractUserSignStatus(String signId){
        if (StringUtils.isBlank(signId)) {
            return Collections.emptyMap();
        }

        Result<ContractProto.ResponseGetReceiver> result = signContractService.getReceiver(signId);
        if (RpcResult.isFail(result)) {
            log.error("getReceiver fail. signId={},rCode={}", signId, result.rCode());
            return Collections.emptyMap();
        }
        List<CommonProto.ReceiverInfo> receiversList = result.target().getReceiversList();
        Map<Long, UserSignStatusEnum> map = new HashMap<>();
        for (CommonProto.ReceiverInfo receiverInfo : receiversList) {
            long uid = receiverInfo.getUid();
            String signStatus = receiverInfo.getSignStatus();
            String role = receiverInfo.getRole();
            log.info("getReceiver signId={},uid={},signStatus={},role={}", signId, uid, signStatus, role);
            if (SignRoleEnum.PLATFORM.getName().equals(role)) {
                //忽略平台
                continue;
            }
            map.putIfAbsent(uid, UserSignStatusEnum.from(signStatus));
        }
        return map;
    }

    @Override
    public List<FamilyAndNjContractBean> queryIdentityNoJoinFamily(String identityNo) {
        return iContractRemote.queryIdentityNoJoinFamily(identityNo);
    }

    @Override
    public Optional<String> genContractSignUrl(Long reqUid, String signId) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        String code = PayTenantCodeEnum.getPayTenantCode(appId);

        Pair<Long, FamilyConstants.FamilyType> familyTypePair = getFamilyType(reqUid);
        Long familyId = familyTypePair.getLeft();
        FamilyConstants.FamilyType familyType = familyTypePair.getRight();

        BestSignConstants.UserType userType = BestSignConstants.UserType.PERSON;
        if (FamilyConstants.FamilyType.C_FAMILY.name().equals(familyType.name())) {
            userType = BestSignConstants.UserType.ENTERPRISE;
        }

        String account = getAccount(code, reqUid, familyType, familyId);

        long signUid = reqUid;
        if (familyType != FamilyConstants.FamilyType.ANCHOR) {
            signUid = familyId;
        }

        Result<AuthProto.ResponseGetBindingAccount> bindingAccount = authService.getBindingAccount(code, signUid);
        log.info("genContractSignUrl bindingAccount. signUid:{}, code:{}, rCode: {}, signId:{}", signUid, code, bindingAccount.rCode(), signId);

        if (bindingAccount.rCode() == AuthService.GET_BINDING_ACCOUNT_FAIL){
            // 未绑定
            return bindingAccount(signUid, signId, account, code, userType);
        }

        if (RpcResult.isFail(bindingAccount)) {
            log.error("genContractSignUrl getBindingAccount is fail ,tenantCode={},signUid={}", code, signUid);
            return Optional.empty();
        }

        CommonProto.BindAccount bindAccount = bindingAccount.target().getBindAccount();

        // 查不到信息，绑定账号
        if (StringUtils.isEmpty(bindAccount.getAccount())){
            return bindingAccount(signUid, signId, account, code, userType);
        }else if (!Objects.equals(account, bindAccount.getAccount())){
            // 账号不一致，解绑
            Result<AuthProto.ResponseUnbindingAccount> responseUnbindingAccountResult = authService.unbindingAccount(code, signUid);
            if (RpcResult.isFail(responseUnbindingAccountResult)) {
                log.error("genContractSignUrl unbindingAccount fail. tenantCode={},userId={}", code, signUid);
                return Optional.empty();
            }

            // 重新绑定
            return bindingAccount(signUid, signId, account, code, userType);
        }


        Result<AuthProto.ResponseGenSsoLink> result = authService.genSsoLink(CommonProto.SsoLink.newBuilder()
                .setTenantCode(code)
                .setContractId(signId)
                .setUid(signUid)
                .setTargetPage(BestSignConstants.TargetPageType.SIGNING.getCode())
                .build());
        if (RpcResult.isFail(result)) {
            log.error("genContractSignUrl fail. signUid={},code={},signId={},rCode={}", signUid, code, signId, result.rCode());
            return Optional.empty();
        }
        return Optional.of(result.target().getTargetUrl());
    }

    /**
     * 获取上上签绑定账号链接
     */
    private Optional<String> bindingAccount(Long reqUid, String signId, String account, String code, BestSignConstants.UserType userType) {
        if (StrUtil.isEmpty(account)){
            log.error("genContractSignUrl getAccount is null. reqUid={}, code={}, signId={}", reqUid, code, signId);
            return Optional.empty();
        }

        Result<AuthProto.ResponseBindingAccount> bindingAccountResult = authService.bindingAccount(code, reqUid, account, userType.getType(), BestSignConstants.TargetPageType.SIGNING.getCode(), signId, null);
        if (RpcResult.isFail(bindingAccountResult)){
            log.error("genContractSignUrl bindingAccount fail. reqUid={},code={},signId={},rCode={}", reqUid, code, signId, bindingAccountResult.rCode());
            return Optional.empty();
        }
        return Optional.of(bindingAccountResult.target().getTargetUrl());
    }

    private String getAccount(String code, Long uid, FamilyConstants.FamilyType familyType, Long familyId) {
        if (familyType.name().equals(FamilyConstants.FamilyType.ANCHOR.name()) || familyType.name().equals(FamilyConstants.FamilyType.P_FAMILY.name())) {
            //个人
            Result<AuthProto.ResponseGetPersonalInfo> personalInfo = authService.getPersonalInfo(code, uid, familyType.getCode(), familyId);
            if (RpcResult.isFail(personalInfo)) {
                log.error("getPersonalInfo fail. code={},uid={},familyType={},familyId={},rCode={}", code, uid, familyType, familyId, personalInfo.rCode());
                return null;
            }

            return Optional.of(personalInfo.target()).map(AuthProto.ResponseGetPersonalInfo::getPersonalInfo).map(CommonProto.PersonalInfo::getAccount).orElse(null);
        }
        if (familyType.name().equals(FamilyConstants.FamilyType.C_FAMILY.name())) {
            //公司家族
            Result<AuthProto.ResponseGetEnterpriseInfo> result = authService.getEnterpriseInfo(code, familyId);
            if (RpcResult.isFail(result)) {
                log.error("getEnterpriseInfo fail. code={},familyId={},rCode={}", code, familyId, result.rCode());
                return null;
            }

            return Optional.of(result.target()).map(AuthProto.ResponseGetEnterpriseInfo::getEnterpriseInfo).map(CommonProto.EnterpriseInfo::getAccount).orElse(null);
        }
        return null;

    }

    /**
     * 获取familyId 以及 家族类型
     * @return Pair<Long, FamilyConstants.FamilyType> familyId, familyType
     */
    private Pair<Long, FamilyConstants.FamilyType> getFamilyType(Long reqUid) {
        UserInFamilyBean userInFamily = familyManager.getUserInFamily(reqUid);

        if (userInFamily.isFamily()) {
            return Pair.of(userInFamily.getFamilyId(), FamilyConstants.FamilyType.C_FAMILY);
        }

        if (userInFamily.isRoom() || userInFamily.isPlayer()) {
            return Pair.of(userInFamily.getFamilyId(), FamilyConstants.FamilyType.ANCHOR);
        }

        return Pair.of(-1L, FamilyConstants.FamilyType.ANCHOR);
    }

    @Override
    public Optional<String> genContactViewUrl(String signId) {
        Result<ContractProto.ResponseGetContractUrl> result = signContractService.getContractUrl(signId);
        if (RpcResult.isFail(result)) {
            log.error("genContactViewUrl fail. signId={},rCode={}", signId, result.rCode());
            return Optional.empty();
        }
        return Optional.of(result.target().getUrl());
    }

    @Override
    public List<FamilyNjJoinRecordDTO> queryFamilyNjJoinRecord(QueryFamilyNjJoinRecordDTO request) {
        return iContractRemote.queryFamilyNjJoinRecord(request);
    }

    @Override
    public Optional<FamilyAndNjContractBean> queryLastSign(Long userId) {
        Optional<FamilyAndNjContractBean> contractBeanOp = iContractRemote.queryLastSign(userId);
        if (!contractBeanOp.isPresent()) {
            LogContext.addResLog("contractBeanOp is null");
            return Optional.empty();
        }

        //查询双方签约状态
        FamilyAndNjContractBean contractBean = contractBeanOp.get();
        if (StringUtils.isBlank(contractBean.getSignId())) {
            //未签署
            LogContext.addResLog("signId is null");
            return Optional.of(contractBean);
        }

        Map<Long, UserSignStatusEnum> signStatus = getContractUserSignStatus(contractBean.getSignId());
        UserSignStatusEnum njSignStatus = signStatus.get(contractBean.getNjUserId());
        if (njSignStatus != null) {
            contractBean.setNjSignStatus(njSignStatus.getCode());
        } else {
            contractBean.setNjSignStatus(UserSignStatusEnum.WAIT_SIGN.getCode());
        }

        UserSignStatusEnum familySignStatus = signStatus.get(contractBean.getFamilyId());
        if (familySignStatus != null) {
            contractBean.setFamilySignStatus(familySignStatus.getCode());
        } else {
            contractBean.setFamilySignStatus(UserSignStatusEnum.WAIT_SIGN.getCode());
        }

        return Optional.of(contractBean);
    }

    @Override
    public boolean isUserSignAsRoom(Long userId) {
        return iContractRemote.isUserSignAsRoom(userId);
    }

    @Override
    public PageBean<FamilyAndNjContractBean> queryContract(RequestFamilyAndNjContractDTO request) {
        return iContractRemote.queryContract(request);
    }

    @Override
    public List<TodoSignBean> todoRoomList(RequestFamilyTodoRoomList request) {
        //查询签约的待办
        PageBean<FamilyAndNjContractBean> signList = iContractRemote.queryContract(RequestFamilyAndNjContractDTO.builder()
                .type(ContractTypeEnum.SIGN)
                .familyId(request.getFamilyId())
                .relation(SignRelationEnum.WAIT_SIGN)
                .relation(SignRelationEnum.SIGNING)
                .noExpire(true)
                .build());

        //查询解约的待办
        PageBean<FamilyAndNjContractBean> cancelList = iContractRemote.queryCancelApply(RequestFamilyAndNjCancelApply.builder()
                .familyId(request.getFamilyId())
                .audit(AuditStatusEnum.AGREED)
                .audit(AuditStatusEnum.PENDING)
                .relation(SignRelationEnum.WAIT_SIGN)
                .relation(SignRelationEnum.SIGNING)
                .build());

        List<FamilyAndNjContractBean> contractList = new ArrayList<>();
        contractList.addAll(signList.getList());
        contractList.addAll(cancelList.getList());

        //排序
        contractList.sort(Comparator.comparing(FamilyAndNjContractBean::getCreateTime).reversed());

        //签约合同的结算方式
        Map<Long, SignSettleDTO> settleMap = new HashMap<>();

        if (CollectionUtils.isNotEmpty(cancelList.getList())) {
            List<Long> contractIds = cancelList.getList().stream().map(FamilyAndNjContractBean::getContractId).collect(Collectors.toList());
            List<FamilyNjCancelRecordDTO> cancelRecords = this.queryCancelRecordByContractId(contractIds);
            if (CollectionUtils.isNotEmpty(cancelRecords)) {
                //key=解约合同ID，value=原合同ID
                Map<Long, Long> idMap = cancelRecords.stream().collect(Collectors.toMap(FamilyNjCancelRecordDTO::getCancelContractId, FamilyNjCancelRecordDTO::getOldContractId, (k1,k2)->k2));

                PageBean<FamilyAndNjContractBean> oldList = this.queryContract(RequestFamilyAndNjContractDTO.builder()
                        .contractLists(idMap.values()).pageSize(100)
                        .build());

                //解约的查询原合同的结算信息
                Map<Long, SignSettleDTO> cancelSettle = settleRemote.querySettle(RequestContractSettle.builder().contracts(oldList.getList()).build());

                for (Map.Entry<Long, Long> entry : idMap.entrySet()) {
                    Long oldId = entry.getValue();
                    SignSettleDTO settleDTO = cancelSettle.get(oldId);
                    if (settleDTO != null) {
                        settleMap.put(entry.getKey(), settleDTO);
                    }
                }
            }
        }

        if (CollectionUtils.isNotEmpty(signList.getList())) {
            settleMap.putAll(settleRemote.querySettle(RequestContractSettle.builder()
                    .contracts(signList.getList()).build()));
        }

        List<TodoSignBean> todoList = new ArrayList<>();
        for (FamilyAndNjContractBean contractBean : contractList) {
            TodoSignBean todoBean = SignInfraConvert.I.familyNjSign2TodoBean(contractBean);

            SignSettleDTO settle = settleMap.get(contractBean.getContractId());
            if (settle != null) {
                todoBean.setSettleType(settle.getSettleType())
                        .setSettlePercentage(settle.getSettlePercentage());
            }

            //双方签约状态
            if (StringUtils.isNotBlank(contractBean.getSignId())) {
                Map<Long, UserSignStatusEnum> signMap = getContractUserSignStatus(contractBean.getSignId());
                UserSignStatusEnum njStatus = signMap.get(contractBean.getNjUserId());
                if (njStatus != null) {
                    todoBean.setNjSignStatus(njStatus.getCode());
                }
                UserSignStatusEnum familyStatus = signMap.get(contractBean.getFamilyId());
                if (familyStatus != null) {
                    todoBean.setFamilySignStatus(familyStatus.getCode());
                }
            }

            todoList.add(todoBean);
        }
        return todoList;
    }

    @Override
    public PageBean<RoomSignRecordBean> queryRoomSignList(RequestQueryRoomSignList request) {

        PageBean<FamilyAndNjContractBean> signList = PageBean.empty();
        if (request.getType() == ContractTypeEnum.CANCEL
                && (request.getStatus() == SignRelationEnum.WAIT_AUDIT || request.getStatus() == SignRelationEnum.AUDIT_FAIL)) {
            RequestFamilyAndNjCancelApply.RequestFamilyAndNjCancelApplyBuilder paramBuilder = RequestFamilyAndNjCancelApply.builder()
                    .familyId(request.getFamilyId())
                    .pageNo(request.getPageNo())
                    .pageSize(request.getPageSize());

            if (request.getStatus() == SignRelationEnum.WAIT_AUDIT) {
                paramBuilder.audit(AuditStatusEnum.PENDING);
            }
            if (request.getStatus() == SignRelationEnum.AUDIT_FAIL) {
                paramBuilder.audit(AuditStatusEnum.REFUSED);
            }

            //查询用户ID
            if (StringUtils.isNotBlank(request.getUserBand())) {
                Long userId = userManager.getUserIdByBand(request.getUserBand());
                if (userId == null) {
                    //用户不存在，返回空列表
                    LogContext.addResLog("userId is null");
                    return PageBean.empty();
                }
                paramBuilder.njId(userId);
            }

            signList = iContractRemote.queryCancelApply(paramBuilder.build());

        } else {
            RequestFamilyAndNjContractDTO.RequestFamilyAndNjContractDTOBuilder paramBuilder = RequestFamilyAndNjContractDTO.builder()
                    .type(request.getType())
                    .familyId(request.getFamilyId())
                    .relation(request.getStatus())
                    .pageNo(request.getPageNo())
                    .pageSize(request.getPageSize());

            if (request.getStatus() == SignRelationEnum.WAIT_SIGN) {
                //待签署需要包括签署中的
                paramBuilder.relation(SignRelationEnum.SIGNING);
            }

            if (request.getType() == ContractTypeEnum.SIGN && request.getStatus() == SignRelationEnum.SIGN_SUCCESS) {
                paramBuilder.type(ContractTypeEnum.SUBJECT_CHANGE);
                paramBuilder.type(ContractTypeEnum.RENEW);
            }

            if (request.getType() == ContractTypeEnum.CANCEL && request.getStatus() == SignRelationEnum.SIGN_SUCCESS) {
                paramBuilder.relation(SignRelationEnum.STOP_CONTRACT);
            }

            //查询用户ID
            if (StringUtils.isNotBlank(request.getUserBand())) {
                Long userId = userManager.getUserIdByBand(request.getUserBand());
                if (userId == null) {
                    //用户不存在，返回空列表
                    LogContext.addResLog("userId is null");
                    return PageBean.empty();
                }
                paramBuilder.njId(userId);
            }

            signList = iContractRemote.queryContract(paramBuilder.build());

        }

        if (CollectionUtils.isEmpty(signList.getList())) {
            LogContext.addResLog("signList is empty");
            return PageBean.empty();
        }

        List<RoomSignRecordBean> recordList = new ArrayList<>();
        for (FamilyAndNjContractBean contractBean : signList.getList()) {
            RoomSignRecordBean recordBean = SignInfraConvert.I.familyNjSign2RecordBean(contractBean);

            //双方签约状态
            if (StringUtils.isNotBlank(contractBean.getSignId())) {
                Map<Long, UserSignStatusEnum> signMap = getContractUserSignStatus(contractBean.getSignId());
                UserSignStatusEnum njStatus = signMap.get(contractBean.getNjUserId());
                if (njStatus != null) {
                    recordBean.setNjSignStatus(njStatus.getCode());
                }
                UserSignStatusEnum familyStatus = signMap.get(contractBean.getFamilyId());
                if (familyStatus != null) {
                    recordBean.setFamilySignStatus(familyStatus.getCode());
                }
            }

            recordList.add(recordBean);
        }

        return PageBean.of(signList.getTotal(), recordList);
    }

    @Override
    public Integer countSignRoomNum(long familyId) {
        return iContractRemote.countSignRoomNum(familyId);
    }

    @Override
    public boolean isPassSignRealNameVerify(long userId) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Result<AuthProto.ResponseGetPersonalInfo> result = authService.getPersonalInfo(PayTenantCodeEnum.getPayTenantCode(appId), userId, FamilyConstants.FamilyType.ANCHOR.getCode(), 0L);
        if (result.rCode() == 2) {
            LogContext.addResLog("getPersonalInfo not exist");
            return false;
        }
        if (RpcResult.isFail(result)) {
            log.error("getPersonalInfo fail. appId={},userId={},rCode={}", appId, userId, result.rCode());
            return false;
        }
        String status = result.target().getPersonalInfo().getStatus();
        LogContext.addResLog("status={}", status);
        return AuthStatus.AUTO_AUTH_PASS.getCode().equals(status);
    }

    @Override
    public IdentifyStatusEnum getSignRealNameStatus(long userId) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Result<AuthProto.ResponseGetPersonalInfo> result = authService.getPersonalInfo(PayTenantCodeEnum.getPayTenantCode(appId), userId, FamilyConstants.FamilyType.ANCHOR.getCode(), 0L);
        if (result.rCode() == 2) {
            LogContext.addResLog("getPersonalInfo not exist");
            return IdentifyStatusEnum.UNFINISHED;
        }
        if (RpcResult.isFail(result)) {
            log.error("getPersonalInfo fail. appId={},userId={},rCode={}", appId, userId, result.rCode());
            return IdentifyStatusEnum.UNFINISHED;
        }
        String status = result.target().getPersonalInfo().getStatus();
        LogContext.addResLog("status={}", status);

        if (AuthStatus.IN_AUDIT.getCode().equals(status)
                || AuthStatus.IN_AUTH.getCode().equals(status)
                || AuthStatus.IN_WILL_AUTH.getCode().equals(status)
                || AuthStatus.REPEAT_AUTH.getCode().equals(status)
        ) {
            return IdentifyStatusEnum.WAIT_AUDIT;
        }

        return AuthStatus.AUTO_AUTH_PASS.getCode().equals(status)
                ? IdentifyStatusEnum.FINISHED
                : IdentifyStatusEnum.UNFINISHED;
    }

    @Override
    public Optional<SignPersonalInfoDTO> getSignPersonalInfo(long userId) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Result<AuthProto.ResponseGetPersonalInfo> result = authService.getPersonalInfo(PayTenantCodeEnum.getPayTenantCode(appId), userId, FamilyConstants.FamilyType.ANCHOR.getCode(), 0L);
        if (RpcResult.isFail(result)) {
            log.error("getSignPersonalInfo fail. appId={},userId={},rCode={}", appId, userId, result.rCode());
            return Optional.empty();
        }
        CommonProto.PersonalInfo pb = result.target().getPersonalInfo();
        return Optional.of(new SignPersonalInfoDTO()
                .setAuthStatus(SignAuthStatusEnum.getByCode(pb.getStatus()))
                .setIdentityNo(pb.getIdentityNo())
        );
    }

    @Override
    public ResponseUserApplyAdmin userApplyAdmin(RequestUserApplyAdmin request) {
        return iContractRemote.userApplyAdmin(request);
    }

    @Override
    public ResponseFamilyInviteAdmin familyInviteAdmin(RequestFamilyInviteAdmin request) {
        return iContractRemote.familyInviteAdmin(request);
    }

    @Override
    public Optional<String> getSignToken(long userId) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        String code = PayTenantCodeEnum.getPayTenantCode(appId);
        Result<AuthProto.ResponseGetSignToken> result = authService.getSignToken(code, userId);
        if (RpcResult.isFail(result)) {
            log.error("getSignToken fail. code={},userId={},rCode={}", code, userId, result.rCode());
            return Optional.empty();
        }
        return Optional.of(result.target().getToken());
    }

    @Override
    public Optional<String> doSignGenSignId(Long contractId) {
        return iContractRemote.doSignGenSignId(contractId);
    }

    @Override
    public Optional<String> familyApplyCancelAdmin(RequestFamilyApplyCancelAdmin request) {
        return iContractRemote.familyAdminCancel(RequestFamilyAdminCancel.builder()
                .curUserId(request.getCurUserId())
                .njId(request.getTargetUserId())
                .familyId(request.getFamilyId())
                .subjectId(request.getFamilyId())
                .contractId(request.getContractId())
                .build());
    }

    @Override
    public Optional<String> adminApplyCancelFamilyForSignId(RequestAdminApplyCancelFamily request) {
        return iContractRemote.familyAdminCancel(RequestFamilyAdminCancel.builder()
                .curUserId(request.getCurUserId())
                .njId(request.getCurUserId())
                .familyId(request.getFamilyId())
                .subjectId(request.getCurUserId())
                .contractId(request.getContractId())
                .build());
    }

    @Override
    public ResponseAdminApplyCancelFamily adminApplyCancelFamily(RequestAdminApplyCancelFamily request) {
        return iContractRemote.adminApplyCancelFamily(request);
    }

    @Override
    public boolean existEffectChangeObjByNjId(Long njId) {
        return false;
    }

    @Override
    public Pair<Integer, String> familyAdminInviteConfirm(Long curUserId, Long targetUserId, RoleEnum operateRole) {
        return iContractRemote.familyAdminInviteConfirm(curUserId, targetUserId, operateRole);
    }

    @Override
    public Pair<Integer, String> familyAdminInviteCancelConfirm(Long familySignId, RoleEnum operateRole, Long curUserId) {
        return iContractRemote.familyAdminInviteCancelConfirm(familySignId, operateRole, curUserId);
    }

    @Override
    public PageBean<FamilyNjSignRecordDTO> querySignRecord(QueryFamilyNjSignRecordDTO param) {
        return iContractRemote.querySignRecord(param);
    }

    @Override
    public Pair<Integer, String> doFamilyNjConfirmSign(Long familyNjSignId, Long curUserId, ContractTypeEnum type, RoleEnum operateRole) {
        return iContractRemote.doFamilyNjConfirmSign(familyNjSignId, curUserId, type, operateRole);
    }

    @Override
    public List<FamilyNjCancelRecordDTO> queryCancelRecordByContractId(List<Long> contractIds) {
        return iContractRemote.queryCancelRecordByContractId(contractIds);
    }

    @Override
    public List<ContractInfoDto> queryContractInfoByTime(long familyId, Date startDate, Date endDate) {
        return iContractRemote.queryContractInfoByTime(familyId, startDate, endDate);
    }

    @Override
    public Optional<Long> getNjSignFamilyId(Long njId) {
        RequestFamilyAndNjContractDTO.RequestFamilyAndNjContractDTOBuilder builder = RequestFamilyAndNjContractDTO.builder()
                .njId(njId)
                .type(ContractTypeEnum.SIGN)
                .type(ContractTypeEnum.SUBJECT_CHANGE)
                .type(ContractTypeEnum.RENEW)
                .relation(SignRelationEnum.SIGN_SUCCESS)
                .noExpire(true);
        PageBean<FamilyAndNjContractBean> list = iContractRemote.queryContract(builder.build());
        if (CollectionUtils.isNotEmpty(list.getList())) {
            return Optional.of(list.getList().get(0).getFamilyId());
        }
        return Optional.empty();
    }
}
