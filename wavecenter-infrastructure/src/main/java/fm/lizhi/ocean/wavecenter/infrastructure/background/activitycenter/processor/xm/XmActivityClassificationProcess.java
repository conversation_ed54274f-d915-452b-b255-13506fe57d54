package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.processor.xm;

import java.util.List;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.ActivityConfig;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.framework.apollo.core.enums.Env;
import com.google.common.collect.Lists;
import com.lizhi.commons.config.core.util.ConfigUtils;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityBigClass;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityClassConfig;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.processor.IActivityClassificationProcess;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.remote.xm.XmActivityClassRemote;
import lombok.extern.slf4j.Slf4j;
import xm.fm.lizhi.live.pp.dto.officialseat.PrimaryActivityConfigDto;
import xm.fm.lizhi.live.pp.dto.officialseat.SecondaryActivityConfigDto;
import xm.fm.lizhi.live.pp.enums.officialseat.ActivityStatusEnum;

@Slf4j
@Component
public class XmActivityClassificationProcess implements IActivityClassificationProcess {

    @Autowired
    private XmActivityClassRemote xmActivityClassRemote;

    @Autowired
    private ActivityConfig activityConfig;

    @Override
    public Result<Void> syncBigClassToBiz(ActivityBigClass entity) {
        //预发环境不做同步
        if (!activityConfig.getEnableSyncCategory() && ConfigUtils.getEnvRequired() == Env.PRE) {
            return RpcResult.success();
        }
        Result<Void> result = xmActivityClassRemote.syncSaveBigClass(entity);
        if (RpcResult.isFail(result)) {
            return RpcResult.fail(result.rCode(), result.getMessage());
        }
        log.info("同步活动大类成功, id: {}, name: {}", entity.getId(), entity.getName());
        return RpcResult.success();
    }

    @Override
    public Result<Void> syncClassToBiz(ActivityClassConfig entity) {
        //预发环境不做同步
        if (!activityConfig.getEnableSyncCategory() && ConfigUtils.getEnvRequired() == Env.PRE) {
            return RpcResult.success();
        }
        Result<Void> result = xmActivityClassRemote.syncSaveClass(entity);
        if (RpcResult.isFail(result)) {
            return RpcResult.fail(result.rCode(), result.getMessage());
        }
        log.info("同步活动二级分类成功, id: {}, name: {}", entity.getId(), entity.getName());
        return RpcResult.success();
    }


    @Override
    public Result<Void> deleteBigClass(Long id, String operator) {
        //预发环境不做操作
        if (!activityConfig.getEnableSyncCategory() && ConfigUtils.getEnvRequired() == Env.PRE) {
            return RpcResult.success();
        }
        //先查询一下是否业务中不存在该分类
        Result<List<PrimaryActivityConfigDto>> getRes = xmActivityClassRemote.getBigClassById(Lists.newArrayList(id));
        if (RpcResult.isFail(getRes)) {
            return RpcResult.fail(getRes.rCode(), getRes.getMessage());
        }

        if (CollectionUtils.isEmpty(getRes.target())) {
            return RpcResult.success();
        }

        //过滤出ID相同的分类
        PrimaryActivityConfigDto dto = getRes.target().stream().filter(item -> item.getId().equals(id)).findFirst().orElse(null);
        //如果业务已经删除或者不存在该分类，就直接返回成功
        if (dto == null || dto.getStatus() == ActivityStatusEnum.DELETE.getStatus()) {
            return RpcResult.success();
        }
        //如果业务中存在该分类，则删除
        Result<Void> result = xmActivityClassRemote.deleteBigClass(id, operator);
        if (RpcResult.isFail(result)) {
            return RpcResult.fail(result.rCode(), result.getMessage());
        }
        log.info("删除活动大类成功, id: {}", id);
        return RpcResult.success();
    }


    @Override
    public Result<Void> deleteClass(Long id, String operator) {
        //预发环境不做操作
        if (!activityConfig.getEnableSyncCategory() && ConfigUtils.getEnvRequired() == Env.PRE) {
            return RpcResult.success();
        }
        //先查询一下是否业务中不存在该分类
        Result<List<SecondaryActivityConfigDto>> getRes = xmActivityClassRemote.getClassById(Lists.newArrayList(id));
        if (RpcResult.isFail(getRes)) {
            return RpcResult.fail(getRes.rCode(), getRes.getMessage());
        }

        if (CollectionUtils.isEmpty(getRes.target())) {
            return RpcResult.success();
        }

        //过滤出ID相同的分类
        SecondaryActivityConfigDto dto = getRes.target().stream().filter(item -> item.getId().equals(id)).findFirst().orElse(null);
        //如果业务已经删除或者不存在该分类，就直接返回成功
        if (dto == null || dto.getStatus() == ActivityStatusEnum.DELETE.getStatus()) {
            return RpcResult.success();
        }
        //如果业务中存在该分类，则删除
        Result<Void> result = xmActivityClassRemote.deleteClass(id, operator);
        if (RpcResult.isFail(result)) {
            return RpcResult.fail(result.rCode(), result.getMessage());
        }
        log.info("删除活动二级分类成功, id: {}", id);
        return RpcResult.success();
    }


    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.XIMI;
    }

}
