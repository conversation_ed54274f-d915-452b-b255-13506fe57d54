package fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.GetFamilyLevelAwardItemBean;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.SaveFamilyLevelAwardItemBean;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestCreateFamilyLevelAwardRule;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilyLevelAwardItem;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR,
        imports = {
                ConfigUtils.class,
                Date.class,
        }
)
public interface FamilyLevelAwardItemConvert {

    FamilyLevelAwardItemConvert I = org.mapstruct.factory.Mappers.getMapper(FamilyLevelAwardItemConvert.class);

    default List<WcFamilyLevelAwardItem> toCreateEntities(RequestCreateFamilyLevelAwardRule request, Long ruleId) {
        List<SaveFamilyLevelAwardItemBean> beans = request.getItems();
        if (CollectionUtils.isEmpty(beans)) {
            return Collections.emptyList();
        }
        ArrayList<WcFamilyLevelAwardItem> entities = new ArrayList<>(beans.size());
        for (SaveFamilyLevelAwardItemBean bean : beans) {
            entities.add(toCreateEntity(bean, request.getAppId(), ruleId, request.getOperator()));
        }
        return entities;
    }

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "resourceNumber", source = "bean.resourceNumber", defaultValue = "0")
    @Mapping(target = "resourceValidPeriod", source = "bean.resourceValidPeriod", defaultValue = "0")
    @Mapping(target = "resourceId", source = "bean.resourceId", defaultValue = "0L")
    @Mapping(target = "deleted", constant = "false")
    @Mapping(target = "createTime", expression = "java(new Date())")
    @Mapping(target = "creator", source = "operator")
    @Mapping(target = "modifyTime", expression = "java(new Date())")
    @Mapping(target = "modifier", source = "operator")
    WcFamilyLevelAwardItem toCreateEntity(SaveFamilyLevelAwardItemBean bean, Integer appId, Long ruleId, String operator);

    @BeanMapping(unmappedTargetPolicy = ReportingPolicy.IGNORE, unmappedSourcePolicy = ReportingPolicy.ERROR)
    @Mapping(target = "resourceNumber", source = "bean.resourceNumber", defaultValue = "0")
    @Mapping(target = "resourceValidPeriod", source = "bean.resourceValidPeriod", defaultValue = "0")
    @Mapping(target = "resourceId", source = "bean.resourceId", defaultValue = "0L")
    @Mapping(target = "deleted", constant = "false")
    @Mapping(target = "modifyTime", expression = "java(new Date())")
    @Mapping(target = "modifier", source = "operator")
    WcFamilyLevelAwardItem toUpdateEntity(SaveFamilyLevelAwardItemBean bean, Long id, String operator);

    List<GetFamilyLevelAwardItemBean> toGetBeans(List<WcFamilyLevelAwardItem> entities);
}
