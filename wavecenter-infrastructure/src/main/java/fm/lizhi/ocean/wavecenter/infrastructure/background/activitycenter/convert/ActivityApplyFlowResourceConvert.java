package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityFlowResourceAuditBean;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityApplyFlowResource;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActivityApplyFlowResourceConvert {
    ActivityApplyFlowResourceConvert I = Mappers.getMapper(ActivityApplyFlowResourceConvert.class);

    @Mapping(target = "resourceConfigId", source = "flowResourceId")
    ActivityApplyFlowResource toActivityFlowResource(ActivityFlowResourceAuditBean flowResources);

    List<ActivityApplyFlowResource> toActivityFlowResourceList(List<ActivityFlowResourceAuditBean> flowResources);

}

