package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert;

import fm.lizhi.hy.amusement.protocol.DressUpInfoProto;
import fm.lizhi.hy.vip.protocol.HyMedalBaseV2Proto;
import fm.lizhi.live.pp.idl.officialCertifiedTag.dto.tag.OfficialCertifiedTagDTO;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.DecorateEnum;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.constants.DecorateMapping;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.DecorateDTO;
import fm.lizhi.xm.vip.bean.decorate.resp.PageDecorateDto;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        imports = {DecorateEnum.class, UrlUtils.class})
public interface ActivityDecorateConvert {
    ActivityDecorateConvert I = Mappers.getMapper(ActivityDecorateConvert.class);

    @Named("convertDecorateType")
    default Integer convertDecorateType(Integer type, @Context BusinessEvnEnum businessEvnEnum) {
        if (null == type){
            return null;
        }

        return DecorateMapping.bizValue2WaveType(type, businessEvnEnum);
    }

    @Mappings({
            @Mapping(source = "thumbUrl", target = "previewUrl"),
            @Mapping(source = "type", target = "type", qualifiedByName = "convertDecorateType"),
    })
    DecorateDTO xmGetDecorateResp2DecorateDTO(PageDecorateDto dto, @Context BusinessEvnEnum businessEvnEnum);
    List<DecorateDTO> xmGetDecorateListResp2DecorateDTO(List<PageDecorateDto> decorateList, @Context BusinessEvnEnum businessEvnEnum);




    @Mappings({
            @Mapping(source = "dressUpInfo.materialUrl", target = "previewUrl"),
            @Mapping(source = "dressUpInfo.dressUpName", target = "name"),
            @Mapping(source = "dressUpInfo.dressUpType", target = "type", qualifiedByName = "convertDecorateType"),
    })
    DecorateDTO hyResponseGetDressUpList2DecorateDTO(DressUpInfoProto.DressUpInfo dressUpInfo, @Context BusinessEvnEnum businessEvnEnum);
    List<DecorateDTO> hyResponseGetDressUpList2DecorateDTO(List<DressUpInfoProto.DressUpInfo> dressUpInfoList, @Context BusinessEvnEnum businessEvnEnum);


    @Mappings({
            @Mapping(source = "thumbUrl", target = "previewUrl"),
            @Mapping(source = "type", target = "type", qualifiedByName = "convertDecorateType"),
    })
    DecorateDTO ppGetDecorateResp2DecorateDTO(fm.lizhi.pp.vip.bean.resp.PageDecorateDto decorate, @Context BusinessEvnEnum businessEvnEnum);
    List<DecorateDTO> ppGetDecorateListResp2DecorateDTO(List<fm.lizhi.pp.vip.bean.resp.PageDecorateDto> pageDecorates, @Context BusinessEvnEnum businessEvnEnum);

    @Mappings({
            @Mapping(target = "previewUrl", expression = "java(UrlUtils.addCdnHost(cdnHost, medalInfo.getImageUrl()))"),
            @Mapping(source = "medalInfo.medalName", target = "name"),
            @Mapping(target = "type", expression = "java(DecorateEnum.MEDAL.getType())"),
    })
    DecorateDTO convertToDecorateInfoBean(HyMedalBaseV2Proto.MedalInfoV2 medalInfo, @Context String cdnHost);
    List<DecorateDTO> convertToDecorateInfoBean(List<HyMedalBaseV2Proto.MedalInfoV2> medalInfoList, @Context String cdnHost);


    @Mappings({
            @Mapping(target = "previewUrl", expression = "java(UrlUtils.addCdnHost(cdnHost, officialCertifiedTagDTO.getTagImage()))"),
            @Mapping(target = "type", expression = "java(DecorateEnum.USER_GLORY.getType())"),
    })
    DecorateDTO hyResponseOfficialCertified2DecorateDTO(OfficialCertifiedTagDTO officialCertifiedTagDTO, @Context String cdnHost);
    List<DecorateDTO> hyResponseOfficialCertifiedList2DecorateDTO(List<OfficialCertifiedTagDTO> list,  @Context String cdnHost);
}
