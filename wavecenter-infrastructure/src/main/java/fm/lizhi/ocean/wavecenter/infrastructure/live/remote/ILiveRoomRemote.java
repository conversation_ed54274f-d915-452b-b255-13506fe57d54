package fm.lizhi.ocean.wavecenter.infrastructure.live.remote;

import fm.lizhi.ocean.wavecenter.api.live.constants.RoomCategoryEnum;
import fm.lizhi.ocean.wavecenter.base.remote.IRemote;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/5/9 15:46
 */
public interface ILiveRoomRemote extends IRemote {

    /**
     * 查询用户厅品类
     * @param userId
     * @return
     */
    Optional<RoomCategoryEnum> getUserRoomCategory(Long userId);



}
