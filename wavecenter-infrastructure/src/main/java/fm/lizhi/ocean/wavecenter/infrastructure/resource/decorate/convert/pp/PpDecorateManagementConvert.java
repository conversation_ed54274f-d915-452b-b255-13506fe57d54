package fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.convert.pp;

import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.pp.PpCreateAvatarWidgetBean;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.pp.PpCreateRoomBackgroundBean;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestCreateAvatarWidget;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestCreateRoomBackground;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface PpDecorateManagementConvert {

    PpDecorateManagementConvert I = Mappers.getMapper(PpDecorateManagementConvert.class);

    PpCreateRoomBackgroundBean toPpCreateRoomBackgroundBean(RequestCreateRoomBackground request);

    PpCreateAvatarWidgetBean toPpCreateAvatarWidgetBean(RequestCreateAvatarWidget request);
}
