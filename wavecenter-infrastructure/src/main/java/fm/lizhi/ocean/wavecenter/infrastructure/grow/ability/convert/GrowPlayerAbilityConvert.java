package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.convert;

import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowPlayerAbilityWeekCapability;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.PlayerAbilityWeekCapabilityDTO;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/11 10:08
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface GrowPlayerAbilityConvert {

    GrowPlayerAbilityConvert I = Mappers.getMapper(GrowPlayerAbilityConvert.class);

    PlayerAbilityWeekCapabilityDTO abilityWeekCapability2DTO(WcGrowPlayerAbilityWeekCapability dto);

    List<PlayerAbilityWeekCapabilityDTO> abilityWeekCapabilitys2DTOs(List<WcGrowPlayerAbilityWeekCapability> dtos);

}
