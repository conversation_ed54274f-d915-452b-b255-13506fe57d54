package fm.lizhi.ocean.wavecenter.infrastructure.grow.repository;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.FamilyLevel;
import fm.lizhi.ocean.wavecenter.domain.grow.repository.FamilyLevelRepository;
import fm.lizhi.ocean.wavecenter.domain.system.Visitor;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.convert.FamilyLevelConfigConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.datastore.entity.WcFamilyLevelAward;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.datastore.entity.WcFamilyLevelAwardExample;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.datastore.entity.WcFamilyLevelConfig;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.datastore.entity.WcFamilyLevelConfigExample;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.datastore.mapper.WcFamilyLevelAwardMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.datastore.mapper.WcFamilyLevelConfigMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/18 18:03
 */
@Component
public class FamilyLevelRepositoryImpl implements FamilyLevelRepository {

    @Autowired
    private WcFamilyLevelConfigMapper familyLevelConfigMapper;
    @Autowired
    private WcFamilyLevelAwardMapper familyLevelAwardMapper;
    @Autowired
    private FamilyLevelConfigConvert familyLevelConfigConvert;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(FamilyLevel familyLevel, Visitor visitor) {

        WcFamilyLevelConfig po = familyLevelConfigConvert.entity2Po(familyLevel);
        po.setAppId(visitor.getAppId());
        po.setModifyUser(visitor.getName());

        WcFamilyLevelConfig idExist = familyLevelConfigMapper.selectByPrimaryKey(WcFamilyLevelConfig.builder()
                .id(familyLevel.getId())
                .build());
        if (idExist == null) {
            // 新增
            po.setDeployEnv(ConfigUtils.getEnv().name());
            po.setCreateTime(new Date());
            familyLevelConfigMapper.insert(po);
        } else {
            // 更新
            po.setUpdateTime(new Date());
            po.setDeleted(0);
            familyLevelConfigMapper.updateByPrimaryKey(po);

            // 删除已关联的宣传图
            WcFamilyLevelAwardExample example = new WcFamilyLevelAwardExample();
            example.createCriteria()
                    .andAppIdEqualTo(visitor.getAppId())
                    .andLevelIdEqualTo(familyLevel.getId());
            familyLevelAwardMapper.deleteByExample(example);
        }

        updateFamilyLevelAward(familyLevel.getFamilyLevelMedia().getAwardImgs(), familyLevel.getId(), visitor.getAppId());
    }

    @Override
    public List<FamilyLevel> getAppFamilyLevels(Integer appId) {
        WcFamilyLevelConfigExample example = new WcFamilyLevelConfigExample();
        example.setOrderByClause("level_value");
        example.createCriteria()
                .andDeployEnvEqualTo(ConfigUtils.getEnv().name())
                .andDeletedEqualTo(0)
                .andAppIdEqualTo(appId);

        List<WcFamilyLevelConfig> configList = familyLevelConfigMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(configList)) {
            return Collections.emptyList();
        }

        return configList.stream().map(this::toFamilyLevel).collect(Collectors.toList());
    }

    @Override
    public Optional<FamilyLevel> getLevel(Integer appId, Long levelId) {
        WcFamilyLevelConfig param = new WcFamilyLevelConfig();
        param.setAppId(appId);
        param.setId(levelId);
        param.setDeployEnv(ConfigUtils.getEnv().name());
        param.setDeleted(0);

        List<WcFamilyLevelConfig> configList = familyLevelConfigMapper.selectMany(param);
        if (CollectionUtils.isEmpty(configList)) {
            return Optional.empty();
        }

        return Optional.of(toFamilyLevel(configList.get(0)));
    }

    private FamilyLevel toFamilyLevel(WcFamilyLevelConfig config) {
        return new FamilyLevel(config.getId()
                , config.getAppId()
                , config.getLevelName()
                , config.getMinExp());
    }

    private void updateFamilyLevelAward(List<String> awardImgs, Long levelId, Integer appId){
        if (CollectionUtils.isEmpty(awardImgs)) {
            return;
        }
        List<WcFamilyLevelAward> poList = new ArrayList<>();
        for (String awardImg : awardImgs) {
            WcFamilyLevelAward po = WcFamilyLevelAward.builder()
                    .appId(appId)
                    .levelId(levelId)
                    .awardImg(UrlUtils.removeHostOrEmpty(awardImg))
                    .createTime(new Date())
                    .updateTime(new Date())
                    .build();
            poList.add(po);
        }
        familyLevelAwardMapper.batchInsert(poList);
    }

}
