package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.convert;

import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.*;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.IncomeSummaryDTO;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface RoomDataConvert {

    RoomDataConvert I = Mappers.getMapper(RoomDataConvert.class);

    /**
     * 转换厅收入信息统计
     *
     * @param statPo 统计结果
     * @return 结果
     */
    IncomeSummaryDTO convertRoomDayInfoStat(WcDataRoomDayStatPo statPo);

    /**
     * 转换厅收入信息统计
     *
     * @param statPo 统计结果
     * @return 结果
     */
    IncomeSummaryDTO convertRoomWeekInfoStat(WcDataRoomWeekStatPo statPo);

    /**
     * 转换厅收入信息统计
     *
     * @param statPo 统计结果
     * @return 结果
     */
    IncomeSummaryDTO convertRoomMonthInfoStat(WcDataRoomMonthStatPo statPo);
}
