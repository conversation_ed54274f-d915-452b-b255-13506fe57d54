package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.manager;

import cn.hutool.core.collection.CollUtil;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.DecorateBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestBatchGetDecorate;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.DecorateInfoBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityApplyDecorateDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityApplyManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.convert.ActivityDecorateConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.remote.IDecorateRemote;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityDecorateManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.request.RequestGetDecorate;
import fm.lizhi.ocean.wavecenter.service.resource.decorate.manager.DecorateManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityDecorateManagerImpl implements ActivityDecorateManager {

    @Autowired
    private IDecorateRemote decorateRemote;

    @Autowired
    private CommonConfig commonConfig;

    @Autowired
    private ActivityApplyManager activityApplyManager;

    @Autowired
    private DecorateManager decorateManager;

    @Override
    public Result<PageBean<DecorateBean>> getDecorateList(RequestGetDecorate param) {

        PageBean<DecorateInfoBean> decorateInfoBeanPageBeans = decorateManager.getDecorates(PlatformDecorateTypeEnum.getByType(param.getType()),
                                        param.getDressUpId(), param.getName(), param.getPageNo(), param.getPageSize());


        List<DecorateBean> decorateBeans = ActivityDecorateConvert.I.decorateInfoBean2DecorateBeans(decorateInfoBeanPageBeans.getList());
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, PageBean.of(decorateInfoBeanPageBeans.getTotal(), decorateBeans));
    }

    @Override
    public Result<List<DecorateBean>> batchGetDecorateList(RequestBatchGetDecorate param) {
        if (log.isDebugEnabled()) {
            log.debug("batchGetDecorateList param={}", JsonUtil.dumps(param));
        }

        List<DecorateInfoBean> decorateInfoBeans = decorateManager.batchGetDecorates(PlatformDecorateTypeEnum.getByType(param.getType()), param.getIds());

        List<DecorateBean> decorateBeans = ActivityDecorateConvert.I.decorateInfoBean2DecorateBeans(decorateInfoBeans);
        if (CollUtil.isEmpty(decorateBeans)) {
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, Collections.emptyList());
        }

        if (log.isDebugEnabled()) {
            log.debug("batchGetDecorateList result={}", JsonUtil.dumps(decorateBeans));
        }

        Map<String, DecorateBean> map = decorateBeans.stream()
                .filter(Objects::nonNull).filter(decorateBean -> decorateBean.getId() != null)
                .collect(Collectors.toMap(DecorateBean::getId, Function.identity()));
        List<DecorateBean> res = new ArrayList<>(decorateBeans.size());
        for (Long id : param.getIds()) {
            DecorateBean decorateBean = map.get(String.valueOf(id));
            if (decorateBean == null) {
                continue;
            }
            //按照请求的顺序返回
            decorateBean.setPreviewUrl(UrlUtils.addHostOrEmpty(decorateBean.getPreviewUrl(), commonConfig.getBizConfig().getCdnHost()));
            res.add(decorateBean);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, res);
    }

    @Override
    public DecorateBean getDecorateById(Long id, int appId, Integer type) {

        RequestBatchGetDecorate request = RequestBatchGetDecorate.builder()
                .appId(appId)
                .type(type)
                .ids(Collections.singletonList(id))
                .build();

        Result<List<DecorateBean>> result = batchGetDecorateList(request);
        if (RpcResult.isFail(result)) {
            return null;
        }

        return CollUtil.getFirst(result.target());
    }

    @Override
    public List<DecorateBean> getDecorateListById(Long activityId, int appId) {
        List<ActivityApplyDecorateDTO> decorateDTOS = activityApplyManager.getDecorateListByActivityId(activityId);
        if (CollectionUtils.isEmpty(decorateDTOS)) {
            return Collections.emptyList();
        }

        Map<Integer, List<Long>> typeMap = decorateDTOS.stream()
                .collect(Collectors.groupingBy(ActivityApplyDecorateDTO::getDecorateType
                        , Collectors.mapping(ActivityApplyDecorateDTO::getDecorateId, Collectors.toList())
                ));

        List<DecorateBean> decorateBeans = new ArrayList<>();
        for (Map.Entry<Integer, List<Long>> entry : typeMap.entrySet()) {
            RequestBatchGetDecorate request = RequestBatchGetDecorate.builder()
                    .appId(appId)
                    .type(entry.getKey())
                    .ids(entry.getValue())
                    .build();

            Result<List<DecorateBean>> result = batchGetDecorateList(request);
            if (RpcResult.isFail(result)) {
                continue;
            }
            decorateBeans.addAll(result.target());
        }
        return decorateBeans;
    }
}
