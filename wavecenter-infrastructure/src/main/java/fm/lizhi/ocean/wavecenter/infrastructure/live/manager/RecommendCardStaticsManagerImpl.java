package fm.lizhi.ocean.wavecenter.infrastructure.live.manager;

import fm.lizhi.ocean.wavecenter.api.live.response.ResponseRecommendCardStaticsInfo;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataRecommendCard;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataRecommendCardExample;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.mapper.WcDataRecommendCardMapper;
import fm.lizhi.ocean.wavecenter.service.live.manager.RecommendCardStaticsManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 推荐卡统计数据管理实现类
 */
@Component
public class RecommendCardStaticsManagerImpl implements RecommendCardStaticsManager {

    @Autowired
    private WcDataRecommendCardMapper wcDataRecommendCardMapper;

    @Override
    public List<ResponseRecommendCardStaticsInfo> getRecommendCardStatics(int appId, List<Long> recommendCardRecordIds) {
        if (CollectionUtils.isEmpty(recommendCardRecordIds)) {
            return new ArrayList<>();
        }

        // 1.根据传入的list查询表wavecenter_data_recommend_card，list的id对应字段use_record_id
        WcDataRecommendCardExample example = new WcDataRecommendCardExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andUseRecordIdIn(recommendCardRecordIds);
        List<WcDataRecommendCard> cardList = wcDataRecommendCardMapper.selectByExample(example);

        if (CollectionUtils.isEmpty(cardList)) {
            return new ArrayList<>();
        }

        // 2.根据before_half_count与use_count计算曝光率，计算公式为(use_count-before_half_count)/before_half_count
        DecimalFormat df = new DecimalFormat("+0%;-0%");
        return cardList.stream()
                .map(card -> {
                    int useCount = card.getUseCount() == null ? 0 : card.getUseCount();
                    int beforeHalfCount = card.getBeforeHalfCount() == null ? 0 : card.getBeforeHalfCount();

                    ResponseRecommendCardStaticsInfo info = new ResponseRecommendCardStaticsInfo();
                    info.setId(card.getUseRecordId());

                    // 计算曝光值：use_count-before_half_count
                    int useResult = useCount - beforeHalfCount;
                    info.setUseCount(useResult);

                    // 计算曝光率: (use_count-before_half_count)/before_half_count
                    if (beforeHalfCount > 0) {
                        double rate = (double) (useResult) / beforeHalfCount;
                        if (rate < 0.05) {
                            rate = 0.05;
                        }
                        info.setUseCountRate(df.format(rate));
                    } else {
                        info.setUseCountRate("");
                    }

                    return info;
                })
                .collect(Collectors.toList());
    }
} 