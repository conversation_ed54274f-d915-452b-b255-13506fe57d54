package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.dao;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.csp.sentinel.util.AssertUtil;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.api.live.constants.RoomCategoryOptionConstants;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert.ActivityNoticeConfigConvert;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityNoticeCategoryRelation;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityNoticeCategoryRelationExample;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityNoticeConfig;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityNoticeConfigExample;
import fm.lizhi.ocean.wavecenter.datastore.background.mapper.ActivityNoticeCategoryRelationMapper;
import fm.lizhi.ocean.wavecenter.datastore.background.mapper.ActivityNoticeConfigMapper;
import fm.lizhi.ocean.wavecenter.service.common.manager.IdManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class ActivityNoticeDao {

    @Autowired
    private ActivityNoticeConfigMapper activityNoticeConfigMapper;

    @Autowired
    private ActivityNoticeCategoryRelationMapper activityNoticeCategoryRelationMapper;

    @Autowired
    private IdManager idManager;


    @Transactional
    public Long insertOrUpdate(ActivityNoticeConfig config, List<Integer> categoryList) {

        boolean success;
        if (config.getId() == null || config.getId() < 0) {
            config.setId(idManager.genId());
            success = activityNoticeConfigMapper.insert(config) > 0;
        } else {
            success = activityNoticeConfigMapper.updateByPrimaryKey(config) > 0;
        }

        if (success){
            ActivityNoticeCategoryRelationExample example = new ActivityNoticeCategoryRelationExample();
            example.createCriteria()
                    .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                    .andNoticeIdEqualTo(config.getId());

            activityNoticeCategoryRelationMapper.deleteByExample(example);

            List<ActivityNoticeCategoryRelation> relationList = categoryList.stream()
                    .map(category -> ActivityNoticeConfigConvert.I.buildActivityNoticeCategoryRelation(config, category))
                    .collect(Collectors.toList());
            boolean insertCategoryRes = activityNoticeCategoryRelationMapper.batchInsert(relationList) > 0;
            AssertUtil.assertState(insertCategoryRes, "写入品类关系失败");
            return config.getId();
        }
        return null;

    }

    @Transactional
    public boolean deleteNoticeConfig(Long id) {
        ActivityNoticeConfig param = new ActivityNoticeConfig();
        param.setId(id);
        boolean success = activityNoticeConfigMapper.deleteByPrimaryKey(param) > 0;
        AssertUtil.assertState(success, "删除公告失败");

        ActivityNoticeCategoryRelationExample example = new ActivityNoticeCategoryRelationExample();
        example.createCriteria()
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andNoticeIdEqualTo(id);
        activityNoticeCategoryRelationMapper.deleteByExample(example);
        return true;
    }

    /**
     * 查询活动公告关系
     * @param appId
     * @param category
     * @return
     */
    public List<ActivityNoticeCategoryRelation> selectNoticeConfigRelation(Integer appId, Integer category) {
        ActivityNoticeCategoryRelationExample example = new ActivityNoticeCategoryRelationExample();
        example.createCriteria().andAppIdEqualTo(appId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andCategoryValueIn(CollUtil.newArrayList(category, RoomCategoryOptionConstants.UNLIMITED_VALUE));
        return activityNoticeCategoryRelationMapper.selectByExample(example);
    }


    /**
     * 查询活动公告配置
     * @param id
     * @return
     */
    public ActivityNoticeConfig selectNoticeConfigById(Long id) {
        ActivityNoticeConfig param = new ActivityNoticeConfig();
        param.setId(id);
        return activityNoticeConfigMapper.selectByPrimaryKey(param);
    }

    /**
     * 查询活动公告关系
     * @param appId
     * @param noticeId
     * @return
     */
    public List<ActivityNoticeCategoryRelation> selectNoticeCategoryRelation(Integer appId, Long noticeId) {
        return selectNoticeCategoryRelation(appId, CollUtil.newArrayList(noticeId));
    }

    public List<ActivityNoticeCategoryRelation> selectNoticeCategoryRelation(Integer appId, List<Long> noticeIdList) {
        ActivityNoticeCategoryRelationExample example = new ActivityNoticeCategoryRelationExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andNoticeIdIn(noticeIdList)
        ;
        return activityNoticeCategoryRelationMapper.selectByExample(example);
    }

    /**
     * 查询平台下所有公告
     * @param appId
     * @return
     */
    public List<ActivityNoticeConfig> selectAllNoticeConfigList(Integer appId) {
        ActivityNoticeConfigExample example = new ActivityNoticeConfigExample();
        example.createCriteria()
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andAppIdEqualTo(appId);
        example.setOrderByClause("create_time desc");
        return activityNoticeConfigMapper.selectByExample(example);
    }
}
