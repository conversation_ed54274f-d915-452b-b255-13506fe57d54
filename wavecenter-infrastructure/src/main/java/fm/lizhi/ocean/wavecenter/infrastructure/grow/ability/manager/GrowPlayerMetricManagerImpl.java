package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.manager;

import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.convert.GrowPlayerMetricConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowPlayerMetricValue;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowPlayerMetricValueExample;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.mapper.WcGrowPlayerMetricValueMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.mapper.ext.WcGrowPlayerMetricValueExtMapper;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.PlayerDataForGrowDTO;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.GrowPlayerMetricValueDTO;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.SettlePeriodDTO;
import fm.lizhi.ocean.wavecenter.service.grow.ability.manager.GrowPlayerMetricManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/10 14:37
 */
@Component
public class GrowPlayerMetricManagerImpl implements GrowPlayerMetricManager {

    @Autowired
    private WcGrowPlayerMetricValueMapper growPlayerMetricValueMapper;
    @Autowired
    private FamilyManager familyManager;
    @Autowired
    private WcGrowPlayerMetricValueExtMapper playerMetricValueExtMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void savePlayerMetric(PlayerDataForGrowDTO data, SettlePeriodDTO settlePeriodDTO) {
        UserInFamilyBean userInFamily = familyManager.getUserInFamily(data.getPlayerId());
        Long familyId = userInFamily.getFamilyId() != null ? userInFamily.getFamilyId() : 0;
        Long roomId = userInFamily.getNjId() != null ? userInFamily.getNjId() : 0;

        // 构建查询条件
        WcGrowPlayerMetricValueExample example = new WcGrowPlayerMetricValueExample();
        example.createCriteria()
            .andAppIdEqualTo(ContextUtils.getBusinessEvnEnum().getAppId())
            .andPlayerIdEqualTo(data.getPlayerId())
            .andStartWeekDateEqualTo(settlePeriodDTO.getStartDate())
            .andEndWeekDateEqualTo(settlePeriodDTO.getEndDate());

        // 查询现有记录
        List<WcGrowPlayerMetricValue> existingMetrics = growPlayerMetricValueMapper.selectByExample(example);
        boolean exists = CollectionUtils.isNotEmpty(existingMetrics);

        // 构造实体
        WcGrowPlayerMetricValue entity = GrowPlayerMetricConvert.I.playerDataForGrowDTO2WcGrowPlayerMetricValue(data);
        entity.setAppId(ContextUtils.getBusinessEvnEnum().getAppId());
        entity.setStartWeekDate(settlePeriodDTO.getStartDate());
        entity.setEndWeekDate(settlePeriodDTO.getEndDate());
        entity.setModifyTime(new Date());

        if (exists) {
            // 更新逻辑
            WcGrowPlayerMetricValue existing = existingMetrics.get(0);
            entity.setId(existing.getId());
            entity.setCreateTime(existing.getCreateTime()); // 保留原有创建时间
            growPlayerMetricValueMapper.updateByPrimaryKey(entity);
        } else {
            // 新增逻辑
            entity.setRoomId(roomId);
            entity.setFamilyId(familyId);
            entity.setCreateTime(new Date());
            growPlayerMetricValueMapper.insert(entity);
        }
    }

    @Override
    public GrowPlayerMetricValueDTO queryPlayerMetricSum(Long roomId, SettlePeriodDTO settlePeriodDTO) {
        GrowPlayerMetricValueDTO sumDto = new GrowPlayerMetricValueDTO();
        Long minPlayerId = 0L;
        int pageSize = 100;
        
        while (true) {
            List<WcGrowPlayerMetricValue> metrics = playerMetricValueExtMapper.selectByRoomAndPeriodWithCursor(
                roomId,
                settlePeriodDTO.getStartDate(),
                settlePeriodDTO.getEndDate(),
                minPlayerId,
                pageSize,
                ContextUtils.getBusinessEvnEnum().getAppId()
            );
            
            if (metrics.isEmpty()) {
                break;
            }
            
            // 聚合指标
            metrics.forEach(metric -> {
                sumDto.setChatUserCnt(intAdd(sumDto.getChatUserCnt(), metric.getChatUserCnt()));
                sumDto.setReplyChatUserCnt(intAdd(sumDto.getReplyChatUserCnt(), metric.getReplyChatUserCnt()));
                sumDto.setReplyChatNewUserCnt(intAdd(sumDto.getReplyChatNewUserCnt(), metric.getReplyChatNewUserCnt()));
                sumDto.setGiftUserCnt(intAdd(sumDto.getGiftUserCnt(), metric.getGiftUserCnt()));
                sumDto.setGiftNewUserCnt(intAdd(sumDto.getGiftNewUserCnt(), metric.getGiftNewUserCnt()));
                sumDto.setAllIncome(bigDecimalAdd(sumDto.getAllIncome(), metric.getAllIncome()));
                sumDto.setUpGuestDur(bigDecimalAdd(sumDto.getUpGuestDur(), metric.getUpGuestDur()));
                sumDto.setNewFansUserCnt(intAdd(sumDto.getNewFansUserCnt(), metric.getNewFansUserCnt()));
                sumDto.setViolationCnt(intAdd(sumDto.getViolationCnt(), metric.getViolationCnt()));
                sumDto.setCheckInCnt(intAdd(sumDto.getCheckInCnt(), metric.getCheckInCnt()));
            });
            
            // 更新游标
            minPlayerId = metrics.get(metrics.size() - 1).getPlayerId();
        }
        
        return sumDto;
    }

    private Integer intAdd(Integer a, Integer b){
        if (a == null) {
            return b;
        }
        if (b == null) {
            return a;
        }
        return a + b;
    }

    private BigDecimal bigDecimalAdd(BigDecimal a, BigDecimal b){
        if (a == null) {
            return b;
        }
        if (b == null) {
            return a;
        }
        return a.add(b);
    }


}
