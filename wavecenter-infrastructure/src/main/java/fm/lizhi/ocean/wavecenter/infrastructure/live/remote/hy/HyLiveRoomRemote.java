package fm.lizhi.ocean.wavecenter.infrastructure.live.remote.hy;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import fm.hy.family.api.RoomWhitelistService;
import fm.hy.family.protocol.RoomWhitelistProto;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.live.room.hy.enums.LiveRoomType;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.live.constants.RoomCategoryEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.live.remote.ILiveRoomRemote;
import fm.lizhi.ocean.wavecenter.service.live.config.LiveConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/5/9 15:48
 */
@Slf4j
@Component
public class HyLiveRoomRemote implements ILiveRoomRemote {

    @Autowired
    private RoomWhitelistService roomWhitelistService;

    @Autowired
    private LiveConfig liveConfig;

    @Override
    public Optional<RoomCategoryEnum> getUserRoomCategory(Long userId) {
        log.info("userId={}", userId);
        Result<RoomWhitelistProto.ResponseGetRoomWhitelistByUserId> result = roomWhitelistService.getRoomWhitelistByUserId(userId);
        if (RpcResult.isFail(result)) {
            log.warn("getRoomWhitelistByUserId fail. rCode={}", result.rCode());
            return Optional.empty();
        }

        List<RoomWhitelistProto.RoomWhitelistInfo> roomWhitelistInfoList = result.target().getRoomWhitelistInfoList();
        if (CollectionUtils.isEmpty(roomWhitelistInfoList)) {
            return Optional.empty();
        }

        int roomType = roomWhitelistInfoList.get(0).getRoomType();
        log.info("roomType={}", roomType);

        JSONObject categoryMapping = liveConfig.getHy().getCategoryMapping();
        Integer roomCategoryValue = categoryMapping.getInteger(String.valueOf(roomType));
        if (roomCategoryValue == null) {
            return Optional.empty();
        }

        RoomCategoryEnum categoryEnum = RoomCategoryEnum.getByValue(roomCategoryValue);
        return Optional.ofNullable(categoryEnum);
    }


    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.HEI_YE;
    }
}
