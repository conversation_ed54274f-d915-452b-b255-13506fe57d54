package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.dao;

import com.alibaba.csp.sentinel.util.AssertUtil;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.*;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.mapper.ActivityAnnouncementDeployRecordMapper;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.mapper.ActivityResourceGiveRecordMapper;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.UpdateDressUpStatusDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.UpdateFlowResourceStatusDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.UpdateResourceRecordInfoDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public class ActivityMaterielDao {

    @Autowired
    private ActivityAnnouncementDeployRecordMapper announcementDeployRecordMapper;

    @Autowired
    private ActivityResourceGiveRecordMapper activityResourceGiveRecordMapper;

    @Autowired
    private ActivityFlowResourceGiveDao activityFlowResourceGiveDao;

    @Autowired
    private ActivityResourceGiveDao activityResourceGiveDao;

    @Autowired
    private ActivityDressUpResourceGiveDao activityDressUpDao;

    /**
     * 初始化活动物料
     *
     * @param resourceGiveRecords     主资源记录
     * @param flowResourceGiveRecords 流量资源记录
     * @param dressUpGiveRecords      装扮资源
     * @param roomAnnouncement        房间公告
     */
    @Transactional(rollbackFor = Exception.class)
    public void initActivityMateriel(List<ActivityResourceGiveRecord> resourceGiveRecords,
                                     List<ActivityFlowResourceGiveRecord> flowResourceGiveRecords,
                                     List<ActivityDressUpGiveRecord> dressUpGiveRecords,
                                     ActivityAnnouncementDeployRecord roomAnnouncement) {
        boolean resourceRes = activityResourceGiveDao.batchSaveRecords(resourceGiveRecords);
        AssertUtil.assertState(resourceRes, "保存活动资源发放记录失败");
        boolean flowRes = activityFlowResourceGiveDao.batchSaveRecords(flowResourceGiveRecords);
        AssertUtil.assertState(flowRes, "保存流量资源发放记录失败");
        boolean dressRes = activityDressUpDao.batchSaveRecords(dressUpGiveRecords);
        AssertUtil.assertState(dressRes, "保存装扮资源发放记录失败");
        if (roomAnnouncement != null) {
            int row = announcementDeployRecordMapper.insert(roomAnnouncement);
            AssertUtil.assertState(row > 0, "保存房间公告失败");
        }
    }

    public boolean isActivityMaterielExist(Long activityId) {
        ActivityResourceGiveRecordExample activityResourceGiveRecordExample = new ActivityResourceGiveRecordExample();
        activityResourceGiveRecordExample.createCriteria().andActivityIdEqualTo(activityId);
        List<ActivityResourceGiveRecord> activityResourceGiveRecords = activityResourceGiveRecordMapper.selectByExample(activityResourceGiveRecordExample);

        ActivityAnnouncementDeployRecordExample announcementDeployRecordExample = new ActivityAnnouncementDeployRecordExample();
        announcementDeployRecordExample.createCriteria().andActivityIdEqualTo(activityId);
        List<ActivityAnnouncementDeployRecord> activityAnnouncementDeployRecords = announcementDeployRecordMapper.selectByExample(announcementDeployRecordExample);

        return CollectionUtils.isNotEmpty(activityResourceGiveRecords) || CollectionUtils.isNotEmpty(activityAnnouncementDeployRecords);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateResourceGive(UpdateResourceRecordInfoDTO resourceGiveRecord,
                                   List<UpdateFlowResourceStatusDTO> flowResourceGiveRecords,
                                   List<UpdateDressUpStatusDTO> dressUpGiveRecords) {
        // 更新主资源记录
        boolean resourceRes = activityResourceGiveDao.updateResourceRecordInfo(resourceGiveRecord);
        AssertUtil.assertState(resourceRes, "修改资源信息失败");
        boolean flowRes = activityFlowResourceGiveDao.batchUpdateFlowResourceStatus(flowResourceGiveRecords);
        AssertUtil.assertState(flowRes, "修改流量资源状态失败");
        boolean dressRes = activityDressUpDao.batchUpdateDressUpStatus(dressUpGiveRecords);
        AssertUtil.assertState(dressRes, "修改装扮资源状态失败");
    }
}
