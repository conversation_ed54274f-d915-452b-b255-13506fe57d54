package fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestCreateAvatarWidget;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestCreateRoomBackground;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.response.ResponseCreateAvatarWidget;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.response.ResponseCreateRoomBackground;
import fm.lizhi.ocean.wavecenter.base.remote.IRemote;

/**
 * 装扮管理后台远程接口
 */
public interface DecorateManagementRemote extends IRemote {

    /**
     * 创建直播间背景
     *
     * @param request 请求参数bean
     * @return 响应结果, 成功则包含创建的直播间背景id
     */
    Result<ResponseCreateRoomBackground> createRoomBackground(RequestCreateRoomBackground request);

    /**
     * 创建头像框
     *
     * @param request 请求参数bean
     * @return 响应结果, 成功则包含创建的头像框id
     */
    Result<ResponseCreateAvatarWidget> createAvatarWidget(RequestCreateAvatarWidget request);
}
