package fm.lizhi.ocean.wavecenter.infrastructure.resource.shortnumber.manager;

import fm.lizhi.ocean.wavecenter.service.resource.config.BizResourceConfig;
import fm.lizhi.ocean.wavecenter.service.resource.config.ResourceConfig;
import fm.lizhi.ocean.wavecenter.service.resource.shortnumber.dto.ShortNumberDTO;
import fm.lizhi.ocean.wavecenter.service.resource.shortnumber.manager.ShortNumberManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class ShortNumberManagerImpl implements ShortNumberManager {

    @Autowired
    private ResourceConfig resourceConfig;

    @Override
    public List<ShortNumberDTO> listShortNumber(int appId) {
        BizResourceConfig bizConfig = resourceConfig.getBizConfig(appId);
        if (bizConfig == null) {
            return Collections.emptyList();
        }
        return new ArrayList<>(bizConfig.getShortNumberConfigs());
    }

    @Override
    public ShortNumberDTO getShortNumber(int appId, long id) {
        BizResourceConfig bizConfig = resourceConfig.getBizConfig(appId);
        if (bizConfig == null) {
            return null;
        }
        for (ShortNumberDTO shortNumberConfig : bizConfig.getShortNumberConfigs()) {
            if (Objects.equals(id, shortNumberConfig.getId())) {
                return shortNumberConfig;
            }
        }
        return null;
    }
}
