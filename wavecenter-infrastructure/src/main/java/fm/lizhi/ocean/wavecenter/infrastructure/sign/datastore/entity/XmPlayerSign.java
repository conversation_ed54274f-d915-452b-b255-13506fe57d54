package fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity;

import lombok.*;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 陪玩签约记录
 *
 * @date 2024-04-25 05:37:45
 */
@Table(name = "`player_sign`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class XmPlayerSign {
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;


    /**
     * 家族id
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 房主id
     */
    @Column(name= "`nj_id`")
    private Long njId;

    /**
     * 陪玩用户ID
     */
    @Column(name= "`user_id`")
    private Long userId;

    /**
     * ADMIN:厅管侧 PLAYER:用户侧
     */
    @Column(name= "`user_type`")
    private String userType;

    /**
     * SIGN签约 CANCEL解约
     */
    @Column(name= "`type`")
    private String type;

    /**
     * WAIT_SIGN待签署 OVERDUE逾期未签 SIGN_SUCCEED签约成功 STOP_CONTRACT已解约
     */
    @Column(name= "`status`")
    private String status;

    /**
     * 签署截止时间
     */
    @Column(name= "`sign_deadline`")
    private Date signDeadline;

    /**
     * 签约生效开始时间
     */
    @Column(name= "`start_time`")
    private Date startTime;

    /**
     * 签约生效结束时间
     */
    @Column(name= "`end_time`")
    private Date endTime;

    /**
     * 主键ID
     */
    @Column(name= "`parent_id`")
    private Long parentId;

    /**
     * 解约时间
     */
    @Column(name= "`stop_time`")
    private Date stopTime;

    /**
     * 解约理由
     */
    @Column(name= "`cancel_reason`")
    private String cancelReason;

    /**
     * 额外信息
     */
    @Column(name= "`extra`")
    private String extra;

    /**
     * 签约申请发起时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", njId=").append(njId);
        sb.append(", familyId=").append(familyId);
        sb.append(", userId=").append(userId);
        sb.append(", userType=").append(userType);
        sb.append(", type=").append(type);
        sb.append(", status=").append(status);
        sb.append(", signDeadline=").append(signDeadline);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", parentId=").append(parentId);
        sb.append(", stopTime=").append(stopTime);
        sb.append(", extra=").append(extra);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}
