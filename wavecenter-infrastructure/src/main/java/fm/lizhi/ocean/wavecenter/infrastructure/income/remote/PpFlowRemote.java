package fm.lizhi.ocean.wavecenter.infrastructure.income.remote;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.api.income.bean.GetRoomSignRoomParamBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.dto.PageDto;
import fm.lizhi.ocean.wavecenter.infrastructure.common.manager.CreatorDataQueryCommonManager;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.LiveGiveGiftActionPo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.PpLiveGiveGiftActionMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.income.convert.IncomeInfraConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.income.remote.req.PersonalGiftflowReq;
import fm.lizhi.ocean.wavecenter.infrastructure.income.remote.req.RoomGiftflowReq;
import fm.lizhi.ocean.wavecenter.service.income.dto.GiveGiftFlowDto;
import fm.lizhi.ocean.wavecenter.service.income.dto.RoomRecFlowSumDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/4/24 15:14
 */
@Component
public class PpFlowRemote implements IFlowRemote{

    @Autowired
    private PpLiveGiveGiftActionMapper giveGiftActionMapper;
    @Autowired
    private CreatorDataQueryCommonManager creatorDataQueryCommonManager;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public PageDto<GiveGiftFlowDto> getRoomRecFlow(GetRoomSignRoomParamBean paramBean) {
        PageList<LiveGiveGiftActionPo> pagePoList = giveGiftActionMapper.getRoomRecFlow(paramBean, paramBean.getPageNo(), paramBean.getPageSize());
        List<GiveGiftFlowDto> dtoList = IncomeInfraConvert.I.liveGiveGiftActionPos2FlowDtos(pagePoList);
        return PageDto.of(pagePoList.getTotal(), dtoList);
    }

    @Override
    public RoomRecFlowSumDto getRoomRecFlowSum(GetRoomSignRoomParamBean paramBean) {
        return giveGiftActionMapper.getRoomRecFlowSum(paramBean);
    }

    @Override
    public PageDto<GiveGiftFlowDto> getPersonalGiftflow(PersonalGiftflowReq req) {
        PageList<LiveGiveGiftActionPo> pagePoList = giveGiftActionMapper.getPersonalGiftflow(req.getRecUserId()
                , req.getStartDate(), req.getEndDate(), req.getRecRoomId(), req.getSendUserId()
                , req.getPageNumber(), req.getPageSize());
        List<GiveGiftFlowDto> dtoList = IncomeInfraConvert.I.liveGiveGiftActionPos2FlowDtos(pagePoList);
        return PageDto.of(pagePoList.getTotal(), dtoList);
    }

    @Override
    public GiveGiftFlowDto getPersonalGiftflowSum(PersonalGiftflowReq req) {
        LiveGiveGiftActionPo po = giveGiftActionMapper.getPersonalGiftflowSum(req.getRecUserId()
                , req.getStartDate(), req.getEndDate(), req.getRecRoomId(), req.getSendUserId());
        return IncomeInfraConvert.I.liveGiveGiftActionPo2FlowDto(po);
    }

    @Override
    public PageDto<GiveGiftFlowDto> getRoomGiftflow(RoomGiftflowReq req) {
        PageList<LiveGiveGiftActionPo> pagePoList = giveGiftActionMapper.getRoomGiftflow(req.getRecRoomId()
                , req.getStartDate(), req.getEndDate(), req.getRecUserId(), req.getSendUserId()
                , req.getPageNumber(), req.getPageSize());
        List<GiveGiftFlowDto> dtoList = IncomeInfraConvert.I.liveGiveGiftActionPos2FlowDtos(pagePoList);
        return PageDto.of(pagePoList.getTotal(), dtoList);
    }

    @Override
    public GiveGiftFlowDto getRoomGiftflowSum(RoomGiftflowReq req) {
        LiveGiveGiftActionPo po = giveGiftActionMapper.getRoomGiftflowSum(req.getRecRoomId()
                , req.getStartDate(), req.getEndDate(), req.getRecUserId(), req.getSendUserId());
        return IncomeInfraConvert.I.liveGiveGiftActionPo2FlowDto(po);
    }

    @Override
    public Map<Long, String> getFlowRemark(Set<Long> flowIds) {
        return creatorDataQueryCommonManager.getAccountHistoryRemark(flowIds);
    }
}
