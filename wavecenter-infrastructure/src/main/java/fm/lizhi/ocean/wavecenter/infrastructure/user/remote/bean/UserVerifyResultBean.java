package fm.lizhi.ocean.wavecenter.infrastructure.user.remote.bean;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class UserVerifyResultBean {
 /**
      * 主键ID  
      */
      private Long id;
      /**
       * 记录ID
       */
      private Long recordId;
      /**
       * 用户ID
       */
      private Long userId;
      /**
       * 应用ID
       */
      private Integer appId;
      /**    
       * 子应用ID
       */
      private Integer subAppId;
      /**
       * 业务ID
       */
      private Integer bizId;
      /**
       * 证件类型
       */
      private Integer idCardType;
      /**
       * 姓名
       */
      private String name;
      /**
       * 证件号
       */
      private String idCardNumber;
      /**
       * 认证类型
       */
      private Integer verifyType;
      /**
       * 认证状态
       */
      private Integer verifyStatus;
      /**
       * 创建时间
       */
      private Long createTime;
      /**
       * 修改时间
       */
      private Long modifyTime;
      /**
       * 证件正面
       */
      private String idCardFront;
      /**
       * 证件反面
       */
      private String idCardBack;
      /**
       * 证件人像
       */
      private String idCardPerson;
      /**
       * 业务名称
       */
      private String bizName;
      /**
       * 手机号码
       */
      private String phoneNum;
      /**
       * 审核人
       */
      private String reviewer;
      /**
       * 审核备注
       */
      private String reviewNote;
      /**
       * 支付宝流水号
       */
      private String zfbBizNo;
      /**
       * 业务订单号
       */
      private String transactionId;
}
