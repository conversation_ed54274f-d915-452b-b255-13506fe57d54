package fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.api.live.bean.GuildRoomDayStats;
import fm.lizhi.ocean.wavecenter.api.live.bean.GuildRoomDayStatsSummaryRes;
import fm.lizhi.ocean.wavecenter.api.live.bean.RoomDayStatsSummaryRes;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.*;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.po.RoomCheckPlayerPo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 */
@DataStore(namespace = "mysql_ocean_wave_r")
public interface WaveRoomCheckInRecordMapper {

    /**
     * 统计用户子在时间段内的有效打卡厅数
     * @param appId
     * @param userId
     * @param startTime
     * @param endTime
     * @return
     */
    @Select({
            "<script>"
            , "select count(*)"
            , "from wave_check_in_record"
            , "where schedule_id in (select id from wave_check_in_schedule where start_time &gt;= #{startTime} and start_time &lt;= #{endTime} and app_id=#{appId})"
            , "  and user_id = #{userId}"
            , "  and status = 1"
            , "</script>"
    })
    Long countUserCheckIn(@Param("appId")Integer appId
            , @Param("userId")Long userId
            , @Param("startTime") Date startTime
            , @Param("endTime") Date endTime);

    /**
     * 明细 按天统计 厅的
     *
     * @param appId
     * @param njId
     * @param startDate
     * @param endDate
     * @return
     */
    @Select({
            "<script>" +
                    " select  " +
                    "       sum(income) as income, " +
                    "       sum(CASE WHEN a.`status` > 0  THEN 1 ELSE 0 END) as seatOrder, " +
                    "       sum( CASE WHEN charm_value = original_value THEN original_value ELSE charm_value END ) as charm,  " +
                    "       DATE(start_time) as statDate," +
                    "       sum(CASE WHEN a.`status` = 1 THEN 1 ELSE 0 END )  as checkPlayerNumber " +
                    "from wave_check_in_record a left join " +
                    "     wave_check_in_schedule b on a.schedule_id = b.id " +
                    "where b.start_time &gt;= #{startDate} and  b.start_time &lt;= #{endDate}  and b.app_id=  #{appId}  and b.nj_id= #{njId} GROUP BY DATE(b.start_time) " +
                    "</script>"
    })
    List<RoomDayCalendarEntity> roomCalendar(@Param("appId") Integer appId, @Param("njId") Long njId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);



    @Select({
            "<script>" +
                    " select " +
                    "       sum(income) as income," +
                    "       sum(CASE WHEN a.`status` > 0  THEN 1 ELSE 0 END) as seatOrder,  " +
                    "       sum( CASE WHEN charm_value = original_value THEN original_value ELSE charm_value END ) as charm, " +
                    "       sum( CASE WHEN user_id = host_id THEN 1 ELSE 0 END ) as hostCnt  " +
                    "from wave_check_in_record a left join " +
                    "     wave_check_in_schedule b on a.schedule_id = b.id " +
                    "where b.start_time &gt;= #{startDate} and  b.start_time &lt;= #{endDate}  and b.app_id=  #{appId}  and b.nj_id= #{njId}" +
                    "</script>"
    })
    RoomDayStatsSummaryRes roomDaySummary(@Param("appId") Integer appId, @Param("njId") Long njId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);




    @Select({
            "<script>" +
                    " select " +
                    "  a.user_id, " +
                    "       sum(income) as income," +
                    "       sum(CASE WHEN a.`status` > 0  THEN 1 ELSE 0 END) as seatOrder,  " +
                    "       sum( CASE WHEN charm_value = original_value THEN original_value ELSE charm_value END ) as charm, " +
                    "       sum( CASE WHEN user_id = host_id THEN 1 ELSE 0 END ) as hostCnt  " +
                    "from wave_check_in_record a left join " +
                    "     wave_check_in_schedule b on a.schedule_id = b.id " +
                    "where b.start_time &gt;= #{startDate} and  b.start_time &lt;= #{endDate}  and b.app_id=  #{appId}  and b.nj_id= #{njId} " +
                    "<if test=' null != userId and userId > 0 '>" +
                    " and  a.user_id  = #{userId} " +
                    "</if>" +
                    "group by a.user_id " +
                    "</script>"
    })
    List<RoomDayStatsEntity> roomDayStats(@Param("appId") Integer appId, @Param("njId") Long njId, @Param("userId") Long userId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);




    @Select({
            "<script>" +
                    " select  " +
                    "       a.user_id, " +
                    "       sum(income) as income, " +
                    "       sum(CASE WHEN a.`status` > 0  THEN 1 ELSE 0 END) as seatOrder, " +
                    "       sum( CASE WHEN charm_value = original_value THEN original_value ELSE charm_value END ) as charm,  " +
                    "       DATE(start_time) as statDate," +
                    "       sum( CASE WHEN user_id = host_id THEN 1 ELSE 0 END ) as hostCnt  " +
                    "from wave_check_in_record a left join " +
                    "     wave_check_in_schedule b on a.schedule_id = b.id " +
                    "where b.start_time &gt;= #{startDate} and  b.start_time &lt;= #{endDate}  and b.app_id=  #{appId}  and b.nj_id= #{njId}" +
                    "<if test=' null != userId and userId > 0 '>" +
                    " and  a.user_id  = #{userId} " +
                    "</if>" +
                    " GROUP BY a.user_id,DATE(b.start_time) " +
                    "</script>"
    })
    List<RoomDayStatsDetailEntity> roomDayStatsDetail(@Param("appId") Integer appId, @Param("njId") Long njId, @Param("userId") Long userId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);




    @Select({
            "<script>" +
                    " select DISTINCT(a.user_id)   " +
                    "from wave_check_in_record a left join  " +
                    "     wave_check_in_schedule b on a.schedule_id = b.id  " +
                    "where b.start_time &gt;= #{startDate} and  b.start_time &lt;= #{endDate}  and b.app_id=  #{appId}  and b.nj_id= #{njId}    " +
                    "<if test=' null != userId and userId > 0 '>" +
                    " and  a.user_id  = #{userId} " +
                    "</if>" +
                    "</script>"
    })
    List<Long> roomDayStatsUserId(@Param("appId") Integer appId, @Param("njId") Long njId, @Param("userId") Long userId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);





    @Select({
            "<script>" +
                    " select   " +
                    "       sum(income) as income, " +
                    "       sum(CASE WHEN a.`status` > 0  THEN 1 ELSE 0 END) as seatOrder, " +
                    "       sum( CASE WHEN charm_value = original_value THEN original_value ELSE charm_value END ) as charm, " +
                    "       sum(CASE WHEN a.`status` > 0 THEN 1 ELSE 0 END )  as checkInPlayer, " +
                    "       DATE(start_time) as statDate  ,  " +
                    "       nj_id, " +
                    "       #{familyId} as familyId  " +
                    "from wave_check_in_record a left join " +
                    "     wave_check_in_schedule b on a.schedule_id = b.id " +
                    "where b.start_time &gt;= #{startDate} and  b.start_time &lt;= #{endDate}  and b.app_id=  #{appId}   " +
                    " and b.family_id = #{familyId} " +

                    "<if test=' null != njId and njId > 0 '>" +
                    " and  b.nj_id  = #{njId} " +
                    "</if>" +
                    "   GROUP BY b.nj_id, DATE(b.start_time)  " +
                    "</script>"
    })
    List<WcRoomCheckInDayStats> guildRoomDayDetail(@Param("appId") Integer appId, @Param("familyId") Long familyId, @Param("njId") Long njId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);


    @Select({
            "<script>"
            ," select   "
            ,"       a.user_id, "
            ,"       DATE(b.start_time) as statDate,  "
            ,"       b.nj_id "
            ,"from wave_check_in_record a left join "
            ,"     wave_check_in_schedule b on a.schedule_id = b.id "
            ,"where b.start_time &gt;= #{startDate} and  b.start_time &lt;= #{endDate}  and b.app_id=#{appId} and a.status > 0 "
            ," and b.family_id = #{familyId} "
            ,"<if test=' null != njId and njId > 0 '>"
            ," and  b.nj_id = #{njId} "
            ,"</if>"
            ,"</script>"
    })
    List<RoomCheckPlayerPo> guildRoomDayDetailCheckPlayer(@Param("appId") Integer appId, @Param("familyId") Long familyId, @Param("njId") Long njId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);



    @Select({
            "<script>" +
                    " select DISTINCT(b.nj_id) from wave_check_in_schedule b where b.start_time &gt;= #{startDate} and  b.start_time &lt;= #{endDate}  and b.app_id=  #{appId}    " +
                    " and b.family_id = #{familyId} " +
                    "  and  app_id = #{appId}  " +
                    "<if test=' null != njId and njId > 0 '>" +
                    " and  b.nj_id  = #{njId} " +
                    "</if>" +
                    "</script>"
    })
    List<Long> guildRoomDayAllNjId(@Param("appId") Integer appId, @Param("familyId") Long familyId, @Param("njId") Long njId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);




    @Select({
            "<script>" +
                    " select   " +
                    "       sum(income) as income,  " +
                    "       sum(CASE WHEN a.`status` > 0  THEN 1 ELSE 0 END) as seatOrder,  " +
                    "       sum( CASE WHEN charm_value = original_value THEN original_value ELSE charm_value END ) as charm,  " +
                    "       sum(CASE WHEN a.`status` > 0 THEN 1 ELSE 0 END )  as checkPlayerNumber,  " +
                    "       nj_id  " +
                    "from wave_check_in_record a left join  " +
                    "     wave_check_in_schedule b on a.schedule_id = b.id  " +
                    "where b.start_time &gt;= #{startDate} and  b.start_time &lt;= #{endDate}  and b.app_id=  #{appId}    " +
                    " and b.family_id = #{familyId} " +
                    "<if test=' null != njId and njId > 0 '>" +
                    " and  b.nj_id  = #{njId} " +
                    "</if>" +
                    "  GROUP BY b.nj_id   " +
                    "</script>"
    })
    List<GuildRoomDayStats> guildRoomDayStats(@Param("appId") Integer appId, @Param("familyId") Long familyId, @Param("njId") Long njId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);



    @Select({
            "<script>" +
                    " select   " +
                    "       sum(income) as income,  " +
                    "       sum(CASE WHEN a.`status` > 0  THEN 1 ELSE 0 END) as seatOrder,  " +
                    "       sum( CASE WHEN charm_value = original_value THEN original_value ELSE charm_value END ) as charm,  " +
                    "       sum(CASE WHEN a.`status` = 1 THEN 1 ELSE 0 END )  as checkPlayerNumber " +
                    "from wave_check_in_record a left join  " +
                    "     wave_check_in_schedule b on a.schedule_id = b.id  " +
                    "where b.start_time &gt;= #{startDate} and  b.start_time &lt;= #{endDate}  and b.app_id=  #{appId}    " +
                    " and b.family_id = #{familyId} " +
                    "<if test=' null != njId and njId > 0 '>" +
                    " and  b.nj_id  = #{njId} " +
                    "</if>" +
                    "</script>"
    })
    GuildRoomDayStatsSummaryRes guildRoomDaySummary(@Param("appId") Integer appId, @Param("familyId") Long familyId, @Param("njId") Long njId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Select({
            "<script>" +
                    "select app_id, family_id, host_id, a.room_id, user_id, nj_id, income, " +
                    "a.`status`, charm_value, original_value as charm , start_time, end_time, " +
                    " (CASE WHEN user_id = host_id THEN 1 ELSE 0 END) as is_host, " +
                    " b.remark,a.create_time, a.modify_time" +
                    " from wave_check_in_schedule b inner join " +
                    " wave_check_in_record a on a.schedule_id = b.id " +
                    " where b.nj_id =#{njId} and  b.start_time &gt;= #{startDate} and  b.start_time &lt;= #{endDate} and b.app_id=#{appId}" +
                    "</script>"
    })
    List<WaveCheckInRecordEntity> roomHourDetail(@Param("appId") Integer appId, @Param("njId") Long njId,
                                                 @Param("startDate") Date startDate, @Param("endDate") Date endDate);


}
