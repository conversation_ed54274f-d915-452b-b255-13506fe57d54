package fm.lizhi.ocean.wavecenter.infrastructure.live.remote.pp;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.live.room.pp.api.LiveAfficheService;
import fm.lizhi.live.room.pp.api.LiveNewService;
import fm.lizhi.live.room.pp.api.LiveRoomService;
import fm.lizhi.live.room.pp.protocol.LiveAfficheProto;
import fm.lizhi.live.room.pp.protocol.LiveNewProto;
import fm.lizhi.live.room.pp.protocol.LiveRoomProto;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.live.dto.LiveDto;
import fm.lizhi.ocean.wavecenter.infrastructure.live.remote.ILiveRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.live.request.EditRoomNoticeRequest;
import fm.lizhi.ocean.wavecenter.infrastructure.live.request.GetRoomInfoByNjIdResponse;
import fm.lizhi.ocean.wavecenter.infrastructure.live.request.GetRoomNoticeRequest;
import fm.lizhi.ocean.wavecenter.infrastructure.live.request.GetRoomNoticeResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/8/26 14:37
 */
@Slf4j
@Component
public class PpLiveRemote implements ILiveRemote {

    @Autowired
    private LiveNewService liveNewService;

    @Autowired
    private LiveAfficheService liveAfficheService;

    @Autowired
    private LiveRoomService liveRoomService;

    @Override
    public List<LiveDto> getLiveByIds(List<Long> ids) {
        return Collections.emptyList();
    }

    @Override
    public Optional<LiveDto> getLiveInfo(Long liveId) {
        Result<LiveNewProto.ResponseGetLiveNoCache> result = liveNewService.getLiveNoCache(LiveNewProto.GetLiveParams.newBuilder()
                .setLiveId(liveId)
                .build());
        if (RpcResult.isFail(result)) {
            log.warn("pp getLiveInfo fail. rCode={},liveId={}", result.rCode(), liveId);
            return Optional.empty();
        }

        LiveNewProto.Live livePb = result.target().getLive();
        return Optional.of(new LiveDto()
                .setId(liveId)
                .setOnAir(livePb.getStatus() == 1)
                .setRoomType(livePb.getType())
                .setUserId(livePb.getUserId())
        );
    }

    @Override
    public Optional<Long> getLatestLiveIdByUserId(Long userId) {
        Result<LiveNewProto.ResponseGetUpToDateLiveIdNotCache> result = liveNewService.getUpToDateLiveIdNotCache(LiveNewProto.GetUpToDateLiveIdParams.newBuilder().setUserId(userId).build());
        if (RpcResult.isFail(result)) {
            log.warn("pp getLatestLiveInfoByUserId fail. rCode={},userId={}", result.rCode(), userId);
            return Optional.empty();
        }
        LiveNewProto.ResponseGetUpToDateLiveIdNotCache targeted = result.target();
        return Optional.of(targeted.getLiveId());
    }

    @Override
    public Result<GetRoomNoticeResponse> getRoomNotice(GetRoomNoticeRequest request) {
        Result<LiveAfficheProto.ResponseGetLiveAffiche> result = liveAfficheService.getLiveAffiche(request.getRoomId());
        if (RpcResult.isFail(result)) {
            log.warn("pp getRoomNotice fail. rCode={},roomId={}", result.rCode(), request.getRoomId());
            return RpcResult.fail(GER_ROOM_NOTICE_FAIL);
        }
        GetRoomNoticeResponse content = new GetRoomNoticeResponse().setContent(result.target().getContent());
        return RpcResult.success(content);
    }

    @Override
    public Result<Void> editRoomNotice(EditRoomNoticeRequest request) {
        Result<LiveAfficheProto.ResponseEditLiveAffiche> result = liveAfficheService.editLiveAffiche(request.getRoomId(), request.getContent());
        if (RpcResult.isFail(result)) {
            log.warn("pp editRoomNotice fail. rCode={},roomId={}", result.rCode(), request.getRoomId());
            return RpcResult.fail(EDIT_ROOM_NOTICE_FAIL);
        }
        return RpcResult.success();
    }

    @Override
    public Result<GetRoomInfoByNjIdResponse> getRoomInfoByNjId(Long userId) {
        Result<LiveRoomProto.ResponseGetLiveRoomByUserId> result = liveRoomService.getLiveRoomByUserId(userId);
        if (RpcResult.isFail(result)) {
            log.warn("pp getRoomInfoByNjId fail. rCode={},userId={}", result.rCode(), userId);
            if (result.rCode() == LiveRoomService.GET_LIVE_ROOM_BY_USER_ID_NO_CACHE_ERROR_NOT_FOUND) {
                return RpcResult.fail(GET_ROOM_INFO_BY_NJ_ID_NO_EXIST);
            }
            return RpcResult.fail(GET_ROOM_INFO_BY_NJ_ID_FAIL);
        }

        LiveRoomProto.LiveRoom liveRoom = result.target().getLiveRoom();
        GetRoomInfoByNjIdResponse info = new GetRoomInfoByNjIdResponse().setId(liveRoom.getId())
                .setName(liveRoom.getName())
                .setRadioId(liveRoom.getRadioId())
                .setStatus(liveRoom.getStatus())
                .setUserId(liveRoom.getUserId());
        return RpcResult.success(info);

    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }
}
