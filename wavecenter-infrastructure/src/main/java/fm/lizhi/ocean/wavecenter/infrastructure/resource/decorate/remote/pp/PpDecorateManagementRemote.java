package fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.pp;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.pp.PpCreateAvatarWidgetBean;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.pp.PpCreateRoomBackgroundBean;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestCreateAvatarWidget;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestCreateRoomBackground;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.response.ResponseCreateAvatarWidget;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.response.ResponseCreateRoomBackground;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.common.validation.BeanValidator;
import fm.lizhi.ocean.wavecenter.common.validation.ValidateResult;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.convert.pp.PpDecorateManagementConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.DecorateManagementRemote;
import fm.lizhi.pp.vip.api.PpDecorateService;
import fm.lizhi.pp.vip.bean.dto.DecorateDto;
import fm.lizhi.pp.vip.bean.dto.DecorateExtInfoDto;
import fm.lizhi.pp.vip.bean.enums.DecorateExtInfoKeyEnum;
import fm.lizhi.pp.vip.constant.DecorateSourceEnum;
import fm.lizhi.pp.vip.constant.DecorateTypeEnum;
import fm.lizhi.pp.vip.protocol.PpDecorateProto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class PpDecorateManagementRemote implements DecorateManagementRemote {

    private final PpDecorateManagementConvert ppDecorateManagementConvert = PpDecorateManagementConvert.I;

    @Autowired
    private BeanValidator beanValidator;

    @Autowired
    private PpDecorateService ppDecorateService;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public Result<ResponseCreateRoomBackground> createRoomBackground(RequestCreateRoomBackground request) {
        try {
            PpCreateRoomBackgroundBean bean = ppDecorateManagementConvert.toPpCreateRoomBackgroundBean(request);
            ValidateResult validateResult = beanValidator.validate(bean);
            if (validateResult.isInvalid()) {
                log.info("createRoomBackground request invalid, request={}, validateResult={}", request, validateResult);
                return RpcResult.fail(CommonService.PARAM_ERROR, validateResult.getMessage());
            }
            DecorateDto roomBackgroundDTO = buildCreateRoomBackgroundDTO(bean);
            String roomBackgroundDTOStr = JsonUtils.toJsonString(roomBackgroundDTO);
            Result<PpDecorateProto.ResponseAddOrEditDecorate> result = ppDecorateService.addOrEditDecorate(roomBackgroundDTOStr);
            int rCode = result.rCode();
            if (rCode == PpDecorateService.ADD_OR_EDIT_DECORATE_ILLEGAL_PARAMS) {
                log.info("addOrEditDecorate params invalid, roomBackgroundDTOStr={}", roomBackgroundDTOStr);
                return RpcResult.fail(CommonService.PARAM_ERROR, "参数错误");
            }
            if (rCode != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                log.error("addOrEditDecorate fail, rCode={}, roomBackgroundDTOStr={}", rCode, roomBackgroundDTOStr);
                return RpcResult.fail(CommonService.BUSINESS_ERROR, "保存失败");
            }
            PpDecorateProto.ResponseAddOrEditDecorate target = result.target();
            log.info("addOrEditDecorate success, roomBackgroundDTOStr={}, target={}", roomBackgroundDTOStr, target);
            ResponseCreateRoomBackground response = new ResponseCreateRoomBackground();
            response.setId(target.getDecorateIdeId());
            response.setPreviewUrl(bean.getThumbUrl());
            return RpcResult.success(response);
        } catch (RuntimeException e) {
            log.error("createRoomBackground error, request={}", request, e);
            return RpcResult.fail(CommonService.INTERNAL_ERROR, "内部错误");
        }
    }

    private DecorateDto buildCreateRoomBackgroundDTO(PpCreateRoomBackgroundBean bean) {
        int decorateType = DecorateTypeEnum.BACKGROUND.getType();
        DecorateDto decorateDto = new DecorateDto();
        decorateDto.setSource(DecorateSourceEnum.PC.getType());
        decorateDto.setType(decorateType);
        decorateDto.setName(bean.getName());
        // 各种URL字段去掉host和开头的斜杠, 保持和互娱后台前端提交的一致
        decorateDto.setThumbUrl(UrlUtils.removeHostAndStartSlashOrEmpty(bean.getThumbUrl()));
        decorateDto.setMaterialUrl(UrlUtils.removeHostAndStartSlashOrEmpty(bean.getMaterialUrl()));
        decorateDto.setIconUrl(UrlUtils.removeHostAndStartSlashOrEmpty(bean.getIconUrl()));
        decorateDto.setValidMin(bean.getValidMin());
        decorateDto.setWeight(bean.getWeight());
        // 扩展信息
        ArrayList<DecorateExtInfoDto> decorateExtInfoDTOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(bean.getLiveModes())) {
            DecorateExtInfoDto liveModesExtDTO = buildLiveModesExtDTO(decorateType, bean.getLiveModes());
            decorateExtInfoDTOS.add(liveModesExtDTO);
        }
        DecorateExtInfoDto backgroundColorExtDTO = buildBackgroundColorExtDTO(decorateType, bean.getBackgroundColor());
        decorateExtInfoDTOS.add(backgroundColorExtDTO);
        String extInfo = JsonUtils.toJsonString(decorateExtInfoDTOS);
        decorateDto.setExtInfo(extInfo);
        return decorateDto;
    }

    private DecorateExtInfoDto buildLiveModesExtDTO(int decorateType, List<Integer> liveModes) {
        String liveModesValue = liveModes.stream().distinct().map(Object::toString).collect(Collectors.joining(","));
        DecorateExtInfoDto decorateExtInfoDto = new DecorateExtInfoDto();
        decorateExtInfoDto.setType(decorateType);
        decorateExtInfoDto.setKey(DecorateExtInfoKeyEnum.liveModes.getKey());
        decorateExtInfoDto.setName(DecorateExtInfoKeyEnum.liveModes.getName());
        decorateExtInfoDto.setValue(liveModesValue);
        return decorateExtInfoDto;
    }

    private DecorateExtInfoDto buildBackgroundColorExtDTO(int decorateType, String backgroundColor) {
        DecorateExtInfoDto decorateExtInfoDto = new DecorateExtInfoDto();
        decorateExtInfoDto.setType(decorateType);
        decorateExtInfoDto.setKey(DecorateExtInfoKeyEnum.backgroundColor.getKey());
        decorateExtInfoDto.setName(DecorateExtInfoKeyEnum.backgroundColor.getName());
        decorateExtInfoDto.setValue(backgroundColor);
        return decorateExtInfoDto;
    }

    @Override
    public Result<ResponseCreateAvatarWidget> createAvatarWidget(RequestCreateAvatarWidget request) {
        try {
            PpCreateAvatarWidgetBean bean = ppDecorateManagementConvert.toPpCreateAvatarWidgetBean(request);
            ValidateResult validateResult = beanValidator.validate(bean);
            if (validateResult.isInvalid()) {
                log.info("createAvatarWidget request invalid, request={}, validateResult={}", request, validateResult);
                return RpcResult.fail(CommonService.PARAM_ERROR, validateResult.getMessage());
            }
            DecorateDto avatarWidgetDTO = buildCreateAvatarWidgetDTO(bean);
            String avatarWidgetDTOStr = JsonUtils.toJsonString(avatarWidgetDTO);
            Result<PpDecorateProto.ResponseAddOrEditDecorate> result = ppDecorateService.addOrEditDecorate(avatarWidgetDTOStr);
            int rCode = result.rCode();
            if (rCode == PpDecorateService.ADD_OR_EDIT_DECORATE_ILLEGAL_PARAMS) {
                log.info("addOrEditDecorate params invalid, avatarWidgetDTOStr={}", avatarWidgetDTOStr);
                return RpcResult.fail(CommonService.PARAM_ERROR, "参数错误");
            }
            if (rCode != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                log.error("addOrEditDecorate fail, rCode={}, avatarWidgetDTOStr={}", rCode, avatarWidgetDTOStr);
                return RpcResult.fail(CommonService.BUSINESS_ERROR, "保存失败");
            }
            PpDecorateProto.ResponseAddOrEditDecorate target = result.target();
            log.info("addOrEditDecorate success, avatarWidgetDTOStr={}, target={}", avatarWidgetDTOStr, target);
            ResponseCreateAvatarWidget response = new ResponseCreateAvatarWidget();
            response.setId(target.getDecorateIdeId());
            response.setPreviewUrl(bean.getThumbUrl());
            return RpcResult.success(response);
        } catch (RuntimeException e) {
            log.error("createAvatarWidget error, request={}", request, e);
            return RpcResult.fail(CommonService.INTERNAL_ERROR, "内部错误");
        }
    }

    private DecorateDto buildCreateAvatarWidgetDTO(PpCreateAvatarWidgetBean bean) {
        int decorateType = DecorateTypeEnum.AVATAR_WIDGET.getType();
        DecorateDto decorateDto = new DecorateDto();
        decorateDto.setSource(DecorateSourceEnum.PC.getType());
        decorateDto.setType(decorateType);
        decorateDto.setName(bean.getName());
        // 各种URL字段去掉host和开头的斜杠, 保持和互娱后台前端提交的一致
        decorateDto.setThumbUrl(UrlUtils.removeHostAndStartSlashOrEmpty(bean.getThumbUrl()));
        decorateDto.setMaterialUrl(UrlUtils.removeHostAndStartSlashOrEmpty(bean.getMaterialUrl()));
        // PP互娱后台前端提交的头像框图标URL和静态图URL一致, 这里也保持一致
        decorateDto.setIconUrl(UrlUtils.removeHostAndStartSlashOrEmpty(bean.getThumbUrl()));
        decorateDto.setValidMin(bean.getValidMin());
        decorateDto.setWeight(0);
        // 扩展信息, 如果扩展信息列表为空则设置为"[]", 参考前端提交值传参
        String extInfo = JsonUtils.toJsonString(Collections.emptyList());
        decorateDto.setExtInfo(extInfo);
        return decorateDto;
    }
}
