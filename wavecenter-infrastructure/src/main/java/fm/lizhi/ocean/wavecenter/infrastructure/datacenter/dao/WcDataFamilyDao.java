package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.dao;

import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataFamilyDayExample;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataFamilyMonthExample;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataFamilyWeekExample;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.mapper.WcDataFamilyDayMapper;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.mapper.WcDataFamilyMonthMapper;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.mapper.WcDataFamilyWeekMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
public class WcDataFamilyDao {

    @Resource
    private WcDataFamilyDayMapper wcDataFamilyDayMapper;

    @Resource
    private WcDataFamilyWeekMapper wcDataFamilyWeekMapper;

    @Resource
    private WcDataFamilyMonthMapper wcDataFamilyMonthMapper;


    /**
     * 计算总条数
     *
     * @param appId    业务ID
     * @param familyId 公会ID
     * @return 总条数
     */
    public long countFamilyDay(int appId, Long familyId) {
        WcDataFamilyDayExample example = new WcDataFamilyDayExample();
        example.createCriteria().andAppIdEqualTo(appId).andFamilyIdEqualTo(familyId);
        return wcDataFamilyDayMapper.countByExample(example);
    }

    /**
     * 计算周统计总条数
     *
     * @param appId    业务ID
     * @param familyId 公会ID
     * @return 总条数
     */
    public long countFamilyWeek(int appId, Long familyId) {
        WcDataFamilyWeekExample example = new WcDataFamilyWeekExample();
        example.createCriteria().andAppIdEqualTo(appId).andFamilyIdEqualTo(familyId);
        return wcDataFamilyWeekMapper.countByExample(example);
    }

    /**
     * 计算月统计总条数
     *
     * @param appId    业务ID
     * @param familyId 公会ID
     * @return 总条数
     */
    public long countFamilyMonth(int appId, Long familyId) {
        WcDataFamilyMonthExample example = new WcDataFamilyMonthExample();
        example.createCriteria().andAppIdEqualTo(appId).andFamilyIdEqualTo(familyId);
        return wcDataFamilyMonthMapper.countByExample(example);
    }
}
