package fm.lizhi.ocean.wavecenter.infrastructure.user.po;

import fm.lizhi.commons.config.util.JsonUtil;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/4/17 22:38
 */
@Data
@Accessors(chain = true)
public class RefreshTokenInfoPo {
    private Integer appId;
    private Long userId;
    private String deviceId;
    private String accessToken;
    private Long roleConfigId;
    private String roleCode;
    private Long subjectId;
    private Integer loginType;

    public static RefreshTokenInfoPo of(String valueJsonStr){
        return JsonUtil.loads(valueJsonStr, RefreshTokenInfoPo.class);
    }

    public String toJson(){
        return JsonUtil.dumps(this);
    }
}
