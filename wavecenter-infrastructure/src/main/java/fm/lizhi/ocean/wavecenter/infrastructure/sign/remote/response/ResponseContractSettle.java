package fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.response;

import fm.lizhi.ocean.wavecenter.service.sign.dto.SignSettleDTO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/15 19:15
 */
@Data
@Accessors(chain = true)
public class ResponseContractSettle {

    /**
     * key=合同ID
     * value=结算信息
     */
    private Map<Long, SignSettleDTO> settleMap;

}
