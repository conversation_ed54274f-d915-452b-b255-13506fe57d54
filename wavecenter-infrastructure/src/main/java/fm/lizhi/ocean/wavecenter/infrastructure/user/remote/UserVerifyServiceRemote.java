package fm.lizhi.ocean.wavecenter.infrastructure.user.remote;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import fm.lizhi.common.verify.api.UserVerifyService;
import fm.lizhi.common.verify.protocol.UserVerifyProto.GetUserVerifyResultParams;
import fm.lizhi.common.verify.protocol.UserVerifyProto.ResponseGetUserVerifyResult;
import fm.lizhi.common.verify.protocol.UserVerifyProto.ResponseSearchUserVerifyResult;
import fm.lizhi.common.verify.protocol.UserVerifyProto.SearchUserVerifyResultParams;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.lamp.common.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.bean.UserVerifyResultBean;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.convert.UserVerifyConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.req.GetUserVerifyResultReq;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.req.SearchUserVerifyResultReq;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.res.GetUserVerifyResultRes;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.res.SearchUserVerifyResultRes;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class UserVerifyServiceRemote {

    @Autowired
    private UserVerifyService userVerifyService;

    /**
     * 获取用户认证结果
     * 
     * @param req 请求参数
     * @return 响应结果
     */
    public Result<GetUserVerifyResultRes> getUserVerifyResult(GetUserVerifyResultReq req) {
        GetUserVerifyResultParams.Builder builder = GetUserVerifyResultParams.newBuilder();
        if (req.getUserId() != null) {
            builder.setUserId(req.getUserId());
        }
        if (req.getAppId() != null) {
            builder.setAppId(req.getAppId());
        }
        Result<ResponseGetUserVerifyResult> result = userVerifyService.getUserVerifyResult(builder.build());
        if (RpcResult.isFail(result)) {
            log.warn("getUserVerifyResult failed, rCode: {}, req: {}", result.rCode(), JsonUtils.toJsonString(req));
            if (result.rCode() == UserVerifyService.GET_USER_VERIFY_RESULT_NOT_FOUND) {
                return RpcResult.fail(GET_USER_VERIFY_RESULT_NOT_FOUND);
            }
            return RpcResult.fail(GET_USER_VERIFY_RESULT_FAIL);
        }
        UserVerifyResultBean bean = UserVerifyConvert.I.verifyResultToRes(result.target().getResult());
        GetUserVerifyResultRes res = new GetUserVerifyResultRes();
        res.setUserVerifyResult(bean);
        return RpcResult.success(res);
    }

    /**
     * 搜索用户认证结果
     * 
     * @param req 请求参数
     * @return 响应结果
     */
    public Result<SearchUserVerifyResultRes> searchUserVerifyResult(SearchUserVerifyResultReq req) {
        SearchUserVerifyResultParams param = UserVerifyConvert.I.searchVerifyParamToProto(req);
        Result<ResponseSearchUserVerifyResult> result = userVerifyService.searchUserVerifyResult(param);
        if (RpcResult.isFail(result)) {
            log.warn("searchUserVerifyResult failed, rCode: {}, req: {}", result.rCode(), JsonUtils.toJsonString(req));
            if (result.rCode() == UserVerifyService.SEARCH_USER_VERIFY_RESULT_NOT_FOUND) {
                return RpcResult.fail(SEARCH_USER_VERIFY_RESULT_NOT_FOUND);
            }
            return RpcResult.fail(SEARCH_USER_VERIFY_RESULT_FAIL);
        }

        SearchUserVerifyResultRes res = UserVerifyConvert.I.searchVerifyResultToRes(result.target());
        return RpcResult.success(res);
    }

    int GET_USER_VERIFY_RESULT_FAIL = 1;
    int GET_USER_VERIFY_RESULT_NOT_FOUND = 2;

    int SEARCH_USER_VERIFY_RESULT_FAIL = 1;
    int SEARCH_USER_VERIFY_RESULT_NOT_FOUND = 2;

}
