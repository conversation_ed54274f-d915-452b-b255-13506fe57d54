package fm.lizhi.ocean.wavecenter.infrastructure.message.convert;

import fm.lizhi.ocean.wavecenter.api.message.bean.MessageBean;
import fm.lizhi.ocean.wavecenter.api.message.request.RequestSendMessage;
import fm.lizhi.ocean.wavecenter.api.message.request.RequestSendMessageBatch;
import fm.lizhi.ocean.wavecenter.api.message.request.RequestSendMessageToRole;
import fm.lizhi.ocean.wavecenter.datastore.platform.message.entity.WcMessage;
import fm.lizhi.ocean.wavecenter.datastore.platform.message.entity.WcNoticeConfig;
import fm.lizhi.ocean.wavecenter.infrastructure.message.dto.SendMessageJobDTO;
import fm.lizhi.ocean.wavecenter.infrastructure.message.param.BatchSaveMessageParam;
import fm.lizhi.ocean.wavecenter.infrastructure.message.param.SaveMessageParam;
import fm.lizhi.ocean.wavecenter.infrastructure.message.param.SaveMessageToRoleParam;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface MessageConvert {
    MessageConvert I = Mappers.getMapper(MessageConvert.class);


    @Mappings({
            @Mapping(source = "roleCode", target = "visibleRoleCode")
    })
    WcMessage buildWcMessage(SaveMessageParam param);

    @Mappings(
            {
                    @Mapping(target = "createTime", expression = "java(new java.util.Date())"),
                    @Mapping(target = "updateTime", expression = "java(new java.util.Date())"),
                    @Mapping(target = "deleted", expression = "java(false)")

            }
    )
    WcMessage buildWcMessage(BatchSaveMessageParam param, Long targetUserId);

    SaveMessageParam convertSaveMessageParam(RequestSendMessage param);

    BatchSaveMessageParam convertSaveMessageBatchParam(RequestSendMessageBatch param);

    SaveMessageToRoleParam convertSaveMessageToRoleParam(RequestSendMessageToRole param);

    SaveMessageParam convertSaveMessageParam(SaveMessageToRoleParam param, String roleCode);

    MessageBean convertMessageBean(WcMessage message, String content, Boolean read);

    /**
     * WcMessage 转 MessageBean（不含 content/read 字段）
     */
    @Mappings({
            @Mapping(target = "createTime", expression = "java(message.getCreateTime() != null ? message.getCreateTime().getTime() : 0)"),
            @Mapping(target = "updateTime", expression = "java(message.getUpdateTime() != null ? message.getUpdateTime().getTime() : 0)")
    })
    MessageBean convertMessageBeanSimple(WcMessage message);

    /**
     * WcNoticeConfig 转 MessageBean
     */
    @Mappings({
            @Mapping(target = "targetUserId", ignore = true),
            @Mapping(target = "sendUserId", ignore = true),
            @Mapping(target = "visibleRoleCode", ignore = true),
            @Mapping(target = "read", ignore = true),
            @Mapping(target = "targetLink", ignore = true),
            @Mapping(target = "bizId", ignore = true),
            @Mapping(target = "createTime", expression = "java(notice.getEffectTime() != null ? notice.getEffectTime().getTime() : 0)"),
            @Mapping(target = "updateTime", expression = "java(notice.getModifyTime() != null ? notice.getModifyTime().getTime() : 0)")
    })
    MessageBean convertNoticeConfigToMessageBean(WcNoticeConfig notice);

    default Long dateToLong(Date date) {
        return date == null ? null : date.getTime();
    }

    RequestSendMessageBatch sendMessageJobDTO2RequestSendMessageBatch(SendMessageJobDTO dto);

    RequestSendMessageToRole sendMessageJobDTO2RequestSendMessageToRole(SendMessageJobDTO dto);
}
