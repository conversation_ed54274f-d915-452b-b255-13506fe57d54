package fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.dao;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestClearFamilySpecialRecommendCardName;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestListFamilySpecialRecommendCardName;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestUploadFamilySpecialRecommendCardName;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert.FamilySpecialRecommendCardNameConvert;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilySpecialRecommendCardName;
import fm.lizhi.ocean.wavecenter.datastore.award.family.mapper.WcFamilySpecialRecommendCardNameMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.mapper.ext.WcFamilySpecialRecommendCardNameExtMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;

@Slf4j
@Repository
public class FamilyOtherAwardRuleDao {

    @Autowired
    private WcFamilySpecialRecommendCardNameExtMapper wcFamilySpecialRecommendCardNameExtMapper;
    @Autowired
    private WcFamilySpecialRecommendCardNameMapper wcFamilySpecialRecommendCardNameMapper;

    /**
     * 替换特殊推荐卡名单
     *
     * @param request 请求
     */
    @Transactional
    public void replaceSpecialRecommendCardName(RequestUploadFamilySpecialRecommendCardName request) {
        log.info("replaceSpecialRecommendCardName request={}", request);
        Integer appId = request.getAppId();
        String deployEnv = ConfigUtils.getEnvRequired().name();
        int deleteRows = wcFamilySpecialRecommendCardNameExtMapper.deleteByAppId(appId, deployEnv);
        log.info("replaceSpecialRecommendCardName deleteRows={}", deleteRows);
        List<WcFamilySpecialRecommendCardName> entities = FamilySpecialRecommendCardNameConvert.I.toCreateEntities(request);
        int createRows = wcFamilySpecialRecommendCardNameMapper.batchInsert(entities);
        log.info("replaceSpecialRecommendCardName createRows={}", createRows);
    }

    /**
     * 清空特殊推荐卡名单
     *
     * @param request 请求
     */
    public void clearSpecialRecommendCardName(RequestClearFamilySpecialRecommendCardName request) {
        log.info("clearSpecialRecommendCardName request={}", request);
        Integer appId = request.getAppId();
        String deployEnv = ConfigUtils.getEnvRequired().name();
        int deleteRows = wcFamilySpecialRecommendCardNameExtMapper.deleteByAppId(appId, deployEnv);
        log.info("clearSpecialRecommendCardName deleteRows={}", deleteRows);
    }

    /**
     * 列出特殊推荐卡名单
     *
     * @param request 请求
     * @return 特殊推荐卡名单
     */
    public PageList<WcFamilySpecialRecommendCardName> listSpecialRecommendCardName(
            RequestListFamilySpecialRecommendCardName request) {
        log.info("listSpecialRecommendCardName request={}", request);
        Integer appId = request.getAppId();
        Integer pageNumber = request.getPageNumber();
        Integer pageSize = request.getPageSize();
        String deployEnv = ConfigUtils.getEnvRequired().name();
        PageList<WcFamilySpecialRecommendCardName> pageList = wcFamilySpecialRecommendCardNameExtMapper
                .list(appId, deployEnv, pageNumber, pageSize);
        log.info("listSpecialRecommendCardName total={}, listSize={}", pageList.getTotal(), pageList.size());
        return pageList;
    }

    /**
     * 获取指定应用下的所有特殊推荐卡名单map. key为用户id, value为特殊推荐卡发放数量
     *
     * @param appId 应用id
     * @return 特殊推荐卡名单map
     */
    public Map<Long, Integer> getAppSpecialRecommendCardNameAsUserNumberMap(int appId) {
        log.info("getAppSpecialRecommendCardNameAsUserNumberMap appId={}", appId);
        String deployEnv = ConfigUtils.getEnvRequired().name();
        WcFamilySpecialRecommendCardName selectMany = new WcFamilySpecialRecommendCardName();
        selectMany.setDeployEnv(deployEnv);
        selectMany.setAppId(appId);
        List<WcFamilySpecialRecommendCardName> list = wcFamilySpecialRecommendCardNameMapper.selectMany(selectMany);
        log.info("getAppSpecialRecommendCardNameAsUserNumberMap listSize={}", list.size());
        TreeMap<Long, Integer> userNumberMap = new TreeMap<>();
        for (WcFamilySpecialRecommendCardName entity : list) {
            userNumberMap.put(entity.getUserId(), entity.getNumber());
        }
        return userNumberMap;
    }
}
