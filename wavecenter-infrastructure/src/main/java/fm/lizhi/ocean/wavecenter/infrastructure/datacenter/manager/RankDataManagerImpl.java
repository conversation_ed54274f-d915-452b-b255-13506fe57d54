package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.manager;

import com.alibaba.fastjson.JSONObject;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.constants.DateType;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.*;
import fm.lizhi.ocean.wavecenter.api.user.bean.RoomBean;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.*;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.mapper.*;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PayTenantCodeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.constants.MetricsConstants;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.constants.MetricsMeta;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.convert.DataCenterInfraConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.entity.*;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.*;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote.ICharmStatRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote.IRankDataRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.income.dto.RoomIncomeDTO;
import fm.lizhi.ocean.wavecenter.infrastructure.income.manager.PaymentManager;
import fm.lizhi.ocean.wavecenter.service.datacenter.config.DataCenterConfig;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.RankDataManager;
import fm.lizhi.pay.settle.api.creativecenter.CreatorDataQueryService;
import fm.lizhi.pay.settle.protocol.creativecenter.CreatorDataQueryProto;
import fm.lizhi.pay.settle.settleenum.creativecenter.PeriodTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/22 20:43
 */
@Slf4j
@Component
public class RankDataManagerImpl implements RankDataManager {

    @Autowired
    private ICharmStatRemote iCharmStatRemote;
    @Autowired
    private WcDataPlayerRoomDayMapper playerRoomDayMapper;
    @Autowired
    private WcDataPlayerRoomWeekMapper playerRoomWeekMapper;
    @Autowired
    private WcDataPlayerRoomMonthMapper playerRoomMonthMapper;

    @Autowired
    private WcDataRoomFamilyDayMapper roomFamilyDayMapper;
    @Autowired
    private WcDataRoomFamilyWeekMapper roomFamilyWeekMapper;
    @Autowired
    private WcDataRoomFamilyMonthMapper roomFamilyMonthMapper;


    @Autowired
    private CreatorDataQueryService creatorDataQueryService;

    @Autowired
    private IRankDataRemote iRankDataRemote;

    @Autowired
    private WcDataRoomFamilyDayStatMapper wcDataRoomFamilyDayStatMapper;

    @Autowired
    private DataCenterConfig dataCenterConfig;

    @Autowired
    private PaymentManager paymentManager;


    @Override
    public List<RankRoomBean> room(RankGetRoomParamBean paramBean) {
        //如果不是查今日的厅业绩榜，就从数据库查大数据的数据
        if (paramBean.getDate() != null && !paramBean.getDate().equals(DateUtil.formatDateToString(new Date(), DateUtil.date_2))) {
            return selectRoomFamilyDayIncomeStat(paramBean);
        }

        if (dataCenterConfig.isRankQueryPay()) {
            CreatorDataQueryProto.QueryHallPerformanceStatisticsRequest.Builder builder = CreatorDataQueryProto
                    .QueryHallPerformanceStatisticsRequest.newBuilder()
                    .setFamilyId(paramBean.getFamilyId())
                    .setTenantCode(PayTenantCodeEnum.getPayTenantCode(paramBean.getAppId()))
                    .setPeriodType(PeriodTypeEnum.TODAY.getPeriodType());

            if (CollectionUtils.isNotEmpty(paramBean.getRoomIds())) {
                builder.addAllManageHallId(paramBean.getRoomIds());
            }

            Result<CreatorDataQueryProto.ResponseQueryHallPerformanceStatistics> result = creatorDataQueryService.queryHallPerformanceStatistics(builder.build(),
                    DateUtil.formatDateToString(new Date(), DateUtil.NORMAL_DATE_FORMAT));
            if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                log.error("room error paramBean={} rCode={}", JSONObject.toJSONString(paramBean), result.rCode());
                return Collections.emptyList();
            }
            CreatorDataQueryProto.ResponseQueryHallPerformanceStatistics performanceStatistics = result.target();
            List<CreatorDataQueryProto.QueryHallPerformanceStatisticsResponse> dataList = performanceStatistics.getDataList();

            return dataList.stream().map(e -> {
                RoomBean roomBean = new RoomBean();
                roomBean.setId(e.getHallId());
                return RankRoomBean.builder()
                        .income(new BigDecimal(e.getStatisticsValue()))
                        .roomInfo(roomBean)
                        .build();
            }).collect(Collectors.toList());
        }

        // 查询统计数据
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        List<RoomIncomeDTO> topRoomIncomeForGuild = paymentManager.getTopRoomIncomeForGuild(appId, paramBean.getFamilyId(), paramBean.getRoomIds(), new Date(), paramBean.getRankType(), 10);
        return topRoomIncomeForGuild.stream().map(e -> {
            RoomBean roomBean = new RoomBean();
            roomBean.setId(e.getNjId());
            return RankRoomBean.builder()
                    .income(e.getIncome())
                    .roomInfo(roomBean)
                    .build();
        }).collect(Collectors.toList());
    }

    @Override
    public List<RankBean> guildPlayer(long familyId, List<Long> roomIds, Date date, OrderType rankType) {
        return iRankDataRemote.guildPlayer(familyId, roomIds, date, rankType);
    }

    @Override
    public List<RankBean> roomPlayer(long roomId, long familyId, Date date, OrderType rankType) {
        return iRankDataRemote.roomPlayer(roomId, familyId, date, rankType);
    }

    @Override
    public PageBean<PlayerRankBean> playerRankPageList(GetSignPlayerParamBean paramBean) {

        DateType dateType = paramBean.getDateType();
        Long roomId = paramBean.getRoomId();
        Long familyId = paramBean.getFamilyId();
        List<Long> roomScopeIds = paramBean.getRoomIds();

        //排序条件
        String filedSqlName = "sign_player_cnt";
        Optional<MetricsMeta> metricsMeta = MetricsConstants.getMetricsMeta(paramBean.getOrderMetrics());
        if (metricsMeta.isPresent()) {
            filedSqlName = metricsMeta.get().getPoName();
        }
        //防止相同值乱序
        String orderSql = filedSqlName + " " + paramBean.getOrderType().getValue() + ", id desc";

        List<PlayerRankBean> list = null;
        int total = 0;
        if (dateType == DateType.DAY) {
            Integer dateDayValue = MyDateUtil.getDateDayValue(paramBean.getStartDate());
            WcDataPlayerRoomDayExample example = new WcDataPlayerRoomDayExample();
            WcDataPlayerRoomDayExample.Criteria criteria = example.createCriteria();
            criteria.andAppIdEqualTo(paramBean.getAppId())
                    .andFamilyIdEqualTo(familyId)
                    .andStatDateValueEqualTo(dateDayValue);
            if (roomId != null) {
                criteria.andRoomIdEqualTo(roomId);
            }
            if (paramBean.isFilterZero()) {
                criteria.andCharmGreaterThan(0);
            }
            if (CollectionUtils.isNotEmpty(roomScopeIds)) {
                criteria.andRoomIdIn(roomScopeIds);
            }
            example.setOrderByClause(orderSql);
            PageList<WcDataPlayerRoomDay> pageList = playerRoomDayMapper.pageByExample(example, paramBean.getPageNo(), paramBean.getPageSize());
            list = DataCenterInfraConvert.I.playerRoomDays2PlayerRankBeans(pageList);
            total = pageList.getTotal();
        }

        if (dateType == DateType.WEEK) {
            WcDataPlayerRoomWeekExample example = new WcDataPlayerRoomWeekExample();
            WcDataPlayerRoomWeekExample.Criteria criteria = example.createCriteria();
            criteria.andAppIdEqualTo(paramBean.getAppId())
                    .andFamilyIdEqualTo(familyId)
                    .andStartWeekDateEqualTo(paramBean.getStartDate())
                    .andEndWeekDateEqualTo(paramBean.getEndDate())
            ;
            if (roomId != null) {
                criteria.andRoomIdEqualTo(roomId);
            }
            if (paramBean.isFilterZero()) {
                criteria.andCharmGreaterThan(0);
            }
            if (CollectionUtils.isNotEmpty(roomScopeIds)) {
                criteria.andRoomIdIn(roomScopeIds);
            }
            example.setOrderByClause(orderSql);
            PageList<WcDataPlayerRoomWeek> pageList = playerRoomWeekMapper.pageByExample(example, paramBean.getPageNo(), paramBean.getPageSize());
            list = DataCenterInfraConvert.I.playerRoomWeeks2PlayerRankBeans(pageList);
            total = pageList.getTotal();
        }

        if (dateType == DateType.MONTH) {
            Integer statMonth = MyDateUtil.getDateMonthValue(paramBean.getStartDate());
            WcDataPlayerRoomMonthExample example = new WcDataPlayerRoomMonthExample();
            WcDataPlayerRoomMonthExample.Criteria criteria = example.createCriteria();
            criteria.andAppIdEqualTo(paramBean.getAppId())
                    .andFamilyIdEqualTo(familyId)
                    .andStatMonthEqualTo(statMonth)
            ;
            if (roomId != null) {
                criteria.andRoomIdEqualTo(roomId);
            }
            if (paramBean.isFilterZero()) {
                criteria.andCharmGreaterThan(0);
            }
            if (CollectionUtils.isNotEmpty(roomScopeIds)) {
                criteria.andRoomIdIn(roomScopeIds);
            }
            example.setOrderByClause(orderSql);
            PageList<WcDataPlayerRoomMonth> pageList = playerRoomMonthMapper.pageByExample(example, paramBean.getPageNo(), paramBean.getPageSize());
            list = DataCenterInfraConvert.I.playerRoomMonths2PlayerRankBeans(pageList);
            total = pageList.getTotal();
        }

        return PageBean.of(total, list);
    }

    @Override
    public PageBean<RoomRankBean> roomRankPageList(GetSignRoomParamBean paramBean) {
        DateType dateType = paramBean.getDateType();
        Long familyId = paramBean.getFamilyId();
        Long roomId = paramBean.getRoomId();
        List<Long> roomScopeIds = paramBean.getRoomIds();

        //排序条件
        String filedSqlName = "sign_player_cnt";
        Optional<MetricsMeta> metricsMeta = MetricsConstants.getMetricsMeta(paramBean.getOrderMetrics());
        if (metricsMeta.isPresent()) {
            filedSqlName = metricsMeta.get().getPoName();
        }
        String orderSql = filedSqlName + " " + paramBean.getOrderType().getValue() + ", id desc";

        List<RoomRankBean> list = null;
        int total = 0;
        if (dateType == DateType.DAY) {
            Integer dateDayValue = MyDateUtil.getDateDayValue(paramBean.getStartDate());
            WcDataRoomFamilyDayExample example = new WcDataRoomFamilyDayExample();
            WcDataRoomFamilyDayExample.Criteria criteria = example.createCriteria();
            criteria.andAppIdEqualTo(paramBean.getAppId())
                    .andFamilyIdEqualTo(familyId)
                    .andStatDateValueEqualTo(dateDayValue);
            if (roomId != null) {
                criteria.andRoomIdEqualTo(roomId);
            }
            if (paramBean.isFilterZero()) {
                criteria.andIncomeGreaterThan(new BigDecimal(0));
            }
            if (CollectionUtils.isNotEmpty(roomScopeIds)) {
                criteria.andRoomIdIn(roomScopeIds);
            }
            example.setOrderByClause(orderSql);
            PageList<WcDataRoomFamilyDay> pageList = roomFamilyDayMapper.pageByExample(example, paramBean.getPageNo(), paramBean.getPageSize());
            list = DataCenterInfraConvert.I.roomFamilyDays2RoomRankBeans(pageList);
            total = pageList.getTotal();
        }

        if (dateType == DateType.WEEK) {
            WcDataRoomFamilyWeekExample example = new WcDataRoomFamilyWeekExample();
            WcDataRoomFamilyWeekExample.Criteria criteria = example.createCriteria();
            criteria.andAppIdEqualTo(paramBean.getAppId())
                    .andFamilyIdEqualTo(familyId)
                    .andStartWeekDateEqualTo(paramBean.getStartDate())
                    .andEndWeekDateEqualTo(paramBean.getEndDate())
            ;
            if (roomId != null) {
                criteria.andRoomIdEqualTo(roomId);
            }
            if (paramBean.isFilterZero()) {
                criteria.andIncomeGreaterThan(new BigDecimal(0));
            }
            if (CollectionUtils.isNotEmpty(roomScopeIds)) {
                criteria.andRoomIdIn(roomScopeIds);
            }
            example.setOrderByClause(orderSql);
            PageList<WcDataRoomFamilyWeek> pageList = roomFamilyWeekMapper.pageByExample(example, paramBean.getPageNo(), paramBean.getPageSize());
            list = DataCenterInfraConvert.I.roomFamilyWeeks2RoomRankBeans(pageList);
            total = pageList.getTotal();
        }

        if (dateType == DateType.MONTH) {
            Integer statMonth = MyDateUtil.getDateMonthValue(paramBean.getStartDate());
            WcDataRoomFamilyMonthExample example = new WcDataRoomFamilyMonthExample();
            WcDataRoomFamilyMonthExample.Criteria criteria = example.createCriteria();
            criteria.andAppIdEqualTo(paramBean.getAppId())
                    .andFamilyIdEqualTo(familyId)
                    .andStatMonthEqualTo(statMonth)
            ;
            if (roomId != null) {
                criteria.andRoomIdEqualTo(roomId);
            }
            if (paramBean.isFilterZero()) {
                criteria.andIncomeGreaterThan(new BigDecimal(0));
            }
            if (CollectionUtils.isNotEmpty(roomScopeIds)) {
                criteria.andRoomIdIn(roomScopeIds);
            }
            example.setOrderByClause(orderSql);
            PageList<WcDataRoomFamilyMonth> pageList = roomFamilyMonthMapper.pageByExample(example, paramBean.getPageNo(), paramBean.getPageSize());
            list = DataCenterInfraConvert.I.roomFamilyMonths2RoomRankBeans(pageList);
            total = pageList.getTotal();
        }

        return PageBean.of(total, list);
    }

    private List<RankRoomBean> selectRoomFamilyDayIncomeStat(RankGetRoomParamBean paramBean) {
        List<WcDataRoomFamilyDayPo> pos = wcDataRoomFamilyDayStatMapper.selectRoomFamilyDayIncomeStat(paramBean.getAppId(),
                paramBean.getFamilyId(), paramBean.getRoomIds(), paramBean.getDate(), paramBean.getRankType().getValue(), 10);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream().map(e -> {
            RoomBean roomBean = new RoomBean();
            roomBean.setId(e.getRoomId());
            return RankRoomBean.builder()
                    .income(e.getIncome())
                    .roomInfo(roomBean)
                    .build();
        }).collect(Collectors.toList());
    }
}
