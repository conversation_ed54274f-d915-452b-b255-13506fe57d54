package fm.lizhi.ocean.wavecenter.infrastructure.resource.recommendcard.remote.pp;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.live.room.pp.api.RecommendCardService;
import fm.lizhi.live.room.pp.bean.*;
import fm.lizhi.live.room.recommendation.api.PpRecommendationCardManageService;
import fm.lizhi.live.room.recommendation.protocol.PpRecommendationCardManageServiceProto;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.resource.constants.ResourceDeliverStatusEnum;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.*;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.request.*;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.response.RewardResultBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.CountDownLatchWrapper;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.ThreadConstants;
import fm.lizhi.ocean.wavecenter.common.utils.WcAssert;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.recommendcard.remote.RecommendCardRemote;
import fm.lizhi.ocean.wavecenter.service.resource.config.ResourceConfig;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto.GetUseRecordParamDTO;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto.RecommendAllocationRecordDTO;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto.RecommendCardUseRecordDTO;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto.UserRecommendCardStockDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/21 15:26
 */
@Slf4j
@Component
public class PpRecommendCardRemote implements RecommendCardRemote {

    @Autowired
    private PpRecommendationCardManageService ppRecommendationCardManageService;

    @Autowired
    private RecommendCardService recommendCardService;

    @Autowired
    private UserManager userManager;

    @Autowired
    private ResourceConfig resourceConfig;

    @Override
    public List<UserRecommendCardStockDTO> getUserStock(List<Long> userIds) {
        Result<List<fm.lizhi.live.room.pp.bean.UserRecommendCardStockDTO>> result = recommendCardService.getUserStock(userIds);
        if (RpcResult.isFail(result)) {
            log.warn("getUserStock fail. rCode={},userIds={}", result.rCode(), JsonUtil.dumps(userIds));
            return Collections.emptyList();
        }

        return result.target().stream().map(v->new UserRecommendCardStockDTO()
                .setUserId(v.getUserId())
                .setStock(v.getStock()))
                .collect(Collectors.toList());
    }

    @Override
    public Integer getExpireNum(Long userId, Date startDate, Date endDate) {
        // PP没有有效期
        return 0;
    }

    @Override
    public PageBean<RecommendCardUseRecordBean> getUseRecordForManagement(RequestGetUseRecord request) {
        PpRecommendationCardManageServiceProto.RecommendationCardUseRecordListRequest.Builder builder = PpRecommendationCardManageServiceProto.RecommendationCardUseRecordListRequest.newBuilder();
        if (request.getUserId() != null) {
            builder.setUserId(request.getUserId());
        }
        builder.setPageNumber(request.getPageNo()).setPageSize(request.getPageSize());
        Result<PpRecommendationCardManageServiceProto.ResponseGetRecommendationCardUseRecordList> result = ppRecommendationCardManageService.getRecommendationCardUseRecordList(builder.build());
        if (RpcResult.isFail(result)) {
            log.warn("getRecommendationCardUseRecordList fail. rCode={},request={}", result.rCode(), JsonUtil.dumps(request));
            return PageBean.empty();
        }
        List<RecommendCardUseRecordBean> beanList = new ArrayList<>();
        for (PpRecommendationCardManageServiceProto.UseRecord useRecord : result.target().getRecordList()) {
            RecommendCardUseRecordBean bean = new RecommendCardUseRecordBean();
            bean.setId(useRecord.getId());
            bean.setNjId(useRecord.getUserId());
            bean.setNjName(useRecord.getUserName());
            bean.setUseTime(DateUtil.formatStrToNormalDate(useRecord.getUseTime()));
            bean.setRecommendationTime(useRecord.getRecommendationTime());
            bean.setUseNum(useRecord.getNums());
            bean.setPosition(String.valueOf(useRecord.getRecommendationIndex()));
            beanList.add(bean);
        }

        return PageBean.of((int) result.target().getTotal(), beanList);
    }

    @Override
    public PageBean<RecommendCardSendRecordBean> getSendRecord(RequestGetSendRecord request) {

        PpRecommendationCardManageServiceProto.GetRecommendationCardSendRecordListRequest.Builder builder = PpRecommendationCardManageServiceProto.GetRecommendationCardSendRecordListRequest.newBuilder();
        builder.setPageNumber(request.getPageNo());
        builder.setPageSize(request.getPageSize());
        builder.setStartTime(DateUtil.formatDateNormal(DateUtil.getDayStart(request.getStartTime())));
        builder.setEndTime(DateUtil.formatDateNormal(DateUtil.getDayEnd(request.getEndTime())));
        builder.setUserId(0);
        if (request.getUserId() != null) {
            builder.setUserId(request.getUserId());
        }

        Result<PpRecommendationCardManageServiceProto.ResponseGetRecommendationCardSendRecordList> result = ppRecommendationCardManageService.getRecommendationCardSendRecordList(builder.build());
        if (RpcResult.isFail(result)) {
            log.warn("getRecommendationCardSendRecordList fail. rCode={},request={}", result.rCode(), request);
            return PageBean.empty();
        }

        List<RecommendCardSendRecordBean> beanList = new ArrayList<>();
        for (PpRecommendationCardManageServiceProto.SendRecord sendRecord : result.target().getRecordList()) {
            beanList.add(buildSendRecordBean(sendRecord));
        }

        return PageBean.of((int) result.target().getTotal(), beanList);
    }

    private RecommendCardSendRecordBean buildSendRecordBean(PpRecommendationCardManageServiceProto.SendRecord sendRecord){
        RecommendCardSendRecordBean bean = new RecommendCardSendRecordBean();
        bean.setId(sendRecord.getSendRecordId());
        bean.setUserId(sendRecord.getUserId());
        bean.setUserBand(sendRecord.getBand());
        bean.setUserName(sendRecord.getUserName());
        bean.setSendTime(DateUtil.formatStrToNormalDate(sendRecord.getSendTime()));
        bean.setSendNum(sendRecord.getNums());
        bean.setReason(sendRecord.getReason());
        if (sendRecord.getStatus() == 1) {
            bean.setStatus(ResourceDeliverStatusEnum.DELIVER_SUCCESS.getValue());
        } else if (sendRecord.getStatus() == 2) {
            bean.setStatus(ResourceDeliverStatusEnum.RECYCLE.getValue());
        } else {
            bean.setStatus(ResourceDeliverStatusEnum.UNKNOWN.getValue());
        }
        return bean;
    }

    @Override
    public List<BatchSendUserResultBean> batchSend(RequestBatchSend request) {
        List<SendRecommendCardBean> sendRecommendCards = request.getSendRecommendCards();
        if (CollectionUtils.isEmpty(sendRecommendCards)) {
            return Collections.emptyList();
        }

        List<BatchSendUserResultBean> resultList = new CopyOnWriteArrayList<>();
        List<List<SendRecommendCardBean>> partition = Lists.partition(sendRecommendCards, 20);
        CountDownLatchWrapper downLatchWrapper = new CountDownLatchWrapper(ThreadConstants.batchSendRecommendCardPool, 4, partition.size());

        for (List<SendRecommendCardBean> cardBeans : partition) {
            downLatchWrapper.submit(()->{

                for (SendRecommendCardBean sendRecommendCard : cardBeans) {
                    PpRecommendationCardManageServiceProto.SendUserRecommendationCardRequest.Builder builder = PpRecommendationCardManageServiceProto.SendUserRecommendationCardRequest.newBuilder();
                    builder.setNums(sendRecommendCard.getSendNum())
                            .setOperator(request.getOperator())
                            .setUserId(sendRecommendCard.getUserId())
                            .setReason(sendRecommendCard.getReason());
                    Result<PpRecommendationCardManageServiceProto.ResponseSendUserRecommendationCard> result = ppRecommendationCardManageService.sendUserRecommendationCard(builder.build());
                    log.info("batchSendRecommendCard rCode={}, request={}", result.rCode(), sendRecommendCard);
                    if (RpcResult.isFail(result)) {
                        resultList.add(new BatchSendUserResultBean()
                                .setUserId(sendRecommendCard.getUserId())
                                .setMsg("服务异常")
                                .setResultCode(result.rCode()));
                    }
                }

            });
        }

        downLatchWrapper.await();
        return resultList;
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public RewardResultBean rewardRecommendCard(Long operatorUserId, List<RecommendCardRewardBean> rewardBeans) {
        List<PpRecommendationCardManageServiceProto.FamilyBatchSendUserRecommendationCardRequest> njInfoList= new ArrayList<>(rewardBeans.size());
        for (RecommendCardRewardBean rewardBean : rewardBeans) {
            PpRecommendationCardManageServiceProto.FamilyBatchSendUserRecommendationCardRequest.Builder builder =
                    PpRecommendationCardManageServiceProto.FamilyBatchSendUserRecommendationCardRequest.newBuilder();
            builder.setNums(rewardBean.getNum()).setUserId(rewardBean.getTargetUserId());
            njInfoList.add(builder.build());
        }

        Result<PpRecommendationCardManageServiceProto.ResponseFamilyBatchSendUserRecommendationCard> result =
                ppRecommendationCardManageService.familyBatchSendUserRecommendationCard(operatorUserId, njInfoList);
        if (RpcResult.isFail(result)) {
            log.warn("pp rewardRecommendCard error, operatorUserId: {}, rewardBeans: {}", operatorUserId, rewardBeans);
            return new RewardResultBean().setCode(REWARD_RECOMMEND_CARD_ERROR).setRewardResult("请求失败，请联系管理员");
        }

        String rewardNote = result.target().getNote();

        if (StringUtils.isNotBlank(rewardNote)) {
            return new RewardResultBean().setCode(REWARD_RECOMMEND_CARD_NOT_SUCCESS).setRewardResult(rewardNote);
        }
        return new RewardResultBean().setCode(REWARD_RECOMMEND_CARD_SUCCESS).setRewardResult("success");
    }

    @Override
    public boolean recycle(RequestRecycle request) {
        WcAssert.notNull(request.getSendRecordId(), "sendRecordId is null");
        PpRecommendationCardManageServiceProto.RecycleUserRecommendationCardRequest.Builder builder = PpRecommendationCardManageServiceProto.RecycleUserRecommendationCardRequest.newBuilder();
        builder.setSendRecordId(request.getSendRecordId());
        builder.setOperator(request.getOperator());
        Result<PpRecommendationCardManageServiceProto.ResponseRecycleUserRecommendationCard> result = ppRecommendationCardManageService.recycleUserRecommendationCard(builder.build());
        if (RpcResult.isFail(result)) {
            log.warn("recycle error, rCode={},sendRecordId={}", result.rCode(), request.getSendRecordId());
            return false;
        }
        return true;
    }

    @Override
    public PageBean<RecommendAllocationRecordDTO> getAllocationRecord(RequestGetAllocationRecord request) {

        Result<PageDTO<DistributeAnchorRecordDTO>> result = recommendCardService.getFamilyRecommendRecord(new RequestGetFamilyRecommendRecord()
                .setFamilyUserId(request.getFamilyUserId())
                .setStartTime(request.getStartDate())
                .setEndTime(request.getEndDate())
                .setPageNo(request.getPageNum())
                .setRecUserId(request.getNjId())
                .setPageSize(request.getPageSize()));
        if (RpcResult.isFail(result)) {
            log.warn("getAllocationRecord error, rCode={},request={}", result.rCode(), request);
            return PageBean.empty();
        }

        List<Long> njIds = result.target().getList().stream()
                .map(DistributeAnchorRecordDTO::getNjId)
                .collect(Collectors.toList());
        Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(njIds);

        List<RecommendAllocationRecordDTO> dtos = new ArrayList<>();
        for (DistributeAnchorRecordDTO pb : result.target().getList()) {
            RecommendAllocationRecordDTO dto = new RecommendAllocationRecordDTO();
            dto.setNjId(pb.getNjId());
            dto.setAllocationTime(DateUtil.formatStrToDate(pb.getCreateTime(), DateUtil.NORMAL_DATE_FORMAT));
            dto.setNums(pb.getTotalRecommend());
            dto.setDetail("");

            SimpleUserDto userDto = userMap.get(pb.getNjId());
            // 拼接detail
            if (pb.getRecordType() == 1 && userDto != null) {
                // 1表示家族长给厅主分配
                dto.setDetail("分配给房间["+userDto.getName()+"]");
            }
            if (pb.getRecordType() == 2) {
                // 2后台给家族长分配
                dto.setDetail("收到奖励");
            }

            dtos.add(dto);
        }

        return PageBean.of(result.target().getTotal(), dtos);
    }

    @Override
    public PageBean<RecommendCardUseRecordDTO> getUseRecord(GetUseRecordParamDTO paramDTO) {

        RequestGetUseRecordPageList request = new RequestGetUseRecordPageList();
        request.setUserIds(paramDTO.getUserIds());
        request.setCreateTimeAsc(paramDTO.isCreateTimeAsc()?1:0);
        request.setPageNumber(paramDTO.getPageNumber());
        request.setPageSize(paramDTO.getPageSize());

        Result<PageDTO<RecommendationCardUseRecordDTO>> result = recommendCardService.getUseRecordPageList(request);
        if (RpcResult.isFail(result)) {
            log.warn("getUseRecordPageList fail. rCode={},request={}", result.rCode(), request);
            return PageBean.empty();
        }

        String positionNameConfig = resourceConfig.getPp().getRecommendCardPositionNameConfig();
        List<String> recommendCardPositionNameList = Lists.newArrayList();
        if (StringUtils.isNotBlank(positionNameConfig)) {
            recommendCardPositionNameList = Splitter.on(",").omitEmptyStrings().trimResults().splitToList(positionNameConfig);
        }

        List<RecommendCardUseRecordDTO> dtoList = new ArrayList<>();
        for (RecommendationCardUseRecordDTO pb : result.target().getList()) {
            RecommendCardUseRecordDTO dto = new RecommendCardUseRecordDTO();
            dto.setId(pb.getId());
            dto.setNjId(pb.getUserId());
            dto.setNums(pb.getNums());
            dto.setRecommendIndex(pb.getRecommendIndex());
            if (pb.getRecommendIndex() != null) {
                dto.setPosition(recommendCardPositionNameList.get(pb.getRecommendIndex() - 1));
            }
            dto.setRecommendTime(pb.getRecommendTime());
            dto.setUseTime(pb.getCreateTime());
            dtoList.add(dto);
        }

        return PageBean.of(result.target().getTotal(), dtoList);
    }
}
