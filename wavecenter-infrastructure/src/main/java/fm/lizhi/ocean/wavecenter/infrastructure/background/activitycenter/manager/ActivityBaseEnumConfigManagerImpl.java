package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.manager;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityToolBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.*;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.*;
import fm.lizhi.ocean.wavecenter.api.live.constants.RoomCategoryEnum;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseActivityConfigBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.live.constants.RoomCategoryOptionConstants;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.ActivityConfig;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityToolsManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityBaseEnumConfigManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityBaseEnumConfigManagerImpl implements ActivityBaseEnumConfigManager {


    @Autowired
    private ActivityToolsManager activityToolManager;

    @Autowired
    private ActivityConfig activityConfig;

    @Override
    public Result<ResponseActivityConfigBean> getBaseEnumConfig(int appId) {
        ResponseActivityConfigBean bean = new ResponseActivityConfigBean()
                .setApplyRuleList(buildActivityBaseConfigBeanList(ActivityApplyRuleEnum.class))
                .setDecorateTypeList(buildActivityBaseConfigBeanList(DecorateEnum.class))
                .setFodderClassificationList(buildActivityBaseConfigBeanList(ActivityFodderClassificationEnum.class))
                .setActivityGoalList(buildActivityBaseConfigBeanList(ActivityGoalEnum.class))
                .setAppInfoList(buildAppInfoList())
                .setActivityToolList(buildActivityTools(appId))
                .setAutoConfigResourceList(filterAutoConfigResourceList(buildAutoConfigResourceList(), appId))
                .setHeightLightList(buildActivityTemplateHighlightConfigBeanList())
                .setBigClassTypeList(buildActivityBigClassTypeConfigBeanList())
                .setRoomCategoryList(buildRoomCategoryList(appId))
                .setRewardTagList(buildActivityRewardTagConfigBeanList())
                ;
        if (log.isDebugEnabled()) {
            log.debug("bean={}", JsonUtil.dumps(bean));
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, bean);
    }

    private List<ActivityResourceEnumBean> buildActivityRewardTagConfigBeanList() {
        return Arrays.stream(AutoConfigResourceEnum.values())
                .filter(resourceEnum ->
                        resourceEnum.getResourceCode().equals(AutoConfigResourceEnum.REC_CARD.getResourceCode())
                        || resourceEnum.getResourceCode().equals(AutoConfigResourceEnum.BANNER.getResourceCode())
                        || resourceEnum.getResourceCode().equals(AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode()))
                .map( activityRewardTagEnum -> new ActivityResourceEnumBean()
                        .setName(activityRewardTagEnum.getResourceName())
                        .setCode(activityRewardTagEnum.getResourceCode())
                )
                .collect(Collectors.toList());
    }

    /**
     * 厅品列构建
     * @param appId
     * @return
     */
    private List<RoomCategoryBean> buildRoomCategoryList(int appId) {
        List<RoomCategoryBean> list = new ArrayList<>();
        List<Integer> supportRoomCategoryValues = activityConfig.getBizConfig(appId).getSupportRoomCategoryValues();
        for (Map.Entry<Integer, String> entry : RoomCategoryOptionConstants.valueMap.entrySet()) {
            Integer value = entry.getKey();
            if (!supportRoomCategoryValues.contains(value)) {
                continue;
            }
            list.add(new RoomCategoryBean()
                    .setName(entry.getValue())
                    .setType(value)
            );
        }
        return list;
    }

    private List<ActivityToolsConfigBean> buildActivityTools(int appId) {
        List<ActivityToolBean> tools = activityToolManager.getAllActivityTool(appId);
        List<ActivityToolsConfigBean> result = new ArrayList<>();
        for (ActivityToolBean bean : tools) {
            result.add(new ActivityToolsConfigBean()
                 .setName(bean.getName())
                 .setType(bean.getType())
                 .setToolType(bean.getToolType())
                 .setToolDesc(bean.getToolDesc())
            );
        }
        return result;
    }

    /**
     * 构建自动资源配置列表
     */
    private List<ActivityAutoConfigResourceBean> buildAutoConfigResourceList() {
        ArrayList<ActivityAutoConfigResourceBean> list = new ArrayList<>(AutoConfigResourceEnum.values().length);
        for (AutoConfigResourceEnum resourceEnum : AutoConfigResourceEnum.values()) {
            ActivityAutoConfigResourceBean bean = (ActivityAutoConfigResourceBean) new ActivityAutoConfigResourceBean()
                    .setResourceCode(resourceEnum.getResourceCode())
                    .setDeployType(resourceEnum.getDeployType())
                    .setSupportUpload(resourceEnum.isSupportUpload())
                    .setName(resourceEnum.getResourceName());
            list.add(bean);
        }

        return list;
    }

    private List<ActivityAutoConfigResourceBean> filterAutoConfigResourceList(List<ActivityAutoConfigResourceBean> resourceBeans, int appId) {
        // 小陪伴过滤掉节目单资源
        if (BusinessEvnEnum.HEI_YE.getAppId() == appId) {
            return resourceBeans.stream()
                    .filter(bean -> !AutoConfigResourceEnum.PROGRAMME.getResourceCode().equals(bean.getResourceCode()))
                    .collect(Collectors.toList());
        }
        return resourceBeans;
    }

    /**
     * 构建应用枚举
     */
    private List<ActivityBaseConfigBean> buildAppInfoList() {
        return Arrays.stream(BusinessEvnEnum.values())
                .map( business -> new ActivityBaseConfigBean().setType(business.getAppId()).setName(getAppCnName(business)))
                .collect(Collectors.toList());
    }

    private String getAppCnName(BusinessEvnEnum businessEvnEnum){
        if (businessEvnEnum == BusinessEvnEnum.HEI_YE) {
            return "小陪伴";
        }
        if (businessEvnEnum == BusinessEvnEnum.PP) {
            return "PP";
        }
        if (businessEvnEnum == BusinessEvnEnum.XIMI) {
            return "小西米";
        }
        return "";
    }

    /**
     * 构造枚举配置
     */
    private  <E extends Enum<E> & TypeNameProvider> List<ActivityBaseConfigBean> buildActivityBaseConfigBeanList(Class<E> enumClass) {
        List<ActivityBaseConfigBean> list = new ArrayList<>();
        for (E enumConstant : enumClass.getEnumConstants()) {
            list.add(new ActivityBaseConfigBean()
                    .setName(enumConstant.getName())
                    .setType(enumConstant.getType())
            );
        }
        return list;
    }

    private List<ActivityTemplateHighlightConfigBean> buildActivityTemplateHighlightConfigBeanList() {
        ActivityTemplateHighlightEnum[] activityTemplateHighlightEnums = ActivityTemplateHighlightEnum.values();
        List<ActivityTemplateHighlightConfigBean> list = new ArrayList<>(activityTemplateHighlightEnums.length);
        for (ActivityTemplateHighlightEnum activityTemplateHighlightEnum : activityTemplateHighlightEnums) {
            ActivityTemplateHighlightConfigBean bean = new ActivityTemplateHighlightConfigBean();
            bean.setCode(activityTemplateHighlightEnum.getCode());
            bean.setName(activityTemplateHighlightEnum.getName());
            list.add(bean);
        }
        return list;
    }

    private List<ActivityBigClassTypeConfigBean> buildActivityBigClassTypeConfigBeanList() {
        ActivityBigClassTypeEnum[] values = ActivityBigClassTypeEnum.values();
        List<ActivityBigClassTypeConfigBean> list = new ArrayList<>(values.length);
        for (ActivityBigClassTypeEnum value : values) {
            list.add(new ActivityBigClassTypeConfigBean()
                    .setType(value.getType())
                    .setName(value.getName())
            );
        }
        return list;
    }
}
