package fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WaveCheckInUserTask;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 麦序福利只读库扩展mapper, 用于web站数据统计
 */
@DataStore(namespace = "mysql_ocean_wave_r")
public interface WaveCheckInUserTaskExtMapper {

    @Select("<script>\n" +
            "  SELECT * FROM wave_check_in_user_task\n" +
            "  WHERE record_id IN\n" +
            "    <foreach collection=\"recordIds\" item=\"recordId\" open=\"(\" separator=\",\" close=\")\">\n" +
            "      #{recordId}\n" +
            "    </foreach>\n" +
            "</script>")
    List<WaveCheckInUserTask> getCheckInUserTasksByRecordIds(@Param("recordIds") List<Long> recordIds);
}
