package fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.dao;

import cn.hutool.core.collection.CollUtil;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.*;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.ext.WcDataPlayerHourRealTimeExtMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.mapper.WaveCheckInAllMicGiftRecordMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.mapper.WaveCheckInDayMicRecordMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.mapper.WaveCheckInManagerConfigMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.mapper.WaveCheckInScheduleMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.mapper.ext.*;
import fm.lizhi.ocean.wavecenter.infrastructure.live.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 麦序福利数据统计访问层, 用于单独从只读库读取数据, 避免对主库造成压力
 */
@Repository
@Slf4j
public class WaveCheckInDataDao {

    @Autowired
    private WaveCheckInScheduleExtMapper waveCheckInScheduleExtMapper;

    @Autowired
    private WaveCheckInRecordExtMapper waveCheckInRecordExtMapper;

    @Autowired
    private WaveCheckInUserTaskExtMapper waveCheckInUserTaskExtMapper;

    @Autowired
    private WaveCheckInUnDoneExtMapper waveCheckInUnDoneExtMapper;

    @Autowired
    private WaveCheckInLightGiftRecordExtMapper waveCheckInLightGiftRecordExtMapper;

    @Autowired
    private WaveCheckInScheduleMapper waveCheckInScheduleMapper;

    @Autowired
    private WaveCheckInAllMicGiftRecordMapper allMicGiftRecordMapper;

    @Autowired
    private WaveCheckInDayMicRecordMapper dayMicRecordMapper;

    @Autowired
    private WcDataPlayerHourRealTimeExtMapper playerHourRealTimeExtMapper;

    @Autowired
    private WaveCheckInManagerConfigMapper checkInManagerConfigMapper;

    /**
     * 查询日麦序奖励
     * @return
     */
    public List<WaveCheckInDayMicRecord> getDayMicRecords(Integer appId, Long familyId, Long roomId, List<Long> userIds
            , Long startDay, Long endDay) {
        if (startDay == null || endDay == null) {
            //必须指定时间范围
            return Collections.emptyList();
        }
        WaveCheckInDayMicRecordExample example = new WaveCheckInDayMicRecordExample();
        WaveCheckInDayMicRecordExample.Criteria criteria = example.createCriteria();
        if (appId != null) {
            criteria.andAppIdEqualTo(appId);
        }
        if (familyId != null) {
            criteria.andFamilyIdEqualTo(familyId);
        }
        if (roomId != null) {
            criteria.andRoomIdEqualTo(roomId);
        }
        if (CollectionUtils.isNotEmpty(userIds)) {
            criteria.andUserIdIn(userIds);
        }
        criteria.andCalcDateBetween(new Date(startDay), new Date(endDay));
        return this.dayMicRecordMapper.selectByExample(example);
    }

    /**
     * 查询全麦奖励记录
     *
     * @param scheduleIds
     * @return
     */
    public List<WaveCheckInAllMicGiftRecord> getAllMicGiftRecords(List<Long> scheduleIds){
        if (CollectionUtils.isEmpty(scheduleIds)) {
            return Collections.emptyList();
        }
        WaveCheckInAllMicGiftRecordExample example = new WaveCheckInAllMicGiftRecordExample();
        example.createCriteria().andScheduleIdIn(scheduleIds).andAllocationUserIdIsNotNull();
        return allMicGiftRecordMapper.selectByExample(example);
    }

    /**
     * 查询全麦奖励记录
     *
     * @param scheduleIds
     * @param playerId
     * @return
     */
    public List<WaveCheckInAllMicGiftRecord> getAllMicGiftRecordsByUserId(List<Long> scheduleIds, Long playerId){
        if (CollectionUtils.isEmpty(scheduleIds)) {
            return Collections.emptyList();
        }
        WaveCheckInAllMicGiftRecordExample example = new WaveCheckInAllMicGiftRecordExample();
        example.createCriteria().andScheduleIdIn(scheduleIds)
                .andAllocationUserIdEqualTo(playerId)
                .andAllocationUserIdIsNotNull();
        return allMicGiftRecordMapper.selectByExample(example);
    }

    /**
     * 获取麦序福利房间档期列表, 注意外部调用方应控制好查询的时间范围, 避免查询过多数据
     *
     * @param roomId    房间id
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param familyId  家族id, 可选的过滤条件
     * @return 档期列表
     */
    public List<WaveCheckInSchedule> getCheckInRoomSchedules(long roomId, long startDate, long endDate, Long familyId) {
        return waveCheckInScheduleExtMapper.getCheckInRoomSchedules(roomId, new Date(startDate), new Date(endDate), familyId);
    }

    /**
     * 根据档期id列表获取麦序福利收入汇总
     *
     * @param scheduleIds 档期id列表
     * @return 房间收入汇总
     */
    public WaveCheckInIncomeSumDTO getCheckInIncomeSum(List<Long> scheduleIds) {
        if (CollectionUtils.isEmpty(scheduleIds)) {
            return new WaveCheckInIncomeSumDTO();
        }
        return waveCheckInRecordExtMapper.getCheckInIncomeSum(scheduleIds);
    }

    /**
     * 获取麦序福利排挡主播数(去重)
     *
     * @param scheduleIds 档期id列表
     * @return 排挡主播数
     */
    public int getCheckInScheduledPlayerCnt(List<Long> scheduleIds) {
        if (CollectionUtils.isEmpty(scheduleIds)) {
            return 0;
        }
        return waveCheckInRecordExtMapper.getCheckInScheduledPlayerCnt(scheduleIds);
    }

    /**
     * 根据档期id列表获取麦序福利用户打卡记录列表
     *
     * @param scheduleIds 档期id列表
     * @return 用户打卡记录列表
     */
    public List<WaveCheckInRecord> getCheckInRecordsByScheduleIds(List<Long> scheduleIds) {
        if (CollectionUtils.isEmpty(scheduleIds)) {
            return Collections.emptyList();
        }
        return waveCheckInRecordExtMapper.getCheckInRecordsByScheduleIds(scheduleIds);
    }

    /**
     * 根据记录id列表获取麦序福利用户任务列表
     *
     * @param recordIds 记录id列表
     * @return 用户任务列表
     */
    public List<WaveCheckInUserTask> getCheckInUserTasksByRecordIds(List<Long> recordIds) {
        if (CollectionUtils.isEmpty(recordIds)) {
            return Collections.emptyList();
        }
        return waveCheckInUserTaskExtMapper.getCheckInUserTasksByRecordIds(recordIds);
    }

    /**
     * 获取麦序福利房间用户未完成打卡记录列表
     *
     * @param userIds   用户id列表
     * @param roomId    房间id
     * @param startDate 开始日期, 包含
     * @param endDate   结束日期, 包含
     * @return 房间用户未完成打卡记录列表
     */
    public List<WaveCheckInUnDone> getCheckInRoomUserUnDoneList(List<Long> userIds, long roomId, long startDate, long endDate) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        return waveCheckInUnDoneExtMapper.getCheckInRoomUserUnDoneList(userIds, roomId, new Date(startDate), new Date(endDate));
    }

    /**
     * 根据档期id列表麦序福利厅用户收光礼物分组数据列表
     *
     * @param scheduleIds 档期id列表
     * @return 用户收光礼物分组数据列表
     */
    public List<WaveCheckInRoomUserLightGiftGroupDTO> getCheckInRoomUserLightGiftGroups(List<Long> scheduleIds) {
        if (CollectionUtils.isEmpty(scheduleIds)) {
            return Collections.emptyList();
        }
        return waveCheckInLightGiftRecordExtMapper.getCheckInRoomUserLightGiftGroups(scheduleIds);
    }

    /**
     * 根据档期id列表获取麦序福利用户收光奖励金额分组数据列表
     *
     * @param scheduleIds 档期id列表
     * @return 用户收光奖励金额分组数据列表
     */
    public List<WaveCheckInRoomUserRewardAmountGroupDTO> getCheckInRoomUserRewardAmountGroups(List<Long> scheduleIds) {
        if (CollectionUtils.isEmpty(scheduleIds)) {
            return Collections.emptyList();
        }
        return waveCheckInLightGiftRecordExtMapper.getCheckInRoomUserRewardAmountGroups(scheduleIds);
    }

    /**
     * 获取麦序福利主播汇总数据
     *
     * @param playerId  主播ID
     * @param startDate 开始日期, 包含
     * @param endDate   结束日期, 包含
     * @param familyId  家族ID, 可选的过滤条件
     * @param roomId    房间ID, 可选的过滤条件
     * @return 主播汇总数据
     */
    public WaveCheckInPlayerSumDTO getCheckInPlayerSum(long playerId, long startDate, long endDate, Long familyId, Long roomId) {
        WaveCheckInPlayerSumDTO dto = waveCheckInRecordExtMapper.getCheckInPlayerSum(playerId, new Date(startDate), new Date(endDate), familyId, roomId);
        long hostCount = countCheckInSchedulesByHostId(playerId, startDate, endDate, familyId, roomId);
        if (dto == null) {
            dto = new WaveCheckInPlayerSumDTO().setCharm(0L).setIncome(0L).setSeatCnt(0);
        }
        dto.setHostCnt((int) hostCount);
        return dto;
    }

    /**
     * 获取麦序福利主播档期列表
     *
     * @param playerId  主播ID
     * @param startDate 开始日期, 包含
     * @param endDate   结束日期, 包含
     * @param familyId  家族ID, 可选的过滤条件
     * @param roomId    房间ID, 可选的过滤条件
     * @return 主播档期列表
     */
    public List<WaveCheckInSchedule> getCheckInPlayerSchedules(long playerId, long startDate, long endDate, Long familyId, Long roomId) {
        return waveCheckInScheduleExtMapper.getCheckInPlayerSchedules(playerId, new Date(startDate), new Date(endDate), familyId, roomId);
    }


    /**
     * 获取麦序福利主播或主持的档期列表
     *
     * @param playerId  主播ID
     * @param startDate 开始日期, 包含
     * @param endDate   结束日期, 包含
     * @param familyId  家族ID, 可选的过滤条件
     * @param roomId    房间ID, 可选的过滤条件
     * @return 主播档期列表
     */
    public List<WaveCheckInSchedule> getCheckInPlayerAndHostSchedules(long playerId, long startDate, long endDate, Long familyId, Long roomId) {
        List<WaveCheckInSchedule> playerScheduleList = waveCheckInScheduleExtMapper.getCheckInPlayerSchedules(playerId, new Date(startDate), new Date(endDate), familyId, roomId);
        List<WaveCheckInSchedule> hostScheduleList = getCheckInSchedulesByHostId(playerId, startDate, endDate, familyId, roomId);

        return CollUtil.distinct(CollUtil.addAll(playerScheduleList, hostScheduleList), WaveCheckInSchedule::getId, true);
    }

    /**
     * 获取主播打卡记录列表
     *
     * @param scheduleIds 档期id列表
     * @return 主播打卡记录列表
     */
    public List<WaveCheckInRecord> getCheckInPlayerRecords(List<Long> scheduleIds, long playerId) {
        if (CollectionUtils.isEmpty(scheduleIds)) {
            return Collections.emptyList();
        }
        return waveCheckInRecordExtMapper.getCheckInPlayerRecords(scheduleIds, playerId);
    }

    /**
     * 获取麦序福利用户未完成打卡记录列表
     *
     * @param userId    用户id
     * @param roomIds   房间id列表
     * @param startDate 开始日期, 包含
     * @param endDate   结束日期, 包含
     * @param familyId  家族ID, 可选的过滤条件
     * @return 用户未完成打卡记录列表
     */
    public List<WaveCheckInUnDone> getCheckInUserUnDoneList(long userId, List<Long> roomIds, long startDate, long endDate, Long familyId) {
        if (CollectionUtils.isEmpty(roomIds)) {
            return Collections.emptyList();
        }
        return waveCheckInUnDoneExtMapper.getCheckInUserUnDoneList(userId, roomIds, new Date(startDate), new Date(endDate), familyId);
    }

    /**
     * 根据档期id列表和收礼人id获取麦序福利用户收光礼物分组数据列表
     *
     * @param scheduleIds 档期id列表
     * @param recUserId   收礼人id
     * @return 用户收光礼物分组数据列表
     */
    public List<WaveCheckInUserLightGiftGroupDTO> getCheckInUserLightGiftGroups(List<Long> scheduleIds, long recUserId) {
        if (CollectionUtils.isEmpty(scheduleIds)) {
            return Collections.emptyList();
        }
        return waveCheckInLightGiftRecordExtMapper.getCheckInUserLightGiftGroups(scheduleIds, recUserId);
    }

    /**
     * 根据档期id列表和收礼人id获取麦序福利用户收光奖励金额分组数据列表
     *
     * @param scheduleIds 档期id列表
     * @param recUserId   收礼人id
     * @return 用户收光奖励金额分组数据列表
     */
    public List<WaveCheckInUserRewardAmountGroupDTO> getCheckInUserRewardAmountGroups(List<Long> scheduleIds, long recUserId) {
        if (CollectionUtils.isEmpty(scheduleIds)) {
            return Collections.emptyList();
        }
        return waveCheckInLightGiftRecordExtMapper.getCheckInUserRewardAmountGroups(scheduleIds, recUserId);
    }

    /**
     * 统计指定主持的档期记录数
     */
    public long countCheckInSchedulesByHostId(long hostId, long startDate, long endDate, Long familyId, Long roomId) {
        WaveCheckInScheduleExample waveCheckInScheduleExample = new WaveCheckInScheduleExample();
        WaveCheckInScheduleExample.Criteria criteria = waveCheckInScheduleExample.createCriteria()
                .andHostIdEqualTo(hostId)
                .andStartTimeBetween(new Date(startDate), new Date(endDate));

        if (roomId != null) {
            criteria.andRoomIdEqualTo(roomId);
        }
        if (familyId!= null) {
            criteria.andFamilyIdEqualTo(familyId);
        }
        return waveCheckInScheduleMapper.countByExample(waveCheckInScheduleExample);
    }


    /**
     * 获取指定主持的档期记录
     */
    public List<WaveCheckInSchedule> getCheckInSchedulesByHostId(long hostId, long startDate, long endDate, Long familyId, Long roomId) {
        WaveCheckInScheduleExample waveCheckInScheduleExample = new WaveCheckInScheduleExample();
        WaveCheckInScheduleExample.Criteria criteria = waveCheckInScheduleExample.createCriteria()
                .andHostIdEqualTo(hostId)
                .andStartTimeBetween(new Date(startDate), new Date(endDate));

        if (roomId != null) {
            criteria.andRoomIdEqualTo(roomId);
        }
        if (familyId!= null) {
            criteria.andFamilyIdEqualTo(familyId);
        }

        return waveCheckInScheduleMapper.selectByExample(waveCheckInScheduleExample);
    }

    /**
     * 获取指定小时的私信报告
     */
    public List<WcDataPlayerHourRealTime> getHourPlayerChatStatList(int appId, List<Long> playerIds, Date hourTime) {
        return playerHourRealTimeExtMapper.getWcDataPlayerHourRealTimes(appId, playerIds, hourTime);
    }


    /**
     * 获取管理员
     * @param appId
     * @param njId
     * @return
     */
    public List<Long> getManagerIds(Integer appId, Long njId) {
        WaveCheckInManagerConfig config = new WaveCheckInManagerConfig();
        config.setAppId(appId);
        config.setNjId(njId);
        List<WaveCheckInManagerConfig> waveCheckInManagerConfigs = checkInManagerConfigMapper.selectMany(config);
        if(CollectionUtils.isEmpty(waveCheckInManagerConfigs)) {
            return Collections.emptyList();
        }
        return waveCheckInManagerConfigs.stream().map(WaveCheckInManagerConfig::getManagerId).collect(Collectors.toList());
    }
}
