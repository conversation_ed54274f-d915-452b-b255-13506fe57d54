package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote.pp;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.IncomeBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomAssessmentInfoBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.ThreadConstants;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PaySettleConfigCodeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.PpPlayerSignCharmStatMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.PlayerSignPerformancePo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote.IRoomDataRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.income.manager.PaymentManager;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.AssessTimeDto;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.GetRoomPlayerPerformanceParamDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2024/5/23 16:15
 */
@Component
public class PpRoomDataRemote implements IRoomDataRemote {

    @Autowired
    private PaymentManager paymentManager;

    @Autowired
    private PpPlayerSignCharmStatMapper ppPlayerSignCharmStatMapper;

    @Override
    public RoomAssessmentInfoBean queryAssessment(Long familyId, Long roomId) {
        RoomAssessmentInfoBean roomAssessmentInfoBean = new RoomAssessmentInfoBean();
        CompletableFuture.allOf(
                CompletableFuture.runAsync(() -> {
                    IncomeBean sumIncome = paymentManager.getIncomeBeanByRoom(familyId, roomId, PaySettleConfigCodeEnum.HALL_INCOME_TOTAL_AMOUNT);
                    roomAssessmentInfoBean.setSumIncome(sumIncome);
                }, ThreadConstants.roomDatePool),
                CompletableFuture.runAsync(() -> {
                    //签约厅
                    IncomeBean roomIncome = paymentManager.getIncomeBeanByRoom(familyId, roomId, PaySettleConfigCodeEnum.HALL_HALL_INCOME_TOTAL_AMOUNT);
                    roomAssessmentInfoBean.setRoomIncome(roomIncome);
                }, ThreadConstants.roomDatePool),
                CompletableFuture.runAsync(() -> {
                    //个播
                    IncomeBean individualIncome = paymentManager.getIncomeBeanByRoom(familyId, roomId, PaySettleConfigCodeEnum.HALL_INDIVIDUAL_INCOME_TOTAL_AMOUNT);
                    roomAssessmentInfoBean.setIndividualIncome(individualIncome);
                }, ThreadConstants.roomDatePool),
                CompletableFuture.runAsync(() -> {
                    IncomeBean vipIncome = paymentManager.getIncomeBeanByRoom(familyId, roomId, PaySettleConfigCodeEnum.HALL_NOBILITY_ROYALTY_INCOME_TOTAL_AMOUNT);
                    roomAssessmentInfoBean.setVipIncome(vipIncome);
                }, ThreadConstants.roomDatePool),
                CompletableFuture.runAsync(() -> {
                    IncomeBean officialIncome = paymentManager.getIncomeBeanByRoom(familyId, roomId, PaySettleConfigCodeEnum.HALL_OFFICIAL_HALL_INCOME_TOTAL_AMOUNT);
                    roomAssessmentInfoBean.setOfficialIncome(officialIncome);
                }, ThreadConstants.roomDatePool)
        ).join();

        return roomAssessmentInfoBean;
    }

    @Override
    public PageList<PlayerSignPerformancePo> selectPlayerSignPerformance(GetRoomPlayerPerformanceParamDto paramDto) {
        AssessTimeDto assessTime = paramDto.getAssessTime();
        String startDate = DateUtil.formatDateToString(assessTime.getStartDate(), DateUtil.date_2);
        String endDate = DateUtil.formatDateToString(assessTime.getEndDate(), DateUtil.date_2);
        return ppPlayerSignCharmStatMapper.selectPlayerSignCharmSum(paramDto.getRoomId()
                , paramDto.getFamilyId()
                , startDate
                , endDate
                , paramDto.getOrderType().getValue()
                , paramDto.getPageNo()
                , paramDto.getPageSize()
        );
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }
}
