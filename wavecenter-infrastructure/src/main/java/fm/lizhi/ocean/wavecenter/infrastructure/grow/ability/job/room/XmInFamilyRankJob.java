package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.job.room;

import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/6/11 14:16
 */
@Slf4j
@Component
public class XmInFamilyRankJob extends AbstractInFamilyRankJob implements JobHandler {

    @Override
    public void execute(JobExecuteContext jobExecuteContext) throws Exception {
        String paramJson = jobExecuteContext.getParam();
        log.info("XmInFamilyRankJob start`param={}", paramJson);
        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.XIMI);
        super.doSettle(JsonUtil.loads(paramJson, InFamilyRankParam.class));
    }

}
