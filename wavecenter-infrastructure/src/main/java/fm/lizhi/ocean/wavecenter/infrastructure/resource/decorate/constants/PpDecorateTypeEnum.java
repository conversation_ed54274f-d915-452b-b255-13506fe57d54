package fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.constants;

import java.util.Arrays;

import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.pp.vip.constant.DecorateTypeEnum;

public enum PpDecorateTypeEnum {

    /**
     * 座驾
     */
    VEHICLE(DecorateTypeEnum.MOUNT.getType(), PlatformDecorateTypeEnum.VEHICLE),

    /**
     * 勋章
     */
    MEDAL(PpDecorateTypeConstants.MEDAL_TYPE, PlatformDecorateTypeEnum.MEDAL),
    
    /**
     * 头像框
     */
    AVATAR(DecorateTypeEnum.AVATAR_WIDGET.getType(), PlatformDecorateTypeEnum.AVATAR),
    
    /**
     * 背景
     */
    BACKGROUND(DecorateTypeEnum.BACKGROUND.getType(), PlatformDecorateTypeEnum.BACKGROUND),
    
    /**
     * 用户官方认证
     */
    USER_GLORY(DecorateTypeEnum.USER_GLORY.getType(), PlatformDecorateTypeEnum.USER_GLORY),

    /**
     * 气泡 历史原因 维护两份
     */
    BUBBLE(DecorateTypeEnum.BUBBLE.getType(), PlatformDecorateTypeEnum.BUBBLE);

    private final int type;

    private final PlatformDecorateTypeEnum decorateTypeEnum;
    PpDecorateTypeEnum(int type, PlatformDecorateTypeEnum decorateTypeEnum) {
        this.type = type;
        this.decorateTypeEnum = decorateTypeEnum;   
    }

    public int getType() {
        return type;
    }

    public PlatformDecorateTypeEnum getDecorateTypeEnum() {
        return decorateTypeEnum;
    }

    public static PpDecorateTypeEnum getByType(PlatformDecorateTypeEnum platformDecorateTypeEnum) {
        return Arrays.stream(PpDecorateTypeEnum.values())
                .filter(ppDecorateTypeEnum -> ppDecorateTypeEnum.getDecorateTypeEnum().equals(platformDecorateTypeEnum))
                .findFirst()
                .orElse(null);
    }

    public int calValidMin(Integer decorateExpireTime) {
        //@why pp的官方认证不能配置有效期 需要手动传
        if(decorateExpireTime <= 0 && decorateTypeEnum == PlatformDecorateTypeEnum.USER_GLORY) {
            // 默认官方认证有效期到2099年
            return MyDateUtil.getMinutesFromNow(DecorateConstant.DEFAULT_GLORY_VALID_TIME.toString());
        }
        return decorateExpireTime;
    }
}

/**
 * PP平台装扮类型常量
 */
interface PpDecorateTypeConstants {
    /**
     * 勋章类型
     */
    int MEDAL_TYPE = 99;
}
