package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityBigClassBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityClassConfigBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseActivityClassification;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityClassificationConfigBean;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityBigClass;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityClassConfig;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface ActivityClassificationConvert {

    ActivityClassificationConvert I = Mappers.getMapper(ActivityClassificationConvert.class);

    List<ActivityBigClassBean> convert2ActivityBigClassBeans(List<ActivityBigClass> list);

    @Mapping(target = "categoryList", ignore = true)
    ActivityBigClassBean convert2ActivityBigClassBean(ActivityBigClass po);

    List<ActivityClassConfigBean> convert2ActivityClassConfigBeans(List<ActivityClassConfig> list);

    @Mappings({
            @Mapping(source = "cls.id", target = "classId"),
            @Mapping(source = "cls.name", target = "className"),
            @Mapping(source = "activityBigClass.id", target = "bigClassId"),
            @Mapping(source = "activityBigClass.name", target = "bigClassName"),
            @Mapping(source = "activityBigClass.type", target = "bigClassType"),
    })
    ActivityClassificationConfigBean convert2ActivityClassificationBean(ActivityClassConfig cls, ActivityBigClass activityBigClass);

    @Mappings({
            @Mapping(source = "activityBigClass.id", target = "id"),
            @Mapping(source = "activityBigClass.name", target = "name"),
            @Mapping(source = "list", target = "classList"),
    })
    ResponseActivityClassification convert2ResponseActivityClassification(ActivityBigClass activityBigClass, List<ActivityClassConfig> list);

    default Long dateToLong(Date date) {
        return date != null ? date.getTime() : null;
    }
}
