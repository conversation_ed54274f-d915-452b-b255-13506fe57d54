package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.remote;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.base.remote.IRemote;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.OfficialSeatDetailDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ResultDTO;

public interface IOfficialSeatServiceRemote<T extends OfficialSeatDetailDTO> extends IRemote {

    /**
     * 保存官频位信息，简单配置，仅给活动中心提报的活动用
     *
     * @param t 参数
     * @return 结果
     */
    Result<ResultDTO> saveOfficialSeatWithActivity(T t);

    /**
     * 查询相同时间，同一个位置的配置
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param seat      位置
     * @param tabId     板块ID，例如：热推、小哥哥、小姐姐等板块
     * @return 结束时间
     */
    Result<T> findSameTimeOfficialSeatConfig(Long startTime, Long endTime, Integer seat, Long tabId);


    /**
     * 删除官频位
     *
     * @param bizRecordId 业务记录ID
     * @return 结果
     */
    Result<Void> deleteOfficialSeat(long bizRecordId);

    /**
     * 根据业务记录ID查询官频位信息
     *
     * @param bizRecordId 业务记录ID
     * @return 结果
     */
    Result<T> findOfficialSeatByRecordId(long bizRecordId);


    /**
     * 保存官频位失败
     */
    int SAVE_OFFICIAL_SEAT_WITH_ACTIVITY_FAIL = 1;

    /**
     * 官频位重复
     */
    int SAVE_OFFICIAL_SEAT_WITH_ACTIVITY_DUPLICATE_POSITION = 2;

    /**
     * 不存在相同时间的配置
     */
    int FIND_SAME_TIME_OFFICIAL_SEAT_CONFIG_NOT_SAME = 1;

    /**
     * 相同时间存在相同的位置
     */
    int FIND_SAME_TIME_OFFICIAL_SEAT_CONFIG_SAME_TIME_SEAT = 2;

    /**
     * 查询失败
     */
    int FIND_SAME_TIME_OFFICIAL_SEAT_CONFIG_FAIL = 3;

    /**
     * 查询官频位失败
     */
    int FIND_OFFICIAL_SEAT_BY_RECORD_ID_FAIL = 1;

    /**
     * 官频位不存在
     */
    int FIND_OFFICIAL_SEAT_BY_RECORD_ID_NOT_EXIST = 2;

    /**
     * 删除官频位失败
     */
    int DELETE_OFFICIAL_SEAT_FAIL = 1;




}
