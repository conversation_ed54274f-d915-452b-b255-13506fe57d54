package fm.lizhi.ocean.wavecenter.infrastructure.message.datastore.dao;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.api.message.bean.UnReadMessageCountBean;
import fm.lizhi.ocean.wavecenter.infrastructure.message.constants.WcNoticeStatusConstants;
import fm.lizhi.ocean.wavecenter.datastore.platform.message.entity.WcNoticeConfig;
import fm.lizhi.ocean.wavecenter.datastore.platform.message.entity.WcNoticeConfigExample;
import fm.lizhi.ocean.wavecenter.infrastructure.message.datastore.mapper.WcNoticeConfigExtMapper;
import fm.lizhi.ocean.wavecenter.datastore.platform.message.mapper.WcNoticeConfigMapper;
import fm.lizhi.ocean.wavecenter.service.message.dto.WcNoticeConfigQueryDTO;
import fm.lizhi.ocean.wavecenter.service.message.dto.WcNoticeConfigQueryParamDTO;
import fm.lizhi.ocean.wavecenter.service.message.dto.WcNoticeUpdateStatusDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * 通知配置DAO
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
@Repository
@RequiredArgsConstructor
public class WcNoticeConfigDao {

    @Autowired
    private WcNoticeConfigExtMapper wcNoticeConfigExtMapper;

    @Autowired
    private WcNoticeConfigMapper wcNoticeConfigMapper;

    /**
     * 保存公告配置
     *
     * @param config 公告配置
     * @return 保存结果
     */
    public int saveWcNoticeConfig(WcNoticeConfig config) {
        config.setDeployEnv(ConfigUtils.getEnvRequired().name());
        config.setDeleted(Boolean.FALSE);
        config.setStatus(WcNoticeStatusConstants.STATUS_UP);
        return wcNoticeConfigMapper.insert(config);
    }

    /**
     * 更新公告配置
     *
     * @param config 公告配置
     * @return 更新结果
     */
    public int updateWcNoticeConfig(WcNoticeConfig config) {
        //根据id更新
        WcNoticeConfig entity = new WcNoticeConfig();
        entity.setId(config.getId());
        entity.setOperator(config.getOperator());
        if (config.getTitle() != null) {
            entity.setTitle(config.getTitle());
        }
        if (config.getContent() != null) {
            entity.setContent(config.getContent());
        }
        if (config.getEffectTime() != null) {
            entity.setEffectTime(config.getEffectTime());
        }
        if (config.getType() != null) {
            entity.setType(config.getType());
        }
        return wcNoticeConfigMapper.updateByPrimaryKey(entity);
    }

    /**
     * 查询公告配置
     *
     * @param queryDTO 查询条件
     * @return 公告配置列表
     */
    public PageList<WcNoticeConfig> queryWcNoticeConfigPage(WcNoticeConfigQueryDTO queryDTO) {
        WcNoticeConfigExample example = new WcNoticeConfigExample();
        WcNoticeConfigExample.Criteria criteria = example.createCriteria();
        if (StringUtils.hasText(queryDTO.getTitle())) {
            criteria.andTitleLike("%" + queryDTO.getTitle() + "%");
        }
        if (queryDTO.getType() != null) {
            criteria.andTypeEqualTo(queryDTO.getType());
        }
        if (queryDTO.getStatus() != null) {
            criteria.andStatusEqualTo(queryDTO.getStatus());
        }

        if (queryDTO.getMinEffectTime() != null) {
            criteria.andEffectTimeGreaterThanOrEqualTo(queryDTO.getMinEffectTime());
        }
        if (queryDTO.getMaxEffectTime() != null) {
            criteria.andEffectTimeLessThanOrEqualTo(queryDTO.getMaxEffectTime());
        }

        // 只查当前环境
        criteria.andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        criteria.andAppIdEqualTo(queryDTO.getAppId());
        criteria.andDeletedEqualTo(Boolean.FALSE);
        return wcNoticeConfigMapper.pageByExample(example, queryDTO.getPageNo(), queryDTO.getPageSize());
    }

    /**
     * 根据id删除公告配置
     *
     * @param id 公告配置id
     * @return 删除结果
     */
    public int deleteWcNoticeConfigById(Long id) {
        WcNoticeConfig entity = new WcNoticeConfig();
        entity.setId(id);
        entity.setDeleted(Boolean.TRUE);
        return wcNoticeConfigMapper.updateByPrimaryKey(entity);
    }

    /**
     * 根据生效时间查询公告配置
     *
     * @param queryDTO 查询条件
     * @return 公告配置列表
     */
    public List<WcNoticeConfig> queryWcNoticeConfigByEffectTimePage(WcNoticeConfigQueryParamDTO queryDTO) {
        Date lastMaxEffectTime = queryDTO.getLastMaxEffectTime() != null ? new Date(queryDTO.getLastMaxEffectTime()) : new Date(0);
        return wcNoticeConfigExtMapper.selectByPage(queryDTO.getAppId(), queryDTO.getType(), lastMaxEffectTime, new Date(),
                WcNoticeStatusConstants.STATUS_UP, ConfigUtils.getEnvRequired().name(), queryDTO.getPageSize());
    }

    /**
     * 统计未读消息数
     *
     * @param type                消息类型
     * @param appId               应用ID
     * @param effectTimeStamp     生效时间戳
     * @param showNoticeTimeStamp 显示通知时间戳
     * @param targetUserId        目标用户ID
     * @return 未读消息数
     */
    public List<UnReadMessageCountBean> countUnreadByType(Integer type, Integer appId,
                                                          Long effectTimeStamp, Long showNoticeTimeStamp,
                                                          Long targetUserId) {
        return wcNoticeConfigExtMapper.countUnreadByType(type, appId,
                effectTimeStamp, showNoticeTimeStamp,
                ConfigUtils.getEnvRequired().name(), targetUserId);
    }

    /**
     * 查询最近生效的公告配置
     *
     * @param size 条数
     * @return 公告配置列表
     */
    public List<WcNoticeConfig> queryRecentNotices(int appId, int size) {
        WcNoticeConfigExample example = new WcNoticeConfigExample();
        example.setOrderByClause("effect_time DESC limit " + size);
        WcNoticeConfigExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(appId);
        criteria.andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        criteria.andDeletedEqualTo(Boolean.FALSE);
        criteria.andStatusEqualTo(WcNoticeStatusConstants.STATUS_UP);
        criteria.andEffectTimeLessThanOrEqualTo(new Date());
        return wcNoticeConfigMapper.selectByExample(example);
    }

    /**
     * 查询生效公告数量
     *
     * @param appId 应用ID
     * @return 生效公告数量
     */
    public Long queryRecentNoticesCount(int appId) {
        WcNoticeConfigExample example = new WcNoticeConfigExample();
        WcNoticeConfigExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(appId);
        criteria.andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        criteria.andDeletedEqualTo(Boolean.FALSE);
        criteria.andStatusEqualTo(WcNoticeStatusConstants.STATUS_UP);
        criteria.andEffectTimeGreaterThanOrEqualTo(new Date());
        return wcNoticeConfigMapper.countByExample(example);
    }

    /**
     * 上下架
     *
     * @param configDTO 配置
     * @return 结果
     */
    public boolean updateNoticeStatus(WcNoticeUpdateStatusDTO configDTO) {
        WcNoticeConfig entity = new WcNoticeConfig();
        entity.setId(configDTO.getId());
        entity.setStatus(configDTO.getStatus());
        entity.setOperator(configDTO.getOperator());
        return wcNoticeConfigMapper.updateByPrimaryKey(entity) > 0;
    }

}
