package fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.xm;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.constants.FamilyTypeMapping;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.IUserFamilyRemote;
import fm.lizhi.xm.family.api.FamilyService;
import fm.lizhi.xm.family.api.PlayerSignService;
import fm.lizhi.xm.family.bean.FamilyInfo;
import fm.lizhi.xm.family.constants.UserType;
import fm.lizhi.xm.family.protocol.FamilyServiceProto;
import fm.lizhi.xm.family.protocol.PlayerSignServiceProto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class XmUserFamilyRemote implements IUserFamilyRemote {

    @Autowired
    private FamilyService familyService;
    @Autowired
    private CommonConfig commonConfig;
    @Autowired
    private PlayerSignService playerSignService;

    private final LoadingCache<Long, Optional<FamilyBean>> FAMILY_INFO_CACHE = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterAccess(10, TimeUnit.MINUTES)
            .build(new CacheLoader<Long, Optional<FamilyBean>>() {
                @Override
                public Optional<FamilyBean> load(Long familyId) {
                    return getFamily(familyId);
                }
            });

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.XIMI;
    }

    @Override
    public UserInFamilyBean getUserInFamily(long userId) {
        Result<FamilyServiceProto.ResponseGetUserFamily> res = familyService.getUserFamily(userId);
        if (RpcResult.isFail(res)) {
            log.warn("ximi getUserInFamily fail userId={},rCode={}", userId, res.rCode());
            return UserInFamilyBean.builder().build();
        }

        FamilyServiceProto.UserFamilyInfo userFamilyInfo = res.target().getUserFamilyInfo();
        int userType = userFamilyInfo.getUserType();
        return UserInFamilyBean.builder()
                .familyId(userFamilyInfo.getFamilyId())
                .njId(userFamilyInfo.getNjId())
                .isPlayer(userType == UserType.PLAYER.getIndex())
                .isRoom(userType == UserType.ADMIN.getIndex())
                .isFamily(userType == UserType.FAMILY.getIndex())
                .build();
    }

    @Override
    public Optional<FamilyBean> getUserFamily(long userId) {
        Result<FamilyServiceProto.ResponseGetUserFamily> result = familyService.getUserFamily(userId);
        if (RpcResult.isFail(result)) {
            log.warn("xm getUserFamily fail userId={},rCode={}", userId, result.rCode());
            return Optional.empty();
        }
        FamilyServiceProto.UserFamilyInfo userFamilyInfo = result.target().getUserFamilyInfo();
        
        long familyId = userFamilyInfo.getFamilyId();
        String familyName = userFamilyInfo.getFamilyName();

        Optional<FamilyBean> family = getFamily(familyId);

        return Optional.of(new FamilyBean()
                .setFamilyType(family.map(FamilyBean::getFamilyType).orElse(null))
                .setUserId(family.map(FamilyBean::getUserId).orElse(0L))
                .setId(familyId)
                .setUserType(userFamilyInfo.getUserType())
                .setFamilyName(familyName));
    }

    @Override
    public Optional<FamilyBean> getFamily(long familyId) {
        Result<FamilyServiceProto.ResponseGetFamilyInfo> familyInfoRes = familyService.getFamilyInfo(FamilyServiceProto.FamilyParam.newBuilder()
                .setFamilyId(familyId)
                .build());
        if (RpcResult.isFail(familyInfoRes)) {
            log.warn("ximi,getFamily,familyId={},rCode={}", familyId, familyInfoRes.rCode());
            return Optional.empty();
        }
        String familyJsonStr = familyInfoRes.target().getFamily();
        if (StringUtils.isBlank(familyJsonStr)) {
            return Optional.empty();
        }
        FamilyInfo familyInfo = JsonUtil.loads(familyJsonStr, FamilyInfo.class);
        FamilyBean bean = new FamilyBean();
        bean.setId(familyId);
        bean.setFamilyName(familyInfo.getFamilyName());
        bean.setUserId(familyInfo.getUserId());
        bean.setFamilyNote(familyInfo.getFamilyNote());
        bean.setFamilyIconUrl(UrlUtils.getImageUrl(commonConfig.getXm().getCdnHost(), familyInfo.getFamilyIconUrl()));
        bean.setCreateTime(familyInfo.getCreateTime());
        bean.setFamilyType(FamilyTypeMapping.bizValue2WaveType(familyInfo.getFamilyType()));
        return Optional.of(bean);
    }

    @Override
    public Integer countCanOpenRoomNum(long familyId) {
        Result<FamilyServiceProto.ResponseGetFamilyInfo> result = familyService.getFamilyInfo(FamilyServiceProto.FamilyParam.newBuilder().setFamilyId(familyId).build());
        if (RpcResult.isFail(result)) {
            log.error("xm getFamilyInfo fail. familyId={},rCode={}", familyId, result.rCode());
            return 0;
        }

        String familyStr = result.target().getFamily();
        FamilyInfo familyInfo = JsonUtil.loads(familyStr, FamilyInfo.class);
        return familyInfo.getOpenRoomNum() == null ? 0 : familyInfo.getOpenRoomNum();
    }

    @Override
    public Optional<FamilyBean> getFamilyByUserId(long userId) {
        return Optional.empty();
    }

    @Override
    public Optional<FamilyBean> getFamilyByCache(long familyId) {
        try {
            return FAMILY_INFO_CACHE.get(familyId);
        } catch (ExecutionException e) {
            log.warn("xm getFamilyByCache error familyId={}", familyId, e);
            return Optional.empty();
        }
    }

    @Override
    public Optional<Long> playerCurSignNj(long userId) {
        Result<PlayerSignServiceProto.ResponsePlayerCurSignNj> res = playerSignService.playerCurSignNj(userId);
        if (res.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("xm playerCurSignNj fail, userId={}`rCode={}", userId, res.rCode());
            return Optional.empty();
        }
        return Optional.of(res.target().getNjId());
    }
}
