package fm.lizhi.ocean.wavecenter.infrastructure.live.convert;


import fm.lizhi.ocean.wavecenter.api.live.bean.AddAuditRecordBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.AddAuditRecordFullParamBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.AuditRecordBean;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.AuditRecord;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcAuditRecordFull;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface LiveAuditConvert {

    LiveAuditConvert I = Mappers.getMapper(LiveAuditConvert.class);


    AuditRecord AddAuditRecordBeanTo2Bean(AddAuditRecordBean bean);

    AuditRecordBean entityTo2Bean(AuditRecord entity);

    WcAuditRecordFull fullBean2Entity(AddAuditRecordFullParamBean bean);

    @Mappings({
            @Mapping(target = "roomId", source = "bizNjId"),
            @Mapping(target = "insertTime", source = "auditStartTime"),
            @Mapping(target = "scene", source = "sceneName"),
    })
    AuditRecordBean fullEntity2AuditRecordBean(WcAuditRecordFull entity);

    List<AuditRecordBean> fullEntitys2AuditRecordBeans(List<WcAuditRecordFull> entitys);

}
