package fm.lizhi.ocean.wavecenter.infrastructure.permissions.manager;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageParamBean;
import fm.lizhi.ocean.wavecenter.api.permissions.bean.AddRoleAuthRefBean;
import fm.lizhi.ocean.wavecenter.api.permissions.bean.RoleAuthRefBean;
import fm.lizhi.ocean.wavecenter.api.permissions.bean.RoleInfoAuthRefBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.datastore.platform.premission.entity.*;
import fm.lizhi.ocean.wavecenter.infrastructure.permissions.convert.RoleInfraConvert;
import fm.lizhi.ocean.wavecenter.datastore.platform.premission.mapper.WcAuthRoomScopeMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.permissions.datastore.mapper.WcRoleAuthRefExtMapper;
import fm.lizhi.ocean.wavecenter.datastore.platform.premission.mapper.WcRoleAuthRefMapper;
import fm.lizhi.ocean.wavecenter.datastore.platform.premission.mapper.WcRoleMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.permissions.remote.LiveRoomRoleRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.user.convert.UserConvert;
import fm.lizhi.ocean.wavecenter.service.permissions.constants.PermissionConstants;
import fm.lizhi.ocean.wavecenter.service.permissions.dto.RoleAuthRefDto;
import fm.lizhi.ocean.wavecenter.service.permissions.dto.RoleDto;
import fm.lizhi.ocean.wavecenter.service.permissions.manager.RoleManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nonnull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/27 15:27
 */
@Slf4j
@Component
public class RoleManagerImpl implements RoleManager {
    @Autowired
    private WcRoleMapper wcRoleMapper;
    @Autowired
    private WcRoleAuthRefMapper wcRoleAuthRefMapper;
    @Autowired
    private UserManager userManager;
    @Autowired
    private FamilyManager familyManager;
    @Autowired
    private WcAuthRoomScopeMapper authRoomScopeMapper;
    @Autowired
    private WcRoleAuthRefExtMapper roleAuthRefExtMapper;
    @Autowired
    private LiveRoomRoleRemote liveRoomRoleRemote;

    @Nonnull
    @Override
    public String getUserRoleCode(long userId) {
        //查询业务侧的角色ID
        UserInFamilyBean userFamily = familyManager.getUserInFamily(userId);

        //与创作者中心的进行映射
        if (userFamily.isFamily()) {
            return RoleEnum.FAMILY.getRoleCode();
        } else if (userFamily.isRoom()) {
            return RoleEnum.ROOM.getRoleCode();
        } else if (userFamily.isPlayer()) {
            return RoleEnum.PLAYER.getRoleCode();
        } else {
            return RoleEnum.USER.getRoleCode();
        }
    }

    @Override
    public List<RoleDto> getAllRoles() {
        List<WcRole> poList = wcRoleMapper.selectMany(new WcRole());
        if (CollectionUtils.isEmpty(poList)) {
            return Collections.emptyList();
        }
        return RoleInfraConvert.I.rolePos2Dtos(poList);
    }

    @Override
    public List<RoleDto> getRolesByCodes(List<String> roleCodes) {
        WcRoleExample example = new WcRoleExample();
        example.createCriteria().andRoleCodeIn(roleCodes);
        List<WcRole> wcRoles = wcRoleMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(wcRoles)) {
            return Collections.emptyList();
        }
        return RoleInfraConvert.I.rolePos2Dtos(wcRoles);
    }

    @Override
    public PageBean<RoleAuthRefBean> getAllAuthConfig(int appId, long createUserId, Long userId, String roleCode, PageParamBean pageParamBean) {
        WcRoleAuthRefExample example = new WcRoleAuthRefExample();
        example.setOrderByClause("create_time desc");
        WcRoleAuthRefExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(appId)
                .andCreateUserIdEqualTo(createUserId)
                .andDeletedEqualTo(0);
        if (userId != null) {
            criteria.andUserIdEqualTo(userId);
        }
        if (StringUtils.isNotBlank(roleCode)) {
            criteria.andRoleCodeEqualTo(roleCode);
        }
        PageList<WcRoleAuthRef> poPageList = wcRoleAuthRefMapper.pageByExample(example, pageParamBean.getPageNo(), pageParamBean.getPageSize());
        if (CollectionUtils.isEmpty(poPageList)) {
            return PageBean.empty();
        }
        return PageBean.of(poPageList.getTotal(), authRefPo2Bean(poPageList));
    }

    private List<RoleAuthRefBean> authRefPo2Bean(List<WcRoleAuthRef> poPageList){
        List<Long> userIds = new ArrayList<>();
        List<Long> authIds = new ArrayList<>();
        poPageList.forEach(wcRoleAuthRef -> {
            userIds.add(wcRoleAuthRef.getUserId());
            userIds.add(wcRoleAuthRef.getSubjectUserId());
            authIds.add(wcRoleAuthRef.getId());
        });

        WcAuthRoomScopeExample scopeExample = new WcAuthRoomScopeExample();
        scopeExample.createCriteria().andAuthIdIn(authIds).andDeletedEqualTo(0);
        List<WcAuthRoomScope> scopeList = authRoomScopeMapper.selectByExample(scopeExample);
        Map<Long, List<Long>> scopeMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(scopeList)) {
            scopeMap = scopeList.stream().collect(Collectors.groupingBy(WcAuthRoomScope::getAuthId
                    , Collectors.mapping(v->{
                        userIds.add(v.getRoomId());
                        return v.getRoomId();
                    }, Collectors.toList())));
        }

        List<SimpleUserDto> userList = userManager.getSimpleUserByIds(userIds);
        Map<Long, SimpleUserDto> userMap = userList.stream().collect(Collectors.toMap(SimpleUserDto::getId, v -> v, (k1, k2) -> k2));

        List<RoleAuthRefBean> beanList = new ArrayList<>(poPageList.size());
        for (WcRoleAuthRef wcRoleAuthRef : poPageList) {
            RoleAuthRefBean bean = RoleInfraConvert.I.authRefPo2Bean(wcRoleAuthRef);
            bean.setUserInfo(UserConvert.I.simpleDto2Bean(userMap.get(wcRoleAuthRef.getUserId())));
            bean.setSubject(UserConvert.I.simpleDto2Bean(userMap.get(wcRoleAuthRef.getSubjectUserId())));

            List<Long> authRoomIds = scopeMap.get(wcRoleAuthRef.getId());
            if (CollectionUtils.isNotEmpty(authRoomIds)) {
                List<UserBean> authRoomList = new ArrayList<>();
                for (Long authRoomId : authRoomIds) {
                    authRoomList.add(UserConvert.I.simpleDto2Bean(userMap.get(authRoomId)));
                }
                bean.setAuthRoomList(authRoomList);
            }

            beanList.add(bean);
        }
        return beanList;
    }

    @Override
    public Optional<RoleAuthRefDto> getAuthConfig(long authConfigId) {
        WcRoleAuthRef param = new WcRoleAuthRef();
        param.setId(authConfigId);

        List<WcRoleAuthRef> wcRoleAuthRefs = wcRoleAuthRefMapper.selectMany(param);
        if (CollectionUtils.isEmpty(wcRoleAuthRefs)) {
            return Optional.empty();
        }

        return Optional.ofNullable(RoleInfraConvert.I.authRefPo2Dto(wcRoleAuthRefs.get(0)));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addAuthConfig(int appId, AddRoleAuthRefBean addRoleAuthRefBean, long subjectId) {
        if (RoleEnum.FAMILY_ADMIN.getRoleCode().equals(addRoleAuthRefBean.getRoleCode())) {
            WcRoleAuthRef param = new WcRoleAuthRef();
            param.setAppId(appId);
            param.setFamilyId(addRoleAuthRefBean.getFamilyId());
            param.setDeleted(0);
            param.setRoleCode(addRoleAuthRefBean.getRoleCode());
            param.setUserId(addRoleAuthRefBean.getUserId());

            Long authId = null;
            List<WcRoleAuthRef> wcRoleAuthRefs = wcRoleAuthRefMapper.selectMany(param);
            if (CollectionUtils.isEmpty(wcRoleAuthRefs)) {
                authId = insertAuthConfig(appId, addRoleAuthRefBean, subjectId);
            } else {
                WcRoleAuthRef wcRoleAuthRef = wcRoleAuthRefs.get(0);
                authId = wcRoleAuthRef.getId();
                //更新配置
                wcRoleAuthRef.setSubjectId(subjectId);
                wcRoleAuthRef.setSubjectUserId(addRoleAuthRefBean.getSubjectUserId());
                wcRoleAuthRef.setStatus(1);
                wcRoleAuthRef.setModifyTime(new Date());
                wcRoleAuthRefMapper.updateByPrimaryKey(wcRoleAuthRef);
            }

            //先删除旧配置，再重新保存
            WcAuthRoomScopeExample example = new WcAuthRoomScopeExample();
            example.createCriteria().andAuthIdEqualTo(authId).andDeletedEqualTo(0);
            WcAuthRoomScope deleteEntity = new WcAuthRoomScope();
            deleteEntity.setDeleted(1);
            authRoomScopeMapper.updateByExample(deleteEntity, example);

            List<WcAuthRoomScope> insertList = new ArrayList<>();
            for (Long authRoomId : addRoleAuthRefBean.getAuthRoomIds()) {
                insertList.add(WcAuthRoomScope.builder()
                        .deleted(0)
                        .roomId(authRoomId)
                        .familyId(addRoleAuthRefBean.getFamilyId())
                        .appId(appId)
                        .authId(authId)
                        .createTime(new Date())
                        .updateTime(new Date())
                        .build());
            }
            authRoomScopeMapper.batchInsert(insertList);

        } else {
            insertAuthConfig(appId, addRoleAuthRefBean, subjectId);
        }
    }

    private Long insertAuthConfig(int appId, AddRoleAuthRefBean addRoleAuthRefBean, long subjectId) {
        WcRoleAuthRef roleAuthRef = new WcRoleAuthRef();
        roleAuthRef.setAppId(appId);
        roleAuthRef.setUserId(addRoleAuthRefBean.getUserId());
        roleAuthRef.setRoleCode(addRoleAuthRefBean.getRoleCode());
        roleAuthRef.setSubjectId(subjectId);
        roleAuthRef.setSubjectUserId(addRoleAuthRefBean.getSubjectUserId());
        roleAuthRef.setStatus(PermissionConstants.RoleRefStatus.ACT);
        roleAuthRef.setCreateTime(new Date());
        roleAuthRef.setModifyTime(new Date());
        roleAuthRef.setCreateUserId(addRoleAuthRefBean.getCreateUserId());
        roleAuthRef.setFamilyId(addRoleAuthRefBean.getFamilyId());
        roleAuthRef.setDeleted(0);
        wcRoleAuthRefMapper.insert(roleAuthRef);
        return roleAuthRef.getId();
    }

    @Override
    public boolean existsAuthConfig(int appId, long familyId, long userId, String roleCode, long subjectId) {
        WcRoleAuthRefExample example = new WcRoleAuthRefExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andUserIdEqualTo(userId)
                .andRoleCodeEqualTo(roleCode)
                .andSubjectIdEqualTo(subjectId)
                .andFamilyIdEqualTo(familyId)
                .andDeletedEqualTo(0);
        return wcRoleAuthRefMapper.countByExample(example) > 0;
    }

    @Override
    public void modifyAuthConfigStatus(long configId, int status) {
        WcRoleAuthRef entity = new WcRoleAuthRef();
        entity.setId(configId);
        entity.setStatus(status);
        entity.setModifyTime(new Date());
        wcRoleAuthRefMapper.updateByPrimaryKey(entity);
    }

    @Override
    public List<RoleInfoAuthRefBean> getUserAuthRoles(int appId, long userId) {
        WcRoleAuthRefExample example = new WcRoleAuthRefExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andUserIdEqualTo(userId)
                .andDeletedEqualTo(0)
                .andStatusEqualTo(PermissionConstants.RoleRefStatus.ACT);
        List<WcRoleAuthRef> poList = wcRoleAuthRefMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(poList)) {
            return Collections.emptyList();
        }

        List<RoleInfoAuthRefBean> beanList = RoleInfraConvert.I.roleAuthRefBeans2RoleInfoBeans(authRefPo2Bean(poList));
        List<RoleDto> roleDtos = getRolesByCodes(beanList.stream()
                .map(RoleAuthRefBean::getRoleCode)
                .collect(Collectors.toList()));
        Map<String, RoleDto> roleNameMap = roleDtos.stream().collect(Collectors.toMap(RoleDto::getRoleCode, v->v));
        for (RoleInfoAuthRefBean bean : beanList) {
            RoleDto roleDto = roleNameMap.get(bean.getRoleCode());
            if (roleDto == null) {
                continue;
            }
            bean.setRoleName(roleDto.getRoleName());
            bean.setLevel(roleDto.getLevel());
        }
        beanList.sort(Comparator.comparingInt(RoleInfoAuthRefBean::getLevel));
        return beanList;
    }

    @Override
    public List<Long> getRoleRoomDataScope(RoleEnum role, Long userId, Long familyId) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();

        WcRoleAuthRef param = new WcRoleAuthRef();
        param.setAppId(appId);
        param.setFamilyId(familyId);
        param.setDeleted(0);
        param.setRoleCode(role.getRoleCode());
        param.setUserId(userId);

        List<WcRoleAuthRef> wcRoleAuthRefs = wcRoleAuthRefMapper.selectMany(param);
        if (CollectionUtils.isEmpty(wcRoleAuthRefs)) {
            LogContext.addResLog("role ref is empty");
            return Collections.emptyList();
        }

        Long authId = wcRoleAuthRefs.get(0).getId();

        WcAuthRoomScope entity = new WcAuthRoomScope();
        entity.setDeleted(0);
        entity.setAuthId(authId);
        List<WcAuthRoomScope> wcAuthRoomScopes = authRoomScopeMapper.selectMany(entity);
        return wcAuthRoomScopes.stream().map(WcAuthRoomScope::getRoomId).collect(Collectors.toList());
    }

    @Override
    public void removeUsersWithFamilyAuth(Long familyId, List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        int count = roleAuthRefExtMapper.deleteByFamilyAndUserIds(familyId, appId, userIds);
        log.info("delete role auth ref count: {}", count);
    }

    @Override
    public void removeFamilyRoomDataScope(Long familyId, Long roomId) {
        if (familyId == null || roomId == null) {
            return;
        }

        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        WcAuthRoomScopeExample example = new WcAuthRoomScopeExample();
        example.createCriteria()
                .andFamilyIdEqualTo(familyId)
                .andRoomIdEqualTo(roomId)
                .andAppIdEqualTo(appId)
                .andDeletedEqualTo(0);

        WcAuthRoomScope entity = new WcAuthRoomScope();
        entity.setDeleted(1);
        entity.setUpdateTime(new Date());
        authRoomScopeMapper.updateByExample(entity, example);
    }

    @Override
    public List<Long> getSuperAdminUserIds(List<Long> njIds) {
        return liveRoomRoleRemote.getSuperAdminUserIds(njIds);
    }

    @Override
    public List<Long> getRoomRoleAuthUserIds(Integer appId, Long subjectId, RoleEnum roleCode) {
        return roleAuthRefExtMapper.getRoomRoleAuthRefList(appId, subjectId, roleCode.getRoleCode());
    }
}
