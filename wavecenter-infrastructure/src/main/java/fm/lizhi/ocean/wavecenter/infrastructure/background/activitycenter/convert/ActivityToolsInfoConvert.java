package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityToolBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityToolsInfoBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityTools;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityTools;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityToolsInfo;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        imports = {Date.class, ConfigUtils.class}
)
public interface ActivityToolsInfoConvert {
    ActivityToolsInfoConvert I = Mappers.getMapper(ActivityToolsInfoConvert.class);

    default Long map(Date date) {
        return date.getTime();
    }

    @Mappings({

            @Mapping(target = "createTime", expression = "java(new Date())"),
            @Mapping(target = "modifyTime", expression = "java(new Date())"),
            @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())"),
            @Mapping(target = "id", ignore = true)
    })
    ActivityToolsInfo buildActivityToolsInfo(RequestSaveActivityTools param, int toolValue);

    @Mappings({
            @Mapping(target = "modifyTime", expression = "java(new Date())"),
            @Mapping(target = "createTime", ignore = true),
            @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())"),
            @Mapping(target = "toolValue", ignore = true)
    })
    ActivityToolsInfo convertUpdateActivityToolsInfo(RequestUpdateActivityTools param);


    List<ActivityToolsInfoBean> convertActivityToolsInfoBeanList(List<ActivityToolsInfo> list);

    @Mappings({
            @Mapping(source = "toolValue", target = "type"),
            @Mapping(source = "name", target = "name"),
            @Mapping(source = "toolDesc", target = "toolDesc"),
            @Mapping(source = "type", target = "toolType")
    })
    ActivityToolBean convertActivityToolBean(ActivityToolsInfo bean);
    List<ActivityToolBean> convertActivityToolBeans(List<ActivityToolsInfo> activityToolsInfoList);

}
