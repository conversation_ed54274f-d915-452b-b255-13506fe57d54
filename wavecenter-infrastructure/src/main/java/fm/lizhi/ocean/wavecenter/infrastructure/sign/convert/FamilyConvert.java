package fm.lizhi.ocean.wavecenter.infrastructure.sign.convert;

import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.pp.family.protocol.BaseProto;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2024/4/10 18:22
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface FamilyConvert {
    FamilyConvert I = Mappers.getMapper(FamilyConvert.class);

    FamilyBean familyPb2Dto(BaseProto.FamilyProto pb);

}
