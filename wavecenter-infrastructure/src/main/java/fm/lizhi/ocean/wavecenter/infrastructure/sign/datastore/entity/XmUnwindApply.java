package fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 解约申请
 *
 * @date 2024-10-15 05:34:47
 */
@Table(name = "`unwind_apply`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class XmUnwindApply {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 原合同contract表的id
     */
    @Column(name= "`contract_id`")
    private Long contractId;

    /**
     * 解约contract表的id
     */
    @Column(name= "`unwind_contract_id`")
    private Long unwindContractId;

    /**
     * 签约dc的contract_id
     */
    @Column(name= "`unwind_sign_id`")
    private String unwindSignId;

    /**
     * 家族ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 主播ID
     */
    @Column(name= "`nj_id`")
    private Long njId;

    /**
     * 待审核:PENDING 已同意:AGREED 已拒绝:REFUSED
     */
    @Column(name= "`audit_status`")
    private String auditStatus;

    /**
     * 同合同状态
     */
    @Column(name= "`status`")
    private String status;

    /**
     * 备注
     */
    @Column(name= "`remark`")
    private String remark;

    /**
     * 操作人
     */
    @Column(name= "`operator`")
    private String operator;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 签约订单外部id
     */
    @Column(name= "`order_id`")
    private Long orderId;

    /**
     * 发起人角色1管理员3家族长
     */
    @Column(name= "`apply_user_type`")
    private Integer applyUserType;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", contractId=").append(contractId);
        sb.append(", unwindContractId=").append(unwindContractId);
        sb.append(", unwindSignId=").append(unwindSignId);
        sb.append(", familyId=").append(familyId);
        sb.append(", njId=").append(njId);
        sb.append(", auditStatus=").append(auditStatus);
        sb.append(", status=").append(status);
        sb.append(", remark=").append(remark);
        sb.append(", operator=").append(operator);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", orderId=").append(orderId);
        sb.append(", applyUserType=").append(applyUserType);
        sb.append("]");
        return sb.toString();
    }
}