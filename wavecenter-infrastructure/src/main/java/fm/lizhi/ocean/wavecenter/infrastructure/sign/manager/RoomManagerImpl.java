package fm.lizhi.ocean.wavecenter.infrastructure.sign.manager;

import com.google.common.collect.Lists;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.*;
import fm.lizhi.ocean.wavecenter.common.dto.PageDto;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataPlayerRoomWeek;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.WcDataPlayerRoomWeekExtMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.WcDataRoomFamilyWeekExtMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.WcDataRoomFamilyWeekIncomePo;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.dto.PlayerSignSortDTO;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.dto.RoomSignSortDTO;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.process.RoomProcessor;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.IContractRemote;
import fm.lizhi.ocean.wavecenter.base.processor.ProcessorFactory;
import fm.lizhi.ocean.wavecenter.service.sign.manager.RoomManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/10 14:43
 */
@Slf4j
@Component
public class RoomManagerImpl implements RoomManager {

    @Autowired
    private IContractRemote iContractRemote;

    @Autowired
    private UserManager userManager;

    @Resource
    private WcDataRoomFamilyWeekExtMapper wcDataRoomFamilyWeekExtMapper;

    @Resource
    private WcDataPlayerRoomWeekExtMapper wcDataPlayerRoomWeekExtMapper;

    @Autowired
    private ProcessorFactory processorFactory;


    @Override
    public PageBean<RoomSignBean> getAllSingGuildRooms(long familyId, int pageNo, int pageSize) {
        PageBean<RoomSignBean> allGuildRooms = iContractRemote.getAllSingGuildRooms(familyId, pageNo, pageSize);

        //查询厅主名称
        List<RoomSignBean> list = allGuildRooms.getList();
        List<Long> njIds = list.stream().map(RoomBean::getId).collect(Collectors.toList());

        List<SimpleUserDto> njList = userManager.getSimpleUserByIds(njIds);
        Map<Long, SimpleUserDto> njMap = njList.stream().collect(Collectors.toMap(SimpleUserDto::getId, v -> v, (k1, k2) -> k2));
        for (RoomSignBean roomSignBean : list) {
            SimpleUserDto simpleUserDto = njMap.get(roomSignBean.getId());
            if (simpleUserDto == null) {
                continue;
            }
            roomSignBean.setName(simpleUserDto.getName());
            roomSignBean.setBand(simpleUserDto.getBand());
            roomSignBean.setPhoto(simpleUserDto.getAvatar());
        }

        return allGuildRooms;
    }

    @Override
    public PageBean<RoomSignBean> getAllGuildRooms(long familyId, List<Long> roomIds, int pageNo, int pageSize) {
        PageBean<RoomSignBean> allGuildRooms = iContractRemote.getAllGuildRooms(familyId, roomIds, pageNo, pageSize);

        //查询厅主名称
        List<RoomSignBean> list = allGuildRooms.getList();
        if (CollectionUtils.isEmpty(list)) {
            return allGuildRooms;
        }

        Set<Long> njIds = list.stream().map(RoomBean::getId).collect(Collectors.toSet());

        // 获取厅上周收入数据并按收入排序
        List<RoomSignBean> sortedList = sortRoomsByWeekIncome(ContextUtils.getBusinessEvnEnum().getAppId(), list);

        List<SimpleUserDto> njList = userManager.getSimpleUserByIds(new ArrayList<>(njIds));
        Map<Long, SimpleUserDto> njMap = njList.stream().collect(Collectors.toMap(SimpleUserDto::getId, v -> v, (k1, k2) -> k2));
        for (RoomSignBean roomSignBean : sortedList) {
            SimpleUserDto simpleUserDto = njMap.get(roomSignBean.getId());
            if (simpleUserDto == null) {
                continue;
            }
            roomSignBean.setName(simpleUserDto.getName());
            roomSignBean.setBand(simpleUserDto.getBand());
            roomSignBean.setPhoto(simpleUserDto.getAvatar());
        }

        return PageBean.of(allGuildRooms.getTotal(), sortedList);
    }

    @Override
    public PageBean<PlayerSignBean> getAllRoomPlayers(long roomId, int pageNo, int pageSize) {
        PageBean<PlayerSignBean> allRoomPlayers = iContractRemote.getAllRoomPlayers(roomId, pageNo, pageSize);

        //查询主播名称
        List<PlayerSignBean> list = allRoomPlayers.getList();
        Set<Long> userIds = list.stream().map(PlayerBean::getId).collect(Collectors.toSet());

        // 获取厅上周收入数据并按收入排序
        List<PlayerSignBean> playerSignBeans = sortPlayersByWeekIncome(ContextUtils.getBusinessEvnEnum().getAppId(), roomId, list);

        List<SimpleUserDto> njList = userManager.getSimpleUserByIds(new ArrayList<>(userIds));
        Map<Long, SimpleUserDto> userMap = njList.stream().collect(Collectors.toMap(SimpleUserDto::getId, v -> v, (k1, k2) -> k2));
        for (PlayerSignBean playerSignBean : playerSignBeans) {
            SimpleUserDto simpleUserDto = userMap.get(playerSignBean.getId());
            if (simpleUserDto == null) {
                continue;
            }
            playerSignBean.setName(simpleUserDto.getName());
            playerSignBean.setBand(simpleUserDto.getBand());
            playerSignBean.setPhoto(simpleUserDto.getAvatar());
        }
        return PageBean.of(allRoomPlayers.getTotal(), playerSignBeans);
    }

    @Override
    public List<Long> getAllSignRoomPlayerIds(long roomId) {
        return iContractRemote.getAllSignRoomPlayerIds(roomId);
    }

    @Override
    public PageDto<PlayerSignBean> getAllGuildPlayer(QueryGuildPlayerBean req, int pageNo, int pageSize) {
        PageDto<PlayerSignBean> allGuildPlayer = iContractRemote.getAllGuildPlayer(req.getFamilyId(), req.getRoomIds(), req.getStatus(), pageNo, pageSize);

        //查询主播名称
        List<PlayerSignBean> list = allGuildPlayer.getList();
        if (CollectionUtils.isEmpty(list)) {
            return allGuildPlayer;
        }

        Set<Long> userIds = list.stream().map(PlayerBean::getId).collect(Collectors.toSet());
        // 获取厅上周收入数据并按收入排序
        List<PlayerSignBean> sortedList = sortPlayersByRoomWeekIncome(req.getAppId(), req.getFamilyId(), list);

        List<SimpleUserDto> njList = userManager.getSimpleUserByIds(new ArrayList<>(userIds));
        Map<Long, SimpleUserDto> userMap = njList.stream().collect(Collectors.toMap(SimpleUserDto::getId, v -> v, (k1, k2) -> k2));
        for (PlayerSignBean playerSignBean : sortedList) {
            SimpleUserDto simpleUserDto = userMap.get(playerSignBean.getId());
            if (simpleUserDto == null) {
                continue;
            }
            playerSignBean.setName(simpleUserDto.getName());
            playerSignBean.setBand(simpleUserDto.getBand());
            playerSignBean.setPhoto(simpleUserDto.getAvatar());
        }


        return PageDto.of(allGuildPlayer.getTotal(), sortedList);
    }

    @Override
    public Optional<PlayerSignBean> getPlayerSignInfo(Long familyId, Long roomId, long userId) {
        Optional<PlayerSignBean> playerSignOp = iContractRemote.getPlayerSign(familyId, roomId, userId);
        if (!playerSignOp.isPresent()) {
            return Optional.empty();
        }
        PlayerSignBean signBean = playerSignOp.get();
        List<SimpleUserDto> simpleUserList = userManager.getSimpleUserByIds(Collections.singletonList(signBean.getId()));
        if (CollectionUtils.isEmpty(simpleUserList)) {
            return Optional.empty();
        }
        SimpleUserDto simpleUserDto = simpleUserList.get(0);
        signBean.setName(simpleUserDto.getName());
        signBean.setBand(simpleUserDto.getBand());
        signBean.setPhoto(simpleUserDto.getAvatar());
        return Optional.of(signBean);
    }

    @Override
    public List<Long> getFamilyAllNjId(Long familyId) {
        return iContractRemote.getFamilyAllNjId(familyId);
    }


    /**
     * 查询陪玩上周收入
     *
     * @param appId     业务ID
     * @param playerIds 陪玩ID列表
     * @return 厅ID -> 收入的映射
     */
    private Map<Long, Long> getPlayerWeekIncome(Integer appId, Long familyId, Long roomId, List<Long> playerIds) {
        try {
            // 获取上周的开始和结束日期
            Date lastWeekStart = MyDateUtil.getLastWeekStartDay();
            Date lastWeekEnd = MyDateUtil.getLastWeekEndDay();
            lastWeekStart = DateUtil.formatStrToDate(DateUtil.formatDateToString(lastWeekStart, DateUtil.date_2), DateUtil.date_2);
            lastWeekEnd = DateUtil.formatStrToDate(DateUtil.formatDateToString(lastWeekEnd, DateUtil.date_2), DateUtil.date_2);

            // 分批次查询，一次查询100个
            List<List<Long>> roomIdsList = Lists.partition(playerIds, 100);
            Map<Long, Long> resultMap = new HashMap<>(playerIds.size());

            RoomProcessor processor = processorFactory.getProcessor(RoomProcessor.class);
            for (List<Long> batchRoomIds : roomIdsList) {
                List<WcDataPlayerRoomWeek> incomeList = wcDataPlayerRoomWeekExtMapper.getWcRoomDataPlayerWeek(appId, familyId, roomId, batchRoomIds, lastWeekStart, lastWeekEnd);
                // 转成map
                Map<Long, Long> incomeMap = processor.playerWeekIncomeToMap(incomeList);
                for (Long playerId : batchRoomIds) {
                    if (!incomeMap.containsKey(playerId)) {
                        resultMap.put(playerId, 0L);
                        continue;
                    }
                    resultMap.put(playerId, incomeMap.get(playerId));
                }
            }

            return resultMap;
        } catch (Exception e) {
            log.error("getRoomWeekIncomeWithCache error, appId={}, roomIds size={}", appId, playerIds.size(), e);
            return Collections.emptyMap();
        }
    }

    /**
     * 按厅上周收入对主播列表进行排序
     *
     * @param playerList 主播列表
     * @return 按厅收入倒序排列的主播列表
     */
    private List<PlayerSignBean> sortPlayersByRoomWeekIncome(Integer appId, Long familyId, List<PlayerSignBean> playerList) {
        try {
            if (CollectionUtils.isEmpty(playerList)) {
                return playerList;
            }
            // 提取陪玩ID
            Set<Long> userIds = playerList.stream().map(PlayerBean::getId).collect(Collectors.toSet());
            // 获取厅收入数据
            Map<Long, Long> roomIncomeMap = getPlayerWeekIncome(appId, familyId, null, new ArrayList<>(userIds));

            List<PlayerSignSortDTO> sortList = Lists.newArrayList();
            playerList.forEach(roomSignBean -> {
                PlayerSignSortDTO signSortDTO = new PlayerSignSortDTO()
                        .setPlayerSignBean(roomSignBean)
                        .setIncome(roomIncomeMap.get(roomSignBean.getId()));
                sortList.add(signSortDTO);
            });

            // 按厅收入倒序排序（收入高的在前）
            sortList.sort(
                Comparator.comparingInt((PlayerSignSortDTO dto) ->
                    Optional.ofNullable(dto.getPlayerSignBean().getSignStatus()).orElse(0)
                ).reversed()
                .thenComparing(Comparator.comparingLong(PlayerSignSortDTO::getIncome).reversed())
            );
            //按顺序取出sortList中的roomSignBean
            return sortList.stream().map(PlayerSignSortDTO::getPlayerSignBean).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("sortPlayersByRoomWeekIncome error, return original order", e);
            return playerList;
        }
    }

    /**
     * 按厅上周收入对厅列表进行排序
     *
     * @param roomList 厅列表
     * @return 按厅收入倒序排列的厅列表
     */
    private List<RoomSignBean> sortRoomsByWeekIncome(Integer appId, List<RoomSignBean> roomList) {
        try {
            if (CollectionUtils.isEmpty(roomList)) {
                return roomList;
            }
            // 提取所有厅ID
            Set<Long> roomIds = roomList.stream()
                    .map(RoomBean::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            // 获取厅收入数据
            Map<Long, Long> roomIncomeMap = getRoomFamilyWeekIncome(appId, new ArrayList<>(roomIds));

            List<RoomSignSortDTO> sortList = Lists.newArrayList();
            roomList.forEach(roomSignBean -> {
                RoomSignSortDTO roomSignSortDTO = new RoomSignSortDTO()
                        .setRoomSignBean(roomSignBean)
                        .setIncome(roomIncomeMap.get(roomSignBean.getId()));
                sortList.add(roomSignSortDTO);
            });

            // 按厅收入倒序排序（收入高的在前）
            sortList.sort(
                    Comparator.comparingInt((RoomSignSortDTO dto) ->
                                    Optional.ofNullable(dto.getRoomSignBean().getSignStatus()).orElse(0)
                            ).reversed()
                            .thenComparing(Comparator.comparingLong(RoomSignSortDTO::getIncome).reversed())
            );

            //按顺序取出sortList中的roomSignBean
            return sortList.stream().map(RoomSignSortDTO::getRoomSignBean).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("sortRoomsByWeekIncome error, return original order", e);
            return roomList;
        }
    }

    /**
     * 查询厅下面的主播上周收入
     *
     * @param appId 业务ID
     * @param list  陪玩列表
     * @return 陪玩ID -> 收入的映射
     */
    private List<PlayerSignBean> sortPlayersByWeekIncome(Integer appId, Long roomId, List<PlayerSignBean> list) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return list;
            }
            // 提取所有的陪玩ID
            Set<Long> playerIds = list.stream().map(PlayerBean::getId).collect(Collectors.toSet());

            // 获取厅收入数据
            Map<Long, Long> playerWeekIncome = getPlayerWeekIncome(appId, null, roomId, new ArrayList<>(playerIds));

            List<PlayerSignSortDTO> sortList = Lists.newArrayList();
            list.forEach(roomSignBean -> {
                PlayerSignSortDTO signSortDTO = new PlayerSignSortDTO()
                        .setPlayerSignBean(roomSignBean)
                        .setIncome(playerWeekIncome.get(roomSignBean.getId()));
                sortList.add(signSortDTO);
            });

            // 按厅收入倒序排序（收入高的在前）
            sortList.sort(
                Comparator.comparingInt((PlayerSignSortDTO dto) ->
                    Optional.ofNullable(dto.getPlayerSignBean().getSignStatus()).orElse(0)
                ).reversed()
                .thenComparing(Comparator.comparingLong(PlayerSignSortDTO::getIncome).reversed())
            );

            //按顺序取出sortList中的roomSignBean
            return sortList.stream().map(PlayerSignSortDTO::getPlayerSignBean).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("sortPlayersByWeekIncome error, return original order", e);
            return list;
        }
    }

    /**
     * 获取厅上周收入数据（带缓存）
     *
     * @param appId   业务ID
     * @param roomIds 厅ID列表
     * @return 厅ID -> 收入的映射
     */
    private Map<Long, Long> getRoomFamilyWeekIncome(Integer appId, List<Long> roomIds) {
        try {
            // 获取上周的开始和结束日期
            Date lastWeekStart = MyDateUtil.getLastWeekStartDay();
            Date lastWeekEnd = MyDateUtil.getLastWeekEndDay();
            lastWeekStart = DateUtil.formatStrToDate(DateUtil.formatDateToString(lastWeekStart, DateUtil.date_2), DateUtil.date_2);
            lastWeekEnd = DateUtil.formatStrToDate(DateUtil.formatDateToString(lastWeekEnd, DateUtil.date_2), DateUtil.date_2);

            // 分批次查询，一次查询100个
            List<List<Long>> roomIdsList = Lists.partition(roomIds, 100);
            Map<Long, Long> resultMap = new HashMap<>(roomIds.size());

            RoomProcessor processor = processorFactory.getProcessor(RoomProcessor.class);
            for (List<Long> batchRoomIds : roomIdsList) {
                List<WcDataRoomFamilyWeekIncomePo> incomeList = wcDataRoomFamilyWeekExtMapper.queryRoomFamilyWeekIncomeByIds(
                        batchRoomIds, appId, lastWeekStart, lastWeekEnd);
                Map<Long, Long> incomeMap = processor.roomsWeekIncomeToMap(incomeList);
                for (Long id : batchRoomIds) {
                    if (!incomeMap.containsKey(id)) {
                        resultMap.put(id, 0L);
                        continue;
                    }
                    resultMap.put(id, incomeMap.get(id));
                }
            }

            return resultMap;
        } catch (Exception e) {
            log.error("getRoomFamilyWeekIncomeWithCache error, appId={}, roomIds size={}", appId, roomIds.size(), e);
            return Collections.emptyMap();
        }
    }


}
