package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.manager;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.Period;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.PlayerAbility;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.convert.GrowPlayerAbilityConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowPlayerAbilityWeek;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowPlayerAbilityWeekCapability;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowPlayerAbilityWeekCapabilityExample;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowPlayerAbilityWeekExample;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.mapper.WcGrowPlayerAbilityWeekCapabilityMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.mapper.WcGrowPlayerAbilityWeekMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.mapper.ext.WcGrowPlayerAbilityWeekCapabilityExtMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.mapper.ext.WcGrowPlayerAbilityWeekExtMapper;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.PlayerAbilityWeekCapabilityDTO;
import fm.lizhi.ocean.wavecenter.service.grow.ability.manager.GrowPlayerAbilityManager;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/10 17:39
 */
@Component
public class GrowPlayerAbilityManagerImpl implements GrowPlayerAbilityManager {

    @Autowired
    private WcGrowPlayerAbilityWeekMapper abilityWeekMapper;
    @Autowired
    private WcGrowPlayerAbilityWeekExtMapper abilityWeekExtMapper;
    @Autowired
    private WcGrowPlayerAbilityWeekCapabilityExtMapper abilityWeekCapabilityExtMapper;
    @Autowired
    private WcGrowPlayerAbilityWeekCapabilityMapper abilityWeekCapabilityMapper;

    @Override
    public List<Long> getRoomIdsWeekHasPlayerAbilityByMinRoomId(Date weekStartDay, Date weekEndDay, Long minRoomId, Integer pageSize) {
        return abilityWeekExtMapper.getRoomIdsWeekHasPlayerAbilityByMinRoomId(ContextUtils.getBusinessEvnEnum().getAppId()
                , ConfigUtils.getEnvRequired().name()
                , weekStartDay
                , weekEndDay
                , minRoomId
                , pageSize
        );
    }

    @Override
    public Integer countRoomAbilityPlayer(Long roomId, Date weekStartDay, Date weekEndDay) {
        long count = abilityWeekExtMapper.countPlayerAbilityByRoomId(ContextUtils.getBusinessEvnEnum().getAppId()
                , ConfigUtils.getEnvRequired().name()
                , weekStartDay
                , weekEndDay
                , roomId
        );
        return Math.toIntExact(count);
    }

    @Override
    public List<PlayerAbilityWeekCapabilityDTO> getRoomPlayerWeekCapability(Long roomId, Date weekStartDay, Date weekEndDay, Long minId, Integer pageSize) {
        List<WcGrowPlayerAbilityWeekCapability> entityList = abilityWeekCapabilityExtMapper.getRoomPlayerWeekCapabilityPage(roomId
                , weekStartDay
                , weekEndDay
                , minId
                , pageSize
                , ContextUtils.getBusinessEvnEnum().getAppId()
                , ConfigUtils.getEnvRequired().name());
        return GrowPlayerAbilityConvert.I.abilityWeekCapabilitys2DTOs(entityList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void savePlayerAbility(PlayerAbility playerAbility, Long roomId, Long familyId, Date playerSignTime, boolean playerFirstSignNjFamily) {
        Period period = playerAbility.getPeriod();

        // 构建查询条件
        WcGrowPlayerAbilityWeekExample example = new WcGrowPlayerAbilityWeekExample();
        example.createCriteria()
                .andPlayerIdEqualTo(playerAbility.getPlayerId())
                .andStartWeekDateEqualTo(period.getStart())
                .andEndWeekDateEqualTo(period.getEnd())
                .andAppIdEqualTo(playerAbility.getAppId())
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());

        List<WcGrowPlayerAbilityWeek> existingWeeks = abilityWeekMapper.selectByExample(example);
        boolean exists = CollectionUtils.isNotEmpty(existingWeeks);

        // 构造/更新周能力记录
        WcGrowPlayerAbilityWeek abilityWeek = new WcGrowPlayerAbilityWeek();
        abilityWeek.setId(exists ? existingWeeks.get(0).getId() : playerAbility.getId());
        abilityWeek.setPlayerId(playerAbility.getPlayerId());
        abilityWeek.setTotalScore(playerAbility.settleCompositeScore());
        abilityWeek.setStartWeekDate(period.getStart());
        abilityWeek.setEndWeekDate(period.getEnd());
        abilityWeek.setAppId(playerAbility.getAppId());
        abilityWeek.setDeployEnv(ConfigUtils.getEnvRequired().name());
        abilityWeek.setModifyTime(new Date());

        if (exists) {
            abilityWeekMapper.updateByPrimaryKey(abilityWeek);
        } else {
            abilityWeek.setSignDate(playerSignTime);
            abilityWeek.setFirstSignInFamily(playerFirstSignNjFamily ? 1 : 0);
            abilityWeek.setRoomId(roomId);
            abilityWeek.setFamilyId(familyId);
            abilityWeek.setCreateTime(new Date());
            abilityWeekMapper.insert(abilityWeek);
        }

        // 删除旧的能力细分记录
        WcGrowPlayerAbilityWeekCapabilityExample capabilityExample = new WcGrowPlayerAbilityWeekCapabilityExample();
        capabilityExample.createCriteria()
                .andPlayerIdEqualTo(playerAbility.getPlayerId())
                .andStartWeekDateEqualTo(period.getStart())
                .andEndWeekDateEqualTo(period.getEnd())
                .andAppIdEqualTo(playerAbility.getAppId())
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        abilityWeekCapabilityMapper.deleteByExample(capabilityExample);

        // 插入新的能力细分记录
        List<WcGrowPlayerAbilityWeekCapability> capabilities = new ArrayList<>();
        for (Map.Entry<String, BigDecimal> entry : playerAbility.getCapabilityScore().entrySet()) {
            WcGrowPlayerAbilityWeekCapability capability = new WcGrowPlayerAbilityWeekCapability();
            capability.setPlayerId(playerAbility.getPlayerId());
            capability.setRoomId(roomId);
            capability.setFamilyId(familyId);
            capability.setSignDate(playerSignTime);
            capability.setFirstSignInFamily(playerFirstSignNjFamily ? 1 : 0);
            capability.setCapabilityCode(entry.getKey());
            capability.setAbilityValue(entry.getValue());
            capability.setStartWeekDate(period.getStart());
            capability.setEndWeekDate(period.getEnd());
            capability.setAppId(playerAbility.getAppId());
            capability.setDeployEnv(ConfigUtils.getEnvRequired().name());
            capability.setCreateTime(new Date());
            capability.setModifyTime(new Date());
            BigDecimal preCompareScore = playerAbility.getPrePeriodScoreCompare().get(entry.getKey());
            capability.setCompareWeekValue(preCompareScore);
            BigDecimal preScore = playerAbility.getPrePeriodScore().get(entry.getKey());
            capability.setLastWeekAbilityValue(preScore);
            capabilities.add(capability);
        }
        abilityWeekCapabilityMapper.batchInsert(capabilities);
    }
}
