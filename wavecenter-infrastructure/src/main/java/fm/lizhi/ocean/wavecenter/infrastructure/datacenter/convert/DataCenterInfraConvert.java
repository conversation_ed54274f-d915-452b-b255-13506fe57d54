package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.convert;

import cn.hutool.core.convert.NumberChineseFormatter;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.*;
import fm.lizhi.ocean.wavecenter.base.util.CalculateUtil;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.*;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.CharmStatPo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.IncomeStatPo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.PlayerSignPerformancePo;
import fm.lizhi.ocean.wavecenter.service.common.dto.PaySettlePeriodDto;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.*;
import fm.lizhi.pay.settle.protocol.creativecenter.CreatorDataQueryProto;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/18 20:39
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface DataCenterInfraConvert {

    DataCenterInfraConvert I = Mappers.getMapper(DataCenterInfraConvert.class);

    @Mappings({
            @Mapping(source = "userId", target = "playerInfo.id"),
            @Mapping(source = "totalValue", target = "currentCharm"),
    })
    PlayerPerformanceBean playerSignCharmSumPo2Bean(PlayerSignCharmSumPo po);

    List<PlayerPerformanceBean> playerSignCharmSumPos2Beans(List<PlayerSignCharmSumPo> pos);

    @Mappings({
            @Mapping(source = "userId", target = "playerInfo.id"),
            @Mapping(source = "totalValue", target = "currentCharm"),
    })
    PlayerPerformanceBean playerSignPerformancePos2Bean(PlayerSignPerformancePo po);

    List<PlayerPerformanceBean> playerSignPerformancePos2Beans(List<PlayerSignPerformancePo> pos);

    @Mappings({
            @Mapping(source = "value", target = "charm"),
            @Mapping(source = "njId", target = "room.id"),
            @Mapping(source = "userId", target = "player.id"),
    })
    RankBean charmStatPo2Bean(CharmStatPo po);

    List<RankBean> charmStatPos2Beans(List<CharmStatPo> pos);

    @Mappings({
            @Mapping(source = "playerId", target = "playerInfo.id"),
            @Mapping(source = "roomId", target = "roomInfo.id"),
//            @Mapping(source = "replyChatRate", target = "replyChatRate", qualifiedByName = "convertRate"),
//            @Mapping(source = "chatEnterRoomRate", target = "chatEnterRoomRate", qualifiedByName = "convertRate"),
//            @Mapping(source = "chatGiftRate", target = "chatGiftRate", qualifiedByName = "convertRate"),
//            @Mapping(source = "inviteEnterRoomRate", target = "inviteEnterRoomRate", qualifiedByName = "convertRate"),
//            @Mapping(source = "inviteGiftRate", target = "inviteGiftRate", qualifiedByName = "convertRate"),
    })
    PlayerRankBean playerRoomDay2PlayerRankBean(WcDataPlayerRoomDay po);

    List<PlayerRankBean> playerRoomDays2PlayerRankBeans(List<WcDataPlayerRoomDay> pos);

    @Mappings({
            @Mapping(source = "playerId", target = "playerInfo.id"),
            @Mapping(source = "roomId", target = "roomInfo.id"),
//            @Mapping(source = "replyChatRate", target = "replyChatRate", qualifiedByName = "convertRate"),
//            @Mapping(source = "chatEnterRoomRate", target = "chatEnterRoomRate", qualifiedByName = "convertRate"),
//            @Mapping(source = "chatGiftRate", target = "chatGiftRate", qualifiedByName = "convertRate"),
//            @Mapping(source = "inviteEnterRoomRate", target = "inviteEnterRoomRate", qualifiedByName = "convertRate"),
//            @Mapping(source = "inviteGiftRate", target = "inviteGiftRate", qualifiedByName = "convertRate"),
    })
    PlayerRankBean playerRoomWeek2PlayerRankBean(WcDataPlayerRoomWeek po);

    List<PlayerRankBean> playerRoomWeeks2PlayerRankBeans(List<WcDataPlayerRoomWeek> pos);

    @Mappings({
            @Mapping(source = "playerId", target = "playerInfo.id"),
            @Mapping(source = "roomId", target = "roomInfo.id"),
//            @Mapping(source = "replyChatRate", target = "replyChatRate", qualifiedByName = "convertRate"),
//            @Mapping(source = "chatEnterRoomRate", target = "chatEnterRoomRate", qualifiedByName = "convertRate"),
//            @Mapping(source = "chatGiftRate", target = "chatGiftRate", qualifiedByName = "convertRate"),
//            @Mapping(source = "inviteEnterRoomRate", target = "inviteEnterRoomRate", qualifiedByName = "convertRate"),
//            @Mapping(source = "inviteGiftRate", target = "inviteGiftRate", qualifiedByName = "convertRate"),
    })
    PlayerRankBean playerRoomMonth2PlayerRankBean(WcDataPlayerRoomMonth po);

    List<PlayerRankBean> playerRoomMonths2PlayerRankBeans(List<WcDataPlayerRoomMonth> pos);

    @Named("convertRate")
    default String convertRate(BigDecimal bigDecimal){
        if (bigDecimal == null) {
            return CalculateUtil.formatPercent("0");
        }
        return CalculateUtil.formatPercent(bigDecimal);
    }

    @Named("digitToChinese")
    default String digitToChinese(String digit){
        if (StrUtil.isBlank(digit) || !NumberUtil.isNumber(digit)) {
            return "";
        }

        return "第" + NumberChineseFormatter.format(Long.parseLong(digit), false, false) + "梯队";
    }

    @Mappings({
//            @Mapping(source = "playerId", target = "playerInfo.id"),
            @Mapping(source = "roomId", target = "roomInfo.id"),
//            @Mapping(source = "replyChatRate", target = "replyChatRate", qualifiedByName = "convertRate"),
//            @Mapping(source = "chatEnterRoomRate", target = "chatEnterRoomRate", qualifiedByName = "convertRate"),
//            @Mapping(source = "chatGiftRate", target = "chatGiftRate", qualifiedByName = "convertRate"),
//            @Mapping(source = "inviteEnterRoomRate", target = "inviteEnterRoomRate", qualifiedByName = "convertRate"),
//            @Mapping(source = "inviteGiftRate", target = "inviteGiftRate", qualifiedByName = "convertRate"),
    })
    RoomRankBean roomFamilyDay2RoomRankBean(WcDataRoomFamilyDay po);

    List<RoomRankBean> roomFamilyDays2RoomRankBeans(List<WcDataRoomFamilyDay> pos);


    @Mappings({
//            @Mapping(source = "playerId", target = "playerInfo.id"),
            @Mapping(source = "roomId", target = "roomInfo.id"),
//            @Mapping(source = "replyChatRate", target = "replyChatRate", qualifiedByName = "convertRate"),
//            @Mapping(source = "chatEnterRoomRate", target = "chatEnterRoomRate", qualifiedByName = "convertRate"),
//            @Mapping(source = "chatGiftRate", target = "chatGiftRate", qualifiedByName = "convertRate"),
//            @Mapping(source = "inviteEnterRoomRate", target = "inviteEnterRoomRate", qualifiedByName = "convertRate"),
//            @Mapping(source = "inviteGiftRate", target = "inviteGiftRate", qualifiedByName = "convertRate"),
    })
    RoomRankBean roomFamilyWeek2RoomRankBean(WcDataRoomFamilyWeek po);
    List<RoomRankBean> roomFamilyWeeks2RoomRankBeans(List<WcDataRoomFamilyWeek> pos);

    @Mappings({
//            @Mapping(source = "playerId", target = "playerInfo.id"),
            @Mapping(source = "roomId", target = "roomInfo.id"),
//            @Mapping(source = "replyChatRate", target = "replyChatRate", qualifiedByName = "convertRate"),
//            @Mapping(source = "chatEnterRoomRate", target = "chatEnterRoomRate", qualifiedByName = "convertRate"),
//            @Mapping(source = "chatGiftRate", target = "chatGiftRate", qualifiedByName = "convertRate"),
//            @Mapping(source = "inviteEnterRoomRate", target = "inviteEnterRoomRate", qualifiedByName = "convertRate"),
//            @Mapping(source = "inviteGiftRate", target = "inviteGiftRate", qualifiedByName = "convertRate"),
    })
    RoomRankBean roomFamilyMonth2RoomRankBean(WcDataRoomFamilyMonth po);
    List<RoomRankBean> roomFamilyMonths2RoomRankBeans(List<WcDataRoomFamilyMonth> pos);


    @Mappings({
            @Mapping(source = "hallId", target = "roomInfo.id"),
            @Mapping(source = "currentGrade", target = "echelon", qualifiedByName = "digitToChinese"),
            @Mapping(source = "currentSettleRatioDisplay", target = "settlementRatio"),
            @Mapping(source = "nextGradeGap", target = "next"),
            @Mapping(source = "assessStatisticsValue", target = "examinationFlow"),
            @Mapping(source = "statisticsValue", target = "sumIncome"),
    })
    RoomPerformanceBean hallPerformance2roomPerformanceBean(CreatorDataQueryProto.QueryHallPerformanceResponse po);
    List<RoomPerformanceBean> hallPerformances2roomPerformanceBeans(List<CreatorDataQueryProto.QueryHallPerformanceResponse> pos);

    IncomeCharmBean incomeBean2IncomeCharmBean(IncomeBean incomeBean);

    AssessTimeDto toAssessTimeDto(PaySettlePeriodDto period);

    @Mappings({
            @Mapping(source = "njId", target = "room.id"),
            @Mapping(source = "userId", target = "player.id"),
    })
    RankBean incomeStatPo2Bean(IncomeStatPo po);

    List<RankBean> incomeStatPo2Beans(List<IncomeStatPo> pos);

    List<DataFamilyDayDTO> toDataFamilyDayDTOs(List<WcDataFamilyDay> list);

    List<DataRoomFamilyDayDTO> roomFamilyDay2DTO(List<WcDataRoomFamilyDay> roomFamilyDayList);

    List<DataRoomFamilyWeekDTO> roomFamilyWeeks2DTOs(List<WcDataRoomFamilyWeek> poList);

    DataRoomFamilyWeekDTO roomFamilyWeek2DTO(WcDataRoomFamilyWeek po);

    List<DataRoomFamilyMonthDTO> roomFamilyMonths2DTOs(List<WcDataRoomFamilyMonth> poList);

    DataRoomFamilyMonthDTO roomFamilyMonth2DTO(WcDataRoomFamilyMonth po);

    DataRoomDayDTO roomDay2DTO(WcDataRoomDay po);

    List<DataRoomDayDTO> roomDays2DTOs(List<WcDataRoomDay> pos);

    DataPlayerDayDTO playerDay2DTO(WcDataPlayerDay po);

    List<DataPlayerDayDTO> playerDays2DTOs(List<WcDataPlayerDay> pos);

    List<DataPlayerRoomDayDTO> playerRoomDays2DTOs(List<WcDataPlayerRoomDay> pos);

    DataPlayerRoomDayDTO playerRoomDay2DTO(WcDataPlayerRoomDay po);
}
