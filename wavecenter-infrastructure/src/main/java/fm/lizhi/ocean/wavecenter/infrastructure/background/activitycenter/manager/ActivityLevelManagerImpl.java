package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.manager;

import static fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityLevelConfigService.HAS_LEVEL_REPEAT;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.lizhi.commons.config.core.util.ConfigUtils;

import cn.hutool.core.collection.CollUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityClassificationConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityLevelConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityLevel;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityLevel;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityLevelConfigService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert.ActivityLevelConfigConvert;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityLevelConfig;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityLevelConfigExample;
import fm.lizhi.ocean.wavecenter.datastore.background.mapper.ActivityLevelConfigMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.LogicDeleteConstants;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityClassificationManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityLevelManager;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityLevelManagerImpl implements ActivityLevelManager {

    @Autowired
    private ActivityLevelConfigMapper activityLevelConfigMapper;

    @Autowired
    private ActivityClassificationManager activityClassificationManager;



    @Override
    public Result<Boolean> saveLevel(RequestSaveActivityLevel param) {

        if (existLevelConfig(param.getAppId(), param.getLevel())){
            log.info("存在重复记录. level: {}, appId: {}", param.getLevel(), param.getAppId());
            return RpcResult.fail(HAS_LEVEL_REPEAT, "存在重复等级，保存失败");
        }

        // 检查一下已删除的相同名称的等级是否存在，存在的话，直接拿出来设置为 未删除
        // 兼容的核心场景是：如果等级已经被绑定到模板中，后续需要根据等级名称查询模板时，可以直接获取到。
        // 活动分类在这个场景里更能体现问题。
        ActivityLevelConfig levelConfig = activityLevelConfigMapper.selectOne(ActivityLevelConfig.builder()
                .level(param.getLevel())
                .deleted(LogicDeleteConstants.DELETED)
                .appId(param.getAppId())
                .deployEnv(ConfigUtils.getEnvRequired().name())
                .build());

        boolean success;
        if (levelConfig != null) {
            levelConfig.setDeleted(LogicDeleteConstants.NOT_DELETED);
            levelConfig.setOperator(param.getOperator());
            levelConfig.setModifyTime(new Date());
            success = activityLevelConfigMapper.updateByPrimaryKey(levelConfig) > 0;
            log.info("resume level success. level: {}, appId: {}", levelConfig.getLevel(), levelConfig.getAppId());
        }else {
            levelConfig = new ActivityLevelConfig();
            levelConfig.setAppId(param.getAppId());
            levelConfig.setLevel(param.getLevel());
            levelConfig.setOperator(param.getOperator());
            levelConfig.setDeleted(LogicDeleteConstants.NOT_DELETED);
            levelConfig.setDeployEnv(ConfigUtils.getEnvRequired().name());

            success = activityLevelConfigMapper.insert(levelConfig) > 0;
        }
        return new Result<>(success? GeneralRCode.GENERAL_RCODE_SUCCESS : ActivityLevelConfigService.SAVE_LEVEL_FAIL, success);
    }


    @Override
    public Result<Boolean> updateLevel(RequestUpdateActivityLevel param) {

        if (null == param.getId() || param.getId() <= 0){
            log.info("id is null. level: {}, appId: {}", param.getLevel(), param.getAppId());
            return RpcResult.fail(ActivityLevelConfigService.UPDATE_LEVEL_FAIL, "参数异常，更新失败");
        }

        ActivityLevelConfig levelConfig = activityLevelConfigMapper.selectOne(ActivityLevelConfig.builder()
                .level(param.getLevel())
                .deployEnv(ConfigUtils.getEnvRequired().name())
                .build());

        // 存在的记录和本次更新的 ID 不一致，且未删除，则不允许更新
        if (levelConfig != null && !levelConfig.getId().equals(param.getId()) && levelConfig.getDeleted().equals(LogicDeleteConstants.NOT_DELETED)) {
            log.info("存在重复记录. level: {}, appId: {}", param.getLevel(), param.getAppId());
            return RpcResult.fail(HAS_LEVEL_REPEAT, "存在重复等级，更新失败");

        }

        ActivityLevelConfig config = new ActivityLevelConfig();
        config.setId(param.getId());
        config.setLevel(param.getLevel());
        config.setOperator(param.getOperator());
        config.setDeployEnv(ConfigUtils.getEnvRequired().name());
        config.setModifyTime(new Date());

        boolean success = activityLevelConfigMapper.updateByPrimaryKey(config) > 0;
        return new Result<>(success ? GeneralRCode.GENERAL_RCODE_SUCCESS : ActivityLevelConfigService.UPDATE_LEVEL_FAIL, success);
    }

    @Override
    public Result<Boolean> deleteLevel(Long id, String operator) {
        if (null == id || id <= 0){
            log.info("delete is fail. id is null. id: {}", id);
            return RpcResult.fail(ActivityLevelConfigService.DELETE_LEVEL_FAIL, "参数异常，删除失败");
        }

        ActivityLevelConfig.ActivityLevelConfigBuilder builder = ActivityLevelConfig.builder();
        builder.id(id)
                .deleted(LogicDeleteConstants.DELETED)
                .operator(operator)
                .modifyTime(new Date());

        boolean success = activityLevelConfigMapper.updateByPrimaryKey(builder.build()) > 0;
        return new Result<>(success ? GeneralRCode.GENERAL_RCODE_SUCCESS : ActivityLevelConfigService.DELETE_LEVEL_FAIL, success);
    }


    @Override
    public List<ActivityLevelConfigBean> listByAppId(Integer appId) {
        ActivityLevelConfigExample example = new ActivityLevelConfigExample();
        example.createCriteria().andAppIdEqualTo(appId)
                .andDeletedEqualTo(LogicDeleteConstants.NOT_DELETED)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());

        example.setOrderByClause("create_time desc");
        List<ActivityLevelConfig> list = activityLevelConfigMapper.selectByExample(example);
        return ActivityLevelConfigConvert.I.convert2ActivityLevelConfigBeans(list);
    }

    @Override
    public List<ActivityLevelConfigBean> listByAppIdAndLevelIds(Integer appId, List<Long> levelIds) {
        if (CollUtil.isEmpty(levelIds)){
            return Collections.emptyList();
        }

        ActivityLevelConfigExample example = new ActivityLevelConfigExample();
        example.createCriteria()
                .andIdIn(levelIds)
                .andDeletedEqualTo(LogicDeleteConstants.NOT_DELETED)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andAppIdEqualTo(appId);


        List<ActivityLevelConfig> list = activityLevelConfigMapper.selectByExample(example);
        return ActivityLevelConfigConvert.I.activityLevelConfig2ActivityLevelConfigBeans(list);
    }

    @Override
    public List<Long> batchExistLevel(Integer appId, List<Long> levelIds) {
        if (CollUtil.isEmpty(levelIds)){
            return Collections.emptyList();
        }

        List<ActivityLevelConfigBean> list = listByAppIdAndLevelIds(appId, levelIds);

        // 如果为空，代表等级 ID 都不存在
        if (CollUtil.isEmpty(list)){
            return levelIds;
        }

        Collection<Long> notExistLevelIds = CollUtil.disjunction(levelIds, list.stream().map(ActivityLevelConfigBean::getId).collect(Collectors.toList()));
        return new ArrayList<>(notExistLevelIds);
    }

    @Override
    public Boolean existLevelById(Integer appId, Long levelId) {
        ActivityLevelConfigExample example = new ActivityLevelConfigExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andIdEqualTo(levelId)
                .andDeletedEqualTo(LogicDeleteConstants.NOT_DELETED)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
        ;

        return activityLevelConfigMapper.countByExample(example) > 0;
    }

    @Override
    public ActivityLevelConfigBean getLevelById(Integer appId, Long levelId) {

        ActivityLevelConfig param = ActivityLevelConfig.builder()
                .id(levelId)
                .build();

        ActivityLevelConfig levelConfig = activityLevelConfigMapper.selectByPrimaryKey(param);
        return ActivityLevelConfigConvert.I.convert2ActivityLevelConfigBean(levelConfig);
    }

    @Override
    public ActivityLevelConfigBean getLevelByClassId(Integer appId, Long classId) {
        ActivityClassificationConfigBean classification = activityClassificationManager.getActivityClassification(classId);
        if (null == classification){
            return null;
        }

        return  getLevelById(appId, classification.getLevelId());
    }


    /**
     * 校验活动等级是否存在
     */
    public Boolean existLevelConfig(int appId, String level){
        ActivityLevelConfigExample example = new ActivityLevelConfigExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andLevelEqualTo(level)
                .andDeletedEqualTo(LogicDeleteConstants.NOT_DELETED)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
        ;

        return activityLevelConfigMapper.countByExample(example) > 0;
    }

}
