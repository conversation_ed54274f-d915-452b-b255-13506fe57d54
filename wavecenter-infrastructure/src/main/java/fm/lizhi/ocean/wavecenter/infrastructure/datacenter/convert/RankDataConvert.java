package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.convert;

import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataPayPlayerDay;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataPlayerRoomDay;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.IncomeStatPo;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/25 19:48
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface RankDataConvert {

    RankDataConvert I = Mappers.getMapper(RankDataConvert.class);

    @Mapping(target = "income", source = "income")
    @Mapping(target = "userId", source = "playerId")
    @Mapping(target = "njId", source = "roomId")
    IncomeStatPo dataPlayerRoomDay2IncomeStatPo(WcDataPlayerRoomDay entity);

    List<IncomeStatPo> dataPlayerRoomDays2IncomeStatPos(List<WcDataPlayerRoomDay> entitys);

    @Mapping(target = "income", source = "income")
    @Mapping(target = "userId", source = "playerId")
    @Mapping(target = "njId", source = "roomId")
    IncomeStatPo dataPayPlayerDay2IncomeStatPo(WcDataPayPlayerDay entity);

    List<IncomeStatPo> dataPayPlayerDays2IncomeStatPos(List<WcDataPayPlayerDay> entitys);

}
