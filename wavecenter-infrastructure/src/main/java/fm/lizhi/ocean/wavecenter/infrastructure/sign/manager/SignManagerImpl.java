package fm.lizhi.ocean.wavecenter.infrastructure.sign.manager;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.FamilyAndNjContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.NjAndPlayerContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.RoomSignInfoBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.SignPlayerInfoBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.AuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignConstant;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInfoBean;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.IContractRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.INonContractRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.request.RequestFamilyAndNjCancelApply;
import fm.lizhi.ocean.wavecenter.service.sign.dto.QueryNonContractDTO;
import fm.lizhi.ocean.wavecenter.service.sign.dto.SMSignPlayerPageListReqDto;
import fm.lizhi.ocean.wavecenter.service.sign.dto.SMSignRoomPageListReqDto;
import fm.lizhi.ocean.wavecenter.service.sign.manager.SignManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserInfoDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/13 15:26
 */
@Slf4j
@Component
public class SignManagerImpl implements SignManager {

    @Autowired
    private IContractRemote iContractRemote;
    @Autowired
    private UserManager userManager;
    @Autowired
    private INonContractRemote iNonContractRemote;

    @Override
    public PageBean<RoomSignInfoBean> signRoomPageList(SMSignRoomPageListReqDto reqDto) {
        PageBean<RoomSignInfoBean> pageBean = iContractRemote.guildSignRoomPageList(reqDto);

        List<RoomSignInfoBean> list = pageBean.getList();
        List<Long> roomIds = list.stream().filter(v -> v.getRoomInfo() != null).map(v -> v.getRoomInfo().getId()).collect(Collectors.toList());
        //统计签约主播数
        Map<Long, Integer> countMap = iNonContractRemote.countSignPlayerByRooms(roomIds);

        List<UserInfoDto> roomUserInfoList = userManager.getUserInfoByIds(roomIds);
        Map<Long, UserInfoDto> roomUserInfoMap = roomUserInfoList.stream().collect(Collectors.toMap(SimpleUserDto::getId, v -> v, (k1, k2) -> k2));

        // 查询解约中的记录
        List<FamilyAndNjContractBean> cancelList = getDoingCancel(reqDto.getFamilyId());
        List<Long> cancelNjList = cancelList.stream().map(FamilyAndNjContractBean::getNjUserId).collect(Collectors.toList());

        for (RoomSignInfoBean roomSignInfoBean : list) {
            UserInfoBean roomInfo = roomSignInfoBean.getRoomInfo();
            if (roomInfo == null) {
                continue;
            }
            roomSignInfoBean.setSignPlayerCnt(countMap.getOrDefault(roomInfo.getId(), 0));
            UserInfoDto userInfoDto = roomUserInfoMap.get(roomInfo.getId());
            if (userInfoDto != null) {
                roomInfo.setBand(userInfoDto.getBand());
                roomInfo.setName(userInfoDto.getName());
                roomInfo.setPhoneNum(userInfoDto.getPhoneNum());
            }
            if (cancelNjList.contains(roomInfo.getId())) {
                roomSignInfoBean.setSignStatus(SignConstant.CANCELING);
            }
        }

        return pageBean;
    }

    private List<FamilyAndNjContractBean> getDoingCancel(Long familyId){
        //查询解约的待办
        PageBean<FamilyAndNjContractBean> cancelList = iContractRemote.queryCancelApply(RequestFamilyAndNjCancelApply.builder()
                .familyId(familyId)
                .audit(AuditStatusEnum.AGREED)
                .audit(AuditStatusEnum.PENDING)
                .relation(SignRelationEnum.WAIT_SIGN)
                .relation(SignRelationEnum.SIGNING)
                .build());
        return cancelList.getList();
    }

    @Override
    public PageBean<SignPlayerInfoBean> signPlayerPageList(SMSignPlayerPageListReqDto reqDto) {
        PageBean<SignPlayerInfoBean> pageBean = iContractRemote.signPlayerPageList(reqDto);
        List<SignPlayerInfoBean> list = pageBean.getList();

        // 查询解约列表
        List<NjAndPlayerContractBean> cancelList = queryDoingCancelList(reqDto.getRoomIds());
        List<Long> cancelPlayerIds = cancelList.stream().map(NjAndPlayerContractBean::getPlayerUserId).collect(Collectors.toList());

        Set<Long> userIds = new HashSet<>();
        for (SignPlayerInfoBean bean : list) {
            UserBean roomInfo = bean.getRoomInfo();
            if (roomInfo != null) {
                userIds.add(roomInfo.getId());
            }
            UserBean playerInfo = bean.getPlayerInfo();
            if (playerInfo != null) {
                userIds.add(playerInfo.getId());
            }
        }

        Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(new ArrayList<>(userIds));
        for (SignPlayerInfoBean bean : list) {
            UserBean roomInfo = bean.getRoomInfo();
            if (roomInfo != null) {
                SimpleUserDto roomDto = userMap.get(roomInfo.getId());
                if (roomDto != null) {
                    roomInfo.setBand(roomDto.getBand());
                    roomInfo.setName(roomDto.getName());
                }
            }
            UserBean playerInfo = bean.getPlayerInfo();
            if (playerInfo != null) {
                SimpleUserDto playerDto = userMap.get(playerInfo.getId());
                if (playerDto != null) {
                    playerInfo.setBand(playerDto.getBand());
                    playerInfo.setName(playerDto.getName());
                }
                if (cancelPlayerIds.contains(playerInfo.getId())) {
                    bean.setSignStatus(SignConstant.CANCELING);
                }
            }
        }
        return pageBean;
    }

    private List<NjAndPlayerContractBean> queryDoingCancelList(List<Long> njIds){
        PageBean<NjAndPlayerContractBean> pageList = iNonContractRemote.queryList(QueryNonContractDTO.builder()
                .njIds(njIds)
                .type(ContractTypeEnum.CANCEL)
                .status(SignRelationEnum.WAIT_SIGN)
                .status(SignRelationEnum.SIGN_CONFIRM)
                .pageNo(1).pageSize(100)
                .build()
        );
        return pageList.getList();
    }
}
