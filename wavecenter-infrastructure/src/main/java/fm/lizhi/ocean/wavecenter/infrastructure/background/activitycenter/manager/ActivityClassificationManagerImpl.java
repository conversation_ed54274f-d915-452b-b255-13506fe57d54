package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.manager;

import cn.hutool.core.collection.CollUtil;
import com.lizhi.commons.config.core.util.ConfigUtils;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityBigClassBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityClassConfigBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityBigClassTypeEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseActivityClassification;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityClassificationConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.*;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityClassificationConfigService;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert.ActivityClassificationConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.dao.ActivityClassificationDao;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.dao.ActivityLevelDao;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityBigClass;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityBigClassCategory;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityClassConfig;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityLevelConfig;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.processor.IActivityClassificationProcess;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.LogicDeleteConstants;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityClassificationManager;
import fm.lizhi.ocean.wavecenter.service.common.manager.IdManager;
import fm.lizhi.ocean.wavecenter.base.processor.ProcessorFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityClassificationConfigService.*;

/**
 * 活动分类管理器
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityClassificationManagerImpl implements ActivityClassificationManager {

    @Autowired
    private ActivityClassificationDao activityClassificationDao;

    @Autowired
    private ActivityLevelDao activityLevelDao;

    @Autowired
    private ProcessorFactory processorFactory;

    @Autowired
    private IdManager idManager;


    @Override
    public Result<Void> saveBigClassification(RequestSaveActivityBigClass req) {
        try {
            IActivityClassificationProcess processor = processorFactory.getProcessor(IActivityClassificationProcess.class);
            Integer appId = req.getAppId();
            String name = req.getName();
            ActivityBigClass oldEntity = activityClassificationDao.getActivityBigClassByAppIdAndName(appId, name);
            if (oldEntity != null && !oldEntity.getDeleted()) {
                log.info("saveBigClassification. name duplicate. name: {}, appId: {}", name, appId);
                return RpcResult.fail(HAS_CLASSIFICATION_REPEAT, "存在重复大类");
            }
            if (!ActivityBigClassTypeEnum.isValidType(req.getType())) {
                log.info("saveBigClassification. type: {}, appId: {}", req.getType(), appId);
                return RpcResult.fail(SAVE_CLASSIFICATION_FAIL, "分类不存在");
            }

            ActivityBigClass entity = buildActivityBigClass(idManager.genId(), appId, name, req.getType(), req.getOperator(), req.getWeight());
            //先同步到业务，如果失败了，中断流程
            Result<Void> result = processor.syncBigClassToBiz(entity);
            if (RpcResult.isFail(result)) {
                log.warn("同步活动大类失败, id: {}, name: {}, type: {}, bizType: {}, weight: {}", entity.getId(), entity.getName());
                return RpcResult.fail(result.rCode(), result.getMessage());
            }

            activityClassificationDao.saveBigClassificationAndCategory(entity, req.getCategoryList());
            return RpcResult.success();
        } catch (RuntimeException e) {
            log.error("saveBigClassification error. req: {}", req, e);
            return RpcResult.fail(ActivityClassificationConfigService.SAVE_CLASSIFICATION_FAIL, e.getMessage());
        }
    }

    @Override
    public Result<Void> updateBigClassification(RequestUpdateActivityBigClass req) {
        try {
            IActivityClassificationProcess processor = processorFactory.getProcessor(IActivityClassificationProcess.class);
            Long id = req.getId();
            String name = req.getName();
            ActivityBigClass oldEntity = activityClassificationDao.getActivityBigClass(id);
            if (oldEntity == null) {
                log.info("updateBigClassification fail. id not found. id: {}", id);
                return RpcResult.fail(CommonService.PARAM_ERROR, "更新大类失败，id不存在");
            }
            if (!Objects.equals(oldEntity.getDeployEnv(), ConfigUtils.getEnvRequired().name())) {
                log.info("updateBigClassification fail. deployEnv not match. id: {}, deployEnv: {}", id, oldEntity.getDeployEnv());
                return RpcResult.fail(CommonService.PARAM_ERROR, "更新大类失败，部署环境不匹配");
            }

            Integer appId = oldEntity.getAppId();
            ActivityBigClass nameHolder = activityClassificationDao.getActivityBigClassByAppIdAndName(appId, name);
            if (nameHolder != null && !Objects.equals(nameHolder.getId(), id)) {
                log.info("updateBigClassification fail. name duplicate. name: {}, appId: {}, currentId: {}, holderId: {}",
                        name, appId, id, nameHolder.getId());
                if (nameHolder.getDeleted()) {
                    return RpcResult.fail(HAS_CLASSIFICATION_REPEAT, "存在已删除的重复大类，请直接新建同名大类以恢复旧大类");
                } else {
                    return RpcResult.fail(HAS_CLASSIFICATION_REPEAT, "存在重复大类");
                }
            }

            ActivityBigClass entity = buildActivityBigClass(oldEntity.getId(), null, name, oldEntity.getType(), req.getOperator(), req.getWeight());
            //先修改业务的
            Result<Void> result = processor.syncBigClassToBiz(entity);
            if (RpcResult.isFail(result)) {
                log.warn("同步活动大类失败, id: {}, name: {}, type: {}, bizType: {}, weight: {}", entity.getId(), entity.getName());
                return RpcResult.fail(result.rCode(), result.getMessage());
            }
            activityClassificationDao.updateBigClassAndCategory(req);
            return RpcResult.success();
        } catch (RuntimeException e) {
            log.error("updateBigClassification error. req: {}", req, e);
            return RpcResult.fail(UPDATE_CLASSIFICATION_FAIL, e.getMessage());
        }
    }

    @Override
    public Result<Void> deleteBigClassification(RequestDeleteActivityBigClass req) {
        try {
            Long id = req.getId();
            ActivityBigClass oldEntity = activityClassificationDao.getActivityBigClass(id);
            if (oldEntity == null) {
                log.info("deleteBigClassification fail. id not found. id: {}", id);
                return RpcResult.fail(CommonService.PARAM_ERROR, "删除大类失败，id不存在");
            }
            if (oldEntity.getDeleted()) {
                log.info("deleteBigClassification fail. already deleted. id: {}", id);
                return RpcResult.fail(CommonService.PARAM_ERROR, "删除大类失败，已删除");
            }
            if (!Objects.equals(oldEntity.getDeployEnv(), ConfigUtils.getEnvRequired().name())) {
                log.info("deleteBigClassification fail. deployEnv not match. id: {}, deployEnv: {}", id, oldEntity.getDeployEnv());
                return RpcResult.fail(CommonService.PARAM_ERROR, "删除大类失败，部署环境不匹配");
            }

            IActivityClassificationProcess processor = processorFactory.getProcessor(IActivityClassificationProcess.class);
            //先删除业务中的分类，如果失败了，中断流程
            Result<Void> result = processor.deleteBigClass(id, req.getOperator());
            if (RpcResult.isFail(result)) {
                return RpcResult.fail(result.rCode(), result.getMessage());
            }

            activityClassificationDao.deleteBigClassification(req);
            return RpcResult.success();
        } catch (RuntimeException e) {
            log.error("deleteBigClassification error. req: {}", req, e);
            return RpcResult.fail(DELETE_CLASSIFICATION_FAIL, e.getMessage());
        }
    }

    @Override
    public Result<List<ActivityBigClassBean>> listBigClassByAppId(int appId) {
        List<ActivityBigClass> entities = activityClassificationDao.listBigClassificationByAppId(appId);
        List<ActivityBigClassBean> beans = ActivityClassificationConvert.I.convert2ActivityBigClassBeans(entities);

        List<Long> bigclassIds = entities.stream().map(ActivityBigClass::getId).collect(Collectors.toList());
        // 查询品类
        Map<Long, List<ActivityBigClassCategory>> categoryMap = activityClassificationDao.getBigClassCategoryMap(appId, bigclassIds);
        for (ActivityBigClassBean bean : beans) {
            List<ActivityBigClassCategory> cList = categoryMap.get(bean.getId());
            if (CollectionUtils.isEmpty(cList)) {
                continue;
            }
            List<Integer> collected = cList.stream()
                    .map(ActivityBigClassCategory::getCategoryValue)
                    .distinct()
                    .collect(Collectors.toList());
            bean.setCategoryList(collected);
        }

        return RpcResult.success(beans);
    }

    @Override
    public Result<Void> saveClassification(RequestSaveActivityClassification req) {
        try {
            Long bigClassId = req.getBigClassId();
            String name = req.getName();
            Long levelId = req.getLevelId();
            Result<Void> checkResult = preCheckBigClassAndLevel(bigClassId, levelId);
            if (RpcResult.isFail(checkResult)) {
                return checkResult;
            }
            ActivityClassConfig oldEntity = activityClassificationDao.getActivityClassConfigByBigClassIdAndName(bigClassId, name);
            if (oldEntity != null && !oldEntity.getDeleted()) {
                log.info("saveClassification. name duplicate. name: {}, bigClassId: {}", name, bigClassId);
                return RpcResult.fail(HAS_CLASSIFICATION_REPEAT, "存在重复分类");
            }

            IActivityClassificationProcess processor = processorFactory.getProcessor(IActivityClassificationProcess.class);
            //先同步到业务，如果失败了，中断流程
            //构建实体
            ActivityClassConfig entity = buildActivityClassConfig(idManager.genId(), bigClassId, name, levelId, req.getOperator(), req.getWeight());
            Result<Void> result = processor.syncClassToBiz(entity);
            if (RpcResult.isFail(result)) {
                log.warn("同步活动二级分类失败, id: {}, bigClassId: {}", entity.getId(), entity.getBigClassId());
                return RpcResult.fail(result.rCode(), result.getMessage());
            }
            activityClassificationDao.saveClassification(entity);
            return RpcResult.success();
        } catch (RuntimeException e) {
            log.error("saveClassification error. req: {}", req, e);
            return RpcResult.fail(ActivityClassificationConfigService.SAVE_CLASSIFICATION_FAIL, e.getMessage());
        }
    }

    /**
     * 保存分类之前的校验, 主要检测大类和等级是否存在以及是否匹配
     *
     * @param bigClassId 大类id
     * @param levelId    登记id
     * @return 校验结果
     */
    private Result<Void> preCheckBigClassAndLevel(Long bigClassId, Long levelId) {
        // 校验大类是否存在
        ActivityBigClass bigClass = activityClassificationDao.getActivityBigClass(bigClassId);
        if (bigClass == null) {
            log.info("big class not found. bigClassId: {}", bigClassId);
            return RpcResult.fail(SAVE_CLASSIFICATION_BIG_CLASS_NOT_FOUND, "大类不存在");
        }
        if (bigClass.getDeleted()) {
            log.info("big class is deleted. bigClassId: {}", bigClassId);
            return RpcResult.fail(SAVE_CLASSIFICATION_BIG_CLASS_NOT_FOUND, "大类已删除");
        }
        if (!Objects.equals(bigClass.getDeployEnv(), ConfigUtils.getEnvRequired().name())) {
            log.info("big class deployEnv not match. bigClassId: {}, deployEnv: {}", bigClassId, bigClass.getDeployEnv());
            return RpcResult.fail(SAVE_CLASSIFICATION_BIG_CLASS_NOT_FOUND, "大类部署环境不匹配");
        }
        Integer appId = bigClass.getAppId();
        // 校验等级是否存在
        ActivityLevelConfig levelConfig = activityLevelDao.getActivityLevelConfig(levelId);
        if (levelConfig == null) {
            log.info("activityLevel not found, levelId: {}", levelId);
            return RpcResult.fail(SAVE_CLASSIFICATION_ACTIVITY_LEVEL_NOT_EXIST, "活动等级不存在");
        }
        if (Objects.equals(levelConfig.getDeleted(), LogicDeleteConstants.DELETED)) {
            log.info("activityLevel is deleted, levelId: {}", levelId);
            return RpcResult.fail(SAVE_CLASSIFICATION_ACTIVITY_LEVEL_NOT_EXIST, "活动等级已删除");
        }
        if (!Objects.equals(levelConfig.getAppId(), appId)) {
            log.info("activityLevel appId and bigClass appId not match, levelId: {}, levelAppId: {}, bigClassId: {}, bigClassAppId: {}",
                    levelId, levelConfig.getAppId(), bigClassId, bigClass.getAppId());
            return RpcResult.fail(SAVE_CLASSIFICATION_ACTIVITY_LEVEL_NOT_EXIST, "活动等级与大类appId不匹配");
        }
        if (!Objects.equals(levelConfig.getDeployEnv(), ConfigUtils.getEnvRequired().name())) {
            log.info("activityLevel deployEnv not match, levelId: {}, deployEnv: {}", levelId, levelConfig.getDeployEnv());
            return RpcResult.fail(SAVE_CLASSIFICATION_ACTIVITY_LEVEL_NOT_EXIST, "活动等级部署环境不匹配");
        }
        return RpcResult.success();
    }

    @Override
    public Result<Void> updateClassification(RequestUpdateActivityClassification req) {
        try {
            Long id = req.getId();
            Long levelId = req.getLevelId();
            String name = req.getName();
            ActivityClassConfig oldEntity = activityClassificationDao.getActivityClassConfig(id);
            if (oldEntity == null) {
                log.info("updateClassification fail. id not found. id: {}", id);
                return RpcResult.fail(CommonService.PARAM_ERROR, "更新分类失败，id不存在");
            }
            if (!Objects.equals(oldEntity.getDeployEnv(), ConfigUtils.getEnvRequired().name())) {
                log.info("updateClassification fail. deployEnv not match. id: {}, deployEnv: {}", id, oldEntity.getDeployEnv());
                return RpcResult.fail(CommonService.PARAM_ERROR, "更新分类失败，部署环境不匹配");
            }
            Long bigClassId = oldEntity.getBigClassId();
            Result<Void> checkResult = preCheckBigClassAndLevel(bigClassId, levelId);
            if (RpcResult.isFail(checkResult)) {
                return checkResult;
            }
            ActivityClassConfig nameHolder = activityClassificationDao.getActivityClassConfigByBigClassIdAndName(bigClassId, name);
            if (nameHolder != null && !Objects.equals(nameHolder.getId(), id)) {
                log.info("updateClassification fail. name duplicate. name: {}, bigClassId: {}, currentId: {}, holderId: {}",
                        name, bigClassId, id, nameHolder.getId());
                if (nameHolder.getDeleted()) {
                    return RpcResult.fail(HAS_CLASSIFICATION_REPEAT, "存在已删除的重复分类，请直接新建同名分类以恢复旧分类");
                } else {
                    return RpcResult.fail(HAS_CLASSIFICATION_REPEAT, "存在重复分类");
                }
            }

            IActivityClassificationProcess processor = processorFactory.getProcessor(IActivityClassificationProcess.class);
            //先同步到业务，如果失败了，中断流程
            //构建实体
            ActivityClassConfig entity = buildActivityClassConfig(oldEntity.getId(), bigClassId, name, levelId, req.getOperator(), req.getWeight());
            Result<Void> result = processor.syncClassToBiz(entity);
            if (RpcResult.isFail(result)) {
                log.warn("同步活动二级分类失败, id: {}, bigClassId: {}", entity.getId(), entity.getBigClassId());
                return RpcResult.fail(result.rCode(), result.getMessage());
            }

            activityClassificationDao.updateClassification(req);
            return RpcResult.success();
        } catch (RuntimeException e) {
            log.error("updateClassification error. req: {}", req, e);
            return RpcResult.fail(UPDATE_CLASSIFICATION_FAIL, e.getMessage());
        }
    }

    @Override
    public Result<Void> deleteClassification(RequestDeleteActivityClassification req) {
        try {
            Long id = req.getId();
            ActivityClassConfig oldEntity = activityClassificationDao.getActivityClassConfig(id);
            if (oldEntity == null) {
                log.info("deleteClassification fail. id not found. id: {}", id);
                return RpcResult.fail(CommonService.PARAM_ERROR, "删除分类失败，id不存在");
            }
            if (oldEntity.getDeleted()) {
                log.info("deleteClassification fail. already deleted. id: {}", id);
                return RpcResult.fail(CommonService.PARAM_ERROR, "删除分类失败，已删除");
            }
            if (!Objects.equals(oldEntity.getDeployEnv(), ConfigUtils.getEnvRequired().name())) {
                log.info("deleteClassification fail. deployEnv not match. id: {}, deployEnv: {}", id, oldEntity.getDeployEnv());
                return RpcResult.fail(CommonService.PARAM_ERROR, "删除分类失败，部署环境不匹配");
            }
            IActivityClassificationProcess processor = processorFactory.getProcessor(IActivityClassificationProcess.class);
            //先删除业务中的分类，如果失败了，中断流程
            Result<Void> result = processor.deleteClass(id, req.getOperator());
            if (RpcResult.isFail(result)) {
                return RpcResult.fail(result.rCode(), result.getMessage());
            }
            activityClassificationDao.deleteClassification(req);
            return RpcResult.success();
        } catch (RuntimeException e) {
            log.error("deleteClassification error. req: {}", req, e);
            return RpcResult.fail(DELETE_CLASSIFICATION_FAIL, e.getMessage());
        }
    }

    @Override
    public Result<List<ActivityClassConfigBean>> listClassificationByBigClassId(long bigClassId) {
        List<ActivityClassConfig> entities = activityClassificationDao.listClassificationByBigClassId(bigClassId);
        List<ActivityClassConfigBean> beans = ActivityClassificationConvert.I.convert2ActivityClassConfigBeans(entities);
        return RpcResult.success(beans);
    }

    @Override
    public List<ActivityClassificationConfigBean> batchActivityClassification(List<Long> classIds) {
        if (CollectionUtils.isEmpty(classIds)) {
            return Collections.emptyList();
        }
        ArrayList<Long> distinctClassIds = new ArrayList<>(new LinkedHashSet<>(classIds));
        Map<Long, ActivityClassConfig> classConfigMap = activityClassificationDao.getClassIdToClassConfigMapByClassIds(distinctClassIds);
        LinkedHashSet<Long> bigClassIds = new LinkedHashSet<>();
        for (ActivityClassConfig classConfig : classConfigMap.values()) {
            bigClassIds.add(classConfig.getBigClassId());
        }
        Map<Long, ActivityBigClass> bigClassMap = activityClassificationDao.getBigClassIdToBigClassMapByBigClassIds(new ArrayList<>(bigClassIds));
        ArrayList<ActivityClassificationConfigBean> beans = new ArrayList<>(distinctClassIds.size());
        for (Long classId : distinctClassIds) {
            ActivityClassConfig classConfig = classConfigMap.get(classId);
            if (classConfig == null) {
                continue;
            }
            ActivityBigClass bigClass = bigClassMap.get(classConfig.getBigClassId());
            beans.add(ActivityClassificationConvert.I.convert2ActivityClassificationBean(classConfig, bigClass));
        }
        return beans;
    }

    @Override
    public ActivityClassificationConfigBean getActivityClassification(Long classId) {
        ActivityClassConfig classConfig = activityClassificationDao.getActivityClassConfig(classId);
        if (classConfig == null) {
            return null;
        }
        Long bigClassId = classConfig.getBigClassId();
        ActivityBigClass bigClass = activityClassificationDao.getActivityBigClass(bigClassId);
        return ActivityClassificationConvert.I.convert2ActivityClassificationBean(classConfig, bigClass);
    }

    @Override
    public Result<List<ResponseActivityClassification>> getClassificationList(int appId, ArrayList<Integer> categoryValue) {
        List<ActivityBigClass> bigClasses = activityClassificationDao.listBigClassificationByAppId(appId);
        if (CollUtil.isNotEmpty(categoryValue)){
            List<ActivityBigClassCategory> bigClassCategoryList = activityClassificationDao.getBigClassesByCategoryValue(appId, categoryValue);
            if (CollectionUtils.isNotEmpty(bigClassCategoryList)){
                List<Long> bigClassIdList = bigClassCategoryList.stream().map(ActivityBigClassCategory::getBigClassId).collect(Collectors.toList());
                bigClasses = bigClasses.stream().filter(bigClass -> bigClassIdList.contains(bigClass.getId())).collect(Collectors.toList());
            }
        }

        List<ActivityClassConfig> classConfigs = activityClassificationDao.listClassificationByAppId(appId);
        HashMap<Long, List<ActivityClassConfig>> bigClassIdToClassConfigsMap = new HashMap<>();
        for (ActivityClassConfig classConfig : classConfigs) {
            Long bigClassId = classConfig.getBigClassId();
            bigClassIdToClassConfigsMap.computeIfAbsent(bigClassId, k -> new ArrayList<>()).add(classConfig);
        }
        ArrayList<ResponseActivityClassification> beans = new ArrayList<>(bigClasses.size());
        for (ActivityBigClass bigClass : bigClasses) {
            Long bigClassId = bigClass.getId();
            List<ActivityClassConfig> classConfigsOfBigClass = bigClassIdToClassConfigsMap.getOrDefault(bigClassId, Collections.emptyList());
            ResponseActivityClassification resp = ActivityClassificationConvert.I.convert2ResponseActivityClassification(bigClass, classConfigsOfBigClass);
            beans.add(resp);
        }
        return RpcResult.success(beans);
    }

    /**
     * 构建活动大类
     *
     * @return 活动大类
     */
    private ActivityBigClass buildActivityBigClass(long id, Integer appId, String name, Integer type, String operator, int weight) {
        ActivityBigClass entity = new ActivityBigClass();
        entity.setId(id);
        entity.setAppId(appId);
        entity.setName(name);
        entity.setType(type);
        entity.setOperator(operator);
        entity.setWeight(weight);
        entity.setCreateTime(new Date());
        entity.setModifyTime(new Date());
        entity.setDeleted(false);

        //判空
        if (type != null) {
            entity.setType(type);
        }
        if (appId != null) {
            entity.setAppId(appId);
        }
        entity.setDeployEnv(ConfigUtils.getEnvRequired().name());
        return entity;
    }

    /**
     * 构建活动二级分类
     *
     * @return 活动二级分类
     */
    private ActivityClassConfig buildActivityClassConfig(long id, Long bigClassId, String name, Long levelId, String operator, int weight) {
        ActivityClassConfig entity = new ActivityClassConfig();
        entity.setId(id);
        entity.setBigClassId(bigClassId);
        entity.setName(name);
        entity.setLevelId(levelId);
        entity.setOperator(operator);
        entity.setWeight(weight);
        entity.setDeployEnv(ConfigUtils.getEnvRequired().name());
        entity.setCreateTime(new Date());
        entity.setModifyTime(new Date());
        entity.setDeleted(false);
        return entity;
    }

    @Override
    public List<Long> getClassificationIdListByLevelId(Long levelId) {
        if (levelId == null) {
            return Collections.emptyList();
        }

        List<ActivityClassConfig> classConfigs = activityClassificationDao.listClassificationByLevelId(levelId);
        if (CollectionUtils.isEmpty(classConfigs)) {
            return Collections.emptyList();
        }

        return classConfigs.stream()
            .map(ActivityClassConfig::getId)
            .collect(Collectors.toList());
    }

    @Override
    public List<Integer> getClassCategoryValue(int appId, Long classId) {
        return activityClassificationDao.getClassCategory(appId, classId);
    }
}
