package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.convert;

import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcPayAccountFlow;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.kafka.consumer.AccountFlowMsg;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.PayAccountFlowDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2025/4/22 18:28
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PayAccountFlowConvert {

    PayAccountFlowConvert I = Mappers.getMapper(PayAccountFlowConvert.class);

    @Mapping(source = "appId", target = "payAppId")
    @Mapping(source = "id", target = "flowId")
    WcPayAccountFlow msgToEntity(AccountFlowMsg msg);

    @Mapping(target = "appId", expression = "java(fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PayTenantCodeEnum.toAppId(msg.getTenantCode()))")
    @Mapping(target = "flowId", source = "id")
    @Mapping(target = "tradeDate", expression = "java(fm.lizhi.commons.util.DateUtil.formatStrToDate(msg.getTradeTime(), fm.lizhi.commons.util.DateUtil.date_2))")
    @Mapping(target = "accountEngineCode", source = "accountCode")
    PayAccountFlowDTO msgToDto(AccountFlowMsg msg);

}
