package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.common.datastore.mysql.constant.ParamContants;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestCountGeneralActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestPageGeneralActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestPageHotActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestPageActivityTemplate;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityTemplateInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface ActivityTemplateInfoExtMapper {

    @Select("<script>\n" +
            "  SELECT * FROM `activity_template_info` \n" +
            "  <where>\n" +
            "    `deleted` = 0 AND `deploy_env` = #{deployEnv} \n" +
            "    <if test=\"entity.appId != null\"> \n" +
            "      AND `app_id` = #{entity.appId} \n" +
            "    </if> \n" +
            "    <if test=\"entity.id != null\"> \n" +
            "      AND `id` = #{entity.id} \n" +
            "    </if> \n" +
            "    <if test=\"entity.name != null and entity.name != ''\"> \n" +
            "      AND `name` like concat('%', #{entity.name}, '%') \n" +
            "    </if> \n" +
            "    <if test=\"entity.status != null\"> \n" +
            "      AND `status` = #{entity.status} \n" +
            "    </if> \n" +
            "    <if test=\"entity.hotRec != null\"> \n" +
            "      AND `hot_rec` = #{entity.hotRec} \n" +
            "    </if> \n" +
            "    <if test='null != entity.classIds and entity.classIds.size > 0'> AND `class_id` IN " +
            "      <foreach collection='entity.classIds' item='cId' open='(' separator=',' close=')'>  " +
            "       #{cId} " +
            "      </foreach> " +
            "    </if> \n" +
            "  </where> \n" +
            "  ORDER by `create_time` DESC, `id` DESC \n" +
            "</script>")
    PageList<ActivityTemplateInfo> pageTemplate(@Param(ParamContants.ENTITY) RequestPageActivityTemplate param,
                                                @Param("deployEnv") String deployEnv,
                                                @Param(ParamContants.PAGE_NUMBER) int pageNumber,
                                                @Param(ParamContants.PAGE_SIZE) int pageSize);

    @Select({
            "<script>",
            "SELECT * FROM activity_template_info ati",
            "LEFT JOIN activity_class_config acc ON ati.class_id = acc.id",
            "LEFT JOIN activity_big_class_category abcc ON acc.big_class_id = abcc.big_class_id",
            "WHERE ati.`deleted` = 0",
            "  AND ati.`deploy_env` = #{deployEnv}",
            "  AND ati.`status` = 1",
            "  AND ati.`app_id` = #{entity.appId}",
            "  AND ati.`hot_rec` = 1",
            "  <if test='entity.categoryValue != null and !entity.categoryValue.isEmpty()'>",
            "    AND abcc.category_value IN ",
            "    <foreach collection='entity.categoryValue' item='category' open='(' separator=',' close=')'>",
            "      #{category}",
            "    </foreach>",
            "  </if>",
            "    <if test=\"njList != null and !njList.isEmpty() \">",
            "    AND (ati.id not in (select distinct template_id from activity_template_nj_list where list_type = 1 and app_id = #{entity.appId}) or ",
            "       ati.id in (select distinct template_id from activity_template_nj_list where app_id = #{entity.appId} and list_type = 1 and nj_id in ",
            "            <foreach collection=\"njList\" item=\"njId\" open=\"(\" separator=\",\" close=\")\"> ",
            "              #{njId} ",
            "            </foreach>",
            "     )) ",
            "    </if>" ,
            "ORDER BY",
            "  ati.`hot_weight` DESC,",
            "  ati.`create_time` DESC,",
            "  ati.`id` DESC",
            "</script>"
    })
    PageList<ActivityTemplateInfo> pageHotTemplate(@Param(ParamContants.ENTITY) RequestPageHotActivityTemplate param,
                                                   @Param("deployEnv") String deployEnv,
                                                   @Param("njList") List<Long> njList,
                                                   @Param(ParamContants.PAGE_NUMBER) int pageNumber,
                                                   @Param(ParamContants.PAGE_SIZE) int pageSize);

    @Select("<script>\n" +
            "  SELECT `activity_template_info`.* FROM ( \n" +
            "      SELECT `id`, `big_class_id`, `create_time`, `weight` FROM `activity_class_config` \n" +
            "      WHERE `deploy_env` = #{deployEnv} \n" +
            "        <if test=\"entity.classIds != null and !entity.classIds.isEmpty() \"> \n" +
            "          AND `id` IN \n" +
            "            <foreach collection=\"entity.classIds\" item=\"classId\" open=\"(\" separator=\",\" close=\")\"> \n" +
            "              #{classId} \n" +
            "            </foreach> \n" +
            "        </if> \n" +
            "    ) `activity_class_config` \n" +
            "  LEFT JOIN `activity_big_class` \n" +
            "    ON `activity_class_config`.`big_class_id` = `activity_big_class`.`id` \n" +
            "  LEFT JOIN `activity_template_info` \n" +
            "    ON `activity_class_config`.`id` = `activity_template_info`.`class_id` \n" +
            "  WHERE `activity_template_info`.`deleted` = 0 \n" +
            "    AND `activity_template_info`.`deploy_env` = #{deployEnv} \n" +
            "    AND `activity_template_info`.`status` = 1 \n" +
            "    AND `activity_template_info`.`app_id` = #{entity.appId} \n" +
            "    <if test=\"entity.njIds != null and !entity.njIds.isEmpty() \"> \n" +
            "    AND (`activity_template_info`.id not in (select distinct template_id from activity_template_nj_list where list_type = 1 and app_id = #{entity.appId}) or " +
            "       `activity_template_info`.id in (select distinct template_id from activity_template_nj_list where app_id = #{entity.appId} and list_type = 1 and nj_id in " +
            "            <foreach collection=\"entity.njIds\" item=\"njId\" open=\"(\" separator=\",\" close=\")\"> \n" +
            "              #{njId} \n" +
            "            </foreach> \n" +
            " )) " +
            "    </if> \n" +
            "  ORDER by \n" +
            "    `activity_big_class`.`weight` DESC, \n" +
            "    `activity_class_config`.`weight` DESC, \n" +
            "    `activity_template_info`.`weight` DESC, \n" +
            "    `activity_template_info`.`create_time` DESC \n" +
            "</script>")
    PageList<ActivityTemplateInfo> pageGeneralTemplate(@Param(ParamContants.ENTITY) RequestPageGeneralActivityTemplate param,
                                                       @Param("deployEnv") String deployEnv,
                                                       @Param(ParamContants.PAGE_NUMBER) int pageNumber,
                                                       @Param(ParamContants.PAGE_SIZE) int pageSize);

    @Select("<script>\n" +
            "  SELECT COUNT(*) FROM ( \n" +
            "      SELECT `id`, `big_class_id`, `create_time`, `weight` FROM `activity_class_config` \n" +
            "      WHERE `deploy_env` = #{deployEnv} \n" +
            "        <if test=\"entity.classIds != null and !entity.classIds.isEmpty() \"> \n" +
            "          AND `id` IN \n" +
            "            <foreach collection=\"entity.classIds\" item=\"classId\" open=\"(\" separator=\",\" close=\")\"> \n" +
            "              #{classId} \n" +
            "            </foreach> \n" +
            "        </if> \n" +
            "    ) `activity_class_config` \n" +
            "  LEFT JOIN `activity_big_class` \n" +
            "    ON `activity_class_config`.`big_class_id` = `activity_big_class`.`id` \n" +
            "  LEFT JOIN `activity_template_info` \n" +
            "    ON `activity_class_config`.`id` = `activity_template_info`.`class_id` \n" +
            "  WHERE `activity_template_info`.`deleted` = 0 \n" +
            "    AND `activity_template_info`.`deploy_env` = #{deployEnv} \n" +
            "    AND `activity_template_info`.`status` = 1 \n" +
            "    AND `activity_template_info`.`app_id` = #{entity.appId} \n" +
            "    <if test=\"entity.njIds != null and !entity.njIds.isEmpty() \"> \n" +
            "    AND (`activity_template_info`.id not in (select distinct template_id from activity_template_nj_list where list_type = 1 and app_id = #{entity.appId}) or " +
            "       `activity_template_info`.id in (select distinct template_id from activity_template_nj_list where app_id = #{entity.appId} and list_type = 1 and nj_id in " +
            "            <foreach collection=\"entity.njIds\" item=\"njId\" open=\"(\" separator=\",\" close=\")\"> \n" +
            "              #{njId} \n" +
            "            </foreach> \n" +
            " )) " +
            "    </if> \n" +
            "</script>")
    Long countGeneralTemplate(@Param(ParamContants.ENTITY) RequestCountGeneralActivityTemplate param,
                             @Param("deployEnv") String deployEnv);

    @Update({
            "<script>"
            , "update activity_template_info set "
            , "activity_duration_limit=#{activityDurationLimit}, activity_start_time_limit=#{activityStartTimeLimit}, activity_end_time_limit=#{activityEndTimeLimit} where id=#{id}"
            , "</script>"
    })
    int updateTimeLimit(@Param("id")Long id
            , @Param("activityDurationLimit") Integer activityDurationLimit
            , @Param("activityStartTimeLimit") Date activityStartTimeLimit
            , @Param("activityEndTimeLimit") Date activityEndTimeLimit
    );

    @Update({
            "<script>"
            , "update activity_template_info set "
            , "up_start_time=#{upStartTime}, up_end_time=#{upEndTime} where id=#{id}"
            , "</script>"
    })
    int updateUpTimeLimit(@Param("id")Long id
            , @Param("upStartTime") Date upStartTime
            , @Param("upEndTime") Date upEndTime
    );
}
