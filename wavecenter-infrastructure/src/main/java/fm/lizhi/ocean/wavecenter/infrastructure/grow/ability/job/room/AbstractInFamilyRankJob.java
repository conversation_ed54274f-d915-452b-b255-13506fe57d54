package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.job.room;


import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.SettlePeriodDTO;
import fm.lizhi.ocean.wavecenter.service.grow.ability.handler.RoomInFamilyRankHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/11 14:15
 */
@Slf4j
public abstract class AbstractInFamilyRankJob {

    @Autowired
    private RoomInFamilyRankHandler roomInFamilyRankHandler;

    /**
     * 结算入口
     */
    protected void doSettle(InFamilyRankParam param){
        // 获取结算周期
        SettlePeriodDTO settlePeriod = getSettlePeriod(param);
        log.info("settlePeriod={}", settlePeriod);

        Long familyId = settleFamilyId(param);
        if (familyId != null) {
            roomInFamilyRankHandler.settle(settlePeriod, familyId);
        } else {
            roomInFamilyRankHandler.settle(settlePeriod);
        }
    }

    /**
     * 获取结算周期
     * @param param
     * @return
     */
    private SettlePeriodDTO getSettlePeriod(InFamilyRankParam param){
        // 优先获取参数
        if (param != null && param.getWeekStartDay() != null) {
            Date weekStart = MyDateUtil.getDayValueDate(param.getWeekStartDay());
            Date weekEnd = MyDateUtil.getLastWeekEndDayByStartDay(weekStart);
            return new SettlePeriodDTO(weekStart, weekEnd);
        }

        // 否则获取上一个自然周的周期
        Date lastWeekStartDay = MyDateUtil.getLastWeekStartDay();
        Date lastWeekEndDay = MyDateUtil.getLastWeekEndDay();
        return new SettlePeriodDTO(lastWeekStartDay, lastWeekEndDay);
    }

    /**
     * 查询指定结算名单
     * @param param
     * @return
     */
    private Long settleFamilyId(InFamilyRankParam param){
        // 优先获取参数
        if (param != null && param.getFamilyId() != null) {
            return param.getFamilyId();
        }
        return null;
    }

}
