package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.*;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityTemplateStatusEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseGetGeneralActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateDetailFlowResourceBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateDetailProcessBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateFlowResourceBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplatePageBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestCreateActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityTemplateShelfStatus;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseGetActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseGetActivityTemplateShelfStatus;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wavecenter.base.convert.CommonConvert;
import fm.lizhi.ocean.wavecenter.common.utils.JoinerUtils;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityTemplateHighlight;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityTemplateInfo;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

/**
 * 活动模板信息转换器
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        imports = {Date.class, UrlUtils.class, StringUtils.class, JoinerUtils.class, ConfigUtils.class, ActivityTemplateStatusEnum.class},
        uses = {CommonConvert.class}
)
public abstract class ActivityTemplateInfoConverter {

    @Autowired
    protected CommonConfig commonConfig;

    /**
     * 转换为创建活动模板信息的实体
     *
     * @param req 请求对象
     * @return 创建活动模板信息的实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "auxiliaryPropUrl", expression = "java(JoinerUtils.joinUrlRelativePath(req.getAuxiliaryPropUrls()))")
    @Mapping(target = "posterUrl", expression = "java(UrlUtils.removeHostOrEmpty(req.getPosterUrl()))")
    @Mapping(target = "activityTool", expression = "java(JoinerUtils.join(req.getActivityTools()))")
    @Mapping(target = "roomAnnouncement", expression = "java(StringUtils.defaultString(req.getRoomAnnouncement()))")
    @Mapping(target = "roomBackgroundIds", expression = "java(JoinerUtils.join(req.getRoomBackgroundIds()))")
    @Mapping(target = "avatarWidgetIds", expression = "java(JoinerUtils.join(req.getAvatarWidgetIds()))")
    @Mapping(target = "roomAnnouncementImage", expression = "java(JoinerUtils.joinUrlRelativePath(req.getRoomAnnouncementImages()))")
    @Mapping(target = "deleted", constant = "false")
    @Mapping(target = "cover", constant = "")
    @Mapping(target = "status", expression = "java(ActivityTemplateStatusEnum.OFF_SHELF.getStatus())")
    @Mapping(target = "weight", constant = "0")
    @Mapping(target = "hotRec", constant = "false")
    @Mapping(target = "hotWeight", constant = "0")
    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnv().name())")
    @Mapping(target = "createTime", expression = "java(new Date())")
    @Mapping(target = "modifyTime", expression = "java(new Date())")
    @Mapping(target = "upStartTime", ignore = true)
    @Mapping(target = "upEndTime", ignore = true)
    public abstract ActivityTemplateInfo toCreateActivityTemplateInfo(RequestCreateActivityTemplate req);

    /**
     * 转换为更新活动模板信息的实体
     *
     * @param req 请求对象
     * @return 更新活动模板信息的实体
     */
    @Mapping(target = "auxiliaryPropUrl", expression = "java(JoinerUtils.joinUrlRelativePath(req.getAuxiliaryPropUrls()))")
    @Mapping(target = "posterUrl", expression = "java(UrlUtils.removeHostOrEmpty(req.getPosterUrl()))")
    @Mapping(target = "activityTool", expression = "java(JoinerUtils.join(req.getActivityTools()))")
    @Mapping(target = "roomAnnouncement", expression = "java(StringUtils.defaultString(req.getRoomAnnouncement()))")
    @Mapping(target = "roomBackgroundIds", expression = "java(JoinerUtils.join(req.getRoomBackgroundIds()))")
    @Mapping(target = "avatarWidgetIds", expression = "java(JoinerUtils.join(req.getAvatarWidgetIds()))")
    @Mapping(target = "roomAnnouncementImage", expression = "java(JoinerUtils.joinUrlRelativePath(req.getRoomAnnouncementImages()))")
    @Mapping(target = "aiGen", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "cover", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "weight", ignore = true)
    @Mapping(target = "hotRec", ignore = true)
    @Mapping(target = "hotWeight", ignore = true)
    @Mapping(target = "deployEnv", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "modifyTime", expression = "java(new Date())")
    @Mapping(target = "upStartTime", ignore = true)
    @Mapping(target = "upEndTime", ignore = true)
    public abstract ActivityTemplateInfo toUpdateActivityTemplateInfo(RequestUpdateActivityTemplate req);

    /**
     * 转换为更新活动模板上下架状态的实体
     *
     * @param req 请求对象
     * @return 更新活动模板上下架状态的实体
     */
    @Mapping(target = "appId", ignore = true)
    @Mapping(target = "name", ignore = true)
    @Mapping(target = "classId", ignore = true)
    @Mapping(target = "goal", ignore = true)
    @Mapping(target = "introduction", ignore = true)
    @Mapping(target = "auxiliaryPropUrl", ignore = true)
    @Mapping(target = "posterUrl", ignore = true)
    @Mapping(target = "activityTool", ignore = true)
    @Mapping(target = "roomAnnouncement", ignore = true)
    @Mapping(target = "roomBackgroundIds", ignore = true)
    @Mapping(target = "avatarWidgetIds", ignore = true)
    @Mapping(target = "roomAnnouncementImage", ignore = true)
    @Mapping(target = "cover", expression = "java(UrlUtils.removeHostOrEmpty(req.getCover()))")
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "aiGen", ignore = true)
    @Mapping(target = "deployEnv", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "modifyTime", expression = "java(new Date())")
    @Mapping(target = "roomBackgroundLimit", ignore = true)
    @Mapping(target = "avatarWidgetLimit", ignore = true)
    @Mapping(target = "activityDurationLimit", ignore = true)
    @Mapping(target = "activityStartTimeLimit", ignore = true)
    @Mapping(target = "activityEndTimeLimit", ignore = true)
    public abstract ActivityTemplateInfo toUpdateActivityTemplateInfoShelfStatus(RequestUpdateActivityTemplateShelfStatus req);

    /**
     * 转换为获取活动模板上下架状态的响应
     *
     * @param template   活动模板信息
     * @param highlights 活动模板亮点标签列表
     * @return 获取活动模板上下架状态的响应
     */
    @Mapping(target = "cover", expression = "java(UrlUtils.addHostOrEmpty(template.getCover(),commonConfig.getRomeFsDownloadCdn()))")
    public abstract ResponseGetActivityTemplateShelfStatus toResponseGetActivityTemplateShelfStatus(ActivityTemplateInfo template, List<ActivityTemplateHighlight> highlights, List<Long> njWhiteList);

    /**
     * 转换为活动模板分页信息
     *
     * @param template     活动模板信息
     * @param className    类别名称
     * @param bigClassId   大类id
     * @param bigClassName 大类名称
     * @return 活动模板分页信息
     */
    @Mapping(target = "cover", expression = "java(UrlUtils.addHostOrEmpty(template.getCover(),commonConfig.getRomeFsDownloadCdn()))")
    @Mapping(target = "createTime", expression = "java(template.getCreateTime().getTime())")
    @Mapping(target = "modifyTime", expression = "java(template.getModifyTime().getTime())")
    public abstract ActivityTemplatePageBean toActivityTemplatePageBean(ActivityTemplateInfo template, String className, Long bigClassId, String bigClassName);

    /**
     * 转换为获取活动模板信息的响应
     *
     * @param template     活动模板信息
     * @param className    分类名称
     * @param bigClassId   大类id
     * @param bigClassName 大类名称
     * @param processes    活动模板流程列表
     * @param flowResources 流量资源列表
     * @return 获取活动模板信息的响应
     */
    @Mapping(target = "auxiliaryPropUrls", expression = "java(JoinerUtils.splitUrlAndAddHost(template.getAuxiliaryPropUrl(), commonConfig.getRomeFsDownloadCdn()))")
    @Mapping(target = "posterUrl", expression = "java(UrlUtils.addHostOrEmpty(template.getPosterUrl(), commonConfig.getRomeFsDownloadCdn()))")
    @Mapping(target = "activityTools", expression = "java(JoinerUtils.splitInteger(template.getActivityTool()))")
    @Mapping(target = "roomAnnouncementImages", expression = "java(JoinerUtils.splitUrlAndAddHost(template.getRoomAnnouncementImage(), commonConfig.getRomeFsDownloadCdn()))")
    @Mapping(target = "roomBackgroundIds", expression = "java(JoinerUtils.splitLong(template.getRoomBackgroundIds()))")
    @Mapping(target = "avatarWidgetIds", expression = "java(JoinerUtils.splitLong(template.getAvatarWidgetIds()))")
    @Mapping(target = "cover", expression = "java(UrlUtils.addHostOrEmpty(template.getCover(),commonConfig.getRomeFsDownloadCdn()))")
    @Mapping(target = "createTime", expression = "java(template.getCreateTime().getTime())")
    @Mapping(target = "modifyTime", expression = "java(template.getModifyTime().getTime())")
    public abstract ResponseGetActivityTemplate toResponseGetActivityTemplate(ActivityTemplateInfo template, String className, Long bigClassId, String bigClassName, List<ActivityTemplateDetailProcessBean> processes, List<ActivityTemplateDetailFlowResourceBean> flowResources, Integer bigClassType);

    /**
     * 转换为热门活动模板信息
     *
     * @param template     活动模板信息
     * @param usageCount 使用次数
     * @return 热门活动模板信息
     */
    @Mapping(target = "cover", expression = "java(UrlUtils.addHostOrEmpty(template.getCover(),commonConfig.getRomeFsDownloadCdn()))")
    public abstract ActivityTemplateHotPageBean toActivityTemplateHotPageBean(ActivityTemplateInfo template, String usageCount, List<ActivityTemplateFlowResourceBean> flowResources, List<ActivityTemplateHighlight> highlights);

    /**
     * 转换为普通活动模板信息
     *
     * @param template     活动模板信息
     * @param bigClassId 大类id
     * @return 普通活动模板信息
     */
    @Mapping(target = "cover", expression = "java(UrlUtils.addHostOrEmpty(template.getCover(),commonConfig.getRomeFsDownloadCdn()))")
    public abstract ActivityTemplateGeneralPageBean toActivityTemplateGeneralPageBean(ActivityTemplateInfo template, Long bigClassId, List<ActivityTemplateFlowResourceBean> flowResources, List<ActivityTemplateHighlight> highlights);

    /**
     * 转换为通用活动模板信息
     *
     * @param template     活动模板信息
     * @param bigClassId   大类id
     * @param bigClassName 大类名称
     * @param className    分类名称
     * @param level        等级
     * @param processes    活动模板流程列表
     * @param flowResources 流量资源列表
     * @param highlights   活动模板亮点标签列表
     * @return 通用活动模板信息
     */
    @Mapping(target = "auxiliaryPropUrls", expression = "java(JoinerUtils.splitUrlAndAddHost(template.getAuxiliaryPropUrl(), commonConfig.getRomeFsDownloadCdn()))")
    @Mapping(target = "posterUrl", expression = "java(UrlUtils.addHostOrEmpty(template.getPosterUrl(), commonConfig.getRomeFsDownloadCdn()))")
    @Mapping(target = "activityTools", expression = "java(JoinerUtils.splitInteger(template.getActivityTool()))")
    @Mapping(target = "roomAnnouncementImages", expression = "java(JoinerUtils.splitUrlAndAddHost(template.getRoomAnnouncementImage(), commonConfig.getRomeFsDownloadCdn()))")
    @Mapping(target = "roomBackgroundIds", expression = "java(JoinerUtils.splitLong(template.getRoomBackgroundIds()))")
    @Mapping(target = "avatarWidgetIds", expression = "java(JoinerUtils.splitLong(template.getAvatarWidgetIds()))")
    @Mapping(target = "cover", expression = "java(UrlUtils.addHostOrEmpty(template.getCover(),commonConfig.getRomeFsDownloadCdn()))")
    public abstract ResponseGetGeneralActivityTemplate toResponseGetGeneralActivityTemplate(ActivityTemplateInfo template, Long bigClassId, String bigClassName, String className, String level, List<ActivityTemplateGeneralProcessBean> processes, List<ActivityTemplateGeneralFlowResourceBean> flowResources, List<ActivityTemplateHighlight> highlights, Integer bigClassType, Long levelId);

    /**
     * 转换为活动模板信息bean
     *
     * @param template 活动模板信息
     * @return 活动模板信息bean
     */
    @Mapping(target = "auxiliaryPropUrls", expression = "java(JoinerUtils.splitUrlAndAddHost(template.getAuxiliaryPropUrl(), commonConfig.getRomeFsDownloadCdn()))")
    @Mapping(target = "posterUrl", expression = "java(UrlUtils.addHostOrEmpty(template.getPosterUrl(), commonConfig.getRomeFsDownloadCdn()))")
    @Mapping(target = "activityTools", expression = "java(JoinerUtils.splitInteger(template.getActivityTool()))")
    @Mapping(target = "roomAnnouncementImages", expression = "java(JoinerUtils.splitUrlAndAddHost(template.getRoomAnnouncementImage(), commonConfig.getRomeFsDownloadCdn()))")
    @Mapping(target = "roomBackgroundIds", expression = "java(JoinerUtils.splitLong(template.getRoomBackgroundIds()))")
    @Mapping(target = "avatarWidgetIds", expression = "java(JoinerUtils.splitLong(template.getAvatarWidgetIds()))")
    @Mapping(target = "cover", expression = "java(UrlUtils.addHostOrEmpty(template.getCover(),commonConfig.getRomeFsDownloadCdn()))")
    @Mapping(target = "createTime", expression = "java(template.getCreateTime().getTime())")
    @Mapping(target = "modifyTime", expression = "java(template.getModifyTime().getTime())")
    public abstract ActivityTemplateInfoBean toActivityTemplateInfoBean(ActivityTemplateInfo template);
}
