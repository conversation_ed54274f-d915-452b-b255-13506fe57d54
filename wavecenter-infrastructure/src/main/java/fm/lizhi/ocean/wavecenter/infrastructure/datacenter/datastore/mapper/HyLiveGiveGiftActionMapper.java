package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.common.datastore.mysql.constant.ParamContants;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.api.income.bean.GetRoomSignRoomParamBean;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.LiveGiveGiftActionPo;
import fm.lizhi.ocean.wavecenter.service.income.dto.RoomRecFlowSumDto;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

@DataStore(namespace = "mysql_heiye_lizhilivegift_r")
public interface HyLiveGiveGiftActionMapper {

    @Select({
            "<script>"
            , "select "
            , "p.id"
            ,"     , p.user_id"
            ,"     , p.nj_id"
            ,"     , p.gift_id"
            ,"     , p.total_litchi_amount"
            ,"     , p.gift_amount"
            ,"     , p.create_time"
            ,"     , p.modify_time"
            ,"     , p.source"
            ,"     , p.biz_id"
            ,"     , p.biz_info"
            ,"     , abs(p.value) value"
            ,"     , p.live_id"
            ,"     , p.rec_user_id"
            ,"     , p.target_gift_id"
            ,"     , p.is_give_coin"
            ,"     , p.give_coin_amount"
            ,"     , p.gift_type"
            ,"     , p.live_type"
            ,"     , p.reward"
            ,"     , p.prop_id"
            ,"     , p.channel"
            ,"     , p.extend_type"
            ,"     , p.app_id"
            ,"     , p.income_user_id"
            ,"     , p.unit_price"
            ,"     , p.sub_app_id"
            ,"     , p.new_user"
            , "from live_give_gift_action p"
            , "where  p.nj_id = #{paramBean.roomId}"
            ,"<if test='null != paramBean.sendUserId'>"
            , " and p.user_id = #{paramBean.sendUserId}"
            , "</if>"
            ,"<if test='null != paramBean.recUserId'>"
            , " and p.rec_user_id = #{paramBean.recUserId}"
            , "</if>"
            , "and p.create_time &gt;= #{paramBean.startDate}"
            , "and p.create_time &lt;= #{paramBean.endDate}"
            , "order by p.create_time desc"
            ,"</script>"
    })
    PageList<LiveGiveGiftActionPo> getRoomRecFlow(@Param("paramBean") GetRoomSignRoomParamBean paramBean
            , @Param(ParamContants.PAGE_NUMBER)int pageNumber
            , @Param(ParamContants.PAGE_SIZE)int pageSize);


    @Select({
            "<script>"
            , "select sum(p.total_litchi_amount) income, sum(abs(p.value)) charm"
            , "from live_give_gift_action p"
            , "where  p.nj_id = #{roomId}"
            , "<if test='null != sendUserId'>"
            , " and p.user_id = #{sendUserId}"
            , "</if>"
            , "<if test='null != recUserId'>"
            , " and p.rec_user_id = #{recUserId}"
            , "</if>"
            , "and p.create_time &gt;= #{startDate}"
            , "and p.create_time &lt;= #{endDate}"
            , "</script>"
    })
    RoomRecFlowSumDto getRoomRecFlowSum(GetRoomSignRoomParamBean paramBean);


    @Select({
            "<script>"
            , "select sum(abs(a.value))"
            , "from live_give_gift_action a"
            , "where a.nj_id IN "
            , "<foreach collection='roomIds' item='rId' open='(' separator=',' close=')'>"
            , "#{rId}"
            , "</foreach>"
            , "  and a.rec_user_id = #{playerId}"
            , "  and a.create_time &gt;= #{startDate}"
            , "  and a.create_time &lt;= #{endDate}"
            , "</script>"
    })
    Integer getRoomIncomeCharm(List<Long> roomIds, long playerId, Date startDate, Date endDate);

    /**
     * 查询个播魅力值
     * live_type=4=个播类型
     *
     * @param playerId
     * @param startDate
     * @param endDate
     * @return
     */
    @Select({
            "<script>"
            , "select"
            , "a.id"
            ,"     , a.user_id"
            ,"     , a.nj_id"
            ,"     , a.gift_id"
            ,"     , a.total_litchi_amount"
            ,"     , a.gift_amount"
            ,"     , a.create_time"
            ,"     , a.modify_time"
            ,"     , a.source"
            ,"     , a.biz_id"
            ,"     , a.biz_info"
            ,"     , abs(a.value) value"
            ,"     , a.live_id"
            ,"     , a.rec_user_id"
            ,"     , a.target_gift_id"
            ,"     , a.is_give_coin"
            ,"     , a.give_coin_amount"
            ,"     , a.gift_type"
            ,"     , a.live_type"
            ,"     , a.reward"
            ,"     , a.prop_id"
            ,"     , a.channel"
            ,"     , a.extend_type"
            ,"     , a.app_id"
            ,"     , a.income_user_id"
            ,"     , a.unit_price"
            ,"     , a.sub_app_id"
            ,"     , a.new_user"
            , "from live_give_gift_action a"
            , "where a.nj_id = #{playerId}"
            , "  and a.create_time &gt;= #{startDate}"
            , "  and a.create_time &lt;= #{endDate}"
            , "</script>"
    })
    List<LiveGiveGiftActionPo> getPersonIncomeCharmList(long playerId, Date startDate, Date endDate);


    @Select({
            "<script>"
            , "select "
            , "p.id"
            ,"     , p.user_id"
            ,"     , p.nj_id"
            ,"     , p.gift_id"
            ,"     , p.total_litchi_amount"
            ,"     , p.gift_amount"
            ,"     , p.create_time"
            ,"     , p.modify_time"
            ,"     , p.source"
            ,"     , p.biz_id"
            ,"     , p.biz_info"
            ,"     , abs(p.value) value"
            ,"     , p.live_id"
            ,"     , p.rec_user_id"
            ,"     , p.target_gift_id"
            ,"     , p.is_give_coin"
            ,"     , p.give_coin_amount"
            ,"     , p.gift_type"
            ,"     , p.live_type"
            ,"     , p.reward"
            ,"     , p.prop_id"
            ,"     , p.channel"
            ,"     , p.extend_type"
            ,"     , p.app_id"
            ,"     , p.income_user_id"
            ,"     , p.unit_price"
            ,"     , p.sub_app_id"
            ,"     , p.new_user"
            , "from live_give_gift_action p"
            , "where 1=1"
            , "and p.nj_id = #{recRoomId}"
            , "and p.create_time &gt;= #{startDate}"
            , "and p.create_time &lt;= #{endDate}"
            , "<if test='null != recUserId'>"
            , " and p.rec_user_id = #{recUserId}"
            , "</if>"
            , "<if test='null != sendUserId'>"
            , " and p.user_id = #{sendUserId}"
            , "</if>"
            , "order by p.create_time desc"
            , "</script>"
    })
    PageList<LiveGiveGiftActionPo> getRoomGiftflow(@Param("recRoomId") long recRoomId
            , @Param("startDate") Date startDate
            , @Param("endDate") Date endDate
            , @Param("recUserId") Long recUserId
            , @Param("sendUserId") Long sendUserId
            , @Param(ParamContants.PAGE_NUMBER) int pageNumber
            , @Param(ParamContants.PAGE_SIZE) int pageSize);

    @Select({
            "<script>"
            , "select sum(abs(p.value)) value, sum(p.total_litchi_amount) totalLitchiAmount"
            , "from live_give_gift_action p"
            , "where 1=1"
            , "and p.nj_id = #{recRoomId}"
            , "and p.create_time &gt;= #{startDate}"
            , "and p.create_time &lt;= #{endDate}"
            , "<if test='null != recUserId'>"
            , " and p.rec_user_id = #{recUserId}"
            , "</if>"
            , "<if test='null != sendUserId'>"
            , " and p.user_id = #{sendUserId}"
            , "</if>"
            , "</script>"
    })
    LiveGiveGiftActionPo getRoomGiftflowSum(@Param("recRoomId") long recRoomId
            , @Param("startDate") Date startDate
            , @Param("endDate") Date endDate
            , @Param("recUserId") Long recUserId
            , @Param("sendUserId") Long sendUserId);


    @Select({
            "<script>"
            , "select sum(abs(p.value)) value , sum(p.total_litchi_amount) totalLitchiAmount "
            , "from live_give_gift_action p"
            , "where 1=1"
            , "and p.rec_user_id = #{recUserId}"
            , "and p.create_time &gt;= #{startDate}"
            , "and p.create_time &lt;= #{endDate}"
            , "<if test='null != recRoomId'>"
            , " and p.nj_id = #{recRoomId}"
            , "</if>"
            , "<if test='null != sendUserId'>"
            , " and p.user_id = #{sendUserId}"
            , "</if>"
            , "</script>"
    })
    LiveGiveGiftActionPo getPersonalGiftflowSum(@Param("recUserId") long recUserId
            , @Param("startDate") Date startDate
            , @Param("endDate") Date endDate
            , @Param("recRoomId") Long recRoomId
            , @Param("sendUserId") Long sendUserId);


    @Select({
            "<script>"
            , "select "
            , "p.id"
            ,"     , p.user_id"
            ,"     , p.nj_id"
            ,"     , p.gift_id"
            ,"     , p.total_litchi_amount"
            ,"     , p.gift_amount"
            ,"     , p.create_time"
            ,"     , p.modify_time"
            ,"     , p.source"
            ,"     , p.biz_id"
            ,"     , p.biz_info"
            ,"     , abs(p.value) value"
            ,"     , p.live_id"
            ,"     , p.rec_user_id"
            ,"     , p.target_gift_id"
            ,"     , p.is_give_coin"
            ,"     , p.give_coin_amount"
            ,"     , p.gift_type"
            ,"     , p.live_type"
            ,"     , p.reward"
            ,"     , p.prop_id"
            ,"     , p.channel"
            ,"     , p.extend_type"
            ,"     , p.app_id"
            ,"     , p.income_user_id"
            ,"     , p.unit_price"
            ,"     , p.sub_app_id"
            ,"     , p.new_user"
            , "from live_give_gift_action p"
            , "where 1=1"
            , "and p.rec_user_id = #{recUserId}"
            , "and p.create_time &gt;= #{startDate}"
            , "and p.create_time &lt;= #{endDate}"
            , "<if test='null != recRoomId'>"
            , " and p.nj_id = #{recRoomId}"
            , "</if>"
            , "<if test='null != sendUserId'>"
            , " and p.user_id = #{sendUserId}"
            , "</if>"
            , "order by p.create_time desc"
            , "</script>"
    })
    PageList<LiveGiveGiftActionPo> getPersonalGiftflow(@Param("recUserId") long recUserId
            , @Param("startDate") Date startDate
            , @Param("endDate") Date endDate
            , @Param("recRoomId") Long recRoomId
            , @Param("sendUserId") Long sendUserId
            , @Param(ParamContants.PAGE_NUMBER) int pageNumber
            , @Param(ParamContants.PAGE_SIZE) int pageSize);
}
