package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityNoticeCategoryRelation;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityNoticeConfig;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityNoticeConfigDTO;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActivityNoticeConfigConvert {
    ActivityNoticeConfigConvert I = Mappers.getMapper(ActivityNoticeConfigConvert.class);

    @Mappings({
            @Mapping(target = "deployEnv", expression = "java(com.lizhi.commons.config.core.util.ConfigUtils.getEnvRequired().name())"),
            @Mapping(target = "modifyTime", expression = "java(new java.util.Date())"),
            @Mapping(target = "createTime", expression = "java(new java.util.Date())"),
    })
    ActivityNoticeConfig activityNoticeConfigDTO2ActivityNoticeConfig(ActivityNoticeConfigDTO activityNoticeConfigDTO);

    @Mapping(target = "noticeId", source = "config.id")
    @Mapping(target = "categoryValue", source = "category")
    @Mapping(target = "deployEnv", expression = "java(com.lizhi.commons.config.core.util.ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "id", ignore = true)
    ActivityNoticeCategoryRelation buildActivityNoticeCategoryRelation(ActivityNoticeConfig config, Integer category);

    List<ActivityNoticeConfigDTO> activityNoticeConfig2ActivityNoticeConfigDTOs(List<ActivityNoticeConfig> configList);

    @Mapping(target = "categoryList", ignore = true)
    ActivityNoticeConfigDTO activityNoticeConfig2ActivityNoticeConfigDTO(ActivityNoticeConfig config);

    ActivityNoticeConfigDTO buildActivityNoticeConfigDTO(ActivityNoticeConfig config, List<Integer> categoryList);
}
