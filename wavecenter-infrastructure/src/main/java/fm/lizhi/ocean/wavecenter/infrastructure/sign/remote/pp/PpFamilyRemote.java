package fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.pp;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.mapper.ext.PpFamilyExtMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.IFamilyRemote;
import fm.pp.family.api.SocietyService;
import fm.pp.family.protocol.SocietyServiceProto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/3/19 16:18
 */
@Slf4j
@Component
public class PpFamilyRemote implements IFamilyRemote {

    @Autowired
    private PpFamilyExtMapper familyExtMapper;
    @Autowired
    private SocietyService societyService;

    @Override
    public List<Long> getFamilyIdsByPage(Long lastFamilyId, Integer pageSize) {
        return familyExtMapper.getFamilyIdsByPage(lastFamilyId, pageSize);
    }

    @Override
    public Optional<String> getSocietyCode(Long familyId) {
        Result<SocietyServiceProto.ResponseFamilySocietyByFamilyId> result = societyService.familySocietyByFamilyId(familyId);
        if (RpcResult.isFail(result)) {
            log.error("societyService.familySocietyByFamilyId error, rCode={}, familyId={}", result.rCode(), familyId);
            return Optional.empty();
        }
        SocietyServiceProto.ResponseFamilySocietyByFamilyId res = result.target();
        return Optional.of(StringUtils.isNotBlank(res.getSocietyCode()) ? res.getSocietyCode() : String.valueOf(res.getFamilyId()));
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }
}
