package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityTemplateGeneralProcessBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateDetailProcessBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateProcessBean;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityTemplateProcess;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 活动模板流程转换器
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface ActivityTemplateProcessConverter {

    /**
     * 转换为创建活动模板流程的实体列表
     *
     * @param beans      请求对象列表
     * @param templateId 活动模板ID
     * @return 创建活动模板流程的实体列表
     */
    default List<ActivityTemplateProcess> toCreateActivityTemplateProcesses(List<ActivityTemplateProcessBean> beans, long templateId) {
        if (CollectionUtils.isEmpty(beans)) {
            return Collections.emptyList();
        }
        ArrayList<ActivityTemplateProcess> activityTemplateProcesses = new ArrayList<>(beans.size());
        for (int index = 0; index < beans.size(); index++) {
            activityTemplateProcesses.add(toCreateActivityTemplateProcess(beans.get(index), templateId, index));
        }
        return activityTemplateProcesses;
    }

    @Mapping(target = "id", ignore = true)
    ActivityTemplateProcess toCreateActivityTemplateProcess(ActivityTemplateProcessBean bean, long templateId, int index);

    /**
     * 转换为API接口返回的活动模板流程的Bean列表
     *
     * @param entities 实体列表
     * @return API接口返回的活动模板流程的Bean列表
     */
    List<ActivityTemplateProcessBean> toActivityTemplateProcessBeans(List<ActivityTemplateProcess> entities);

    /**
     * 转换为API接口返回的活动模板流程的Bean列表
     *
     * @param entities 实体列表
     * @return API接口返回的活动模板流程的Bean列表
     */
    List<ActivityTemplateDetailProcessBean> toActivityTemplateDetailProcessBeans(List<ActivityTemplateProcess> entities);

    /**
     * 转换为API接口返回的通用活动模板流程的Bean列表
     *
     * @param entities 实体列表
     * @return API接口返回的通用活动模板流程的Bean列表
     */
    List<ActivityTemplateGeneralProcessBean> toActivityTemplateGeneralProcessBeans(List<ActivityTemplateProcess> entities);
}
