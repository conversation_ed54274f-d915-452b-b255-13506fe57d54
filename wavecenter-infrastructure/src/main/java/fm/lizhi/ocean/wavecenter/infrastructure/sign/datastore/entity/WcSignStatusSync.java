package fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 签约状态同步表
 *
 * @date 2024-10-28 03:40:27
 */
@Table(name = "`wavecenter_sign_status_sync`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcSignStatusSync {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 业务 ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 签约ID,合同ID或者签约记录ID
     */
    @Column(name= "`contract_id`")
    private Long contractId;

    /**
     * 确认状态,WAIT_CREATE_SIGN=待发起人签署,WAIT_TARGET_SIGN=待接受方签署,CONFIRM=已确认
     */
    @Column(name= "`confirm_status`")
    private String confirmStatus;

    /**
     * 类型：SIGN签约，CANCEL解约
     */
    @Column(name= "`type`")
    private String type;

    /**
     * 发起人角色：ROOM,FAMILY,PLAYER,USER
     */
    @Column(name= "`create_role`")
    private String createRole;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", contractId=").append(contractId);
        sb.append(", confirmStatus=").append(confirmStatus);
        sb.append(", type=").append(type);
        sb.append(", createRole=").append(createRole);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}