package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.manager;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.CountDataBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.PlayerPerformanceBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomAssessmentInfoBean;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.*;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.mapper.*;
import fm.lizhi.ocean.wavecenter.infrastructure.common.MetricsUtil;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.constants.MetricsConstants;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.constants.MetricsMeta;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.convert.DataCenterInfraConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.convert.RoomDataConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.dao.WcDataRoomDao;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.*;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.ext.WcDataPayRoomDayExtMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.PlayerSignPerformancePo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.WcDataRoomDayStatPo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.WcDataRoomWeekStatPo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.WcDataRoomMonthStatPo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.WcDataPayRoomDayIncomeStatPo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.convert.GuildDataConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.constants.StatPeriodConstants;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote.ICharmStatRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote.IRoomDataRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.live.manager.CheckInRedisManagerImpl;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.*;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.RoomDataManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/18 15:59
 */
@Component
@Slf4j
public class RoomDataManagerImpl implements RoomDataManager {

    @Autowired
    private ICharmStatRemote iCharmStatRemote;
    @Autowired
    private WcDataRoomFamilyDayMapper wcDataRoomFamilyDayMapper;
    @Autowired
    private WcDataRoomDayMapper roomDayMapper;
    @Autowired
    private WcDataRoomFamilyWeekMapper wcDataRoomFamilyWeekMapper;
    @Autowired
    private WcDataRoomWeekMapper roomWeekMapper;
    @Autowired
    private WcDataRoomFamilyMonthMapper wcDataRoomFamilyMonthMapper;
    @Autowired
    private WcDataRoomMonthMapper roomMonthMapper;
    @Autowired
    private IRoomDataRemote iRoomDataRemote;
    @Autowired
    private WcDataRoomFamilyDayExtMapper roomFamilyDayExtMapper;
    @Autowired
    private WcDataRoomFamilyWeekExtMapper roomFamilyWeekExtMapper;
    @Autowired
    private WcDataRoomFamilyMonthExtMapper roomFamilyMonthExtMapper;
    @Autowired
    private WcDataPayRoomDayExtMapper wcDataPayRoomDayExtMapper;
    @Autowired
    private CheckInRedisManagerImpl checkInRedisManager;

    @Autowired
    private WcDataRoomDao wcDataRoomDao;

    @Override
    public Optional<RoomAssessmentInfoBean> getAssessmentInfo(int appId, long familyId, long roomId) {
        RoomAssessmentInfoBean roomAssessmentInfoBean = iRoomDataRemote.queryAssessment(familyId, roomId);
        roomAssessmentInfoBean.setFlushTime(String.valueOf(System.currentTimeMillis()));
        return Optional.of(roomAssessmentInfoBean);
    }

    @Override
    public PageBean<PlayerPerformanceBean> getPlayerPerformance(GetRoomPlayerPerformanceParamDto paramDto) {
        PageList<PlayerSignPerformancePo> playerSignPerformancePos = iRoomDataRemote.selectPlayerSignPerformance(paramDto);
        return PageBean.of(playerSignPerformancePos.getTotal(), DataCenterInfraConvert.I.playerSignPerformancePos2Beans(playerSignPerformancePos));
    }

    @Override
    public List<PlayerPerformanceBean> getPlayerPerformanceList(int appId, long familyId, long roomId, List<Long> playerId, AssessTimeDto assessTimeDto) {
        List<PlayerSignCharmSumPo> list = iCharmStatRemote.selectPlayerSignCharmSumByUsers(roomId, familyId, playerId, assessTimeDto.getStartDate(), assessTimeDto.getEndDate());
        return DataCenterInfraConvert.I.playerSignCharmSumPos2Beans(list);
    }

    @Override
    public Map<String, String> getRoomDayKeyIndicators(int appId, Long familyId, long roomId, Date date, List<String> valueMetrics) {
        if (familyId != null) {
            WcDataRoomFamilyDay param = new WcDataRoomFamilyDay();
            param.setAppId(appId);
            param.setRoomId(roomId);
            param.setFamilyId(familyId);
            param.setStatDateValue(Integer.valueOf(DateUtil.formatDateToString(date, DateUtil.date)));

            log.info("getRoomDayKeyIndicators selectMany. param={}", JsonUtil.dumps(param));
            List<WcDataRoomFamilyDay> wcDataRoomDays = wcDataRoomFamilyDayMapper.selectMany(param);
            Map<String, String> baseValueMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(wcDataRoomDays)) {
                WcDataRoomFamilyDay wcDataRoomDay = wcDataRoomDays.get(0);
                baseValueMap = MetricsUtil.convertToMap(WcDataRoomFamilyDay.class, wcDataRoomDay);
            }

            return MetricsUtil.convertToResultMap(valueMetrics, baseValueMap);
        }

        WcDataRoomDay param = new WcDataRoomDay();
        param.setAppId(appId);
        param.setRoomId(roomId);
        param.setStatDateValue(Integer.valueOf(DateUtil.formatDateToString(date, DateUtil.date)));

        log.info("roomDayMapper selectMany. param={}", JsonUtil.dumps(param));
        List<WcDataRoomDay> wcDataRoomDays = roomDayMapper.selectMany(param);
        Map<String, String> baseValueMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(wcDataRoomDays)) {
            WcDataRoomDay wcDataRoomDay = wcDataRoomDays.get(0);
            baseValueMap = MetricsUtil.convertToMap(WcDataRoomDay.class, wcDataRoomDay);
        }
        return MetricsUtil.convertToResultMap(valueMetrics, baseValueMap);
    }

    @Override
    public Map<String, String> getRoomDayKeyIndicatorsSum(int appId, Long familyId, List<Long> roomIds, Date date, List<String> valueMetrics) {
        //指标持久化字段名称
        List<String> poMetricsNames = new ArrayList<>();
        for (String valueMetric : valueMetrics) {
            Optional<MetricsMeta> metricsMeta = MetricsConstants.getMetricsMeta(valueMetric);
            metricsMeta.ifPresent(meta -> poMetricsNames.add(meta.getPoName()));
        }
        if (CollectionUtils.isEmpty(poMetricsNames)) {
            return Collections.emptyMap();
        }
        LogContext.addResLog("poMetricsNames={}", JsonUtil.dumps(poMetricsNames));
        List<WcDataRoomFamilyDay> sumList = roomFamilyDayExtMapper.sum(poMetricsNames, appId, familyId, roomIds, MyDateUtil.getDateDayValue(date));
        if (CollectionUtils.isEmpty(sumList)) {
            return Collections.emptyMap();
        }
        WcDataRoomFamilyDay sum = sumList.get(0);
        LogContext.addResLog("sum={}", JsonUtil.dumps(sum));
        Map<String, String> baseValueMap = MetricsUtil.convertToMap(WcDataRoomFamilyDay.class, sum);
        return MetricsUtil.convertToResultMap(valueMetrics, baseValueMap);
    }

    @Override
    public List<DataRoomFamilyDayDTO> getRoomDayKeyIndicatorsSum(Long familyId, List<Long> roomIds, List<Integer> dayValues, List<String> valueMetrics) {
        //指标持久化字段名称
        List<String> poMetricsNames = new ArrayList<>();
        for (String valueMetric : valueMetrics) {
            Optional<MetricsMeta> metricsMeta = MetricsConstants.getMetricsMeta(valueMetric);
            metricsMeta.ifPresent(meta -> poMetricsNames.add(meta.getPoName()));
        }
        if (CollectionUtils.isEmpty(poMetricsNames)) {
            return Collections.emptyList();
        }
        LogContext.addResLog("poMetricsNames={}", JsonUtil.dumps(poMetricsNames));

        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        List<WcDataRoomFamilyDay> poList = roomFamilyDayExtMapper.sumDays(poMetricsNames, appId, familyId, roomIds, dayValues);

        return DataCenterInfraConvert.I.roomFamilyDay2DTO(poList);
    }

    @Override
    public Map<String, String> getRoomWeekKeyIndicators(int appId, Long familyId, long roomId, Date startDate, Date endDate, List<String> valueMetrics) {
        if (familyId != null) {
            WcDataRoomFamilyWeek param = new WcDataRoomFamilyWeek();
            param.setAppId(appId);
            param.setRoomId(roomId);
            param.setFamilyId(familyId);
            param.setStartWeekDate(startDate);
            param.setEndWeekDate(endDate);

            log.info("getRoomWeekKeyIndicators selectMany. param={}", JsonUtil.dumps(param));
            List<WcDataRoomFamilyWeek> list = wcDataRoomFamilyWeekMapper.selectMany(param);
            Map<String, String> baseValueMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(list)) {
                WcDataRoomFamilyWeek wcDataRoomWeek = list.get(0);
                baseValueMap = MetricsUtil.convertToMap(WcDataRoomFamilyWeek.class, wcDataRoomWeek);
            }

            return MetricsUtil.convertToResultMap(valueMetrics, baseValueMap);
        }

        WcDataRoomWeek param = new WcDataRoomWeek();
        param.setAppId(appId);
        param.setRoomId(roomId);
        param.setStartWeekDate(startDate);
        param.setEndWeekDate(endDate);

        log.info("roomWeekMapper selectMany. param={}", JsonUtil.dumps(param));
        List<WcDataRoomWeek> list = roomWeekMapper.selectMany(param);
        Map<String, String> baseValueMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(list)) {
            WcDataRoomWeek wcDataRoomWeek = list.get(0);
            baseValueMap = MetricsUtil.convertToMap(WcDataRoomWeek.class, wcDataRoomWeek);
        }

        return MetricsUtil.convertToResultMap(valueMetrics, baseValueMap);

    }

    @Override
    public Map<String, String> getRoomWeekKeyIndicatorsSum(Long familyId, List<Long> roomIds, Date startDate, Date endDate, List<String> valueMetrics) {
        //指标持久化字段名称
        List<String> poMetricsNames = new ArrayList<>();
        for (String valueMetric : valueMetrics) {
            Optional<MetricsMeta> metricsMeta = MetricsConstants.getMetricsMeta(valueMetric);
            metricsMeta.ifPresent(meta -> poMetricsNames.add(meta.getPoName()));
        }
        if (CollectionUtils.isEmpty(poMetricsNames)) {
            return Collections.emptyMap();
        }
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();

        LogContext.addResLog("poMetricsNames={}", JsonUtil.dumps(poMetricsNames));
        List<WcDataRoomFamilyWeek> sumList = roomFamilyWeekExtMapper.sum(poMetricsNames
                , appId
                , familyId, roomIds
                , DateUtil.formatDateToString(startDate, DateUtil.date_2)
                , DateUtil.formatDateToString(endDate, DateUtil.date_2)
        );
        if (CollectionUtils.isEmpty(sumList)) {
            return Collections.emptyMap();
        }
        WcDataRoomFamilyWeek sum = sumList.get(0);
        LogContext.addResLog("sum={}", JsonUtil.dumps(sum));
        Map<String, String> baseValueMap = MetricsUtil.convertToMap(WcDataRoomFamilyWeek.class, sum);
        return MetricsUtil.convertToResultMap(valueMetrics, baseValueMap);
    }

    @Override
    public Map<String, String> getRoomMonthKeyIndicatorsSum(Long familyId, List<Long> roomIds, Date monthDate, List<String> valueMetrics) {
        //指标持久化字段名称
        List<String> poMetricsNames = new ArrayList<>();
        for (String valueMetric : valueMetrics) {
            Optional<MetricsMeta> metricsMeta = MetricsConstants.getMetricsMeta(valueMetric);
            metricsMeta.ifPresent(meta -> poMetricsNames.add(meta.getPoName()));
        }
        if (CollectionUtils.isEmpty(poMetricsNames)) {
            return Collections.emptyMap();
        }
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();

        LogContext.addResLog("poMetricsNames={}", JsonUtil.dumps(poMetricsNames));
        List<WcDataRoomFamilyMonth> sumList = roomFamilyMonthExtMapper.sum(poMetricsNames
                , appId
                , familyId, roomIds
                , MyDateUtil.getDateMonthValue(monthDate)
        );
        if (CollectionUtils.isEmpty(sumList)) {
            return Collections.emptyMap();
        }
        WcDataRoomFamilyMonth sum = sumList.get(0);
        LogContext.addResLog("sum={}", JsonUtil.dumps(sum));
        Map<String, String> baseValueMap = MetricsUtil.convertToMap(WcDataRoomFamilyMonth.class, sum);
        return MetricsUtil.convertToResultMap(valueMetrics, baseValueMap);
    }

    @Override
    public Map<String, String> getRoomMonthKeyIndicators(int appId, Long familyId, long roomId, Date monthDate, List<String> valueMetrics) {
        if (familyId != null) {
            WcDataRoomFamilyMonth param = new WcDataRoomFamilyMonth();
            param.setAppId(appId);
            param.setRoomId(roomId);
            param.setFamilyId(familyId);
            param.setStatMonth(Integer.valueOf(DateUtil.formatDateToString(monthDate, "yyyyMM")));

            log.info("getRoomMonthKeyIndicators selectMany. param={}", JsonUtil.dumps(param));
            List<WcDataRoomFamilyMonth> list = wcDataRoomFamilyMonthMapper.selectMany(param);
            Map<String, String> baseValueMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(list)) {
                WcDataRoomFamilyMonth wcDataRoomMonth = list.get(0);
                baseValueMap = MetricsUtil.convertToMap(WcDataRoomFamilyMonth.class, wcDataRoomMonth);
            }

            return MetricsUtil.convertToResultMap(valueMetrics, baseValueMap);
        }

        WcDataRoomMonth param = new WcDataRoomMonth();
        param.setAppId(appId);
        param.setRoomId(roomId);
        param.setStatMonth(Integer.valueOf(DateUtil.formatDateToString(monthDate, "yyyyMM")));

        log.info("roomMonthMapper selectMany. param={}", JsonUtil.dumps(param));
        List<WcDataRoomMonth> list = roomMonthMapper.selectMany(param);
        Map<String, String> baseValueMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(list)) {
            WcDataRoomMonth wcDataRoomMonth = list.get(0);
            baseValueMap = MetricsUtil.convertToMap(WcDataRoomMonth.class, wcDataRoomMonth);
        }
        return MetricsUtil.convertToResultMap(valueMetrics, baseValueMap);
    }

    @Override
    public List<CountDataBean> getIndicatorTrend(Long familyId, long roomId, String metric, int days) {
        List<Integer> dates = MyDateUtil.getDateDayValueList(days);

        Map<Integer, Object> valueMap = new HashMap<>();

        if (familyId != null) {
            WcDataRoomFamilyDayExample example = new WcDataRoomFamilyDayExample();
            example.createCriteria()
                    .andRoomIdEqualTo(roomId)
                    .andFamilyIdEqualTo(familyId)
                    .andAppIdEqualTo(ContextUtils.getBusinessEvnEnum().getAppId())
                    .andStatDateValueIn(dates);
            List<WcDataRoomFamilyDay> list = wcDataRoomFamilyDayMapper.selectByExample(example);
            valueMap = list.stream().collect(Collectors.toMap(WcDataRoomFamilyDay::getStatDateValue, v -> v, (k1, k2) -> k2));
        } else {
            WcDataRoomDayExample example = new WcDataRoomDayExample();
            example.createCriteria()
                    .andRoomIdEqualTo(roomId)
                    .andAppIdEqualTo(ContextUtils.getBusinessEvnEnum().getAppId())
                    .andStatDateValueIn(dates);
            List<WcDataRoomDay> list = roomDayMapper.selectByExample(example);
            valueMap = list.stream().collect(Collectors.toMap(WcDataRoomDay::getStatDateValue, v -> v, (k1, k2) -> k2));
        }

        List<CountDataBean> result = new ArrayList<>(days);
        for (Integer date : dates) {
            CountDataBean countDataBean = new CountDataBean();
            countDataBean.setDate(MyDateUtil.getDayValueDate(date));
            Object wcDataDay = valueMap.get(date);
            if (wcDataDay != null) {
                ReflectionUtils.doWithLocalFields(wcDataDay.getClass(), field -> {
                    String name = field.getName();
                    if (!name.equals(metric)) {
                        return;
                    }

                    Optional<MetricsMeta> metricsMeta = MetricsConstants.getMetricsMeta(name);
                    if (!metricsMeta.isPresent()) {
                        return;
                    }

                    ReflectionUtils.makeAccessible(field);
                    Object value = ReflectionUtils.getField(field, wcDataDay);

                    MetricsMeta.ValueFactory valueFactory = metricsMeta.get().getValueFactory();
                    String formatValue = valueFactory.calculateValue(metricsMeta.get(), name, String.valueOf(value));
                    countDataBean.setValue(formatValue);
                });
            }
            result.add(countDataBean);
        }

        return result;
    }

    @Override
    public List<DataRoomFamilyDayDTO> getRoomDayData(Integer appId, Long familyId, Date startDate, Date endDate) {
        return getRoomDayData(appId, familyId, null, startDate, endDate);
    }


    @Override
    public List<DataRoomFamilyDayDTO> getRoomDayData(Integer appId, Long familyId, Long roomId, Date startDate, Date endDate) {

        WcDataRoomFamilyDayExample example = new WcDataRoomFamilyDayExample();
        WcDataRoomFamilyDayExample.Criteria criteria = example.createCriteria()
                .andFamilyIdEqualTo(familyId)
                .andAppIdEqualTo(appId)
                .andStatDateValueBetween(MyDateUtil.getDateDayValue(startDate), MyDateUtil.getDateDayValue(endDate));

        if (roomId != null) {
            criteria.andRoomIdEqualTo(roomId);
        }

        List<WcDataRoomFamilyDay> roomFamilyDayList = wcDataRoomFamilyDayMapper.selectByExample(example);
        return DataCenterInfraConvert.I.roomFamilyDay2DTO(roomFamilyDayList);
    }

    @Override
    public List<DataRoomFamilyDayDTO> getRoomDayData(Long familyId, List<Long> roomIds, Date day) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        WcDataRoomFamilyDayExample example = new WcDataRoomFamilyDayExample();
        example.createCriteria()
                .andFamilyIdEqualTo(familyId)
                .andAppIdEqualTo(appId)
                .andRoomIdIn(roomIds)
                .andStatDateValueEqualTo(MyDateUtil.getDateDayValue(day));
        List<WcDataRoomFamilyDay> roomFamilyDayList = wcDataRoomFamilyDayMapper.selectByExample(example);
        return DataCenterInfraConvert.I.roomFamilyDay2DTO(roomFamilyDayList);
    }

    @Override
    public List<DataRoomFamilyWeekDTO> getRoomWeekData(Long familyId, List<Long> roomIds, Date startDay, Date endDay) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        WcDataRoomFamilyWeekExample example = new WcDataRoomFamilyWeekExample();
        example.createCriteria()
                .andFamilyIdEqualTo(familyId)
                .andAppIdEqualTo(appId)
                .andRoomIdIn(roomIds)
                .andStartWeekDateEqualTo(startDay)
                .andEndWeekDateEqualTo(endDay);
        List<WcDataRoomFamilyWeek> roomFamilyWeekList = wcDataRoomFamilyWeekMapper.selectByExample(example);
        return DataCenterInfraConvert.I.roomFamilyWeeks2DTOs(roomFamilyWeekList);
    }

    @Override
    public List<DataRoomFamilyMonthDTO> getRoomMonthData(Long familyId, List<Long> roomIds, Date month) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        WcDataRoomFamilyMonthExample example = new WcDataRoomFamilyMonthExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andFamilyIdEqualTo(familyId)
                .andRoomIdIn(roomIds)
                .andStatMonthEqualTo(MyDateUtil.getDateMonthValue(month));
        List<WcDataRoomFamilyMonth> poList = wcDataRoomFamilyMonthMapper.selectByExample(example);
        return DataCenterInfraConvert.I.roomFamilyMonths2DTOs(poList);
    }

    @Override
    public List<DataRoomFamilyDayDTO> getRoomFamilyDayList(GetRoomDayListParam param) {
        WcDataRoomFamilyDayExample example = new WcDataRoomFamilyDayExample();
        example.setOrderByClause("stat_date_value desc");
        WcDataRoomFamilyDayExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(ContextUtils.getBusinessEvnEnum().getAppId());
        if (param.getFamilyId() != null) {
            criteria.andFamilyIdEqualTo(param.getFamilyId());
        }
        if (param.getRoomId() != null) {
            criteria.andRoomIdEqualTo(param.getRoomId());
        }
        if (CollectionUtils.isNotEmpty(param.getRoomIds())) {
            criteria.andRoomIdIn(param.getRoomIds());
        }
        if (CollectionUtils.isNotEmpty(param.getDayValues())) {
            criteria.andStatDateValueIn(param.getDayValues());
        }
        if (param.getStartDay() != null && param.getEndDay() != null) {
            criteria.andStatDateValueBetween(MyDateUtil.getDateDayValue(param.getStartDay())
                    , MyDateUtil.getDateDayValue(param.getEndDay())
            );
        }

        List<WcDataRoomFamilyDay> poList = wcDataRoomFamilyDayMapper.selectByExample(example);
        return DataCenterInfraConvert.I.roomFamilyDay2DTO(poList);
    }

    @Override
    public List<DataRoomDayDTO> getRoomDayList(GetRoomDayListParam param) {
        WcDataRoomDayExample example = new WcDataRoomDayExample();
        example.setOrderByClause("stat_date_value desc");
        WcDataRoomDayExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(ContextUtils.getBusinessEvnEnum().getAppId());
        if (param.getRoomId() != null) {
            criteria.andRoomIdEqualTo(param.getRoomId());
        }
        if (CollectionUtils.isNotEmpty(param.getDayValues())) {
            criteria.andStatDateValueIn(param.getDayValues());
        }
        if (CollectionUtils.isNotEmpty(param.getRoomIds())) {
            criteria.andRoomIdIn(param.getRoomIds());
        }
        if (param.getStartDay() != null && param.getEndDay() != null) {
            criteria.andStatDateValueBetween(MyDateUtil.getDateDayValue(param.getStartDay())
                    , MyDateUtil.getDateDayValue(param.getEndDay())
            );
        }
        List<WcDataRoomDay> poList = roomDayMapper.selectByExample(example);
        return DataCenterInfraConvert.I.roomDays2DTOs(poList);
    }


    /**
     * 连续天数有总收入的PGC厅ID列表
     */
    @Override
    public List<Long> getHasIncomeRoomIdList(GetHasIncomeRoomsParam param) {
        Integer appId = param.getAppId();
        Integer startDayValue = MyDateUtil.getDateDayValue(param.getStartDay());
        Integer endDayValue = MyDateUtil.getDateDayValue(param.getEndDay());
        String deployEnv = ConfigUtils.getEnvRequired().name();
        List<Long> cache = checkInRedisManager.getHasIncomeRoomIdList(appId, startDayValue, endDayValue);
        if (cache != null) {
            return cache;
        }
        List<Long> hasIncomeRoomIds = wcDataPayRoomDayExtMapper.findHasIncomeRoomIds(appId, startDayValue, endDayValue, deployEnv);
        checkInRedisManager.setHasIncomeRoomIdList(appId, startDayValue, endDayValue, hasIncomeRoomIds);
        return hasIncomeRoomIds;
    }

    @Override
    public PageBean<RoomIncomeStatDTO> queryRoomIncomeStats(RoomIncomeStatParamDTO param) {
        QueryRoomIncomeResDTO resDTO = null;
        int total = 0;
        try {
            // 计算total值：查询最早记录距离当前时间的周期数
            // 根据统计周期查询数据
            switch (param.getStatPeriod().toLowerCase()) {
                case StatPeriodConstants.DAY:
                    resDTO = queryRoomDayStats(param);
                    break;
                case StatPeriodConstants.WEEK:
                    resDTO = queryRoomWeekStats(param);
                    break;
                case StatPeriodConstants.MONTH:
                    resDTO = queryRoomMonthStats(param);
                    break;
                default:
                    log.warn("Unsupported stat period: {}", param.getStatPeriod());
                    break;
            }
            return resDTO == null ? PageBean.empty() : PageBean.of(resDTO.getTotal(), resDTO.getResultList());
        } catch (Exception e) {
            log.error("Query room income overview stats failed", e);
            return PageBean.empty();
        }

    }

    /**
     * 查询厅日统计数据
     */
    private QueryRoomIncomeResDTO queryRoomDayStats(RoomIncomeStatParamDTO param) {
        List<RoomIncomeStatDTO> resultList = new ArrayList<>();
        //计算出是否需要从queryIncomeStatByDay查询数据
        Date lastMinTime = param.getLastMinTime() == null ? new Date() : new Date(param.getLastMinTime());
        Integer lastMinDayValue = MyDateUtil.getDateDayValue(lastMinTime);

        // 查询其他日期数据（从大数据表获取）
        List<WcDataRoomDayStatPo> dayStats = roomFamilyDayExtMapper.queryDayStatsByTime(param.getNjId(), param.getAppId(), param.getFamilyId(), lastMinDayValue, param.getPageSize());
        int total = (int) wcDataRoomDao.countRoomDay(param.getAppId(), param.getFamilyId(), param.getNjId());

        int lastMinDayValueBefore = MyDateUtil.getDateDayValue(DateUtil.getDayBefore(lastMinTime, 1));
        // 如果缺少日统计的首天数据，说明大数据还没有洗出来，从实时数据中获取
        boolean hasLastMinDayData = dayStats.stream().anyMatch(stat -> stat.getStatDateValue().equals(lastMinDayValueBefore));
        if (!hasLastMinDayData) {
            // 从实时表获取
            WcDataPayRoomDayIncomeStatPo firstDayStat = wcDataPayRoomDayExtMapper.queryRoomIncomeStatByDay(param.getNjId(), param.getAppId(), lastMinDayValueBefore);
            Date dayBefore = DateUtil.getDayBefore(lastMinTime, 1);
            IncomeSummaryDTO infoStat = GuildDataConvert.I.convertGuildInfoStat(firstDayStat);
            RoomIncomeStatDTO incomeStatDTO = RoomIncomeStatDTO.of(DateUtil.getDayStart(dayBefore).getTime(), DateUtil.getDayEnd(dayBefore).getTime(), infoStat);
            resultList.add(incomeStatDTO);
            total += infoStat == null ? 0 : 1;
        }

        for (WcDataRoomDayStatPo stat : dayStats) {
            IncomeSummaryDTO infoStat = RoomDataConvert.I.convertRoomDayInfoStat(stat);
            RoomIncomeStatDTO incomeStatDTO = RoomIncomeStatDTO.of(stat.getStatDate().getTime(), DateUtil.getDayEnd(stat.getStatDate()).getTime(), infoStat);
            resultList.add(incomeStatDTO);
        }

        // 按开始时间倒序排序
        resultList.sort(Comparator.comparing(RoomIncomeStatDTO::getStartTime).reversed());
        if (resultList.size() > param.getPageSize()) {
            resultList = resultList.subList(0, param.getPageSize());
        }
        return QueryRoomIncomeResDTO.of(total, resultList);
    }

    /**
     * 查询厅周统计数据
     */
    private QueryRoomIncomeResDTO queryRoomWeekStats(RoomIncomeStatParamDTO param) {
        List<RoomIncomeStatDTO> resultList = new ArrayList<>();
        Date lastMinTime = param.getLastMinTime() == null ? new Date() : new Date(param.getLastMinTime());
        Date lastWeekTime = param.getLastMinTime() == null ? MyDateUtil.getCurrentWeekStart() : new Date(param.getLastMinTime());
        Date lastDate = DateUtil.formatStrToDate(DateUtil.formatDateToString(lastWeekTime, DateUtil.date_2), DateUtil.date_2);


        List<WcDataRoomWeekStatPo> weekStats = roomFamilyWeekExtMapper.queryWeekStatsByTime(param.getNjId(), param.getAppId(), param.getFamilyId(), lastDate, param.getPageSize());
        int total = (int) wcDataRoomDao.countRoomWeek(param.getAppId(), param.getFamilyId(), param.getNjId());

        // 判断是否存在缺少周统计的首周数据
        Date firstWeekDate = MyDateUtil.getLastWeekStart(lastMinTime);
        boolean hasFirstWeekData = weekStats.stream().anyMatch(stat -> stat.getStartWeekDate().equals(firstWeekDate));
        if (!hasFirstWeekData) {
            //从实时表获取计算
            Date lastWeekEnd = MyDateUtil.getLastWeekEnd(lastWeekTime);
            WcDataPayRoomDayIncomeStatPo firstWeekStat = wcDataPayRoomDayExtMapper.queryRoomIncomeStatByWeek(param.getNjId(),
                    param.getAppId(), MyDateUtil.getDateDayValue(firstWeekDate), MyDateUtil.getDateDayValue(lastWeekEnd));
            IncomeSummaryDTO infoStat = GuildDataConvert.I.convertGuildInfoStat(firstWeekStat);
            RoomIncomeStatDTO incomeStatDTO = RoomIncomeStatDTO.of(firstWeekDate.getTime(), lastWeekEnd.getTime(), infoStat);
            resultList.add(incomeStatDTO);
            total += infoStat == null ? 0 : 1;
        }
        for (WcDataRoomWeekStatPo stat : weekStats) {
            IncomeSummaryDTO infoStat = RoomDataConvert.I.convertRoomWeekInfoStat(stat);
            RoomIncomeStatDTO incomeStatDTO = RoomIncomeStatDTO.of(stat.getStartWeekDate().getTime(), stat.getEndWeekDate().getTime(), infoStat);
            resultList.add(incomeStatDTO);
        }
        // 按周开始时间倒序排序
        resultList.sort(Comparator.comparing(RoomIncomeStatDTO::getStartTime).reversed());
        if (resultList.size() > param.getPageSize()) {
            resultList = resultList.subList(0, param.getPageSize());
        }
        return QueryRoomIncomeResDTO.of(total, resultList);
    }

    /**
     * 查询厅月统计数据
     */
    private QueryRoomIncomeResDTO queryRoomMonthStats(RoomIncomeStatParamDTO param) {
        List<RoomIncomeStatDTO> resultList = new ArrayList<>();

        Date lastMinTime = param.getLastMinTime() == null ? DateUtil.getMonthStart(new Date()) : new Date(param.getLastMinTime());
        int startMonth = MyDateUtil.getDateMonthValue(lastMinTime);

        List<WcDataRoomMonthStatPo> monthStats = roomFamilyMonthExtMapper.queryMonthStatsByTime(param.getNjId(), param.getAppId(), param.getFamilyId(), startMonth, param.getPageSize());
        int total = (int) wcDataRoomDao.countRoomMonth(param.getAppId(), param.getFamilyId(), param.getNjId());

        //判断是否存在缺少月统计的首月数据
        Date lastMonthDate = DateUtil.getMonthBefore(lastMinTime, 1);
        Integer firstMonth = MyDateUtil.getDateMonthValue(lastMonthDate);
        boolean hasFirstMonthData = monthStats.stream().anyMatch(stat -> stat.getStatMonth().equals(firstMonth));
        if (!hasFirstMonthData) {
            // 从实时表获取
            WcDataPayRoomDayIncomeStatPo firstMonthStat = wcDataPayRoomDayExtMapper.queryRoomIncomeStatByMonth(param.getNjId(), param.getAppId(), firstMonth, firstMonth);
            IncomeSummaryDTO infoStat = GuildDataConvert.I.convertGuildInfoStat(firstMonthStat);
            RoomIncomeStatDTO statDTO = RoomIncomeStatDTO.of(DateUtil.getMonthStart(lastMonthDate).getTime(),
                    DateUtil.getMonthEnd(lastMonthDate).getTime(), infoStat);
            resultList.add(statDTO);
            total += infoStat == null ? 0 : 1;
        }

        for (WcDataRoomMonthStatPo stat : monthStats) {
            Date monthDate = DateUtil.formatStrToDate(String.valueOf(stat.getStatMonth()), "yyyyMM");
            IncomeSummaryDTO infoStat = RoomDataConvert.I.convertRoomMonthInfoStat(stat);
            RoomIncomeStatDTO statDTO = RoomIncomeStatDTO.of(DateUtil.getMonthStart(monthDate).getTime(),
                    DateUtil.getMonthEnd(monthDate).getTime(), infoStat);
            resultList.add(statDTO);
        }

        // 按月开始时间倒序排序
        resultList.sort(Comparator.comparing(RoomIncomeStatDTO::getStartTime).reversed());
        if (resultList.size() > param.getPageSize()) {
            resultList = resultList.subList(0, param.getPageSize());
        }
        return QueryRoomIncomeResDTO.of(total, resultList);
    }
}
