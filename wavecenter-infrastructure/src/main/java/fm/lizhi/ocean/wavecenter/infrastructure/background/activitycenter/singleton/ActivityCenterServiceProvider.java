package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.singleton;

import fm.lizhi.common.dubbo.DubboClientBuilder;
import fm.lizhi.hy.amusement.api.DressUpInfoService;
import fm.lizhi.live.pp.idl.officialCertifiedTag.api.OfficialCertifiedTagIDLService;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.ServiceProviderConstants;
import fm.lizhi.pp.vip.api.PpDecorateService;
import fm.lizhi.xm.vip.services.DecorateService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/4/10 14:51
 */
@Configuration
public class ActivityCenterServiceProvider {

    @Bean
    public DecorateService xmDecorateService() {
        return new DubboClientBuilder<>(DecorateService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public PpDecorateService ppDecorateService() {
        return new DubboClientBuilder<>(PpDecorateService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }


    @Bean
    public DressUpInfoService dressUpInfoService() {
        return new DubboClientBuilder<>(DressUpInfoService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public OfficialCertifiedTagIDLService officialCertifiedTagIdlService() {
        return new DubboClientBuilder<>(OfficialCertifiedTagIDLService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }
}
