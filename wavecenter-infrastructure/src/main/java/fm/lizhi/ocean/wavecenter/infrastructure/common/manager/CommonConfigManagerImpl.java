package fm.lizhi.ocean.wavecenter.infrastructure.common.manager;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageConfigBean;
import fm.lizhi.ocean.wavecenter.api.common.bean.SaveConfigReqBean;
import fm.lizhi.ocean.wavecenter.infrastructure.common.convert.ConfigInfraConvert;
import fm.lizhi.ocean.wavecenter.datastore.platform.common.entity.WcUserPageConfig;
import fm.lizhi.ocean.wavecenter.datastore.platform.common.mapper.WcUserPageConfigMapper;
import fm.lizhi.ocean.wavecenter.service.common.manager.CommonConfigManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/20 14:41
 */
@Component
public class CommonConfigManagerImpl implements CommonConfigManager {

    @Autowired
    private WcUserPageConfigMapper wcUserPageConfigMapper;

    @Override
    public void savePageConfig(SaveConfigReqBean reqBean) {
        Integer appId = reqBean.getAppId();
        WcUserPageConfig entity = new WcUserPageConfig();
        entity.setAppId(appId);
        entity.setUserId(reqBean.getUserId());
        entity.setPageCode(reqBean.getPageCode());

        WcUserPageConfig wcUserPageConfig = wcUserPageConfigMapper.selectOne(entity);
        if (wcUserPageConfig == null) {
            entity.setCreateTime(new Date());
            entity.setModifyTime(new Date());
            entity.setConfig(reqBean.getConfig());
            wcUserPageConfigMapper.insert(entity);
        } else {
            wcUserPageConfig.setModifyTime(new Date());
            wcUserPageConfig.setConfig(reqBean.getConfig());
            wcUserPageConfigMapper.updateByPrimaryKey(wcUserPageConfig);
        }
    }

    @Override
    public List<PageConfigBean> getPageCode(int appId, long userId, String pageCode) {
        WcUserPageConfig param = new WcUserPageConfig();
        param.setAppId(appId);
        param.setUserId(userId);
        if (StringUtils.isNotBlank(pageCode)) {
            param.setPageCode(pageCode);
        }
        List<WcUserPageConfig> poList = wcUserPageConfigMapper.selectMany(param);
        return ConfigInfraConvert.I.pageConfigPos2Beans(poList);
    }
}
