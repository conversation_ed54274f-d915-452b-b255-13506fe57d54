package fm.lizhi.ocean.wavecenter.infrastructure.live.manager;

import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.text.StrPool;
import fm.lizhi.common.romefs.javasdk.util.MD5Utils;
import fm.lizhi.ocean.wavecenter.base.util.ThreadUtils;
import fm.lizhi.ocean.wavecenter.common.config.BizCommonConfig;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.ExtensionContentTypeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.common.convert.FileManagerConvert;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.AuditVoiceFileTransferManager;
import fm.lizhi.ocean.wavecenter.service.common.manager.RomeFsManager;
import fm.lizhi.ocean.wavecenter.service.common.param.RomeFsTransferParam;
import fm.lizhi.ocean.wavecenter.service.common.result.RomeFsPutResult;
import fm.lizhi.ocean.wavecenter.service.live.config.LiveConfig;
import fm.lizhi.ocean.wavecenter.service.live.manager.LiveAuditManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.ExecutorService;

/**
 * 转存音频文件
 */
@Slf4j
@Component
public class AuditVoiceFileTransferManagerImpl implements AuditVoiceFileTransferManager {

    @Autowired
    private CommonConfig commonConfig;

    @Autowired
    private RomeFsManager romeFsTransferManager;

    @Autowired
    private LiveAuditManager liveAuditManager;

    @Autowired
    private LiveConfig liveConfig;

    /**
     * 多线程调用线程池
     */
    private final ExecutorService executorService = ThreadUtils.getTtlExecutors("transferVoiceFile", 5, 10);


    public void transferFile(Integer appId, String recordId, String sourceContentUrl) {
        if (StringUtils.isBlank(sourceContentUrl)) {
            log.warn("sourceContentUrl is blank or not has domain url. recordId:{}, sourceContentUrl:{}", recordId, sourceContentUrl);
            return;
        }

        executorService.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    RomeFsTransferParam romeFsTransferParam = buildTransferParam(appId, sourceContentUrl);
                    int tryCount = 0;
                    do {
                        RomeFsPutResult result = romeFsTransferManager.uploadTransfer(romeFsTransferParam);
                        if (result == null) {
                            //失败了
                            log.error("recordId:{}, transfer file fail. sourceContentUrl:{}", recordId, sourceContentUrl);
                            tryCount++;
                            continue;
                        }

                        String targetUrl = UrlUtils.removeHostOrEmpty(result.getFilePath());
                        //修改状态
                        boolean res = liveAuditManager.updateAuditContentUrl(recordId, sourceContentUrl, targetUrl);
                        log.info("transfer file result. recordId:{}, sourceContentUrl:{}, targetUrl:{}, res:{}", recordId, sourceContentUrl, targetUrl, res);
                        //不管修改表是否成功，都直接结束，不重试
                        break;
                    } while (commonConfig.getUploadTransferMaxTryCount() > 0 && tryCount < commonConfig.getUploadTransferMaxTryCount());
                } catch (Exception e) {
                    log.error("transfer file error. recordId:{}, sourceContentUrl:{}", recordId, sourceContentUrl, e);
                }
            }
        });

    }

    /**
     * 构建罗马转存参数
     */
    private @NotNull RomeFsTransferParam buildTransferParam(Integer appId, String sourceContentUrl) {
        String uri = UrlUtils.removeHostOrEmpty(sourceContentUrl);
        String sourceUrl = UrlUtils.addHostOrEmpty(uri, liveConfig.getVoiceFileInternalHost());
        BizCommonConfig bizConfig = commonConfig.getBizConfig(appId);
        RomeFsTransferParam param = new RomeFsTransferParam();
        param.setSourceUrl(sourceUrl);
        param.setAccessModifier(bizConfig.getRomeConfig().getAccessModifier());
        param.setRomeFsConfig(FileManagerConvert.I.convertRomeFsConfig(bizConfig.getRomeConfig()));
        String urlPrefix = commonConfig.getBizConfig(appId).getResourceTransferUrlPrefix(MD5Utils.MD5(sourceContentUrl.getBytes(StandardCharsets.UTF_8)));
        String contentType = resolveContentType(null, sourceContentUrl);
        param.setFileName(urlPrefix + StrPool.DOT + FileNameUtil.getSuffix(sourceContentUrl));
        param.setContentType(contentType);
        return param;
    }

    private String resolveContentType(String contentType, String fileName) {
        if (StringUtils.isNotBlank(contentType)) {
            return contentType;
        }
        String extension = FilenameUtils.getExtension(fileName);
        return ExtensionContentTypeEnum.getContentType(extension);
    }

    public static void main(String[] args) {
        String url = "http://doremerecord.lzpipi.com/pp-loveTalk/5401654699604602495/5401672626798421119/20240828/pp-loveTalk_5401654699604602495_5401672626798421119_1831093706_1_1_1724836415000.aac";
        String extension = FilenameUtils.getExtension(url);
        String contentType = ExtensionContentTypeEnum.getContentType(extension);
        System.out.println(contentType);

        String suffix = FileNameUtil.getSuffix(url);
        System.out.println(suffix);
    }

}
