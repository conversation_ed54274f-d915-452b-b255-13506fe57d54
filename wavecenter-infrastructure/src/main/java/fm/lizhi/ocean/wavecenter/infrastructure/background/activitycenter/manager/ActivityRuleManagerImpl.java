package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.manager;

import com.alibaba.fastjson.JSONObject;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyRuleEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityRuleBaseAbstractBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityRuleConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityRule;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityRule;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityRuleConfigService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert.ActivityRuleConfigConvert;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityRuleConfig;
import fm.lizhi.ocean.wavecenter.datastore.background.mapper.ActivityRuleConfigMapper;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityRuleManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 活动规则配置
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityRuleManagerImpl implements ActivityRuleManager {

    @Autowired
    private ActivityRuleConfigMapper activityRuleConfigMapper;

    @Override
    public Result<Void> saveActivityRule(RequestSaveActivityRule param) {
        ActivityApplyRuleEnum ruleEnum = ActivityApplyRuleEnum.getById(param.getRuleType());
        ActivityRuleBaseAbstractBean ruleBean = this.getRuleBean(ruleEnum, param.getRuleJson());
        Result<Void> checkResult = preCheckRule(param.getRuleType(), param.getAppId(), ruleEnum, ruleBean, false);
        if (RpcResult.isFail(checkResult)) {
            return checkResult;
        }

        ActivityRuleConfig config = new ActivityRuleConfig();
        config.setAppId(param.getAppId());
        config.setRuleType(param.getRuleType());
        config.setOperator(param.getOperator());
        config.setRuleJson(JsonUtils.toJsonStringLegacy(ruleBean));
        config.setDeployEnv(ConfigUtils.getEnvRequired().name());

        return activityRuleConfigMapper.insert(config) > 0
                ? RpcResult.success()
                : RpcResult.fail(ActivityRuleConfigService.SAVE_ACTIVITY_RULE_FAIL, "保存提报规则失败");
    }


    @Override
    public Result<Void> updateActivityRule(RequestUpdateActivityRule param) {
        ActivityApplyRuleEnum ruleEnum = ActivityApplyRuleEnum.getById(param.getRuleType());
        ActivityRuleBaseAbstractBean ruleBean = this.getRuleBean(ruleEnum, param.getRuleJson());
        ActivityRuleConfig ruleConfig = activityRuleConfigMapper.selectByPrimaryKey(ActivityRuleConfig.builder().id(param.getId()).deployEnv(ConfigUtils.getEnvRequired().name()).build());

        Result<Void> checkResult = preCheckRule(param.getRuleType(), param.getAppId(), ruleEnum, ruleBean, ruleConfig.getRuleType().equals(param.getRuleType()));
        if (RpcResult.isFail(checkResult)) {
            return checkResult;
        }

        log.info("updateActivityRule. oldRuleConfig:{}", JSONObject.toJSONString(ruleConfig));
        ActivityRuleConfig updateRuleConfig = ActivityRuleConfig.builder()
                .id(param.getId())
                .ruleJson(JsonUtils.toJsonStringLegacy(ruleBean))
                .modifyTime(new Date())
                .operator(param.getOperator()).build();
        return activityRuleConfigMapper.updateByPrimaryKey(updateRuleConfig) > 0
                ? RpcResult.success()
                : RpcResult.fail(ActivityRuleConfigService.UPDATE_ACTIVITY_RULE_FAIL, "更新提报规则失败");
    }

    @Override
    public Result<Void> deleteActivityRule(Long id, String operator) {

        log.info("deleteActivityRule. id:{}, operator:{}", id, operator);
        return activityRuleConfigMapper.deleteByPrimaryKey(ActivityRuleConfig.builder().id(id).build()) > 0
                ? RpcResult.success()
                : RpcResult.fail(ActivityRuleConfigService.DELETE_ACTIVITY_RULE_FAIL, "删除提报规则失败");
    }

    @Override
    public Result<List<ActivityRuleConfigBean>> listActivityRule(int appId) {
        List<ActivityRuleConfig> activityRuleConfigs = activityRuleConfigMapper.selectMany(ActivityRuleConfig.builder()
                .appId(appId).deployEnv(ConfigUtils.getEnvRequired().name())
                .build());
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, ActivityRuleConfigConvert.I.convertActivityRuleConfigBeans(activityRuleConfigs));
    }

    @Override
    public ActivityRuleConfigBean getActivityRuleByRuleTypeAndAppId(int appId, ActivityApplyRuleEnum ruleEnum) {

        ActivityRuleConfig param = ActivityRuleConfig.builder()
                .appId(appId)
                .ruleType(ruleEnum.getId())
                .deployEnv(ConfigUtils.getEnvRequired().name())
                .build();

        ActivityRuleConfig config = activityRuleConfigMapper.selectOne(param);
        return ActivityRuleConfigConvert.I.convertActivityRuleConfigBean(config);
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T extends ActivityRuleBaseAbstractBean> T getRuleBean(ActivityApplyRuleEnum ruleEnum, String ruleJson) {
        try {
            return (T) JsonUtils.fromJsonStringLegacy(ruleJson, ruleEnum.getClazz());
        } catch (Exception e) {
            log.error("getRuleBean error, rule:{}, ruleJson:{}", ruleEnum.getRuleName(), ruleJson, e);
        }
        return null;
    }

    private Result<Void> preCheckRule(Integer ruleType, int appId, ActivityApplyRuleEnum ruleEnum, ActivityRuleBaseAbstractBean rule, boolean isUpdate) {
        if (ruleEnum == null) {
            log.info("preCheckRule is fail. ruleEnum not found: {}, appId:{}", ruleType, appId);

            return RpcResult.fail(ActivityRuleConfigService.ACTIVITY_APPLY_RULE_ENUM_NOT_FOUNT, "规则类型不存在");
        }

        if (!isUpdate) {
            ActivityRuleConfigBean existRule = getActivityRuleByRuleTypeAndAppId(appId, ruleEnum);
            if (existRule != null) {
                log.info("preCheckRule is fail. exist rule: {}, appId:{}", ruleType, appId);
                return RpcResult.fail(ActivityRuleConfigService.SAVE_ACTIVITY_RULE_EXIST, "规则已存在");
            }
        }

        // 检查规则
        if (rule != null) {
            rule.verify();
        }

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }
}
