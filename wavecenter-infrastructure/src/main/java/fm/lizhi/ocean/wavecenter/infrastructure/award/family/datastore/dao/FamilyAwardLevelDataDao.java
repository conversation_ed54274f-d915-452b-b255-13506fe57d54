package fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.dao;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert.FamilyAwardLevelDataConvert;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilyAwardLevelData;
import fm.lizhi.ocean.wavecenter.datastore.award.family.mapper.WcFamilyAwardLevelDataMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.mapper.ext.WcFamilyAwardLevelDataExtMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.param.ListFamilyAwardLevelDataParam;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.param.ListFamilyAwardLevelFamilyDataParam;
import fm.lizhi.ocean.wavecenter.service.award.family.dto.FamilyAwardLevelDataDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;

@Slf4j
@Repository
public class FamilyAwardLevelDataDao {

    @Autowired
    private WcFamilyAwardLevelDataExtMapper wcFamilyAwardLevelDataExtMapper;
    @Autowired
    private WcFamilyAwardLevelDataMapper wcFamilyAwardLevelDataMapper;

    /**
     * 查询公会等级数据
     *
     * @param appId     应用ID
     * @param familyId  公会ID
     * @param startTime 开始时间
     * @return 公会等级数据
     */
    public WcFamilyAwardLevelData getData(int appId, long familyId, Date startTime) {
        String deployEnv = ConfigUtils.getEnvRequired().name();
        WcFamilyAwardLevelData selectOne = new WcFamilyAwardLevelData();
        selectOne.setDeployEnv(deployEnv);
        selectOne.setAppId(appId);
        selectOne.setFamilyId(familyId);
        selectOne.setStartTime(startTime);
        return wcFamilyAwardLevelDataMapper.selectOne(selectOne);
    }

    /**
     * 创建公会等级数据
     *
     * @param data 公会等级数据
     */
    public void createData(FamilyAwardLevelDataDTO data) {
        log.info("Creating FamilyAwardLevelData, data={}", data);
        WcFamilyAwardLevelData createEntity = FamilyAwardLevelDataConvert.I.toCreateEntity(data);
        int insertRows = wcFamilyAwardLevelDataMapper.insert(createEntity);
        log.info("Inserted FamilyAwardLevelData, rows={}", insertRows);
    }

    /**
     * 更新公会等级数据
     *
     * @param data 公会等级数据
     */
    public void updateData(FamilyAwardLevelDataDTO data) {
        log.info("Updating FamilyAwardLevelData, data={}", data);
        String deployEnv = ConfigUtils.getEnvRequired().name();
        int updateRows = wcFamilyAwardLevelDataExtMapper.updateData(data, deployEnv);
        log.info("Updated FamilyAwardLevelData, rows={}", updateRows);
    }

    /**
     * 列出公会等级数据
     *
     * @param param 参数
     * @return 公会等级数据列表
     */
    public PageList<WcFamilyAwardLevelData> listData(ListFamilyAwardLevelDataParam param) {
        log.info("Listing FamilyAwardLevelData, param={}", param);
        String deployEnv = ConfigUtils.getEnvRequired().name();
        int pageNumber = param.getPageNumber();
        int pageSize = param.getPageSize();
        PageList<WcFamilyAwardLevelData> pageList = wcFamilyAwardLevelDataExtMapper
                .listData(param, deployEnv, pageNumber, pageSize);
        log.info("Listed FamilyAwardLevelData, total={}, listSize={}", pageList.getTotal(), pageList.size());
        return pageList;
    }

    public PageList<WcFamilyAwardLevelData> listData(ListFamilyAwardLevelFamilyDataParam param) {
        log.info("Listing FamilyAwardLevelFamilyData, param={}", param);
        String deployEnv = ConfigUtils.getEnvRequired().name();
        int pageNumber = param.getPageNumber();
        int pageSize = param.getPageSize();
        PageList<WcFamilyAwardLevelData> pageList = wcFamilyAwardLevelDataExtMapper
                .listFamilyData(param, deployEnv, pageNumber, pageSize);
        log.info("Listed FamilyAwardLevelFamilyData, total={}, listSize={}", pageList.getTotal(), pageList.size());
        return pageList;
    }
}
