package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.manager;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.common.datastore.redis.client.RedisClient;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageParamBean;
import fm.lizhi.ocean.wavecenter.api.common.constants.ExportFileStatusEnum;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.FileExportRecordBean;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.convert.FileExportRecordConvert;
import fm.lizhi.ocean.wavecenter.datastore.platform.file.entity.WcFileExportRecord;
import fm.lizhi.ocean.wavecenter.datastore.platform.file.entity.WcFileExportRecordExample;
import fm.lizhi.ocean.wavecenter.datastore.platform.file.mapper.WcFileExportRecordMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.user.constants.UserRedisKey;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.FileExportRecordManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@Service
public class FileExportRecordManagerImpl implements FileExportRecordManager {

    @Autowired
    private WcFileExportRecordMapper wcFileExportRecordMapper;
    @Resource(name = "redis_ocean_wavecenter")
    private RedisClient redisClient;


    @Override
    public FileExportRecordBean createTask(Integer appId, Long userId, String fileName) {
        WcFileExportRecord record = new WcFileExportRecord(appId, userId, fileName);

        wcFileExportRecordMapper.insert(record);
        return FileExportRecordConvert.I.toDto(record);
    }

    @Override
    public FileExportRecordBean updateTask(Long recordId, String filePath, ExportFileStatusEnum fileStatus) {
        WcFileExportRecord updateRecord = WcFileExportRecord.builder().id(recordId)
                .fileStatus(fileStatus.getValue())
                .filePath(filePath)
                .build();
        wcFileExportRecordMapper.updateByPrimaryKey(updateRecord);


        return FileExportRecordConvert.I.toDto(wcFileExportRecordMapper.selectByPrimaryKey(updateRecord));
    }

    @Override
    public Integer clearExpiredFile(Integer expiredDay) {

        Date date = DateUtil.getDayBefore(expiredDay);

        WcFileExportRecordExample wcFileExportRecordExample = new WcFileExportRecordExample();
        wcFileExportRecordExample.createCriteria().andModifyTimeLessThanOrEqualTo(date)
                        .andDeletedEqualTo(0);

        return wcFileExportRecordMapper.updateByExample(WcFileExportRecord.builder().deleted(1).build(), wcFileExportRecordExample);
    }

    @Override
    public PageBean<FileExportRecordBean> getExportList(Integer appId, Long userId, PageParamBean pageParamBean) {
        WcFileExportRecordExample example = new WcFileExportRecordExample();
        example.setOrderByClause("modify_time desc");
        example.createCriteria().andAppIdEqualTo(appId)
                .andUserIdEqualTo(userId)
                .andDeletedEqualTo(0);

        PageList<WcFileExportRecord> pageList = wcFileExportRecordMapper.pageByExample(example, pageParamBean.getPageNo(), pageParamBean.getPageSize());
        if (CollectionUtils.isEmpty(pageList)) {
            return PageBean.empty();
        }


        return PageBean.of(pageList.getTotal(), FileExportRecordConvert.I.toBeanList(pageList));
    }

    @Override
    public int getDownloadFileNum(long userId) {
        String dateValue = DateUtil.formatDateToString(new Date(), DateUtil.date);
        String key = UserRedisKey.DOWN_FILE_NUM.getKey(ContextUtils.getBusinessEvnEnum().getAppId(), userId, dateValue);
        Boolean exists = redisClient.exists(key);
        Long incr = redisClient.incr(key);
        if (Boolean.FALSE.equals(exists)) {
            redisClient.expire(key, 60*60*24);
        }
        return Math.toIntExact(incr);
    }

    @Override
    public Integer markFailToLongFile(int downloadSeconds) {
        //15分钟前的下载时间
        Date beforeTime = DateUtil.getSecondBefore(new Date(), downloadSeconds);
        //修改创建时间在beforeTime之前的，且任务状态依然是进行中的任务的状态为失败
        WcFileExportRecordExample example = new WcFileExportRecordExample();
        example.createCriteria()
                .andCreateTimeLessThan(beforeTime)
                .andFileStatusEqualTo(ExportFileStatusEnum.EXPORTING.getValue());

        WcFileExportRecord entity = new WcFileExportRecord();
        entity.setFileStatus(ExportFileStatusEnum.EXPORTING.getValue());
        return wcFileExportRecordMapper.updateByExample(entity, example);
    }

}
