package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.dao;

import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityDressUpGiveRecord;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityDressUpGiveRecordExample;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.mapper.ActivityDressUpGiveRecordMapper;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.UpdateDressUpStatusDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public class ActivityDressUpResourceGiveDao {

    @Autowired
    private ActivityDressUpGiveRecordMapper activityDressUpGiveRecordMapper;

    /**
     * 批量保存
     *
     * @param records 记录列表
     * @return 结果
     */
    public boolean batchSaveRecords(List<ActivityDressUpGiveRecord> records) {
        if (CollectionUtils.isEmpty(records)) {
            return true;
        }

        for (ActivityDressUpGiveRecord record : records) {
            if (record.getCreateTime() == null) {
                record.setCreateTime(new Date());
            }
            if (record.getModifyTime() == null) {
                record.setModifyTime(new Date());
            }
        }

        return activityDressUpGiveRecordMapper.batchInsert(records) == records.size();
    }

    public boolean batchUpdateDressUpStatus(List<UpdateDressUpStatusDTO> records) {
        if (CollectionUtils.isEmpty(records)) {
            return true;
        }

        for (UpdateDressUpStatusDTO record : records) {
            ActivityDressUpGiveRecordExample example = new ActivityDressUpGiveRecordExample();
            example.createCriteria().andIdEqualTo(record.getId()).andStatusEqualTo(record.getOriginalStatus());
            ActivityDressUpGiveRecord upGiveRecord = ActivityDressUpGiveRecord.builder().id(record.getId()).status(record.getStatus()).modifyTime(new Date()).build();
            boolean res = activityDressUpGiveRecordMapper.updateByExample(upGiveRecord, example) > 0;
            if (!res) {
                return false;
            }
        }
        return true;
    }

    /**
     * 批量删除发放记录
     *
     * @param giveIds 发放ID
     * @return 结果
     */
    public boolean batchDeleteGiveRecord(List<Long> giveIds) {
        if (CollectionUtils.isEmpty(giveIds)) {
            return true;
        }

        ActivityDressUpGiveRecordExample example = new ActivityDressUpGiveRecordExample();
        example.createCriteria().andGiveIdIn(giveIds);
        long row = activityDressUpGiveRecordMapper.deleteByExample(example);
        if(row == 0){
            //可能原本就是没有的，查一把
            ActivityDressUpGiveRecordExample queryExample = new ActivityDressUpGiveRecordExample();
            queryExample.createCriteria().andGiveIdIn(giveIds);
            List<ActivityDressUpGiveRecord> list = activityDressUpGiveRecordMapper.selectByExample(queryExample);
            if(CollectionUtils.isEmpty(list)){
                return true;
            }
        }
        return row > 0;
    }

    /**
     * 批量删除流量资源
     *
     * @param giveIds 发放ID
     * @return 结果
     */
    public boolean batchDeleteDressUpResource(List<Long> giveIds) {
        if (CollectionUtils.isEmpty(giveIds)) {
            return true;
        }
        ActivityDressUpGiveRecordExample example = new ActivityDressUpGiveRecordExample();
        example.createCriteria().andGiveIdIn(giveIds);
        return activityDressUpGiveRecordMapper.deleteByExample(example) > 0;
    }
}

