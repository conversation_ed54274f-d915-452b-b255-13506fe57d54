package fm.lizhi.ocean.wavecenter.infrastructure.file.job;

import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.FileExportRecordManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class ClearExpiredFileJob implements JobHandler {

    @Autowired
    private FileExportRecordManager fileExportRecordManager;


    @Override
    public void execute(JobExecuteContext jobExecuteContext) throws Exception {
        // 清理超过N的数据
        String param = jobExecuteContext.getParam();
        // 默认3天
        Integer expiredDay = 3;
        // 默认下载时长 15分钟
        int minute = 15;

        if (StringUtils.isNotBlank(param)){
            String[] split = param.split(",");
            expiredDay = Integer.valueOf(split[0]);
            if (split.length > 1){
                minute = Integer.parseInt(split[1]);
            }
        }
        log.info("Clear expired file job start. expiredDay: {}", expiredDay);

        Integer expireCount = fileExportRecordManager.clearExpiredFile(expiredDay);
        log.info("Clear expired file job end. expiredDay: {}, expire count: {}", expiredDay, expireCount);

        //修改长时间未完成的任务的状态
        Integer markFailToLongFileCount = fileExportRecordManager.markFailToLongFile(minute * 60);
        log.info("Clear expired file job. minute: {}, markFailToLongFileCount: {}", minute, markFailToLongFileCount);
    }
}