package fm.lizhi.ocean.wavecenter.infrastructure.award.singer.manager;

import cn.hutool.core.collection.CollUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.bean.SingerDecorateRuleBean;
import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateFlowOperateEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateOperateStatusEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.ThreadUtils;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.entity.SingerDecorateFlow;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.mapper.SingerDecorateFlowMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.award.singer.convert.SingerDecorateConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.DecorateRemote;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerInfoDTO;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowDTO;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowGenerateDTO;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowInitParamDTO;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowSendParamDTO;
import fm.lizhi.ocean.wavecenter.service.award.singer.manager.SingerDecorateFlowManager;
import fm.lizhi.ocean.wavecenter.service.award.singer.manager.SingerDecorateManager;
import fm.lizhi.ocean.wavecenter.service.award.singer.manager.SingerDecorateRuleManager;
import fm.lizhi.ocean.wavecenter.service.common.manager.IdManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * 歌手装扮管理器
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class SingerDecorateManagerImpl implements SingerDecorateManager {

    /**
     * 多线程调用线程池
     */
    private final ExecutorService executorService = ThreadUtils.getTtlExecutors("singer-decorate-manager", 50, 50);

    @Autowired
    private IdManager idManager;

    @Autowired
    private SingerDecorateFlowMapper singerDecorateFlowMapper;

    @Autowired
    private SingerDecorateFlowManager singerDecorateFlowManager;

    @Autowired
    private DecorateRemote decorateRemote;


    @Autowired
    private SingerDecorateRuleManager singerDecorateRuleManager;



    @Override
    public void operateSingerDecorateAsync(SingerDecorateFlowSendParamDTO param) {
        executorService.execute(() -> operateSingerDecorate(param));
    }

    @Override
    public void operateSingerDecorate(SingerDecorateFlowSendParamDTO param) {
        try {
            List<SingerDecorateFlowDTO> flowList = singerDecorateFlowManager.getDecorateFlowByTransactionIdAndLteRetryCount(param.getTransactionId(),
                    CollUtil.newArrayList(SingerDecorateOperateStatusEnum.FAIL, SingerDecorateOperateStatusEnum.UNPROCESSED));

            for (SingerDecorateFlowDTO flow : flowList) {
                Result<Void> result = RpcResult.success();
                try {
                    if (flow.getOperateType().equals(SingerDecorateFlowOperateEnum.GRANT.getCode())) {
                        result = decorateRemote.sendDecorate(SingerDecorateConvert.I.buildRequestSendDecorate(flow));
                    } else if (flow.getOperateType().equals(SingerDecorateFlowOperateEnum.RECOVER.getCode())) {
                        result = decorateRemote.recoverDecorate(SingerDecorateConvert.I.buildRequestRecoverDecorate(flow));
                    }
                    if (RpcResult.isFail(result)) {
                        log.warn("operateType:{} singer decorate flow fail. transactionId:{}, flowId:{}, rCode:{}",
                                flow.getOperateType(), param.getTransactionId(), flow.getId(), result.rCode());
                    }

                } catch (Exception e) {
                    log.error("operate singer decorate flow error:{}", param.getTransactionId(), e);
                    result = RpcResult.fail(GeneralRCode.GENERAL_RCODE_SERVER_BUSY);
                }finally {
                    boolean success = singerDecorateFlowManager.updateDecorateFlowStatus(flow,
                            RpcResult.isSuccess(result) ? SingerDecorateOperateStatusEnum.SUCCESS : SingerDecorateOperateStatusEnum.FAIL
                    );
                    log.info("operate singer decorate flow updateDecorateFlowStatus status:{}, transactionId:{}", success, param.getTransactionId());
                }
            }
        }catch (Exception e){
            log.error("operateSingerDecorate error", e);
        }
    }

    @Override
    public Optional<SingerDecorateFlowGenerateDTO> generateAndInsertSingerDecorateFlowAndSendEvent(SingerDecorateFlowInitParamDTO param) {
        Optional<SingerDecorateFlowGenerateDTO> flowGenerateOptional = generateAndInsertSingerDecorateFlow(param);
        flowGenerateOptional.ifPresent(dto -> this.operateSingerDecorateAsync(new SingerDecorateFlowSendParamDTO().setTransactionId(dto.getTransactionId())));

        if (!flowGenerateOptional.isPresent()) {
            log.warn("initSingerDecorateFlowAndSendEvent cannot find transactionId");
        }

        return flowGenerateOptional;
    }



    @Override
    public Optional<SingerDecorateFlowGenerateDTO> generateSingerDecorateFlowList(SingerDecorateFlowInitParamDTO param) {
        Optional<SingerDecorateFlowGenerateDTO> optional = Optional.empty();
        if (SingerDecorateFlowOperateEnum.GRANT.equals(param.getOperateType())) {
            // 发放流水
            optional = generateSingerDecorateFlowListByGrant(param);
        } else if (SingerDecorateFlowOperateEnum.RECOVER.equals(param.getOperateType())) {
            // 回收流水
            optional = generateSingerDecorateFlowListByRecover(param);
        }
        return optional;
    }

    /**
     * 生成歌手发放装扮流水信息
     * <p>
     * 提供到调用方写入，保证事务
     */
    private Optional<SingerDecorateFlowGenerateDTO> generateSingerDecorateFlowListByGrant(SingerDecorateFlowInitParamDTO param) {
        List<SingerInfoDTO> singerInfoList = param.getSingerInfoList();
        if (CollUtil.isEmpty(singerInfoList)) {
            log.warn("generateSingerDecorateFlow cannot find singerInfo");
            return Optional.empty();
        }

        Long transactionId = param.getTransactionId() == null ? idManager.genId(): param.getTransactionId();
        List<SingerDecorateFlow> singerDecorateFlowList = new ArrayList<>();
        for (SingerInfoDTO singer : singerInfoList) {
            List<SingerDecorateRuleBean> ruleList = singerDecorateRuleManager.getSingerDecorateRule(param.getAppId(),
                    SingerTypeEnum.getByType(singer.getSingerType()), singer.getSongStyle(), singer.getOriginalSinger());

            for (SingerDecorateRuleBean rule : ruleList) {
                SingerDecorateFlow flow = SingerDecorateConvert.I.buildSingerDecorateFlowByGrant(singer, rule, transactionId, param);
                singerDecorateFlowList.add(flow);
            }
        }
        return Optional.of(
                new SingerDecorateFlowGenerateDTO()
                        .setTransactionId(transactionId)
                        .setDecorateFlowList(SingerDecorateConvert.I.toSingerDecorateFlowDTOList(singerDecorateFlowList))
        );
    }


    /**
     * 生成歌手回收装扮流水信息
     * <p>
     * 提供到调用方写入，保证事务
     */
    private Optional<SingerDecorateFlowGenerateDTO> generateSingerDecorateFlowListByRecover(SingerDecorateFlowInitParamDTO param) {
        List<SingerInfoDTO> singerInfoList = param.getSingerInfoList();
        if (CollUtil.isEmpty(singerInfoList)) {
            log.warn("initSingerDecorateFlowByRecover cannot find singerInfo");
            return Optional.empty();
        }
        List<SingerDecorateFlow> singerDecorateFlowList = new ArrayList<>();
        Long transactionId = param.getTransactionId() == null ? idManager.genId(): param.getTransactionId();
        for (SingerInfoDTO singerInfo : singerInfoList) {
            // 需要注意这里会不会导致重复回收
            List<SingerDecorateFlowDTO> canRecoverFlow =
                    singerDecorateFlowManager.getCanRecoverDecorateFlowByUserId(param.getAppId(), singerInfo.getUserId(), singerInfo.getSingerType());
            if(CollectionUtils.isNotEmpty(canRecoverFlow)) {
                Map<Long, List<SingerDecorateFlowDTO>> collect =
                        canRecoverFlow.stream().collect(Collectors.groupingBy(SingerDecorateFlowDTO::getDecorateId));
                collect.forEach((decorateId, list) -> {
                    singerDecorateFlowList.add(SingerDecorateConvert.I.buildSingerDecorateFlowByRecover(list.get(0), param, transactionId));
                });
            }
        }

        return Optional.of(
                new SingerDecorateFlowGenerateDTO()
                        .setTransactionId(transactionId)
                        .setDecorateFlowList(SingerDecorateConvert.I.toSingerDecorateFlowDTOList(singerDecorateFlowList))
        );
    }


    private Optional<SingerDecorateFlowGenerateDTO> generateAndInsertSingerDecorateFlow(SingerDecorateFlowInitParamDTO param) {
        if (null == param) {
            return Optional.empty();
        }

        Optional<SingerDecorateFlowGenerateDTO> optional = generateSingerDecorateFlowList(param);
        optional.ifPresent(dto -> {
            if (CollUtil.isNotEmpty(dto.getDecorateFlowList())) {
                singerDecorateFlowMapper.batchInsert(SingerDecorateConvert.I.toSingerDecorateFlowList(dto.getDecorateFlowList()));
            }
        });

        return optional;
    }


}
