package fm.lizhi.ocean.wavecenter.infrastructure.chat.remote.xm;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.chat.remote.IChatServiceRemote;
import fm.lizhi.xm.common.flow.services.chat.XmChatFlowService;
import fm.lizhi.xm.common.flow.services.chat.req.SendChatAsyncReq;
import fm.lizhi.xm.common.flow.services.chat.resp.SendChatAsyncResp;
import fm.lizhi.xm.social.constant.ChatQueueId;
import fm.lizhi.xm.social.constant.ChatType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class XmChatServiceRemote implements IChatServiceRemote {

    @Autowired
    private XmChatFlowService chatService;


    @Override
    public Result<Void> sendChatAsync(long senderUid, long receiverUid, String content) {
        SendChatAsyncReq req = new SendChatAsyncReq();
        req.setChatType(ChatType.TEXT.getValue());
        req.setIncludeSender(false);
        req.setContent(content);
        req.setQueueId(ChatQueueId.HIGH_PRIORITY.getValue());
        req.setReceiverUid(receiverUid);
        req.setSenderUid(senderUid);
        Result<SendChatAsyncResp> result = chatService.sendChatAsync(req);
        return new Result<>(result.rCode(), null);
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.XIMI;
    }

    @Override
    public Result<Void> sendRichTextChatAsync(long senderUid, long receiverUid, String content) {
        SendChatAsyncReq req = new SendChatAsyncReq();
        req.setChatType(ChatType.RICH_TEXT.getValue());
        req.setIncludeSender(false);
        req.setContent(content);
        req.setQueueId(ChatQueueId.HIGH_PRIORITY.getValue());
        req.setReceiverUid(receiverUid);
        req.setSenderUid(senderUid);
        Result<SendChatAsyncResp> result = chatService.sendChatAsync(req);
        return new Result<>(result.rCode(), null);
    }

    @Override
    public Result<Void> sendCardChatAsync(long senderUid, long receiverUid, String content) {
        SendChatAsyncReq req = new SendChatAsyncReq();
        req.setChatType(ChatType.CARD.getValue());
        req.setIncludeSender(false);
        req.setContent(content);
        req.setQueueId(ChatQueueId.HIGH_PRIORITY.getValue());
        req.setReceiverUid(receiverUid);
        req.setSenderUid(senderUid);
        Result<SendChatAsyncResp> result = chatService.sendChatAsync(req);
        return new Result<>(result.rCode(), null);
    }

    @Override
    public Result<Void> sendRichTextChatAsyncLowPriority(long senderUid, long receiverUid, String content) {
        SendChatAsyncReq req = new SendChatAsyncReq();
        req.setChatType(ChatType.RICH_TEXT.getValue());
        req.setIncludeSender(false);
        req.setContent(content);
        req.setQueueId(ChatQueueId.LOW_PRIORITY.getValue());
        req.setReceiverUid(receiverUid);
        req.setSenderUid(senderUid);
        Result<SendChatAsyncResp> result = chatService.sendChatAsync(req);
        return new Result<>(result.rCode(), null);
    }
}
