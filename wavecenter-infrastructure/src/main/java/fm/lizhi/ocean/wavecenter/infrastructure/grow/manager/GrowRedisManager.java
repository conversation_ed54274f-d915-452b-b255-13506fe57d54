package fm.lizhi.ocean.wavecenter.infrastructure.grow.manager;

import fm.lizhi.common.datastore.redis.client.RedisClient;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.constants.GrowRedisKey;
import fm.lizhi.ocean.wavecenter.service.grow.config.GrowConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/19 17:24
 */
@Component
public class GrowRedisManager {

    @Resource(name = "redis_ocean_wavecenter")
    private RedisClient redisClient;

    @Autowired
    private GrowConfig growConfig;

    /**
     * 获取上次结算的familyId
     * @param appId
     * @param startTime
     * @param endTime
     * @return
     */
    public Long getLastSettleFamilyId(Integer appId, Date startTime, Date endTime){
        Integer startDay = MyDateUtil.getDateDayValue(startTime);
        Integer endDay = MyDateUtil.getDateDayValue(endTime);
        String key = GrowRedisKey.FAMILY_LEVEL_SETTLE_LAST_FAMILY.getKey(appId, startDay, endDay);
        String familyId = redisClient.get(key);
        if (familyId == null) {
            return 0L;
        }
        return Long.valueOf(familyId);
    }

    /**
     * 设置上次结算的familyId
     * @param appId
     * @param startTime
     * @param endTime
     * @param lastFamilyId
     */
    public void setLastSettleFamilyId(Integer appId, Date startTime, Date endTime, Long lastFamilyId){
        Integer startDay = MyDateUtil.getDateDayValue(startTime);
        Integer endDay = MyDateUtil.getDateDayValue(endTime);
        String key = GrowRedisKey.FAMILY_LEVEL_SETTLE_LAST_FAMILY.getKey(appId, startDay, endDay);
        redisClient.setex(key, growConfig.getSettleFamilyLevelTagExpireSeconds(), String.valueOf(lastFamilyId));
    }

}
