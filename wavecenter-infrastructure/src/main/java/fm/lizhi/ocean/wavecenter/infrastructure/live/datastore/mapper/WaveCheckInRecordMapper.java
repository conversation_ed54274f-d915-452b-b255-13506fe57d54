package fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.common.datastore.mysql.constant.ParamContants;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.common.datastore.mysql.mybatis.sqlprovider.BaseDeleteProvider;
import fm.lizhi.common.datastore.mysql.mybatis.sqlprovider.BaseInsertProvider;
import fm.lizhi.common.datastore.mysql.mybatis.sqlprovider.BaseSelectProvider;
import fm.lizhi.common.datastore.mysql.mybatis.sqlprovider.BaseUpdateProvider;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WaveCheckInRecord;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WaveCheckInRecordExample;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcCheckInRecord;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 */
@DataStore(namespace = "mysql_ocean_wave_r")
public interface WaveCheckInRecordMapper {

    /**
     * 根据实体对象的字段值查询多条记录，查询条件会跳过NULL值的字段。
     *
     * @param entity  实体对象
     * @return  实体对象列表
     */
    @SelectProvider(type = BaseSelectProvider.class, method="select")
    List<WaveCheckInRecord> selectMany(WaveCheckInRecord entity);

    /**
     * 根据实体对象的字段值查询单条记录，查询条件会跳过NULL值的字段。
     *
     * @param entity  实体对象
     * @return  单个实体对象
     */
    @SelectProvider(type = BaseSelectProvider.class, method="select")
    WaveCheckInRecord selectOne(WaveCheckInRecord entity);

    /**
     * 根据实体对象中主键（{@link javax.persistence.Id}标注）字段查询单条数据。
     *
     * @param entity  实体对象
     * @return  单个实体对象
     */
    @SelectProvider(type = BaseSelectProvider.class, method="selectByPrimaryKey")
    WaveCheckInRecord selectByPrimaryKey(WaveCheckInRecord entity);

    /**
     * 根据实体对象的字段值查询分页记录，查询条件会跳过NULL值的字段。
     *
     * @param entity  实体对象
     * @return  分页数据
     */
    @SelectProvider(type = BaseSelectProvider.class, method="selectPage")
    PageList<WaveCheckInRecord> selectPage(@Param(ParamContants.ENTITIE) WaveCheckInRecord entity, @Param(ParamContants.PAGE_NUMBER)int pageNumber, @Param(ParamContants.PAGE_SIZE)int pageSize);

    /**
     * 根据实体对象中主键字段（{@link javax.persistence.Id}标注）删除数据。
     *
     * @param entity  实体对象
     * @return  影响行数
     */
    @DeleteProvider(type = BaseDeleteProvider.class, method="deleteByPrimaryKey")
    int deleteByPrimaryKey(WaveCheckInRecord entity);

    /**
     * 将实体对象写入数据库，会跳过NULL值的字段。<br/>
     * 如果主键字段（{@link javax.persistence.Id}标注）有设置{@link javax.persistence.GeneratedValue}时，会自动生成主键并设置到实体对象中。
     *
     * @param entity  实体对象
     * @return  影响行数
     */
    @InsertProvider(type = BaseInsertProvider.class, method="insert")
    int insert(WaveCheckInRecord entity);

    /**
     * 批量将实体对象写入数据库（不会跳过NULL值)。<br/>
     * 如果字段有设置{@link javax.persistence.Id}和{@link javax.persistence.GeneratedValue}时，会自动生成值，并设置到实体类实例中。
     *
     * @param entities  实体对象列表
     * @return  影响行数
     */
    @InsertProvider(type = BaseInsertProvider.class, method="batchInsert")
    int batchInsert(@Param(ParamContants.ENTITIES) List<WaveCheckInRecord> entities);

    /**
     * 根据实体类中主键（{@link javax.persistence.Id}标注的字段）更新数据，会跳过NULL值的字段。
     *
     * @param entity  实体对象
     * @return  影响行数
     */
    @UpdateProvider(type = BaseUpdateProvider.class, method="updateByPrimaryKey")
    int updateByPrimaryKey(WaveCheckInRecord entity);

    /**
     * 根据example类生成WHERE条件查询总记录条数
     *
     * @param example
     * @return
     */
    @SelectProvider(type = BaseSelectProvider.class, method="countByExample")
    long countByExample(WaveCheckInRecordExample example);

    /**
     * 根据example类生成WHERE条件删除记录
     *
     * @param example
     * @return
     */
    @DeleteProvider(type = BaseDeleteProvider.class, method="deleteByExample")
    long deleteByExample(WaveCheckInRecordExample example);

    @Select({
            "<script>" +
                    "select app_id, family_id, host_id, a.room_id, user_id, nj_id, income, " +
                    "a.`status`, charm_value, original_value as charm , start_time, end_time, " +
                    " (CASE WHEN user_id = host_id THEN 1 ELSE 0 END) as is_host, " +
                    " b.remark,a.create_time, a.modify_time" +
                    " from wave_check_in_record a left join " +
                    " wave_check_in_schedule b on a.schedule_id = b.id " +
                    " where a.user_id =#{userId} and  b.start_time &gt;= #{startDate} and  b.start_time &lt;= #{endDate} and b.app_id=#{appId} and b.family_id = #{familyId} " +
                    "<if test = 'signType != null and signType == 1 '>" +
                        "<if test = 'njId != null and njId > 0 '>" +
                        " and b.nj_id = #{njId} " +
                        "</if>" +
                    "</if>" +
                    "<if test = 'signType != null and signType == 0 '>" +
                        "<if test = 'njId != null and njId > 0 '>" +
                        " and b.nj_id != #{njId} " +
                        "</if>" +
                    "</if>" +
            "</script>"
    })
    List<WaveCheckInRecord> hourDetail(@Param("appId") Integer appId, @Param("userId") Long userId,
                                       @Param("startDate") Date startDate, @Param("endDate")Date endDate,
                                       @Param("njId") Long njId, @Param("familyId") Long familyId,
                                       @Param("signType") Integer signType);

    @Select({
            "<script>"
            , "select app_id, family_id, host_id, a.room_id, user_id, nj_id, income, "
            , "a.`status`, charm_value, original_value as charm , start_time, end_time, "
            , " (CASE WHEN user_id = host_id THEN 1 ELSE 0 END) as is_host, "
            , " b.remark,a.create_time, a.modify_time"
            , " from wave_check_in_record a left join "
            , " wave_check_in_schedule b on a.schedule_id = b.id "
            , " where b.start_time = #{startDate} and  b.end_time = #{endDate} and b.app_id=#{appId}   "
            , "<if test=' null != playerId and playerId > 0 '>"
            ," and  a.user_id  = #{playerId} "
            ,"</if>"
            ,"<if test=' null != roomId and roomId > 0 '>"
            ," and  b.nj_id  = #{roomId} "
            ,"</if>"
            , "</script>"
    })
    List<WcCheckInRecord> hourDetail2(@Param("appId") Integer appId
            , @Param("roomId") Long roomId, @Param("playerId") Long playerId
            , @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 根据example类生成WHERE条件更新记录数，会跳过实体类对象中的NULL值的字段。<br/>
     *
     * @param entity 实体类对象
     * @param example
     * @return
     */
    @UpdateProvider(type = BaseUpdateProvider.class, method="updateByExample")
    int updateByExample(@Param(ParamContants.ENTITIE) WaveCheckInRecord entity, @Param(ParamContants.EXAMPLE) WaveCheckInRecordExample example);



    @Select({
            "<script>" +
                    "select app_id, family_id, host_id, a.room_id, user_id, nj_id, income, " +
                    "a.`status`, charm_value, original_value as charm , start_time, end_time, " +
                    " (CASE WHEN user_id = host_id THEN 1 ELSE 0 END) as is_host, " +
                    " b.remark,a.create_time, a.modify_time" +
                    " from wave_check_in_schedule b inner join " +
                    " wave_check_in_record a ON a.schedule_id = b.id " +
                    " where b.family_id =#{familyId} and  b.start_time &gt;= #{startDate} and  b.start_time &lt;= #{endDate} and b.app_id=#{appId} " +
                    " order by a.create_time" +
                    "</script>"
    })
    PageList<WaveCheckInRecord> guildRoomHourDetail(@Param("appId") Integer appId, @Param("familyId") Long familyId,
                                                    @Param("startDate") Date startDate, @Param("endDate") Date endDate,
                                                    @Param(ParamContants.PAGE_NUMBER)int pageNumber,
                                                    @Param(ParamContants.PAGE_SIZE)int pageSize);
}
