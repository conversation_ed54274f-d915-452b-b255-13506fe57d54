package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.manager;

import fm.lizhi.common.datastore.mysql.exception.DatastoreMysqlOperationException;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.infrastructure.common.util.ExceptionUtil;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.dao.DataPayPlayerDayDao;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.dao.DataPayRoomDayDao;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.PlayerFlowChangeDTO;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.RoomFlowChangeDTO;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.AccountFlowManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/4/24 14:36
 */
@Slf4j
@Component
public class AccountFlowManagerImpl implements AccountFlowManager {

    @Autowired
    private DataPayRoomDayDao payRoomDayDao;
    @Autowired
    private DataPayPlayerDayDao payPlayerDayDao;
    @Autowired
    private FamilyManager familyManager;

    @Override
    public void saveRoomFlowChange(RoomFlowChangeDTO dto) {
        Date bookkeepingDate = dto.getTradeDate();
        Integer statDateValue = MyDateUtil.getDateDayValue(bookkeepingDate);

        // 查询是否已存在
        if (isRoomDayFlowExist(dto.getAppId(), dto.getRoomId(), statDateValue)) {
            // 存在，更新
            int row = payRoomDayDao.updateRoomDayFlow(dto);
            log.info("saveRoomFlowChange updateRow={}", row);
        } else {
            // 不存在，查询签约数据，然后保存，捕获唯一索引冲突，如果冲突进行更新
            UserInFamilyBean userInFamily = familyManager.getUserInFamily(dto.getRoomId());
            Long familyId = userInFamily.getFamilyId() != null ? userInFamily.getFamilyId() : 0L;
            try {
                int row = payRoomDayDao.addRoomDayFlow(dto, familyId);
                log.info("saveRoomFlowChange addRow={}", row);
            } catch (DatastoreMysqlOperationException e) {
                log.warn("saveRoomFlowChange DatastoreMysqlOperationException:", e);
                if (ExceptionUtil.isDuplicateKeyException(e)) {
                    // 冲突则进行更新
                    int row = payRoomDayDao.updateRoomDayFlow(dto);
                    log.info("saveRoomFlowChange DuplicateKey updateRow={}", row);
                }else {
                    throw e;
                }
            }
        }

    }

    private boolean isRoomDayFlowExist(Integer appId, Long roomId, Integer statDateValue){
        return payRoomDayDao.isRoomDayFlowExist(appId, roomId, statDateValue);
    }

    @Override
    public void savePlayerFlowChange(PlayerFlowChangeDTO dto) {
        Date bookkeepingDate = dto.getTradeDate();
        Integer statDateValue = MyDateUtil.getDateDayValue(bookkeepingDate);

        // 查询是否已存在
        if (isPlayerDayFlowExist(dto.getAppId(), dto.getPlayerId(), statDateValue)) {
            // 存在，更新
            int row = payPlayerDayDao.updatePlayerDayFlow(dto);
            log.info("savePlayerFlowChange updateRow={}", row);
        } else {
            // 不存在，查询签约数据，然后保存，捕获唯一索引冲突，如果冲突进行更新
            UserInFamilyBean userInFamily = familyManager.getUserInFamily(dto.getPlayerId());
            Long familyId = userInFamily.getFamilyId() != null ? userInFamily.getFamilyId() : 0L;
            Long njId = userInFamily.getNjId() != null ? userInFamily.getNjId() : 0L;
            try {
                int row = payPlayerDayDao.addPlayerDayFlow(dto, familyId, njId);
                log.info("savePlayerFlowChange addRow={}", row);
            } catch (DatastoreMysqlOperationException e) {
                log.warn("savePlayerFlowChange DatastoreMysqlOperationException:", e);
                if (ExceptionUtil.isDuplicateKeyException(e)) {
                    // 冲突则进行更新
                    int row = payPlayerDayDao.updatePlayerDayFlow(dto);
                    log.info("savePlayerFlowChange DuplicateKey updateRow={}", row);
                } else {
                    throw e;
                }
            }
        }
    }

    private boolean isPlayerDayFlowExist(Integer appId, Long playerId, Integer statDateValue){
        return payPlayerDayDao.isPlayerDayFlowExist(appId, playerId, statDateValue);
    }
}
