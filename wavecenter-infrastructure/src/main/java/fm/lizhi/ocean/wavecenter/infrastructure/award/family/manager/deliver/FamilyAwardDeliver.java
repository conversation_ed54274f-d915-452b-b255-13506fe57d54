package fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager.deliver;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardResourceDeliverTypeEnum;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardTypeEnum;

/**
 * 公会奖励发放接口
 */
public interface FamilyAwardDeliver {

    /**
     * 是否支持该奖励类型和发放类型
     *
     * @param awardType   奖励类型
     * @param deliverType 发放类型
     * @return 是否支持
     */
    boolean supports(FamilyAwardTypeEnum awardType, FamilyAwardResourceDeliverTypeEnum deliverType);

    /**
     * 获取资源信息, 用于发放记录冗余保存. 实现类必须捕获异常并包装成Result返回
     *
     * @param param 发放资源参数
     * @return 校验资源结果
     */
    Result<GetResourceInfoResult> getResourceInfo(DeliverResourceParam param);

    /**
     * 发放资源. 实现类必须捕获异常并包装成Result返回
     *
     * @param param 发放资源参数
     * @return 发放结果
     */
    Result<Void> deliverResource(DeliverResourceParam param);
}
