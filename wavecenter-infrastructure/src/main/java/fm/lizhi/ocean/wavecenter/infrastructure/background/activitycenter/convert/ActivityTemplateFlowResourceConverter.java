package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityTemplateGeneralFlowResourceBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateDetailFlowResourceBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateFlowResourceBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateFlowResourceExtraBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateFlowResourceImageBean;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityResourceConfig;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityTemplateFlowResource;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.LogicDeleteConstants;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityTemplateFlowResourceDTO;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityTemplateFlowResourceExtraDTO;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityTemplateFlowResourceImageDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * 活动模板流量资源转换器
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.ERROR,
        imports = {UrlUtils.class, JsonUtils.class, Objects.class, LogicDeleteConstants.class,})
public abstract class ActivityTemplateFlowResourceConverter {

    @Autowired
    protected CommonConfig commonConfig;

    /**
     * 转换为创建活动模板流量资源的实体列表
     *
     * @param beans      请求对象列表
     * @param templateId 活动模板ID
     * @return 创建活动模板流量资源的实体列表
     */
    public List<ActivityTemplateFlowResource> toCreateActivityTemplateFlowResources(List<ActivityTemplateFlowResourceBean> beans, long templateId) {
        if (CollectionUtils.isEmpty(beans)) {
            return Collections.emptyList();
        }
        List<ActivityTemplateFlowResource> activityTemplateFlowResources = new ArrayList<>(beans.size());
        for (int index = 0; index < beans.size(); index++) {
            ActivityTemplateFlowResource flowResource = toCreateActivityTemplateFlowResource(beans.get(index), templateId, index);
            activityTemplateFlowResources.add(flowResource);
        }
        return activityTemplateFlowResources;
    }

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "extra", expression = "java(JsonUtils.toJsonString(bean.getExtra()))")
    public abstract ActivityTemplateFlowResource toCreateActivityTemplateFlowResource(ActivityTemplateFlowResourceBean bean, long templateId, int index);

    /**
     * 转换为API接口返回的活动模板流量资源的Bean列表
     *
     * @param entities      实体列表
     * @param imageBeansMap 图片Bean Map, key为活动模板流量资源ID, value为对应的图片Bean列表
     * @return API接口返回的活动模板流量资源的Bean列表
     */
    public List<ActivityTemplateFlowResourceBean> toActivityTemplateFlowResourceBeans(List<ActivityTemplateFlowResource> entities, Map<Long, List<ActivityTemplateFlowResourceImageBean>> imageBeansMap) {
        if (CollectionUtils.isEmpty(entities)) {
            return Collections.emptyList();
        }
        ArrayList<ActivityTemplateFlowResourceBean> activityTemplateFlowResourceBeans = new ArrayList<>(entities.size());
        for (ActivityTemplateFlowResource entity : entities) {
            Long flowResourceId = entity.getId();
            List<ActivityTemplateFlowResourceImageBean> images = MapUtils.emptyIfNull(imageBeansMap).get(flowResourceId);
            ActivityTemplateFlowResourceExtraBean extraBean = StringUtils.isNotBlank(entity.getExtra()) ? JsonUtils.fromJsonString(entity.getExtra(), ActivityTemplateFlowResourceExtraBean.class) : new ActivityTemplateFlowResourceExtraBean();
            ActivityTemplateFlowResourceBean activityTemplateFlowResourceBean = toActivityTemplateFlowResourceBean(entity.getResourceConfigId(), images, extraBean);
            activityTemplateFlowResourceBeans.add(activityTemplateFlowResourceBean);
        }
        return activityTemplateFlowResourceBeans;
    }

    @Mapping(target = "resourceCode", ignore = true)
    @Mapping(target = "name", ignore = true)
    public abstract ActivityTemplateFlowResourceBean toActivityTemplateFlowResourceBean(Long resourceConfigId, List<ActivityTemplateFlowResourceImageBean> images, ActivityTemplateFlowResourceExtraBean extra);

    /**
     * 转换为活动模板流量资源的DTO
     *
     * @param resourceConfig 活动模板流量资源配置实体
     * @param images         图片列表
     * @param extra          扩展信息
     * @return 活动模板流量资源的DTO
     */
    @Mapping(target = "resourceConfigId", source = "resourceConfig.id")
    @Mapping(target = "imageUrl", expression = "java(UrlUtils.addHostOrEmpty(resourceConfig.getImageUrl(), commonConfig.getRomeFsDownloadCdn()))")
    @Mapping(target = "resourceStatus", source = "resourceConfig.status")
    @Mapping(target = "resourceDeleted", expression = "java(Objects.equals(resourceConfig.getDeleted(), LogicDeleteConstants.DELETED))")
    public abstract ActivityTemplateFlowResourceDTO toActivityTemplateFlowResourceDTO(ActivityResourceConfig resourceConfig, List<ActivityTemplateFlowResourceImageDTO> images, ActivityTemplateFlowResourceExtraDTO extra);

    /**
     * 转换为API接口返回的活动模板详情流量资源的Bean列表
     *
     * @param flowResourceDTOS 活动模板流量资源DTO列表
     * @return API接口返回的活动模板详情流量资源的Bean列表
     */
    public abstract List<ActivityTemplateDetailFlowResourceBean> toActivityTemplateDetailFlowResourceBeans(List<ActivityTemplateFlowResourceDTO> flowResourceDTOS);

    /**
     * 转换为API接口返回的通用活动模板流量资源的Bean列表
     *
     * @param flowResourceDTOS 活动模板流量资源DTO列表
     * @return API接口返回的通用活动模板流量资源的Bean列表
     */
    public abstract List<ActivityTemplateGeneralFlowResourceBean> toActivityTemplateGeneralFlowResourceBeans(List<ActivityTemplateFlowResourceDTO> flowResourceDTOS);
}
