package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.manager;

import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.convert.GrowRoomMetricConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowRoomMetricValue;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowRoomMetricValueExample;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.mapper.WcGrowRoomMetricValueMapper;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.GrowRoomMetricValueDTO;
import fm.lizhi.ocean.wavecenter.service.grow.ability.manager.GrowRoomMetricManager;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Component
public class GrowRoomMetricManagerImpl implements GrowRoomMetricManager {

    @Autowired
    private WcGrowRoomMetricValueMapper growRoomMetricValueMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveRoomMetric(GrowRoomMetricValueDTO dto) {
        // 构建查询条件
        WcGrowRoomMetricValueExample example = new WcGrowRoomMetricValueExample();
        example.createCriteria()
            .andAppIdEqualTo(ContextUtils.getBusinessEvnEnum().getAppId())
            .andRoomIdEqualTo(dto.getRoomId())
            .andStartWeekDateEqualTo(dto.getStartWeekDate())
            .andEndWeekDateEqualTo(dto.getEndWeekDate());

        // 查询现有记录
        List<WcGrowRoomMetricValue> existingMetrics = growRoomMetricValueMapper.selectByExample(example);
        boolean exists = CollectionUtils.isNotEmpty(existingMetrics);

        // 构造实体
        WcGrowRoomMetricValue entity = GrowRoomMetricConvert.I.dto2Entity(dto);
        entity.setAppId(ContextUtils.getBusinessEvnEnum().getAppId());
        entity.setModifyTime(new Date());

        if (exists) {
            // 更新逻辑（保留原始创建时间）
            WcGrowRoomMetricValue existing = existingMetrics.get(0);
            entity.setId(existing.getId());
            entity.setCreateTime(existing.getCreateTime());
            growRoomMetricValueMapper.updateByPrimaryKey(entity);
        } else {
            // 新增逻辑（设置创建时间）
            entity.setCreateTime(new Date());
            growRoomMetricValueMapper.insert(entity);
        }
    }
}