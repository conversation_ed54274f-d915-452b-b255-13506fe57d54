package fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.param;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 创建公会奖励发放记录参数
 */
@Data
public class CreateFamilyAwardDeliverRecordParam {

    /**
     * 应用id
     */
    private int appId;

    /**
     * 公会id
     */
    private long familyId;

    /**
     * 公会名称
     */
    private String familyName;

    /**
     * 公会长id
     */
    private long familyUserId;

    /**
     * 公会长名称
     */
    private String familyUserName;

    /**
     * 奖励周期开始时间
     */
    private Date awardStartTime;

    /**
     * 发放时间
     */
    private Date deliverTime;

    /**
     * 发放执行列表
     */
    private List<CreateFamilyAwardDeliverExecutionParam> deliverExecutions;
}
