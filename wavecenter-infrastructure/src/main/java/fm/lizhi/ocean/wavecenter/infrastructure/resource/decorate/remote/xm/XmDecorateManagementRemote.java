package fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.xm;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.xm.XmCreateAvatarWidgetBean;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.xm.XmCreateRoomBackgroundBean;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestCreateAvatarWidget;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestCreateRoomBackground;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.response.ResponseCreateAvatarWidget;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.response.ResponseCreateRoomBackground;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.common.validation.BeanValidator;
import fm.lizhi.ocean.wavecenter.common.validation.ValidateResult;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.convert.xm.XmDecorateManagementConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.DecorateManagementRemote;
import fm.lizhi.xm.vip.bean.decorate.DecorateDto;
import fm.lizhi.xm.vip.bean.decorate.DecorateExtInfoDto;
import fm.lizhi.xm.vip.bean.decorate.DecorateExtInfoKeyEnum;
import fm.lizhi.xm.vip.bean.decorate.DecorateTypeEnum;
import fm.lizhi.xm.vip.bean.decorate.resp.AddOrEditDecorateResp;
import fm.lizhi.xm.vip.bean.decorate.resp.DecorateTipEnum;
import fm.lizhi.xm.vip.constant.decorate.DecorateSourceEnum;
import fm.lizhi.xm.vip.services.DecorateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;

@Component
@Slf4j
public class XmDecorateManagementRemote implements DecorateManagementRemote {

    private final XmDecorateManagementConvert xmDecorateManagementConvert = XmDecorateManagementConvert.I;

    @Autowired
    private BeanValidator beanValidator;

    @Autowired
    private DecorateService decorateService;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.XIMI;
    }

    @Override
    public Result<ResponseCreateRoomBackground> createRoomBackground(RequestCreateRoomBackground request) {
        try {
            XmCreateRoomBackgroundBean bean = xmDecorateManagementConvert.toXmCreateRoomBackgroundBean(request);
            ValidateResult validateResult = beanValidator.validate(bean);
            if (validateResult.isInvalid()) {
                log.info("createRoomBackground request invalid, request={}, validateResult={}", request, validateResult);
                return RpcResult.fail(CommonService.PARAM_ERROR, validateResult.getMessage());
            }
            DecorateDto roomBackgroundDTO = buildCreateRoomBackgroundDTO(bean);
            Result<AddOrEditDecorateResp> result = decorateService.addOrEditDecorate(roomBackgroundDTO);
            int rCode = result.rCode();
            if (rCode == DecorateTipEnum.ILLEGAL_PARAMS.getCode()) {
                log.info("addOrEditDecorate request invalid, roomBackgroundDTO={}", request);
                return RpcResult.fail(CommonService.PARAM_ERROR, "参数错误");
            }
            if (rCode != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                log.info("addOrEditDecorate fail, rCode={}, roomBackgroundDTO={}", rCode, request);
                return RpcResult.fail(CommonService.BUSINESS_ERROR, "保存失败");
            }
            AddOrEditDecorateResp target = result.target();
            log.info("addOrEditDecorate success, roomBackgroundDTO={}, target={}", request, target);
            ResponseCreateRoomBackground response = new ResponseCreateRoomBackground();
            response.setId(target.getDecorateId());
            response.setPreviewUrl(bean.getThumbUrl());
            return RpcResult.success(response);
        } catch (RuntimeException e) {
            log.error("createRoomBackground error, request={}", request, e);
            return RpcResult.fail(CommonService.INTERNAL_ERROR, "内部错误");
        }
    }

    private DecorateDto buildCreateRoomBackgroundDTO(XmCreateRoomBackgroundBean bean) {
        int decorateType = DecorateTypeEnum.BACKGROUD_VALUE;
        // 小西米底层接口已经将url转换成了相对路径, 这里不需要再转换
        DecorateDto decorateDto = new DecorateDto();
        decorateDto.setSource(DecorateSourceEnum.PC.getValue());
        decorateDto.setType(decorateType);
        decorateDto.setName(bean.getName());
        // 各种URL字段去掉host和开头的斜杠, 保持和互娱后台前端提交的一致
        decorateDto.setThumbUrl(UrlUtils.removeHostAndStartSlashOrEmpty(bean.getThumbUrl()));
        decorateDto.setMaterialUrl(UrlUtils.removeHostAndStartSlashOrEmpty(bean.getMaterialUrl()));
        decorateDto.setIconUrl(UrlUtils.removeHostAndStartSlashOrEmpty(bean.getIconUrl()));
        decorateDto.setValidMin(bean.getValidMin());
        // 扩展信息
        ArrayList<DecorateExtInfoDto> decorateExtInfoDTOS = new ArrayList<>();
        decorateExtInfoDTOS.add(buildBackgroundTypeDTO(decorateType, bean.getBackgroundType()));
        if (StringUtils.isNotBlank(bean.getStageUrl())) {
            decorateExtInfoDTOS.add(buildStageUrlDTO(decorateType, bean.getStageUrl()));
        }
        if (StringUtils.isNotBlank(bean.getStageSvgaUrl())) {
            decorateExtInfoDTOS.add(buildStageSvgaUrl(decorateType, bean.getStageSvgaUrl()));
        }
        if (StringUtils.isNotBlank(bean.getStageLocationUrl())) {
            decorateExtInfoDTOS.add(buildStageLocationUrl(decorateType, bean.getStageLocationUrl()));
        }
        if (StringUtils.isNotBlank(bean.getHostModeVoicePrintSvgaUrl())) {
            decorateExtInfoDTOS.add(buildHostModeVoicePrintSvgaUrl(decorateType, bean.getHostModeVoicePrintSvgaUrl()));
        }
        if (StringUtils.isNotBlank(bean.getSingingModeVoicePrintSvgaUrl())) {
            decorateExtInfoDTOS.add(buildSingingModeVoicePrintSvgaUrl(decorateType, bean.getSingingModeVoicePrintSvgaUrl()));
        }
        if (StringUtils.isNotBlank(bean.getStageApertureSvgaUrl())) {
            decorateExtInfoDTOS.add(buildStageApertureSvgaUrl(decorateType, bean.getStageApertureSvgaUrl()));
        }
        if (StringUtils.isNotBlank(bean.getMaterialUrl())) {
            decorateExtInfoDTOS.add(buildMaterialTypeDTO(decorateType, bean.getMaterialType()));
        }
        if (StringUtils.isNotBlank(bean.getBackgroundColor())) {
            decorateExtInfoDTOS.add(buildBackgroundColorDTO(decorateType, bean.getBackgroundColor()));
        }
        String extInfo = JsonUtils.toJsonString(decorateExtInfoDTOS);
        decorateDto.setExtInfo(extInfo);
        return decorateDto;
    }

    private DecorateExtInfoDto buildBackgroundTypeDTO(int decorateType, Integer backgroundType) {
        DecorateExtInfoDto decorateExtInfoDto = new DecorateExtInfoDto();
        decorateExtInfoDto.setType(decorateType);
        decorateExtInfoDto.setKey(DecorateExtInfoKeyEnum.backgroundType.getKey());
        decorateExtInfoDto.setName(DecorateExtInfoKeyEnum.backgroundType.getName());
        decorateExtInfoDto.setValue(backgroundType.toString());
        return decorateExtInfoDto;
    }

    private DecorateExtInfoDto buildStageUrlDTO(int decorateType, String stageUrl) {
        DecorateExtInfoDto decorateExtInfoDto = new DecorateExtInfoDto();
        decorateExtInfoDto.setType(decorateType);
        decorateExtInfoDto.setKey(DecorateExtInfoKeyEnum.stageUrl.getKey());
        decorateExtInfoDto.setName(DecorateExtInfoKeyEnum.stageUrl.getName());
        decorateExtInfoDto.setValue(UrlUtils.removeHostAndStartSlashOrEmpty(stageUrl));
        return decorateExtInfoDto;
    }

    private DecorateExtInfoDto buildStageSvgaUrl(int decorateType, String stageSvgaUrl) {
        DecorateExtInfoDto decorateExtInfoDto = new DecorateExtInfoDto();
        decorateExtInfoDto.setType(decorateType);
        decorateExtInfoDto.setKey(DecorateExtInfoKeyEnum.stageSvgaUrl.getKey());
        decorateExtInfoDto.setName(DecorateExtInfoKeyEnum.stageSvgaUrl.getName());
        decorateExtInfoDto.setValue(UrlUtils.removeHostAndStartSlashOrEmpty(stageSvgaUrl));
        return decorateExtInfoDto;
    }

    private DecorateExtInfoDto buildStageLocationUrl(int decorateType, String stageLocationUrl) {
        DecorateExtInfoDto decorateExtInfoDto = new DecorateExtInfoDto();
        decorateExtInfoDto.setType(decorateType);
        decorateExtInfoDto.setKey(DecorateExtInfoKeyEnum.stageLocationUrl.getKey());
        decorateExtInfoDto.setName(DecorateExtInfoKeyEnum.stageLocationUrl.getName());
        decorateExtInfoDto.setValue(UrlUtils.removeHostAndStartSlashOrEmpty(stageLocationUrl));
        return decorateExtInfoDto;
    }

    private DecorateExtInfoDto buildHostModeVoicePrintSvgaUrl(int decorateType, String hostModeVoicePrintSvgaUrl) {
        DecorateExtInfoDto decorateExtInfoDto = new DecorateExtInfoDto();
        decorateExtInfoDto.setType(decorateType);
        decorateExtInfoDto.setKey(DecorateExtInfoKeyEnum.hostModeVoicePrintSvgaUrl.getKey());
        decorateExtInfoDto.setName(DecorateExtInfoKeyEnum.hostModeVoicePrintSvgaUrl.getName());
        decorateExtInfoDto.setValue(UrlUtils.removeHostAndStartSlashOrEmpty(hostModeVoicePrintSvgaUrl));
        return decorateExtInfoDto;
    }

    private DecorateExtInfoDto buildSingingModeVoicePrintSvgaUrl(int decorateType, String singingModeVoicePrintSvgaUrl) {
        DecorateExtInfoDto decorateExtInfoDto = new DecorateExtInfoDto();
        decorateExtInfoDto.setType(decorateType);
        decorateExtInfoDto.setKey(DecorateExtInfoKeyEnum.singingModeVoicePrintSvgaUrl.getKey());
        decorateExtInfoDto.setName(DecorateExtInfoKeyEnum.singingModeVoicePrintSvgaUrl.getName());
        decorateExtInfoDto.setValue(UrlUtils.removeHostAndStartSlashOrEmpty(singingModeVoicePrintSvgaUrl));
        return decorateExtInfoDto;
    }

    private DecorateExtInfoDto buildStageApertureSvgaUrl(int decorateType, String stageApertureSvgaUrl) {
        DecorateExtInfoDto decorateExtInfoDto = new DecorateExtInfoDto();
        decorateExtInfoDto.setType(decorateType);
        decorateExtInfoDto.setKey(DecorateExtInfoKeyEnum.stageApertureSvgaUrl.getKey());
        decorateExtInfoDto.setName(DecorateExtInfoKeyEnum.stageApertureSvgaUrl.getName());
        decorateExtInfoDto.setValue(UrlUtils.removeHostAndStartSlashOrEmpty(stageApertureSvgaUrl));
        return decorateExtInfoDto;
    }

    private DecorateExtInfoDto buildMaterialTypeDTO(int decorateType, String materialType) {
        DecorateExtInfoDto decorateExtInfoDto = new DecorateExtInfoDto();
        decorateExtInfoDto.setType(decorateType);
        decorateExtInfoDto.setKey(DecorateExtInfoKeyEnum.materialType.getKey());
        decorateExtInfoDto.setName(DecorateExtInfoKeyEnum.materialType.getName());
        decorateExtInfoDto.setValue(materialType);
        return decorateExtInfoDto;
    }

    private DecorateExtInfoDto buildBackgroundColorDTO(int decorateType, String backgroundColor) {
        DecorateExtInfoDto decorateExtInfoDto = new DecorateExtInfoDto();
        decorateExtInfoDto.setType(decorateType);
        decorateExtInfoDto.setKey(DecorateExtInfoKeyEnum.backgroundColor.getKey());
        decorateExtInfoDto.setName(DecorateExtInfoKeyEnum.backgroundColor.getName());
        decorateExtInfoDto.setValue(backgroundColor);
        return decorateExtInfoDto;
    }

    @Override
    public Result<ResponseCreateAvatarWidget> createAvatarWidget(RequestCreateAvatarWidget request) {
        try {
            XmCreateAvatarWidgetBean bean = xmDecorateManagementConvert.toXmCreateAvatarWidgetBean(request);
            ValidateResult validateResult = beanValidator.validate(bean);
            if (validateResult.isInvalid()) {
                log.info("createAvatarWidget request invalid, request={}, validateResult={}", request, validateResult);
                return RpcResult.fail(CommonService.PARAM_ERROR, validateResult.getMessage());
            }
            DecorateDto avatarWidgetDTO = buildCreateAvatarWidgetDTO(bean);
            Result<AddOrEditDecorateResp> result = decorateService.addOrEditDecorate(avatarWidgetDTO);
            int rCode = result.rCode();
            if (rCode == DecorateTipEnum.ILLEGAL_PARAMS.getCode()) {
                log.info("createAvatarWidget request invalid, avatarWidgetDTO={}", request);
                return RpcResult.fail(CommonService.PARAM_ERROR, "参数错误");
            }
            if (rCode != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                log.info("createAvatarWidget fail, rCode={}, avatarWidgetDTO={}", rCode, request);
                return RpcResult.fail(CommonService.BUSINESS_ERROR, "保存失败");
            }
            AddOrEditDecorateResp target = result.target();
            log.info("createAvatarWidget success, avatarWidgetDTO={}, target={}", request, target);
            ResponseCreateAvatarWidget response = new ResponseCreateAvatarWidget();
            response.setId(target.getDecorateId());
            response.setPreviewUrl(bean.getThumbUrl());
            return RpcResult.success(response);
        } catch (RuntimeException e) {
            log.error("createAvatarWidget error, request={}", request, e);
            return RpcResult.fail(CommonService.INTERNAL_ERROR, "内部错误");
        }
    }

    private DecorateDto buildCreateAvatarWidgetDTO(XmCreateAvatarWidgetBean bean) {
        int decorateType = DecorateTypeEnum.AVATAR_WIDGET.getType();
        DecorateDto decorateDto = new DecorateDto();
        decorateDto.setSource(DecorateSourceEnum.PC.getValue());
        decorateDto.setType(decorateType);
        decorateDto.setName(bean.getName());
        // 各种URL字段去掉host和开头的斜杠, 保持和互娱后台前端提交的一致
        decorateDto.setThumbUrl(UrlUtils.removeHostAndStartSlashOrEmpty(bean.getThumbUrl()));
        decorateDto.setMaterialUrl(UrlUtils.removeHostAndStartSlashOrEmpty(bean.getMaterialUrl()));
        // 西米互娱后台前端提交的头像框图标为空字符串, 这里也保持一致
        decorateDto.setIconUrl(StringUtils.EMPTY);
        decorateDto.setPcAniUrl(UrlUtils.removeHostAndStartSlashOrEmpty(StringUtils.defaultString(bean.getPcAniUrl())));
        decorateDto.setValidMin(bean.getValidMin());
        // 扩展信息
        ArrayList<DecorateExtInfoDto> decorateExtInfoDTOS = new ArrayList<>();
        if (StringUtils.isNotBlank(bean.getMaterialUrl())) {
            decorateExtInfoDTOS.add(buildMaterialTypeDTO(decorateType, bean.getMaterialType()));
        }
        String extInfo = JsonUtils.toJsonString(decorateExtInfoDTOS);
        decorateDto.setExtInfo(extInfo);
        return decorateDto;
    }
}
