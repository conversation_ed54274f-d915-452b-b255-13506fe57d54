package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote.pp;

import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RankBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.convert.DataCenterInfraConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.PpPlayerSignCharmStatMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.CharmStatPo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote.IRankDataRemote;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class PpRankDataRemote implements IRankDataRemote {

    @Autowired
    private PpPlayerSignCharmStatMapper ppPlayerSignCharmStatMapper;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public List<RankBean> guildPlayer(long familyId, List<Long> roomIds, Date date, OrderType rankType) {
        String dateStr = DateUtil.formatDateToString(date, DateUtil.date_2);
        List<CharmStatPo> charmStatPos = ppPlayerSignCharmStatMapper.guildPlayerRankPageList(familyId, roomIds, dateStr, rankType.getValue());
        return DataCenterInfraConvert.I.charmStatPos2Beans(charmStatPos);
    }

    @Override
    public List<RankBean> roomPlayer(long njId, long familyId, Date date, OrderType rankType) {
        String dateStr = DateUtil.formatDateToString(date, DateUtil.date_2);
        List<CharmStatPo> charmStatPos = ppPlayerSignCharmStatMapper.roomPlayerRankPageList(njId, familyId, dateStr, rankType.getValue());
        return DataCenterInfraConvert.I.charmStatPos2Beans(charmStatPos);
    }
}
