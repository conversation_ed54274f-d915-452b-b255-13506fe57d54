package fm.lizhi.ocean.wavecenter.infrastructure.live.dto;

import fm.lizhi.ocean.wavecenter.api.live.bean.WaveCheckInRoomStatisticReportBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.WaveCheckInUserBean;
import lombok.Data;

import java.util.List;


/**
 * 获取打卡厅明细统计的响应
 */
@Data
public class GetCheckInRoomStatisticReportDTO {

    /**
     * 打卡厅明细统计列表
     */
    private List<WaveCheckInRoomStatisticReportBean> list;

    /**
     * 厅主信息
     */
    private WaveCheckInUserBean roomInfo;
}
