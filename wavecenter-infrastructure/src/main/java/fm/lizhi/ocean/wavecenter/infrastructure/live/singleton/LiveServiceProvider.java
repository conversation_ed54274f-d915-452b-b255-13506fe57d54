package fm.lizhi.ocean.wavecenter.infrastructure.live.singleton;

import fm.hy.family.api.RoomWhitelistService;
import fm.lizhi.common.dubbo.DubboClientBuilder;
import fm.lizhi.live.room.hy.api.LiveNewService;
import fm.lizhi.live.room.pp.api.LiveRoomService;
import fm.lizhi.ocean.lamp.common.generic.annotation.ScanBusinessProviderAPI;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.ServiceProviderConstants;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/5/24 19:10
 */
@ScanBusinessProviderAPI(values = {
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = RoomWhitelistService.class),
})
@Configuration
public class LiveServiceProvider {

    @Bean
    public LiveNewService hyLiveNewService(){
        return new DubboClientBuilder<>(LiveNewService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public fm.lizhi.live.room.pp.api.LiveNewService ppLiveNewService(){
        return new DubboClientBuilder<>(fm.lizhi.live.room.pp.api.LiveNewService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public fm.lizhi.live.room.xm.api.LiveNewService xmLiveNewService(){
        return new DubboClientBuilder<>(fm.lizhi.live.room.xm.api.LiveNewService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public fm.lizhi.live.room.hy.api.LiveAfficheService hyLiveAfficheService(){
        return new DubboClientBuilder<>(fm.lizhi.live.room.hy.api.LiveAfficheService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public fm.lizhi.live.room.pp.api.LiveAfficheService ppLiveAfficheService(){
        return new DubboClientBuilder<>(fm.lizhi.live.room.pp.api.LiveAfficheService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public fm.lizhi.live.room.xm.api.LiveRoomService xmLiveRoomService(){
        return new DubboClientBuilder<>(fm.lizhi.live.room.xm.api.LiveRoomService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public fm.lizhi.live.room.hy.api.LiveRoomService hyLiveRoomService(){
        return new DubboClientBuilder<>(fm.lizhi.live.room.hy.api.LiveRoomService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public LiveRoomService liveRoomService(){
        return new DubboClientBuilder<>(LiveRoomService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

}
