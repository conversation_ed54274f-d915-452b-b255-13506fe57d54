package fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager.deliver.other;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardResourceDeliverTypeEnum;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardTypeEnum;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert.FamilyAwardNewRoomConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager.deliver.DeliverResourceParam;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager.deliver.FamilyAwardDeliver;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager.deliver.GetResourceInfoResult;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.newroom.remote.NewRoomRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.newroom.remote.request.RequestSetNewRoomConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 公会其他奖励新厅名额发放实现
 */
@Component
@Slf4j
public class FamilyOtherAwardNewRoomDeliver implements FamilyAwardDeliver {

    @Autowired
    private NewRoomRemote newRoomRemote;

    @Override
    public boolean supports(FamilyAwardTypeEnum awardType, FamilyAwardResourceDeliverTypeEnum deliverType) {
        return awardType == FamilyAwardTypeEnum.OTHER && deliverType == FamilyAwardResourceDeliverTypeEnum.NEW_ROOM;
    }

    @Override
    public Result<GetResourceInfoResult> getResourceInfo(DeliverResourceParam param) {
        // 新厅名额没有信息
        return RpcResult.success(GetResourceInfoResult.emptyInfo());
    }

    @Override
    public Result<Void> deliverResource(DeliverResourceParam param) {
        try {
            RequestSetNewRoomConfig request = FamilyAwardNewRoomConvert.I.toRequestSetNewRoomConfig(param);
            log.info("Deliver new room, request={}", request);
            Result<Void> result = newRoomRemote.setNewRoomConfig(request);
            if (RpcResult.isFail(result)) {
                log.info("Deliver new room fail, request={}, rCode={}, message={}", request, result.rCode(), result.getMessage());
                return RpcResult.fail(result.rCode(), result.getMessage());
            }
            log.info("Deliver new room success, param={}", param);
            return RpcResult.success();
        } catch (RuntimeException e) {
            log.error("Deliver new room error, param={}", param, e);
            return RpcResult.fail(CommonService.INTERNAL_ERROR, e.getMessage());
        }
    }
}
