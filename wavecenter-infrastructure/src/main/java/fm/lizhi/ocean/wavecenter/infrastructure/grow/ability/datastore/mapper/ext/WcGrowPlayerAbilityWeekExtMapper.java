package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowPlayerAbilityWeek;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcGrowPlayerAbilityWeekExtMapper {

    @Select({
            "<script>"
            , "select distinct room_id"
            , "from wavecenter_grow_player_ability_week"
            , "where start_week_date = #{weekStartDay}"
            , "  and end_week_date = #{weekEndDay}"
            , "  and app_id = #{appId}"
            , "  and deploy_env = #{deployEnv}"
            , "  and room_id &gt; #{minRoomId}"
            , "order by room_id limit #{pageSize}"
            , "</script>"
    })
    List<Long> getRoomIdsWeekHasPlayerAbilityByMinRoomId(
            @Param("appId") Integer appId,
            @Param("deployEnv") String deployEnv,
            @Param("weekStartDay") Date weekStartDay,
            @Param("weekEndDay") Date weekEndDay,
            @Param("minRoomId") Long minRoomId,
            @Param("pageSize") Integer pageSize);

    @Select({
            "<script>"
            , "select count(distinct player_id)"
            , "from wavecenter_grow_player_ability_week"
            , "where start_week_date = #{weekStartDay}"
            , "  and end_week_date = #{weekEndDay}"
            , "  and app_id = #{appId}"
            , "  and deploy_env = #{deployEnv}"
            , "  and room_id = #{roomId}"
            , "</script>"
    })
    long countPlayerAbilityByRoomId(
            @Param("appId") Integer appId,
            @Param("deployEnv") String deployEnv,
            @Param("weekStartDay") Date weekStartDay,
            @Param("weekEndDay") Date weekEndDay,
            @Param("roomId") Long roomId);

    @Select("<script>\n" +
            "  SELECT * FROM `wavecenter_grow_player_ability_week`\n" +
            "  WHERE `deploy_env` = #{deployEnv}\n" +
            "    AND `app_id` = #{appId}\n" +
            "    AND `room_id` = #{roomId}\n" +
            "    AND `start_week_date` = #{startWeekDate}\n" +
            "    <if test=\"firstSignInFamily != null\">\n" +
            "      AND `first_sign_in_family` = #{firstSignInFamily}\n" +
            "    </if>\n" +
            "    <if test=\"minSignDate != null\">\n" +
            "      AND `sign_date` &gt;= #{minSignDate}\n" +
            "    </if>\n" +
            "  ORDER BY `total_score` ${orderType.value}\n" +
            "</script>")
    List<WcGrowPlayerAbilityWeek> getPlayerAbilityWeekRanks(
            @Param("deployEnv") String deployEnv,
            @Param("appId") int appId,
            @Param("roomId") long roomId,
            @Param("startWeekDate") Date startWeekDate,
            @Param("firstSignInFamily") Boolean firstSignInFamily,
            @Param("minSignDate") Date minSignDate,
            @Param("orderType") OrderType orderType);
}
