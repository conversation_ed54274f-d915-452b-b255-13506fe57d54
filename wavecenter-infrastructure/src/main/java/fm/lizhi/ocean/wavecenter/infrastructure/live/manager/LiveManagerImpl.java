package fm.lizhi.ocean.wavecenter.infrastructure.live.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.live.bean.LiveInfoBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.live.convert.LiveConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.live.dto.LiveDto;
import fm.lizhi.ocean.wavecenter.infrastructure.live.remote.ILiveRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.live.request.GetRoomInfoByNjIdResponse;
import fm.lizhi.ocean.wavecenter.infrastructure.live.request.GetRoomNoticeRequest;
import fm.lizhi.ocean.wavecenter.infrastructure.live.request.GetRoomNoticeResponse;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.GetRoomInfoByNjIdDTO;
import fm.lizhi.ocean.wavecenter.service.live.dto.EditRoomNoticeDTO;
import fm.lizhi.ocean.wavecenter.service.live.dto.GetRoomNoticeDTO;
import fm.lizhi.ocean.wavecenter.service.live.dto.GetRoomNoticeParamDTO;
import fm.lizhi.ocean.wavecenter.service.live.manager.LiveManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/8/26 14:34
 */
@Component
public class LiveManagerImpl implements LiveManager {

    @Autowired
    private ILiveRemote iLiveRemote;

    @Override
    public Optional<LiveInfoBean> getLiveInfo(long liveId) {
        Optional<LiveDto> liveInfoOp = iLiveRemote.getLiveInfo(liveId);
        return liveInfoOp.map(liveDto -> new LiveInfoBean().setUserId(liveDto.getUserId()).setOnAir(liveDto.isOnAir()));
    }

    @Override
    public Optional<Long> getLatestLiveIdByUserId(Long userId) {
        return iLiveRemote.getLatestLiveIdByUserId(userId);
    }

    @Override
    public Result<GetRoomNoticeDTO> getRoomNotice(GetRoomNoticeParamDTO dto) {
        GetRoomNoticeRequest getRoomNoticeRequest = LiveConvert.I.buildGetRoomNoticeRequest(dto);
        Result<GetRoomNoticeResponse> result = iLiveRemote.getRoomNotice(getRoomNoticeRequest);
        if (RpcResult.isFail(result)) {
            return RpcResult.fail(result.rCode(), result.getMessage());
        }

        return RpcResult.success(LiveConvert.I.convertRoomDayStats(result.target()));
    }

    @Override
    public Result<Void> editRoomNotice(EditRoomNoticeDTO dto) {
        return iLiveRemote.editRoomNotice(LiveConvert.I.buildEditRoomNoticeRequest(dto));
    }

    @Override
    public Result<GetRoomInfoByNjIdDTO> getRoomInfoByNjId(Long userId) {
        Result<GetRoomInfoByNjIdResponse> result = iLiveRemote.getRoomInfoByNjId(userId);
        if (RpcResult.isFail(result)) {
            return RpcResult.fail(result.rCode());
        }
        GetRoomInfoByNjIdDTO getRoomInfoByNjIdDTO = LiveConvert.I.convertRoomInfo(result.target());
        return RpcResult.success(getRoomInfoByNjIdDTO);
    }

}
