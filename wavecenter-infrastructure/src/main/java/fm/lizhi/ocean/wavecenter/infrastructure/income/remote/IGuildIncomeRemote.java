package fm.lizhi.ocean.wavecenter.infrastructure.income.remote;

import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomIncomeDetailBean;
import fm.lizhi.ocean.wavecenter.base.remote.IRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.income.remote.req.GetSignRoomIncomeDetailReq;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/27 14:21
 */
public interface IGuildIncomeRemote extends IRemote {

    /**
     * 查询公会签约厅收入统计列表
     * @param req
     * @return
     */
    List<RoomIncomeDetailBean> getSignRoomIncomeDetail(GetSignRoomIncomeDetailReq req);

}
