package fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.constants;

import java.util.Arrays;

import fm.lizhi.hy.amusement.enm.DressUpType;
import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import fm.lizhi.pp.vip.constant.DecorateTypeEnum;

/**
 * 黑叶平台装扮枚举
 * <AUTHOR>
 * @date 2025/3/21 15:30
 */
public enum HyDecorateTypeEnum {
    /**
     * 座驾
     */
    VEHICLE(DressUpType.MOUNT.getId(), PlatformDecorateTypeEnum.VEHICLE),

    /**
     * 勋章
     */
    MEDAL(HyDecorateTypeConstants.MEDAL_TYPE, PlatformDecorateTypeEnum.MEDAL),

    /**
     * 头像框
     */
    AVATAR(DressUpType.AVATAR_FRAME.getId(), PlatformDecorateTypeEnum.AVATAR),

    /**
     * 背景
     */
    BACKGROUND(DressUpType.ROOM_BG.getId(), PlatformDecorateTypeEnum.BACKGROUND),

    /**
     * 用户官方认证
     * hy 官方认证不属于装扮
     */
    USER_GLORY(HyDecorateTypeConstants.USER_GLORY_TYPE, PlatformDecorateTypeEnum.USER_GLORY);

    private final int type;

    private final PlatformDecorateTypeEnum decorateTypeEnum;

    HyDecorateTypeEnum(int type, PlatformDecorateTypeEnum decorateTypeEnum) {
        this.type = type;
        this.decorateTypeEnum = decorateTypeEnum;
    }

    public int getType() {
        return type;
    }

    public PlatformDecorateTypeEnum getDecorateTypeEnum() {
        return decorateTypeEnum;
    }

    public static HyDecorateTypeEnum getByType(PlatformDecorateTypeEnum decorateTypeEnum) {
        return Arrays.stream(HyDecorateTypeEnum.values())
                .filter(hyDecorateEnum -> hyDecorateEnum.getDecorateTypeEnum().equals(decorateTypeEnum))
                .findFirst()
                .orElse(null);
    }
}

/**
 * 黑叶平台装扮类型常量
 */
interface HyDecorateTypeConstants {
    /**
     * 勋章类型
     */
    int MEDAL_TYPE = 99;

    /**
     * 用户官方认证类型
     */
    int USER_GLORY_TYPE = 98;
}
