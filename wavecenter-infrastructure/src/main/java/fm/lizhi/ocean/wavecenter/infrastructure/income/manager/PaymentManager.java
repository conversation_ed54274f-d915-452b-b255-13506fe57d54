package fm.lizhi.ocean.wavecenter.infrastructure.income.manager;

import com.alibaba.csp.sentinel.adapter.dubbo.fallback.SentinelDubboFlowRegistry;
import com.google.common.collect.Lists;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.common.constants.DateType;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.IncomeBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.IncomeSummaryThreadBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomPerformanceStatisticsBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.RoomSignBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.CalculateUtil;
import fm.lizhi.ocean.wavecenter.base.util.CountDownLatchWrapper;
import fm.lizhi.ocean.wavecenter.base.util.ThreadUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.ThreadConstants;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataPayPlayerDay;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataPayPlayerDayExample;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataPayRoomDay;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataPayRoomDayExample;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PaySettleConfigCodeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PayTenantCodeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.convert.RankDataConvert;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.mapper.WcDataPayPlayerDayMapper;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.mapper.WcDataPayRoomDayMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.IncomeStatPo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.PlayerIncomeStatisticsBean;
import fm.lizhi.ocean.wavecenter.infrastructure.income.constants.PayAppIdEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.income.dto.ConfigCodeBizInfoDTO;
import fm.lizhi.ocean.wavecenter.infrastructure.income.dto.RoomIncomeDTO;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.IContractRemote;
import fm.lizhi.ocean.wavecenter.service.datacenter.config.DataCenterConfig;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.PerformanceInfoDto;
import fm.lizhi.ocean.wavecenter.service.income.config.IncomeConfig;
import fm.lizhi.ocean.wavecenter.service.income.config.PayAccountConfig;
import fm.lizhi.pay.settle.api.creativecenter.CreatorDataQueryService;
import fm.lizhi.pay.settle.protocol.creativecenter.CreatorDataQueryProto;
import fm.lizhi.pay.settle.settleenum.creativecenter.DirectionEnum;
import fm.lizhi.pay.settle.settleenum.creativecenter.PeriodTypeEnum;
import fm.lizhi.trade.query.center.account.api.AccountQueryService;
import fm.lizhi.trade.query.center.account.protocol.QueryAccountProto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * 支付服务统一入口
 * 一期支付的调用如果写在其他服务，有时间就统一迁移到这里
 *
 * <AUTHOR>
 * @date 2024/4/24 18:13
 */
@Slf4j
@Component
public class PaymentManager {

    int BATCH_QUERY_PLAYER_PAGE_SIZE = 200;

    @Autowired
    private AccountQueryService accountQueryService;

    @Autowired
    private CreatorDataQueryService creatorDataQueryService;

    @Autowired
    private IncomeConfig incomeConfig;

    @Autowired
    private DataCenterConfig dataCenterConfig;

    @Autowired
    private IContractRemote contractRemote;

    @Autowired
    private WcDataPayPlayerDayMapper payPlayerDayMapper;

    @Autowired
    private WcDataPayRoomDayMapper payRoomDayMapper;


    /**
     * 多线程调用线程池
     */
    private final ExecutorService executorService = ThreadUtils.getTtlExecutors("payment", 100, 100);

    @PostConstruct
    public void init(){
        SentinelDubboFlowRegistry.registerFlowHandler(CreatorDataQueryService.class, new CreatorDataQueryServiceHandler());
    }

    /**
     * 查询公会top收入的厅
     * @param appId
     * @param familyId
     * @param roomIds
     * @param date
     * @param rankType
     * @param topN
     * @return
     */
    public List<RoomIncomeDTO> getTopRoomIncomeForGuild(Integer appId, Long familyId, List<Long> roomIds, Date date, OrderType rankType, Integer topN){
        WcDataPayRoomDayExample example = new WcDataPayRoomDayExample();
        if (rankType == OrderType.DESC) {
            // 收入倒序
            example.setOrderByClause("income desc,id");
        } else {
            // 升序
            example.setOrderByClause("income asc,id");
        }

        WcDataPayRoomDayExample.Criteria criteria = example.createCriteria();
        criteria.andIncomeGreaterThan(BigDecimal.ZERO)
                .andFamilyIdEqualTo(familyId)
                .andStatDateValueEqualTo(MyDateUtil.getDateDayValue(date))
                .andAppIdEqualTo(appId);
        if (CollectionUtils.isNotEmpty(roomIds)) {
            criteria.andRoomIdIn(roomIds);
        }

        PageList<WcDataPayRoomDay> list = payRoomDayMapper.pageByExample(example, 1, topN);
        return list.stream().map(v->new RoomIncomeDTO()
                .setNjId(v.getRoomId())
                .setIncome(v.getIncome()))
                .collect(Collectors.toList());
    }

    /**
     * 查询公会主播topN收入
     * @param appId
     * @param familyId
     * @param roomIds
     * @param date
     * @param rankType
     * @param topN
     * @return
     */
    public List<IncomeStatPo> getTopNPlayerIncomeForGuild(Integer appId, Long familyId, List<Long> roomIds, Date date, OrderType rankType, Integer topN){
        if (dataCenterConfig.isRankQueryPay()) {
            List<Long> queryRoomIds = roomIds;
            if (CollectionUtils.isEmpty(queryRoomIds)) {
                //查询所有签约厅
                List<RoomSignBean> roomList = contractRemote.getAllSingGuildRoomsList(familyId);
                queryRoomIds = roomList.stream().map(UserBean::getId).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(queryRoomIds)) {
                LogContext.addResLog("queryRoomIds is empty");
                return Collections.emptyList();
            }
            List<IncomeStatPo> incomeStatPos = new CopyOnWriteArrayList<>();
            CountDownLatchWrapper roomLatch = new CountDownLatchWrapper(ThreadConstants.rankDatePool, queryRoomIds.size());
            for (Long roomId : queryRoomIds) {
                roomLatch.submit(()->{
                    Map<Long, Long> incomeMap = batchGetPlayerIncomeMap(familyId, roomId, date);
                    if (MapUtils.isNotEmpty(incomeMap)) {
                        for (Map.Entry<Long, Long> entry : incomeMap.entrySet()) {
                            IncomeStatPo incomeStatPo = new IncomeStatPo();
                            incomeStatPo.setNjId(roomId);
                            incomeStatPo.setUserId(entry.getKey());
                            incomeStatPo.setIncome(entry.getValue());
                            incomeStatPos.add(incomeStatPo);
                        }
                    }
                });
            }
            roomLatch.await();
            return incomeStatPos;
        }


        WcDataPayPlayerDayExample example = new WcDataPayPlayerDayExample();
        if (rankType == OrderType.DESC) {
            // 收入倒序
            example.setOrderByClause("income desc,id");
        } else {
            // 升序
            example.setOrderByClause("income asc,id");
        }

        WcDataPayPlayerDayExample.Criteria criteria = example.createCriteria();
        criteria.andIncomeGreaterThan(BigDecimal.ZERO)
                .andFamilyIdEqualTo(familyId)
                .andStatDateValueEqualTo(MyDateUtil.getDateDayValue(date))
                .andAppIdEqualTo(appId);
        if (CollectionUtils.isNotEmpty(roomIds)) {
            criteria.andRoomIdIn(roomIds);
        }

        PageList<WcDataPayPlayerDay> list = payPlayerDayMapper.pageByExample(example, 1, topN);
        if (log.isDebugEnabled()) {
            log.debug("getTopNPlayerIncome:{}", JsonUtil.dumps(list));
        }

        return RankDataConvert.I.dataPayPlayerDays2IncomeStatPos(list);
    }

    /**
     * 查询前topN收入的主播
     * @return
     */
    public Map<Long, Long> getTopNPlayerIncome(Integer appId, Long familyId, Long roomId, Date date, OrderType rankType, Integer topN){
        // 当天的查询支付 或者 查询实际统计的数据
        if (dataCenterConfig.isRankQueryPay()) {
            Map<Long, Long> dataMap = batchGetPlayerIncomeMap(familyId, roomId, new Date());
            // 排序并返回topN
            return sortTopMap(dataMap, rankType, topN);
        }

        WcDataPayPlayerDayExample example = new WcDataPayPlayerDayExample();
        if (rankType == OrderType.DESC) {
            // 收入倒序
            example.setOrderByClause("income desc,id");
        } else {
            // 升序
            example.setOrderByClause("income asc,id");
        }

        example.createCriteria()
                .andIncomeGreaterThan(BigDecimal.ZERO)
                .andFamilyIdEqualTo(familyId)
                .andRoomIdEqualTo(roomId)
                .andStatDateValueEqualTo(MyDateUtil.getDateDayValue(date))
                .andAppIdEqualTo(appId);

        PageList<WcDataPayPlayerDay> list = payPlayerDayMapper.pageByExample(example, 1, topN);
        if (log.isDebugEnabled()) {
            log.debug("getTopNPlayerIncome:{}", JsonUtil.dumps(list));
        }
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(WcDataPayPlayerDay::getPlayerId, v->v.getIncome().longValue()));
    }

    private Map<Long, Long> batchGetPlayerIncomeMap(Long familyId, Long roomId, Date date){
        //查询所有签约主播
        List<Long> playerIds = contractRemote.getAllSignRoomPlayerIds(roomId);
        if (CollectionUtils.isEmpty(playerIds)) {
            log.info("playerIds is empty");
            return Collections.emptyMap();
        }

        //并发查询所有主播的考核业绩
        Date dayStart = DateUtil.getDayStart(date);
        Date dayEnd = DateUtil.getDayEnd(date);
        List<List<Long>> listGroup = Lists.partition(playerIds, BATCH_QUERY_PLAYER_PAGE_SIZE);
        Map<Long, Long> incomeMap = new HashMap<>();
        for (List<Long> batchIds : listGroup) {
            incomeMap.putAll(batchGetIncomeByPlayer(familyId, roomId, batchIds, dayStart, dayEnd, PaySettleConfigCodeEnum.ANCHOR_INCOME_TOTAL_AMOUNT));
        }
        return incomeMap;
    }

    /**
     * map排序
     * @param dataMap
     * @param rankType
     * @param topN
     * @return
     */
    private Map<Long, Long> sortTopMap(Map<Long, Long> dataMap, OrderType rankType, Integer topN){
        if (MapUtils.isEmpty(dataMap)) {
            return Collections.emptyMap();
        }

        if (rankType == OrderType.DESC) {
            //倒序
            return dataMap.entrySet().stream()
                    .filter(entry -> entry.getValue() != 0) // 过滤0值
                    .sorted(Map.Entry.<Long, Long>comparingByValue().reversed())
                    .limit(topN)
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (e1, e2) -> e1,
                            LinkedHashMap::new
                    ));
        } else {
            return dataMap.entrySet().stream()
                    .filter(entry -> entry.getValue() != 0) // 过滤0值
                    .sorted(Map.Entry.<Long, Long>comparingByValue())
                    .limit(topN)
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (e1, e2) -> e1,
                            LinkedHashMap::new
                    ));
        }
    }

    /**
     * 查询账户类型
     * @param accountCode
     * @return
     */
    public Optional<String> getAccountType(String accountCode){
        PayAccountConfig payAccountConfig = incomeConfig.getBizConfig().getPayAccountConfig();
        String type = payAccountConfig.getAccountTypeMap().get(accountCode);
        return Optional.ofNullable(type);
    }

    /**
     * 查询收入项目配置
     * @param tenantCode
     * @param configCode
     * @return
     */
    public Optional<ConfigCodeBizInfoDTO> getConfigCodeBizInfo(String tenantCode, String configCode){
        CreatorDataQueryProto.ConfigCodeBizInfoRequest.Builder requestBuilder = CreatorDataQueryProto.ConfigCodeBizInfoRequest.newBuilder()
                .setTenantCode(tenantCode)
                .setConfigCode(configCode);
        log.info("getConfigCodeBizInfo tenantCode={},configCode={}", tenantCode, configCode);
        Result<CreatorDataQueryProto.ResponseConfigCodeBizInfo> result = creatorDataQueryService.getConfigCodeBizInfo(requestBuilder.build(), DateUtil.formatDateToString(new Date(), DateUtil.NORMAL_DATE_FORMAT));
        if (RpcResult.isFail(result)) {
            log.error("getConfigCodeBizInfo error. rCode={}", result.rCode());
            return Optional.empty();
        }

        CreatorDataQueryProto.ConfigCodeBizInfoResponse info = result.target().getInfo();
        ConfigCodeBizInfoDTO dto = new ConfigCodeBizInfoDTO()
                .setAccountCode(info.getAccountCode())
                .setAccountEngineCode(info.getAccountEngineCode())
                .setBizIdList(info.getBizIdListList());
        log.info("getConfigCodeBizInfo dto={}", JsonUtil.dumps(dto));

        return Optional.of(dto);
    }

    /**
     * 查询厅当前考核周期考核信息
     *
     * @param familyId
     * @param roomId
     * @return
     */
    public Optional<PerformanceInfoDto> getPerformanceInfo(Long familyId, Long roomId) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        String periodType = PeriodTypeEnum.CURRENT_PERIOD.getPeriodType();

        log.info("getHallPerformanceInfo. appId={},roomId={},familyId={},periodType={}", appId, roomId, familyId, periodType);
        Result<CreatorDataQueryProto.ResponseGetHallPerformanceInfo> result = creatorDataQueryService.getHallPerformanceInfo(CreatorDataQueryProto.GetHallPerformanceInfoRequest.newBuilder()
                .setTenantCode(PayTenantCodeEnum.getPayTenantCode(appId))
                .setHallId(roomId)
                .setFamilyId(familyId)
                .setPeriodType(periodType)
                .build(), DateUtil.formatDateToString(new Date(), DateUtil.NORMAL_DATE_FORMAT));

        if (RpcResult.isFail(result)) {
            log.warn("getHallPerformanceInfo fail. rCode={}", result.rCode());
            return Optional.empty();
        }

        CreatorDataQueryProto.GetHallPerformanceInfoResponse performanceInfo = result.target().getInfo();
        PerformanceInfoDto dto = new PerformanceInfoDto()
                .setAmount(performanceInfo.getAmount())
                .setCurrentLevel(String.valueOf(performanceInfo.getCurrentLevelIndex()))
                .setNextLevelAmount(performanceInfo.getNextLevelNeedAmount())
                .setCurrentLevelBonus(performanceInfo.getCurrentLevelBonusDisplay());

        log.info("getHallPerformanceInfo dto={}", JsonUtil.dumps(dto));
        return Optional.of(dto);
    }

    /**
     * 获取公会前后期收入
     *
     * @param familyId
     * @param configCodeEnum
     * @return
     */
    public IncomeBean getIncomeBeanByFamily(Long familyId, List<Long> roomIds, PaySettleConfigCodeEnum configCodeEnum) {
        long currentIncome = queryTradeStatisticsValue(familyId, roomIds, configCodeEnum, PeriodTypeEnum.CURRENT_PERIOD);
        long preIncome = queryTradeStatisticsValue(familyId, roomIds, configCodeEnum, PeriodTypeEnum.PRE_PERIOD);
        String currentIncomeStr = String.valueOf(currentIncome);
        String preIncomeStr = String.valueOf(preIncome);
        return new IncomeBean()
                .setCurrent(currentIncomeStr)
                .setPer(preIncomeStr)
                .setRatio(CalculateUtil.relativeRatio(preIncomeStr, currentIncomeStr));
    }

    /**
     * 获取公会前后期收入
     *
     * @param familyId
     * @param configCodeEnum
     * @return
     */
    public IncomeBean getIncomeBeanByFamily(Long familyId, List<Long> roomIds, Date startDate, Date endDate, PaySettleConfigCodeEnum configCodeEnum, DateType dateType) {
        Date startDateBefore = null;
        Date endDateBefore = null;
        switch (dateType) {
            case DAY:
                startDateBefore = DateUtil.getDayBefore(startDate, 1);
                endDateBefore = DateUtil.getDayBefore(endDate, 1);
                break;
            case WEEK:
                startDateBefore = DateUtil.getDayBefore(startDate, 7);
                endDateBefore = DateUtil.getDayBefore(endDate, 7);
                break;
            case MONTH:
                startDateBefore = DateUtil.getMonthBefore(startDate, 1);
                endDateBefore = DateUtil.getMonthBefore(endDate, 1);
                break;
        }


        long currentIncome = queryTradeStatisticsValue(familyId, roomIds, null, startDate, endDate, configCodeEnum, null);
        long preIncome = queryTradeStatisticsValue(familyId, roomIds, null, startDateBefore, endDateBefore, configCodeEnum, null);
        String currentIncomeStr = String.valueOf(currentIncome);
        String preIncomeStr = String.valueOf(preIncome);
        return new IncomeBean()
                .setCurrent(currentIncomeStr)
                .setPer(preIncomeStr)
                .setRatio(CalculateUtil.relativeRatio(preIncomeStr, currentIncomeStr));
    }

    /**
     * 查询陪玩前后期收入
     *
     * @param familyId
     * @param roomId
     * @param playerId
     * @param configCodeEnum
     * @return
     */
    public IncomeBean getIncomeBeanByPlayer(Long familyId, Long roomId, Long playerId, PaySettleConfigCodeEnum configCodeEnum) {
        long currentIncome = queryTradeStatisticsValue(familyId, roomId, playerId, configCodeEnum, PeriodTypeEnum.CURRENT_PERIOD);
        long preIncome = queryTradeStatisticsValue(familyId, roomId, playerId, configCodeEnum, PeriodTypeEnum.PRE_PERIOD);
        String currentIncomeStr = String.valueOf(currentIncome);
        String preIncomeStr = String.valueOf(preIncome);
        return new IncomeBean()
                .setCurrent(currentIncomeStr)
                .setPer(preIncomeStr)
                .setRatio(CalculateUtil.relativeRatio(preIncomeStr, currentIncomeStr));
    }

    /**
     * 查询厅前后期收入
     *
     * @param familyId
     * @param roomId
     * @param configCodeEnum
     * @return
     */
    public IncomeBean getIncomeBeanByRoom(Long familyId, Long roomId, PaySettleConfigCodeEnum configCodeEnum) {
        long currentIncome = queryTradeStatisticsValue(familyId, roomId, null, configCodeEnum, PeriodTypeEnum.CURRENT_PERIOD);
        long preIncome = queryTradeStatisticsValue(familyId, roomId, null, configCodeEnum, PeriodTypeEnum.PRE_PERIOD);
        String currentIncomeStr = String.valueOf(currentIncome);
        String preIncomeStr = String.valueOf(preIncome);
        return new IncomeBean()
                .setCurrent(currentIncomeStr)
                .setPer(preIncomeStr)
                .setRatio(CalculateUtil.relativeRatio(preIncomeStr, currentIncomeStr));
    }

    /**
     * 查询厅前后期收入
     *
     * @param familyId
     * @param roomId
     * @param configCodeEnum
     * @return
     */
    public IncomeBean getIncomeBeanByRoom(Long familyId, Long roomId,  Date startDate, Date endDate, PaySettleConfigCodeEnum configCodeEnum, DateType dateType) {
        Date startDateBefore = null;
        Date endDateBefore = null;
        switch (dateType) {
            case DAY:
                startDateBefore = DateUtil.getDayBefore(startDate, 1);
                endDateBefore = DateUtil.getDayBefore(endDate, 1);
                break;
            case WEEK:
                startDateBefore = DateUtil.getDayBefore(startDate, 7);
                endDateBefore = DateUtil.getDayBefore(endDate, 7);
                break;
            case MONTH:
                startDateBefore = DateUtil.getMonthBefore(startDate, 1);
                endDateBefore = DateUtil.getMonthBefore(endDate, 1);
                break;
        }

        long currentIncome = queryTradeStatisticsValue(familyId, roomId, null, startDate, endDate,  configCodeEnum);
        long preIncome = queryTradeStatisticsValue(familyId, roomId, null, startDateBefore, endDateBefore, configCodeEnum);
        String currentIncomeStr = String.valueOf(currentIncome);
        String preIncomeStr = String.valueOf(preIncome);
        return new IncomeBean()
                .setCurrent(currentIncomeStr)
                .setPer(preIncomeStr)
                .setRatio(CalculateUtil.relativeRatio(preIncomeStr, currentIncomeStr));
    }

    /**
     * 批量查询厅的当前考核周期收入
     * 支付暂时没有时间提供批量接口，先多线程查询
     *
     * @param familyId
     * @param roomIds
     * @return
     */
    public Map<Long, Long> batchGetIncomeByRoom(Long familyId, List<Long> roomIds, Date startDate, Date endDate, List<Integer> bizIds) {
        if (CollectionUtils.isEmpty(roomIds)) {
            return Collections.emptyMap();
        }
        Map<Long, Long> result = new HashMap<>();
        CountDownLatchWrapper countDownLatchWrapper = new CountDownLatchWrapper(executorService, roomIds.size());
        for (Long roomId : roomIds) {
            countDownLatchWrapper.submit(() -> {
                long value = queryHallIncomeDetailTotal(familyId, roomId, startDate, endDate, bizIds);
                result.put(roomId, value);
            });
        }
        return countDownLatchWrapper.await() ? result : Collections.emptyMap();
    }

    /**
     * 批量查询陪玩收入
     *
     * @param familyId
     * @param roomId
     * @param playerIds
     * @param configCodeEnum
     * @return
     */
    public Map<Long, Long> batchGetIncomeByPlayer(Long familyId, Long roomId, List<Long> playerIds, Date startDate, Date endDate, PaySettleConfigCodeEnum configCodeEnum) {
        if (CollectionUtils.isEmpty(playerIds)) {
            return Collections.emptyMap();
        }
        log.info("batchGetIncomeByPlayer. playerIdSize={}", playerIds.size());
        Map<Long, Long> result = new HashMap<>();
        CountDownLatchWrapper countDownLatchWrapper = new CountDownLatchWrapper(executorService, playerIds.size());
        for (Long playerId : playerIds) {
            countDownLatchWrapper.submit(() -> {
                long value = queryTradeStatisticsValue(familyId, roomId, playerId, startDate, endDate, configCodeEnum);
                result.put(playerId, value);
            });
        }
        return countDownLatchWrapper.await() ? result : Collections.emptyMap();
    }

    public Long queryAssetSumInRange(long userId, long beginTime, long endTime, int direction, String bizTag) {
        String accountCode = incomeConfig.getBizConfig().getPlayerIncomeCode();
        String bizIds = incomeConfig.getBizConfig().getPlayerIncomeBizIds();
        Result<QueryAccountProto.ResponseQueryAssestSumInRange> sumInRangeResult =
                accountQueryService.queryAssestSumInRange("5022031727227306035", String.valueOf(userId),
                        "LIZHI", beginTime, endTime, direction, accountCode, bizTag, bizIds);
        if (RpcResult.isFail(sumInRangeResult)) {
            log.warn("queryAssestSumInRange,rcode={}, userId={}, beginTime={}, endTime={}, direction={}, bizTag={},start={},end={},accountCode={},bizIds={}",
                    sumInRangeResult.rCode(), userId, beginTime, endTime, direction, bizTag, DateUtil.formatDateNormal(beginTime), DateUtil.formatDateNormal(endTime), accountCode, bizIds);
            return 0L;
        }
        return sumInRangeResult.target().getSum();
    }


    /**
     * 批量查询用户一段时间内的资产总和（分bizId汇总）
     * 分批次查询。每次100个用户
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @param direction 统计类型 0 支出 1 收入 2 全部
     * @param njIds     主播ID列表
     * @param bizIds    待统计的bizId列表
     * @return 用户资产汇总
     */
    public List<QueryAccountProto.HistorySummary> batchQueryHistorySumInRange(long beginTime, long endTime, int direction, List<Long> njIds, List<Integer> bizIds) {
        List<QueryAccountProto.HistorySummary> result = Lists.newArrayList();
        Lists.partition(njIds, 100).forEach(batch -> {
            result.addAll(getHistorySummaries(beginTime, endTime, direction, batch, bizIds));
        });
        return result;
    }


    /**
     * 批量查询用户一段时间内的资产总和（分bizId汇总）
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @param direction 查询方向
     * @param njIds     用户id列表
     * @param bizIds    资产类型
     * @return 用户资产汇总
     */
    private List<QueryAccountProto.HistorySummary> getHistorySummaries(long beginTime, long endTime, int direction, List<Long> njIds, List<Integer> bizIds) {
        log.info("getHistorySummaries beginTime={}, endTime={}, direction={}, bizIds={}", beginTime, endTime, direction, bizIds);
        // 生成账号信息
        List<QueryAccountProto.AccountInfo> accountInfos = njIds.stream().map(njId -> QueryAccountProto.AccountInfo.newBuilder().
                setAccountNickCode("ACCOUNT_LIZHI_FROZEN").setCurrency("LIZHI").
                setIdentity(String.valueOf(njId)).build()).collect(Collectors.toList());
        // 批量查询账号历史汇总
        Result<QueryAccountProto.ResponseBatchQueryHistorySumInRange> result =
                accountQueryService.batchQueryHistorySumInRange(PayAppIdEnum.getPayAppId(ContextUtils.getBusinessEvnEnum()), beginTime, endTime, direction, accountInfos, bizIds);
        if (result.rCode() != 0) {
            log.warn("batchQueryHistorySumInRange,rcode={}", result.rCode());
            return Collections.emptyList();
        }
        return result.target().getHistorySummaryList();
    }


    /**
     * 查询厅今日收入
     * @param familyId
     * @param roomIds
     * @return
     */
    public List<RoomPerformanceStatisticsBean> queryHallPerformanceStatistics(Long familyId, List<Long> roomIds){

        int appId = ContextUtils.getBusinessEvnEnum().getAppId();

        CreatorDataQueryProto.QueryHallPerformanceStatisticsRequest.Builder builder = CreatorDataQueryProto
                .QueryHallPerformanceStatisticsRequest.newBuilder()
                .setFamilyId(familyId)
                .setTenantCode(PayTenantCodeEnum.getPayTenantCode(appId))
                .setPeriodType(PeriodTypeEnum.TODAY.getPeriodType());

        if (CollectionUtils.isNotEmpty(roomIds)) {
            builder.addAllManageHallId(roomIds);
        }

        Result<CreatorDataQueryProto.ResponseQueryHallPerformanceStatistics> result = creatorDataQueryService.queryHallPerformanceStatistics(builder.build(),
                DateUtil.formatDateToString(new Date(), DateUtil.NORMAL_DATE_FORMAT));
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("queryHallPerformanceStatistics error. familyId: {}, appId: {}, rCode:{}", familyId, appId, result.rCode());
            return Collections.emptyList();
        }
        CreatorDataQueryProto.ResponseQueryHallPerformanceStatistics performanceStatistics = result.target();
        List<CreatorDataQueryProto.QueryHallPerformanceStatisticsResponse> dataList = performanceStatistics.getDataList();

        return dataList.stream().map(data -> new RoomPerformanceStatisticsBean()
                .setRoomId(data.getHallId()).setSumIncome(data.getStatisticsValue()))
                .collect(Collectors.toList());

    }

    /**
     * 获取收入统计
     *
     * @param familyId
     * @param roomId
     * @param playerId
     * @param periodType
     * @return
     */
    public long queryTradeStatisticsValue(Long familyId, Long roomId, Long playerId, PaySettleConfigCodeEnum configCodeEnum, PeriodTypeEnum periodType) {
        return queryTradeStatisticsValue(familyId, Collections.singletonList(roomId), playerId, null, null, configCodeEnum, periodType);
    }

    public long queryTradeStatisticsValue(Long familyId, List<Long> roomIds, PaySettleConfigCodeEnum configCodeEnum, PeriodTypeEnum periodType) {
        return queryTradeStatisticsValue(familyId, roomIds, null, null, null, configCodeEnum, periodType);
    }

    public long queryTradeStatisticsValue(Long familyId, Long roomId, Long playerId, Date startDate, Date endDate, PaySettleConfigCodeEnum configCodeEnum) {
        return queryTradeStatisticsValue(familyId, Lists.newArrayList(roomId), playerId, startDate, endDate, configCodeEnum, null);
    }

    public long queryTradeStatisticsValue(Long familyId, PaySettleConfigCodeEnum configCodeEnum, PeriodTypeEnum periodType) {
        return queryTradeStatisticsValue(familyId, null, null, null, null, configCodeEnum, periodType);
    }

    public long queryTradeStatisticsValue(Long familyId, Long playerId, PaySettleConfigCodeEnum configCodeEnum, PeriodTypeEnum periodType) {
        return queryTradeStatisticsValue(familyId, null, playerId, null, null, configCodeEnum, periodType);
    }
    public long queryFamilyIncomeByDateScope(Long familyId, Date startDate, Date endDate, PaySettleConfigCodeEnum configCodeEnum) {
        return queryTradeStatisticsValue(familyId, null, null, startDate, endDate, configCodeEnum, PeriodTypeEnum.CUSTOMIZE);
    }

    public long queryTradeStatisticsValue(Long familyId, List<Long> roomIds, Long playerId
            , Date startDate, Date endDate
            , PaySettleConfigCodeEnum configCodeEnum, PeriodTypeEnum periodType) {
        try {
            int appId = ContextUtils.getBusinessEvnEnum().getAppId();
            String tenantCode = PayTenantCodeEnum.getPayTenantCode(appId);
            String configCode = configCodeEnum.getConfigCode();
            String startStr = DateUtil.formatDateToString(startDate, DateUtil.NORMAL_DATE_FORMAT);
            String endStr = DateUtil.formatDateToString(DateUtil.getDayEnd(endDate), DateUtil.NORMAL_DATE_FORMAT);
            PeriodTypeEnum periodTypeEnum = periodType == null ? PeriodTypeEnum.CUSTOMIZE : periodType;

            log.info("queryTradeStatisticsValue req tenantCode={}, configCode={}, familyId={}, roomIds={}, playerId={}, startStr={}, endStr={}, periodTypeEnum={}"
                    , tenantCode, configCode, familyId, JsonUtil.dumps(roomIds), playerId, startStr, endStr, periodTypeEnum.getPeriodType());

            CreatorDataQueryProto.QueryTradeStatisticsValueRequest.Builder builder = CreatorDataQueryProto.QueryTradeStatisticsValueRequest.newBuilder()
                    .setTenantCode(tenantCode)
                    .setConfigCode(configCode)
                    .setFamilyId(familyId)
                    .setPeriodType(periodTypeEnum.getPeriodType());
            Optional.ofNullable(playerId).ifPresent(builder::setAnchorId);
            if (StringUtils.isNotBlank(startStr)) {
                builder.setStartTime(startStr);
            }
            if (StringUtils.isNotBlank(endStr)) {
                builder.setEndTime(endStr);
            }
            if (CollectionUtils.isNotEmpty(roomIds)) {
                builder.addAllManageHallId(roomIds);
            }

            Result<CreatorDataQueryProto.ResponseQueryTradeStatisticsValue> result = creatorDataQueryService.queryTradeStatisticsValue(builder.build()
                    , DateUtil.formatDateToString(new Date(), DateUtil.NORMAL_DATE_FORMAT));
            if (RpcResult.isFail(result)) {
                log.warn("queryTradeStatisticsValue fail. rCode={}", result.rCode());
                return 0L;
            }

            long statisticsValue = result.target().getStatisticsValue();
            log.info("queryTradeStatisticsValue response tenantCode={}, configCode={}, familyId={}, roomIds={}, playerId={}, startStr={}, endStr={}, periodTypeEnum={}, statisticsValue={}"
                    , tenantCode, configCode, familyId, JsonUtil.dumps(roomIds), playerId, startStr, endStr, periodTypeEnum.getPeriodType(), statisticsValue);
            return statisticsValue;
        } catch (Exception e) {
            log.error("queryTradeStatisticsValue error:", e);
            return 0L;
        }
    }

    /**
     * 获取收入统计
     *
     * @param familyId
     * @param roomId
     * @return
     */
    public long queryHallIncomeDetailTotal(Long familyId, Long roomId, Date startDate, Date endDate, List<Integer> bizIds) {
        try {
            int appId = ContextUtils.getBusinessEvnEnum().getAppId();
            String tenantCode = PayTenantCodeEnum.getPayTenantCode(appId);
            String startStr = DateUtil.formatDateToString(DateUtil.getDayStart(startDate), DateUtil.NORMAL_DATE_FORMAT);
            String endStr = DateUtil.formatDateToString(DateUtil.getDayEnd(endDate), DateUtil.NORMAL_DATE_FORMAT);
            log.info("queryHallIncomeDetailTotal req tenantCode={}, familyId={}, roomId={}, startStr={}, endStr={}, bizIds={}"
                    , tenantCode, familyId, roomId, startStr, endStr, JsonUtil.dumps(bizIds)
            );

            CreatorDataQueryProto.QueryHallIncomeDetailRequest.Builder builder = CreatorDataQueryProto.QueryHallIncomeDetailRequest.newBuilder()
                    .setTenantCode(tenantCode)
                    .setFamilyId(familyId)
                    .setHallId(roomId)
                    .setTimeStart(startStr)
                    .setTimeEnd(endStr)
                    .setDirection(DirectionEnum.ALL.getCode())
                    .setPage(CreatorDataQueryProto.Page.newBuilder().setPageSize(10).setPageNumber(1).build());
            if (CollectionUtils.isNotEmpty(bizIds)) {
                builder.addAllBizId(bizIds.stream().map(String::valueOf).collect(Collectors.toSet()));
            }

            Result<CreatorDataQueryProto.ResponseQueryHallIncomeDetailTotal> result = creatorDataQueryService.queryHallIncomeDetailTotal(builder.build()
                    , DateUtil.formatDateToString(new Date(), DateUtil.NORMAL_DATE_FORMAT));
            if (RpcResult.isFail(result)) {
                log.warn("queryHallIncomeDetailTotal fail. rCode={}", result.rCode());
                return 0L;
            }
            String statisticsValue = result.target().getStatisticsValue();
            log.info("queryHallIncomeDetailTotal response tenantCode={}, familyId={}, roomId={}, startStr={}, endStr={}, bizIds={}, statisticsValue={}"
                    , tenantCode, familyId, roomId, startStr, endStr, JsonUtil.dumps(bizIds), statisticsValue
            );
            return new BigDecimal(statisticsValue).longValue();
        } catch (Exception e) {
            log.error("queryHallIncomeDetailTotal error, familyId={}, bizId={}",
                    familyId, JsonUtil.dumps(bizIds), e);
            return 0;
        }
    }

    /**
     * 获取厅可结算余额
     *
     * @param appId    应用ID
     * @param familyId 家族ID
     * @param roomId   房间ID
     * @return 可结算余额
     */
    public long getHallSettleAbleAmount(Integer appId, Long familyId, Long roomId) {
        return getHallSettleAbleAmount(appId, familyId, Collections.singletonList(roomId));
    }

    public long getHallSettleAbleAmount(Integer appId, Long familyId, List<Long> roomIds) {
        try {
            String tenantCode = PayTenantCodeEnum.getPayTenantCode(appId);
            String requestTime = DateUtil.formatDateToString(new Date(), DateUtil.NORMAL_DATE_FORMAT);
            log.info("getHallSettleAbleAmount req familyId={}, tenantCode={}, roomIds={}", familyId, tenantCode, JsonUtil.dumps(roomIds));

            CreatorDataQueryProto.GetHallSettleableAmountRequest.Builder builder = CreatorDataQueryProto.GetHallSettleableAmountRequest.newBuilder()
                    .setTenantCode(tenantCode)
                    .setFamilyId(familyId);
            if (CollectionUtils.isNotEmpty(roomIds)) {
                builder.addAllManageHallId(roomIds);
            }
            Result<CreatorDataQueryProto.ResponseGetHallSettleableAmount> canSettlementResult = creatorDataQueryService.getHallSettleableAmount(builder.build(), requestTime);
            if (RpcResult.isFail(canSettlementResult)) {
                log.warn("getHallSettleAbleAmount req is fail. familyId={}, tenantCode={}, roomIds={}, canSettlementResultRCode: {}"
                        , familyId, tenantCode, JsonUtil.dumps(roomIds), canSettlementResult.rCode());
                return 0;
            }

            long statisticsValue = canSettlementResult.target().getStatisticsValue();
            log.info("getHallSettleAbleAmount req response. familyId={}, tenantCode={}, roomIds={}, statisticsValue={}", familyId, tenantCode, JsonUtil.dumps(roomIds), statisticsValue);
            return statisticsValue;
        } catch (Exception e) {
            log.error("getHallSettleAbleAmount req error:", e);
            return 0;
        }
    }

    /**
     * 异步批量查询收入统计
     *
     * @param familyId   家族ID
     * @param roomId     房间ID
     * @param periodType 时间周期
     * @param configList 收入配置code列表
     * @return 收入信息
     */
    public IncomeSummaryThreadBean asyncBatchQueryTrade(Long familyId, Long roomId, PeriodTypeEnum periodType, List<PaySettleConfigCodeEnum> configList) {
        return asyncBatchQueryTrade(familyId, Collections.singletonList(roomId), periodType, configList);
    }

    public IncomeSummaryThreadBean asyncBatchQueryTrade(Long familyId, List<Long> roomIds, PeriodTypeEnum periodType, List<PaySettleConfigCodeEnum> configList) {
        // 创建一个CountDownLatch实例，传入任务数量
        IncomeSummaryThreadBean summaryThreadBean = new IncomeSummaryThreadBean();
        CountDownLatchWrapper countDownLatchWrapper = new CountDownLatchWrapper(executorService, configList.size());
        for (PaySettleConfigCodeEnum config : configList) {
            countDownLatchWrapper.submit(() -> {
                try {
                    long income = queryTradeStatisticsValue(familyId, roomIds, config, periodType);
                    fillIncomeBeanData(config, summaryThreadBean, income);
                } catch (Exception e) {
                    log.error("queryTradeStatisticsValue error:", e);
                }
            });
        }
        countDownLatchWrapper.await();
        return summaryThreadBean;
    }

    /**
     * 补全收入信息
     *
     * @param config            收入配置code
     * @param summaryThreadBean 收入信息
     * @param income            收入
     */
    private void fillIncomeBeanData(PaySettleConfigCodeEnum config, IncomeSummaryThreadBean summaryThreadBean, long income) {
        switch (config) {
            case FAMILY_HALL_INCOME_TOTAL_AMOUNT:
            case HALL_HALL_INCOME_TOTAL_AMOUNT:
                summaryThreadBean.setRoomGift(income);
                break;
            case FAMILY_INDIVIDUAL_INCOME_TOTAL_AMOUNT:
            case HALL_INDIVIDUAL_INCOME_TOTAL_AMOUNT:
                summaryThreadBean.setPlayer(income);
                break;
            case FAMILY_NOBILITY_HALL_ROYALTY_INCOME_TOTAL_AMOUNT:
            case HALL_NOBILITY_ROYALTY_INCOME_TOTAL_AMOUNT:
                summaryThreadBean.setRoomVip(income);
                break;
            case FAMILY_NOBILITY_PERSONAL_ROYALTY_INCOME_TOTAL_AMOUNT:
            case HALL_NOBILITY_PERSONAL_ROYALTY_INCOME_TOTAL_AMOUNT:
                summaryThreadBean.setPlayerVip(income);
                break;
            case FAMILY_OFFICIAL_HALL_INCOME_TOTAL_AMOUNT:
            case HALL_OFFICIAL_HALL_INCOME_TOTAL_AMOUNT:
                summaryThreadBean.setOfficialRoom(income);
                break;
            case FAMILY_INCOME_TOTAL_AMOUNT:
            case HALL_INCOME_TOTAL_AMOUNT:
                summaryThreadBean.setSum(income);
                break;
        }
    }

    public List<PlayerIncomeStatisticsBean> getAllAnchorIncomeStatisticsValue(long roomId, long familyId, int appId, List<Long> allContractUserIds) {

        String tenantCode = PayTenantCodeEnum.getPayTenantCode(appId);
        String requestTime = DateUtil.formatDateToString(new Date(), DateUtil.NORMAL_DATE_FORMAT);
        if (allContractUserIds.size() > incomeConfig.getQueryPayPageSize()) {
            List<PlayerIncomeStatisticsBean> playerIncomeStatisticsPos = new CopyOnWriteArrayList<>();
            List<List<Long>> partition = Lists.partition(allContractUserIds, incomeConfig.getQueryPayPageSize());
            CountDownLatchWrapper latchWrapper = new CountDownLatchWrapper(executorService, partition.size());
            for (List<Long> ids : partition) {
                latchWrapper.submit(()->{
                    List<PlayerIncomeStatisticsBean> list = getPlayerIncomeStatisticsPos(roomId, familyId, tenantCode, requestTime, ids);
                    playerIncomeStatisticsPos.addAll(list);
                });
            }
            latchWrapper.await();
            return playerIncomeStatisticsPos;
        }

        return getPlayerIncomeStatisticsPos(roomId, familyId, tenantCode, requestTime, allContractUserIds);

    }

    private List<PlayerIncomeStatisticsBean> getPlayerIncomeStatisticsPos(long roomId, long familyId, String tenantCode, String requestTime, List<Long> contractUserIds) {
        CreatorDataQueryProto.QueryHallAnchorPerformanceRequest queryHallAnchorPerformanceRequest =
                CreatorDataQueryProto.QueryHallAnchorPerformanceRequest.newBuilder()
                        .setHallId(roomId)
                        .setFamilyId(familyId)
                        .setTenantCode(tenantCode)
                        .addAllAnchorId(contractUserIds)
                        .build();
        log.info("getAnchorIncomeStatisticsValue roomId={},familyId={},tenantCode={},requestTime={},contractUserIds={}", roomId, familyId, tenantCode, requestTime, JsonUtil.dumps(contractUserIds));
        Result<CreatorDataQueryProto.ResponseQueryHallAnchorPerformance> queryHallAnchorPerformanceResult = creatorDataQueryService.queryHallAnchorPerformance(queryHallAnchorPerformanceRequest, requestTime);
        if (RpcResult.isFail(queryHallAnchorPerformanceResult)) {
            log.warn("getAnchorIncomeStatisticsValue is fail. familyId={}, roomId={}, tenantCode={}, canSettlementResultRCode: {}"
                    , familyId, roomId, tenantCode, queryHallAnchorPerformanceResult.rCode());
            return Collections.emptyList();
        }

        List<CreatorDataQueryProto.QueryHallAnchorPerformanceResponse> queryHallAnchorPerformanceResponses = queryHallAnchorPerformanceResult.target().getDataList();
        List<PlayerIncomeStatisticsBean> resultList = queryHallAnchorPerformanceResponses.stream().map(x ->
                new PlayerIncomeStatisticsBean()
                        .setUserId(x.getAnchorId()).setIncomeStatistics(Long.parseLong(x.getCurrentPeriodStatisticsValue()))
                        .setPreIncomeStatistics(Long.parseLong(x.getPrePeriodStatisticsValue())))
                .collect(Collectors.toList());

        log.info("getAnchorIncomeStatisticsValue roomId={},familyId={},tenantCode={},requestTime={},resultList={}", roomId, familyId, tenantCode, requestTime, JsonUtil.dumps(resultList));
        return resultList;
    }

}
