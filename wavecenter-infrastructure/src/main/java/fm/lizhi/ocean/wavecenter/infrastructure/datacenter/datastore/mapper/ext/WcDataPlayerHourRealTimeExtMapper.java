package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataPlayerHourRealTime;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcDataPlayerHourRealTimeExtMapper {

    @Select("<script>\n" +
            "  SELECT * FROM\n" +
            "    wavecenter_data_player_hour_real_time\n" +
            "  WHERE\n" +
            "    player_id IN\n" +
            "      <foreach collection=\"playerIds\" item=\"playerId\" open=\"(\" separator=\",\" close=\")\">\n" +
            "        #{playerId}\n" +
            "      </foreach>\n" +
            "    AND app_id = #{appId} AND stat_date = #{hourTime}" +
            "</script>")
    List<WcDataPlayerHourRealTime> getWcDataPlayerHourRealTimes(@Param("appId") Integer appId, @Param("playerIds") List<Long> playerIds,
                                                                @Param("hourTime") Date hourTime);
}
