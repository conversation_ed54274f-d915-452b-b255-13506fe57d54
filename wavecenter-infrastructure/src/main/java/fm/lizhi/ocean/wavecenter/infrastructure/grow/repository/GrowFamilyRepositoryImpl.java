package fm.lizhi.ocean.wavecenter.infrastructure.grow.repository;

import fm.lizhi.ocean.wavecenter.domain.grow.entity.FamilyLevel;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.Period;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.organization.GrowFamily;
import fm.lizhi.ocean.wavecenter.domain.grow.repository.GrowFamilyRepository;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.datastore.entity.WcFamilyLevelWeekRecord;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.datastore.mapper.WcFamilyLevelWeekRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/3/19 20:41
 */
@Slf4j
@Component
public class GrowFamilyRepositoryImpl implements GrowFamilyRepository {

    @Autowired
    private WcFamilyLevelWeekRecordMapper familyLevelWeekRecordMapper;

    @Override
    public void saveFamily(GrowFamily family, Period period) {
        Optional<FamilyLevel> levelOp = family.getLevel(period);
        if (!levelOp.isPresent()) {
            log.info("level is null. familyId={}", family.getId());
            return;
        }
        FamilyLevel level = levelOp.get();
        WcFamilyLevelWeekRecord record = new WcFamilyLevelWeekRecord();
        record.setLevelId(level.getId());
        record.setFamilyId(family.getId());
        record.setExp(family.getExp(period));
        record.setAppId(family.getAppId());
        record.setSettleStartTime(period.getStart());
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        record.setDeleted(0);
        familyLevelWeekRecordMapper.insert(record);
    }
}
