package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote.hy;

import com.google.common.collect.Lists;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RankBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PaySettleConfigCodeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.convert.DataCenterInfraConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.convert.RankDataConvert;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataPlayerRoomDay;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataPlayerRoomDayExample;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.HyPlayerSignCharmStatMapper;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.mapper.WcDataPlayerRoomDayMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.CharmStatPo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.IncomeStatPo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote.IRankDataRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.income.manager.PaymentManager;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.hy.HyContractRemote;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class HyRankDataRemote implements IRankDataRemote {

    @Autowired
    private HyPlayerSignCharmStatMapper hyPlayerSignCharmStatMapper;

    @Autowired
    private HyContractRemote contractRemote;

    @Autowired
    private PaymentManager paymentManager;

    @Autowired
    private WcDataPlayerRoomDayMapper playerRoomDayMapper;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.HEI_YE;
    }

    @Override
    public List<RankBean> guildPlayer(long familyId, List<Long> roomIds, Date date, OrderType rankType) {
        String dateStr = DateUtil.formatDateToString(date, DateUtil.date_2);

        List<IncomeStatPo> incomeStatPos = new ArrayList<>();
        if ( date != null && !DateUtil.isSameDay(date, new Date()) ) {
            // 历史查询大数据
            List<WcDataPlayerRoomDay> historyIncomes = getHistoryIncomes(familyId, roomIds, date, rankType, 10);
            incomeStatPos = RankDataConvert.I.dataPlayerRoomDays2IncomeStatPos(historyIncomes);
        } else {
            // 当天的查询支付
            incomeStatPos = paymentManager.getTopNPlayerIncomeForGuild(getBusinessEvnEnum().getAppId(), familyId, roomIds, date, rankType, 10);
        }

        if (CollectionUtils.isEmpty(incomeStatPos)) {
            LogContext.addResLog("incomeStatPos is empty");
            return Collections.emptyList();
        }
        List<IncomeStatPo> resultList = sort(incomeStatPos, rankType);

        //取出用户ID
        List<Long> userIdList = resultList.stream().map(IncomeStatPo::getUserId).distinct().collect(Collectors.toList());
        //取出主播ID
        List<Long> njList = resultList.stream().map(IncomeStatPo::getNjId).distinct().collect(Collectors.toList());
        //查询魅力值
        List<CharmStatPo> charmStatPos = hyPlayerSignCharmStatMapper.getGuildPlayerCharmInfo(dateStr, userIdList, njList);
        if (CollectionUtils.isEmpty(charmStatPos)) {
            LogContext.addResLog("charmStatPos is empty");
            return DataCenterInfraConvert.I.incomeStatPo2Beans(resultList);
        }

        //转成map
        Map<String, CharmStatPo> charmStatPoMap = charmStatPos.stream()
                .collect(Collectors.toMap(
                        charmStatPo -> charmStatPo.getUserId() + "_" + charmStatPo.getNjId(),
                        charmStatPo -> charmStatPo,
                        // 如果有重复的key，使用后面的对象替换前面的对象
                        (existing, replacement) -> replacement
                ));

        resultList.forEach(incomeStatPo -> {
            CharmStatPo charmStatPo = charmStatPoMap.get(incomeStatPo.getUserId() + "_" + incomeStatPo.getNjId());
            if (charmStatPo != null) {
                incomeStatPo.setCharm(charmStatPo.getValue());
            }
        });
        return DataCenterInfraConvert.I.incomeStatPo2Beans(resultList);
    }

    @Override
    public List<RankBean> roomPlayer(long njId, long familyId, Date date, OrderType rankType) {
        String dateStr = DateUtil.formatDateToString(date, DateUtil.date_2);

        //查询收入数据
        Map<Long, Long> incomeMap = getTopPlayerRankMap(familyId, njId, date, rankType, 10);
        if (MapUtils.isEmpty(incomeMap)) {
            return Collections.emptyList();
        }

        //排序
        List<IncomeStatPo> incomeStatPos = new ArrayList<>();
        for (Map.Entry<Long, Long> entry : incomeMap.entrySet()) {
            IncomeStatPo incomeStatPo = new IncomeStatPo();
            incomeStatPo.setUserId(entry.getKey());
            incomeStatPo.setIncome(entry.getValue());
            incomeStatPos.add(incomeStatPo);
        }
        List<IncomeStatPo> resultList = sort(incomeStatPos, rankType);

        //取出用户ID
        List<Long> userIdList = resultList.stream().map(IncomeStatPo::getUserId).distinct().collect(Collectors.toList());
        //查询魅力值
        List<CharmStatPo> charmStatPos = hyPlayerSignCharmStatMapper.roomPlayerCharmInfo(njId, dateStr, userIdList, rankType.getValue());
        if (CollectionUtils.isEmpty(charmStatPos)) {
            return DataCenterInfraConvert.I.incomeStatPo2Beans(incomeStatPos);
        }

        //转成map
        Map<Long, CharmStatPo> charmStatPoMap = charmStatPos.stream()
                .collect(Collectors.toMap(CharmStatPo::getUserId, charmStatPo -> charmStatPo, (existing, replacement) -> replacement));
        resultList.forEach(incomeStatPo -> {
            CharmStatPo charmStatPo = charmStatPoMap.get(incomeStatPo.getUserId());
            if (charmStatPo != null) {
                incomeStatPo.setCharm(charmStatPo.getValue());
            }
        });
        return DataCenterInfraConvert.I.incomeStatPo2Beans(resultList);
    }

    /**
     * 查询厅主播的topN收入榜单
     * @param familyId
     * @param roomId
     * @param date
     * @param rankType
     * @param topN
     * @return
     */
    private Map<Long, Long> getTopPlayerRankMap(Long familyId, Long roomId, Date date, OrderType rankType, Integer topN){
        // 非当天查询大数据
        if ( date != null && !DateUtil.isSameDay(date, new Date()) ) {
            List<WcDataPlayerRoomDay> list = getHistoryIncomes(familyId, Collections.singletonList(roomId), date, rankType, topN);
            if (log.isDebugEnabled()) {
                log.debug("getTopPlayerRankMap by datacenter. list={}", JsonUtil.dumps(list));
            }
            if (CollectionUtils.isEmpty(list)) {
                return Collections.emptyMap();
            }
            return list.stream().collect(Collectors.toMap(WcDataPlayerRoomDay::getPlayerId, v->v.getIncome().longValue()));
        } else {
            // 当天的查询支付 或者 查询实际统计的数据
            return paymentManager.getTopNPlayerIncome(getBusinessEvnEnum().getAppId(), familyId, roomId, new Date(), rankType, topN);
        }
    }

    /**
     * 查询厅主播的topN收入榜单 通过大数据
     * @param familyId
     * @param date
     * @param rankType
     * @param topN
     * @return
     */
    private List<WcDataPlayerRoomDay> getHistoryIncomes(Long familyId, List<Long> roomIds, Date date, OrderType rankType, Integer topN){
        WcDataPlayerRoomDayExample example = new WcDataPlayerRoomDayExample();
        if (rankType == OrderType.DESC) {
            // 收入倒序
            example.setOrderByClause("income desc,id");
        } else {
            // 升序
            example.setOrderByClause("income asc,id");
        }

        WcDataPlayerRoomDayExample.Criteria criteria = example.createCriteria();
        criteria.andIncomeGreaterThan(BigDecimal.ZERO)
                .andFamilyIdEqualTo(familyId)
                .andStatDateValueEqualTo(MyDateUtil.getDateDayValue(date))
                .andAppIdEqualTo(getBusinessEvnEnum().getAppId());
        if (CollectionUtils.isNotEmpty(roomIds)) {
            criteria.andRoomIdIn(roomIds);
        }

        return playerRoomDayMapper.pageByExample(example, 1, topN);
    }

    private Map<Long, Long> batchGetPlayerRankMap(Long familyId, Long roomId, Date date){
        //查询所有签约主播
        List<Long> playerIds = contractRemote.getAllSignRoomPlayerIds(roomId);
        if (CollectionUtils.isEmpty(playerIds)) {
            log.info("playerIds is empty");
            return Collections.emptyMap();
        }

        //并发查询所有主播的考核业绩
        Date dayStart = DateUtil.getDayStart(date);
        Date dayEnd = DateUtil.getDayEnd(date);
        List<List<Long>> listGroup = Lists.partition(playerIds, BATCH_QUERY_PLAYER_PAGE_SIZE);
        Map<Long, Long> incomeMap = new HashMap<>();
        for (List<Long> batchIds : listGroup) {
            incomeMap.putAll(paymentManager.batchGetIncomeByPlayer(familyId, roomId, batchIds, dayStart, dayEnd, PaySettleConfigCodeEnum.ANCHOR_INCOME_TOTAL_AMOUNT));
        }
        return incomeMap;
    }

    private List<IncomeStatPo> sort(List<IncomeStatPo> incomeStatPos, OrderType rankType){
        //获取前10，过滤掉0收入的
        List<IncomeStatPo> hasIncomeList = incomeStatPos.stream()
                .filter(p -> p.getIncome() != null && p.getIncome() != 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(hasIncomeList)) {
            return Collections.emptyList();
        }

        List<IncomeStatPo> resultList = new ArrayList<>();
        if (rankType == OrderType.DESC) {
            //倒序
            resultList = hasIncomeList.stream().sorted(Comparator.comparingLong(IncomeStatPo::getIncome).reversed())
                    .limit(10)
                    .collect(Collectors.toList());
        } else {
            resultList = hasIncomeList.stream().sorted(Comparator.comparingLong(IncomeStatPo::getIncome))
                    .limit(10)
                    .collect(Collectors.toList());
        }
        return resultList;
    }
}
