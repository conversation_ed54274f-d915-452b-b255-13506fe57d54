package fm.lizhi.ocean.wavecenter.infrastructure.common.manager;

import fm.lizhi.commons.util.GuidGenerator;
import fm.lizhi.ocean.wavecenter.service.common.manager.IdManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/4/15 14:14
 */
@Component
public class IdManagerImpl implements IdManager {

    @Autowired
    private GuidGenerator guidGenerator;

    @Override
    public long genId() {
        return guidGenerator.genId();
    }
}
