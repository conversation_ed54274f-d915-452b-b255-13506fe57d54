package fm.lizhi.ocean.wavecenter.infrastructure.gift.remote;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.gift.convert.GiftInfraConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.gift.datastore.entity.GiftPo;
import fm.lizhi.ocean.wavecenter.infrastructure.gift.datastore.mapper.XmLiveGiftMapper;
import fm.lizhi.ocean.wavecenter.service.gift.dto.GiftDto;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/24 15:45
 */
@Component
public class XmGiftRemote implements IGiftRemote{

    @Autowired
    private XmLiveGiftMapper liveGiftMapper;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.XIMI;
    }

    @Override
    public List<GiftDto> getByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<GiftPo> list = liveGiftMapper.getByIds(ids);
        return GiftInfraConvert.I.giftPo2Dto(list);
    }
}
