package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.manager;

import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.convert.GrowSettleFlowConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.dao.WcGrowSettleFlowDao;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowSettleFlow;
import fm.lizhi.ocean.wavecenter.service.grow.ability.constants.AbilityConstant;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.PlayerSettleFlowDTO;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.RoomSettleFlowDTO;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.SettlePeriodDTO;
import fm.lizhi.ocean.wavecenter.service.grow.ability.manager.GrowSettleFlowManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/6/6 14:28
 */
@Component
public class GrowSettleFlowManagerImpl implements GrowSettleFlowManager {

    @Autowired
    private WcGrowSettleFlowDao growSettleFlowDao;

    @Override
    public PlayerSettleFlowDTO getOrInitPlayerSettleFlow(SettlePeriodDTO dto) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        WcGrowSettleFlow entity = growSettleFlowDao.getOrInitPlayerSettleFlow(dto, appId, AbilityConstant.SETTLE_FLOW_TYPE_PLAYER);
        return GrowSettleFlowConvert.I.po2DTO(entity);
    }

    @Override
    public RoomSettleFlowDTO getOrInitRoomSettleFlow(SettlePeriodDTO dto) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        WcGrowSettleFlow entity = growSettleFlowDao.getOrInitPlayerSettleFlow(dto, appId, AbilityConstant.SETTLE_FLOW_TYPE_ROOM);
        return GrowSettleFlowConvert.I.po2roomDTO(entity);
    }

    @Override
    public void updatePlayerSettleFlow(PlayerSettleFlowDTO dto) {
        WcGrowSettleFlow po = GrowSettleFlowConvert.I.dto2Po(dto);
        growSettleFlowDao.updateSettleFlow(po);
    }

    @Override
    public void updateRoomSettleFlow(RoomSettleFlowDTO dto) {
        WcGrowSettleFlow po = GrowSettleFlowConvert.I.roomDTO2Po(dto);
        growSettleFlowDao.updateSettleFlow(po);
    }

    @Override
    public PlayerSettleFlowDTO getPlayerSettleFlow(SettlePeriodDTO dto) {
        WcGrowSettleFlow po = growSettleFlowDao.getPlayerSettleFlow(dto, ContextUtils.getBusinessEvnEnum().getAppId(), AbilityConstant.SETTLE_FLOW_TYPE_PLAYER);
        return GrowSettleFlowConvert.I.po2DTO(po);
    }

    @Override
    public RoomSettleFlowDTO getRoomSettleFlow(SettlePeriodDTO dto) {
        WcGrowSettleFlow po = growSettleFlowDao.getPlayerSettleFlow(dto, ContextUtils.getBusinessEvnEnum().getAppId(), AbilityConstant.SETTLE_FLOW_TYPE_ROOM);
        return GrowSettleFlowConvert.I.po2roomDTO(po);
    }
}
