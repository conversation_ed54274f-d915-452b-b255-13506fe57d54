package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.manager;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.convert.GrowRoomAbilityWeekCapabilityConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowRoomAbilityWeekCapability;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowRoomAbilityWeekCapabilityExample;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.mapper.ext.WcGrowRoomAbilityWeekCapabilityExtMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.mapper.WcGrowRoomAbilityWeekCapabilityMapper;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.RoomAbilityWeekCapabilityDTO;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.SettlePeriodDTO;
import fm.lizhi.ocean.wavecenter.service.grow.ability.manager.GrowRoomAbilityManager;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/11 15:00
 */
@Component
public class GrowRoomAbilityManagerImpl implements GrowRoomAbilityManager {

    @Autowired
    private WcGrowRoomAbilityWeekCapabilityMapper roomAbilityWeekCapabilityMapper;
    @Autowired
    private WcGrowRoomAbilityWeekCapabilityExtMapper roomAbilityWeekCapabilityExtMapper;

    @Override
    public List<RoomAbilityWeekCapabilityDTO> getRoomAbilityWeekCapability(Long familyId, SettlePeriodDTO settlePeriod, Long minId, Integer pageSize) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        String deployEnv = ConfigUtils.getEnvRequired().name();

        List<WcGrowRoomAbilityWeekCapability> list = roomAbilityWeekCapabilityExtMapper.getRoomAbilityWeekCapability(
                familyId,
                settlePeriod.getStartDate(),
                settlePeriod.getEndDate(),
                minId != null ? minId : 0L,
                pageSize,
                appId,
                deployEnv
        );

        return GrowRoomAbilityWeekCapabilityConvert.I.roomAbilityWeekCapabilitys2Dtos(list);
    }

    @Override
    public Integer countInFamilyCapabilityHighRoomNum(Long familyId, String capabilityCode, BigDecimal abilityValue, SettlePeriodDTO settlePeriod) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        String deployEnv = ConfigUtils.getEnvRequired().name();

        WcGrowRoomAbilityWeekCapabilityExample example = new WcGrowRoomAbilityWeekCapabilityExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andDeployEnvEqualTo(deployEnv)
                .andFamilyIdEqualTo(familyId)
                .andCapabilityCodeEqualTo(capabilityCode)
                .andAbilityValueGreaterThan(abilityValue)
                .andStartWeekDateEqualTo(settlePeriod.getStartDate())
                .andEndWeekDateEqualTo(settlePeriod.getEndDate());

        long count = roomAbilityWeekCapabilityMapper.countByExample(example);
        return Math.toIntExact(count);
    }

    @Override
    public RoomAbilityWeekCapabilityDTO getRoomAbilityWeekCapability(SettlePeriodDTO settlePeriod, Long roomId, String capabilityCode) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        String deployEnv = ConfigUtils.getEnvRequired().name();

        WcGrowRoomAbilityWeekCapabilityExample example = new WcGrowRoomAbilityWeekCapabilityExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andDeployEnvEqualTo(deployEnv)
                .andRoomIdEqualTo(roomId)
                .andStartWeekDateEqualTo(settlePeriod.getStartDate())
                .andEndWeekDateEqualTo(settlePeriod.getEndDate())
                .andCapabilityCodeEqualTo(capabilityCode);

        List<WcGrowRoomAbilityWeekCapability> list = roomAbilityWeekCapabilityMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        return GrowRoomAbilityWeekCapabilityConvert.I.roomAbilityWeekCapability2Dto(list.get(0));
    }

    @Override
    public void updateRoomAbilityWeekCapability(RoomAbilityWeekCapabilityDTO dto) {
        WcGrowRoomAbilityWeekCapability po = GrowRoomAbilityWeekCapabilityConvert.I.roomAbilityWeekCapabilityDTO2Po(dto);
        roomAbilityWeekCapabilityMapper.updateByPrimaryKey(po);
    }
}
