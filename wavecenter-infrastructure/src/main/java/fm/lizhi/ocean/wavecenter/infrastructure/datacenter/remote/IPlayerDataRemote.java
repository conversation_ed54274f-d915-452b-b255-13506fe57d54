package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote;

import fm.lizhi.ocean.wavecenter.api.datacenter.bean.PlayerAssessmentInfoBean;
import fm.lizhi.ocean.wavecenter.base.remote.IRemote;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.PlayerGetAssessmentDto;

/**
 * 签约主播数据
 * 查询差异化+第三方数据调用
 * <AUTHOR>
 * @date 2024/5/24 11:16
 */
public interface IPlayerDataRemote extends IRemote {

    /**
     * 查询考核周期业绩
     * @param familyId
     * @param roomId
     * @param playerId
     * @return
     */
    PlayerAssessmentInfoBean getAssessmentInfo(PlayerGetAssessmentDto param);

}
