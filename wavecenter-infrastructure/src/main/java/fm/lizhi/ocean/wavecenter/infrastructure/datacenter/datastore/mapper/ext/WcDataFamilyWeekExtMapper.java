package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.WcDataFamilyWeekStatPo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcDataFamilyWeekExtMapper {
    /**
     * 查询公会周统计数据
     *
     * @param familyId      公会ID
     * @param appId         业务ID
     * @param startWeekDate 开始周日期
     * @param pageSize      条数
     * @return 公会周统计数据列表
     */
    @Select("SELECT start_week_date, end_week_date, " +
            "personal_noble_income as playerVip, " +
            "official_hall_income as officialRoom, " +
            "personal_hall_income as player, " +
            "all_income as sum, " +
            "sign_hall_income as roomGift, " +
            "noble_income as roomVip " +
            "FROM wavecenter_data_family_week " +
            "WHERE family_id = #{familyId} AND app_id = #{appId} " +
            "AND start_week_date < #{startWeekDate} " +
            " order by start_week_date desc limit #{pageSize} ")
    List<WcDataFamilyWeekStatPo> queryWeekStatsByTime(@Param("familyId") Long familyId,
                                                      @Param("appId") Integer appId,
                                                      @Param("startWeekDate") Date startWeekDate,
                                                      @Param("pageSize") Integer pageSize);


}
