package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.convert;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityFlowResourceAuditBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.*;
import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.*;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityResourceSimpleInfoDTO;
import fm.lizhi.ocean.wavecenter.service.common.manager.IdManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ActivityMaterielConvert {

    @Autowired
    private IdManager idManager;

    public Pair<List<ActivityResourceGiveRecord>, List<ActivityFlowResourceGiveRecord>> buildFlowResources(ActivityApplyInfo applyInfo,
                                                                                                           List<ActivityApplyFlowResource> resourceList,
                                                                                                           List<ActivityResourceSimpleInfoDTO> resourceConfigs,
                                                                                                           List<ActivityFlowResourceAuditBean> resourceAuditList) {
        Map<Long, ActivityFlowResourceAuditBean> flowResourceAuditMap = resourceAuditList.stream().collect(Collectors.toMap(ActivityFlowResourceAuditBean::getFlowResourceId, Function.identity()));
        Map<Long, ActivityResourceSimpleInfoDTO> resourceMap = resourceConfigs.stream().collect(Collectors.toMap(ActivityResourceSimpleInfoDTO::getId, Function.identity()));
        List<ActivityResourceGiveRecord> resourceGiveRecords = new ArrayList<>();
        List<ActivityFlowResourceGiveRecord> flowResourceGiveRecords = new ArrayList<>();
        if (resourceList != null) {
            for (ActivityApplyFlowResource resource : resourceList) {
                ActivityFlowResourceAuditBean auditBean = flowResourceAuditMap.get(resource.getResourceConfigId());
                //如果不存在或者状态为审核不可发放，就不用初始化了
                if (auditBean == null || Objects.equals(auditBean.getStatus(), ActivityResourceAuditStatusEnum.DISABLE_GIVE.getStatus())) {
                    continue;
                }

                ActivityResourceSimpleInfoDTO resourceConfig = resourceMap.get(resource.getResourceConfigId());
                if (resourceConfig == null) {
                    continue;
                }

                ActivityResourceGiveRecord resourceGiveRecord = initResourceGiveRecord(applyInfo);
                long giveId = idManager.genId();
                resourceGiveRecord.setId(giveId);
                resourceGiveRecord.setResourceId(resource.getId());
                resourceGiveRecord.setType(ActivityResourceTypeEnum.FLOW_RESOURCE.getType());
                resourceGiveRecords.add(resourceGiveRecord);

                //构建流量资源详细记录
                ActivityFlowResourceGiveRecord flowResourceGiveRecord = new ActivityFlowResourceGiveRecord();
                flowResourceGiveRecord.setResourceCode(StringUtils.defaultString(resourceConfig.getResourceCode(), ""));
                flowResourceGiveRecord.setResourceConfigId(resource.getResourceConfigId());
                flowResourceGiveRecord.setResourceName(resourceConfig.getName());
                flowResourceGiveRecord.setDeployType(resourceConfig.getDeployType());
                flowResourceGiveRecord.setGiveId(giveId);
                flowResourceGiveRecord.setImageUrl(resource.getImageUrl());
                flowResourceGiveRecord.setUserId(applyInfo.getNjId());
                flowResourceGiveRecord.setStatus(CommonGiveStatusEnum.WAIT_GIVE.getStatus());
                flowResourceGiveRecord.setExtra(resource.getExtra() == null ? "" : resource.getExtra());
                flowResourceGiveRecord.setBizRecordId(0L);
                flowResourceGiveRecords.add(flowResourceGiveRecord);
            }
        }

        log.info("buildFlowResources.resourceGiveRecords={}", JsonUtil.dumps(resourceGiveRecords));
        log.info("buildFlowResources.flowResourceGiveRecords={}", JsonUtil.dumps(flowResourceGiveRecords));

        return Pair.of(resourceGiveRecords, flowResourceGiveRecords);
    }

    /**
     * 构建装扮资源
     *
     * @param applyInfo 活动信息
     * @return 结果
     */
    public Pair<List<ActivityResourceGiveRecord>, List<ActivityDressUpGiveRecord>> buildDressUpResources(ActivityApplyInfo applyInfo, List<ActivityApplyDecorate> decorates) {
        List<ActivityResourceGiveRecord> resourceGiveRecords = new ArrayList<>();
        List<ActivityDressUpGiveRecord> dressUpGiveRecords = new ArrayList<>();

        List<Long> avatarWidgetIds = new ArrayList<>();
        List<Long> roomBackgroundIds = new ArrayList<>();

        if (applyInfo.getAvatarWidgetId() != null && applyInfo.getAvatarWidgetId() > 0) {
            avatarWidgetIds.add(applyInfo.getAvatarWidgetId());
        }
        if (applyInfo.getRoomBackgroundId() != null && applyInfo.getRoomBackgroundId() > 0) {
            roomBackgroundIds.add(applyInfo.getRoomBackgroundId());
        }
        if (CollectionUtils.isNotEmpty(decorates)) {
            for (ActivityApplyDecorate decorate : decorates) {
                if (decorate.getDecorateType() == null) {
                    continue;
                }
                if (decorate.getDecorateType() == PlatformDecorateTypeEnum.AVATAR.getType()) {
                    avatarWidgetIds.add(decorate.getDecorateId());
                }
                if (decorate.getDecorateType() == PlatformDecorateTypeEnum.BACKGROUND.getType()) {
                    roomBackgroundIds.add(decorate.getDecorateId());
                }
            }
        }

        //如果头像框id为空，则不构建，直接返回
        if (CollectionUtils.isNotEmpty(avatarWidgetIds)
             && (applyInfo.getHostId() != null || StringUtils.isNotBlank(applyInfo.getAccompanyNjIds()))) {

            String njIds = applyInfo.getAccompanyNjIds();
            Set<Long> njIdList = new HashSet<>();
            njIdList.add(Optional.ofNullable(applyInfo.getApplicantUid()).orElse(0L));
            njIdList.add(Optional.ofNullable(applyInfo.getHostId()).orElse(0L));
            njIdList.add(Optional.ofNullable(applyInfo.getNjId()).orElse(0L));
            if (StringUtils.isNotBlank(njIds)) {
                List<Long> ids = Arrays.stream(njIds.split(","))
                        .map(String::trim) // 去除空格
                        .map(Long::parseLong) // 转换为long
                        .collect(Collectors.toList());
                njIdList.addAll(ids);
            }

            for (Long avatarWidgetId : avatarWidgetIds) {
                long giveId = idManager.genId();
                //头像框不等于空，则需要初始化
                ActivityResourceGiveRecord resourceGiveRecord = initResourceGiveRecord(applyInfo);
                resourceGiveRecord.setId(giveId);
                resourceGiveRecord.setResourceId(avatarWidgetId);
                resourceGiveRecord.setType(ActivityResourceTypeEnum.DRESS_UP.getType());
                resourceGiveRecords.add(resourceGiveRecord);

                for (Long njId : njIdList) {
                    if (njId <= 0) {
                        continue;
                    }

                    ActivityDressUpGiveRecord dress = new ActivityDressUpGiveRecord();
                    dress.setUserId(njId);
                    dress.setGiveId(giveId);
                    dress.setStatus(CommonGiveStatusEnum.WAIT_GIVE.getStatus());
                    dress.setType(DecorateEnum.AVATAR.getType());
                    dress.setDressUpId(avatarWidgetId);
                    dressUpGiveRecords.add(dress);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(roomBackgroundIds)) {
            for (Long roomBackgroundId : roomBackgroundIds) {
                long giveId = idManager.genId();
                //不等于空，则需要初始化
                ActivityResourceGiveRecord resourceGiveRecord = initResourceGiveRecord(applyInfo);
                resourceGiveRecord.setId(giveId);
                resourceGiveRecord.setResourceId(roomBackgroundId);
                resourceGiveRecord.setType(ActivityResourceTypeEnum.DRESS_UP.getType());
                resourceGiveRecords.add(resourceGiveRecord);

                ActivityDressUpGiveRecord dress = new ActivityDressUpGiveRecord();
                dress.setUserId(applyInfo.getNjId());
                dress.setGiveId(giveId);
                dress.setStatus(CommonGiveStatusEnum.WAIT_GIVE.getStatus());
                dress.setType(DecorateEnum.BACKGROUND.getType());
                dress.setDressUpId(roomBackgroundId);
                dressUpGiveRecords.add(dress);
            }
        }

        log.info("buildDressUpResources.resourceGiveRecords={}", JsonUtil.dumps(resourceGiveRecords));
        log.info("buildDressUpResources.dressUpGiveRecords={}", JsonUtil.dumps(dressUpGiveRecords));

        return Pair.of(resourceGiveRecords, dressUpGiveRecords);
    }


    /**
     * 构建房间公告
     *
     * @param applyInfo 申请信息
     * @return 房间公告配置记录
     */
    public ActivityAnnouncementDeployRecord buildRoomAnnouncement(ActivityApplyInfo applyInfo) {
        if (StringUtils.isNotBlank(applyInfo.getRoomAnnouncement()) || StringUtils.isNotBlank(applyInfo.getRoomAnnouncementImgUrl())) {
            ActivityAnnouncementDeployRecord record = new ActivityAnnouncementDeployRecord();
            record.setActivityId(applyInfo.getId());
            record.setAppId(applyInfo.getAppId());
            record.setAnnouncement(applyInfo.getRoomAnnouncement());
            record.setOriginalAnnouncement("");
            record.setAnnouncementImgUrl(applyInfo.getRoomAnnouncementImgUrl());
            record.setStartTime(applyInfo.getStartTime());
            record.setEndTime(applyInfo.getEndTime());
            record.setDeployEnv(ConfigUtils.getEnvRequired().name());
            record.setCreateTime(new Date());
            record.setModifyTime(new Date());
            record.setNjId(applyInfo.getNjId());
            record.setStatus(ActivityAnnouncementStatusEnum.UNSETTED.getStatus());
            record.setTryCount(0);
            return record;
        }
        return null;
    }

    /**
     * 初始化资源发放记录
     *
     * @param applyInfo 申请信息
     * @return 实体
     */
    private ActivityResourceGiveRecord initResourceGiveRecord(ActivityApplyInfo applyInfo) {
        ActivityResourceGiveRecord resourceGiveRecord = new ActivityResourceGiveRecord();
        resourceGiveRecord.setActivityId(applyInfo.getId());
        resourceGiveRecord.setAppId(applyInfo.getAppId());
        resourceGiveRecord.setStatus(ActivityResourceGiveStatusEnum.WAIT_GIVE.getStatus());
        resourceGiveRecord.setCreateTime(new Date());
        resourceGiveRecord.setModifyTime(new Date());
        resourceGiveRecord.setDeployEnv(ConfigUtils.getEnvRequired().name());
        resourceGiveRecord.setTryCount(0);
        resourceGiveRecord.setErrorCode(0);
        resourceGiveRecord.setErrorMsg("");
        resourceGiveRecord.setEndTime(applyInfo.getEndTime());
        resourceGiveRecord.setActivityName(applyInfo.getName());
        resourceGiveRecord.setStartTime(applyInfo.getStartTime());
        return resourceGiveRecord;
    }
}
