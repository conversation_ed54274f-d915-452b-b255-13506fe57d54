package fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.ListFamilySpecialRecommendCardNameBean;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.SaveFamilySpecialRecommendCardNameBean;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestClearFamilySpecialRecommendCardName;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestListFamilySpecialRecommendCardName;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestUploadFamilySpecialRecommendCardName;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert.FamilySpecialRecommendCardNameConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.dao.FamilyOtherAwardRuleDao;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilySpecialRecommendCardName;
import fm.lizhi.ocean.wavecenter.service.award.family.manager.FamilyOtherAwardRuleManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class FamilyOtherAwardRuleManagerImpl implements FamilyOtherAwardRuleManager {

    @Autowired
    private FamilyOtherAwardRuleDao familyOtherAwardRuleDao;

    @Autowired
    private UserManager userManager;

    @Override
    public Result<Void> uploadFamilySpecialRecommendCardName(RequestUploadFamilySpecialRecommendCardName request) {
        HashSet<Long> userIdSet = new HashSet<>();
        for (SaveFamilySpecialRecommendCardNameBean bean : request.getList()) {
            Long userId = bean.getUserId();
            if (!userIdSet.add(userId)) {
                return RpcResult.fail(CommonService.PARAM_ERROR, "存在重复的用户ID: " + userId);
            }
        }
        List<Long> userIds = new ArrayList<>(userIdSet);
        List<SimpleUserDto> validUsers = userManager.getSimpleUserByIds(userIds);
        List<Long> validUserIds = validUsers.stream().map(SimpleUserDto::getId).distinct().collect(Collectors.toList());
        if (userIds.size() != validUserIds.size()) {
            Collection<Long> invalidUserIds = CollectionUtils.subtract(userIds, validUserIds);
            return RpcResult.fail(CommonService.PARAM_ERROR, "存在非法用户ID: " + invalidUserIds);
        }
        familyOtherAwardRuleDao.replaceSpecialRecommendCardName(request);
        return RpcResult.success();
    }

    @Override
    public Result<Void> clearFamilySpecialRecommendCardName(RequestClearFamilySpecialRecommendCardName request) {
        familyOtherAwardRuleDao.clearSpecialRecommendCardName(request);
        return RpcResult.success();
    }

    @Override
    public Result<PageBean<ListFamilySpecialRecommendCardNameBean>> listFamilySpecialRecommendCardName(RequestListFamilySpecialRecommendCardName request) {
        PageList<WcFamilySpecialRecommendCardName> entities = familyOtherAwardRuleDao.listSpecialRecommendCardName(request);
        List<ListFamilySpecialRecommendCardNameBean> listBeans = FamilySpecialRecommendCardNameConvert.I.toListBeans(entities);
        return RpcResult.success(PageBean.of(entities.getTotal(), listBeans));
    }
}
