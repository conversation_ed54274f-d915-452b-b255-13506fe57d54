package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowPlayerMetricValue;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcGrowPlayerMetricValueExtMapper {

    @Select({
        "<script>",
        "SELECT * FROM wavecenter_grow_player_metric_value",
        "WHERE room_id = #{roomId}",
        "  AND start_week_date = #{startDate}",
        "  AND end_week_date = #{endDate}",
        "  AND player_id &gt; #{minPlayerId}",
        "  AND app_id = #{appId}",
        "ORDER BY player_id ASC",
        "LIMIT #{pageSize}",
        "</script>"
    })
    List<WcGrowPlayerMetricValue> selectByRoomAndPeriodWithCursor(
        @Param("roomId") Long roomId,
        @Param("startDate") Date startDate,
        @Param("endDate") Date endDate,
        @Param("minPlayerId") Long minPlayerId,
        @Param("pageSize") Integer pageSize,
        @Param("appId") Integer appId
    );

    @Select("<script>\n" +
            "  SELECT * FROM `wavecenter_grow_player_metric_value`\n" +
            "  WHERE `app_id` = #{appId}\n" +
            "    AND `room_id` = #{roomId}\n" +
            "    AND `player_id` = #{playerId}\n" +
            "    AND `start_week_date` &gt;= #{minStartWeekDate}\n" +
            "    AND `start_week_date` &lt;= #{maxStartWeekDate}\n" +
            "</script>")
    List<WcGrowPlayerMetricValue> getWeeksPlayerMetricValues(
            @Param("appId") int appId,
            @Param("roomId") long roomId,
            @Param("playerId") long playerId,
            @Param("minStartWeekDate") Date minStartWeekDate,
            @Param("maxStartWeekDate") Date maxStartWeekDate);
}
