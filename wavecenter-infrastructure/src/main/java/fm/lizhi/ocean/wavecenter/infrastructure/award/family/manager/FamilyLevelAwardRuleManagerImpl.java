package fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.GetFamilyLevelAwardItemBean;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.ListFamilyLevelAwardRuleBean;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.SaveFamilyLevelAwardItemBean;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardResourceTypeEnum;
import fm.lizhi.ocean.wavecenter.api.award.family.request.*;
import fm.lizhi.ocean.wavecenter.api.award.family.response.ResponseGetFamilyLevelAwardRule;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.api.grow.level.bean.FamilyLevelConfigBean;
import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.DecorateInfoBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.validation.ValidateResult;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert.FamilyLevelAwardItemConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert.FamilyLevelAwardRuleConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.dao.FamilyLevelAwardRuleDao;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilyLevelAwardItem;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilyLevelAwardRule;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.validation.FamilyLevelAwardResourceValidator;
import fm.lizhi.ocean.wavecenter.service.award.family.manager.FamilyLevelAwardRuleManager;
import fm.lizhi.ocean.wavecenter.service.grow.manager.FamilyLevelConfigManager;
import fm.lizhi.ocean.wavecenter.service.resource.decorate.manager.DecorateManager;
import fm.lizhi.ocean.wavecenter.service.resource.shortnumber.dto.ShortNumberDTO;
import fm.lizhi.ocean.wavecenter.service.resource.shortnumber.manager.ShortNumberManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class FamilyLevelAwardRuleManagerImpl implements FamilyLevelAwardRuleManager {

    @Autowired
    private FamilyLevelAwardRuleDao familyLevelAwardRuleDao;

    @Autowired
    private FamilyLevelAwardResourceValidator familyLevelAwardResourceValidator;

    @Autowired
    private FamilyLevelConfigManager familyLevelConfigManager;

    @Autowired
    private DecorateManager decorateManager;

    @Autowired
    private ShortNumberManager shortNumberManager;

    @Override
    public Result<Void> createRule(RequestCreateFamilyLevelAwardRule request) {
        Integer appId = request.getAppId();
        Long levelId = request.getLevelId();
        List<SaveFamilyLevelAwardItemBean> items = request.getItems();
        // 奖励资源基础校验
        ValidateResult itemsValidateResult = familyLevelAwardResourceValidator.validate(items);
        if (itemsValidateResult.isInvalid()) {
            return RpcResult.fail(CommonService.PARAM_ERROR, itemsValidateResult.getMessage());
        }
        // 规则是否已存在
        WcFamilyLevelAwardRule oldRule = familyLevelAwardRuleDao.getUndeletedRule(appId, levelId);
        if (oldRule != null) {
            return RpcResult.fail(CommonService.PARAM_ERROR, "该等级规则已存在");
        }
        familyLevelAwardRuleDao.createRule(request);
        return RpcResult.success();
    }

    @Override
    public Result<Void> updateRule(RequestUpdateFamilyLevelAwardRule request) {
        Long ruleId = request.getId();
        List<SaveFamilyLevelAwardItemBean> items = request.getItems();
        // 奖励资源基础校验
        ValidateResult itemsValidateResult = familyLevelAwardResourceValidator.validate(items);
        if (itemsValidateResult.isInvalid()) {
            return RpcResult.fail(CommonService.PARAM_ERROR, itemsValidateResult.getMessage());
        }
        // 规则是否存在
        WcFamilyLevelAwardRule oldRule = familyLevelAwardRuleDao.getRule(ruleId);
        if (oldRule == null) {
            return RpcResult.fail(CommonService.PARAM_ERROR, "规则不存在, ruleId=" + ruleId);
        }
        if (oldRule.getDeleted()) {
            return RpcResult.fail(CommonService.PARAM_ERROR, "规则已删除, ruleId=" + ruleId);
        }
        // 更新规则
        familyLevelAwardRuleDao.updateRule(request);
        return RpcResult.success();
    }

    @Override
    public Result<Void> deleteRule(RequestDeleteFamilyLevelAwardRule request) {
        Long ruleId = request.getId();
        WcFamilyLevelAwardRule oldRule = familyLevelAwardRuleDao.getRule(ruleId);
        if (oldRule == null) {
            return RpcResult.fail(CommonService.PARAM_ERROR, "规则不存在, ruleId=" + ruleId);
        }
        if (oldRule.getDeleted()) {
            return RpcResult.fail(CommonService.PARAM_ERROR, "规则已删除, ruleId=" + ruleId);
        }
        familyLevelAwardRuleDao.deleteRule(request);
        return RpcResult.success();
    }

    @Override
    public Result<List<ListFamilyLevelAwardRuleBean>> listRule(RequestListFamilyLevelAwardRule request) {
        Integer appId = request.getAppId();
        List<WcFamilyLevelAwardRule> rules = familyLevelAwardRuleDao.getRulesByAppId(appId);
        List<Long> ruleIds = rules.stream().map(WcFamilyLevelAwardRule::getId).collect(Collectors.toList());
        Map<Long, Map<Integer, WcFamilyLevelAwardItem>> rulesResourceTypeItemMap = familyLevelAwardRuleDao.getRulesResourceTypeItemMap(ruleIds);
        List<Long> levelIds = rules.stream().map(WcFamilyLevelAwardRule::getLevelId).collect(Collectors.toList());
        Map<Long, FamilyLevelConfigBean> levelMap = familyLevelConfigManager.getLevelConfigMap(levelIds);
        ArrayList<ListFamilyLevelAwardRuleBean> ruleBeans = new ArrayList<>(rules.size());
        for (WcFamilyLevelAwardRule rule : rules) {
            Long ruleId = rule.getId();
            Long levelId = rule.getLevelId();
            Map<Integer, WcFamilyLevelAwardItem> itemMap = rulesResourceTypeItemMap.getOrDefault(ruleId, Collections.emptyMap());
            // 构造返回数据
            ListFamilyLevelAwardRuleBean ruleBean = new ListFamilyLevelAwardRuleBean();
            ruleBean.setId(ruleId);
            // 公会等级信息
            FamilyLevelConfigBean level = levelMap.get(levelId);
            ruleBean.setLevelId(levelId);
            ruleBean.setLevelName(level != null ? level.getLevelName() : StringUtils.EMPTY);
            ruleBean.setLevelDeleted(level != null ? level.getDeleted() : true);
            // 推荐卡
            WcFamilyLevelAwardItem recommendCardItem = itemMap.get(FamilyAwardResourceTypeEnum.RECOMMEND_CARD.getValue());
            ruleBean.setRecommendCardNumber(recommendCardItem != null ? recommendCardItem.getResourceNumber() : 0);
            ruleBean.setRecommendCardValidPeriod(recommendCardItem != null ? recommendCardItem.getResourceValidPeriod() : null);
            // 座驾
            WcFamilyLevelAwardItem vehicleItem = itemMap.get(FamilyAwardResourceTypeEnum.VEHICLE.getValue());
            Long vehicleId = vehicleItem != null ? vehicleItem.getResourceId() : null;
            DecorateInfoBean vehicle = vehicleId != null ? decorateManager.getDecorateInfo(PlatformDecorateTypeEnum.VEHICLE, vehicleId) : null;
            ruleBean.setVehicleId(vehicleId);
            ruleBean.setVehicleName(vehicle != null ? vehicle.getDecorateName() : null);
            ruleBean.setVehicleImage(vehicle != null ? vehicle.getDecorateImage() : null);
            ruleBean.setVehicleValidPeriod(vehicleItem != null ? vehicleItem.getResourceValidPeriod() : null);
            // 勋章
            WcFamilyLevelAwardItem medalItem = itemMap.get(FamilyAwardResourceTypeEnum.MEDAL.getValue());
            Long medalId = medalItem != null ? medalItem.getResourceId() : null;
            DecorateInfoBean medal = medalId != null ? decorateManager.getDecorateInfo(PlatformDecorateTypeEnum.MEDAL, medalId) : null;
            ruleBean.setMedalId(medalId);
            ruleBean.setMedalName(medal != null ? medal.getDecorateName() : null);
            ruleBean.setMedalImage(medal != null ? medal.getDecorateImage() : null);
            ruleBean.setMedalValidPeriod(medalItem != null ? medalItem.getResourceValidPeriod() : null);
            // 短号
            WcFamilyLevelAwardItem shortNumberItem = itemMap.get(FamilyAwardResourceTypeEnum.SHORT_NUMBER.getValue());
            Long shortNumberId = shortNumberItem != null ? shortNumberItem.getResourceId() : null;
            ShortNumberDTO shortNumber = shortNumberId != null ? shortNumberManager.getShortNumber(appId, shortNumberId) : null;
            ruleBean.setShortNumberId(shortNumberId);
            ruleBean.setShortNumberName(shortNumber != null ? shortNumber.getName() : null);
            // 规则自身信息
            ruleBean.setCreateTime(rule.getCreateTime().getTime());
            ruleBean.setCreator(rule.getCreator());
            ruleBean.setModifyTime(rule.getModifyTime().getTime());
            ruleBean.setModifier(rule.getModifier());
            ruleBeans.add(ruleBean);
        }
        return RpcResult.success(ruleBeans);
    }

    @Override
    public Result<ResponseGetFamilyLevelAwardRule> getFamilyLevelAwardRule(RequestGetFamilyLevelAwardRule request) {
        Long ruleId = request.getId();
        WcFamilyLevelAwardRule rule = familyLevelAwardRuleDao.getRule(ruleId);
        if (rule == null) {
            return RpcResult.fail(CommonService.PARAM_ERROR, "规则不存在, ruleId=" + ruleId);
        }
        if (rule.getDeleted()) {
            return RpcResult.fail(CommonService.PARAM_ERROR, "规则已删除, ruleId=" + ruleId);
        }
        Long levelId = rule.getLevelId();
        List<WcFamilyLevelAwardItem> itemEntities = familyLevelAwardRuleDao.getRuleItems(ruleId);
        List<GetFamilyLevelAwardItemBean> itemBeans = FamilyLevelAwardItemConvert.I.toGetBeans(itemEntities);
        FamilyLevelConfigBean level = familyLevelConfigManager.getLevelConfig(levelId);
        String levelName = level != null ? level.getLevelName() : StringUtils.EMPTY;
        Boolean levelDeleted = level != null ? level.getDeleted() : true;
        ResponseGetFamilyLevelAwardRule response = FamilyLevelAwardRuleConvert.I
                .toResponseGetFamilyLevelAwardRule(rule, itemBeans, levelId, levelName, levelDeleted);
        return RpcResult.success(response);
    }
}
