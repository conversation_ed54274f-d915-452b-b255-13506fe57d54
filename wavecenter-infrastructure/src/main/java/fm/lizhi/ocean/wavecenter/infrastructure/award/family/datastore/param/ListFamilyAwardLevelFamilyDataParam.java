package fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.param;

import lombok.Data;

import java.util.Date;

/**
 * 列出公会等级数据的参数
 */
@Data
public class ListFamilyAwardLevelFamilyDataParam {

    /**
     * 应用id
     */
    private final int appId;

    /**
     * 公会ID
     */
    private final Long familyId;

    /**
     * 奖励周期开始时间, 精确匹配
     */
    private final Date awardStartTime;

    /**
     * 最大创建时间, 用于限定查询范围
     */
    private final Date maxCreateTime;

    /**
     * 分页页码
     */
    private final int pageNumber;

    /**
     * 分页大小
     */
    private final int pageSize;
}
