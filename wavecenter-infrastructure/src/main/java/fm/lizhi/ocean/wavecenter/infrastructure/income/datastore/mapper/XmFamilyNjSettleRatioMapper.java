package fm.lizhi.ocean.wavecenter.infrastructure.income.datastore.mapper;


import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.po.XmFamilyNjSettleRatioPo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@DataStore(namespace = "mysql_xm_lzppfamily_r")
public interface XmFamilyNjSettleRatioMapper {


    @Select(" select percentage from family_nj_settle_ratio " +
            "where nj_id = #{njId}  " +
            "  and status = 'SIGN_SUCCEED'  " +
            "  order by create_time desc limit 1 ")
    Long getPlayerIncome(@Param("njId") long njId);

    @Select({"<script>"
            , "select *"
            , "from family_nj_settle_ratio"
            , "where family_id = #{familyId}"

            , "and nj_id in "
            , "<foreach collection='njIds' item='njId' open='(' separator=',' close=')'>"
            , "#{njId}"
            , "</foreach>"

            , "  and (nj_id, create_time) in (select nj_id, MAX(create_time)"
            , "  from family_nj_settle_ratio"
            , "  where family_id = #{familyId}"
            , "    and nj_id in "
            , "<foreach collection='njIds' item='njId' open='(' separator=',' close=')'>"
            , "#{njId}"
            , "</foreach>"
            , "    and status = 'AUDIT_SUCCESS' and start_time&lt;=now() "
            , "  group by nj_id)"
            , "</script>"})
    List<XmFamilyNjSettleRatioPo> selectBest(@Param("familyId") long familyId, @Param("njIds") List<Long> njIds);

    @Select({"<script>"
            , "select distinct nj_id"
            , "from family_nj_settle_ratio"
            , "where family_id = #{familyId}"
            , "and status = 'AUDIT_SUCCESS' and start_time&lt;=now() "
            , "<if test='null != max'>"
            , " and percentage &lt;=#{max}"
            , "</if>"
            , "<if test='null != min'>"
            , " and percentage &gt;=#{min}"
            , "</if>"
            , "</script>"})
    List<Long> selectSettleScopeNjIds(@Param("familyId") long familyId, @Param("max")Integer max, @Param("min") Integer min);


}
