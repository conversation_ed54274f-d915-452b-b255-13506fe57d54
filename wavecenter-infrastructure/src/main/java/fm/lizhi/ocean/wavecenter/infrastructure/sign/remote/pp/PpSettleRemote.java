package fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.pp;

import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.FamilyAndNjContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity.PpSettle;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity.PpSettleExample;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.mapper.PpSettleMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.IContractRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.ISettleRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.request.RequestContractSettle;
import fm.lizhi.ocean.wavecenter.service.sign.dto.RequestFamilyAndNjContractDTO;
import fm.lizhi.ocean.wavecenter.service.sign.dto.SignSettleDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/15 19:21
 */
@Component
public class PpSettleRemote implements ISettleRemote {

    @Autowired
    private PpSettleMapper settleMapper;
    @Autowired
    private IContractRemote iContractRemote;

    @Override
    public Optional<SignSettleDTO> querySettleByNj(Long njId) {
        //查询厅当前生效的合同
        PageBean<FamilyAndNjContractBean> contractList = iContractRemote.queryContract(RequestFamilyAndNjContractDTO.builder()
                .njId(njId)
                .type(ContractTypeEnum.SIGN)
                .type(ContractTypeEnum.RENEW)
                .type(ContractTypeEnum.SUBJECT_CHANGE)
                .relation(SignRelationEnum.SIGN_SUCCESS)
                .pageNo(1).pageSize(1)
                .build());
        if (CollectionUtils.isEmpty(contractList.getList())) {
            LogContext.addResLog("queryContract is empty. njId={}", njId);
            return Optional.empty();
        }

        //通过合同查询结算信息
        List<PpSettle> ppSettles = selectSettleByContractId(contractList.getList().stream()
                .map(FamilyAndNjContractBean::getContractId)
                .collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(ppSettles)) {
            LogContext.addResLog("ppSettles is empty. njId={}", njId);
            return Optional.empty();
        }
        PpSettle ppSettle = ppSettles.get(0);
        return Optional.of(new SignSettleDTO()
                .setSettlePercentage(ppSettle.getPercentage())
                .setSettleType(ppSettle.getType()));
    }

    @Override
    public Map<Long, SignSettleDTO> querySettle(RequestContractSettle request) {
        if (CollectionUtils.isEmpty(request.getContracts())) {
            LogContext.addResLog("contract is empty");
            return Collections.emptyMap();
        }

        List<PpSettle> list = selectSettleByContractId(request.getContracts().stream()
                .map(FamilyAndNjContractBean::getContractId)
                .collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(list)) {
            LogContext.addResLog("ppSettle list is empty");
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.toMap(PpSettle::getContractId, v->new SignSettleDTO()
                .setSettleType(v.getType())
                .setSettlePercentage(v.getPercentage())));
    }

    private List<PpSettle> selectSettleByContractId(List<Long> contractIds) {
        PpSettleExample example = new PpSettleExample();
        PpSettleExample.Criteria criteria = example.createCriteria();
        criteria.andContractIdIn(contractIds);
        return settleMapper.selectByExample(example);
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }
}
