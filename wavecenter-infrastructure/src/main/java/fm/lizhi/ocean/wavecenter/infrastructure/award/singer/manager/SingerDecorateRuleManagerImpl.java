package fm.lizhi.ocean.wavecenter.infrastructure.award.singer.manager;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Sets;
import com.lark.oapi.core.utils.Lists;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SongStyleBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.bean.SingerDecorateRuleBean;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestPageSingerDecorateRule;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestSaveSingerDecorateRule;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestUpdateSingerDecorateRule;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.common.dto.PageDto;
import fm.lizhi.ocean.wavecenter.infrastructure.award.singer.convert.SingerDecorateConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.award.singer.datastore.dao.SingerDecorateDao;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.entity.SingerDecorateRule;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.mapper.SingerDecorateRuleMapper;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.constant.SongStyleRangeType;
import fm.lizhi.ocean.wavecenter.module.api.award.singer.constants.SingerDecorateConditionTypeEnum;
import fm.lizhi.ocean.wavecenter.module.api.award.singer.constants.SingerDecorateRuleConstant;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.config.SingerAnchorConfig;
import fm.lizhi.ocean.wavecenter.service.award.singer.manager.SingerDecorateRuleManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotEmpty;
import java.util.*;
import java.util.stream.Collectors;

/**
 *  歌手装扮规则配置
 * <AUTHOR>
 */
@Component
@Slf4j
public class SingerDecorateRuleManagerImpl implements SingerDecorateRuleManager {

    @Autowired
    private SingerDecorateDao singerDecorateDao;

    @Autowired
    private SingerDecorateRuleMapper singerDecorateRuleMapper;
    @Autowired
    private SingerAnchorConfig singerAnchorConfig;

    @Override
    public PageDto<SingerDecorateRuleBean> pageSingerDecorateRule(RequestPageSingerDecorateRule request) {
        PageList<SingerDecorateRule> pageList = singerDecorateDao.pageSingerDecorateRule(request.getAppId(), request.getPageNo(), request.getPageSize());
        if (CollUtil.isEmpty(pageList)){
            return PageDto.empty();
        }

        return PageDto.of(pageList.getTotal(), SingerDecorateConvert.I.convertSingerDecorateRuleBeanList(pageList));
    }

    @Override
    public Boolean saveSingerDecorateRule(RequestSaveSingerDecorateRule request) {
        return false;
    }

    @Override
    public Boolean updateSingerDecorateRule(RequestUpdateSingerDecorateRule request) {
        return false;
    }

    @Override
    public List<SingerDecorateRuleBean> getSingerDecorateRule(int appId, SingerTypeEnum singerType, @NotEmpty String songStyle, boolean originalSinger) {
        List<String> playerSongStyleList = Arrays.stream(songStyle.split(",")).collect(Collectors.toList());
        //@why 默认存在全能曲风的规则配置
        List<SingerDecorateRule> result = getSingerDecorateRule(appId, singerType, playerSongStyleList, originalSinger, true);
        //@why 实际上没命中全能曲风配置，找其他可命中配置
        if(SongStyleRangeType.isVersatile(playerSongStyleList) && CollectionUtils.isEmpty(result)) {
            result = getSingerDecorateRule(appId, singerType, playerSongStyleList, originalSinger, false);
        }
        return SingerDecorateConvert.I.convertSingerDecorateRuleBeanList(result);
    }


    public List<SingerDecorateRule> getSingerDecorateRule(int appId, SingerTypeEnum singerType, @NotEmpty List<String> playerSongStyleList, boolean originalSinger, boolean existVersatileRule) {
        //命中的曲风条件集合
        List<String> songStyleConditions = collectSongStyleCondition(appId, playerSongStyleList, originalSinger, existVersatileRule);
        log.info("getSingerDecorateRule;existVersatile={};appId={};singerType={};songStyle={};original={};songStyleConditions={}", existVersatileRule, appId, singerType, playerSongStyleList, originalSinger, JsonUtils.toJsonString(songStyleConditions));
        //命中的原创条件
        List<String> originalConditions = collectOriginalConditions(originalSinger);
        Set<String> combineConditionIndex = SingerDecorateConditionTypeEnum.combineMultipleConditions(songStyleConditions, originalConditions);
        log.info("getSingerDecorateRule;existVersatile={};appId={};singerType={};songStyle={};original={};combineConditionIndex={}", existVersatileRule, appId, singerType, playerSongStyleList, originalSinger, JsonUtils.toJsonString(combineConditionIndex));
        //组合条件索引
        //一把查出来
        List<SingerDecorateRule> result = singerDecorateDao.getSingerDecorateRule(appId, singerType, combineConditionIndex);
        log.info("getSingerDecorateRule;existVersatile={};appId={};singerType={};songStyle={};original={};result={}", existVersatileRule, appId, singerType, playerSongStyleList, originalSinger, result);
        return result;
    }


    /**
     * 收集原创歌手条件
     * eg:
     * - 无需原创 -> 2_FALSE
     * - 原创 -> 2_TRUE
     */
    private List<String> collectOriginalConditions(boolean originalSinger) {
        //满足 不需要校验原创的条件
        List<String> result = Lists.newArrayList(SingerDecorateConditionTypeEnum.ORIGINAL_SINGER.buildConditionIndex(Boolean.FALSE));
        //有原创资质
        if(originalSinger) {
            //满足 需要原创的条件
            result.add(SingerDecorateConditionTypeEnum.ORIGINAL_SINGER.buildConditionIndex(Boolean.TRUE));
        }
        return result;
    }

    /**
     * 收集曲风条件
     * eg:
     * - 固定曲风3个曲风 -> 1_1_曲风1_曲风2_曲风3
     * - 无配置曲风(固定曲风0个曲风) -> 1_1
     * - 任一曲风 -> 1_2
     * - 全能 -> 1_3
     */
    @NotNull
    private List<String> collectSongStyleCondition(int appId, List<String> playerSongStyleList, boolean originalSinger, boolean existVersatile) {
        List<String> songStyleConditions = Lists.newArrayList();
        if(originalSinger) {
            //@what 原创命中无需校验的曲风
            //@why 非原创一定会配置曲风 如果后面可以配置了 就去掉if即可
            String index = SingerDecorateConditionTypeEnum.MUSIC_STYLE.buildConditionIndex(SongStyleRangeType.NONE, Collections.emptyList());
            songStyleConditions.add(index);
        }
        //存在全能曲风可以命中，优先命中全能曲风
        if(existVersatile && SongStyleRangeType.isVersatile(playerSongStyleList)) {
            songStyleConditions.add(SingerDecorateConditionTypeEnum.MUSIC_STYLE.buildConditionIndex(SongStyleRangeType.VERSATILE, Collections.emptyList()));
            return songStyleConditions;
        }
        //命中任一曲风
        songStyleConditions.add(SingerDecorateConditionTypeEnum.MUSIC_STYLE.buildConditionIndex(SongStyleRangeType.ALL, Collections.emptyList()));
        //命中固定曲风
        //1个或者多个 按照配置的曲风索引升序拼接
        String songStyleConfig = singerAnchorConfig.getBizConfig(appId).getSongStyleConfig();
        List<SongStyleBean> songStyleConfigs = JsonUtil.loadsArray(songStyleConfig, SongStyleBean.class);
        List<String> sortedSongStyle = songStyleConfigs.stream()
                .filter(bean -> playerSongStyleList.contains(bean.getName()))
                .sorted(Comparator.comparingInt(SongStyleBean::getConditionIndex))
                .map(SongStyleBean::getName)
                .collect(Collectors.toList());

        String index = SingerDecorateConditionTypeEnum.MUSIC_STYLE.buildConditionIndex(SongStyleRangeType.FIXED, sortedSongStyle);
        songStyleConditions.add(index);
        return songStyleConditions;
    }
}
