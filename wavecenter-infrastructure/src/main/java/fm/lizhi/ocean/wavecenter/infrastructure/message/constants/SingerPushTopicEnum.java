package fm.lizhi.ocean.wavecenter.infrastructure.message.constants;

/**
 * <AUTHOR>
 * @date 2024/4/17 15:50
 */
public enum SingerPushTopicEnum implements IPushTopic {

    /**
     * 歌手认证状态变更
     * WAVECENTER_SINGER_VERIFY_STATUS_CHANGE_#{AppId}_#{userId}
     */
    SINGER_VERIFY_STATUS_CHANGE,

    ;

    @Override
    public String getPrefix() {
        return "WAVECENTER";
    }

    @Override
    public String getName() {
        return this.name();
    }

}
