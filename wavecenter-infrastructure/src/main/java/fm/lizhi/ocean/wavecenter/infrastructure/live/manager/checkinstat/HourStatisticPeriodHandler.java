package fm.lizhi.ocean.wavecenter.infrastructure.live.manager.checkinstat;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import fm.lizhi.ocean.wavecenter.api.live.constants.CheckInDateTypeEnum;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataPlayerHourRealTime;
import fm.lizhi.ocean.wavecenter.infrastructure.live.convert.WaveCheckInDataConverter;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.dao.WaveCheckInDataDao;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WaveCheckInDayMicRecord;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WaveCheckInUserTask;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 小时统计时间段处理器实现
 */
@Component
public class HourStatisticPeriodHandler implements StatisticPeriodHandler {

    @Autowired
    private WaveCheckInDataDao waveCheckInDataDao;
    @Autowired
    private WaveCheckInDataConverter converter;


    @Override
    public boolean supports(CheckInDateTypeEnum dateType) {
        return CheckInDateTypeEnum.HOUR.equals(dateType);
    }

    /**
     * user -> ChatStat
     * @param appId
     * @param playerIds
     * @param startDate
     * @param endDate
     * @return
     */
    @Override
    public Map<Long, List<ChatStat>> buildPlayerChatStatMap(int appId, List<Long> playerIds, long startDate, long endDate) {
        if(CollectionUtils.isEmpty(playerIds)) {
            return Collections.emptyMap();
        }
        Date date = new Date(startDate);
        DateTime dateTime = DateUtil.beginOfHour(date);
        List<WcDataPlayerHourRealTime> playerHourRealTimes = waveCheckInDataDao.getHourPlayerChatStatList(appId, playerIds, dateTime);
        return playerHourRealTimes.stream().collect(Collectors.groupingBy(WcDataPlayerHourRealTime::getPlayerId,
                Collectors.mapping(converter::toHourChatStat, Collectors.toList())));
    }

    @Override
    public List<WaveCheckInUserTask> buildWaveCheckInUserTaskList(List<Long> recordIds) {
        return waveCheckInDataDao.getCheckInUserTasksByRecordIds(recordIds);
    }

    @Override
    public Optional<SimpleUserDto> buildHostInfo(Map<Long, SimpleUserDto> simpleUserMap, List<Long> hostIds) {
        if(CollectionUtils.isEmpty(hostIds) || MapUtils.isEmpty(simpleUserMap)) {
            return Optional.empty();
        }
        return Optional.ofNullable(simpleUserMap.get(hostIds.get(0)));
    }

    @Override
    public List<StatisticPeriod> buildPresetStatisticPeriods(long startDate, long endDate) {
        return Collections.emptyList();
    }

    @Override
    public StatisticPeriod buildNormalizedStatisticPeriod(long startTime) {
        // 日统计直接使用其开始时间表示时间段
        return new StatisticPeriod(CheckInDateTypeEnum.HOUR, startTime);
    }

    @Override
    public String formatStatisticPeriod(StatisticPeriod period) {
        LocalDateTime time = LocalDateTime.ofInstant(Instant.ofEpochMilli(period.getStartTime()), ZoneId.systemDefault());
        int startHour = time.getHour();
        int endHour = startHour + 1;
        // 比如 1-2, 23-24
        return String.format("%d-%d", startHour, endHour);
    }

    @Override
    public Map<Long, DayMicCounter> buildDayMicCounterMap(List<WaveCheckInDayMicRecord> dayMicRecords) {
        return Collections.emptyMap();
    }
}
