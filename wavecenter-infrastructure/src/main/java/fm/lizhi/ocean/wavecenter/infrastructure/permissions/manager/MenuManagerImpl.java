package fm.lizhi.ocean.wavecenter.infrastructure.permissions.manager;

import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.datastore.platform.premission.entity.WcRoleMenu;
import fm.lizhi.ocean.wavecenter.datastore.platform.premission.entity.WcRoleMenuExample;
import fm.lizhi.ocean.wavecenter.datastore.platform.premission.mapper.WcRoleMenuMapper;
import fm.lizhi.ocean.wavecenter.service.permissions.manager.MenuManager;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/27 16:21
 */
@Component
public class MenuManagerImpl implements MenuManager {

    @Autowired
    private WcRoleMenuMapper wcRoleMenuMapper;

    @Nonnull
    @Override
    public List<String> getRoleMenuCode(String roleCode) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();

        WcRoleMenuExample example = new WcRoleMenuExample();
        example.createCriteria().andAppIdEqualTo(appId)
                .andRoleCodeEqualTo(roleCode);
        List<WcRoleMenu> wcRoleMenus = wcRoleMenuMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(wcRoleMenus)) {
            return Collections.emptyList();
        }

        return wcRoleMenus.stream().map(WcRoleMenu::getMenuCode).collect(Collectors.toList());
    }

}
