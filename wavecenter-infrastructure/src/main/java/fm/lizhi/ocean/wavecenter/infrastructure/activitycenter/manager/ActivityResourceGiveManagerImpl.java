package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.manager;

import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityResourceGiveStatusEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityResourceTypeEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.CommonGiveStatusEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.DecorateEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.RedisLock;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityFlowResourceGiveRecord;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.ext.ActivityFlowResourceGiveRecordExtMapper;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.ActivityConfig;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.*;
import fm.lizhi.ocean.wavecenter.service.activitycenter.handler.DressUpGiveHandler;
import fm.lizhi.ocean.wavecenter.service.activitycenter.handler.DressUpHandlerFactory;
import fm.lizhi.ocean.wavecenter.service.activitycenter.handler.FlowResourceGiveHandler;
import fm.lizhi.ocean.wavecenter.service.activitycenter.handler.FlowResourceHandlerFactory;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 活动资源发放
 */
@Slf4j
@Component
public class ActivityResourceGiveManagerImpl implements ActivityResourceGiveManager {

    @Autowired
    private ActivityMaterielGiveManager activityMaterielManager;

    @Autowired
    private DressUpHandlerFactory dressUpHandlerFactory;

    @Autowired
    private ActivityApplyManager activityApplyManager;

    @Autowired
    private ActivityConfig activityConfig;

    @Autowired
    private FlowResourceHandlerFactory factory;

    @Autowired
    private ActivityRedisManager activityRedisManager;

    @Autowired
    private ActivityResourceTransferManager activityResourceTransferManager;

    @Autowired
    private ActivityFlowResourceGiveRecordExtMapper flowResourceGiveExtMapper;

    /**
     * 定时任务发放资源操作
     */
    public Result<List<ResourceGiveResultDTO>> giveResourceJobExecute(Integer appId) {
        //查询活动未开始且待发放的资源记录列表
        long currentTime = System.currentTimeMillis();
        List<ActivityResourceGiveDTO> giveResourceList = activityMaterielManager.getWaitGiveResourceList(appId, currentTime, activityConfig.getMaxTryGiveCount());
        if (CollectionUtils.isEmpty(giveResourceList)) {
            return RpcResult.success(Collections.emptyList());
        }

        //过滤掉距离活动开始时间大于预设值的资源
        List<ActivityResourceGiveDTO> resourceGiveDTOS = giveResourceList.stream()
                .filter(item -> (item.getStartTime().getTime() - System.currentTimeMillis()) / 1000 / 60 <= activityConfig.getMinPreactGiveResource())
                .collect(Collectors.toList());
        return giveResourceCore(appId, resourceGiveDTOS, false);
    }

    /**
     * 审核通过后实时发放资源
     *
     * @param appId      应用ID
     * @param activityId 活动ID
     * @return 结果
     */
    public Result<List<ResourceGiveResultDTO>> realTimeGiveResource(Integer appId, Long activityId) {
        List<ActivityResourceGiveDTO> list = activityMaterielManager.getGiveResourceListByActivityId(appId, activityId, Lists.newArrayList(ActivityResourceGiveStatusEnum.WAIT_GIVE.getStatus()));
        if (CollectionUtils.isEmpty(list)) {
            return RpcResult.success(Collections.emptyList());
        }

        return giveResourceCore(appId, list, true);
    }

    /**
     * 修改已经发放的流量资源
     *
     * @param activityId                  活动ID
     * @return 结果
     */
    public Result<String> deleteAlreadyGiveResource(Long activityId) {
        int deleteStatus = 1;
        //查询旧的流量资源的发放记录
        List<ActivityFlowResourceGiveRecord> flowResourceGiveList = flowResourceGiveExtMapper.getFlowResourcesGiveRecordList(activityId, ActivityResourceGiveStatusEnum.SUCCESS.getStatus());
        //oldFlowResourceDTOS 转成map
        Map<Long, ActivityFlowResourceGiveRecord> oldFlowResourceGiveMap = flowResourceGiveList.stream()
                .collect(Collectors.toMap(ActivityFlowResourceGiveRecord::getResourceConfigId, item -> item));

        //flowResourceGiveList 转成map
        Map<Long, ActivityFlowResourceGiveRecord> flowResourceRecordMap = flowResourceGiveList.stream()
                .collect(Collectors.toMap(ActivityFlowResourceGiveRecord::getId, item -> item));
        for (ActivityFlowResourceGiveRecord record : flowResourceGiveList) {
            //遍历流量资源，只要不一样，就直接删除，后面再统一发
            ActivityFlowResourceGiveRecord oldResourceGiveRecord = oldFlowResourceGiveMap.get(record.getResourceConfigId());
            if (oldResourceGiveRecord == null) {
                continue;
            }
            //统一删除，后面再统一补发
            ActivityFlowResourceGiveRecord resourceDTO = flowResourceRecordMap.get(oldResourceGiveRecord.getId());
            FlowResourceGiveHandler handler = factory.getHandler(resourceDTO.getResourceCode());
            DeleteOfficialSeatParamDTO paramDTO = new DeleteOfficialSeatParamDTO().setBizRecordId(oldResourceGiveRecord.getBizRecordId()).setNjId(oldResourceGiveRecord.getUserId());
            Result<Void> result = handler.cancelGiveFlowResource(paramDTO);
            if (RpcResult.isFail(result)) {
                log.warn("删除业务侧流量资源失败，code={}, 原因：{}", result.rCode(), result.getMessage());
                return RpcResult.fail(deleteStatus, "删除业务侧流量资源失败，原因：" + result.getMessage());
            }
        }
        return RpcResult.success();
    }

    /**
     * 发放资源核心操作
     *
     * @param appId            应用ID
     * @param giveResourceList 待发放资源列表
     * @param isRealTimeGive   是否为实时发放
     * @return 发放结果
     */
    private Result<List<ResourceGiveResultDTO>> giveResourceCore(Integer appId, List<ActivityResourceGiveDTO> giveResourceList, boolean isRealTimeGive) {
        int GIVE_RESOURCE_FAIL = 1;
        List<ResourceGiveResultDTO> res = new ArrayList<>();
        for (ActivityResourceGiveDTO giveResource : giveResourceList) {
            //加分布式锁，避免重复发放
            try (RedisLock lock = activityRedisManager.getResourceGiveLock(appId, giveResource.getId())) {
                if (!lock.tryLock()) {
                    //获取不到锁先跳过
                    continue;
                }

                ResourceGiveResultDTO giveResultDTO = null;
                if (giveResource.getType() == ActivityResourceTypeEnum.DRESS_UP.getType()) {
                    if (isRealTimeGive) {
                        continue;
                    }
                    //装扮发放
                    giveResultDTO = dressUpGiveExecute(giveResource);
                } else if (giveResource.getType() == ActivityResourceTypeEnum.FLOW_RESOURCE.getType()) {
                    //流量资源处理
                    giveResultDTO = flowResourceGiveExecute(giveResource, isRealTimeGive);
                }
                if (giveResultDTO != null && !giveResultDTO.isSuccess()) {
                    res.add(giveResultDTO);
                }
            } catch (Exception e) {
                log.error("giveResourceLock error, giveResourceId:{}", giveResource.getId(), e);
                return RpcResult.fail(GIVE_RESOURCE_FAIL);
            }
        }
        return RpcResult.success(res);
    }

    /**
     * 发放装扮操作
     *
     * @param giveResource 发放资源记录
     * @return 发放结果
     */
    private ResourceGiveResultDTO dressUpGiveExecute(ActivityResourceGiveDTO giveResource) {
        List<ActivityDressUpGiveDTO> waitGiveDressUpList = activityMaterielManager.getWaitGiveDressUpList(giveResource.getId());
        if (CollectionUtils.isEmpty(waitGiveDressUpList)) {
            return ResourceGiveResultDTO.success();
        }

        ResourceGiveResultDTO giveResultDTO = new ResourceGiveResultDTO().setSuccess(true);
        List<UpdateResourceGiveParamDTO.ResourceDetailGiveParam> giveDetailResultList = new ArrayList<>();
        String errorMsg = "";
        int errorCode = 0;
        for (ActivityDressUpGiveDTO dressUpGive : waitGiveDressUpList) {
            DressUpGiveHandler handler = dressUpHandlerFactory.getHandler(dressUpGive.getType());
            if (handler == null) {
                log.error("giveDressUp handler is null, type:{}", dressUpGive.getType());
                continue;
            }

            DressUpGiveContext giveContext = new DressUpGiveContext().setResourceGiveDTO(giveResource).setDressUpGiveDTO(dressUpGive);
            Result<Void> result = handler.giveDressUp(giveContext);
            if (!RpcResult.isSuccess(result)) {
                //记录发放失败的信息
                addResourceGiveFailResult(giveResultDTO, dressUpGive.getId(), DecorateEnum.getDecorateName(dressUpGive.getType()), result.getMessage());
                //装扮是一对多的关系，这里取最后一个错误的信息即可
                errorMsg = result.getMessage();
                errorCode = result.rCode();
            }

            int status = RpcResult.isSuccess(result) ? CommonGiveStatusEnum.SUCCESS.getStatus() : CommonGiveStatusEnum.GIVE_FAIL.getStatus();
            addResourceGiveDetailResult(giveDetailResultList, dressUpGive.getId(), dressUpGive.getStatus(), status, 0L);
        }

        //只要有一个发放失败，就把主表设置成失败
        UpdateResourceGiveParamDTO giveParamDTO = buildResourceGiveParamDTO(giveResource, giveDetailResultList, errorCode, errorMsg);
        //批量状态和主记录状态
        boolean res = activityMaterielManager.updateResourceGiveRecord(giveParamDTO);
        log.info("giveDressUp, activityId:{}, resourceId:{},targetResourceStatus:{}, res:{}", giveResource.getActivityId(), giveResource.getId(), giveParamDTO.getTargetStatus(), res);
        return giveResultDTO;
    }

    /**
     * 流量资源发放操作
     *
     * @param giveResource   待发放的资源记录
     * @param isRealTimeGive 是否立即发放
     * @return 发放结果实体
     */
    public ResourceGiveResultDTO flowResourceGiveExecute(ActivityResourceGiveDTO giveResource, boolean isRealTimeGive) {
        List<ActivityFlowResourceGiveDTO> waitGiveFlowResourceList = activityMaterielManager.getWaitGiveFlowResouceList(giveResource.getId());
        if (CollectionUtils.isEmpty(waitGiveFlowResourceList)) {
            log.info("flowResourceGiveExecute.waitGiveFlowResourceList is empty. activityId: {},resourceListSize:{}",
                    giveResource.getActivityId(), waitGiveFlowResourceList.size());
            return ResourceGiveResultDTO.success();
        }

        Long levelId = activityApplyManager.getActivityLevelById(giveResource.getActivityId());
        if (levelId == null) {
            log.info("flowResourceGiveExecute.levelId is empty. activityId: {}", giveResource.getActivityId());
            return ResourceGiveResultDTO.fail();
        }

        ResourceGiveResultDTO giveResultDTO = new ResourceGiveResultDTO().setSuccess(true);
        List<UpdateResourceGiveParamDTO.ResourceDetailGiveParam> giveDetailResultList = new ArrayList<>();
        String errorMsg = "";
        int errorCode = 0;
        for (ActivityFlowResourceGiveDTO flowResource : waitGiveFlowResourceList) {
            //校验是否可发放改资源
            if (!isCanGiveResource(isRealTimeGive, flowResource.getResourceCode())) {
                return ResourceGiveResultDTO.success();
            }

            FlowResourceGiveHandler handler = factory.getHandler(flowResource.getResourceCode());
            if (handler == null) {
                log.error("handler is null, resourceCode:{}", flowResource.getResourceCode());
                continue;
            }

            //构建上下文
            FlowResourceContext context = new FlowResourceContext().setLevelId(levelId).setFlowResourceGiveDTO(flowResource).setResourceGiveDTO(giveResource);
            //替换图片地址
            boolean replaceRes = replaceImageUrl(giveResource.getAppId(), flowResource);
            if (!replaceRes) {
                log.info("replace image url fail, resourceCode:{}, activityId:{}, activityName:{}", flowResource.getResourceCode(), giveResource.getActivityId(), giveResource.getActivityName());
                //补全发放失败的信息
                addResourceGiveFailResult(giveResultDTO, flowResource.getId(), flowResource.getResourceName(), flowResource.getResourceName() + "发放失败");
                addResourceGiveDetailResult(giveDetailResultList, flowResource.getId(), flowResource.getStatus(), CommonGiveStatusEnum.GIVE_FAIL.getStatus(), 0L);
                errorMsg = "图片资源地址不存在";
                errorCode = CommonGiveStatusEnum.GIVE_FAIL.getStatus();
                continue;
            }
            //资源发放操作
            Result<GiveFlowResourceResDTO> result = handler.giveFlowResource(context);
            if (RpcResult.isFail(result)) {
                log.info("give flow resource fail. resourceCode:{}, activityId:{}, activityName:{}, errorMsg:{}",
                        flowResource.getResourceCode(), giveResource.getActivityId(), giveResource.getActivityName(), result.getMessage());
                //补全发放失败的信息
                addResourceGiveFailResult(giveResultDTO, flowResource.getId(), flowResource.getResourceName(), result.getMessage());
                addResourceGiveDetailResult(giveDetailResultList, flowResource.getId(), flowResource.getStatus(), CommonGiveStatusEnum.GIVE_FAIL.getStatus(), 0L);
                errorMsg = result.getMessage();
                errorCode = result.rCode();
                continue;
            }

            log.info("give flow resource success， resourceCode:{}, activityId:{}, activityName:{}, ", flowResource.getResourceCode(), giveResource.getActivityId(), giveResource.getActivityName());
            addResourceGiveDetailResult(giveDetailResultList, flowResource.getId(), flowResource.getStatus(), CommonGiveStatusEnum.SUCCESS.getStatus(), result.target().getBizRecordId());
        }

        //只要有一个发放失败，就把主表设置成失败
        UpdateResourceGiveParamDTO giveParamDTO = buildResourceGiveParamDTO(giveResource, giveDetailResultList, errorCode, errorMsg);
        boolean res = activityMaterielManager.updateResourceGiveRecord(giveParamDTO);
        log.info("giveFlowResource, activityId:{}, resourceId:{},targetResourceStatus:{}, res:{}", giveResource.getActivityId(), giveResource.getId(), giveParamDTO.getTargetStatus(), res);
        //只是修改表记录失败，就不管了
        return giveResultDTO;
    }

    private UpdateResourceGiveParamDTO buildResourceGiveParamDTO(ActivityResourceGiveDTO giveResource,
                                                                 List<UpdateResourceGiveParamDTO.ResourceDetailGiveParam> giveDetailResultList,
                                                                 Integer errorCode, String errorMsg) {
        UpdateResourceGiveParamDTO giveParamDTO = initGiveUpdateParamDTO(giveResource.getId(), giveResource.getType());
        Optional<UpdateResourceGiveParamDTO.ResourceDetailGiveParam> filterOption = giveDetailResultList.stream().filter(detail -> detail.getTargetStatus() == CommonGiveStatusEnum.GIVE_FAIL.getStatus()).findFirst();
        boolean isExistFail = filterOption.isPresent();
        //只要有一个发放失败，就把主表设置成失败
        int targetResourceStatus = isExistFail ? ActivityResourceGiveStatusEnum.GIVE_FAIL.getStatus() : ActivityResourceGiveStatusEnum.SUCCESS.getStatus();
        giveParamDTO.setTargetStatus(targetResourceStatus).setTryCount(isExistFail ? giveResource.getTryCount() + 1 : giveResource.getTryCount());

        List<UpdateResourceGiveParamDTO.ResourceDetailGiveParam> detailParams = new ArrayList<>();
        for (UpdateResourceGiveParamDTO.ResourceDetailGiveParam detail : giveDetailResultList) {
            UpdateResourceGiveParamDTO.ResourceDetailGiveParam detailGiveParam = new UpdateResourceGiveParamDTO.ResourceDetailGiveParam()
                    .setId(detail.getId());
            detailGiveParam.setTargetStatus(detail.getTargetStatus());
            detailGiveParam.setOriginalStatus(detail.getOriginalStatus());
            detailGiveParam.setBizRecordId(detail.getBizRecordId());
            detailParams.add(detailGiveParam);
        }
        giveParamDTO.setDetailParams(detailParams);
        giveParamDTO.setOriginalStatus(giveResource.getStatus());
        giveParamDTO.setErrorMsg(errorMsg);
        giveParamDTO.setErrorCode(errorCode);

        if (giveParamDTO.getTargetStatus() == ActivityResourceGiveStatusEnum.SUCCESS.getStatus()) {
            giveParamDTO.setErrorMsg("");
            giveParamDTO.setErrorCode(0);
        }
        return giveParamDTO;
    }

    /**
     * 初始化资源发放修改记录参数
     *
     * @param giveRecordId 发放记录id
     * @param resourceType 资源类型
     * @return 参数实体
     */
    private UpdateResourceGiveParamDTO initGiveUpdateParamDTO(Long giveRecordId, Integer resourceType) {
        return new UpdateResourceGiveParamDTO().setId(giveRecordId).setResourceType(resourceType);
    }

    /**
     * 记录发放流量资源结果
     *
     * @param result         发放结果实体
     * @param detailRecordId 流量资源发放明细记录id
     * @param resourceName   资源名
     */
    private void addResourceGiveFailResult(ResourceGiveResultDTO result, Long detailRecordId, String resourceName, String errorMessage) {
        List<ResourceGiveResultDTO.ResourceResultDetailBean> errorList = result.getErrorList();
        if (CollectionUtils.isEmpty(errorList)) {
            errorList = new ArrayList<>();
        }

        ResourceGiveResultDTO.ResourceResultDetailBean resultDetailBean = new ResourceGiveResultDTO.ResourceResultDetailBean()
                .setId(detailRecordId)
                .setErrorMessage(errorMessage)
                .setName(resourceName);
        errorList.add(resultDetailBean);
        result.setErrorList(errorList);
        result.setSuccess(false);
    }

    private void addResourceGiveDetailResult(List<UpdateResourceGiveParamDTO.ResourceDetailGiveParam> giveDetailResultList,
                                             Long detailRecordId, int sourceStatus, int targetStatus, Long bizRecordId) {
        UpdateResourceGiveParamDTO.ResourceDetailGiveParam detailGiveParam = new UpdateResourceGiveParamDTO.ResourceDetailGiveParam()
                .setId(detailRecordId)
                .setOriginalStatus(sourceStatus)
                .setTargetStatus(targetStatus)
                .setBizRecordId(bizRecordId == null ? 0 : bizRecordId);
        giveDetailResultList.add(detailGiveParam);
    }

    private void recordGiveDressUpErrorInfo(ResourceGiveResultDTO result, ActivityDressUpGiveDTO dressUpGiveDto, String errorMessage) {
        List<ResourceGiveResultDTO.ResourceResultDetailBean> errorList = result.getErrorList();
        if (CollectionUtils.isEmpty(errorList)) {
            errorList = new ArrayList<>();
        }

        ResourceGiveResultDTO.ResourceResultDetailBean resultDetailBean = new ResourceGiveResultDTO.ResourceResultDetailBean()
                .setId(dressUpGiveDto.getId())
                .setErrorMessage(errorMessage)
                .setName(DecorateEnum.getDecorateName(dressUpGiveDto.getType()));
        errorList.add(resultDetailBean);
        result.setErrorList(errorList);
        result.setSuccess(false);
    }

    /**
     * 记录发放状态
     *
     * @param giveParamDTO 发放结果记录
     * @param flowResource 发放的资源
     * @param result       发放结果
     */
    private void recordGiveStatus(UpdateResourceGiveParamDTO giveParamDTO, ActivityFlowResourceGiveDTO flowResource, Result<Void> result) {
        List<UpdateResourceGiveParamDTO.ResourceDetailGiveParam> detailParams = giveParamDTO.getDetailParams();
        if (CollectionUtils.isEmpty(detailParams)) {
            detailParams = new ArrayList<>();
        }
        if (RpcResult.isFail(result)) {
            giveParamDTO.setErrorCode(result.rCode()).setErrorMsg(result.getMessage());
        }
        UpdateResourceGiveParamDTO.ResourceDetailGiveParam detailGiveParam = new UpdateResourceGiveParamDTO.ResourceDetailGiveParam()
                .setId(flowResource.getId());
        detailGiveParam.setTargetStatus(RpcResult.isSuccess(result) ? CommonGiveStatusEnum.SUCCESS.getStatus() : CommonGiveStatusEnum.GIVE_FAIL.getStatus());
        detailParams.add(detailGiveParam);
        giveParamDTO.setDetailParams(detailParams);
    }

    /**
     * 替换图片url为具体业务的
     *
     * @param appId        业务id
     * @param flowResource 流量资源发放记录
     */
    private boolean replaceImageUrl(Integer appId, ActivityFlowResourceGiveDTO flowResource) {
        if (StringUtils.isEmpty(flowResource.getImageUrl())) {
            return true;
        }
        ActivityResourceTransferResultDTO resourceTransfer = activityResourceTransferManager.getResourceTransfer(appId, flowResource.getImageUrl());
        if (resourceTransfer == null || StringUtils.isEmpty(resourceTransfer.getTargetUri())) {
            log.warn("replaceImageUrl fail, resourceId:{}, appId:{}", flowResource.getId(), appId);
            return false;
        }
        flowResource.setImageUrl(resourceTransfer.getTargetUri());
        return true;
    }

    /**
     * 是否可发放该资源
     *
     * @param isRealTimeGive 是否是实时发放
     * @param resourceCode   资源code
     * @return true: 可发放
     */
    private boolean isCanGiveResource(boolean isRealTimeGive, String resourceCode) {
        if (isRealTimeGive && !activityConfig.getRealTimeGiveResourceCodes().contains(resourceCode)) {
            //如果是实时发放，且不包含该资源，则跳过
            return false;
        }
        if (!isRealTimeGive && activityConfig.getRealTimeGiveResourceCodes().contains(resourceCode)) {
            //定时任务发放，不发放实时资源，跳过
            return false;
        }
        return true;
    }
}
