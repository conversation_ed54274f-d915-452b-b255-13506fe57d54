package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.dao;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.*;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.mapper.WcGrowCapabilityMetricRefMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.mapper.WcGrowRoomAbilityWeekCapabilityMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.mapper.ext.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListValuedMap;
import org.apache.commons.collections4.SetValuedMap;
import org.apache.commons.collections4.multimap.ArrayListValuedHashMap;
import org.apache.commons.collections4.multimap.HashSetValuedHashMap;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class WcGrowAbilityDao {

    @Autowired
    private WcGrowCapabilityMetricRefExtMapper wcGrowCapabilityMetricRefExtMapper;
    @Autowired
    private WcGrowCapabilityMetricRefMapper wcGrowCapabilityMetricRefMapper;

    @Autowired
    private WcGrowRoomAbilityWeekCapabilityMapper wcGrowRoomAbilityWeekCapabilityMapper;

    @Autowired
    private WcGrowRoomMetricValueExtMapper WcGrowRoomMetricValueExtMapper;

    @Autowired
    private WcGrowPlayerAbilityWeekExtMapper wcGrowPlayerAbilityWeekExtMapper;

    @Autowired
    private WcGrowPlayerAbilityWeekCapabilityExtMapper wcGrowPlayerAbilityWeekCapabilityExtMapper;

    @Autowired
    private WcGrowPlayerMetricValueExtMapper wcGrowPlayerMetricValueExtMapper;

    /**
     * 获取厅的周能力表现的能力项列表
     *
     * @param appId         应用id
     * @param roomId        厅id
     * @param startWeekDate 考核周期开始日期
     * @return 厅的周能力表现的能力项列表
     */
    public List<WcGrowRoomAbilityWeekCapability> getRoomWeekCapabilities(int appId, long roomId, Date startWeekDate) {
        Validate.notNull(startWeekDate);
        WcGrowRoomAbilityWeekCapability selectMany = new WcGrowRoomAbilityWeekCapability();
        selectMany.setDeployEnv(ConfigUtils.getEnvRequired().name());
        selectMany.setAppId(appId);
        selectMany.setRoomId(roomId);
        selectMany.setStartWeekDate(startWeekDate);
        return wcGrowRoomAbilityWeekCapabilityMapper.selectMany(selectMany);
    }

    /**
     * 获取指定考核周期的能力项的周指标关联列表
     *
     * @param appId           应用id
     * @param startWeekDate   考核周期开始日期
     * @return 能力项的周指标关联列表
     */
    public List<WcGrowCapabilityMetricRef> getCapabilityMetricRefs(int appId, Date startWeekDate) {
        Validate.notNull(startWeekDate);
        WcGrowCapabilityMetricRef selectMany = new WcGrowCapabilityMetricRef();
        selectMany.setDeployEnv(ConfigUtils.getEnvRequired().name());
        selectMany.setAppId(appId);
        selectMany.setStartWeekDate(startWeekDate);
        return wcGrowCapabilityMetricRefMapper.selectMany(selectMany);
    }

    /**
     * 获取指定考核周期的能力项与其周指标列表的映射关系, key: 能力项code, value: 指标code列表
     *
     * @param appId           应用id
     * @param startWeekDate   考核周期开始日期
     * @return 能力项与其周指标列表的映射关系
     */
    public ListValuedMap<String, String> getCapabilityMetricCodesMap(int appId, Date startWeekDate) {
        List<WcGrowCapabilityMetricRef> refs = getCapabilityMetricRefs(appId, startWeekDate);
        ArrayListValuedHashMap<String, String> capabilityMetricCodesMap = new ArrayListValuedHashMap<>();
        for (WcGrowCapabilityMetricRef ref : refs) {
            capabilityMetricCodesMap.put(ref.getCapabilityCode(), ref.getMetricCode());
        }
        return capabilityMetricCodesMap;
    }

    /**
     * 获取指定考核周期范围内的能力项的周指标关联列表
     *
     * @param appId            应用id
     * @param minStartWeekDate 考核周期开始日期的最小值, 包含
     * @param maxStartWeekDate 考核周期开始日期的最大值, 包含
     * @return 能力项的周指标关联列表
     */
    public List<WcGrowCapabilityMetricRef> getWeeksCapabilityMetricRefs(int appId, Date minStartWeekDate, Date maxStartWeekDate) {
        Validate.notNull(minStartWeekDate);
        Validate.notNull(maxStartWeekDate);
        String deployEnv = ConfigUtils.getEnvRequired().name();
        return wcGrowCapabilityMetricRefExtMapper.getWeeksCapabilityMetricRefs(deployEnv, appId, minStartWeekDate, maxStartWeekDate);
    }

    /**
     * 获取指定考核周期范围内的能力项与其周指标列表的映射关系, key: 考核周期开始日期, value: 能力项code -> 指标code列表
     *
     * @param appId            应用id
     * @param minStartWeekDate 考核周期开始日期的最小值, 包含
     * @param maxStartWeekDate 考核周期开始日期的最大值, 包含
     * @return 能力项与其周指标列表的映射关系
     */
    public Map<Date, SetValuedMap<String, String>> getWeeksCapabilityMetricCodesMap(int appId, Date minStartWeekDate, Date maxStartWeekDate) {
        List<WcGrowCapabilityMetricRef> refs = getWeeksCapabilityMetricRefs(appId, minStartWeekDate, maxStartWeekDate);
        HashMap<Date, SetValuedMap<String, String>> weekCapabilityMetricCodesMap = new HashMap<>();
        for (WcGrowCapabilityMetricRef ref : refs) {
            Date startWeekDate = ref.getStartWeekDate();
            String capabilityCode = ref.getCapabilityCode();
            String metricCode = ref.getMetricCode();
            SetValuedMap<String, String> metricCodesMap = weekCapabilityMetricCodesMap.computeIfAbsent(startWeekDate, k -> new HashSetValuedHashMap<>());
            metricCodesMap.put(capabilityCode, metricCode);
        }
        return weekCapabilityMetricCodesMap;
    }

    /**
     * 获取指定考核周期范围内的厅的周指标值列表
     *
     * @param appId            应用id
     * @param roomId           厅id
     * @param minStartWeekDate 考核周期开始日期的最小值, 包含
     * @param maxStartWeekDate 考核周期开始日期的最大值, 包含
     * @return 厅的周指标值列表
     */
    public List<WcGrowRoomMetricValue> getWeeksRoomMetricValues(int appId, long roomId, Date minStartWeekDate, Date maxStartWeekDate) {
        Validate.notNull(minStartWeekDate);
        Validate.notNull(maxStartWeekDate);
        return WcGrowRoomMetricValueExtMapper.getWeeksRoomMetricValues(appId, roomId, minStartWeekDate, maxStartWeekDate);
    }

    /**
     * 获取指定考核周期范围内的厅的周指标值列表, key: 考核周期开始日期, value: 周指标值
     *
     * @param appId            应用id
     * @param roomId           厅id
     * @param minStartWeekDate 考核周期开始日期的最小值, 包含
     * @param maxStartWeekDate 考核周期开始日期的最大值, 包含
     * @return 厅的周指标值列表映射
     */
    public Map<Date, WcGrowRoomMetricValue> getWeeksRoomMetricValueMap(int appId, long roomId, Date minStartWeekDate, Date maxStartWeekDate) {
        List<WcGrowRoomMetricValue> metricValues = getWeeksRoomMetricValues(appId, roomId, minStartWeekDate, maxStartWeekDate);
        HashMap<Date, WcGrowRoomMetricValue> startWeekDateToMetricValueMap = new HashMap<>();
        for (WcGrowRoomMetricValue metricValue : metricValues) {
            startWeekDateToMetricValueMap.put(metricValue.getStartWeekDate(), metricValue);
        }
        return startWeekDateToMetricValueMap;
    }

    /**
     * 获取厅主播的综合分排行列表
     *
     * @param appId             应用id
     * @param roomId            厅id
     * @param startWeekDate     考核周期开始日期
     * @param firstSignInFamily 是否首次签约该公会, 如果为null则不限制
     * @param minSignDate       最小签约日期, 包含, 如果为null则不限制
     * @param orderType         排序方向
     * @return 厅主播的能力表现列表
     */
    public List<WcGrowPlayerAbilityWeek> getPlayerAbilityWeekRanks(
            int appId, long roomId, Date startWeekDate, Boolean firstSignInFamily, Date minSignDate, OrderType orderType) {
        Validate.notNull(startWeekDate);
        String deployEnv = ConfigUtils.getEnvRequired().name();
        return wcGrowPlayerAbilityWeekExtMapper.getPlayerAbilityWeekRanks(
                deployEnv, appId, roomId, startWeekDate, firstSignInFamily, minSignDate, orderType);
    }

    /**
     * 获取厅主播的能力分排行列表
     *
     * @param appId          应用id
     * @param roomId         厅id
     * @param startWeekDate  考核周期开始日期
     * @param capabilityCode 能力项code
     * @param minSignDate    最小签约日期, 包含, 如果为null则不限制
     * @param orderType      排序方向
     * @return 厅主播的能力项表现列表
     */
    public List<WcGrowPlayerAbilityWeekCapability> getPlayerAbilityWeekCapabilityRanks(
            int appId, long roomId, Date startWeekDate, String capabilityCode, Boolean firstSignInFamily, Date minSignDate, OrderType orderType) {
        Validate.notNull(startWeekDate);
        Validate.notBlank(capabilityCode);
        String deployEnv = ConfigUtils.getEnvRequired().name();
        return wcGrowPlayerAbilityWeekCapabilityExtMapper.getPlayerAbilityWeekCapabilityRanks(
                deployEnv, appId, roomId, startWeekDate, capabilityCode, firstSignInFamily, minSignDate, orderType);
    }

    /**
     * 获取指定厅主播的能力项表现列表
     *
     * @param appId            应用id
     * @param roomId           厅id
     * @param playerId         主播id
     * @param minStartWeekDate 考核周期开始日期的最小值, 包含
     * @param maxStartWeekDate 考核周期开始日期的最大值, 包含
     * @return 指定厅主播的能力项表现列表
     */
    public List<WcGrowPlayerAbilityWeekCapability> getPlayerAbilityWeekCapabilities(
            int appId, long roomId, long playerId, Date minStartWeekDate, Date maxStartWeekDate) {
        String deployEnv = ConfigUtils.getEnvRequired().name();
        return wcGrowPlayerAbilityWeekCapabilityExtMapper.getPlayerAbilityWeekCapabilities(
                deployEnv, appId, roomId, playerId, minStartWeekDate, maxStartWeekDate);
    }

    /**
     * 获取指定厅主播的考核周期开始日期到能力项表现列表的映射, key: 考核周期开始日期, value: 能力项表现列表
     *
     * @param appId            应用id
     * @param roomId           厅id
     * @param playerId         主播id
     * @param minStartWeekDate 考核周期开始日期的最小值, 包含
     * @param maxStartWeekDate 考核周期开始日期的最大值, 包含
     * @return 指定厅主播的考核周期开始日期到能力项表现列表的映射
     */
    public ListValuedMap<Date, WcGrowPlayerAbilityWeekCapability> getPlayerAbilityWeekCapabilitiesMap(
            int appId, long roomId, long playerId, Date minStartWeekDate, Date maxStartWeekDate) {
        List<WcGrowPlayerAbilityWeekCapability> capabilities = getPlayerAbilityWeekCapabilities(appId, roomId, playerId, minStartWeekDate, maxStartWeekDate);
        ArrayListValuedHashMap<Date, WcGrowPlayerAbilityWeekCapability> startWeekDateToCapabilitiesMap = new ArrayListValuedHashMap<>();
        for (WcGrowPlayerAbilityWeekCapability capability : capabilities) {
            startWeekDateToCapabilitiesMap.put(capability.getStartWeekDate(), capability);
        }
        return startWeekDateToCapabilitiesMap;
    }

    /**
     * 获取指定考核周期范围内的厅主播的周指标值列表
     *
     * @param appId            应用id
     * @param roomId           厅id
     * @param playerId         主播id
     * @param minStartWeekDate 考核周期开始日期的最小值, 包含
     * @param maxStartWeekDate 考核周期开始日期的最大值, 包含
     * @return 厅主播的周指标值列表
     */
    public List<WcGrowPlayerMetricValue> getWeeksPlayerMetricValues(
            int appId, long roomId, long playerId, Date minStartWeekDate, Date maxStartWeekDate) {
        Validate.notNull(minStartWeekDate);
        Validate.notNull(maxStartWeekDate);
        return wcGrowPlayerMetricValueExtMapper.getWeeksPlayerMetricValues(appId, roomId, playerId, minStartWeekDate, maxStartWeekDate);
    }

    /**
     * 获取指定考核周期范围内的厅主播的周指标值列表, key: 考核周期开始日期, value: 周指标值
     *
     * @param appId            应用id
     * @param roomId           厅id
     * @param playerId         主播id
     * @param minStartWeekDate 考核周期开始日期的最小值, 包含
     * @param maxStartWeekDate 考核周期开始日期的最大值, 包含
     * @return 厅主播的周指标值列表映射
     */
    public Map<Date, WcGrowPlayerMetricValue> getWeeksPlayerMetricValueMap(
            int appId, long roomId, long playerId, Date minStartWeekDate, Date maxStartWeekDate) {
        List<WcGrowPlayerMetricValue> metricValues = getWeeksPlayerMetricValues(appId, roomId, playerId, minStartWeekDate, maxStartWeekDate);
        HashMap<Date, WcGrowPlayerMetricValue> startWeekDateToMetricValueMap = new HashMap<>();
        for (WcGrowPlayerMetricValue metricValue : metricValues) {
            startWeekDateToMetricValueMap.put(metricValue.getStartWeekDate(), metricValue);
        }
        return startWeekDateToMetricValueMap;
    }
}
