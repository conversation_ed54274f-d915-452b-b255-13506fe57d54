package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.constants;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Table;
import fm.lizhi.ocean.wavecenter.api.common.constants.MetricsEnum;
import fm.lizhi.ocean.wavecenter.base.util.CalculateUtil;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.*;
import org.springframework.util.ReflectionUtils;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 指标常量
 * <AUTHOR>
 * @date 2024/4/19 23:48
 */
public class MetricsConstants {

    /**
     * 持久化类忽略的字段名
     */
    private static final List<String> ignoreFieldName = Lists.newArrayList(
            WcDataRoomMonth.Fields.id,
            WcDataRoomMonth.Fields.appId,
            WcDataRoomMonth.Fields.statMonth,
            WcDataRoomMonth.Fields.statYear,
            WcDataRoomWeek.Fields.startWeekDate,
            WcDataRoomWeek.Fields.endWeekDate,
            WcDataRoomDay.Fields.statDate,
            WcDataRoomDay.Fields.statDateValue,
            WcDataRoomMonth.Fields.roomId,
            WcDataRoomMonth.Fields.createTime,
            WcDataRoomMonth.Fields.modifyTime
    );

    /**
     * 需要转换为百分比的字段名后缀
     */
    private static final List<String> rateFieldNameSuffix = Lists.newArrayList(
            "Rate", "Performance"
    );

    private static final Table<MetricsNs, String, MetricsMeta> METRICS_MAP = HashBasedTable.create();

    static {
        for (MetricsEnum value : MetricsEnum.values()) {
            METRICS_MAP.put(MetricsNs.DEFAULT, value.getValue(), new MetricsMeta(value.getValue()));
        }

        convertPoField(WcDataRoomDay.class);
        convertPoField(WcDataFamilyDay.class);
        convertPoField(WcDataPlayerDay.class);
        convertPoField(WcDataRoomWeek.class);
        convertPoField(WcDataRoomMonth.class);
        convertPoField(WcDataPlayerRoomDay.class);
        convertPoField(WcDataPlayerRoomWeek.class);
        convertPoField(WcDataPlayerRoomMonth.class);
        convertPoField(WcDataRoomFamilyDay.class);
        convertPoField(WcDataRoomFamilyWeek.class);
        convertPoField(WcDataRoomFamilyMonth.class);

        //需要特殊计算的字段
        //公会数据上麦主播数字段别名，因为上麦主播数，在公会数据中的字段名是upGuestPlayerCnt，在厅的数据中的字段名是signUpGuestPlayerCnt
        MetricsMeta meta = new MetricsMeta("signUpGuestPlayerCnt", "up_guest_player_cnt", WcDataFamilyDay.Fields.upGuestPlayerCnt);
        METRICS_MAP.put(MetricsNs.FAMILY, "signUpGuestPlayerCnt", meta);
    }

    public static Optional<MetricsMeta> getMetricsMeta(String metricsName) {
        return Optional.ofNullable(METRICS_MAP.get(MetricsNs.DEFAULT, metricsName));
    }

    public static Optional<MetricsMeta> getMetricsMeta(MetricsNs ns, String metricsName) {
        MetricsMeta meta = METRICS_MAP.get(ns, metricsName);
        if (meta != null) {
            return Optional.of(meta);
        }
        return getMetricsMeta(metricsName);
    }

    private static void putDefaultMetricsMeta(String metricsName, MetricsMeta.ValueFactory valueFactory) {
        METRICS_MAP.put(MetricsNs.DEFAULT, metricsName, new MetricsMeta(metricsName, valueFactory));
    }

    private static void convertPoField(Class<?> clazz){
        ReflectionUtils.doWithLocalFields(clazz, field -> {
            String name = field.getName();
            if (ignoreFieldName.contains(name)) {
                return;
            }

            String poName = field.getName();
            Column column = field.getAnnotation(Column.class);
            if (column != null) {
                poName = column.name();
            }

            for (String fieldNameSuffix : rateFieldNameSuffix) {
                if (name.endsWith(fieldNameSuffix)) {
                    METRICS_MAP.put(MetricsNs.DEFAULT, field.getName(), new MetricsMeta(field.getName(), poName, field.getName(), (calculateMetrics, baseValue) -> {
                        String strValue = baseValue.get(calculateMetrics.getAliasName());
                        if (strValue == null) {
                            strValue = "0";
                        }
                        //保留2位小数
                        BigDecimal bigDecimal = new BigDecimal(strValue);
                        return CalculateUtil.formatRate(bigDecimal);
                    }));
                    return;
                }
            }
            METRICS_MAP.put(MetricsNs.DEFAULT, field.getName(), new MetricsMeta(field.getName(), poName, field.getName()));
        });
    }

}
