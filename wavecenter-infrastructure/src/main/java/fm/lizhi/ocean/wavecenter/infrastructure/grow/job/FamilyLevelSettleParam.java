package fm.lizhi.ocean.wavecenter.infrastructure.grow.job;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/19 14:37
 */
@Data
public class FamilyLevelSettleParam {

    /**
     * 特殊命令执行
     * resetApp  重置业务线周期的结算标识
     * all 手动结算全部
     */
    private String cmd;

    /**
     * 结算指定公会
     */
    private List<Long> familyIds;

    /**
     * 指定公会结算为某个等级
     * key=familyId,value=levelId
     */
    private Map<Long, Long> familyLevelMap;

    /**
     * 结算周期开始时间
     * yyyyMMdd
     */
    private Integer startDay;

    /**
     * 结算周期结束时间
     * yyyyMMdd
     */
    private Integer endDay;

}
