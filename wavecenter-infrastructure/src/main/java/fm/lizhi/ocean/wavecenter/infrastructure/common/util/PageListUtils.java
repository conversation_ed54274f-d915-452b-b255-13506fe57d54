package fm.lizhi.ocean.wavecenter.infrastructure.common.util;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import org.apache.commons.lang3.ArrayUtils;

/**
 * 分页列表工具类
 */
public class PageListUtils {

    /**
     * 复制一个分页列表, 但不复制元素. 主要用于分页列表的类型转换
     *
     * @param source 源分页列表
     * @param <T>    源类型
     * @param <R>    目标类型
     * @return 复制后的分页列表
     */
    public static <T, R> PageList<R> copyWithoutElement(PageList<T> source) {
        PageList<R> copy = new PageList<>();
        copy.setTotal(source.getTotal());
        copy.setTotalPage(source.getTotalPage());
        copy.setPageNumber(source.getPageNumber());
        copy.setPageSize(source.getPageSize());
        copy.setPrevPageNumber(source.getPrevPageNumber());
        copy.setNextPageNumber(source.getNextPageNumber());
        copy.setHasPrevPage(source.isHasPrevPage());
        copy.setHasNextPage(source.isHasNextPage());
        copy.setPageNumbers(ArrayUtils.clone(source.getPageNumbers()));
        return copy;
    }
}
