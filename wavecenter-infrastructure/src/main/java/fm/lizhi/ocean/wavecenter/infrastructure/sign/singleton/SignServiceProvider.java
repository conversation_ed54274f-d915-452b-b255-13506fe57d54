package fm.lizhi.ocean.wavecenter.infrastructure.sign.singleton;

import fm.hy.family.api.FamilySignService;
import fm.hy.family.api.PlayerSignService;
import fm.hy.family.api.SignService;
import fm.lizhi.common.dubbo.DubboClientBuilder;
import fm.lizhi.ocean.lamp.common.generic.annotation.ScanBusinessProviderAPI;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.ServiceProviderConstants;
import fm.lizhi.trade.contract.api.SignContractService;
import fm.lizhi.xm.family.api.SettleRatioService;
import fm.pp.family.api.FamilyNjService;
import fm.pp.family.api.NewHallService;
import fm.pp.family.api.SocietyService;
import fm.pp.family.api.UnwindContractService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/10/9 16:18
 */
@ScanBusinessProviderAPI(values = {
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = SocietyService.class),
})
@Configuration
public class SignServiceProvider {

    @Bean
    public UnwindContractService ppUnwindContractService(){
        return new DubboClientBuilder<>(UnwindContractService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public fm.lizhi.xm.family.api.UnwindContractService xmUnwindContractService(){
        return new DubboClientBuilder<>(fm.lizhi.xm.family.api.UnwindContractService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public SignContractService signContractService(){
        return new DubboClientBuilder<>(SignContractService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public PlayerSignService hyPlayerSignService(){
        return new DubboClientBuilder<>(PlayerSignService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public FamilyNjService ppFamilyNjService(){
        return new DubboClientBuilder<>(FamilyNjService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public fm.lizhi.xm.family.api.PlayerSignService xmPlayerSignService(){
        return new DubboClientBuilder<>(fm.lizhi.xm.family.api.PlayerSignService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public FamilySignService hyFamilySignService(){
        return new DubboClientBuilder<>(FamilySignService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public SettleRatioService xmSettleRatioService(){
        return new DubboClientBuilder<>(SettleRatioService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public SignService hySignService(){
        return new DubboClientBuilder<>(SignService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.writeRetries)
                .build();
    }

    @Bean
    public fm.lizhi.xm.family.api.SignService xmSignService(){
        return new DubboClientBuilder<>(fm.lizhi.xm.family.api.SignService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.writeRetries)
                .build();
    }

    @Bean
    public fm.pp.family.api.SignService ppSignService() {
        return new DubboClientBuilder<>(fm.pp.family.api.SignService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.writeRetries)
                .build();
    }

    @Bean
    public NewHallService ppNewHallService(){
        return new DubboClientBuilder<>(NewHallService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

}
