package fm.lizhi.ocean.wavecenter.infrastructure.common.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PaySettlePeriodNumberEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PayTenantCodeEnum;
import fm.lizhi.ocean.wavecenter.service.common.dto.PaySettlePeriodDto;
import fm.lizhi.pay.settle.api.creativecenter.CreatorDataQueryService;
import fm.lizhi.pay.settle.protocol.creativecenter.CreatorDataQueryProto;
import fm.lizhi.pay.settle.settleenum.creativecenter.PeriodTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 支付服务公共接口
 * <AUTHOR>
 */
@Component
@Slf4j
public class CreatorDataQueryCommonManager {


    @Autowired
    private CreatorDataQueryService creatorDataQueryService;


    /**
     * 获取结算周期
     */
    public PaySettlePeriodDto getSettlePeriod(int appId, Long familyId, PaySettlePeriodNumberEnum periodNumber){
        String tenantCode = PayTenantCodeEnum.getPayTenantCode(appId);
        try {
            log.info("getSettlePeriod[getSettlePeriod] req tenantCode={},familyId={},PeriodNumber={}",tenantCode,familyId,periodNumber.getNumber());

            Result<CreatorDataQueryProto.ResponseGetSettlePeriod> result = creatorDataQueryService.getSettlePeriod(CreatorDataQueryProto.GetSettlePeriodRequest.newBuilder()
                    .setTenantCode(tenantCode)
                    .setPeriodNumber(periodNumber.getNumber())
                    .setFamilyId(familyId)
                    .build(), DateUtil.formatDateToString(new Date(), DateUtil.NORMAL_DATE_FORMAT));

            if (RpcResult.isFail(result)){
                log.error("getSettlePeriod[getSettlePeriod] fail  tenantCode={},familyId={},PeriodNumber={},rCode={}",tenantCode,familyId,periodNumber.getNumber(),result.rCode());
                return null;
            }
            CreatorDataQueryProto.ResponseGetSettlePeriod period = result.target();
            PaySettlePeriodDto paySettlePeriodDto = PaySettlePeriodDto.builder()
                    .startDate(DateUtil.parseSimple(period.getSettlePeriodStart()))
                    .endDate(DateUtil.parseSimple(period.getSettlePeriodEnd()))
                    .build();
            log.info("getSettlePeriod[getSettlePeriod] response tenantCode={},familyId={},PeriodNumber={},paySettlePeriodDto={}",tenantCode,familyId,periodNumber.getNumber(), JSONObject.toJSONString(paySettlePeriodDto));
            return paySettlePeriodDto;
        } catch (Exception e) {
            log.error("getSettlePeriod[getSettlePeriod] error  tenantCode={},familyId={},PeriodNumber={}",tenantCode,familyId,periodNumber.getNumber(),e);
        }
        return null;
    }

    /**
     * 获取收入
     * @param familyId
     * @param roomId
     * @param playerId
     * @param configCode
     * @param periodType
     * @return
     */
    public long queryTradeStatisticsValue(Long familyId, Long roomId, Long playerId, String configCode, PeriodTypeEnum periodType) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        String tenantCode = PayTenantCodeEnum.getPayTenantCode(appId);
        try {

            log.info("queryTradeStatisticsValue[queryTradeStatisticsValue] req tenantCode={}, configCode={}, familyId={}, roomId={}, playerId={} , periodType={}"
                    , tenantCode
                    , configCode
                    , familyId
                    , roomId
                    , playerId
                    , periodType.getPeriodType()
            );

            CreatorDataQueryProto.QueryTradeStatisticsValueRequest.Builder builder = CreatorDataQueryProto.QueryTradeStatisticsValueRequest.newBuilder()
                    .setTenantCode(tenantCode)
                    .setConfigCode(configCode)
                    .setFamilyId(familyId)
                    .setPeriodType(periodType.getPeriodType())
                    ;
            Optional.ofNullable(roomId).ifPresent(builder::setHallId);
            Optional.ofNullable(playerId).ifPresent(builder::setAnchorId);

            Result<CreatorDataQueryProto.ResponseQueryTradeStatisticsValue> result = creatorDataQueryService.queryTradeStatisticsValue(builder.build(), DateUtil.formatDateToString(new Date(), DateUtil.NORMAL_DATE_FORMAT));
            if (RpcResult.isFail(result)){
                log.warn("queryTradeStatisticsValue[queryTradeStatisticsValue] fail. rCode={}, tenantCode={}, configCode={}, familyId={}, roomId={}, playerId={} , periodType={}"
                        , result.rCode()
                        , tenantCode
                        , configCode
                        , familyId
                        , roomId
                        , playerId
                        , periodType.getPeriodType()
                );
                return 0L;
            }
            long statisticsValue = result.target().getStatisticsValue();
            log.info("queryTradeStatisticsValue[queryTradeStatisticsValue] response tenantCode={}, configCode={}, familyId={}, roomId={}, playerId={} , periodType={},statisticsValue={}"
                    , tenantCode
                    , configCode
                    , familyId
                    , roomId
                    , playerId
                    , periodType.getPeriodType()
                    , statisticsValue
            );
            return statisticsValue;
        } catch (Exception e) {
            log.error("queryTradeStatisticsValue[queryTradeStatisticsValue] response tenantCode={}, configCode={}, familyId={}, roomId={}, playerId={} , periodType={}"
                    , tenantCode
                    , configCode
                    , familyId
                    , roomId
                    , playerId
                    , periodType.getPeriodType()
                    , e
            );
        }
        return 0L;
    }


    /**
     * 根据流水号获取内容(流水备注)
     * @param flowIds
     * @return
     */
    public Map<Long, String> getAccountHistoryRemark(Set<Long> flowIds){
        if (CollUtil.isEmpty(flowIds)){
            return MapUtil.empty();
        }

        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Result<CreatorDataQueryProto.ResponseQueryAccountHistoryRemark> result = creatorDataQueryService.queryAccountHistoryRemark(CreatorDataQueryProto.QueryAccountHistoryRemarkRequest.newBuilder()
                        .setTenantCode(PayTenantCodeEnum.getPayTenantCode(appId))
                        .addAllOutTradeId(flowIds.stream().map(String::valueOf).collect(Collectors.toSet()))
                        .build(),
                DateUtil.formatDateToString(new Date(), DateUtil.NORMAL_DATE_FORMAT)
        );

        if (RpcResult.isFail(result)){
            log.warn("pay service getAccountHistoryRemark is fail. appId: {}, flowIs: {}, rCode: {}, result: {}", appId, flowIds, result.rCode(), JsonUtil.dumps(result));
            return MapUtil.empty();
        }

        List<CreatorDataQueryProto.QueryAccountHistoryRemarkResponse> target = result.target().getDataList();
        return target.stream()
                .collect(Collectors.toMap(
                        e -> Long.valueOf(e.getOutTradeId()),
                        CreatorDataQueryProto.QueryAccountHistoryRemarkResponse::getRemark
                ));


    }


}
