package fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.common.datastore.mysql.constant.ParamContants;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.common.datastore.mysql.mybatis.sqlprovider.BaseDeleteProvider;
import fm.lizhi.common.datastore.mysql.mybatis.sqlprovider.BaseInsertProvider;
import fm.lizhi.common.datastore.mysql.mybatis.sqlprovider.BaseSelectProvider;
import fm.lizhi.common.datastore.mysql.mybatis.sqlprovider.BaseUpdateProvider;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity.HyContract;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity.HyContractExample;
import fm.lizhi.ocean.wavecenter.service.sign.dto.SMSignRoomPageListReqDto;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 */
@DataStore(namespace = "mysql_heiye_lzppfamily_r")
public interface HyContractMapper {


    /**
     * 查询合同列表
     *
     * @param njId
     * @param familyId
     * @param type
     * @param signStatus
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Select("<script>"
            + " select * from contract where 1=1"
            + "<if test = 'njId>0'> and nj_id=#{njId}</if>"
            + "<if test = 'familyId>0'> and family_id=#{familyId}</if>"
            + "<if test = 'types.size()>0'> "
            + " and type in "
            + " <foreach collection='types' item='item' index='index' open='(' close=')' separator=','>#{item}</foreach> "
            + "</if>"
            + "<if test = 'signStatus.size()>0'> "
            + " and status in "
            + " <foreach collection='signStatus' item='item' index='index' open='(' close=')' separator=','>#{item}</foreach> "
            + "</if>"
            + " order by create_time desc "
            + "</script>")
    PageList<HyContract> selectList(@Param("njId") Long njId, @Param("familyId") Long familyId,
                                    @Param("types") List<String> types, @Param("signStatus") List<String> signStatus,
                                    @Param(ParamContants.PAGE_NUMBER) int pageNumber, @Param(ParamContants.PAGE_SIZE) int pageSize
    );

    @Select({"<script>"
            , "select distinct nj_id"
            , "from contract"
            , "where family_id = #{familyId}"
            , "  and ((status in ('SIGN_SUCCEED', 'OVERDUE_CONTRACT') and type in ('SIGN','RENEW','SUBJECT_CHANGE') and begin_time &lt;= #{endDate} and"
            , "        expire_time &gt;= #{startDate}) or"
            , "       (status = 'STOP_CONTRACT' and type in ('SIGN','RENEW','SUBJECT_CHANGE') and begin_time &lt;= #{endDate} and"
            , "        stop_time &gt;= #{startDate}))"
            , "</script>"})
    PageList<Long> getSingGuildRoomsByDate(@Param("familyId") Long familyId,
                                    @Param("startDate") String startDate, @Param("endDate") String endDate,
                                    @Param(ParamContants.PAGE_NUMBER) int pageNumber, @Param(ParamContants.PAGE_SIZE) int pageSize
    );


    /**
     * 根据实体对象的字段值查询多条记录，查询条件会跳过NULL值的字段。
     *
     * @param entity 实体对象
     * @return 实体对象列表
     */
    @SelectProvider(type = BaseSelectProvider.class, method = "select")
    List<HyContract> selectMany(HyContract entity);

    /**
     * 根据实体对象的字段值查询单条记录，查询条件会跳过NULL值的字段。
     *
     * @param entity 实体对象
     * @return 单个实体对象
     */
    @SelectProvider(type = BaseSelectProvider.class, method = "select")
    HyContract selectOne(HyContract entity);

    /**
     * 根据实体对象中主键（{@link javax.persistence.Id}标注）字段查询单条数据。
     *
     * @param entity 实体对象
     * @return 单个实体对象
     */
    @SelectProvider(type = BaseSelectProvider.class, method = "selectByPrimaryKey")
    HyContract selectByPrimaryKey(HyContract entity);

    /**
     * 根据实体对象的字段值查询分页记录，查询条件会跳过NULL值的字段。
     *
     * @param entity 实体对象
     * @return 分页数据
     */
    @SelectProvider(type = BaseSelectProvider.class, method = "selectPage")
    PageList<HyContract> selectPage(@Param(ParamContants.ENTITIE) HyContract entity, @Param(ParamContants.PAGE_NUMBER) int pageNumber, @Param(ParamContants.PAGE_SIZE) int pageSize);

    /**
     * 根据实体对象中主键字段（{@link javax.persistence.Id}标注）删除数据。
     *
     * @param entity 实体对象
     * @return 影响行数
     */
    @DeleteProvider(type = BaseDeleteProvider.class, method = "deleteByPrimaryKey")
    int deleteByPrimaryKey(HyContract entity);

    /**
     * 将实体对象写入数据库，会跳过NULL值的字段。<br/>
     * 如果主键字段（{@link javax.persistence.Id}标注）有设置{@link javax.persistence.GeneratedValue}时，会自动生成主键并设置到实体对象中。
     *
     * @param entity 实体对象
     * @return 影响行数
     */
    @InsertProvider(type = BaseInsertProvider.class, method = "insert")
    int insert(HyContract entity);

    /**
     * 批量将实体对象写入数据库（不会跳过NULL值)。<br/>
     * 如果字段有设置{@link javax.persistence.Id}和{@link javax.persistence.GeneratedValue}时，会自动生成值，并设置到实体类实例中。
     *
     * @param entities 实体对象列表
     * @return 影响行数
     */
    @InsertProvider(type = BaseInsertProvider.class, method = "batchInsert")
    int batchInsert(@Param(ParamContants.ENTITIES) List<HyContract> entities);

    /**
     * 根据实体类中主键（{@link javax.persistence.Id}标注的字段）更新数据，会跳过NULL值的字段。
     *
     * @param entity 实体对象
     * @return 影响行数
     */
    @UpdateProvider(type = BaseUpdateProvider.class, method = "updateByPrimaryKey")
    int updateByPrimaryKey(HyContract entity);

    /**
     * 根据example类生成WHERE条件查询总记录条数
     *
     * @param example
     * @return
     */
    @SelectProvider(type = BaseSelectProvider.class, method = "countByExample")
    long countByExample(HyContractExample example);

    /**
     * 根据example类生成WHERE条件删除记录
     *
     * @param example
     * @return
     */
    @DeleteProvider(type = BaseDeleteProvider.class, method = "deleteByExample")
    long deleteByExample(HyContractExample example);

    /**
     * 根据example类生成WHERE条件查询记录数
     *
     * @param example
     * @return
     */
    @SelectProvider(type = BaseSelectProvider.class, method = "selectByExample")
    List<HyContract> selectByExample(HyContractExample example);

    /**
     * 根据example类生成WHERE条件查询分页记录数
     *
     * @param example
     * @param pageNumber 页码
     * @param pageSize   每页数据大小
     * @return
     */
    @SelectProvider(type = BaseSelectProvider.class, method = "pageByExample")
    PageList<HyContract> pageByExample(@Param(ParamContants.EXAMPLE) HyContractExample example, @Param(ParamContants.PAGE_NUMBER) int pageNumber, @Param(ParamContants.PAGE_SIZE) int pageSize);

    /**
     * 根据example类生成WHERE条件更新记录数，会跳过实体类对象中的NULL值的字段。<br/>
     *
     * @param entity  实体类对象
     * @param example
     * @return
     */
    @UpdateProvider(type = BaseUpdateProvider.class, method = "updateByExample")
    int updateByExample(@Param(ParamContants.ENTITIE) HyContract entity, @Param(ParamContants.EXAMPLE) HyContractExample example);

    @Select({
            "<script>"
            , "SELECT *"
            , "FROM contract"
            , "WHERE type in ('SIGN','RENEW','SUBJECT_CHANGE') and status in ('STOP_CONTRACT', 'CHANGE_OBJECT', 'SIGN_SUCCEED') and family_id = #{familyId}"
            , "<if test='roomIds != null and roomIds.size() > 0'>"
            , "and nj_id in "
            ,   "<foreach item='njId' collection='roomIds' open='(' separator=',' close=')'>"
            ,       "#{njId}"
            ,   "</foreach>"
            , "</if>"
            , "AND (family_id, nj_id, create_time) IN ("
            , "    SELECT family_id, nj_id, MAX(create_time)"
            , "    FROM contract"
            , "    WHERE type in ('SIGN','RENEW','SUBJECT_CHANGE') and status in ('STOP_CONTRACT', 'CHANGE_OBJECT', 'SIGN_SUCCEED') and family_id = #{familyId}"
            , "<if test='roomIds != null and roomIds.size() > 0'>"
            , "and nj_id in "
            ,   "<foreach item='njId' collection='roomIds' open='(' separator=',' close=')'>"
            ,       "#{njId}"
            ,   "</foreach>"
            , "</if>"
            , "    GROUP BY family_id, nj_id"
            , ")"
            , "</script>"
    })
    PageList<HyContract> pageFamilyNjBest(@Param("familyId") long familyId
            , @Param("roomIds") List<Long> roomIds
            , @Param(ParamContants.PAGE_NUMBER) int pageNumber
            , @Param(ParamContants.PAGE_SIZE) int pageSize);

    @Select({
            "<script>"
            , "select family_id"
            , "from contract"
            , "where nj_id = #{roomId}"
            , "  and ((status in ('SIGN_SUCCEED', 'OVERDUE_CONTRACT') and type in ('SIGN','RENEW','SUBJECT_CHANGE') and begin_time &lt;= #{date}) or"
            , "       (status = 'STOP_CONTRACT' and type in ('SIGN','RENEW','SUBJECT_CHANGE') and begin_time &lt;= #{date} and"
            , "        stop_time &gt;= #{date}))"
            , "order by create_time desc"
            , "limit 1"
            , "</script>"
    })
    List<Long> getRoomSignFamilyInDate(@Param("roomId") Long roomId, @Param("date") String date);

    @Select({"<script>"
            , "SELECT *"
            , "FROM contract"
            , "WHERE type in ('SIGN','RENEW','SUBJECT_CHANGE') and status in ('STOP_CONTRACT', 'CHANGE_OBJECT', 'SIGN_SUCCEED') and family_id = #{param.familyId}"

            , "<if test=' null != param.roomId and param.roomId > 0 '>"
            , "    and nj_id = #{param.roomId}"
            , "</if>"

            , "<if test='null != param.roomIds and param.roomIds.size > 0'>"
            , "and nj_id in "
            , "<foreach collection='param.roomIds' item='rId' open='(' separator=',' close=')'>"
            , "#{rId}"
            , "</foreach>"
            , "</if>"

            , "<if test=' null != param.signStartDate '>"
            , "    and sign_finish_time&gt;=#{param.signStartDate}"
            , "</if>"
            , "<if test=' null != param.signEndDate '>"
            , "    and sign_finish_time&lt;=#{param.signEndDate}"
            , "</if>"

            , "<if test=' null != param.expireStartDate '>"
            , "    and expire_time&gt;=#{param.expireStartDate}"
            , "</if>"
            , "<if test=' null != param.expireEndDate '>"
            , "    and expire_time&lt;=#{param.expireEndDate}"
            , "</if>"

            , "<if test=' null != param.stopStartDate '>"
            , "    and stop_time&gt;=#{param.stopStartDate}"
            , "</if>"
            , "<if test=' null != param.stopEndDate '>"
            , "    and stop_time&lt;=#{param.stopEndDate}"
            , "</if>"

            , "<if test=' null != param.signStatus and param.signStatus == 1 '>"
            , "    and status='SIGN_SUCCEED'"
            , "</if>"

            , "<if test=' null != param.signStatus and param.signStatus == 0 '>"
            , "    and status in ('STOP_CONTRACT', 'CHANGE_OBJECT')"
            , "</if>"

            , "AND (family_id, nj_id, create_time) IN ("
            , "    SELECT family_id, nj_id, MAX(create_time)"
            , "    FROM contract"
            , "    WHERE type in ('SIGN','RENEW','SUBJECT_CHANGE') and status in ('STOP_CONTRACT', 'CHANGE_OBJECT', 'SIGN_SUCCEED') and family_id = #{param.familyId}"

            , "<if test=' null != param.roomId and param.roomId > 0 '>"
            , "    and nj_id = #{param.roomId}"
            , "</if>"

            , "<if test='null != param.roomIds and param.roomIds.size > 0'>"
            , "and nj_id in "
            , "<foreach collection='param.roomIds' item='rId' open='(' separator=',' close=')'>"
            , "#{rId}"
            , "</foreach>"
            , "</if>"

            , "    GROUP BY family_id, nj_id"
            , ") order by sign_finish_time desc"

            , "</script>"})
    PageList<HyContract> guildSignRoomPageList(@Param("param") SMSignRoomPageListReqDto param, @Param(ParamContants.PAGE_NUMBER) int pageNumber, @Param(ParamContants.PAGE_SIZE) int pageSize);

    @Select({
            "<script>"
            , "select distinct nj_id"
            , "from contract"
            , "where status in ('STOP_CONTRACT', 'CHANGE_OBJECT', 'SIGN_SUCCEED')"
            , "  and type in ('SIGN','RENEW','SUBJECT_CHANGE')"
            , "  and family_id = #{family}"
            , "  and nj_id &gt; #{minNjId}"
            , "order by nj_id"
            , "limit #{pageSize}"
            , "</script>"
    })
    List<Long> getHistoryNjIds(@Param("family") Long familyId, @Param("minNjId") Long minNjId, @Param("pageSize") Integer pageSize);


}
