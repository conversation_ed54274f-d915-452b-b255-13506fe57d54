package fm.lizhi.ocean.wavecenter.infrastructure.message.datastore.dao;

import fm.lizhi.ocean.wavecenter.datastore.platform.message.entity.WcMessageReadRecord;
import fm.lizhi.ocean.wavecenter.datastore.platform.message.entity.WcMessageReadRecordExample;
import fm.lizhi.ocean.wavecenter.datastore.platform.message.mapper.WcMessageReadRecordMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class WaveCenterMessageReadDao {

    @Autowired
    private WcMessageReadRecordMapper wcMessageReadRecordMapper;

    /**
     * 批量查询已读记录
     *
     * @param userId     用户ID
     * @param messageIds 消息ID
     * @return 结果
     */
    public Map<Long, WcMessageReadRecord> batchQueryReadRecord(Long userId, List<Long> messageIds) {
        if (CollectionUtils.isEmpty(messageIds)) {
            return new HashMap<>(2);
        }
        // 获取已读
        WcMessageReadRecordExample readRecordExample = new WcMessageReadRecordExample();
        readRecordExample.createCriteria().andMessageIdIn(messageIds).andUserIdEqualTo(userId);
        List<WcMessageReadRecord> readRecordList = wcMessageReadRecordMapper.selectByExample(readRecordExample);
        return readRecordList.stream()
                .collect(Collectors.toMap(WcMessageReadRecord::getMessageId, v -> v, (a, b) -> a));
    }

}
