package fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.convert.xm;

import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.xm.XmCreateAvatarWidgetBean;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.xm.XmCreateRoomBackgroundBean;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestCreateAvatarWidget;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestCreateRoomBackground;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface XmDecorateManagementConvert {

    XmDecorateManagementConvert I = Mappers.getMapper(XmDecorateManagementConvert.class);

    XmCreateRoomBackgroundBean toXmCreateRoomBackgroundBean(RequestCreateRoomBackground request);

    XmCreateAvatarWidgetBean toXmCreateAvatarWidgetBean(RequestCreateAvatarWidget request);
}
