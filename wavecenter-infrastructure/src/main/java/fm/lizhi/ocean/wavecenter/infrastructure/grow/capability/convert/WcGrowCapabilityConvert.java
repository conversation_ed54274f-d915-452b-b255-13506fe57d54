package fm.lizhi.ocean.wavecenter.infrastructure.grow.capability.convert;

import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.datastore.grow.capability.entity.WcGrowCapability;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import fm.lizhi.ocean.wavecenter.service.grow.dto.WcGrowCapabilityDTO;

/**
 * <AUTHOR>
 */
@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        imports = {
                UrlUtils.class
        }
)
public interface WcGrowCapabilityConvert {
    WcGrowCapabilityConvert INSTANCE = Mappers.getMapper(WcGrowCapabilityConvert.class);

    /**
     * entity转dto
     *
     * @param entity entity
     * @return dto
     */
    WcGrowCapabilityDTO entityToDto(WcGrowCapability entity);
}
