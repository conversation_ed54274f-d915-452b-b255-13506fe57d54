package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.manager;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.TaskInfoI;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.convert.GrowPlayerTaskConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowPlayerTask;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowPlayerTaskExample;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.mapper.WcGrowPlayerTaskMapper;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.GrowPlayerTaskDTO;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.SettlePeriodDTO;
import fm.lizhi.ocean.wavecenter.service.grow.ability.manager.GrowPlayerTaskManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/6 18:27
 */
@Slf4j
@Component
public class GrowPlayerTaskManagerImpl implements GrowPlayerTaskManager {

    @Autowired
    private WcGrowPlayerTaskMapper growPlayerTaskMapper;
    @Autowired
    private FamilyManager familyManager;

    @Override
    public void deletePeriodTask(Long playerId, SettlePeriodDTO periodDTO) {
        // 构建查询条件
        WcGrowPlayerTaskExample example = new WcGrowPlayerTaskExample();
        example.createCriteria()
                .andAppIdEqualTo(ContextUtils.getBusinessEvnEnum().getAppId())
                .andPlayerIdEqualTo(playerId)
                .andDeletedEqualTo(0)
                .andStartWeekDateEqualTo(periodDTO.getStartDate())
                .andEndWeekDateEqualTo(periodDTO.getEndDate())
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());

        WcGrowPlayerTask entity = new WcGrowPlayerTask();
        entity.setDeleted(1);
        int row = growPlayerTaskMapper.updateByExample(entity, example);
        log.info("delete row={}", row);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePlayerTask(TaskInfoI taskInfo, SettlePeriodDTO periodDTO) {
        UserInFamilyBean userInFamily = familyManager.getUserInFamily(taskInfo.getUserId());
        Long roomId = userInFamily.getNjId() != null ? userInFamily.getNjId() : 0;
        Long familyId = userInFamily.getFamilyId() != null ? userInFamily.getFamilyId() : 0;

        // 构建查询条件
        WcGrowPlayerTaskExample example = new WcGrowPlayerTaskExample();
        example.createCriteria()
                .andAppIdEqualTo(ContextUtils.getBusinessEvnEnum().getAppId())
                .andTemplateIdEqualTo(taskInfo.getTemplateId())
                .andPlayerIdEqualTo(taskInfo.getUserId())
                .andStartWeekDateEqualTo(periodDTO.getStartDate())
                .andEndWeekDateEqualTo(periodDTO.getEndDate())
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());

        // 查询现有记录
        List<WcGrowPlayerTask> existingTasks = growPlayerTaskMapper.selectByExample(example);
        boolean exists = CollectionUtils.isNotEmpty(existingTasks);

        WcGrowPlayerTask task = new WcGrowPlayerTask();
        // 设置公共字段
        task.setTemplateId(taskInfo.getTemplateId());
        task.setPlayerId(taskInfo.getUserId());
        task.setRoomId(roomId);
        task.setFamilyId(familyId);
        task.setFinishTime(taskInfo.getExecuteFinishTime());
        task.setAppId(ContextUtils.getBusinessEvnEnum().getAppId());
        task.setDeployEnv(ConfigUtils.getEnvRequired().name());
        task.setModifyTime(new Date());
        task.setStartWeekDate(periodDTO.getStartDate());
        task.setEndWeekDate(periodDTO.getEndDate());
        task.setDeleted(0);

        if (exists) {
            // 更新逻辑：保留原始创建时间，设置主键ID
            WcGrowPlayerTask existing = existingTasks.get(0);
            task.setId(existing.getId());
            task.setCreateTime(existing.getCreateTime());
            growPlayerTaskMapper.updateByPrimaryKey(task);
        } else {
            // 新增逻辑：生成新ID，设置创建时间
            task.setId(taskInfo.getId());
            task.setCreateTime(new Date());
            growPlayerTaskMapper.insert(task);
        }
    }

    @Override
    public List<GrowPlayerTaskDTO> getPlayerFinishByTime(Long playerId, Date startTime, Date endTime) {
        // 构建查询条件
        WcGrowPlayerTaskExample example = new WcGrowPlayerTaskExample();
        example.createCriteria()
                .andPlayerIdEqualTo(playerId)
                .andStartWeekDateGreaterThanOrEqualTo(startTime)
                .andEndWeekDateLessThanOrEqualTo(endTime)
                .andDeletedEqualTo(0)
                .andAppIdEqualTo(ContextUtils.getBusinessEvnEnum().getAppId())
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());

        // 查询数据库记录
        List<WcGrowPlayerTask> tasks = growPlayerTaskMapper.selectByExample(example);
        
        // 转换为DTO对象
        return GrowPlayerTaskConvert.I.entitys2DTOs(tasks);
    }
}
