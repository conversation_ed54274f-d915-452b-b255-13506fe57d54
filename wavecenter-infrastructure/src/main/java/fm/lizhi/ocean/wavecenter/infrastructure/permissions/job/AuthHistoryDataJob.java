package fm.lizhi.ocean.wavecenter.infrastructure.permissions.job;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.datastore.platform.premission.entity.WcRoleAuthRef;
import fm.lizhi.ocean.wavecenter.datastore.platform.premission.entity.WcRoleAuthRefExample;
import fm.lizhi.ocean.wavecenter.datastore.platform.premission.mapper.WcRoleAuthRefMapper;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 临时定时任务
 * 历史授权数据没有familyId，清洗填充
 * <AUTHOR>
 * @date 2024/12/6 14:51
 */
@Component
public class AuthHistoryDataJob implements JobHandler {

    @Autowired
    private WcRoleAuthRefMapper roleAuthRefMapper;
    @Autowired
    private FamilyManager familyManager;

    @Override
    public void execute(JobExecuteContext jobExecuteContext) throws Exception {
        for (BusinessEvnEnum busEvn : BusinessEvnEnum.values()) {
            if (busEvn.getOnline() != 1) {
                continue;
            }
            ContextUtils.setBusinessEvnEnum(busEvn);
            WcRoleAuthRefExample example = new WcRoleAuthRefExample();
            example.createCriteria()
                    .andAppIdEqualTo(busEvn.getAppId())
                    .andFamilyIdEqualTo(0L)
                    .andDeletedEqualTo(0);

            PageList<WcRoleAuthRef> pageList = roleAuthRefMapper.pageByExample(example, 1, 50);
            if (CollectionUtils.isNotEmpty(pageList)) {
                for (WcRoleAuthRef wcRoleAuthRef : pageList) {
                    UserInFamilyBean userInFamily = familyManager.getUserInFamily(wcRoleAuthRef.getCreateUserId());
                    if (userInFamily.getFamilyId() != null) {
                        wcRoleAuthRef.setFamilyId(userInFamily.getFamilyId());
                        roleAuthRefMapper.updateByPrimaryKey(wcRoleAuthRef);
                    }
                }
            }

            WcRoleAuthRefExample example2 = new WcRoleAuthRefExample();
            example2.createCriteria()
                    .andAppIdEqualTo(busEvn.getAppId())
                    .andFamilyIdIsNull()
                    .andDeletedEqualTo(0);

            PageList<WcRoleAuthRef> pageList2 = roleAuthRefMapper.pageByExample(example, 1, 50);
            if (CollectionUtils.isNotEmpty(pageList2)) {
                for (WcRoleAuthRef wcRoleAuthRef : pageList2) {
                    UserInFamilyBean userInFamily = familyManager.getUserInFamily(wcRoleAuthRef.getCreateUserId());
                    if (userInFamily.getFamilyId() != null) {
                        wcRoleAuthRef.setFamilyId(userInFamily.getFamilyId());
                        roleAuthRefMapper.updateByPrimaryKey(wcRoleAuthRef);
                    }
                }
            }
            ContextUtils.clearContext();
        }

    }
}
