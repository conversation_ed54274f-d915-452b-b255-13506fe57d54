package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.dao;

import com.alibaba.csp.sentinel.util.AssertUtil;
import com.google.common.collect.Lists;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityAnnouncementStatusEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityResourceAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityResourceGiveStatusEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityResourceTypeEnum;
import fm.lizhi.ocean.wavecenter.common.utils.DateTimeUtils;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.*;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.ext.ActivityApplyInfoExtraMapper;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.mapper.ActivityApplyInfoMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.po.ActivityUpdatePo;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.LogicDeleteConstants;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityUserCancelParamDTO;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 活动管理事务操作
 */
@Slf4j
@Repository
public class ActivityApplyDao {

    @Autowired
    private ActivityApplyInfoMapper applyInfoMapper;

    @Autowired
    private ActivityApplyFlowResourceDao flowResourceDao;

    @Autowired
    private ActivityApplyProcessDao processDao;

    @Autowired
    private ActivityTemplateUsedRelationDao relationDao;

    @Autowired
    private ActivityOfficialSeatTimeDao officialSeatTimeDao;

    @Autowired
    private ActivityApplyInfoExtraMapper activityApplyInfoExtraMapper;

    @Autowired
    private ActivityResourceGiveDao activityResourceGiveDao;

    @Autowired
    private ActivityRoomAnnouncementDeployDao activityRoomAnnouncementDeployDao;

    @Autowired
    private ActivityDressUpResourceGiveDao activityDressUpResourceGiveDao;

    @Autowired
    private ActivityFlowResourceGiveDao activityFlowResourceGiveDao;

    @Autowired
    private ActivityApplyDecorateDao activityApplyDecorateDao;

    /**
     * 统计周申请数量
     * @param appId
     * @param njId
     * @param weekStart
     * @param weekEnd
     * @return
     */
    public long countApplyInWeek(int appId, long njId, Date weekStart, Date weekEnd){
        ActivityApplyInfoExample example = new ActivityApplyInfoExample();
        example.createCriteria()
                .andDeletedEqualTo(0)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andAuditStatusIn(Lists.newArrayList(ActivityAuditStatusEnum.AUDIT_PASS.getStatus()
                        , ActivityAuditStatusEnum.WAITING_AUDIT.getStatus()
                ))
                .andAppIdEqualTo(appId)
                .andNjIdEqualTo(njId)
                .andStartTimeBetween(weekStart, weekEnd);
        return applyInfoMapper.countByExample(example);
    }

    /**
     * 保存活动数据，事务操作，失败直接抛异常
     *
     * @param applyInfo            活动申请信息
     * @param flowResources        流量资源列表
     * @param processes            流程列表
     * @param templateUsedRelation 模板关联关系
     * @param officialSeatTimeList 官频位时间表
     * @param maxSeatCount         官频位最大值
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveActivityApplyData(ActivityApplyInfo applyInfo,
                                      List<ActivityApplyDecorate> decorates,
                                      List<ActivityApplyFlowResource> flowResources,
                                      List<ActivityApplyProcess> processes,
                                      ActivityTemplateUsedRelation templateUsedRelation,
                                      List<ActivityOfficialSeatTime> officialSeatTimeList,
                                      int maxSeatCount) {
        //保存活动信息
        boolean infoRes = this.saveApplyRecord(applyInfo);
        AssertUtil.assertState(infoRes, "保存提报活动信息失败");
        boolean decorateRes = activityApplyDecorateDao.saveDecorates(applyInfo.getId(), decorates);
        AssertUtil.assertState(decorateRes, "保存活动装饰信息失败");
        boolean resourceRes = flowResourceDao.batchSaveRecord(flowResources);
        AssertUtil.assertState(resourceRes, "保存流量资源失败");
        boolean processRes = processDao.saveActivityApplyProcess(processes);
        AssertUtil.assertState(processRes, "保存活动流程失败");
        boolean relationRes = relationDao.saveTemplateUsedRelation(templateUsedRelation);
        AssertUtil.assertState(relationRes, "保存模板关联关系失败");
        Pair<Boolean, ActivityOfficialSeatTime> seatTimeRes = officialSeatTimeDao.updateOfficialSeatTime(officialSeatTimeList, maxSeatCount);
        assertSeatTimeRes(seatTimeRes, applyInfo.getId(), "官频位可配置数量不足，请检查时间段：");
    }

    /**
     * 保存申请记录
     *
     * @param record 记录
     * @return 结果
     */
    public boolean saveApplyRecord(ActivityApplyInfo record) {
        return applyInfoMapper.insert(record) > 0;
    }

    /**
     * 获取活动信息
     *
     * @param id 活动ID
     * @return 活动信息
     */
    public ActivityApplyInfo getActivityInfoById(Long id) {
        ActivityApplyInfo applyInfo = new ActivityApplyInfo();
        applyInfo.setId(id);
        return applyInfoMapper.selectByPrimaryKey(applyInfo);
    }

    /**
     * 根据活动ID列表获取活动信息
     *
     * @param ids 活动ID列表
     * @return 活动信息列表
     */
    public List<ActivityApplyInfo> getActivityInfoByIds(List<Long> ids) {
        ActivityApplyInfoExample example = new ActivityApplyInfoExample();
        example.createCriteria().andIdIn(ids);
        return applyInfoMapper.selectByExample(example);
    }

    @Transactional(rollbackFor = Exception.class)
    public void agreeActivityApply(Long activityId, String operator, List<ActivityApplyFlowResource> activityApplyFlowResources, Integer version) {
        ActivityApplyInfoExample example = new ActivityApplyInfoExample();
        example.createCriteria().andIdEqualTo(activityId).andVersionEqualTo(version);

        ActivityApplyInfo applyInfo = new ActivityApplyInfo();
        applyInfo.setAuditStatus(ActivityAuditStatusEnum.AUDIT_PASS.getStatus());
        applyInfo.setId(activityId);
        applyInfo.setAuditOperator(operator);
        AssertUtil.assertState(applyInfoMapper.updateByExample(applyInfo, example) > 0, "活动审批失败");
        AssertUtil.assertState(flowResourceDao.batchUpdateRecord(activityId, activityApplyFlowResources), "更新流量资源失败");
    }

    /**
     * 拒绝活动申请
     *
     * @param activityId 活动ID
     * @param reason     原因
     * @param operator
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public void rejectActivityApply(Long activityId, List<ActivityOfficialSeatTime> seatTimeList, String reason, String operator, Integer version) {
        ActivityApplyInfoExample example = new ActivityApplyInfoExample();
        example.createCriteria().andIdEqualTo(activityId).andVersionEqualTo(version);

        ActivityApplyInfo applyInfo = new ActivityApplyInfo();
        applyInfo.setAuditReason(reason);
        applyInfo.setId(activityId);
        applyInfo.setAuditStatus(ActivityAuditStatusEnum.AUDIT_REJECTED.getStatus());
        applyInfo.setAuditOperator(operator);
        applyInfo.setVersion(version + 1);
        boolean res = applyInfoMapper.updateByExample(applyInfo, example) > 0;
        AssertUtil.assertState(res, "修改活动状态失败");
        flowResourceDao.updateFlowResourceStatus(activityId, ActivityResourceAuditStatusEnum.DISABLE_GIVE.getStatus());
        // 释放官频位数量
        Pair<Boolean, ActivityOfficialSeatTime> seatTimeRes = officialSeatTimeDao.decreasedCount(seatTimeList);
        if (!seatTimeRes.getLeft()) {
            String startTime = DateUtil.formatDateToString(seatTimeRes.getRight().getStartTime(), DateUtil.datetime_2);
            String endTime = DateUtil.formatDateToString(seatTimeRes.getRight().getEndTime(), DateUtil.datetime_2);
            AssertUtil.assertState(seatTimeRes.getLeft(), "减少官频位数量失败，时间段：" + startTime + "~" + endTime);
        }
    }

    /**
     * 获取大于指定时间的活动列表
     */
    public List<ActivityApplyInfo> getActivityInfoByStartTimeGte(Date startTime) {
        ActivityApplyInfoExample example = new ActivityApplyInfoExample();
        example.createCriteria().andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andDeletedEqualTo(LogicDeleteConstants.NOT_DELETED)
                .andStartTimeGreaterThanOrEqualTo(startTime)
        ;

        return applyInfoMapper.selectByExample(example);
    }

    /**
     * 删除活动提报
     *
     * @param activityId
     * @return
     */
    public Boolean deleteActivityApply(Long activityId) {
        ActivityApplyInfo applyInfo = new ActivityApplyInfo();
        applyInfo.setId(activityId);
        applyInfo.setDeleted(LogicDeleteConstants.DELETED);
        return applyInfoMapper.updateByPrimaryKey(applyInfo) > 0;
    }

    public Boolean updateActivityApplyInfo(ActivityApplyInfo applyInfo) {
        return activityApplyInfoExtraMapper.updateActivityApplyInfoByIdAndVersion(applyInfo) > 0;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateActivityApply(ActivityUpdatePo activityUpdatePo) {
        long processDeleteCount = processDao.batchDeleteRecord(activityUpdatePo.getApplyInfo().getId());
        log.info("updateActivityApply: 删除流程记录数:{}", processDeleteCount);

        //更新活动信息
        boolean updateInfoRes = updateActivityApplyInfo(activityUpdatePo.getApplyInfo());
        AssertUtil.assertState(updateInfoRes, "更新活动信息失败");

        //更新装扮信息
        boolean decoratesRes = activityApplyDecorateDao.saveDecorates(activityUpdatePo.getApplyInfo().getId(), activityUpdatePo.getDecorates());
        AssertUtil.assertState(decoratesRes, "更新装扮信息失败");

        //批量删除流量资源，流程，官频位时间表
        long flowDeleteCount = flowResourceDao.batchDeleteRecord(activityUpdatePo.getApplyInfo().getId());
        log.info("updateActivityApply: 删除流量资源记录数:{}", flowDeleteCount);
        boolean saveFlowResourceRes = flowResourceDao.batchSaveRecord(activityUpdatePo.getFlowResources());
        AssertUtil.assertState(saveFlowResourceRes, "保存流量资源失败");

        //更新官频位时间表
        //1.新旧官频位完全一致的情况下，不更新
        //2.旧官频位不存在的情况下，只更新新官频位
        //3.新旧官频位不一致的情况下，更新新旧官频位
        //顺序比较两个官频位列表的长度以及开始和结束时间
        if (!isSeatTimeListAllTheSame(activityUpdatePo.getSeatTimeList(), activityUpdatePo.getOldSeatTimeList())) {
            log.info("updateActivityApply: 官频位时间表发生变化，深入判断是否更新, activityId:{}", activityUpdatePo.getApplyInfo().getId());
            Pair<Boolean, ActivityOfficialSeatTime> seatTimeRes;
            if (!activityUpdatePo.getOldSeatTimeList().isEmpty()) {
                seatTimeRes = officialSeatTimeDao.decreasedCount(activityUpdatePo.getOldSeatTimeList());
                assertSeatTimeRes(seatTimeRes, activityUpdatePo.getApplyInfo().getId(), "官频位数量归还失败，请检查时间段：");
            }
            seatTimeRes = officialSeatTimeDao.updateOfficialSeatTime(activityUpdatePo.getSeatTimeList(), activityUpdatePo.getMaxSeatCount());
            assertSeatTimeRes(seatTimeRes, activityUpdatePo.getApplyInfo().getId(), "官频位可配置数量不足，请检查时间段：");
        }

        boolean saveProcessRes = processDao.saveActivityApplyProcess(activityUpdatePo.getProcessList());
        AssertUtil.assertState(saveProcessRes, "保存流程失败");
        if (activityUpdatePo.getRelation() != null) {
            long relationDeleteCount = relationDao.deleteTemplateUsedRelationByActivityId(activityUpdatePo.getApplyInfo().getId());
            log.info("updateActivityApply: 删除模板关联关系记录数:{}", relationDeleteCount);
            boolean saveRelationRes = relationDao.saveTemplateUsedRelation(activityUpdatePo.getRelation());
            AssertUtil.assertState(saveRelationRes, "保存模板关联关系失败");
        }

        //如果是审批通过，需要更新装扮，资源发放记录和房间公告
        if (Objects.equals(activityUpdatePo.getApplyInfo().getAuditStatus(), ActivityAuditStatusEnum.AUDIT_PASS.getStatus())) {
            //删除流量资源发放记录和子记录
            activityFlowResourceGiveDao.batchDeleteFlowResource(activityUpdatePo.getFlowResourceGiveIds());
            activityDressUpResourceGiveDao.batchDeleteDressUpResource(activityUpdatePo.getGiveIds());
            activityResourceGiveDao.batchDeleteRecord(activityUpdatePo.getApplyInfo().getId(), null);
            activityRoomAnnouncementDeployDao.deleteAnnouncementByActivityId(activityUpdatePo.getApplyInfo().getId());
        }
    }

    /**
     * 取消活动
     *
     * @param cancelParamDTO      上下文参数
     * @param resourceGiveRecords 资源发放记录
     * @param roomAnnouncement    房间公告
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelActivity(ActivityUserCancelParamDTO cancelParamDTO,
                               List<ActivityResourceGiveRecord> resourceGiveRecords,
                               ActivityAnnouncementDeployRecord roomAnnouncement,
                               Integer version) {
        Integer auditStatus = cancelParamDTO.getActivityInfo().getAuditStatus();
        boolean applyInfoRes = this.updateActivityAuditStatus(cancelParamDTO.getActivityId(), auditStatus, cancelParamDTO.getTargetAuditStatus(), cancelParamDTO.getReason(), cancelParamDTO.getOperator(), version);
        AssertUtil.assertState(applyInfoRes, "更新活动信息失败,请刷新页面重试");
        if (CollectionUtils.isNotEmpty(resourceGiveRecords)) {
            boolean batchUpdateRes = activityResourceGiveDao.batchUpdateResourceGiveRecordStatus(resourceGiveRecords, ActivityResourceGiveStatusEnum.CANCEL_GIVE.getStatus());
            AssertUtil.assertState(batchUpdateRes, "修改资源发放状态失败");
        }

        if (roomAnnouncement != null) {
            boolean res = activityRoomAnnouncementDeployDao.updateAnnouncementDeployStatus(roomAnnouncement.getId(), roomAnnouncement.getStatus(), ActivityAnnouncementStatusEnum.CANCEL_SET.getStatus());
            AssertUtil.assertState(res, "更新房间公告状态失败");
        }
    }

    /**
     * 修改活动状态
     *
     * @param activityId     活动ID
     * @param originalStatus 原始状态
     * @param targetStatus   目标状态
     * @param reason         原因
     * @return 结果
     */
    public boolean updateActivityAuditStatus(Long activityId, Integer originalStatus, Integer targetStatus, String reason, String operator, Integer version) {
        ActivityApplyInfo applyInfo = new ActivityApplyInfo();
        applyInfo.setId(activityId);
        applyInfo.setAuditStatus(targetStatus);
        if (StringUtils.isNotBlank(reason)) {
            applyInfo.setAuditReason(reason);
        }

        if (StringUtils.isNotBlank(operator)) {
            applyInfo.setAuditOperator(operator);
        }
        applyInfo.setVersion(version);
        return activityApplyInfoExtraMapper.updateActivityStatusByIdAndVersion(applyInfo, originalStatus) > 0;
    }

    /**
     * 审批前取消活动
     *
     * @param cancelParamDTO       上下文参数
     * @param officialSeatTimeList 官频位时间表
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelActivityBeforeAudit(ActivityUserCancelParamDTO cancelParamDTO, List<ActivityOfficialSeatTime> officialSeatTimeList) {
        boolean res = updateActivityAuditStatus(cancelParamDTO.getActivityId(), cancelParamDTO.getActivityInfo().getAuditStatus(), cancelParamDTO.getTargetAuditStatus(), cancelParamDTO.getReason(), cancelParamDTO.getOperator(), cancelParamDTO.getVersion());
        AssertUtil.assertState(res, "更新活动状态失败");
        if (CollectionUtils.isNotEmpty(officialSeatTimeList)) {
            Pair<Boolean, ActivityOfficialSeatTime> seatTimeRes = officialSeatTimeDao.decreasedCount(officialSeatTimeList);
            AssertUtil.assertState(seatTimeRes.getLeft(), "减少官频位数量失败");
        }
    }


    /**
     * 取消活动
     *
     * @param cancelParamDTO      上下文参数
     * @param resourceGiveRecords 资源发放记录
     * @param roomAnnouncement    房间公告
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelActivityAfterAudit(ActivityUserCancelParamDTO cancelParamDTO,
                                         List<ActivityOfficialSeatTime> officialSeatTimeList,
                                         List<ActivityResourceGiveRecord> resourceGiveRecords,
                                         ActivityAnnouncementDeployRecord roomAnnouncement,
                                         Integer version) {
        Integer auditStatus = cancelParamDTO.getActivityInfo().getAuditStatus();
        boolean applyInfoRes = this.updateActivityAuditStatus(cancelParamDTO.getActivityId(), auditStatus, cancelParamDTO.getTargetAuditStatus(), cancelParamDTO.getReason(), cancelParamDTO.getOperator(), version);
        AssertUtil.assertState(applyInfoRes, "更新活动信息失败,请刷新页面重试");
        if (CollectionUtils.isNotEmpty(resourceGiveRecords)) {
            boolean batchUpdateRes = activityResourceGiveDao.batchUpdateResourceGiveRecordStatus(resourceGiveRecords, ActivityResourceGiveStatusEnum.CANCEL_GIVE.getStatus());
            AssertUtil.assertState(batchUpdateRes, "修改资源发放状态失败");
        }

        if (CollectionUtils.isNotEmpty(officialSeatTimeList)) {
            Pair<Boolean, ActivityOfficialSeatTime> seatTimeRes = officialSeatTimeDao.decreasedCount(officialSeatTimeList);
            AssertUtil.assertState(seatTimeRes.getLeft(), "减少官频位数量失败");
        }

        if (roomAnnouncement != null) {
            boolean res = activityRoomAnnouncementDeployDao.updateAnnouncementDeployStatus(roomAnnouncement.getId(), roomAnnouncement.getStatus(), ActivityAnnouncementStatusEnum.CANCEL_SET.getStatus());
            AssertUtil.assertState(res, "更新房间公告状态失败");
        }
    }

    /**
     * 查询1小时后开始的活动列表
     *
     * @param nextHour 下一个小时数，例如1表示下一个小时，2表示下两个小时
     * @return 活动列表
     */
    public List<ActivityApplyInfo> getNextHourStartActivityList(int nextHour) {
        Date nextHourStartTime = DateTimeUtils.getNextHourRoundTime(new Date(), nextHour);
        ActivityApplyInfoExample example = new ActivityApplyInfoExample();
        example.createCriteria()
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andDeletedEqualTo(LogicDeleteConstants.NOT_DELETED)
                .andAuditStatusEqualTo(ActivityAuditStatusEnum.AUDIT_PASS.getStatus())
                .andStartTimeEqualTo(nextHourStartTime);
        return applyInfoMapper.selectByExample(example);
    }

    /**
     * 查询昨天结束的活动列表
     *
     * @return 活动列表
     */
    public List<ActivityApplyInfo> getLastDayEndActivityList() {
        //获取昨天结束的时间
        Date yesterdayEnd = DateUtil.getDayStart(new Date());
        Date yesterdayStart = DateUtil.getDayBefore(yesterdayEnd, 1);

        ActivityApplyInfoExample example = new ActivityApplyInfoExample();
        example.createCriteria()
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andDeletedEqualTo(LogicDeleteConstants.NOT_DELETED)
                .andAuditStatusEqualTo(ActivityAuditStatusEnum.AUDIT_PASS.getStatus())
                .andStartTimeGreaterThanOrEqualTo(yesterdayStart)
                .andStartTimeLessThan(yesterdayEnd)
                .andEndTimeLessThanOrEqualTo(yesterdayEnd);
        return applyInfoMapper.selectByExample(example);
    }

    /**
     * 更新装扮和资源发放记录
     *
     * @param giveIds             装扮发放ID
     * @param dressUpGiveRecords  装扮发放记录
     * @param activityId          活动ID
     * @param resourceGiveRecords 资源发放记录
     */
    private void updateDressUpAndResourceRecords(List<Long> giveIds,
                                                 List<ActivityDressUpGiveRecord> dressUpGiveRecords,
                                                 Long activityId,
                                                 List<ActivityResourceGiveRecord> resourceGiveRecords) {
        //之前已有装扮发放记录，则删除
        boolean deleteDressUpRes = activityDressUpResourceGiveDao.batchDeleteGiveRecord(giveIds);
        AssertUtil.assertState(deleteDressUpRes, "删除装扮发放记录失败");

        //之前没有装扮发放记录，则新增
        boolean saveDressUpRes = activityDressUpResourceGiveDao.batchSaveRecords(dressUpGiveRecords);
        AssertUtil.assertState(saveDressUpRes, "新增装扮发放记录失败");

        // 删除资源为装扮类型的发放记录
        boolean deleteResourceRes = activityResourceGiveDao.batchDeleteRecord(activityId, ActivityResourceTypeEnum.DRESS_UP.getType());
        AssertUtil.assertState(deleteResourceRes, "删除资源发放记录失败");

        //新增资源为装扮类型的发放记录
        boolean saveResourceRes = activityResourceGiveDao.batchSaveRecord(resourceGiveRecords);
        AssertUtil.assertState(saveResourceRes, "新增资源发放记录失败");
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateActivityAndDressUp(ActivityApplyInfo applyInfo, List<Long> giveIds,
                                         List<ActivityResourceGiveRecord> resourceGiveRecords,
                                         List<ActivityDressUpGiveRecord> dressUpGiveRecords) {
        //更新活动信息
        boolean updateInfoRes = updateActivityApplyInfo(applyInfo);
        AssertUtil.assertState(updateInfoRes, "更新活动信息失败");

        updateDressUpAndResourceRecords(giveIds, dressUpGiveRecords, applyInfo.getId(), resourceGiveRecords);
    }

    private void assertSeatTimeRes(Pair<Boolean, ActivityOfficialSeatTime> seatTimeRes, Long activityId, String message) {
        if (!seatTimeRes.getLeft()) {
            String startTime = DateUtil.formatDateToString(seatTimeRes.getRight().getStartTime(), DateUtil.datetime_2);
            String endTime = DateUtil.formatDateToString(seatTimeRes.getRight().getEndTime(), DateUtil.datetime_2);
            log.warn("updateActivityApply: 官频位数量变更失败，activityId:{}，时间段:{}", activityId, startTime + "~" + endTime);
            AssertUtil.assertState(seatTimeRes.getLeft(), message + startTime + "~" + endTime);
        }
    }

    private boolean isSeatTimeListAllTheSame(List<ActivityOfficialSeatTime> seatTimeList, List<ActivityOfficialSeatTime> oldSeatTimeList) {
        if (seatTimeList.size() != oldSeatTimeList.size()) {
            return false;
        }

        for (int i = 0; i < seatTimeList.size(); i++) {
            ActivityOfficialSeatTime newSeat = seatTimeList.get(i);
            ActivityOfficialSeatTime oldSeat = oldSeatTimeList.get(i);

            if (!Objects.equals(newSeat.getStartTime(), oldSeat.getStartTime()) ||
                    !Objects.equals(newSeat.getEndTime(), oldSeat.getEndTime())) {
                return false;
            }

            if (!Objects.equals(newSeat.getSeat(), oldSeat.getSeat())) {
                return false;
            }
        }
        return true;
    }


}
