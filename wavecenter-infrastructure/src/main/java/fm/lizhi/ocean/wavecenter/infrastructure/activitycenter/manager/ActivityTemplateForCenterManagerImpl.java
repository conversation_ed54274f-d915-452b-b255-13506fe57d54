package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.manager;

import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.convert.ActivityTemplateConvert;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityTemplateUsedRelation;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityTemplateUsedRelationExample;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.mapper.ActivityTemplateUsedRelationMapper;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityTemplateInfo;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityTemplateInfoExample;
import fm.lizhi.ocean.wavecenter.datastore.background.mapper.ActivityTemplateInfoMapper;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityTemplateBaseInfoDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityTemplateInfoDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityTemplateForCenterManager;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class ActivityTemplateForCenterManagerImpl implements ActivityTemplateForCenterManager {

     @Autowired
    private ActivityTemplateInfoMapper activityTemplateMapper;

    @Autowired
    private ActivityTemplateUsedRelationMapper activityTemplateUsedRelationMapper;

    @Override
    public List<ActivityTemplateInfoDTO> getActivityTemplateInfoList(List<Long> activityIds) {
        ActivityTemplateUsedRelationExample example = new ActivityTemplateUsedRelationExample();
        example.createCriteria().andActivityIdIn(activityIds);
        List<ActivityTemplateUsedRelation> activityTemplateUsedRelations = activityTemplateUsedRelationMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(activityTemplateUsedRelations)) {
            return Collections.emptyList();
        }
        
        List<Long> templateIds = activityTemplateUsedRelations.stream()
            .map(ActivityTemplateUsedRelation::getTemplateId)
            .collect(Collectors.toList());
        
        ActivityTemplateInfoExample templateExample = new ActivityTemplateInfoExample();
        templateExample.createCriteria().andIdIn(templateIds);
        List<ActivityTemplateInfo> activityTemplates = activityTemplateMapper.selectByExample(templateExample);
        
        // 构建模板ID到模板信息的映射
        Map<Long, ActivityTemplateInfo> templateMap = activityTemplates.stream()
            .collect(Collectors.toMap(ActivityTemplateInfo::getId, Function.identity()));
        
        // 转换数据
        return activityTemplateUsedRelations.stream()
            .map(relation -> {
                ActivityTemplateInfo template = templateMap.get(relation.getTemplateId());
                if (template == null) {
                    return null;
                }
                return ActivityTemplateConvert.I.bean2Dto(template, relation.getActivityId());
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    @Override
    public Optional<ActivityTemplateBaseInfoDTO> getTemplateBaseInfo(Long templateId) {
        ActivityTemplateInfo activityTemplateInfo = activityTemplateMapper.selectOne(ActivityTemplateInfo.builder().id(templateId).build());
        if (activityTemplateInfo == null) {
            return Optional.empty();
        }
        return Optional.of(ActivityTemplateConvert.I.entity2BaseDTO(activityTemplateInfo));
    }
}
