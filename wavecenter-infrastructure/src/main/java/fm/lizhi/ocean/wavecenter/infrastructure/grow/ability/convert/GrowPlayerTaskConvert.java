package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.convert;

import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowPlayerTask;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.GrowPlayerTaskDTO;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface GrowPlayerTaskConvert {
    GrowPlayerTaskConvert I = Mappers.getMapper(GrowPlayerTaskConvert.class);
    
    GrowPlayerTaskDTO entity2DTO(WcGrowPlayerTask entity);

    List<GrowPlayerTaskDTO> entitys2DTOs(List<WcGrowPlayerTask> entitys);
}