package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.dao;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PaySettleConfigCodeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.common.util.ColumnUtils;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataPayPlayerDay;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataPayPlayerDayExample;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.mapper.WcDataPayPlayerDayMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.ext.WcDataPayPlayerDayExtMapper;
import fm.lizhi.ocean.wavecenter.service.common.manager.IdManager;
import fm.lizhi.ocean.wavecenter.service.datacenter.constants.AccountOpEnum;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.PlayerFlowChangeDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/4/24 14:40
 */
@Slf4j
@Repository
public class DataPayPlayerDayDao {

    @Autowired
    private WcDataPayPlayerDayMapper dataPayPlayerDayMapper;
    @Autowired
    private WcDataPayPlayerDayExtMapper dataPayPlayerDayExtMapper;
    @Autowired
    private IdManager idManager;

    /**
     * 规则和字段映射关系
     * key=规则名称 configCode
     * value=字段名称
     */
    private Map<String, String> configCodeColumnMap = new HashMap<>();

    public DataPayPlayerDayDao() {
        configCodeColumnMap.put(PaySettleConfigCodeEnum.ANCHOR_INCOME_TOTAL_AMOUNT.getConfigCode(), ColumnUtils.getColumnName(WcDataPayPlayerDay.class, WcDataPayPlayerDay.Fields.income));
        configCodeColumnMap.put(PaySettleConfigCodeEnum.ANCHOR_HALL_INCOME_TOTAL_AMOUNT.getConfigCode(), ColumnUtils.getColumnName(WcDataPayPlayerDay.class, WcDataPayPlayerDay.Fields.signHallIncome));
        configCodeColumnMap.put(PaySettleConfigCodeEnum.ANCHOR_OFFICIAL_HALL_INCOME_TOTAL_AMOUNT.getConfigCode(), ColumnUtils.getColumnName(WcDataPayPlayerDay.class, WcDataPayPlayerDay.Fields.officialHallIncome));
        configCodeColumnMap.put(PaySettleConfigCodeEnum.PERSONAL_ANCHOR_INDIVIDUAL_INCOME_TOTAL_AMOUNT.getConfigCode(), ColumnUtils.getColumnName(WcDataPayPlayerDay.class, WcDataPayPlayerDay.Fields.personalIncome));
        configCodeColumnMap.put(PaySettleConfigCodeEnum.PERSONAL_ANCHOR_NOBILITY_ROYALTY_INCOME_TOTAL_AMOUNT.getConfigCode(), ColumnUtils.getColumnName(WcDataPayPlayerDay.class, WcDataPayPlayerDay.Fields.personalNobleIncome));
    }

    /**
     * 判断主播日流水是否存在
     * @param appId
     * @param playerId
     * @param statDateValue
     * @return
     */
    public boolean isPlayerDayFlowExist(Integer appId, Long playerId, Integer statDateValue){
        WcDataPayPlayerDayExample example = new WcDataPayPlayerDayExample();
        example.createCriteria()
                .andPlayerIdEqualTo(playerId)
                .andStatDateValueEqualTo(statDateValue)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andAppIdEqualTo(appId);
        return dataPayPlayerDayMapper.countByExample(example) > 0;
    }

    /**
     * 保存主播日流水变更
     * @param dto
     * @param familyId
     * @return
     */
    public int addPlayerDayFlow(PlayerFlowChangeDTO dto, Long familyId, Long njId){
        if (CollectionUtils.isEmpty(dto.getRuleCodes())) {
            return 0;
        }

        Integer statDateValue = MyDateUtil.getDateDayValue(dto.getTradeDate());

        WcDataPayPlayerDay entity = new WcDataPayPlayerDay();
        entity.setId(idManager.genId());
        entity.setAppId(dto.getAppId());
        entity.setStatDate(dto.getTradeDate());
        entity.setStatDateValue(statDateValue);
        entity.setPlayerId(dto.getPlayerId());
        entity.setRoomId(njId);
        entity.setFamilyId(familyId);
        entity.setCreateTime(new Date());
        entity.setModifyTime(new Date());
        entity.setDeployEnv(ConfigUtils.getEnvRequired().name());

        List<String> columns = new ArrayList<>();
        for (String ruleCode : dto.getRuleCodes()) {
            String column = configCodeColumnMap.get(ruleCode);
            if (column != null) {
                columns.add(column);
            }
        }
        log.info("columns={}", JsonUtil.dumps(columns));

        if (CollectionUtils.isEmpty(columns)) {
            log.warn("columns is empty");
            return 0;
        }

        String amount = dto.getAccountOpType() == AccountOpEnum.MINUS
                ? "-"+dto.getAmount()
                : dto.getAmount();

        return dataPayPlayerDayExtMapper.insertForColumn(entity, columns, amount);
    }

    /**
     * 更新流水
     * @param dto
     * @return
     */
    public int updatePlayerDayFlow(PlayerFlowChangeDTO dto){
        if (CollectionUtils.isEmpty(dto.getRuleCodes())) {
            return 0;
        }

        Integer statDateValue = MyDateUtil.getDateDayValue(dto.getTradeDate());

        WcDataPayPlayerDay entity = new WcDataPayPlayerDay();
        entity.setAppId(dto.getAppId());
        entity.setStatDateValue(statDateValue);
        entity.setPlayerId(dto.getPlayerId());
        entity.setModifyTime(new Date());
        entity.setDeployEnv(ConfigUtils.getEnvRequired().name());

        List<String> columns = new ArrayList<>();
        for (String ruleCode : dto.getRuleCodes()) {
            String column = configCodeColumnMap.get(ruleCode);
            if (column != null) {
                columns.add(column);
            }
        }
        log.info("columns={}", JsonUtil.dumps(columns));

        if (CollectionUtils.isEmpty(columns)) {
            log.warn("columns is empty");
            return 0;
        }

        String amount = dto.getAccountOpType() == AccountOpEnum.MINUS
                ? "-"+dto.getAmount()
                : "+"+dto.getAmount();

        return dataPayPlayerDayExtMapper.updateForColumn(entity, columns, amount);
    }

}
