package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote.xm;

import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.PlayerSignCharmSumPo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.WcPlayerSignCharmStateMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.XmLiveGiveGiftActionMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.AdminPlayerIncomeInfoPo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.CharmStatPo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote.ICharmStatRemote;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/24 16:06
 */
@Slf4j
@Component
public class XmCharmStatRemote implements ICharmStatRemote {

    @Autowired
    private XmLiveGiveGiftActionMapper liveGiveGiftActionMapper;

    @Autowired
    private WcPlayerSignCharmStateMapper wcPlayerSignCharmStateMapper;

    @Autowired
    private WcPlayerSignCharmStateMapper playerSignCharmStateMapper;

    @Override
    public List<PlayerSignCharmSumPo> selectPlayerSignCharmSumByUsers(long njId, long familyId, List<Long> userIds, Date startDate, Date endDate) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        String startDateStr = DateUtil.formatDateToString(startDate, DateUtil.date_2);
        String endDateStr = DateUtil.formatDateToString(endDate, DateUtil.date_2);
        int appId = BusinessEvnEnum.XIMI.appId();
        return wcPlayerSignCharmStateMapper.selectPlayerSignCharmSumByUsers(njId, familyId, appId, userIds, startDateStr, endDateStr);
    }

    @Override
    public Integer getRoomIncomeCharm(List<Long> roomIds, long playerId, Date startDate, Date endDate) {
        return liveGiveGiftActionMapper.getRoomIncomeCharm(roomIds, playerId, startDate, endDate);
    }

    @Override
    public Integer getPersonIncomeCharm(long playerId, Date startDate, Date endDate) {
        //西米没有个播统计
        return 0;
    }

    @Override
    public Map<Long, AdminPlayerIncomeInfoPo> getAdminPlayerIncomeInfoMap(List<Long> njIds, Date startDate, Date endDate) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        String startDateStr = DateUtil.formatDateToString(startDate, DateUtil.date_2);
        String endDateStr = DateUtil.formatDateToString(endDate, DateUtil.date_2);
        List<CharmStatPo> countList = playerSignCharmStateMapper.getRoomPlayerCharmByRooms(appId, njIds, startDateStr, endDateStr);
        if (CollectionUtils.isEmpty(countList)) {
            log.info("xm getAdminPlayerIncomeInfoMap countList is empty");
            return Collections.emptyMap();
        }

        Map<Long, List<CharmStatPo>> groupMap = countList.stream().collect(Collectors.groupingBy(CharmStatPo::getNjId));
        //按照厅汇总魅力值返回
        Map<Long, AdminPlayerIncomeInfoPo> result = new HashMap<>();
        for (Map.Entry<Long, List<CharmStatPo>> entry : groupMap.entrySet()) {
            List<CharmStatPo> value = entry.getValue();
            if (CollectionUtils.isEmpty(value)) {
                continue;
            }
            Long sumValue = value.stream().map(CharmStatPo::getValue).reduce(0L, Long::sum);
            result.put(entry.getKey(), new AdminPlayerIncomeInfoPo().setNjId(entry.getKey()).setTotalCharmValue(sumValue));
        }
        return result;
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.XIMI;
    }
}
