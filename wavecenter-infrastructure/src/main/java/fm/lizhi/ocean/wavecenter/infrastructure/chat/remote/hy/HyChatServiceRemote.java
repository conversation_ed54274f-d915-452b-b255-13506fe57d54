package fm.lizhi.ocean.wavecenter.infrastructure.chat.remote.hy;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.hy.chat.api.PpChatService;
import fm.lizhi.hy.chat.constant.ChatQueueId;
import fm.lizhi.hy.chat.constant.ChatType;
import fm.lizhi.hy.chat.protocol.PpChatProto;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.chat.remote.IChatServiceRemote;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class HyChatServiceRemote implements IChatServiceRemote {

    @Autowired
    private PpChatService chatService;


    @Override
    public Result<Void> sendChatAsync(long senderUid, long receiverUid, String content) {
        Result<PpChatProto.ResponseSendChatAsync> result = chatService.sendChatAsync(senderUid, receiverUid, ChatType.TEXT.getValue(), content,
                false, ChatQueueId.HIGH_PRIORITY.getValue());
        return new Result<>(result.rCode(), null);
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.HEI_YE;
    }

    @Override
    public Result<Void> sendRichTextChatAsync(long senderUid, long receiverUid, String content) {
        Result<PpChatProto.ResponseSendChatAsync> result = chatService.sendChatAsync(senderUid, receiverUid, ChatType.RICH_TEXT.getValue(), content,
                false, ChatQueueId.HIGH_PRIORITY.getValue());
        return new Result<>(result.rCode(), null);
    }

    @Override
    public Result<Void> sendCardChatAsync(long senderUid, long receiverUid, String content) {
        Result<PpChatProto.ResponseSendChatAsync> result = chatService.sendChatAsync(senderUid, receiverUid, ChatType.CARD.getValue(), content,
                false, ChatQueueId.HIGH_PRIORITY.getValue());
        return new Result<>(result.rCode(), null);
    }

    @Override
    public Result<Void> sendRichTextChatAsyncLowPriority(long senderUid, long receiverUid, String content) {
        Result<PpChatProto.ResponseSendChatAsync> result = chatService.sendChatAsync(senderUid, receiverUid, ChatType.RICH_TEXT.getValue(), content,
                false, ChatQueueId.LOW_PRIORITY.getValue());
        return new Result<>(result.rCode(), null);
    }
}
