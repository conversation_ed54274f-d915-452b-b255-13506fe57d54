package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.common.datastore.mysql.constant.ParamContants;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.PlayerSignCharmSumPo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.CharmStatPo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.PlayerSignPerformancePo;
import fm.lizhi.ocean.wavecenter.infrastructure.income.dto.PlayerPayCountParamDto;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 查询业务从库
 * <AUTHOR>
 * @date 2024/4/18 20:10
 */
@DataStore(namespace = "mysql_pplive_lzppfamily_r")
public interface PpPlayerSignCharmStatMapper {

    /**
     * 查询主播魅力值汇总
     * @param njId
     * @param startDate
     * @param endDate
     * @param orderType
     * @return
     */
    @Select({
            "<script>"
            , "select s.user_id, sum(s.value) totalValue"
            , "from player_sign_charm_stat s"
            , "where s.nj_id = #{njId}"
            , "and s.family_id= #{familyId}"
            , "and s.stat_day &gt;= #{startDate}"
            , "and s.stat_day &lt;= #{endDate}"
            , "group by s.user_id "
            , "having totalValue != 0"
            , "order by totalValue ${orderType}"
            ,"</script>"
    })
    PageList<PlayerSignPerformancePo> selectPlayerSignCharmSum(@Param("njId") long njId
            , @Param("familyId") long familyId
            , @Param("startDate") String startDate
            , @Param("endDate") String endDate
            , @Param("orderType") String orderType
            , @Param(ParamContants.PAGE_NUMBER)int pageNumber
            , @Param(ParamContants.PAGE_SIZE)int pageSize);


    @Select({
            "<script>"
            , "select s.user_id, sum(s.value) totalValue"
            , "from player_sign_charm_stat s"
            , "where s.nj_id = #{njId} and s.family_id=#{familyId}"
            , "<if test='null != userIds and userIds.size > 0'>"
            , "and s.user_id in "
            , "<foreach collection='userIds' item='uId' open='(' separator=',' close=')'>"
            , "#{uId}"
            , "</foreach>"
            , "</if>"
            , "and s.stat_day &gt;= #{startDate} "
            , "and s.stat_day &lt;= #{endDate} "
            , "group by s.user_id "
            ,"</script>"
    })
    List<PlayerSignCharmSumPo> selectPlayerSignCharmSumByUsers(@Param("njId") long njId
            , @Param("familyId") long familyId
            , @Param("userIds") List<Long> userIds
            , @Param("startDate") String startDate
            , @Param("endDate") String endDate);


    @Select({
            "<script>"
            , "select s.user_id, s.nj_id, sum(s.value) value"
            , "from player_sign_charm_stat s"
            , "where s.family_id = #{familyId}"
            , "and s.stat_day=#{date}"

            , "<if test='null != roomIds and roomIds.size > 0'>"
            , "and s.nj_id in "
            , "<foreach collection='roomIds' item='rId' open='(' separator=',' close=')'>"
            , "#{rId}"
            , "</foreach>"
            , "</if>"

            , "group by s.user_id, s.nj_id"
            , "order by value ${orderType}"
            , "limit 10"
            ,"</script>"
    })
    List<CharmStatPo> guildPlayerRankPageList(@Param("familyId") long familyId
            , @Param("roomIds") List<Long> roomIds
            , @Param("date") String date
            , @Param("orderType") String orderType);



    @Select({
            "<script>"
            , "select s.user_id, s.nj_id, sum(s.value) value"
            , "from player_sign_charm_stat s"
            , "where s.nj_id = #{njId}"
            , "and s.stat_day=#{date}"
//            , "and s.family_id = #{familyId}"
            , "group by s.user_id, s.nj_id"
            , "order by value ${orderType}"
            , "limit 10"
            ,"</script>"
    })
    List<CharmStatPo> roomPlayerRankPageList(@Param("njId") long njId, @Param("familyId") long familyId, @Param("date") String date, @Param("orderType") String orderType);

    /**
     * 获取公会的有收入主播数
     * @param param
     * @param stateDayStart
     * @param stateDayEnd
     * @return
     */
    @Select({"<script>"
            , " select count(distinct user_id) as incomeCount from player_sign_charm_stat "
            , " where stat_day &gt;= #{stateDayStart} and stat_day &lt;=#{stateDayEnd} and is_deleted = 0 "
            , "<if test=' null != param.roomIds and param.roomIds.size > 0 '>"
            , "and nj_id in "
            , "<foreach collection='param.roomIds' item='rId' open='(' separator=',' close=')'>"
            , "#{rId}"
            , "</foreach>"
            , "</if>"
            , "<if test=' null != param.playerIds and param.playerIds.size > 0 '>"
            , "and user_id in "
            , "<foreach collection='param.playerIds' item='pId' open='(' separator=',' close=')'>"
            , "#{pId}"
            , "</foreach>"
            , "</if>"
            , " group by is_deleted "
            , "</script>"
    })
    Integer getPlayerPayCountForFamily(@Param("param") PlayerPayCountParamDto param
            , @Param("stateDayStart") String stateDayStart
            , @Param("stateDayEnd") String stateDayEnd);



}
