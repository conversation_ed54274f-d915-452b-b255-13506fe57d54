package fm.lizhi.ocean.wavecenter.infrastructure.sign.remote;

import fm.lizhi.ocean.wavecenter.base.remote.IRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.request.RequestContractSettle;
import fm.lizhi.ocean.wavecenter.service.sign.dto.SignSettleDTO;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/10/15 19:13
 */
public interface ISettleRemote extends IRemote {

    /**
     * 查询结算信息
     * key=njId
     * @param request
     * @return
     */
    Map<Long, SignSettleDTO> querySettle(RequestContractSettle request);

    /**
     * 查询厅对应的结算信息
     * @param njId
     * @return
     */
    Optional<SignSettleDTO> querySettleByNj(Long njId);

}
