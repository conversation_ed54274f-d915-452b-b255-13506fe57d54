package fm.lizhi.ocean.wavecenter.infrastructure.grow.datastore.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 公会等级奖励参考图
 *
 * @date 2025-03-18 04:41:38
 */
@Table(name = "`wavecenter_family_level_award`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcFamilyLevelAward {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 等级id
     */
    @Column(name= "`level_id`")
    private Long levelId;

    /**
     * 奖励参考图
     */
    @Column(name= "`award_img`")
    private String awardImg;

    /**
     * 业务 ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`update_time`")
    private Date updateTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", levelId=").append(levelId);
        sb.append(", awardImg=").append(awardImg);
        sb.append(", appId=").append(appId);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }
}