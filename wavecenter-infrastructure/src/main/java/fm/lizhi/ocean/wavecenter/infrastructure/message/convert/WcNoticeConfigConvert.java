package fm.lizhi.ocean.wavecenter.infrastructure.message.convert;

import java.util.List;

import fm.lizhi.ocean.wavecenter.service.message.dto.WcNoticeConfigQueryResDTO;
import fm.lizhi.ocean.wavecenter.service.message.dto.WcNoticeConfigSaveParamDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import com.lizhi.commons.config.core.util.ConfigUtils;

import fm.lizhi.ocean.wavecenter.datastore.platform.message.entity.WcNoticeConfig;
import fm.lizhi.ocean.wavecenter.service.message.dto.WcNoticeConfigDTO;

/**
 * <AUTHOR>
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR,
        imports = {
                ConfigUtils.class
        }
)
public interface WcNoticeConfigConvert {
    WcNoticeConfigConvert I = Mappers.getMapper(WcNoticeConfigConvert.class);

    /**
     * dto 转 entity
     *
     * @param dto dto
     * @return 结果
     */
    @Mapping(target = "deleted", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "modifyTime", ignore = true)
    WcNoticeConfig dtoToEntity(WcNoticeConfigSaveParamDTO dto);

    /**
     * entity 转 dto
     *
     * @param list 数据
     * @return 结果
     */
    List<WcNoticeConfigQueryResDTO> queryResEntityToDTOList(List<WcNoticeConfig> list);

    @Mapping(target = "read", constant = "false")
    WcNoticeConfigQueryResDTO queryResEntityToDTO(WcNoticeConfig config);

    List<WcNoticeConfigDTO> entityToDTOList(List<WcNoticeConfig> page);


}
