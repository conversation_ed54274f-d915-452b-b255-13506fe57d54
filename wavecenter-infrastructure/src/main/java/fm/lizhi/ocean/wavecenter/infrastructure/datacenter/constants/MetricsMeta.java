package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.constants;

import fm.lizhi.ocean.wavecenter.base.util.CalculateUtil;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/4/19 23:48
 */
public class MetricsMeta {

    /**
     * 指标名
     */
    @Getter
    private String name;

    /**
     * 持久化名称
     */
    @Getter
    private String poName;

    /**
     * 别名
     */
    @Getter
    private String aliasName;

    /**
     * 值工厂
     */
    @Getter
    private ValueFactory valueFactory;

    private MetricsMeta() {
    }

    public MetricsMeta(String name) {
        this(name, name, name);
    }

    public MetricsMeta(String name, ValueFactory valueFactory) {
        this(name, name, name, valueFactory);
    }

    public MetricsMeta(String name, String poName, String aliseName) {
        this(name, poName, aliseName, null);
    }

    public MetricsMeta(String name, String poName, String aliseName, ValueFactory valueFactory) {
        this.name = name;
        this.poName = poName;
        this.aliasName = aliseName;
        if (valueFactory == null) {
            this.valueFactory = (calculateMetrics, baseValue) -> {
                String valueStr = baseValue.get(calculateMetrics.getAliasName());
                return CalculateUtil.formatDecimal(valueStr);
            };
        } else {
            this.valueFactory = valueFactory;
        }
    }

    @FunctionalInterface
    public interface ValueFactory{

        String calculateValue(MetricsMeta calculateMetrics, Map<String, String> baseValue);

        default String calculateValue(MetricsMeta calculateMetrics, String name, String value){
            Map<String, String> baseValue = new HashMap<>(1);
            baseValue.put(name, value);
            return calculateValue(calculateMetrics, baseValue);
        }
    }
}
