package fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WaveCheckInUnDone;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * 麦序福利只读库扩展mapper, 用于web站数据统计
 */
@DataStore(namespace = "mysql_ocean_wave_r")
public interface WaveCheckInUnDoneExtMapper {

    @Select("<script>\n" +
            "  SELECT * FROM wave_check_in_un_done\n" +
            "  WHERE user_id IN\n" +
            "    <foreach collection=\"userIds\" item=\"userId\" open=\"(\" separator=\",\" close=\")\">\n" +
            "      #{userId}\n" +
            "    </foreach>\n" +
            "    AND room_id = #{roomId}\n" +
            "    AND curr_date &gt;= #{minDateInclude}\n" +
            "    AND curr_date &lt;= #{maxDateInclude}\n" +
            "</script>")
    List<WaveCheckInUnDone> getCheckInRoomUserUnDoneList(@Param("userIds") List<Long> userIds, @Param("roomId") Long roomId,
                                                         @Param("minDateInclude") Date minDateInclude,
                                                         @Param("maxDateInclude") Date maxDateInclude);

    @Select("<script>\n" +
            "  SELECT * FROM wave_check_in_un_done\n" +
            "  WHERE room_id IN\n" +
            "    <foreach collection=\"roomIds\" item=\"roomId\" open=\"(\" separator=\",\" close=\")\">\n" +
            "      #{roomId}\n" +
            "    </foreach>\n" +
            "    AND user_id = #{userId}\n" +
            "    AND curr_date &gt;= #{minDateInclude}\n" +
            "    AND curr_date &lt;= #{maxDateInclude}\n" +
            "    <if test=\"familyId != null\">\n" +
            "      AND family_id = #{familyId}\n" +
            "    </if>\n" +
            "</script>")
    List<WaveCheckInUnDone> getCheckInUserUnDoneList(@Param("userId") long userId, @Param("roomIds") List<Long> roomIds,
                                                     @Param("minDateInclude") Date minDateInclude,
                                                     @Param("maxDateInclude") Date maxDateInclude,
                                                     @Param("familyId") Long familyId);
}
