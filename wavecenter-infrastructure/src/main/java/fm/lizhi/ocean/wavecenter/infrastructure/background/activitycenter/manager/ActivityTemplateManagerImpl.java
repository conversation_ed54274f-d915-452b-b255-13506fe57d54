package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.manager;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityTemplateGeneralPageBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityTemplateHotPageBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityTemplateInfoBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityResourceStatusEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityTemplateStatusEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.AutoConfigResourceEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestCountGeneralActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestPageGeneralActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestPageHotActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseGetGeneralActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateFlowResourceBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateFlowResourceExtraBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplatePageBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.*;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseGetActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseGetActivityTemplateShelfStatus;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert.ActivityTemplateInfoConverter;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.dao.ActivityResourceDao;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.dao.ActivityTemplateDao;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityResourceConfig;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityTemplateInfo;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.LogicDeleteConstants;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.ActivityConfig;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.CommonActivityConfig;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityTemplateManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityTemplateFlowResourceDTO;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 活动模板管理器实现
 */
@Component
@Slf4j
public class ActivityTemplateManagerImpl implements ActivityTemplateManager {

    @Autowired
    private ActivityConfig activityConfig;

    @Autowired
    private ActivityResourceDao activityResourceDao;

    @Autowired
    private ActivityTemplateDao activityTemplateDao;
    @Autowired
    private ActivityTemplateInfoConverter activityTemplateInfoConverter;

    @Autowired
    private FamilyManager familyManager;

    @Override
    public Result<Long> createTemplate(RequestCreateActivityTemplate req) {
        log.info("createTemplate req={}", req);
        Result<Void> validateFlowResources = validateFlowResources(req.getAppId(), req.getFlowResources());
        if (RpcResult.isFail(validateFlowResources)) {
            return RpcResult.fail(CommonService.PARAM_ERROR, validateFlowResources.getMessage());
        }
        long templateId = activityTemplateDao.createTemplate(req);
        log.info("createTemplate templateId={}", templateId);
        return RpcResult.success(templateId);
    }

    private Result<Void> validateFlowResources(Integer appId, List<ActivityTemplateFlowResourceBean> flowResources) {
        if (CollectionUtils.isEmpty(flowResources)) {
            return RpcResult.success();
        }
        // 查询资源配置
        ArrayList<Long> resourceConfigIds = new ArrayList<>();
        for (ActivityTemplateFlowResourceBean flowResource : flowResources) {
            resourceConfigIds.add(flowResource.getResourceConfigId());
        }
        Map<Long, ActivityResourceConfig> resourceConfigIdToResourceConfigMap = activityResourceDao
                .getResourceByIdsAsMap(resourceConfigIds);
        CommonActivityConfig bizConfig = activityConfig.getBizConfig(appId);
        // 校验资源
        for (ActivityTemplateFlowResourceBean flowResource : flowResources) {
            Long resourceConfigId = flowResource.getResourceConfigId();
            ActivityTemplateFlowResourceExtraBean extra = flowResource.getExtra();
            ActivityResourceConfig resourceConfig = resourceConfigIdToResourceConfigMap.get(resourceConfigId);
            if (resourceConfig == null
                    || Objects.equals(resourceConfig.getDeleted(), LogicDeleteConstants.DELETED)
                    || Objects.equals(resourceConfig.getStatus(), ActivityResourceStatusEnum.DISABLED.getValue())) {
                log.info("validateFlowResources resourceConfig is invalid, resourceConfigId={}", resourceConfigId);
                return RpcResult.fail(CommonService.PARAM_ERROR, "无效的资源配置id, id=" + resourceConfigId);
            }
            // 校验官频位资源
            if (Objects.equals(resourceConfig.getResourceCode(), AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode())) {
                // 先保留，保证平滑上线
                Integer durationLimit = extra.getDurationLimit();
                List<Integer> officialSeatDurationLimits = bizConfig.getOfficialSeatDurationLimits();
                if (durationLimit != null && !officialSeatDurationLimits.contains(durationLimit)) {
                    log.info("validateFlowResources durationLimit is invalid, resourceConfigId={}, durationLimit: {}," +
                            " officialSeatDurationLimits: {}", resourceConfigId, durationLimit, officialSeatDurationLimits);
                    return RpcResult.fail(CommonService.PARAM_ERROR, "官频位资源时长限制无效");
                }
                List<Integer> officialSeatNumbers = extra.getOfficialSeatNumbers();
                if (CollectionUtils.isEmpty(officialSeatNumbers)) {
                    log.info("validateFlowResources officialSeatNumbers is empty, resourceConfigId={}", resourceConfigId);
                    return RpcResult.fail(CommonService.PARAM_ERROR, "官频位资源开放位置不能为空");
                }
                List<Integer> officialSeatAvailableNumbers = bizConfig.getOfficialSeatAvailableNumbers();
                if (!CollectionUtils.containsAll(officialSeatAvailableNumbers, officialSeatNumbers)) {
                    log.info("validateFlowResources officialSeatNumbers is invalid, resourceConfigId={}," +
                                    " officialSeatNumbers: {}, officialSeatAvailableNumbers: {}", resourceConfigId,
                            officialSeatNumbers, officialSeatAvailableNumbers);
                    return RpcResult.fail(CommonService.PARAM_ERROR, "官频位资源开放位置无效");
                }
            }
        }
        return RpcResult.success();
    }

    @Override
    public Result<Void> updateTemplate(RequestUpdateActivityTemplate req) {
        log.info("updateTemplate req={}", req);
        Long templateId = req.getId();
        if (activityTemplateDao.isTemplateAbsent(templateId)) {
            return RpcResult.fail(CommonService.PARAM_ERROR, "无效的更新模板参数, id=" + templateId);
        }
        Result<Void> validateFlowResources = validateFlowResources(req.getAppId(), req.getFlowResources());
        if (RpcResult.isFail(validateFlowResources)) {
            return RpcResult.fail(CommonService.PARAM_ERROR, validateFlowResources.getMessage());
        }
        activityTemplateDao.updateTemplate(req);
        return RpcResult.success();
    }

    @Override
    public Result<Void> deleteTemplate(RequestDeleteActivityTemplate req) {
        log.info("deleteTemplate req={}", req);
        Long templateId = req.getId();
        if (activityTemplateDao.isTemplateAbsent(templateId)) {
            return RpcResult.fail(CommonService.PARAM_ERROR, "无效的删除模板参数, id=" + templateId);
        }
        activityTemplateDao.deleteTemplate(req);
        return RpcResult.success();
    }

    @Override
    public Result<Void> updateShelfStatus(RequestUpdateActivityTemplateShelfStatus req) {
        log.info("updateShelfStatus req={}", req);
        Long templateId = req.getId();
        if (activityTemplateDao.isTemplateAbsent(templateId)) {
            return RpcResult.fail(CommonService.PARAM_ERROR, "无效的更新模板上下架状态参数, id=" + templateId);
        }
        activityTemplateDao.updateShelfStatus(req);
        return RpcResult.success();
    }

    @Override
    public void updateStatus(Long templateId, ActivityTemplateStatusEnum status) {
        log.info("templateId={}, status={}", templateId, status);
        activityTemplateDao.updateStatus(templateId, status);
    }

    @Override
    public Result<ResponseGetActivityTemplateShelfStatus> getShelfStatus(long templateId) {
        log.info("getShelfStatus templateId={}", templateId);
        if (activityTemplateDao.isTemplateAbsent(templateId)) {
            return RpcResult.fail(CommonService.PARAM_ERROR, "无效的获取模板上下架状态参数, id=" + templateId);
        }
        ResponseGetActivityTemplateShelfStatus resp = activityTemplateDao.getShelfStatus(templateId);
        log.debug("getShelfStatus resp={}", resp);
        return RpcResult.success(resp);
    }

    @Override
    public Result<PageBean<ActivityTemplatePageBean>> pageTemplate(RequestPageActivityTemplate req) {
        log.info("pageTemplate req={}", req);
        PageList<ActivityTemplatePageBean> page = activityTemplateDao.pageTemplate(req);
        return RpcResult.success(PageBean.of(page.getTotal(), page));
    }

    @Override
    public Result<ResponseGetActivityTemplate> getTemplate(long templateId) {
        log.info("getTemplate templateId={}", templateId);
        if (activityTemplateDao.isTemplateAbsent(templateId)) {
            return RpcResult.fail(CommonService.PARAM_ERROR, "无效的获取模板参数, id=" + templateId);
        }
        ResponseGetActivityTemplate resp = activityTemplateDao.getTemplate(templateId);
        log.debug("getTemplate resp={}", resp);
        return RpcResult.success(resp);
    }

    @Override
    public Result<PageBean<ActivityTemplateHotPageBean>> pageHotTemplate(RequestPageHotActivityTemplate req, List<Long> njList) {
        log.info("pageHotTemplate req={}", req);
        PageList<ActivityTemplateHotPageBean> page = activityTemplateDao.pageHotTemplate(req, njList);
        return RpcResult.success(PageBean.of(page.getTotal(), page));
    }

    @Override
    public Result<PageBean<ActivityTemplateGeneralPageBean>> pageGeneralTemplate(RequestPageGeneralActivityTemplate req) {
        log.info("pageGeneralTemplate req={}", req);
        // 抽取为公共方法
        Optional<Long> njId = enrichRequestWithUserNjIds(req.getRequestUserId());
        njId.ifPresent(aLong -> req.getNjIds().add(aLong));

        PageList<ActivityTemplateGeneralPageBean> page = activityTemplateDao.pageGeneralTemplate(req);
        return RpcResult.success(PageBean.of(page.getTotal(), page));
    }

    @Override
    public Result<Long> countGeneralTemplate(RequestCountGeneralActivityTemplate req) {
        log.info("countGeneralTemplate req={}", req);
        // 复用用户签约厅主查询逻辑
        Optional<Long> njIds = enrichRequestWithUserNjIds(req.getRequestUserId());
        njIds.ifPresent(aLong -> req.getNjIds().add(aLong));

        Long count = activityTemplateDao.countGeneralTemplate(req);
        return RpcResult.success(count);
    }

    /**
     * 根据用户信息丰富请求中的厅主ID列表
     *
     * @return
     */
    private Optional<Long> enrichRequestWithUserNjIds(Long requestUserId) {
        UserInFamilyBean userInFamily = familyManager.getUserInFamily(requestUserId);
        if (userInFamily.isRoom() || userInFamily.isPlayer()) {
            log.info("njId={}", userInFamily.getNjId());
            return Optional.ofNullable(userInFamily.getNjId());
        }
        return Optional.empty();
    }

    @Override
    public Result<ResponseGetGeneralActivityTemplate> getGeneralTemplate(long templateId) {
        log.info("getGeneralTemplate templateId={}", templateId);
        if (activityTemplateDao.isTemplateAbsent(templateId)) {
            return RpcResult.fail(CommonService.PARAM_ERROR, "无效的获取通用模板详情参数, id=" + templateId);
        }
        ResponseGetGeneralActivityTemplate resp = activityTemplateDao.getGeneralTemplate(templateId);
        log.debug("getGeneralTemplate resp={}", resp);
        return RpcResult.success(resp);
    }

    @Override
    public ActivityTemplateInfoBean getTemplateInfoBean(Long templateId) {
        if (templateId == null) {
            log.warn("getTemplateInfoBean invalid. templateId is null");
            return null;
        }
        ActivityTemplateInfo templateInfo = activityTemplateDao.getTemplateInfoById(templateId);
        if (templateInfo == null) {
            log.warn("getTemplateInfoBean failed, templateInfo is null. templateId={}", templateId);
            return null;
        }
        return activityTemplateInfoConverter.toActivityTemplateInfoBean(templateInfo);
    }

    @Override
    public ActivityTemplateFlowResourceDTO getTemplateOfficialSeat(long templateId) {
        log.info("getTemplateOfficialSeat templateId={}", templateId);
        if (activityTemplateDao.isTemplateAbsent(templateId)) {
            log.warn("getTemplateOfficialSeat failed, template is absent. templateId={}", templateId);
            return null;
        }
        return activityTemplateDao.getTemplateOfficialSeat(templateId);
    }

    @Override
    public List<Long> getTemplateNjWhitelist(long templateId) {
        return activityTemplateDao.getTemplateNjWhitelist(templateId, ContextUtils.getBusinessEvnEnum().getAppId());
    }

    @Override
    public ActivityTemplateFlowResourceDTO getTemplateProgramme(long templateId) {
        log.info("getTemplateProgramme templateId={}", templateId);
        return activityTemplateDao.getTemplateProgramme(templateId);
    }
}
