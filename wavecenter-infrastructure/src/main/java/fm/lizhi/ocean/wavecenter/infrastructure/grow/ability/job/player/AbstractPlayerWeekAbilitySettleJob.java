package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.job.player;

import fm.lizhi.common.datastore.redis.client.RedisClient;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.common.utils.SingleNodeRunLock;
import fm.lizhi.ocean.wavecenter.infrastructure.common.util.RedisSingleNodeRunLock;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.SettlePeriodDTO;
import fm.lizhi.ocean.wavecenter.service.grow.ability.handler.PlayerAbilitySettleHandler;
import fm.lizhi.ocean.wavecenter.service.grow.ability.handler.RoomAbilitySettleHandler;
import fm.lizhi.ocean.wavecenter.service.grow.ability.handler.RoomInFamilyRankHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 主播周能力结算
 * <AUTHOR>
 * @date 2025/6/6 11:45
 */
@Slf4j
public abstract class AbstractPlayerWeekAbilitySettleJob {

    @Autowired
    private PlayerAbilitySettleHandler playerAbilitySettleHandler;
    @Autowired
    private RoomAbilitySettleHandler roomAbilitySettleHandler;
    @Autowired
    private RoomInFamilyRankHandler roomInFamilyRankHandler;

    /**
     * 结算入口
     */
    protected void doSettle(PlayerWeekAbilitySettleParam param){
        // 获取结算周期
        SettlePeriodDTO settlePeriod = getSettlePeriod(param);
        log.info("settlePeriod={}", settlePeriod);

        // 是否指定结算
        List<Long> playerIds = settlePlayerIds(param);
        if (CollectionUtils.isEmpty(playerIds)) {
            // 主播结算
            playerAbilitySettleHandler.settleByPeriod(settlePeriod);
            // 厅结算
            roomAbilitySettleHandler.settleByPeriod(settlePeriod);
            // 厅排名结算
            roomInFamilyRankHandler.settle(settlePeriod);
        } else {
            playerAbilitySettleHandler.settleByPeriod(settlePeriod, playerIds);
        }
    }

    /**
     * 获取结算周期
     * @param param
     * @return
     */
    private SettlePeriodDTO getSettlePeriod(PlayerWeekAbilitySettleParam param){
        // 优先获取参数
        if (param != null && param.getWeekStartDay() != null) {
            Date weekStart = MyDateUtil.getDayValueDate(param.getWeekStartDay());
            Date weekEnd = MyDateUtil.getLastWeekEndDayByStartDay(weekStart);
            return new SettlePeriodDTO(weekStart, weekEnd);
        }

        // 否则获取上一个自然周的周期
        Date lastWeekStartDay = MyDateUtil.getLastWeekStartDay();
        Date lastWeekEndDay = MyDateUtil.getLastWeekEndDay();
        return new SettlePeriodDTO(lastWeekStartDay, lastWeekEndDay);
    }

    /**
     * 查询指定结算名单
     * @param param
     * @return
     */
    private List<Long> settlePlayerIds(PlayerWeekAbilitySettleParam param){
        // 优先获取参数
        if (param != null && CollectionUtils.isNotEmpty(param.getPlayerIds())) {
            return param.getPlayerIds();
        }
        return Collections.emptyList();
    }

}
