package fm.lizhi.ocean.wavecenter.infrastructure.grow.tasktemplate.manager;

import com.alibaba.fastjson.JSON;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.tasktemplate.convert.TaskTemplateConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.tasktemplate.datastore.dao.TaskTemplateDao;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.tasktemplate.datastore.dao.GrowTaskTemplateCapabilityDao;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.tasktemplate.datastore.entity.WcGrowTaskTemplate;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.tasktemplate.datastore.entity.WcGrowTaskTemplateCapability;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto.*;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.manager.TaskTemplateManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 任务模版Manager实现类
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Slf4j
@Component
public class TaskTemplateManagerImpl implements TaskTemplateManager {

    @Autowired
    private TaskTemplateDao taskTemplateDao;

    @Autowired
    private GrowTaskTemplateCapabilityDao growTaskTemplateCapabilityDao;

    @Override
    public Boolean saveTaskTemplate(SaveTaskTemplateDTO saveTaskTemplateDTO, String operator) {
        log.info("saveTaskTemplate saveTaskTemplateDTO={}, createUser={}", saveTaskTemplateDTO, operator);

        if (saveTaskTemplateDTO == null || CollectionUtils.isEmpty(saveTaskTemplateDTO.getTaskTemplateList())) {
            log.warn("saveTaskTemplate request is empty");
            return false;
        }

        try {
            // 处理每个任务模版项
            for (TaskTemplateItemDTO item : saveTaskTemplateDTO.getTaskTemplateList()) {
                // 生成模版代码
                String templateCode = generateTemplateCode(item);
                // 查询当前版本号
                Long maxVersion = taskTemplateDao.getMaxVersionByTemplateCode(templateCode, saveTaskTemplateDTO.getAppId());
                Long newVersion = maxVersion == null ? 1L : maxVersion + 1L;
                // 构建任务模版实体
                WcGrowTaskTemplate taskTemplate = buildTaskTemplateEntity(item, templateCode, newVersion, saveTaskTemplateDTO.getAppId(), operator);
                // 构建能力配置实体列表
                List<WcGrowTaskTemplateCapability> capabilityEntities = buildCapabilityEntities(item, saveTaskTemplateDTO.getAppId());
                // 保存任务模版（事务操作）
                taskTemplateDao.saveTaskTemplate(taskTemplate, capabilityEntities, maxVersion);
            }

            return true;

        } catch (Exception e) {
            log.error("saveTaskTemplate error", e);
            return false;
        }
    }

    @Override
    public TaskTemplateQueryResultDTO pageQueryTaskTemplateList(Integer appId, int pageNum, int pageSize) {
        // 1. 分页查能力表
        PageList<WcGrowTaskTemplate> pageResult = taskTemplateDao.queryTaskTemplatePage(appId, pageNum, pageSize);
        long total = pageResult.getTotal();

        // 2. 批量查主表
        List<Long> templateIds = pageResult.stream()
                .map(WcGrowTaskTemplate::getId)
                .distinct()
                .collect(Collectors.toList());
        List<WcGrowTaskTemplateCapability> capabilityList = growTaskTemplateCapabilityDao.queryCapabilityByByTemplateIds(appId, templateIds);
        Map<Long, WcGrowTaskTemplate> capabilityMap = pageResult.stream().collect(Collectors.toMap(WcGrowTaskTemplate::getId, t -> t));

        // 3. 组装返回
        List<TaskTemplateQueryItemDTO> resultList = TaskTemplateConvert.I.toQueryItemDTOList(capabilityList, capabilityMap);

        return TaskTemplateQueryResultDTO.builder()
                .list(resultList)
                .total(total)
                .build();
    }

    @Override
    public List<TaskTemplateConditionDTO> queryEnableTaskTemplateConditionList() {
        return taskTemplateDao.queryEnableTaskTemplateConditionList(ContextUtils.getBusinessEvnEnum().getAppId());
    }

    @Override
    public List<TaskTemplateConditionDTO> queryTaskTemplateConditionByIds(List<Long> templateIds) {
        if (CollectionUtils.isEmpty(templateIds)) {
            return Collections.emptyList();
        }
        
        // 1. 根据ID列表查询模板主表
        List<WcGrowTaskTemplate> templateList = taskTemplateDao.getTemplatesByIds(templateIds);
        if (CollectionUtils.isEmpty(templateList)) {
            return Collections.emptyList();
        }
        
        // 2. 批量查询能力配置表
        List<Long> validTemplateIds = templateList.stream().map(WcGrowTaskTemplate::getId).collect(Collectors.toList());
        List<WcGrowTaskTemplateCapability> capabilityList = growTaskTemplateCapabilityDao.queryCapabilityByByTemplateIds(
            ContextUtils.getBusinessEvnEnum().getAppId(), validTemplateIds);
        
        // 3. 按模板ID分组能力配置
        Map<Long, List<WcGrowTaskTemplateCapability>> capabilityMap = capabilityList.stream()
            .collect(Collectors.groupingBy(WcGrowTaskTemplateCapability::getTemplateId));
        
        // 4. 手动转换DTO列表（参考TaskTemplateDao的转换方式）
        List<TaskTemplateConditionDTO> result = new ArrayList<>();
        for (WcGrowTaskTemplate template : templateList) {
            List<WcGrowTaskTemplateCapability> cap = capabilityMap.get(template.getId());
            if (CollectionUtils.isEmpty(cap)) {
                continue;
            }

            TaskTemplateConditionDTO dto = new TaskTemplateConditionDTO();
            dto.setId(template.getId());
            // 解析conditionJson字段
            dto.setConditionGroup(JsonUtil.loads(template.getConditionJson(), ConditionGroupDTO.class));
            // 转换能力配置列表
            dto.setCapability(TaskTemplateConvert.I.capabilityEntityToDto(cap.get(0)));
            
            result.add(dto);
        }
        return result;
    }

    @Override
    public Boolean deleteGrowTaskTemplate(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            log.warn("deleteGrowTaskTemplate id is null");
            return false;
        }
        try {
            return taskTemplateDao.deleteGrowTaskTemplateById(ids);
        } catch (Exception e) {
            log.error("deleteGrowTaskTemplate error", e);
            return false;
        }
    }

    @Override
    public List<TaskTemplateQueryItemDTO> queryTaskTemplateList(String capabilityCode, Integer appId) {
        List<WcGrowTaskTemplateCapability> capabilityList = growTaskTemplateCapabilityDao.queryCapabilityByCapabilityCode(capabilityCode, appId);
        List<Long> templateIds = capabilityList.stream()
                .map(WcGrowTaskTemplateCapability::getTemplateId)
                .distinct()
                .collect(Collectors.toList());
        List<WcGrowTaskTemplate> templateList = taskTemplateDao.getTemplatesByIdsFilterStatus(templateIds);
        Map<Long, WcGrowTaskTemplate> templateMap = templateList.stream().collect(Collectors.toMap(WcGrowTaskTemplate::getId, t -> t));
        return TaskTemplateConvert.I.toQueryItemDTOList(capabilityList, templateMap);
    }

    /**
     * 生成模版代码
     * 如果id为空，则是首次保存，对参数进行MD5加密
     * 如果id不为空，则从已有数据中获取templateCode
     *
     * @param item  任务模版项
     * @return 模版代码
     */
    private String generateTemplateCode(TaskTemplateItemDTO item) {
        if (item.getId() != null) {
            // 如果有ID，从现有数据中获取templateCode
            WcGrowTaskTemplate existTemplate = taskTemplateDao.getTaskTemplateById(item.getId());
            if (existTemplate != null) {
                return existTemplate.getTemplateCode();
            }
        }

        // 首次保存，生成MD5
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 构建任务模版实体
     *
     * @param item         任务模版项
     * @param templateCode 模版代码
     * @param version      版本号
     * @param appId        应用ID
     * @param operator     创建人
     * @return 任务模版实体
     */
    private WcGrowTaskTemplate buildTaskTemplateEntity(TaskTemplateItemDTO item, String templateCode, Long version, Integer appId, String operator) {
        String deployEnv = ConfigUtils.getEnvRequired().name();
        Date now = new Date();
        String conditionJson = JSON.toJSONString(item.getConditionGroup());
        // 启用状态
        return WcGrowTaskTemplate.builder().templateCode(templateCode).version(version).status(1).deleted(Boolean.FALSE)
                .appId(appId).deployEnv(deployEnv).createTime(now).modifyTime(now).createUser(operator).conditionJson(conditionJson).build();
    }

    /**
     * 构建能力配置实体列表
     *
     * @param item  任务模版项
     * @param appId 应用ID
     * @return 能力配置实体列表
     */
    private List<WcGrowTaskTemplateCapability> buildCapabilityEntities(TaskTemplateItemDTO item, Integer appId) {
        String deployEnv = ConfigUtils.getEnvRequired().name();
        Date now = new Date();
        List<WcGrowTaskTemplateCapability> capabilities = new ArrayList<>();

        WcGrowTaskTemplateCapability capability = WcGrowTaskTemplateCapability.builder().capabilityCode(item.getCapabilityCode()).abilityValue(item.getCapabilityScore()).appId(appId).deployEnv(deployEnv).createTime(now).modifyTime(now).build();

        capabilities.add(capability);
        return capabilities;
    }

    /**
     * 转换任务模版列表为查询结果项列表
     *
     * @param templateList 任务模版列表
     * @return 查询结果项列表
     */
    private List<TaskTemplateQueryItemDTO> convertToQueryItemList(List<WcGrowTaskTemplate> templateList) {
        if (CollectionUtils.isEmpty(templateList)) {
            return new ArrayList<>();
        }

        List<TaskTemplateQueryItemDTO> itemList = new ArrayList<>();

        for (WcGrowTaskTemplate template : templateList) {
            // 查询对应的能力配置
            List<WcGrowTaskTemplateCapability> capabilities = growTaskTemplateCapabilityDao.queryCapabilityByByTemplateIds(template.getAppId(), Collections.singletonList(template.getId()));

            if (CollectionUtils.isNotEmpty(capabilities)) {
                // 每个能力配置生成一个查询结果项
                for (WcGrowTaskTemplateCapability capability : capabilities) {
                    TaskTemplateQueryItemDTO item = TaskTemplateQueryItemDTO.builder().id(String.valueOf(template.getId())).capabilityCode(capability.getCapabilityCode()).capabilityScore(capability.getAbilityValue().intValue()).conditionJson(template.getConditionJson()).capabilityName(null).build();
                    itemList.add(item);
                }
            }
        }

        return itemList;
    }
} 