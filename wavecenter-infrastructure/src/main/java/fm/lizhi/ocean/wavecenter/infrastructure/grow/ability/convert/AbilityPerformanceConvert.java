package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.convert;

import fm.lizhi.ocean.wavecenter.api.grow.ability.bean.*;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowPlayerAbilityWeek;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowPlayerAbilityWeekCapability;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity.WcGrowRoomAbilityWeekCapability;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import org.apache.commons.collections4.ListValuedMap;
import org.mapstruct.*;

import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface AbilityPerformanceConvert {

    /**
     * 将能力项实体列表转换为厅能力bean列表
     *
     * @param entities              能力项实体列表
     * @param capabilityCodeNameMap 能力项code到名称的映射, key: 能力项code, value: 能力项名称
     * @param metricBeanMap         能力项code到指标bean列表的映射, key: 能力项code, value: 指标bean列表
     * @return 厅能力bean列表
     */
    List<RoomAbilityBean> toRoomAbilityBeans(List<WcGrowRoomAbilityWeekCapability> entities, @Context Map<String, String> capabilityCodeNameMap, @Context ListValuedMap<String, RoomAbilityMetricBean> metricBeanMap);

    @Mapping(target = "capabilityName", source = "capabilityCode", qualifiedByName = "getCapabilityName")
    @Mapping(target = "thisWeekValue", source = "abilityValue")
    @Mapping(target = "lastWeekValue", source = "lastWeekAbilityValue")
    @Mapping(target = "thisWeekValueChange", source = "compareWeekValue")
    @Mapping(target = "thisWeekRank", source = "roomInFamilyRank")
    @Mapping(target = "thisWeekRankChange", source = "roomInFamilyCompareWeekRank")
    @Mapping(target = "metrics", source = "capabilityCode", qualifiedByName = "getRoomAbilityMetricBeans")
    RoomAbilityBean toRoomAbilityBean(WcGrowRoomAbilityWeekCapability entity, @Context Map<String, String> capabilityCodeNameMap, @Context ListValuedMap<String, RoomAbilityMetricBean> metricBeanMap);

    @Named("getCapabilityName")
    default String getCapabilityName(String capabilityCode, @Context Map<String, String> capabilityCodeNameMap) {
        return capabilityCodeNameMap.get(capabilityCode);
    }

    @Named("getRoomAbilityMetricBeans")
    default List<RoomAbilityMetricBean> getRoomAbilityMetricBeans(String capabilityCode, @Context ListValuedMap<String, RoomAbilityMetricBean> metricBeanMap) {
        return metricBeanMap.get(capabilityCode);
    }

    /**
     * 将厅主播周结算实体列表转换为厅主播排名bean列表
     *
     * @param entities 厅主播周结算实体列表
     * @param userMap  用户信息映射, key: 用户id, value: 用户dto
     * @return 厅主播排名bean列表
     */
    List<RoomPlayerRankBean> playerAbilityWeeksToBeans(List<WcGrowPlayerAbilityWeek> entities, @Context Map<Long, SimpleUserDto> userMap);

    @Mapping(target = "playerId", source = "playerId")
    @Mapping(target = "playerName", source = "playerId", qualifiedByName = "getUserName")
    @Mapping(target = "playerBand", source = "playerId", qualifiedByName = "getUserBand")
    @Mapping(target = "playerAvatar", source = "playerId", qualifiedByName = "getUserAvatar")
    @Mapping(target = "thisWeekValue", source = "totalScore")
    RoomPlayerRankBean playerAbilityWeekToBean(WcGrowPlayerAbilityWeek entity, @Context Map<Long, SimpleUserDto> userMap);

    /**
     * 将厅主播周结算能力细分实体列表转换为厅主播排名bean列表
     *
     * @param entities 厅主播周结算能力细分实体列表
     * @param userMap  用户信息映射, key: 用户id, value: 用户dto
     * @return 厅主播排名bean列表
     */
    List<RoomPlayerRankBean> playerAbilityWeekCapabilitiesToBeans(List<WcGrowPlayerAbilityWeekCapability> entities, @Context Map<Long, SimpleUserDto> userMap);

    @Mapping(target = "playerId", source = "playerId")
    @Mapping(target = "playerName", source = "playerId", qualifiedByName = "getUserName")
    @Mapping(target = "playerBand", source = "playerId", qualifiedByName = "getUserBand")
    @Mapping(target = "playerAvatar", source = "playerId", qualifiedByName = "getUserAvatar")
    @Mapping(target = "thisWeekValue", source = "abilityValue")
    RoomPlayerRankBean playerAbilityWeekCapabilityToBean(WcGrowPlayerAbilityWeekCapability entity, @Context Map<Long, SimpleUserDto> userMap);

    @Named("getUserName")
    default String getUserName(Long userId, @Context Map<Long, SimpleUserDto> userMap) {
        SimpleUserDto userDto = userMap.get(userId);
        return userDto != null ? userDto.getName() : null;
    }

    @Named("getUserBand")
    default String getUserBand(Long userId, @Context Map<Long, SimpleUserDto> userMap) {
        SimpleUserDto userDto = userMap.get(userId);
        return userDto != null ? userDto.getBand() : null;
    }

    @Named("getUserAvatar")
    default String getUserAvatar(Long userId, @Context Map<Long, SimpleUserDto> userMap) {
        SimpleUserDto userDto = userMap.get(userId);
        return userDto != null ? userDto.getAvatar() : null;
    }

    @Mapping(target = "capabilityName", source = "entity.capabilityCode", qualifiedByName = "getCapabilityName")
    @Mapping(target = "thisWeekValue", source = "entity.abilityValue")
    @Mapping(target = "lastWeekValue", source = "entity.lastWeekAbilityValue")
    @Mapping(target = "thisWeekValueChange", source = "entity.compareWeekValue")
    PlayerAbilityBean toPlayerAbilityBean(WcGrowPlayerAbilityWeekCapability entity, List<PlayerAbilityMetricBean> metrics, List<String> assessMetrics, List<RoomAbilityMetricBean> roomMetrics, @Context Map<String, String> capabilityCodeNameMap);

    @Mapping(target = "startWeekDate", source = "startWeekDate", qualifiedByName = "formatStartWeekDate")
    @Mapping(target = "endWeekDate", source = "startWeekDate", qualifiedByName = "formatEndWeekDateByStartWeekDate")
    PlayerAbilityWeekBean toPlayerAbilityWeekBean(Date startWeekDate, List<PlayerAbilityBean> abilities);

    @Named("formatStartWeekDate")
    default String formatStartWeekDate(Date startWeekDate) {
        if (startWeekDate == null) {
            return null;
        }
        return startWeekDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().format(DateTimeFormatter.ISO_LOCAL_DATE);
    }

    @Named("formatEndWeekDateByStartWeekDate")
    default String formatEndWeekDateByStartWeekDate(Date startWeekDate) {
        if (startWeekDate == null) {
            return null;
        }
        return startWeekDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().plusDays(6).format(DateTimeFormatter.ISO_LOCAL_DATE);
    }
}
