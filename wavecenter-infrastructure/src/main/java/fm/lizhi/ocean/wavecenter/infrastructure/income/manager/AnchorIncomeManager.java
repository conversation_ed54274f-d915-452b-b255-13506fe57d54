package fm.lizhi.ocean.wavecenter.infrastructure.income.manager;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.income.bean.PlayerRevenueAndIncomeBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.PlayerRevenueSumTimeBean;
import fm.lizhi.ocean.wavecenter.api.income.constants.IncomeType;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.CountDownLatchWrapper;
import fm.lizhi.ocean.wavecenter.base.util.ThreadUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PaySettleConfigCodeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PayTenantCodeEnum;
import fm.lizhi.pay.settle.api.creativecenter.CreatorDataQueryService;
import fm.lizhi.pay.settle.protocol.creativecenter.CreatorDataQueryProto;
import fm.lizhi.pay.settle.settleenum.creativecenter.DirectionEnum;
import fm.lizhi.pay.settle.settleenum.creativecenter.PeriodTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

@Slf4j
@Component
public class AnchorIncomeManager {


    @Autowired
    private CreatorDataQueryService creatorDataQueryService;
    @Autowired
    private PaymentManager paymentManager;

    /**
     * 多线程调用线程池
     */
    private final ExecutorService executorService = ThreadUtils.getTtlExecutors("payment", 50, 50);


    public CreatorDataQueryProto.ResponseQueryAnchorIncomeWithEarningDetail queryAnchorIncomeDetailWithEarningDetail(int appId, long userId, DirectionEnum direction, List<IncomeType> incomeType, Date startDate, Date endDate, PaySettleConfigCodeEnum configCodeEnum, int pageNo, int pageSize, Long flushTime) {
        String tenantCode = PayTenantCodeEnum.getPayTenantCode(appId);
        String configCode = configCodeEnum.getConfigCode();
        String startTime = fm.lizhi.commons.util.DateUtil.formatDateToString(startDate, fm.lizhi.commons.util.DateUtil.date_2) + " 00:00:00";
        String endTime = fm.lizhi.commons.util.DateUtil.formatDateToString(endDate, fm.lizhi.commons.util.DateUtil.date_2) + " 23:59:59";

        log.info("queryAnchorIncomeDetailWithEarningDetail[queryAnchorIncomeWithEarningDetail] req userId={} tenantCode={},configCode={},startTime={},endTime={},flushTime={}", userId, tenantCode, configCode, startTime, endTime, flushTime);
        CreatorDataQueryProto.QueryAnchorIncomeWithEarningDetailRequest.Builder builder = CreatorDataQueryProto.QueryAnchorIncomeWithEarningDetailRequest.newBuilder()
                .setTenantCode(tenantCode)
                .setAnchorId(userId)
                .setConfigCode(configCode)
                .setTimeStart(startTime)
                .setTimeEnd(endTime)
                .setDirection(direction.getCode())
                .setPage(CreatorDataQueryProto.Page.newBuilder()
                        .setPageSize(pageSize)
                        .setPageNumber(pageNo)
                        .build());

        if (CollUtil.isNotEmpty(incomeType)) {
            builder.addAllBizId(incomeType.stream().map(IncomeType::getValue).map(String::valueOf).collect(Collectors.toList()));
        }
        Result<CreatorDataQueryProto.ResponseQueryAnchorIncomeWithEarningDetail> result = creatorDataQueryService.queryAnchorIncomeWithEarningDetail(builder.build(),
                DateUtil.formatDateToString(new Date(flushTime), DateUtil.NORMAL_DATE_FORMAT));
        if (RpcResult.isFail(result)) {
            log.warn("queryAnchorIncomeDetailWithEarningDetail[queryAnchorIncomeWithEarningDetail] fail req userId={} tenantCode={},configCode={},startTime={},endTime={},rCode={}", userId, tenantCode, configCode, startTime, endTime, result.rCode());
            return null;
        }
        CreatorDataQueryProto.ResponseQueryAnchorIncomeWithEarningDetail target = result.target();
        if (target == null) {
            log.warn("queryAnchorIncomeDetail[queryAnchorIncomeDetail] response is null  req userId={} tenantCode={},configCode={},startTime={},endTime={},rCode={}", userId, tenantCode, configCode, startTime, endTime, result.rCode());
            return null;
        }
        return target;
    }


    /**
     * 获取主播收益-支付
     */
    public CreatorDataQueryProto.ResponseQueryAnchorIncomeDetail queryAnchorIncomeDetail(int appId, long userId, DirectionEnum direction, List<IncomeType> incomeType, Date startDate, Date endDate, PaySettleConfigCodeEnum configCodeEnum, int pageNo, int pageSize, Long flushTime) {
        String tenantCode = PayTenantCodeEnum.getPayTenantCode(appId);
        String configCode = configCodeEnum.getConfigCode();
        String startTime = fm.lizhi.commons.util.DateUtil.formatDateToString(startDate, fm.lizhi.commons.util.DateUtil.date_2) + " 00:00:00";
        String endTime = fm.lizhi.commons.util.DateUtil.formatDateToString(endDate, fm.lizhi.commons.util.DateUtil.date_2) + " 23:59:59";

        log.info("queryAnchorIncomeDetail[queryAnchorIncomeDetail] req tenantCode={},configCode={},startTime={},endTime={},flushTime={}", tenantCode, configCode, startTime, endTime, flushTime);

        CreatorDataQueryProto.QueryAnchorIncomeDetailRequest.Builder builder = CreatorDataQueryProto.QueryAnchorIncomeDetailRequest.newBuilder()
                .setTenantCode(tenantCode)
                .setAnchorId(userId)
                .setConfigCode(configCode)
                .setTimeStart(startTime)
                .setTimeEnd(endTime)
                .setDirection(direction.getCode())
                .setPage(CreatorDataQueryProto.Page.newBuilder()
                        .setPageSize(pageSize)
                        .setPageNumber(pageNo)
                        .build());

        if (CollUtil.isNotEmpty(incomeType)) {
            builder.addAllBizId(incomeType.stream().map(IncomeType::getValue).map(String::valueOf).collect(Collectors.toList()));
        }

        Result<CreatorDataQueryProto.ResponseQueryAnchorIncomeDetail> result = creatorDataQueryService.queryAnchorIncomeDetail(builder.build(),
                fm.lizhi.commons.util.DateUtil.formatDateToString(new Date(flushTime), fm.lizhi.commons.util.DateUtil.NORMAL_DATE_FORMAT));

        if (RpcResult.isFail(result)) {
            log.warn("queryAnchorIncomeDetail[queryAnchorIncomeDetail] fail req tenantCode={},configCode={},startTime={},endTime={},rCode={}", tenantCode, configCode, startTime, endTime, result.rCode());
            return null;
        }
        CreatorDataQueryProto.ResponseQueryAnchorIncomeDetail target = result.target();
        if (target == null) {
            log.warn("queryAnchorIncomeDetail[queryAnchorIncomeDetail] response is null  req tenantCode={},configCode={},startTime={},endTime={},rCode={}", tenantCode, configCode, startTime, endTime, result.rCode());
        }
        // 具体出参在上层调用 打印了
        return target;
    }


    /**
     * 小陪伴+西米的 个人收益获取
     *
     * @param PlayerRevenueConfigCodeList
     * @param tenantCode
     * @param familyId
     * @param userId
     * @param periodTypeEnum
     * @return
     */
    public PlayerRevenueSumTimeBean getPlayerRevenueSumTimeBean(List<PaySettleConfigCodeEnum> PlayerRevenueConfigCodeList, String tenantCode, long familyId, long userId, PeriodTypeEnum periodTypeEnum) {
        CountDownLatchWrapper dayCountDownLatchWrapper = new CountDownLatchWrapper(executorService, PlayerRevenueConfigCodeList.size());
        Map<String, Long> dayBalanceMap = new ConcurrentHashMap<>();
        for (PaySettleConfigCodeEnum paySettleConfigCodeEnum : PlayerRevenueConfigCodeList) {
            dayCountDownLatchWrapper.submit(() -> {
                long balance = paymentManager.queryTradeStatisticsValue(familyId, userId, paySettleConfigCodeEnum, periodTypeEnum);
                dayBalanceMap.put(paySettleConfigCodeEnum.getConfigCode(), balance);
            });
        }
        dayCountDownLatchWrapper.await();
        // 今日
        PlayerRevenueSumTimeBean revenueSumTimeBean = new PlayerRevenueSumTimeBean();
        revenueSumTimeBean.setSumIncome(String.valueOf(dayBalanceMap.getOrDefault(PaySettleConfigCodeEnum.ANCHOR_EXAM_INCOME_TOTAL_AMOUNT.getConfigCode(), 0L)));
        revenueSumTimeBean.setSumRevenueIncome(String.valueOf(dayBalanceMap.getOrDefault(PaySettleConfigCodeEnum.ANCHOR_EXAM_PROFIT_TOTAL_AMOUNT.getConfigCode(), 0L)));
        revenueSumTimeBean.setOfficialHallIncome(new PlayerRevenueAndIncomeBean(
                String.valueOf(dayBalanceMap.getOrDefault(PaySettleConfigCodeEnum.ANCHOR_OFFICIAL_HALL_INCOME_TOTAL_AMOUNT.getConfigCode(), 0L)),
                String.valueOf(dayBalanceMap.getOrDefault(PaySettleConfigCodeEnum.ANCHOR_OFFICIAL_HALL_PROFIT_TOTAL_AMOUNT.getConfigCode(), 0L)))
        );
        revenueSumTimeBean.setHallIncome(new PlayerRevenueAndIncomeBean(
                String.valueOf(dayBalanceMap.getOrDefault(PaySettleConfigCodeEnum.ANCHOR_HALL_INCOME_TOTAL_AMOUNT.getConfigCode(), 0L)),
                String.valueOf(dayBalanceMap.getOrDefault(PaySettleConfigCodeEnum.ANCHOR_HALL_PROFIT_TOTAL_AMOUNT.getConfigCode(), 0L)))
        );
        revenueSumTimeBean.setPersonalIncome(new PlayerRevenueAndIncomeBean(
                String.valueOf(dayBalanceMap.getOrDefault(PaySettleConfigCodeEnum.PERSONAL_ANCHOR_INDIVIDUAL_INCOME_TOTAL_AMOUNT.getConfigCode(), 0L)),
                String.valueOf(dayBalanceMap.getOrDefault(PaySettleConfigCodeEnum.ANCHOR_INDIVIDUAL_PROFIT_TOTAL_AMOUNT.getConfigCode(), 0L)))
        );
        return revenueSumTimeBean;
    }

    /**
     * 获取主播收益总值 - 支付
     *
     * @return 返回 收入 + 收益 两个值
     */
    public Pair<Integer, Integer> queryAnchorIncomeWithEarningDetailTotal(int appId, long userId, Date startDate, Date endDate, DirectionEnum direction, PaySettleConfigCodeEnum configCode, List<IncomeType> incomeType) {

        String startTime = fm.lizhi.commons.util.DateUtil.formatDateToString(startDate, fm.lizhi.commons.util.DateUtil.date_2) + " 00:00:00";
        String endTime = fm.lizhi.commons.util.DateUtil.formatDateToString(endDate, fm.lizhi.commons.util.DateUtil.date_2) + " 23:59:59";
        String tenantCode = PayTenantCodeEnum.getPayTenantCode(appId);

        log.info("queryAnchorIncomeWithEarningDetailTotal[queryAnchorIncomeWithEarningDetailTotal] req tenantCode={},ConfigCode={},AnchorId={},startTime={},endTime={},Direction={},incomeType={}"
                , tenantCode
                , configCode.getConfigCode()
                , userId
                , startTime
                , endTime
                , direction.getCode()
                , JSONObject.toJSONString(incomeType)
        );

        CreatorDataQueryProto.QueryAnchorIncomeWithEarningDetailRequest.Builder builder = CreatorDataQueryProto.QueryAnchorIncomeWithEarningDetailRequest.newBuilder()
                .setTenantCode(tenantCode)
                .setConfigCode(configCode.getConfigCode())
                .setAnchorId(userId)
                .setTimeStart(startTime)
                .setTimeEnd(endTime)
                .setDirection(direction.getCode())
                // 分页参数必传，汇总值不影响.
                .setPage(CreatorDataQueryProto.Page.newBuilder()
                        .setPageNumber(1)
                        .setPageSize(20)
                        .build());

        if (CollUtil.isNotEmpty(incomeType)) {
            builder.addAllBizId(incomeType.stream().map(IncomeType::getValue).map(String::valueOf).collect(Collectors.toList()));
        }

        Result<CreatorDataQueryProto.ResponseQueryAnchorIncomeWithEarningDetailTotal> result = creatorDataQueryService.queryAnchorIncomeWithEarningDetailTotal(builder.build(),
                fm.lizhi.commons.util.DateUtil.formatDateToString(new Date(), fm.lizhi.commons.util.DateUtil.NORMAL_DATE_FORMAT));

        if (RpcResult.isFail(result)) {
            log.warn("queryAnchorIncomeWithEarningDetailTotal[queryAnchorIncomeWithEarningDetailTotal] fail req tenantCode={},ConfigCode={},AnchorId={},startTime={},endTime={},Direction={},incomeType={},rCode={}"
                    , tenantCode
                    , configCode.getConfigCode()
                    , userId
                    , startTime
                    , endTime
                    , direction.getCode()
                    , JSONObject.toJSONString(incomeType)
                    , result.rCode()
            );
            return Pair.of(0, 0);
        }
        String statisticsValue = result.target().getStatisticsValue();
        String earningStatisticsValue = result.target().getEarningStatisticsValue();
        log.info("queryAnchorIncomeWithEarningDetailTotal[queryAnchorIncomeWithEarningDetailTotal] response tenantCode={},ConfigCode={},AnchorId={},startTime={},endTime={},Direction={},incomeType={},statisticsValue={},earningStatisticsValue={}"
                , tenantCode
                , configCode.getConfigCode()
                , userId
                , startTime
                , endTime
                , direction.getCode()
                , JSONObject.toJSONString(incomeType)
                , statisticsValue
                , earningStatisticsValue
        );

        return Pair.of(Integer.valueOf(statisticsValue), Integer.parseInt(earningStatisticsValue));
    }


    /**
     * 获取主播收益汇总值 - 支付
     */
    public Integer queryAnchorIncomeDetailTotal(int appId, long userId, Date startDate, Date endDate, DirectionEnum direction, PaySettleConfigCodeEnum configCode, List<IncomeType> incomeType) {

        String startTime = fm.lizhi.commons.util.DateUtil.formatDateToString(startDate, fm.lizhi.commons.util.DateUtil.date_2) + " 00:00:00";
        String endTime = fm.lizhi.commons.util.DateUtil.formatDateToString(endDate, fm.lizhi.commons.util.DateUtil.date_2) + " 23:59:59";
        String tenantCode = PayTenantCodeEnum.getPayTenantCode(appId);

        log.info("queryAnchorIncomeDetailTotal[queryAnchorIncomeDetailTotal] req tenantCode={},ConfigCode={},AnchorId={},startTime={},endTime={},Direction={},incomeType={}"
                , tenantCode
                , configCode.getConfigCode()
                , userId
                , startTime
                , endTime
                , direction.getCode()
                , JSONObject.toJSONString(incomeType)
        );

        CreatorDataQueryProto.QueryAnchorIncomeDetailRequest.Builder builder = CreatorDataQueryProto.QueryAnchorIncomeDetailRequest.newBuilder()
                .setTenantCode(tenantCode)
                .setConfigCode(configCode.getConfigCode())
                .setAnchorId(userId)
                .setTimeStart(startTime)
                .setTimeEnd(endTime)
                .setDirection(direction.getCode())
                // 分页参数必传，汇总值不影响.
                .setPage(CreatorDataQueryProto.Page.newBuilder()
                        .setPageNumber(1)
                        .setPageSize(20)
                        .build());

        if (CollUtil.isNotEmpty(incomeType)) {
            builder.addAllBizId(incomeType.stream().map(IncomeType::getValue).map(String::valueOf).collect(Collectors.toList()));
        }

        Result<CreatorDataQueryProto.ResponseQueryAnchorIncomeDetailTotal> result = creatorDataQueryService.queryAnchorIncomeDetailTotal(builder.build(),
                fm.lizhi.commons.util.DateUtil.formatDateToString(new Date(), fm.lizhi.commons.util.DateUtil.NORMAL_DATE_FORMAT));

        if (RpcResult.isFail(result)) {
            log.warn("queryAnchorIncomeDetailTotal[queryAnchorIncomeDetailTotal] fail req tenantCode={},ConfigCode={},AnchorId={},startTime={},endTime={},Direction={},incomeType={},rCode={}"
                    , tenantCode
                    , configCode.getConfigCode()
                    , userId
                    , startTime
                    , endTime
                    , direction.getCode()
                    , JSONObject.toJSONString(incomeType)
                    , result.rCode()
            );
            return 0;
        }
        String statisticsValue = result.target().getStatisticsValue();
        log.info("queryAnchorIncomeDetailTotal[queryAnchorIncomeDetailTotal] response tenantCode={},ConfigCode={},AnchorId={},startTime={},endTime={},Direction={},incomeType={},statisticsValue={}"
                , tenantCode
                , configCode.getConfigCode()
                , userId
                , startTime
                , endTime
                , direction.getCode()
                , JSONObject.toJSONString(incomeType)
                , statisticsValue
        );

        return Integer.valueOf(statisticsValue);
    }

}
