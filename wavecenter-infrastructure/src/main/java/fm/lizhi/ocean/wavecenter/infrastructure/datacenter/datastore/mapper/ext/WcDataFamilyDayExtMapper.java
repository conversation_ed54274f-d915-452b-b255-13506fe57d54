package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.ext;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.WcDataFamilyDayStatPo;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcDataFamilyDayExtMapper {

    /**
     * 分页查询公会日统计数据
     * @param familyId 公会ID
     * @param appId 业务ID
     * @param lastTime 上次查询的最小时间戳
     * @return 公会日统计数据列表
     */
    @Select("SELECT stat_date_value, stat_date, " +
            "personal_noble_income as playerVip, " +
            "official_hall_income as officialRoom, " +
            "personal_hall_income as player, " +
            "all_income as sum, " +
            "sign_hall_income as roomGift, " +
            "noble_income as roomVip " +
            "FROM wavecenter_data_family_day " +
            "WHERE family_id = #{familyId} AND app_id = #{appId} " +
            "AND stat_date_value < #{lastTime} " +
            " order by stat_date_value desc limit #{pageSize} ")
    List<WcDataFamilyDayStatPo> queryDayStatsByTime(@Param("familyId") Long familyId,
                                                    @Param("appId") Integer appId,
                                                    @Param("lastTime") Integer lastTime,
                                                    @Param("pageSize") Integer pageSize);
}
