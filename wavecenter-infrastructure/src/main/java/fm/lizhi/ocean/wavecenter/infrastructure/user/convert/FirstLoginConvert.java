package fm.lizhi.ocean.wavecenter.infrastructure.user.convert;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.datastore.user.entity.WcUserFirstLoginRecord;
import fm.lizhi.ocean.wavecenter.service.user.dto.FirstLoginRecordDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.FirstLoginRecordParamDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR,
        imports = {
                ConfigUtils.class
        }
)
public interface FirstLoginConvert {

    FirstLoginConvert  INSTANCE = Mappers.getMapper(FirstLoginConvert.class);

    /**
     * dto 转 entity
     *
     * @param dto dto
     * @return 结果
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "createTime", ignore = true)
    WcUserFirstLoginRecord toEntity(FirstLoginRecordParamDTO dto);

    /**
     * entity 转 dto
     *
     * @param entity entity
     * @return 结果
     */
    FirstLoginRecordDTO toDTO(WcUserFirstLoginRecord entity);

}
