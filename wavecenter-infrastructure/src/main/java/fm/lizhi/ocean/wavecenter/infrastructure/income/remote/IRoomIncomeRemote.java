package fm.lizhi.ocean.wavecenter.infrastructure.income.remote;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.GetRoomSignPlayerIncomeParamBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.RoomSignPlayerIncomeBean;
import fm.lizhi.ocean.wavecenter.base.remote.IRemote;

/**
 * <AUTHOR>
 * @date 2024/5/27 17:53
 */
public interface IRoomIncomeRemote extends IRemote {

    /**
     * 签约主播收入统计
     * @param paramBean
     * @return
     */
    PageBean<RoomSignPlayerIncomeBean> getRoomSignPlayerIncome(GetRoomSignPlayerIncomeParamBean paramBean);

}
