package fm.lizhi.ocean.wavecenter.infrastructure.live.manager;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.constants.DateType;
import fm.lizhi.ocean.wavecenter.api.live.bean.LiveSmsPlayerParamBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.LiveSmsRoomParamBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.PlayerSmsStatBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.RoomSmsStatBean;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.*;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.mapper.*;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.constants.MetricsConstants;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.constants.MetricsMeta;
import fm.lizhi.ocean.wavecenter.infrastructure.live.convert.LiveSmsInfraConvert;
import fm.lizhi.ocean.wavecenter.service.live.manager.LiveSmsManager;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/4/20 17:19
 */
@Component
public class LiveSmsManagerImpl implements LiveSmsManager {

    @Autowired
    private WcDataRoomFamilyDayMapper wcDataRoomFamilyDayMapper;
    @Autowired
    private WcDataRoomFamilyWeekMapper wcDataRoomFamilyWeekMapper;
    @Autowired
    private WcDataRoomFamilyMonthMapper wcDataRoomFamilyMonthMapper;
    @Autowired
    private WcDataPlayerRoomDayMapper wcDataPlayerRoomDayMapper;
    @Autowired
    private WcDataPlayerRoomWeekMapper wcDataPlayerRoomWeekMapper;
    @Autowired
    private WcDataPlayerRoomMonthMapper wcDataPlayerRoomMonthMapper;
    @Autowired
    private WcDataPlayerDayMapper wcDataPlayerDayMapper;
    @Autowired
    private WcDataPlayerWeekMapper wcDataPlayerWeekMapper;
    @Autowired
    private WcDataPlayerMonthMapper wcDataPlayerMonthMapper;

    @Override
    public PageBean<RoomSmsStatBean> roomList(long family, Long roomId, LiveSmsRoomParamBean paramBean) {
        Integer appId = paramBean.getAppId();
        DateType dateType = paramBean.getDateType();
        Date startDate = paramBean.getStartDate();
        Date endDate = paramBean.getEndDate();

        //排序条件
        String filedSqlName = "sign_player_cnt";
        Optional<MetricsMeta> metricsMeta = MetricsConstants.getMetricsMeta(paramBean.getOrderMetrics());
        if (metricsMeta.isPresent()) {
            filedSqlName = metricsMeta.get().getPoName();
        }
        String orderSql = filedSqlName + " " + paramBean.getOrderType().getValue() + ", id desc";

        List<RoomSmsStatBean> result = new ArrayList<>();
        int total = 0;
        if (dateType == DateType.DAY) {
            Integer startDateValue = Integer.valueOf(DateUtil.formatDateToString(startDate, DateUtil.date));
            WcDataRoomFamilyDayExample dayExample = new WcDataRoomFamilyDayExample();
            WcDataRoomFamilyDayExample.Criteria criteria = dayExample.createCriteria();
            criteria.andAppIdEqualTo(appId)
                    .andFamilyIdEqualTo(family)
                    .andStatDateValueEqualTo(startDateValue)
            ;
            if (roomId != null) {
                criteria.andRoomIdEqualTo(roomId);
            }
            if (CollectionUtils.isNotEmpty(paramBean.getRoomIds())) {
                criteria.andRoomIdIn(paramBean.getRoomIds());
            }
            if (paramBean.isFilterZero()) {
                //过滤掉为0的
                criteria.andChatUserCntGreaterThan(0);
            }
            //排序条件
            dayExample.setOrderByClause(orderSql);

            PageList<WcDataRoomFamilyDay> pageList = wcDataRoomFamilyDayMapper.pageByExample(dayExample, paramBean.getPageNo(), paramBean.getPageSize());
            result = LiveSmsInfraConvert.I.roomFamilyDayPos2SmsBeans(pageList);
            total = pageList.getTotal();
        }

        if (dateType == DateType.WEEK) {
            WcDataRoomFamilyWeekExample weekExample = new WcDataRoomFamilyWeekExample();
            WcDataRoomFamilyWeekExample.Criteria criteria = weekExample.createCriteria();
            criteria.andAppIdEqualTo(appId)
                    .andFamilyIdEqualTo(family)
                    .andStartWeekDateEqualTo(startDate)
                    .andEndWeekDateEqualTo(endDate)
            ;
            if (roomId != null) {
                criteria.andRoomIdEqualTo(roomId);
            }
            if (CollectionUtils.isNotEmpty(paramBean.getRoomIds())) {
                criteria.andRoomIdIn(paramBean.getRoomIds());
            }
            if (paramBean.isFilterZero()) {
                //过滤掉为0的
                criteria.andChatUserCntGreaterThan(0);
            }
            //排序条件
            weekExample.setOrderByClause(orderSql);

            PageList<WcDataRoomFamilyWeek> pageList = wcDataRoomFamilyWeekMapper.pageByExample(weekExample, paramBean.getPageNo(), paramBean.getPageSize());
            result = LiveSmsInfraConvert.I.roomFamilyWeekPos2SmsBeans(pageList);
            total = pageList.getTotal();
        }

        if (dateType == DateType.MONTH) {
            Integer statValue = Integer.valueOf(DateUtil.formatDateToString(startDate, "yyyyMM"));
            WcDataRoomFamilyMonthExample monthExample = new WcDataRoomFamilyMonthExample();
            WcDataRoomFamilyMonthExample.Criteria criteria = monthExample.createCriteria();
            criteria.andAppIdEqualTo(appId)
                    .andFamilyIdEqualTo(family)
                    .andStatMonthEqualTo(statValue)
            ;
            if (roomId != null) {
                criteria.andRoomIdEqualTo(roomId);
            }
            if (CollectionUtils.isNotEmpty(paramBean.getRoomIds())) {
                criteria.andRoomIdIn(paramBean.getRoomIds());
            }
            if (paramBean.isFilterZero()) {
                //过滤掉为0的
                criteria.andChatUserCntGreaterThan(0);
            }
            //排序条件
            monthExample.setOrderByClause(orderSql);

            PageList<WcDataRoomFamilyMonth> pageList = wcDataRoomFamilyMonthMapper.pageByExample(monthExample, paramBean.getPageNo(), paramBean.getPageSize());
            result = LiveSmsInfraConvert.I.roomFamilyMonthPos2SmsBeans(pageList);
            total = pageList.getTotal();
        }
        return PageBean.of(total, result);
    }

    @Override
    public PageBean<PlayerSmsStatBean> playerList(long family, long roomId, Long playerId, LiveSmsPlayerParamBean paramBean) {
        Integer appId = paramBean.getAppId();
        DateType dateType = paramBean.getDateType();
        Date startDate = paramBean.getStartDate();
        Date endDate = paramBean.getEndDate();

        //排序条件
        String filedSqlName = "chat_user_cnt";
        Optional<MetricsMeta> metricsMeta = MetricsConstants.getMetricsMeta(paramBean.getOrderMetrics());
        if (metricsMeta.isPresent()) {
            filedSqlName = metricsMeta.get().getPoName();
        }
        String orderSql = filedSqlName + " " + paramBean.getOrderType().getValue();

        List<PlayerSmsStatBean> result = new ArrayList<>();
        int total = 0;
        if (dateType == DateType.DAY) {
            Integer startDateValue = Integer.valueOf(DateUtil.formatDateToString(startDate, DateUtil.date));
            WcDataPlayerRoomDayExample dayExample = new WcDataPlayerRoomDayExample();
            WcDataPlayerRoomDayExample.Criteria criteria = dayExample.createCriteria();
            criteria.andAppIdEqualTo(appId)
                    .andFamilyIdEqualTo(family)
                    .andRoomIdEqualTo(roomId)
                    .andStatDateValueEqualTo(startDateValue)
            ;
            if (playerId != null) {
                criteria.andPlayerIdEqualTo(playerId);
            }
            if (paramBean.isFilterZero()) {
                //过滤掉为0的
                criteria.andChatUserCntGreaterThan(0);
            }
            //排序条件
            dayExample.setOrderByClause(orderSql);

            PageList<WcDataPlayerRoomDay> pageList = wcDataPlayerRoomDayMapper.pageByExample(dayExample, paramBean.getPageNo(), paramBean.getPageSize());
            result = LiveSmsInfraConvert.I.playerRoomDayPos2SmsBeans(pageList);
            total = pageList.getTotal();
        }

        if (dateType == DateType.WEEK) {
            WcDataPlayerRoomWeekExample weekExample = new WcDataPlayerRoomWeekExample();
            WcDataPlayerRoomWeekExample.Criteria criteria = weekExample.createCriteria();
            criteria.andAppIdEqualTo(appId)
                    .andFamilyIdEqualTo(family)
                    .andRoomIdEqualTo(roomId)
                    .andStartWeekDateEqualTo(startDate)
                    .andEndWeekDateEqualTo(endDate)
            ;
            if (playerId != null) {
                criteria.andPlayerIdEqualTo(playerId);
            }
            if (paramBean.isFilterZero()) {
                //过滤掉为0的
                criteria.andChatUserCntGreaterThan(0);
            }
            //排序条件
            weekExample.setOrderByClause(orderSql);

            PageList<WcDataPlayerRoomWeek> pageList = wcDataPlayerRoomWeekMapper.pageByExample(weekExample, paramBean.getPageNo(), paramBean.getPageSize());
            result = LiveSmsInfraConvert.I.playerRoomWeekPos2SmsBeans(pageList);
            total = pageList.getTotal();
        }

        if (dateType == DateType.MONTH) {
            Integer statValue = Integer.valueOf(DateUtil.formatDateToString(startDate, "yyyyMM"));
            WcDataPlayerRoomMonthExample monthExample = new WcDataPlayerRoomMonthExample();
            WcDataPlayerRoomMonthExample.Criteria criteria = monthExample.createCriteria();
            criteria.andAppIdEqualTo(appId)
                    .andFamilyIdEqualTo(family)
                    .andRoomIdEqualTo(roomId)
                    .andStatMonthEqualTo(statValue)
            ;
            if (playerId != null) {
                criteria.andPlayerIdEqualTo(playerId);
            }
            if (paramBean.isFilterZero()) {
                //过滤掉为0的
                criteria.andChatUserCntGreaterThan(0);
            }
            //排序条件
            monthExample.setOrderByClause(orderSql);

            PageList<WcDataPlayerRoomMonth> pageList = wcDataPlayerRoomMonthMapper.pageByExample(monthExample, paramBean.getPageNo(), paramBean.getPageSize());
            result = LiveSmsInfraConvert.I.playerRoomMonthPos2SmsBeans(pageList);
            total = pageList.getTotal();
        }
        return PageBean.of(total, result);
    }

    @Override
    public PageBean<PlayerSmsStatBean> playerListForPlayer(long playerId, LiveSmsPlayerParamBean paramBean) {
        Integer appId = paramBean.getAppId();
        DateType dateType = paramBean.getDateType();
        Date startDate = paramBean.getStartDate();
        Date endDate = paramBean.getEndDate();

        //排序条件
        String filedSqlName = "chat_user_cnt";
        Optional<MetricsMeta> metricsMeta = MetricsConstants.getMetricsMeta(paramBean.getOrderMetrics());
        if (metricsMeta.isPresent()) {
            filedSqlName = metricsMeta.get().getPoName();
        }
        String orderSql = filedSqlName + " " + paramBean.getOrderType().getValue();

        List<PlayerSmsStatBean> result = new ArrayList<>();
        int total = 0;
        if (dateType == DateType.DAY) {
            Integer startDateValue = Integer.valueOf(DateUtil.formatDateToString(startDate, DateUtil.date));
            WcDataPlayerDayExample dayExample = new WcDataPlayerDayExample();
            WcDataPlayerDayExample.Criteria criteria = dayExample.createCriteria();
            criteria.andAppIdEqualTo(appId)
                    .andPlayerIdEqualTo(playerId)
                    .andStatDateValueEqualTo(startDateValue)
            ;
            if (paramBean.isFilterZero()) {
                //过滤掉为0的
                criteria.andChatUserCntGreaterThan(0);
            }
            //排序条件
            dayExample.setOrderByClause(orderSql);

            PageList<WcDataPlayerDay> pageList = wcDataPlayerDayMapper.pageByExample(dayExample, paramBean.getPageNo(), paramBean.getPageSize());
            result = LiveSmsInfraConvert.I.playerDayPos2SmsBeans(pageList);
            total = pageList.getTotal();
        }

        if (dateType == DateType.WEEK) {
            WcDataPlayerWeekExample weekExample = new WcDataPlayerWeekExample();
            WcDataPlayerWeekExample.Criteria criteria = weekExample.createCriteria();
            criteria.andAppIdEqualTo(appId)
                    .andPlayerIdEqualTo(playerId)
                    .andStartWeekDateEqualTo(startDate)
                    .andEndWeekDateEqualTo(endDate)
            ;
            if (paramBean.isFilterZero()) {
                //过滤掉为0的
                criteria.andChatUserCntGreaterThan(0);
            }
            //排序条件
            weekExample.setOrderByClause(orderSql);

            PageList<WcDataPlayerWeek> pageList = wcDataPlayerWeekMapper.pageByExample(weekExample, paramBean.getPageNo(), paramBean.getPageSize());
            result = LiveSmsInfraConvert.I.playerWeekPos2SmsBeans(pageList);
            total = pageList.getTotal();
        }

        if (dateType == DateType.MONTH) {
            Integer statValue = Integer.valueOf(DateUtil.formatDateToString(startDate, "yyyyMM"));
            WcDataPlayerMonthExample monthExample = new WcDataPlayerMonthExample();
            WcDataPlayerMonthExample.Criteria criteria = monthExample.createCriteria();
            criteria.andAppIdEqualTo(appId)
                    .andPlayerIdEqualTo(playerId)
                    .andStatMonthEqualTo(statValue)
            ;
            if (paramBean.isFilterZero()) {
                //过滤掉为0的
                criteria.andChatUserCntGreaterThan(0);
            }
            //排序条件
            monthExample.setOrderByClause(orderSql);

            PageList<WcDataPlayerMonth> pageList = wcDataPlayerMonthMapper.pageByExample(monthExample, paramBean.getPageNo(), paramBean.getPageSize());
            result = LiveSmsInfraConvert.I.playerMonthPos2SmsBeans(pageList);
            total = pageList.getTotal();
        }

        return PageBean.of(total, result);
    }
}
