package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.manager;

import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.convert.ActivityAiResultRateConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.dao.ActivityAiResultRateDao;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityAiResultRate;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityAiResultRateDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityAiResultRateManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * AI结果评分 Manager 实现类
 */
@Slf4j
@Component
public class ActivityAiResultRateManagerImpl implements ActivityAiResultRateManager {

    @Resource
    private ActivityAiResultRateDao activityAiResultRateDao;

    @Override
    public boolean saveActivityAiResultRate(ActivityAiResultRateDTO dto) {
        log.info("保存AI结果评分，参数：{}", dto);
        ActivityAiResultRate entity = ActivityAiResultRateConvert.INSTANCE.toEntity(dto);
        return activityAiResultRateDao.saveActivityAiResultRate(entity);
    }
} 