package fm.lizhi.ocean.wavecenter.infrastructure.user.remote.pp;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.CacheStats;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.lamp.common.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.user.convert.UserConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.IUserRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.req.GetUserVerifyDataReq;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.req.GetUserWithdrawStatusReq;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.res.GetUserVerifyDataRes;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.res.GetUserWithdrawStatusRes;
import fm.lizhi.ocean.wavecenter.service.user.config.UserConfig;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserInfoDto;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserMediaDto;
import fm.lizhi.pp.security.api.ban.PpUserBanService;
import fm.lizhi.pp.security.api.sms.H5TokenService;
import fm.lizhi.pp.security.constant.ban.BanStatus;
import fm.lizhi.pp.security.protocol.ban.PpUserBanProto;
import fm.lizhi.pp.security.protocol.sms.H5TokenProto;
import fm.lizhi.pp.user.account.user.api.PpNewUserService;
import fm.lizhi.pp.user.account.user.api.PpWithdrawUserService;
import fm.lizhi.pp.user.account.user.protocol.PpUserBaseProto;
import fm.lizhi.pp.user.account.user.protocol.PpUserProto;
import fm.lizhi.pp.user.account.user.protocol.PpUserRequestProto;
import fm.lizhi.pp.user.account.verify.api.UserVerifyProxyService;
import fm.lizhi.pp.user.account.verify.protocol.PpUserVerifyProxyProto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/10 15:07
 */
@Slf4j
@Component
public class PpUserRemote implements IUserRemote {

    @Autowired
    private PpNewUserService ppNewUserService;
    @Autowired
    private UserConfig userConfig;
    @Autowired
    private CommonConfig commonConfig;
    @Autowired
    private UserVerifyProxyService userVerifyProxyService;
    @Autowired
    private PpUserBanService ppUserBanService;
    @Autowired
    private H5TokenService h5TokenService;
    @Autowired
    private PpWithdrawUserService ppWithdrawUserService;

    /**
     * 用户信息缓存
     */
    private final LoadingCache<Long, Optional<SimpleUserDto>> SIMPLE_USER_CACHE = CacheBuilder.newBuilder()
            .maximumSize(20000)
            .expireAfterAccess(10, TimeUnit.MINUTES)
            .recordStats()
            .build(new CacheLoader<Long, Optional<SimpleUserDto>>() {
                @Override
                public Optional<SimpleUserDto> load(Long userId) throws Exception {
                    // @what：直接调批量接口查询 @why：PP服务的单个查询接口会查询标签信息，这里不需要
                    Map<Long, Optional<SimpleUserDto>> userMap = remoteLoadSimpleUserMap(Lists.newArrayList(userId));
                    if (userMap.get(userId) == null) {
                        return Optional.empty();
                    }
                    return userMap.get(userId);
                }

                @Override
                public Map<Long, Optional<SimpleUserDto>> loadAll(Iterable<? extends Long> userIds) throws Exception {
                    //批量查询，调用getAll方法时，除去缓存中存在的，剩下的调用此方法批量查询，例如：[1,2,3,4]，缓存中存在2,3，那么调用此方法查询1,4
                    return remoteLoadSimpleUserMap(Lists.newArrayList(userIds));
                }
            });

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public List<SimpleUserDto> getSimpleUserByIds(List<Long> ids) {
        try {
            ImmutableMap<Long, Optional<SimpleUserDto>> userCacheMap = SIMPLE_USER_CACHE.getAll(ids);

            if (userConfig.getPp().isPrintCacheStatLogSwitch()) {
                CacheStats stats = SIMPLE_USER_CACHE.stats();
                long size = SIMPLE_USER_CACHE.size();
                log.info("pp getSimpleUserByIds cache size={}, stats:{}", size, stats.toString());
            }

            List<SimpleUserDto> simpleUserList = new ArrayList<>(ids.size());
            for (Optional<SimpleUserDto> userOptional : userCacheMap.values()) {
                userOptional.ifPresent(simpleUserList::add);
            }
            return simpleUserList;
        } catch (Exception e) {
            log.warn("pp,getSimpleUserByIds exception:", e);
            return Collections.emptyList();
        }
    }

    @Override
    public Optional<GetUserVerifyDataRes> getUserVerifyDataResult(GetUserVerifyDataReq param) {
        Result<PpUserVerifyProxyProto.ResponseGetUserVerifyResult> result = userVerifyProxyService.getUserVerifyResult(PpUserVerifyProxyProto.GetUserVerifyResultParams.newBuilder()
                .setAppId(param.getAppId())
                .setUserId(param.getUserId())
                .build());
        if (RpcResult.isFail(result)) {
            return Optional.empty();
        }
        return Optional.ofNullable(UserConvert.I.verifyPb2Res(result.target().getResult()));
    }

    @Override
    public boolean getUserBanStatus(Long userId) {
        Result<PpUserBanProto.ResponseGetUserBanStatus> result = ppUserBanService.getUserBanStatus(userId);
        if (RpcResult.isFail(result)) {
            log.warn("pp getUserBanStatus fail userId={},rCode={}", userId, result.rCode());
            return true;
        }
        return !Objects.equals(result.target().getStatus(), BanStatus.NORMAL.getValue());
    }

    @Override
    public Optional<Long> getUserIdByBusinessToken(String businessToken) {
        Result<H5TokenProto.ResponseGetH5UserIdByToken> result = h5TokenService.getH5UserIdByToken(businessToken);
        if (RpcResult.isFail(result)) {
            log.warn("pp getUserIdByBusinessToken fail businessToken={},rCode={}", businessToken, result.rCode());
            return Optional.empty();
        }
        return Optional.of(result.target().getUserId());
    }

    private Map<Long, Optional<SimpleUserDto>> remoteLoadSimpleUserMap(List<Long> userIdList) {
        Map<Long, Optional<SimpleUserDto>> result = new HashMap<>();
        try {
            Result<PpUserProto.ResponseGetUsers> resp = ppNewUserService.getUsers(userIdList, false);
            if (RpcResult.isFail(resp)) {
                log.warn("pp,remoteLoadSimpleUserMap,rCode={},userIdList={}", resp.rCode(), JsonUtils.toJsonString(userIdList));
                return Collections.emptyMap();
            }

            List<PpUserBaseProto.User> userList = resp.target().getUserList();
            Map<Long, PpUserBaseProto.User> userMap = userList.stream().collect(Collectors.toMap(PpUserBaseProto.User::getId, v -> v, (k1, k2) -> k1));
            for (Long userId : userIdList) {
                PpUserBaseProto.User user = userMap.get(userId);
                if (user == null) {
                    LogContext.addReqLog("userId={} not found", userId);
                    result.put(userId, Optional.empty());
                    continue;
                }

                SimpleUserDto simpleUserDto = UserConvert.I.userPb2SimpleDto(user);
                simpleUserDto.setAvatar(UrlUtils.getAvatarUrl(commonConfig.getPp().getCdnHost(), user.getPortrait()));
                result.put(userId, Optional.of(simpleUserDto));
            }
        } catch (Exception e) {
            log.warn("pp,remoteLoadSimpleUserMap,exception", e);
        }
        return result;
    }


    @Override
    public Optional<SimpleUserDto> getUserInfoByBand(String band) {

        Result<PpUserProto.ResponseGetUser> result = ppNewUserService.getUser(PpUserRequestProto.GetUserRequest.newBuilder().setBand(band).build());
        // 处理出参
        PpUserProto.ResponseGetUser target = result.target();
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS || Objects.isNull(target)) {
            //  处理操作失败或 target 为 null 的情况
            return Optional.empty();
        }
        return Optional.of(convertToSimple(target.getUser()));
    }

    @Override
    public List<UserInfoDto> getUserInfoByIds(List<Long> userIds) {
        Result<PpUserProto.ResponseGetUsers> result = ppNewUserService.getUsers(userIds, false);
        if (RpcResult.isFail(result)) {
            log.warn("pp,getUserInfoByIds,rCode={},userIds={}", result.rCode(), JsonUtil.dumps(userIds));
            return Collections.emptyList();
        }

        List<PpUserBaseProto.User> userList = result.target().getUserList();
        List<UserInfoDto> list = new ArrayList<>();
        for (PpUserBaseProto.User user : userList) {
            list.add(convertToUserInfoDto(user));
        }
        return list;
    }

    @Override
    public Optional<UserInfoDto> getUserInfoById(long userId) {
        return Optional.empty();
    }

    @Override
    public Optional<UserMediaDto> getUserMediaById(long userId) {
        return Optional.empty();
    }

    @Override
    public int getUserBandStatus(Long userId) {
        return 0;
    }

    private SimpleUserDto convertToSimple(PpUserBaseProto.User user) {
        long userId = user.getId();
        SimpleUserDto simpleUserDto = new SimpleUserDto();
        simpleUserDto.setId(userId);
        simpleUserDto.setBand(user.getBand());
        simpleUserDto.setName(user.getName());
        simpleUserDto.setAvatar(UrlUtils.getAvatarUrl(commonConfig.getPp().getCdnHost(), user.getPortrait()));
        return simpleUserDto;
    }

    private UserInfoDto convertToUserInfoDto(PpUserBaseProto.User user) {
        UserInfoDto userInfoDto = new UserInfoDto();
        userInfoDto.setId(user.getId());
        userInfoDto.setName(user.getName());
        userInfoDto.setBand(user.getBand());
        userInfoDto.setPhoneNum(user.getPhoneNum());
        return userInfoDto;
    }

    @Override
    public boolean finishPlayerCenterAuth(Long userId) {
        return false;
    }

    @Override
    public Optional<SimpleUserDto> getUserByKeyWord(Long userId, String userName) {
        PpUserRequestProto.GetUserRequest request = PpUserRequestProto.GetUserRequest.newBuilder()
                .setUserId(userId)
                .setName(userName)
                .build();
        Result<PpUserProto.ResponseGetUser> result = ppNewUserService.getUser(request);
        if (RpcResult.isFail(result)) {
            log.error("pp getUserByKeyWord fail. userId={},userName={},rCode={}", userId, userName, result.rCode());
            return Optional.empty();
        }
        PpUserBaseProto.User user = result.target().getUser();
        return Optional.of(UserConvert.I.userPb2SimpleDto(user));
    }

    @Override
    public Optional<GetUserWithdrawStatusRes> getUserWithdrawStatus(GetUserWithdrawStatusReq param) {
        PpUserRequestProto.CheckUserWithdrawStatusRequest request = PpUserRequestProto.CheckUserWithdrawStatusRequest.newBuilder().setUserId(param.getUserId()).build();
        Result<PpUserProto.ResponseCheckUserWithdrawStatus> result = ppWithdrawUserService.checkUserWithdrawStatus(request);
        if (RpcResult.isFail(result)) {
            return Optional.empty();
        }
        GetUserWithdrawStatusRes withdrawStatus = GetUserWithdrawStatusRes.builder()
                .userId(result.target().getUserId())
                .note(result.target().getNote())
                .withdraw(result.target().getIsWithdraw())
                .build();
        return Optional.ofNullable(withdrawStatus);
    }
}
