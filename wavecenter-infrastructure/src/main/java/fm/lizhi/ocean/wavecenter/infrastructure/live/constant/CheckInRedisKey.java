package fm.lizhi.ocean.wavecenter.infrastructure.live.constant;

import fm.lizhi.ocean.wavecenter.base.constants.IRedisKey;

/**
 * 打卡缓存key
 */
public enum CheckInRedisKey implements IRedisKey {

    /**
     * 近15天有收入的厅
     * WC_CHECK_IN_REPORT_HAS_INCOME_NJ_ID_DATA_[appId]_[start]_[end]
     * value=厅主id列表json
     */
    REPORT_HAS_INCOME_NJ_ID_DATA,

    /**
     * 打卡报告数据
     * WC_CHECK_IN_REPORT_STAT_DATA_[appId]_[dataType]_[njId]_[yyyy_MM_dd_HH]
     * value=json
     */
    REPORT_STAT_DATA,

    /**
     * 打卡报告数据-锁
     * WC_CHECK_IN_REPORT_STAT_DATA_LOCK_[appId]_[dataType]_[roomId]_[yyyyMMddHH]
     *
     */
    REPORT_STAT_DATA_LOCK,


    /**
     * 某个小时内 已经通知的厅数量
     * WC_CHECK_IN_REPORT_HAS_REPORT_NJ_ID_COUNT_[appId]_[yyMMddHH]
     * value=通知数量
     */
    REPORT_HAS_REPORT_NJ_ID_COUNT,
    ;

    @Override
    public String getPrefix() {
        return "WC_CHECK_IN";
    }

    @Override
    public String getName() {
        return this.name();
    }
}
