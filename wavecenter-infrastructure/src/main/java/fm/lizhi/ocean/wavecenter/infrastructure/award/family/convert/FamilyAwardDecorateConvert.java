package fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import fm.lizhi.ocean.wavecenter.base.convert.CommonConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager.deliver.DeliverResourceParam;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.request.RequestSendDecorate;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.Date;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR,
        uses = {
                CommonConvert.class,
        },
        imports = {
                ConfigUtils.class,
                Date.class,
                PlatformDecorateTypeEnum.class,
        }
)
public interface FamilyAwardDecorateConvert {

    FamilyAwardDecorateConvert I = Mappers.getMapper(FamilyAwardDecorateConvert.class);

    @Mapping(target = "decorateType", expression = "java(PlatformDecorateTypeEnum.VEHICLE)")
    @Mapping(target = "decorateId", source = "resourceId")
    @Mapping(target = "decorateNumber", source = "resourceNumber")
    @Mapping(target = "decorateExpireTime", source = "resourceValidPeriod", qualifiedByName = "daysToMinutes")
    @Mapping(target = "userId", source = "familyUserId")
    RequestSendDecorate toRequestSendVehicle(DeliverResourceParam param);

    @Mapping(target = "decorateType", expression = "java(PlatformDecorateTypeEnum.MEDAL)")
    @Mapping(target = "decorateId", source = "resourceId")
    @Mapping(target = "decorateNumber", source = "resourceNumber")
    @Mapping(target = "decorateExpireTime", source = "resourceValidPeriod", qualifiedByName = "daysToMinutes")
    @Mapping(target = "userId", source = "familyUserId")
    RequestSendDecorate toRequestSendMedal(DeliverResourceParam param);

    @Named("daysToMinutes")
    default Integer daysToMinutes(Integer days) {
        return days != null ? days * 24 * 60 : 0;
    }
}
