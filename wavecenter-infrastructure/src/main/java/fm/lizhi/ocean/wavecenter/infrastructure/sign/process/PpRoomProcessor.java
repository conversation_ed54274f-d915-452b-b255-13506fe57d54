package fm.lizhi.ocean.wavecenter.infrastructure.sign.process;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataPlayerRoomWeek;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.WcDataRoomFamilyWeekIncomePo;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class PpRoomProcessor implements RoomProcessor {
    @Override
    public Map<Long, Long> playerWeekIncomeToMap(List<WcDataPlayerRoomWeek> incomeList) {
        // 转成map
        return incomeList.stream().collect(Collectors.toMap(
                WcDataPlayerRoomWeek::getPlayerId,
                w -> w.getCharm().longValue(),
                (existing, replacement) -> existing));
    }

    @Override
    public Map<Long, Long> roomsWeekIncomeToMap(List<WcDataRoomFamilyWeekIncomePo> incomeList) {
        return incomeList.stream().collect(Collectors.toMap(
                WcDataRoomFamilyWeekIncomePo::getRoomId,
                po -> po.getCharm() != null ? po.getCharm() : 0,
                (existing, replacement) -> existing
        ));
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.PP;
    }
}
