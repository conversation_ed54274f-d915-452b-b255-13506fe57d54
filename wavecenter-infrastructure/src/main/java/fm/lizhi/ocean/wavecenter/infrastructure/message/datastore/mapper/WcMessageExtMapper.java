package fm.lizhi.ocean.wavecenter.infrastructure.message.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.datastore.platform.message.entity.WcMessage;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcMessageExtMapper {
    /**
     * 查询消息列表
     */
    @Select({
            "<script>",
            "SELECT *",
            "FROM wavecenter_message wm",
            "WHERE ",
            "wm.app_id = #{appId}",
            "  AND wm.deleted = 0",
            "<if test='null != type'>",
            "  AND wm.type = #{type}",
            "</if>",
            "<if test='null != performanceId and performanceId > 0'>",
            "  AND unix_timestamp(wm.create_time) &lt; #{performanceId}",
            "</if>",
            "  AND (wm.visible_role_code = #{roleCode} OR wm.target_user_id = #{targetUserId})",
            "order by create_time desc",
            "LIMIT #{limit}"
            ,"</script>"
    })
    List<WcMessage> selectMessage(@Param("type") Integer type, @Param("appId") Integer appId,
                                  @Param("performanceId") Long performanceId, @Param("roleCode") String roleCode,
                                  @Param("targetUserId") Long targetUserId, @Param("limit") Integer limit);

    /**
     * 查询未读消息数
     */
    @Select({
            "<script>",
            "SELECT count(1)",
            "FROM wavecenter_message wm",
            "WHERE wm.app_id = #{appId}",
            "  AND wm.deleted = 0",
            "<if test='null != type'>",
            "  AND wm.type = #{type}",
            "</if>",
            "<if test='null != showNoticeTimeStamp'>",
            "  AND unix_timestamp(wm.create_time) &gt;= #{showNoticeTimeStamp}",
            "</if>",
            "  AND (wm.visible_role_code = #{roleCode} OR wm.target_user_id = #{targetUserId})",
            "  AND NOT EXISTS (",
            "    SELECT 1",
            "    FROM wavecenter_message_read_record wmrr",
            "    WHERE wmrr.message_id = wm.id and wmrr.user_id = #{targetUserId}",
            "  )"
            ,"</script>"
    })
    Long countMessageByNotRead(@Param("type") Integer type,  @Param("appId") int appId,
                               @Param("showNoticeTimeStamp") Long showNoticeTimeStamp,
                               @Param("roleCode") String roleCode,
                               @Param("targetUserId") Long targetUserId);

    /**
     * 查询未读消息ID列表
     */
    @Select({
            "<script>",
            "SELECT wm.id",
            "FROM wavecenter_message wm",
            "WHERE wm.app_id = #{appId}",
            "  AND wm.deleted = 0",
            "<if test='null != type'>",
            "  AND wm.type = #{type}",
            "</if>",
            "  AND (wm.visible_role_code = #{roleCode} OR wm.target_user_id = #{targetUserId})",
            "  AND NOT EXISTS (",
            "    SELECT 1",
            "    FROM wavecenter_message_read_record wmrr",
            "    WHERE wmrr.message_id = wm.id and wmrr.user_id = #{targetUserId}",
            "  )"
            ,"</script>"
    })
    List<Long> getMessageIdByNotRead(@Param("type") Integer type,  @Param("appId") int appId,
                                     @Param("roleCode") String roleCode,
                               @Param("targetUserId") Long targetUserId);
}
