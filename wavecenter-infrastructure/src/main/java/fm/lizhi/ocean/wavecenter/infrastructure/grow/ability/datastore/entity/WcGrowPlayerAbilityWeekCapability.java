package fm.lizhi.ocean.wavecenter.infrastructure.grow.ability.datastore.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 成长体系主播周结算能力细分
 *
 * @date 2025-06-11 02:51:35
 */
@Table(name = "`wavecenter_grow_player_ability_week_capability`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcGrowPlayerAbilityWeekCapability {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 主播ID
     */
    @Column(name= "`player_id`")
    private Long playerId;

    /**
     * 厅ID,结算时签约的厅
     */
    @Column(name= "`room_id`")
    private Long roomId;

    /**
     * 家族ID，结算时签约的家族ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 签约时间
     */
    @Column(name= "`sign_date`")
    private Date signDate;

    /**
     * 跟当前签约的公会是否为首次签约 1=是,0=否
     */
    @Column(name= "`first_sign_in_family`")
    private Integer firstSignInFamily;

    /**
     * 能力项code
     */
    @Column(name= "`capability_code`")
    private String capabilityCode;

    /**
     * 能力分
     */
    @Column(name= "`ability_value`")
    private BigDecimal abilityValue;

    /**
     * 上周能力分
     */
    @Column(name= "`last_week_ability_value`")
    private BigDecimal lastWeekAbilityValue;

    /**
     * 跟上周能力分的对比情况
     */
    @Column(name= "`compare_week_value`")
    private BigDecimal compareWeekValue;

    /**
     * 周开始日期 格式 YYYY-MM-DD
     */
    @Column(name= "`start_week_date`")
    private Date startWeekDate;

    /**
     * 周结束日期 格式 YYYY-MM-DD
     */
    @Column(name= "`end_week_date`")
    private Date endWeekDate;

    /**
     * 业务 ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 服务部署环境, TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", playerId=").append(playerId);
        sb.append(", roomId=").append(roomId);
        sb.append(", familyId=").append(familyId);
        sb.append(", signDate=").append(signDate);
        sb.append(", firstSignInFamily=").append(firstSignInFamily);
        sb.append(", capabilityCode=").append(capabilityCode);
        sb.append(", abilityValue=").append(abilityValue);
        sb.append(", lastWeekAbilityValue=").append(lastWeekAbilityValue);
        sb.append(", compareWeekValue=").append(compareWeekValue);
        sb.append(", startWeekDate=").append(startWeekDate);
        sb.append(", endWeekDate=").append(endWeekDate);
        sb.append(", appId=").append(appId);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}