package fm.lizhi.ocean.wavecenter.infrastructure.grow.convert;

import fm.lizhi.ocean.wavecenter.api.grow.level.bean.FamilyLevelConfigAwardBean;
import fm.lizhi.ocean.wavecenter.api.grow.level.bean.FamilyLevelConfigBean;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.FamilyLevel;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.LogicDeleteConstants;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.datastore.entity.WcFamilyLevelAward;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.datastore.entity.WcFamilyLevelConfig;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/3/18 18:29
 */
@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        imports = {
                UrlUtils.class
        }
)
public abstract class FamilyLevelConfigConvert {

    @Autowired
    protected CommonConfig commonConfig;

    @Mapping(target = "levelIcon", expression = "java(UrlUtils.addHostOrEmpty(po.getLevelIcon(), commonConfig.getRomeFsDownloadCdn()))")
    @Mapping(target = "levelMedal", expression = "java(UrlUtils.addHostOrEmpty(po.getLevelMedal(), commonConfig.getRomeFsDownloadCdn()))")
    @Mapping(target = "deleted", source = "deleted", qualifiedByName = "logicDeleteIntegerToBoolean")
    public abstract FamilyLevelConfigBean po2Bean(WcFamilyLevelConfig po);

    @Named("logicDeleteIntegerToBoolean")
    protected Boolean logicDeleteIntegerToBoolean(Integer deleted) {
        if (deleted == null) {
            return null;
        }
        return Objects.equals(deleted, LogicDeleteConstants.DELETED);
    }

    public abstract List<FamilyLevelConfigBean> pos2Beans(List<WcFamilyLevelConfig> pos);

    @Mappings({
            @Mapping(source = "name", target = "levelName"),
            @Mapping(expression = "java(UrlUtils.removeHostOrEmpty(familyLevel.getFamilyLevelMedia().getIconUrl()))", target = "levelIcon"),
            @Mapping(expression = "java(UrlUtils.removeHostOrEmpty(familyLevel.getFamilyLevelMedia().getMedalUrl()))", target = "levelMedal"),
            @Mapping(source = "familyLevelMedia.themColor", target = "themColor"),
            @Mapping(source = "familyLevelMedia.backgroundColor", target = "backgroundColor"),
    })
    public abstract WcFamilyLevelConfig entity2Po(FamilyLevel familyLevel);

    public abstract FamilyLevelConfigAwardBean bean2AwardBean(FamilyLevelConfigBean bean, List<String> awardImgs);

    public List<FamilyLevelConfigAwardBean> beans2AwardBeans(List<FamilyLevelConfigBean> beans, Map<Long, List<WcFamilyLevelAward>> awardMap) {
        if (CollectionUtils.isEmpty(beans)) {
            return Collections.emptyList();
        }
        ArrayList<FamilyLevelConfigAwardBean> awardBeans = new ArrayList<>(beans.size());
        for (FamilyLevelConfigBean bean : beans) {
            Long levelId = bean.getId();
            ArrayList<String> awardImgs = new ArrayList<>();
            for (WcFamilyLevelAward levelAward : awardMap.getOrDefault(levelId, Collections.emptyList())) {
                String awardImg = UrlUtils.addHostOrEmpty(levelAward.getAwardImg(), commonConfig.getRomeFsDownloadCdn());
                awardImgs.add(awardImg);
            }
            FamilyLevelConfigAwardBean awardBean = bean2AwardBean(bean, awardImgs);
            awardBeans.add(awardBean);
        }
        return awardBeans;
    }
}
