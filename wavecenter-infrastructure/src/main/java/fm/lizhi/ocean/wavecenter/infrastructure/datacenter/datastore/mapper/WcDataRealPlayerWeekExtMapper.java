package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcDataRealPlayerWeekExtMapper {

    @Select({
            "<script>"
            , "select distinct player_id"
            , "from wavecenter_data_real_player_week"
            , "where player_id &gt; #{minPlayerId}"
            , "  and start_week_date = #{weekStartDay}"
            , "  and end_week_date = #{weekEndDay}"
            , "  and app_id = #{appId}"
            , "  and all_income &gt; 0"
            , "order by player_id limit #{pageSize}"
            , "</script>"
    })
    List<Long> getPlayerIdsWeekHasIncomeByMinPlayerId(
            @Param("weekStartDay") Date weekStartDay,
            @Param("weekEndDay") Date weekEndDay,
            @Param("minPlayerId") Long minPlayerId,
            @Param("pageSize") Integer pageSize,
            @Param("appId") Integer appId
    );


}
