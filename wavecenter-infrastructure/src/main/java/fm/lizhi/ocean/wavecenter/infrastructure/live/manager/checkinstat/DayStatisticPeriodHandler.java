package fm.lizhi.ocean.wavecenter.infrastructure.live.manager.checkinstat;

import fm.lizhi.ocean.wavecenter.api.live.constants.CheckInDateTypeEnum;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataPlayerDayRealTime;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.ext.WcDataPlayerDayRealTimeExtMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.live.convert.WaveCheckInDataConverter;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WaveCheckInDayMicRecord;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WaveCheckInUserTask;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 日统计时间段处理器实现
 */
@Component
public class DayStatisticPeriodHandler implements StatisticPeriodHandler {


    @Autowired
    private WcDataPlayerDayRealTimeExtMapper playerDayRealTimeExtMapper;
    @Autowired
    private WaveCheckInDataConverter converter;

    @Override
    public boolean supports(CheckInDateTypeEnum dateType) {
        return CheckInDateTypeEnum.DAY.equals(dateType);
    }

    @Override
    public List<StatisticPeriod> buildPresetStatisticPeriods(long startDate, long endDate) {
        // 日统计的每段为1小时
        List<StatisticPeriod> statisticPeriods = new ArrayList<>();
        long periodStart = startDate;
        while (periodStart <= endDate) {
            statisticPeriods.add(new StatisticPeriod(CheckInDateTypeEnum.DAY, periodStart));
            periodStart += 60 * 60 * 1000L;
        }
        return statisticPeriods;
    }

    @Override
    public StatisticPeriod buildNormalizedStatisticPeriod(long startTime) {
        // 日统计直接使用其开始时间表示时间段
        return new StatisticPeriod(CheckInDateTypeEnum.DAY, startTime);
    }

    @Override
    public String formatStatisticPeriod(StatisticPeriod period) {
        LocalDateTime time = LocalDateTime.ofInstant(Instant.ofEpochMilli(period.getStartTime()), ZoneId.systemDefault());
        int startHour = time.getHour();
        int endHour = startHour + 1;
        // 比如 1-2, 23-24
        return String.format("%d-%d", startHour, endHour);
    }

    @Override
    public Map<Long, DayMicCounter> buildDayMicCounterMap(List<WaveCheckInDayMicRecord> dayMicRecords) {
        if (CollectionUtils.isEmpty(dayMicRecords)) {
            return Collections.emptyMap();
        }
        //日维度，一个用户只会有一条数据
        return dayMicRecords.stream().collect(Collectors.toMap(
                WaveCheckInDayMicRecord::getUserId,
                v -> new DayMicCounter().setRewardAmountSum(v.getRewardAmountSum()),
                (k1, k2) -> k2
        ));
    }

    @Override
    public Map<Long, List<ChatStat>> buildPlayerChatStatMap(int appId, List<Long> playerIds, long startDate, long endDate) {
        if(CollectionUtils.isEmpty(playerIds)) {
            return Collections.emptyMap();
        }
        Integer start = MyDateUtil.getDateDayValue(new Date(startDate));
        Integer end = MyDateUtil.getDateDayValue(new Date(endDate));
        List<WcDataPlayerDayRealTime> playerDayRealTimes = playerDayRealTimeExtMapper.getWcDataPlayerDayRealTimes(appId, playerIds, start, end);
        return playerDayRealTimes.stream().collect(Collectors.groupingBy(WcDataPlayerDayRealTime::getPlayerId,
                Collectors.mapping(converter::toDayChatStat, Collectors.toList())));
    }

    @Override
    public List<WaveCheckInUserTask> buildWaveCheckInUserTaskList(List<Long> recordIds) {
        return Collections.emptyList();
    }

    @Override
    public Optional<SimpleUserDto> buildHostInfo(Map<Long, SimpleUserDto> simpleUserMap, List<Long> hostIds) {
        return Optional.empty();
    }
}
