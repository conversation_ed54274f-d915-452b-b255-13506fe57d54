package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityTemplateStatusTask;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityTemplateStatusTaskDTO;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/8 18:08
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActivityTemplateStatusTaskConvert {

    ActivityTemplateStatusTaskConvert I = Mappers.getMapper(ActivityTemplateStatusTaskConvert.class);

    ActivityTemplateStatusTaskDTO entity2DTO(ActivityTemplateStatusTask entity);

    List<ActivityTemplateStatusTaskDTO> entitys2DTOs(List<ActivityTemplateStatusTask> entitys);

}
