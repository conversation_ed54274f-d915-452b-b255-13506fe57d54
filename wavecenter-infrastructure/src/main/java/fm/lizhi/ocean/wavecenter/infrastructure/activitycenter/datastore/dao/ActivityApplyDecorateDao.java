package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.dao;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityApplyDecorate;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityApplyDecorateExample;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.mapper.ActivityApplyDecorateMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/15 16:46
 */
@Slf4j
@Repository
public class ActivityApplyDecorateDao {

    @Autowired
    private ActivityApplyDecorateMapper activityApplyDecorateMapper;

    /**
     * 保存活动装饰信息
     * @param activityId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDecorates(Long activityId, List<ActivityApplyDecorate> decorates){
        if (activityId == null) {
            log.info("saveDecorates activityId is null.");
            return false;
        }

        String deployEnv = ConfigUtils.getEnvRequired().name();

        ActivityApplyDecorateExample deleteExample = new ActivityApplyDecorateExample();
        deleteExample.createCriteria()
                .andDeployEnvEqualTo(deployEnv)
                .andActivityIdEqualTo(activityId);
        long deleteRow = activityApplyDecorateMapper.deleteByExample(deleteExample);
        log.info("saveDecorates deleteRow={}", deleteRow);

        if (CollectionUtils.isEmpty(decorates)) {
            log.info("saveDecorates decorates is empty.");
            return true;
        }

        for (ActivityApplyDecorate decorate : decorates) {
            decorate.setDeployEnv(deployEnv);
        }

        return activityApplyDecorateMapper.batchInsert(decorates) > 0;
    }

    /**
     * 查询活动的装扮
     * @param activityId
     * @return
     */
    public List<ActivityApplyDecorate> getDecorates(Long activityId){
        ActivityApplyDecorateExample example = new ActivityApplyDecorateExample();
        example.createCriteria()
                .andDeployEnvEqualTo(ConfigUtils.getEnv().name())
                .andDeletedEqualTo(0)
                .andActivityIdEqualTo(activityId);
        return activityApplyDecorateMapper.selectByExample(example);
    }


}
