package fm.lizhi.ocean.wavecenter.infrastructure.common.manager;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpUtil;
import com.google.common.net.MediaType;
import fm.lizhi.common.romefs.javasdk.config.RomeFsConfig;
import fm.lizhi.common.romefs.javasdk.exception.RomeFsException;
import fm.lizhi.common.romefs.javasdk.service.CustomMultipartUploadService;
import fm.lizhi.common.romefs.javasdk.service.PutObjectService;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.ExtensionContentTypeEnum;
import fm.lizhi.ocean.wavecenter.service.common.param.RomeFsPutParam;
import fm.lizhi.ocean.wavecenter.service.common.manager.RomeFsManager;
import fm.lizhi.ocean.wavecenter.service.common.param.RomeFsTransferParam;
import fm.lizhi.ocean.wavecenter.service.common.result.RomeFsPutResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.UncheckedIOException;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;
import java.util.TreeMap;

/**
 * 罗马文件系统管理器
 */
@Component
@Slf4j
public class RomeFsManagerImpl implements RomeFsManager {

    /**
     * 上传文件. 自动根据文件大小分片上传和重试, 在重试次数内会屏蔽异常, 超过重试次数会抛出异常. 参考
     * <a href="https://lizhi2021.feishu.cn/wiki/MB2rwxahqimokGky8RwceMhlnvf">罗马文件上传-快速开始(服务端)</a>
     *
     * @param param 上传参数
     * @return 上传结果
     * @see RomeFsException
     */
    @Override
    public RomeFsPutResult putObject(RomeFsPutParam param) {
        try {
            RomeFsConfig romeFsConfig = param.getRomeFsConfig();
            String accessModifier = param.getAccessModifier();
            Path localPath = param.getLocalPath();
            String fileName = resolveFileName(param.getFileName(), localPath);
            String contentType = resolveContentType(param.getContentType(), fileName);
            int retries = Math.max(param.getRetries(), 0);
            long fileSize = Files.size(localPath);
            int partBytes = param.getPartBytes();
            String filePath;
            if (fileSize <= partBytes) {
                filePath = singleUpload(romeFsConfig, accessModifier, contentType, fileName, localPath, retries);
            } else {
                filePath = multipartUpload(romeFsConfig, accessModifier, contentType, fileName, localPath, retries, partBytes);
            }
            String md5 = md5Hex(localPath);
            RomeFsPutResult romeFsPutResult = new RomeFsPutResult();
            romeFsPutResult.setFilePath(filePath);
            romeFsPutResult.setFileSize(fileSize);
            romeFsPutResult.setMd5(md5);
            log.info("Put object success, filePath={}, fileSize={}, md5={}", filePath, fileSize, md5);
            return romeFsPutResult;
        } catch (RomeFsException e) {
            throw e;
        } catch (IOException e) {
            throw new UncheckedIOException("RomeFsPutParam=" + JsonUtils.toJsonString(param), e);
        } catch (Exception e) {
            throw new IllegalStateException("RomeFsPutParam=" + JsonUtils.toJsonString(param), e);
        }
    }

    @Override
    public RomeFsPutResult uploadTransfer(RomeFsTransferParam param) {

        if (StringUtils.isBlank(param.getSourceUrl())){
            log.error("uploadTransfer is error. sourceUrl is null.");
            return null;
        }

        // 先下载
        File tmpFile;
        try {
            tmpFile = HttpUtil.downloadFileFromUrl(param.getSourceUrl(), "");
        }catch (Exception e){
            log.error("download file from url error. sourceUrl: {}, fileType:{} ", param.getSourceUrl(), param.getContentType(), e);
            return null;
        }

        // 再上传
        try {
            if (StringUtils.isBlank(param.getFileName())){
                param.setFileName(tmpFile.getName());
            }
            param.setLocalPath(tmpFile.toPath());
            RomeFsPutResult result = putObject(param);
            log.info("rome fs upload success, sourceUrl={}, targetUrl={}", param.getSourceUrl(), result != null ? result.getFilePath() : "");
            return result;
        }catch (Exception e){
            log.error("uploadTransfer is error. sourceUrl: {}, fileType:{} ", param.getSourceUrl(), param.getContentType(), e);
            return null;
        }finally {
            FileUtil.del(tmpFile);
        }
    }

    private String md5Hex(Path localPath) throws IOException {
        try (InputStream inputStream = Files.newInputStream(localPath)) {
            return DigestUtils.md5Hex(inputStream);
        }
    }

    private String resolveFileName(String fileName, Path localPath) {
        if (StringUtils.isNotBlank(fileName)) {
            return fileName;
        }
        return localPath.getFileName().toString();
    }

    private String resolveContentType(String contentType, String fileName) {
        if (StringUtils.isNotBlank(contentType)) {
            return contentType;
        }
        String extension = FilenameUtils.getExtension(fileName);
        return ExtensionContentTypeEnum.getContentType(extension);
    }

    /**
     * 单文件上传
     *
     * @param romeFsConfig   罗马文件系统配置
     * @param accessModifier 访问权限
     * @param contentType    文件类型
     * @param fileName       文件名
     * @param localPath      本地文件路径
     * @param retries        重试次数
     * @return 上传后的文件路径
     * @throws Exception 上传异常
     */
    private String singleUpload(RomeFsConfig romeFsConfig, String accessModifier, String contentType,
                                String fileName, Path localPath, int retries) throws Exception {
        PutObjectService putObjectService = new PutObjectService(romeFsConfig);
        for (int retryCount = 0; retryCount <= retries; retryCount++) {
            try {
                String filePath = putObjectService.putObject(accessModifier, contentType, fileName, localPath.toFile());
                log.info("Single upload success, filePath={}", filePath);
                return filePath;
            } catch (Exception e) {
                if (retryCount < retries) {
                    log.debug("Single upload has an exception", e);
                    log.info("Single upload has an exception, fileName={}, retryCount={}", fileName, retryCount);
                } else {
                    throw e;
                }
            }
        }
        // 正常情况下不会执行到这里
        throw new IllegalStateException("Single upload have failed, fileName=" + fileName);
    }

    /**
     * 分片上传
     *
     * @param romeFsConfig   罗马文件系统配置
     * @param accessModifier 访问权限
     * @param contentType    文件类型
     * @param fileName       文件名
     * @param localPath      本地文件路径
     * @param retries        重试次数
     * @param partBytes      分片大小
     * @return 上传后的文件路径
     * @throws Exception 上传异常
     */
    private String multipartUpload(RomeFsConfig romeFsConfig, String accessModifier, String contentType,
                                   String fileName, Path localPath, int retries, int partBytes) throws Exception {
        CustomMultipartUploadService customMultipartUploadService = new CustomMultipartUploadService(romeFsConfig);
        CustomMultipartUploadService.MultipartUploadId multipartUploadId = customMultipartUploadService
                .createMultipartUpload(accessModifier, contentType, fileName);
        TreeMap<Integer, String> eTagMap = new TreeMap<>();
        try (FileChannel fileChannel = FileChannel.open(localPath, StandardOpenOption.READ)) {
            ByteBuffer buffer = ByteBuffer.allocate(partBytes);
            int partNumber = 0;
            byte[] fullPartBuffer = new byte[partBytes];
            while (true) {
                buffer.clear();
                partNumber++;
                int read = fileChannel.read(buffer);
                if (read == -1) {
                    break;
                }
                buffer.flip();
                byte[] data = buffer.remaining() == partBytes ? fullPartBuffer : new byte[buffer.remaining()];
                buffer.get(data);
                String eTag = partUpload(customMultipartUploadService, multipartUploadId, partNumber, data, retries);
                eTagMap.put(partNumber, eTag);
            }
            String filePath = customMultipartUploadService.complete(multipartUploadId, eTagMap);
            log.info("Multipart upload success, filePath={}", filePath);
            return filePath;
        } catch (Exception e) {
            try {
                customMultipartUploadService.abort(multipartUploadId);
            } catch (RuntimeException ex) {
                log.info("Abort multipart upload has an exception", ex);
            }
            throw e;
        }
    }

    private String partUpload(CustomMultipartUploadService customMultipartUploadService,
                              CustomMultipartUploadService.MultipartUploadId multipartUploadId,
                              int partNumber, byte[] data, int retries) throws Exception {
        String uploadId = multipartUploadId.getUploadId();
        for (int retryCount = 0; retryCount <= retries; retryCount++) {
            try {
                return customMultipartUploadService.uploadPart(multipartUploadId, partNumber, data);
            } catch (Exception e) {
                if (retryCount < retries) {
                    log.debug("Part upload has an exception", e);
                    log.info("Part upload has an exception, uploadId={}, partNumber={}, retryCount={}",
                            uploadId, partNumber, retryCount);
                } else {
                    throw e;
                }
            }
        }
        // 正常情况下不会执行到这里
        throw new IllegalStateException("Part upload have failed, uploadId=" + uploadId + ", partNumber=" + partNumber);
    }
}
