package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityLevelConfigBean;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityLevelConfig;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActivityLevelConfigConvert {
    ActivityLevelConfigConvert I = Mappers.getMapper(ActivityLevelConfigConvert.class);

    List<ActivityLevelConfigBean> convert2ActivityLevelConfigBeans(List<ActivityLevelConfig> list);

    List<ActivityLevelConfigBean> activityLevelConfig2ActivityLevelConfigBeans(List<ActivityLevelConfig> list);

    ActivityLevelConfigBean convert2ActivityLevelConfigBean(ActivityLevelConfig levelConfig);

    default Long dateToLong(Date date) {
        return date.getTime();
    }

}
