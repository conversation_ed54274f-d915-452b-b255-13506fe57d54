package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataRoomFamilyMonth;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.WcDataRoomMonthStatPo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcDataRoomFamilyMonthExtMapper {

    @Select({
            "<script>"
            , "select "
            , "<foreach collection='metrics' item='m' separator=','>"
            , "sum(${m}) as ${m}"
            , "</foreach>"
            , "from wavecenter_data_room_family_month where app_id=#{appId}"
            , "and family_id=#{familyId}"
            , "and room_id in "
            , "<foreach collection='roomIds' item='roomId' open='(' close=')' separator=','>"
            , "#{roomId}"
            , "</foreach>"
            , "and stat_month=#{statMonth}"
            , "group by stat_month"
            , "</script>"
    })
    List<WcDataRoomFamilyMonth> sum(@Param("metrics")List<String> metrics
            , @Param("appId") Integer appId
            , @Param("familyId") Long familyId
            , @Param("roomIds") List<Long> roomIds
            , @Param("statMonth") Integer statMonth
    );

    /**
     * 查询厅月统计数据
     *
     * @param njId       厅ID
     * @param appId      业务ID
     * @param familyId   公会ID
     * @param startMonth 开始月份
     * @param pageSize   每页条数
     * @return 厅月统计数据列表
     */
    @Select("SELECT " +
            "    stat_month, " +
            "    room_id, " +
            "    personal_noble_income AS playerVip, " +
            "    official_hall_income AS officialRoom, " +
            "    personal_hall_income as player, " +
            "    all_income AS sum, " +
            "    sign_hall_income AS roomGift, " +
            "    noble_income AS roomVip " +
            "FROM wavecenter_data_room_month " +
            "WHERE room_id = #{njId} " +
            "    AND app_id = #{appId} " +
            "    AND stat_month < #{startMonth} " +
            " order by stat_month desc limit #{pageSize}")
    List<WcDataRoomMonthStatPo> queryMonthStatsByTime(@Param("njId") Long njId,
                                                      @Param("appId") Integer appId,
                                                      @Param("familyId") Long familyId,
                                                      @Param("startMonth") Integer startMonth,
                                                      @Param("pageSize") Integer pageSize);


}
