package fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote;

import java.util.List;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.DecorateInfoBean;
import fm.lizhi.ocean.wavecenter.base.remote.IRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.request.RequestRecoverDecorate;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.request.RequestSendDecorate;

/**
 * 装扮远程接口
 * <AUTHOR>
 * @date 2025/3/21 15:30
 */
public interface DecorateRemote extends IRemote {

    /**
     * 获取装扮信息
     * @param decorateTypeEnum 装扮类型
     * @return 装扮信息
     */
    DecorateInfoBean getDecorateInfo(PlatformDecorateTypeEnum decorateType, Long decorateId);

    /**
     * 获取装扮信息列表
     * @param
     * @return 装扮信息列表
     */
    PageBean<DecorateInfoBean> getDecorateInfoList(PlatformDecorateTypeEnum decorateType, Long decorateId, String decorateName, int pageNum, int pageSize);

    /**
     * 批量获取装扮信息
     * @param
     * @return 装扮信息列表
     */
    List<DecorateInfoBean> batchGetDecorateInfo(PlatformDecorateTypeEnum decorateType, List<Long> decorateIds);

    /**
     * 单次发放装扮
     *
     * @param request 请求参数
     * @return 发放结果
     */
    Result<Void> sendDecorate(RequestSendDecorate request);


    /**
     * 单次回收装扮
     * @param request
     * @return
     */
    Result<Void> recoverDecorate(RequestRecoverDecorate request);
}
