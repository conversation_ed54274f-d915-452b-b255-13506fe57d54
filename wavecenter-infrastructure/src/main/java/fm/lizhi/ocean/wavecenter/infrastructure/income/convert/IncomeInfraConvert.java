package fm.lizhi.ocean.wavecenter.infrastructure.income.convert;

import fm.lizhi.ocean.wavecenter.api.common.constants.IncomeTypeEnum;
import fm.lizhi.ocean.wavecenter.api.income.bean.*;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.LiveGiveGiftActionPo;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcFlowFamilyNj;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcFlowUser;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.res.PlayerIncomeStatRes;
import fm.lizhi.ocean.wavecenter.service.income.dto.GiveGiftFlowDto;
import fm.lizhi.pay.settle.protocol.creativecenter.CreatorDataQueryProto;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/23 19:24
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface IncomeInfraConvert {

    IncomeInfraConvert I = Mappers.getMapper(IncomeInfraConvert.class);

    @Mappings({
            @Mapping(source = "flowDate", target = "date"),
            @Mapping(source = "njId", target = "roomInfo.id"),
            @Mapping(source = "njName", target = "roomInfo.name"),
            @Mapping(source = "njBand", target = "roomInfo.band"),
            @Mapping(source = "bizId", target = "incomeType"),
            @Mapping(source = "income", target = "income"),
            @Mapping(source = "remark", target = "content"),
            @Mapping(source = "bizId",target = "incomeTypeName",qualifiedByName = "incomeTypeConvert"),
    })
    GuildIncomeDetailBean flowFamilyNj2guildIncomeDetailBean(WcFlowFamilyNj po);

    List<GuildIncomeDetailBean> flowFamilyNjs2guildIncomeDetailBeans(List<WcFlowFamilyNj> pos);


    @Mappings({
            @Mapping(source = "flowDate", target = "date"),
            @Mapping(source = "bizId", target = "incomeType"),
            @Mapping(source = "income", target = "income"),
            @Mapping(source = "remark", target = "content"),
            @Mapping(source = "bizId",target = "incomeName",qualifiedByName = "incomeTypeConvert"),
    })
    RoomIncomeDetailBean flowFamilyNj2roomIncomeDetailBean(WcFlowFamilyNj po);

    List<RoomIncomeDetailBean> flowFamilyNjs2roomIncomeDetailBeans(List<WcFlowFamilyNj> pos);


    @Mappings({
            @Mapping(source = "flowDate", target = "date"),
            @Mapping(source = "income", target = "income"),
            @Mapping(source = "remark", target = "content"),
            @Mapping(source = "incomeAmount", target = "revenueAmount"),
    })
    PersonalIncomeDetailBean flowUser2personalIncomeDetailBean(WcFlowUser po);
    List<PersonalIncomeDetailBean> flowUsers2personalIncomeDetailBeans(List<WcFlowUser> pos);

    @Mappings({
            @Mapping(source = "flowDate", target = "date"),
            @Mapping(source = "income", target = "income"),
            @Mapping(source = "remark", target = "content"),
            @Mapping(source = "bizId", target = "incomeType"),
            @Mapping(source = "bizId",target = "incomeName",qualifiedByName = "incomeTypeConvert"),
    })
    PlayerIncomeDetailBean flowUser2playerIncomeDetailBean(WcFlowUser po);



    List<PlayerIncomeDetailBean> flowUsers2playerIncomeDetailBeans(List<WcFlowUser> pos);


    @Mappings({
            @Mapping(target = "date", source = "createTime"),
            @Mapping(target = "roomId", source = "njId"),
            @Mapping(target = "sendUserId", source = "userId"),
            @Mapping(target = "income", source = "totalLitchiAmount"),
            @Mapping(target = "requestId", source = "id"),
            @Mapping(target = "charm", source = "value")
    })
    GiveGiftFlowDto liveGiveGiftActionPo2FlowDto(LiveGiveGiftActionPo po);

    List<GiveGiftFlowDto> liveGiveGiftActionPos2FlowDtos(List<LiveGiveGiftActionPo> pos);

    @Mappings({
            @Mapping(target = "playerInfo.id", source = "id"),
            @Mapping(target = "playerInfo.name", source = "name"),
            @Mapping(target = "playerInfo.band", source = "band"),
            @Mapping(target = "charm", source = "charmValue"),
            @Mapping(target = "personalHallIncome", source = "playerPersonalLiveIncome"),
    })
    RoomSignPlayerIncomeBean playerIncomeStatRes2Bean(PlayerIncomeStatRes res);

    List<RoomSignPlayerIncomeBean> playerIncomeStatRess2Beans(List<PlayerIncomeStatRes> ress);

    @Mappings({
            @Mapping(target = "recRoomInfo.id", source = "roomId"),
            @Mapping(target = "sendUserInfo.id", source = "sendUserId"),
    })
    PersonalGiftflowBean giveGiftFlowDto2PersonalGiftflowBean(GiveGiftFlowDto dto);

    @Mappings({
            @Mapping(target = "recRoomInfo.id", source = "roomId"),
            @Mapping(target = "sendUserInfo.id", source = "sendUserId"),
            @Mapping(target = "recUserInfo.id", source = "recUserId"),
    })
    PlayerRoomGiftflowBean giveGiftFlowDto2PlayerRoomGiftflowBean(GiveGiftFlowDto dto);


    @Mappings({
            @Mapping(target = "date", source = "happenedTime",qualifiedByName = "date"),
            @Mapping(target = "income", source = "amount"),
            @Mapping(target = "content", source = "remark"),
    })
    PersonalIncomeDetailBean queryAnchorIncomeDetailResponse2personalIncomeDetailBean(CreatorDataQueryProto.QueryAnchorIncomeDetailResponse po);


    List<PersonalIncomeDetailBean> queryAnchorIncomeDetailResponses2personalIncomeDetailBeans(List<CreatorDataQueryProto.QueryAnchorIncomeDetailResponse> list);


    @Mappings({
            @Mapping(target = "date", source = "happenedTime",qualifiedByName = "date"),
            @Mapping(target = "income", source = "amount"),
            @Mapping(target = "content", source = "remark"),
            @Mapping(target = "incomeType", source = "bizId"),
            @Mapping(target = "revenueAmount", source = "earningAmount"),
    })
    PersonalIncomeDetailBean queryAnchorIncomeDetailWithEarningResponse2personalIncomeDetailBean(CreatorDataQueryProto.QueryAnchorIncomeWithEarningDetailResponse po);

    List<PersonalIncomeDetailBean> queryAnchorIncomeDetailWithEarningResponses2personalIncomeDetailBeans(List<CreatorDataQueryProto.QueryAnchorIncomeWithEarningDetailResponse> list);


    @Mappings({
            @Mapping(target = "date", source = "happenedTime",qualifiedByName = "date"),
            @Mapping(target = "income", source = "amount"),
            @Mapping(target = "content", source = "remark"),
            @Mapping(target = "incomeType", source = "bizId"),
    })
    PlayerIncomeDetailBean queryAnchorIncomeDetailResponse2PlayerIncomeDetailBean(CreatorDataQueryProto.QueryAnchorIncomeDetailResponse po);
    List<PlayerIncomeDetailBean> queryAnchorIncomeDetailResponses2PlayerIncomeDetailBeans(List<CreatorDataQueryProto.QueryAnchorIncomeDetailResponse> list);


    @Named("date")
    default Date dateConvert(String dataStr){
        if (StringUtils.isEmpty(dataStr)) {
            return null;
        }
        return fm.lizhi.commons.util.DateUtil.formatStrToDate(dataStr, fm.lizhi.commons.util.DateUtil.NORMAL_DATE_FORMAT);
    }


    @Named("incomeTypeConvert")
    default String incomeTypeConvert(Integer bizId){
        return IncomeTypeEnum.getName(bizId);
    }


}
