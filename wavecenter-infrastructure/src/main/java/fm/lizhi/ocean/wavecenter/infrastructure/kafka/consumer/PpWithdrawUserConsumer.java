package fm.lizhi.ocean.wavecenter.infrastructure.kafka.consumer;


import com.alibaba.fastjson.JSON;
import fm.lizhi.common.kafka.common.strategy.AutoOffsetReset;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaHandler;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaListener;
import fm.lizhi.common.kafka.ioc.api.strategy.annotation.AutoOffsetResetStrategy;
import fm.lizhi.hy.user.account.bean.UserWithdrawMsg;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.common.utils.KafkaMsgUtils;
import fm.lizhi.ocean.wavecenter.service.user.manager.LoginManager;
import fm.lizhi.pp.user.account.meta.UserConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@KafkaListener(clusterNamespace = "lizhi-kafka-bootstrap-server")
public class PpWithdrawUserConsumer {

    @Autowired
    private LoginManager loginManager;

    @KafkaHandler(topic = "pp_topic_user_withdraw_event",
            group = "pp_topic_withdraw_user_wavecenter_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleWithdrawUser(String body) {
        String msg = null;
        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.PP);
            log.info("pp.handleWithdrawUser msg:{}", msg);
            UserWithdrawMsg eventDto = JSON.parseObject(msg, UserWithdrawMsg.class);
            if (eventDto == null) {
                return;
            }

            if (eventDto.getStatus() != UserConstant.STATUS_NORMAL) {
                //调用退登接口
                loginManager.deleteAllLoginInfo(ContextUtils.getBusinessEvnEnum().getAppId(), eventDto.getUserId());
            }
        } catch (Exception e) {
            log.error("pp.handleWithdrawUser", e);
        }
    }


}
