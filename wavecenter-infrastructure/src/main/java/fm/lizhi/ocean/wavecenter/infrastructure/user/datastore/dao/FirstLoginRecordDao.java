package fm.lizhi.ocean.wavecenter.infrastructure.user.datastore.dao;

import fm.lizhi.ocean.wavecenter.datastore.user.entity.WcUserFirstLoginRecord;
import fm.lizhi.ocean.wavecenter.datastore.user.mapper.WcUserFirstLoginRecordMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.lizhi.commons.config.core.util.ConfigUtils;

import java.util.Optional;

/**
 * 首次登录记录DAO
 * <AUTHOR>
 */
@Repository
public class FirstLoginRecordDao {
    @Autowired
    private WcUserFirstLoginRecordMapper mapper;

    /**
     * 保存首次登录记录
     * @param record 记录实体
     * @return 影响行数
     */
    public int insert(WcUserFirstLoginRecord record) {
        // 如果记录已存在则忽略
        record.setDeployEnv(ConfigUtils.getEnvRequired().name());
        return mapper.insert(record);
    }

    /**
     * 根据appId和userId查询首次登录记录
     * @param appId 应用ID
     * @param userId 用户ID
     * @return 记录实体
     */
    public Optional<WcUserFirstLoginRecord> getUserFirstLoginInfo(Integer appId, Long userId) {
        WcUserFirstLoginRecord query = new WcUserFirstLoginRecord();
        query.setAppId(appId);
        query.setUserId(userId);
        query.setDeployEnv(ConfigUtils.getEnvRequired().name());
        return Optional.ofNullable(mapper.selectOne(query));
    }
} 