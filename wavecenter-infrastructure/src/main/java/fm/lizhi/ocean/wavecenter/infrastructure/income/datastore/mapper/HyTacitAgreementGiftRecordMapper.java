package fm.lizhi.ocean.wavecenter.infrastructure.income.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/5/28 16:42
 */
@DataStore(namespace = "mysql_heiye_lzppactivity_r")
public interface HyTacitAgreementGiftRecordMapper {

    @Select({
            "<script>"
            , "select flow_id "
            , "from tacit_agreement_gift_record"
            , "where flow_id IN "
            , "<foreach collection='flowIds' item='fId' open='(' separator=',' close=')'>"
            , "#{fId}"
            , "</foreach>"
            , "</script>"
    })
    List<Long> existFlowIdByFlowIds(@Param("flowIds") Set<Long> flowIds);

}
