package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote;

import fm.lizhi.ocean.wavecenter.base.remote.IRemote;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.PlayerSignCharmSumPo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.AdminPlayerIncomeInfoPo;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/4/18 20:31
 */
public interface ICharmStatRemote extends IRemote {



    List<PlayerSignCharmSumPo> selectPlayerSignCharmSumByUsers(long njId
            , long familyId
            , List<Long> userIds
            , Date startDate
            , Date endDate);

    /**
     * 查询厅收入魅力值
     * @param roomIds
     * @param playerId
     * @param startDate
     * @param endDate
     * @return
     */
    Integer getRoomIncomeCharm(List<Long> roomIds, long playerId, Date startDate, Date endDate);

    Integer getPersonIncomeCharm(long playerId, Date startDate, Date endDate);

    /**
     * 获取厅收入信息
     * @param njIds 厅主id列表
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 厅收入信息 key=njId value=厅收入信息
     */
    Map<Long, AdminPlayerIncomeInfoPo> getAdminPlayerIncomeInfoMap(List<Long> njIds, Date startDate, Date endDate);

}
