package fm.lizhi.ocean.wavecenter.infrastructure.grow.constants;

import fm.lizhi.ocean.wavecenter.base.constants.IRedisKey;

/**
 * <AUTHOR>
 * @date 2025/3/19 17:27
 */
public enum GrowRedisKey implements IRedisKey {

    /**
     * 公会等级结算最后结算公会ID
     * WC_GROW_FAMILY_LEVEL_SETTLE_LAST_FAMILY_[appId]_[yyyyMMdd]_[yyyyMMdd]
     * value=familyId
     */
    FAMILY_LEVEL_SETTLE_LAST_FAMILY
    ;

    @Override
    public String getPrefix() {
        return "WC_GROW";
    }

    @Override
    public String getName() {
        return this.name();
    }
}
