package fm.lizhi.ocean.wavecenter.infrastructure.user.manager;

import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.IUserGroupRemote;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserGroupManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/5/9 16:43
 */
@Component
public class UserGroupManagerImpl implements UserGroupManager {

    @Autowired
    private IUserGroupRemote iUserGroupRemote;

    @Override
    public boolean isUserInGroup(Long userId, Long groupId) {
        return iUserGroupRemote.isUserInGroup(userId, groupId);
    }

}
