package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.dao;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.*;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.*;
import fm.lizhi.ocean.wavecenter.datastore.background.mapper.ActivityBigClassCategoryMapper;
import fm.lizhi.ocean.wavecenter.datastore.background.mapper.ActivityBigClassMapper;
import fm.lizhi.ocean.wavecenter.datastore.background.mapper.ActivityClassConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Repository
@Slf4j
public class ActivityClassificationDao {

    @Autowired
    private ActivityBigClassMapper activityBigClassMapper;

    @Autowired
    private ActivityClassConfigMapper activityClassConfigMapper;

    @Autowired
    private ActivityBigClassCategoryMapper activityBigClassCategoryMapper;

    /**
     * 查询分类的品类
     * @param classId
     * @return
     */
    public List<Integer> getClassCategory(Integer appId, Long classId){
        ActivityClassConfigExample example = new ActivityClassConfigExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andDeletedEqualTo(false)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andIdEqualTo(classId);
        List<ActivityClassConfig> list = activityClassConfigMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        Long bigClassId = list.get(0).getBigClassId();

        List<ActivityBigClassCategory> categoryList = getBigClassCategory(appId, Lists.newArrayList(bigClassId));
        if (CollectionUtils.isEmpty(categoryList)) {
            return Collections.emptyList();
        }
        return categoryList.stream().map(ActivityBigClassCategory::getCategoryValue).collect(Collectors.toList());
    }

    /**
     * 保存分类以及品类
     * @param entity
     * @param categoryValues
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveBigClassificationAndCategory(ActivityBigClass entity, List<Integer> categoryValues){
        // 保存分类
        Long bigClassId = saveBigClassification(entity);

        // 更新品类
        updateBigClassCategory(bigClassId, entity.getAppId(), categoryValues);
    }

    /**
     * 更新品类
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBigClassCategory(Long bigClassId, Integer appId, List<Integer> categoryValues){
        // 删除已有的
        ActivityBigClassCategoryExample example = new ActivityBigClassCategoryExample();
        example.createCriteria().andAppIdEqualTo(appId).andBigClassIdEqualTo(bigClassId);
        long row = activityBigClassCategoryMapper.deleteByExample(example);
        log.info("updateBigClassCategory delete. row={}", row);

        // 重新写入
        if (CollectionUtils.isEmpty(categoryValues)) {
            return;
        }

        List<ActivityBigClassCategory> insertList = new ArrayList<>();
        for (Integer categoryValue : categoryValues) {
            ActivityBigClassCategory entity = new ActivityBigClassCategory();
            entity.setBigClassId(bigClassId);
            entity.setAppId(appId);
            entity.setCategoryValue(categoryValue);
            entity.setCreateTime(new Date());
            entity.setUpdateTime(new Date());
            insertList.add(entity);
        }
        int insertRow = activityBigClassCategoryMapper.batchInsert(insertList);
        log.info("updateBigClassCategory insert. row={}", insertRow);
    }

    /**
     * 保存大类, 如果已存在同名但被删除的大类, 则恢复被删除的大类为未删除状态. 外层调用者需自行检查是否存在同名未删除的大类
     *
     * @param req 请求参数
     */
    @Transactional
    public Long saveBigClassification(ActivityBigClass entity) {
        Integer appId = entity.getAppId();
        String name = entity.getName();
        Date now = new Date();
        ActivityBigClass oldEntity = getActivityBigClassByAppIdAndName(appId, name);
        if (oldEntity == null) {
            activityBigClassMapper.insert(entity);
            log.info("saveBigClassification insert new big class, req={}", entity);
            return entity.getId();
        }
        //修改不能改分类类型
        Long oldId = oldEntity.getId();
        ActivityBigClass updateEntity = new ActivityBigClass();
        updateEntity.setId(oldId);
        updateEntity.setDeleted(false);
        updateEntity.setWeight(entity.getWeight());
        updateEntity.setModifyTime(now);
        updateEntity.setOperator(entity.getOperator());
        activityBigClassMapper.updateByPrimaryKey(updateEntity);
        log.info("saveBigClassification resume big class, req={}, oldId={}", entity, oldId);
        return oldId;
    }

    /**
     * 根据应用ID和名称查询大类
     *
     * @param appId 应用ID
     * @param name  大类名称
     * @return 大类
     */
    public ActivityBigClass getActivityBigClassByAppIdAndName(int appId, String name) {
        ActivityBigClass selectOne = new ActivityBigClass();
        selectOne.setAppId(appId);
        selectOne.setName(name);
        selectOne.setDeployEnv(ConfigUtils.getEnvRequired().name());
        return activityBigClassMapper.selectOne(selectOne);
    }

    /**
     * 更新大类. 外层调用者需自行检查是否存在同名未删除的大类, 以及部署环境是否一致
     *
     * @param req 请求参数
     */
    @Transactional
    public void updateBigClassification(RequestUpdateActivityBigClass req) {
        ActivityBigClass updateEntity = new ActivityBigClass();
        updateEntity.setId(req.getId());
        updateEntity.setName(req.getName());
        updateEntity.setWeight(req.getWeight());
        updateEntity.setModifyTime(new Date());
        updateEntity.setOperator(req.getOperator());
        activityBigClassMapper.updateByPrimaryKey(updateEntity);
        log.info("updateBigClassification, req={}", req);
    }

    /**
     * 更新大类和品类
     * @param req
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBigClassAndCategory(RequestUpdateActivityBigClass req){
        updateBigClassification(req);
        updateBigClassCategory(req.getId(), req.getAppId(), req.getCategoryList());
    }

    /**
     * 删除大类, 并删除大类关联的分类
     *
     * @param req 请求参数
     */
    @Transactional
    public void deleteBigClassification(RequestDeleteActivityBigClass req) {
        Long bigClassId = req.getId();
        String operator = req.getOperator();
        Date now = new Date();
        // 大类表
        ActivityBigClass deleteBigClass = new ActivityBigClass();
        deleteBigClass.setId(bigClassId);
        deleteBigClass.setDeleted(true);
        deleteBigClass.setModifyTime(now);
        deleteBigClass.setOperator(operator);
        int deletedBigClassRows = activityBigClassMapper.updateByPrimaryKey(deleteBigClass);
        log.info("deleteBigClassification, bigClassId={}, deletedBigClassRows={}", bigClassId, deletedBigClassRows);
        // 分类表
        ActivityClassConfigExample deleteClassCondition = new ActivityClassConfigExample();
        deleteClassCondition.createCriteria().andBigClassIdEqualTo(bigClassId).andDeletedEqualTo(Boolean.FALSE);
        ActivityClassConfig deleteClass = new ActivityClassConfig();
        deleteClass.setDeleted(true);
        deleteClass.setModifyTime(now);
        deleteClass.setOperator(operator);
        int deletedClassRows = activityClassConfigMapper.updateByExample(deleteClass, deleteClassCondition);
        log.info("deleteBigClassification, bigClassId={}, deletedClassRows={}", bigClassId, deletedClassRows);
    }

    /**
     * 根据应用ID查询大类列表, 不包括已删除的大类
     *
     * @param appId 应用ID
     * @return 大类列表
     */
    public List<ActivityBigClass> listBigClassificationByAppId(int appId) {
        ActivityBigClassExample listBigClass = new ActivityBigClassExample();
        listBigClass.createCriteria()
                .andAppIdEqualTo(appId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andDeletedEqualTo(Boolean.FALSE);
        listBigClass.setOrderByClause("`weight` DESC, `id` DESC");
        return activityBigClassMapper.selectByExample(listBigClass);
    }

    /**
     * 根据大类ID查询大类信息
     *
     * @param bigClassId 大类ID
     * @return 大类信息
     */
    public ActivityClassConfig getActivityClassConfigByBigClassIdAndName(long bigClassId, String name) {
        ActivityClassConfig selectOne = new ActivityClassConfig();
        selectOne.setBigClassId(bigClassId);
        selectOne.setName(name);
        selectOne.setDeployEnv(ConfigUtils.getEnvRequired().name());
        return activityClassConfigMapper.selectOne(selectOne);
    }

    /**
     * 保存分类, 如果同个大类下已存在同名但被删除的分类, 则恢复被删除的分类为未删除状态. 外层调用者需自行检查是否存在同名未删除的分类
     *
     * @param entity 请求参数
     */
    @Transactional
    public void saveClassification(ActivityClassConfig entity) {
        Long bigClassId = entity.getBigClassId();
        String name = entity.getName();
        Long levelId = entity.getLevelId();
        String operator = entity.getOperator();
        ActivityBigClass bigClass = getActivityBigClass(bigClassId);
        Integer appId = bigClass.getAppId();
        ActivityClassConfig oldEntity = getActivityClassConfigByBigClassIdAndName(bigClassId, name);
        Date now = new Date();
        if (oldEntity == null) {
            entity.setAppId(appId);
            activityClassConfigMapper.insert(entity);
            log.info("saveClassification insert new class, req={}", entity);
            return;
        }
        Long oldId = oldEntity.getId();
        ActivityClassConfig updateEntity = new ActivityClassConfig();
        updateEntity.setId(oldId);
        updateEntity.setLevelId(levelId);
        updateEntity.setWeight(entity.getWeight());
        updateEntity.setDeleted(false);
        updateEntity.setModifyTime(now);
        updateEntity.setOperator(operator);
        activityClassConfigMapper.updateByPrimaryKey(updateEntity);
        log.info("saveClassification resume class, req={}, oldId={}", entity, oldId);
    }

    /**
     * 更新分类. 外层调用者需自行检查是否存在同名未删除的分类, 以及部署环境是否一致
     *
     * @param req 请求参数
     */
    @Transactional
    public void updateClassification(RequestUpdateActivityClassification req) {
        ActivityClassConfig updateEntity = new ActivityClassConfig();
        updateEntity.setId(req.getId());
        updateEntity.setLevelId(req.getLevelId());
        updateEntity.setName(req.getName());
        updateEntity.setWeight(req.getWeight());
        updateEntity.setModifyTime(new Date());
        updateEntity.setOperator(req.getOperator());
        activityClassConfigMapper.updateByPrimaryKey(updateEntity);
        log.info("updateClassification, req={}", req);
    }

    /**
     * 删除分类
     *
     * @param req 请求参数
     */
    public void deleteClassification(RequestDeleteActivityClassification req) {
        ActivityClassConfig deleteClassConfig = new ActivityClassConfig();
        deleteClassConfig.setId(req.getId());
        deleteClassConfig.setDeleted(true);
        deleteClassConfig.setModifyTime(new Date());
        deleteClassConfig.setOperator(req.getOperator());
        int deletedClassRows = activityClassConfigMapper.updateByPrimaryKey(deleteClassConfig);
        log.info("deleteClassification, req={}, deletedClassRows={}", req, deletedClassRows);
    }

    /**
     * 根据大类ID查询分类列表, 不包括已删除的分类
     *
     * @param bigClassId 大类ID
     * @return 分类列表
     */
    public List<ActivityClassConfig> listClassificationByBigClassId(long bigClassId) {
        ActivityClassConfigExample listClass = new ActivityClassConfigExample();
        listClass.createCriteria()
                .andBigClassIdEqualTo(bigClassId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andDeletedEqualTo(Boolean.FALSE);
        listClass.setOrderByClause("`weight` DESC, `id` DESC");
        return activityClassConfigMapper.selectByExample(listClass);
    }

    /**
     * 根据大类或者小类ID，查询并过滤出所有小类，并返回
     * 比如 classIdsOrBigClassIds 中既有大类ID，也有小类ID，则返回所有小类ID对应的分类
     *
     * @param classIdsOrBigClassIds 大类ID或者小类ID
     * @return 分类列表
     */
    public List<ActivityClassConfig> listClassificationByClassOrBigClass(List<Long> classIdsOrBigClassIds) {
        List<Long> classIds = new ArrayList<>();

        List<Long> bigClassIds = new ArrayList<>();
        ActivityClassConfigExample listClass = new ActivityClassConfigExample();
        listClass.createCriteria()
                .andBigClassIdIn(classIdsOrBigClassIds)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andDeletedEqualTo(Boolean.FALSE);
        List<ActivityClassConfig> refList = activityClassConfigMapper.selectByExample(listClass);
        if (CollectionUtils.isNotEmpty(refList)) {
            classIds.addAll(refList.stream().map(ActivityClassConfig::getId).collect(Collectors.toList()));
            bigClassIds.addAll(refList.stream().map(ActivityClassConfig::getBigClassId).distinct().collect(Collectors.toList()));
        }

        for (Long classIdsOrBigClassId : classIdsOrBigClassIds) {
            if (!bigClassIds.contains(classIdsOrBigClassId)) {
                // 说明是小类
                classIds.add(classIdsOrBigClassId);
            }
        }

        if (CollectionUtils.isEmpty(classIds)) {
            return Collections.emptyList();
        }

        ActivityClassConfigExample example = new ActivityClassConfigExample();
        example.createCriteria()
                .andIdIn(classIds)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andDeletedEqualTo(Boolean.FALSE);
        example.setOrderByClause("`weight` DESC, `id` DESC");
        return activityClassConfigMapper.selectByExample(example);
    }

    /**
     * 根据给定的分类Id列表，获取分类Id到分类的映射，不区分各种条件, 只要id存在就查出来
     *
     * @param classIds 分类Id列表
     * @return 分类Id到分类的映射
     */
    public Map<Long, ActivityClassConfig> getClassIdToClassConfigMapByClassIds(List<Long> classIds) {
        if (CollectionUtils.isEmpty(classIds)) {
            return Collections.emptyMap();
        }
        ActivityClassConfigExample example = new ActivityClassConfigExample();
        ActivityClassConfigExample.Criteria criteria = example.createCriteria();
        criteria.andIdIn(classIds);
        List<ActivityClassConfig> activityClassConfigs = activityClassConfigMapper.selectByExample(example);
        HashMap<Long, ActivityClassConfig> classIdToActivityClassConfigMap = new HashMap<>();
        for (ActivityClassConfig activityClassConfig : activityClassConfigs) {
            classIdToActivityClassConfigMap.put(activityClassConfig.getId(), activityClassConfig);
        }
        return classIdToActivityClassConfigMap;
    }

    /**
     * 查询活动分类, 不区分各种条件, 只要id存在就查出来
     *
     * @param classId 分类Id
     * @return 活动分类
     */
    public ActivityClassConfig getActivityClassConfig(Long classId) {
        if (classId == null) {
            return null;
        }
        ActivityClassConfig getById = new ActivityClassConfig();
        getById.setId(classId);
        return activityClassConfigMapper.selectByPrimaryKey(getById);
    }

    /**
     * 根据活动分类查询活动大类（不区分是否被删除）
     */
    public ActivityBigClass getActivityBigClass(Long bigClassId) {
        if (bigClassId == null) {
            return null;
        }
        ActivityBigClass entity = new ActivityBigClass();
        entity.setId(bigClassId);
        return activityBigClassMapper.selectByPrimaryKey(entity);
    }

    /**
     * 根据给定的大类Id列表，获取大类Id到大类的映射，不区分各种条件, 只要id存在就查出来
     *
     * @param bigClassIds 大类Id列表
     * @return 大类Id到大类的映射
     */
    public Map<Long, ActivityBigClass> getBigClassIdToBigClassMapByBigClassIds(List<Long> bigClassIds) {
        if (CollectionUtils.isEmpty(bigClassIds)) {
            return Collections.emptyMap();
        }
        ActivityBigClassExample example = new ActivityBigClassExample();
        example.createCriteria()
                .andIdIn(bigClassIds);
        List<ActivityBigClass> activityBigClasses = activityBigClassMapper.selectByExample(example);
        HashMap<Long, ActivityBigClass> bigClassIdToActivityBigClassMap = new HashMap<>();
        for (ActivityBigClass activityBig : activityBigClasses) {
            bigClassIdToActivityBigClassMap.put(activityBig.getId(), activityBig);
        }
        return bigClassIdToActivityBigClassMap;
    }

    /**
     * 根据应用ID查询分类列表, 不包括已删除的分类
     *
     * @param appId 应用ID
     * @return 分类列表
     */
    public List<ActivityClassConfig> listClassificationByAppId(int appId) {
        ActivityClassConfigExample example = new ActivityClassConfigExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andDeletedEqualTo(Boolean.FALSE)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        example.setOrderByClause("`weight` DESC, `id` DESC");
        return activityClassConfigMapper.selectByExample(example);
    }

    public List<ActivityClassConfig> listClassificationByLevelId(Long levelId) {
        if (levelId == null) {
            return Collections.emptyList();
        }
        
        ActivityClassConfigExample example = new ActivityClassConfigExample();
        example.createCriteria()
                .andLevelIdEqualTo(levelId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andDeletedEqualTo(Boolean.FALSE);
        
        return activityClassConfigMapper.selectByExample(example);
    }

    /**
     * 根据应用ID和大类ID列表查询小类列表
     *
     * @param appId      应用ID
     * @param bigClassIds 分类ID列表
     * @return 分类列表
     */
    public List<ActivityClassConfig> getClassConfigListByBigClassIds(int appId, List<Long> bigClassIds) {
        if (CollectionUtils.isEmpty(bigClassIds)) {
            return Collections.emptyList();
        }

        ActivityClassConfigExample example = new ActivityClassConfigExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andDeletedEqualTo(Boolean.FALSE)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andBigClassIdIn(bigClassIds);

        return activityClassConfigMapper.selectByExample(example);
    }

    /**
     * 根据品类值获取大类ID列表
     *
     * @param appId
     * @param categoryValue
     * @return
     */
    public List<ActivityBigClassCategory> getBigClassesByCategoryValue(int appId, ArrayList<Integer> categoryValue) {
        if (CollUtil.isEmpty(categoryValue)) {
            return Collections.emptyList();
        }

        ActivityBigClassCategoryExample example = new ActivityBigClassCategoryExample();
        example.createCriteria()
                .andCategoryValueIn(categoryValue)
               .andAppIdEqualTo(appId);

        return activityBigClassCategoryMapper.selectByExample(example);
    }

    /**
     * 查询分类的品类
     * @param appId
     * @param bigClassIds
     * @return
     */
    public List<ActivityBigClassCategory> getBigClassCategory(int appId, List<Long> bigClassIds){
        if (CollectionUtils.isEmpty(bigClassIds)) {
            return Collections.emptyList();
        }

        ActivityBigClassCategoryExample example = new ActivityBigClassCategoryExample();
        example.createCriteria()
                .andBigClassIdIn(bigClassIds)
                .andAppIdEqualTo(appId);

        return activityBigClassCategoryMapper.selectByExample(example);
    }

    /**
     * 查询分类的品类
     * @param appId
     * @param bigClassIds
     * @return
     */
    public Map<Long, List<ActivityBigClassCategory>> getBigClassCategoryMap(int appId, List<Long> bigClassIds){
        List<ActivityBigClassCategory> list = getBigClassCategory(appId, bigClassIds);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.groupingBy(ActivityBigClassCategory::getBigClassId));
    }
}
