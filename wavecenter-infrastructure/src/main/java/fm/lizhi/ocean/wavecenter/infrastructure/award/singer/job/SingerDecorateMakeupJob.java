package fm.lizhi.ocean.wavecenter.infrastructure.award.singer.job;


import cn.hutool.core.collection.CollUtil;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateOperateStatusEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.award.singer.datastore.dao.SingerDecorateDao;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.entity.SingerDecorateFlow;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowSendParamDTO;
import fm.lizhi.ocean.wavecenter.service.award.singer.manager.SingerDecorateManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 补偿装扮发放/回收
 * <AUTHOR>
 */
@Slf4j
@Component
public class SingerDecorateMakeupJob implements JobHandler {


    @Autowired
    private SingerDecorateDao singerDecorateDao;

    @Autowired
    private SingerDecorateManager singerDecorateManager;


    @Override
    public void execute(JobExecuteContext context) throws Exception {

        int pageSize = 50;
        int pageNo = 1;
        boolean isHasNextPage;
        AtomicInteger count = new AtomicInteger(0);

        log.info("SingerDecorateMakeupJob execute start");

        do {
            // 查询出未处理/处理失败的流水
            PageList<SingerDecorateFlow> pageList = singerDecorateDao.getDecorateFlowByStatus(
                    CollUtil.newArrayList(SingerDecorateOperateStatusEnum.UNPROCESSED, SingerDecorateOperateStatusEnum.FAIL),
                    pageSize, pageNo
            );

            if (CollUtil.isEmpty(pageList)){
                return;
            }

            // map, key=transactionId, value=appId
            Map<Long, Integer> appIdMap = pageList.stream()
                    .collect(Collectors.toMap(
                            SingerDecorateFlow::getTransactionId,
                            SingerDecorateFlow::getAppId,
                            (existing, replacement) -> existing
                    ));


            pageList.stream()
                    .map(SingerDecorateFlow::getTransactionId).distinct().collect(Collectors.toList())
                    .forEach(transactionId -> {
                        try {
                            // 设置业务环境
                            ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.from(appIdMap.get(transactionId)));
                            singerDecorateManager.operateSingerDecorate(new SingerDecorateFlowSendParamDTO().setTransactionId(transactionId));
                            log.info("SingerDecorateMakeupJob execute success, transactionId: {}", transactionId);
                            count.incrementAndGet();
                        }catch (Exception e){
                            log.error("SingerDecorateMakeupJob execute error, transactionId: {}", transactionId, e);
                        }finally {
                            ContextUtils.clearContext();
                        }
                    });
            pageNo++;
            isHasNextPage = pageList.isHasNextPage();
        } while (isHasNextPage);

        log.info("SingerDecorateMakeupJob execute end. count: {}", count.get());
    }

    

}
