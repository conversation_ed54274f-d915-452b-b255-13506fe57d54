package fm.lizhi.ocean.wavecenter.infrastructure.award.family.job;

import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager.deliver.FamilyOtherAwardDeliverManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 公会其他奖励发放任务  手动发放
 */
@Component
@Slf4j
public class FamilyOtherAwardDeliverHandleJob implements JobHandler {

    @Autowired
    private FamilyOtherAwardDeliverManager familyOtherAwardDeliverManager;

    @Override
    public void execute(JobExecuteContext context) throws Exception {
        FamilyOtherAwardDeliverHandleJobParam param = parseParam(context);
        if (param == null) {
            return;
        }

        Integer appId = param.getAppId();
        Date awardStartTime = param.getAwardStartTime();
        List<Long> familyIds = param.getFamilyIds();

        if (appId == null || awardStartTime == null || CollectionUtils.isEmpty(familyIds)) {
            return;
        }

        log.info("FamilyOtherAwardDeliverHandleJob start, appId: {}, awardStartTime: {}, familyIds: {}", appId, awardStartTime, familyIds);
        for (Long familyId : familyIds) {
            familyOtherAwardDeliverManager.deliverAward(appId, awardStartTime, familyId);
        }

        log.info("FamilyOtherAwardDeliverHandleJob end");
    }

    private FamilyOtherAwardDeliverHandleJobParam parseParam(JobExecuteContext context) {
        if (StringUtils.isBlank(context.getParam())) {
            return null;
        }
        try {
            return JsonUtils.fromJsonString(context.getParam(), FamilyOtherAwardDeliverHandleJobParam.class);
        } catch (RuntimeException e) {
            log.info("Failed to parse param: {}", context.getParam(), e);
            return null;
        }
    }


}
