package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.remote.pp;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestBatchGetDecorate;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.constants.DecorateMapping;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert.ActivityDecorateConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.remote.IDecorateRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.remote.request.pp.RequestGetDecorateList;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.DecorateDTO;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.request.RequestGetDecorate;
import fm.lizhi.pp.vip.api.PpDecorateService;
import fm.lizhi.pp.vip.bean.resp.PageDecorateDto;
import fm.lizhi.pp.vip.protocol.PpDecorateProto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class PpDecorateRemoteImpl implements IDecorateRemote {

    @Autowired
    private PpDecorateService decorateService;


    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public Result<PageBean<DecorateDTO>> getDecorateList(RequestGetDecorate request) {

        RequestGetDecorateList param = RequestGetDecorateList.builder()
                .pageNum(request.getPageNo())
                .pageSize(request.getPageSize())
                .id(request.getDressUpId())
                .type(DecorateMapping.waveType2Biz(request.getType(), BusinessEvnEnum.PP))
                .build();

        if (StrUtil.isNotBlank(request.getName())) {
            param.setName(request.getName());
        }

        Result<PpDecorateProto.ResponseGetDecorateList> result = decorateService.getDecorateList(JsonUtils.toJsonStringLegacy(param));

        if (RpcResult.isFail(result)) {
            log.warn("pp decorate result fail. type:{}, name:{}, pageNo:{}, pageSize:{}", request.getType(), request.getName(), request.getPageNo(), request.getPageSize());
            return new Result<>(result.rCode(), PageBean.empty());
        }

        PpDecorateProto.ResponseGetDecorateList target = result.target();
        List<DecorateDTO> list = ActivityDecorateConvert.I.ppGetDecorateListResp2DecorateDTO(
                JSONObject.parseArray(target.getDecorateListStr(), PageDecorateDto.class), BusinessEvnEnum.PP
        );
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, PageBean.of((int) target.getTotal(), list));
    }

    @Override
    public Result<List<DecorateDTO>> batchGetDecorateList(RequestBatchGetDecorate request) {
        List<Long> ids = request.getIds();
        List<DecorateDTO> res = new ArrayList<>();
        for (Long id : ids) {
            Result<PpDecorateProto.ResponseGetDecorateInfoById> result = decorateService.getDecorateInfoById(id);
            if (RpcResult.isFail(result)) {
                log.warn("pp batch decorate result fail. type:{}, id:{}", request.getType(), id);
                return RpcResult.fail(BATCH_GET_DECORATE_LIST_FAIL);
            }
            String decorateDtoStr = result.target().getDecorateDtoStr();
            DecorateDTO decorateDTO = ActivityDecorateConvert.I.ppGetDecorateResp2DecorateDTO(JSONObject.parseObject(decorateDtoStr, PageDecorateDto.class), BusinessEvnEnum.PP);
            res.add(decorateDTO);
        }

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, res);
    }
}
