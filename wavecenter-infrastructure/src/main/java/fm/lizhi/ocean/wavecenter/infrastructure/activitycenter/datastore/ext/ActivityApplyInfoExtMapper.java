package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityApplyInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/17 14:42
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface ActivityApplyInfoExtMapper {

    @Select({
        "<script>"
            , "SELECT * FROM activity_apply_info WHERE app_id=#{appId} and audit_status=#{auditStatus}"
            , "AND nj_id=#{njId}"
            , "AND NOW() &gt;= DATE_SUB(start_time, INTERVAL #{startTimeBeforeMinute} MINUTE) "
            , "AND NOW() &lt;= DATE_ADD(end_time, INTERVAL #{endTimeAfterMinute} MINUTE) "
            , "AND deleted=0 AND deploy_env=#{deployEnv}"
            , "</script>"
    })
    List<ActivityApplyInfo> getInTimeRangeActivityApply(@Param("appId") int appId
            , @Param("njId") long njId
            , @Param("auditStatus") int auditStatus
            , @Param("deployEnv") String deployEnv
            , @Param("startTimeBeforeMinute") int startTimeBeforeMinute
            , @Param("endTimeAfterMinute") int endTimeAfterMinute
    );

    @Select({
            "<script>",
            "SELECT ",
            "ai.id,",
            "ai.app_id,",
            "ai.name,",
            "ai.nj_id,",
            "ai.family_id,",
            "ai.class_id,",
            "ai.start_time,",
            "ai.end_time,",
            "ai.audit_status,",
            "ai.version,",
            "ai.apply_type,",
            "ai.audit_reason,",
            "ai.contact,",
            "ai.applicant_uid,",
            "ai.contact_number,",
            "ai.host_id,",
            "ai.accompany_nj_ids,",
            "ai.goal,",
            "ai.introduction,",
            "ai.auxiliary_prop_url,",
            "ai.poster_url,",
            "ai.activity_tool,",
            "ai.room_announcement,",
            "ai.room_announcement_img_url,",
            "ai.room_background_id,",
            "ai.avatar_widget_id,",
            "ai.deploy_env,",
            "ai.deleted,",
            "ai.audit_operator,",
            "ai.modify_time,",
            "ai.create_time,",
            "ai.model",
            " FROM activity_apply_info AS ai",
            "LEFT JOIN activity_apply_flow_resource afr ON ai.id = afr.activity_id",
            "LEFT JOIN activity_resource_give_record rgr ON ai.id = rgr.activity_id",
            "WHERE ai.audit_status = #{auditStatus}",
            "AND ai.start_time &gt;= #{startTime}",
            "AND ai.end_time &lt;= #{endTime}",
            "AND ai.deleted = 0",
            "AND ai.deploy_env = #{deployEnv}",
            "AND rgr.status = #{giveStatus}",
            "AND afr.resource_config_id = #{resourceConfigId}",
            "GROUP BY ai.id",
            "</script>"
    })
    List<ActivityApplyInfo> getInTimeRangeActivityApplyByProgramme(@Param("appId") Integer appId,
                                                                   @Param("startTime") Date startTime,
                                                                   @Param("endTime") Date endTime,
                                                                   @Param("auditStatus") Integer auditStatus,
                                                                   @Param("giveStatus") int giveStatus,
                                                                   @Param("deployEnv") String deployEnv,
                                                                   @Param("resourceConfigId") Long resourceConfigId);
}
