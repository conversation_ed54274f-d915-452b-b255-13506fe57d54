package fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.hy;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.mapper.ext.HyFamilyExtMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.IFamilyRemote;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/3/19 16:19
 */
@Component
public class HyFamilyRemote implements IFamilyRemote {

    @Autowired
    private HyFamilyExtMapper familyExtMapper;

    @Override
    public List<Long> getFamilyIdsByPage(Long lastFamilyId, Integer pageSize) {
        return familyExtMapper.getFamilyIdsByPage(lastFamilyId, pageSize);
    }

    @Override
    public Optional<String> getSocietyCode(Long familyId) {
        return Optional.empty();
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.HEI_YE;
    }
}
