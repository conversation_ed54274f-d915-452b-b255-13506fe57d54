package fm.lizhi.ocean.wavecenter.infrastructure.live.manager;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.datastore.redis.client.RedisClient;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.live.constants.CheckInDateTypeEnum;
import fm.lizhi.ocean.wavecenter.base.constants.TimeConstant;
import fm.lizhi.ocean.wavecenter.common.utils.RedisLock;
import fm.lizhi.ocean.wavecenter.infrastructure.live.constant.CheckInRedisKey;
import fm.lizhi.ocean.wavecenter.infrastructure.live.dto.GetCheckInRoomStatisticReportDTO;
import fm.lizhi.ocean.wavecenter.service.live.config.LiveConfig;
import fm.lizhi.ocean.wavecenter.service.live.manager.CheckInRedisManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR>
 * @date 2025/3/19 17:24
 */
@Component
public class CheckInRedisManagerImpl implements CheckInRedisManager {

    @Resource(name = "redis_ocean_wavecenter")
    private RedisClient redisClient;

    @Autowired
    private LiveConfig liveConfig;



    @Nullable
    public GetCheckInRoomStatisticReportDTO getCheckInRoomStatisticReport(Integer appId, CheckInDateTypeEnum dateType, Long njId, Long startDate) {
        if(liveConfig.isCloseCheckInReportDataCache()) {
            return null;
        }

        String dateFormat = DateUtil.formatDateToString(new Date(startDate), "yyyy_MM_dd_HH");
        String key = CheckInRedisKey.REPORT_STAT_DATA.getKey(appId, dateType.name(), njId, dateFormat);
        String reportData = redisClient.get(key);
        if(reportData == null) {
            return null;
        }
        return JsonUtil.loads(reportData, GetCheckInRoomStatisticReportDTO.class);
    }

    public RedisLock getCheckInRoomStatisticReportLock(Integer appId, CheckInDateTypeEnum dateType, Long roomId, Long startDate) {
        int timeout = TimeConstant.FIVE_SECOND_MILLISECOND;
        String dateFormat = DateUtil.formatDateToString(new Date(startDate), "yyyy_MM_dd_HH");
        String key = CheckInRedisKey.REPORT_STAT_DATA_LOCK.getKey(appId, dateType.name(), roomId, dateFormat);
        return new RedisLock(redisClient, key, timeout, timeout);
    }

    public void setCheckInRoomStatisticReport(Integer appId, CheckInDateTypeEnum dateType, Long roomId, Long startDate, GetCheckInRoomStatisticReportDTO data) {
        String dateFormat = DateUtil.formatDateToString(new Date(startDate), "yyyy_MM_dd_HH");
        String key = CheckInRedisKey.REPORT_STAT_DATA.getKey(appId, dateType.name(), roomId, dateFormat);
        // 基础过期时间加上0-120秒的随机时间
        int baseExpireSeconds = liveConfig.getCheckInRoomReportDataExpireSeconds();
        int randomExpireSeconds = baseExpireSeconds + ThreadLocalRandom.current().nextInt(0, TimeConstant.TWO_MINUTE);
        redisClient.setex(key, randomExpireSeconds, JsonUtil.dumps(data));
    }

    public List<Long> getHasIncomeRoomIdList(Integer appId, Integer startDayValue, Integer endDayValue) {
        String key = CheckInRedisKey.REPORT_HAS_INCOME_NJ_ID_DATA.getKey(appId, startDayValue, endDayValue, ConfigUtils.getEnvRequired().name());
        String reportData = redisClient.get(key);
        if(reportData == null) {
            return null;
        }
        return JsonUtil.loadsArray(reportData, Long.class);
    }

    public void setHasIncomeRoomIdList(Integer appId, Integer startDayValue, Integer endDayValue, List<Long> hasIncomeRoomIds) {
        String key = CheckInRedisKey.REPORT_HAS_INCOME_NJ_ID_DATA.getKey(appId, startDayValue, endDayValue, ConfigUtils.getEnvRequired().name());
        redisClient.setex(key, TimeConstant.ONE_DAY_SECOND, JsonUtil.dumps(hasIncomeRoomIds));
    }

    @Override
    public int getAlreadyReportCount(Integer appId, Long reportDate) {
        if(liveConfig.isCloseBatchSend()) {
            return 0;
        }
        String dateFormat = DateUtil.formatDateToString(new Date(reportDate), "yyyy_MM_dd_HH");
        String key = CheckInRedisKey.REPORT_HAS_REPORT_NJ_ID_COUNT.getKey(appId, dateFormat, ConfigUtils.getEnvRequired().name());
        String numStr = redisClient.get(key);
        return numStr == null ? 0 : Integer.parseInt(numStr);
    }

    @Override
    public void incrAlreadyReportCount(Integer appId, Long reportDate) {
        if(liveConfig.isCloseBatchSend()) {
            return;
        }
        String dateFormat = DateUtil.formatDateToString(new Date(reportDate), "yyyy_MM_dd_HH");
        String key = CheckInRedisKey.REPORT_HAS_REPORT_NJ_ID_COUNT.getKey(appId, dateFormat, ConfigUtils.getEnvRequired().name());
        redisClient.incr(key);
        redisClient.expire(key, TimeConstant.THREE_HOUR_SECOND);
    }
}
