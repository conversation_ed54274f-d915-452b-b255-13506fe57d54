package fm.lizhi.ocean.wavecenter.infrastructure.live.manager.checkinstat;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 主播私信结果
 */
@Data
@Accessors(chain = true)
public class ChatStat {
    /**
     * 用户id
     */
    private Long playerId;

    /**
     * 统计时间
     */
    private Date statDate;
    /**
     * 私信人数
     */
    private Integer chatUserCnt = 0;
    /**
     * 私信回复率
     */
    private BigDecimal replyChatRate = BigDecimal.ZERO;
}
