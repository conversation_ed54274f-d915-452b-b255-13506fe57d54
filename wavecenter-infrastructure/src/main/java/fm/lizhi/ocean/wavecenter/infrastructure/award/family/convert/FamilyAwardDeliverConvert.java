package fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert;

import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardResourceDeliverTypeEnum;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardResourceTypeEnum;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardTypeEnum;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilyAwardDeliverExecution;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilyAwardDeliverItem;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilyAwardDeliverRecord;
import fm.lizhi.ocean.wavecenter.datastore.award.family.entity.WcFamilyLevelAwardItem;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.param.CreateFamilyAwardDeliverExecutionParam;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.param.CreateFamilyAwardDeliverItemParam;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager.deliver.DeliverResourceParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.Validate;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR,
        imports = {
                FamilyAwardTypeEnum.class,
                FamilyAwardResourceDeliverTypeEnum.class,
                FamilyAwardResourceTypeEnum.class,
        }
)
public interface FamilyAwardDeliverConvert {

    FamilyAwardDeliverConvert I = Mappers.getMapper(FamilyAwardDeliverConvert.class);

    /**
     * 根据单一奖励项生成发放资源参数
     *
     * @param itemEntity     奖励项实体
     * @param familyId       公会ID
     * @param familyName     公会名称
     * @param familyUserId   公会长ID
     * @param familyUserName 公会长名称
     * @return 发放资源参数
     */
    @Mapping(target = "resourceAwardType", expression = "java(FamilyAwardTypeEnum.LEVEL)")
    @Mapping(target = "resourceDeliverType", source = "itemEntity", qualifiedByName = "itemEntityToResourceDeliverType")
    @Mapping(target = "resourceTypes", source = "itemEntity", qualifiedByName = "itemEntityToResourceTypes")
    DeliverResourceParam toDeliverResourceParam(
            WcFamilyLevelAwardItem itemEntity, long familyId, String familyName, long familyUserId, String familyUserName);

    @Named("itemEntityToResourceDeliverType")
    default FamilyAwardResourceDeliverTypeEnum itemEntityToResourceDeliverType(WcFamilyLevelAwardItem itemEntity) {
        FamilyAwardResourceTypeEnum resourceType = FamilyAwardResourceTypeEnum.fromValue(itemEntity.getResourceType());
        Validate.notNull(resourceType, "Invalid resourceType=%s", itemEntity.getResourceType());
        return resourceType.getDeliverType();
    }

    @Named("itemEntityToResourceTypes")
    default List<FamilyAwardResourceTypeEnum> itemEntityToResourceTypes(WcFamilyLevelAwardItem itemEntity) {
        FamilyAwardResourceTypeEnum resourceType = FamilyAwardResourceTypeEnum.fromValue(itemEntity.getResourceType());
        Validate.notNull(resourceType, "Invalid resourceType=%s", itemEntity.getResourceType());
        return Collections.singletonList(resourceType);
    }

    /**
     * 根据发放执行参数生成发放资源参数
     *
     * @param executionParam    发放执行参数
     * @param appId             应用ID
     * @param resourceAwardType 奖励类型
     * @param familyId          公会ID
     * @param familyName        公会名称
     * @param familyUserId      公会长ID
     * @param familyUserName    公会长名称
     * @return 发放资源参数
     */
    @Mapping(target = "resourceTypes", source = "executionParam", qualifiedByName = "executionParamToResourceTypes")
    DeliverResourceParam toDeliverResourceParam(
            CreateFamilyAwardDeliverExecutionParam executionParam, int appId, FamilyAwardTypeEnum resourceAwardType,
            long familyId, String familyName, long familyUserId, String familyUserName);

    @Named("executionParamToResourceTypes")
    default List<FamilyAwardResourceTypeEnum> executionParamToResourceTypes(CreateFamilyAwardDeliverExecutionParam executionParam) {
        ArrayList<FamilyAwardResourceTypeEnum> resourceTypes = new ArrayList<>();
        for (CreateFamilyAwardDeliverItemParam deliverItem : executionParam.getDeliverItems()) {
            resourceTypes.add(deliverItem.getResourceType());
        }
        return resourceTypes;
    }

    /**
     * 根据发放记录, 发放执行和发放项列表生成发放资源参数
     *
     * @param record    发放记录
     * @param execution 发放执行
     * @param items     发放项列表
     * @return 发放资源参数
     */
    @Mapping(target = "appId", source = "execution.appId")
    @Mapping(target = "resourceAwardType", source = "items", qualifiedByName = "firstItemResourceAwardType")
    @Mapping(target = "resourceDeliverType", expression = "java(FamilyAwardResourceDeliverTypeEnum.fromValue(execution.getResourceDeliverType()))")
    @Mapping(target = "resourceTypes", source = "items", qualifiedByName = "itemsToResourceTypes")
    DeliverResourceParam toDeliverResourceParam(
            WcFamilyAwardDeliverRecord record, WcFamilyAwardDeliverExecution execution, List<WcFamilyAwardDeliverItem> items);

    @Named("firstItemResourceAwardType")
    default FamilyAwardTypeEnum firstItemResourceAwardType(List<WcFamilyAwardDeliverItem> items) {
        if (CollectionUtils.isEmpty(items)) {
            return null;
        }
        FamilyAwardResourceTypeEnum resourceTYpe = FamilyAwardResourceTypeEnum.fromValue(items.get(0).getResourceType());
        return resourceTYpe != null ? resourceTYpe.getAwardType() : null;
    }

    @Named("itemsToResourceTypes")
    default List<FamilyAwardResourceTypeEnum> itemsToResourceTypes(List<WcFamilyAwardDeliverItem> items) {
        if (CollectionUtils.isEmpty(items)) {
            return Collections.emptyList();
        }
        ArrayList<FamilyAwardResourceTypeEnum> resourceTypes = new ArrayList<>(items.size());
        for (WcFamilyAwardDeliverItem item : items) {
            FamilyAwardResourceTypeEnum resourceType = FamilyAwardResourceTypeEnum.fromValue(item.getResourceType());
            resourceTypes.add(resourceType);
        }
        return resourceTypes;
    }
}
