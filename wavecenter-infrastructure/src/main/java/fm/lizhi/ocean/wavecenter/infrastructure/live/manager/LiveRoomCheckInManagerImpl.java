package fm.lizhi.ocean.wavecenter.infrastructure.live.manager;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import fm.lizhi.ocean.wavecenter.api.live.bean.*;
import fm.lizhi.ocean.wavecenter.api.live.constants.CheckInStatus;
import fm.lizhi.ocean.wavecenter.api.user.bean.RoomBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.*;
import fm.lizhi.ocean.wavecenter.infrastructure.live.convert.LiveRoomCheckConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.mapper.WaveCheckInRecordEntityMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.mapper.WaveRoomCheckInRecordMapper;
import fm.lizhi.ocean.wavecenter.service.live.manager.LiveRoomCheckInManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Component
@Deprecated
public class LiveRoomCheckInManagerImpl implements LiveRoomCheckInManager {


    @Autowired
    private CheckInDayRouter checkInDayRouter;

    @Autowired
    private WaveRoomCheckInRecordMapper waveRoomCheckInRecordMapper;
    @Autowired
    private CheckGroupManager checkGroupManager;
    @Autowired
    private WaveCheckInRecordEntityMapper waveCheckInRecordMapper;


    @Override
    public RoomDayCalendarRes roomCalendar(RoomDayCalendarReq req) {
        List<RoomDayCalendarEntity> dayCalendarEntities = checkInDayRouter.roomCalendar(req.getAppId(), req.getRoomId(), req.getStartDate(), req.getEndDate());

        BigDecimal sumIncome = new BigDecimal(0);
        long sumCharm = 0;
        int sumSeatOrder = 0;

        for (RoomDayCalendarEntity entity : dayCalendarEntities) {
            sumIncome = sumIncome.add(entity.getIncome());
            sumCharm += entity.getCharm();
            sumSeatOrder += entity.getSeatOrder();
        }

        RoomDayCalendarStatsRes.RoomDayCalendarStatsResBuilder builder = RoomDayCalendarStatsRes.builder().charm(sumCharm)
                .income(sumIncome.toString())
                .seatOrder(sumSeatOrder);

        Integer incomePlayerNum = waveCheckInRecordMapper.roomHourIncomePlayerSummary(req.getAppId(), req.getRoomId(), req.getStartDate(), req.getEndDate());
        builder.playerNumber(incomePlayerNum);

        List<RoomDayCalendarDetailRes> detailRes = dayCalendarEntities.stream().map(LiveRoomCheckConvert.I::convertRoomDayCalendarDetailRes).collect(Collectors.toList());

        return RoomDayCalendarRes.builder()
                .stats(builder.build())
                .detail(detailRes)
                .build();
//        return
    }

    @Override
    public RoomHourCheckDetailRes hourDetail(RoomHourCheckDetailReq req) {
        // 一次性查询出来
        List<WcCheckInRecord> checkInRecords = waveCheckInRecordMapper.hourDetail2(req.getAppId(), req.getRoomId(), req.getPlayerId(), req.getStartDate(), req.getEndDate());

        if (CollectionUtils.isEmpty(checkInRecords)) {
            return RoomHourCheckDetailRes.builder().build();
        }
        // 主持id
        Long hostId = checkInRecords.get(0).getHostId();
        UserBean hostUserBean = new UserBean();
        hostUserBean.setId(hostId);
        // 厅主信息
        Long roomId = checkInRecords.get(0).getNjId();
        RoomBean roomBean = new RoomBean();
        roomBean.setId(roomId);

        List<PlayerCheckInDetailRes> unCheckPlayer = new ArrayList<>();

        List<PlayerCheckInDetailRes> checkPlayer = new ArrayList<>();

        BigDecimal sumIncome = new BigDecimal(0);
        Long sumCharm = 0L;
        Integer playCheckNumber = 0;
        Integer seatOrder = 0;

        for (WcCheckInRecord checkInRecord : checkInRecords) {
            PlayerCheckInDetailRes.PlayerCheckInDetailResBuilder builder = PlayerCheckInDetailRes.builder();
            builder.seatOrder(checkInRecord.getStatus());
            builder.originalCharm(checkInRecord.getCharm());
            builder.charm(checkInRecord.getCharmValue());
            builder.income(String.valueOf(checkInRecord.getIncome()));
            builder.id(checkInRecord.getUserId());
            if (checkInRecord.getStatus() == CheckInStatus.UN_CHECKED.getValue()) {
                // 未打卡
                unCheckPlayer.add(builder.build());
            } else {
                playCheckNumber++;
                // 打卡
                checkPlayer.add(builder.build());
            }

            sumIncome = sumIncome.add(checkInRecord.getIncome());
            sumCharm += checkInRecord.getCharm();
            seatOrder += checkInRecord.getStatus();

        }
        RoomHourCheckDetailStatsRes stats = RoomHourCheckDetailStatsRes.builder()
                .charm(sumCharm)
                .income(String.valueOf(sumIncome))
                .playCheckNumber(playCheckNumber)
                .seatOrder(seatOrder)
                .build();

        checkPlayer.sort(Comparator.comparingLong(PlayerCheckInDetailRes::getCharm).reversed());
        unCheckPlayer.sort(Comparator.comparingLong(PlayerCheckInDetailRes::getCharm).reversed());

        return RoomHourCheckDetailRes.builder()
                .host(hostUserBean)
                .stats(stats)
                .room(roomBean)
                .remark(checkInRecords.get(0).getRemark())
                .checkPlayer(checkPlayer)
                .unCheckPlayer(unCheckPlayer)
                .build();
    }


    @Override
    public LRCSRoomDayStatsRes roomDayStats(RoomDayCheckStatsReq req) {

        Date rangeStart = MyDateUtil.getRangeStartDesc(req.getStartDate(), req.getEndDate(), req.getPageNo(), req.getPageSize());
        Date rangeEnd = MyDateUtil.getRangeEndDesc(req.getStartDate(), req.getEndDate(), req.getPageNo(), req.getPageSize());

        List<Long> userIds = checkInDayRouter.roomDayStatsUserId(req.getAppId(), req.getRoomId(), req.getUserId(), req.getStartDate(), req.getEndDate());

        if (CollectionUtils.isEmpty(userIds)) {
            return new LRCSRoomDayStatsRes();
        }
        List<Date> rangeDayDate = MyDateUtil.getRangeDayDate(req.getStartDate(), req.getEndDate(), req.getPageNo(), req.getPageSize());

        // 厅下面主播的  分页时间范围的 按天的 打卡情况
        List<RoomDayStatsDetailEntity> roomDayStatsDetail = checkInDayRouter.roomDayStatsDetail(req.getAppId(), req.getRoomId(), req.getUserId(), rangeStart, rangeEnd);

        //主播的 合计值
        Map<Long, Map<Date, RoomDayStatsDetailEntity>> userDayStatsDetailMap = roomDayStatsDetail.stream().collect(Collectors.groupingBy(RoomDayStatsDetailEntity::getUserId
                , Collectors.toMap(RoomDayStatsDetailEntity::getStatDate, e -> e, (existingValue, newValue) -> existingValue)));

        // 按天统计
        List<RoomDayStatsEntity> roomDayStats = checkInDayRouter.roomDayStats(req.getAppId(), req.getRoomId(), req.getUserId(), req.getStartDate(), req.getEndDate());
        Map<Long, RoomDayStatsEntity> userDayStatsMap = roomDayStats.stream().collect(Collectors.toMap(RoomDayStatsEntity::getUserId,
                roomDay -> roomDay,
                // 将重复的njId处理掉，保留第一个出现的值
                (existingValue, newValue) -> existingValue));

        List<RoomDayStatsRes> resList = new ArrayList<>();

        for (Long userId : userIds) {
            RoomDayStatsRes res = new RoomDayStatsRes();
            UserBean userBean = new UserBean();
            userBean.setId(userId);

            RoomDayStatsEntity stats = userDayStatsMap.getOrDefault(userId, RoomDayStatsEntity.builder().build());

            Map<Date, RoomDayStatsDetailEntity> dayStatsDetailEntityMap = userDayStatsDetailMap.getOrDefault(userId, new HashMap<>());
            List<RoomDayStatsDetailEntity> detailEntityList = new ArrayList<>();
            for (Date date : rangeDayDate) {
                RoomDayStatsDetailEntity detailEntity = dayStatsDetailEntityMap.getOrDefault(date, RoomDayStatsDetailEntity.builder().statDate(date).build());
                detailEntityList.add(detailEntity);
            }
            List<RoomDayDetail> details = detailEntityList.stream().map(LiveRoomCheckConvert.I::convertRoomDayDetail).sorted(Comparator.comparing(RoomDayDetail::getTime).reversed()).collect(Collectors.toList());

            res.setPlayer(userBean);
            res.setDetail(details);
            res.setStats(LiveRoomCheckConvert.I.convertRoomDayStats(stats));
            resList.add(res);
        }

        //排序
        //statsRes排序
        resList.sort((o1, o2) -> {
            if (o1 == null || o1.getStats() == null) {
                return 1;
            }
            if (o2 == null || o2.getStats() == null) {
                return -1;
            }
            BigDecimal income1 = o1.getStats().foundIncomeNum();
            BigDecimal income2 = o2.getStats().foundIncomeNum();
            return income1.compareTo(income2);
        });

        LRCSRoomDayStatsRes res = new LRCSRoomDayStatsRes();
        res.setList(resList);
        res.setTimeStats(checkGroupManager.groupTimeStats(resList));
        res.setTimeStatsSum(checkGroupManager.sumTimeStats(resList));
        return res;
    }

    @Override
    public RoomDayStatsSummaryRes roomDayStatsSummary(RoomDayCheckStatsReq req) {
        RoomDayStatsSummaryRes roomDayStatsSummaryRes = checkInDayRouter.roomDaySummary(req.getAppId(), req.getRoomId(), req.getStartDate(), req.getEndDate());
        if (roomDayStatsSummaryRes == null) {
            roomDayStatsSummaryRes = RoomDayStatsSummaryRes.builder().build();
        }
        return roomDayStatsSummaryRes;
    }

    @Override
    public LRCSRoomHourStatsRes roomHourStats(RoomHourCheckStatsReq req) {
        //主播的小时的所有的打卡明细, 如果查询缓慢，用njId查一遍roomId， 去命中索引
        List<WaveCheckInRecordEntity> checkInRecords = waveRoomCheckInRecordMapper.roomHourDetail(req.getAppId(),
                req.getRoomId(), req.getStartDate(), req.getEndDate());
        if (CollectionUtils.isEmpty(checkInRecords)) {
            return new LRCSRoomHourStatsRes();
        }

        List<Date> rangeHourDate = MyDateUtil.getRangeHourDate(req.getStartDate(), req.getEndDate());

        // 按天
        Map<Long, Map<Date, WaveCheckInRecordEntity>> dayGroupedRecordMap = checkInRecords.stream()
                .filter(v->v.getUserId() != null && v.getStartTime() != null)
                .collect(Collectors.groupingBy(
                        WaveCheckInRecordEntity::getUserId,
                        Collectors.toMap(WaveCheckInRecordEntity::getStartTime, e -> e, (existingValue, newValue) -> existingValue)));

        List<RoomHourStatsRes> statsRes = new ArrayList<>();
        for (Long userId : dayGroupedRecordMap.keySet()) {
            RoomHourStatsRes res = new RoomHourStatsRes();
            UserBean player = new UserBean();
            player.setId(userId);

            Map<Date, WaveCheckInRecordEntity> hourRecordMap = dayGroupedRecordMap.getOrDefault(userId, new HashMap<>());

            List<RoomHourDetail> details = new ArrayList<>();

            BigDecimal daySumInCome = new BigDecimal(0);
            long daySumCharm = 0;
            int daySumSeatOrder = 0;
            int daySumHostCnt = 0;
            for (Date date : rangeHourDate) {
                WaveCheckInRecordEntity checkInRecord = hourRecordMap.get(date);
                if (checkInRecord != null) {
                    daySumInCome = daySumInCome.add(checkInRecord.getIncome());
                    daySumCharm += checkInRecord.getCharmValue() != null ? checkInRecord.getCharmValue() : checkInRecord.getCharm();
                    if (checkInRecord.getStatus() != null && checkInRecord.getStatus() > 0) {
                        daySumSeatOrder++;
                    }
                    daySumHostCnt += checkInRecord.getIsHost();
                } else {
                    checkInRecord = WaveCheckInRecordEntity.builder().startTime(date).build();
                }
                details.add(getRoomHourDetail(checkInRecord));
            }

            RoomHourStats stats = new RoomHourStats();
            stats.setCharm(daySumCharm);
            stats.setIncome(daySumInCome.toString());
            stats.setSeatOrder(daySumSeatOrder);
            stats.setHostCnt(daySumHostCnt);

            res.setPlayer(player);
            res.setStats(stats);
            res.setDetail(details);
            statsRes.add(res);
        }

        statsRes.sort((o1, o2) -> {
            if (o1 == null || o1.getStats() == null) {
                return 1;
            }
            if (o2 == null || o2.getStats() == null) {
                return -1;
            }
            BigDecimal income1 = o1.getStats().foundIncomeNum();
            BigDecimal income2 = o2.getStats().foundIncomeNum();
            return income1.compareTo(income2);
        });

        LRCSRoomHourStatsRes res = new LRCSRoomHourStatsRes();
        res.setTotal(statsRes.size());
        res.setList(statsRes);
        res.setTimeStats(checkGroupManager.groupTimeStats(statsRes));
        res.setTimeStatsSum(checkGroupManager.sumTimeStats(statsRes));

        return res;
    }


    private RoomHourDetail getRoomHourDetail(WaveCheckInRecordEntity checkInRecord) {
        RoomHourDetail detail = new RoomHourDetail();

        detail.setTime(checkInRecord.getStartTime());
        detail.setStartDate(checkInRecord.getStartTime());
        detail.setEndDate(checkInRecord.getEndTime());
        if (checkInRecord.getIncome() != null) {
            detail.setIncome(String.valueOf(checkInRecord.getIncome()));
        }
        detail.setCharm(checkInRecord.getCharmValue() != null ? checkInRecord.getCharmValue() : checkInRecord.getCharm());
        detail.setHostCnt(checkInRecord.getIsHost());
        detail.setCheckStatus(checkInRecord.getStatus());
        detail.setRemark(checkInRecord.getRemark());
        return detail;
    }


    @Override
    public RoomHourStatsSummaryRes roomHourStatsSummary(RoomHourCheckStatsReq req) {
        // 小时汇总
        RoomHourSummaryEntity summaryEntity = waveCheckInRecordMapper.roomHourSummary(req.getAppId(), req.getRoomId(), req.getStartDate(), req.getEndDate());
        if (summaryEntity == null) {
            log.warn("roomHourStatsSummary summaryEntity is null req={}", JSONObject.toJSONString(req));
            return RoomHourStatsSummaryRes.builder().build();
        }
        RoomHourStatsSummaryRes.RoomHourStatsSummaryResBuilder builder = RoomHourStatsSummaryRes.builder();
        return builder.charm(summaryEntity.getCharm())
                .income(summaryEntity.getIncome().toString())
                .seatOrder(summaryEntity.getSeatOrder())
                .hostCnt(summaryEntity.getHostCnt()).build();
    }
}
