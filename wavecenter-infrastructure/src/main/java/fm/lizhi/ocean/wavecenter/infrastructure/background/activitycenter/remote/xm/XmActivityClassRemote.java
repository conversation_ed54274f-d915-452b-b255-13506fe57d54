package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.remote.xm;

import java.util.List;

import fm.lizhi.ocean.wavecenter.service.activitycenter.config.ActivityConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.constants.ActivityBigClassTypeMapping;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityBigClass;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityClassConfig;
import lombok.extern.slf4j.Slf4j;
import xm.fm.lizhi.live.pp.dto.officialseat.GetPrimaryActivityConfigListByIdsReq;
import xm.fm.lizhi.live.pp.dto.officialseat.GetPrimaryActivityConfigListByIdsResp;
import xm.fm.lizhi.live.pp.dto.officialseat.GetSecondActivityConfigListByIdsReq;
import xm.fm.lizhi.live.pp.dto.officialseat.GetSecondActivityConfigListByIdsResp;
import xm.fm.lizhi.live.pp.dto.officialseat.PrimaryActivityConfigDto;
import xm.fm.lizhi.live.pp.dto.officialseat.SecondaryActivityConfigDto;
import xm.fm.lizhi.live.pp.dto.officialseat.SyncSaveWavePrimaryActivityReq;
import xm.fm.lizhi.live.pp.dto.officialseat.SyncSaveWavePrimaryActivityResp;
import xm.fm.lizhi.live.pp.dto.officialseat.SyncSaveWaveSecondaryActivityReq;
import xm.fm.lizhi.live.pp.dto.officialseat.SyncSaveWaveSecondaryActivityResp;
import xm.fm.lizhi.live.pp.dto.officialseat.UpdatePrimaryActivityConfigStatusReq;
import xm.fm.lizhi.live.pp.dto.officialseat.UpdatePrimaryActivityConfigStatusResp;
import xm.fm.lizhi.live.pp.dto.officialseat.UpdateSecondaryActivityConfigStatusReq;
import xm.fm.lizhi.live.pp.dto.officialseat.UpdateSecondaryActivityConfigStatusResp;
import xm.fm.lizhi.live.pp.enums.officialseat.ActivityStatusEnum;
import xm.fm.lizhi.live.pp.services.OfficialSeatService;

@Slf4j
@Component
public class XmActivityClassRemote  {

   @Autowired
   private OfficialSeatService officialSeatService;

   @Autowired
   private ActivityConfig activityConfig;

   /**
    * 同步活动大类
    * @param entity 活动大类
    * @return 结果
    */
    public Result<Void> syncSaveBigClass(ActivityBigClass entity) {
        SyncSaveWavePrimaryActivityReq req = new SyncSaveWavePrimaryActivityReq();
        Integer bizType = ActivityBigClassTypeMapping.waveType2Biz(entity.getType());
        req.setId(entity.getId());
        req.setTitle(entity.getName());
        req.setType(bizType);
        req.setOperator(entity.getOperator());
        req.setStatus(ActivityStatusEnum.VALID.getStatus());
        req.setImageUrl(activityConfig.getXm().getXmPrimaryClassImgUrl());
        req.setWeight(entity.getWeight());
        Result<SyncSaveWavePrimaryActivityResp> result = officialSeatService.syncSaveWavePrimaryActivity(req);
        if (RpcResult.isFail(result)) {
            log.warn("同步活动大类失败, id: {}, name: {}, type: {}, bizType: {}, weight: {}", entity.getId(), entity.getName(), entity.getType(), bizType, entity.getWeight());
            return RpcResult.fail(result.rCode());
        }
       
        if(result.target().getCode() != 0) {
            return RpcResult.fail(Sync_Save_Big_Class_Fail, result.target().getMessage());
        }
        return RpcResult.success();
    }

    /**
     * 同步二级分类
     * @param entity 二级分类
     * @return 结果
     */
    public Result<Void> syncSaveClass(ActivityClassConfig entity) {
        SyncSaveWaveSecondaryActivityReq req = new SyncSaveWaveSecondaryActivityReq();
        req.setId(entity.getId());
        req.setPrimaryActivityId(entity.getBigClassId());
        req.setTitle(entity.getName());
        req.setWeight(entity.getWeight());
        req.setOperator(entity.getOperator());
        req.setStatus(ActivityStatusEnum.VALID.getStatus());
        req.setImageUrl(activityConfig.getXm().getXmSecondaryClassImgUrl());

        Result<SyncSaveWaveSecondaryActivityResp> result = officialSeatService.syncSaveWaveSecondaryActivity(req);
        if (RpcResult.isFail(result)) {
            log.warn("同步二级分类失败, id: {}, bigClassId: {}", entity.getId(), entity.getBigClassId());
            return RpcResult.fail(result.rCode());
        }

        if(result.target().getCode() != 0) {
            return RpcResult.fail(Sync_Class_Fail, result.target().getMessage());
        }
        return RpcResult.success();
    }

    /**
     * 删除一级分类
     * @param id 一级分类id
     * @param operator 操作人
     * @return 结果
     */
    public Result<Void> deleteBigClass(Long id, String operator) {
        UpdatePrimaryActivityConfigStatusReq req = new UpdatePrimaryActivityConfigStatusReq();
        req.setId(id);
        req.setStatus(ActivityStatusEnum.DELETE.getStatus());
        req.setOperator(operator);
        
        Result<UpdatePrimaryActivityConfigStatusResp> result = officialSeatService.updatePrimaryActivityConfigStatus(req);
        if (RpcResult.isFail(result)) {
            log.warn("删除一级分类失败, id: {}", id);
            return RpcResult.fail(result.rCode());
        }

        if(result.target().getCode() != 0) {
            return RpcResult.fail(Delete_Big_Class_Fail, result.target().getMessage());
        }
        return RpcResult.success();
    }

    /**
     * 删除二级分类
     * @param id 二级分类id
     * @param operator 操作人
     * @return 结果
     */
    public Result<Void> deleteClass(Long id, String operator) {
        UpdateSecondaryActivityConfigStatusReq req = new UpdateSecondaryActivityConfigStatusReq();
        req.setId(id);
        req.setStatus(ActivityStatusEnum.DELETE.getStatus());
        req.setOperator(operator);
        Result<UpdateSecondaryActivityConfigStatusResp> result = officialSeatService.updateSecondaryActivityConfigStatus(req);
        if (RpcResult.isFail(result)) {
            log.warn("删除二级分类失败, id: {}", id);
            return RpcResult.fail(result.rCode());
        }

        if(result.target().getCode() != 0) {
            return RpcResult.fail(Delete_Class_Fail, result.target().getMessage());
        }
        return RpcResult.success();
    }

    /**
     * 根据ID查询一级分类
     * @param ids 一级分类id
     * @return 一级分类
     */
    public Result<List<PrimaryActivityConfigDto>> getBigClassById(List<Long> ids) {
        GetPrimaryActivityConfigListByIdsReq req = new GetPrimaryActivityConfigListByIdsReq();
        req.setIds(ids);
        Result<GetPrimaryActivityConfigListByIdsResp> result = officialSeatService.getPrimaryActivityConfigListByIds(req);
        if (RpcResult.isFail(result)) {
            log.warn("查询一级分类失败, ids: {}", ids);
            return RpcResult.fail(result.rCode());
        }

        List<PrimaryActivityConfigDto> list = result.target().getList();
        return RpcResult.success(list);
    }

    /**
     * 根据ID查询二级分类
     * @param ids 二级分类id
     * @return 二级分类
     */
    public Result<List<SecondaryActivityConfigDto>> getClassById(List<Long> ids) {
        GetSecondActivityConfigListByIdsReq req = new GetSecondActivityConfigListByIdsReq();
        req.setIds(ids);
        Result<GetSecondActivityConfigListByIdsResp> result = officialSeatService.getSecondActivityConfigListByIds(req);
        if (RpcResult.isFail(result)) {
            log.warn("查询二级分类失败, ids: {}", ids);
            return RpcResult.fail(result.rCode());
        }

        List<SecondaryActivityConfigDto> list = result.target().getList();
        return RpcResult.success(list);
    }

    int Sync_Save_Big_Class_Fail = 1;
    int Sync_Class_Fail = 2;
    int Delete_Big_Class_Fail = 3;
    int Delete_Class_Fail = 4;
}
   
   
