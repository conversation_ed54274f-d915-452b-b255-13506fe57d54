package fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task;

import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.condition.AbstractCondition;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.condition.ConditionJudgeContext;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.condition.LogicSymbolEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * 条件组任务
 * 结果：通过或不通过
 * <AUTHOR>
 * @date 2025/6/5 16:32
 */
@Slf4j
public class ConditionGroupTask implements HaveResultTaskI<Boolean> {

    private final Long id;

    private final Long templateId;

    private final Integer appId;

    private final Long userId;

    private final List<AbstractCondition<?,?>> conditions;

    private final LogicSymbolEnum logicSymbol;

    private boolean isExecuteFinish = false;

    private Date executeFinishTime;

    /**
     * 条件组是否通过
     */
    private boolean conditionGroupResult;

    public ConditionGroupTask(Long id, Long templateId, Integer appId, Long userId, List<AbstractCondition<?, ?>> conditions, LogicSymbolEnum logicSymbol) {
        this.id = id;
        this.templateId = templateId;
        this.appId = appId;
        this.userId = userId;
        this.conditions = conditions;
        this.logicSymbol = logicSymbol;
    }

    @Override
    public void execute(TaskExecuteContext taskExecuteContext) {
        if (this.isExecuteFinish) {
            return;
        }

        if (CollectionUtils.isEmpty(this.conditions)) {
            noPass();
            return;
        }

        // AND初始为true，OR初始为false
        boolean finalResult = logicSymbol == LogicSymbolEnum.AND;

        for (int i = 0; i < conditions.size(); i++) {
            AbstractCondition<?,?> condition = conditions.get(i);
            boolean currentResult = condition.judge(ConditionJudgeContext.builder()
                    .taskExecuteContext(taskExecuteContext)
                    .build());
            log.debug("condition execute templateId={},i={},currentResult={}", templateId, i, currentResult);

            // AND逻辑遇到false立即返回
            if (logicSymbol == LogicSymbolEnum.AND && !currentResult) {
                noPass();
                return;
            }
            // OR逻辑遇到true立即返回
            if (logicSymbol == LogicSymbolEnum.OR && currentResult) {
                pass();
                return;
            }

            finalResult = logicSymbol == LogicSymbolEnum.AND ?
                    finalResult :
                    finalResult || currentResult;
        }

        if (finalResult) {
            pass();
        } else {
            noPass();
        }
    }

    private void noPass(){
        this.conditionGroupResult = false;
        finish();
    }

    private void pass(){
        this.conditionGroupResult = true;
        finish();
    }

    private void finish(){
        this.isExecuteFinish = true;
        this.executeFinishTime = new Date();
    }

    @Override
    public boolean isExecuteFinish() {
        return this.isExecuteFinish;
    }

    @Override
    public Date getExecuteFinishTime() {
        return this.executeFinishTime;
    }

    @Override
    public Boolean getResult() {
        return this.conditionGroupResult;
    }

    @Override
    public Long getId() {
        return this.id;
    }

    @Override
    public Long getTemplateId() {
        return this.templateId;
    }

    @Override
    public Integer getAppId() {
        return this.appId;
    }

    @Override
    public Long getUserId() {
        return this.userId;
    }
}
