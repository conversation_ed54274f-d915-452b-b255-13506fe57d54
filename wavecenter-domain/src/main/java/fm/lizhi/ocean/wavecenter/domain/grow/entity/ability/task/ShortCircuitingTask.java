package fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 任务链中其中一个任务通过即结束
 * <AUTHOR>
 * @date 2025/6/5 15:41
 */
@Slf4j
public class ShortCircuitingTask implements HaveResultTaskI<TaskInfoI> {

    private final Long id;

    /**
     * 完成的任务
     */
    private TaskInfoI circuitTask;

    /**
     * 任务链
     */
    private final List<HaveResultTaskI<Boolean>> tasks;

    private Date executeFinishTime;

    public ShortCircuitingTask(Long id) {
        this.id = id;
        this.tasks = new ArrayList<>();
    }

    /**
     * 添加后执行的任务
     * @param task
     */
    public void addLast(HaveResultTaskI<Boolean> task) {
        Assert.isTrue(!isExecuteFinish(), "ShortCircuitingTask isFinish");
        this.tasks.add(task);
    }

    @Override
    public void execute(TaskExecuteContext taskExecuteContext) {
        if (isExecuteFinish()) {
            return;
        }

        if (CollectionUtils.isEmpty(this.tasks)) {
            return;
        }

        // 先按照顺序执行
        for (int i = 0; i < this.tasks.size(); i++) {
            HaveResultTaskI<Boolean> task = this.tasks.get(i);
            task.execute(taskExecuteContext);
            if (!task.isExecuteFinish()) {
                continue;
            }
            Boolean result = task.getResult();
            log.debug("shortCircuitingTask templateId={},taskId={},result={}", task.getTemplateId(), task.getId(), result);
            if (result != null && result) {
                finish(task);
                break;
            }
        }

        // 如果都没有完成，则默认完成最后一个
        if (!isExecuteFinish()) {
            HaveResultTaskI<Boolean> task = this.tasks.get(this.tasks.size() - 1);
            finish(task);
        }
    }

    private void finish(TaskInfoI circuitTask){
        this.circuitTask = circuitTask;
        this.executeFinishTime = new Date();
    }

    @Override
    public boolean isExecuteFinish() {
        return this.circuitTask != null;
    }

    @Override
    public Date getExecuteFinishTime() {
        return this.executeFinishTime;
    }

    @Override
    public TaskInfoI getResult() {
        return this.circuitTask;
    }

    @Override
    public Long getId() {
        return this.id;
    }

    @Override
    public Long getTemplateId() {
        return 0L;
    }

    @Override
    public Integer getAppId() {
        return 0;
    }

    @Override
    public Long getUserId() {
        return 0L;
    }
}
