package fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.condition.number;


import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.condition.ComparatorEnum;

import java.math.BigDecimal;

/**
 * 小于
 * <AUTHOR>
 * @date 2025/2/14 18:26
 */
public class NumberLtCondition extends AbstractNumberCondition {

    public NumberLtCondition(String contextAttribute, BigDecimal rightValue) {
        super(contextAttribute, ComparatorEnum.NUM_LT, rightValue);
    }

    @Override
    protected boolean doCompare(BigDecimal leftValue, BigDecimal rightValue) {
        return leftValue.compareTo(rightValue) < 0;
    }
}
