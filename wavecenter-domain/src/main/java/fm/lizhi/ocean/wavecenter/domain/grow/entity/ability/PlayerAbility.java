package fm.lizhi.ocean.wavecenter.domain.grow.entity.ability;

import fm.lizhi.ocean.wavecenter.domain.grow.entity.Period;
import lombok.Getter;
import org.apache.commons.collections4.MapUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 主播能力
 * <AUTHOR>
 * @date 2025/6/9 11:39
 */
@Getter
public class PlayerAbility {

    private Long id;

    private Integer appId;

    private Long playerId;

    /**
     * 各能力项分数
     */
    private Map<String, BigDecimal> capabilityScore;

    /**
     * 上周能力分值
     */
    private Map<String, BigDecimal> prePeriodScore;

    /**
     * 与上周期的比值
     */
    private Map<String, BigDecimal> prePeriodScoreCompare;

    /**
     * 周期
     */
    private Period period;

    public PlayerAbility(Long id, Integer appId, Long playerId, Period period) {
        this.id = id;
        this.appId = appId;
        this.playerId = playerId;
        this.period = period;
        this.capabilityScore = new HashMap<>();
        this.prePeriodScore = new HashMap<>();
        this.prePeriodScoreCompare = new HashMap<>();
    }

    /**
     * 添加能力项分数
     * @param capabilityCode
     * @param capabilityScore
     */
    public void addCapabilityScore(String capabilityCode, BigDecimal capabilityScore) {
        if (this.capabilityScore == null) {
            this.capabilityScore = new HashMap<>();
        }
        this.capabilityScore.put(capabilityCode, capabilityScore);
    }

    /**
     * 计算综合能力分
     * @return
     */
    public BigDecimal settleCompositeScore(){
        if (MapUtils.isEmpty(this.capabilityScore)) {
            return BigDecimal.ZERO;
        }
        return this.capabilityScore.values().stream().reduce(BigDecimal::add).get();
    }

    /**
     * 对比上周期的能力
     * @param prePeriodPlayerAbility
     */
    public void comparePrePeriodScore(PlayerAbility prePeriodPlayerAbility) {
        if (prePeriodPlayerAbility == null) {
            return;
        }

        Map<String, BigDecimal> preCapabilityMap = prePeriodPlayerAbility.getCapabilityScore();
        if (MapUtils.isEmpty(preCapabilityMap)) {
            return;
        }

        // 上周分值
        this.prePeriodScore.putAll(preCapabilityMap);

        if (MapUtils.isEmpty(this.capabilityScore)) {
            return;
        }

        for (Map.Entry<String, BigDecimal> entry : this.capabilityScore.entrySet()) {
            BigDecimal preScore = preCapabilityMap.get(entry.getKey());
            if (preScore == null) {
                continue;
            }
            this.prePeriodScoreCompare.put(entry.getKey(), entry.getValue().subtract(preScore));
        }
    }

}
