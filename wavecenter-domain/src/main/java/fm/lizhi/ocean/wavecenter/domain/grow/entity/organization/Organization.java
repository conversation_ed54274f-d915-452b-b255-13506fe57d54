package fm.lizhi.ocean.wavecenter.domain.grow.entity.organization;

import fm.lizhi.ocean.wavecenter.domain.grow.entity.Level;

/**
 * 成长体系的组织
 * <AUTHOR>
 * @date 2025/3/19 18:53
 */
public interface Organization<L extends Level> {

    Long getId();


    Integer getAppId();

    /**
     * 当前等级
     * @return
     */
    L getLevel();

    /**
     * 当前经验值
     * @return
     */
    Integer getExp();

}
