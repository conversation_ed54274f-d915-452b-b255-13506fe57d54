package fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.condition;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/6 19:15
 */
public enum LogicSymbolEnum {

    AND("AND"),

    OR("OR");

    @Getter
    private String value;

    LogicSymbolEnum(String value) {
        this.value = value;
    }

    public static LogicSymbolEnum getByValue(String value) {
        for (LogicSymbolEnum logicSymbolEnum : LogicSymbolEnum.values()) {
            if (logicSymbolEnum.value.equals(value)) {
                return logicSymbolEnum;
            }
        }
        return null;
    }

}
