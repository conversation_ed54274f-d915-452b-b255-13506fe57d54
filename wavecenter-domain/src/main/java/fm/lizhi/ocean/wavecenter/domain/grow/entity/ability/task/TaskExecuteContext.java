package fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task;

import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/6 19:28
 */
@Getter
@Builder
public class TaskExecuteContext {

    private PlayerSupportMetric playerSupportMetric;

    public static class TaskExecuteContextBuilder {

        public TaskExecuteContext build() {
            if (this.playerSupportMetric == null) {
                this.playerSupportMetric = PlayerSupportMetric.builder().build();
            }
            return new TaskExecuteContext(this.playerSupportMetric);
        }

    }

}
