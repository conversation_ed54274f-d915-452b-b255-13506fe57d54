package fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.condition;


/**
 * 条件对象头像类
 * 泛型为value的类型
 * <AUTHOR>
 * @date 2025/2/14 18:28
 */
public abstract class AbstractCondition<L, R> {

    /**
     * 左值
     */
    protected L leftValue;

    protected ComparatorEnum comparator;

    /**
     * 右值
     */
    protected R rightValue;

    protected AbstractCondition(L leftValue, ComparatorEnum comparator, R rightValue) {
        this.leftValue = leftValue;
        this.comparator = comparator;
        this.rightValue = rightValue;
    }

    /**
     * 进行条件判断
     * @param context
     * @return
     */
    public abstract boolean judge(ConditionJudgeContext context);

}
