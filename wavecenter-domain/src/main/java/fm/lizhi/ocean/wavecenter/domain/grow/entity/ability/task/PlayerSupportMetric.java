package fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task;

import lombok.Builder;
import lombok.Getter;

import java.math.BigDecimal;

/**
 * 主播支持的指标
 * <AUTHOR>
 * @date 2025/6/9 15:48
 */
@Getter
@Builder
public class PlayerSupportMetric {

    /**
     * 私信人数
     */
    private Integer chatUserCnt;

    /**
     * 私信回复人数(有效私信用户)
     */
    private Integer replyChatUserCnt;

    /**
     * 私信回复新用户人数(有效私信新用户数)
     */
    private Integer replyChatNewUserCnt;

    /**
     * 送礼人数(付费用户数)
     */
    private Integer giftUserCnt;

    /**
     * 送礼新用户人数(付费新用户数)
     */
    private Integer giftNewUserCnt;

    /**
     * 总收入
     */
    private BigDecimal allIncome;

    /**
     * 上麦时长(分钟)
     */
    private BigDecimal upGuestDur;

    /**
     * 新增粉丝数(累计新增粉丝)
     */
    private Integer newFansUserCnt;

    /**
     * 违规次数
     */
    private Integer violationCnt;

    /**
     * 有效麦序数
     */
    private Integer checkInCnt;

}
