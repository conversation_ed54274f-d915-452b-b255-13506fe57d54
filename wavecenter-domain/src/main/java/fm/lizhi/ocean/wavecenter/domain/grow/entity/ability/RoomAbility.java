package fm.lizhi.ocean.wavecenter.domain.grow.entity.ability;

import fm.lizhi.ocean.wavecenter.domain.grow.entity.Period;
import lombok.Getter;
import org.apache.commons.collections4.MapUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 厅能力
 * <AUTHOR>
 * @date 2025/6/9 11:39
 */
@Getter
public class RoomAbility {

    private Long id;

    private Integer appId;

    private Long roomId;

    /**
     * 各能力项分数
     */
    private Map<String, BigDecimal> capabilityScore;

    /**
     * 上周能力分值
     */
    private Map<String, BigDecimal> prePeriodScore;

    /**
     * 与上周期的比值
     */
    private Map<String, BigDecimal> prePeriodScoreCompare;

    /**
     * 周期
     */
    private Period period;

    /**
     * 主播数
     */
    private Integer playerCount;

    public RoomAbility(Long id, Integer appId, Long roomId, Period period, Integer playerCount) {
        this.id = id;
        this.appId = appId;
        this.roomId = roomId;
        this.period = period;
        this.playerCount = playerCount;
        this.capabilityScore = new HashMap<>();
        this.prePeriodScore = new HashMap<>();
        this.prePeriodScoreCompare = new HashMap<>();
    }

    public RoomAbility(Long id, Integer appId, Long roomId, Period period, Integer playerCount, Map<String, BigDecimal> capabilityScore) {
        this.id = id;
        this.appId = appId;
        this.roomId = roomId;
        this.period = period;
        this.playerCount = playerCount;
        this.capabilityScore = capabilityScore;
        this.prePeriodScore = new HashMap<>();
        this.prePeriodScoreCompare = new HashMap<>();
    }

    /**
     * 添加能力分
     * @param capabilityCode
     * @param capabilityScores
     */
    public void addCapabilityScore(String capabilityCode, List<BigDecimal> capabilityScores) {
        BigDecimal capabilityAvgScore = this.capabilityScore.get(capabilityCode);
        if (capabilityAvgScore == null) {
            BigDecimal added = capabilityScores.stream().reduce(BigDecimal::add).get();
            this.capabilityScore.put(capabilityCode, settleCapabilityScore(added));
        } else {
            // 原始值
            BigDecimal multiply = capabilityAvgScore.multiply(new BigDecimal(this.playerCount));
            BigDecimal added = multiply.add(capabilityScores.stream().reduce(BigDecimal::add).get());
            this.capabilityScore.put(capabilityCode, settleCapabilityScore(added));
        }
    }

    /**
     * 添加能力分
     * @param capabilityScores key=能力项code value=主播能力项值列表
     */
    public void addCapabilityScore(Map<String, List<BigDecimal>> capabilityScores) {
        for (Map.Entry<String, List<BigDecimal>> entry : capabilityScores.entrySet()) {
            this.addCapabilityScore(entry.getKey(), entry.getValue());
        }
    }

    private BigDecimal settleCapabilityScore(BigDecimal sumScore) {
        return sumScore.divide(new BigDecimal(this.playerCount), 1, RoundingMode.HALF_UP);
    }

    /**
     * 计算综合能力分
     * @return
     */
    public BigDecimal settleCompositeScore(){
        if (MapUtils.isEmpty(this.capabilityScore)) {
            return BigDecimal.ZERO;
        }
        return this.capabilityScore.values().stream().reduce(BigDecimal::add).get();
    }

    /**
     * 对比上周期的能力
     * @param prePeriodAbility
     */
    public void comparePrePeriodScore(RoomAbility prePeriodAbility) {
        if (prePeriodAbility == null) {
            return;
        }

        Map<String, BigDecimal> preCapabilityMap = prePeriodAbility.getCapabilityScore();
        if (MapUtils.isEmpty(preCapabilityMap)) {
            return;
        }

        // 上周分值
        this.prePeriodScore.putAll(preCapabilityMap);
        if (MapUtils.isEmpty(this.capabilityScore)) {
            return;
        }

        for (Map.Entry<String, BigDecimal> entry : this.capabilityScore.entrySet()) {
            BigDecimal preScore = preCapabilityMap.get(entry.getKey());
            if (preScore == null) {
                continue;
            }
            this.prePeriodScoreCompare.put(entry.getKey(), entry.getValue().subtract(preScore));
        }
    }

}
