package fm.lizhi.ocean.wavecenter.domain.grow.entity;

import lombok.Getter;
import org.springframework.util.Assert;

import java.util.Date;

/**
 * 周期
 * <AUTHOR>
 * @date 2025/3/20 09:54
 */
@Getter
public class Period {

    private final Date start;

    private final Date end;

    public Period(Date start, Date end) {
        Assert.notNull(start, "start is null");
        Assert.notNull(end, "start is null");
        this.start = start;
        this.end = end;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + Long.hashCode(start.getTime());
        result = prime * result + Long.hashCode(end.getTime());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Period period = (Period) obj;
        return period.getStart().equals(this.start) && period.getEnd().equals(this.end);
    }

}
