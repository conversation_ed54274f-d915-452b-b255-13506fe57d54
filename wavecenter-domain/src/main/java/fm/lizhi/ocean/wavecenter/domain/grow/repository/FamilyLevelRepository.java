package fm.lizhi.ocean.wavecenter.domain.grow.repository;

import fm.lizhi.ocean.wavecenter.domain.grow.entity.FamilyLevel;
import fm.lizhi.ocean.wavecenter.domain.system.Visitor;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/3/18 17:52
 */
public interface FamilyLevelRepository {

    /**
     * 保存公会等级
     * @param familyLevel
     * @param visitor
     */
    void save(FamilyLevel familyLevel, Visitor visitor);

    /**
     * 获取业务存在的公会等级
     * @param appId
     * @return
     */
    List<FamilyLevel> getAppFamilyLevels(Integer appId);

    /**
     * 获取等级
     * @param appId
     * @param levelId
     * @return
     */
    Optional<FamilyLevel> getLevel(Integer appId, Long levelId);

}
