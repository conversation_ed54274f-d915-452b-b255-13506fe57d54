package fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.condition;


import com.alibaba.fastjson.JSONPath;

/**
 * 具有从上下文解析左值能力的条件, 左值会解析为Object类型
 * <AUTHOR>
 * @date 2025/2/14 18:28
 */
public abstract class AbstractParseContextLeftObjectValueCondition<R> extends AbstractCondition<Object, R> {

    /**
     * 上下文属性字段名称
     */
    protected String contextAttribute;

    protected AbstractParseContextLeftObjectValueCondition(String contextAttribute, ComparatorEnum comparator, R rightValue) {
        super(null, comparator, rightValue);
        this.contextAttribute = contextAttribute;
    }

    protected abstract boolean doJudge(Object leftValue, ComparatorEnum comparator, R rightValue);

    @Override
    public boolean judge(ConditionJudgeContext context) {
        // jsonPath 解析
        if (super.leftValue == null) {
            super.leftValue = JSONPath.eval(context.getTaskExecuteContext().getPlayerSupportMetric(), "$." + this.contextAttribute);
        }
        return doJudge(super.leftValue, super.comparator, super.rightValue);
    }
}
