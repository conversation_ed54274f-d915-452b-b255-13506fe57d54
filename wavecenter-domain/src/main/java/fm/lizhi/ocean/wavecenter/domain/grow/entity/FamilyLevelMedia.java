package fm.lizhi.ocean.wavecenter.domain.grow.entity;

import lombok.Builder;
import lombok.Getter;

import java.util.List;

/**
 * 公会等级媒体资源 值对象
 * <AUTHOR>
 * @date 2025/3/18 17:43
 */
@Getter
@Builder(toBuilder = true)
public class FamilyLevelMedia {

    /**
     * 角标
     */
    private String iconUrl;

    /**
     * 勋章图
     */
    private String medalUrl;

    /**
     * 主题色
     */
    private String themColor;

    /**
     * 底色
     */
    private String backgroundColor;

    /**
     * 奖励宣传图
     */
    private List<String> awardImgs;

}
