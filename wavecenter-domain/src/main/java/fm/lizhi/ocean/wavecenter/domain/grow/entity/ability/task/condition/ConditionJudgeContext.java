package fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.condition;

import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.TaskExecuteContext;
import lombok.Builder;
import lombok.Getter;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @date 2025/6/9 10:11
 */
@Getter
@Builder
public class ConditionJudgeContext {

    private TaskExecuteContext taskExecuteContext;

    public static class ConditionJudgeContextBuilder {
        public ConditionJudgeContext build() {
            Assert.notNull(this.taskExecuteContext, "taskExecuteContext is null");
            return new ConditionJudgeContext(this.taskExecuteContext);
        }
    }

}
