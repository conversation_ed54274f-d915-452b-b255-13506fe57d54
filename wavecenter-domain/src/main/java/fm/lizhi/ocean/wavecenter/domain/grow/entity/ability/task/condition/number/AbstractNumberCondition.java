package fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.condition.number;


import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.condition.AbstractParseContextLeftObjectValueCondition;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.condition.ComparatorEnum;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;

/**
 * 数值对比条件
 * <AUTHOR>
 * @date 2025/2/26 15:18
 */
public abstract class AbstractNumberCondition extends AbstractParseContextLeftObjectValueCondition<BigDecimal> {

    protected AbstractNumberCondition(String contextAttribute, ComparatorEnum comparator, BigDecimal rightValue) {
        super(contextAttribute, comparator, rightValue);
    }

    protected abstract boolean doCompare(BigDecimal leftValue, BigDecimal rightValue);

    @Override
    protected boolean doJudge(Object leftValue, ComparatorEnum comparator, BigDecimal rightValue) {
        String attrValueStr = String.valueOf(leftValue);
        if (!NumberUtils.isCreatable(attrValueStr)) {
            return false;
        }

        BigDecimal attrValue = new BigDecimal(attrValueStr);
        return doCompare(attrValue, rightValue);
    }
}
