package fm.lizhi.ocean.wavecenter.domain.eventBus;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/3/20 07:55
 */
@Component
public class EventBusHolder {

    private static EventBus eventBus = new LogEventBus();

    @Autowired(required = false)
    public EventBusHolder(EventBus eventBus) {
        if (eventBus != null) {
            EventBusHolder.eventBus = eventBus;
        }
    }

    public static EventBus get() {
        return eventBus;
    }
}
