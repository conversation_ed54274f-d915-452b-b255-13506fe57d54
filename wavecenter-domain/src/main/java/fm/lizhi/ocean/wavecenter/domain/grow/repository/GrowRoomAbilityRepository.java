package fm.lizhi.ocean.wavecenter.domain.grow.repository;

import fm.lizhi.ocean.wavecenter.domain.grow.entity.Period;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.RoomAbility;

/**
 * <AUTHOR>
 * @date 2025/6/9 20:43
 */
public interface GrowRoomAbilityRepository {

    /**
     * 查询厅周期的能力
     * @param roomId
     * @param period
     * @return
     */
    RoomAbility getRoomAbility(Long roomId, Period period);

    /**
     * 保存厅能力
     * @param roomAbility
     */
    void saveRoomAbility(RoomAbility roomAbility);

}
