package fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/9 10:21
 */
public interface TaskInfoI {

    /**
     * 任务ID
     * @return
     */
    Long getId();

    /**
     * 任务模板ID
     * @return
     */
    Long getTemplateId();

    /**
     * 应用ID
     * @return
     */
    Integer getAppId();

    /**
     * 任务所属用户ID
     * @return
     */
    Long getUserId();

    /**
     * 任务完成时间
     * @return
     */
    Date getExecuteFinishTime();

}
