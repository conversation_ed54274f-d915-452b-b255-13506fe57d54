package fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.condition.number;


import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.condition.ComparatorEnum;

import java.math.BigDecimal;

/**
 * 大于
 * <AUTHOR>
 * @date 2025/2/14 18:26
 */
public class NumberGtCondition extends AbstractNumberCondition {

    public NumberGtCondition(String contextAttribute, BigDecimal rightValue) {
        super(contextAttribute, ComparatorEnum.NUM_GT, rightValue);
    }

    @Override
    protected boolean doCompare(BigDecimal leftValue, BigDecimal rightValue) {
        return leftValue.compareTo(rightValue) > 0;
    }
}
