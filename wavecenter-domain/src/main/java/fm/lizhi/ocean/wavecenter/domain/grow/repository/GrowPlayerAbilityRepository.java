package fm.lizhi.ocean.wavecenter.domain.grow.repository;

import fm.lizhi.ocean.wavecenter.domain.grow.entity.Period;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.PlayerAbility;

/**
 * <AUTHOR>
 * @date 2025/6/9 20:43
 */
public interface GrowPlayerAbilityRepository {

    /**
     * 查询主播周期的能力
     * @param playerId
     * @param period
     * @return
     */
    PlayerAbility getPlayerAbility(Long playerId, Period period);

    /**
     * 保存主播能力
     * @param playerAbility
     */
    void savePlayerAbility(PlayerAbility playerAbility);

}
