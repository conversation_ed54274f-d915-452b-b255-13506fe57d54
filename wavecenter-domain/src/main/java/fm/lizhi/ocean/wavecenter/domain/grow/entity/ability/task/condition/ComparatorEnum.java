package fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.condition;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/6 19:16
 */
public enum ComparatorEnum {

    NUM_EQ("=")
    , NUM_GT(">")
    , NUM_LT("<")
    , NUM_GE(">=")
    , NUM_LE("<=")
    , NUM_NE("!=")
    ;

    @Getter
    private String value;

    ComparatorEnum(String value) {
        this.value = value;
    }

    public static ComparatorEnum getByValue(String value) {
        for (ComparatorEnum comparatorEnum : ComparatorEnum.values()) {
            if (comparatorEnum.value.equals(value)) {
                return comparatorEnum;
            }
        }
        return null;
    }
}
