package fm.lizhi.ocean.wavecenter.domain.grow.task;

import com.google.common.collect.Lists;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.ConditionGroupTask;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.TaskExecuteContext;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.condition.AbstractCondition;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.condition.ComparatorEnum;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.condition.ConditionJudgeContext;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.condition.LogicSymbolEnum;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2025/6/11 17:11
 */
public class ConditionGroupTaskTest {

    @Test
    public void testAnd1() {

        // and关系, 第一个条件为false时，后面的条件不执行，且返回结果为false
        ConditionGroupTask conditionGroupTask = new ConditionGroupTask(1L, 1L, 1, 1L
                , Lists.newArrayList(buildCondition(false)
                        , new AbstractCondition<Integer, Integer>(1, ComparatorEnum.NUM_EQ, 2) {
                            @Override
                            public boolean judge(ConditionJudgeContext context) {
                                throw new RuntimeException("can not judge");
                            }
                        }
                )
                , LogicSymbolEnum.AND
        );

        conditionGroupTask.execute(TaskExecuteContext.builder().build());
        Assert.assertTrue(conditionGroupTask.isExecuteFinish());
        Assert.assertFalse(conditionGroupTask.getResult());
    }

    @Test
    public void testAnd2() {

        // and关系, 第一个条件为true时，第二个为true, 返回结果为true
        ConditionGroupTask conditionGroupTask = new ConditionGroupTask(1L, 1L, 1, 1L
                , Lists.newArrayList(buildCondition(true)
                        , buildCondition(true)
                )
                , LogicSymbolEnum.AND
        );

        conditionGroupTask.execute(TaskExecuteContext.builder().build());
        Assert.assertTrue(conditionGroupTask.isExecuteFinish());
        Assert.assertTrue(conditionGroupTask.getResult());

    }

    @Test
    public void testAnd3() {

        // and关系, 第一个条件为true时，第二个为false, 返回结果为false
        ConditionGroupTask conditionGroupTask = new ConditionGroupTask(1L, 1L, 1, 1L
                , Lists.newArrayList(buildCondition(true)
                , buildCondition(false)
                , buildCondition(true)
        )
                , LogicSymbolEnum.AND
        );

        conditionGroupTask.execute(TaskExecuteContext.builder().build());
        Assert.assertTrue(conditionGroupTask.isExecuteFinish());
        Assert.assertFalse(conditionGroupTask.getResult());

    }

    @Test
    public void testOr1() {
        // or关系, 第一个条件为true时，后面的条件不执行，且返回结果为true
        ConditionGroupTask conditionGroupTask = new ConditionGroupTask(1L, 1L, 1, 1L
                , Lists.newArrayList(buildCondition(true)
                , new AbstractCondition<Integer, Integer>(1, ComparatorEnum.NUM_EQ, 2) {
                    @Override
                    public boolean judge(ConditionJudgeContext context) {
                        throw new RuntimeException("can not judge");
                    }
                }
        )
                , LogicSymbolEnum.OR
        );

        conditionGroupTask.execute(TaskExecuteContext.builder().build());
        Assert.assertTrue(conditionGroupTask.isExecuteFinish());
        Assert.assertTrue(conditionGroupTask.getResult());
    }

    @Test
    public void testOr2() {

        // or关系, 第一个条件为false时，第二个为true, 返回结果为true
        ConditionGroupTask conditionGroupTask = new ConditionGroupTask(1L, 1L, 1, 1L
                , Lists.newArrayList(buildCondition(false)
                , buildCondition(true)
        )
                , LogicSymbolEnum.OR
        );

        conditionGroupTask.execute(TaskExecuteContext.builder().build());
        Assert.assertTrue(conditionGroupTask.isExecuteFinish());
        Assert.assertTrue(conditionGroupTask.getResult());

    }

    @Test
    public void testOr3() {

        // or关系, 第一个条件为false时，第二个为false, 返回结果为false
        ConditionGroupTask conditionGroupTask = new ConditionGroupTask(1L, 1L, 1, 1L
                , Lists.newArrayList(buildCondition(false)
                , buildCondition(false)
                , buildCondition(false)
        )
                , LogicSymbolEnum.OR
        );

        conditionGroupTask.execute(TaskExecuteContext.builder().build());
        Assert.assertTrue(conditionGroupTask.isExecuteFinish());
        Assert.assertFalse(conditionGroupTask.getResult());

    }

    private AbstractCondition<?, ?> buildCondition(boolean result) {
        return new AbstractCondition<Integer, Integer>(1, ComparatorEnum.NUM_EQ, 2) {
            @Override
            public boolean judge(ConditionJudgeContext context) {
                return result;
            }
        };
    }

}
