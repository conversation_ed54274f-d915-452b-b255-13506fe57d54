package fm.lizhi.ocean.wavecenter.domain.grow.task;

import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.PlayerSupportMetric;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.TaskExecuteContext;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.condition.ConditionJudgeContext;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.condition.number.*;
import org.junit.Assert;
import org.junit.Test;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/11 17:37
 */
public class NumberConditionTest {

    @Test
    public void testEqParser1() {
        // 表达式能解析到值
        NumberEqCondition condition = new NumberEqCondition("allIncome", new BigDecimal(100));
        boolean judge = condition.judge(ConditionJudgeContext.builder()
                .taskExecuteContext(TaskExecuteContext.builder()
                        .playerSupportMetric(PlayerSupportMetric.builder()
                                .allIncome(new BigDecimal(100))
                                .build())
                        .build())
                .build());
        Assert.assertTrue(judge);

    }

    @Test
    public void testEqParser2() {
        // 解析不到值
        NumberEqCondition condition = new NumberEqCondition("allIncome", new BigDecimal(100));
        boolean judge = condition.judge(ConditionJudgeContext.builder()
                .taskExecuteContext(TaskExecuteContext.builder()
                        .playerSupportMetric(PlayerSupportMetric.builder()
                                .build())
                        .build())
                .build());
        Assert.assertFalse(judge);
    }

    @Test
    public void testEqParser3() {
        // 字段不存在
        NumberEqCondition condition = new NumberEqCondition("ttttt", new BigDecimal(100));
        boolean judge = condition.judge(ConditionJudgeContext.builder()
                .taskExecuteContext(TaskExecuteContext.builder()
                        .playerSupportMetric(PlayerSupportMetric.builder()
                                .build())
                        .build())
                .build());
        Assert.assertFalse(judge);

    }

    @Test
    public void testEqParser4() {
        // 类型不一致
        NumberEqCondition condition = new NumberEqCondition("chatUserCnt", new BigDecimal(100));
        boolean judge = condition.judge(ConditionJudgeContext.builder()
                .taskExecuteContext(TaskExecuteContext.builder()
                        .playerSupportMetric(PlayerSupportMetric.builder()
                                .chatUserCnt(100)
                                .build())
                        .build())
                .build());
        Assert.assertTrue(judge);
    }

    @Test
    public void testEq1() {
        NumberEqCondition condition = new NumberEqCondition("allIncome", new BigDecimal(100));
        boolean judge = condition.judge(ConditionJudgeContext.builder()
                .taskExecuteContext(TaskExecuteContext.builder()
                        .playerSupportMetric(PlayerSupportMetric.builder()
                                .allIncome(new BigDecimal(101))
                                .build())
                        .build())
                .build());
        Assert.assertFalse(judge);
    }

    @Test
    public void testNe1() {
        NumberNeCondition condition = new NumberNeCondition("allIncome", new BigDecimal(100));
        boolean judge = condition.judge(ConditionJudgeContext.builder()
                .taskExecuteContext(TaskExecuteContext.builder()
                        .playerSupportMetric(PlayerSupportMetric.builder()
                                .allIncome(new BigDecimal(101))
                                .build())
                        .build())
                .build());
        Assert.assertTrue(judge);
    }

    @Test
    public void testNe2() {
        NumberNeCondition condition = new NumberNeCondition("allIncome", new BigDecimal(100));
        boolean judge = condition.judge(ConditionJudgeContext.builder()
                .taskExecuteContext(TaskExecuteContext.builder()
                        .playerSupportMetric(PlayerSupportMetric.builder()
                                .allIncome(new BigDecimal(100))
                                .build())
                        .build())
                .build());
        Assert.assertFalse(judge);
    }

    @Test
    public void testGe() {
        Assert.assertTrue(new NumberGeCondition("allIncome", new BigDecimal(100)).judge(ConditionJudgeContext.builder()
                .taskExecuteContext(TaskExecuteContext.builder()
                        .playerSupportMetric(PlayerSupportMetric.builder()
                                .allIncome(new BigDecimal(100))
                                .build())
                        .build())
                .build()));
        Assert.assertTrue(new NumberGeCondition("allIncome", new BigDecimal(100)).judge(ConditionJudgeContext.builder()
                .taskExecuteContext(TaskExecuteContext.builder()
                        .playerSupportMetric(PlayerSupportMetric.builder()
                                .allIncome(new BigDecimal(101))
                                .build())
                        .build())
                .build()));
        Assert.assertFalse(new NumberGeCondition("allIncome", new BigDecimal(100)).judge(ConditionJudgeContext.builder()
                .taskExecuteContext(TaskExecuteContext.builder()
                        .playerSupportMetric(PlayerSupportMetric.builder()
                                .allIncome(new BigDecimal(99))
                                .build())
                        .build())
                .build()));
    }

    @Test
    public void testGt(){
        Assert.assertTrue(new NumberGtCondition("allIncome", new BigDecimal(100)).judge(ConditionJudgeContext.builder()
                .taskExecuteContext(TaskExecuteContext.builder()
                        .playerSupportMetric(PlayerSupportMetric.builder()
                                .allIncome(new BigDecimal(101))
                                .build())
                        .build())
                .build()));
        Assert.assertFalse(new NumberGtCondition("allIncome", new BigDecimal(100)).judge(ConditionJudgeContext.builder()
                .taskExecuteContext(TaskExecuteContext.builder()
                        .playerSupportMetric(PlayerSupportMetric.builder()
                                .allIncome(new BigDecimal(100))
                                .build())
                        .build())
                .build()));
        Assert.assertFalse(new NumberGtCondition("allIncome", new BigDecimal(100)).judge(ConditionJudgeContext.builder()
                .taskExecuteContext(TaskExecuteContext.builder()
                        .playerSupportMetric(PlayerSupportMetric.builder()
                                .allIncome(new BigDecimal(99))
                                .build())
                        .build())
                .build()));
    }

    @Test
    public void testLe(){
        Assert.assertTrue(new NumberLeCondition("allIncome", new BigDecimal(100)).judge(ConditionJudgeContext.builder()
                .taskExecuteContext(TaskExecuteContext.builder()
                        .playerSupportMetric(PlayerSupportMetric.builder()
                                .allIncome(new BigDecimal(100))
                                .build())
                        .build())
                .build()));
        Assert.assertFalse(new NumberLeCondition("allIncome", new BigDecimal(100)).judge(ConditionJudgeContext.builder()
                .taskExecuteContext(TaskExecuteContext.builder()
                        .playerSupportMetric(PlayerSupportMetric.builder()
                                .allIncome(new BigDecimal(101))
                                .build())
                        .build())
                .build()));
        Assert.assertTrue(new NumberLeCondition("allIncome", new BigDecimal(100)).judge(ConditionJudgeContext.builder()
                .taskExecuteContext(TaskExecuteContext.builder()
                        .playerSupportMetric(PlayerSupportMetric.builder()
                                .allIncome(new BigDecimal(99))
                                .build())
                        .build())
                .build()));
    }

    @Test
    public void testLt(){
        Assert.assertFalse(new NumberLtCondition("allIncome", new BigDecimal(100)).judge(ConditionJudgeContext.builder()
                .taskExecuteContext(TaskExecuteContext.builder()
                        .playerSupportMetric(PlayerSupportMetric.builder()
                                .allIncome(new BigDecimal(100))
                                .build())
                        .build())
                .build()));
        Assert.assertFalse(new NumberLtCondition("allIncome", new BigDecimal(100)).judge(ConditionJudgeContext.builder()
                .taskExecuteContext(TaskExecuteContext.builder()
                        .playerSupportMetric(PlayerSupportMetric.builder()
                                .allIncome(new BigDecimal(101))
                                .build())
                        .build())
                .build()));
        Assert.assertTrue(new NumberLtCondition("allIncome", new BigDecimal(100)).judge(ConditionJudgeContext.builder()
                .taskExecuteContext(TaskExecuteContext.builder()
                        .playerSupportMetric(PlayerSupportMetric.builder()
                                .allIncome(new BigDecimal(99))
                                .build())
                        .build())
                .build()));
    }

}
