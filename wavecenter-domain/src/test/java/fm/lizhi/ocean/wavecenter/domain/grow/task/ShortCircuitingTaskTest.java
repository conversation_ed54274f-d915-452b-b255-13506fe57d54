package fm.lizhi.ocean.wavecenter.domain.grow.task;

import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.HaveResultTaskI;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.ShortCircuitingTask;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.TaskExecuteContext;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.TaskInfoI;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/11 16:32
 */
@Slf4j
public class ShortCircuitingTaskTest {

    /**
     * 3个任务, 测试执行到到第二个就退出
     */
    @Test
    public void test(){
        ShortCircuitingTask shortCircuitingTask = new ShortCircuitingTask(1L);
        shortCircuitingTask.addLast(new HaveResultTaskI<Boolean>() {
            @Override
            public Boolean getResult() {
                return false;
            }

            @Override
            public void execute(TaskExecuteContext taskExecuteContext) {
                log.info(getId()+" execute");
            }

            @Override
            public boolean isExecuteFinish() {
                return true;
            }

            @Override
            public Long getId() {
                return 3L;
            }

            @Override
            public Long getTemplateId() {
                return 0L;
            }

            @Override
            public Integer getAppId() {
                return 0;
            }

            @Override
            public Long getUserId() {
                return 0L;
            }

            @Override
            public Date getExecuteFinishTime() {
                return null;
            }
        });
        shortCircuitingTask.addLast(new HaveResultTaskI<Boolean>() {
            @Override
            public Boolean getResult() {
                return true;
            }

            @Override
            public void execute(TaskExecuteContext taskExecuteContext) {
                log.info(getId()+" execute");
            }

            @Override
            public boolean isExecuteFinish() {
                return true;
            }

            @Override
            public Long getId() {
                return 2L;
            }

            @Override
            public Long getTemplateId() {
                return 0L;
            }

            @Override
            public Integer getAppId() {
                return 0;
            }

            @Override
            public Long getUserId() {
                return 0L;
            }

            @Override
            public Date getExecuteFinishTime() {
                return null;
            }
        });
        shortCircuitingTask.addLast(new HaveResultTaskI<Boolean>() {
            @Override
            public Boolean getResult() {
                return false;
            }

            @Override
            public void execute(TaskExecuteContext taskExecuteContext) {
                throw new RuntimeException(getId()+"can not execute");
            }

            @Override
            public boolean isExecuteFinish() {
                return true;
            }

            @Override
            public Long getId() {
                return 1L;
            }

            @Override
            public Long getTemplateId() {
                return 0L;
            }

            @Override
            public Integer getAppId() {
                return 0;
            }

            @Override
            public Long getUserId() {
                return 0L;
            }

            @Override
            public Date getExecuteFinishTime() {
                return null;
            }
        });

        shortCircuitingTask.execute(TaskExecuteContext.builder().build());
        Assert.assertTrue(shortCircuitingTask.isExecuteFinish());

        TaskInfoI result = shortCircuitingTask.getResult();
        Long id = result.getId();
        Assert.assertEquals(2L, id.longValue());
    }

    /**
     * 3个任务, 所有任务都不通过，则最后一个默认完成
     */
    @Test
    public void test2(){
        ShortCircuitingTask shortCircuitingTask = new ShortCircuitingTask(1L);
        shortCircuitingTask.addLast(new HaveResultTaskI<Boolean>() {
            @Override
            public Boolean getResult() {
                return false;
            }

            @Override
            public void execute(TaskExecuteContext taskExecuteContext) {
                log.info(getId()+" execute");
            }

            @Override
            public boolean isExecuteFinish() {
                return true;
            }

            @Override
            public Long getId() {
                return 3L;
            }

            @Override
            public Long getTemplateId() {
                return 0L;
            }

            @Override
            public Integer getAppId() {
                return 0;
            }

            @Override
            public Long getUserId() {
                return 0L;
            }

            @Override
            public Date getExecuteFinishTime() {
                return null;
            }
        });
        shortCircuitingTask.addLast(new HaveResultTaskI<Boolean>() {
            @Override
            public Boolean getResult() {
                return false;
            }

            @Override
            public void execute(TaskExecuteContext taskExecuteContext) {
                log.info(getId()+" execute");
            }

            @Override
            public boolean isExecuteFinish() {
                return true;
            }

            @Override
            public Long getId() {
                return 2L;
            }

            @Override
            public Long getTemplateId() {
                return 0L;
            }

            @Override
            public Integer getAppId() {
                return 0;
            }

            @Override
            public Long getUserId() {
                return 0L;
            }

            @Override
            public Date getExecuteFinishTime() {
                return null;
            }
        });
        shortCircuitingTask.addLast(new HaveResultTaskI<Boolean>() {
            @Override
            public Boolean getResult() {
                return false;
            }

            @Override
            public void execute(TaskExecuteContext taskExecuteContext) {
                log.info(getId()+" execute");
            }

            @Override
            public boolean isExecuteFinish() {
                return true;
            }

            @Override
            public Long getId() {
                return 1L;
            }

            @Override
            public Long getTemplateId() {
                return 0L;
            }

            @Override
            public Integer getAppId() {
                return 0;
            }

            @Override
            public Long getUserId() {
                return 0L;
            }

            @Override
            public Date getExecuteFinishTime() {
                return null;
            }
        });

        shortCircuitingTask.execute(TaskExecuteContext.builder().build());
        Assert.assertTrue(shortCircuitingTask.isExecuteFinish());

        TaskInfoI result = shortCircuitingTask.getResult();
        Long id = result.getId();
        Assert.assertEquals(1L, id.longValue());
    }

    /**
     * 3个任务, 执行到最后一个通过
     */
    @Test
    public void test3(){
        ShortCircuitingTask shortCircuitingTask = new ShortCircuitingTask(1L);
        shortCircuitingTask.addLast(new HaveResultTaskI<Boolean>() {
            @Override
            public Boolean getResult() {
                return false;
            }

            @Override
            public void execute(TaskExecuteContext taskExecuteContext) {
                log.info(getId()+" execute");
            }

            @Override
            public boolean isExecuteFinish() {
                return true;
            }

            @Override
            public Long getId() {
                return 3L;
            }

            @Override
            public Long getTemplateId() {
                return 0L;
            }

            @Override
            public Integer getAppId() {
                return 0;
            }

            @Override
            public Long getUserId() {
                return 0L;
            }

            @Override
            public Date getExecuteFinishTime() {
                return null;
            }
        });
        shortCircuitingTask.addLast(new HaveResultTaskI<Boolean>() {
            @Override
            public Boolean getResult() {
                return false;
            }

            @Override
            public void execute(TaskExecuteContext taskExecuteContext) {
                log.info(getId()+" execute");
            }

            @Override
            public boolean isExecuteFinish() {
                return true;
            }

            @Override
            public Long getId() {
                return 2L;
            }

            @Override
            public Long getTemplateId() {
                return 0L;
            }

            @Override
            public Integer getAppId() {
                return 0;
            }

            @Override
            public Long getUserId() {
                return 0L;
            }

            @Override
            public Date getExecuteFinishTime() {
                return null;
            }
        });
        shortCircuitingTask.addLast(new HaveResultTaskI<Boolean>() {
            @Override
            public Boolean getResult() {
                return true;
            }

            @Override
            public void execute(TaskExecuteContext taskExecuteContext) {
                log.info(getId()+" execute");
            }

            @Override
            public boolean isExecuteFinish() {
                return true;
            }

            @Override
            public Long getId() {
                return 1L;
            }

            @Override
            public Long getTemplateId() {
                return 0L;
            }

            @Override
            public Integer getAppId() {
                return 0;
            }

            @Override
            public Long getUserId() {
                return 0L;
            }

            @Override
            public Date getExecuteFinishTime() {
                return null;
            }
        });

        shortCircuitingTask.execute(TaskExecuteContext.builder().build());
        Assert.assertTrue(shortCircuitingTask.isExecuteFinish());

        TaskInfoI result = shortCircuitingTask.getResult();
        Long id = result.getId();
        Assert.assertEquals(1L, id.longValue());
    }

}
