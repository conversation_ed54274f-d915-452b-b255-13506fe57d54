package fm.lizhi.ocean.wavecenter.common.validation;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.validation.*;

import java.util.List;

/**
 * Bean校验器
 */
@Component
public class BeanValidator {

    /**
     * spring的校验器, 使用该接口而不是javax的校验器
     */
    @Autowired
    private Validator validator;

    /**
     * 校验bean, 默认bean的名称为空白
     *
     * @param bean 待校验的bean
     * @return 校验结果
     */
    public ValidateResult validate(Object bean) {
        return validate(bean, StringUtils.EMPTY);
    }

    /**
     * 校验bean
     *
     * @param bean            待校验的bean
     * @param objectName      bean的名称
     * @param validationHints 校验注解的groups属性
     * @return 校验结果
     */
    public ValidateResult validate(Object bean, String objectName, Object... validationHints) {
        if (bean == null) {
            return ValidateResult.objectError(objectName, "bean is null");
        }
        BeanPropertyBindingResult errors = new BeanPropertyBindingResult(bean, objectName);
        if (validator instanceof SmartValidator && validationHints.length > 0) {
            ((SmartValidator) validator).validate(bean, errors, validationHints);
        } else {
            validator.validate(bean, errors);
        }
        List<FieldError> fieldErrors = errors.getFieldErrors();
        if (CollectionUtils.isNotEmpty(fieldErrors)) {
            FieldError fieldError = fieldErrors.get(0);
            return ValidateResult.fieldError(fieldError.getField(), fieldError.getDefaultMessage());
        }
        List<ObjectError> globalErrors = errors.getGlobalErrors();
        if (CollectionUtils.isNotEmpty(globalErrors)) {
            ObjectError objectError = globalErrors.get(0);
            return ValidateResult.objectError(objectError.getObjectName(), objectError.getDefaultMessage());
        }
        return ValidateResult.valid();
    }
}
