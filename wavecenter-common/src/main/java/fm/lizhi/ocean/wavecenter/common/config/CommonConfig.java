package fm.lizhi.ocean.wavecenter.common.config;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.config.AbsBizConfig;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @date 2024/4/10 16:36
 */
@Data
@ConfigurationProperties(prefix = "wavecenter-common")
public class CommonConfig extends AbsBizConfig<BizCommonConfig> {

    private PpBizCommonConfig pp;

    private HyBizCommonConfig hy;

    private XmBizCommonConfig xm;

    /**
     * 送礼消费幂等记录过期时间
     */
    private Integer consumeIdempotent = 60 * 60 * 24 * 7;

    private String consumeIdempotentTag = "first";

    private String evaluateTag = "20240614";

    private String romeFsDownloadCdn = "http://romefs.yfxn.lizhi.fm";

    private String romeFsCdn = "http://romefs.yfxn.lizhi.fm";

    private String romeFsDownloadIntranetCdn = "http://romefs.yfxn.lizhi.fm";

    /**
     * 跳转页面的action
     */
    private String actionWebJson = "{\"type\":\"6\",\"url\":\"#{url}\",\"wk\":true,\"isFull\":true,\"isLight\":true,\"urlShareable\":false}";


    /**
     * 转存操作最大重试次数
     */
    private Integer uploadTransferMaxTryCount = 3;

    public CommonConfig() {
        PpBizCommonConfig ppBizCommonConfig = new PpBizCommonConfig();
        this.pp = ppBizCommonConfig;

        HyBizCommonConfig hyBizCommonConfig = new HyBizCommonConfig();
        this.hy = hyBizCommonConfig;

        XmBizCommonConfig xmBizCommonConfig = new XmBizCommonConfig();
        this.xm = xmBizCommonConfig;

        bizConfigMap.put(BusinessEvnEnum.XIMI.appId(), xmBizCommonConfig);
        bizConfigMap.put(BusinessEvnEnum.HEI_YE.appId(), hyBizCommonConfig);
        bizConfigMap.put(BusinessEvnEnum.PP.appId(), ppBizCommonConfig);
    }
}
