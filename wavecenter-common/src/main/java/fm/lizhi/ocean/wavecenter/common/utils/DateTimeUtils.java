package fm.lizhi.ocean.wavecenter.common.utils;

import fm.lizhi.ocean.wavecenter.common.dto.DateDTO;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.*;

public class DateTimeUtils {

    /**
     * 将日期格式化为字符串
     *
     * @param date   日期
     * @param patten 格式
     * @return 结果
     */
    public static Date formatTimeToDate(Date date, String patten) {
        if (date == null) {
            return null;
        }
        // 创建SimpleDateFormat对象并设置日期格式
        SimpleDateFormat sdf = new SimpleDateFormat(patten);
        // 将日期格式化为字符串
        String formattedDateString = sdf.format(date);
        // 将字符串解析回Date对象
        try {
            return sdf.parse(formattedDateString);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 计算给定日期是当年的第几周。
     *
     * @param date 当前日期，类型为 Date
     * @return 返回给定日期是今年的第几周
     */
    public static int getWeekOfYear(Date date) {
        // 将 Date 对象转换为 LocalDate
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        // 使用默认地区的Locale来创建WeekFields实例
        WeekFields weekFields = WeekFields.of(Locale.getDefault());

        return localDate.get(weekFields.weekOfYear());
    }

    /**
     * 将时间段拆分为30分钟间隔的列表。
     *
     * @param start 开始时间
     * @param end   结束时间
     * @return 结果列表
     */
    public static List<DateDTO> divideTimeSlots(Date start, Date end) {
        List<DateDTO> timeSlots = new ArrayList<>();
        long currentTime = start.getTime();
        long endTime = end.getTime();
        // 30 minutes in milliseconds
        final long halfHourMillis = 30 * 60 * 1000;

        while (currentTime < endTime) {
            long nextTime = currentTime + halfHourMillis;
            if (nextTime > endTime) {
                nextTime = endTime;
            }

            DateDTO dateDTO = new DateDTO().setStartTime(new Date(currentTime)).setEndTime(new Date(nextTime));
            timeSlots.add(dateDTO);
            currentTime = nextTime;
        }
        return timeSlots;
    }

    /**
     * 获取上一周的开始时间（上周一）
     *
     * @return 上周一的 Date 对象
     */
    public static Date getLastWeekStartTime() {
        // 获取当前日期
        LocalDate today = LocalDate.now();
        // 计算上周一的日期
        LocalDate lastWeekStart = today.minusWeeks(1).with(DayOfWeek.MONDAY);
        // 转换为 Date 对象
        return Date.from(lastWeekStart.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取当前时间的下一个小时的整30分或者00分
     *
     * @param startTime 开始时间
     * @return 结果
     */
    public static Date getNextHourRoundTime(Date startTime, int nextHour) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);

        // 加1小时
        calendar.add(Calendar.HOUR_OF_DAY, nextHour);

        int minute = calendar.get(Calendar.MINUTE);

        // 如果分钟小于30，向上取整到30分
        // 如果分钟大于等于30，向上取整到下一个整点
        if (minute < 30) {
            calendar.set(Calendar.MINUTE, 30);
        } else {
            calendar.add(Calendar.HOUR_OF_DAY, 1);
            calendar.set(Calendar.MINUTE, 0);
        }

        // 清除秒和毫秒
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return calendar.getTime();
    }

    /**
     * 判断给定日期是否为周一的开始时间（00:00:00.000）
     *
     * @param date 日期
     * @return 是否为周一的开始时间
     */
    public static boolean isMondayStartTime(Date date) {
        if (date == null) {
            return false;
        }
        LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        return isMondayStartTime(localDateTime);
    }

    /**
     * 判断给定日期是否为周一的开始时间（00:00:00.000）
     *
     * @param localDateTime 日期
     * @return 是否为周一的开始时间
     */
    public static boolean isMondayStartTime(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return false;
        }
        return localDateTime.getDayOfWeek() == DayOfWeek.MONDAY
                && localDateTime.getHour() == 0
                && localDateTime.getMinute() == 0
                && localDateTime.getSecond() == 0
                && localDateTime.getNano() == 0;
    }

    /**
     * 判断给定时间戳是否为周一的开始时间（00:00:00.000）
     *
     * @param millis 时间戳
     * @return 是否为周一的开始时间
     */
    public static boolean isMondayStartTime(Long millis) {
        if (millis == null) {
            return false;
        }
        LocalDateTime localDateTime = Instant.ofEpochMilli(millis).atZone(ZoneId.systemDefault()).toLocalDateTime();
        return isMondayStartTime(localDateTime);
    }

    /**
     * 判断给定日期是否为周日的结束时间（23:59:59.999）
     *
     * @param date 日期
     * @return 是否为周日的结束时间
     */
    public static boolean isSundayEndTime(Date date) {
        if (date == null) {
            return false;
        }
        LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        return isSundayEndTime(localDateTime);
    }

    /**
     * 判断给定日期是否为周日的结束时间（23:59:59.999）
     *
     * @param localDateTime 日期
     * @return 是否为周日的结束时间
     */
    public static boolean isSundayEndTime(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return false;
        }
        return localDateTime.getDayOfWeek() == DayOfWeek.SUNDAY
                && localDateTime.getHour() == 23
                && localDateTime.getMinute() == 59
                && localDateTime.getSecond() == 59
                && localDateTime.getNano() == 999000000;
    }

    /**
     * 判断给定时间戳是否为周日的结束时间（23:59:59.999）
     *
     * @param millis 时间戳
     * @return 是否为周日的结束时间
     */
    public static boolean isSundayEndTime(Long millis) {
        if (millis == null) {
            return false;
        }
        LocalDateTime localDateTime = Instant.ofEpochMilli(millis).atZone(ZoneId.systemDefault()).toLocalDateTime();
        return isSundayEndTime(localDateTime);
    }

    /**
     * 判断两个日期是否同一周的开始结束, 第一个日期是周一的开始时间, 第二个日期是周日的结束时间
     *
     * @param first  第一个日期
     * @param second 第二个日期
     * @return 是否同一周的开始结束
     */
    public static boolean isSameWeekStartEnd(Date first, Date second) {
        if (!isMondayStartTime(first)) {
            return false;
        }
        if (!isSundayEndTime(second)) {
            return false;
        }
        return second.getTime() - first.getTime() == 7 * 24 * 60 * 60 * 1000L - 1;
    }

    /**
     * 判断两个日期是否同一周的开始结束, 第一个日期是周一的开始时间, 第二个日期是周日的结束时间
     *
     * @param first  第一个日期
     * @param second 第二个日期
     * @return 是否同一周的开始结束
     */
    public static boolean isSameWeekStartEnd(Long first, Long second) {
        if (!isMondayStartTime(first)) {
            return false;
        }
        if (!isSundayEndTime(second)) {
            return false;
        }
        return second - first == 7 * 24 * 60 * 60 * 1000L - 1;
    }

    /**
     * 获取上一周周一的开始时间
     *
     * @return 上一周周一的开始时间
     */
    public static Date getLastWeekMondayStartTime() {
        LocalDate today = LocalDate.now();
        LocalDate lastWeekMonday;
        if (today.getDayOfWeek() == DayOfWeek.MONDAY) {
            lastWeekMonday = today.with(TemporalAdjusters.previous(DayOfWeek.MONDAY));
        } else {
            lastWeekMonday = today.with(TemporalAdjusters.previous(DayOfWeek.MONDAY))
                    .with(TemporalAdjusters.previous(DayOfWeek.MONDAY));
        }
        return Date.from(lastWeekMonday.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取给定日期的同一周周日的结束时间
     *
     * @param date 给定日期
     * @return 同一周周日的结束时间
     */
    public static Date getSameWeekSundayEndTime(Date date) {
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().toLocalDate();
        LocalDate sameWeekSunday;
        if (localDate.getDayOfWeek() == DayOfWeek.SUNDAY) {
            sameWeekSunday = localDate;
        } else {
            sameWeekSunday = localDate.with(TemporalAdjusters.next(DayOfWeek.SUNDAY));
        }
        LocalDateTime sameWeekSundayEndTime = sameWeekSunday.atTime(23, 59, 59, 999000000);
        return Date.from(sameWeekSundayEndTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取给定日期的开始时间（00:00:00.000）
     *
     * @param date 给定日期
     * @return 给定日期的开始时间（00:00:00.000）
     */
    public static Date getStartTimeOfDay(Date date) {
        LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime startTimeOfDay = localDateTime.withHour(0).withMinute(0).withSecond(0).withNano(0);
        return Date.from(startTimeOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 基于"yyyy-MM-dd"格式解析文本并返回Date
     *
     * @param text 文本
     * @return Date对象
     */
    public static Date parseDate(String text) {
        LocalDate localDate = LocalDate.parse(text, DateTimeFormatter.ISO_LOCAL_DATE);
        Instant instant = localDate.atStartOfDay(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }

    /**
     * 从给定日期中加上指定周数
     *
     * @param date  给定日期
     * @param weeks 要加上的周数
     * @return 加上指定周数后的Date对象
     */
    public static Date plusWeeks(Date date, int weeks) {
        LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        return Date.from(localDateTime.plusWeeks(weeks).atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 从给定日期中加上一周
     *
     * @param date 给定日期
     * @return 加上一周后的Date对象
     */
    public static Date plus1Week(Date date) {
        return plusWeeks(date, 1);
    }

    /**
     * 从给定日期中减去指定周数
     *
     * @param date  给定日期
     * @param weeks 要减去的周数
     * @return 减去指定周数后的Date对象
     */
    public static Date minusWeeks(Date date, int weeks) {
        LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        return Date.from(localDateTime.minusWeeks(weeks).atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 从给定日期中减去一周
     *
     * @param date 给定日期
     * @return 减去一周后的Date对象
     */
    public static Date minus1Week(Date date) {
        return minusWeeks(date, 1);
    }
}