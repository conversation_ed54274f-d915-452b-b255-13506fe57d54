package fm.lizhi.ocean.wavecenter.common.utils;

import com.google.common.collect.Lists;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/2 18:15
 */
@Slf4j
public class BizClusterRouter {

    private final List<BusinessEvnEnum> registry;

    private final int shardingCnt;

    private final int shardingId;

    public BizClusterRouter(int shardingCnt, int shardingId) {
        if (shardingCnt <= 0) {
            throw new IllegalArgumentException("shardingCnt must be greater than 0");
        }
        if (shardingId < 0) {
            throw new IllegalArgumentException("shardingId must be greater than or equal to 0");
        }
        this.registry = Lists.newArrayList(BusinessEvnEnum.PP, BusinessEvnEnum.XIMI, BusinessEvnEnum.HEI_YE);
        this.shardingCnt = shardingCnt;
        this.shardingId = shardingId;
    }

    /**
     * 判断当前分片是否应路由到目标业务环境。
     * <p>
     * 路由逻辑基于分片总数(shardingCnt)、当前分片ID(shardingId)和目标环境在注册表中的位置(index)：
     * 1. 当分片总数 >= 注册环境数量时：分片ID取模分片总数等于目标索引时命中
     * 2. 当分片总数 < 注册环境数量时：分片ID取模分片总数等于目标索引对(注册表大小-1)取模时命中
     *
     * @param targetBusinessEvnEnum 目标业务环境枚举值
     * @return true 表示当前分片应路由到目标环境，false 表示不路由
     */
    public boolean route(BusinessEvnEnum targetBusinessEvnEnum) {
        if(shardingCnt == 1) {
            return true;
        }
        // 获取目标环境在注册表中的索引位置
        int targetIndex = registry.indexOf(targetBusinessEvnEnum);

        // 处理目标环境不在注册表中的情况
        if (targetIndex == -1) {
            log.warn("Target environment not found in registry: {}", targetBusinessEvnEnum);
            return false;
        }
        //取出可靠的分片索引(0,1,2,....)
        int shardingIndex = shardingId % shardingCnt;

        // 情况1：分片总数 >= 注册环境数量
        if (shardingCnt >= registry.size()) {
            // 集群数量大于注册业务数量 分片ID和注册表索引一致则命中
            if (shardingIndex == targetIndex) {
                log.info("sharding route greater than registry. shardingCnt={},shardingId={},shardingIndex={},index={}", shardingCnt, shardingId, shardingIndex, targetIndex);
                return true;
            }
            log.info("sharding route greater than registry. no pass. shardingCnt={},shardingId={},shardingIndex={},index={}", shardingCnt, shardingId, shardingIndex, targetIndex);
            return false;
        }

        // 情况2：分片总数 < 注册环境数量
        // 将超出分片数量的任务，按分片数量取模进行分配(分摊)
        // eg: shardingId为[0,1], 注册表[0,1,2,3,4,5,6,....]
        //     注册表中超出(overSize)的部分[3,4,5,6,..], 将均匀摊给shardingId[0,1]
        int shardingIndexIncludeOverSize = targetIndex % shardingCnt;

        if (shardingIndex == shardingIndexIncludeOverSize) {
            // 补充缺失的 index 参数
            log.info("sharding route less than registry. shardingCnt={},shardingId={},shardingIndexIncludeOverSize={},shardingIndex={},index={}", shardingCnt, shardingId, shardingIndexIncludeOverSize, shardingIndex, targetIndex);
            return true;
        }

        log.info("sharding route less than registry. shardingCnt={},shardingId={},shardingIndexIncludeOverSize={},shardingIndex={},index={}", shardingCnt, shardingId, shardingIndexIncludeOverSize, shardingIndex, targetIndex);
        return false;
    }

}
