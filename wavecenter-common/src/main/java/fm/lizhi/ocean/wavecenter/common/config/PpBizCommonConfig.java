package fm.lizhi.ocean.wavecenter.common.config;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.utils.PlaceholderUtils;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/4/10 16:39
 */
@Data
public class PpBizCommonConfig implements BizCommonConfig {

    private String cdnHost = "https://cdnoffice.lizhi.fm";

    private String riskAppId = "ppyw";

    private String auditSceneName = "{\"1160003\":\"-\",\"1160002\":\"-\",\"1130001\":\"私信消息\",\"1160004\":\"私信消息\",\"1160018\":\"-\",\"1160009\":\"-\",\"1160005\":\"-\",\"1160011\":\"-\",\"1160017\":\"-\",\"1160008\":\"-\",\"1160016\":\"-\"}";

    private long sendChatUid = 5064908973115913772L;

    private RomeConfig romeConfig = new RomeConfig(String.valueOf(BusinessEvnEnum.PP.getAppId()), "http://romefs.yfxn.lizhi.fm/");

    /**
     * 资源转存路径
     */
    private String resourceTransferUrlPrefix = "/publicTransfer/${businessName}/${fileName}";

    @Override
    public String getResourceTransferUrlPrefix(String fileName) {
        return PlaceholderUtils.replace(
                resourceTransferUrlPrefix,
                "businessName", BusinessEvnEnum.PP.getName(),
                "fileName", fileName
        );
    }
}
