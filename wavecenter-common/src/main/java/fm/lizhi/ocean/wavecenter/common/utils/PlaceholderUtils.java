package fm.lizhi.ocean.wavecenter.common.utils;

import org.apache.commons.lang3.Validate;
import org.springframework.util.PropertyPlaceholderHelper;

import java.util.Properties;

/**
 * 占位符工具类
 */
public class PlaceholderUtils {

    /**
     * 根据占位符模式和参数替换占位符. 占位符的格式为{@code ${key}}, 当key不存在时, 会抛出异常;
     * 带默认值的占位符的格式为{@code ${key:defaultValue}}
     *
     * @param pattern 占位符模式
     * @param args    占位符参数, 奇数为key, 偶数为value
     * @return 替换后的字符串
     */
    public static String replace(String pattern, String... args) {
        Validate.isTrue(args.length % 2 == 0, "args length must be even");
        Properties properties = new Properties();
        for (int i = 0; i < args.length; i += 2) {
            String key = args[i];
            Validate.notNull(key, "key must not be null but args[%d] is null", i);
            String value = args[i + 1];
            Validate.notNull(value, "value must not be null but args[%d] is null", i + 1);
            properties.setProperty(key, value);
        }
        PropertyPlaceholderHelper helper = new PropertyPlaceholderHelper("${", "}", ":", false);
        return helper.replacePlaceholders(pattern, properties);
    }
}
