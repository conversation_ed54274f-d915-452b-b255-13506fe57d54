package fm.lizhi.ocean.wavecenter.common.config;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.utils.PlaceholderUtils;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/4/10 16:39
 */
@Data
public class HyBizCommonConfig implements BizCommonConfig {

    private String cdnHost = "https://cdnoffice.lizhi.fm";

    private String riskAppId = "heiye";

    /**
     * 特殊厅品类名称
     */
    private String roomTypeName = "[{\"tabId\":\"20012\",\"tabName\":\"娱乐\"},{\"tabId\":\"10014\",\"tabName\":\"交友\"},{\"tabId\":\"10010\",\"tabName\":\"点唱\"},{\"tabId\":\"10015\",\"tabName\":\"CP\"}]";

    private String auditSceneName = "{\"1360003\":\"-\",\"1360002\":\"-\",\"1330002\":\"私信消息\",\"1360004\":\"私信消息\",\"1360018\":\"-\",\"1360008\":\"-\",\"1360017\":\"-\",\"1360005\":\"-\",\"1360016\":\"-\"}";

    private long sendChatUid = 1250651256339515138L;

    private RomeConfig romeConfig = new RomeConfig(String.valueOf(BusinessEvnEnum.HEI_YE.getAppId()), "http://romefs.yfxn.lizhi.fm/");

    /**
     * 资源转存路径
     */
    private String resourceTransferUrlPrefix = "/publicTransfer/${businessName}/${fileName}";

    @Override
    public String getResourceTransferUrlPrefix(String fileName) {
        return PlaceholderUtils.replace(
                resourceTransferUrlPrefix,
                "businessName", BusinessEvnEnum.HEI_YE.getName(),
                "fileName", fileName
        );
    }
}
