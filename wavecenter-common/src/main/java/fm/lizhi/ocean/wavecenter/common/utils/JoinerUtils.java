package fm.lizhi.ocean.wavecenter.common.utils;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 拼接工具类, 主要用于数据库使用一个字段存储多个值使用逗号分隔的场景. 该工具默认集合元素不存在null, 调用者需自行保证.
 */
public class JoinerUtils {

    /**
     * 默认分隔符
     */
    private static final String DEFAULT_DELIMITER = ",";

    /**
     * 使用逗号拼接多个url的相对路径, 如果列表为空则返回空字符串
     *
     * @param urls url列表
     * @return 拼接结果
     */
    public static String joinUrlRelativePath(List<String> urls) {
        if (CollectionUtils.isEmpty(urls)) {
            return StringUtils.EMPTY;
        }
        StringJoiner stringJoiner = new StringJoiner(DEFAULT_DELIMITER);
        for (String url : urls) {
            String relativePath = UrlUtils.removeHostOrEmpty(url);
            stringJoiner.add(relativePath);
        }
        return stringJoiner.toString();
    }

    /**
     * 使用逗号拆分字符串为多个url的相对路径并添加host, 如果拆分后的相对路径为空则设为空字符串
     *
     * @param joinedUrl 拼接的url
     * @param host      host
     * @return 拆分后并添加host的url列表
     */
    public static List<String> splitUrlAndAddHost(String joinedUrl, String host) {
        if (StringUtils.isEmpty(joinedUrl)) {
            return Collections.emptyList();
        }
        String[] relativePathArray = joinedUrl.split(DEFAULT_DELIMITER);
        ArrayList<String> urls = new ArrayList<>(relativePathArray.length);
        for (String relativePath : relativePathArray) {
            String fullUrl = UrlUtils.addHostOrEmpty(relativePath, host);
            urls.add(fullUrl);
        }
        return urls;
    }

    /**
     * 使用逗号拼接多个对象
     *
     * @param list 列表
     * @param <T>  泛型
     * @return 拼接结果
     */
    public static <T> String join(List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        StringJoiner stringJoiner = new StringJoiner(DEFAULT_DELIMITER);
        for (T t : list) {
            stringJoiner.add(t.toString());
        }
        return stringJoiner.toString();
    }

    /**
     * 使用逗号拆分字符串为多个字符串, 如果输入字符串为空则返回空列表
     *
     * @param string 字符串
     * @return 拆分后的字符串列表
     */
    public static List<String> splitString(String string) {
        if (StringUtils.isEmpty(string)) {
            return Collections.emptyList();
        }
        String[] split = string.split(DEFAULT_DELIMITER);
        return new ArrayList<>(Arrays.asList(split));
    }

    /**
     * 使用逗号拆分字符串为多个整数, 如果输入字符串为空则返回空列表
     *
     * @param string 字符串
     * @return 拆分后的整数列表
     */
    public static List<Integer> splitInteger(String string) {
        if (StringUtils.isEmpty(string)) {
            return Collections.emptyList();
        }
        String[] split = string.split(DEFAULT_DELIMITER);
        ArrayList<Integer> integers = new ArrayList<>(split.length);
        for (String s : split) {
            integers.add(Integer.parseInt(s));
        }
        return integers;
    }

    /**
     * 使用逗号拆分字符串为多个长整数, 如果输入字符串为空则返回空列表
     *
     * @param string 字符串
     * @return 拆分后的长整数列表
     */
    public static List<Long> splitLong(String string) {
        if (StringUtils.isEmpty(string)) {
            return Collections.emptyList();
        }
        String[] split = string.split(DEFAULT_DELIMITER);
        ArrayList<Long> longs = new ArrayList<>(split.length);
        for (String s : split) {
            longs.add(Long.parseLong(s));
        }
        return longs;
    }
}
