package fm.lizhi.ocean.wavecenter.common.config;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 罗马上传配置
 */
@Data
@NoArgsConstructor
public class RomeConfig {
    /**
     * 罗马上传appId
     */
    private String appId;
    /**
     * app名称
     */
    private String hostApp = "";
    /**
     * 设备id, 如果是服务端应该传服务名
     */
    private String deviceId = "lz-ocean-wavecenter";
    /**
     * 上传域名, 以斜杆结尾
     */
    private String address;
    /**
     * 是否使用自定义文件名
     */
    private boolean customFileName = true;
    /**
     * 访问权限, 可选值: public, private
     */
    private String accessModifier = "public";


    public RomeConfig(String appId, String address) {
        this.appId = appId;
        this.address = address;
    }
}