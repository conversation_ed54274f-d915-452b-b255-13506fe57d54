package fm.lizhi.ocean.wavecenter.base.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.module.paramnames.ParameterNamesModule;

import java.io.IOException;
import java.io.UncheckedIOException;
import java.math.BigDecimal;
import java.util.List;

/**
 * 默认JSON序列化工具类
 */
public final class JsonUtils {

    /**
     * 遗留的默认ObjectMapper对象, 用于兼容之前的代码
     */
    private static final ObjectMapper OBJECT_MAPPER_LEGACY = new ObjectMapper()
            .registerModule(new ParameterNamesModule())
            .registerModule(new Jdk8Module())
            .registerModule(new JavaTimeModule())
            .registerModule(new SimpleModule()
                    .addDeserializer(BigDecimal.class, new BigDecimalDeserializer())
                    .addSerializer(BigDecimal.class, new BigDecimalSerializer()))
            .setSerializationInclusion(JsonInclude.Include.NON_NULL)
            .configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false)
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .configure(MapperFeature.DEFAULT_VIEW_INCLUSION, false);

    /**
     * 默认ObjectMapper对象
     */
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper()
            .registerModule(new ParameterNamesModule())
            .registerModule(new Jdk8Module())
            .registerModule(new JavaTimeModule())
            .setSerializationInclusion(JsonInclude.Include.NON_NULL)
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    private JsonUtils() {
    }

    /**
     * 将对象按默认的规则序列化为JSON字符串. 这是一个遗留的方法, 使用的ObjectMapper指定了WRITE_DATES_AS_TIMESTAMPS为false,
     * 但没有指定Date的序列化格式, 因此会产生一个非标准Date序列化字符串. 此处保留该方法, 以兼容之前的代码.
     * <p>
     * 新代码请使用{@link #toJsonString(Object)}方法
     * </p>
     *
     * @param value 要序列化的对象
     * @return JSON字符串
     */
    public static String toJsonStringLegacy(Object value) {
        try {
            return OBJECT_MAPPER_LEGACY.writeValueAsString(value);
        } catch (JsonProcessingException e) {
            throw new UncheckedIOException(e);
        }
    }

    /**
     * 将JSON字符串按默认的规则反序列化为对象. 这是一个遗留的方法, 旧版的ObjectMapper配置了一些特殊的反序列化规则.
     * <p>
     * 新代码请使用{@link #fromJsonString(String, Class)}方法
     * </p>
     *
     * @param content JSON字符串
     * @param clazz   要反序列化的类型
     * @param <T>     反序列化类型泛型
     * @return 反序列化的对象
     */
    public static <T> T fromJsonStringLegacy(String content, Class<T> clazz) {
        try {
            return OBJECT_MAPPER_LEGACY.readValue(content, clazz);
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }

    /**
     * 将对象按默认的规则序列化为JSON字符串.
     *
     * @param value 要序列化的对象
     * @return JSON字符串
     */
    public static String toJsonString(Object value) {
        try {
            return OBJECT_MAPPER.writeValueAsString(value);
        } catch (JsonProcessingException e) {
            throw new UncheckedIOException(e);
        }
    }

    /**
     * 将JSON字符串按默认的规则反序列化为对象.
     *
     * @param content JSON字符串
     * @param clazz   要反序列化的类型
     * @param <T>     反序列化类型泛型
     * @return 反序列化的对象
     */
    public static <T> T fromJsonString(String content, Class<T> clazz) {
        try {
            return OBJECT_MAPPER.readValue(content, clazz);
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }
}
