package fm.lizhi.ocean.wavecenter.base.remote;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.context.ApplicationContext;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/3/20 16:14
 */
public class RemoteProxy implements InvocationHandler {

    private Class<?> target;

    private ApplicationContext applicationContext;

    public RemoteProxy(Class<?> target, ApplicationContext applicationContext) {
        this.target = target;
        this.applicationContext = applicationContext;
    }

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        if (Objects.equals(method.getName(), "hashCode")
                && ArrayUtils.isEmpty(method.getParameters())) {
            return System.identityHashCode(proxy);
        }
        if (Objects.equals(method.getName(), "equals")
                && ArrayUtils.getLength(method.getParameters()) == 1
                && Objects.equals(method.getParameters()[0].getType(), Object.class)) {
            return proxy == args[0];
        }
        if (Objects.equals(method.getName(), "toString")
                && ArrayUtils.isEmpty(method.getParameters())) {
            return "Proxy_" + System.identityHashCode(proxy);
        }

        //从上下文获取业务
        BusinessEvnEnum businessEvnEnum = ContextUtils.getBusinessEvnEnum();
        Map<String, ?> beansOfType = applicationContext.getBeansOfType(target);
        for (Map.Entry<String, ?> entry : beansOfType.entrySet()) {
            String beanName = entry.getKey();
            if (beanName.startsWith(RemoteProxyRegistry.BEAN_NAME_PREFIX)) {
                continue;
            }

            Object bean = entry.getValue();
            if (bean instanceof IRemote) {
                IRemote iRemote = (IRemote) bean;
                if (iRemote.getBusinessEvnEnum().equals(businessEvnEnum)) {
                    return method.invoke(iRemote, args);
                }
            }
        }
        throw new RuntimeException("remote service not found");
    }
}
