package fm.lizhi.ocean.wavecenter.base.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date 2024/4/18 19:29
 */
public class CalculateUtil {


    /**
     * 计算环比-保留四位小数
     * ((本期-上期)/上期)*100%
     * @param pre 上期
     * @param current 下期
     * @return
     */
    public static BigDecimal relativeRatioNotFormat(String pre, String current){
        if (pre == null) {
            pre = "0";
        }
        if (current == null) {
            current = "0";
        }

        BigDecimal preDecimal = new BigDecimal(recoverPercent(pre));
        BigDecimal currentDecimal = new BigDecimal(recoverPercent(current));

        //前后相等，返回0
        if (preDecimal.compareTo(currentDecimal) == 0) {
            return new BigDecimal(0);
        }
        //上期为0，返回1
        if (preDecimal.compareTo(new BigDecimal(0)) == 0) {
            return new BigDecimal(1);
        }

        BigDecimal sub = currentDecimal.subtract(preDecimal);
        return sub.divide(preDecimal, 4, RoundingMode.HALF_UP);
    }


    /**
     * 计算环比-保留两位小数
     * ((本期-上期)/上期)*100%
     * @param pre 上期
     * @param current 下期
     * @return
     */
    public static String relativeRatio(Integer pre, Integer current){
        if (pre == null) {
            pre = 0;
        }
        if (current == null) {
            current = 0;
        }
        return relativeRatio(new BigDecimal(String.valueOf(pre)), new BigDecimal(String.valueOf(current)));
    }

    /**
     * 计算环比
     * @param pre
     * @param current
     * @return
     */
    public static String relativeRatio(String pre, String current){
        if (pre == null) {
            pre = "0";
        }
        if (current == null) {
            current = "0";
        }
        pre = recoverPercent(pre);
        current = recoverPercent(current);
        return relativeRatio(new BigDecimal(pre), new BigDecimal(current));
    }

    /**
     * 格式化环比
     * @param preDecimal
     * @param currentDecimal
     * @return
     */
    public static String relativeRatio(BigDecimal preDecimal, BigDecimal currentDecimal){
        if (preDecimal == null) {
            preDecimal = new BigDecimal("0");
        }
        if (currentDecimal == null) {
            currentDecimal = new BigDecimal("0");
        }
        //前后相等，返回0
        if (preDecimal.compareTo(currentDecimal) == 0) {
            return formatRatio(new BigDecimal("0"));
        }
        //上期为0，返回100%
        if (preDecimal.compareTo(new BigDecimal("0")) == 0) {
            return formatRatio(new BigDecimal("1"));
        }

        BigDecimal sub = currentDecimal.subtract(preDecimal);
        BigDecimal divide = sub.divide(preDecimal, 4, RoundingMode.HALF_UP);
        return formatRatio(divide);
    }

    /**
     * 计算占比
     * @param a
     * @param b
     * @return a/b的百分比
     */
    public static String calculatePercent(String a, String b){
        if (a == null) {
            a = "0";
        }
        if (b == null) {
            b = "0";
        }
        BigDecimal aBig = new BigDecimal(a);
        BigDecimal bBig = new BigDecimal(b);
        if (bBig.compareTo(new BigDecimal("0")) == 0) {
            return formatPercent(bBig);
        }
        BigDecimal divide = aBig.divide(bBig, 2, RoundingMode.HALF_UP);
        return formatPercent(divide);
    }

    /**
     * 格式化百分比
     * @param value
     * @return
     */
    public static String formatPercent(BigDecimal value){
       return formatPercent(value, 2, RoundingMode.HALF_UP);
    }

    /**
     * 格式化百分比
     * @param value
     * @return
     */
    public static String formatPercent(BigDecimal value, int scaleNum, RoundingMode roundingMode){
        if (value == null) {
            value = new BigDecimal("0");
        }
        BigDecimal multiply = value.multiply(new BigDecimal("100"));
        BigDecimal scale = multiply.setScale(scaleNum, roundingMode);
        return scale + "%";
    }

    /**
     * 格式化比例数值
     * @param value 小数值
     * @return
     */
    public static String formatRate(BigDecimal value){
        BigDecimal bigDecimal = value.setScale(2, RoundingMode.HALF_UP);
        return CalculateUtil.formatPercent(bigDecimal);
    }

    /**
     * 格式化百分比
     * @param strValue
     * @return
     */
    public static String formatPercent(String strValue){
        if (strValue == null) {
            strValue = "0";
        }
        return formatPercent(new BigDecimal(strValue));
    }

    /**
     * 格式化环比
     * @param value
     * @return
     */
    public static String formatRatio(BigDecimal value){
        if (value == null) {
            value = new BigDecimal("0");
        }
        BigDecimal multiply = value.multiply(new BigDecimal("100"));
        BigDecimal scale = multiply.setScale(2, RoundingMode.HALF_UP);
        int comparisonResult = scale.compareTo(new BigDecimal("0"));
        if (comparisonResult > 0) {
            return "+" + scale + "%";
        } else {
            return scale + "%";
        }
    }

    /**
     * 复原百分比值字符串
     * @param percentValueStr
     * @return
     */
    public static String recoverPercent(String percentValueStr){
        if (percentValueStr.contains("%")) {
            percentValueStr = percentValueStr.replace("%", "");
            percentValueStr = percentValueStr.replace("+", "");
            BigDecimal bigDecimal = new BigDecimal(percentValueStr);
            BigDecimal divide = bigDecimal.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP);
            return divide.toString();
        }
        return percentValueStr;
    }

    /**
     * 格式化小数
     * @param value
     * @return
     */
    public static String formatDecimal(String value){
        if (value == null) {
            return "0";
        }
        if (!value.contains(".")){
            return value;
        }
        BigDecimal bigDecimal = new BigDecimal(value);
        BigDecimal scale = bigDecimal.setScale(0, BigDecimal.ROUND_DOWN);
        return scale.toString();
    }

    public static String formatDecimal(BigDecimal value){
        if (value == null) {
            return "0";
        }
        BigDecimal scale = value.setScale(0, BigDecimal.ROUND_DOWN);
        return scale.toString();
    }

}
