package fm.lizhi.ocean.wavecenter.base.constants;

/**
 * 时间常量
 */
public class TimeConstant {

    /**
     * 5秒 毫秒值
     */
    public static int FIVE_SECOND_MILLISECOND = 5000;
    /**
     * 一分钟秒数
     */
    public static int ONE_MINUTE = 60;

    /**
     * 2分钟秒数
     */
    public static int TWO_MINUTE = 120;



    /**
     * 3个小时的描述
     */
    public static int THREE_HOUR_SECOND = 3 * 60 * 60;


    /**
     * 一分钟毫秒数
     */
    public static int ONE_MINUTE_MILLISECOND = 60 * 1000;

    /**
     * 一天的描述
     */
    public static int ONE_DAY_MILLISECOND = 24 * 60 * 60 * 1000;

    /**
     * 一天的描述
     */
    public static int ONE_DAY_SECOND = 24 * 60 * 60;

    /**
     * 一周秒数
     */
    public static int ONE_WEEK = 7 * 24 * 60 * 60;

    /**
     * 一秒毫秒数
     */
    public static int ONE_SECOND_MILLISECOND = 1000;
}
