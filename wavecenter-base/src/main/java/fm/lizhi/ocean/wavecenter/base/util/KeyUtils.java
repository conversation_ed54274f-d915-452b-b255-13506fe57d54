package fm.lizhi.ocean.wavecenter.base.util;

import com.ctrip.framework.apollo.core.enums.Env;
import com.lizhi.commons.config.core.util.ConfigUtils;

/**
 * key的工具类
 *
 * <AUTHOR>
 * @date 2023/12/14
 */
public class KeyUtils {

    /**
     * 获取redis key 前缀
     *
     * @param name key名称
     * @param afx  是否区分预发线上
     * @return key前缀
     */
    public static String getPrefix(String name, boolean afx) {
        if (afx && ConfigUtils.getEnv() == Env.PRE) {
            return name + "_PRE";
        }
        return name;
    }
}
