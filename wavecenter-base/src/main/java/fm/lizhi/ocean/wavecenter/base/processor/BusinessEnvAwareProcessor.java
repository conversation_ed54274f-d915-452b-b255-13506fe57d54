package fm.lizhi.ocean.wavecenter.base.processor;


import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;

/**
 * 差异化逻辑基础接口
 */
public interface BusinessEnvAwareProcessor {

    /**
     * 获取业务环境数据
     *
     * @return 业务环境
     */
    BusinessEvnEnum getBusinessEnv();

    /**
     * 为了给工厂启动的时候校验处理器是不是所有业务已经实现
     * 防止后期processor设计上实现了多个接口或者有多层继承
     * @return
     */
    Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor();

}
