package fm.lizhi.ocean.wavecenter.base.config;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2024/4/12 15:38
 */
public abstract class AbsBizConfig<T> {

    protected Map<Integer, T> bizConfigMap = new ConcurrentHashMap<>();

    public T getBizConfig() {
        BusinessEvnEnum businessEvnEnum = ContextUtils.getBusinessEvnEnum();
        if (businessEvnEnum == null) {
            return null;
        }
        return getBizConfig(businessEvnEnum.appId());
    }

    public T getBizConfig(int appId) {
        return bizConfigMap.get(appId);
    }
}
