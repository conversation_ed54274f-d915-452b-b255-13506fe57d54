package fm.lizhi.ocean.wavecenter.base.processor;


import com.ctrip.framework.apollo.core.enums.Env;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Description: 流程处理类工厂
 *
 * <AUTHOR>
 * Created in  2023/8/11
 */
@Component
public class ProcessorFactory implements InitializingBean {

    @Autowired(required = false)
    private List<BusinessEnvAwareProcessor> processorList;

    private final Map<String, Map<Integer, BusinessEnvAwareProcessor>> classProcessorMap = new HashMap<>();

    /**
     * 获取处理类
     *
     * @param clazz 流程处理类类型
     * @param <T>   泛型
     * @return 处理器
     */
    public <T extends BusinessEnvAwareProcessor> T getProcessor(Class<T> clazz) {
        Map<Integer, BusinessEnvAwareProcessor> appIdMap = this.classProcessorMap.get(clazz.getName());
        if (MapUtils.isEmpty(appIdMap)) {
            throw new RuntimeException("未找到对应的处理类");
        }

        Integer appId = ContextUtils.getBusinessEvnEnum().getAppId();
        BusinessEnvAwareProcessor processor = appIdMap.get(appId);
        if (processor == null) {
            throw new RuntimeException("未找到对应的处理类");
        }

        return clazz.cast(processor);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        if (CollectionUtils.isEmpty(this.processorList)) {
            return;
        }

        // @what：字段注入后再解析为map @why：构造参数注入无法解决循环依赖
        for (BusinessEnvAwareProcessor processor : this.processorList) {
            int appId = processor.getBusinessEnv().getAppId();
            Class<? extends BusinessEnvAwareProcessor> baseClass = processor.getBaseBusinessProcessor();
            this.classProcessorMap.computeIfAbsent(baseClass.getName(), key -> new HashMap<>())
                    .put(appId, processor);
        }

        if (isCheck()) {
            int bizNum = (int) Arrays.stream(BusinessEvnEnum.values()).filter(v -> v.getOnline() == 1).count();
            for (Map.Entry<String, Map<Integer, BusinessEnvAwareProcessor>> mapEntry : this.classProcessorMap.entrySet()) {
                if (mapEntry.getValue().keySet().size() != bizNum) {
                    throw new RuntimeException(mapEntry.getKey()+" 实现类不完整");
                }
            }
        }

    }

    private boolean isCheck() {
        return ConfigUtils.getEnv() == Env.TEST
                || ConfigUtils.getEnv() == Env.LOCAL
                || ConfigUtils.getEnv() == Env.DEV;
    }
}
