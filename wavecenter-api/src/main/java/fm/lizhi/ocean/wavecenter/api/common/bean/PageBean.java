package fm.lizhi.ocean.wavecenter.api.common.bean;

import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/10 11:49
 */
@Data
public class PageBean<T> {

    private int total = 0;

    private List<T> list;

    private Long flushTime = 0L;

    public PageBean() {
    }

    public PageBean(int total, List<T> list) {
        this.total = total;
        this.list = list;
    }

    public PageBean(int total, List<T> list, Long flushTime) {
        this.total = total;
        this.list = list;
        this.flushTime = flushTime;
    }

    public static <T> PageBean<T> of(int total, List<T> list) {
        return new PageBean<>(total, list);
    }

    public static <T> PageBean<T> of(int total, List<T> list, Long flushTime) {
        return new PageBean<>(total, list, flushTime);
    }

    public static <T> PageBean<T> empty() {
        return new PageBean<>(0, Collections.emptyList());
    }
}
