package fm.lizhi.ocean.wavecenter.api.anchor.singer.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 考核入口配置
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RequestSaveApplyMenuConfig implements IContextRequest {


    /**
     * 应用ID
     */
    @NotNull(message = "应用ID不能为空")
    @AppEnumId
    private int appId;

    /**
     * 是否开启
     */
    private Boolean enabled;

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 结束时间
     */
    private Long endTime;

    /**
     * 歌手类型
     */
    private Integer singerType;

    /**
     * 操作人
     */
    private String operator;

    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}
