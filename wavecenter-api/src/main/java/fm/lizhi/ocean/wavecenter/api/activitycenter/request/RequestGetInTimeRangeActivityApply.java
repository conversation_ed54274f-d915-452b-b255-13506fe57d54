package fm.lizhi.ocean.wavecenter.api.activitycenter.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/4/17 10:47
 */
@Data
@Accessors(chain = true)
public class RequestGetInTimeRangeActivityApply implements IContextRequest {

    @AppEnumId
    @NotNull(message = "appId is null")
    private Integer appId;

    @NotNull(message = "njId is null")
    private Long njId;

    /**
     * 开始时间前分钟数
     */
    @NotNull(message = "startTimeBeforeMinute is null")
    private Integer startTimeBeforeMinute;

    /**
     * 结束时间往后的分钟数
     */
    @NotNull(message = "endTimeAfterMinute is null")
    private Integer endTimeAfterMinute;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
