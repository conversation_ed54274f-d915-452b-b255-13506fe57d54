package fm.lizhi.ocean.wavecenter.api.common.constants;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 数据指标
 * <AUTHOR>
 * @date 2024/4/18 18:26
 */
public enum MetricsEnum {

    /**
     * 魅力值
     */
    CHARM("charm", "魅力值")

    /**
     * 厅签约主播数
     */
    , SIGN_PLAYER_CNT("signPlayerCnt", "厅签约主播数")

    /**
     * 当期收入
     */
    , CURR_INCOME("currIncome", "当期收入")
    /**
     * 上期收入
     */
    , PRE_INCOME("preIncome", "上期收入")

    /**
     * 收入
     */
    , INCOME("income", "收入")

    /**
     * 总收入
     */
    , ALL_INCOME("allIncome", "总收入")

    /**
     * 麦序
     */
    , SEAT_ORDER("seatOrder", "有效麦序")

    /**
     * 主持档
     */
    , HOST_CNT("hostCnt", "主持档")

    /**
     * 上麦时长
     */
    , UP_GUEST_DUR("upGuestDur", "上麦时长")

    , SIGN_HALL_INCOME("signHallIncome", "签约厅收礼收入")

    , PERSONAL_HALL_INCOME("personalHallIncome", "个播收礼收入")

    , OFFICIAL_HALL_INCOME("officialHallIncome", "官方厅收礼收入")

    , NOBLE_INCOME("nobleIncome", "贵族提成收入")

    , PERSONAL_NOBLE_INCOME("personalNobleIncome", "个播贵族提成收入")

    , SIGN_ROOM_CNT("signRoomCnt", "公会签约厅数")

    , OPEN_ROOM_CNT("openRoomCnt", "公会开播厅数")

    , INCOME_ROOM_CNT("incomeRoomCnt", "公会有收入厅数")

    , OPEN_RATE("openRate", "开播率")

    , INCOME_ROOM_RATE("incomeRoomRate", "公会有收入厅占比")

    , ROOM_AVG_INCOME("roomAvgIncome", "公会厅均收入")

    , ROOM_AVG_CHARM("roomAvgCharm", "公会厅均魅力值")

    , UP_GUEST_PLAYER_CNT("upGuestPlayerCnt", "公会上麦主播数")
    , SIGN_UP_GUEST_PLAYER_CNT("signUpGuestPlayerCnt", "公会上麦主播数")

    , INCOME_PLAYER_CNT("incomePlayerCnt", "公会有收入主播数")

    , UP_PLAYER_RATE("upPlayerRate", "主播上麦率")

    , INCOME_PLAYER_RATE("incomePlayerRate", "有收入主播占比")

    , PLAYER_AVG_INCOME("playerAvgIncome", "人均收入")

    , PLAYER_AVG_CHARM("playerAvgCharm", "人均魅力值")
    ;

    @Getter
    private String value;
    @Getter
    private String name;

    private static Map<String, MetricsEnum> valueMap = new HashMap<>();

    static {
        for (MetricsEnum metrics : MetricsEnum.values()) {
            valueMap.put(metrics.value, metrics);
        }
    }

    MetricsEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static MetricsEnum fromValue(String value) {
        return valueMap.get(value);
    }


}
