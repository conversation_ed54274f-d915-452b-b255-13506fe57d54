package fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 条件
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConditionBean {

    /**
     * 指标code
     */
    @NotBlank(message = "指标code不能为空")
    private String metricCode;

    /**
     * 比较符 (>=, >, <=, <, =, !=)
     */
    @NotBlank(message = "比较符不能为空")
    private String comparator;

    /**
     * 值
     */
    @NotNull(message = "条件值不能为空")
    @Min(value = 0, message = "条件值必须大于等于0")
    private Integer value;
}
