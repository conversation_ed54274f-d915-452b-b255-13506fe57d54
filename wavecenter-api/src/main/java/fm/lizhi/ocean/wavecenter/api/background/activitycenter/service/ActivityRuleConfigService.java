package fm.lizhi.ocean.wavecenter.api.background.activitycenter.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityRuleConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityRule;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityRule;

import java.util.List;

/**
 * 活动提报规则
 * <AUTHOR>
 */
public interface ActivityRuleConfigService {

    /**
     * 保存活动提报规则
     * <p>
     * 额外关注状态码: {@link #ACTIVITY_APPLY_RULE_ENUM_NOT_FOUNT}
     */
    Result<Void> saveActivityRule(RequestSaveActivityRule param);


    /**
     * 修改活动提报规则
     * <p>
     * 额外关注状态码: {@link #ACTIVITY_APPLY_RULE_ENUM_NOT_FOUNT} & {@link #SAVE_ACTIVITY_RULE_FAIL}
     */
    Result<Void> updateActivityRule(RequestUpdateActivityRule param);


    /**
     * 删除活动提报规则
     */
    Result<Void> deleteActivityRule(Long id, int appId, String operator);


    /**
     * 分页查询活动提报规则
     */
    Result<List<ActivityRuleConfigBean>> listActivityRule(int appId);


    /**
     * 规则重复
     */
    int SAVE_ACTIVITY_RULE_EXIST = 2130001;

    /**
     * 保存规则失败
     */
    int SAVE_ACTIVITY_RULE_FAIL = 2130002;

    /**
     * 规则枚举不存在
     */
    int ACTIVITY_APPLY_RULE_ENUM_NOT_FOUNT = 2130101;

    /**
     * 更新规则失败
     */
    int UPDATE_ACTIVITY_RULE_FAIL = 2130201;

    /**
     * 删除规则失败
     */
    int DELETE_ACTIVITY_RULE_FAIL = 2130301;
}
