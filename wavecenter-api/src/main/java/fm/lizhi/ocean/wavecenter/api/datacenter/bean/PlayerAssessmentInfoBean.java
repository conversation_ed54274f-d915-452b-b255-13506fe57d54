package fm.lizhi.ocean.wavecenter.api.datacenter.bean;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/4/17 16:48
 */
@Data
@Accessors(chain = true)
public class PlayerAssessmentInfoBean extends AssessmentTimeBean{

    /**
     * 刷新时间
     */
    private Long flushTime;

    /**
     * 总收入
     */
    private IncomeBean sumIncome;

    /**
     * 总魅力值
     */
    private CharmRatioBean sumCharm;

    /**
     * 签约厅收礼收入
     */
    private IncomeBean roomIncome;

    /**
     * 厅收礼魅力值
     */
    private CharmRatioBean roomCharm;

    /**
     * 个播收入
     */
    private IncomeBean individualIncome;

    /**
     * 个播魅力值
     */
    private CharmRatioBean individualCharm;

    /**
     * 官方厅收礼收入
     */
    private IncomeBean officialIncome;

    /**
     * 官方厅收礼魅力值
     */
    private CharmRatioBean officialCharm;

}
