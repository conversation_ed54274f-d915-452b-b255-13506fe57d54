package fm.lizhi.ocean.wavecenter.api.background.activitycenter.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 更新活动模板请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RequestUpdateActivityTemplate extends RequestSaveActivityTemplate {

    /**
     * 活动模板id
     */
    @NotNull(message = "活动模板id不能为空")
    private Long id;
}
