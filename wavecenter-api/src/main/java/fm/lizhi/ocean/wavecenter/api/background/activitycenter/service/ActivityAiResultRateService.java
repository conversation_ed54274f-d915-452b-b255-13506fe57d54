package fm.lizhi.ocean.wavecenter.api.background.activitycenter.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.ActivityAiResultRateRequest;

/**
 * AI结果评分 Service 接口
 */
public interface ActivityAiResultRateService {

    /**
     * 保存AI结果评分
     *
     * @param request ActivityAiResultRateRequest 请求对象
     * @return ActivityAiResultRateResponse 响应对象
     */
    Result<Void> saveActivityAiResultRate(ActivityAiResultRateRequest request);


    /**
     * 保存AI结果评分失败
     */
    int SAVE_ACTIVITY_AI_RESULT_RATE_FAILED = 2440000;
} 