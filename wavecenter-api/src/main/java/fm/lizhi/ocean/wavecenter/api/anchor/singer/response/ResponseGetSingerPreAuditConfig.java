package fm.lizhi.ocean.wavecenter.api.anchor.singer.response;

import java.util.List;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerPreAuditConfigBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerAuditConfigBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerContentConfigBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SongStyleConfig;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ResponseGetSingerPreAuditConfig {

    /**
     * 预审核配置列表
     */
    private List<SingerPreAuditConfigBean> auditConfigList;

    /**
     * 音频审核配置
     */
    private SingerAuditConfigBean audioConfig;

    /**
     * 歌手审核文案配置
     */
    private SingerContentConfigBean contentConfig;

    /**
     * 歌曲风格配置
     */
    private SongStyleConfig songStyleConfig;


}
