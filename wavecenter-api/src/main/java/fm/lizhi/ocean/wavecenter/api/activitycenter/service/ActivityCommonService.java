package fm.lizhi.ocean.wavecenter.api.activitycenter.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestActivityOptionOfficialTime;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseActivityConfig;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseActivityOptionOfficialTime;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseGetBaseActivityConfig;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseActivityConfigBean;

public interface ActivityCommonService {

    /**
     * 查询活动配置
     *
     * @return 结果
     */
    Result<ResponseActivityConfig> getActivityConfigs(Integer appId);

    /**
     * 获取一些基础的枚举配置
     */
    Result<ResponseActivityConfigBean> getBaseEnumConfig(int appId);

    /**
     * 获取基础活动配置, 主要用于活动模板层面的配置
     *
     * @param appId 应用id
     * @return 结果
     */
    Result<ResponseGetBaseActivityConfig> getBaseActivityConfig(int appId);
}
