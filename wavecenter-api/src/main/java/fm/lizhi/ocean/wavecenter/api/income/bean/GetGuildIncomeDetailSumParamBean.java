package fm.lizhi.ocean.wavecenter.api.income.bean;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import fm.lizhi.ocean.wavecenter.api.income.constants.IncomeType;
import lombok.Builder;
import lombok.Getter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/23 14:47
 */
@Getter
@Builder
public class GetGuildIncomeDetailSumParamBean {

    private Integer appId;

    private Long familyId;

    /**
     * 开始时间
     */
    private Date startDate;

    private Date endDate;

    private Long roomId;

    /**
     * 收入类型
     */
    private List<IncomeType> incomeType;

    public static class GetGuildIncomeDetailSumParamBeanBuilder{
        public GetGuildIncomeDetailSumParamBean build(){
            ApiAssert.notNull(appId, "appId is required");
            ApiAssert.notNull(familyId, "familyId is required");
            ApiAssert.notNull(startDate, "startDate is required");
            ApiAssert.notNull(endDate, "endDate is required");
            ApiAssert.notNull(roomId, "roomId is required");
//            ApiAssert.notNull(incomeType, "incomeType is required");
            return new GetGuildIncomeDetailSumParamBean(appId, familyId, startDate, endDate, roomId, incomeType);
        }
    }

}
