package fm.lizhi.ocean.wavecenter.api.background.activitycenter.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplatePageBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.*;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseCreateActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseGetActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseGetActivityTemplateShelfStatus;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 活动模板配置服务
 */
public interface ActivityTemplateConfigService {

    /**
     * 创建活动模板
     *
     * @param req 请求
     * @return 结果
     */
    Result<ResponseCreateActivityTemplate> createTemplate(@NotNull @Valid RequestCreateActivityTemplate req);

    /**
     * 更新活动模板
     *
     * @param req 请求
     * @return 结果
     */
    Result<Void> updateTemplate(@NotNull @Valid RequestUpdateActivityTemplate req);

    /**
     * 删除活动模板
     *
     * @param req 请求
     * @return 结果
     */
    Result<Void> deleteTemplate(@NotNull @Valid RequestDeleteActivityTemplate req);

    /**
     * 更新活动模板上下架状态
     *
     * @param req 请求
     * @return 结果
     */
    Result<Void> updateShelfStatus(@NotNull @Valid RequestUpdateActivityTemplateShelfStatus req);

    /**
     * 获取活动模板上下架状态
     *
     * @param id 活动模板id
     * @return 结果
     */
    Result<ResponseGetActivityTemplateShelfStatus> getShelfStatus(long id);

    /**
     * 分页查询活动模板
     *
     * @param req 请求
     * @return 结果
     */
    Result<PageBean<ActivityTemplatePageBean>> pageTemplate(@NotNull @Valid RequestPageActivityTemplate req);

    /**
     * 获取活动模板
     *
     * @param id 活动模板id
     * @return 结果
     */
    Result<ResponseGetActivityTemplate> getTemplate(long id);

    /**
     * 创建活动模板失败
     */
    int CREATE_TEMPLATE_FAIL = 2140001;

    /**
     * 更新活动模板失败
     */
    int UPDATE_TEMPLATE_FAIL = 2140101;

    /**
     * 删除活动模板失败
     */
    int DELETE_TEMPLATE_FAIL = 2140201;

    /**
     * 更新活动模板上下架状态失败
     */
    int UPDATE_SHELF_STATUS_FAIL = 2140301;

    /**
     * 获取活动模板上下架状态失败
     */
    int GET_SHELF_STATUS_FAIL = 2140401;

    /**
     * 分页查询活动模板失败
     */
    int PAGE_TEMPLATE_FAIL = 2140501;

    /**
     * 获取活动模板失败
     */
    int GET_TEMPLATE_FAIL = 2140601;
}
