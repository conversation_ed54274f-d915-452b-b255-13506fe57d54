package fm.lizhi.ocean.wavecenter.api.background.activitycenter.service;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.*;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseCancelActivity;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseBatchActivityAuditData;

/**
 * 活动运营后台操作
 */
public interface ActivityAdminOperateService {

    /**
     * 驳回活动申请
     *
     * @param param 驳回参数
     * @return 结果
     */
    Result<Void> rejectActivityApply(RequestActivityAuditReject param);

    /**
     * 同意活动申请&资源发放
     *
     * @param param
     * @return
     */
    Result<String> agreeActivityApply(RequestActivityAuditAgree param);

    /**
     * 取消活动
     *
     * @param param 参数
     * @return 结果
     */
    Result<ResponseCancelActivity> adminCancelActivity(@NotNull @Valid RequestCancelActivity param);


    /**
     * 运营修改活动申请
     *
     * @param activityModifyBean 参数
     * @return 结果
     */
    Result<ResponseActivityModifyBean> adminModifyActivity(@NotNull @Valid RequestActivityModifyBean activityModifyBean);

    /**
     * 批量驳回活动申请
     *
     * @param param 批量驳回参数
     * @return 结果
     */
    Result<ResponseBatchActivityAuditData> batchRejectActivityApply(RequestBatchActivityAuditReject param);

    /**
     * 批量同意活动申请&资源发放
     *
     * @param param 批量同意参数
     * @return 结果
     */
    Result<ResponseBatchActivityAuditData> batchAgreeActivityApply(RequestBatchActivityAuditAgree param);

    /**
     * 同意活动异常
     */
    int AGREE_ACTIVITY_APPLY_FAIL = 22300001;

    /**
     * 同意活动申请参数异常
     */
    int AGREE_ACTIVITY_APPLY_PARAM_ERROR = 22300002;

    /**
     * 拒绝活动申请参数异常
     */
    int REJECT_ACTIVITY_APPLY_PARAM_ERROR = 22300003;

    /**
     * 拒绝操作失败
     */
    int REJECT_ACTIVITY_APPLY_FAIL = 22300004;

    /**
     * 版本号不一致
     */
    int ACTIVITY_VERSION_NOT_MATCH = 22300005;

    /**
     * 活动取消失败
     */
    int CANCEL_ACTIVITY_FAIL = 22300006;

    /**
     * 活动时间异常
     */
    int CANCEL_ACTIVITY_TIME_ERROR = 22300007;

    /**
     * 活动审核状态异常
     */
    int CANCEL_ACTIVITY_AUDIT_STATUS_ERROR = 22300008;

    /**
     * 活动取消参数异常
     */
    int CANCEL_ACTIVITY_PARAM_ERROR = 22300009;

    /**
     * 活动修改太快
     */
    int ACTIVITY_MODIFY_TOO_FAST = 22300010;

    /**
     * 活动修改失败
     */
    int ACTIVITY_MODIFY_FAIL = 22300011;

    /**
     * 取消活动版本号不一致
     */
    int CANCEL_ACTIVITY_VERSION_NOT_MATCH = 22300012;

    /**
     * 活动资源除推荐卡外不允许修改
     */
    int ACTIVITY_RESOURCE_NOT_MODIFY = 22300013;

    /**
     * 删除流量资源失败
     */
    int ACTIVITY_MODIFY_DELETE_RESOURCE_FAIL = 22300014;

    /**
     * 修改记录失败
     */
    int ACTIVITY_MODIFY_RECORD_FAIL = 22300015;

    /**
     * 资源发放失败
     */
    int ACTIVITY_MODIFY_RESOURCE_GIVE_FAIL = 22300015;

    /**
     * 批量驳回活动申请失败
     */
    int BATCH_REJECT_ACTIVITY_APPLY_FAIL = 22300016;

    /**
     * 批量同意活动申请失败
     */
    int BATCH_AGREE_ACTIVITY_APPLY_FAIL = 22300017;

}
