package fm.lizhi.ocean.wavecenter.api.anchor.singer.request;

import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;

@Data
public class RequestModifyVerifyApplyRemark implements IContextRequest {

    /**
     * 记录ID
     */
    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 备注内容
     */
    private String remark;

    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}
