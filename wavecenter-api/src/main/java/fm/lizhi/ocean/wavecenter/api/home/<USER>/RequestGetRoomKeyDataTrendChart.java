package fm.lizhi.ocean.wavecenter.api.home.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 获取数据中心关键指标趋势图
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RequestGetRoomKeyDataTrendChart {

    /**
     * 应用id
     */
    @NotNull(message = "应用id不能为空")
    @AppEnumId
    private Integer appId;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空")
    private Long userId;

    /**
     * 家族 ID
     */
    @NotNull(message = "家族id不能为空")
    private Long familyId;

    /**
     * 房间 ID
     */
    @NotNull(message = "房间id不能为空")
    private Long roomId;

    /**
     * 开始时间 YYYY-MM-DD
     */
    @NotNull(message = "开始时间不能为空")
    private String startDate;

    /**
     * 结束时间 YYYY-MM-DD
     */
    @NotNull(message = "结束时间不能为空")
    private String endDate;
}
