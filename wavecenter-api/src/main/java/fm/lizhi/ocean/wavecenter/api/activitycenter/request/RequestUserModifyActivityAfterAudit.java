package fm.lizhi.ocean.wavecenter.api.activitycenter.request;

import javax.validation.constraints.NotNull;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import lombok.Data;

@Data
public class RequestUserModifyActivityAfterAudit {

    /**
     * 操作人用户ID
     */
    @NotNull(message = "操作人用户ID不能为空")
    private Long operateUserId;

    /**
     * 活动ID
     */
    @NotNull(message = "活动ID不能为空")
    private Long activityId;

    /**
     * 主持人ID
     */
    private Long hostId;

    /**
     * 陪档主播ID
     */
    private String accompanyNjIds;

    /**
     * 应用ID
     */
    @AppEnumId(message = "应用ID不能为空")
    private Integer appId;

    /**
     * 版本号
     */
    @NotNull(message = "版本号不能为空")
    private Integer version;
}
