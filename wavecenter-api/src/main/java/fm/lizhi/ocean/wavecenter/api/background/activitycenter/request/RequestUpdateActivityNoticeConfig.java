package fm.lizhi.ocean.wavecenter.api.background.activitycenter.request;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import lombok.Data;

import java.util.List;

@Data
public class RequestUpdateActivityNoticeConfig {
    
    private Long id;

    @AppEnumId
    private Integer appId;

    /**
     * 操作人
     */
    @NotNull(message = "操作人不能为空")
    private String operator;

    /**
     * 品类值
     */
    @Valid
    private List<Integer> categoryList;

    /**
     * 公告内容
     */
    private String content;

}
