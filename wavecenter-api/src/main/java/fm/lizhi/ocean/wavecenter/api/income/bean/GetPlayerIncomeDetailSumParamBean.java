package fm.lizhi.ocean.wavecenter.api.income.bean;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import fm.lizhi.ocean.wavecenter.api.income.constants.IncomeType;
import lombok.Builder;
import lombok.Getter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/24 14:47
 */
@Getter
@Builder
public class GetPlayerIncomeDetailSumParamBean {

    private Integer appId;

    /**
     * 开始时间
     */
    private Date startDate;

    private Date endDate;

    /**
     * 收入类型
     */
    private List<IncomeType> incomeType;

    private Long userId;


    public static class GetPlayerIncomeDetailSumParamBeanBuilder{
        public GetPlayerIncomeDetailSumParamBean build(){
            ApiAssert.notNull(appId, "appId is required");
            ApiAssert.notNull(startDate, "startDate is required");
            ApiAssert.notNull(endDate, "endDate is required");
//            ApiAssert.notNull(incomeType, "incomeType is required");
            ApiAssert.notNull(userId, "userId is required");
            return new GetPlayerIncomeDetailSumParamBean(appId, startDate, endDate, incomeType, userId);
        }
    }

}
