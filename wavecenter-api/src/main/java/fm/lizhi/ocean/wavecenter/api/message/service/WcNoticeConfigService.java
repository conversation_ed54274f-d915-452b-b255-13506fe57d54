package fm.lizhi.ocean.wavecenter.api.message.service;

import javax.validation.Valid;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.message.request.*;
import fm.lizhi.ocean.wavecenter.api.message.response.ResponseUnReadMessageCount;
import fm.lizhi.ocean.wavecenter.api.message.response.ResponseWcNoticeConfigPage;

/**
 * 公告配置 Service 接口
 *
 * <AUTHOR>
 */
public interface WcNoticeConfigService {
    /**
     * 保存公告配置
     * @param request 公告配置请求
     * @return 保存结果
     */
    Result<Void> saveWcNoticeConfig(@Valid RequestWcNoticeConfigSave request);

    /**
     * 根据id删除公告配置
     * @param id id
     * @return 删除结果
     */
    Result<Void> deleteWcNoticeConfigById(@Valid Long id);

    /**
     * 分页查询公告配置
     * @param request 查询参数
     * @return 分页结果
     */
    Result<ResponseWcNoticeConfigPage> queryWcNoticeConfigPage(@Valid RequestWcNoticeConfigPage request);

    /**
     * 获取未读消息数
     * @param request 查询参数
     * @return 未读消息数
     */
    Result<ResponseUnReadMessageCount> getUnReadMessageCount(RequestUnReadMessageCount request);

    /**
     * 保存公告配置
     * @param request 公告配置请求
     * @return 保存结果
     */
    Result<Void> updateWcNoticeStatus(@Valid RequestWcNoticeUpdateStatus request);

    /**
     * 保存失败
     */
    int SAVE_WC_NOTICE_CONFIG_ERROR = 2470000;

    /**
     * 删除失败
     */
    int DELETE_WC_NOTICE_CONFIG_ERROR = 2470001;

    /**
     * 分页查询失败
     */
    int QUERY_WC_NOTICE_CONFIG_PAGE_ERROR = 2470002;

    /**
     * 按生效时间分页查询失败
     */
    int QUERY_WC_NOTICE_CONFIG_BY_EFFECT_TIME_PAGE_ERROR = 2470003;

    /**
     * 更新状态失败
     */
    int UPDATE_WC_NOTICE_STATUS_ERROR = 2470004;
} 