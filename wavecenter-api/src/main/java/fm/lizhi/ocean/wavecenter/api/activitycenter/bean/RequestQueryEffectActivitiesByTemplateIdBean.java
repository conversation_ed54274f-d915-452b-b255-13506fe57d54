package fm.lizhi.ocean.wavecenter.api.activitycenter.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class RequestQueryEffectActivitiesByTemplateIdBean {


    private Integer appId;

    /**
     * 审核状态
     *
     * @see fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityAuditStatusEnum
     */
    private List<Integer> auditStatus;


    /**
     * 活动状态
     *
     * @see fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityStatusEnum
     */
    private Integer activityStatus;

    //模板 ID
    private Long templateId;

}