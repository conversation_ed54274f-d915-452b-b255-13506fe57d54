package fm.lizhi.ocean.wavecenter.api.income.bean;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/4/24 17:19
 */
@Getter
@Builder
public class GetRoomSignPlayerIncomeParamBean {

    private Integer appId;

    private Long userId;

    private Date startDate;

    private Date endDate;

    private Integer pageNo;

    private Integer pageSize;

    public static class GetRoomSignPlayerIncomeParamBeanBuilder{
        public GetRoomSignPlayerIncomeParamBean build(){
            ApiAssert.notNull(appId, "appId is required");
            ApiAssert.notNull(userId, "userId is required");
            ApiAssert.notNull(startDate, "startDate is required");
            ApiAssert.notNull(endDate, "endDate is required");
            if (pageNo == null || pageNo <= 0) {
                pageNo = 1;
            }
            if (pageSize == null || pageSize <= 0) {
                pageSize = 20;
            }
            return new GetRoomSignPlayerIncomeParamBean(appId, userId, startDate, endDate, pageNo, pageSize);
        }
    }

}
