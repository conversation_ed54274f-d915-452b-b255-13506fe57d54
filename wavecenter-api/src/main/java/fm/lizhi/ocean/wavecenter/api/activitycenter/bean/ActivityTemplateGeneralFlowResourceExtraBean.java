package fm.lizhi.ocean.wavecenter.api.activitycenter.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 活动模板流量资源扩展信息
 */
@Data
@Accessors(chain = true)
public class ActivityTemplateGeneralFlowResourceExtraBean {

    /**
     * 官频位时长限制, 单位分钟, 当资源类型为官频位时有值
     */
    @Deprecated
    private Integer durationLimit;

    /**
     * 官频位可选座位号列表, 当资源类型为官频位时有值
     */
    private List<Integer> officialSeatNumbers;
}
