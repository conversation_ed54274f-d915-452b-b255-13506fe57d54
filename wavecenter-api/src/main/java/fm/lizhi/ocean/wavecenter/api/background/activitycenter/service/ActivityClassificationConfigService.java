package fm.lizhi.ocean.wavecenter.api.background.activitycenter.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityBigClassBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityClassConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 活动分类
 * 活动大类 -> 活动分类 (1:N)
 * <AUTHOR>
 */
public interface ActivityClassificationConfigService {


    // region ---- 活动大类 ----

    /**
     * 保存活动大类
     */
    Result<Void> saveBigClassification(@NotNull @Valid RequestSaveActivityBigClass req);

    /**
     * 更新活动大类
     */
    Result<Void> updateBigClassification(@NotNull @Valid RequestUpdateActivityBigClass req);

    /**
     * 删除活动大类
     */
    Result<Void> deleteBigClassification(@NotNull @Valid RequestDeleteActivityBigClass req);

    /**
     * 查询活动大类列表
     */
    Result<List<ActivityBigClassBean>> listBigClassByAppId(int appId);

    // endregion

    // region ---- 活动分类 ----

    /**
     * 保存活动分类
     * <p>
     * 额外关注状态码 {@link #HAS_CLASSIFICATION_REPEAT} & {@link #SAVE_CLASSIFICATION_BIG_CLASS_NOT_FOUND} & {@link #SAVE_CLASSIFICATION_ACTIVITY_LEVEL_NOT_EXIST}
     */
    Result<Void> saveClassification(@NotNull @Valid RequestSaveActivityClassification req);

    /**
     * 更新活动分类
     * <p>
     * 额外关注状态码 {@link #HAS_CLASSIFICATION_REPEAT} & {@link #SAVE_CLASSIFICATION_BIG_CLASS_NOT_FOUND} & {@link #SAVE_CLASSIFICATION_ACTIVITY_LEVEL_NOT_EXIST}
     */
    Result<Void> updateClassification(@NotNull @Valid RequestUpdateActivityClassification req);

    /**
     * 删除活动分类
     */
    Result<Void> deleteClassification(@NotNull @Valid RequestDeleteActivityClassification req);

    /**
     * 查询活动分类列表
     */
    Result<List<ActivityClassConfigBean>> listClassificationByBigClassId(long bigClassId);

    // endregion

    // region ---- error code ----

    /**
     * 存在重复记录
     */
    int HAS_CLASSIFICATION_REPEAT = 2070001;

    /**
     * 保存分类失败
     */
    int SAVE_CLASSIFICATION_FAIL = 2070101;

    /**
     * 更新分类失败
     */
    int UPDATE_CLASSIFICATION_FAIL = 2070201;

    /**
     * 删除活动等级失败
     */
    int DELETE_CLASSIFICATION_FAIL = 2070301;

    /**
     * 大类不存在
     */
    int SAVE_CLASSIFICATION_BIG_CLASS_NOT_FOUND = 2070002;

    /**
     * 活动等级不存在
     */
    int SAVE_CLASSIFICATION_ACTIVITY_LEVEL_NOT_EXIST = 2070003;

    // endregion

}
