package fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.response;

import fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.bean.TaskTemplateQueryBean;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 查询任务模版响应
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResponseQueryTaskTemplate {

    /**
     * 任务模版列表
     */
    private List<TaskTemplateQueryBean> list;

    /**
     * 总数
     */
    private Long total;
} 