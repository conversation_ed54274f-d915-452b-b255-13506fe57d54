package fm.lizhi.ocean.wavecenter.api.message.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum WcNoticeConfigEnum {
    /**
     * 新功能上线
     */
    NEW_FUNCTION(1, "新功能上线"),

    /**
     * 活动公告
     */
    ACTIVITY(2, "活动公告"),

    /**
     * 签约申请
     */
    SIGN_APPLY(3, "签约申请"),

    /**
     * 其他
     */
    OTHER(4, "其他");

    private final int code;
    private final String desc;

    public static WcNoticeConfigEnum getByCode(int code) {
        for (WcNoticeConfigEnum e : WcNoticeConfigEnum.values()) {
            if (e.getCode() == code) {
                return e;
            }
        }
        return null;
    }
}
