package fm.lizhi.ocean.wavecenter.api.activitycenter.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 活动模板状态枚举
 */
@AllArgsConstructor
@Getter
public enum ActivityTemplateStatusEnum {

    /**
     * 上架
     */
    ON_SHELF(1),
    /**
     * 下架
     */
    OFF_SHELF(2),

    /**
     * 定期上架
     */
    ON_SHELF_AUTO(3)
    ;

    /**
     * 状态值
     */
    private final Integer status;

    public static ActivityTemplateStatusEnum getByStatus(Integer status) {
        for (ActivityTemplateStatusEnum value : values()) {
            if (value.status.equals(status)) {
                return value;
            }
        }
        return null;
    }
}
