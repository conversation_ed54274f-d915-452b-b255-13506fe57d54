package fm.lizhi.ocean.wavecenter.api.background.activitycenter.request;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityResourceDeployTypeConstants;
import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 修改活动资源
 * <AUTHOR>
 */
@Data
@Builder
public class RequestUpdateActivityResource {

    private Long id;

    /**
     * 资源名称
     */
    private String name;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 资源介绍
     */
    private String introduction;

    /**
     * 资源图片 URL
     */
    private String imageUrl;

    /**
     * 关联等级 ID 列表
     */
    private List<Long> relationLevelIds;

    /**
     * 资源配置类型，1：自动，2：手动
     */
    private Integer deployType;

    /**
     * 是否可用，0：禁用，1：可用
     */
    private Integer status;

    /**
     * 自动资源的code
     */
    private String resourceCode;

    /**
     * 是否必选
     */
    private Boolean required;

    /**
     * 操作人
     */
    private String operator;

    private RequestUpdateActivityResource(RequestUpdateActivityResource.RequestUpdateActivityResourceBuilder builder) {
        this.id = builder.id;
        this.name = builder.name;
        this.appId = builder.appId;
        this.introduction = builder.introduction;
        this.imageUrl = builder.imageUrl;
        this.relationLevelIds = builder.relationLevelIds;
        this.deployType = builder.deployType;
        this.status = builder.status;
        this.resourceCode = builder.resourceCode;
        this.required = builder.required;
        this.operator = builder.operator;
    }


    public static class RequestUpdateActivityResourceBuilder {

        public RequestUpdateActivityResource build() {


            ApiAssert.notNull(appId, "appId is null");
            ApiAssert.notNull(operator, "操作人不能为空");
            ApiAssert.notNull(name, "资源名称不能为空");
            ApiAssert.notNull(deployType, "资源配置类型不能为空");
            ApiAssert.notNull(status, "可用状态不能为空");
            ApiAssert.notNull(required, "必选状态不能为空");
            ApiAssert.notNull(id, "ID不能为空");

            if (deployType.equals(ActivityResourceDeployTypeConstants.AUTO_CONFIG)) {
                ApiAssert.notNull(resourceCode, "自动资源的code不能为空");
            }

            if (relationLevelIds == null || relationLevelIds.isEmpty()) {
                ApiAssert.notNull(relationLevelIds, "关联等级ID列表不能为空");
            }


            return new RequestUpdateActivityResource(this);
        }
    }


}
