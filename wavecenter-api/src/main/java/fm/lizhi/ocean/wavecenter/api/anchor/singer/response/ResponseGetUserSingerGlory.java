package fm.lizhi.ocean.wavecenter.api.anchor.singer.response;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.UserSingerGloryBean;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class ResponseGetUserSingerGlory {

    /**
     * 歌手类型
     */
    private Integer singerType;

    /**
     * 是否原创歌手
     */
    private Boolean originalSinger;

    /**
     * 歌手勋章列表
     */
    private List<UserSingerGloryBean> singerGloryBadgeList;

}
