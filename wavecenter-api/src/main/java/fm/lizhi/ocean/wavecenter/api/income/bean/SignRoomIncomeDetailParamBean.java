package fm.lizhi.ocean.wavecenter.api.income.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * @description: 厅收入入参
 * @author: guoyibin
 * @create: 2024/04/29 14:08
 */
@Data
@Accessors(chain = true)
public class SignRoomIncomeDetailParamBean {

    private Integer appId;

    private Long familyId;

    private Long njId;

    private Date startDate;

    private Date endDate;

    private Integer page = 1;

    private Integer pageSize = 20;

    /**
     * 厅数据范围
     * @since 1.3.1
     */
    private List<Long> roomIds;
}
