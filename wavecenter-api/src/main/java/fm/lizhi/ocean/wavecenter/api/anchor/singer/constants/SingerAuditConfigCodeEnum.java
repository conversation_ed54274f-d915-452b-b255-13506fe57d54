package fm.lizhi.ocean.wavecenter.api.anchor.singer.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 歌手认证配置code枚举
 */
@Getter
@AllArgsConstructor
public enum SingerAuditConfigCodeEnum {

    /**
     * 现场录制审核
     */
    LIVE_RECORD_AUDIT("enabledVerifyLipSync",AuditSceneEnum.FRONTEND.getScene(), "现场录制审核"),

    /**
     * 噪音检测
     */
    NOISE_AUDIT("enabledVerifyNoise",AuditSceneEnum.FRONTEND.getScene(), "噪音检测"),

    /**
     * 签约厅状态检测
     */
    SIGNED_HALL_STATUS("signedHallStatus",AuditSceneEnum.BACKEND.getScene(), "签约厅状态")


    ;


    private String code;

    /**
     * 审核场景，1：前端审核，2：后端审核
     */
    private Integer auditScene;


    /**
     * 描述
     */
    private String desc;

    @Getter
    @AllArgsConstructor
    public enum AuditSceneEnum {
        FRONTEND(1, "前端审核"),
        BACKEND(2, "服务端审核");

        private Integer scene;
        private String desc;
    }

    public static SingerAuditConfigCodeEnum getByCode(String code) {
        for (SingerAuditConfigCodeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
