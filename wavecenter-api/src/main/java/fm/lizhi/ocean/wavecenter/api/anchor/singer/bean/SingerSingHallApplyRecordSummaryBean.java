package fm.lizhi.ocean.wavecenter.api.anchor.singer.bean;

import java.util.Date;

import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import lombok.*;
import lombok.experimental.Accessors;

/**
 *
 * 点唱厅申请记录-汇总
 *
 * <AUTHOR>
 * @date 2025-03-27 03:27:23
 */
@Data
@Accessors(chain = true)
public class SingerSingHallApplyRecordSummaryBean {
    /**
     * ID
     */
    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 家族ID
     */
    private Long familyId;

    /**
     * 家族信息
     */
    private FamilyBean familyInfo;

    /**
     * 厅主ID
     */
    private Long njId;

    /**
     * 厅主信息
     */
    private UserBean njInfo;

    /**
     * 审核状态，0-审核中，1-审核通过，2-审核未通过
     */
    private Integer auditStatus;


    /**
     * 认证歌手数
     */
    private Long singerAuthCnt;

    /**
     * 优质歌手数
     */
    private Long seniorSingerAuthCnt;

    /**
     * 提交时间
     */
    private Long applyTime;

    /**
     * 添加时间
     */
    private Long createTime;

    /**
     * 操作人
     */
    private String operator;
}