package fm.lizhi.ocean.wavecenter.api.income.bean;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import fm.lizhi.ocean.wavecenter.api.income.constants.IncomeType;
import lombok.Builder;
import lombok.Getter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/28 14:47
 */
@Getter
@Builder
public class GetRoomIncomeDetailParamBean {

    private Integer appId;

    private Long familyId;

    /**
     * 开始时间
     */
    private Date startDate;

    private Date endDate;

    private Integer pageNo;

    private Integer pageSize;

    private Long roomId;

    /**
     * 收入类型
     */
    private List<IncomeType> incomeType;

    private Long flushTime;


    public static class GetRoomIncomeDetailParamBeanBuilder{

        public GetRoomIncomeDetailParamBean build(){
            ApiAssert.notNull(appId, "appId is required");
            ApiAssert.notNull(familyId, "familyId is required");
            ApiAssert.notNull(startDate, "startDate is required");
            ApiAssert.notNull(endDate, "endDate is required");
            ApiAssert.notNull(roomId, "roomId is required");
//            ApiAssert.notNull(incomeType, "incomeType is required");
            if(flushTime==null || flushTime<=0){
                flushTime = System.currentTimeMillis();
            }
            if (pageNo == null || pageNo <= 0) {
                pageNo = 1;
            }
            if (pageSize == null || pageSize <= 0) {
                pageSize = 20;
            }
            return new GetRoomIncomeDetailParamBean(appId, familyId, startDate, endDate, pageNo, pageSize, roomId, incomeType,flushTime);
        }
    }

}
