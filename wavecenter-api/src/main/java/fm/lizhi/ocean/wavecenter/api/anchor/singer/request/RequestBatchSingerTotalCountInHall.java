package fm.lizhi.ocean.wavecenter.api.anchor.singer.request;

import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class RequestBatchSingerTotalCountInHall implements IContextRequest {

    private Integer appId;

    /**
     * 厅主ID
     */
    private List<Long> njId;

    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}
