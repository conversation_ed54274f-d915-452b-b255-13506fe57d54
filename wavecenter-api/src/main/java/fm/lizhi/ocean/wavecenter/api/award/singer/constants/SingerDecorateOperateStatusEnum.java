package fm.lizhi.ocean.wavecenter.api.award.singer.constants;

import lombok.Getter;

/**
 * 用户装扮处理状态
 * <AUTHOR>
 */
@Getter
public enum SingerDecorateOperateStatusEnum {
    /**
     * 未处理
     */
    UNPROCESSED(1),

    /**
     * 成功
     */
     SUCCESS(2),

    /**
     * 失败
     */
    FAIL(3),
    ;

    SingerDecorateOperateStatusEnum(int status) {
        this.status = status;
    }

    private int status;
}
