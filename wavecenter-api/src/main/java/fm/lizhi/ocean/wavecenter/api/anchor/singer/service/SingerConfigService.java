package fm.lizhi.ocean.wavecenter.api.anchor.singer.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestSaveApplyMenuConfig;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestUpdateSingerAuditConfig;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestSingerChatScene;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.*;

import java.util.List;

import javax.validation.Valid;

/**
 * 歌手认证服务
 */
public interface SingerConfigService {

    /**
     * 配置操作失败错误码
     */
    int SINGER_CONFIG_FAILED = 1001;

    /**
     * 获取歌手认证配置
     *
     * @return 歌手认证配置
     */
    Result<ResponseGetSingerPreAuditConfig> getSingerAudioAuditConfig(int appId, int singerType);

    /**
     * 获取歌手认证枚举
     *
     * @return 歌手认证枚举
     */
    Result<ResponseSingerEnumerateConfig> getEnumerateConfig(int appId);

    /**
     * 保存预审核配置
     *
     * @param request 请求参数
     * @return 结果
     */
    Result<Void> updateSingerAuditConfig(@Valid RequestUpdateSingerAuditConfig request);

    /**
     * 获取歌手预审核配置
     *
     * @return 歌手认证配置
     */
    Result<ResponseSingerAuditConfig> getSingerPreAuditConfig(int appId);

    /**
     * 新增私信场景配置
     *
     * @param request 请求参数
     * @return 结果
     */
    Result<Void> addSingerChatScene(RequestSingerChatScene request);

    /**
     * 删除私信场景配置
     *
     * @param appId 应用ID
     * @param sceneCode 场景码
     * @return 结果
     */
    Result<Void> deleteSingerChatScene(Integer appId, String sceneCode);

    /**
     * 查询私信场景配置
     *
     * @param appId      应用ID
     * @param singerType 歌手类型
     * @param sceneCode  场景码，可选
     * @return 配置信息列表
     */
    Result<List<ResponseSingerChatScene>> getSingerChatScene(Integer appId, Integer singerType, String sceneCode);


    /**
     * 保存审核入口配置
     */
    @Deprecated
    Result<Void> saveApplyMenuConfig(RequestSaveApplyMenuConfig request);

    /**
     * 获取审核入口配置
     */
    @Deprecated
    Result<ResponseApplyMenuConfig> getApplyMenuConfig(int appId, Integer singerType);

    /**
     * 获取歌手曲风数量配置
     */
    Result<ResponseSongStyleConfig> getSongStyleConfig(int appId);


}
