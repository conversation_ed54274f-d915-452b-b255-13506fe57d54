package fm.lizhi.ocean.wavecenter.api.activitycenter.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 素材分类枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ActivityFodderClassificationEnum implements TypeNameProvider {

    /**
     * 模板封面
     */
    TEMPLATE_COVER(1, "模板封面"),

    /**
     * 节目单封面
     */
    PROGRAM_COVER(2, "节目单封面"),

    /**
     * 官频房封面
     */
    OFFICIAL_CHANNEL_COVER(3, "官频房封面"),


    /**
     * 宣传横幅
     */
    PROMOTIONAL_BANNER(4, "宣传横幅");

    private Integer type;

    private String name;

}
