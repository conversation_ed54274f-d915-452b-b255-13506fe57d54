package fm.lizhi.ocean.wavecenter.api.common.bean;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/4/20 14:29
 */
@Getter
@Builder
public class SaveConfigReqBean {

    private Integer appId;

    private Long userId;

    /**
     * 配置json字符串
     */
    private String config;

    /**
     * 页面code
     */
    private String pageCode;

    public static class SaveConfigReqBeanBuilder{
        public SaveConfigReqBean build(){
            ApiAssert.notNull(appId, "appId can not be null");
            ApiAssert.notNull(userId, "userId can not be null");
            ApiAssert.hasText(config, "config can not be null");
            ApiAssert.hasText(pageCode, "pageCode can not be null");
            return new SaveConfigReqBean(appId, userId, config, pageCode);
        }
    }

}
