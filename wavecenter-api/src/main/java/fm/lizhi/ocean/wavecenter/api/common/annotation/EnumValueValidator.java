package fm.lizhi.ocean.wavecenter.api.common.annotation;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Objects;

/**
 * {@link EnumValue}注解验证器实现
 */
class EnumValueValidator implements ConstraintValidator<EnumValue, Object> {

    /**
     * 枚举类型
     */
    private Class<? extends Enum<?>> enumType;
    /**
     * 取值方法
     */
    private Method getValueMethod;

    @Override
    public void initialize(EnumValue constraintAnnotation) {
        enumType = constraintAnnotation.value();
        getValueMethod = findGetValueMethod(enumType, constraintAnnotation.fieldName());
    }

    private Method findGetValueMethod(Class<? extends Enum<?>> clazz, String fieldName) {
        try {
            BeanInfo beanInfo = Introspector.getBeanInfo(clazz);
            for (PropertyDescriptor propertyDescriptor : beanInfo.getPropertyDescriptors()) {
                if (propertyDescriptor.getName().equals(fieldName) && propertyDescriptor.getReadMethod() != null) {
                    return propertyDescriptor.getReadMethod();
                }
            }
            throw new IllegalArgumentException(String.format("Fail to find getValue method from %s with field name %s",
                    clazz.getName(), fieldName));
        } catch (IntrospectionException e) {
            throw new IllegalArgumentException(String.format("Fail to find getValue method from %s with field name %s",
                    clazz.getName(), fieldName), e);
        }
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (value == null) {
            // 校验是否为null应该使用@NotNull, 所以如果为null直接返回true
            return true;
        }
        for (Enum<?> enumConstant : enumType.getEnumConstants()) {
            try {
                Object enumValue = getValueMethod.invoke(enumConstant);
                if (Objects.equals(value, enumValue)) {
                    return true;
                }
            } catch (InvocationTargetException | IllegalAccessException | RuntimeException e) {
                throw new IllegalArgumentException("Fail to invoke getter method " + getValueMethod, e);
            }
        }
        return false;
    }
}
