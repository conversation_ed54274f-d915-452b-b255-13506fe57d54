package fm.lizhi.ocean.wavecenter.api.background.activitycenter.response;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityTemplateStatusEnum;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateHighlightBean;
import lombok.Data;

import java.util.List;

/**
 * 获取活动模板上下架状态响应
 */
@Data
public class ResponseGetActivityTemplateShelfStatus {

    /**
     * 活动模板id
     */
    private Long id;

    /**
     * 封面地址
     */
    private String cover;

    /**
     * 上下架状态
     *
     * @see ActivityTemplateStatusEnum
     */
    private Integer status;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 是否热门推荐
     */
    private Boolean hotRec;

    /**
     * 热门推荐权重
     */
    private Integer hotWeight;

    /**
     * 活动模板亮点标签列表
     */
    private List<ActivityTemplateHighlightBean> highlights;

    /**
     * 上架开始时间
     */
    private Long upStartTime;

    /**
     * 上架结束时间
     */
    private Long upEndTime;

    /**
     * 厅主白名单列表
     */
    private List<Long> njWhiteList;
}
