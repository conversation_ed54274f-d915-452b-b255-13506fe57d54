package fm.lizhi.ocean.wavecenter.api.award.singer.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RequestUpdateSingerDecorateRule implements IContextRequest {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空")
    private Long id;

    /**
     * 应用ID
     */
    @NotNull(message = "应用ID不能为空")
    @AppEnumId
    private Integer appId;

    /**
     * 歌手认证等级
     */
    private Integer singerType;

    /**
     * 曲风
     */
    private String songStyle;

    /**
     * 装扮类型
     */
    private Integer decorateType;

    /**
     * 装扮ID
     */
    private Long decorateId;


    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 是否删除
     */
    private Boolean deleted;

    /**
     * 操作人
     */
    @NotNull(message = "操作人不能为空")
    private String operator;


    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}
