package fm.lizhi.ocean.wavecenter.api.permissions.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/12/4 14:22
 */
@Data
@Accessors(chain = true)
public class RequestGetRoleRoomDataScope implements IContextRequest {

    @AppEnumId
    @NotNull(message = "appId is null")
    private Integer appId;

    @NotNull(message = "userId is null")
    private Long userId;

    @NotNull(message = "role is null")
    private RoleEnum role;

    @NotNull(message = "familyId is null")
    private Long familyId;

    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}
