package fm.lizhi.ocean.wavecenter.api.datacenter.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/18 15:14
 */
@Data
public class RequestGuildGetIndicatorTrend implements IContextRequest {

    @AppEnumId
    @NotNull(message = "appId is null")
    private Integer appId;

    @NotNull(message = "familyId is null")
    private Long familyId;

    @NotBlank(message = "metric is blank")
    private String metric;

    private List<Long> roomIds;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
