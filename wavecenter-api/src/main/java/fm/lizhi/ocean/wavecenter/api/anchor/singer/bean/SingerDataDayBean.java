package fm.lizhi.ocean.wavecenter.api.anchor.singer.bean;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 *
 * 歌手汇总-天
 * <AUTHOR>
 * @date 2025-03-27 03:27:23
 */
@Data
@Accessors(chain = true)
public class SingerDataDayBean {
    /**
     * ID
     */
    private Long id;

    /**
     * 业务
     */
    private Integer appId;

    /**
     * 日期 格式 YYYY-MM-DD
     */
    private Long statDate;

    /**
     * 日期 格式 YYYYMMDD
     */
    private Integer statDateValue;

    /**
     * 歌手ID
     */
    private Long userId;

    /**
     * 厅主ID
     */
    private Long njId;

    /**
     * 家族ID
     */
    private Long familyId;

    /**
     * 近一日营收（钻石）
     */
    private Long lastDayIncome;

    /**
     * 近一周营收（钻石）
     */
    private Long lastWeekIncome;

    /**
     * 近一个月营收（钻石）
     */
    private Long lastMonthIncome;

    /**
     * 近一个月上麦数
     */
    private Long lastMonthUpMicCnt;
}