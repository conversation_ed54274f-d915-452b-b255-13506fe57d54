package fm.lizhi.ocean.wavecenter.api.grow.level.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.grow.level.bean.FamilyLevelConfigAwardBean;
import fm.lizhi.ocean.wavecenter.api.grow.level.request.RequestDeleteFamilyLevelConfig;
import fm.lizhi.ocean.wavecenter.api.grow.level.request.RequestGetFamilyLevelConfigList;
import fm.lizhi.ocean.wavecenter.api.grow.level.request.RequestSaveFamilyLevelConfig;

import javax.validation.Valid;
import java.util.List;

/**
 * 公会等级配置
 * <p>
 * 错误码以227开头, 详细规范参考
 * <a href="https://lizhi2021.feishu.cn/wiki/Ld0Rw5uvGiUimnkgez5cSVJ9n1c">错误码规范</a>
 * <p>
 * 通用错误码见{@link fm.lizhi.ocean.wavecenter.api.common.service.CommonService}
 * <AUTHOR>
 * @date 2025/3/18 16:57
 */
public interface FamilyLevelConfigService {

    /**
     * 保存等级配置
     * @param request
     * @return
     */
    Result<Void> save(@Valid RequestSaveFamilyLevelConfig request);

    /**
     * 删除等级配置
     * @param request
     * @return
     */
    Result<Void> delete(@Valid RequestDeleteFamilyLevelConfig request);

    /**
     * 查询等级配置
     * @param request
     * @return
     */
    Result<List<FamilyLevelConfigAwardBean>> list(RequestGetFamilyLevelConfigList request);

    /**
     * 等级已存在
     */
    int SAVE_LEVEL_EXIST = 2270001;

    /**
     * 收入范围覆盖到旧等级
     */
    int SAVE_LEVEL_INCOME_COVER = 2270002;


}
