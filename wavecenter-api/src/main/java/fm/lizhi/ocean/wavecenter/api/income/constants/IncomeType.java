package fm.lizhi.ocean.wavecenter.api.income.constants;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 收入类型
 * <AUTHOR>
 * @date 2024/4/23 14:50
 */
public enum IncomeType {

    /**
     * 签约厅收礼
     */
    SIGN_HALL_INCOME(2)

    /**
     * PGC房非签约送礼
     */
    , PGC_UN_SIGN_HALL_INCOME(505)

    /**
     * 个播收礼
     */
    , PERSONAL_HALL_INCOME(528)

    /**
     * 签约厅贵族提成 购买
     */
    , SIGN_ROOM_VIP_INCOME_BUY(376)

    /**
     * 签约厅贵族提成 续费
     */
    , SIGN_ROOM_VIP_INCOME_CONT(377)

    /**
     * 个播贵族提成 购买
     */
    , PLAYER_VIP_BUY(529)

    /**
     * 个播贵族提成 续费
     */
    , PLAYER_VIP_CONT(530)

    /**
     * 官方厅收礼
     */
    , OFFICIAL_INCOME(2010008)

    /**
     * 业绩结算
     */
    , PERFORMANCE_SETTLEMENT(360)
    ;

    @Getter
    private int value;

    private static final Map<Integer, IncomeType> map = new HashMap<>();

    static {
        for (IncomeType type : IncomeType.values()) {
            map.put(type.value, type);
        }
    }

    IncomeType(int value) {
        this.value = value;
    }

    public static IncomeType valueOf(int value) {
        return map.get(value);
    }
}
