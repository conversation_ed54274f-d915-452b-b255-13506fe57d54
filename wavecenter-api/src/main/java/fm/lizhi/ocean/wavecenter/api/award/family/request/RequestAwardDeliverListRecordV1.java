package fm.lizhi.ocean.wavecenter.api.award.family.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/3/26 11:54
 */
@Data
@Accessors(chain = true)
public class RequestAwardDeliverListRecordV1 implements IContextRequest {

    @NotNull(message = "appId is null")
    @AppEnumId
    private Integer appId;

    private Integer pageNumber = 1;

    private Integer pageSize = 20;

    /**
     * 公会长用户id
     */
    private Long familyUserId;

    /**
     * 公会id
     */
    private Long familyId;

    /**
     * 公会名称（模糊搜索）
     */
    private String familyName;

    /**
     * 最小发放时间, 毫秒时间戳, 包含
     */
    private Long minDeliverTime;

    /**
     * 最大发放时间, 毫秒时间戳, 包含
     */
    private Long maxDeliverTime;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
