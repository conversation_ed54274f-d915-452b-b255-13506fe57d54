package fm.lizhi.ocean.wavecenter.api.anchor.singer.request;

import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class RequestGetSingerVerifyRecord implements IContextRequest {

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 厅ID
     */
    private Long njId;

    /**
     * 厅主波段号
     */
    private String njBand;

    private Integer pageNo;

    private Integer pageSize;

    /**
     * 是否在黑名单中
     */
    private Boolean inBlackList;

    /**
     * 歌手类型
     */
    private Integer singerType;

    /**
     * 最小申请时间
     */
    private Long minApplyTime;

    /**
     * 最大申请时间
     */
    private Long maxApplyTime;

    /**
     * 歌曲风格
     */
    private List<String> songStyle;

    /**
     * 审核状态 0: 未审核 1: 审核中 2: 审核通过 3: 审核不通过
     */
    private List<Integer> auditStatus;

    /**
     * 审核结果
     */
    private Integer auditResult;

    /**
     * 是否原创
     */
    private Boolean originalSinger;

    /**
     * 歌手波段号
     */
    private String singerBand;

    /**
     * 排序字段
     * 支持：createTime, auditTime
     */
    private String orderMetrics;

    /**
     * 排序方向
     * ASC-升序, DESC-降序，默认DESC
     */
    private OrderType orderType;


    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}
