package fm.lizhi.ocean.wavecenter.api.anchor.singer.response;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerFamilyInfoBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerUserInfoBean;
import lombok.Data;

@Data
public class ResponseQueryHistoryVerifyRecord {

    /**
     * 歌手信息
     */
    private SingerUserInfoBean singerUserInfo;

    /**
     * 家族信息
     */
    private SingerFamilyInfoBean familyInfo;

    /**
     * 歌曲名称
     */
    private String songName;

    /**
     * 歌曲风格
     */
    private String songStyle;

    /**
     * 签约信息
     */
    private SingerUserInfoBean njInfo;

    /**
     * 是否为原唱
     */
    private Boolean originalSinger;

    /**
     * 歌手状态
     */
    private Integer singerStatus;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 审核通过时间
     */
    private Long passTime;

    /**
     * 淘汰时间
     */
    private Long eliminateTime;

    /**
     * 淘汰原因
     */
    private String eliminateReason;
}
