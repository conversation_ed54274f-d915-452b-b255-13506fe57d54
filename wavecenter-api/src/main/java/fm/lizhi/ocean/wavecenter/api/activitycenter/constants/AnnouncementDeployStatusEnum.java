package fm.lizhi.ocean.wavecenter.api.activitycenter.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum AnnouncementDeployStatusEnum {

    /**
     * 待设置
     */
    WAIT_SET(0),

    /**
     * 设置失败
     */
    SET_FAIL(1),

    /**
     * 设置成功
     */
    SET_SUCCESS(2),

    /**
     * 恢复失败
     */
    RECOVER_FAIL(3),

    /**
     * 恢复成功
     */
    RECOVER_SUCCESS(4);

    private int status;
}
