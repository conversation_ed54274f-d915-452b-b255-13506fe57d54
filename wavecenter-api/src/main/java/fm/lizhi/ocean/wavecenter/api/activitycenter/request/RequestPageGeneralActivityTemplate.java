package fm.lizhi.ocean.wavecenter.api.activitycenter.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * 分页查询通用活动模板请求
 */
@Data
public class RequestPageGeneralActivityTemplate implements IContextRequest {

    /**
     * 分页页码
     */
    @NotNull(message = "分页页码不能为空")
    private Integer pageNo;

    /**
     * 分页大小
     */
    @NotNull(message = "分页大小不能为空")
    @Max(value = 1000, message = "分页大小不能超过1000")
    private Integer pageSize;

    /**
     * 应用id
     */
    @NotNull(message = "应用id不能为空")
    @AppEnumId
    private Integer appId;

    /**
     * 分类id列表, 如果是全部则把所有的分类id传进来
     */
    private List<Long> classIds;

    /**
     * 请求用户id
     */
    private Long requestUserId;

    /**
     * 需要过滤的白名单厅主ID
     */
    private List<Long> njIds = new ArrayList<>();

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
