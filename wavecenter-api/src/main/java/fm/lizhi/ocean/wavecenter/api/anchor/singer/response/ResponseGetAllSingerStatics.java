package fm.lizhi.ocean.wavecenter.api.anchor.singer.response;

import lombok.Data;
import lombok.experimental.Accessors;
@Data
@Accessors(chain = true)
public class ResponseGetAllSingerStatics {

    /**
     * 全平台歌手总数
     */
    private Integer allSingerCnt;

    /**
     * 全平台歌手收入总数
     */
    private Integer allIncomeSingerCnt;

    /**
     * 高级歌手数量
     */
    private int seniorSingerCnt;

    /**
     * 有收入的高级歌手数量
     */
    private int allIncomeSeniorSingerCnt;

    /**
     * 原创高级歌手数量
     */
    private int originalSeniorSinger;

    /**
     * 有收入的原创高级歌手数量
     */
    private int allIncomeOriginalSeniorSingerCnt;

}
