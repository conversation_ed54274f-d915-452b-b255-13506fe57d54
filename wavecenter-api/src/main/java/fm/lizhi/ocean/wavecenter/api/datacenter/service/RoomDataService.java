package fm.lizhi.ocean.wavecenter.api.datacenter.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.*;

import java.util.Date;
import java.util.List;

/**
 * 公会数据
 * <AUTHOR>
 * @date 2024/4/17 17:03
 */
public interface RoomDataService {

    /**
     * 厅考核业绩-查询
     * @param appId
     * @param roomId
     * @return
     */
    Result<RoomAssessmentInfoBean> getAssessmentInfo(int appId, long roomId, long familyId);

    /**
     * 厅数据-考核周期列表
     * @param paramBean
     * @return
     */
    Result<RoomPlayerPerformanceResBean> getPlayerPerformance(GetRoomPlayerPerformanceBean paramBean);

    /**
     * 查询关键指标
     * @param paramBean
     * @return
     */
    Result<List<IndicatorBean>> getKeyIndicators(RoomGetKeyIndicatorsParamBean paramBean);

    /**
     * 查询趋势图数据
     * @param appId
     * @param roomId
     * @param metric
     * @return
     */
    Result<IndicatorTrendResBean> getIndicatorTrend(int appId, Long familyId, long roomId, String metric);


    /**
     * 查询厅有收入主播数
     */
    Result<Integer> getPlayerPayCountInCurrentFamily(int appId, Long roomId, Long startDate, Long endDate);


    int ASSESSMENT_INFO_NOT_FOUND = 1;

    int GET_PLAYER_PAY_COUNT_IN_CURRENT_FAMILY_NO_FOUND = 1;

}
