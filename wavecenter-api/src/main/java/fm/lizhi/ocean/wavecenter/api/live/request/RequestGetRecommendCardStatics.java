package fm.lizhi.ocean.wavecenter.api.live.request;

import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description: 推荐卡曝光数据查询
 * @author: guoyibin
 * @create: 2025/03/26 15:27
 */
@Data
public class RequestGetRecommendCardStatics implements IContextRequest {

    /**
     * 推荐卡业务使用记录ID
     */
    @NotNull(message = "应用ID不能为空")
    private int appId;

    @NotEmpty(message = "推荐卡业务使用记录ID不能为空")
    private List<Long> recommendCardRecordIds;

    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}
