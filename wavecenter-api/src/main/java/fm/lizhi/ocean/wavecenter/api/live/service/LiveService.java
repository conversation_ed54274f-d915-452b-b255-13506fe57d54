package fm.lizhi.ocean.wavecenter.api.live.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.live.bean.LiveInfoBean;
import fm.lizhi.ocean.wavecenter.api.live.response.ResponseGetRoomInfoByNjId;

/**
 * 直播相关服务
 * <AUTHOR>
 * @date 2024/8/26 14:28
 */
public interface LiveService {

    /**
     * 通过直播ID查询直播信息
     * @param appId
     * @param liveId
     * @return
     */
    Result<LiveInfoBean> getLiveInfo(int appId, long liveId);

    /**
     * 通过厅主ID查询直播房间信息
     *
     * @param appId 应用ID
     * @param njId  厅主ID
     * @return 直播房间信息
     */
    Result<ResponseGetRoomInfoByNjId> getRoomInfoByNjId(int appId, long njId);

    /**
     * 直播间找不到
     */
    int LIVE_NOT_FOUNT = 1;

    /**
     * 通过厅主ID查询直播房间信息失败
     */
    int GET_ROOM_INFO_BY_NJ_ID_FAIL = 1;
    /**
     * 通过厅主ID查询直播房间信息不存在
     */
    int GET_ROOM_INFO_BY_NJ_ID_NO_EXIST = 2;
}
