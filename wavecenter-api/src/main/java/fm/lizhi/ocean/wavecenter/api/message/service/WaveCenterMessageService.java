package fm.lizhi.ocean.wavecenter.api.message.service;


import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.message.request.*;
import fm.lizhi.ocean.wavecenter.api.message.response.ResponseGetMessageList;
import fm.lizhi.ocean.wavecenter.api.message.response.ResponseQueryRecentMessages;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface WaveCenterMessageService {


    /**
     * 向指定用户发送消息
     *
     * @return messageId
     */
    Result<Long> sendMessage(RequestSendMessage param);

    /**
     * 批量发送消息
     */
    Result<List<Long>> sendMessageBatch(RequestSendMessageBatch param);


    /**
     * 向指定角色发送消息
     */
    Result<List<Long>> sendMessage2Role(RequestSendMessageToRole param);


    /**
     * 获取消息列表
     */
    Result<ResponseGetMessageList> getMessageList(RequestGetMessageList param);

    /**
     * 批量设置已读
     */
    Result<Void> batchRead(RequestBatchReadMessage param);

    /**
     * 查询用户消息和公告混合列表
     *
     * @param appId    应用ID
     * @param userId   用户ID
     * @param size     条数
     * @param roleCode 可见角色
     * @return 消息公告混合列表
     */
    Result<ResponseQueryRecentMessages> queryRecentMessages(Integer appId, Long userId, Integer size, String roleCode);

    /**
     * 批量发送消息失败
     */
    int SEND_MESSAGE_BATCH_TARGET_UID_EMPTY = 2190001;

    /**
     * 发送角色消息失败
     */
    int SEND_MESSAGE_ROLE_TARGET_ROLE_EMPTY = 2190002;


}
