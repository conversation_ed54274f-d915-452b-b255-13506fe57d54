package fm.lizhi.ocean.wavecenter.api.grow.ability.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.api.grow.ability.request.RequestGetPlayerPerformance;
import fm.lizhi.ocean.wavecenter.api.grow.ability.request.RequestGetRoomPerformance;
import fm.lizhi.ocean.wavecenter.api.grow.ability.request.RequestGetRoomPlayerRank;
import fm.lizhi.ocean.wavecenter.api.grow.ability.response.ResponseGetPlayerPerformance;
import fm.lizhi.ocean.wavecenter.api.grow.ability.response.ResponseGetRoomPerformance;
import fm.lizhi.ocean.wavecenter.api.grow.ability.response.ResponseGetRoomPlayerRank;

import javax.validation.Valid;

/**
 * 成长能力表现服务, 接口错误码以245开头.
 * <ul>
 *     <li>错误码规范参考<a href="https://lizhi2021.feishu.cn/wiki/Kulywk1KliQVGTktY56cny01nvg#JYrkdVrauo2bhbx0IeCcYSjsnMh">错误码规范</a></li>
 *     <li>状态码请维护在<a href="https://lizhi2021.feishu.cn/wiki/Ld0Rw5uvGiUimnkgez5cSVJ9n1c">创作者平台状态码维护文档</a></li>
 *     <li>通用错误码请见{@link CommonService}</li>
 * </ul>
 */
public interface AbilityPerformanceService {

    /**
     * 查询厅能力表现
     *
     * @param request 请求参数
     * @return 响应结果
     */
    Result<ResponseGetRoomPerformance> getRoomPerformance(@Valid RequestGetRoomPerformance request);

    /**
     * 查询厅主播排名
     *
     * @param request 请求参数
     * @return 响应结果
     */
    Result<ResponseGetRoomPlayerRank> getRoomPlayerRank(@Valid RequestGetRoomPlayerRank request);

    /**
     * 查询主播能力表现
     *
     * @param request 请求参数
     * @return 响应结果
     */
    Result<ResponseGetPlayerPerformance> getPlayerPerformance(@Valid RequestGetPlayerPerformance request);

    // ------------------ 方法00, getRoomPerformance ------------------

    // ------------------ 方法01, getRoomPlayerRank ------------------

    // ------------------ 方法02, getPlayerPerformance ------------------
}
