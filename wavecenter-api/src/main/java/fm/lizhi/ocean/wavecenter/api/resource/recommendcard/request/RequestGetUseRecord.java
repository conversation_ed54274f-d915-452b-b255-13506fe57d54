package fm.lizhi.ocean.wavecenter.api.resource.recommendcard.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/3/21 17:15
 */
@Data
public class RequestGetUseRecord implements IContextRequest {

    @NotNull(message = "appId is null")
    @AppEnumId
    private Integer appId;

    private Long userId;

    @NotNull(message = "pageNo is null")
    private Integer pageNo = 1;

    @NotNull(message = "pageSize is null")
    private Integer pageSize = 20;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
