package fm.lizhi.ocean.wavecenter.api.activitycenter.bean;


import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.*;

/**
 * 上周收入门槛阈值（厅）
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LastWeekHallIncomeThresholdRuleBean extends ActivityRuleBaseAbstractBean {

    /**
     * 收入值
     */
    private Long income;

    @Override
    public void verify() {
        ApiAssert.notNull(income, "收入门槛不能为空");
        ApiAssert.lessThen(income, 0L, "收入门槛不能小于0");
    }
}
