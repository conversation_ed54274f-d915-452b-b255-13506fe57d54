package fm.lizhi.ocean.wavecenter.api.resource.recommendcard.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/3/25 11:47
 */
@Data
@Accessors(chain = true)
public class RequestGetAllocationItemList implements IContextRequest {

    @NotNull(message = "appId is null")
    @AppEnumId
    private Integer appId;

    @NotNull(message = "familyId is null")
    private Long familyId;

    private Integer pageNo = 1;

    private Integer pageSize = 20;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
