package fm.lizhi.ocean.wavecenter.api.datacenter.bean;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/4/17 16:48
 */
@Data
@Accessors(chain = true)
public class RoomAssessmentInfoBean extends AssessmentTimeBean{

    /**
     * 刷新时间
     */
    private String flushTime;

    /**
     * 总收入
     */
    private IncomeBean sumIncome;

    /**
     * 厅收礼收入
     */
    private IncomeBean roomIncome;

    /**
     * 官方厅收入
     */
    private IncomeBean officialIncome;

    /**
     * 个播收入
     */
    private IncomeBean individualIncome;

    /**
     * 贵族提成
     */
    private IncomeBean vipIncome;

    /**
     * 考核流水
     */
    private EchelonBean examinationFlow;

    /**
     * 距离下一梯队
     */
    private EchelonBean nextEchelon;

    /**
     * 有收入主播数
     */
    private Integer playerPayCount;

}
