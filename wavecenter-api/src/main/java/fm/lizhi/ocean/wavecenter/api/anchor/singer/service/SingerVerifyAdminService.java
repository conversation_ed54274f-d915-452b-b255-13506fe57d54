package fm.lizhi.ocean.wavecenter.api.anchor.singer.service;

import java.util.List;

import javax.validation.Valid;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestBatchAddBlackList;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestBatchCancelBlackList;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestGetSingerVerifyRecord;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestModifyVerifyApplyRemark;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestQueryHistoryVerifyRecord;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestVerifyAudit;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseBatchCancelBlackList;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseGetSingerVerifyRecord;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseQueryHistoryVerifyRecord;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseVerifyAudit;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;

/**
 * 歌手认证管理员服务
 */
public interface SingerVerifyAdminService {

    /**
     * 歌手认证审核
     *
     * @param request 歌手认证审核请求参数
     * @return 歌手认证审核响应
     */
    Result<ResponseVerifyAudit> verifyAudit(@Valid RequestVerifyAudit request);


    /**
     * 获取歌手认证记录
     *
     * @param request 参数
     * @return 结果
     */
    Result<PageBean<ResponseGetSingerVerifyRecord>> getSingerVerifyRecord(RequestGetSingerVerifyRecord request);

    /**
     * 修改歌手认证申请备注
     *
     * @param request 修改歌手认证申请备注请求
     * @return 结果
     */
    Result<Void> modifyVerifyApplyRemark(RequestModifyVerifyApplyRemark request);


    /**
     * 查询历史歌手认证记录
     *
     * @param request 查询历史歌手认证记录请求
     * @return 结果
     */
    Result<List<ResponseQueryHistoryVerifyRecord>> queryHistoryVerifyRecord(RequestQueryHistoryVerifyRecord request);

    /**
     * 批量拉黑用户
     */
    Result<Void> batchAddBlackList(@Valid RequestBatchAddBlackList request);

    /**
     * 批量取消拉黑用户
     */
    Result<ResponseBatchCancelBlackList> batchCancelBlackList(@Valid RequestBatchCancelBlackList request);
    

    /**
     * 修改备注失败
     */
    int MODIFY_VERIFY_APPLY_REMARK_FAIL = 20410000;

    /**
     * 批量拉黑失败
     */
    int BATCH_ADD_BLACK_LIST_FAIL = 20410001;

    /**
     * 批量拉黑用户未找到
     */
    int BATCH_ADD_BLACK_USER_NOT_FOUND = 20410002;
}
