package fm.lizhi.ocean.wavecenter.api.live.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.LiveSmsPlayerParamBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.LiveSmsRoomParamBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.PlayerSmsStatBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.RoomSmsStatBean;

/**
 * 私信分析
 * <AUTHOR>
 * @date 2024/4/20 16:20
 */
public interface LiveSmsService {

    /**
     * 厅列表
     * @return
     */
    Result<PageBean<RoomSmsStatBean>> roomList(LiveSmsRoomParamBean paramBean);

    /**
     * 主播列表
     * @param paramBean
     * @return
     */
    Result<PageBean<PlayerSmsStatBean>> playerList(LiveSmsPlayerParamBean paramBean);

    /**
     * 无权访问
     */
    int AUTH_ERROR = 1;

}
