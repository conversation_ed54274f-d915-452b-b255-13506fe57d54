package fm.lizhi.ocean.wavecenter.api.anchor.singer.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class SingerPreAuditBean {

    /**
     * 主播审核配置ID
     */
    private Long id;

    /**
     * 配置编码
     */
    private String configCode;

    /**
     * 状态
     */
    private Boolean enabled;

    /**
     * 配置说明
     */
    private String explanation;

    /**
     * 是否可编辑
     */
    private Boolean editable;

}
