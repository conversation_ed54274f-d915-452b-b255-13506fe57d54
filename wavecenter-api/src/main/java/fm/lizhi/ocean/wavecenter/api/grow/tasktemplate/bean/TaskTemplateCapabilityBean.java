package fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 任务模版能力响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskTemplateCapabilityBean {

    /**
     * ID
     */
    private Long id;

    /**
     * 任务模板ID
     */
    private Long templateId;

    /**
     * 能力项code
     */
    private String capabilityCode;

    /**
     * 能力分
     */
    private BigDecimal capabilityScore;

    /**
     * 业务 ID
     */
    private Integer appId;
}
