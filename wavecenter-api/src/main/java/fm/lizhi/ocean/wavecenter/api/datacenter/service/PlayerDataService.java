package fm.lizhi.ocean.wavecenter.api.datacenter.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.IndicatorBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.IndicatorTrendResBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.PlayerAssessmentInfoBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.PlayerGetKeyIndicatorsParamBean;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/21 20:14
 */
public interface PlayerDataService {

    /**
     * 查询趋势图数据
     * @param appId
     * @param playerId
     * @param metric
     * @return
     */
    Result<IndicatorTrendResBean> getIndicatorTrend(int appId, Long familyId, Long roomId, long playerId, String metric);

    /**
     * 查询关键指标
     * @param paramBean
     * @return
     */
    Result<List<IndicatorBean>> getKeyIndicators(PlayerGetKeyIndicatorsParamBean paramBean);

    /**
     * 主播考核业绩-查询
     * @param appId
     * @param playerId
     * @return
     */
    Result<PlayerAssessmentInfoBean> getAssessmentInfo(int appId, long playerId, long familyId, Long roomId);

    int ASSESSMENT_INFO_NOT_FOUND = 1;

}
