package fm.lizhi.ocean.wavecenter.api.datacenter.constants;

/**
 * 家族数据指标常量类
 * <AUTHOR>
 */
public interface GuildDataMetricsConstant {
    /**
     * 总收入
     */
    String INCOME_TOTAL = "incomeTotal";
    /**
     * 厅收礼收入
     */
    String ROOM_GIFT_INCOME = "roomGiftIncome";
    /**
     * 个播收入
     */
    String PERSONAL_LIVE_INCOME = "personalLiveIncome";
    /**
     * 贵族提成收入
     */
    String NOBILITY_UNIT_INCOME = "nobilityUnitIncome";
    /**
     * 魅力值
     */
    String CHARM = "charm";
    /**
     * 签约厅数
     */
    String SIGN_ROOM_CNT = "signRoomCnt";
    /**
     * 开播厅数
     */
    String OPEN_ROOM_CNT = "openRoomCnt";
    /**
     * 厅均收入
     */
    String ROOM_AVG_INCOME = "roomAvgIncome";
    /**
     * 厅均魅力值
     */
    String ROOM_AVG_CHARM = "roomAvgCharm";
    /**
     * 签约主播数
     */
    String SIGN_PLAYER_CNT = "signPlayerCnt";
    /**
     * 上麦主播数
     */
    String UP_GUEST_PLAYER_CNT = "upGuestPlayerCnt";
    /**
     * 有收入主播数
     */
    String INCOME_PLAYER_CNT = "incomePlayerCnt";
    /**
     * 主播人均收入
     */
    String PLAYER_AVG_INCOME = "playerAvgIncome";
    /**
     * 主播人均魅力值
     */
    String PLAYER_AVG_CHARM = "playerAvgCharm";


}
