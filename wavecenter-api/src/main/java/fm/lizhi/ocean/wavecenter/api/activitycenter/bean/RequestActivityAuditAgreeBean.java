package fm.lizhi.ocean.wavecenter.api.activitycenter.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @description: 活动审批通过接口
 * @author: guoyibin
 * @create: 2024/10/24 17:54
 */
public class RequestActivityAuditAgreeBean {

    private Integer appId;

    private Long activityId;

    private String operator;

    private Integer version;
    

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    private List<ActivityFlowResourceAuditBean> flowResourceList;

    public Integer getAppId() {
        return appId;
    }

    public void setAppId(Integer appId) {
        this.appId = appId;
    }

    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    public List<ActivityFlowResourceAuditBean> getFlowResourceList() {
        return flowResourceList;
    }

    public void setFlowResourceList(List<ActivityFlowResourceAuditBean> flowResourceList) {
        this.flowResourceList = flowResourceList;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
}
