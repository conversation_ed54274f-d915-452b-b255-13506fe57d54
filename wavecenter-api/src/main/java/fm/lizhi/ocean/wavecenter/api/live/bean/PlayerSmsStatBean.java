package fm.lizhi.ocean.wavecenter.api.live.bean;

import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 厅私信分析数据
 * <AUTHOR>
 * @date 2024/4/20 15:41
 */
@Data
public class PlayerSmsStatBean {

    private UserBean roomInfo;

    private UserBean playerInfo;

    /**
     * 私信用户数-私信人数  chatCnt
     */
    private Integer chatUserCnt;

    /**
     * 私信回复人数  replyChatCnt
     */
    private Integer replyChatUserCnt;

    /**
     * 私信回复率
     */
    private BigDecimal replyChatRate;

    /**
     * 私信进房人数 chatEnterRoomCnt
     */
    private Integer chatEnterRoomUserCnt;

    /**
     * 私信进房率
     */
    private BigDecimal chatEnterRoomRate;

    /**
     * 邀请人数 inviteCnt
     */
    private Integer inviteUserCnt;

    /**
     * 邀请进房人数 inviteEnterRoomCnt
     */
    private Integer inviteEnterRoomUserCnt;

    /**
     * 邀请付费人数 inviteGiftCnt
     */
    private Integer inviteGiftUserCnt;

    /**
     * 邀请进房率
     */
    private BigDecimal inviteEnterRoomRate;

    /**
     * 邀请付费率
     */
    private BigDecimal inviteGiftRate;

    /**
     * 私信付费人数-私信送礼人数 chatGiftCnt
     */
    private Integer chatGiftUserCnt;


    /**
     * 私信付费率-私信送礼率
     */
    private BigDecimal chatGiftRate;


}
