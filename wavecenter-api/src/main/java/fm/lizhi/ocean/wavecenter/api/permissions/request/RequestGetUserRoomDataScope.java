package fm.lizhi.ocean.wavecenter.api.permissions.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/12/4 19:40
 */
@Data
@Accessors(chain = true)
public class RequestGetUserRoomDataScope implements IContextRequest {

    @AppEnumId
    @NotNull(message = "appId is null")
    private Integer appId;

    @NotNull(message = "userId is null")
    private Long userId;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
