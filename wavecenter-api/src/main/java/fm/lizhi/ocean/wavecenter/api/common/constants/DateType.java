package fm.lizhi.ocean.wavecenter.api.common.constants;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/4/19 14:25
 */
@Getter
public enum DateType {

    DAY("day"), WEEK("week"), MONTH("month");

    private String value;

    private static Map<String, DateType> valueMap = new HashMap<>();

    static {
        for (DateType type : DateType.values()) {
            valueMap.put(type.value, type);
        }
    }

    DateType(String value) {
        this.value = value;
    }

    public static DateType fromValue(String value) {
        return valueMap.get(value);
    }
}
