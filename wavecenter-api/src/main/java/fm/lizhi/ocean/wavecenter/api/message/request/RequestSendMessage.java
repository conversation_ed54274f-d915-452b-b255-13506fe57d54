package fm.lizhi.ocean.wavecenter.api.message.request;

import fm.lizhi.ocean.wavecenter.api.message.constant.MessageTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RequestSendMessage {

    /**
     * 标题，可为空
     */
    private String title;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 目标链接， 只有 MessageTargetLinkEnum.EXTERNAL_JUMP 时， 此字段才生效
     */
    private String targetLink;

    /**
     * 跳转类型
     */
    private String linkType;

    /**
     * 目标用户 ID
     */
    private Long targetUserId;

    /**
     * 发送用户 ID
     */
    private Long sendUserId;

    /**
     * 类型
     * @see MessageTypeEnum
     */
    private Integer type;

    /**
     * 应用 ID
     */
    private int appId;

    /**
     * 业务 ID
     */
    private Long bizId;





}
