package fm.lizhi.ocean.wavecenter.api.anchor.singer.constants;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 歌手预审核状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum SingerPreAuditStatusEnum {

    /**
     * 通过
     */
    PASSED(1, "通过"),

    /**
     * SDK无法确定
     */
    UNABLE_DETERMINE(2, "无法区分是否现场录制，需人工判断"),

    /**
     * SDK监测非现场录制
     */
    REJECT(3, "非现场录制"),

    /**
     * 噪音过大
     */
    NOISE_TOO_LARGE(4, "噪音过大"),
    ;


    public static SingerPreAuditStatusEnum getByStatus(Integer status) {
        for (SingerPreAuditStatusEnum value : values()) {
            if (value.getStatus() == status) {
                return value;
            }
        }
        return null;
    }

    private final int status;

    private final String msg;
}
