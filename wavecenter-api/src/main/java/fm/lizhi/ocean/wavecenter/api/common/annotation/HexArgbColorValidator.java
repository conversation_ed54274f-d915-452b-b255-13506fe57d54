package fm.lizhi.ocean.wavecenter.api.common.annotation;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.regex.Pattern;

/**
 * {@link HexArgbColor}注解验证器实现
 */
class HexArgbColorValidator implements ConstraintValidator<HexArgbColor, String> {

    private static final Pattern PATTERN = Pattern.compile("^[0-9a-fA-F]{8}$");

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }
        return PATTERN.matcher(value).find();
    }
}
