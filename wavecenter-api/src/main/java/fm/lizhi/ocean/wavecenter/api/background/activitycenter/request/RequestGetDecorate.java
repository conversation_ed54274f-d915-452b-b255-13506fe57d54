package fm.lizhi.ocean.wavecenter.api.background.activitycenter.request;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.DecorateEnum;
import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class RequestGetDecorate {


    private int appId;

    /**
     * 类型 1：头像框，2：背景
     * @see DecorateEnum
     */
    private Integer type;

    /**
     * 装扮ID
     */
    private Long dressUpId;

    /**
     * 装扮名称
     */
    private String name;

    private int pageNo;

    private int pageSize;


    private RequestGetDecorate(RequestGetDecorate.RequestGetDecorateBuilder builder) {
        this.name = builder.name;
        this.appId = builder.appId;
        this.type = builder.type;
        this.pageNo = builder.pageNo;
        this.pageSize = builder.pageSize;
        this.dressUpId = builder.dressUpId;
    }

    public static class RequestGetDecorateBuilder {

        public RequestGetDecorate build() {
            ApiAssert.notNull(appId, "appId is null");
            ApiAssert.notNull(type, "type is null");
            if (pageNo <= 0) {
                pageNo = 1;
            }
            if (pageSize <= 0) {
                pageSize = 20;
            }
            return new RequestGetDecorate(this);

        }
    }




}
