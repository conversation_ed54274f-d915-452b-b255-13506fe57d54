package fm.lizhi.ocean.wavecenter.api.activitycenter.bean;

import lombok.*;

import java.util.Date;

/**
 *
 * 活动图片素材
 *
 * <AUTHOR>
 * @date 2024-10-14 11:14:39
 */
@Data
@Builder
public class ActivityImageFodderBean {


    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 素材名称
     */
    private String name;

    /**
     * 素材分类，参考枚举值
     */
    private Integer type;

    /**
     * 素材颜色，包含#
     */
    private String color;

    /**
     * 素材图片地址，斜杠开头
     */
    private String imageUrl;

    /**
     * 宽高比
     */
    private String scale;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 修改时间
     */
    private Long modifyTime;

    /**
     * 创建时间
     */
    private Long createTime;
}