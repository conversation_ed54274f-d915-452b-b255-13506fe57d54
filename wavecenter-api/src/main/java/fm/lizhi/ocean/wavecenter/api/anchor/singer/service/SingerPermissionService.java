package fm.lizhi.ocean.wavecenter.api.anchor.singer.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestCheckSingerPermission;

/**
 * 歌手权限
 * <AUTHOR>
 * @see customer项目：customer-api
 */
@Deprecated
public interface SingerPermissionService {


    /**
     * 校验用户是否具备歌手认证按钮
     */
    @Deprecated
    Result<Boolean> checkSingerPermission(RequestCheckSingerPermission request);
}
