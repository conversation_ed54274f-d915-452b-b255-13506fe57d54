package fm.lizhi.ocean.wavecenter.api.activitycenter.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * ·
 * 活动目标枚举
 *
 * <AUTHOR>
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum ActivityGoalEnum implements TypeNameProvider {

    /**
     * 流水增长
     */
    REVENUE_GROWTH(1, "流水增长"),

    /**
     * 活跃提升
     */
    INCREASED_ACTIVITY(2, "活跃提升")
    ;

    private Integer id;

    private String name;


    @Override
    public Integer getType() {
        return id;
    }

    @Override
    public String getName() {
        return name;
    }
}