package fm.lizhi.ocean.wavecenter.api.common.bean;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/3/20 17:40
 */
@Data
@Accessors(chain = true)
public class ContextRequest implements IContextRequest{

    @NotNull(message = "appId is null")
    @AppEnumId
    private Integer appId;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
