package fm.lizhi.ocean.wavecenter.api.anchor.singer.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ResponseApplyMenuConfig {

    /**
     * 应用ID
     */
    private int appId;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 是否开启
     */
    private boolean enabled;


    /**
     * 入口展示开始时间戳
     */
    private Long startTime;

    /**
     * 入口展示结束时间戳
     */
    private Long endTime;


    /**
     * 歌手类型
     */
    private Integer singerType;

    /**
     * 是否开启弱关联点唱厅
     * TODO 黑叶强行赋值为false
     */
    private boolean openWeakLinkVocalRoom;

}