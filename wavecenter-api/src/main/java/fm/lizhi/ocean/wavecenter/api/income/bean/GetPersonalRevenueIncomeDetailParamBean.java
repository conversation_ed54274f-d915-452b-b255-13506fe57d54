package fm.lizhi.ocean.wavecenter.api.income.bean;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import fm.lizhi.ocean.wavecenter.api.income.constants.IncomeType;
import lombok.Builder;
import lombok.Getter;

import java.util.Date;
import java.util.List;

@Getter
@Builder
public class GetPersonalRevenueIncomeDetailParamBean {

    private Integer appId;
    /**
     * 开始时间
     */
    private Date startDate;

    private Date endDate;

    private Integer pageNo;

    private Integer pageSize;

    private Long userId;

    private List<IncomeType> incomeType;

    private Long flushTime;


    public static class GetPersonalRevenueIncomeDetailParamBeanBuilder{
        public GetPersonalRevenueIncomeDetailParamBean build(){
            ApiAssert.notNull(appId, "appId is required");
            ApiAssert.notNull(startDate, "startDate is required");
            ApiAssert.notNull(endDate, "endDate is required");
            ApiAssert.notNull(userId, "userId is required");
            if (pageNo == null || pageNo <= 0) {
                pageNo = 1;
            }
            if (pageSize == null || pageSize <= 0) {
                pageSize = 20;
            }
            if(flushTime==null || flushTime<=0){
                flushTime = System.currentTimeMillis();
            }
            return new GetPersonalRevenueIncomeDetailParamBean(appId, startDate, endDate, pageNo, pageSize, userId,incomeType,flushTime);
        }
    }

}
