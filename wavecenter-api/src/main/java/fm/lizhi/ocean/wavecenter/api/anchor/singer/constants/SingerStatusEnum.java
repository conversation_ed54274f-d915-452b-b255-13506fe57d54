package fm.lizhi.ocean.wavecenter.api.anchor.singer.constants;


import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 歌手状态枚举
 *
 * <AUTHOR>
 */
@Getter
public enum SingerStatusEnum {

    /**
     * 认证中
     */
    AUTHENTICATING(1),

    /**
     * 生效中
     */
    EFFECTIVE(2),

    /**
     * 已淘汰
     */
    ELIMINATED(3),
    ;


    SingerStatusEnum(int status) {
        this.status = status;
    }

    private final int status;

    public static SingerStatusEnum getByStatus(int status) {
        for (SingerStatusEnum e : SingerStatusEnum.values()) {
            if (e.status == status) {
                return e;
            }
        }
        return null;
    }

    public static List<SingerStatusEnum> getAllSingerStatus() {
        return Arrays.stream(SingerStatusEnum.values()).collect(Collectors.toList());
    }
}
