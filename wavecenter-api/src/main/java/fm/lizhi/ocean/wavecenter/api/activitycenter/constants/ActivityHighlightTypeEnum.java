package fm.lizhi.ocean.wavecenter.api.activitycenter.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ActivityHighlightTypeEnum {

    /**
     * 亮点标签
     */
    HIGHLIGHT(0, "亮点标签"),

    /**
     * 奖励标签
     */
    REWARD(1, "奖励标签");

    private final int type;

    private final String name;

    public static ActivityHighlightTypeEnum getByType(int type) {
        for (ActivityHighlightTypeEnum value : values()) {
            if (value.getType() == type) {
                return value;
            }
        }
        return null;
    }

}
