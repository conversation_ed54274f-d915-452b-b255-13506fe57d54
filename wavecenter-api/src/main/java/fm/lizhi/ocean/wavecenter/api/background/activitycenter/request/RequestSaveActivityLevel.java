package fm.lizhi.ocean.wavecenter.api.background.activitycenter.request;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
@Builder
public class RequestSaveActivityLevel {

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 等级名称
     */
    private String level;

    /**
     * 操作者
     */
    private String operator;

    private RequestSaveActivityLevel(RequestSaveActivityLevel.RequestSaveActivityLevelBuilder builder) {
        this.level = builder.level;
        this.appId = builder.appId;
        this.operator = builder.operator;
    }


    public static class RequestSaveActivityLevelBuilder {

        public RequestSaveActivityLevel build() {

            ApiAssert.notNull(appId, "appId is null");
            ApiAssert.notNull(level, "等级名称不能为空");
            ApiAssert.notNull(operator, "操作人不能为空");

            return new RequestSaveActivityLevel(this);
        }
    }

}