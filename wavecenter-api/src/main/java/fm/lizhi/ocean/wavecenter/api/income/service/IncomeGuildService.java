package fm.lizhi.ocean.wavecenter.api.income.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.GuildIncomeSummaryBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomIncomeDetailBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.*;
import fm.lizhi.ocean.wavecenter.api.income.request.RequestGuildIncomeSummary;
import fm.lizhi.ocean.wavecenter.api.income.request.RequestGuildIncomeStats;
import fm.lizhi.ocean.wavecenter.api.income.response.ResponseGuildIncomeStats;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2024/5/10 08:10
 */
public interface IncomeGuildService {

    /**
     * 公会收入汇总
     * @param familyId 公会ID
     */
    @Deprecated
    Result<GuildIncomeSummaryBean> guildIncomeSummary(long familyId, int appid);

    /**
     * 公会收入汇总
     * 账户收入和日周月收入合计
     * @return
     */
    Result<GuildIncomeSummaryBean> guildIncomeSummaryV2(@Valid RequestGuildIncomeSummary request);

    /**
     * 公会收益-签约厅的收入明细
     * @param signRoomIncomeDetailParamBean
     * @return
     */
    Result<PageBean<RoomIncomeDetailBean>> signRoomIncomeDetail(SignRoomIncomeDetailParamBean signRoomIncomeDetailParamBean);

    /**
     * 公会收益-收入账户明细记录-查询-离线数据
     * 提供给导出使用
     * @param paramBean
     * @return
     */
    Result<PageBean<GuildIncomeDetailBean>> getGuildIncomeDetailOut(GetGuildIncomeDetailParamBean paramBean);

    /**
     * 公会收益-收入账户明细记录-查询-合计
     * @param paramBean
     * @return
     */
    Result<GuildIncomeDetailSumBean> getGuildIncomeDetailSum(GetGuildIncomeDetailSumParamBean paramBean);

    /**
     * 公会收益-收入账户明细记录-查询
     * @param paramBean
     * @return
     */
    Result<PageBean<GuildIncomeDetailBean>> getGuildIncomeDetail(GetGuildIncomeDetailParamBean paramBean);

    /**
     * 查询公会某周期收入概览统计
     * @param request 查询请求
     * @return 分页统计结果
     */
    Result<ResponseGuildIncomeStats> queryGuildIncomeStats(@Valid RequestGuildIncomeStats request);

}
