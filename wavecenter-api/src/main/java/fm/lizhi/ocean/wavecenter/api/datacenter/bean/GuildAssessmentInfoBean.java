package fm.lizhi.ocean.wavecenter.api.datacenter.bean;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/4/17 16:48
 */
@Data
@Accessors(chain = true)
public class GuildAssessmentInfoBean extends AssessmentTimeBean{

    /**
     * 刷新时间-时间戳
     */
    private Long flushTime;

    /**
     * 总收入
     */
    private IncomeBean sumIncome;

    /**
     * 厅收礼收入
     */
    private IncomeBean roomIncome;

    /**
     * 官方厅收入
     */
    private IncomeBean officialIncome;

    /**
     * 个播收入
     */
    private IncomeBean individualIncome;

    /**
     * 贵族提成
     */
    private IncomeBean vipIncome;

    /**
     * 有收入主播数
     */
    private Integer playerPayCount;

}
