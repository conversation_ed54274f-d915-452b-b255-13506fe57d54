package fm.lizhi.ocean.wavecenter.api.background.activitycenter.request;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.DecorateEnum;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class RequestBatchGetDecorate {

    int appId;

    /**
     * 类型 1：头像框，2：背景
     * @see DecorateEnum
     */
    private Integer type;

    /**
     * id 列表
     */
    private List<Long> ids;


}
