package fm.lizhi.ocean.wavecenter.api.anchor.singer.response;

import java.util.List;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.DecorateTypeBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerTypeBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerVerifyAuditStatusBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SongStyleBean;
import lombok.Data;

@Data
public class ResponseSingerEnumerateConfig {

    /**
     * 歌手认证状态枚举
     */
    private List<SongStyleBean> songStyle;

    /**
     * 审核状态
     */
    private List<SingerVerifyAuditStatusBean> singerVerifyAuditStatus;

    /**
     * 歌手类型
     */
    private List<SingerTypeBean> singerType;

    /**
     * 可发放装扮类型
     */
    private List<DecorateTypeBean> decorateType;

}
