package fm.lizhi.ocean.wavecenter.api.datacenter.bean;

import fm.lizhi.ocean.wavecenter.api.common.constants.DateType;
import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;
import lombok.Singular;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/19 14:19
 */
@Getter
@Builder
public class GuildGetKeyIndicatorsParamBean implements IGetKeyIndicatorsParam{

    private Integer appId;

    /**
     * @see DateType
     */
    private DateType dateType;

    /**
     * 开始时间 YYYY-MM-DD
     */
    private String startDate;

    private String endDate;

    /**
     * 只查询值的指标
     */
    @Singular(ignoreNullCollections = true)
    private List<String> valueMetrics;

    /**
     * 需要查询值和环比的指标
     */
    @Singular(ignoreNullCollections = true)
    private List<String> ratioMetrics;

    /**
     * 公会ID
     */
    private Long familyId;

    private List<Long> roomIds;

    public static class GuildGetKeyIndicatorsParamBeanBuilder{
        public GuildGetKeyIndicatorsParamBean build(){
            ApiAssert.notNull(appId, "appId is required");
            ApiAssert.notNull(dateType, "dateType is required");
            ApiAssert.hasText(startDate, "startDate is required");
            ApiAssert.notNull(familyId, "familyId is required");

            if (dateType == DateType.WEEK) {
                ApiAssert.hasText(endDate, "endDate is required");
            }

            if (valueMetrics == null) {
                valueMetrics = new ArrayList<>();
            }
            if (ratioMetrics == null) {
                ratioMetrics = new ArrayList<>();
            }

            return new GuildGetKeyIndicatorsParamBean(appId, dateType
                    , startDate, endDate
                    , valueMetrics, ratioMetrics
                    , familyId, roomIds);
        }
    }

}
