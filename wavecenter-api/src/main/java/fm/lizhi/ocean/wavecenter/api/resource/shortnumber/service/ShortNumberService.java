package fm.lizhi.ocean.wavecenter.api.resource.shortnumber.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.api.resource.shortnumber.request.RequestGetShortNumber;
import fm.lizhi.ocean.wavecenter.api.resource.shortnumber.request.RequestListShortNumber;
import fm.lizhi.ocean.wavecenter.api.resource.shortnumber.response.ResponseGetShortNumber;
import fm.lizhi.ocean.wavecenter.api.resource.shortnumber.response.ResponseListShortNumber;

import javax.validation.Valid;
import java.util.List;

/**
 * 短号服务, 前接口错误码以236开头.
 * <ul>
 *     <li>错误码规范参考<a href="https://lizhi2021.feishu.cn/wiki/Kulywk1KliQVGTktY56cny01nvg#JYrkdVrauo2bhbx0IeCcYSjsnMh">错误码规范</a></li>
 *     <li>状态码请维护在<a href="https://lizhi2021.feishu.cn/wiki/Ld0Rw5uvGiUimnkgez5cSVJ9n1c">创作者平台状态码维护文档</a></li>
 *     <li>通用错误码请见{@link CommonService}</li>
 * </ul>
 */
public interface ShortNumberService {

    /**
     * 列出短号
     *
     * @param request 请求参数
     * @return 列表结果
     */
    Result<List<ResponseListShortNumber>> listShortNumber(@Valid RequestListShortNumber request);

    /**
     * 获取短号
     *
     * @param request 请求参数
     * @return 短号信息
     */
    Result<ResponseGetShortNumber> getShortNumber(@Valid RequestGetShortNumber request);

    // ------------------ 方法00, listShortNumber ------------------

    // ------------------ 方法01, getShortNumber ------------------
}
