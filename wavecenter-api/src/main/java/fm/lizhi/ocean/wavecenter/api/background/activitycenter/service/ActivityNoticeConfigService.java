package fm.lizhi.ocean.wavecenter.api.background.activitycenter.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestDeleteActivityNoticeConfig;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityNoticeConfig;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseGetActivityNoticeConfig;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

public interface ActivityNoticeConfigService {



    /**
     * 获取活动公告配置
     *
     * @param appId 应用id
     * @return 活动公告配置
     */
    Result<List<ResponseGetActivityNoticeConfig>> getNoticeConfig(Integer appId);

    /**
     * 更新活动公告配置
     *
     * @param request 更新活动公告配置参数
     * @return 结果
     */
    Result<Long> updateNoticeConfig(@NotNull @Valid RequestUpdateActivityNoticeConfig request);

    /**
     * 删除活动公告配置
     *
     * @param request 删除活动公告配置参数
     * @return 结果
     */
    Result<Void> deleteNoticeConfig(@NotNull @Valid RequestDeleteActivityNoticeConfig request);

    /**
     * 获取活动公告配置参数无效
     */
    int GET_NOTICE_CONFIG_PARAM_INVALID = 22400000;

    /**
     * 获取活动公告配置失败
     */
    int GET_NOTICE_CONFIG_FAIL = 22400001;

    /**
     * 更新活动公告配置参数无效
     */
    int UPDATE_NOTICE_CONFIG_PARAM_INVALID = 22400002;

    /**
     * 更新活动公告配置失败
     */
    int UPDATE_NOTICE_CONFIG_FAIL = 22400003;
    /**
     * 活动品类已存在
     */
    int UPDATE_NOTICE_CONFIG_EXIST_CATEGORY = 22400004;

    /**
     * 删除活动公告配置失败
     */
    int DELETE_NOTICE_CONFIG_FAIL = 22400005;

}
