package fm.lizhi.ocean.wavecenter.api.activitycenter.bean;

import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateFlowResourceBean;
import lombok.Data;

import java.util.List;

/**
 * 分页查询热门活动模板的结果
 */
@Data
public class ActivityTemplateHotPageBean {

    /**
     * 活动模板id
     */
    private Long id;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 封面地址
     */
    private String cover;

    /**
     * 热门推荐权重
     */
    private Integer hotWeight;

    /**
     * 使用次数
     */
    private String usageCount;

    private List<ActivityTemplateFlowResourceBean> flowResources;

    /**
     * 活动模板亮点标签列表
     */
    private List<ActivityTemplateGeneralHighlightBean> highlights;
}
