package fm.lizhi.ocean.wavecenter.api.datacenter.bean;

import fm.lizhi.ocean.wavecenter.api.common.constants.DateType;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/4/23 15:36
 */
@Getter
@Builder
public class GetSignRoomParamBean {

    private Integer appId;

    /**
     * 请求用户
     */
    private Long userId;

    private Integer pageNo;

    private Integer pageSize;

    private String orderMetrics;

    private OrderType orderType;

    private DateType dateType;

    private Date startDate;

    private Date endDate;

    private Long familyId;

    private Long roomId;

    private boolean filterZero;

    /**
     * 厅数据范围
     * @since 1.4.0
     */
    private List<Long> roomIds;

    public static class GetSignRoomParamBeanBuilder{
        public GetSignRoomParamBean build(){
            ApiAssert.notNull(appId, "appId is null");
            ApiAssert.notNull(userId, "userId is null");
            if (pageNo == null || pageNo <= 0) {
                pageNo = 1;
            }
            if (pageSize == null || pageSize <= 0) {
                pageSize = 20;
            }
            ApiAssert.notNull(orderMetrics, "orderMetrics is null");
            if (orderType == null) {
                orderType = OrderType.DESC;
            }
            ApiAssert.notNull(dateType, "dateType is null");
            ApiAssert.notNull(startDate, "startDate is null");
            if (dateType == DateType.WEEK) {
                ApiAssert.notNull(endDate, "endDate is null");
            }

            return new GetSignRoomParamBean(appId, userId
                    , pageNo
                    , pageSize
                    , orderMetrics
                    , orderType
                    , dateType
                    , startDate
                    , endDate
                    , familyId
                    , roomId
                    , filterZero
                    , roomIds
            );
        }
    }

}
