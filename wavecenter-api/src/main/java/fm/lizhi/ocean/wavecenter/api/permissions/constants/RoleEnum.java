package fm.lizhi.ocean.wavecenter.api.permissions.constants;

import lombok.Getter;

/**
 * 角色枚举
 * <AUTHOR>
 * @date 2024/3/28 17:01
 */
public enum RoleEnum {

    /** 家族长 */
    FAMILY("family")
    /** 厅主 */
    , RO<PERSON>("room")
    /** 主播 */
    , P<PERSON><PERSON><PERSON>("player")
    /** 普通用户 */
    , USER("user")
    /** 高级管理/多厅管理 */
    , FAMILY_ADMIN("familyAdmin")
    ;

    @Getter
    private String roleCode;

    RoleEnum(String roleCode) {
        this.roleCode = roleCode;
    }

    public static RoleEnum getByRoleCode(String roleCode) {
        for (RoleEnum roleEnum : RoleEnum.values()) {
            if (roleEnum.roleCode.equalsIgnoreCase(roleCode)) {
                return roleEnum;
            }
        }
        return null;
    }
}
