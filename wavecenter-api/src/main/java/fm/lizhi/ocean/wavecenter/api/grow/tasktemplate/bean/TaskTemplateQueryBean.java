package fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 任务模版查询响应Bean
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskTemplateQueryBean {

    /**
     * 模版ID
     */
    private String id;

    /**
     * 能力项code
     */
    private String capabilityCode;

    /**
     * 能力分
     */
    private Integer capabilityScore;

    /**
     * 条件组
     */
    private ConditionGroupBean conditionGroup;

    /**
     * 能力名称
     */
    private String capabilityName;
} 