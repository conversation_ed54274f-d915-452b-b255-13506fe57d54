package fm.lizhi.ocean.wavecenter.api.message.request;

import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;

@Data
public class RequestUnReadMessageCount implements IContextRequest{


    /**
     * 消息类型
     * @see fm.lizhi.ocean.wavecenter.infrastructure.notice.wcnotice.constants.WcNoticeConfigEnum
     */
    private Integer type;

    private Integer appId;

    private Long targetUserId;

    private String roleCode;

    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}
