package fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.request.RequestDeleteTaskTemplate;
import fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.request.RequestQueryTaskTemplate;
import fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.request.RequestSaveTaskTemplate;
import fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.response.ResponseQueryTaskTemplate;

import javax.validation.Valid;

/**
 * 任务模版Service接口
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
public interface TaskTemplateService {

    /**
     * 保存任务模版
     *
     * @param request 保存请求
     * @return 保存结果
     */
    Result<Void> saveTaskTemplate(@Valid RequestSaveTaskTemplate request);

    /**
     * 分页查询任务模版列表
     *
     * @param request 查询请求
     * @return 查询结果
     */
    Result<ResponseQueryTaskTemplate> queryTaskTemplateList(@Valid RequestQueryTaskTemplate request);

    /**
     * 根据ID逻辑删除任务模版
     *
     * @return 删除结果
     */
    Result<Void> deleteGrowTaskTemplate(@Valid RequestDeleteTaskTemplate request);

    /**
     * 参数无效
     */
    int PARAM_INVALID = 2010000;

    /**
     * 保存任务模版失败
     */
    int SAVE_TASK_TEMPLATE_FAIL = 2010001;

    /**
     * 查询任务模版失败
     */
    int GET_TASK_TEMPLATE_FAIL = 2010002;

    /**
     * 删除任务模版失败
     */
    int DELETE_TASK_TEMPLATE_FAIL = 2010003;
} 