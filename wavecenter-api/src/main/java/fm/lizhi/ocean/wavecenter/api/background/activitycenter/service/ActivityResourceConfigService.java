package fm.lizhi.ocean.wavecenter.api.background.activitycenter.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestPageActivityResources;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityResource;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityResource;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseActivityResource;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;

import java.util.List;

/**
 * 活动资源
 * <AUTHOR>
 */
public interface ActivityResourceConfigService {

    /**
     * 保存活动资源
     */
    Result<Void> saveActivityResource(RequestSaveActivityResource param);


    /**
     * 修改活动资源
     */
    Result<Void> updateActivityResource(RequestUpdateActivityResource param);


    /**
     * 删除活动资源
     */
    Result<Void> deleteActivityResource(Long id, int appId, String operator);


    /**
     * 分页查询活动资源
     */
    Result<PageBean<ResponseActivityResource>> listActivityResource(RequestPageActivityResources param);

    /**
     * 根据等级获取活动资源列表
     */
    Result<List<ResponseActivityResource>> listActivityResourceByLevelId(Long levelId, int appId);

    /**
     * 等级 ID 不存在
     */
    int SAVE_ACTIVITY_RESOURCE_NOT_FOUND_LEVEL = 2120001;

    /**
     * 自动配置资源不存在
     */
    int SAVE_ACTIVITY_RESOURCE_NOT_FOUND_AUTO_RESOURCE = 2120002;

    /**
     * 资源重复
     */
    int SAVE_ACTIVITY_RESOURCE_RESOURCE_EXIST = 2120003;

    /**
     * 等级 ID 不存在
     */
    int UPDATE_ACTIVITY_RESOURCE_NOT_FOUND_LEVEL = 2120101;

    /**
     * 自动配置资源不存在
     */
    int UPDATE_ACTIVITY_RESOURCE_NOT_FOUND_AUTO_RESOURCE = 2120102;

    /**
     * 配置资源不存在
     */
    int UPDATE_ACTIVITY_RESOURCE_NOT_FOUND_RESOURCE = 2120103;

    /**
     * 资源重复
     */
    int UPDATE_ACTIVITY_RESOURCE_RESOURCE_EXIST = 2120104;


    /**
     * 删除活动资源失败
     */
    int DELETE_ACTIVITY_RESOURCE_FAIL = 2120201;

    /**
     * 活动等级重复
     */
    int SAVE_ACTIVITY_RESOURCE_RESOURCE_LEVEL_REPEAT = 2120301;
}
