package fm.lizhi.ocean.wavecenter.api.activitycenter.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ActivityAnnouncementStatusEnum {

    /**
     * 待设置
     */
    UNSETTED(0),

    /**
     * 设置失败
     */
    SET_FAIL(1),

    /**
     * 设置成功
     */
    SET_SUCCESS(2),

    /**
     * 恢复失败
     */
    RECOVER_FAIL(3),

    /**
     * 恢复成功
     */
    RECOVER_SUCCESS(4),

    /**
     * 取消设置
     */
    CANCEL_SET(5)
    ;


    private int status;

}
