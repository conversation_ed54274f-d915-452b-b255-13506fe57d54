package fm.lizhi.ocean.wavecenter.api.anchor.singer.bean;

import lombok.Data;

import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 *
 * 厅歌手汇总-天
 *
 * <AUTHOR>
 * @date 2025-03-27 03:27:23
 */
@Data
@Accessors(chain = true)
public class SingerDataRoomDayBean {
    /**
     * ID
     */
    private Long id;

    /**
     * 业务
     */
    private Integer appId;

    /**
     * 日期 格式 YYYY-MM-DD
     */
    private Long statDate;

    /**
     * 日期 格式 YYYYMMDD
     */
    private Integer statDateValue;

    /**
     * 厅主ID
     */
    private Long njId;

    /**
     * 家族ID
     */
    private Long familyId;

    /**
     * 优质音乐人数量
     */
    private Integer seniorSingerAuthCnt;

    /**
     * 认证歌手数量
     */
    private Integer singerAuthCnt;

    /**
     * 上一自然周营收（钻石）
     */
    private Long lastWeekIncome;
}