package fm.lizhi.ocean.wavecenter.api.anchor.singer.request;

import java.util.List;

import javax.validation.constraints.NotNull;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;

/**
 * 歌手认证审核请求参数
 */
@Data
public class RequestVerifyAudit implements IContextRequest {

    /**
     * 歌手认证记录ID
     */
    @NotNull(message = "歌手认证记录ID不能为空")
    private List<Long> ids;

    /**
     * 目标审核状态
     */
    @NotNull(message = "目标审核状态不能为空")
    private Integer auditStatus;

    /**
     * 不通过原因
     */
    private String rejectedCause;

    /**
     * 歌手类型
     */
    @NotNull(message = "歌手类型不能为空")
    private Integer singerType;

    /**
     * 应用ID
     */
    @AppEnumId(message = "应用ID不合法")
    @NotNull(message = "应用ID不能为空")
    private Integer appId;

    /**
     * 操作人
     */
    @NotNull(message = "操作人不能为空")
    private String operator;

    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}
