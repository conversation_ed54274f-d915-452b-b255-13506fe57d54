package fm.lizhi.ocean.wavecenter.api.grow.capability.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.grow.capability.request.RequestSaveCapability;
import fm.lizhi.ocean.wavecenter.api.grow.capability.response.ResponseCapability;

import java.util.List;

/**
 * 能力项Service接口
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
public interface CapabilityService {
    /**
     * 查询能力项列表
     *
     * @param appId 业务ID
     * @return 能力项响应列表
     */
    Result<List<ResponseCapability>> queryCapabilityList(Integer appId);

    /**
     * 保存能力项
     *
     * @param request 保存请求
     * @return 保存结果
     */
    Result<Void> saveCapability(RequestSaveCapability request);

    // 保存能力项失败状态码
    int SAVE_CAPABILITY_FAIL = -1;

} 