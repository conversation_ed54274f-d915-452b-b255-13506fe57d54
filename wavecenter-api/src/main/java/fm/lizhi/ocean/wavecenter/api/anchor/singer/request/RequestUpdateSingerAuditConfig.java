package fm.lizhi.ocean.wavecenter.api.anchor.singer.request;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.UpdateSingerAuditConfigBean;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Accessors(chain = true)
public class RequestUpdateSingerAuditConfig {

   private List<UpdateSingerAuditConfigBean> configList;

   /**
    * 操作者
    */
   @NotNull(message = "操作者不能为空")
   private String operator;

}
