package fm.lizhi.ocean.wavecenter.api.datacenter.bean;

import lombok.Data;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/4/17 16:54
 */
@Data
public class IncomeCharmBean extends IncomeBean{

    /**
     * 魅力值
     */
    private Integer charm;


    public IncomeCharmBean fillIncome(IncomeCharmBean bean){
        if (bean == null){
            return this;
        }

        Optional.ofNullable(bean.getCurrent()).ifPresent(this::setCurrent);
        Optional.ofNullable(bean.getPer()).ifPresent(this::setPer);
        Optional.ofNullable(bean.getRatio()).ifPresent(this::setRatio);

        return this;
    }




}
