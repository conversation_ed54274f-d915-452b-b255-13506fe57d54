package fm.lizhi.ocean.wavecenter.api.activitycenter.bean;

import lombok.Data;

import java.util.List;

/**
 * 通用活动模板流量资源, 为了与background包下的区分添加General
 */
@Data
public class ActivityTemplateGeneralFlowResourceBean {

    /**
     * 资源配置id
     */
    private Long resourceConfigId;

    /**
     * 资源名称
     */
    private String name;

    /**
     * 资源介绍
     */
    private String introduction;

    /**
     * 资源预览图片
     */
    private String imageUrl;

    /**
     * 资源配置类型
     *
     * @see fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityResourceDeployTypeConstants
     */
    private Integer deployType;

    /**
     * 资源是否必选
     */
    private Boolean required;

    /**
     * 资源code，只有自动配置的资源有
     */
    private String resourceCode;

    /**
     * 资源状态
     */
    private Integer resourceStatus;

    /**
     * 资源是否已删除
     */
    private Boolean resourceDeleted;

    /**
     * 资源额外信息
     */
    private ActivityTemplateGeneralFlowResourceExtraBean extra;

    /**
     * 资源物料图片列表
     */
    private List<ActivityTemplateGeneralFlowResourceImageBean> images;
}
