package fm.lizhi.ocean.wavecenter.api.grow.ability.request;

import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotNull;
import java.beans.Transient;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 查询厅能力表现请求参数
 */
@Data
public class RequestGetRoomPerformance implements IContextRequest {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 应用id
     */
    @NotNull(message = "应用id不能为空")
    private Integer appId;

    /**
     * 厅id, 注意创作服务中心的厅id实际是取njId
     */
    @NotNull(message = "厅id不能为空")
    private Long roomId;

    /**
     * 考核周期开始日期, 格式为yyyy-MM-dd
     */
    @NotNull(message = "考核周期开始日期不能为空")
    private String startWeekDate;

    @AssertTrue(message = "考核周期开始日期不合法, 应为yyyy-MM-dd的周一日期")
    @Transient
    protected boolean isStartWeekDateValid() {
        if (startWeekDate == null) {
            return true;
        }
        LocalDate localDate;
        try {
            localDate = LocalDate.parse(startWeekDate, DATE_FORMATTER);
        } catch (RuntimeException ignored) {
            return false;
        }
        return localDate.getDayOfWeek() == DayOfWeek.MONDAY;
    }

    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}
