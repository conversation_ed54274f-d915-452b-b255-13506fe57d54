package fm.lizhi.ocean.wavecenter.api.live.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 陪玩日统计指标
 * <AUTHOR>
 * @date 2024/6/11 15:44
 */
@Data
@Accessors(chain = true)
public class PlayerCheckDayStatsBean {

    /**
     * 天
     */
    private Date day;

    /**
     * 收入
     */
    private BigDecimal income;

    /**
     * 魅力值
     */
    private Long charm;

    /**
     * 麦序
     */
    private Integer seatOrder;

    /**
     * 上麦时长
     */
    private Integer upGuestDur;

    /**
     * 主持档
     */
    private Integer hostCnt;

}
