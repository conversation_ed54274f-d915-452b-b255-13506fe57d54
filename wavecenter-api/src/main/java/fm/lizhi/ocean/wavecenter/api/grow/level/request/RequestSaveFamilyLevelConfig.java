package fm.lizhi.ocean.wavecenter.api.grow.level.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/18 17:07
 */
@Data
@Accessors(chain = true)
public class RequestSaveFamilyLevelConfig implements IContextRequest {

    /**
     * 更新时必传
     */
    private Long id;

    @NotNull(message = "appId is null")
    @AppEnumId
    private Integer appId;

    /**
     * 等级名称
     */
    @NotBlank(message = "levelName is blank")
    private String levelName;

    /**
     * 最小流水
     */
    @NotNull(message = "minIncome is null")
    private Integer minIncome;

    /**
     * 角标
     */
    private String levelIcon;

    /**
     * 勋章
     */
    @NotBlank(message = "levelMedal is blank")
    private String levelMedal;

    /**
     * 奖励图片
     */
    private List<String> awardImgs;

    /**
     * 主题色不可为空
     */
    @NotBlank(message = "themColor is blank")
    private String themColor;

    /**
     * 背景颜色不可为空
     */
    @NotBlank(message = "backgroundColor is blank")
    private String backgroundColor;

    /**
     * 操作人
     */
    @NotBlank(message = "operator is blank")
    private String operator;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
