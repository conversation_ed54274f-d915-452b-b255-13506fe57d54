package fm.lizhi.ocean.wavecenter.api.award.singer.response;

import fm.lizhi.ocean.wavecenter.api.award.singer.bean.SingerDecorateRuleBean;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.DecorateInfoBean;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 歌手装扮规则配置响应体
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ResponseSingerDecorateRule {


    /**
     * 装扮规则
     */
    private SingerDecorateRuleBean rule;

    /**
     * 装扮信息
     */
    private DecorateInfoBean decorateInfo;


}
