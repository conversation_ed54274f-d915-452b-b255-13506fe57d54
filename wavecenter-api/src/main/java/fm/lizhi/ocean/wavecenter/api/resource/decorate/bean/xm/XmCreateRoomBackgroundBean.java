package fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.xm;

import fm.lizhi.ocean.wavecenter.api.common.annotation.EnumValue;
import fm.lizhi.ocean.wavecenter.api.common.annotation.HexArgbColor;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.CreateRoomBackgroundBean;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.constants.DecorateBackgroundTypeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.constants.DecorateMaterialTypeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestCreateRoomBackground;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 西米创建直播间背景Bean
 *
 * @see RequestCreateRoomBackground
 */
@Data
public class XmCreateRoomBackgroundBean implements CreateRoomBackgroundBean {

    /**
     * 名称
     */
    @NotNull(message = "名称不能为空")
    @Size(min = 1, max = 64, message = "名称长度必须在{min}到${max}个字符之间")
    private String name;

    /**
     * 静态图(实际使用的大图)
     */
    @NotNull(message = "静态图不能为空")
    private String thumbUrl;

    /**
     * 素材, SVAG或PAG资源
     */
    private String materialUrl;

    /**
     * 素材类型(资源类型)
     *
     * @see DecorateMaterialTypeEnum
     */
    @NotNull(message = "资源类型不能为空")
    @EnumValue(value = DecorateMaterialTypeEnum.class, message = "资源类型不合法")
    private String materialType;

    /**
     * 图标
     */
    @NotNull(message = "图标不能为空")
    private String iconUrl;

    /**
     * 有效期分钟数
     */
    @NotNull(message = "有效期不能为空")
    @Min(value = 1, message = "有效期最小值为{value}分钟")
    private Integer validMin;

    /**
     * 背景特效颜色
     */
    @HexArgbColor(message = "背景特效颜色不合法")
    private String backgroundColor;

    /**
     * 背景类型(皮肤类型)
     */
    @NotNull(message = "皮肤类型不能为空")
    @EnumValue(value = DecorateBackgroundTypeEnum.class, message = "皮肤类型不合法")
    private Integer backgroundType;

    /**
     * 点唱坐底图地址
     */
    private String stageUrl;

    /**
     * 舞台底座svga
     */
    private String stageSvgaUrl;

    /**
     * 舞台位默认图
     */
    private String stageLocationUrl;

    /**
     * 主持模式声纹
     */
    private String hostModeVoicePrintSvgaUrl;

    /**
     * 演唱模式声纹
     */
    private String singingModeVoicePrintSvgaUrl;

    /**
     * 舞台光圈
     */
    private String stageApertureSvgaUrl;
}
