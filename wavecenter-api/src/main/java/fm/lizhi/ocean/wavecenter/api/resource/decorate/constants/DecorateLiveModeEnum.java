package fm.lizhi.ocean.wavecenter.api.resource.decorate.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 装扮直播间模式枚举, 参考{@code fm.lizhi.live.room.xm.enums.LiveModeEnum}
 */
@AllArgsConstructor
@Getter
public enum DecorateLiveModeEnum {

    /**
     * 娱乐模式
     */
    AMUSEMENT_MODE(1),
    /**
     * 电台模式
     */
    RADIO_MODE(4),
    /**
     * 互动模式
     */
    INTERACTIVE_MODE(5),
    /**
     * 点唱厅模式
     */
    VOCAL_MODE(6);;

    /**
     * 直播间模式值
     */
    private final Integer value;
}
