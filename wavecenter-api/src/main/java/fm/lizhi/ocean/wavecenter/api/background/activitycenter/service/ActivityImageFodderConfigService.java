package fm.lizhi.ocean.wavecenter.api.background.activitycenter.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityImageFodderBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.*;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseSaveActivityImageFodder;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;

/**
 * 图片素材
 * <AUTHOR>
 */
public interface ActivityImageFodderConfigService {

    /**
     * 保存图片素材
     */
    Result<ResponseSaveActivityImageFodder> saveImageFodder(RequestSaveActivityImageFodder param);


    /**
     * 修改图片素材
     */
    Result<Void> updateImageFodder(RequestUpdateActivityImageFodder param);


    /**
     * 删除图片素材
     */
    Result<Void> deleteImageFodder(Long id, String operator);


    /**
     * 分页查询图片素材
     */
    Result<PageBean<ActivityImageFodderBean>> pageImageFodder(RequestPageActivityImageFodder param);


    /**
     * 保存图片素材失败
     */
    int SAVE_ACTIVITY_IMAGE_FODDER_FAIL = 2090001;

    /**
     * 更新图片素材失败
     */
    int UPDATE_ACTIVITY_IMAGE_FODDER_FAIL = 2090101;

    /**
     * 删除图片素材失败
     */
    int DELETE_ACTIVITY_IMAGE_FODDER_FAIL = 2090201;
}
