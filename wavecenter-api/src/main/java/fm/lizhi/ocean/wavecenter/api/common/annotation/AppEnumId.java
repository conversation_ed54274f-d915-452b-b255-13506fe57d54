package fm.lizhi.ocean.wavecenter.api.common.annotation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 校验被注解的字段有效的appId值, 注意是int或Integer类型.
 * <p>
 * <b>注意! </b>该注解仅供创作服务中心的服务内部使用, 接口调用方请勿使用.
 */
@Constraint(validatedBy = {AppEnumIdValidator.class})
@Documented
@Retention(RUNTIME)
@Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE})
public @interface AppEnumId {

    /**
     * 校验不通过时的提示消息
     *
     * @return 校验不通过时的提示消息
     */
    String message() default "${validatedValue}不是有效的appId值";

    /**
     * 校验分组类型
     *
     * @return 校验分组类型
     */
    Class<?>[] groups() default {};

    /**
     * 标注负载信息
     *
     * @return 标注负载信息
     */
    Class<? extends Payload>[] payload() default {};
}
