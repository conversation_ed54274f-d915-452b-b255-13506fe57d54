package fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.bean.TaskTemplateBean;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 保存任务模版请求
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RequestSaveTaskTemplate implements IContextRequest {

    /**
     * 任务模版列表
     */
    @NotEmpty(message = "任务模版列表不能为空")
    @Valid
    private List<TaskTemplateBean> taskTemplateList;

    /**
     * 业务ID
     */
    @AppEnumId(message = "业务ID不能为空")
    private Integer appId;

    /**
     * 操作人
     */
    @NotNull(message = "操作人不能为空")
    private String operator;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}