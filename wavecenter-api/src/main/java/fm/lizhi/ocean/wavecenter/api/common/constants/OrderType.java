package fm.lizhi.ocean.wavecenter.api.common.constants;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 排序类型
 * <AUTHOR>
 * @date 2024/4/18 18:26
 */
public enum OrderType {

    DESC("desc"), ASC("asc");

    @Getter
    private String value;

    private static Map<String, OrderType> valueMap = new HashMap<>();

    static {
        for (OrderType orderType : OrderType.values()) {
            valueMap.put(orderType.value, orderType);
        }
    }

    OrderType(String value) {
        this.value = value;
    }

    public static OrderType fromValue(String value) {
        return valueMap.get(value);
    }
}
