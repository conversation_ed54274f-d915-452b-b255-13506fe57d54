package fm.lizhi.ocean.wavecenter.api.activitycenter.constants;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.BannerExtraBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.BaseExtraBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.OfficialSeatExtraBean;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 资源扩展字段映射关系
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public enum ResourceExtraMapping {

    /**
     * PP官频位配置
     */
    OFFICIAL_SEAT_EXTRA(AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode(), OfficialSeatExtraBean.class),

    /**
     * banner
     */
    BANNER(AutoConfigResourceEnum.BANNER.getResourceCode(), BannerExtraBean.class),

    ;


    private String resourceCode;

    private Class<?> resourceClass;

    public static <T extends BaseExtraBean> T convertJsonToExtra(String json, String resourceCode) {
        try {
            if (StringUtils.isEmpty(json)) {
                return null;
            }
            for (ResourceExtraMapping mapping : ResourceExtraMapping.values()) {
                if (mapping.resourceCode.equals(resourceCode)) {
                    return (T) JSONObject.parseObject(json, mapping.resourceClass);
                }
            }
        } catch (Exception e) {
            log.error("convertJsonToExtra happen error:", e);
        }
        return null;
    }

}
