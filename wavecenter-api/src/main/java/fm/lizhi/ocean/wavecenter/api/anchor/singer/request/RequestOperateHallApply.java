package fm.lizhi.ocean.wavecenter.api.anchor.singer.request;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerHallApplyStatusEnum;
import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 点唱厅管理-操作申请名单
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RequestOperateHallApply implements IContextRequest {


    /**
     * 应用ID
     */
    @NotNull(message = "应用ID不能为空")
    @AppEnumId
    private int appId;

    /**
     * 审核状态,如果是审核不通过的话，也会将该记录逻辑删除
     */
    @NotNull(message = "审核状态不能为空")
    private SingerHallApplyStatusEnum status;

    /**
     * 记录 ID
     */
    @NotNull(message = "记录ID不能为空")
    private Long id;

    /**
     * 操作人
     */
    private String operator;


    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}
