package fm.lizhi.ocean.wavecenter.api.home.response;


import fm.lizhi.ocean.wavecenter.api.home.bean.MetricsDataBean;
import fm.lizhi.ocean.wavecenter.api.home.bean.TrendChartBean;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 公会关键数据-趋势图
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ResponseGuildKeyDataTrendChart {


    /**
     * 日期
     */
    private Date date;


    /**
     * 总收入
     */
    private TrendChartBean sumIncome;

    /**
     * 上麦主播数
     */
    private TrendChartBean signUpGuestPlayerCnt;

    /**
     * 厅均收入
     */
    private TrendChartBean roomAvgIncome;

    /**
     * 人均收入
     */
    private TrendChartBean playerAvgIncome;

    /**
     * 有收入主播数
     */
    private TrendChartBean incomePlayerCnt;
}
