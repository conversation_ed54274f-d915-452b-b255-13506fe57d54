package fm.lizhi.ocean.wavecenter.api.common.annotation;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Objects;

/**
 * {@link AppEnumId}注解验证器实现
 */
class AppEnumIdValidator implements ConstraintValidator<AppEnumId, Integer> {

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext context) {
        if (value == null) {
            // 校验是否为null应该使用@NotNull, 所以如果为null直接返回true
            return true;
        }
        for (BusinessEvnEnum businessEvnEnum : BusinessEvnEnum.values()) {
            if (Objects.equals(businessEvnEnum.getAppId(), value) && businessEvnEnum.getOnline() == 1) {
                return true;
            }
        }
        return false;
    }
}
