package fm.lizhi.ocean.wavecenter.api.permissions.bean;

import lombok.Builder;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/27 15:11
 */
@Getter
@Builder
public class GetUserPermissionBean {
    /**
     * 用户角色编码
     */
    private String roleCode;

    /**
     * 拥有的菜单权限
     */
    private List<String> menu;

    /**
     * 可读的组件
     */
    private List<String> readComponents;

    /**
     * 可操作的组件
     */
    private List<String> writeComponents;
}
