package fm.lizhi.ocean.wavecenter.api.home.response;

import fm.lizhi.ocean.wavecenter.api.home.bean.MetricsPerformanceBean;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 厅私信拓客分析
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ResponseRoomMsgAnalysisPerformance {


    /**
     * 私信用户数
     */
    private MetricsPerformanceBean chatUserCnt;

    /**
     * 私信回复数
     */
    private MetricsPerformanceBean replyChatUserCnt;

    /**
     * 私信进房数
     */
    private MetricsPerformanceBean chatEnterRoomUserCnt;


    /**
     * 私信付费数
     */
    private MetricsPerformanceBean chatGiftUserCnt;

}
