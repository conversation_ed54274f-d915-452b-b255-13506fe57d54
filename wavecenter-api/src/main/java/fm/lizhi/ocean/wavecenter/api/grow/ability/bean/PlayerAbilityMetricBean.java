package fm.lizhi.ocean.wavecenter.api.grow.ability.bean;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 主播能力指标bean
 */
@Data
public class PlayerAbilityMetricBean {

    /**
     * 指标code
     */
    private String metricCode;

    /**
     * 指标名称
     */
    private String metricName;

    /**
     * 本周指标值, 根据指标code不同, 可能是整数或小数, 精度也随之不同
     */
    private BigDecimal thisWeekValue;

    /**
     * 本周对比上周指标值变化, 可能为null
     */
    private BigDecimal thisWeekValueChange;

    public static PlayerAbilityMetricBean of(String metricCode, String metricName, BigDecimal thisWeekValue, BigDecimal thisWeekValueChange) {
        PlayerAbilityMetricBean bean = new PlayerAbilityMetricBean();
        bean.setMetricCode(metricCode);
        bean.setMetricName(metricName);
        bean.setThisWeekValue(thisWeekValue);
        bean.setThisWeekValueChange(thisWeekValueChange);
        return bean;
    }
}
