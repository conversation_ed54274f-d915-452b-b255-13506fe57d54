package fm.lizhi.ocean.wavecenter.api.background.activitycenter.request;

import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityBatchItemBean;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 批量同意活动申请请求参数
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RequestBatchActivityAuditAgree implements IContextRequest {
    
    /**
     * 应用ID
     */
    private Integer appId;
    
    /**
     * 操作人
     */
    private String operator;
    
    /**
     * 活动列表
     */
    private List<ActivityBatchItemBean> activity;

    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}
