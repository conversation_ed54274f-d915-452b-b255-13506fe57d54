package fm.lizhi.ocean.wavecenter.api.anchor.singer.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestSendSingerChat;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
public interface SingerChatService {

    /**
     * 发送歌手私信
     *
     * @param request 参数
     * @return 结果
     */
    Result<Void> sendSingerChat(@Valid RequestSendSingerChat request);


    int SEND_SINGER_CHAT_FAIL = 1;
    int DELIVER_SINGER_AWARD_FAIL = 2;

}
