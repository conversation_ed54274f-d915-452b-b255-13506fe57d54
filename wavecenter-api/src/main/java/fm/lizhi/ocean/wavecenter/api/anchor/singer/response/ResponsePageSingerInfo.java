package fm.lizhi.ocean.wavecenter.api.anchor.singer.response;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerFamilyInfoBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerUserInfoBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;


/**
 * 歌手库-歌手维度列表
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ResponsePageSingerInfo {

    /**
     * 歌手信息
     */
    private SingerUserInfoBean singerInfo;

    /**
     * 家族信息
     */
    private SingerFamilyInfoBean familyInfo;

    /**
     * 厅主信息
     */
    private SingerUserInfoBean njInfo;


    /**
     * ID
     */
    private Long id;

    /**
     * 业务ID
     */
    private Integer appId;

    /**
     * 歌手ID
     */
    private Long userId;

    /**
     * 厅主ID
     */
    private Long njId;

    /**
     * 家族ID
     */
    private Long familyId;

    /**
     * 审核表 ID
     */
    private Long singerVerifyId;

    /**
     * 歌手状态 1: 认证中 2: 生效中  3: 已淘汰
     */
    private Integer singerStatus;

    /**
     * 歌曲风格
     */
    private String songStyle;

    /**
     * 是否原创歌手
     */
    private Boolean originalSinger;

    /**
     * 是否已发放奖励 0 未发放 1 已发放
     */
    private Boolean rewardsIssued;

    /**
     * 是否白名单歌手
     */
    private Boolean whiteListSinger;


    /**
     * 1: 新锐歌手，2：优质歌手，3：明星歌手
     */
    private SingerTypeEnum singerType;

    /**
     * 淘汰时间
     */
    private Long eliminationTime;

    /**
     * 通过时间
     */
    private Long auditTime;

    /**
     * 淘汰原因
     */
    private String eliminationReason;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 联系方式
     */
    private String contactNumber;

}
