package fm.lizhi.ocean.wavecenter.api.datacenter.bean;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import fm.lizhi.ocean.wavecenter.api.common.constants.MetricsEnum;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/4/18 19:10
 */
@Getter
@Builder
public class GetRoomPlayerPerformanceBean {

    private Integer appId;
    private Long roomId;

    private Integer pageNo;
    private Integer pageSize;

    /**
     * 排序指标
     */
    private MetricsEnum orderMetrics;
    /**
     * desc,asc
     */
    private OrderType orderType;

    private Long familyId;

    public static class GetRoomPlayerPerformanceBeanBuilder{

        public GetRoomPlayerPerformanceBeanBuilder orderMetricsStr(String orderMetrics){
            this.orderMetrics = MetricsEnum.fromValue(orderMetrics);
            return this;
        }

        public GetRoomPlayerPerformanceBeanBuilder orderTypeStr(String orderType){
            this.orderType = OrderType.valueOf(orderType);
            return this;
        }

        public GetRoomPlayerPerformanceBean build(){
            ApiAssert.notNull(appId, "appId is null");
            ApiAssert.notNull(roomId, "roomId is null");

            if (pageNo == null || pageNo < 0) {
                pageNo = 1;
            }

            if (pageSize == null || pageSize < 0) {
                pageSize = 20;
            }

            if (orderMetrics == null) {
                orderMetrics = MetricsEnum.CHARM;
            }
            if (orderType == null) {
                orderType = OrderType.DESC;
            }

            return new GetRoomPlayerPerformanceBean(appId
                    , roomId
                    , pageNo
                    , pageSize
                    , orderMetrics
                    , orderType
                    , familyId);
        }
    }
}
