package fm.lizhi.ocean.wavecenter.api.anchor.singer.request;

import java.util.List;

import javax.validation.constraints.NotNull;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;

@Data
public class RequestBatchAddBlackList implements IContextRequest {

    @NotNull(message = "用户ID列表不能为空")
    private List<Long> userIds;

    @NotNull(message = "appId不能为空")
    @AppEnumId
    private Integer appId;

    @Override
    public Integer foundIdAppId() {
       return appId;
    }

}
