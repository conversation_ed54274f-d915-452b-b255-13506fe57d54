package fm.lizhi.ocean.wavecenter.api.background.activitycenter.request;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
@Builder
public class RequestUpdateActivityRecommendCard {

    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 等级名称
     */
    private Long levelId;

    /**
     * 发放张数
     */
    private Integer count;

    /**
     * 有效天数
     */
    private Integer validDay;

    /**
     * 操作者
     */
    private String operator;


    private RequestUpdateActivityRecommendCard(RequestUpdateActivityRecommendCard.RequestUpdateActivityRecommendCardBuilder builder) {
        this.id = builder.id;
        this.appId = builder.appId;
        this.levelId = builder.levelId;
        this.count = builder.count;
        this.validDay = builder.validDay;
        this.operator = builder.operator;
    }


    public static class RequestUpdateActivityRecommendCardBuilder {

        public RequestUpdateActivityRecommendCard build() {

            ApiAssert.notNull(appId, "appId is null");
            ApiAssert.notNull(levelId, "等级ID不能为空");
            ApiAssert.notNull(operator, "操作人不能为空");
            ApiAssert.notNull(id, "ID不能为空");
            ApiAssert.notNull(count, "发放张数不能为空");

            return new RequestUpdateActivityRecommendCard(this);
        }
    }
}