package fm.lizhi.ocean.wavecenter.api.common.constants;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 数据指标
 * <AUTHOR>
 * @date 2024/4/18 18:26
 */
public enum AuditMetricsEnum {


    PUSH_PEOPLE_NUMBER("pushPeopleNumber", "pushPeopleNumber")

    , PUSH_NUMBER("pushNumber", "pushNumber")

    ;

    @Getter
    private String value;

    @Getter
    private String poName;

    private static Map<String, AuditMetricsEnum> valueMap = new HashMap<>();

    static {
        for (AuditMetricsEnum metrics : AuditMetricsEnum.values()) {
            valueMap.put(metrics.value, metrics);
        }
    }

    AuditMetricsEnum(String value, String poName) {
        this.value = value;
        this.poName = poName;
    }

    public static AuditMetricsEnum fromValue(String value) {
        return valueMap.get(value);
    }


}
