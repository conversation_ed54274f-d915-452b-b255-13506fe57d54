package fm.lizhi.ocean.wavecenter.api.user.service;

import fm.lizhi.ocean.wavecenter.api.user.request.RequestAddFirstLoginRecord;
import fm.lizhi.ocean.wavecenter.api.user.request.RequestQueryFirstLoginRecord;
import fm.lizhi.ocean.wavecenter.api.user.response.ResponseFirstLoginRecord;
import fm.lizhi.commons.service.client.pojo.Result;

/**
 * 首次登录记录Service接口（API层）
 */
public interface FirstLoginRecordService {
    /**
     * 新增首次登录记录
     * @param request 请求参数
     * @return 结果
     */
    Result<Void> addFirstLoginRecord(RequestAddFirstLoginRecord request);

    /**
     * 保存失败
     */
    int ADD_FIRST_LOGIN_RECORD_ERROR = 10000;
} 