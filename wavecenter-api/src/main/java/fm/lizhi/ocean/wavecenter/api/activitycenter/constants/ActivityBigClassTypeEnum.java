package fm.lizhi.ocean.wavecenter.api.activitycenter.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ActivityBigClassTypeEnum {

    NORMAL(1, "常规"),

    ROOM_WAR(2, "厅战");

    private int type;

    private String name;

    public static boolean isValidType(int type) {
        for (ActivityBigClassTypeEnum item : values()) {
            if (item.getType() == type) {
                return true;
            }
        }
        return false;
    }

}
