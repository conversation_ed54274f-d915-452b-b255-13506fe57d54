package fm.lizhi.ocean.wavecenter.api.live.bean;

import fm.lizhi.ocean.wavecenter.api.user.bean.RoomBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class RoomHourCheckDetailRes {


    /**
     * 未打卡主播
     */
    private List<PlayerCheckInDetailRes> unCheckPlayer;

    /**
     * 打卡主播
     */
    private List<PlayerCheckInDetailRes> checkPlayer;

    /**
     * 合计值
     */
    private RoomHourCheckDetailStatsRes stats;

    /**
     * 厅主信息
     */
    private RoomBean room;

    /**
     * 主持人信息
     */
    private UserBean host;

    /**
     * 备注
     */
    private String remark;

}
