package fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 推荐卡使用记录
 * <AUTHOR>
 * @date 2025/3/14 18:15
 */
@Data
@Accessors(chain = true)
public class RecommendCardUseRecordBean {

    /**
     * 使用记录ID
     */
    private Long id;

    /**
     * 厅主信息
     */
    private Long njId;

    private String njName;

    private String njBand;

    private Long familyId;

    private String familyName;

    /**
     * 使用时间
     */
    private Date useTime;

    /**
     * 推荐时间段
     */
    private String recommendationTime;

    /**
     * 使用数量
     */
    private Integer useNum;

    /**
     * 推荐位置
     */
    private String position;

    /**
     * 推荐类型 仅黑叶
     */
    private String category;

    /**
     * 曝光值
     */
    private Integer exposure;

    /**
     * 曝光率
     */
    private String exposureRate;

}
