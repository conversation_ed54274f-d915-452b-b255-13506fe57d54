package fm.lizhi.ocean.wavecenter.api.activitycenter.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class RequestActivityOptionOfficialTime {

    @AppEnumId
    private Integer appId;

    /**
     * 活动开始时间
     */
    @NotNull(message = "活动开始时间不能为空")
    private Long activityStartTime;

    /**
     * 活动结束时间
     */
    @NotNull(message = "活动结束时间不能为空")
    private Long activityEndTime;

    /**
     * 模板ID
     */
    @NotNull(message = "模板ID不能为空")
    private Long templateId;

}
