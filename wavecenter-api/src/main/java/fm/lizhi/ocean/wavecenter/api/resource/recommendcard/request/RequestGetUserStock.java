package fm.lizhi.ocean.wavecenter.api.resource.recommendcard.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/3/21 14:25
 */
@Data
@Accessors(chain = true)
public class RequestGetUserStock implements IContextRequest {

    @NotNull(message = "appId is null")
    @AppEnumId
    private Integer appId;

    @NotNull(message = "userIds is null")
    private Long userId;

    /**
     * 即将过期天数
     */
    private Integer beExpireDay;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
