package fm.lizhi.ocean.wavecenter.api.background.activitycenter.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.RequestQueryEffectActivitiesByTemplateIdBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.RequestQueryUserActivitiesBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ResponseQueryActivitiesBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ResponseQueryUserActivitiesBean;


/**
 * 活动信息查询
 */
public interface ActivityInfoQueryService {

    /**
     * 查询用户申报的活动列表
     */
    Result<ResponseQueryUserActivitiesBean> queryUserActivities(RequestQueryUserActivitiesBean requestQueryUserActivities);

    /**
     * 查询所有指定条件的活动列表
     */
    Result<ResponseQueryActivitiesBean> queryEffectActivityInfoListByTemplateId(RequestQueryEffectActivitiesByTemplateIdBean requestQueryActivities);

    /**
     * 查询活动列表参数异常
     */
    int QUERY_USER_ACTIVITY_LIST_PARAM_ERROR = 22200012;

}
