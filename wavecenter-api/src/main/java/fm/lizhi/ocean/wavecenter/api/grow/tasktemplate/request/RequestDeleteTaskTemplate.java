package fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.request;

import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/12 19:48
 */
@Data
public class RequestDeleteTaskTemplate implements IContextRequest {

    @NotNull
    private Integer appId;

    @NotEmpty
    private List<Long> ids;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
