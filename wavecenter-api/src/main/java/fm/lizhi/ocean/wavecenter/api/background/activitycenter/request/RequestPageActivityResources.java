package fm.lizhi.ocean.wavecenter.api.background.activitycenter.request;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class RequestPageActivityResources {


    private int appId;

    /**
     * 资源配置类型，1：自动，2：手动
     */
    private Integer deployType;

    private int pageNo;

    private int pageSize;

    private RequestPageActivityResources(RequestPageActivityResources.RequestPageActivityResourcesBuilder builder) {
        this.appId = builder.appId;
        this.deployType = builder.deployType;
        this.pageNo = builder.pageNo;
        this.pageSize = builder.pageSize;
    }


    public static class RequestPageActivityResourcesBuilder {
        public RequestPageActivityResources build() {
            ApiAssert.notNull(appId, "appId is null");
            if (pageNo <= 0) {
                pageNo = 1;
            }
            if (pageSize <= 0) {
                pageSize = 20;
            }
            return new RequestPageActivityResources(this);
        }
    }
}
