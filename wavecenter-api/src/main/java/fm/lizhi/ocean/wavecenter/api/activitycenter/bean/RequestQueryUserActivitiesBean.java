package fm.lizhi.ocean.wavecenter.api.activitycenter.bean;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyTypeEnum;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageParamBean;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @description: 查询用户提报信息入参
 * @author: guoyibin
 * @create: 2024/10/23 14:56
 */
@Data
@Accessors(chain = true)
public class RequestQueryUserActivitiesBean {

    /**
     * 分页参数
     */
    private PageParamBean pageParam;


    private Integer appId;


    /**
     * 最大活动开始时间
     */
    private Long maxStartTime;

    /**
     * 活动结束时间
     */
    private Long minStartTime;

    /**
     * 活动提报开始时间
     */
    private Long applyStartTime;

    /**
     * 活动提报结束时间
     */
    private Long applyEndTime;

    /**
     * 审核状态
     *
     * @see fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityAuditStatusEnum
     */
    private Integer auditStatus;

    /**
     * 活动名
     */
    private String name;

    /**
     * 厅主波段号
     */
    private String njBrand;

    /**
     * 工会ID
     */
    private Long familyId;

    /**
     * 申请人/报名人波段号
     */
    private String applyBrand;

    /**
     * 活动分类ID
     */
    @Deprecated
    private Long classId;

    private List<Long> classIds;

    /**
     * 活动状态
     *
     * @see fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityStatusEnum
     */
    private Integer activityStatus;

    /**
     * 申报类型
     *
     * @see ActivityApplyTypeEnum
     */
    private Integer applyType;
}
