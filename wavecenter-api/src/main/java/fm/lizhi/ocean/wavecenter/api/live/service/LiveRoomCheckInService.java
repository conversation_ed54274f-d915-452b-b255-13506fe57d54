package fm.lizhi.ocean.wavecenter.api.live.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.live.bean.*;

/**
 * 厅打卡统计
 *
 * @deprecated 已迁移至WaveCheckInDataService
 */
@Deprecated
public interface LiveRoomCheckInService {


    /**
     * 厅的日历汇总
     *
     * @param req
     */
    Result<RoomDayCalendarRes> roomCalendar(RoomDayCalendarReq req);


    /**
     * 获取 厅/主播 的打卡详情
     *
     * @return
     */
    Result<RoomHourCheckDetailRes> hourDetail(RoomHourCheckDetailReq req);


    /**
     * 厅的日统计
     *
     * @return
     */
    Result<LRCSRoomDayStatsRes> roomDayStats(RoomDayCheckStatsReq req);


    /**
     * 厅的日汇总
     */
    Result<RoomDayStatsSummaryRes> roomDayStatsSummary(RoomDayCheckStatsReq req);


    /**
     * 厅的小时统计
     *
     * @return
     */
    Result<LRCSRoomHourStatsRes> roomHourStats(RoomHourCheckStatsReq req);


    /**
     * 厅的小时汇总
     */
    Result<RoomHourStatsSummaryRes> roomHourStatsSummary(RoomHourCheckStatsReq req);


}
