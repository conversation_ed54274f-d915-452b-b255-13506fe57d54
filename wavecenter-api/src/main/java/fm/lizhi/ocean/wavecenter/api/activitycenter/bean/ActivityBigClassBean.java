package fm.lizhi.ocean.wavecenter.api.activitycenter.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 活动大类
 *
 * <AUTHOR>
 * @date 2024-10-10 06:34:31
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ActivityBigClassBean {

    /**
     * 大类ID
     */
    private Long id;

    /**
     * 大类名称
     */
    private String name;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 分类类型，1：常规，2：厅战
     * @see fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityBigClassTypeEnum
     */
    private Integer type;

    /**
     * 修改时间
     */
    private Long modifyTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 品类列表
     */
    private List<Integer> categoryList;
}