package fm.lizhi.ocean.wavecenter.api.anchor.singer.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class RequestQueryHistoryVerifyRecord implements IContextRequest {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 应用ID
     */
    @NotNull(message = "应用ID不能为空")
    @AppEnumId
    private int appId;

    /**
     * 歌手类型
     */
    @NotNull(message = "歌手类型不能为空")
    private Integer singerType;

    @Override
    public Integer foundIdAppId() {
        return appId;
    }

}
