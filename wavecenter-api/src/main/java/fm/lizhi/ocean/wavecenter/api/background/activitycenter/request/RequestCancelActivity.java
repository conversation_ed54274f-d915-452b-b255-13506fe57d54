package fm.lizhi.ocean.wavecenter.api.background.activitycenter.request;

import javax.validation.constraints.NotNull;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import lombok.Data;

@Data
public class RequestCancelActivity {

  /**
     * 活动ID
     */
    @NotNull(message = "活动ID不能为空")
    private Long activityId;

    /**
     * 取消原因
     */
    private String reason;

    /**
     * 操作的用户
     */
    @NotNull(message = "操作者不能为空")
    private String operator;

    @AppEnumId
    private Integer appId;

    /**
     * 版本号
     */
    @NotNull(message = "版本号不能为空")
    private Integer version;

}
