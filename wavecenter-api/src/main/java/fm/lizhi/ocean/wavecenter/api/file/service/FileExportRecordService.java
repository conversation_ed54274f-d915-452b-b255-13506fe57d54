package fm.lizhi.ocean.wavecenter.api.file.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageParamBean;
import fm.lizhi.ocean.wavecenter.api.common.constants.ExportFileStatusEnum;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.FileExportRecordBean;

public interface FileExportRecordService {


    /**
     * 创建导出任务
     */
    Result<FileExportRecordBean> createTask(Integer appId, Long userId, String fileName);

    /**
     * 更新导出任务
     */
    Result<FileExportRecordBean> updateTask(Integer appId, Long recordId, String filePath, ExportFileStatusEnum fileStatus);

    /**
     * 获取导出列表
     */
    Result<PageBean<FileExportRecordBean>> getExportList(Integer appId, Long userId, PageParamBean pageParamBean);

    int DOWN_FILE_NUM_LIMIT = 1;


}
