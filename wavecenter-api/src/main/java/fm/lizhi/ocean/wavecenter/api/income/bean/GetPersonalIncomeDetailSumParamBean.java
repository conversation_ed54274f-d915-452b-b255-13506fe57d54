package fm.lizhi.ocean.wavecenter.api.income.bean;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/4/24 14:47
 */
@Getter
@Builder
public class GetPersonalIncomeDetailSumParamBean {

    private Integer appId;
    /**
     * 开始时间
     */
    private Date startDate;

    private Date endDate;

    private Long userId;


    public static class GetPersonalIncomeDetailSumParamBeanBuilder{
        public GetPersonalIncomeDetailSumParamBean build(){
            ApiAssert.notNull(appId, "appId is required");
            ApiAssert.notNull(startDate, "startDate is required");
            ApiAssert.notNull(endDate, "endDate is required");
            ApiAssert.notNull(userId, "userId is required");
            return new GetPersonalIncomeDetailSumParamBean(appId, startDate, endDate, userId);
        }
    }

}
