package fm.lizhi.ocean.wavecenter.api.award.singer.response;

import fm.lizhi.ocean.wavecenter.api.award.singer.bean.SingerDecorateFlowBean;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.DecorateInfoBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 歌手装扮流水响应
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ResponseSingerDecorateFlow {


    /**
     * 流水明细
     */
    private SingerDecorateFlowBean flow;

    /**
     * 装扮信息
     */
    private DecorateInfoBean decorateInfo;

    /**
     * 用户信息
     */
    private UserBean user;
}
