package fm.lizhi.ocean.wavecenter.api.anchor.singer.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 厅内歌手数量响应
 */
@Data
@Accessors(chain = true)
public class ResponseSingerCountInHall {
    /**
     * 生效中的歌手数量
     */
    private Integer effectiveCount;

    /**
     * 审核中的歌手数量(审核中+认证中的歌手数量)
     */
    private Integer authenticatingCount;

    /**
     * 认证中的歌手数量
     */
    private Integer identityCount;
} 