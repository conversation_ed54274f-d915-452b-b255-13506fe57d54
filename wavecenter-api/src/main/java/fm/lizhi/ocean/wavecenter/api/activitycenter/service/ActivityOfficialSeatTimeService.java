package fm.lizhi.ocean.wavecenter.api.activitycenter.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.RequestGetOfficialSeatTimeBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ResponseGetOfficialSeatTimeBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestActivityOptionOfficialTime;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseActivityOptionOfficialTime;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 官频位时间表
 */
public interface ActivityOfficialSeatTimeService {

    Result<ResponseGetOfficialSeatTimeBean> getOfficialSeatTimeList(RequestGetOfficialSeatTimeBean request);

    /**
     * 获取基础活动配置, 主要用于活动模板层面的配置
     *
     * @param request 请求结果
     * @return 结果
     */
    Result<ResponseActivityOptionOfficialTime> getOptionalOfficialTime(@NotNull @Valid RequestActivityOptionOfficialTime request);

    /**
     * 获取活动官频位信息失败
     */
    int GET_OFFICIAL_SEAT_TIME_LIST = 2180001;

    /**
     * 模板不存在
     */
    int GET_OPTIONAL_OFFICIAL_TIME_TEMPLATE_NOT_EXIST = 2180002;

    /**
     * 获取活动官频位信息失败
     */
    int GET_OPTIONAL_OFFICIAL_TIME_FAIL = 2180003;


}
