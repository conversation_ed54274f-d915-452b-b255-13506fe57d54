package fm.lizhi.ocean.wavecenter.api.activitycenter.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class RequestGetOfficialSeatTimeBean {

    private Integer appId;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 座位
     */
    private Integer seat;
}
