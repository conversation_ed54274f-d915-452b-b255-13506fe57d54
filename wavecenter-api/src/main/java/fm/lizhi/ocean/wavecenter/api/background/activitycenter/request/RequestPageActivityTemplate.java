package fm.lizhi.ocean.wavecenter.api.background.activitycenter.request;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 分页查询活动模板请求
 */
@Data
public class RequestPageActivityTemplate {

    /**
     * 分页页码
     */
    @NotNull(message = "分页页码不能为空")
    private Integer pageNo;

    /**
     * 分页大小
     */
    @NotNull(message = "分页大小不能为空")
    @Max(value = 1000, message = "分页大小不能超过1000")
    private Integer pageSize;

    /**
     * 应用id
     */
    private Integer appId;

    /**
     * 模板id
     */
    private Long id;

    /**
     * 模板名称, 模糊查询
     */
    private String name;

    /**
     * 模板状态
     */
    private Integer status;

    /**
     * 是否热门推荐
     */
    private Boolean hotRec;

    /**
     * 分类id
     */
    private List<Long> classIds;
}
