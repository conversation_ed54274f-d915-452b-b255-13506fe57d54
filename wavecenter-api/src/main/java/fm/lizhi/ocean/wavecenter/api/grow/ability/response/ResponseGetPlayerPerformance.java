package fm.lizhi.ocean.wavecenter.api.grow.ability.response;

import fm.lizhi.ocean.wavecenter.api.grow.ability.bean.PlayerAbilityWeekBean;
import lombok.Data;

import java.util.List;

/**
 * 查询主播能力表现请求响应
 */
@Data
public class ResponseGetPlayerPerformance {

    /**
     * 周列表
     */
    private List<PlayerAbilityWeekBean> weeks;

    public static ResponseGetPlayerPerformance of(List<PlayerAbilityWeekBean> weeks) {
        ResponseGetPlayerPerformance response = new ResponseGetPlayerPerformance();
        response.setWeeks(weeks);
        return response;
    }
}
