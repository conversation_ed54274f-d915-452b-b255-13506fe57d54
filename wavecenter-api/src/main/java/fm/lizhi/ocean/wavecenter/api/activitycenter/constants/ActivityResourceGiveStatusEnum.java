package fm.lizhi.ocean.wavecenter.api.activitycenter.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ActivityResourceGiveStatusEnum {

    /**
     * 等待发放
     */
    WAIT_GIVE(0),

    /**
     * 发放失败
     */
    GIVE_FAIL(1),

    /**
     * 发放成功
     */
    SUCCESS(2),

    /**
     * 取消发放
     */
    CANCEL_GIVE(3),
    ;

    private int status;
}
