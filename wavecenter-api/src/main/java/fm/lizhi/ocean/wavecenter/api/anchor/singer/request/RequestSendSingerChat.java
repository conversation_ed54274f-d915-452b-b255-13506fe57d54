package fm.lizhi.ocean.wavecenter.api.anchor.singer.request;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerChatSceneEnum;
import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 发送歌手私信请求
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RequestSendSingerChat implements IContextRequest {

    @AppEnumId(message = "应用ID不能为空")
    private Integer appId;

    @NotNull(message = "歌手ID不能为空")
    private Long singerId;

    @NotNull(message = "歌手类型不能为空")
    private Integer singerType;

    @NotBlank(message = "歌曲风格不能为空")
    private String songStyle;

    @NotNull(message = "场景不能为空")
    private SingerChatSceneEnum scene;

    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}