package fm.lizhi.ocean.wavecenter.api.anchor.singer.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 查询歌手认证申请情况的状态的枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum SingerApplyVerifyStatusEnum {

    /**
     * 未提交内容，未通过审核
     */
    UN_SUBMIT(1, "未提交内容，未通过审核"),

    /**
     * 已提交内容未通过审核
     */
    SUBMITTED(2, "内容正在审核中，请耐心等待"),

    /**
     * 已通过认证
     */
    PASSED(3, "已通过认证");

    private final int status;

    private final String msg;

}
