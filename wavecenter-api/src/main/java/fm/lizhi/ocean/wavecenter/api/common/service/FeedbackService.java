package fm.lizhi.ocean.wavecenter.api.common.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.AddEvaluateRecordReq;
import fm.lizhi.ocean.wavecenter.api.common.bean.GetEvaluateRecordCountReq;

/**
 * <AUTHOR>
 * @date 2024/6/14 20:38
 */
public interface FeedbackService {

    Result<Void> addEvaluateRecord(AddEvaluateRecordReq req);

    Result<Long> getEvaluateRecordCount(GetEvaluateRecordCountReq req);

}
