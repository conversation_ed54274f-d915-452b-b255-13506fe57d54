package fm.lizhi.ocean.wavecenter.api.grow.ability.constants;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import lombok.Getter;

import java.util.EnumMap;

/**
 * 考核指标
 * <AUTHOR>
 * @date 2025/6/11 19:23
 */
public enum AssessMetricEnum {

    /**
     * 周私信用户数
     */
    CHAT_USER_CNT("chatUserCnt", "周私信用户数"),
    /**
     * 周私信回复用户数
     */
    REPLY_CHAT_USER_CNT("replyChatUserCnt", "周私信回复用户数"),
    /**
     * 周私信回复新用户数
     */
    REPLY_CHAT_NEW_USER_CNT("replyChatNewUserCnt", "周私信回复新用户数"),
    /**
     * 周付费用户数
     */
    GIFT_USER_CNT("giftUserCnt", "周付费用户数"),
    /**
     * 周付费新用户数
     */
    GIFT_NEW_USER_CNT("giftNewUserCnt", "周付费新用户数"),
    /**
     * 周收礼金币/钻石数
     */
    ALL_INCOME("allIncome", "周收礼金币/钻石数", new EnumMap<BusinessEvnEnum, String>(BusinessEvnEnum.class){{
        put(BusinessEvnEnum.PP, "周收礼金币数");
        put(BusinessEvnEnum.HEI_YE, "周收礼钻石数");
        put(BusinessEvnEnum.XIMI, "周收礼钻石数");
    }}),
    /**
     * 周上麦时长
     */
    UP_GUEST_DUR("upGuestDur", "周上麦时长(分钟)"),
    /**
     * 周新增粉丝数
     */
    NEW_FANS_USER_CNT("newFansUserCnt", "周新增粉丝数"),
    /**
     * 周违规次数
     */
    VIOLATION_CNT("violationCnt", "周违规次数"),
    /**
     * 周有效麦序
     */
    CHECK_IN_CNT("checkInCnt", "周有效麦序"),
    ;

    @Getter
    private final String code;

    private final String name;

    /**
     * 业务显示名称特殊处理
     */
    private final EnumMap<BusinessEvnEnum, String> bizDisplayName;

    AssessMetricEnum(String code, String name) {
        this(code, name, new EnumMap<>(BusinessEvnEnum.class));
    }

    AssessMetricEnum(String code, String name, EnumMap<BusinessEvnEnum, String> bizDisplayName) {
        if (bizDisplayName == null) {
            throw new IllegalArgumentException("bizDisplayName must not be null");
        }
        this.code = code;
        this.name = name;
        this.bizDisplayName = bizDisplayName;
    }

    public String getName() {
        BusinessEvnEnum businessEvnEnum = ContextUtils.getBusinessEvnEnum();
        if (businessEvnEnum != null) {
            String displayName = bizDisplayName.get(businessEvnEnum);
            if (displayName != null) {
                return displayName;
            }
        }
        return this.name;
    }

    public String getName(BusinessEvnEnum businessEvnEnum) {
        String displayName = this.bizDisplayName.get(businessEvnEnum);
        if (displayName != null) {
            return displayName;
        }
        return this.name;
    }

}
