package fm.lizhi.ocean.wavecenter.api.resource.shortnumber.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 获取短号的请求
 */
@Data
public class RequestGetShortNumber {

    /**
     * 应用id
     */
    @NotNull(message = "appId不能为空")
    @AppEnumId
    private Integer appId;

    /**
     * 短号id
     */
    @NotNull
    private Long id;
}
