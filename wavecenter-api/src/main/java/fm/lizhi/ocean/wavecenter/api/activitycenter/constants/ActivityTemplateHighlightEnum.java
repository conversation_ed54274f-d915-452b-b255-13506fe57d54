package fm.lizhi.ocean.wavecenter.api.activitycenter.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 活动模板亮点标签枚举
 */
@AllArgsConstructor
@Getter
public enum ActivityTemplateHighlightEnum {

    /**
     * 房间热度提升
     */
    ROOM_POPULARITY_INCREMENT("roomPopularityIncrement", "房间热度提升"),
    /**
     * 收入提升
     */
    INCOME_INCREMENT("incomeIncrement", "收入提升"),
    /**
     * 送礼人数提升
     */
    GIFT_SENDER_INCREMENT("giftSenderIncrement", "送礼人数提升"),
    /**
     * 收听人数提升
     */
    LISTENER_INCREMENT("listenerIncrement", "收听人数提升"),
    /**
     * 收听时长提升
     */
    LISTEN_DURATION_INCREMENT("listenDurationIncrement", "收听时长提升"),
    /**
     * 模板已使用次数
     */
    TEMPLATE_USAGE_COUNT("templateUsageCount", "模板已使用次数"),
    ;

    /**
     * 编码, 对应上架模板时传的highlightKey
     */
    private final String code;
    /**
     * 名称
     */
    private final String name;
}
