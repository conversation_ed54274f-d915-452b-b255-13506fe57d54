package fm.lizhi.ocean.wavecenter.api.activitycenter.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestUserCancelActivity;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestUserModifyActivityAfterAudit;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseUserCancelActivity;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 用户活动操作服务
 */
public interface ActivityOperateService {

    /**
     * 取消活动
     *
     * @param request 请求参数
     * @return 结果
     */
    Result<ResponseUserCancelActivity> userCancelActivityV2(@NotNull @Valid RequestUserCancelActivity request);

    /**
     * 用户修改审批后的活动
     *
     * @param request 请求参数
     * @return 结果
     */
    Result<Void> userModifyActivityAfterAudit(@NotNull @Valid RequestUserModifyActivityAfterAudit request);

    /**
     * 参数异常
     */
    int CANCEL_ACTIVITY_PARAM_ERROR = 22000000;

    /**
     * 活动取消失败
     */
    int CANCEL_ACTIVITY_FAIL= 22000001;

    /**
     * 无权限
     */
    int CANCEL_ACTIVITY_NO_PERMISSION= 22000002;

    /**
     * 活动时间异常
     */
    int CANCEL_ACTIVITY_TIME_ERROR= 22000003;

    /**
     * 活动审核状态异常
     */
    int CANCEL_ACTIVITY_AUDIT_STATUS_ERROR= 22000004;

    /**
     * 参数异常
     */
    int MODIFY_ACTIVITY_PARAM_ERROR = 22000005;

    /**
     * 活动修改失败
     */
    int MODIFY_ACTIVITY_FAIL= 22000006;

    /**
     * 无权限
     */
    int MODIFY_ACTIVITY_NO_PERMISSION= 22000007;


}
