package fm.lizhi.ocean.wavecenter.api.income.bean;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/4/24 19:39
 */
@Data
@Accessors(chain = true)
public class PlayerSumResBean {

    private String canSettlement;
    private String tobeSettlement;

    /**
     * 个播收入
     */
    private PlayerSumDataBean personalHall;

    /**
     * 个人收入
     */
    private PlayerSumDataBean personal;

    /**
     * 个人收礼流水
     */
    private PlayerSumDataBean giftFlow;

    /**
     * 房间收礼流水
     */
    private PlayerSumDataBean roomFlow;

    /**
     * 考核收入
     */
    private PlayerRevenueSumDataBean revenue;

    /**
     * 考核 收益比例
     */
    private String incomeRatio;

}
