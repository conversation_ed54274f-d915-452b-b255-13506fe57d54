package fm.lizhi.ocean.wavecenter.api.anchor.singer.request;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.OriginalSingerInfo;
import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;


@Data
public class RequestSingerVerifyApply implements IContextRequest{

    /**
     * 歌曲名称
     */
    @NotNull(message = "歌曲名称不能为空")
    @Size(max = 50, message = "歌曲名称不能超过50个字符")
    private String songName;

    /**
     * 认证音频路径
     */
    @NotNull(message = "认证音频路径不能为空")
    private String audioPath;

    /**
     * 歌曲风格
     */
    @NotNull(message = "歌曲风格不能为空")
    private String songStyle;

    /**
     * 原唱信息
     */
    @Valid
    private OriginalSingerInfo originalSingerInfo;

    /**
     * 歌手类型
     */
    @NotNull(message = "歌手类型不能为空")
    private Integer singerType;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 应用ID   
     */
    @NotNull(message = "appId is null")
    @AppEnumId
    private Integer appId;

    /**
     * 预审核状态
     * 1：通过
     * 2：SDK无法处理
     * 3：SDK判定假唱
     */
    private Integer preAuditStatus;

    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}
