package fm.lizhi.ocean.wavecenter.api.anchor.singer.constants;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.EnumMap;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum SingerTypeMappingEnum {


    /**
     * 新锐歌手
     */
    NEW(SingerTypeEnum.NEW.getType(), new EnumMap<BusinessEvnEnum, String>(BusinessEvnEnum.class) {
        {
            put(BusinessEvnEnum.PP, "认证歌手");
            put(BusinessEvnEnum.XIMI, "认证歌手");
            put(BusinessEvnEnum.HEI_YE, "新锐音乐人");
        }
    }),

    /**
     * 优质歌手
     */
    QUALITY(SingerTypeEnum.QUALITY.getType(), new EnumMap<BusinessEvnEnum, String>(BusinessEvnEnum.class) {
        {
            put(BusinessEvnEnum.PP, "资深歌手");
            put(BusinessEvnEnum.XIMI, "优质音乐人");
            put(BusinessEvnEnum.HEI_YE, "资深音乐人");

        }
    }),

    /**
     * 明星歌手
     */
    STAR(SingerTypeEnum.STAR.getType(), new EnumMap<BusinessEvnEnum, String>(BusinessEvnEnum.class) {
        {
            put(BusinessEvnEnum.HEI_YE, "明星音乐人");
        }
    });

    private final int waveStatus;

    /**
     * 不同业务对应的值
     */
    @Getter
    private final EnumMap<BusinessEvnEnum, String> bizValueMap;

    /**
     * 获取业务歌手类型
     *
     * @return 业务歌手类型
     */
    public static String getBizSingerType(BusinessEvnEnum curBiz, SingerTypeEnum waveSingerType) {
        if (waveSingerType == null) {
            return null;
        }
        for (SingerTypeMappingEnum enumItem : values()) {
            if (enumItem.getWaveStatus() == waveSingerType.getType()) {
                if (enumItem.getBizValueMap().containsKey(curBiz)) {
                    return enumItem.getBizValueMap().get(curBiz);
                }
            }
        }

        return null;
    }
}
