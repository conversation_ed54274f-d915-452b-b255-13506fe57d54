package fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 活动模板流程
 */
@Data
public class ActivityTemplateProcessBean {

    /**
     * 环节名称
     */
    @NotNull(message = "环节名称不能为空")
    @Size(min = 1, max = 30, message = "环节名称长度必须在{min}-{max}之间")
    private String name;

    /**
     * 时长
     */
    @NotNull(message = "环节时长不能为空")
    @Size(min = 1, max = 100, message = "环节时长长度必须在{min}-{max}之间")
    private String duration;

    /**
     * 说明
     */
    @NotNull(message = "环节说明不能为空")
    private String explanation;
}
