package fm.lizhi.ocean.wavecenter.api.message.request;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import lombok.Data;

/**
 * 保存公告配置请求参数
 * <AUTHOR>
 */
@Data
public class RequestWcNoticeConfigSave {
    /**
     * 标题
     */
    @NotBlank(message = "标题不能为空")
    private String title;
    /**
     * 内容
     */
    @NotBlank(message = "内容不能为空")
    private String content;
    /**
     * 类型
     */
    @NotNull(message = "类型不能为空")
    private Integer type;
    /**
     * 生效时间
     */
    @NotNull(message = "生效时间不能为空")
    private Long effectTime;

    @AppEnumId(message = "应用ID不能为空")
    private Integer appId;

    @NotNull(message = "操作人不能为空")
    private String operator;

    /**
     * ID，修改时使用
     */
    private Long id;
} 