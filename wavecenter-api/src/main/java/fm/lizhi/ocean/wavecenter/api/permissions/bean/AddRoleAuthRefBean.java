package fm.lizhi.ocean.wavecenter.api.permissions.bean;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import lombok.Builder;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/11 15:50
 */
@Getter
@Builder(toBuilder = true)
public class AddRoleAuthRefBean {

    /**
     * 被授权用户ID
     */
    private Long userId;

    /**
     * 角色code
     */
    private String roleCode;

    /**
     * 授权账号用户ID
     */
    private Long subjectUserId;

    private Long createUserId;

    /**
     * 授权厅列表
     */
    private List<Long> authRoomIds;

    /**
     * 家族ID
     */
    private Long familyId;

    public static class AddRoleAuthRefBeanBuilder{
        public AddRoleAuthRefBean build(){
            ApiAssert.notNull(userId, "userId is required");
            ApiAssert.hasText(roleCode, "roleCode is required");
            ApiAssert.notNull(createUserId, "createUserId is required");
            ApiAssert.notNull(familyId, "familyId is required");
            ApiAssert.notNull(subjectUserId, "subjectUserId is required");
            if (RoleEnum.FAMILY_ADMIN.getRoleCode().equals(roleCode)){
                ApiAssert.isTrue(authRoomIds != null && authRoomIds.size()>=2, "authRoomIds is required");
            }

            return new AddRoleAuthRefBean(userId, roleCode, subjectUserId, createUserId, authRoomIds, familyId);
        }
    }

}
