package fm.lizhi.ocean.wavecenter.api.activitycenter.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class OfficialSeatTimeItemBean {

    /**
     * 官频位
     */
    private Integer seat;

    /**
     * 官频位展示开始时间
     */
    private Long startTime;

    /**
     * 官频位展示结束时间
     */
    private Long endTime;

    /**
     * 档期官频位厅已使用数量
     */
    private Integer count;

}
