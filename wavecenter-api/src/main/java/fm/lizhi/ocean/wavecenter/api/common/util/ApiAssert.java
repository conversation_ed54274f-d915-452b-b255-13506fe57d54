package fm.lizhi.ocean.wavecenter.api.common.util;

import com.alibaba.dubbo.common.utils.Assert;
import com.ctrip.framework.apollo.core.utils.StringUtils;
import fm.lizhi.ocean.wavecenter.api.common.exception.BeanCheckException;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/4/11 15:55
 */
public abstract class ApiAssert {

    public static void hasText(String text, String message) {
        if (StringUtils.isBlank(text)) {
            throw new BeanCheckException(message);
        }
    }

    public static void notNull(Object object, String message) {
        if (object == null) {
            throw new BeanCheckException(message);
        }
    }

    public static void isTrue(boolean expression, String message) {
        if (!expression) {
            throw new BeanCheckException(message);
        }
    }

    /**
     * 比较大小，参数1&lt;=参数2，抛出异常
     *
     * @param number 数字1
     * @param target 被对比的数字
     */
    public static void lessOrEqual(Integer number, Integer target, String message) {
        if (number == null || target == null || number <= target) {
            throw new BeanCheckException(message);
        }
    }
    /**
     * 比较大小，参数1&lt;参数2，抛出异常
     *
     * @param number 数字1
     * @param target 被对比的数字
     */
    public static void lessThen(Integer number, Integer target, String message) {
        if (number == null || target == null || number < target) {
            throw new BeanCheckException(message);
        }
    }

    /**
     * 比较大小，参数1&lt;参数2，抛出异常
     *
     * @param number 数字1
     * @param target 被对比的数字
     */
    public static void lessThen(Long number, Long target, String message) {
        if (number == null || target == null || number < target) {
            throw new BeanCheckException(message);
        }
    }
}
