package fm.lizhi.ocean.wavecenter.api.activitycenter.bean;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityTemplateStatusEnum;
import lombok.Data;

import java.util.List;

/**
 * 活动模板信息
 *
 * <AUTHOR>
 */
@Data
public class ActivityTemplateInfoBean {

    /**
     * 活动模板id
     */
    private Long id;

    /**
     * 应用id
     */
    private Integer appId;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 分类id
     */
    private Long classId;

    /**
     * 活动目标
     */
    private String goal;

    /**
     * 活动介绍
     */
    private String introduction;

    /**
     * 辅助道具图片列表
     */
    private List<String> auxiliaryPropUrls;

    /**
     * 活动海报
     */
    private String posterUrl;

    /**
     * 玩法工具列表
     */
    private List<Integer> activityTools;

    /**
     * 房间公告
     */
    private String roomAnnouncement;

    /**
     * 房间公告图片列表
     */
    private List<String> roomAnnouncementImages;

    /**
     * 房间背景id列表
     */
    private List<Long> roomBackgroundIds;

    /**
     * 房间头像框id列表
     */
    private List<Long> avatarWidgetIds;

    /**
     * 是否已删除
     */
    private Boolean deleted;

    /**
     * 封面地址
     */
    private String cover;

    /**
     * 上下架状态
     *
     * @see ActivityTemplateStatusEnum
     */
    private Integer status;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 是否热门推荐
     */
    private Boolean hotRec;

    /**
     * 热门推荐权重
     */
    private Integer hotWeight;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 修改时间
     */
    private Long modifyTime;

    /**
     * 操作人
     */
    private String operator;
}
