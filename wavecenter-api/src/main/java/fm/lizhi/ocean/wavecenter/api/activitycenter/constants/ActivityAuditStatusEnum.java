package fm.lizhi.ocean.wavecenter.api.activitycenter.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ActivityAuditStatusEnum {

    /**
     * 等待审核
     */
    WAITING_AUDIT(1),

    /**
     * 审核通过
     */
    AUDIT_PASS(2),

    /**
     * 审核拒绝
     */
    AUDIT_REJECTED(3),

    /**
     * 用户取消
     */
    USER_CANCEL(4),

    /**
     * 官方取消
     */
    OFFICIAL_CANCEL(5),

    ;

    private Integer status;

    public static ActivityAuditStatusEnum getByStatus(Integer status) {
        for (ActivityAuditStatusEnum auditStatusEnum : values()) {
            if (auditStatusEnum.getStatus().equals(status)) {
                return auditStatusEnum;
            }
        }
        return null;
    }
}
