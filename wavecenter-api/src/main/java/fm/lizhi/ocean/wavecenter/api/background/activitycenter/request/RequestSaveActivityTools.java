package fm.lizhi.ocean.wavecenter.api.background.activitycenter.request;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RequestSaveActivityTools {

    /**
     * 应用ID
     */
    @NotNull(message = "appId 不能为空")
    private int appId;

    /**
     * 操作人
     */
    @NotNull(message = "操作人不能为空")
    private String operator;

    /**
     * 工具名称
     */
    @NotNull(message = "工具名称不能为空")
    private String name;

    /**
     * 类型, 1:玩法; 2: 工具
     */
    @NotNull(message = "类型不能为空")
    private Integer type;

    /**
     * 玩法工具描述
     */
    @NotNull(message = "玩法工具描述不能为空")
    private String toolDesc;

    /**
     * 状态 0: 下架; 1: 上架
     */
    @NotNull(message = "上下架状态不能为空")
    private Integer status;

}
