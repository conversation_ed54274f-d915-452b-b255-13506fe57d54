package fm.lizhi.ocean.wavecenter.api.anchor.singer.response;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerEntranceInfo;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ResponseGetSingerEntranceInfo {

    /**
     * 歌手状态
     */
    private Integer status;

    /**
     * 歌手状态描述文案
     */
    private String msg;

    /**
     * 下一个等级歌手入口信息，为空不显示入口
     */
    private SingerEntranceInfo nextSingerEntranceInfo;

    /**
     * 当前等级歌手入口信息，当前歌手等级和下个歌手等级入口信息都为空，不显示当前歌手入口
     */
    private SingerEntranceInfo currentSingerEntranceInfo;
}
