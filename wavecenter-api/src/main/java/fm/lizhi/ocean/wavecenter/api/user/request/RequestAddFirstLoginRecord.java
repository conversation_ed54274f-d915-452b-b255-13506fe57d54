package fm.lizhi.ocean.wavecenter.api.user.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 新增首次登录记录请求
 * <AUTHOR>
 */
@Data
public class RequestAddFirstLoginRecord {
    /** 应用ID */
    @AppEnumId(message = "应用ID不能为空")
    private Integer appId;
    /** 用户ID */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
} 