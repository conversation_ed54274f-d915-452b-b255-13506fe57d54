package fm.lizhi.ocean.wavecenter.api.common.service;

/**
 * 通用服务, 仅定义一些通用的错误码, 请勿在此接口添加方法. 详细规范参考
 * <a href="https://lizhi2021.feishu.cn/wiki/Ld0Rw5uvGiUimnkgez5cSVJ9n1c">错误码规范</a>
 */
public interface CommonService {

    /**
     * 参数错误
     */
    int PARAM_ERROR = 2000001;

    /**
     * 内部错误
     */
    int INTERNAL_ERROR = 2000002;

    /**
     * 业务异常
     */
    int BUSINESS_ERROR = 2000003;
}
