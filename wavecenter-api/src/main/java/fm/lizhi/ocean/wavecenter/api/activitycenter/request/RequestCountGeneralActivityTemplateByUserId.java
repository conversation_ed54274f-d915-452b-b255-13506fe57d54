package fm.lizhi.ocean.wavecenter.api.activitycenter.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * 统计通用活动模板数量请求
 * <AUTHOR>
 */
@Data
public class RequestCountGeneralActivityTemplateByUserId implements IContextRequest {

    /**
     * 应用id
     */
    @NotNull(message = "应用id不能为空")
    @AppEnumId
    private Integer appId;

    /**
     * 请求用户id
     */
    private Long userId;


    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
