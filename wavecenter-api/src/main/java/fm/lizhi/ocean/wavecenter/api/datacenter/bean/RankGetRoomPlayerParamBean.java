package fm.lizhi.ocean.wavecenter.api.datacenter.bean;

import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
public class RankGetRoomPlayerParamBean {

    private Integer appId;

    private Long roomId;

    private Long familyId;

    private OrderType rankType;

    /**
     * 查询日期，可以为空，缺省默认查询当天的数据
     * 格式：yyyy-MM-dd
     */
    private String date;

    public static class RankGetRoomPlayerParamBeanBuilder{
        public RankGetRoomPlayerParamBean build(){
            ApiAssert.notNull(rankType, "rankType is required");
            ApiAssert.notNull(appId, "appId is required");
            ApiAssert.notNull(roomId, "roomId is required");
            return new RankGetRoomPlayerParamBean(appId, roomId, familyId, rankType, date);
        }
    }

}
