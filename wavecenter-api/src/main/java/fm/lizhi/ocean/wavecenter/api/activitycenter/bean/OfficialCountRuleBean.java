package fm.lizhi.ocean.wavecenter.api.activitycenter.bean;


import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.*;

/**
 * 官频位轮播厅规则
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OfficialCountRuleBean extends ActivityRuleBaseAbstractBean {

    /**
     * 轮播厅数
     */
    private Integer count;

    @Override
    public void verify() {
        ApiAssert.notNull(count, "轮播厅数不能为空");
        ApiAssert.lessThen(count, 0, "轮播厅数不能小于0");
    }
}
