package fm.lizhi.ocean.wavecenter.api.anchor.singer.response;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerSingHallApplyRecordSummaryBean;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 点唱厅考核分页响应
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ResponsePageHallApply {

    private List<SingerSingHallApplyRecordSummaryBean> list;

    private int total;
}
