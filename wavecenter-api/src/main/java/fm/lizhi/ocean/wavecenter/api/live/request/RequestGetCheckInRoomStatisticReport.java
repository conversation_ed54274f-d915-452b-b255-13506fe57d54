package fm.lizhi.ocean.wavecenter.api.live.request;

import fm.lizhi.ocean.wavecenter.api.live.constants.CheckInDateTypeEnum;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotNull;
import java.beans.Transient;
import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * 获取麦序福利厅明细统计的请求
 */
@Data
@ToString
public class RequestGetCheckInRoomStatisticReport {

    /**
     * 应用ID
     */
    @NotNull(message = "应用ID不能为空")
    private Integer appId;

    /**
     * 厅主id
     */
    @NotNull(message = "厅主id不能为空")
    private Long njId;

    /**
     * 统计的时间类型
     */
    @NotNull(message = "统计的时间类型不能为空")
    private CheckInDateTypeEnum dateType = CheckInDateTypeEnum.HOUR;

    /**
     * 开始时间毫秒时间戳, 包含
     */
    @NotNull(message = "开始时间不能为空")
    private Long startDate;

    /**
     * 结束时间毫秒时间戳, 包含
     */
    @NotNull(message = "结束时间不能为空")
    private Long endDate;

}
