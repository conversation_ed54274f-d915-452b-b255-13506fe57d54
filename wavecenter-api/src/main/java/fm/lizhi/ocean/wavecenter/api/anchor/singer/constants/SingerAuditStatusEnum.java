package fm.lizhi.ocean.wavecenter.api.anchor.singer.constants;

import java.util.Arrays;
import java.util.List;

import lombok.Getter;

/**
 * 歌手认证审核状态枚举
 * <AUTHOR>
 */
@Getter
public enum SingerAuditStatusEnum implements BaseStatus {

    /**
     * 待审核
     */
    WAIT_AUDIT(1, "待审核"),

    /**
     * 待定
     */
    WAIT_DECIDE(2, "待定"),

    /**
     * 选中
     */
    SELECTED(3, "选中"),

    /**
     * 审核通过
     */
    PASS(4, "审核通过"),

    /**
     * 预审核不通过
     */
    PRE_AUDIT_REJECTED(5, "预审核不通过"),

    /**
     * 审核不通过
     */
    REJECTED(6, "审核不通过"),
    
    ;

    private final int status;

    private final String name;


    SingerAuditStatusEnum(int status, String name) {
        this.status = status;
        this.name = name;
    }

    public static SingerAuditStatusEnum getByType(int type) {
        for (SingerAuditStatusEnum value : values()) {
            if (value.getStatus() == type) {
                return value;
            }
        }
        return null;
    }

    /**
     * 校验歌手类型是否是合法值
     * @param type 歌手类型
     * @return 是否合法
     */
    public static boolean isValid(int type) {
        return getByType(type) != null;
    }

    /**
     * 是否待审核通过
     * @param status 状态
     * @return 是否待审核通过
     */
    public static boolean isWaitPass(int status) {
        return status == WAIT_AUDIT.getStatus() || status == WAIT_DECIDE.getStatus() || status == SELECTED.getStatus();
    }

    /**
     * 是否待审核通过
     * @param status 状态
     * @return 是否待审核通过
     */
    public static boolean isRejected(int status) {
        return status == REJECTED.getStatus() || status == PRE_AUDIT_REJECTED.getStatus();
    }

    /**
     * 获取歌手认证状态枚举列表
     * @return 歌手认证状态枚举列表
     */
    public static List<SingerAuditStatusEnum> getList() {
        return Arrays.asList(values());
    }

    
}
