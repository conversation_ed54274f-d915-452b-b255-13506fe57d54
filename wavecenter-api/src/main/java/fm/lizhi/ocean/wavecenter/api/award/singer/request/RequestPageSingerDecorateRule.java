package fm.lizhi.ocean.wavecenter.api.award.singer.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 分页获取歌手装扮规则配置列表
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RequestPageSingerDecorateRule implements IContextRequest {


    /**
     * 应用ID
     */
    @NotNull(message = "应用ID不能为空")
    @AppEnumId
    private int appId;

    @NotNull(message = "分页大小不能为空")
    @Min(value = 1, message = "分页大小不能小于1")
    private int pageSize;

    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码不能小于1")
    private int pageNo;


    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}
