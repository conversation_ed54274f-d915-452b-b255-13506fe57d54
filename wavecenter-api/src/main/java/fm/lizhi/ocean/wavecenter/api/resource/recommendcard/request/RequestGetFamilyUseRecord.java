package fm.lizhi.ocean.wavecenter.api.resource.recommendcard.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/3/25 18:25
 */
@Data
public class RequestGetFamilyUseRecord implements IContextRequest {

    @NotNull(message = "appId is null")
    @AppEnumId
    private Integer appId;

    @NotNull(message = "familyId is null")
    private Long familyId;

    private Long njId;

    private String useTimeOrderType;

    private Integer pageNo;

    private Integer pageSize;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
