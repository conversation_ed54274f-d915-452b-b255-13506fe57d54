package fm.lizhi.ocean.wavecenter.api.message.request;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RequestGetMessageList {

    /**
     * 时间戳
     */
    private Long performanceId;

    /**
     * 数量
     */
    private Integer size = 20;

    /**
     * 类型
     * @see fm.lizhi.ocean.wavecenter.api.message.constant.MessageTypeEnum
     */
    private Integer type;

    /**
     * 应用 ID
     */
    private int appId;

    /**
     * 角色 ID
     */
    private String roleCode;

    /**
     * 用户ID
     */
    private Long userId;
}
