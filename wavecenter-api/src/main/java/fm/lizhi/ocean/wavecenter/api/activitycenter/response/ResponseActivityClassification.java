package fm.lizhi.ocean.wavecenter.api.activitycenter.response;

import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivitySimpleClassificationBean;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 *
 * 活动分类
 *
 * <AUTHOR>
 * @date 2024-10-10 06:34:31
 */
@Data
@Builder
public class ResponseActivityClassification {

    /**
     * 大类 ID
     */
    private Long id;

    /**
     * 大类名称
     */
    private String name;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 分类类型，1：常规，2：厅战
     */
    private Integer type;


    private List<ActivitySimpleClassificationBean> classList;
}