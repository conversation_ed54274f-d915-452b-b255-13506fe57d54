package fm.lizhi.ocean.wavecenter.api.activitycenter.bean;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyRulePeriodEnum;
import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.*;

/**
 * 提报次数规则
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReportCountRuleBean extends ActivityRuleBaseAbstractBean {


    /**
     * 提报次数
     */
    private Integer count;

    /**
     * 周期
     *
     * @see ActivityApplyRulePeriodEnum
     */
    private String period;

    /**
     * 黑名单用户ID
     */
    private String userIds;

    /**
     * 收入限制
     */
    private Long income;


    @Override
    public void verify() {
        ApiAssert.notNull(count, "提报次数不能为空");
        ApiAssert.lessThen(count, 0, "提报次数不能小于0");
        ApiAssert.notNull(period, "周期不能为空");

    }
}
