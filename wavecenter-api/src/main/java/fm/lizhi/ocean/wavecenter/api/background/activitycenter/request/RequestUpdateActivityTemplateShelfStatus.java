package fm.lizhi.ocean.wavecenter.api.background.activitycenter.request;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityTemplateStatusEnum;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateHighlightBean;
import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.annotation.EnumValue;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 更新活动模板上下架状态请求
 */
@Data
public class RequestUpdateActivityTemplateShelfStatus implements IContextRequest {

    /**
     * 活动模板id
     */
    @NotNull(message = "活动模板id不能为空")
    private Long id;

    /**
     * 封面地址
     */
    @NotBlank(message = "模板封面不能为空")
    private String cover;

    /**
     * 上下架状态
     */
    @NotNull(message = "上下架状态不能为空")
    @EnumValue(value = ActivityTemplateStatusEnum.class, fieldName = EnumValue.FIELD_NAME_STATUS)
    private Integer status;

    /**
     * 权重
     */
    @NotNull(message = "权重不能为空")
    private Integer weight;

    /**
     * 是否热门推荐
     */
    @NotNull(message = "是否热门推荐不能为空")
    private Boolean hotRec;

    /**
     * 热门推荐权重
     */
    @NotNull(message = "热门推荐权重不能为空")
    private Integer hotWeight;

    /**
     * 活动模板亮点标签列表
     */
    @Valid
    private List<ActivityTemplateHighlightBean> highlights;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;

    /**
     * 上架开始时间
     */
    private Long upStartTime;

    /**
     * 上架结束时间
     */
    private Long upEndTime;

    /**
     * 厅主白名单列表
     */
    private List<Long> njWhiteList;

    @AppEnumId
    @NotNull
    private Integer appId;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
