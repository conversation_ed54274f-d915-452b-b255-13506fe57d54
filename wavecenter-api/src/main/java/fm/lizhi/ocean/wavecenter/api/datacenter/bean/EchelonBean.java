package fm.lizhi.ocean.wavecenter.api.datacenter.bean;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 梯队指标
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class EchelonBean {

    /**
     * 当前数值
     */
    private String current;

    /**
     * 考核梯队
     */
    private String currentEchelonName;

    /**
     * 比例
     */
    private String ratio;

    public static EchelonBean of(String current, String currentEchelonName, String ratio){
        return new EchelonBean().setCurrent(current)
                .setCurrentEchelonName(currentEchelonName)
                .setRatio(ratio);
    }
}
