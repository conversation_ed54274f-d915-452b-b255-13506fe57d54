package fm.lizhi.ocean.wavecenter.api.background.activitycenter.request;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityFodderClassificationEnum;
import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
@Builder
public class RequestUpdateActivityImageFodder {

    private Long id;

    private int appId;

    /**
     * 素材名称
     */
    private String name;

    /**
     * 素材分类，参考枚举值
     * @see ActivityFodderClassificationEnum
     */
    private Integer type;

    /**
     * 素材颜色，包含#
     */
    private String color;


    /**
     * 素材宽高比
     */
    private String scale;

    /**
     * 素材图片地址，斜杠开头
     */
    private String imageUrl;

    /**
     * 操作者
     */
    private String operator;

    private RequestUpdateActivityImageFodder(RequestUpdateActivityImageFodder.RequestUpdateActivityImageFodderBuilder builder) {
        this.id = builder.id;
        this.appId = builder.appId;
        this.name = builder.name;
        this.type = builder.type;
        this.color = builder.color;
        this.imageUrl = builder.imageUrl;
        this.operator = builder.operator;
        this.scale = builder.scale;
    }

    public static class RequestUpdateActivityImageFodderBuilder {
        public RequestUpdateActivityImageFodder build() {
            ApiAssert.notNull(id, "id is null");
            ApiAssert.notNull(appId, "appId is null");
            ApiAssert.notNull(name, "name is null");
            ApiAssert.notNull(type, "type is null");
            ApiAssert.notNull(imageUrl, "imageUrl is null");
            ApiAssert.notNull(operator, "operator is null");
            ApiAssert.notNull(scale, "scale is null");
            return new RequestUpdateActivityImageFodder(this);
        }
    }

}