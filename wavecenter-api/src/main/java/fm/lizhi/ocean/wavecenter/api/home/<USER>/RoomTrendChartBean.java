package fm.lizhi.ocean.wavecenter.api.home.bean;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 房间趋势图
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RoomTrendChartBean {


    /**
     * 房间名称
     */
    private String name;

    /**
     * 数值
     */
    private Double value;

    /**
     * 是否达到告警阈值
     */
    private Boolean warn;

    /**
     * 波段号
     */
    private String band;

    /**
     * 环比
     */
    private String ratio;


    /**
     * 上一期的值
     */
    private Double pre;


}
