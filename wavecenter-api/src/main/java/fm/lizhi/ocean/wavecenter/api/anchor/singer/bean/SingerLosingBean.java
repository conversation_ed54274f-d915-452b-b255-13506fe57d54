package fm.lizhi.ocean.wavecenter.api.anchor.singer.bean;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class SingerLosingBean {

    /**
     * 失败的记录ID
     */
    private Long id;

    /**
     * 失败原因
     */
    private String reason;

    public static SingerLosingBean failure(Long id, String reason) {
        SingerLosingBean bean = new SingerLosingBean();
        bean.setId(id);
        bean.setReason(reason);
        return bean;
    }
}
