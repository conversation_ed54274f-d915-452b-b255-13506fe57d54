package fm.lizhi.ocean.wavecenter.api.resource.decorate.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestCreateAvatarWidget;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestCreateRoomBackground;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.response.ResponseCreateAvatarWidget;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.response.ResponseCreateRoomBackground;

/**
 * 装扮管理后台服务, 前接口错误码以243开头.
 * <ul>
 *     <li>错误码规范参考<a href="https://lizhi2021.feishu.cn/wiki/Kulywk1KliQVGTktY56cny01nvg#JYrkdVrauo2bhbx0IeCcYSjsnMh">错误码规范</a></li>
 *     <li>状态码请维护在<a href="https://lizhi2021.feishu.cn/wiki/Ld0Rw5uvGiUimnkgez5cSVJ9n1c">创作者平台状态码维护文档</a></li>
 *     <li>通用错误码请见{@link CommonService}</li>
 * </ul>
 */
public interface DecorateManagementService {

    /**
     * 创建直播间背景
     *
     * @param request 请求参数
     * @return 响应结果
     */
    Result<ResponseCreateRoomBackground> createRoomBackground(RequestCreateRoomBackground request);

    /**
     * 创建头像框
     *
     * @param request 请求参数
     * @return 响应结果
     */
    Result<ResponseCreateAvatarWidget> createAvatarWidget(RequestCreateAvatarWidget request);

    // ------------------ 方法00, createRoomBackground ------------------

    // ------------------ 方法01, createAvatarWidget ------------------
}
