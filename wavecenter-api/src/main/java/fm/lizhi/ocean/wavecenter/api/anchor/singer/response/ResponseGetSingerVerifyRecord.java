package fm.lizhi.ocean.wavecenter.api.anchor.singer.response;

import com.google.common.collect.Lists;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.OriginalSingerInfo;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerFamilyInfoBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerUserInfoBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerVerifySongInfoBean;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class ResponseGetSingerVerifyRecord {

    /**
     * 记录ID
     */
    private Long id;

    /**
     * 歌手信息
     */
    private SingerUserInfoBean singerUserInfo;

    /**
     * 家族信息
     */
    private SingerFamilyInfoBean familyInfo;

    /**
     * 歌曲名称
     */
    private String songName;

    /**
     * 歌曲风格
     */
    private String songStyle;

    /**
     * 签约信息
     */
    private SingerUserInfoBean njInfo;

    /**
     * 原唱歌手信息
     */
    private OriginalSingerInfo originalSingerInfo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 驳回原因
     */
    private String rejectReason;

    /**
     * 是否通过审核
     */
    private Boolean hasPassVerify;

    /**
     * 是否在黑名单中
     */
    private Boolean inBlackList;

    /**
     * 审核状态
     */
    private Integer auditStatus;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 审核时间
     */
    private Long auditTime;

    /**
     * 音频文件地址
     */
    private String audioPath;

    /**
     * 预审核不过原因
     */
    private String preAuditRejectReason;

    /**
     * 歌曲信息列表
     */
    List<SingerVerifySongInfoBean> singerVerifySongInfoBeans = Lists.newArrayList();

    /**
     * 联系方式
     */
    private String contactNumber;
    
    
}
