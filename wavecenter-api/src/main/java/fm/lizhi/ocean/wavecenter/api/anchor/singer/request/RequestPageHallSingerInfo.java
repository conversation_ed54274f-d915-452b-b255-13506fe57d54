package fm.lizhi.ocean.wavecenter.api.anchor.singer.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 歌手库-厅维度列表
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RequestPageHallSingerInfo implements IContextRequest {


    /**
     * 应用 ID
     */
    @NotNull(message = "应用ID不能为空")
    @AppEnumId
    private int appId;

    /**
     * 厅ID
     */
    private Long njId;

    @NotNull(message = "页码不能为空")
    @Min(value = 1)
    private int pageNo;

    @NotNull(message = "页数不能为空")
    @Min(value = 1)
    private int pageSize;


    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}
