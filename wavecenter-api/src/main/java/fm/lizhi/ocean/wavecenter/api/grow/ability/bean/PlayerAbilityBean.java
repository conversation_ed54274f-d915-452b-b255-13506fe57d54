package fm.lizhi.ocean.wavecenter.api.grow.ability.bean;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 主播能力bean
 */
@Data
public class PlayerAbilityBean {

    /**
     * 能力项code
     */
    private String capabilityCode;

    /**
     * 能力项名称
     */
    private String capabilityName;

    /**
     * 本周能力分, 如果是整数则精确到整数, 如果是小数则精确到小数点后1位
     */
    private BigDecimal thisWeekValue;

    /**
     * 上周能力分, 值类型和精度与本周能力分相同, 可能为null
     */
    private BigDecimal lastWeekValue;

    /**
     * 本周对比上周能力分变化, 可能为null
     */
    private BigDecimal thisWeekValueChange;

    /**
     * 能力表现列表, 如果本周没有数据则为空列表
     */
    private List<PlayerAbilityMetricBean> metrics;

    /**
     * 考核的指标code
     */
    private List<String> assessMetrics;

    /**
     * 厅的指标值
     */
    private List<RoomAbilityMetricBean> roomMetrics;
}
