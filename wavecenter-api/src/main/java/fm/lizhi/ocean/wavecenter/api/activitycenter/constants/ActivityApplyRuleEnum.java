package fm.lizhi.ocean.wavecenter.api.activitycenter.constants;

import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * ·
 * 提报规则枚举
 *
 * <AUTHOR>
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum ActivityApplyRuleEnum implements TypeNameProvider {

    /**
     * 提报次数
     */
    REPORTS_COUNT(1, "提报次数", ReportCountRuleBean.class),

    /**
     * 官频位轮播厅数
     */
    OFFICIAL_COUNT(2, "官频位轮播厅数", OfficialCountRuleBean.class),

    /**
     * 黑名单
     */
    BLACK_LIST(3, "黑名单", BlackListRuleBean.class),

    /**
     * 上周收入门槛（厅）
     */
    LAST_WEEK_HALL_INCOME_THRESHOLD(4, "上周收入门槛（厅）", LastWeekHallIncomeThresholdRuleBean.class),
    ;

    /**
     * 1：提报次数，2：官频位轮播厅数
     */
    private Integer id;

    private String ruleName;

    /**
     * 对应的实体配置类
     */
    private Class<? extends ActivityRuleBaseAbstractBean> clazz;


    public static ActivityApplyRuleEnum getById(Integer id) {
        for (ActivityApplyRuleEnum ruleEnum : ActivityApplyRuleEnum.values()) {
            if (ruleEnum.getId().equals(id)) {
                return ruleEnum;
            }
        }
        return null;
    }

    @Override
    public Integer getType() {
        return id;
    }

    @Override
    public String getName() {
        return ruleName;
    }
}