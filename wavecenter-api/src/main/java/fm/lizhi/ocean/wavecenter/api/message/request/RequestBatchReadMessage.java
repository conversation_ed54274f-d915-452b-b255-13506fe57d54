package fm.lizhi.ocean.wavecenter.api.message.request;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class RequestBatchReadMessage {

    /**
     * 类型
     */
    private Integer type;

    /**
     * 用户 ID
     */
    private Long userId;

    /**
     * 角色
     */
    private String roleCode;

    /**
     * 应用 ID
     */
    private int appId;

    /**
     * 消息 ID 列表
     */
    private List<Long> ids;

    /**
     * 是否一键已读
     * true: 全部已读，忽略 ids
     * false: 使用 ids
     */
    private Boolean allRead;
}
