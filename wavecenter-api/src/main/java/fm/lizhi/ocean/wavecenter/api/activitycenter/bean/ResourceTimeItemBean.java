package fm.lizhi.ocean.wavecenter.api.activitycenter.bean;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ResourceTimeItemBean {

    /**
     * 官频位
     */
    private Integer seat;

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 结束时间
     */
    private Long endTime;

    /**
     * 档期官频位厅已使用数量
     */
    private Integer seatCount;

    /**
     * 节目单已提报厅数
     */
    private Integer programmeCount;

}
