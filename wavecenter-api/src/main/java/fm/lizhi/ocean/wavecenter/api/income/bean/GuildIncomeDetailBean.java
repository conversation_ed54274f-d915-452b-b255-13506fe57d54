package fm.lizhi.ocean.wavecenter.api.income.bean;

import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/4/23 15:11
 */

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class GuildIncomeDetailBean {

    private Date date;

    private UserBean roomInfo;

    private String incomeType;

    private String income;

    private String content;

    private String incomeTypeName;

}
