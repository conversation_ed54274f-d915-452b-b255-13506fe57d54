package fm.lizhi.ocean.wavecenter.api.activitycenter.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 活动工具bean
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ActivityToolBean {

    /**
     * 类型
     * <p>
     * 字段对应的是 ActivityToolsInfoBean#toolValue
     */
    private Integer type;

    /**
     * 名称
     */
    private String name;

    /**
     * 描述
     */
    private String toolDesc;

    /**
     * 工具类型, 1:玩法; 2: 工具
     */
    private Integer toolType;
}
