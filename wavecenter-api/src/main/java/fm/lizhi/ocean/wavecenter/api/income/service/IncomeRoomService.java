package fm.lizhi.ocean.wavecenter.api.income.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomIncomeSummaryBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.*;
import fm.lizhi.ocean.wavecenter.api.income.request.RequestRoomIncomeStats;
import fm.lizhi.ocean.wavecenter.api.income.response.ResponseRoomIncomeStats;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2024/5/10 08:14
 */
public interface IncomeRoomService {

    /**
     * 签约厅收入汇总
     * @param roomId 签约厅ID
     */
    Result<RoomIncomeSummaryBean> roomIncomeSummary(long familyId, long roomId, int appid);

    /**
     * 签约厅收入账户明细记录-查询
     *
     * @param paramBean
     * @return
     */
    Result<PageBean<RoomIncomeDetailBean>> getRoomIncomeDetail(GetRoomIncomeDetailParamBean paramBean);

    /**
     * 签约厅-收入账户明细记录-查询-离线数据
     * 提供给导出使用
     * @param paramBean
     * @return
     */
    Result<PageBean<RoomIncomeDetailBean>> getRoomIncomeDetailOut(GetRoomIncomeDetailParamBean paramBean);

    /**
     * 签约厅-收入账户明细记录-查询-合计
     * @param paramBean
     * @return
     */
    Result<RoomIncomeDetailSumBean> getRoomIncomeDetailSum(GetRoomIncomeDetailSumParamBean paramBean);

    /**
     * 厅收益-签约厅收礼流水记录-查询
     * @param paramBean
     * @return
     */
    Result<PageBean<RoomSignRoomBean>> getRoomSignRoom(GetRoomSignRoomParamBean paramBean);

    /**
     * 厅收益-签约厅收礼流水记录-查询合计
     * @param paramBean
     * @return
     */
    Result<RoomSignRoomSumResBean> getRoomSignRoomSum(GetRoomSignRoomParamBean paramBean);

    /**
     * 厅收益-签约主播收入-查询
     * @return
     */
    Result<PageBean<RoomSignPlayerIncomeBean>> getRoomSignPlayerIncome(GetRoomSignPlayerIncomeParamBean paramBean);

    /**
     * 查询厅某周期收入概览统计
     * @param request 查询请求
     * @return 统计结果
     */
    Result<ResponseRoomIncomeStats> queryRoomIncomeStats(@Valid RequestRoomIncomeStats request);

}
