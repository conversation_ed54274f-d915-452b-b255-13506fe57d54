package fm.lizhi.ocean.wavecenter.api.anchor.singer.request;

import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class RequestSingerRoomDetails implements IContextRequest{

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 厅主ID
     */
    private Long njId;

    /**
     * 页码
     */
    private Integer pageNo = 1;

    /**
     * 每页条数
     */
    private Integer pageSize = 10;

    /**
     * 厅主波段号
     */
    private String njBand;

    /**
     * 排序字段
     * 支持：singerAuth<PERSON><PERSON>, seniorSingerAuthCnt, income
     */
    private String orderMetrics;

    /**
     * 排序方向
     * ASC-升序, DESC-降序，默认DESC
     */
    private OrderType orderType;

    @Override
    public Integer foundIdAppId() {
        return appId;
    }


}
