package fm.lizhi.ocean.wavecenter.api.income.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.*;

/**
 * <AUTHOR>
 * @date 2024/4/23 15:17
 */
public interface IncomePlayerService {

    /**
     * 个人收益-个人收入明细记录-查询
     */
    Result<PageBean<PersonalIncomeDetailBean>> getPersonalIncomeDetail(GetPersonalIncomeDetailParamBean paramBean);

    /**
     * 个人收益-个人收入明细记录-查询-离线数据
     * 提供给导出使用
     */
    Result<PageBean<PersonalIncomeDetailBean>> getPersonalIncomeDetailOut(GetPersonalIncomeDetailParamBean paramBean);

    /**
     * 个人收益-个人收入明细记录-查询-合计
     *
     * @param paramBean
     * @return
     */
    Result<PersonalIncomeDetailSumBean> getPersonalIncomeDetailSum(GetPersonalIncomeDetailSumParamBean paramBean);


    /**
     * 个人收益-个播收入明细记录-查询
     */
    Result<PageBean<PlayerIncomeDetailBean>> getPlayerIncomeDetail(GetPlayerIncomeDetailParamBean paramBean);

    /**
     * 个人收益-个播收入明细记录-查询-离线数据
     * 提供给导出使用
     */
    Result<PageBean<PlayerIncomeDetailBean>> getPlayerIncomeDetailOut(GetPlayerIncomeDetailParamBean paramBean);

    /**
     * 个人收益-个播收入明细记录-查询-合计
     *
     * @param paramBean
     * @return
     */
    Result<PlayerIncomeDetailSumBean> getPlayerIncomeDetailSum(GetPlayerIncomeDetailSumParamBean paramBean);

    /**
     * 个人收益-tab按钮收入汇总
     *
     * @param appId
     * @param userId
     * @return
     */
    Result<PlayerSumResBean> playerSum(int appId, long userId);

    /**
     * 个人收益-个人收礼流水-查询
     *
     * @param paramBean
     * @return
     */
    Result<PageBean<PersonalGiftflowBean>> playerPersonalGiftflow(PlayerPersonalGiftflowParamBean paramBean);

    /**
     * 个人收益-个人收礼流水-查询
     *
     * @param paramBean
     * @return
     */
    Result<PersonalGiftflowBean> playerPersonalGiftflowSum(PlayerPersonalGiftflowParamBean paramBean);

    /**
     * 个人收益-房间收礼流水-查询
     *
     * @param paramBean
     * @return
     */
    Result<PageBean<PlayerRoomGiftflowBean>> playerRoomGiftflow(PlayerRoomGiftflowParamBean paramBean);

    /**
     * 个人收益-房间收礼流水-查询合计
     *
     * @param paramBean
     * @return
     */
    Result<PlayerRoomGiftflowBean> playerRoomGiftflowSum(PlayerRoomGiftflowParamBean paramBean);


    /**
     * 个人收益-考核收入明细记录-查询
     */
    Result<PageBean<PersonalIncomeDetailBean>> getRevenueIncomeDetail(GetPersonalRevenueIncomeDetailParamBean paramBean);

    /**
     * 个人收益-考核明细记录-查询-离线数据
     * 提供给导出使用
     */
    Result<PageBean<PersonalIncomeDetailBean>> getRevenueIncomeDetailOut(GetPersonalIncomeDetailParamBean paramBean);

    /**
     * 个人收益-考核明细记录-查询-合计
     *
     * @param paramBean
     * @return
     */
    Result<PersonalIncomeDetailSumBean> getRevenueIncomeDetailSum(GetPersonalRevenueIncomeDetailParamBean paramBean);


}
