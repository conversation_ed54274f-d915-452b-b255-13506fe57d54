package fm.lizhi.ocean.wavecenter.api.background.activitycenter.request;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class RequestActivityAuditReject {

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 审核不通过原因
     */
    private String reason;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 版本号
     */
    private Integer version;

}
