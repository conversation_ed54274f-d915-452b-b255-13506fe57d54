package fm.lizhi.ocean.wavecenter.api.datacenter.bean;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import fm.lizhi.ocean.wavecenter.api.common.constants.DateType;
import lombok.Builder;
import lombok.Getter;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/19 14:19
 */
@Getter
@Builder
public class RoomGetKeyIndicatorsParamBean implements IGetKeyIndicatorsParam{

    private Integer appId;

    /**
     * @see DateType
     */
    private DateType dateType;

    /**
     * 开始时间 YYYY-MM-DD
     */
    private String startDate;

    private String endDate;

    /**
     * 只查询值的指标
     */
    private List<String> valueMetrics;

    /**
     * 需要查询值和环比的指标
     */
    private List<String> ratioMetrics;

    /**
     * 厅主ID
     */
    private Long roomId;

    /**
     * 家族ID
     */
    private Long familyId;

    public static class RoomGetKeyIndicatorsParamBeanBuilder{
        public RoomGetKeyIndicatorsParamBean build(){
            ApiAssert.notNull(appId, "appId is required");
            ApiAssert.notNull(dateType, "dateType is required");
            ApiAssert.hasText(startDate, "startDate is required");
            ApiAssert.notNull(roomId, "roomId is required");
//            ApiAssert.notNull(familyId, "familyId is required");

            if (dateType == DateType.WEEK) {
                ApiAssert.hasText(endDate, "endDate is required");
            }

            if (valueMetrics == null) {
                valueMetrics = Collections.emptyList();
            }
            if (ratioMetrics == null) {
                ratioMetrics = Collections.emptyList();
            }

            return new RoomGetKeyIndicatorsParamBean(appId, dateType, startDate, endDate, valueMetrics, ratioMetrics, roomId, familyId);
        }
    }

}
