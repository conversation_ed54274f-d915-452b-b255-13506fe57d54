package fm.lizhi.ocean.wavecenter.api.anchor.singer.request;

import java.util.List;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerChatSceneBean;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 歌手私信场景配置请求
 */
@Data
@Accessors(chain = true)
public class RequestSingerChatScene implements IContextRequest {

    private Integer appId;

    private List<SingerChatSceneBean> sceneChatList;

    @Override
    public Integer foundIdAppId() {
        return appId;
    }
} 