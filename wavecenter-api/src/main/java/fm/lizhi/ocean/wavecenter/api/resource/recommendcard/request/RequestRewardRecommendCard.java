package fm.lizhi.ocean.wavecenter.api.resource.recommendcard.request;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

import javax.validation.constraints.NotNull;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.RecommendCardRewardBean;

/**
 * <AUTHOR>
 * @date 2025/3/21 15:35
 */
@Data
@Accessors(chain = true)
public class RequestRewardRecommendCard implements IContextRequest{

    @NotNull(message = "appId is null")
    @AppEnumId
    private Integer appId;

    /**
     * 操作人id
     */
    private Long operatorUserId;

    /**
     * 奖励列表
     */
    private List<RecommendCardRewardBean> rewardBeans;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }

}
