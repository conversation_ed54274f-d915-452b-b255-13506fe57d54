package fm.lizhi.ocean.wavecenter.api.background.activitycenter.response;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 批量操作活动审核响应数据
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ResponseBatchActivityAuditData {
    
    /**
     * 错误列表
     */
    private List<AuditStatus> list;
    
    /**
     * 错误项
     */
    @Data
    @Accessors(chain = true)
    public static class AuditStatus {
        
        /**
         * 活动ID
         */
        private Long id;
        
        /**
         * 活动名称
         */
        private String name;
        
        /**
         * 提示
         */
        private String message;

        /**
         * 状态, 成功、失败
         */
        private Boolean status;
    }
}
