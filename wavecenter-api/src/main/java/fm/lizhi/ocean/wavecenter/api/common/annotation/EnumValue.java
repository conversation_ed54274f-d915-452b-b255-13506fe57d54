package fm.lizhi.ocean.wavecenter.api.common.annotation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 校验被注解的字段是否指定枚举类的值. 枚举类必须有指定字段名的getter方法, 该方法的返回值将被视为合法的枚举值.
 * <p>
 * <b>注意! </b>该注解仅供创作服务中心的服务内部使用, 接口调用方请勿使用.
 */
@Constraint(validatedBy = {EnumValueValidator.class})
@Documented
@Retention(RUNTIME)
@Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE})
public @interface EnumValue {

    /**
     * 指定的枚举类
     *
     * @return 枚举类
     */
    Class<? extends Enum<?>> value();

    /**
     * 枚举值字段名, 默认为{@value FIELD_NAME_VALUE}
     *
     * @return 枚举值字段名
     */
    String fieldName() default FIELD_NAME_VALUE;

    /**
     * 校验不通过时的提示消息
     *
     * @return 校验不通过时的提示消息
     */
    String message() default "${validatedValue}不是枚举{value}的值";

    /**
     * 校验分组类型
     *
     * @return 校验分组类型
     */
    Class<?>[] groups() default {};

    /**
     * 标注负载信息
     *
     * @return 标注负载信息
     */
    Class<? extends Payload>[] payload() default {};

    /**
     * 枚举值字段名value
     */
    String FIELD_NAME_VALUE = "value";
    /**
     * 枚举值字段名status
     */
    String FIELD_NAME_STATUS = "status";
    /**
     * 枚举值字段名type
     */
    String FIELD_NAME_TYPE = "type";
}
