package fm.lizhi.ocean.wavecenter.api.anchor.singer.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 淘汰歌手
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RequestEliminateSinger implements IContextRequest {


    /**
     * 应用 ID
     */
    @NotNull(message = "应用ID不能为空")
    @AppEnumId
    private int appId;

    /**
     * 歌手库ID
     */
    private List<Long> ids;

    /**
     * 淘汰原因
     */
    private String eliminateReason;

    /**
     * 操作人
     */
    private String operator;

    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}
