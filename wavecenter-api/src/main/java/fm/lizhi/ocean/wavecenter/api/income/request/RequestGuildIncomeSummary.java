package fm.lizhi.ocean.wavecenter.api.income.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/13 18:30
 */
@Data
@Accessors(chain = true)
public class RequestGuildIncomeSummary implements IContextRequest {

    @AppEnumId
    @NotNull(message = "appId is null")
    private Integer appId;

    @NotNull(message = "familyId is null")
    private Long familyId;

    private List<Long> roomIds;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
