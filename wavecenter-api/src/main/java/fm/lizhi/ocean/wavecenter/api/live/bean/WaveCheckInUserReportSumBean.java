package fm.lizhi.ocean.wavecenter.api.live.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 麦序福利用户打卡汇总数据, 可能来自多种统计时间范围
 */
@Data
@Accessors(chain = true)
public class WaveCheckInUserReportSumBean {

    /**
     * 有效麦序
     */
    private Integer seatCnt = 0;

    /**
     * 合计魅力值
     */
    private Long sumCharm = 0L;

    /**
     * 私信人数
     */
    private Integer chatUserCnt = 0;
}

