package fm.lizhi.ocean.wavecenter.api.background.activitycenter.request;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityResourceDeployTypeConstants;
import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 保存活动资源
 * <AUTHOR>
 */
@Data
@Builder
public class RequestSaveActivityResource {

    /**
     * 资源名称
     */
    private String name;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 资源介绍
     */
    private String introduction;

    /**
     * 资源图片 URL
     */
    private String imageUrl;

    /**
     * 关联等级 ID 列表
     */
    private List<Long> relationLevelIds;

    /**
     * 资源配置类型，1：自动，2：手动
     */
    private Integer deployType;

    /**
     * 是否可用，0：禁用，1：可用
     */
    private Integer status;

    /**
     * 自动资源的code
     */
    private String resourceCode;

    /**
     * 是否必选
     */
    private Boolean required;

    /**
     * 操作人
     */
    private String operator;


    private RequestSaveActivityResource(RequestSaveActivityResource.RequestSaveActivityResourceBuilder builder) {
        this.appId = builder.appId;
        this.operator = builder.operator;
        this.name = builder.name;
        this.introduction = builder.introduction;
        this.imageUrl = builder.imageUrl;
        this.relationLevelIds = builder.relationLevelIds;
        this.deployType = builder.deployType;
        this.status = builder.status;
        this.required = builder.required;
        this.resourceCode = builder.resourceCode;
    }


    public static class RequestSaveActivityResourceBuilder {

        public RequestSaveActivityResource build() {

            ApiAssert.notNull(appId, "appId is null");
            ApiAssert.notNull(operator, "操作人不能为空");
            ApiAssert.notNull(name, "资源名称不能为空");
            ApiAssert.notNull(deployType, "资源配置类型不能为空");
            ApiAssert.notNull(status, "可用状态不能为空");
            ApiAssert.notNull(required, "必选状态不能为空");

            if (deployType.equals(ActivityResourceDeployTypeConstants.AUTO_CONFIG)) {
                ApiAssert.notNull(resourceCode, "自动资源的code不能为空");
            }

            if (relationLevelIds == null || relationLevelIds.isEmpty()) {
                ApiAssert.notNull(relationLevelIds, "关联等级ID列表不能为空");
            }

            return new RequestSaveActivityResource(this);
        }
    }



}
