package fm.lizhi.ocean.wavecenter.api.resource.recommendcard.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/22 10:19
 */
@Data
@Accessors(chain = true)
public class RequestGetSendRecord implements IContextRequest {

    @NotNull(message = "appId is null")
    @AppEnumId
    private Integer appId;

    /**
     * 开始时间
     */
    @NotNull(message = "startTime is null")
    private Date startTime;

    /**
     * 结束时间
     */
    @NotNull(message = "endTime is null")
    private Date endTime;

    /**
     * 接收用户ID
     */
    private Long userId;

    /**
     * 页码
     */
    @Min(value = 1, message = "pageSize must be greater than 0")
    @NotNull(message = "pageNo is null")
    private Integer pageNo = 1;

    @Min(value = 1, message = "pageSize must be greater than 0")
    @NotNull(message = "pageSize is null")
    private Integer pageSize = 10;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
