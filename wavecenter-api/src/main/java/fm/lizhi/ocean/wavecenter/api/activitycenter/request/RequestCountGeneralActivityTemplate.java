package fm.lizhi.ocean.wavecenter.api.activitycenter.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * 统计通用活动模板数量请求
 * <AUTHOR>
 */
@Data
public class RequestCountGeneralActivityTemplate implements IContextRequest {

    /**
     * 应用id
     */
    @NotNull(message = "应用id不能为空")
    @AppEnumId
    private Integer appId;

    /**
     * 分类id列表, 如果是全部则把所有的分类id传进来
     */
    private List<Long> classIds;

    /**
     * 请求用户id
     */
    private Long requestUserId;

    /**
     * 需要过滤的白名单厅主ID
     */
    private List<Long> njIds = new ArrayList<>();

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
