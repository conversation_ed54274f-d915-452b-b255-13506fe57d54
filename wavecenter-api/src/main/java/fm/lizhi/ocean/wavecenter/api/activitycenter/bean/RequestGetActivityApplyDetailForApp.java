package fm.lizhi.ocean.wavecenter.api.activitycenter.bean;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 请求参数
 * <AUTHOR>
 */
@Data
public class RequestGetActivityApplyDetailForApp {
    /**
     * 应用ID
     */
    private Integer appId;
    /**
     * 活动ID列表
     */
    private List<Long> activityIds;
    /**
     * 厅主ID
     */
    private Long njId;
    /**
     * 开始时间
     */
    private Date beginDate;
    /**
     * 结束时间
     */
    private Date endDate;
}
