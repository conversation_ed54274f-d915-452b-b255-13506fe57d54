package fm.lizhi.ocean.wavecenter.api.anchor.singer.bean;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SingerAuditConfigBean {

    /**
     * 音频时长
     */
    private Integer durationSec;

    /**
     * 语音比例 语音占比
     */
    private Integer thresholdSpeechProportion;

    /**
     * 音乐占比
     */
    private Integer thresholdMusicProportion;

    /**
     * 噪音占比，越大说明低噪越大。
     */
    private Integer thresholdNoiseDB;

    /**
     * 噪音阈值, 与{@code thresholdNoiseDB}功能一样. 因为升级音频审核sdk, 旧版sdk读{@code thresholdNoiseDB}, 新版sdk读{@code thresholdNoiseDBV2}.
     */
    private Integer thresholdNoiseDBV2;

    /**
     * 是否开启语音占比验证
     */
    private Boolean enabledVerifyProportion;
}
