package fm.lizhi.ocean.wavecenter.api.anchor.singer.response;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerDataRoomDayBean;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 歌手库-厅维度列表
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ResponsePageHallSingerInfo {

    private int total;

    private List<SingerDataRoomDayBean> list;

    /**
     * 全平台歌手数
     */
    private Long allSingerCnt;

    /**
     * 全平台有收入歌手数
     */
    private Long allIncomeSingerCnt;
}
