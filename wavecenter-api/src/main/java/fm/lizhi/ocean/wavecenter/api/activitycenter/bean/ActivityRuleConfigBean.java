package fm.lizhi.ocean.wavecenter.api.activitycenter.bean;

import lombok.*;

import java.util.Date;

/**
 *
 * 活动-提报规则
 *
 * <AUTHOR>
 * @date 2024-10-12 05:22:23
 */
@Builder
@Data
public class ActivityRuleConfigBean {
    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 规则类型，1：提报次数，2：官频位轮播厅数
     * @see fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyRuleEnum
     */
    private Integer ruleType;

    /**
     * 修改时间
     */
    private Long modifyTime;

    /**
     * 操作者
     */
    private String operator;

    /**
     * 规则json，根据类型映射对象
     */
    private String ruleJson;

}