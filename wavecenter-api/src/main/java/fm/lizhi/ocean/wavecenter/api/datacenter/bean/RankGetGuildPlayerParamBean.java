package fm.lizhi.ocean.wavecenter.api.datacenter.bean;

import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/22 20:25
 */
@Getter
@Builder
public class RankGetGuildPlayerParamBean {

    private Integer appId;

    private Long familyId;

    private OrderType rankType;

    /**
     * 查询日期，可以为空，缺省默认查询当天的数据
     * 格式：yyyy-MM-dd
     */
    private String date;

    /**
     * 厅数据范围
     * @since 1.4.0
     */
    private List<Long> roomIds;

    public static class RankGetGuildPlayerParamBeanBuilder{
        public RankGetGuildPlayerParamBean build(){
            ApiAssert.notNull(rankType, "rankType is required");
            ApiAssert.notNull(appId, "appId is required");
            ApiAssert.notNull(familyId, "familyId is required");
            return new RankGetGuildPlayerParamBean(appId, familyId, rankType, date, roomIds);
        }
    }

}
