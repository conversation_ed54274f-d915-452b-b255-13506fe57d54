package fm.lizhi.ocean.wavecenter.api.grow.ability.response;

import fm.lizhi.ocean.wavecenter.api.grow.ability.bean.RoomAbilityBean;
import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * 查询厅能力表现请求响应
 */
@Data
public class ResponseGetRoomPerformance {

    /**
     * 能力表现列表, 本周没数据则为空列表
     */
    private List<RoomAbilityBean> abilities;

    public static ResponseGetRoomPerformance ofEmpty() {
        ResponseGetRoomPerformance response = new ResponseGetRoomPerformance();
        response.setAbilities(Collections.emptyList());
        return response;
    }

    public static ResponseGetRoomPerformance of(List<RoomAbilityBean> abilities) {
        ResponseGetRoomPerformance response = new ResponseGetRoomPerformance();
        response.setAbilities(abilities);
        return response;
    }
}
