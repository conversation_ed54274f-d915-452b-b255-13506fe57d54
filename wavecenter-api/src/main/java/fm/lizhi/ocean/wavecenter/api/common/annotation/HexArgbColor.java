package fm.lizhi.ocean.wavecenter.api.common.annotation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 校验被注解的字段是否为HEX颜色格式'AARRGGBB'.
 */
@Constraint(validatedBy = {HexArgbColorValidator.class})
@Documented
@Retention(RUNTIME)
@Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE})
public @interface HexArgbColor {

    /**
     * 校验不通过时的提示消息
     *
     * @return 校验不通过时的提示消息
     */
    String message() default "${validatedValue}不是合法的ARGB颜色值";

    /**
     * 校验分组类型
     *
     * @return 校验分组类型
     */
    Class<?>[] groups() default {};

    /**
     * 标注负载信息
     *
     * @return 标注负载信息
     */
    Class<? extends Payload>[] payload() default {};
}
