package fm.lizhi.ocean.wavecenter.api.activitycenter.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityTemplateGeneralPageBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityTemplateHotPageBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestCountGeneralActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestCountGeneralActivityTemplateByUserId;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestPageGeneralActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestPageHotActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseGetGeneralActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 活动模板服务
 */
public interface ActivityTemplateService {

    /**
     * 分页查询热门活动模板
     *
     * @param req 请求参数
     * @return 响应结果
     */
    Result<PageBean<ActivityTemplateHotPageBean>> pageHotTemplate(@NotNull @Valid RequestPageHotActivityTemplate req);

    /**
     * 分页查询通用活动模板
     *
     * @param req 请求参数
     * @return 响应结果
     */
    Result<PageBean<ActivityTemplateGeneralPageBean>> pageGeneralTemplate(@NotNull @Valid RequestPageGeneralActivityTemplate req);

    /**
     * 获取通用活动模板详情
     *
     * @param templateId 模板id
     * @return 响应结果
     */
    Result<ResponseGetGeneralActivityTemplate> getGeneralTemplate(Long templateId);

    /**
     * 获取通用活动模板数量
     *
     * @param req 请求参数
     * @return 响应结果
     */
    Result<Long> countGeneralTemplate(@NotNull @Valid RequestCountGeneralActivityTemplate req);

    /**
     * 根据用户ID获取通用活动模板数量
     *
     * @return 响应结果
     */
    Result<Long> countGeneralTemplateByUserId(@NotNull @Valid RequestCountGeneralActivityTemplateByUserId req);

    /**
     * 分页查询热门活动模板失败
     */
    int PAGE_HOT_TEMPLATE_FAIL = 2160001;

    /**
     * 分页查询通用活动模板失败
     */
    int PAGE_GENERAL_TEMPLATE_FAIL = 2160101;

    /**
     * 获取通用活动模板详情失败
     */
    int GET_GENERAL_TEMPLATE_FAIL = 2160201;

    /**
     * 获取通用活动模板数量失败
     */
    int COUNT_GENERAL_TEMPLATE_FAIL = 2160301;
}
