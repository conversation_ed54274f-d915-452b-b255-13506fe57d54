package fm.lizhi.ocean.wavecenter.api.anchor.singer.request;

import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RequestCheckSingerPermission implements IContextRequest {

    private Long njId;

    private Long liveId;

    private Long userId;

    private int appId;

    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}
