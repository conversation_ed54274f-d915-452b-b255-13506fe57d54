package fm.lizhi.ocean.wavecenter.api.background.activitycenter.request;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyRuleEnum;
import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class RequestSaveActivityRule {

    /**
     * 应用ID
     */
    private int appId;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 规则类型
     * @see ActivityApplyRuleEnum
     */
    private Integer ruleType;

    /**
     * 规则
     */
    private String ruleJson;

    private RequestSaveActivityRule(RequestSaveActivityRule.RequestSaveActivityRuleBuilder builder) {
        this.appId = builder.appId;
        this.operator = builder.operator;
        this.ruleType = builder.ruleType;
        this.ruleJson = builder.ruleJson;
    }

    public static class RequestSaveActivityRuleBuilder {
        public RequestSaveActivityRule build() {
            ApiAssert.notNull(appId, "appId is null");
            ApiAssert.notNull(operator, "operator is null");
            ApiAssert.notNull(ruleType, "ruleType is null");
            ApiAssert.notNull(ruleJson, "rule is null");
            return new RequestSaveActivityRule(this);
        }
    }
}
