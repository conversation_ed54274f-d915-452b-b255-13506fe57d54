package fm.lizhi.ocean.wavecenter.api.anchor.singer.request;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerHallApplyStatusEnum;
import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 点唱厅管理-分页查询
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RequestPageHallApply implements IContextRequest {

    /**
     * 应用ID
     */
    @NotNull(message = "应用ID不能为空")
    @AppEnumId
    private int appId;

    /**
     * 厅ID
     */
    private Long njId;

    /**
     * 厅主波段号
     */
    private String njBand;

    /**
     * 审核状态
     * @see SingerHallApplyStatusEnum
     */
    private Integer auditStatus;

    /**
     * 开始时间戳
     */
    private Long startTime;

    /**
     * 结束时间戳
     */
    private Long endTime;

    /**
     * 页码
     */
    @NotNull(message = "页码不能为空")
    private Integer pageNo;
    /**
     * 每页数量
     */
    @NotNull(message = "每页数量不能为空")
    private Integer pageSize;

    /**
     * 排序字段
     * 支持：singerAuthCnt, seniorSingerAuthCnt, createTime
     */
    private String orderMetrics;

    /**
     * 排序方向
     * ASC-升序, DESC-降序，默认DESC
     */
    private OrderType orderType;

    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}
