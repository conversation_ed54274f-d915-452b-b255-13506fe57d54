package fm.lizhi.ocean.wavecenter.api.anchor.singer.constants;

import lombok.Getter;

/**
 * 点唱厅考核状态枚举
 *
 * <AUTHOR>
 */
@Getter
public enum SingerHallApplyStatusEnum {

    /**
     * 审核中
     */
    APPLYING(0),
    /**
     * 审核通过
     */
    APPLYED(1),
    /**
     * 审核不通过
     */
    REJECTED(2),
    ;


    private final int status;


    SingerHallApplyStatusEnum(int status) {
        this.status = status;
    }

    public static SingerHallApplyStatusEnum getByStatus(Integer status) {
        if (status == null) {
            return null;
        }
        for (SingerHallApplyStatusEnum value : values()) {
            if (value.getStatus() == status) {
                return value;
            }
        }
        return null;
    }
}
