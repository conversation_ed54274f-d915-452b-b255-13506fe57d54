package fm.lizhi.ocean.wavecenter.api.income.bean;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/4/24 20:01
 */
@Getter
@Builder
public class PlayerPersonalGiftflowParamBean {

    private Integer appId;

    private Long userId;

    private Date startDate;

    private Date endDate;

    private Integer pageNo=1;

    private Integer pageSize=20;

    private String recRoomBand;

    private String sendUserBand;

    public static class PlayerPersonalGiftflowParamBeanBuilder{
        public PlayerPersonalGiftflowParamBean build(){
            ApiAssert.notNull(appId, "appId can not be null");
            ApiAssert.notNull(userId, "userId can not be null");
            ApiAssert.notNull(startDate, "userId can not be null");
            ApiAssert.notNull(endDate, "userId can not be null");
            if (pageNo == null || pageNo <= 0) {
                pageNo = 1;
            }
            if (pageSize == null || pageSize <= 0) {
                pageSize = 20;
            }
            return new PlayerPersonalGiftflowParamBean(appId, userId, startDate, endDate, pageNo, pageSize, recRoomBand, sendUserBand);
        }
    }

}
