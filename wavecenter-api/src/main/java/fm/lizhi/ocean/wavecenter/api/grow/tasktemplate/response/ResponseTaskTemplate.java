package fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.response;

import fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.bean.TaskTemplateCapabilityBean;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 任务模版响应
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResponseTaskTemplate {

    /**
     * 模版ID
     */
    private Long id;

    /**
     * 模版代码
     */
    private String templateCode;

    /**
     * 版本号
     */
    private Long version;

    /**
     * 状态: 1=启用,0=禁用
     */
    private Integer status;

    /**
     * 业务 ID
     */
    private Integer appId;

    /**
     * 条件JSON
     */
    private String conditionJson;

    /**
     * 能力配置列表
     */
    private List<TaskTemplateCapabilityBean> capabilityList;

}