package fm.lizhi.ocean.wavecenter.api.activitycenter.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.RequestGetResourceTimeBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ResponseGetResourceTimeBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseActivityConfig;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseGetBaseActivityConfig;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseActivityConfigBean;

public interface ActivityResourceTimeService {
    /**
     * 获取资源时间表
     */
    Result<ResponseGetResourceTimeBean> getResourceTimeList(RequestGetResourceTimeBean request);


    /**
     * 获取资源时间列表失败
     */
    int GET_RESOURCE_TIME_LIST = 2420000;

}
