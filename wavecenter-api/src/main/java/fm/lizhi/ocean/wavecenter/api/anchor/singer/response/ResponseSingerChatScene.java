package fm.lizhi.ocean.wavecenter.api.anchor.singer.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 歌手私信场景配置响应
 */
@Data
@Accessors(chain = true)
public class ResponseSingerChatScene {

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 歌手类型
     */
    private Integer singerType;

    /**
     * 场景码
     */
    private String sceneCode;

    /**
     * 私信文案
     */
    private String content;

    /**
     * 跳转链接
     */
    private String actionUrl;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 配置标题
     */
    private String title;

    /**
     * 条件描述
     */
    private String conditionRemark;
} 