package fm.lizhi.ocean.wavecenter.api.resource.decorate.request;

import javax.validation.constraints.NotNull;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class RequestGetDecorateInfo implements IContextRequest{

    @NotNull(message = "appId is null")
    @AppEnumId
    private Integer appId;

    /**
     * 装扮id
     */
    @NotNull(message = "decorateId is null")
    private Long decorateId;

    /**
     * 装扮类型
     */
    @NotNull(message = "decorateType is null")
    private Integer decorateType;

    @Override
    public Integer foundIdAppId() {
        return appId;
    }

}