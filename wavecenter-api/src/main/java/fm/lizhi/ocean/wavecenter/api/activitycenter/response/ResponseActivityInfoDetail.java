package fm.lizhi.ocean.wavecenter.api.activitycenter.response;

import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.*;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ResponseActivityInfoDetail{

    /**
     * 活动ID
     */
    private Long id;

    /**
     * 模板 ID
     */
    private Long templateId;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动报名类型
     */
    private Integer applyType;

    /**
     * 活动审核状态
     */
    private Integer auditStatus;

    /**
     * 活动状态
     */
    private Integer activityStatus;

    /**
     * 活动联系人
     */
    private String contact;

    /**
     * 活动开始时间
     */
    private Long startTime;

    /**
     * 活动结束时间
     */
    private Long endTime;

    /**
     * 活动联系方式号码
     */
    private String contactNumber;

    /**
     * 活动主持ID
     */
    private Long hostId;

    /**
     * 审核原因
     */
    private String auditReason;

    /**
     * 活动分类名称
     */
    private String className;

    /**
     * 大分类名称
     */
    private String bigClassName;

    /**
     * 大类类型
     */
    private Integer bigClassType;

    /**
     * 分类ID
     */
    private Long classId;

    /**
     * 活动目标，不超过100字
     */
    private String goal;

    /**
     * 活动介绍，不超过100字
     */
    private String introduction;

    /**
     * 模板封面
     */
    private String cover;

    /**
     * 大类 ID
     */
    private Long bigClassId;

    /**
     * 活动工具
     */
    private List<ActivityToolBean> activityToolList;

    /**
     * 活动海报图片
     */
    private String posterUrl;

    /**
     * 活动道具图片
     */
    private List<String> auxiliaryPropUrls;

    /**
     * 陪档主播
     */
    private List<UserBean> accompanyNjs;

    /**
     * 主持信息
     */
    private UserBean hostInfo;

    /**
     * 活动环节
     */
    private List<ActivityProcessBean> processes;

    /**
     * 房间背景信息
     */
    @Deprecated
    private DecorateBean roomBackgroundInfo;

    /**
     * 房间背景信息
     */
    private List<DecorateBean> roomBackgroundInfos;

    /**
     * 头像框信息
     */
    @Deprecated
    private DecorateBean avatarWidgetInfo;

    /**
     * 头像框信息
     */
    private List<DecorateBean> avatarWidgetInfos;

    /**
     * 厅主信息
     */
    private UserBean njInfo;

    /**
     * 流量资源
     */
    private List<ActivityFlowResourceDetailBean> flowResourceDetails;

    /**
     * 房间公告
     */
    private String roomAnnouncement;

    /**
     * 房间公告图片
     */
    private List<String> roomAnnouncementImages;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 申请者uid
     */
    private Long applicantUid;

    /**
     * 提报模式
     */
    private Integer model;
}
