package fm.lizhi.ocean.wavecenter.api.sign.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.FamilyAndNjContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.FamilyNjSignRecordBean;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestQueryContract;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestQueryFamilyNjSignRecordById;

/**
 * <AUTHOR>
 */
public interface ContractService {

    /**
     * 查询合同
     * @param request
     * @return
     */
    Result<FamilyAndNjContractBean> queryContract(RequestQueryContract request);

    /**
     * 查询签署记录, 仅 hy 实现
     * @param param
     * @return
     */
    Result<FamilyNjSignRecordBean> querySignRecordById(RequestQueryFamilyNjSignRecordById param);

    /**
     * 合同不存在
     */
    int QUERY_CONTRACT_NOT_EXIST = 2460001;

    /**
     * 签约记录不存在
     */
    int QUERY_SIGN_RECORD_NOT_EXIST = 2460002;
}
