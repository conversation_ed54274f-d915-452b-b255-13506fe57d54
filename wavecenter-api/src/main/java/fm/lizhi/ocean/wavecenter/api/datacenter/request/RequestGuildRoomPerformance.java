package fm.lizhi.ocean.wavecenter.api.datacenter.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/11 16:16
 */
@Data
@Accessors(chain = true)
public class RequestGuildRoomPerformance implements IContextRequest {

    @AppEnumId
    @NotNull(message = "appId is null")
    private Integer appId;

    private Long familyId;

    private List<Long> roomIds;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
