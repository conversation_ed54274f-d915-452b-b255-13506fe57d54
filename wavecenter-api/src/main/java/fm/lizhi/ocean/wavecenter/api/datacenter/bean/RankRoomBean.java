package fm.lizhi.ocean.wavecenter.api.datacenter.bean;


import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class RankRoomBean {


    /**
     * 厅主信息
     */
    private UserBean roomInfo;


    /**
     * 收入
     */
    private BigDecimal income;


}
