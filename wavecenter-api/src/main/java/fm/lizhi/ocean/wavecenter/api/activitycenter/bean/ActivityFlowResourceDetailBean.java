package fm.lizhi.ocean.wavecenter.api.activitycenter.bean;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityResourceAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityResourceGiveStatusEnum;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ActivityFlowResourceDetailBean extends ActivityResourceBean {

    /**
     * 资源额外信息
     */
    private FlowResourceExtra extra;

    /**
     * 资源审核状态，0：待审核，1：不发放，2：可发放
     *
     * @see ActivityResourceAuditStatusEnum
     */
    private Integer resourceAuditStatus;

    /**
     * 发放状态，0：未发放，1：发放失败，2：发放成功
     *
     * @see ActivityResourceGiveStatusEnum
     */
    private Integer giveStatus;

    /**
     * 流量资源图片地址
     */
    private String resourceImageUrl;
}
