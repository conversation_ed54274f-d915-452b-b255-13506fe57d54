package fm.lizhi.ocean.wavecenter.api.datacenter.bean;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 公会收入汇总
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class GuildIncomeSummaryThreadBean {

    /**
     * 可结算
     */
    private volatile Long canSettlement;

    /**
     * 待结算
     */
    private volatile Long tobeSettlement;

    /**
     * 今日收入
     */
    private volatile IncomeSummaryBean day;

    /**
     * 本周收入
     */
    private volatile IncomeSummaryBean week;

    /**
     * 本月收入
     */
    private volatile IncomeSummaryBean month;

}
