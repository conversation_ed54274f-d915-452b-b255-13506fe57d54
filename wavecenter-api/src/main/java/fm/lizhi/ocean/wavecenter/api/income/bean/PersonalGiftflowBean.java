package fm.lizhi.ocean.wavecenter.api.income.bean;

import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/4/24 20:03
 */
@Data
public class PersonalGiftflowBean {

    private Date date;

    private UserBean recRoomInfo;

    private String giftName;

    /**
     * 魅力值
     */
    private Integer charm;

    /**
     * 收入钻
     */
    private Integer income;

    private String content;

    private UserBean sendUserInfo;

}
