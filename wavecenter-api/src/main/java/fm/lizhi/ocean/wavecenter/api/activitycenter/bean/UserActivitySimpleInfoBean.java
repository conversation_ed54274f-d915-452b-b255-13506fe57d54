package fm.lizhi.ocean.wavecenter.api.activitycenter.bean;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @description: 用户活动提报简单信息
 * @author: guoyibin
 * @create: 2024/10/23 15:44
 */
@Data
@Accessors(chain = true)
public class UserActivitySimpleInfoBean {

    /**
     * 活动ID
     */
    private Long id;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动报名类型
     */
    private Integer applyType;

    /**
     * 活动审核状态
     */
    private Integer auditStatus;

    /**
     * 审核原因
     */
    private String auditReason;

    /**
     * 活动开始时间
     */
    private Long startTime;

    /**
     * 活动结束时间
     */
    private Long endTime;


    /**
     * 活动分类名称
     */
    private String className;

    /**
     * 大分类名称
     */
    private String bigClassName;

    /**
     * 分类ID
     */
    private Long classId;

    /**
     * 活动等级
     */
    private String levelName;

    /**
     * 提报人ID
     */
    private Long applicantUid;

    /**
     * 提报人波段号
     */
    private String applicantBand;

    /**
     * 提报人名称
     */
    private String applicantName;

    /**
     * 提报家族ID
     */
    private Long familyId;

    /**
     * 提报家族名称
     */
    private String familyName;

    /**
     * 提报厅厅主ID
     */
    private Long njId;

    /**
     * 提报厅厅主波段号
     */
    private String njBrand;

    /**
     * 提报厅厅主名称
     */
    private String njName;

    /**
     * 操作人名称
     */
    private String auditOperator;

    /**
     * 活动提报时间
     */
    private Long createTime;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 活动状态
     *
     * @see fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityStatusEnum
     */
    private Integer activityStatus;

}
