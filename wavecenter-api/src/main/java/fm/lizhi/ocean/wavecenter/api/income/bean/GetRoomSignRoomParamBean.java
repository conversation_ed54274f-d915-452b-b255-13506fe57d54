package fm.lizhi.ocean.wavecenter.api.income.bean;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/4/24 14:17
 */
@Getter
@Builder
public class GetRoomSignRoomParamBean {

    private Integer appId;

    private Long roomId;

    @Setter
    private Date startDate;

    @Setter
    private Date endDate;

    private Integer pageNo;

    private Integer pageSize;


    /**
     * 送礼人ID
     */
    @Setter
    private Long sendUserId;

    /**
     * 收礼人
     */
    @Setter
    private Long recUserId;

    /**
     * 家族ID 家族长请求才有
     */
    private Long familyId;


    public static class GetRoomSignRoomParamBeanBuilder{
        public GetRoomSignRoomParamBean build(){
            ApiAssert.notNull(appId, "appId is required");
            ApiAssert.notNull(roomId, "roomId is required");
            ApiAssert.notNull(startDate, "startDate is required");
            ApiAssert.notNull(endDate, "endDate is required");
            if (pageNo == null || pageNo <= 0) {
                pageNo = 1;
            }
            if (pageSize == null || pageSize <= 0) {
                pageSize = 20;
            }
            return new GetRoomSignRoomParamBean(appId, roomId, startDate, endDate, pageNo, pageSize,sendUserId,recUserId, familyId);
        }
    }

}
