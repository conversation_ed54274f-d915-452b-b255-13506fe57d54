package fm.lizhi.ocean.wavecenter.api.datacenter.bean;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class IncomeSummaryThreadBean {
    /**
     * 收入
     */
    private volatile Long sum;

    /**
     * 签约厅收礼
     */
    private volatile Long roomGift;

    /**
     * 个播收礼
     */
    private volatile Long player;

    /**
     * 签约厅贵族提成
     */
    private volatile Long roomVip;


    /**
     * 个播贵族提成
     */
    private volatile Long playerVip;

    /**
     * 官方厅收入
     */
    private volatile Long officialRoom;

}
