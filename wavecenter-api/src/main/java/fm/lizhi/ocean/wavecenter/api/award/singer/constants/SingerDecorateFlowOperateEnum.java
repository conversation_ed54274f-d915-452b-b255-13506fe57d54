package fm.lizhi.ocean.wavecenter.api.award.singer.constants;

import lombok.Getter;

/**
 * 装扮流水操作类型
 * <AUTHOR>
 */

@Getter
public enum SingerDecorateFlowOperateEnum {

    /**
     * 发放
     */
    GRANT(1),

    /**
     * 回收
     */
     RECOVER(2),
    ;


    SingerDecorateFlowOperateEnum(int code) {
        this.code = code;
    }

    private final int code;

    public static SingerDecorateFlowOperateEnum getEnumByCode(int code) {
        for (SingerDecorateFlowOperateEnum value : SingerDecorateFlowOperateEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
