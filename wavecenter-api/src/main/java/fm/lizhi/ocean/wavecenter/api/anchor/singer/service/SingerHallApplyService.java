package fm.lizhi.ocean.wavecenter.api.anchor.singer.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestImportHallApply;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestOperateHallApply;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestPageHallApply;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponsePageHallApply;

import javax.validation.Valid;
import java.util.List;

/**
 * 点唱厅管理
 * 异常码235xxxx
 * <AUTHOR>
 */
public interface SingerHallApplyService {

    /**
     * 点唱厅列表
     */
    Result<ResponsePageHallApply> pageHallApplyList(@Valid RequestPageHallApply request);

    /**
     * 点唱厅导入
     */
    Result<List<Long>> importHallApply(@Valid RequestImportHallApply request);

    /**
     * 点唱厅操作
     */
    Result<Void> operateHallApply(@Valid RequestOperateHallApply request);

    /**
     * 导入失败
     */
    int IMPORT_HALL_APPLY_FAIL = 2350001;

    /**
     * 操作失败
     */
    int OPERATE_HALL_APPLY_FAIL = 2350002;

    /**
     * 操作失败, 类型不支持
     */
    int OPERATE_HALL_APPLY_NOT_SUPPORT_STATUS = 2350003;



}
