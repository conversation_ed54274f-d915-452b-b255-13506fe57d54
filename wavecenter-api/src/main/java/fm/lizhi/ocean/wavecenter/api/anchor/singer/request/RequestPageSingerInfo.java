package fm.lizhi.ocean.wavecenter.api.anchor.singer.request;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RequestPageSingerInfo implements IContextRequest {

    /**
     * 应用ID
     */
    @NotNull(message = "应用ID不能为空")
    @AppEnumId
    private Integer appId;

    /**
     * 页码
     */
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码不能小于0")
    private int pageNo;

    /**
     * 页数
     */
    @NotNull(message = "页数不能为空")
    @Min(value = 1, message = "页数不能小于0")
    private int pageSize;


    /**
     * 歌手类型
     */
    @NotNull(message = "歌手类型不能为空")
    private SingerTypeEnum singerType;

    /**
     * 厅ID
     */
     private Long njId;


    /**
     * 主播 ID
     */
    private Long userId;

    /**
     * 起始通过时间
     */
    private Long startAuditTime;

    /**
     * 结束通过时间
     */
    private Long endAuditTime;

    /**
     * 起始淘汰时间
     */
    private Long startEliminationTime;

    /**
     * 结束淘汰时间
     */
    private Long endEliminationTime;

    /**
     * 歌手状态
     */
    private List<SingerStatusEnum> singerStatus;

    /**
     * 歌曲风格
     */
    private List<String> songStyle;

    /**
     * 厅主波段号
     */
    private String njBand;

    /**
     * 歌手波段号
     */
    private String singerBand;

    /**
     * 是否原创歌手
     */
    private Boolean originalSinger;

    /**
     * 是否白名单歌手
     */
    private Boolean whiteListSinger;

    /**
     * 排序字段
     * 支持：auditTime, eliminationTime, createTime, modifyTime
     */
    private String orderMetrics;

    /**
     * 排序方向
     * ASC-升序, DESC-降序，默认DESC
     */
    private OrderType orderType;


    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}
