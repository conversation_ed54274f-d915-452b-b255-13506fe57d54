package fm.lizhi.ocean.wavecenter.api.resource.recommendcard.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/3/22 17:08
 */
@Data
@Accessors(chain = true)
public class RequestRecycle implements IContextRequest {

    @NotNull(message = "appId is null")
    @AppEnumId
    private Integer appId;

    private String operator;

    private String reason;

    /**
     * 发放记录ID
     */
    private Long sendRecordId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 回收数量
     * 有 recordId 根据 recordId 回收
     */
    @Max(value = 500, message = "回收数量不能超过500")
    private Integer num;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
