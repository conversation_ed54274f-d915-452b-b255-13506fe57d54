package fm.lizhi.ocean.wavecenter.api.grow.level.request;

import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/3/18 19:39
 */
@Data
@Accessors(chain = true)
public class RequestDeleteFamilyLevelConfig implements IContextRequest {

    @NotNull(message = "id is null")
    private Long id;

    private Integer appId;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
