package fm.lizhi.ocean.wavecenter.api.permissions.bean;

import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/11 11:53
 */
@Data
public class RoleAuthRefBean {

    /**
     * 配置数据的ID
     */
    private String id;

    /**
     * 被授权用户
     */
    private UserBean userInfo;

    /**
     * 角色code
     */
    private String roleCode;

    /**
     * 授权账号
     */
    private UserBean subject;


    private Long subjectId;

    /**
     * 状态
     * 1=启用 0=禁用
     */
    private Integer status;

    private Date modifyTime;

    /**
     * 授权厅列表
     */
    private List<UserBean> authRoomList;

}
