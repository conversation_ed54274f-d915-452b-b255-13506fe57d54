package fm.lizhi.ocean.wavecenter.api.common.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageConfigBean;
import fm.lizhi.ocean.wavecenter.api.common.bean.SaveConfigReqBean;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/20 14:39
 */
public interface CommonConfigService {

    /**
     * 保存用户页面配置
     * @param reqBean
     * @return
     */
    Result<Void> savePageConfig(SaveConfigReqBean reqBean);

    /**
     * 获取页面配置
     * @param appId
     * @param userId
     * @param pageCode 空则获取全部
     * @return
     */
    Result<List<PageConfigBean>> getPageConfig(int appId, long userId, String pageCode);
}
