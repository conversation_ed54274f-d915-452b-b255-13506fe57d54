package fm.lizhi.ocean.wavecenter.api.award.singer.serivce;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestPageSingerDecorateRule;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestSaveSingerDecorateRule;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestUpdateSingerDecorateRule;
import fm.lizhi.ocean.wavecenter.api.award.singer.response.ResponseSingerDecorateRule;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;

import javax.validation.Valid;

/**
 * 歌手装扮规则配置, 接口错误码以 238 开头.
 * <AUTHOR>
 */
public interface SingerDecorateRuleService {


    /**
     * 分页获取歌手装扮规则配置列表
     */
    Result<PageBean<ResponseSingerDecorateRule>> pageSingerDecorateRule(@Valid RequestPageSingerDecorateRule request);


    /**
     * 保存 歌手装扮规则配置
     */
    Result<Void> saveSingerDecorateRule(@Valid RequestSaveSingerDecorateRule request);

    /**
     * 更新 歌手装扮规则配置
     */
    Result<Void> updateSingerDecorateRule(@Valid RequestUpdateSingerDecorateRule request);

    /**
     * 保存 歌手装扮规则配置 失败
     */
    int SAVE_SINGER_DECORATE_RULE_FAIL = 2380001;

    /**
     * 更新 歌手装扮规则配置 失败
     */
    int UPDATE_SINGER_DECORATE_RULE_FAIL = 2380101;

}
