package fm.lizhi.ocean.wavecenter.api.live.bean;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PlayerCheckInDetailRes {

    /**
     * 打卡的用户id
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 波段号
     */
    private String band;

    private String income;

    /**
     * 最终的魅力值
     */
    private Long charm;

    /**
     * 麦序
     */
    private Integer seatOrder;

    /**
     * 调整前的
     */
    private Long originalCharm;

}
