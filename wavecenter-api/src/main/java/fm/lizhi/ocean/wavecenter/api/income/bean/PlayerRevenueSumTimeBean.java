package fm.lizhi.ocean.wavecenter.api.income.bean;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class PlayerRevenueSumTimeBean {

    /**
     * 收入
     */
    private String sumIncome;

    /**
     * 收益
     */
    private String sumRevenueIncome;

    /**
     * 个播收礼
     */
    private PlayerRevenueAndIncomeBean personalIncome;


    /**
     * 签约厅收礼
     */
    private PlayerRevenueAndIncomeBean hallIncome;

    /**
     * 官方厅收礼
     */
    private PlayerRevenueAndIncomeBean officialHallIncome;

}
