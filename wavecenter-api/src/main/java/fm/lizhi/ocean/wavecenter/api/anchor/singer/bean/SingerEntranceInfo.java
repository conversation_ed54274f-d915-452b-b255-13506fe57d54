package fm.lizhi.ocean.wavecenter.api.anchor.singer.bean;

import lombok.Data;
import lombok.experimental.Accessors;


/**
 * 歌手入口信息
 */
@Data
@Accessors(chain = true)
public class SingerEntranceInfo {

    /**
     * 歌手类型
     */
    private Integer singerType;

    /**
     * 对应业务歌手类型名称
     */
    private String singerBizName;

    /**
     * 入口显示开关
     */
    private Boolean entranceShowSwitch = false;

}
