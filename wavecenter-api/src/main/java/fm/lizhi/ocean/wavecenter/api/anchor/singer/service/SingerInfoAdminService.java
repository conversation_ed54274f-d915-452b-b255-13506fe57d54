package fm.lizhi.ocean.wavecenter.api.anchor.singer.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.*;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseGetAllSingerStatics;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponsePageSingerInfo;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseSingerRoomDetails;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;

/**
 * 歌手库信息管理
 * <AUTHOR>
 */
public interface SingerInfoAdminService {

    /**
     * 获取厅主下的歌手详情
     * @param request
     * @return
     */
    Result<PageBean<ResponseSingerRoomDetails>> singerRoomDetails(RequestSingerRoomDetails request);

    /**
     * 获取全平台歌手汇总信息
     * @return
     */
    Result<ResponseGetAllSingerStatics> getAllSingerStatics(RequestGetAllSingerStatics request);


    /**
     * 分页查询歌手信息
     */
    Result<PageBean<ResponsePageSingerInfo>> pageSingerInfo(RequestPageSingerInfo request);

    /**
     * 淘汰歌手
     */
    Result<Void> eliminateSinger(RequestEliminateSinger request);


    /**
     * 晋升歌手
     */
    Result<Void> upgradeSinger(RequestUpgradeSinger request);


    /**
     * 淘汰歌手失败
     */
    int ELIMINATE_SINGER_FAIL = 2410001;

    /**
     * 晋升歌手失败
     */
    int UPGRADE_SINGER_FAIL = 2410101;



}
