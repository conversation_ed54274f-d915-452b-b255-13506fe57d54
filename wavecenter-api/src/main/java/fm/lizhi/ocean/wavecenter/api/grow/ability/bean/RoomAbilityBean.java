package fm.lizhi.ocean.wavecenter.api.grow.ability.bean;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 厅能力bean
 */
@Data
public class RoomAbilityBean {

    /**
     * 能力项code
     */
    private String capabilityCode;

    /**
     * 能力项名称
     */
    private String capabilityName;

    /**
     * 本周能力分, 如果是整数则精确到整数, 如果是小数则精确到小数点后1位
     */
    private BigDecimal thisWeekValue;

    /**
     * 上周能力分, 值类型和精度与本周能力分相同, 可能为null
     */
    private BigDecimal lastWeekValue;

    /**
     * 本周对比上周能力分变化, 可能为null
     */
    private BigDecimal thisWeekValueChange;

    /**
     * 本周公会排名
     */
    private Integer thisWeekRank;

    /**
     * 本周对比上周公会排名变化, 可能为null
     */
    private Integer thisWeekRankChange;

    /**
     * 指标列表
     */
    private List<RoomAbilityMetricBean> metrics;
}
