package fm.lizhi.ocean.wavecenter.api.activitycenter.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RequestGetResourceTimeBean {

    private Integer appId;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 座位
     */
    private Integer seat;

    /**
     * 活动模板id
     */
    private Long templateId;
}
