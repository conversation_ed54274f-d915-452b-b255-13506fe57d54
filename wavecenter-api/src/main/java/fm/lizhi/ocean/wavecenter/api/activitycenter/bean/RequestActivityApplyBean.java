package fm.lizhi.ocean.wavecenter.api.activitycenter.bean;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyTypeEnum;
import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 活动申请bean
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RequestActivityApplyBean implements IContextRequest{

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 应用ID
     */
    @NotNull(message = "请选择应用")
    @AppEnumId
    private Integer appId;

    /**
     * 业务定义不超过10个字，稍微冗余
     */
    @NotNull(message = "活动主题不能为空")
    private String name;

    /**
     * 提报厅厅主ID
     */
    @NotNull(message = "找不到厅主")
    private Long njId;

    /**
     * 活动分类ID
     */
    @NotNull(message = "请选择活动分类")
    private Long classId;

    /**
     * 活动开始时间
     */
    @NotNull(message = "活动开始时间不能为空")
    private Long startTime;

    /**
     * 活动结束时间
     */
    @NotNull(message = "活动结束时间不能为空")
    private Long endTime;

    /**
     * 申请类型，1：自主提报，2: 官方活动
     */
    private ActivityApplyTypeEnum applyType;

    /**
     * 联系人
     */
    private String contact;

    /**
     * 申请者uid
     */
    @NotNull(message = "请登录后再提起申报")
    private Long applicantUid;

    /**
     * 联系方式
     */
    @NotNull(message = "报名人微信不能为空")
    private String contactNumber;

    /**
     * 主持人ID
     */
    private Long hostId;

    /**
     * 陪档主播ID列表
     */
    private List<Long> accompanyNjIds;

    /**
     * 活动目标，不超过100字
     */
    private String goal;

    /**
     * 活动介绍，不超过100字
     */
    @NotNull(message = "活动介绍不能为空")
    private String introduction;

    /**
     * 活动辅助道具图片地址，多个逗号分隔
     */
    private List<String> auxiliaryPropUrl;

    /**
     * 活动海报图片地址
     */
    private String posterUrl;

    /**
     * 玩法工具
     */
    @Valid
    private List<Integer> activityTool;

    /**
     * 房间公告，不超过500字
     */
    private String roomAnnouncement;

    /**
     * 房间公告图片，不超过3个
     */
    private List<String> roomAnnouncementImgUrl;


    /**
     * 房间背景ID列表
     */
    private List<Long> roomBackgroundIds;

    /**
     * 头像框ID列表
     */
    private List<Long> avatarWidgetIds;

    /**
     * 流量资源列表
     */
    @Valid
    private List<ActivityFlowResourceBean> flowResources;

    /**
     * 活动环节列表
     */
    @Valid
    private List<ActivityProcessBean> processList;

    /**
     * 模板ID
     */
    @NotNull(message = "请选择活动模板")
    private Long templateId;

    /**
     * 最大官频位数量
     */
    private Integer maxSeatCount = 0;

    /**
     * 版本号
     */
    private Integer version = 0;

    /**
     * 活动类型
     * @see fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyModelEnum
     */
    private Integer model;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
