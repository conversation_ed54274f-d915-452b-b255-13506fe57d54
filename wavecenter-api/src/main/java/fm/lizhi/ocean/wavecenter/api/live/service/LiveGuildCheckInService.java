package fm.lizhi.ocean.wavecenter.api.live.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.live.bean.*;

/**
 * 工会打卡统计
 *
 * @deprecated 已迁移至WaveCheckInDataService
 */
@Deprecated
public interface LiveGuildCheckInService {


    /**
     * 厅的日统计
     *
     * @return
     */
    Result<LGCSRoomDayStatsRes> roomDayStats(GuildRoomDayCheckStatsReq req);


    /**
     * 厅的日汇总
     */
    Result<GuildRoomDayStatsSummaryRes> roomDayStatsSummary(GuildRoomDayCheckStatsReq req);


    /**
     * 厅的小时统计
     *
     * @return
     */
    Result<LGCSRoomHourStatsRes> roomHourStats(GuildRoomHourCheckStatsReq req);


    /**
     * 厅的小时汇总
     */
    Result<GuildRoomHourStatsSummaryRes> roomHourStatsSummary(GuildRoomHourCheckStatsReq req);

}
