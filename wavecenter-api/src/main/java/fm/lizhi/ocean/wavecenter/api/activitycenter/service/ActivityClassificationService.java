package fm.lizhi.ocean.wavecenter.api.activitycenter.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseActivityClassification;

import java.util.List;

/**
 * 活动分类
 * <AUTHOR>
 */
public interface ActivityClassificationService {

    /**
     * 获取分类列表
     * @param appId
     * @return
     */
    Result<List<ResponseActivityClassification>> getClassificationList(int appId);

    /**
     * 获取用户分类列表
     * @param appId
     * @param userId
     * @return
     */
    Result<List<ResponseActivityClassification>> getClassificationListByUserId(int appId, Long userId);
}
