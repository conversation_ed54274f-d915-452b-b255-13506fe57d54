package fm.lizhi.ocean.wavecenter.api.anchor.singer.service;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestBatchSingerTotalCountInHall;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestGetUserSingerGlory;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestSingerTotalCountInHall;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestUserIsSinger;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseBatchSingerCountInHall;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseGetUserSingerGlory;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseSingerCountInHall;
import fm.lizhi.commons.service.client.pojo.Result;

/**
 * 歌手库
 *
 * <AUTHOR>
 */
public interface SingerInfoService {


    /**
     * 获取用户歌手认证勋章，目前仅 PP 使用
     *
     */
    Result<ResponseGetUserSingerGlory> getUserSingerGlory(RequestGetUserSingerGlory request);

    /**
     * 判断用户是否是歌手
     *
     * @param request
     * @return
     */
    Result<Boolean> userIsSinger(RequestUserIsSinger request);

    /**
     * 获取厅主下的歌手总数
     *
     * @param request
     * @return 返回生效中和审核中的歌手数量
     */
    Result<ResponseSingerCountInHall> singerTotalCountInHall(RequestSingerTotalCountInHall request);

    /**
     * 批量查询厅主下的歌手总数
     *
     * @param request 请求参数
     * @return 返回生效中和审核中的歌手数量
     */
    Result<ResponseBatchSingerCountInHall> batchSingerTotalCountInHall(RequestBatchSingerTotalCountInHall request);
}
