package fm.lizhi.ocean.wavecenter.api.anchor.singer.request;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.OriginalSingerInfo;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SongInfo;
import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @description: 歌手认证新入口
 * @author: guoyibin
 * @create: 2025/07/21 17:07
 */
@Data
public class RequestSingerVerifyApplyV2 implements IContextRequest {

    @Valid
    private OriginalSingerInfo originalSingerInfo;

    @Valid
    private List<SongInfo> songInfos;

    @NotNull(message = "歌手类型不能为空")
    private Integer singerType;

    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @NotNull(message = "appId is null")
    @AppEnumId
    private Integer appId;

    /**
     * 联系方式
     */
    @NotNull(message = "联系方式不能为空")
    @Size(min = 1, max = 64, message = "联系方式不能超过64")
    private String contactNumber;


    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}
