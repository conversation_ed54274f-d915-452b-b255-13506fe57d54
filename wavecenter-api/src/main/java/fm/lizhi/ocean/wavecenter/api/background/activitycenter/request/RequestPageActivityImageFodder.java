package fm.lizhi.ocean.wavecenter.api.background.activitycenter.request;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityFodderClassificationEnum;
import lombok.Builder;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
@Builder
public class RequestPageActivityImageFodder {

    /**
     * 应用 ID
     * 允许为空
     */
    private Integer appId;

    /**
     * 素材名称
     */
    private String name;

    /**
     * 素材分类，参考枚举值
     * @see ActivityFodderClassificationEnum
     */
    private Integer type;

    private int pageNo;

    private int pageSize;


    private RequestPageActivityImageFodder(RequestPageActivityImageFodder.RequestPageActivityImageFodderBuilder builder) {
        this.appId = builder.appId;
        this.name = builder.name;
        this.type = builder.type;
        this.pageNo = builder.pageNo;
        this.pageSize = builder.pageSize;
    }


    public static class RequestPageActivityImageFodderBuilder {
        public RequestPageActivityImageFodder build() {

            if (pageNo <= 0) {
                pageNo = 1;
            }
            if (pageSize <= 0) {
                pageSize = 20;
            }
            return new RequestPageActivityImageFodder(this);

        }
    }
}