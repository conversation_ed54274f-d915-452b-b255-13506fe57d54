package fm.lizhi.ocean.wavecenter.api.home.response;


import fm.lizhi.ocean.wavecenter.api.home.bean.TrendChartBean;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 厅关键数据-趋势图
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ResponseRoomKeyDataTrendChart {


    /**
     * 日期
     */
    private Date date;


    /**
     * 总收入
     */
    private TrendChartBean sumIncome;

    /**
     * 上麦主播数
     */
    private TrendChartBean signUpGuestPlayerCnt;

    /**
     * 考核收入
     */
    private TrendChartBean income;

    /**
     * 人均收入
     */
    private TrendChartBean playerAvgIncome;

    /**
     * 有收入主播数
     */
    private TrendChartBean incomePlayerCnt;
}
