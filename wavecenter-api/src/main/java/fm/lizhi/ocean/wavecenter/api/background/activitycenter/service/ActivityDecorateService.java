package fm.lizhi.ocean.wavecenter.api.background.activitycenter.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.DecorateBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestBatchGetDecorate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestGetDecorate;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;

import java.util.List;

/**
 * 活动装扮
 * <AUTHOR>
 */
public interface ActivityDecorateService {


    /**
     * 获取装扮列表
     * @param param
     * @return
     */
    Result<PageBean<DecorateBean>> getDecorateList(RequestGetDecorate param);

    /**
     * 批量获取装扮列表
     */
    Result<List<DecorateBean>> batchGetDecorateList(RequestBatchGetDecorate param);

    /**
     * 获取装扮列表失败
     */
    int GET_DECORATE_LIST_FAIL = 2080001;

    /**
     * 批量获取装扮列表失败
     */
    int BATCH_GET_DECORATE_LIST_FAIL = 2080101;

}
