package fm.lizhi.ocean.wavecenter.api.anchor.singer.bean;

import java.util.List;

import javax.validation.constraints.NotNull;

import lombok.Data;

@Data
public class OriginalSingerInfo {

    /**
     * 是否为原唱
     */
    @NotNull(message = "是否为原唱不能为空")
    private Boolean originalSinger;

    /**
     * 原唱链接
     */
    private String originalSongUrl;

    /**
     * 社交认证图片列表，最多三个
     */
    private List<String> socialVerifyImageList;
}
