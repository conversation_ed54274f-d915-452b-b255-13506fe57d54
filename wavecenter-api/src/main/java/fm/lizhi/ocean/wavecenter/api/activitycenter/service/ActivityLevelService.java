package fm.lizhi.ocean.wavecenter.api.activitycenter.service;

import java.util.List;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityLevelInfoBean;

public interface ActivityLevelService {

    /**
     * 根据应用ID查询活动等级
     */
    Result<List<ActivityLevelInfoBean>> listByAppId(Integer appId);

    /**
     * 获取活动等级失败
     */
    int GET_ACTIVITY_LEVEL_FAIL = 2180001;
}
