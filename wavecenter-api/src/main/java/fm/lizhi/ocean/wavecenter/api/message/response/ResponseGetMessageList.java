package fm.lizhi.ocean.wavecenter.api.message.response;

import fm.lizhi.ocean.wavecenter.api.message.bean.MessageBean;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-10-30 04:25:18
 */
@Data
@Accessors(chain = true)
public class ResponseGetMessageList {

    /**
     * 消息列表
     */
    private List<MessageBean> messageList;

    /**
     * 未读数
     */
    private Long unRead;

    /**
     * 当前页最后一条消息的创建时间戳
     */
    private Long performanceId;
}