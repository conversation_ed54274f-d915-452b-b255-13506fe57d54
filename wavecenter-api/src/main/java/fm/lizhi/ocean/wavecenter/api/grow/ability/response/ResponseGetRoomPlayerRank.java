package fm.lizhi.ocean.wavecenter.api.grow.ability.response;

import fm.lizhi.ocean.wavecenter.api.grow.ability.bean.RoomPlayerRankBean;
import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * 查询厅主播排名请求响应
 */
@Data
public class ResponseGetRoomPlayerRank {

    /**
     * 厅主播排名列表
     */
    private List<RoomPlayerRankBean> list;

    public static ResponseGetRoomPlayerRank ofEmpty() {
        ResponseGetRoomPlayerRank response = new ResponseGetRoomPlayerRank();
        response.setList(Collections.emptyList());
        return response;
    }

    public static ResponseGetRoomPlayerRank of(List<RoomPlayerRankBean> list) {
        ResponseGetRoomPlayerRank response = new ResponseGetRoomPlayerRank();
        response.setList(list);
        return response;
    }
}
