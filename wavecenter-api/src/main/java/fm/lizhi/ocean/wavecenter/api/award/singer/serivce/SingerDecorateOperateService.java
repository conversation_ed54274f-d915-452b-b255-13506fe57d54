package fm.lizhi.ocean.wavecenter.api.award.singer.serivce;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestBatchDeliverSingerAward;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestDeliverSingerAward;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
public interface SingerDecorateOperateService {

    /**
     * 发放歌手奖励
     *
     * @param request 发放歌手奖励请求参数，包含应用ID、歌手ID、歌手类型和认证记录ID
     * @return 发放结果，成功时返回空结果，失败时返回错误信息
     */
    Result<Void> singerDecorateAwardOperate(@Valid RequestDeliverSingerAward request);

    /**
     * 批量发放歌手奖励
     */
    Result<Void> batchSingerDecorateAwardOperate(@Valid RequestBatchDeliverSingerAward request);

    int DELIVER_SINGER_AWARD_FAIL = 2390001;

}
