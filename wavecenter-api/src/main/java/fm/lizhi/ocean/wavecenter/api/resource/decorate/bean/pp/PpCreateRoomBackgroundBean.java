package fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.pp;

import fm.lizhi.ocean.wavecenter.api.common.annotation.HexArgbColor;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.CreateRoomBackgroundBean;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.constants.DecorateLiveModeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestCreateRoomBackground;
import lombok.Data;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.beans.Transient;
import java.util.HashSet;
import java.util.List;

/**
 * PP创建直播间背景Bean
 *
 * @see RequestCreateRoomBackground
 */
@Data
public class PpCreateRoomBackgroundBean implements CreateRoomBackgroundBean {

    /**
     * 名称
     */
    @NotNull(message = "名称不能为空")
    @Size(min = 1, max = 64, message = "名称长度必须在{min}到${max}个字符之间")
    private String name;

    /**
     * 静态图(实际使用的大图)
     */
    @NotNull(message = "静态图不能为空")
    private String thumbUrl;

    /**
     * 素材, SVGA或PAG资源
     */
    private String materialUrl;

    /**
     * 图标
     */
    @NotNull(message = "图标不能为空")
    private String iconUrl;

    /**
     * 有效期分钟数
     */
    @NotNull(message = "有效期不能为空")
    @Min(value = 1, message = "有效期最小值为{value}分钟")
    private Integer validMin;

    /**
     * 权重
     */
    @NotNull(message = "权重不能为空")
    @Min(value = 1, message = "权重最小值为{value}")
    private Integer weight;

    /**
     * 直播模式
     *
     * @see DecorateLiveModeEnum
     */
    private List<Integer> liveModes;

    /**
     * 弹道颜色
     */
    @NotNull(message = "弹道颜色不能为空")
    @HexArgbColor(message = "弹道颜色不合法")
    private String backgroundColor;

    @AssertTrue(message = "直播模式不合法")
    @Transient
    protected boolean isLiveModesValid() {
        if (liveModes == null || liveModes.isEmpty()) {
            return true;
        }
        HashSet<Integer> values = new HashSet<>();
        for (DecorateLiveModeEnum liveModeEnum : DecorateLiveModeEnum.values()) {
            values.add(liveModeEnum.getValue());
        }
        for (Integer liveMode : liveModes) {
            if (!values.contains(liveMode)) {
                return false;
            }
        }
        return true;
    }
}
