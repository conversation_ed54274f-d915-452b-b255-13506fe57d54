package fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.hy;

import fm.lizhi.ocean.wavecenter.api.common.annotation.EnumValue;
import fm.lizhi.ocean.wavecenter.api.common.annotation.HexArgbColor;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.CreateRoomBackgroundBean;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.constants.DecorateTimeTypeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.constants.DecorateVisibilityEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestCreateRoomBackground;
import lombok.Data;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.beans.Transient;
import java.util.Objects;

/**
 * 黑叶创建直播间背景Bean
 *
 * @see RequestCreateRoomBackground
 */
@Data
public class HyCreateRoomBackgroundBean implements CreateRoomBackgroundBean {

    /**
     * 名称
     */
    @NotNull(message = "名称不能为空")
    @Size(min = 1, max = 64, message = "名称长度必须在{min}到${max}个字符之间")
    private String name;

    /**
     * 角标
     */
    private String thumbUrl;

    /**
     * 图片
     */
    @NotNull(message = "图片不能为空")
    private String materialUrl;

    /**
     * svga素材地址
     */
    private String svgaMaterialUrl;

    /**
     * 有效期分钟数, 有效期类型不为永久时必填
     */
    private Integer validMin;

    /**
     * 可见性
     *
     * @see DecorateVisibilityEnum
     */
    @NotNull(message = "可见性不能为空")
    @EnumValue(value = DecorateVisibilityEnum.class, message = "可见性不合法")
    private Integer visibility;

    /**
     * 背景特效颜色
     */
    @HexArgbColor(message = "背景特效颜色不合法")
    private String backgroundColor;

    /**
     * 时间限制类型
     *
     * @see DecorateTimeTypeEnum
     */
    @NotNull(message = "时间类型不能为空")
    @EnumValue(value = DecorateTimeTypeEnum.class, message = "时间类型不合法")
    private Integer timeType;

    /**
     * 价格(金币数)
     */
    @NotNull(message = "价格不能为空")
    private Integer dressUpCoin;

    /**
     * 备注
     */
    @Size(max = 255, message = "备注长度不能超过${max}个字符")
    private String remark;

    @AssertTrue(message = "时间类型为限期时, 有效期必须大于0")
    @Transient
    protected boolean isValidMinValid() {
        if (!Objects.equals(timeType, DecorateTimeTypeEnum.LIMIT_TIME.getValue())) {
            return true;
        }
        return validMin != null && validMin > 0;
    }
}
