package fm.lizhi.ocean.wavecenter.api.live.bean;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;

import java.util.Date;

@Getter
@Builder
public class AddAuditRecordFullParamBean {

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 签约厅主ID
     */
    private Long signNjId;

    /**
     * 签约家族ID
     */
    private Long signFamilyId;

    /**
     * 关联的业务场景ID: 直播场景为liveId
     */
    private Long bizId;

    /**
     * 关联的业务场景对应的厅主ID,比如直播的厅主ID
     */
    private Long bizNjId;

    /**
     * 关联的业务场景对应的家族ID,比如直播的家族ID
     */
    private Long bizFamilyId;

    /**
     * 违规用户ID
     */
    private Long userId;

    /**
     * 受害者用户ID
     */
    private Long toUserId;

    /**
     * 审核处罚类型
     */
    private Integer op;

    /**
     * 操作理由
     */
    private String reason;

    /**
     * 审核时间
     */
    private Date auditEndTime;

    /**
     * 送审时间
     */
    private Date auditStartTime;

    /**
     * 送审id
     */
    private String auditId;

    /**
     * 审核记录ID
     */
    private String recordId;

    /**
     * 接审业务场景值
     */
    private Integer sceneType;

    /**
     * 接审业务场景名称
     */
    private String sceneName;

    /**
     * 处罚有效时间:1天
     */
    private String pushTime;

    /**
     * 原录音文件 URL
     */
    private String sourceContentUrl;

    private AddAuditRecordFullParamBean(AddAuditRecordFullParamBeanBuilder beanBuilder) {
        this.appId = beanBuilder.appId;
        this.signNjId = beanBuilder.signNjId;
        this.signFamilyId = beanBuilder.signFamilyId;
        this.bizId = beanBuilder.bizId;
        this.bizNjId = beanBuilder.bizNjId;
        this.bizFamilyId = beanBuilder.bizFamilyId;
        this.userId = beanBuilder.userId;
        this.toUserId = beanBuilder.toUserId;
        this.op = beanBuilder.op;
        this.reason = beanBuilder.reason;
        this.auditEndTime = beanBuilder.auditEndTime;
        this.auditStartTime = beanBuilder.auditStartTime;
        this.auditId = beanBuilder.auditId;
        this.recordId = beanBuilder.recordId;
        this.sceneType = beanBuilder.sceneType;
        this.sceneName = beanBuilder.sceneName;
        this.pushTime = beanBuilder.pushTime;
        this.sourceContentUrl = beanBuilder.sourceContentUrl;
    }

    public static class AddAuditRecordFullParamBeanBuilder {
        public AddAuditRecordFullParamBean build() {
            ApiAssert.notNull(appId, "appId is null");
            ApiAssert.notNull(userId, "userId is null");
            ApiAssert.notNull(op, "op is null");
            ApiAssert.hasText(reason, "reason is null");
            ApiAssert.notNull(auditEndTime, "auditEndTime is null");
            ApiAssert.notNull(auditStartTime, "auditStartTime is null");
            ApiAssert.hasText(auditId, "auditId is null");
            ApiAssert.hasText(recordId, "recordId is null");
            ApiAssert.notNull(sceneType, "sceneType is null");
            ApiAssert.hasText(sceneName, "sceneName is null");
            if (this.signNjId == null) {
                this.signNjId = 0L;
            }
            if (this.signFamilyId == null) {
                this.signFamilyId = 0L;
            }
            if (this.bizNjId == null) {
                this.bizNjId = 0L;
            }
            if (this.bizFamilyId == null) {
                this.bizFamilyId = 0L;
            }
            if (this.bizId == null) {
                this.bizId = 0L;
            }
            if (this.toUserId == null) {
                this.toUserId = 0L;
            }
            if (this.sourceContentUrl == null) {
                this.sourceContentUrl = "";
            }
            return new AddAuditRecordFullParamBean(this);
        }
    }

}
