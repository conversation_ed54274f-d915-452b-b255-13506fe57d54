package fm.lizhi.ocean.wavecenter.api.resource.decorate.request;

import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.hy.HyCreateRoomBackgroundBean;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.pp.PpCreateRoomBackgroundBean;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.xm.XmCreateRoomBackgroundBean;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.constants.DecorateLiveModeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.constants.DecorateMaterialTypeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.constants.DecorateTimeTypeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.constants.DecorateVisibilityEnum;
import lombok.Data;

import java.util.List;

/**
 * 创建直播间背景请求, 包含互娱多个业务线的请求参数, 服务端实际使用时会根据业务线转换成对应的bean, 并进行参数校验.
 * 注意, 该类约定所有URL字段均为业务线的CDN域名, 可以直接入业务库.
 *
 * @see HyCreateRoomBackgroundBean
 * @see PpCreateRoomBackgroundBean
 * @see XmCreateRoomBackgroundBean
 */
@Data
public class RequestCreateRoomBackground implements IContextRequest {

    /**
     * 应用id
     */
    private Integer appId;

    /**
     * 名称
     */
    private String name;

    /**
     * 静态图或角标
     */
    private String thumbUrl;

    /**
     * 素材
     */
    private String materialUrl;

    /**
     * 素材类型
     *
     * @see DecorateMaterialTypeEnum
     */
    private String materialType;

    /**
     * 图标
     */
    private String iconUrl;

    /**
     * svga素材地址
     */
    private String svgaMaterialUrl;

    /**
     * 有效期分钟数, 有效期类型不为永久时必填
     */
    private Integer validMin;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 可见性
     *
     * @see DecorateVisibilityEnum
     */
    private Integer visibility;

    /**
     * 直播模式
     *
     * @see DecorateLiveModeEnum
     */
    private List<Integer> liveModes;

    /**
     * 背景颜色
     */
    private String backgroundColor;

    /**
     * 背景类型(皮肤类型)
     */
    private Integer backgroundType;

    /**
     * 点唱坐底图地址
     */
    private String stageUrl;

    /**
     * 舞台底座svga
     */
    private String stageSvgaUrl;

    /**
     * 舞台位默认图
     */
    private String stageLocationUrl;

    /**
     * 主持模式声纹
     */
    private String hostModeVoicePrintSvgaUrl;

    /**
     * 演唱模式声纹
     */
    private String singingModeVoicePrintSvgaUrl;

    /**
     * 舞台光圈
     */
    private String stageApertureSvgaUrl;

    /**
     * 时间限制类型
     *
     * @see DecorateTimeTypeEnum
     */
    private Integer timeType;

    /**
     * 价格(金币数)
     */
    private Integer dressUpCoin;

    /**
     * 备注
     */
    private String remark;

    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}
