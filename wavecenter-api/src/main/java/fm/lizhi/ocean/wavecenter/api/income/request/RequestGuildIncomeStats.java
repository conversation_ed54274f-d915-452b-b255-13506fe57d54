package fm.lizhi.ocean.wavecenter.api.income.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;

/**
 * 查询公会收入概览统计请求
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
@Data
public class RequestGuildIncomeStats implements IContextRequest {

    /**
     * 页面大小
     */
    @NotNull(message = "页面大小不能为空")
    @Max(value = 50, message = "页面大小不能大于50")
    private Integer pageSize;

    /**
     * 应用ID
     */
    @AppEnumId(message = "应用ID不能为空")
    private Integer appId;

    /**
     * 公会ID
     */
    @NotNull(message = "公会ID不能为空")
    private Long familyId;

    /**
     * 统计周期 (day、week、month)
     */
    @NotNull(message = "统计周期不能为空")
    private String statPeriod;

    /**
     * 上次查询的最小时间戳
     */
    private Long lastMinTime;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
} 