package fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.xm;

import fm.lizhi.ocean.wavecenter.api.common.annotation.EnumValue;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.constants.DecorateMaterialTypeEnum;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 西米创建头像框Bean
 *
 * @see XmCreateRoomBackgroundBean
 */
@Data
public class XmCreateAvatarWidgetBean {

    /**
     * 名称
     */
    @NotNull(message = "名称不能为空")
    @Size(min = 1, max = 64, message = "名称长度必须在{min}到${max}个字符之间")
    private String name;

    /**
     * 静态图(实际使用的大图)
     */
    @NotNull(message = "静态图不能为空")
    private String thumbUrl;

    /**
     * 素材, SVAG或PAG资源
     */
    private String materialUrl;

    /**
     * 素材类型(资源类型)
     *
     * @see DecorateMaterialTypeEnum
     */
    @NotNull(message = "资源类型不能为空")
    @EnumValue(value = DecorateMaterialTypeEnum.class, message = "资源类型不合法")
    private String materialType;

    /**
     * PC端动画资源webp文件
     */
    private String pcAniUrl;

    /**
     * 有效期分钟数
     */
    @NotNull(message = "有效期不能为空")
    @Min(value = 1, message = "有效期最小值为{value}分钟")
    private Integer validMin;
}
