package fm.lizhi.ocean.wavecenter.api.datacenter.bean;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class RoomIncomeBean {

    /**
     * 房间信息
     */
    private RoomInfo roomInfo;

    /**
     * 魅力值
     */
    private Integer charm;

    /**
     * 总收入（钻）
     */
    private BigDecimal income;

    /**
     * 签约厅收礼（收入钻）
     */
    private BigDecimal signRoomIncome;

    /**
     * 个播收入（钻）
     */
    private BigDecimal personalRoomIncome;

    /**
     * 厅贵族提成
     */
    private BigDecimal signRoomVipIncome;

    /**
     * 个播贵族提成
     */
    private BigDecimal personalRoomVipIncome;

    /**
     * 有收入主播数
     */
    private int playerPayCount;
}
