package fm.lizhi.ocean.wavecenter.api.live.request;

import lombok.Data;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotNull;
import java.beans.Transient;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * 获取麦序福利主播明细统计的请求
 */
@Data
public class RequestGetCheckInPlayerStatistic {

    /**
     * 应用ID
     */
    @NotNull(message = "应用ID不能为空")
    private Integer appId;

    /**
     * 主播id
     */
    @NotNull(message = "主播id不能为空")
    private Long playerId;

    /**
     * 开始时间毫秒时间戳, 包含
     */
    @NotNull(message = "开始时间不能为空")
    private Long startDate;

    /**
     * 结束时间毫秒时间戳, 包含
     */
    @NotNull(message = "结束时间不能为空")
    private Long endDate;

    /**
     * 家族ID. 如果是主播视角或厅主视角则不传, 如果是家族长视角或高级管理视角则需要传家族ID(避免家族长看到别的家族数据).
     */
    private Long familyId;

    /**
     * 房间ID. 如果是主播视角、家族长视角或高级管理视角则不传, 如果是厅主视角则需要传房间ID.
     */
    private Long roomId;

    @AssertTrue(message = "开始时间必须是0时0分0秒")
    @Transient
    private boolean isStartDateValid() {
        if (startDate == null) {
            return true;
        }
        LocalDateTime startTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(startDate), ZoneId.systemDefault());
        return startTime.getHour() == 0 && startTime.getMinute() == 0 && startTime.getSecond() == 0 && startTime.getNano() == 0;
    }

    @AssertTrue(message = "结束时间必须是23时59分59秒999")
    @Transient
    private boolean isEndDateValid() {
        if (endDate == null) {
            return true;
        }
        LocalDateTime endTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(endDate), ZoneId.systemDefault());
        return endTime.getHour() == 23 && endTime.getMinute() == 59 && endTime.getSecond() == 59 && endTime.getNano() == 999000000;
    }

    @AssertTrue(message = "时间范围不合法, 必须在14天内")
    @Transient
    private boolean isDateRangeValid() {
        if (startDate == null || endDate == null) {
            return true;
        }
        if (endDate < startDate) {
            return false;
        }
        // 暂不考虑闰秒
        return endDate - startDate < 14 * 24 * 60 * 60 * 1000L;
    }
}
