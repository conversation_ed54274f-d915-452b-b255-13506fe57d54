package fm.lizhi.ocean.wavecenter.api.live.bean;


import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class RoomDayStatsRes implements IDetailList<RoomDayDetail, RoomDayStats>{


    private UserBean player;

    /**
     * 合计
     */
    private RoomDayStats stats;

    /**
     * 明细
     */
    private List<RoomDayDetail> detail;

    @Override
    public List<RoomDayDetail> foundDetail() {
        return this.detail;
    }

    @Override
    public RoomDayStats foundStats() {
        return this.stats;
    }
}
