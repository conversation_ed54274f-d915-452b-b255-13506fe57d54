package fm.lizhi.ocean.wavecenter.api.resource.decorate.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.DecorateInfoBean;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestGetDecorateInfo;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestGetDecorates;

public interface DecorateService {

    /**
     * 获取装饰信息
     * @param decorateId
     * @return
     */
    Result<DecorateInfoBean> getDecorateInfo(RequestGetDecorateInfo request);

    /**
     * 获取装饰列表
     * @param request
     * @return
     */
    Result<PageBean<DecorateInfoBean>> getDecorates(RequestGetDecorates request);

    /**
     * 获取装饰列表失败
     */
    int GET_DECORATES_FAIL = 2340001;

    /**
     * 获取装饰信息失败
     */
    int GET_DECORATE_INFO_FAIL = 2340002;

    /**
     * 装饰类型不存在
     */
    int DECORATE_TYPE_NOT_EXIST = 2340003;

    /**
     * 装饰不存在
     */
    int DECORATE_NOT_EXIST = 2340004;

}
