package fm.lizhi.ocean.wavecenter.api.background.activitycenter.request;

import lombok.Data;

import java.io.Serializable;

/**
 * AI结果评分请求对象
 */
@Data
public class ActivityAiResultRateRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 序列ID
     */
    private String serialId;

    /**
     * 分数
     */
    private Integer score;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 建议
     */
    private String advice;
} 