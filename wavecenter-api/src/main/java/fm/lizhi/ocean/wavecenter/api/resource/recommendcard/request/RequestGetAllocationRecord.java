package fm.lizhi.ocean.wavecenter.api.resource.recommendcard.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/24 20:04
 */
@Data
@Accessors(chain = true)
public class RequestGetAllocationRecord implements IContextRequest {

    @NotNull(message = "appId is null")
    @AppEnumId
    private Integer appId;

    @NotNull(message = "familyUserId is null")
    private Long familyUserId;

    private Long njId;

    private Date startDate;

    private Date endDate;

    private Integer pageNum = 1;

    private Integer pageSize = 10;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }

}
