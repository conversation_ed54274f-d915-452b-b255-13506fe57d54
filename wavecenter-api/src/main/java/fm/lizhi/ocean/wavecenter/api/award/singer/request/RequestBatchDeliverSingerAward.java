package fm.lizhi.ocean.wavecenter.api.award.singer.request;

import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateFlowOperateEnum;
import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 批量发放歌手奖励请求
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RequestBatchDeliverSingerAward implements IContextRequest {

    @AppEnumId(message = "应用ID不能为空")
    private Integer appId;

    @NotEmpty(message = "歌手ID列表不能为空")
    private List<Long> singerIds;

    @NotNull(message = "歌手类型不能为空")
    private Integer singerType;

    /**
     * 操作人
     */
    @NotNull(message = "操作人不能为空")
    private String operator;

    /**
     * 原因
     */
    @NotNull(message = "原因不能为空")
    private String reason;

    /**
     * 操作类型
     */
    @NotNull(message = "操作类型不能为空")
    private SingerDecorateFlowOperateEnum operateType;

    /**
     * 事务ID
     */
    private Long transactionId;


    @Override
    public Integer foundIdAppId() {
        return appId;
    }

}
