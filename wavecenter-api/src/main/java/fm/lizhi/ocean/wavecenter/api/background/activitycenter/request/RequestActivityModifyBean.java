package fm.lizhi.ocean.wavecenter.api.background.activitycenter.request;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityFlowResourceBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityProcessBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyTypeEnum;
import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import lombok.Data;

/**
 * @description: 运营修改活动申请
 * @author: guoyibin
 * @create: 2024/12/26 17:54
 */
@Data
public class RequestActivityModifyBean {

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 应用ID
     */
    @NotNull(message = "请选择应用")
    @AppEnumId
    private Integer appId;

    /**
     * 业务定义不超过10个字，稍微冗余
     */
    @NotNull(message = "活动主题不能为空")
    private String name;

    /**
     * 活动开始时间
     */
    @NotNull(message = "活动开始时间不能为空")
    private Long startTime;

    /**
     * 活动结束时间
     */
    @NotNull(message = "活动结束时间不能为空")
    private Long endTime;

    /**
     * 申请类型，1：自主提报，2: 官方活动
     */
    private ActivityApplyTypeEnum applyType;

    /**
     * 联系人
     */
    @NotNull(message = "活动联系人不能为空")
    private String contact;

    /**
     * 申请者uid
     */
    @NotNull(message = "请登录后再提起申报")
    private Long applicantUid;

    /**
     * 联系方式
     */
    @NotNull(message = "报名人微信不能为空")
    private String contactNumber;

    /**
     * 主持人ID
     */
    private Long hostId;

    /**
     * 陪档主播ID列表
     */
    private List<Long> accompanyNjIds;

    /**
     * 活动目标，不超过100字
     */
    @NotNull(message = "活动目标不能为空")
    private String goal;

    /**
     * 活动介绍，不超过100字
     */
    @NotNull(message = "活动介绍不能为空")
    private String introduction;

    /**
     * 活动辅助道具图片地址，多个逗号分隔
     */
    @Valid
    private List<String> auxiliaryPropUrl;

    /**
     * 玩法工具
     */
    @Valid
    private List<Integer> activityTool;

    /**
     * 房间公告，不超过500字
     */
    private String roomAnnouncement;

    /**
     * 房间公告图片，不超过3个
     */
    private List<String> roomAnnouncementImgUrl;

    /**
     * 房间背景ID
     */
    @Deprecated
    private Long roomBackgroundId;

    /**
     * 房间背景ID列表
     */
    private List<Long> roomBackgroundIds;

    /**
     * 头像框ID
     */
    @Deprecated
    private Long avatarWidgetId;

    /**
     * 头像框ID列表
     */
    private List<Long> avatarWidgetIds;

    /**
     * 活动环节列表
     */
    @Valid
    private List<ActivityProcessBean> processList;

    /**
     * 流量资源列表
     */
    @Valid
    private List<ActivityFlowResourceBean> flowResources;

    /**
     * 最大官频位数量
     */
    private Integer maxSeatCount = 0;

    /**
     * 版本号
     */
    private Integer version = 0;

    /**
     * 活动海报图片地址
     */
    private String posterUrl;

    /**
     * 模板ID
     */
    private Long templateId;

    /**
     * 分类id
     */
    private Long classId;

    /**
     * 活动状态，1：待审批，2：审批通过，3：审批不通过
     */
    private Integer auditStatus;

    /**
     * 活动类型
     * @see fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyModelEnum
     */
    private Integer model;
}
