package fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 任务模版项
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskTemplateBean {

    /**
     * 能力项code
     */
    @NotBlank(message = "能力项code不能为空")
    private String capabilityCode;

    /**
     * 能力分
     */
    @NotNull(message = "能力分不能为空")
    @DecimalMin(value = "0", message = "能力分必须大于等于0")
    private Integer capabilityScore;

    /**
     * 条件组
     */
    @NotNull(message = "条件组不能为空")
    @Valid
    private ConditionGroupBean conditionGroup;

    /**
     * ID (更新时传入，新增时为空)
     */
    private Long id;
}
