package fm.lizhi.ocean.wavecenter.api.message.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import lombok.Data;

/**
 * 公告配置分页查询请求参数
 * <AUTHOR>
 */
@Data
public class RequestWcNoticeConfigPage {
    /**
     * 页码
     */
    private Integer pageNo;
    /**
     * 每页条数
     */
    private Integer pageSize;
    /**
     * 类型
     */
    private Integer type;

    /**
     * 生效时间
     */
    private Long minEffectTime;
    /**
     * 生效时间
     */
    private Long maxEffectTime;

    /**
     * 标题
     */
    private String title;

    /**
     * 是否上架
     */
    private Integer status;

    /**
     * 应用ID
     */
    @AppEnumId(message = "应用ID不能为空")
    private Integer appId;

}