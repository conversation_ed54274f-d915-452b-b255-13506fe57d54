package fm.lizhi.ocean.wavecenter.api.anchor.singer.response;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.RoomDetailUserInfoBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerFamilyInfoBean;
import lombok.Data;

@Data
public class ResponseSingerRoomDetails {

    /**
     * 家族信息
     */ 
    private SingerFamilyInfoBean familyInfo;

    /**
     * 高级歌手认证数量
     */
    private int seniorSingerAuthCnt;

    /**
     * 歌手认证数量
     */
    private int singerAuthCnt;

    /**
     * 营收(钻石)
     */
    private long income;

    /**
     * 厅主信息
     */
    private RoomDetailUserInfoBean njInfo;
}
