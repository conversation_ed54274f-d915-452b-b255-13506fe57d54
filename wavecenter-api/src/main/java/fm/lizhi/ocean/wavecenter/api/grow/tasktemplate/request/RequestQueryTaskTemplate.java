package fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
 * 查询任务模版请求
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Data
public class RequestQueryTaskTemplate {

    /**
     * 页码
     */
    @Min(value = 1, message = "页码必须大于等于1")
    @NotNull(message = "页码不能为空")
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    @Min(value = 1, message = "页大小必须大于等于1")
    @Max(value = 100, message = "页大小不能超过100")
    @NotNull(message = "页大小不能为空")
    private Integer pageSize = 10;

    /**
     * 能力项code
     */
    private String capabilityCode;

    /**
     * 业务ID
     */
    @AppEnumId(message = "业务ID不能为空")
    private Integer appId;
} 