package fm.lizhi.ocean.wavecenter.api.common.constants;


import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum IncomeTypeEnum {

    SING_ROOM_GIFT("2","送礼\\收礼","签约厅收礼"),
    PGC_SING_ROOM_GIFT("505","PGC房非签约送礼","签约厅收礼"),
    PERSON_GIFT("528","个播送礼","个播送礼"),
    VIP_ROOM_BUG("376","厅贵族购买","签约厅贵族提成"),
    VIP_ROOM_RENEW("377","厅贵族续费","签约厅贵族提成"),
    PERSON_VIP_BUG("529","个播贵族购买","个播贵族提成"),
    PERSON_VIP_RENEW("530","个播贵族续费","个播贵族提成"),
    OFFICIAL_GIFT("201008","官厅送礼","官方厅送礼"),
    ;


    private String bizId;

    private String bizName;

    private String waveName;

    IncomeTypeEnum(String bizId, String bizName, String waveName) {
        this.bizId = bizId;
        this.bizName = bizName;
        this.waveName = waveName;
    }

    static Map<String,String> incomeTypeMap = new HashMap<>();

    static {
        for (IncomeTypeEnum value : IncomeTypeEnum.values()) {
            incomeTypeMap.put(value.bizId,value.waveName);
        }
    }


    /**
     * 获取名称
     *
     * @param bizId
     * @return
     */
    public static String getName(Integer bizId){
        return incomeTypeMap.getOrDefault(String.valueOf(bizId),String.valueOf(bizId));
    }


}
