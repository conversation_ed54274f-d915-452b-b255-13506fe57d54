package fm.lizhi.ocean.wavecenter.api.anchor.singer.constants;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 平台歌手等级定义
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum SingerTypeEnum {

    /**
     * 新锐歌手
     */
    NEW(1, "新锐歌手", false),

    /**
     * 优质歌手
     */
    QUALITY(2, "优质歌手", true),

    /**
     * 明星歌手
     */
    STAR(3, "明星歌手", true),
    ;

    private final int type;

    private final String name;

    /**
     * 是否是高级歌手
     */
    private final boolean senior;



    public static SingerTypeEnum getByType(int type) {
        for (SingerTypeEnum value : values()) {
            if (value.getType() == type) {
                return value;
            }
        }
        return null;
    }

    /**
     * 校验歌手类型是否是合法值
     *
     * @param type 歌手类型
     * @return 是否合法
     */
    public static boolean isValid(int type) {
        return getByType(type) != null;
    }

    /**
     * 根据类型ID获取出下一个类型，返回值：SingerTypeEnum
     *
     * @param type 类型ID
     * @return 下一个类型
     */
    public static SingerTypeEnum getNextType(Integer type) {
        if (type == null || type < 1) {
            return NEW;
        }
        if (type == STAR.getType()) {
            return null;
        }
        return getByType(type + 1);
    }

    /**
     * 根据类型ID获取出前一个类型，返回值：SingerTypeEnum
     *
     * @param type 类型ID
     * @return 前一个类型
     */
    public static int getPreType(Integer type) {
        if (type == null || type < 1) {
            return 0;
        }
        if (type == STAR.getType()) {
            return NEW.getType();
        }
        SingerTypeEnum byType = getByType(type - 1);
        return byType == null ? 0 : byType.getType();
    }


    /**
     * 判断是否是首个等级
     *
     * @param type 类型
     * @return 结果
     */
    public static boolean isPrimaryType(int type) {
        return type == SingerTypeEnum.NEW.type;
    }


    /**
     * 获取比当前歌手类型小的歌手类型
     *
     * @param currentSingerType 当前歌手类型
     * @return 结果列表
     */
    public static List<Integer> getPriorSingerTypes(int currentSingerType) {
        SingerTypeEnum typeEnum = getByType(currentSingerType);
        if (typeEnum == null || !typeEnum.isSenior()) {
            return new ArrayList<>();
        }
        List<Integer> list = Lists.newArrayList();
        for (SingerTypeEnum value : values()) {
            if (value.getType() < currentSingerType) {
                list.add(value.getType());
            }
        }
        return list;
    }
}
