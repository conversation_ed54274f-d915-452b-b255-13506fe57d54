package fm.lizhi.ocean.wavecenter.api.sign.request;

import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RequestQueryContract implements IContextRequest {

    /**
     * 合同 ID， 传其中一个就可以
     */
    private Long contractId;

    /**
     * 签约 ID, 传其中一个就可以
     */
    private String signId;

    /**
     * 应用ID
     */
    private int appId;

    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}
