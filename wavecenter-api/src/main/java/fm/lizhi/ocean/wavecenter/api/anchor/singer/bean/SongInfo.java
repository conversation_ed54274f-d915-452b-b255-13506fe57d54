package fm.lizhi.ocean.wavecenter.api.anchor.singer.bean;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * @description: 歌曲信息
 * @author: guoyibin
 * @create: 2025/07/21 17:08
 */
@Data
public class SongInfo {

    /**
     * 歌曲名称
     */
    @NotNull(message = "歌曲名称不能为空")
    @Size(min = 1, max = 50, message = "歌曲名称不能超过50个字符")
    private String songName;

    /**
     * 音频文件地址
     */
    @NotNull(message = "认证音频路径不能为空")
    @Size(min = 1, message = "认证音频路径不能是空字符")
    private String audioPath;

    /**
     * 歌曲风格
     */
    @NotNull(message = "歌曲风格不能为空")
    @Size(min = 1, message = "歌曲风格不能是空字符")
    private String songStyle;

    /**
     * 预审核状态
     * 1：通过
     * 2：SDK无法处理
     * 3：SDK判定假唱
     */
    private Integer preAuditStatus;
}
