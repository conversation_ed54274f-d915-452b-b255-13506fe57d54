package fm.lizhi.ocean.wavecenter.api.anchor.singer.bean;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 *
 * 点唱厅申请记录
 *
 * <AUTHOR>
 * @date 2025-03-27 03:27:23
 */
@Data
@Accessors(chain = true)
public class SingerSingHallApplyRecordBean {
    /**
     * ID
     */
    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 家族ID
     */
    private Long familyId;

    /**
     * 厅主ID
     */
    private Long njId;

    /**
     * 审核状态，0-审核中，1-审核通过，2-审核未通过
     */
    private Integer auditStatus;

    /**
     * 提交时间
     */
    private Long applyTime;

    /**
     * 添加时间
     */
    private Long createTime;

    /**
     * 操作人
     */
    private String operator;
}