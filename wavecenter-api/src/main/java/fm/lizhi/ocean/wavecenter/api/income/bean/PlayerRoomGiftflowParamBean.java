package fm.lizhi.ocean.wavecenter.api.income.bean;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/4/25 10:12
 */
@Getter
@Builder
public class PlayerRoomGiftflowParamBean {

    private Integer appId;

    private Long userId;

    private Date startDate;

    private Date endDate;

    private Integer pageNo;

    private Integer pageSize;

    private String recUserBand;

    private String sendUserBand;

    public static class PlayerRoomGiftflowParamBeanBuilder{
        public PlayerRoomGiftflowParamBean build(){
            ApiAssert.notNull(appId, "appId is required");
            ApiAssert.notNull(userId, "userId is required");
            ApiAssert.notNull(startDate, "startDate is required");
            ApiAssert.notNull(endDate, "endDate is required");
            if (pageNo == null || pageNo <= 0) {
                pageNo = 1;
            }
            if (pageSize == null || pageSize <= 0) {
                pageSize = 20;
            }
            return new PlayerRoomGiftflowParamBean(appId
                    , userId
                    , startDate
                    , endDate
                    , pageNo
                    , pageSize
                    , recUserBand
                    , sendUserBand);
        }
    }

}
