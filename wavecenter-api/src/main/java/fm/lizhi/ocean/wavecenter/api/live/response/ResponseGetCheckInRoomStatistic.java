package fm.lizhi.ocean.wavecenter.api.live.response;

import fm.lizhi.ocean.wavecenter.api.live.bean.WaveCheckInRoomStatisticBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.WaveCheckInUserBean;
import lombok.Data;

import java.util.List;

/**
 * 获取麦序福利厅明细统计的响应
 */
@Data
public class ResponseGetCheckInRoomStatistic {

    /**
     * 麦序福利厅明细统计列表
     */
    private List<WaveCheckInRoomStatisticBean> list;

    /**
     * 主持人信息 小时 才有值
     */
    private WaveCheckInUserBean host;
}
