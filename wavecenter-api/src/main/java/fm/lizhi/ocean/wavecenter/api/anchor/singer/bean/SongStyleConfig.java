package fm.lizhi.ocean.wavecenter.api.anchor.singer.bean;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @description: 曲风数量
 * @author: guoyibin
 * @create: 2025/07/25 16:02
 */
@Data
@Accessors(chain = true)
public class SongStyleConfig {

    /**
     * 最大曲风数量
     */
    private Integer maxStyleNum;

    /**
     * 歌手类型
     */
    private Integer singerType;

    /**
     * 是否支持多曲风
     */
    private Boolean supportMultiStyle;
}
