package fm.lizhi.ocean.wavecenter.api.home.response;


import fm.lizhi.ocean.wavecenter.api.home.bean.MetricsDataBean;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 公会关键数据汇总
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ResponseGuildKeyDataSummary {


    /**
     * 公会收入汇总
     */
    private MetricsDataBean sumIncome;

    /**
     * 上麦主播数
     */
    private MetricsDataBean signUpGuestPlayerCnt;

    /**
     * 有收入主播数
     */
    private MetricsDataBean incomePlayerCnt;
}
