package fm.lizhi.ocean.wavecenter.api.resource.recommendcard.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.SendRecommendCardBean;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/22 15:47
 */
@Data
@Accessors(chain = true)
public class RequestBatchSend implements IContextRequest {

    @NotNull(message = "appId is null")
    @AppEnumId
    private Integer appId;

    /**
     * 操作人
     */
    private String operator;

    @NotEmpty(message = "sendRecommendCards is empty")
    private List<SendRecommendCardBean> sendRecommendCards;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
