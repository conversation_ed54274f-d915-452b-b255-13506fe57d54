package fm.lizhi.ocean.wavecenter.api.live.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/6/25 11:06
 */
@Data
@Accessors(chain = true)
public class TimeStatsBean {

    private Date time;

    private BigDecimal income;

    private Integer charm;

    private Integer seatOrder;

    private Integer hostCnt;

    private Integer checkPlayerNumber;


}
