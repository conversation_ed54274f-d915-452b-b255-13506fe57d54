package fm.lizhi.ocean.wavecenter.api.datacenter.bean;

import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;

import java.util.List;

@Getter
@Builder
public class RankGetRoomParamBean {

    private Integer appId;

    private Long familyId;

    private OrderType rankType;

    /**
     * 查询日期，可以为空，缺省默认查询当天的数据
     * 格式：yyyy-MM-dd
     */
    private String date;

    /**
     * 厅数据范围
     * @since 1.4.0
     */
    private List<Long> roomIds;

    public static class RankGetRoomParamBeanBuilder{
        public RankGetRoomParamBean build(){
            ApiAssert.notNull(rankType, "rankType is required");
            ApiAssert.notNull(appId, "appId is required");
            ApiAssert.notNull(familyId, "familyId is required");
            return new RankGetRoomParamBean(appId, familyId, rankType, date, roomIds);
        }
    }

}
