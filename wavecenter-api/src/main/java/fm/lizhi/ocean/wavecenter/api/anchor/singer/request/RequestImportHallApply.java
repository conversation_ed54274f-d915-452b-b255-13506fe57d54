package fm.lizhi.ocean.wavecenter.api.anchor.singer.request;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerHallApplySourceEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerHallApplyStatusEnum;
import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 点唱厅管理-导入申请名单
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RequestImportHallApply implements IContextRequest {

    /**
     * 应用ID
     */
    @NotNull(message = "应用ID不能为空")
    @AppEnumId
    private int appId;

    /**
     * 厅ID 列表
     */
    @NotNull(message = "厅ID列表不能为空")
    private List<Long> njIds;

    /**
     * 审核状态
     */
    @NotNull(message = "审核状态不能为空")
    private SingerHallApplyStatusEnum status;

    /**
     * 操作人
     */
    @NotNull(message = "操作人不能为空")
    private String operator;

    /**
     * 来源
     */
    private SingerHallApplySourceEnum source;

    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}
