package fm.lizhi.ocean.wavecenter.api.activitycenter.bean;

import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateFlowResourceBean;
import lombok.Data;

import java.util.List;

/**
 * 分页查询通用活动模板的结果
 */
@Data
public class ActivityTemplateGeneralPageBean {

    /**
     * 活动模板id
     */
    private Long id;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 大类id
     */
    private Long bigClassId;

    /**
     * 分类id
     */
    private Long classId;

    /**
     * 封面地址
     */
    private String cover;

    /**
     * 权重
     */
    private Integer weight;

    private List<ActivityTemplateFlowResourceBean> flowResources;

    /**
     * 活动模板亮点标签列表
     */
    private List<ActivityTemplateGeneralHighlightBean> highlights;
}
