package fm.lizhi.ocean.wavecenter.api.background.activitycenter.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 新建活动大类请求
 *
 * <AUTHOR>
 * @date 2024-10-10 06:34:31
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class RequestSaveActivityBigClass {

    /**
     * 活动大类名称
     */
    @NotNull(message = "活动大类名称不能为空")
    @Size(min = 1, max = 60, message = "活动大类名称长度必须在{min}-{max}之间")
    private String name;

    /**
     * 应用ID
     */
    @NotNull(message = "应用id不能为空")
    @AppEnumId
    private Integer appId;

    /**
     * 权重
     */
    @NotNull(message = "权重不能为空")
    @Min(value = 1, message = "权重必须大于等于1")
    private Integer weight;

    /**
     * 分类类型，1：常规，2：厅战
     */
    @NotNull(message = "分类类型不能为空")
    private Integer type;

    /**
     * 操作人
     */
    @NotNull
    private String operator;

    /**
     * 品类列表
     */
    private List<Integer> categoryList;
}