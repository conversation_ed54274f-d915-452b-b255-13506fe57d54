package fm.lizhi.ocean.wavecenter.api.activitycenter.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * 活动分类
 *
 * <AUTHOR>
 * @date 2024-10-10 06:34:31
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ActivityClassConfigBean {

    /**
     * 分类ID
     */
    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 大类ID
     */
    private Long bigClassId;

    /**
     * 类型名称
     */
    private String name;

    /**
     * 等级ID
     */
    private Long levelId;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 修改时间
     */
    private Long modifyTime;

    /**
     * 操作人
     */
    private String operator;
}