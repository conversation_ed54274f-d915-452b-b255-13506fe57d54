package fm.lizhi.ocean.wavecenter.api.datacenter.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.*;
import fm.lizhi.ocean.wavecenter.api.datacenter.request.RequestGuildGetAssessmentInfo;
import fm.lizhi.ocean.wavecenter.api.datacenter.request.RequestGuildGetIndicatorTrend;
import fm.lizhi.ocean.wavecenter.api.datacenter.request.RequestGuildRoomPerformance;

import javax.validation.Valid;
import java.util.List;

/**
 * 公会数据
 * <AUTHOR>
 * @date 2024/4/17 17:03
 */
public interface GuildDataService {

    /**
     * 公会考核业绩-查询
     * 新接口使用 getAssessmentInfoV2
     * @param appId
     * @param familyId
     * @return
     */
    @Deprecated
    Result<GuildAssessmentInfoBean> getAssessmentInfo(int appId, long familyId);

    /**
     * 公会数据-公会考核业绩
     * @param request
     * @return
     */
    Result<GuildAssessmentInfoBean> getAssessmentInfoV2(@Valid RequestGuildGetAssessmentInfo request);

    /**
     * 查询关键指标
     * @param paramBean
     * @return
     */
    Result<List<IndicatorBean>> getKeyIndicators(GuildGetKeyIndicatorsParamBean paramBean);

    /**
     * 签约厅业绩
     * @param appId
     * @param familyId
     * @return
     */
    @Deprecated
    Result<GuildRoomPerformanceResBean> roomPerformance(int appId, long familyId);

    /**
     * 签约厅业绩
     * @param request
     * @return
     */
    Result<GuildRoomPerformanceResBean> roomPerformanceV2(@Valid RequestGuildRoomPerformance request);

    /**
     * 查询趋势图数据
     * @param appId
     * @param familyId
     * @param metric
     * @return
     */
    @Deprecated
    Result<IndicatorTrendResBean> getIndicatorTrend(int appId, long familyId, String metric);

    /**
     * 查询趋势图数据
     * @param request
     * @return
     */
    Result<IndicatorTrendResBean> getIndicatorTrendV2(@Valid RequestGuildGetIndicatorTrend request);

    int ASSESSMENT_INFO_NOT_FOUND = 1;

}
