package fm.lizhi.ocean.wavecenter.api.activitycenter.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.*;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestGetInTimeRangeActivityApply;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseActivityInfoDetail;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 活动提报接口服务
 * 202000XX 的错误码不符合规范
 * 解决方案：新增加的错误码按照DC的标准定义，7位，方法位从01开始，舍弃00。
 * 比如：activityApply方法新增了ACTIVITY_APPLY_WHITELIST_ERROR，异常码定义为：2020101
 * queryActivityList方法新增了QUERY_ACTIVITY_LIST_FAIL，异常码定义为：2020201
 */
public interface ActivityApplyService {

    /**
     * 活动申请
     *
     * @param paramBean 参数
     * @return 结果
     */
    Result<Void> activityApply(@NotNull @Valid RequestActivityApplyBean paramBean);

    /**
     * 修改活动申请
     *
     * @param paramBean 参数
     * @return 结果
     */
    Result<Void> activityModify(@NotNull @Valid RequestActivityApplyBean paramBean);

    /**
     * 查询活动日历列表数据
     *
     * @param param 查询参数
     * @return 结果
     */
    Result<ResponseQueryActivityListBean> queryActivityList(RequestQueryActivityListBean param);

    /**
     * 删除活动
     */
    Result<Void> deleteActivityApply(Long activityId, int appId);


    /**
     * 活动详情
     */
    Result<ResponseActivityInfoDetail> queryActivityInfoDetail(Long activityId, int appId);


    /**
     * 获取资源审批结果
     */
    Result<List<ActivityFlowResourceDetailBean>> getFlowResourceAuditResult(Long activityId, int appId);


    /**
     * 获取活动申请详情, 用于APP端查看
     *
     * @param request 请求参数
     * @return 结果
     */
    Result<ResponseGetActivityApplyDetailForApp> getActivityApplyDetailForApp(RequestGetActivityApplyDetailForApp request);


    /**
     * 获取活动申请信息 - 简单信息
     *
     * @param request 请求参数
     * @return 结果
     */
    Result<ResponseGetActivityApplyInfo> getActivityApplyInfo(RequestGetActivityApplyInfo request);

    /**
     * 查询在时间范围内的活动申请
     * @return
     */
    Result<List<ActivityApplyToolBean>> getInTimeRangeActivityApply(@Valid RequestGetInTimeRangeActivityApply request);

    /**
     * 活动申请参数异常
     */
    int ACTIVITY_APPLY_PARAM_ERROR = 20200001;

    /**
     * 相同时间段活动已存在
     */
    int ACTIVITY_APPLY_TIME_RANGE_EXIST_ACTIVITY = 20200002;

    /**
     * 请求频繁，请稍候重试
     */
    int ACTIVITY_APPLY_REQ_TOO_FAST = 20200003;

    /**
     * 本周单厅申请数量已超过限制
     */
    int ACTIVITY_APPLY_OVER_COUNT_LIMIT = 20200004;

    /**
     * 活动申请失败
     */
    int ACTIVITY_APPLY_FAIL = 20200005;

    /**
     * 活动申请数据审核未通过
     */
    int ACTIVITY_APPLY_DATA_AUDIT_NO_PASS = 20200006;

    /**
     * 服务器异常，请稍候重试
     */
    int ACTIVITY_APPLY_ERROR = 20200007;

    /**
     * 模板白名单校验错误
     */
    int ACTIVITY_APPLY_WHITELIST_ERROR = 2020101;

    /**
     * 模板状态异常
     */
    int ACTIVITY_APPLY_STATUS_ERROR = 2020102;

    /**
     * 品类异常
     */
    int ACTIVITY_APPLY_CATEGORY_ERROR = 2020103;

    /**
     * 查询活动列表参数异常
     */
    int QUERY_ACTIVITY_LIST_PARAM_ERROR = 20200008;

    /**
     * 拒绝活动申请参数异常
     */
    int REJECT_ACTIVITY_APPLY_PARAM_ERROR = 20200009;

    /**
     * 拒绝操作失败
     */
    int REJECT_ACTIVITY_APPLY_FAIL = 20200010;

    /**
     * 活动不存在
     */
    int QUERY_ACTIVITY_INFO_DETAIL_PARAM_ERROR = 20200011;

    /**
     * 查询活动列表参数异常
     */
    int QUERY_USER_ACTIVITY_LIST_PARAM_ERROR = 20200012;

    /**
     * 查询活动列表参数异常
     */
    int QUERY_USER_ACTIVITY_PARAM_BAND_NOT_EXIT = 20200015;

    /**
     * 删除活动参数异常
     */
    int DELETE_ACTIVITY_APPLY_PARAM_ERROR = 20200013;

    /**
     * 删除活动失败
     */
    int DELETE_ACTIVITY_APPLY_FAIL = 20200014;

    /**
     * 同意活动异常
     */
    int AGREE_ACTIVITY_APPLY_FAIL = 20200016;

    /**
     * 同意活动申请参数异常
     */
    int AGREE_ACTIVITY_APPLY_PARAM_ERROR = 20200017;

    /**
     * 查询资源审核结果失败
     */
    int GET_FLOW_RESOURCE_AUDIT_RESULT_FAIL = 20200018;

    /**
     * 获取app端查看的活动申请详情参数异常
     */
    int GET_ACTIVITY_APPLY_PARAM_ERROR = 20200011;

    /**
     * 获取app端查看的活动申请详情失败
     */
    int GET_ACTIVITY_APPLY_TIME_RANGE_ERROR = 20200012;

    /**
     * 活动修改失败
     */
    int ACTIVITY_MODIFY_FAIL = 20200013;

    /**
     * 活动修改参数异常
     */
    int ACTIVITY_MODIFY_PARAM_ERROR = 20200014;

    /**
     * 活动修改请求频繁，请稍候重试
     */
    int ACTIVITY_MODIFY_REQ_TOO_FAST = 20200015;

    /**
     * 活动不存在
     */
    int ACTIVITY_MODIFY_ACTIVITY_NOT_EXIST = 20200016;

    /**
     * 非待审核状态不允许修改
     */
    int ACTIVITY_MODIFY_ACTIVITY_NOT_AUDIT = 20200017;

    /**
     * 官方提报，活动开始前N分钟不允许修改
     */
    int ACTIVITY_NOT_MODIFY_ACTIVITY_BEFORE_START = 20200018;

    /**
     * 活动修改失败
     */
    int ACTIVITY_MODIFY_ERROR = 20200018;

    /**
     * 版本号不一致
     */
    int ACTIVITY_VERSION_NOT_MATCH = 20200019;



    /**
     * 获取查看的活动申请简单参数异常
     */
    int GET_ACTIVITY_APPLY_INFO_PARAM_ERROR = 20200013;

    /**
     * 活动取消不允许修改
     */
    int ACTIVITY_MODIFY_ACTIVITY_CANCEL = 20200019;

    /**
     * 活动申请模板时长超过限制
     */
    int ACTIVITY_APPLY_TEMPLATE_DURATION_LIMIT = 20200020;

    /**
     * 活动修改模板时长超过限制
     */
    int ACTIVITY_MODIFY_TEMPLATE_DURATION_LIMIT = 20200021;

}
