package fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 活动模板亮点标签
 */
@Data
public class ActivityTemplateHighlightBean {

    /**
     * 亮点标签key
     */
    @NotNull(message = "亮点标签key不能为空")
    @Size(min = 1, max = 100, message = "亮点标签key长度必须在{min}-{max}之间")
    private String highlightKey;

    /**
     * 亮点标签value
     */
    @NotNull(message = "亮点标签value不能为空")
    @Size(min = 1, max = 100, message = "亮点标签value长度必须在{min}-{max}之间")
    private String highlightValue;

    /**
     * 亮点标签类型，0：亮点标签，1：奖励标签
     */
    private Integer type;
}
