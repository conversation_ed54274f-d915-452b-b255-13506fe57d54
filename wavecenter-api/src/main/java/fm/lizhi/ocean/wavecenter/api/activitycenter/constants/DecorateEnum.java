package fm.lizhi.ocean.wavecenter.api.activitycenter.constants;

import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import lombok.Getter;

/**
 * 装扮枚举
 *
 * <AUTHOR>
 *
 * 要求下面枚举的值保持一致
 * @see fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum
 */
@Getter
public enum DecorateEnum implements TypeNameProvider {

    /**
     * 头像框
     */
    AVATAR(PlatformDecorateTypeEnum.AVATAR.getType(), "头像框"),

    /**
     * 背景
     */
    BACKGROUND(PlatformDecorateTypeEnum.BACKGROUND.getType(), "房间背景"),

    /**
     * 勋章
     */
    MEDAL(PlatformDecorateTypeEnum.MEDAL.getType(), "勋章"),

    /**
     * 官方认证
     */
    USER_GLORY(PlatformDecorateTypeEnum.USER_GLORY.getType(), "官方认证"),

    /**
     * 气泡 历史原因 维护两份
     */
    BUBBLE(PlatformDecorateTypeEnum.BUBBLE.getType(), "气泡")
    ;


    private final Integer type;

    private final String name;

    DecorateEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }

    public static String getDecorateName(Integer type) {
        for (DecorateEnum decorateEnum : DecorateEnum.values()) {
            if (decorateEnum.getType().equals(type)) {
                return decorateEnum.getName();
            }
        }
        return "";
    }

}
