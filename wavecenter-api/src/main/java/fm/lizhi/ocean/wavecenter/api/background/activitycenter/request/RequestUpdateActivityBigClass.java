package fm.lizhi.ocean.wavecenter.api.background.activitycenter.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;

import java.util.List;

/**
 * 更新活动大类请求
 *
 * <AUTHOR>
 * @date 2024-10-10 06:34:31
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class RequestUpdateActivityBigClass {

    /**
     * 活动大类ID
     */
    @NotNull(message = "活动大类id不能为空")
    private Long id;

    /**
     * 活动大类名称
     */
    @NotNull(message = "活动大类名称不能为空")
    @Size(min = 1, max = 60, message = "活动大类名称长度必须在{min}~{max}之间")
    private String name;

    /**
     * 权重
     */
    @NotNull(message = "权重不能为空")
    @Min(value = 1, message = "权重必须大于等于1")
    private Integer weight;

    /**
     * 操作人
     */
    @NotNull(message = "操作人不能为空")
    private String operator;

    /**
     * 应用ID
     */
    @AppEnumId
    private Integer appId;

    /**
     * 品类列表
     */
    private List<Integer> categoryList;
}
