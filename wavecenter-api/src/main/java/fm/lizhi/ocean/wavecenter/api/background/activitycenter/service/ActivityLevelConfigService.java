package fm.lizhi.ocean.wavecenter.api.background.activitycenter.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityLevelConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityLevel;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityLevel;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ActivityLevelConfigService {


    /**
     * 保存
     * <p>
     * 额外关注状态码: {@link #HAS_LEVEL_REPEAT}
     */
    Result<Boolean> saveLevel(RequestSaveActivityLevel param);


    /**
     * 更新
     * <p>
     * 额外关注状态码: {@link #HAS_LEVEL_REPEAT}
     */
    Result<Boolean> updateLevel(RequestUpdateActivityLevel param);

    /**
     * 删除
     */
    Result<Boolean> deleteLevel(Long id, Integer appId, String operator);


    /**
     * 查询列表
     */
    Result<List<ActivityLevelConfigBean>> listByAppId(Integer appId);

    /**
     * 根据分类 ID 查询等级
     */
    Result<ActivityLevelConfigBean> getLevelByClassId(Long classId, Integer appId);

    /**
     * 存在重复记录
     */
    int HAS_LEVEL_REPEAT = 2100001;

    /**
     * 保存活动等级失败
     */
    int SAVE_LEVEL_FAIL = 2100101;

    /**
     * 更新活动等级失败
     */
    int UPDATE_LEVEL_FAIL = 2100201;

    /**
     * 删除活动等级失败
     */
    int DELETE_LEVEL_FAIL = 2100301;

}
