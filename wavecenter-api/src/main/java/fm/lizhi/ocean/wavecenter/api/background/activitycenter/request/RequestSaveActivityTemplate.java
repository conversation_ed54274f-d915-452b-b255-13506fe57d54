package fm.lizhi.ocean.wavecenter.api.background.activitycenter.request;

import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateFlowResourceBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateProcessBean;
import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * 创建或更新活动模板请求基础类
 */
@Data
abstract class RequestSaveActivityTemplate {

    /**
     * 应用id
     */
    @NotNull(message = "应用id不能为空")
    @AppEnumId
    private Integer appId;

    /**
     * 模板名称
     */
    @NotBlank(message = "模板名称不能为空")
    @Size(min = 1, max = 20, message = "模板名称长度必须在{min}-{max}之间")
    private String name;

    /**
     * 分类id
     */
    @NotNull(message = "分类id不能为空")
    private Long classId;

    /**
     * 活动目标
     */
    @NotBlank(message = "活动目标不能为空")
    @Size(min = 1, max = 100, message = "活动目标长度必须在{min}-{max}之间")
    private String goal;

    /**
     * 活动介绍
     */
    @NotBlank(message = "活动介绍不能为空")
    private String introduction;

    /**
     * 活动流程列表
     */
    @Valid
    private List<ActivityTemplateProcessBean> processes;

    /**
     * 辅助道具图片列表
     */
    @Size(max = 5, message = "辅助道具图片数量不能超过{max}")
    @Valid
    private List<String> auxiliaryPropUrls;

    /**
     * 活动海报
     */
    private String posterUrl;

    /**
     * 玩法工具列表
     */
    @Valid
    private List<Integer> activityTools;

    /**
     * 房间公告
     */
    @Size(max = 150, message = "房间公告长度不能超过{max}")
    private String roomAnnouncement;

    /**
     * 房间公告图片列表
     */
    @Valid
    @Size(max = 2, message = "房间公告图片数量不能超过{max}")
    private List<String> roomAnnouncementImages;

    /**
     * 房间背景id列表
     */
    @Size(max = 20, message = "房间背景数量不能超过{max}")
    @Valid
    private List<Long> roomBackgroundIds;

    /**
     * 房间背景可选数量限制 -1表示没有限制
     * @since 1.4.6
     */
    private Integer roomBackgroundLimit = 1;

    /**
     * 房间头像框id列表
     */
    @Size(max = 20, message = "房间头像框数量不能超过{max}")
    @Valid
    private List<Long> avatarWidgetIds;

    /**
     * 头像框可选数量限制 -1表示没有限制
     * @since 1.4.6
     */
    private Integer avatarWidgetLimit = 1;

    /**
     * 流量资源列表
     */
    @Valid
    private List<ActivityTemplateFlowResourceBean> flowResources;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;

    /**
     * 活动限时
     */
    private Integer activityDurationLimit;

    /**
     * 活动开始时间限制
     */
    private Long activityStartTimeLimit;

    /**
     * 活动结束时间限制
     */
    private Long activityEndTimeLimit;
}
