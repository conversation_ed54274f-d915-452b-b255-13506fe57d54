package fm.lizhi.ocean.wavecenter.api.anchor.singer.response;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerTypeInfo;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ResponseGetSingerStatus {

    /**
     * 歌手状态
     */
    private Integer status;

    /**
     * 歌手状态描述文案
     */
    private String msg;

    /**
     * 下一个等级歌手类型
     */
    private SingerTypeInfo nextSingerType;

    /**
     * 当前歌手类型
     */
    private SingerTypeInfo currentSingerType;
}
