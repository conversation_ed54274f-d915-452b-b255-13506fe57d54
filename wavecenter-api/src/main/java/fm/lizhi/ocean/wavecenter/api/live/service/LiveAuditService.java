package fm.lizhi.ocean.wavecenter.api.live.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.*;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;

public interface LiveAuditService {

    /**
     * 获取工会的签约厅下面的 违规陪玩人员信息
     *
     * @param page
     * @param pageSize
     * @return
     */
    Result<PageBean<UserBean>> signRoomPushPlayer(RoomPushParamBean paramBean, int page, int pageSize);

    /**
     * 公会的审核记录统计
     *
     * @return
     */
    Result<PageBean<GuildAuditRecordStatsBean>> guildAuditRecordStats(GuildAuditStatsParamBean paramBean, int page, int pageSize);


    /**
     * 厅的审核记录统计
     *
     * @return
     */
    Result<RoomAuditRecordStatsBean> roomAuditRecordStats(RoomAuditStatsParamBean paramBean);

    /**
     * 主播 搜索 审核记录详情
     *
     * @param paramBean
     * @param page
     * @param pageSize
     * @return
     */
    Result<PageBean<PlayerAuditRecordBean>> playerAuditRecordDetail(PlayerAuditRecordSearchParamBean paramBean, int page, int pageSize);

    /**
     * 公会搜索 审核记录详情
     *
     * @param paramBean
     * @param page
     * @param pageSize
     * @return
     */
    Result<PageBean<GuildAuditRecordBean>> guildAuditRecordDetail(GuildAuditRecordSearchParamBean paramBean,int page,int pageSize);

    /**
     * 厅搜索 审核记录详情
     *
     * @param paramBean
     * @param page
     * @param pageSize
     * @return
     */
    Result<PageBean<RoomAuditRecordBean>> roomAuditRecordDetail(RoomAuditRecordSearchParamBean paramBean,int page,int pageSize);

    /**
     * 添加审核记录
     *
     * @param recordBean
     * @return
     */
    Result<Void> addAuditRecord(AddAuditRecordBean recordBean);

    /**
     * 保存审核记录完整信息
     * @return
     */
    Result<Void> addAuditRecordFullInfo(AddAuditRecordFullParamBean recordBean);


    // 新增审核记录失败
    int ADD_AUDIT_RECORD_ERROR = 1;

    // 审核记录已存在
    int AUDIT_RECORD_EXIST = 2;


}
