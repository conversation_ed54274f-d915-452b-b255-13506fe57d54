package fm.lizhi.ocean.wavecenter.api.resource.decorate.request;

import javax.validation.constraints.NotNull;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;
@Data
@Accessors(chain = true)
public class RequestGetDecorates implements IContextRequest{

    @NotNull(message = "appId is null")
    @AppEnumId
    private Integer appId;

    /**
     * 装扮类型
     */
    @NotNull(message = "decorateType is null")
    private Integer decorateType;

    /**
     * 装扮名称
     */
    private String decorateName;

    /**
     * 页码
     */
    private Integer pageNo = 1;

    /**
     * 每页数量
     */
    private Integer pageSize = 20;

    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}