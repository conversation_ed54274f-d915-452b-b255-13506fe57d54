package fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.pp;

import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestCreateAvatarWidget;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * PP创建头像框Bean
 *
 * @see RequestCreateAvatarWidget
 */
@Data
public class PpCreateAvatarWidgetBean {

    /**
     * 名称
     */
    @NotNull(message = "名称不能为空")
    @Size(min = 1, max = 64, message = "名称长度必须在{min}到${max}个字符之间")
    private String name;

    /**
     * 静态图(实际使用的大图)
     */
    @NotNull(message = "静态图不能为空")
    private String thumbUrl;

    /**
     * 素材, SVGA或PAG资源
     */
    private String materialUrl;

    /**
     * 有效期分钟数
     */
    @NotNull(message = "有效期不能为空")
    @Min(value = 1, message = "有效期最小值为{value}分钟")
    private Integer validMin;
}
