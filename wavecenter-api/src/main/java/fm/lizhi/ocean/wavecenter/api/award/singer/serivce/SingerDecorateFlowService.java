package fm.lizhi.ocean.wavecenter.api.award.singer.serivce;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestPageSingerDecorateFlow;
import fm.lizhi.ocean.wavecenter.api.award.singer.response.ResponseSingerDecorateFlow;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;

/**
 * 歌手装扮发放流水, 接口错误码以 239 开头.
 * <AUTHOR>
 */
public interface SingerDecorateFlowService {


    /**
     * 分页查询歌手装扮流水
     */
    Result<PageBean<ResponseSingerDecorateFlow>> pageSingerDecorateFlow(RequestPageSingerDecorateFlow request);


}
