package fm.lizhi.ocean.wavecenter.api.anchor.singer.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 歌手私信场景定义
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum SingerChatSceneEnum {

    /**
     * 预审核不通过（签约关系）：
     */
    PRE_AUDIT_NOT_PASS_BY_SIGN("preAuditNotPassBySign", "预审核不通过【签约关系】"),

    /**
     * 预审核不过过（音频不过）：
     */
    PRE_AUDIT_NOT_PASS_BY_AUDIO("preAuditNotPassByAudio", "预审核不通过【音频】"),

    /**
     * 歌手认证审核通过
     */
    SINGER_AUDIT_PASS("singerAuditPass", "认证审核通过"),

    /**
     * 主播通过，厅审核中
     */
    HOST_PASS_HALL_AUDITING("hostPassHallAuditing", "主播通过，厅审核中"),

    /**
     * 主播通过，厅未通过
     */
    HOST_PASS_AUDIT_HALL_NOT_PASS("hostPassAuditHallNotPass", "主播通过，厅未通过"),

    /**
     * 人审不通过
     */
    HUMAN_AUDIT_NOT_PASS("humanAuditNotPass", "人审不通过"),

    /**
     * 手动淘汰歌手
     */
    MANUAL_DISQUALIFICATION("manualDisqualification", "手动淘汰歌手"),

    /**
     * 选中操作
     */
    SELECTED("selected", "选中操作"),

    /**
     * 选中后不通过
     */
    SELECTED_THAN_NO_PASS("selectedThanNoPass", "选中操作后不通过"),

    /**
     * 自动淘汰歌手
     */
    AUTO_DISQUALIFICATION("autoDisqualification", "自动淘汰歌手");

    /**
     * 场景编码
     */
    private final String sceneCode;

    /**
     * 场景名称
     */
    private final String name;

}
