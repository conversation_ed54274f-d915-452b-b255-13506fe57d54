package fm.lizhi.ocean.wavecenter.service.live.handler;

import cn.hutool.crypto.SignUtil;
import com.ctrip.framework.apollo.core.enums.Env;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.live.constants.CheckInDateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.service.common.constants.WaveCenterChatQueueEnum;
import fm.lizhi.ocean.wavecenter.service.common.manager.ChatManager;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.GetHasIncomeRoomsParam;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.RoomDataManager;
import fm.lizhi.ocean.wavecenter.service.live.config.LiveConfig;
import fm.lizhi.ocean.wavecenter.service.live.manager.CheckInRedisManager;
import fm.lizhi.ocean.wavecenter.service.live.manager.WaveCheckInDataManager;
import fm.lizhi.ocean.wavecenter.service.permissions.manager.RoleManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CheckInNotifyHandler {

    @Autowired
    private RoomDataManager roomDataManager;
    @Autowired
    private ChatManager chatManager;
    @Autowired
    private RoleManager roleManager;
    @Autowired
    private WaveCheckInDataManager waveCheckInDataManager;
    @Autowired
    private LiveConfig liveConfig;
    @Autowired
    private List<CheckInRoomPeriodStatHandler> checkInRoomPeriodStatHandlers;
    @Autowired
    private CheckInRedisManager checkInRedisManager;


    private static final List<CheckInDateTypeEnum> SUPPORT_DATE_TYPES =
            Arrays.asList(CheckInDateTypeEnum.HOUR, CheckInDateTypeEnum.DAY, CheckInDateTypeEnum.WEEK);

    /**
     * 执行通知
     * @param appId
     */
    public void executeNotify(Date triggerTime, Integer appId){
        log.info("start executeNotify;appId={};triggerTime={}", appId, triggerTime.getTime());
        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.from(appId));
        //有无发送者
        long sender = liveConfig.getBizConfig().getCheckInReportSender();
        if (sender <= 0) {
            log.error("sendReportDataMessageByAppId fail;sender is null");
            return;
        }
        // 获取指定厅
        Integer days = liveConfig.getCheckInRoomRecentRevenueDays();
        GetHasIncomeRoomsParam incomeRoomsParam = new GetHasIncomeRoomsParam()
                        .setAppId(appId)
                        .setStartDay(MyDateUtil.getOffsetDayStartBefore(triggerTime, days))
                        .setEndDay(MyDateUtil.getOffsetDayStartBefore(triggerTime, 1));
        List<Long> hasIncomeRoomIdList = roomDataManager.getHasIncomeRoomIdList(incomeRoomsParam);
        String checkInReportWhitelistStr = liveConfig.getCheckInReportWhitelistStr();
        //预发一定要配
        if(ConfigUtils.getEnvRequired() == Env.PRE && StringUtils.isBlank(checkInReportWhitelistStr)) {
            log.error("打卡报告发送，预发需要配置白名单");
            return;
        }
        //过滤出白名单中的用户
        if(StringUtils.isNotBlank(checkInReportWhitelistStr)) {
            hasIncomeRoomIdList = hasIncomeRoomIdList.stream()
                    .filter(hasIncomeRoomId -> checkInReportWhitelistStr.contains(hasIncomeRoomId.toString()))
                    .collect(Collectors.toList());
        }
        //排个序
        Collections.sort(hasIncomeRoomIdList);
        //获取当前已执行的数量
        int alreadyReportCount = checkInRedisManager.getAlreadyReportCount(appId, triggerTime.getTime());
        int totalNjSize = hasIncomeRoomIdList.size();
        log.info("executeNotify getAlreadyReportCount;alreadyReportCount={};totalNjSize={};incomeRoomsParam={}", alreadyReportCount, totalNjSize, incomeRoomsParam);
        log.debug("executeNotify;appId={};hasIncomeRoomIdList={}", appId, hasIncomeRoomIdList);
        //截断
        if(alreadyReportCount >= totalNjSize) {
            return;
        }
        //分批次的数量
        int countPerMin = liveConfig.getCheckInReportCountPerMin();
        if(countPerMin <= 0) {
            countPerMin = NumberUtils.INTEGER_ONE;
        }
        //[alreadyReportCount,end)
        int end = Math.min(alreadyReportCount + countPerMin,totalNjSize);
        int totalReceiverId = 0;
        //发送报告私信
        for (int i = alreadyReportCount; i < end; i++) {
            totalReceiverId += sendReportDataMessageByRoom(appId, sender, hasIncomeRoomIdList.get(i), triggerTime);
            checkInRedisManager.incrAlreadyReportCount(appId, triggerTime.getTime());
        }
        log.info("sendReportDataMessageByAppId finish;appId={};totalRoom={};totalReceiverId={}",
                appId, totalNjSize, totalReceiverId);
    }

    /**
     * 每个厅发送发送小时、日、周打卡报告私信、结束语
     */
    private int sendReportDataMessageByRoom(Integer appId, Long senderId, Long njId, Date now) {
//        Result<GetRoomInfoByNjIdDTO> roomInfoByNjId = liveManager.getRoomInfoByNjId(njId);
//        if(roomInfoByNjId.rCode() != 0) {
//            log.error("sendReportDataMessageByRoom;roomId no found;appId={};njId={}", appId, njId);
//            return 0;
//        }
        //获取所有报告都需要通知的基本人群
        Set<Long> baseReceiverIds = getBaseReceiverIds(appId, njId);
        //收集被通知的所有人
        Set<Long> collectAllReceiver = new HashSet<>(baseReceiverIds);

        List<CheckInDateTypeEnum> notifiedDateTypes = Lists.newArrayList();
        //增加额外指定人群，并进行个性化的报告
        for (CheckInDateTypeEnum dateTypeEnum : SUPPORT_DATE_TYPES) {
            CheckInRoomPeriodStatHandler handler = getStatHandler(dateTypeEnum);
            if(handler == null) {
                continue;
            }
            if(!handler.isTimeToReport(now)) {
                log.debug("is not time to report;time={};dateType={}", now, dateTypeEnum);
                continue;
            }
            if(!handler.canSend(njId)) {
                log.debug("cannot send report;dateType={};njId={}", dateTypeEnum, njId);
                continue;
            }
            notifiedDateTypes.add(dateTypeEnum);
            //构建通知内容
            String content = handler.buildContent(appId, njId, now);
            //通知基础人群
            chatManager.batchSendCardChatAsync(senderId, baseReceiverIds, content);

            //获取额外指定人群
            List<Long> otherReceivers = handler.populateExtraReceiver(appId, njId, now);
            //过滤掉基本人群中已有的用户
            Set<Long> collect = otherReceivers.stream().filter(otherId -> !baseReceiverIds.contains(otherId))
                    .collect(Collectors.toSet());
            //额外通知的指定人群
            chatManager.batchSendCardChatAsync(senderId, collect, content);
            //收集额外指定人群
            collectAllReceiver.addAll(collect);
        }

        //没有通知，只有小时的&开关关掉了 则不发送
        if(CollectionUtils.isEmpty(notifiedDateTypes) ||
                !liveConfig.isOpenHourWebCenterChat() && notifiedDateTypes.size() == 1 && notifiedDateTypes.get(0) == CheckInDateTypeEnum.HOUR) {
            log.info("sendReportDataMessageByRoom1;njId={};triggerTime={};sendSize={}", njId, now, collectAllReceiver.size());
            log.debug("sendReportDataMessageByRoom1;njId={};triggerTime={};send={}", njId, now, collectAllReceiver);
            return collectAllReceiver.size();
        }

        //所有报告都通知了，整句结束语，消息为低优先级
        chatManager.batchSendRichTextChatAsync(senderId,
                collectAllReceiver,
                String.format(liveConfig.getCheckInReportEndMsgModel(), liveConfig.getBizConfig().getCheckInDetailUrl()),
                WaveCenterChatQueueEnum.LOW_PRIORITY);
        log.info("sendReportDataMessageByRoom2;njId={};triggerTime={};sendSize={}", njId, now, collectAllReceiver.size());
        log.debug("sendReportDataMessageByRoom2;njId={};triggerTime={};send={}", njId, now, collectAllReceiver);
        return collectAllReceiver.size();
    }


    /**
     * 获取支持的处理器
     * @param dateTypeEnum
     * @return
     */
    private CheckInRoomPeriodStatHandler getStatHandler(CheckInDateTypeEnum dateTypeEnum) {
        for (CheckInRoomPeriodStatHandler handler : checkInRoomPeriodStatHandlers) {
            if(handler.support(dateTypeEnum)) {
                return handler;
            }
        }
        log.error("type is not support;dateType={}", dateTypeEnum);
        return null;
    }

    /**
     * 获取指定厅的私信接受主播列表
     */
    private Set<Long> getBaseReceiverIds(Integer appId, Long njId) {
        //厅主也要通知
        Set<Long> result = Sets.newHashSet(njId);
        //打卡管理员
        List<Long> managerIds = waveCheckInDataManager.getManagerIds(appId, njId);
        result.addAll(managerIds);
        //厅管理员
        List<Long> roomRoleAuthUserIds = roleManager.getRoomRoleAuthUserIds(appId, njId, RoleEnum.ROOM);
        result.addAll(roomRoleAuthUserIds);
        log.info("getRoomCheckInReceiverIds;appId={};njId={};managerIds.size={};total={}",
                appId, njId, managerIds.size(), result.size());
        log.debug("getRoomCheckInReceiverIds;appId={};njId={};managerIds={};total={}",
                appId, njId, managerIds, result);
        return result;
    }


    public static void main(String[] args) {
        Map<String, String> params = new HashMap<>();
        params.put("dateType", "HOUR");
        params.put("njId", "5248023412317627958");
        params.put("startDate", "1749718800000");
        params.put("endDate", "1749722399999");
        params.put("appId", "9637128");
        String signCode = SignUtil.signParamsMd5(params, "432346Ebuf#*(BF#$^GFB");
        System.out.println(signCode);
    }

}
