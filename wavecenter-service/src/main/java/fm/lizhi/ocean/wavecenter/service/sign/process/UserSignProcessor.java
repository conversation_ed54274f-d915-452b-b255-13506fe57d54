package fm.lizhi.ocean.wavecenter.service.sign.process;

import fm.lizhi.ocean.wavecenter.api.sign.bean.FamilyAndNjContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.request.*;
import fm.lizhi.ocean.wavecenter.api.sign.response.*;
import fm.lizhi.ocean.wavecenter.base.processor.BusinessEnvAwareProcessor;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/10/10 17:36
 */
public interface UserSignProcessor extends BusinessEnvAwareProcessor {

    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return UserSignProcessor.class;
    }

    /**
     * 用户申请为主播前置检查
     * @param request
     * @return
     */
    ResponseUserApplyPlayer userApplyPlayerCheck(RequestUserApplyPlayer request);

    /**
     * 用户签署管理员申请检查
     * @param request
     * @return
     */
    ResponseUserSignAdminInvite signAdminInviteCheck(RequestUserSignAdminInvite request);

    /**
     * 用户信息检查
     * @param request
     * @return
     */
    ResponseUserInfoStatus signInfoCheck(RequestUserInfoStatus request);

    /**
     * 用户申请成为厅主校验检查
     * @param request
     * @return
     */
    ResponseUserApplyAdmin checkApplyAdmin(RequestUserApplyAdmin request);

    default void applyAdminSuccessProcessor(RequestUserApplyAdmin request, Long contractId) {

    }

    /**
     * 是否存在待签署合同
     * @param request
     * @return
     */
    Optional<FamilyAndNjContractBean> existWaitSignContract(RequestUserApplyAdmin request);

    /**
     * 用户签约为管理员检查
     * @param request
     * @return
     */
    ResponseUserDoSign doSignAdminCheck(RequestUserDoSign request);

    default void doSignSuccessProcessor(RequestUserDoSign request){

    }

    /**
     * 用户签约为管理员生成签署ID
     * @param request
     * @return
     */
    Optional<String> doSignGenSignId(RequestUserDoSign request);

}
