package fm.lizhi.ocean.wavecenter.service.award.singer.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.award.singer.bean.SingerDecorateFlowBean;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestPageSingerDecorateFlow;
import fm.lizhi.ocean.wavecenter.api.award.singer.response.ResponseSingerDecorateFlow;
import fm.lizhi.ocean.wavecenter.api.award.singer.response.ResponseSingerDecorateRule;
import fm.lizhi.ocean.wavecenter.api.award.singer.serivce.SingerDecorateFlowService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.DecorateInfoBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.dto.PageDto;
import fm.lizhi.ocean.wavecenter.service.award.singer.convert.SingerDecorateConvert;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowDTO;
import fm.lizhi.ocean.wavecenter.service.award.singer.manager.SingerDecorateFlowManager;
import fm.lizhi.ocean.wavecenter.service.resource.decorate.manager.DecorateManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@ServiceProvider
@Slf4j
public class SingerDecorateFlowServiceImpl implements SingerDecorateFlowService {

    @Autowired
    private SingerDecorateFlowManager singerDecorateFlowManager;

    @Autowired
    private DecorateManager decorateManager;

    @Autowired
    private UserManager userManager;

    @Override
    public Result<PageBean<ResponseSingerDecorateFlow>> pageSingerDecorateFlow(RequestPageSingerDecorateFlow request) {

        PageDto<SingerDecorateFlowDTO> pageDto = singerDecorateFlowManager.pageSingerDecorateFlow(SingerDecorateConvert.I.convertSingerDecorateFlowParamDTO(request),
                request.getPageNo(), request.getPageSize());

        if (CollUtil.isEmpty(pageDto.getList())){
            return RpcResult.success(PageBean.empty());
        }

        List<Long> userIds = pageDto.getList().stream().map(SingerDecorateFlowDTO::getUserId).collect(Collectors.toList());
        Map<Long, SimpleUserDto> userMapByIds = userManager.getSimpleUserMapByIds(userIds);

        PageBean<ResponseSingerDecorateFlow> pageBean = PageBean.of(pageDto.getTotal(), pageDto.getList().stream().map(bean -> {
            DecorateInfoBean decorateInfo = decorateManager.getDecorateInfo(PlatformDecorateTypeEnum.getByType(bean.getDecorateType()), bean.getDecorateId());
            return SingerDecorateConvert.I.buildResponseSingerDecorateFlow(bean, decorateInfo, MapUtil.get(userMapByIds, bean.getUserId(), SimpleUserDto.class));
        }).collect(Collectors.toList()));
        return RpcResult.success(pageBean);
    }
}
