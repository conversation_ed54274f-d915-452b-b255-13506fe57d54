package fm.lizhi.ocean.wavecenter.service.income.impl;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomIncomeSummaryBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.*;
import fm.lizhi.ocean.wavecenter.api.income.request.RequestRoomIncomeStats;
import fm.lizhi.ocean.wavecenter.api.income.response.ResponseRoomIncomeStats;
import fm.lizhi.ocean.wavecenter.api.income.service.IncomeRoomService;
import fm.lizhi.ocean.wavecenter.api.sign.bean.FamilyAndNjContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.dto.PageDto;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.gift.dto.GiftDto;
import fm.lizhi.ocean.wavecenter.service.gift.manager.GiftManager;
import fm.lizhi.ocean.wavecenter.service.income.convert.IncomeConvert;
import fm.lizhi.ocean.wavecenter.service.income.dto.GiveGiftFlowDto;
import fm.lizhi.ocean.wavecenter.service.income.dto.RoomRecFlowSumDto;
import fm.lizhi.ocean.wavecenter.service.income.manager.FlowManager;
import fm.lizhi.ocean.wavecenter.service.income.manager.IncomeManager;
import fm.lizhi.ocean.wavecenter.service.sign.dto.RequestFamilyAndNjContractDTO;
import fm.lizhi.ocean.wavecenter.service.sign.manager.ContractManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.RoomDataManager;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.RoomIncomeStatParamDTO;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.RoomIncomeStatDTO;
import fm.lizhi.ocean.wavecenter.service.income.convert.IncomeRoomConvert;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/10 08:21
 */
@ServiceProvider
public class IncomeRoomServiceImpl implements IncomeRoomService {

    @Autowired
    private IncomeManager incomeManager;

    @Autowired
    private UserManager userManager;

    @Autowired
    private FlowManager flowManager;

    @Autowired
    private GiftManager giftManager;

    @Autowired
    private ContractManager contractManager;

    @Autowired
    private RoomDataManager roomDataManager;

    @Override
    public Result<RoomIncomeSummaryBean> roomIncomeSummary(long familyId, long roomId, int appid) {
        LogContext.addReqLog("appId={}, familyId={}, roomId={}", appid, familyId, roomId);
        LogContext.addResLog("appId={}, familyId={}, roomId={}", appid, familyId, roomId);
        return ResultHandler.handle(appid, () -> {
            RoomIncomeSummaryBean roomIncomeSummaryBean = incomeManager.roomIncomeSummary(familyId, roomId, appid);
            return RpcResult.success(roomIncomeSummaryBean);
        });
    }

    @Override
    public Result<PageBean<fm.lizhi.ocean.wavecenter.api.income.bean.RoomIncomeDetailBean>> getRoomIncomeDetail(GetRoomIncomeDetailParamBean paramBean) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));
        return ResultHandler.handle(paramBean.getAppId(), ()-> RpcResult.success(incomeManager.getRoomIncomeDetail(paramBean)));
    }

    @Override
    public Result<PageBean<fm.lizhi.ocean.wavecenter.api.income.bean.RoomIncomeDetailBean>> getRoomIncomeDetailOut(GetRoomIncomeDetailParamBean paramBean) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));
        return ResultHandler.handle(paramBean.getAppId(), ()-> RpcResult.success(flowManager.getRoomIncomeDetailOut(paramBean)));
    }

    @Override
    public Result<RoomIncomeDetailSumBean> getRoomIncomeDetailSum(GetRoomIncomeDetailSumParamBean paramBean) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));
        return ResultHandler.handle(paramBean.getAppId(), ()-> RpcResult.success(incomeManager.getRoomIncomeDetailSum(paramBean)));
    }

    @Override
    public Result<PageBean<RoomSignRoomBean>> getRoomSignRoom(GetRoomSignRoomParamBean paramBean) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));
        return ResultHandler.handle(paramBean.getAppId(), ()->{
            //替换波段号为ID
            if (paramBean.getRecUserId() != null) {
                paramBean.setRecUserId(userManager.getUserIdByBand(String.valueOf(paramBean.getRecUserId())));
            }
            if (paramBean.getSendUserId() != null) {
                paramBean.setSendUserId(userManager.getUserIdByBand(String.valueOf(paramBean.getSendUserId())));
            }

            //时间范围处理 是查询当前有效签约期内的流水
            if (paramBean.getFamilyId() != null) {
                PageBean<FamilyAndNjContractBean> list = contractManager.queryContract(RequestFamilyAndNjContractDTO.builder()
                        .familyId(paramBean.getFamilyId())
                        .njId(paramBean.getRoomId())
                        .type(ContractTypeEnum.SIGN)
                        .type(ContractTypeEnum.SUBJECT_CHANGE)
                        .type(ContractTypeEnum.RENEW)
                        .relation(SignRelationEnum.SIGN_SUCCESS)
                        .pageSize(1)
                        .build());
                if (CollectionUtils.isNotEmpty(list.getList())) {
                    FamilyAndNjContractBean contract = list.getList().get(0);
                    Date beginTime = contract.getBeginTime();
                    if (beginTime.getTime() > paramBean.getStartDate().getTime()) {
                        paramBean.setStartDate(beginTime);
                    }

                    Date endTime = contract.getStopTime() == null ? contract.getExpireTime() : contract.getStopTime();
                    if (endTime.getTime() < paramBean.getEndDate().getTime()) {
                        paramBean.setEndDate(endTime);
                    }
                }
            }

            //查询流水记录pre
            PageDto<GiveGiftFlowDto> pageDto = flowManager.getRoomRecFlow(paramBean);
            List<GiveGiftFlowDto> flowList = pageDto.getList();

            List<Long> giftIds = new ArrayList<>();
            List<Long> requestId = new ArrayList<>();
            List<Long> userIds = new ArrayList<>();
            for (GiveGiftFlowDto flowDto : flowList) {
                giftIds.add(flowDto.getGiftId());
                requestId.add(flowDto.getRequestId());
                userIds.add(flowDto.getRecUserId());
                userIds.add(flowDto.getRoomId());
                userIds.add(flowDto.getSendUserId());
            }

            //查询礼物名称
            List<GiftDto> giftList = giftManager.getByIds(giftIds);
            Map<Long, String> giftNameMap = giftList.stream().collect(Collectors.toMap(GiftDto::getId, GiftDto::getName));

            // 调用支付查询流水内容
            Map<Long, String> flowContentMap = incomeManager.getAccountHistoryRemark(flowList.stream().map(GiveGiftFlowDto::getRequestId).collect(Collectors.toSet()));

            //查询用户信息
            Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(userIds);

            List<RoomSignRoomBean> beanList = new ArrayList<>(flowList.size());
            for (GiveGiftFlowDto dto : flowList) {
                RoomSignRoomBean bean = IncomeConvert.I.giveGiftFlowDto2RoomSignRoomBean(dto);
                bean.setGiftName(giftNameMap.get(dto.getGiftId()));
                bean.setContent(flowContentMap.get(dto.getRequestId()));

                UserBean recRoomInfo = bean.getRecRoomInfo();
                SimpleUserDto roomUserDto = userMap.get(recRoomInfo.getId());
                if (roomUserDto != null) {
                    recRoomInfo.setBand(roomUserDto.getBand());
                    recRoomInfo.setName(roomUserDto.getName());
                }

                UserBean sendUserInfo = bean.getSendUserInfo();
                SimpleUserDto sendUserDto = userMap.get(sendUserInfo.getId());
                if (sendUserDto != null) {
                    sendUserInfo.setBand(sendUserDto.getBand());
                    sendUserInfo.setName(sendUserDto.getName());
                }

                UserBean recUserInfo = bean.getRecUserInfo();
                SimpleUserDto recUserDto = userMap.get(recUserInfo.getId());
                if (recUserDto != null) {
                    recUserInfo.setBand(recUserDto.getBand());
                    recUserInfo.setName(recUserDto.getName());
                }

                beanList.add(bean);
            }
            return RpcResult.success(PageBean.of(pageDto.getTotal(), beanList));
        });
    }

    @Override
    public Result<RoomSignRoomSumResBean> getRoomSignRoomSum(GetRoomSignRoomParamBean paramBean) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));
        return ResultHandler.handle(paramBean.getAppId(), ()->{
            //替换波段号为ID
            if (paramBean.getRecUserId() != null) {
                paramBean.setRecUserId(userManager.getUserIdByBand(String.valueOf(paramBean.getRecUserId())));
            }
            if (paramBean.getSendUserId() != null) {
                paramBean.setSendUserId(userManager.getUserIdByBand(String.valueOf(paramBean.getSendUserId())));
            }

            //时间范围处理 是查询当前有效签约期内的流水
            if (paramBean.getFamilyId() != null) {
                PageBean<FamilyAndNjContractBean> list = contractManager.queryContract(RequestFamilyAndNjContractDTO.builder()
                        .familyId(paramBean.getFamilyId())
                        .njId(paramBean.getRoomId())
                        .type(ContractTypeEnum.SIGN)
                        .type(ContractTypeEnum.SUBJECT_CHANGE)
                        .type(ContractTypeEnum.RENEW)
                        .relation(SignRelationEnum.SIGN_SUCCESS)
                        .pageSize(1)
                        .build());
                if (CollectionUtils.isNotEmpty(list.getList())) {
                    FamilyAndNjContractBean contract = list.getList().get(0);
                    Date beginTime = contract.getBeginTime();
                    if (beginTime.getTime() > paramBean.getStartDate().getTime()) {
                        paramBean.setStartDate(beginTime);
                    }

                    Date endTime = contract.getStopTime() == null ? contract.getExpireTime() : contract.getStopTime();
                    if (endTime.getTime() < paramBean.getEndDate().getTime()) {
                        paramBean.setEndDate(endTime);
                    }
                }
            }

            RoomRecFlowSumDto dto = flowManager.getRoomRecFlowSum(paramBean);
            if (null == dto){
                return RpcResult.success(new RoomSignRoomSumResBean());
            }
            return RpcResult.success(new RoomSignRoomSumResBean().setIncome(dto.getIncome()).setCharm(dto.getCharm()));
        });
    }


    @Override
    public Result<PageBean<RoomSignPlayerIncomeBean>> getRoomSignPlayerIncome(GetRoomSignPlayerIncomeParamBean paramBean) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));
        return ResultHandler.handle(paramBean.getAppId(), ()->{
            return RpcResult.success(incomeManager.getRoomSignPlayerIncome(paramBean));
        });
    }

    @Override
    public Result<ResponseRoomIncomeStats> queryRoomIncomeStats(RequestRoomIncomeStats request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        RoomIncomeStatParamDTO paramDto = IncomeRoomConvert.I.convertToParamDto(request);
        // 调用Manager层查询数据
        PageBean<RoomIncomeStatDTO> pageResult = roomDataManager.queryRoomIncomeStats(paramDto);
        // 使用转换器转换响应结果
        ResponseRoomIncomeStats response = IncomeRoomConvert.I.convertToResponse(pageResult);
        LogContext.addResLog("response total={}", response.getTotal());
        return RpcResult.success(response);
    }

}
