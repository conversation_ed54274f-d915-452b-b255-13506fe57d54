package fm.lizhi.ocean.wavecenter.service.permissions.convert;

import fm.lizhi.ocean.wavecenter.api.permissions.bean.RoleAuthRefBean;
import fm.lizhi.ocean.wavecenter.api.permissions.bean.RoleBean;
import fm.lizhi.ocean.wavecenter.service.permissions.dto.RoleAuthRefDto;
import fm.lizhi.ocean.wavecenter.service.permissions.dto.RoleDto;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/11 11:10
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface RoleConvert {
    RoleConvert I = Mappers.getMapper(RoleConvert.class);

    RoleBean roleDto2Bean(RoleDto dto);

    List<RoleBean> roleDtos2Beans(List<RoleDto> dtos);

    @Mappings({
            @Mapping(target = "userInfo.id", source = "userId"),
            @Mapping(target = "subject.id", source = "subjectUserId"),
    })
    RoleAuthRefBean roleAuthRefDto2Bean(RoleAuthRefDto dto);

}
