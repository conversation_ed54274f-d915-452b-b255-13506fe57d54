package fm.lizhi.ocean.wavecenter.service.sign.impl;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.bean.IAdminSignRecord;
import fm.lizhi.ocean.wavecenter.api.sign.bean.NjAndPlayerContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.PlayerSignHistoryRecordBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import fm.lizhi.ocean.wavecenter.api.sign.request.*;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponsePlayerApplyCancel;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponsePlayerOperateCancel;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseWithdrawCancel;
import fm.lizhi.ocean.wavecenter.api.sign.service.SignPlayerService;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.sign.convert.SignConvert;
import fm.lizhi.ocean.wavecenter.service.sign.dto.OperateSignDTO;
import fm.lizhi.ocean.wavecenter.service.sign.dto.QueryNonContractDTO;
import fm.lizhi.ocean.wavecenter.service.sign.manager.NonContractManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@ServiceProvider
public class SignPlayerServiceImpl implements SignPlayerService {

    @Autowired
    private NonContractManager nonContractManager;
    @Autowired
    private UserManager userManager;

    @Override
    public Result<ResponsePlayerApplyCancel> applyCancel(RequestPlayerApplyCancel request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        return ResultHandler.handle(request.getAppId(), () -> {

            ResponsePlayerApplyCancel res = nonContractManager.playerApplyCancel(request);
            LogContext.addResLog("resCode={}", res.getCode());

            return RpcResult.success(res);
        });
    }

    @Override
    public Result<ResponseWithdrawCancel> withdrawCancel(RequestWithdrawCancel request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        return ResultHandler.handle(request.getAppId(), () -> {

            ResponseWithdrawCancel res = nonContractManager.withdrawCancel(request.getPlayerSignId(), request.getCurUserId()
                    , RoleEnum.PLAYER);
            LogContext.addResLog("resCode={}", res.getCode());

            return RpcResult.success(res);
        });
    }

    @Override
    public Result<ResponsePlayerOperateCancel> operateAdminCancel(RequestPlayerOperateCancel request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        return ResultHandler.handle(request.getAppId(), () -> {

            OperateSignDTO res = nonContractManager.operateSign(request.getPlayerSignId()
                    , request.getCurUserId()
                    , ContractTypeEnum.CANCEL
                    , RoleEnum.PLAYER
                    , request.getOperateType());
            LogContext.addResLog("resCode={}", res.getCode());

            return RpcResult.success(new ResponsePlayerOperateCancel().setCode(res.getCode()).setMsg(res.getMsg()));
        });
    }

    @Override
    public Result<PageBean<PlayerSignHistoryRecordBean>> querySignHistory(RequestPlayerSignHistory request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        return ResultHandler.handle(request.getAppId(), () -> {

            QueryNonContractDTO.QueryNonContractDTOBuilder queryNonContractDTOBuilder = QueryNonContractDTO.builder()
                    .userId(request.getPlayerUserId())
                    .pageNo(request.getPageNo())
                    .pageSize(request.getPageSize());
            if (request.getType() != null) {
                queryNonContractDTOBuilder.type(request.getType());
            }
            if (request.getStatus() != null) {
                queryNonContractDTOBuilder.status(request.getStatus());
            }

            PageBean<NjAndPlayerContractBean> pageList = nonContractManager.queryList(queryNonContractDTOBuilder
                    .build());
            if (CollectionUtils.isEmpty(pageList.getList())) {
                LogContext.addResLog("contract list is empty");
                return RpcResult.success(PageBean.empty());
            }

            //解约的原合同签约时间
            Map<Long, NjAndPlayerContractBean> oldMap = new HashMap<>();
            List<Long> oldIds = pageList.getList().stream()
                    .filter(v -> ContractTypeEnum.CANCEL.getCode().equals(v.getType()))
                    .map(NjAndPlayerContractBean::getOldContractId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(oldIds)) {
                PageBean<NjAndPlayerContractBean> oldPageBean = nonContractManager.queryList(QueryNonContractDTO.builder().contractIdLists(oldIds).pageSize(100).build());
                oldMap = oldPageBean.getList().stream().collect(Collectors.toMap(NjAndPlayerContractBean::getContractId, v -> v));
            }

            //查询用户信息
            List<Long> njIds = pageList.getList().stream().map(NjAndPlayerContractBean::getNjUserId).collect(Collectors.toList());
            Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(njIds);

            List<PlayerSignHistoryRecordBean> beanList = new ArrayList<>();
            for (NjAndPlayerContractBean contractBean : pageList.getList()) {
                PlayerSignHistoryRecordBean record = SignConvert.I.njAndPlayerContractBean2HistoryBean(contractBean);
                UserBean roomInfo = record.getRoomInfo();
                SimpleUserDto userInfo = userMap.get(roomInfo.getId());
                if (userInfo != null) {
                    roomInfo.setName(userInfo.getName());
                    roomInfo.setPhoto(userInfo.getAvatar());
                    roomInfo.setBand(userInfo.getBand());
                }

                if (ContractTypeEnum.CANCEL.getCode().equals(contractBean.getType())
                        && contractBean.getOldContractId() != null) {
                    NjAndPlayerContractBean old = oldMap.get(contractBean.getOldContractId());
                    if (old != null) {
                        record.setOldStartTime(old.getStartTime());
                        record.setStopTime(old.getStopTime());
                    }
                }

                //状态转换
                detailStatus(record);
                beanList.add(record);
            }

            return RpcResult.success(PageBean.of(pageList.getTotal(), beanList));
        });
    }

    /**
     * 区分详细状态
     * @param todoSignBean
     */
    private void detailStatus(IAdminSignRecord todoSignBean){
        //待签署 -> 陪玩发起，待对方签署
        if ((SignRelationEnum.WAIT_SIGN.getCode().equals(todoSignBean.findStatus()) || SignRelationEnum.SIGNING.getCode().equals(todoSignBean.findStatus()))
                && RoleEnum.PLAYER.getRoleCode().equals(todoSignBean.findCreateUser())) {
            todoSignBean.changeStatus(SignRelationEnum.WAIT_PARTNER_SIGN.getCode());
        }

        //逾期 -> 陪玩发起，对方逾期
        if (SignRelationEnum.OVERDUE.getCode().equals(todoSignBean.findStatus())
                && RoleEnum.PLAYER.getRoleCode().equals(todoSignBean.findCreateUser())) {
            todoSignBean.changeStatus(SignRelationEnum.PARTNER_OVERDUE.getCode());
        }

        //拒绝 -> 陪玩发起，对方拒绝
        if (SignRelationEnum.REJECT.getCode().equals(todoSignBean.findStatus())
                && RoleEnum.PLAYER.getRoleCode().equals(todoSignBean.findCreateUser())) {
            todoSignBean.changeStatus(SignRelationEnum.PARTNER_REJECT.getCode());
        }
    }

    @Override
    public Result<Boolean> hasSignRecordWithRooms(RequestHasSignRecordWithRooms request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        PageBean<NjAndPlayerContractBean> signPageList = nonContractManager.queryList(QueryNonContractDTO.builder()
                .userId(request.getPlayerId())
                .njIds(request.getNjIds())
                .type(ContractTypeEnum.SIGN)
                .type(ContractTypeEnum.SUBJECT_CHANGE)
                .status(SignRelationEnum.SIGN_SUCCESS)
                .status(SignRelationEnum.STOP_CONTRACT)
                .build());
        return RpcResult.success(CollectionUtils.isNotEmpty(signPageList.getList()));
    }
}
