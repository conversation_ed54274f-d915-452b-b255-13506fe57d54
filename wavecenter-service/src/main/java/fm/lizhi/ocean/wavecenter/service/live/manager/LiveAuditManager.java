package fm.lizhi.ocean.wavecenter.service.live.manager;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.*;
import fm.lizhi.ocean.wavecenter.service.live.dto.GuildAuditRecordDto;
import fm.lizhi.ocean.wavecenter.service.live.dto.RoomAuditRecordDto;

import java.util.Date;

public interface LiveAuditManager {


    PageBean<AuditRecordBean> getAuditRecordDetail(AuditRecordSearchParamBean paramBean, int page, int pageSize);

    boolean addAuditRecord(AddAuditRecordBean recordBean);

    PageBean<GuildAuditRecordDto> guildAuditRecordStats(GuildAuditStatsParamBean paramVo, int page, int pageSize);

    RoomAuditRecordDto roomAuditRecordStats(RoomAuditStatsParamBean paramVo);

    PageBean<Long> signRoomPushPlayer(RoomPushParamBean paramBean, int page, int pageSize);

    /**
     * 保存审核记录完整数据
     *
     * @param recordFullBean
     * @return
     */
    boolean addAuditRecordFull(AddAuditRecordFullParamBean recordFullBean);

    /**
     * 判断审核记录是否已存在
     *
     * @param recordId
     * @return
     */
    boolean existAuditRecord(String recordId);

    /**
     * 修改审核记录的转储文件地址
     *
     * @param recordId          审核ID
     * @param sourceContentUrl 原始转储文件地址
     * @param targetContentUrl 目标转储文件地址
     * @return 结果
     */
    boolean updateAuditContentUrl(String recordId, String sourceContentUrl, String targetContentUrl);

    /**
     * 查询用户的违规次数
     * @param playerId
     * @param startDate
     * @param endDate
     * @return
     */
    Long countPlayerAudit(Long playerId, Date startDate, Date endDate);

}
