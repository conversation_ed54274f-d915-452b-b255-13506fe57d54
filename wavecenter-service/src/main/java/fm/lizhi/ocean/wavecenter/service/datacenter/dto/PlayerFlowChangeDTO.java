package fm.lizhi.ocean.wavecenter.service.datacenter.dto;

import fm.lizhi.ocean.wavecenter.service.datacenter.constants.AccountOpEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/24 14:06
 */
@Data
@Accessors(chain = true)
public class PlayerFlowChangeDTO {

    /**
     * 业务线appId
     */
    private Integer appId;

    private AccountOpEnum accountOpType;

    /**
     * 金额
     */
    private String amount;

    /**
     * 主播ID
     */
    private Long playerId;

    /**
     * lizhi_ppapp_live - PP约玩  lizhi_heiye_common-黑叶 lizhi_ximi_common-西米
     */
    private String tenantCode;

    /**
     * 规则
     */
    private List<String> ruleCodes;

    /**
     * 记账日
     */
    private Date tradeDate;

}
