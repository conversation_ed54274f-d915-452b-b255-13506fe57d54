package fm.lizhi.ocean.wavecenter.service.sign.handler;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.bean.FamilyAndNjContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.FlowConfirmStatusEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.UserSignStatusEnum;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.service.sign.dto.*;
import fm.lizhi.ocean.wavecenter.service.sign.manager.ContractManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.NonContractManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.SignFlowManager;
import fm.lizhi.ocean.wavecenter.service.sign.process.hy.HyAdminSignProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/10/26 15:55
 */
@Slf4j
@Component
public class HySignConfirmHandler {

    @Autowired
    private SignFlowManager signFlowManager;
    @Autowired
    private ContractManager contractManager;
    @Autowired
    private NonContractManager nonContractManager;
    @Autowired
    private FamilyManager familyManager;
    @Autowired
    private HyAdminSignProcessor hyAdminSignProcessor;

    /**
     * 确认签约的合同
     */
    public void doConfirmSign(int pageNo, int pageSize){

        //发起人确认 family/inviteSign
        findWaitCreateSign(pageNo, pageSize);

        //接收人确认 family/confirm
//        findWaitTargetSign(pageNo, pageSize);

    }

    /**
     * 待发起人确认处理
     * @param pageNo
     * @param pageSize
     */
    private void findWaitCreateSign(int pageNo, int pageSize){
        PageBean<SignStatusSyncDTO> flowList = signFlowManager.querySignStatusSync(QuerySignStatusSyncDTO.builder()
                .confirmStatus(FlowConfirmStatusEnum.WAIT_CREATE_SIGN)
                .type(ContractTypeEnum.SIGN)
                .appId(ContextUtils.getBusinessEvnEnum().appId())
                .pageNo(pageNo).pageSize(pageSize)
                .build());
        if (CollectionUtils.isEmpty(flowList.getList())) {
            log.info("findWaitCreateSign flowList is empty");
            return;
        }

        for (SignStatusSyncDTO flow : flowList.getList()) {
            Long contractId = flow.getContractId();
            RoleEnum role = RoleEnum.getByRoleCode(flow.getCreateRole());
            if (role == null) {
                log.info("findWaitCreateSign role is null. contractId={},createRole={}", contractId, flow.getCreateRole());
                continue;
            }
            confirmWaitCreateSign(flow.getId(), contractId, role);
        }
    }

    /**
     * 发起人是否已经确认
     * @param type
     * @param contractId
     * @return
     */
    private boolean isCreateRecordIsFinish(ContractTypeEnum type, Long contractId, RoleEnum role){
        PageBean<SignStatusSyncDTO> flowList = signFlowManager.querySignStatusSync(QuerySignStatusSyncDTO.builder()
                .type(type)
                .contractId(contractId)
                .role(role)
                .appId(ContextUtils.getBusinessEvnEnum().appId())
                .pageNo(1).pageSize(1)
                .build());
        if (CollectionUtils.isEmpty(flowList.getList())) {
            log.info("isCreateRecordIsFinish flowList is empty");
            //可能是在手机发起，所以不校验
            return true;
        }
        SignStatusSyncDTO signStatusSyncDTO = flowList.getList().get(0);
        return FlowConfirmStatusEnum.CREATE_CONFIRM.getCode().equals(signStatusSyncDTO.getConfirmStatus())
                || FlowConfirmStatusEnum.INVOKED.getCode().equals(signStatusSyncDTO.getConfirmStatus());
    }

    /**
     * 待接收人确认处理
     * @param pageNo
     * @param pageSize
     */
    private void findWaitTargetSign(int pageNo, int pageSize) {
        PageBean<SignStatusSyncDTO> flowList = signFlowManager.querySignStatusSync(QuerySignStatusSyncDTO.builder()
                .confirmStatus(FlowConfirmStatusEnum.WAIT_TARGET_SIGN)
                .type(ContractTypeEnum.SIGN)
                .appId(ContextUtils.getBusinessEvnEnum().appId())
                .pageNo(pageNo).pageSize(pageSize)
                .build());
        if (CollectionUtils.isEmpty(flowList.getList())) {
            log.info("findWaitTargetSign flowList is empty");
            return;
        }

        for (SignStatusSyncDTO flow : flowList.getList()) {
            Long contractId = flow.getContractId();
            RoleEnum role = RoleEnum.getByRoleCode(flow.getCreateRole());
            if (role == null) {
                log.info("findWaitTargetSign role is null. contractId={},createRole={}", contractId, flow.getCreateRole());
                continue;
            }

            //发起人是否已经确认
            if (!isCreateRecordIsFinish(ContractTypeEnum.SIGN, contractId, role == RoleEnum.FAMILY ? RoleEnum.ROOM : RoleEnum.FAMILY)) {
                log.info("findWaitTargetSign create not finish. contractId={}", contractId);
                continue;
            }

            confirmWaitTargetSign(flow.getId(), contractId, role);
        }

    }

    /**
     * 确认解约的合同
     */
    public void doConfirmCancel(int pageNo, int pageSize) {
        findWaitCreateCancel(pageNo, pageSize);
        findWaitTargetCancel(pageNo, pageSize);
    }

    /**
     * 待发起人确认处理
     * @param pageNo
     * @param pageSize
     */
    private void findWaitCreateCancel(int pageNo, int pageSize){
        PageBean<SignStatusSyncDTO> flowList = signFlowManager.querySignStatusSync(QuerySignStatusSyncDTO.builder()
                .confirmStatus(FlowConfirmStatusEnum.WAIT_CREATE_SIGN)
                .type(ContractTypeEnum.CANCEL)
                .appId(ContextUtils.getBusinessEvnEnum().appId())
                .pageNo(pageNo).pageSize(pageSize)
                .build());
        if (CollectionUtils.isEmpty(flowList.getList())) {
            log.info("findWaitCreateCancel flowList is empty");
            return;
        }

        for (SignStatusSyncDTO flow : flowList.getList()) {
            Long contractId = flow.getContractId();
            RoleEnum role = RoleEnum.getByRoleCode(flow.getCreateRole());
            if (role == null) {
                log.info("findWaitCreateCancel role is null. contractId={},createRole={}", contractId, flow.getCreateRole());
                continue;
            }
            confirmWaitCreateCancel(flow.getId(), contractId, role);
        }
    }

    /**
     * 待接收人确认处理
     * @param pageNo
     * @param pageSize
     */
    private void findWaitTargetCancel(int pageNo, int pageSize) {
        PageBean<SignStatusSyncDTO> flowList = signFlowManager.querySignStatusSync(QuerySignStatusSyncDTO.builder()
                .confirmStatus(FlowConfirmStatusEnum.WAIT_TARGET_SIGN)
                .type(ContractTypeEnum.CANCEL)
                .appId(ContextUtils.getBusinessEvnEnum().appId())
                .pageNo(pageNo).pageSize(pageSize)
                .build());
        if (CollectionUtils.isEmpty(flowList.getList())) {
            log.info("findWaitTargetCancel flowList is empty");
            return;
        }

        for (SignStatusSyncDTO flow : flowList.getList()) {
            Long contractId = flow.getContractId();
            RoleEnum role = RoleEnum.getByRoleCode(flow.getCreateRole());
            if (role == null) {
                log.info("findWaitTargetCancel role is null. contractId={},createRole={}", contractId, flow.getCreateRole());
                continue;
            }
            if (!isCreateRecordIsFinish(ContractTypeEnum.CANCEL, contractId, role == RoleEnum.FAMILY ? RoleEnum.ROOM : RoleEnum.FAMILY)) {
                log.info("findWaitTargetCancel create not finish. contractId={}", contractId);
                continue;
            }

            confirmWaitTargetCancel(flow.getId(), contractId, role);
        }

    }

    /**
     * 接收人确认
     * @param syncId
     * @param contractId
     * @param targetRole
     */
    private void confirmWaitTargetSign(Long syncId, Long contractId, RoleEnum targetRole) {
        try {
            boolean isLock = signFlowManager.lockFlow(syncId);
            if (!isLock) {
                log.info("confirmWaitTargetSign lock fail. syncId={},contractId={}", syncId, contractId);
                return;
            }

            //查询出合同信息
            PageBean<FamilyAndNjContractBean> pageBean = contractManager.queryContract(RequestFamilyAndNjContractDTO.builder()
                    .pageSize(1)
                    .contractId(contractId)
                    .build());
            if (CollectionUtils.isEmpty(pageBean.getList())) {
                log.info("confirmWaitTargetSign pageList is empty. contractId={}", contractId);
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.CONTRACT_NOT_EXIST.getCode());
                return;
            }

            FamilyAndNjContractBean contract = pageBean.getList().get(0);

            //过期不处理
            if (contract.getSignDeadline() != null && contract.getSignDeadline().before(new Date())) {
                log.info("confirmWaitTargetSign contract expired. contractId={}", contractId);
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.OVERDUE.getCode());
                return;
            }

            //合同状态异常不处理
            if ((!SignRelationEnum.WAIT_SIGN.getCode().equals(contract.getStatus()))
                    && (!SignRelationEnum.SIGNING.getCode().equals(contract.getStatus()))
                    && (!SignRelationEnum.SIGN_SUCCESS.getCode().equals(contract.getStatus()))
            ) {
                //合同签署出现异常，不需要确认
                log.info("confirmWaitCreateSign status is error. contractId={},status={}", contractId, contract.getStatus());
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.STATUS_ERROR.getCode());
                return;
            }

            Long njUserId = contract.getNjUserId();
            Long familyId = contract.getFamilyId();
            Optional<FamilyBean> familyOp = familyManager.getFamily(ContextUtils.getBusinessEvnEnum().appId(), familyId);
            if (!familyOp.isPresent()) {
                log.info("family not exist. contractId={}", contractId);
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.FAMILY_NOT_EXIST.getCode());
                return;
            }
            Long familyUserId = familyOp.get().getUserId();
            Long curUserId = RoleEnum.FAMILY == targetRole ? familyUserId : njUserId;

            //发起人是否已签署
            UserSignStatusEnum userSign = getUserSign(contract.getSignId(), RoleEnum.FAMILY == targetRole ? familyId : njUserId);
            if (userSign == null) {
                log.info("signStatus is null");
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.SIGN_STATUS_IS_NULL.getCode());
                return;
            }

            //还未签署成功，等待下一次检查
            if (userSign == UserSignStatusEnum.WAIT_SIGN || userSign == UserSignStatusEnum.SIGNING) {
                log.info("confirmWaitTargetSign userSign not sign. contractId={},curUserId={}", contractId, curUserId);
                return;
            }

            //签署失败，不更新
            if (userSign != UserSignStatusEnum.SIGN_SUCCESS) {
                log.info("signStatus is not success");
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.SIGN_NOT_SUCCESS.getCode());
                return;
            }

            //对方拒签，不更新
            UserSignStatusEnum targetSignStatus = getUserSign(contract.getSignId(), RoleEnum.FAMILY == targetRole ? njUserId : familyId);
            if (targetSignStatus == UserSignStatusEnum.REJECT) {
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.TARGET_REJECT.getCode());
                return;
            }

            //查询出最近的待签约记录
            PageBean<FamilyNjSignRecordDTO> signList = contractManager.querySignRecord(QueryFamilyNjSignRecordDTO.builder()
                    .familyUserId(familyUserId)
                    .njId(njUserId)
                    .status(SignRelationEnum.WAIT_SIGN)
                    .type(ContractTypeEnum.SIGN)
                    .build());
            if (CollectionUtils.isEmpty(signList.getList())) {
                //等待，因为假如发起人还未签署，是没有数据的
                log.info("signList not exist. contractId={}", contractId);
                return;
            }

            Long familySignId = signList.getList().get(0).getId();
            Pair<Integer, String> canSignCheckRes = nonContractManager.checkCanSignForConfirm(familySignId, curUserId, targetRole, RoleEnum.ROOM);
            if (canSignCheckRes.getKey() != 0) {
                log.info("canSignCheckRes no pass. contractId={},canSignCheckRes={}", contractId, canSignCheckRes.getKey());
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.LIMIT_CHECK.getCode());
                return;
            }

            if (targetRole == RoleEnum.ROOM) {
                boolean passSignRealNameVerify = contractManager.isPassSignRealNameVerify(curUserId);
                if (!passSignRealNameVerify) {
                    log.info("passSignRealNameVerify no pass. contractId={}", contractId);
                    //不用更新状态，等实名认证后即可
                    return;
                }
            }

            Pair<Integer, String> unionFamily = hyAdminSignProcessor.checkVerifyNjUnionFamily(njUserId, familyUserId);
            if (unionFamily.getKey() != 0) {
                log.info("unionFamily no pass. contractId={},unionFamily={}", contractId, unionFamily.getKey());
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.UNION_CHECK.getCode());
                return;
            }

            log.info("confirmWaitTargetSign start. contractId={},curUserId={},familySignId={},targetRole={}", contractId, curUserId, familySignId, targetRole.getRoleCode());
            Pair<Integer, String> result = contractManager.doFamilyNjConfirmSign(familySignId, curUserId, ContractTypeEnum.SIGN, targetRole);
            if (result.getKey() != 0) {
                log.info("doFamilyNjConfirmSign no pass. contractId={},result={}", contractId, result.getKey());
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.CONDITION_NO_PASS.getCode());
            } else {
                log.info("confirmWaitTargetSign success.");
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.TARGET_CONFIRM.getCode());
            }

        } catch (Exception e) {
            log.error("confirmWaitTargetSign exception syncId={} error:", syncId, e);
        } finally {
            signFlowManager.unlockFlow(syncId);
        }

    }

    /**
     * 接收人确认
     * @param syncId
     * @param contractId
     * @param targetRole
     */
    private void confirmWaitTargetCancel(Long syncId, Long contractId, RoleEnum targetRole) {
        try {
            boolean isLock = signFlowManager.lockFlow(syncId);
            if (!isLock) {
                log.info("confirmWaitTargetCancel lock fail. syncId={},contractId={}", syncId, contractId);
                return;
            }

            //查询出合同信息
            PageBean<FamilyAndNjContractBean> pageBean = contractManager.queryContract(RequestFamilyAndNjContractDTO.builder()
                    .pageSize(1)
                    .contractId(contractId)
                    .build());
            if (CollectionUtils.isEmpty(pageBean.getList())) {
                log.info("confirmWaitTargetCancel pageList is empty. contractId={}", contractId);
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.CONTRACT_NOT_EXIST.getCode());
                return;
            }

            FamilyAndNjContractBean contract = pageBean.getList().get(0);

            //过期不处理
            if (contract.getSignDeadline() != null && contract.getSignDeadline().before(new Date())) {
                log.info("confirmWaitTargetCancel contract expired. contractId={}", contractId);
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.OVERDUE.getCode());
                return;
            }

            //合同状态检查
            if ((!SignRelationEnum.WAIT_SIGN.getCode().equals(contract.getStatus()))
                    && (!SignRelationEnum.SIGNING.getCode().equals(contract.getStatus()))
                    && (!SignRelationEnum.SIGN_SUCCESS.getCode().equals(contract.getStatus()))
            ) {
                //合同签署出现异常，不需要确认
                log.info("confirmWaitTargetCancel status is error. contractId={},status={}", contractId, contract.getStatus());
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.STATUS_ERROR.getCode());
                return;
            }

            Long njUserId = contract.getNjUserId();
            Long familyId = contract.getFamilyId();
            Optional<FamilyBean> familyOp = familyManager.getFamily(ContextUtils.getBusinessEvnEnum().appId(), familyId);
            if (!familyOp.isPresent()) {
                log.info("confirmWaitTargetCancel family not exist. contractId={}", contractId);
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.FAMILY_NOT_EXIST.getCode());
                return;
            }
            Long familyUserId = familyOp.get().getUserId();
            Long curUserId = RoleEnum.FAMILY == targetRole ? familyUserId : njUserId;

            //发起人是否已签署
            UserSignStatusEnum userSign = getUserSign(contract.getSignId(), RoleEnum.FAMILY == targetRole ? familyId : njUserId);
            if (userSign == null) {
                log.info("signStatus is null");
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.SIGN_STATUS_IS_NULL.getCode());
                return;
            }

            //还未签署成功，等待下一次检查
            if (userSign == UserSignStatusEnum.WAIT_SIGN || userSign == UserSignStatusEnum.SIGNING) {
                log.info("confirmWaitTargetCancel userSign not sign. contractId={},curUserId={}", contractId, curUserId);
                return;
            }

            //签署失败，不更新
            if (userSign != UserSignStatusEnum.SIGN_SUCCESS) {
                log.info("signStatus is not success");
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.SIGN_NOT_SUCCESS.getCode());
                return;
            }

            //对方拒签，不更新
            UserSignStatusEnum targetSignStatus = getUserSign(contract.getSignId(), RoleEnum.FAMILY == targetRole ? njUserId : familyId);
            if (targetSignStatus == UserSignStatusEnum.REJECT) {
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.TARGET_REJECT.getCode());
                return;
            }

            //查询出最近的待签约记录
            PageBean<FamilyNjSignRecordDTO> signList = contractManager.querySignRecord(QueryFamilyNjSignRecordDTO.builder()
                    .familyUserId(familyUserId)
                    .njId(njUserId)
                    .status(SignRelationEnum.WAIT_SIGN)
                    .type(ContractTypeEnum.CANCEL)
                    .build());
            if (CollectionUtils.isEmpty(signList.getList())) {
                //不改状态，等发起人签完之后，才会生成解约申请记录
                log.info("confirmWaitTargetCancel signList not exist. contractId={}", contractId);
                return;
            }

            Long familySignId = signList.getList().get(0).getId();
            log.info("confirmWaitTargetCancel doFamilyNjConfirmSign. contractId={},familySignId={},curUserId={},targetRole={}", contractId, familySignId, curUserId, targetRole.getRoleCode());
            Pair<Integer, String> result = contractManager.doFamilyNjConfirmSign(familySignId, curUserId, ContractTypeEnum.CANCEL, targetRole);
            if (result.getKey() != 0) {
                log.info("confirmWaitTargetCancel no pass. contractId={},result={}", contractId, result.getKey());
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.CONDITION_NO_PASS.getCode());
            } else {
                log.info("confirmWaitTargetCancel success.");
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.TARGET_CONFIRM.getCode());
            }

        } catch (Exception e) {
            log.error("confirmWaitTargetCancel exception syncId={} error:", syncId, e);
        } finally {
            signFlowManager.unlockFlow(syncId);
        }

    }

    /**
     * 查询签署状态
     * @param signId
     * @param userId
     * @return
     */
    private UserSignStatusEnum getUserSign(String signId, Long userId){
        Map<Long, UserSignStatusEnum> signStatusMap = contractManager.getContractUserSignStatus(signId);
        return signStatusMap.get(userId);
    }

    /**
     * 发起人确认签署
     * @param syncId
     * @param contractId
     * @param createRole
     */
    private void confirmWaitCreateSign(Long syncId, Long contractId, RoleEnum createRole){
        try {
            boolean isLock = signFlowManager.lockFlow(syncId);
            if (!isLock) {
                log.info("confirmWaitCreateSign lock fail. syncId={},contractId={}", syncId, contractId);
                return;
            }

            PageBean<FamilyAndNjContractBean> pageBean = contractManager.queryContract(RequestFamilyAndNjContractDTO.builder()
                    .pageSize(1)
                    .contractId(contractId)
                    .build());
            if (CollectionUtils.isEmpty(pageBean.getList())) {
                log.info("confirmWaitCreateSign pageList is empty. contractId={}", contractId);
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.CONTRACT_NOT_EXIST.getCode());
                return;
            }

            FamilyAndNjContractBean contract = pageBean.getList().get(0);

            //过期不处理
            if (contract.getSignDeadline() != null && contract.getSignDeadline().before(new Date())) {
                log.info("confirmWaitCreateSign contract expired. contractId={}", contractId);
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.OVERDUE.getCode());
                return;
            }

            if ((!SignRelationEnum.WAIT_SIGN.getCode().equals(contract.getStatus()))
                    && (!SignRelationEnum.SIGNING.getCode().equals(contract.getStatus()))
                    && (!SignRelationEnum.SIGN_SUCCESS.getCode().equals(contract.getStatus()))
            ) {
                //合同签署出现异常，不需要确认
                log.info("confirmWaitCreateSign status is error. contractId={},status={}", contractId, contract.getStatus());
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.STATUS_ERROR.getCode());
                return;
            }

            //基础信息
            Long njUserId = contract.getNjUserId();
            Long familyId = contract.getFamilyId();
            Optional<FamilyBean> familyOp = familyManager.getFamily(ContextUtils.getBusinessEvnEnum().appId(), familyId);
            if (!familyOp.isPresent()) {
                log.info("family not exist. contractId={}", contractId);
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.FAMILY_NOT_EXIST.getCode());
                return;
            }
            Long familyUserId = familyOp.get().getUserId();
            Long curUserId = RoleEnum.FAMILY == createRole ? familyUserId : njUserId;
            Long targetUserId = RoleEnum.FAMILY == createRole ? njUserId : familyUserId;

            //发起人是否已签署
            UserSignStatusEnum userSign = getUserSign(contract.getSignId(), RoleEnum.FAMILY == createRole ? familyId : njUserId);
            if (userSign == null) {
                log.info("signStatus is null");
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.SIGN_STATUS_IS_NULL.getCode());
                return;
            }

            //还未签署成功，等待下一次检查
            if (userSign == UserSignStatusEnum.WAIT_SIGN || userSign == UserSignStatusEnum.SIGNING) {
                log.info("confirmWaitCreateSign userSign not sign. contractId={},curUserId={}", contractId, curUserId);
                return;
            }

            //签署失败，不更新
            if (userSign != UserSignStatusEnum.SIGN_SUCCESS) {
                log.info("signStatus is not success");
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.SIGN_NOT_SUCCESS.getCode());
                return;
            }

            //前置状态检查
            boolean inChangeCompany = nonContractManager.isInChangeCompany(curUserId);
            if (inChangeCompany) {
                log.info("confirmWaitCreateSign inChangeCompany. contractId={}", contractId);
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.IN_CHANGE_COMPANY.getCode());
                return;
            }

            //家族开厅数量检查
            Integer canOpenNum = familyManager.countCanOpenRoomNum(familyId);
            Integer signRoomNum = familyManager.countSignRoomNum(familyId);
            if (signRoomNum >= canOpenNum) {
                log.info("confirmWaitCreateSign contractId={},canOpenNum={},signRoomNum={}", contractId, canOpenNum, signRoomNum);
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.CAN_OPEN_NUM_LIMIT.getCode());
                return;
            }

            //冷冻期检查
            Pair<Integer, String> limitCheck = nonContractManager.checkInviteSignLimit(familyUserId, njUserId, createRole, RoleEnum.ROOM);
            if (limitCheck.getKey() != 0) {
                log.info("confirmWaitCreateSign contractId={},limitCheck={}", contractId, limitCheck.getKey());
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.LIMIT_CHECK.getCode());
                return;
            }

            //调用inviteSign
            log.info("confirmWaitCreateSign familyAdminInviteConfirm. contractId={},curUserId={},targetUserId={},createRole={}", contractId, curUserId, targetUserId, createRole.getRoleCode());
            Pair<Integer, String> confirmRes = contractManager.familyAdminInviteConfirm(curUserId, targetUserId, createRole);
            if (confirmRes.getKey() != 0) {
                log.info("confirmWaitCreateSign invite fail. contractId={},confirmRes={}", contractId, confirmRes.getKey());
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.CONDITION_NO_PASS.getCode());
            } else {
                log.info("confirmWaitCreateSign success. contractId={}", contractId);
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.CREATE_CONFIRM.getCode());
            }

        } catch (Exception e) {
            log.error("confirmWaitCreateSign syncId={} error:", syncId, e);
        } finally {
            signFlowManager.unlockFlow(syncId);
        }
    }

    /**
     * 发起人确认签署
     * @param syncId
     * @param contractId
     * @param createRole
     */
    private void confirmWaitCreateCancel(Long syncId, Long contractId, RoleEnum createRole){
        try {
            boolean isLock = signFlowManager.lockFlow(syncId);
            if (!isLock) {
                log.info("confirmWaitCreateCancel lock fail. syncId={},contractId={}", syncId, contractId);
                return;
            }

            PageBean<FamilyAndNjContractBean> pageBean = contractManager.queryContract(RequestFamilyAndNjContractDTO.builder()
                    .pageSize(1)
                    .contractId(contractId)
                    .build());
            if (CollectionUtils.isEmpty(pageBean.getList())) {
                log.info("confirmWaitCreateCancel pageList is empty. contractId={}", contractId);
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.CONTRACT_NOT_EXIST.getCode());
                return;
            }

            FamilyAndNjContractBean contract = pageBean.getList().get(0);

            //过期不处理
            if (contract.getSignDeadline() != null && contract.getSignDeadline().before(new Date())) {
                log.info("confirmWaitCreateCancel contract expired. contractId={}", contractId);
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.OVERDUE.getCode());
                return;
            }

            //合同状态
            if ((!SignRelationEnum.WAIT_SIGN.getCode().equals(contract.getStatus()))
                    && (!SignRelationEnum.SIGNING.getCode().equals(contract.getStatus()))
                    && (!SignRelationEnum.SIGN_SUCCESS.getCode().equals(contract.getStatus()))
            ) {
                //合同签署出现异常，不需要确认
                log.info("confirmWaitCreateCancel status is error. contractId={},status={}", contractId, contract.getStatus());
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.STATUS_ERROR.getCode());
                return;
            }

            //基础信息
            Long njUserId = contract.getNjUserId();
            Long familyId = contract.getFamilyId();
            Optional<FamilyBean> familyOp = familyManager.getFamily(ContextUtils.getBusinessEvnEnum().appId(), familyId);
            if (!familyOp.isPresent()) {
                log.info("confirmWaitCreateCancel family not exist. contractId={}", contractId);
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.FAMILY_NOT_EXIST.getCode());
                return;
            }
            Long familyUserId = familyOp.get().getUserId();
            Long curUserId = RoleEnum.FAMILY == createRole ? familyUserId : njUserId;

            //发起人是否已签署
            UserSignStatusEnum userSign = getUserSign(contract.getSignId(), RoleEnum.FAMILY == createRole ? familyId : njUserId);
            if (userSign == null) {
                log.info("signStatus is null");
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.SIGN_STATUS_IS_NULL.getCode());
                return;
            }

            //还未签署成功，等待下一次检查
            if (userSign == UserSignStatusEnum.WAIT_SIGN || userSign == UserSignStatusEnum.SIGNING) {
                log.info("confirmWaitCreateCancel userSign not sign. contractId={},curUserId={}", contractId, curUserId);
                return;
            }

            //签署失败，不更新
            if (userSign != UserSignStatusEnum.SIGN_SUCCESS) {
                log.info("signStatus is not success");
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.SIGN_NOT_SUCCESS.getCode());
                return;
            }

            //查询最新的签约记录
            PageBean<FamilyNjSignRecordDTO> signList = contractManager.querySignRecord(QueryFamilyNjSignRecordDTO.builder()
                    .familyUserId(familyUserId)
                    .njId(njUserId)
                    .type(ContractTypeEnum.SIGN)
                    .status(SignRelationEnum.SIGN_SUCCESS)
                    .build());
            if (CollectionUtils.isEmpty(signList.getList())) {
                log.info("confirmWaitCreateCancel signList is empty. contractId={}", contractId);
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.FAMILY_NJ_SIGN_NOT_EXIST.getCode());
                return;
            }

            Long familySignId = signList.getList().get(0).getId();

            //判断是否调用过 inviteCancel
            PageBean<FamilyNjSignRecordDTO> parentList = contractManager.querySignRecord(QueryFamilyNjSignRecordDTO.builder()
                    .parentId(familySignId)
                    .status(SignRelationEnum.WAIT_SIGN)
                    .status(SignRelationEnum.SIGNING)
                    .build());

            if (CollectionUtils.isNotEmpty(parentList.getList())) {
                //合同签署出现异常，不需要确认
                log.info("confirmWaitCreateCancel status is error. contractId={},familySignId={}", contractId, familySignId);
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.INVOKED.getCode());
                return;
            }

            //调用inviteSign
            log.info("confirmWaitCreateCancel familyAdminInviteCancelConfirm. contractId={},familySignId={},createRole={},curUserId={}", contractId, familySignId, createRole.getRoleCode(), curUserId);
            Pair<Integer, String> confirmRes = contractManager.familyAdminInviteCancelConfirm(familySignId, createRole, curUserId);
            if (confirmRes.getKey() != 0) {
                log.info("confirmWaitCreateCancel invite fail. contractId={},confirmRes={}", contractId, confirmRes.getKey());
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.CONDITION_NO_PASS.getCode());
            } else {
                log.info("confirmWaitCreateCancel success.");
                signFlowManager.changeConfirmStatus(syncId, FlowConfirmStatusEnum.CREATE_CONFIRM.getCode());
            }

        } catch (Exception e) {
            log.error("confirmWaitCreateSign syncId={} error:", syncId, e);
        } finally {
            signFlowManager.unlockFlow(syncId);
        }
    }

}
