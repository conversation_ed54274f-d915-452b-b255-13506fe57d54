package fm.lizhi.ocean.wavecenter.service.user.convert;

import fm.lizhi.ocean.wavecenter.api.user.bean.LoginRoleInfoBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserRoleInfoBean;
import fm.lizhi.ocean.wavecenter.service.user.dto.LoginRoleInfoDto;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/15 18:29
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface UserCommonConvert {
    UserCommonConvert I = Mappers.getMapper(UserCommonConvert.class);

    @Mappings({
            @Mapping(source = "avatar", target = "photo")
    })
    UserBean simpleUserDto2userBean(SimpleUserDto dto);

    List<UserBean> simpleUserDtos2userBeans(List<SimpleUserDto> dtos);

    LoginRoleInfoBean loginRoleInfoDto2Bean(LoginRoleInfoDto dto);

    @Mappings({
            @Mapping(source = "avatar", target = "photo")
    })
    UserRoleInfoBean simpleUserDto2UserRoleBean(SimpleUserDto dto);
}
