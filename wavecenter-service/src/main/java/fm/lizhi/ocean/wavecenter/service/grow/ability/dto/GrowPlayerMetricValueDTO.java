package fm.lizhi.ocean.wavecenter.service.grow.ability.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/10 19:49
 */
@Data
public class GrowPlayerMetricValueDTO {

    private Long id;

    /**
     * 业务 ID
     */
    private Integer appId;

    /**
     * 陪玩ID
     */
    private Long playerId;

    /**
     * 厅ID
     */
    private Long roomId;

    /**
     * 公会ID
     */
    private Long familyId;

    /**
     * 周开始日期 格式 YYYY-MM-DD
     */
    private Date startWeekDate;

    /**
     * 周结束日期 格式 YYYY-MM-DD
     */
    private Date endWeekDate;

    /**
     * 私信人数
     */
    private Integer chatUserCnt;

    /**
     * 私信回复人数(有效私信用户)
     */
    private Integer replyChatUserCnt;

    /**
     * 私信回复新用户人数(有效私信新用户数)
     */
    private Integer replyChatNewUserCnt;

    /**
     * 送礼人数(付费用户数)
     */
    private Integer giftUserCnt;

    /**
     * 送礼新用户人数(付费新用户数)
     */
    private Integer giftNewUserCnt;

    /**
     * 总收入
     */
    private BigDecimal allIncome;

    /**
     * 上麦时长(分钟)
     */
    private BigDecimal upGuestDur;

    /**
     * 新增粉丝数(累计新增粉丝)
     */
    private Integer newFansUserCnt;

    /**
     * 违规次数
     */
    private Integer violationCnt;

    /**
     * 有效麦序数
     */
    private Integer checkInCnt;

}
