package fm.lizhi.ocean.wavecenter.service.live.manager;

import fm.lizhi.ocean.wavecenter.service.live.dto.*;

import java.util.List;

/**
 * 陪玩打卡数据查询
 * <AUTHOR>
 * @date 2024/6/11 16:14
 */
public interface LivePlayerCheckInManager {

    /**
     * 日统计-查询
     * @param reqDto
     * @return
     */
    List<PlayerCheckDayStatsDto> queryDayStats(PlayerCheckDayStatsReqDto reqDto);

    /**
     * 小时统计-查询
     * @param reqDto
     * @return
     */
    List<PlayerCheckHourStatsDayDto> queryHourStats(PlayerCheckHourStatsReqDto reqDto);

    /**
     * 汇总-根据参数的时间范围计算合计
     * @param reqDto
     * @return
     */
    PlayerCheckStatsSumDto sum(PlayerCheckHourStatsReqDto reqDto);

}
