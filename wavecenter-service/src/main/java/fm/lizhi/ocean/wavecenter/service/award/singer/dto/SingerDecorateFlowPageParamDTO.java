package fm.lizhi.ocean.wavecenter.service.award.singer.dto;

import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateFlowOperateEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateFlowOperatorEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 歌手装扮流水
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SingerDecorateFlowPageParamDTO {

    /**
     * 应用ID
     */
    private int appId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 装扮类型
     * @see fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum
     */
    private Integer decorateType;

    /**
     * 操作类型 1: 发放 2: 回收
     * @see SingerDecorateFlowOperateEnum
     */
    private Integer operateType;

    /**
     * 操作人, 不做模糊查询
     * @see SingerDecorateFlowOperatorEnum
     */
    private String operator;

    /**
     * 开始操作时间
     */
    private Long startOperateTime;

    /**
     * 结束操作时间
     */
    private Long endOperateTime;
}
