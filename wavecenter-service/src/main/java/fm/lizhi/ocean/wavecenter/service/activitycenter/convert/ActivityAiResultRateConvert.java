package fm.lizhi.ocean.wavecenter.service.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.ActivityAiResultRateRequest;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityAiResultRateDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * AI结果评分对象转换器
 */
@Mapper(
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface ActivityAiResultRateConvert {

    ActivityAiResultRateConvert INSTANCE = Mappers.getMapper(ActivityAiResultRateConvert.class);

    /**
     * Request 转 DTO
     *
     * @param request ActivityAiResultRateRequest 请求对象
     * @return ActivityAiResultRateDTO 数据传输对象
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "advice", expression = "java(request.getAdvice() == null ? \"\" : request.getAdvice())")
    ActivityAiResultRateDTO requestToDTO(ActivityAiResultRateRequest request);
} 