package fm.lizhi.ocean.wavecenter.service.live.impl;


import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.live.bean.*;
import fm.lizhi.ocean.wavecenter.api.live.service.LivePlayerCheckInService;
import fm.lizhi.ocean.wavecenter.api.user.bean.RoomBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.live.convert.PlayerCheckStatsConvert;
import fm.lizhi.ocean.wavecenter.service.live.dto.*;
import fm.lizhi.ocean.wavecenter.service.live.manager.LivePlayerCheckInManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@ServiceProvider
public class LivePlayerCheckInServiceImpl implements LivePlayerCheckInService {

    @Autowired
    private LivePlayerCheckInManager playerCheckInManager;
    @Autowired
    private UserManager userManager;

    @Override
    public Result<List<PlayerCheckDayStatsBean>> dayStats(PlayerCheckDayStatsReq req) {
        LogContext.addReqLog("req={}", JsonUtil.dumps(req));
        LogContext.addResLog("req={}", JsonUtil.dumps(req));
        return ResultHandler.handle(req.getAppId(), ()->{
            PlayerCheckDayStatsReqDto reqDto = PlayerCheckStatsConvert.I.playerCheckDayStatsReq2Dto(req);
            List<PlayerCheckDayStatsDto> list = playerCheckInManager.queryDayStats(reqDto);
            return RpcResult.success(PlayerCheckStatsConvert.I.dayStatsDtos2Beans(list));
        });
    }

    @Override
    public Result<List<PlayerCheckHourStatsDayBean>> hourStats(PlayerCheckHourStatsReq req) {
        LogContext.addReqLog("req={}", JsonUtil.dumps(req));
        LogContext.addResLog("req={}", JsonUtil.dumps(req));
        return ResultHandler.handle(req.getAppId(), ()->{
            PlayerCheckHourStatsReqDto reqDto = PlayerCheckStatsConvert.I.playerCheckHourStatsReq2Dto(req);
            List<PlayerCheckHourStatsDayDto> list = playerCheckInManager.queryHourStats(reqDto);

            Set<Long> roomIds = list.stream()
                    .flatMap(playerCheckHourStatsDayDto -> playerCheckHourStatsDayDto.getDetail().stream())
                    .map(PlayerCheckHourStatsDto::getRoomId)
                    .collect(Collectors.toSet());

            Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(new ArrayList<>(roomIds));
            List<PlayerCheckHourStatsDayBean> beanList = PlayerCheckStatsConvert.I.hourStatsDayDtos2Beans(list);
            for (PlayerCheckHourStatsDayBean bean : beanList) {
                for (PlayerCheckHourStatsBean statsBean : bean.getDetail()) {
                    RoomBean room = statsBean.getRoom();
                    if (room == null) {
                        continue;
                    }
                    SimpleUserDto roomDto = userMap.get(room.getId());
                    if(roomDto!=null){
                        room.setBand(roomDto.getBand());
                        room.setName(roomDto.getName());
                    }
                }
            }
            return RpcResult.success(beanList);
        });
    }

    @Override
    public Result<PlayerCheckStatsSumBean> sum(PlayerCheckHourStatsReq req) {
        LogContext.addReqLog("req={}", JsonUtil.dumps(req));
        LogContext.addResLog("req={}", JsonUtil.dumps(req));
        return ResultHandler.handle(req.getAppId(), ()->{
            PlayerCheckHourStatsReqDto reqDto = PlayerCheckStatsConvert.I.playerCheckHourStatsReq2Dto(req);
            PlayerCheckStatsSumDto sum = playerCheckInManager.sum(reqDto);
            return RpcResult.success(PlayerCheckStatsConvert.I.sumDto2Bean(sum));
        });
    }
}
