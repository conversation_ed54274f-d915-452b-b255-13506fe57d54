package fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityRecommendCardConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityRecommendCard;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityRecommendCard;

import java.util.List;

/**
 * 推荐卡
 * <AUTHOR>
 */
public interface ActivityRecommendCardManager {


    /**
     * 保存
     */
    Result<Void> saveRecommendCard(RequestSaveActivityRecommendCard param);


    /**
     * 更新
     */
    Result<Void> updateRecommendCard(RequestUpdateActivityRecommendCard param);

    /**
     * 删除
     */
    Result<Void> deleteRecommendCard(Long id, String operator);


    /**
     * 查询列表
     */
    Result<List<ActivityRecommendCardConfigBean>> listByAppId(Integer appId);

    ActivityRecommendCardConfigBean getRecommendCardByAppIdAndLevelId(Integer appId, Long levelId);
}
