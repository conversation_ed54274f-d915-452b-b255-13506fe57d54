package fm.lizhi.ocean.wavecenter.service.anchor.singer.convert;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.OriginalSingerInfo;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerFamilyInfoBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerUserInfoBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestVerifyAudit;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseGetSingerVerifyRecord;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseQueryHistoryVerifyRecord;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerAuditParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerOperateRecordDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerVerifyRecordDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.UpdateSingerVerifyStatusParamDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        imports = {
                java.util.Arrays.class
        }
)
public interface SingerVerifyRecordConvert {

    SingerVerifyRecordConvert I = Mappers.getMapper(SingerVerifyRecordConvert.class);

    @Mappings({
            @Mapping(target = "songName", source = "dto.songName"),
            @Mapping(target = "auditStatus", source = "dto.auditStatus"),
            @Mapping(target = "songStyle", source = "dto.songStyle"),
            @Mapping(target = "createTime", expression = "java(dto.getCreateTime() != null ? dto.getCreateTime().getTime() : 0L)"),
            @Mapping(target = "auditTime", expression = "java(dto.getAuditTime() != null ? dto.getAuditTime().getTime() : 0L)"),
            @Mapping(target = "originalSingerInfo", source = "dto"),
            @Mapping(target = "familyInfo", ignore = true),
            @Mapping(target = "singerUserInfo", ignore = true),
            @Mapping(target = "njInfo", ignore = true),
            @Mapping(target = "singerVerifySongInfoBeans", ignore = true)
    })
    ResponseGetSingerVerifyRecord toResponseGetSingerVerifyRecord(SingerVerifyRecordDTO dto);

    @Mapping(target = "id", source = "userDto.id")
    @Mapping(target = "name", source = "userDto.name")
    @Mapping(target = "band", source = "userDto.band")
    @Mapping(target = "gender", source = "userDto.gender")
    SingerUserInfoBean buildSingerUserInfo(SimpleUserDto userDto);

    @Mapping(target = "originalSinger", source = "dto.originalSinger")
    @Mapping(target = "originalSongUrl", source = "dto.originalSongUrl")
    @Mapping(target = "socialVerifyImageList", expression = "java(dto.getSocialVerifyImage() != null ? Arrays.asList(dto.getSocialVerifyImage().split(\",\")) : null)")
    OriginalSingerInfo buildOriginalSingerInfo(SingerVerifyRecordDTO dto);

    @Mapping(target = "id", source = "family.id")
    @Mapping(target = "familyName", source = "family.familyName")
    SingerFamilyInfoBean buildFamilyInfo(FamilyBean family);


    default List<ResponseGetSingerVerifyRecord> toResponseGetSingerVerifyRecordList(List<SingerVerifyRecordDTO> dtoList,
                                                                                    Map<Long, SimpleUserDto> userMap, Map<Long, FamilyBean> familyMap) {
        if (dtoList == null) {
            return null;
        }

        return dtoList.stream()
                .map(dto -> {
                    ResponseGetSingerVerifyRecord responseGetSingerVerifyRecord = toResponseGetSingerVerifyRecord(dto);
                    // 设置歌手信息
                    if (userMap != null && userMap.containsKey(dto.getUserId())) {
                        responseGetSingerVerifyRecord.setSingerUserInfo(buildSingerUserInfo(userMap.get(dto.getUserId())));
                    }
                    // 设置厅主信息
                    if (userMap != null && userMap.containsKey(dto.getNjId())) {
                        responseGetSingerVerifyRecord.setNjInfo(buildSingerUserInfo(userMap.get(dto.getNjId())));
                    }
                    // 设置家族信息
                    if (familyMap != null && familyMap.containsKey(dto.getFamilyId())) {
                        responseGetSingerVerifyRecord.setFamilyInfo(buildFamilyInfo(familyMap.get(dto.getFamilyId())));
                    }
                    return responseGetSingerVerifyRecord;
                }).collect(Collectors.toList());
    }

    /**
     * 构建更新参数
     *
     * @param verifyRecord       审核记录
     * @param targetAuditStatus  目标审核状态
     * @param targetSingerStatus 目标歌手状态
     * @return 更新参数DTO
     */
    default UpdateSingerVerifyStatusParamDTO buildUpdateParam(SingerVerifyRecordDTO verifyRecord, SingerAuditParamDTO param, Integer targetAuditStatus,
                                                              Integer targetSingerStatus, boolean needUpdateSingerInfo, Integer passSingerType) {
        UpdateSingerVerifyStatusParamDTO paramDTO = new UpdateSingerVerifyStatusParamDTO();
        paramDTO.setId(verifyRecord.getId());
        paramDTO.setCurrentAuditStatus(verifyRecord.getAuditStatus());
        paramDTO.setTargetAuditStatus(targetAuditStatus);
        paramDTO.setTargetSingerStatus(targetSingerStatus);
        paramDTO.setOperator(param.getOperator());
        paramDTO.setRejectReason(param.getRejectReason());
        paramDTO.setNeedUpdateSingerInfo(needUpdateSingerInfo);
        paramDTO.setPassSingerType(passSingerType);
        return paramDTO;
    }

    default List<ResponseQueryHistoryVerifyRecord> toResponseQueryHistoryVerifyRecordList(List<SingerOperateRecordDTO> dtoList, Map<Long, SimpleUserDto> userMap,
                                                                                          Map<Long, FamilyBean> familyMap) {
        if (dtoList == null) {
            return null;
        }
        List<ResponseQueryHistoryVerifyRecord> result = new ArrayList<>();
        for (SingerOperateRecordDTO dto : dtoList) {
            ResponseQueryHistoryVerifyRecord record = new ResponseQueryHistoryVerifyRecord();
            if (userMap != null && userMap.containsKey(dto.getUserId())) {
                record.setSingerUserInfo(buildSingerUserInfo(userMap.get(dto.getUserId())));
            }
            if (dto.getFamilyId() != null && familyMap != null && familyMap.containsKey(dto.getFamilyId())) {
                record.setFamilyInfo(buildFamilyInfo(familyMap.get(dto.getFamilyId())));
            }
            if (userMap != null && userMap.containsKey(dto.getUserId())) {
                record.setNjInfo(buildSingerUserInfo(userMap.get(dto.getUserId())));
            }
            record.setSongName(dto.getSongName());
            record.setSongStyle(dto.getSongStyle());
            record.setOriginalSinger(dto.getOriginalSinger());
            //查询出来的历史记录，默认是淘汰的
            record.setSingerStatus(SingerStatusEnum.ELIMINATED.getStatus());
            record.setOperator(dto.getOperator());
            record.setPassTime(dto.getPassTime() != null ? dto.getPassTime().getTime() : 0L);
            record.setEliminateTime(dto.getCreateTime().getTime());
            record.setEliminateReason(dto.getEliminationReason());
            result.add(record);
        }
        return result;
    }
} 