package fm.lizhi.ocean.wavecenter.service.activitycenter.manager;

import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityToolBean;

import java.util.List;

/**
 * <AUTHOR>
 */

public interface ActivityToolsManager {

    /**
     * 根据类型获取玩法工具
     */
    ActivityToolBean getActivityTool(Integer toolValue, int appId);

    /**
     * 获取所有的玩法工具
     */
    List<ActivityToolBean> getAllActivityTool(int appId);

    /**
     * 批量获取工具
     */
    List<ActivityToolBean> batchGetTools(int appId, List<Integer> toolValues);
}
