package fm.lizhi.ocean.wavecenter.service.datacenter.impl;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.*;
import fm.lizhi.ocean.wavecenter.api.datacenter.service.RoomDataService;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.datacenter.config.DataCenterConfig;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.AssessTimeDto;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.PerformanceInfoDto;
import fm.lizhi.ocean.wavecenter.service.datacenter.handler.RoomDataHandler;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.AssessmentManager;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.RoomDataManager;
import fm.lizhi.ocean.wavecenter.service.income.manager.IncomeManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/4/18 15:57
 */
@ServiceProvider
public class RoomDataServiceImpl implements RoomDataService {

    @Autowired
    private RoomDataManager roomDataManager;
    @Autowired
    private RoomDataHandler roomDataHandler;
    @Autowired
    private DataCenterConfig dataCenterConfig;
    @Autowired
    private AssessmentManager assessmentManager;
    @Autowired
    private IncomeManager incomeManager;
    @Autowired
    private FamilyManager familyManager;

    @Override
    public Result<RoomAssessmentInfoBean> getAssessmentInfo(int appId, long roomId, long familyId) {
        LogContext.addReqLog("appId={},roomId={},familyId={}", appId, roomId, familyId);
        LogContext.addResLog("appId={},roomId={},familyId={}", appId, roomId, familyId);
        return ResultHandler.handle(appId, ()->{
            Optional<RoomAssessmentInfoBean> assessmentInfoOp = roomDataManager.getAssessmentInfo(appId, familyId, roomId);
            if (!assessmentInfoOp.isPresent()) {
                return RpcResult.fail(ASSESSMENT_INFO_NOT_FOUND);
            }
            RoomAssessmentInfoBean roomAssessmentInfoBean = assessmentInfoOp.get();

            //查询当前考核周期
            AssessTimeDto assessTime = assessmentManager.getCurrentTime(appId, familyId);
            roomAssessmentInfoBean.setStartDate(assessTime.getStartDate().getTime());
            roomAssessmentInfoBean.setEndDate(assessTime.getEndDate().getTime());

            //有收入主播数
            int playerPayCount = incomeManager.getPlayerPayCountByRoom(familyId, roomId, assessTime.getStartDate(), assessTime.getEndDate());
            roomAssessmentInfoBean.setPlayerPayCount(playerPayCount);

            //梯队信息
            Optional<PerformanceInfoDto> performanceInfoOp = assessmentManager.getPerformanceInfo(familyId, roomId);
            if (performanceInfoOp.isPresent()) {
                PerformanceInfoDto performanceInfo = performanceInfoOp.get();
                roomAssessmentInfoBean.setExaminationFlow(EchelonBean.of(
                                performanceInfo.getAmount(),
                                performanceInfo.getCurrentLevel(),
                                performanceInfo.getCurrentLevelBonus())
                        )
                        .setNextEchelon(EchelonBean.of(
                                performanceInfo.getNextLevelAmount(),
                                performanceInfo.getCurrentLevel(),
                                performanceInfo.getCurrentLevelBonus())
                        );
            }
            return RpcResult.success(roomAssessmentInfoBean);
        });
    }

    @Override
    public Result<RoomPlayerPerformanceResBean> getPlayerPerformance(GetRoomPlayerPerformanceBean paramBean) {
        LogContext.addReqLog("paramBean{}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean{}", JsonUtil.dumps(paramBean));
        return ResultHandler.handle(paramBean.getAppId(), ()->RpcResult.success(roomDataHandler.getPlayerPerformance(paramBean)));
    }

    @Override
    public Result<List<IndicatorBean>> getKeyIndicators(RoomGetKeyIndicatorsParamBean paramBean) {
        LogContext.addReqLog("paramBean{}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean{}", JsonUtil.dumps(paramBean));
        return ResultHandler.handle(paramBean.getAppId(), ()->{
            return RpcResult.success(roomDataHandler.getKeyIndicators(paramBean));
        });
    }

    @Override
    public Result<IndicatorTrendResBean> getIndicatorTrend(int appId, Long familyId, long roomId, String metric) {
        LogContext.addReqLog("appId={},familyId={},roomId={},metric={}", appId, familyId, roomId, metric);
        LogContext.addResLog("appId={},familyId={},roomId={},metric={}", appId, familyId, roomId, metric);
        return ResultHandler.handle(appId, ()->{
            int days = dataCenterConfig.getIndicatorTrendDays();
            List<CountDataBean> indicatorTrend = roomDataManager.getIndicatorTrend(familyId, roomId, metric, days);
            return RpcResult.success(new IndicatorTrendResBean()
                    .setCountDays(days)
                    .setCountData(indicatorTrend));
        });
    }

    @Override
    public Result<Integer> getPlayerPayCountInCurrentFamily(int appId, Long roomId, Long startDate, Long endDate) {
        LogContext.addReqLog("appId={},roomId={},startDate={},endDate={}", appId, roomId, startDate, endDate);
        LogContext.addResLog("appId={},roomId={},startDate={},endDate={}", appId, roomId, startDate, endDate);
        return ResultHandler.handle(appId, ()->{
            //有收入主播数
            int playerPayCount = incomeManager.getPlayerPayCountByRoom(0L, roomId, new Date(startDate), new Date(endDate));
            return RpcResult.success(playerPayCount);
        });
    }
}
