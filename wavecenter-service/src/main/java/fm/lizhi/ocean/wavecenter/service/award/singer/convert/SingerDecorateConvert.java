package fm.lizhi.ocean.wavecenter.service.award.singer.convert;

import fm.lizhi.ocean.wavecenter.api.award.singer.bean.SingerDecorateRuleBean;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestPageSingerDecorateFlow;
import fm.lizhi.ocean.wavecenter.api.award.singer.response.ResponseSingerDecorateFlow;
import fm.lizhi.ocean.wavecenter.api.award.singer.response.ResponseSingerDecorateRule;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.DecorateInfoBean;
import fm.lizhi.ocean.wavecenter.base.convert.CommonConvert;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowDTO;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowPageParamDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        imports = {
        },uses = {CommonConvert.class}
)
public interface SingerDecorateConvert {

    SingerDecorateConvert I = Mappers.getMapper(SingerDecorateConvert.class);


    ResponseSingerDecorateRule buildResponseSingerDecorateRule(SingerDecorateRuleBean rule, DecorateInfoBean decorateInfo);

    SingerDecorateFlowPageParamDTO convertSingerDecorateFlowParamDTO(RequestPageSingerDecorateFlow request);

    @Mappings({
            @Mapping(source = "user.avatar", target = "user.photo"),
            @Mapping(source = "user.id", target = "user.id"),
            @Mapping(source = "user.name", target = "user.name"),
            @Mapping(source = "user.band", target = "user.band")
    })
    ResponseSingerDecorateFlow buildResponseSingerDecorateFlow(SingerDecorateFlowDTO flow, DecorateInfoBean decorateInfo, SimpleUserDto user);

}
