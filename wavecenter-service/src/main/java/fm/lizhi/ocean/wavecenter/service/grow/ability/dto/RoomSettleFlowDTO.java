package fm.lizhi.ocean.wavecenter.service.grow.ability.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 厅结算流程
 * <AUTHOR>
 * @date 2025/6/6 16:27
 */
@Data
@Accessors(chain = true)
public class RoomSettleFlowDTO {

    private Long id;

    /**
     * 最后一个结算的厅iD
     */
    private Long lastSettleId;

    /**
     * 状态:1=结算中,2=结算完成
     */
    private Integer status;

    /**
     * 周开始日期 格式 YYYY-MM-DD
     */
    private Date startWeekDate;

    /**
     * 周结束日期 格式 YYYY-MM-DD
     */
    private Date endWeekDate;

    /**
     * 业务 ID
     */
    private Integer appId;

}
