package fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 任务模版项DTO
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskTemplateItemDTO {

    /**
     * 能力项code
     */
    private String capabilityCode;

    /**
     * 能力分
     */
    private BigDecimal capabilityScore;

    /**
     * 条件组
     */
    private ConditionGroupDTO conditionGroup;

    /**
     * ID (更新时传入，新增时为空)
     */
    private Long id;
}
