package fm.lizhi.ocean.wavecenter.service.background.activitycenter.impl;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityImageFodderBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestPageActivityImageFodder;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityImageFodder;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityImageFodder;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseSaveActivityImageFodder;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityImageFodderConfigService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityImageFodderConfigManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 图片素材
 * <AUTHOR>
 */
@ServiceProvider
@Slf4j
public class ActivityImageFodderConfigServiceImpl implements ActivityImageFodderConfigService {

    @Autowired
    private ActivityImageFodderConfigManager activityImageFodderConfigManager;


    @Override
    public Result<ResponseSaveActivityImageFodder> saveImageFodder(RequestSaveActivityImageFodder param) {
        return activityImageFodderConfigManager.saveImageFodder(param);
    }

    @Override
    public Result<Void> updateImageFodder(RequestUpdateActivityImageFodder param) {
        if (param.getId() == null || param.getId() <= 0) {
            log.warn("update image fodder failed, param id is null or empty. param:{}", param);
            return RpcResult.fail(ActivityImageFodderConfigService.UPDATE_ACTIVITY_IMAGE_FODDER_FAIL, "参数不正确");
        }

        return activityImageFodderConfigManager.updateImageFodder(param);
    }

    @Override
    public Result<Void> deleteImageFodder(Long id, String operator) {
        if (id == null || id <= 0) {
            log.warn("delete image fodder failed, id is null or empty. id:{}, operator:{}", id, operator);
            return RpcResult.fail(ActivityImageFodderConfigService.DELETE_ACTIVITY_IMAGE_FODDER_FAIL, "参数不正确");
        }
        return activityImageFodderConfigManager.deleteImageFodder(id, operator);
    }

    @Override
    public Result<PageBean<ActivityImageFodderBean>> pageImageFodder(RequestPageActivityImageFodder param) {
        return activityImageFodderConfigManager.pageImageFodder(param);
    }
}
