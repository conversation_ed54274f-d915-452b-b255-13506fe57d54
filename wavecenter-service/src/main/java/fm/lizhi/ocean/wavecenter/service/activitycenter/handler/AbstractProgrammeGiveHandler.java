package fm.lizhi.ocean.wavecenter.service.activitycenter.handler;

import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityToolBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityClassificationConfigBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.ActivityConfig;
import fm.lizhi.ocean.wavecenter.service.activitycenter.constatns.ResourceGiveErrorTipConstant;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.*;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityApplyManager;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityMaterielManager;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityResourceTransferManager;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityToolsManager;
import fm.lizhi.ocean.wavecenter.service.activitycenter.processor.IProgrammeGiveProcess;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityClassificationManager;
import fm.lizhi.ocean.wavecenter.base.processor.ProcessorFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * @description: 节目资源发放模板方法
 * @author: guoyibin
 * @create: 2024/11/21 14:29
 */
@Slf4j
public abstract class AbstractProgrammeGiveHandler implements FlowResourceGiveHandler {

    @Autowired
    private ActivityMaterielManager activityMaterielManager;

    @Autowired
    private ActivityApplyManager activityApplyManager;

    @Autowired
    private ActivityClassificationManager activityClassificationManager;

    @Autowired
    private ActivityToolsManager activityToolManager;

    @Autowired
    private ProcessorFactory processorFactory;

    @Autowired
    private ActivityResourceTransferManager activityResourceTransferManager;

    @Autowired
    private ActivityConfig config;

    @Override
    public Result<GiveFlowResourceResDTO> giveFlowResource(FlowResourceContext context) {
        try {
            IProgrammeGiveProcess processor = processorFactory.getProcessor(IProgrammeGiveProcess.class);
            if (!processor.isAutoGive()) {
                //不能自动发放，默认是成功
                return RpcResult.success(new GiveFlowResourceResDTO());
            }

            ActivityInfoDTO activityInfoDT0 = activityApplyManager.getActivityInfoById(context.getResourceGiveDTO().getActivityId());
            if (activityInfoDT0 == null) {
                log.error("ProgrammeGiveHandler.giveFlowResource activityId={} not exist", context.getResourceGiveDTO().getActivityId());
                return RpcResult.fail(FlowResourceGiveHandler.GIVE_FLOW_RESOURCE_NO_CONFIG, ResourceGiveErrorTipConstant.PROGRAMME_GIVE_ACTIVITY_NOT_EXIST);
            }
            //获取海报转存信息
            ActivityResourceTransferResultDTO transferResult = activityResourceTransferManager.getResourceTransfer(context.getResourceGiveDTO().getAppId(), activityInfoDT0.getPosterUrl());
            if (transferResult != null) {
                activityInfoDT0.setPosterUrl(transferResult.getTargetUri());
            }

            //查询活动等级信息
            ActivityClassificationConfigBean classification = activityClassificationManager.getActivityClassification(activityInfoDT0.getClassId());
            if (classification == null) {
                log.error("ProgrammeGiveHandler.giveFlowResource activityClassId={} not exist", activityInfoDT0.getClassId());
                return RpcResult.fail(FlowResourceGiveHandler.GIVE_FLOW_RESOURCE_NO_CONFIG, ResourceGiveErrorTipConstant.PROGRAMME_GIVE_LEVEL_EXCEPTION);
            }

            Map<String, Long> primaryClassMap = getPrimaryClassMap();
            Map<String, Long> secondaryClassMap = getSecondaryClassMap();
            if (config.getEnableActivityCategoryMapping() && (primaryClassMap == null || secondaryClassMap == null
                    || !primaryClassMap.containsKey(String.valueOf(classification.getBigClassId()))
                    || !secondaryClassMap.containsKey(String.valueOf(classification.getClassId())))) {
                log.error("ProgrammeGiveHandler.giveFlowResource activityClassId={} not exist", activityInfoDT0.getClassId());
                return RpcResult.fail(FlowResourceGiveHandler.GIVE_FLOW_RESOURCE_NO_CONFIG, ResourceGiveErrorTipConstant.PROGRAMME_GIVE_LEVEL_CONFIG_EXCEPTION);
            }

            SyncWaveActivityToApplyRecordDTO dto = buildSyncRequest(context, activityInfoDT0, classification, secondaryClassMap, primaryClassMap);
            log.info("ProgrammeGiveHandler.giveFlowResource syncWaveActivityToApplyRecord, activityId={}, dto={}", activityInfoDT0.getId(), dto);
            Result<Long> result = activityMaterielManager.syncWaveActivityToApplyRecord(dto);
            if (RpcResult.isFail(result)) {
                String msg = StringUtils.isBlank(result.getMessage()) ? ResourceGiveErrorTipConstant.PROGRAMME_GIVE_FAIL_EXCEPTION : result.getMessage();
                log.info("ProgrammeGiveHandler.giveFlowResource code={}, activityId={}", result.rCode(), activityInfoDT0.getId());
                return RpcResult.fail(GIVE_FLOW_RESOURCE_FAIL, msg);
            }
            return RpcResult.success(new GiveFlowResourceResDTO().setBizRecordId(result.target()));
        } catch (Exception e) {
            log.error("ProgrammeGiveHandler.giveFlowResource happen error:activityId={}", context.getResourceGiveDTO().getActivityId(), e);
            return RpcResult.fail(GIVE_FLOW_RESOURCE_FAIL, ResourceGiveErrorTipConstant.PROGRAMME_GIVE_FAIL_EXCEPTION);
        }
    }

    /**
     * 取消发放流量资源
     *
     * @param param 参数
     * @return 结果
     */
    @Override
    public Result<Void> cancelGiveFlowResource(DeleteOfficialSeatParamDTO param) {
        IProgrammeGiveProcess processor = processorFactory.getProcessor(IProgrammeGiveProcess.class);
        if (!processor.isAutoGive()) {
            //不是自动发放的，默认是成功
            return RpcResult.success();
        }

        if (param.getBizRecordId() == null || param.getBizRecordId() <= 0) {
            //如果没有业务记录ID，默认是成功
            return RpcResult.success();
        }
        DeleteWaveActivityToApplyRecordDTO dto = new DeleteWaveActivityToApplyRecordDTO();
        dto.setId(param.getBizRecordId());
        return activityMaterielManager.deleteWaveActivityToApplyRecord(dto);
    }

    /**
     * 获取一级分类映射
     *
     * @return
     */
    protected abstract Map<String, Long> getPrimaryClassMap();

    /**
     * 获取二级分类映射
     *
     * @return
     */
    protected abstract Map<String, Long> getSecondaryClassMap();

    /**
     * 构建活动工具
     *
     * @param activityToolIdStr 活动工具ID
     * @param appId
     * @return 结果字符串
     */
    private String buildActivityTool(String activityToolIdStr, Integer appId) {
        String activityTool = "";
        StringBuilder builder = new StringBuilder();
        if (StringUtils.isNotBlank(activityToolIdStr)) {
            String[] tools = activityToolIdStr.split(",");
            for (String toolsItem : tools) {
                if (StringUtils.isBlank(toolsItem)) {
                    continue;
                }
                ActivityToolBean toolBean = activityToolManager.getActivityTool(Integer.parseInt(toolsItem), appId);
                if (toolBean != null) {
                    builder.append(toolBean.getName()).append(",");
                }
            }
            if (builder.length() > 0) {
                builder.deleteCharAt(builder.length() - 1);
            }
            activityTool = builder.toString();
        }
        return activityTool;
    }

    private SyncWaveActivityToApplyRecordDTO buildSyncRequest(FlowResourceContext context,
                                                              ActivityInfoDTO activityInfoDT0,
                                                              ActivityClassificationConfigBean classification,
                                                              Map<String, Long> secondaryClassMap,
                                                              Map<String, Long> primaryClassMap) {
        ActivityFlowResourceGiveDTO flowResourceGiveDTO = context.getFlowResourceGiveDTO();
        flowResourceGiveDTO.setImageUrl(flowResourceGiveDTO.getImageUrl() == null ? "" : StringUtils.removeStart(flowResourceGiveDTO.getImageUrl(), "/"));
        activityInfoDT0.setPosterUrl(activityInfoDT0.getPosterUrl() == null ? "" : StringUtils.removeStart(activityInfoDT0.getPosterUrl(), "/"));

        SyncWaveActivityToApplyRecordDTO dto = new SyncWaveActivityToApplyRecordDTO();
        dto.setActivityProcess(activityInfoDT0.getIntroduction());
        dto.setActivityTheme(activityInfoDT0.getName());
        dto.setActivityWay(classification.getBigClassName() + "-" + classification.getClassName());
        dto.setActivityTool(buildActivityTool(activityInfoDT0.getActivityTool(), context.getResourceGiveDTO().getAppId()));
        dto.setOperator("creator");
        dto.setFamilyId(activityInfoDT0.getFamilyId());
        dto.setApplicantId(activityInfoDT0.getApplicantUid());
        dto.setStartTime(activityInfoDT0.getStartTime().getTime());
        dto.setEndTime(activityInfoDT0.getEndTime().getTime());
        dto.setOfficialActivityBackgroundUrl(flowResourceGiveDTO.getImageUrl());
        dto.setCustomUrl(activityInfoDT0.getPosterUrl());
        dto.setNjIds(Lists.newArrayList(activityInfoDT0.getNjId()));
        //将平台的分类ID映射成业务的
        Boolean categoryMapping = config.getEnableActivityCategoryMapping();
        Long activityBigClassId = categoryMapping ? primaryClassMap.get(String.valueOf(classification.getBigClassId())) : classification.getBigClassId();
        Long activityClassId = categoryMapping ? secondaryClassMap.get(String.valueOf(classification.getClassId())) : classification.getClassId();
        dto.setPrimaryActivityId(activityBigClassId);
        dto.setSecondaryActivityId(activityClassId);
        return dto;
    }
}
