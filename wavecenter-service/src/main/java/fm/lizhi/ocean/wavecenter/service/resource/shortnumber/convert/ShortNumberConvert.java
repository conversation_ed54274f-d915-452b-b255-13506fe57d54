package fm.lizhi.ocean.wavecenter.service.resource.shortnumber.convert;

import fm.lizhi.ocean.wavecenter.api.resource.shortnumber.response.ResponseGetShortNumber;
import fm.lizhi.ocean.wavecenter.api.resource.shortnumber.response.ResponseListShortNumber;
import fm.lizhi.ocean.wavecenter.service.resource.shortnumber.dto.ShortNumberDTO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface ShortNumberConvert {

    ShortNumberConvert I = Mappers.getMapper(ShortNumberConvert.class);

    List<ResponseListShortNumber> toResponseListShortNumbers(List<ShortNumberDTO> dtoList);

    ResponseGetShortNumber toResponseGetShortNumber(ShortNumberDTO dto);
}
