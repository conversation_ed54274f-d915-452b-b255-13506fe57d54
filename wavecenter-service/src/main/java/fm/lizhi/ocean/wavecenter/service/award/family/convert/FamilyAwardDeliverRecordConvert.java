package fm.lizhi.ocean.wavecenter.service.award.family.convert;

import fm.lizhi.ocean.wavecenter.api.award.family.bean.AwardDeliverRecordV1Bean;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.AwardDeliverRecordV2Bean;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.FamilyAwardDeliverItemBean;
import fm.lizhi.ocean.wavecenter.base.convert.CommonConvert;
import fm.lizhi.ocean.wavecenter.service.award.family.dto.FamilyAwardDeliverItemDTO;
import fm.lizhi.ocean.wavecenter.service.award.family.dto.FamilyAwardDeliverRecordDTO;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/26 14:46
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        uses = CommonConvert.class
)
public interface FamilyAwardDeliverRecordConvert {

    FamilyAwardDeliverRecordConvert I = Mappers.getMapper(FamilyAwardDeliverRecordConvert.class);

    AwardDeliverRecordV2Bean dto2Bean(FamilyAwardDeliverRecordDTO dto);

    AwardDeliverRecordV1Bean dto2V1Bean(FamilyAwardDeliverRecordDTO dto);

    FamilyAwardDeliverItemBean itemDto2ItemBean(FamilyAwardDeliverItemDTO dto);

    List<FamilyAwardDeliverItemBean> itemDtos2ItemBeans(List<FamilyAwardDeliverItemDTO> dtos);

}
