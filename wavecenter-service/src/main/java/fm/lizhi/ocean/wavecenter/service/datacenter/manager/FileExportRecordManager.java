package fm.lizhi.ocean.wavecenter.service.datacenter.manager;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageParamBean;
import fm.lizhi.ocean.wavecenter.api.common.constants.ExportFileStatusEnum;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.FileExportRecordBean;

public interface FileExportRecordManager {

    /**
     * 创建导出任务
     */
    FileExportRecordBean createTask(Integer appId, Long userId, String fileName);

    /**
     * 更新导出任务
     */
    FileExportRecordBean updateTask(Long recordId, String filePath, ExportFileStatusEnum fileStatus);

    /**
     * 过期文件
     * @param expiredDay
     * @return
     */
    Integer clearExpiredFile(Integer expiredDay);

    /**
     * 分页获取导出列表
     * @param appId
     * @param userId
     * @param pageParamBean
     * @return
     */
    PageBean<FileExportRecordBean> getExportList(Integer appId, Long userId, PageParamBean pageParamBean);

    /**
     * 获取当天下载文件数量
     * @param userId
     * @return
     */
    int getDownloadFileNum(long userId);

    /**
     * 标记长时间未完成的任务为下载失败
     * @param downloadSeconds 下载时长
     */
    Integer markFailToLongFile(int downloadSeconds);
}
