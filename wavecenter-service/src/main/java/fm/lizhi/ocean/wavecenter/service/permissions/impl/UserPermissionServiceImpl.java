package fm.lizhi.ocean.wavecenter.service.permissions.impl;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.permissions.bean.GetUserPermissionBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.permissions.request.RequestGetUserRoomDataScope;
import fm.lizhi.ocean.wavecenter.api.permissions.service.UserPermissionService;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.permissions.config.PermissionsConfig;
import fm.lizhi.ocean.wavecenter.service.permissions.constants.PermissionTypeEnum;
import fm.lizhi.ocean.wavecenter.service.permissions.dto.ComponentDto;
import fm.lizhi.ocean.wavecenter.service.permissions.manager.ComponentManager;
import fm.lizhi.ocean.wavecenter.service.permissions.manager.MenuManager;
import fm.lizhi.ocean.wavecenter.service.permissions.manager.RoleManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.user.constants.UserConstant;
import fm.lizhi.ocean.wavecenter.service.user.manager.LoginManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/27 15:13
 */
@Slf4j
@ServiceProvider
public class UserPermissionServiceImpl implements UserPermissionService {

    @Autowired
    private RoleManager roleManager;
    @Autowired
    private MenuManager menuManager;
    @Autowired
    private ComponentManager componentManager;
    @Autowired
    private LoginManager loginManager;
    @Autowired
    private PermissionsConfig permissionsConfig;
    @Autowired
    private FamilyManager familyManager;

    @Override
    public Result<GetUserPermissionBean> getUserPermission(int appId, long userId, String deviceId) {
        LogContext.addReqLog("getUserPermission. appId={}, userId={}", appId, userId);
        LogContext.addResLog("getUserPermission. appId={}, userId={}", appId, userId);
        return ResultHandler.handle(appId, ()->{
            //查询用户角色
            Optional<String> roleCodeOp = loginManager.getUserRoleCode(appId, userId, deviceId);
            //登录未选角色
            String roleCode = roleCodeOp.orElseGet(() -> roleManager.getUserRoleCode(userId));
            LogContext.addResLog("roleCode={}", roleCode);

            //查询角色拥有的菜单
            List<String> roleMenuCode = menuManager.getRoleMenuCode(roleCode);

            //过滤掉不可授权的菜单
            int loginType = loginManager.getLoginType(appId, userId, deviceId);
            LogContext.addResLog("loginType={}", loginType);
            if (UserConstant.LoginType.AUTH == loginType) {
                roleMenuCode.removeAll(permissionsConfig.getFilterAuthMenu());
            }
            log.info("roleMenuCode={}", JsonUtil.dumps(roleMenuCode));

            //查询角色拥有的组件和读写权限
            List<ComponentDto> roleComponent = componentManager.getRoleComponent(roleCode);
            Map<PermissionTypeEnum, List<String>> group = roleComponent.stream()
                    .collect(Collectors.groupingBy(
                            ComponentDto::getPermissionType,
                            Collectors.mapping(ComponentDto::getComponentCode, Collectors.toList())
                    ));

            return RpcResult.success(GetUserPermissionBean.builder()
                    .roleCode(roleCode)
                    .menu(roleMenuCode)
                    .writeComponents(group.get(PermissionTypeEnum.WRITE))
                    .readComponents(group.get(PermissionTypeEnum.READ))
                    .build());
        });
    }

    @Override
    public Result<String> getUserRoleCode(int appId, long userId) {
        LogContext.addReqLog("appId={}, userId={}", appId, userId);
        LogContext.addResLog("appId={}, userId={}", appId, userId);
        return ResultHandler.handle(appId, ()->{
            String roleCode = roleManager.getUserRoleCode(userId);
            LogContext.addResLog("roleCode={}", roleCode);
            return RpcResult.success(roleCode);
        });
    }

    @Override
    public Result<List<Long>> getUserRoomDataScope(RequestGetUserRoomDataScope request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        //查询用户当前签约的家族
        UserInFamilyBean userInFamily = familyManager.getUserInFamily(request.getUserId());
        Long familyId = userInFamily.getFamilyId();
        LogContext.addResLog("familyId={}", familyId);
        //查询用户在当前家族下的所有授权记录
        List<Long> roomIds = roleManager.getRoleRoomDataScope(RoleEnum.FAMILY_ADMIN, request.getUserId(), familyId);
        LogContext.addResLog("roomIds={}", JsonUtil.dumps(roomIds));
        return RpcResult.success(roomIds);
    }
}
