package fm.lizhi.ocean.wavecenter.service.file.impl;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageParamBean;
import fm.lizhi.ocean.wavecenter.api.common.constants.ExportFileStatusEnum;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.FileExportRecordBean;
import fm.lizhi.ocean.wavecenter.api.file.service.FileExportRecordService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.FileExportRecordManager;
import fm.lizhi.ocean.wavecenter.service.user.config.UserConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;


@ServiceProvider
@Slf4j
public class FileExportRecordServiceImpl implements FileExportRecordService {

    @Autowired
    private FileExportRecordManager fileExportRecordManager;
    @Autowired
    private UserConfig userConfig;

    @Override
    public Result<FileExportRecordBean> createTask(Integer appId, Long userId, String fileName) {
        return ResultHandler.handle(appId,()-> {
            int downloadFileNum = fileExportRecordManager.getDownloadFileNum(userId);
            if (downloadFileNum >= userConfig.getDownFileDayLimit()) {
                return RpcResult.fail(DOWN_FILE_NUM_LIMIT);
            }

            return RpcResult.success(fileExportRecordManager.createTask(appId, userId, fileName));
        });
    }

    @Override
    public Result<FileExportRecordBean> updateTask(Integer appId, Long recordId, String filePath, ExportFileStatusEnum fileStatus) {
        return ResultHandler.handle(appId, () -> RpcResult.success(fileExportRecordManager.updateTask(recordId, filePath, fileStatus)));
    }

    @Override
    public Result<PageBean<FileExportRecordBean>> getExportList(Integer appId, Long userId, PageParamBean pageParamBean) {
        return ResultHandler.handle(appId, () -> RpcResult.success(fileExportRecordManager.getExportList(appId, userId, pageParamBean)));
    }
}
