package fm.lizhi.ocean.wavecenter.service.sign.process.xm;

import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.FamilyAndNjContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.NjAndPlayerContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.IdentifyStatusEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import fm.lizhi.ocean.wavecenter.api.sign.request.*;
import fm.lizhi.ocean.wavecenter.api.sign.response.*;
import fm.lizhi.ocean.wavecenter.api.sign.service.SignUserService;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.service.sign.config.SignConfig;
import fm.lizhi.ocean.wavecenter.service.sign.dto.RequestFamilyAndNjContractDTO;
import fm.lizhi.ocean.wavecenter.service.sign.manager.ContractManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.NonContractManager;
import fm.lizhi.ocean.wavecenter.service.sign.process.UserSignProcessor;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserInfoDto;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserMediaDto;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserVerifyDataDTO;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/10/10 17:40
 */
@Component
public class XmUserSignProcessor implements UserSignProcessor {

    @Autowired
    private ContractManager contractManager;

    @Autowired
    private NonContractManager nonContractManager;

    @Autowired
    private UserManager userManager;

    @Autowired
    private FamilyManager familyManager;

    @Autowired
    private SignConfig signConfig;

    @Override
    public ResponseUserSignAdminInvite signAdminInviteCheck(RequestUserSignAdminInvite request) {
        //西米不支持
        return new ResponseUserSignAdminInvite().setCode(-1);
    }

    @Override
    public ResponseUserApplyPlayer userApplyPlayerCheck(RequestUserApplyPlayer request) {
        ResponseUserApplyPlayer res = new ResponseUserApplyPlayer().setCode(0);

        //检查签约状态：合同表和主播签约表是否都为空
        if (!isNonSign(request.getCurUserId())) {
            return res.setCode(SignUserService.APPLY_PLAYER_REQ_SIGNED);
        }

        //媒体资料检查
        if (!userInfoPass(request.getCurUserId())) {
            return res.setCode(SignUserService.APPLY_PLAYER_REQ_USER_MEDIA_INFO_NOT_EXIST);
        }
        if (!mediaPass(request.getCurUserId())) {
            LogContext.addResLog("media info no pass");
            return res.setCode(SignUserService.APPLY_PLAYER_REQ_USER_MEDIA_INFO_NOT_EXIST);
        }

        //实名检查开关和白名单 方便测试
        if (!signConfig.getXm().isLimitVerifyJoinFamilySwitch()) {
            return res;
        }

        //平台实名检查
        Pair<Integer, String> verifyRes = verifyUnionFamilyCheck(request.getCurUserId(), request.getTargetUserId());
        if (verifyRes.getKey() != 0) {
            return res.setCode(verifyRes.getKey()).setMsg(verifyRes.getValue());
        }

        return res;
    }

    @Override
    public ResponseUserInfoStatus signInfoCheck(RequestUserInfoStatus request) {
        ResponseUserInfoStatus res = new ResponseUserInfoStatus();
        if (!mediaPass(request.getUserId())) {
            return res;
        }
        if (!userInfoPass(request.getUserId())) {
            return res;
        }
        res.setInfoStatus(IdentifyStatusEnum.FINISHED.getCode());
        return res;
    }

    @Override
    public ResponseUserApplyAdmin checkApplyAdmin(RequestUserApplyAdmin request) {
        ResponseUserApplyAdmin res = new ResponseUserApplyAdmin().setCode(0);

        if (!signConfig.getBizConfig().isVerifyUnionFamily()) {
            return res;
        }

        Pair<Integer, String> checkRes = verifyUnionFamilyCheck(request.getCurUserId(), request.getTargetUserId());
        if (checkRes.getKey() != 0) {
            return res.setCode(checkRes.getKey()).setMsg(checkRes.getValue());
        }

        return res;
    }

    /**
     * 实名信息关联家族检查
     * @param curUserId
     * @param targetUserId
     * @return
     */
    private Pair<Integer, String> verifyUnionFamilyCheck(Long curUserId, Long targetUserId){
        Pair<Integer, String> success = Pair.of(0, "");

        //平台实名检查
        Optional<UserVerifyDataDTO> verifyDataOp = userManager.getVerifyData(curUserId);
        if ((!verifyDataOp.isPresent()) || verifyDataOp.get().getVerifyStatus() != 2) {
            return Pair.of(SignUserService.APPLY_ADMIN_NO_PLATFORM_VERIFY, "");
        }

        //实名多账号检查
        String idCardNumber = verifyDataOp.get().getIdCardNumber();
        List<Long> verifyUserIds = userManager.getUserIdFromVerifyResult(idCardNumber);
        if (CollectionUtils.isEmpty(verifyUserIds)) {
            return Pair.of(-1, "实名信息未关联账号");
        }

        if (verifyUserIds.size() == 1) {
            return success;
        }

        Optional<FamilyBean> targetUserFamilyOp = familyManager.getUserFamily(targetUserId);
        if (!targetUserFamilyOp.isPresent()) {
            return Pair.of(SignUserService.APPLY_ADMIN_TARGET_USER_NO_FAMILY, "");
        }
        Long targetFamilyId = targetUserFamilyOp.get().getId();

        for (Long verifyUserId : verifyUserIds) {
            Optional<FamilyBean> verifyUserFamilyOp = familyManager.getUserFamily(verifyUserId);
            if (verifyUserFamilyOp.isPresent() && !Objects.equals(verifyUserFamilyOp.get().getId(), targetFamilyId)) {
                Optional<UserInfoDto> userInfoDto = userManager.getUserInfoById2(verifyUserId);
                String band = "";
                if (userInfoDto.isPresent()) {
                    band = userInfoDto.get().getBand();
                }
                String msg = String.format("当前实名身份已签约家族,请在原账号先解约后再申请,已签约的用户为%s,家族为%s"
                        , band, verifyUserFamilyOp.get().getFamilyName());
                return Pair.of(SignUserService.APPLY_ADMIN_JOINED_FAMILY, msg);
            }
        }
        return success;
    }

    @Override
    public Optional<FamilyAndNjContractBean> existWaitSignContract(RequestUserApplyAdmin request) {
        return Optional.empty();
    }

    @Override
    public ResponseUserDoSign doSignAdminCheck(RequestUserDoSign request) {
        //西米不支持
        return new ResponseUserDoSign().setCode(-1);
    }

    @Override
    public Optional<String> doSignGenSignId(RequestUserDoSign request) {
        return Optional.empty();
    }

    /**
     * 用户信息是否完整
     * @param userId
     * @return
     */
    private boolean userInfoPass(Long userId){
        Optional<UserInfoDto> userOp = userManager.getUserInfoById2(userId);
        if (!userOp.isPresent()) {
            return false;
        }

        UserInfoDto userInfoDto = userOp.get();
        return StringUtils.isNotBlank(userInfoDto.getPhoneNum()) && StringUtils.isNotBlank(userInfoDto.getVoice());
    }

    /**
     * 媒体信息是否通过
     * @param userId
     * @return
     */
    private boolean mediaPass(Long userId){
        Optional<UserMediaDto> media = userManager.getUserMediaById(userId);
        if (!media.isPresent()) {
            return false;
        }
        //相册信息
        if (StringUtils.isBlank(media.get().getAlbumListJSON()) || "[]".equals(media.get().getAlbumListJSON())) {
            return false;
        }
        //语音条信息
        if (StringUtils.isBlank(media.get().getVoiceListJSON()) || "[]".equals(media.get().getVoiceListJSON())) {
            return false;
        }
        return true;
    }

    /**
     * 是否未签约过
     * @param userId
     * @return
     */
    private boolean isNonSign(Long userId){
        ArrayList<ContractTypeEnum> types = Lists.newArrayList(ContractTypeEnum.SIGN);
        ArrayList<SignRelationEnum> statusList = Lists.newArrayList(SignRelationEnum.WAIT_SIGN, SignRelationEnum.SIGN_SUCCESS);

        PageBean<FamilyAndNjContractBean> contractList = contractManager.queryContract(RequestFamilyAndNjContractDTO.builder()
                .njId(userId)
                .types(types)
                .relations(statusList)
                .build());
        List<NjAndPlayerContractBean> signList = nonContractManager.queryUserSign(userId, types, statusList);

        return CollectionUtils.isEmpty(contractList.getList()) && CollectionUtils.isEmpty(signList);
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.XIMI;
    }
}
