package fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto;

import lombok.Data;

import java.util.List;

/**
 * 活动模板流量资源扩展信息DTO
 */
@Data
public class ActivityTemplateFlowResourceExtraDTO {

    /**
     * 官频位时长限制, 单位分钟, 当资源类型为官频位时有值
     */
    @Deprecated
    private Integer durationLimit;

    /**
     * 官频位可选座位号列表, 当资源类型为官频位时有值
     */
    private List<Integer> officialSeatNumbers;
}
