package fm.lizhi.ocean.wavecenter.service.award.family.impl;

import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.AwardDeliverRecordV1Bean;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.AwardDeliverRecordV2Bean;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.FamilyAwardDeliverItemBean;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardResourceTypeEnum;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestAwardDeliverListRecordV1;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestAwardDeliverListRecordV2;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestGetListDeliverItem;
import fm.lizhi.ocean.wavecenter.api.award.family.service.FamilyAwardDeliverService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.dto.PageDto;
import fm.lizhi.ocean.wavecenter.service.award.family.convert.FamilyAwardDeliverRecordConvert;
import fm.lizhi.ocean.wavecenter.service.award.family.dto.FamilyAwardDeliverItemDTO;
import fm.lizhi.ocean.wavecenter.service.award.family.dto.FamilyAwardDeliverRecordDTO;
import fm.lizhi.ocean.wavecenter.service.award.family.dto.GetFamilyAwardDeliverRecordParamDTO;
import fm.lizhi.ocean.wavecenter.service.award.family.manager.FamilyAwardDeliverRecordManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/26 14:06
 */
@Slf4j
@ServiceProvider
public class FamilyAwardDeliverServiceImpl implements FamilyAwardDeliverService {

    @Autowired
    private FamilyAwardDeliverRecordManager familyAwardDeliverRecordManager;

    @Override
    public Result<PageBean<AwardDeliverRecordV2Bean>> listRecordV2(RequestAwardDeliverListRecordV2 request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);

        GetFamilyAwardDeliverRecordParamDTO paramDTO = new GetFamilyAwardDeliverRecordParamDTO();
        paramDTO.setFamilyUserId(request.getFamilyUserId())
                .setFamilyId(request.getFamilyId())
                .setFamilyName(request.getFamilyName())
                .setMinDeliverTime(request.getMinDeliverTime())
                .setMaxDeliverTime(request.getMaxDeliverTime());

        // 查询奖励发放记录
        PageDto<FamilyAwardDeliverRecordDTO> pageDto = familyAwardDeliverRecordManager.getFamilyRecord(paramDTO, request.getPageNumber(), request.getPageSize());
        if (CollectionUtils.isEmpty(pageDto.getList())) {
            LogContext.addResLog("pageDto is empty");
            return RpcResult.success(PageBean.empty());
        }

        // 查询奖励发放详细资源
        List<Long> recordIds = pageDto.getList().stream().map(FamilyAwardDeliverRecordDTO::getId).collect(Collectors.toList());
        List<FamilyAwardDeliverItemDTO> itemList = familyAwardDeliverRecordManager.getFamilyAwardDeliverItem(recordIds);
        Map<Long, List<FamilyAwardDeliverItemDTO>> itemMap = itemList.stream().collect(Collectors.groupingBy(FamilyAwardDeliverItemDTO::getRecordId));

        List<AwardDeliverRecordV2Bean> beanList = new ArrayList<>();
        for (FamilyAwardDeliverRecordDTO dto : pageDto.getList()) {
            AwardDeliverRecordV2Bean bean = FamilyAwardDeliverRecordConvert.I.dto2Bean(dto);
            beanList.add(bean);

            List<FamilyAwardDeliverItemDTO> items = itemMap.get(dto.getId());
            if (CollectionUtils.isEmpty(items)) {
                continue;
            }

            Map<Integer, Integer> resourceNumMap = items.stream().collect(Collectors.toMap(FamilyAwardDeliverItemDTO::getResourceType
                    , FamilyAwardDeliverItemDTO::getResourceNumber));

            // 推荐卡
            bean.setLevelRecommendCardNumber(getResourceNumber(resourceNumMap, FamilyAwardResourceTypeEnum.PP_LEVEL_RECOMMEND_CARD));
            bean.setFlowGrowthRecommendCardNumber(getResourceNumber(resourceNumMap, FamilyAwardResourceTypeEnum.PP_FLOW_GROWTH_RECOMMEND_CARD));
            bean.setNewRoomRetainRecommendCardNumber(getResourceNumber(resourceNumMap, FamilyAwardResourceTypeEnum.PP_NEW_ROOM_RETAIN_RECOMMEND_CARD));
            bean.setZeroLostRoomRecommendCardNumber(getResourceNumber(resourceNumMap, FamilyAwardResourceTypeEnum.PP_ZERO_LOST_ROOM_RECOMMEND_CARD));
            bean.setSpecialRecommendCardNumber(getResourceNumber(resourceNumMap, FamilyAwardResourceTypeEnum.PP_SPECIAL_RECOMMEND_CARD));
            bean.setTotalRecommendCardNumber(bean.getLevelRecommendCardNumber()
                    + bean.getFlowGrowthRecommendCardNumber()
                    + bean.getNewRoomRetainRecommendCardNumber()
                    + bean.getZeroLostRoomRecommendCardNumber()
                    + bean.getSpecialRecommendCardNumber());

            // 新厅名额
            bean.setLevelNewRoomNumber(getResourceNumber(resourceNumMap, FamilyAwardResourceTypeEnum.PP_LEVEL_NEW_ROOM));
            bean.setFlowGrowthNewRoomNumber(getResourceNumber(resourceNumMap, FamilyAwardResourceTypeEnum.PP_FLOW_GROWTH_NEW_ROOM));
            bean.setLostRoomNewRoomNumber(getResourceNumber(resourceNumMap, FamilyAwardResourceTypeEnum.PP_LOST_ROOM_NEW_ROOM));
            bean.setNewRoomRetainNewRoomNumber(getResourceNumber(resourceNumMap, FamilyAwardResourceTypeEnum.PP_NEW_ROOM_RETAIN_NEW_ROOM));
            // 新厅名额数如果总和小于0则置为0. 后续改为直接查execution表, 里面存的总数是调整过的.
            bean.setTotalNewRoomNumber(Math.max(bean.getLevelNewRoomNumber()
                    + bean.getFlowGrowthNewRoomNumber()
                    + bean.getLostRoomNewRoomNumber()
                    + bean.getNewRoomRetainNewRoomNumber(), 0));
        }

        return RpcResult.success(PageBean.of(pageDto.getTotal(), beanList));
    }

    private Integer getResourceNumber(Map<Integer, Integer> resourceNumMap, FamilyAwardResourceTypeEnum resourceType) {
        return resourceNumMap.getOrDefault(resourceType.getValue(), 0);
    }

    @Override
    public Result<PageBean<AwardDeliverRecordV1Bean>> listRecordV1(RequestAwardDeliverListRecordV1 request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);

        GetFamilyAwardDeliverRecordParamDTO paramDTO = new GetFamilyAwardDeliverRecordParamDTO();
        paramDTO.setFamilyUserId(request.getFamilyUserId())
                .setFamilyId(request.getFamilyId())
                .setFamilyName(request.getFamilyName())
                .setMinDeliverTime(request.getMinDeliverTime())
                .setMaxDeliverTime(request.getMaxDeliverTime());

        // 查询奖励发放记录
        PageDto<FamilyAwardDeliverRecordDTO> pageDto = familyAwardDeliverRecordManager.getFamilyRecord(paramDTO, request.getPageNumber(), request.getPageSize());
        if (CollectionUtils.isEmpty(pageDto.getList())) {
            LogContext.addResLog("pageDto is empty");
            return RpcResult.success(PageBean.empty());
        }

        // 查询奖励发放详细资源
        List<Long> recordIds = pageDto.getList().stream().map(FamilyAwardDeliverRecordDTO::getId).collect(Collectors.toList());
        List<FamilyAwardDeliverItemDTO> itemList = familyAwardDeliverRecordManager.getFamilyAwardDeliverItem(recordIds);
        Map<Long, List<FamilyAwardDeliverItemDTO>> itemMap = itemList.stream().collect(Collectors.groupingBy(FamilyAwardDeliverItemDTO::getRecordId));

        List<AwardDeliverRecordV1Bean> beanList = new ArrayList<>();
        for (FamilyAwardDeliverRecordDTO dto : pageDto.getList()) {
            AwardDeliverRecordV1Bean bean = FamilyAwardDeliverRecordConvert.I.dto2V1Bean(dto);
            beanList.add(bean);

            List<FamilyAwardDeliverItemDTO> items = itemMap.get(dto.getId());
            if (CollectionUtils.isEmpty(items)) {
                continue;
            }

            Map<Integer, FamilyAwardDeliverItemDTO> resourceMap = items.stream().collect(Collectors.toMap(FamilyAwardDeliverItemDTO::getResourceType
                    , v -> v));

            FamilyAwardDeliverItemDTO card = resourceMap.get(FamilyAwardResourceTypeEnum.RECOMMEND_CARD.getValue());
            if (card != null) {
                bean.setRecommendCardNumber(card.getResourceNumber());
            }

            FamilyAwardDeliverItemDTO vehicle = resourceMap.get(FamilyAwardResourceTypeEnum.VEHICLE.getValue());
            if (vehicle != null) {
                bean.setVehicleId(vehicle.getResourceId());
                bean.setVehicleName(vehicle.getResourceName());
                bean.setVehicleImage(vehicle.getResourceImage());
            }

            FamilyAwardDeliverItemDTO medal = resourceMap.get(FamilyAwardResourceTypeEnum.MEDAL.getValue());
            if (medal != null) {
                bean.setMedalId(medal.getResourceId());
                bean.setMedalName(medal.getResourceName());
                bean.setMedalImage(medal.getResourceImage());
            }

            FamilyAwardDeliverItemDTO shortNumber = resourceMap.get(FamilyAwardResourceTypeEnum.SHORT_NUMBER.getValue());
            if (shortNumber != null) {
                bean.setShortNumberId(shortNumber.getResourceId());
                bean.setShortNumberName(shortNumber.getResourceName());
            }
        }

        return RpcResult.success(PageBean.of(pageDto.getTotal(), beanList));
    }

    @Override
    public Result<List<FamilyAwardDeliverItemBean>> listDeliverItem(RequestGetListDeliverItem request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);

        List<FamilyAwardDeliverItemDTO> itemList = familyAwardDeliverRecordManager.getFamilyAwardDeliverItem(Lists.newArrayList(request.getRecordId()));
        if (CollectionUtils.isEmpty(itemList)) {
            LogContext.addResLog("itemList is empty");
            return RpcResult.success(Collections.emptyList());
        }

        List<FamilyAwardDeliverItemBean> familyAwardDeliverItemBeans = FamilyAwardDeliverRecordConvert.I.itemDtos2ItemBeans(itemList);
        return RpcResult.success(familyAwardDeliverItemBeans);
    }
}
