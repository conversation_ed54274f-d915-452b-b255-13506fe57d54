package fm.lizhi.ocean.wavecenter.service.activitycenter.impl;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ApplyTimeConfigBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseActivityConfig;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseGetBaseActivityConfig;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityCommonService;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseActivityConfigBean;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.ActivityConfig;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.CommonActivityConfig;
import fm.lizhi.ocean.wavecenter.service.activitycenter.convert.BaseActivityConfigConverter;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityBaseEnumConfigManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@ServiceProvider
public class ActivityCommonServiceImpl implements ActivityCommonService {

    @Autowired
    private ActivityConfig activityConfig;

    @Autowired
    private ActivityBaseEnumConfigManager activityBaseEnumConfigManager;

    @Autowired
    private BaseActivityConfigConverter baseActivityConfigConverter;

    @Override
    public Result<ResponseActivityConfig> getActivityConfigs(Integer appId) {
        return ResultHandler.handle(appId, () -> {
            ApplyTimeConfigBean applyTimeConfigBean = new ApplyTimeConfigBean()
                    .setMinApplyPreactMin(activityConfig.getBizConfig().getMinApplyPreactMin())
                    .setMaxPreactApplyDay(activityConfig.getBizConfig().getMaxPreactApplyDay())
                    .setMaxActivityPeriodMin(activityConfig.getBizConfig().getMaxActivityPeriodMin());
            ResponseActivityConfig config = new ResponseActivityConfig().setApplyTimeConfig(applyTimeConfigBean);
            return RpcResult.success(config);
        });
    }


    @Override
    public Result<ResponseActivityConfigBean> getBaseEnumConfig(int appId) {
        LogContext.addReqLog("`appId={}", appId);
        LogContext.addResLog("`appId={}", appId);
        return ResultHandler.handle(appId, ()-> activityBaseEnumConfigManager.getBaseEnumConfig(appId));
    }

    @Override
    public Result<ResponseGetBaseActivityConfig> getBaseActivityConfig(int appId) {
        LogContext.addReqLog("`appId={}", appId);
        CommonActivityConfig bizConfig = activityConfig.getBizConfig(appId);
        if (bizConfig == null) {
            return RpcResult.fail(CommonService.PARAM_ERROR, "不支持的appId: " + appId);
        }
        ResponseGetBaseActivityConfig response = baseActivityConfigConverter.toResponseGetBaseActivityConfig(bizConfig);
        return RpcResult.success(response);
    }
}
