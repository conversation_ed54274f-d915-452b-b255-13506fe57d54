package fm.lizhi.ocean.wavecenter.service.grow.ability.manager;

import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.PlayerAbility;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.PlayerAbilityWeekCapabilityDTO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/10 17:38
 */
public interface GrowPlayerAbilityManager {

    /**
     * 查询周期内有主播能力数据的厅列表
     * @return
     */
    List<Long> getRoomIdsWeekHasPlayerAbilityByMinRoomId(Date weekStartDay, Date weekEndDay, Long minRoomId, Integer pageSize);

    /**
     * 查询厅内有结算能力的主播数
     * @return
     */
    Integer countRoomAbilityPlayer(Long roomId, Date weekStartDay, Date weekEndDay);

    /**
     * 查询厅内主播能力
     * @param roomId
     * @param weekStartDay
     * @param weekEndDay
     * @param minId
     * @param pageSize
     * @return
     */
    List<PlayerAbilityWeekCapabilityDTO> getRoomPlayerWeekCapability(Long roomId, Date weekStartDay, Date weekEndDay, Long minId, Integer pageSize);

    /**
     * 保存主播能力
     * @param playerAbility
     * @param playerSignTime
     * @param playerFirstSignNjFamily
     */
    void savePlayerAbility(PlayerAbility playerAbility, Long roomId, Long familyId, Date playerSignTime, boolean playerFirstSignNjFamily);

}
