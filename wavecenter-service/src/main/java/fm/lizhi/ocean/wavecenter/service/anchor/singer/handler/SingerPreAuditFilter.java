package fm.lizhi.ocean.wavecenter.service.anchor.singer.handler;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerAuditConfigCodeEnum;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerFilterParamDTO;

/**
 * 歌手认证预审核过滤器
 */
public interface SingerPreAuditFilter {

    /**
     * 过滤器
     *
     * @param config 配置
     * @return 结果, 失败文案
     */
    Result<Void> filter(SingerFilterParamDTO config);

    /**
     * 获取配置code枚举
     *
     * @return 配置code枚举
     */
    SingerAuditConfigCodeEnum getCodeEnum();

    /**
     * 过滤失败
     */
    int FILTER_FAIL_CODE = 1;

    /**
     * 厅状态审核失败
     */
    int HALL_STATUS_AUDIT_FAIL_CODE = 2;
}
