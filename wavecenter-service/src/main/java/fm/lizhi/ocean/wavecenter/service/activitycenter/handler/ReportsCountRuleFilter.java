package fm.lizhi.ocean.wavecenter.service.activitycenter.handler;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityRuleConfigBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ReportCountRuleBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.RequestActivityApplyBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyRuleEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.service.activitycenter.constatns.ActivityApplyErrorTipConstant;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityApplyContext;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityParamDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityApplyManager;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityRedisManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityRuleManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

import static fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityApplyService.ACTIVITY_APPLY_OVER_COUNT_LIMIT;

@Slf4j
@Component
public class ReportsCountRuleFilter implements ApplyRuleFilter {

    @Autowired
    private ActivityRedisManager activityOperationRedisManager;


    @Autowired
    private ActivityRuleManager activityRuleManager;

    @Autowired
    private ActivityApplyManager activityApplyManager;

    @Override
    public Result<Void> filter(ActivityApplyContext context, ActivityRuleConfigBean rule) {
        ActivityParamDTO paramBean = context.getParamDTO();
        //提报次数校验
        Integer count = activityApplyManager.getApplyCountByWeekly(paramBean.getAppId(), paramBean.getNjId(), new Date(paramBean.getStartTime()));
        log.info("activityApply, applyCount:{}, appId:{}, njId:{}, startTime:{}", count, paramBean.getAppId(), paramBean.getNjId(), paramBean.getStartTime());
        if (!Objects.isNull(rule)) {
            ReportCountRuleBean ruleBean = activityRuleManager.getRuleBean(ActivityApplyRuleEnum.REPORTS_COUNT, rule.getRuleJson());
            if (ruleBean != null && count >= ruleBean.getCount()) {
                return RpcResult.fail(ACTIVITY_APPLY_OVER_COUNT_LIMIT, String.format(ActivityApplyErrorTipConstant.APPLY_ACTIVITY_COUNT_INVALID, ruleBean.getCount()));
            }
        }
        return RpcResult.success();
    }

    @Override
    public ActivityApplyRuleEnum getRuleTypeEnum() {
        return ActivityApplyRuleEnum.REPORTS_COUNT;
    }
}
