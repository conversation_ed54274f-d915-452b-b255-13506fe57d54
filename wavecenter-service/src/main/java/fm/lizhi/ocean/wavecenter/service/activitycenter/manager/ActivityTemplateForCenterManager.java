package fm.lizhi.ocean.wavecenter.service.activitycenter.manager;

import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityTemplateBaseInfoDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityTemplateInfoDTO;

import java.util.List;
import java.util.Optional;

public interface ActivityTemplateForCenterManager {

    /**
     * 获取活动模板信息
     * @param activityIds 活动ID列表
     * @return 活动模板信息列表
     */
    List<ActivityTemplateInfoDTO> getActivityTemplateInfoList(List<Long> activityIds);

    /**
     * 查询模板信息
     * @param templateId
     * @return
     */
    Optional<ActivityTemplateBaseInfoDTO> getTemplateBaseInfo(Long templateId);
}
