package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityAiResultRateDTO {
    /**
     * 主键
     */
    private Long id;
    /**
     * 应用ID
     */
    private Integer appId;
    /**
     * 类型
     */
    private Integer type;
    /**
     * 序列ID
     */
    private Long serialId;
    /**
     * 评分
     */
    private Integer score;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建者
     */
    private String creator;
    /**
     * 意见
     */
    private String advice;
} 