package fm.lizhi.ocean.wavecenter.service.anchor.singer.manager;

import java.util.List;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerChatSceneEnum;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerInfoDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerChatSceneDTO;

/**
 * 歌手私信管理
 */
public interface SingerChatManager {

    /**
     * 发送审核结果私信
     *
     * @param appId    应用ID
     * @param singerId 歌手ID
     * @param scene    场景
     */
    void sendAuditResultChat(Integer appId, Long singerId, Integer singerType, String songStyle, SingerChatSceneEnum scene);

    /**
     * 批量发送审核结果私信
     */
    void batchSendAuditResultChat(Integer appId, List<SingerInfoDTO> singerInfoList, SingerChatSceneEnum scene);

    /**
     * 批量发送审核结果私信-异步
     */
    void batchSendAuditResultChatAsync(Integer appId, List<SingerInfoDTO> singerIn<PERSON>List, SingerChatSceneEnum scene);

    /**
     * 新增审核私信配置
     *
     * @param config 配置信息
     */
    void addAuditChatConfig(SingerChatSceneDTO config);

    /**
     * 删除审核私信配置
     *
     * @param appId 应用ID
     * @param sceneCode 场景码
     */
    void deleteAuditChatConfig(Integer appId, String sceneCode);

    /**
     * 修改审核私信配置
     *
     * @param config 配置信息
     */
    void updateAuditChatConfig(SingerChatSceneDTO config);

    /**
     * 根据appId、singerType和sceneCode更新配置内容
     *
     * @param appId 应用ID
     * @param singerType 歌手类型
     * @param sceneCode 场景码
     * @param content 内容
     * @param actionUrl 跳转链接
     */
    void updateAuditChatConfigByAppIdAndSceneCode(Integer appId, Integer singerType, String sceneCode, String content, String actionUrl);

    /**
     * 查询审核私信配置
     *
     * @param appId      应用ID
     * @param singerType 歌手类型
     * @param sceneCode  场景码，可选
     * @return 配置信息列表
     */
    List<SingerChatSceneDTO> getAuditChatConfig(Integer appId, Integer singerType, String sceneCode);
}
