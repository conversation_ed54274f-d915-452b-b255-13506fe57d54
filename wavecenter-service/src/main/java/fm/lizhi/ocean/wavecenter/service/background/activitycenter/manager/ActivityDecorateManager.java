package fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.DecorateBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestBatchGetDecorate;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.DecorateDTO;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.request.RequestGetDecorate;

import java.util.List;

/**
 * 活动装扮
 * <AUTHOR>
 */
public interface ActivityDecorateManager {


    /**
     * 获取装扮列表
     * @param param
     * @return
     */
    Result<PageBean<DecorateBean>> getDecorateList(RequestGetDecorate param);


    /**
     * 批量获取装扮列表
     * @param param
     * @return
     */
    Result<List<DecorateBean>> batchGetDecorateList(RequestBatchGetDecorate param);


    /**
     * 根据 ID 获取头像框
     */
    DecorateBean getDecorateById(Long id, int appId, Integer type);

    /**
     * 查询活动对应的装扮
     * @param activityId
     * @param appId
     * @return
     */
    List<DecorateBean> getDecorateListById(Long activityId, int appId);

}
