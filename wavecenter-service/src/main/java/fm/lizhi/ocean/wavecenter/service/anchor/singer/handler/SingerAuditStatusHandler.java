package fm.lizhi.ocean.wavecenter.service.anchor.singer.handler;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerAuditParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerExecuteAuditDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerVerifyRecordDTO;

/**
 * 歌手认证预审核过滤器
 */
public interface SingerAuditStatusHandler {

    /**
     * 执行审核操作
     *
     * @param param 审核参数
     * @param verifyRecord 认证记录
     */
    SingerExecuteAuditDTO executeAudit(SingerAuditParamDTO param, SingerVerifyRecordDTO verifyRecord);

    /**
     * 审核状态枚举
     *
     * @return 审核状态枚举
     */
    SingerAuditStatusEnum getAuditStatusEnum();
}
