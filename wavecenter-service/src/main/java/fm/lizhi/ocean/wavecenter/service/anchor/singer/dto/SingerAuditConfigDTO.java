package fm.lizhi.ocean.wavecenter.service.anchor.singer.dto;

import java.util.Date;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SingerAuditConfigDTO {

    /**
     * 主播审核配置ID
     */
    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 配置编码
     */
    private String configCode;

    /**
     * 状态
     */
    private Boolean enabled;

    /**
     * 配置说明
     */
    private String explanation;

    /**
     * 是否可编辑
     */
    private Boolean editable;

    /**
     * 审核场景，1：前端审核，2：后端审核
     */
    private Integer auditScene;

    /**
     * 创建时间
     */
    private Date createTime;

}
