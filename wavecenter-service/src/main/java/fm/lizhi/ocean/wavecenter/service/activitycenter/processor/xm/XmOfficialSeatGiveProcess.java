package fm.lizhi.ocean.wavecenter.service.activitycenter.processor.xm;

import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.OfficialSeatExtraBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.ActivityConfig;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.FlowResourceContext;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.OfficialSeatExtraDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.SaveOfficialSeatParamDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.processor.IOfficialSeatGiveProcess;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class XmOfficialSeatGiveProcess implements IOfficialSeatGiveProcess {

    @Autowired
    private ActivityConfig activityConfig;

    @Override
    public void fillOfficialSeatParam(FlowResourceContext context, SaveOfficialSeatParamDTO param, OfficialSeatExtraBean extraBean) {
        OfficialSeatExtraDTO extraDTO = new OfficialSeatExtraDTO()
                .setNote(context.getResourceGiveDTO().getActivityName());
        param.setTabId(Long.valueOf(activityConfig.getXm().getOfficialSeatTabId()));
        param.setExtra(extraDTO);

    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.XIMI;
    }
}
