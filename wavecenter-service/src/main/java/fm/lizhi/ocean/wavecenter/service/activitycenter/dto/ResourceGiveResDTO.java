package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ResourceGiveResDTO {

    /**
     * 是否改为手动配置
     */
    private boolean manualConfig;

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 错误提示
     */
    private String msg;

    public static ResourceGiveResDTO fail(String msg, boolean manualConfig) {
        return new ResourceGiveResDTO().setMsg(msg).setManualConfig(manualConfig).setSuccess(false);
    }

    public static ResourceGiveResDTO success() {
        return new ResourceGiveResDTO().setSuccess(true);
    }

    public static ResourceGiveResDTO success(int code, String msg, boolean manualConfig) {
        return new ResourceGiveResDTO().setSuccess(code == 0).setMsg(msg).setManualConfig(manualConfig);
    }

}
