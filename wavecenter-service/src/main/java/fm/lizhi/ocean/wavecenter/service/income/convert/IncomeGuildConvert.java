package fm.lizhi.ocean.wavecenter.service.income.convert;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.IncomeSummaryBean;
import fm.lizhi.ocean.wavecenter.api.income.request.RequestGuildIncomeStats;
import fm.lizhi.ocean.wavecenter.api.income.bean.IncomeStatBean;
import fm.lizhi.ocean.wavecenter.api.income.response.ResponseGuildIncomeStats;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.GuildIncomeStatParamDTO;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.GuildIncomeStatDTO;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.IncomeSummaryDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface IncomeGuildConvert {

    IncomeGuildConvert I = Mappers.getMapper(IncomeGuildConvert.class);

    /**
     * 转换请求参数
     *
     * @param request 请求参数
     * @return 参数DTO
     */
    GuildIncomeStatParamDTO convertToParamDto(RequestGuildIncomeStats request);

    /**
     * 转换响应结果
     *
     * @param pageResult 分页结果
     * @return 响应结果
     */
    @Mapping(target = "total", source = "total")
    @Mapping(target = "list", source = "list")
    ResponseGuildIncomeStats convertToResponse(PageBean<GuildIncomeStatDTO> pageResult);

    /**
     * 转换统计项
     *
     * @param dto 统计DTO
     * @return 响应统计项
     */
    @Mapping(target = "startTime", source = "startTime")
    @Mapping(target = "endTime", source = "endTime")
    @Mapping(target = "info", source = "info")
    IncomeStatBean convertToStatItem(GuildIncomeStatDTO dto);

    /**
     * 转换收入信息
     *
     * @param incomeInfo 收入信息DTO
     * @return 响应收入信息
     */
    IncomeSummaryBean convertToIncomeInfo(IncomeSummaryDTO incomeInfo);

    /**
     * 转换统计项列表
     *
     * @param dtoList 统计DTO列表
     * @return 响应统计项列表
     */
    List<IncomeStatBean> convertToStatItemList(List<GuildIncomeStatDTO> dtoList);
}
