package fm.lizhi.ocean.wavecenter.service.activitycenter.manager;

import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityResourceTransferDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityResourceTransferResultDTO;

/**
 * <AUTHOR>
 */
public interface ActivityResourceTransferManager {

    /**
     * 资源转存
     *
     * @return
     */
    void transfer(ActivityResourceTransferDTO dto);

    /**
     * 查询转存记录
     * @param appId
     * @param sourceUri 原始资源地址，相对地址，以/开头
     * @return 资源转存后的地址，相对地址，以/开头
     */
    ActivityResourceTransferResultDTO getResourceTransfer(int appId, String sourceUri);
}
