package fm.lizhi.ocean.wavecenter.service.award.singer.impl;

import cn.hutool.core.collection.CollUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.award.singer.bean.SingerDecorateRuleBean;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestPageSingerDecorateRule;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestSaveSingerDecorateRule;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestUpdateSingerDecorateRule;
import fm.lizhi.ocean.wavecenter.api.award.singer.response.ResponseSingerDecorateRule;
import fm.lizhi.ocean.wavecenter.api.award.singer.serivce.SingerDecorateRuleService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.DecorateInfoBean;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.service.DecorateService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.dto.PageDto;
import fm.lizhi.ocean.wavecenter.service.award.singer.convert.SingerDecorateConvert;
import fm.lizhi.ocean.wavecenter.service.award.singer.manager.SingerDecorateRuleManager;
import fm.lizhi.ocean.wavecenter.service.resource.decorate.manager.DecorateManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 歌手装扮规则配置
 * <AUTHOR>
 */
@ServiceProvider
@Slf4j
public class SingerDecorateRuleServiceImpl implements SingerDecorateRuleService {

    @Autowired
    private SingerDecorateRuleManager singerDecorateRuleManager;

    @Autowired
    private DecorateManager decorateManager;



    @Override
    public Result<PageBean<ResponseSingerDecorateRule>> pageSingerDecorateRule(RequestPageSingerDecorateRule request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);

        PageDto<SingerDecorateRuleBean> pageDto = singerDecorateRuleManager.pageSingerDecorateRule(request);

        if (CollUtil.isEmpty(pageDto.getList())){
            return RpcResult.success(PageBean.empty());
        }

        PageBean<ResponseSingerDecorateRule> pageBean = PageBean.of(pageDto.getTotal(), pageDto.getList().stream().map(bean -> {
            DecorateInfoBean decorateInfo = decorateManager.getDecorateInfo(PlatformDecorateTypeEnum.getByType(bean.getDecorateType()), bean.getDecorateId());
            return SingerDecorateConvert.I.buildResponseSingerDecorateRule(bean, decorateInfo);
        }).collect(Collectors.toList()));

        return RpcResult.success(pageBean);
    }

    @Override
    public Result<Void> saveSingerDecorateRule(RequestSaveSingerDecorateRule request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);

        Boolean success = singerDecorateRuleManager.saveSingerDecorateRule(request);
        return success ? RpcResult.success(): RpcResult.fail(SingerDecorateRuleService.SAVE_SINGER_DECORATE_RULE_FAIL, "保存失败");
    }

    @Override
    public Result<Void> updateSingerDecorateRule(RequestUpdateSingerDecorateRule request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);

        Boolean success = singerDecorateRuleManager.updateSingerDecorateRule(request);
        return success? RpcResult.success(): RpcResult.fail(SingerDecorateRuleService.UPDATE_SINGER_DECORATE_RULE_FAIL, "更新失败");
    }
}
