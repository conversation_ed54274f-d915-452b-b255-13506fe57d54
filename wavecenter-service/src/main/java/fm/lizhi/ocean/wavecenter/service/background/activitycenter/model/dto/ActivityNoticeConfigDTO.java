package fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class ActivityNoticeConfigDTO {

    private Long id;

    private Integer appId;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 公告内容
     */
    private String content;


    /**
     * 品类值
     */
    private List<Integer> categoryList;

}
