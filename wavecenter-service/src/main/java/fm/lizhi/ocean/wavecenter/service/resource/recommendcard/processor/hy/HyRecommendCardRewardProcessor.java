package fm.lizhi.ocean.wavecenter.service.resource.recommendcard.processor.hy;

import java.util.List;
import java.util.Optional;

import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.request.RequestGetFamilyUseRecord;
import org.springframework.stereotype.Component;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.RecommendCardRewardBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.response.RewardResultBean;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.processor.RecommendCardRewardProcessor;

/**
 * <AUTHOR>
 * @date 2025/3/21 15:41
 */
@Component
public class HyRecommendCardRewardProcessor implements RecommendCardRewardProcessor {

    @Override
    public RewardResultBean checkCanReward(long operatorUserId, List<RecommendCardRewardBean> rewardBeans) {

        Optional<RecommendCardRewardBean> any = rewardBeans.stream().filter(x -> x.getNum() < 1 || x.getNum() > 99).findAny();
        if (any.isPresent()) {
            return new RewardResultBean().setCode(NUM_NOT_VALID).setRewardResult("分配的数量不能小于1或者大于99");
        }
        return new RewardResultBean().setCode(0).setRewardResult("success");
    }

    @Override
    public void familyUseRecordQueryUserId(List<Long> queryRoomIds, RequestGetFamilyUseRecord request, List<Long> njIds) {

    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.HEI_YE;
    }
}