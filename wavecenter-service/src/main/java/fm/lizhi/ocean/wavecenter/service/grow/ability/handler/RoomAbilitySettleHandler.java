package fm.lizhi.ocean.wavecenter.service.grow.ability.handler;

import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.Period;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.RoomAbility;
import fm.lizhi.ocean.wavecenter.domain.grow.repository.GrowRoomAbilityRepository;
import fm.lizhi.ocean.wavecenter.service.common.manager.IdManager;
import fm.lizhi.ocean.wavecenter.service.grow.ability.constants.AbilityConstant;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.*;
import fm.lizhi.ocean.wavecenter.service.grow.ability.manager.*;
import fm.lizhi.ocean.wavecenter.service.grow.config.GrowConfig;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto.ConditionDTO;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto.TaskTemplateConditionDTO;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.manager.TaskTemplateManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/10 16:54
 */
@Slf4j
@Component
public class RoomAbilitySettleHandler {

    @Autowired
    private GrowSettleFlowManager growSettleFlowManager;
    @Autowired
    private GrowPlayerAbilityManager growPlayerAbilityManager;
    @Autowired
    private GrowConfig growConfig;
    @Autowired
    private IdManager idManager;
    @Autowired
    private GrowRoomAbilityRepository growRoomAbilityRepository;
    @Autowired
    private GrowPlayerMetricManager growPlayerMetricManager;
    @Autowired
    private GrowRoomMetricManager growRoomMetricManager;
    @Autowired
    private TaskTemplateManager taskTemplateManager;
    @Autowired
    private GrowCapabilityMetricRefManager growCapabilityMetricRefManager;

    /**
     * 根据周期结算所有厅
     * @param settlePeriod
     */
    public void settleByPeriod(SettlePeriodDTO settlePeriod){
        // 确认当前周期主播是否已经结算完毕
        PlayerSettleFlowDTO playerSettleFlow = growSettleFlowManager.getPlayerSettleFlow(settlePeriod);
        if (playerSettleFlow == null || AbilityConstant.SETTLE_FLOW_STATUS_DOING.equals(playerSettleFlow.getStatus())) {
            log.info("settleByPeriod playerSettleFlow is doing. playerSettleFlow={}", playerSettleFlow);
            return;
        }

        // 初始化流程
        RoomSettleFlowDTO flow = growSettleFlowManager.getOrInitRoomSettleFlow(settlePeriod);
        if (AbilityConstant.SETTLE_FLOW_STATUS_FINISH.equals(flow.getStatus())) {
            log.info("settle flow is finish");
            return;
        }

        // 查询周期待结算厅名单
        List<Long> roomIds = getToBeSettleRoomIdsByFlow(flow);
        log.info("toBeSettleRoomIds={}", roomIds);
        doSettleRooms(roomIds, settlePeriod, flow);

        // 保存能力关联快照
        saveCapabilityMetricRef(settlePeriod);

        // 结算流程结束
        log.info("settle flow do finish");
        flow.setStatus(AbilityConstant.SETTLE_FLOW_STATUS_FINISH);
        growSettleFlowManager.updateRoomSettleFlow(flow);
    }

    private void saveCapabilityMetricRef(SettlePeriodDTO settlePeriod){
        List<TaskTemplateConditionDTO> templateConditionList = taskTemplateManager.queryEnableTaskTemplateConditionList();

        Set<String> capabilityCodeMetricSet = new HashSet<>();
        List<GrowCapabilityMetricRefDTO> refList = new ArrayList<>();
        for (TaskTemplateConditionDTO template : templateConditionList) {
            for (ConditionDTO conditionDTO : template.getConditionGroup().getConditionList()) {
                String key = template.getCapability().getCapabilityCode() + "_" + conditionDTO.getMetricCode();
                if (capabilityCodeMetricSet.contains(key)) {
                    continue;
                }
                capabilityCodeMetricSet.add(key);

                GrowCapabilityMetricRefDTO ref = new GrowCapabilityMetricRefDTO();
                ref.setCapabilityCode(template.getCapability().getCapabilityCode());
                ref.setMetricCode(conditionDTO.getMetricCode());
                ref.setStartWeekDate(settlePeriod.getStartDate());
                ref.setEndWeekDate(settlePeriod.getEndDate());
                refList.add(ref);
            }
        }

        growCapabilityMetricRefManager.saveRef(refList);
    }

    private void doSettleRooms(List<Long> roomIds, SettlePeriodDTO settlePeriodDTO, RoomSettleFlowDTO flow){
        if (CollectionUtils.isEmpty(roomIds)) {
            return;
        }

        while (CollectionUtils.isNotEmpty(roomIds)) {
            for (Long roomId : roomIds) {
                doSettleRoom(roomId, settlePeriodDTO);
                flow.setLastSettleId(roomId);
                growSettleFlowManager.updateRoomSettleFlow(flow);
            }
            roomIds = getToBeSettleRoomIdsByFlow(flow);
        }
    }

    private void doSettleRoom(Long roomId, SettlePeriodDTO settlePeriodDTO){
        try {
            Integer playerCount = growPlayerAbilityManager.countRoomAbilityPlayer(roomId, settlePeriodDTO.getStartDate(), settlePeriodDTO.getEndDate());
            // 结算能力
            doSettleRoomAbility(roomId, settlePeriodDTO, playerCount);
            // 结算指标
            doSettleMeticValue(roomId, settlePeriodDTO, playerCount);
        } catch (Exception e) {
            log.error("RoomAbilitySettleHandlerRoomId={}, Failed to settle room ability", roomId, e);
        }
    }

    private void doSettleMeticValue(Long roomId, SettlePeriodDTO settlePeriodDTO, Integer playerCount){
        GrowPlayerMetricValueDTO sumMetric = growPlayerMetricManager.queryPlayerMetricSum(roomId, settlePeriodDTO);

        GrowRoomMetricValueDTO avg = new GrowRoomMetricValueDTO();
        avg.setRoomId(roomId);
        avg.setStartWeekDate(settlePeriodDTO.getStartDate());
        avg.setEndWeekDate(settlePeriodDTO.getEndDate());
        avg.setChatUserCnt(intAvg(sumMetric.getChatUserCnt(), playerCount));
        avg.setReplyChatUserCnt(intAvg(sumMetric.getReplyChatUserCnt(), playerCount));
        avg.setReplyChatNewUserCnt(intAvg(sumMetric.getReplyChatNewUserCnt(), playerCount));
        avg.setGiftUserCnt(intAvg(sumMetric.getGiftUserCnt(), playerCount));
        avg.setGiftNewUserCnt(intAvg(sumMetric.getGiftNewUserCnt(), playerCount));
        avg.setAllIncome(bigDecimalAvg(sumMetric.getAllIncome(), playerCount));
        avg.setUpGuestDur(bigDecimalAvg(sumMetric.getUpGuestDur(), playerCount));
        avg.setNewFansUserCnt(intAvg(sumMetric.getNewFansUserCnt(), playerCount));
        avg.setViolationCnt(intAvg(sumMetric.getViolationCnt(), playerCount));
        avg.setCheckInCnt(intAvg(sumMetric.getCheckInCnt(), playerCount));

        growRoomMetricManager.saveRoomMetric(avg);
    }

    private BigDecimal intAvg(Integer sum, Integer cnt){
        return new BigDecimal(sum).divide(new BigDecimal(cnt), 1, RoundingMode.HALF_UP);
    }

    private BigDecimal bigDecimalAvg(BigDecimal sum, Integer cnt){
        return sum.divide(new BigDecimal(cnt), 1, RoundingMode.HALF_UP);
    }

    private void doSettleRoomAbility(Long roomId, SettlePeriodDTO settlePeriodDTO, Integer playerCount){
        log.info("RoomAbilitySettleHandlerRoomId={}, Starting room ability settlement for period: {}", roomId, settlePeriodDTO);
        RoomAbility roomAbility = buildRoomAbility(roomId, settlePeriodDTO, playerCount);

        // 查询主播能力
        List<PlayerAbilityWeekCapabilityDTO> capabilitys = getRoomPlayerWeekCapability(roomId, settlePeriodDTO, 0L);
        log.info("RoomAbilitySettleHandlerRoomId={}, Retrieved initial player capabilities, size: {}", roomId, capabilitys != null ? capabilitys.size() : 0);

        while (CollectionUtils.isNotEmpty(capabilitys)) {
            // key=能力项code value=主播能力项值列表
            Map<String, List<BigDecimal>> capabilityValueMap = capabilitys.stream()
                    .collect(Collectors.groupingBy(
                            PlayerAbilityWeekCapabilityDTO::getCapabilityCode,
                            Collectors.mapping(PlayerAbilityWeekCapabilityDTO::getAbilityValue, Collectors.toList())
                    ));
            // 结算
            roomAbility.addCapabilityScore(capabilityValueMap);
            log.info("RoomAbilitySettleHandlerRoomId={}, Processed batch of capabilities, last processed id: {}", roomId,
                    capabilitys.isEmpty() ? 0L : capabilitys.get(capabilitys.size() - 1).getId());
            capabilitys = getRoomPlayerWeekCapability(roomId, settlePeriodDTO, capabilitys.get(capabilitys.size() - 1).getId());
        }

        // 上周期能力
        log.info("RoomAbilitySettleHandlerRoomId={}, Retrieving previous period ability data", roomId);
        RoomAbility preRoomAbility = growRoomAbilityRepository.getRoomAbility(roomId, buildPrePeriod(settlePeriodDTO));
        roomAbility.comparePrePeriodScore(preRoomAbility);
        log.info("RoomAbilitySettleHandlerRoomId={}, Compared with previous period ability data", roomId);

        // 保存能力
        growRoomAbilityRepository.saveRoomAbility(roomAbility);
        log.info("RoomAbilitySettleHandlerRoomId={}, Successfully saved room ability data", roomId);
    }

    /**
     * 上一个周期
     * @param settlePeriodDTO
     * @return
     */
    private Period buildPrePeriod(SettlePeriodDTO settlePeriodDTO){
        return new Period(DateUtil.getDayBefore(settlePeriodDTO.getStartDate(), 7)
                , DateUtil.getDayBefore(settlePeriodDTO.getEndDate(), 7)
        );
    }

    private List<PlayerAbilityWeekCapabilityDTO> getRoomPlayerWeekCapability(Long roomId, SettlePeriodDTO settlePeriodDTO, Long minId) {
        return growPlayerAbilityManager.getRoomPlayerWeekCapability(roomId
                , settlePeriodDTO.getStartDate()
                , settlePeriodDTO.getEndDate()
                , minId
                , growConfig.getRoomSettlePlayerAbilityPageSize());
    }

    private RoomAbility buildRoomAbility(Long roomId, SettlePeriodDTO settlePeriodDTO, Integer playerCount){
        return new RoomAbility(idManager.genId()
                , ContextUtils.getBusinessEvnEnum().getAppId()
                , roomId
                , new Period(settlePeriodDTO.getStartDate(), settlePeriodDTO.getEndDate())
                , playerCount
        );
    }

    /**
     * 查询流程的待结算主播
     * @param flow
     * @return
     */
    private List<Long> getToBeSettleRoomIdsByFlow(RoomSettleFlowDTO flow){
        // 上一次的结算ID
        Long lastSettleId = flow.getLastSettleId();
        return growPlayerAbilityManager.getRoomIdsWeekHasPlayerAbilityByMinRoomId(flow.getStartWeekDate()
                , flow.getEndWeekDate()
                , lastSettleId
                , growConfig.getAbilitySettlePageSize());
    }

    /**
     * 指定厅结算
     * @param settlePeriod
     * @param roomIds
     */
    public void settleByPeriod(SettlePeriodDTO settlePeriod, List<Long> roomIds){
        for (Long room : roomIds) {
            doSettleRoom(room, settlePeriod);
        }
    }

}
