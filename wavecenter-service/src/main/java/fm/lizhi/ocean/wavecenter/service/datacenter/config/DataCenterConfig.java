package fm.lizhi.ocean.wavecenter.service.datacenter.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @date 2024/4/21 16:08
 */
@Data
@ConfigurationProperties(prefix = "wavecenter-datacenter")
public class DataCenterConfig {

    /**
     * 关键指标趋势图查询天数
     */
    private int indicatorTrendDays = 30;

    /**
     * 首页环比数据预警值, 环比降低达到该值
     */
    private Double ratioWarning = -0.1;

    /**
     * 最大同行表现比例，超过就只展示这个值
     */
    private String maxPerformance = "0.95";

    /**
     * 榜单数据是否查询支付接口
     */
    private boolean rankQueryPay = false;



}
