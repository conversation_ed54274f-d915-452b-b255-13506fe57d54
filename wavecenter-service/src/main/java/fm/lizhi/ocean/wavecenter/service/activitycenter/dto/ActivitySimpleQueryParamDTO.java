package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyTypeEnum;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ActivitySimpleQueryParamDTO {

    /**
     * 页数
     */
    private Integer pageNo;

    /**
     * 每页数量
     */
    private Integer pageSize;

    private Integer appId;


    /**
     * 最大活动开始时间
     */
    private Date maxStartTime;

    /**
     * 活动结束时间
     */
    private Date minStartTime;

    /**
     * 活动状态
     */
    private Integer activityStatus;

    /**
     * 活动名
     */
    private String name;

    /**
     * 厅主ID
     */
    private Long njId;

    /**
     * 工会ID
     */
    private Long familyId;

    /**
     * 申请人/报名人ID
     */
    private Long applyUserId;

    /**
     * 审核状态
     */
    private List<Integer> auditStatus;

    /**
     * 申报类型
     *
     * @see ActivityApplyTypeEnum
     */
    private Integer applyType;

    /**
     * 是否已删除, 如果不传则查询全部
     */
    private Integer deleted;

    /**
     * 活动提报开始时间
     */
    private Date applyStartTime;

    /**
     * 活动提报名结束时间
     */
    private Date applyEndTime;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 活动分类ID列表
     */
    private List<Long> classIds;

    /**
     * 活动ID
     */
    private Long activityId;
}
