package fm.lizhi.ocean.wavecenter.service.live.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.live.bean.*;
import fm.lizhi.ocean.wavecenter.api.live.service.LiveRoomCheckInService;
import fm.lizhi.ocean.wavecenter.api.user.bean.RoomBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.live.manager.LiveRoomCheckInManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@ServiceProvider
public class LiveRoomCheckInServiceImpl implements LiveRoomCheckInService {


    @Autowired
    private LiveRoomCheckInManager liveRoomCheckInManager;

    @Autowired
    private UserManager userManager;


    @Override
    public Result<RoomDayCalendarRes> roomCalendar(RoomDayCalendarReq req) {
        LogContext.addReqLog("req={}", JsonUtil.dumps(req));
        LogContext.addResLog("req={}", JsonUtil.dumps(req));

        return ResultHandler.handle(req.getAppId(), () -> {
            RoomDayCalendarRes roomDayCalendarRes = liveRoomCheckInManager.roomCalendar(req);
            return RpcResult.success(roomDayCalendarRes);
        });
    }

    @Override
    public Result<RoomHourCheckDetailRes> hourDetail(RoomHourCheckDetailReq req) {
        LogContext.addReqLog("req={}", JsonUtil.dumps(req));
        LogContext.addResLog("req={}", JsonUtil.dumps(req));
        return ResultHandler.handle(req.getAppId(), () -> {
            RoomHourCheckDetailRes roomHourCheckDetailRes = liveRoomCheckInManager.hourDetail(req);

            UserBean host = roomHourCheckDetailRes.getHost();
            if (host == null) {
                return RpcResult.success(roomHourCheckDetailRes);
            }
            Set<Long> userIds = new HashSet<>();

            Set<Long> checkPlayerUserIdSet = roomHourCheckDetailRes.getCheckPlayer().stream().map(PlayerCheckInDetailRes::getId).collect(Collectors.toSet());
            Set<Long> unCheckPlayerUserIdSet = roomHourCheckDetailRes.getUnCheckPlayer().stream().map(PlayerCheckInDetailRes::getId).collect(Collectors.toSet());
            userIds.add(host.getId());
            userIds.addAll(checkPlayerUserIdSet);
            userIds.addAll(unCheckPlayerUserIdSet);
            userIds.add(roomHourCheckDetailRes.getRoom().getId());

            List<SimpleUserDto> userInfoList = userManager.getSimpleUserByIds(new ArrayList<>(userIds));
            Map<Long, SimpleUserDto> userInfoMap = userInfoList.stream().collect(Collectors.toMap(SimpleUserDto::getId, v -> v));

            for (PlayerCheckInDetailRes res : roomHourCheckDetailRes.getCheckPlayer()) {
                SimpleUserDto simpleUserDto = userInfoMap.get(res.getId());
                if (simpleUserDto != null) {
                    res.setBand(simpleUserDto.getBand());
                    res.setName(simpleUserDto.getName());
                }
            }

            for (PlayerCheckInDetailRes res : roomHourCheckDetailRes.getUnCheckPlayer()) {
                SimpleUserDto simpleUserDto = userInfoMap.get(res.getId());
                if (simpleUserDto != null) {
                    res.setBand(simpleUserDto.getBand());
                    res.setName(simpleUserDto.getName());
                }
            }
            SimpleUserDto hostUserInfo = userInfoMap.get(host.getId());
            if (hostUserInfo != null) {
                host.setBand(hostUserInfo.getBand());
                host.setName(hostUserInfo.getName());
                host.setPhoto(hostUserInfo.getAvatar());
            }
            SimpleUserDto roomUserInfo = userInfoMap.get(req.getRoomId());
            if (roomUserInfo != null) {
                RoomBean room = roomHourCheckDetailRes.getRoom();
                room.setBand(roomUserInfo.getBand());
                room.setName(roomUserInfo.getName());
                room.setPhoto(roomUserInfo.getAvatar());
            }
            return RpcResult.success(roomHourCheckDetailRes);
        });
    }


    @Override
    public Result<LRCSRoomDayStatsRes> roomDayStats(RoomDayCheckStatsReq req) {
        LogContext.addReqLog("req={}", JsonUtil.dumps(req));
        LogContext.addResLog("req={}", JsonUtil.dumps(req));
        return ResultHandler.handle(req.getAppId(), () -> {
            int total = MyDateUtil.getRangeTotal(req.getStartDate(), req.getEndDate());
            LRCSRoomDayStatsRes roomDayStatsResResult = liveRoomCheckInManager.roomDayStats(req);
            roomDayStatsResResult.setTotal(total);
            List<RoomDayStatsRes> roomDayStatsRes = roomDayStatsResResult.getList();
            if (CollectionUtils.isEmpty(roomDayStatsRes)) {
                return RpcResult.success(roomDayStatsResResult);
            }
            Set<Long> userIds = roomDayStatsRes.stream().map(e -> e.getPlayer().getId()).collect(Collectors.toSet());
            List<SimpleUserDto> userInfoList = userManager.getSimpleUserByIds(new ArrayList<>(userIds));
            Map<Long, SimpleUserDto> userInfoMap = userInfoList.stream().collect(Collectors.toMap(SimpleUserDto::getId, v -> v));
            for (RoomDayStatsRes roomDayStatsRe : roomDayStatsRes) {
                UserBean player = roomDayStatsRe.getPlayer();
                SimpleUserDto simpleUserDto = userInfoMap.get(player.getId());
                if (simpleUserDto != null) {
                    player.setBand(simpleUserDto.getBand());
                    player.setName(simpleUserDto.getName());
                    player.setPhoto(simpleUserDto.getAvatar());
                }
            }
            return RpcResult.success(roomDayStatsResResult);
        });
    }

    @Override
    public Result<RoomDayStatsSummaryRes> roomDayStatsSummary(RoomDayCheckStatsReq req) {
        LogContext.addReqLog("req={}", JsonUtil.dumps(req));
        LogContext.addResLog("req={}", JsonUtil.dumps(req));
        return ResultHandler.handle(req.getAppId(), () -> {
            RoomDayStatsSummaryRes roomDayStatsSummaryRes = liveRoomCheckInManager.roomDayStatsSummary(req);
            return RpcResult.success(roomDayStatsSummaryRes);
        });
    }

    @Override
    public Result<LRCSRoomHourStatsRes> roomHourStats(RoomHourCheckStatsReq req) {
        LogContext.addReqLog("req={}", JsonUtil.dumps(req));
        LogContext.addResLog("req={}", JsonUtil.dumps(req));
        return ResultHandler.handle(req.getAppId(), () -> {
            LRCSRoomHourStatsRes roomHourStatsResResult = liveRoomCheckInManager.roomHourStats(req);
            List<RoomHourStatsRes> roomHourStatsRes = roomHourStatsResResult.getList();
            if (CollectionUtils.isEmpty(roomHourStatsRes)) {
                return RpcResult.success(roomHourStatsResResult);
            }
            Set<Long> userIds = roomHourStatsRes.stream().map(e -> e.getPlayer().getId()).collect(Collectors.toSet());
            List<SimpleUserDto> userInfoList = userManager.getSimpleUserByIds(new ArrayList<>(userIds));
            Map<Long, SimpleUserDto> userInfoMap = userInfoList.stream().collect(Collectors.toMap(SimpleUserDto::getId, v -> v));
            for (RoomHourStatsRes roomHourStatsRe : roomHourStatsRes) {
                UserBean player = roomHourStatsRe.getPlayer();
                SimpleUserDto simpleUserDto = userInfoMap.get(player.getId());
                if (simpleUserDto != null) {
                    player.setBand(simpleUserDto.getBand());
                    player.setName(simpleUserDto.getName());
                    player.setPhoto(simpleUserDto.getAvatar());
                }
            }
            return RpcResult.success(roomHourStatsResResult);
        });
    }

    @Override
    public Result<RoomHourStatsSummaryRes> roomHourStatsSummary(RoomHourCheckStatsReq req) {
        LogContext.addReqLog("req={}", JsonUtil.dumps(req));
        LogContext.addResLog("req={}", JsonUtil.dumps(req));
        return ResultHandler.handle(req.getAppId(), () -> {
            RoomHourStatsSummaryRes roomHourStatsSummaryRes = liveRoomCheckInManager.roomHourStatsSummary(req);
            return RpcResult.success(roomHourStatsSummaryRes);
        });
    }
}
