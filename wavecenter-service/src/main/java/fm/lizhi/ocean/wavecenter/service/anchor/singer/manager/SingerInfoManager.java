package fm.lizhi.ocean.wavecenter.service.anchor.singer.manager;

import java.util.List;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseGetAllSingerStatics;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.EliminationSingerByNjParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.PageSingerInfoParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.PassSingerByNjParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SaveSingerInfoParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerCountInHallDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerInfoDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerRoomDetailDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.UpdateSingerInfoParamDTO;

/**
 * 歌手库
 *
 * <AUTHOR>
 */
public interface SingerInfoManager {


    /**
     * 保存歌手信息
     */
    Boolean saveSingerInfo(SaveSingerInfoParamDTO param);

    /**
     * 批量获取厅下的歌手信息
     *
     * @param njId         厅ID
     * @param singerType   歌手类型, null 的话查全部
     * @param singerStatus 歌手状态, null 的话查全部
     * @return 歌手信息列表
     */
    List<SingerInfoDTO> getSingerInfoByNjId(int appId, Long njId, SingerTypeEnum singerType, SingerStatusEnum singerStatus);

    /**
     * 通过厅下的歌手
     *
     * @param param
     * @return
     */
    boolean passSingerByNjId(PassSingerByNjParamDTO param);

    /**
     * 淘汰厅下的歌手
     *
     * @return
     */
    boolean eliminateSingerByNjId(EliminationSingerByNjParamDTO param);

    /**
     * 淘汰歌手
     */
    Boolean eliminateSinger(int appId, List<Long> ids, String operator, String eliminateReason, Boolean isManual);

    /**
     * 升级歌手
     */
    Boolean upgradeSinger(int appId, List<Long> ids, SingerTypeEnum singerType, String operator);


    /**
     * 根据用户ID查询歌手信息列表
     *
     * @param appId        应用ID
     * @param userId       用户ID
     * @param singerStatus 歌手状态
     * @return 歌手信息列表
     */
    List<SingerInfoDTO> getSingerInfoByUserId(int appId, Long userId, SingerStatusEnum singerStatus);

    List<SingerInfoDTO> getSingerInfoByUserId(int appId, Long userId, List<SingerStatusEnum> singerStatusList);

    /**
     * 根据用户ID和应用ID以及歌手类型查询歌手信息
     *
     * @param appId      应用ID
     * @param userId     用户ID
     * @param singerType 歌手类型
     * @return 歌手信息列表
     */
    SingerInfoDTO getSingerInfo(int appId, Long userId, Integer singerType);

    /**
     * 判断用户是否是歌手
     *
     * @param appId  应用ID
     * @param userId 用户ID
     * @return 是否是歌手
     */
    boolean isSinger(Integer appId, Long userId);

    /**
     * 获取厅主下的歌手总数
     *
     * @param njId  厅主ID
     * @param appId appID
     * @return 返回生效中和审核中的歌手数量，第一个值为生效中数量，第二个值为审核中数量
     */
    SingerCountInHallDTO singerTotalCountInHall(Integer appId, Long njId);

    /**
     * 获取厅主下的歌手详情
     *
     * @param njId         厅主ID
     * @param pageNo       页码
     * @param pageSize     每页大小
     * @param orderMetrics
     * @param orderType
     * @return 歌手详情列表
     */
    PageBean<SingerRoomDetailDTO> singerRoomDetails(Integer appId, Long njId, Integer pageNo, Integer pageSize, String orderMetrics, OrderType orderType);

    /**
     * 获取全平台歌手汇总信息
     *
     * @param appId 应用ID
     * @return 全平台歌手汇总信息
     */
    ResponseGetAllSingerStatics getAllSingerStatics(Integer appId);

    /**
     * 分页查询歌手信息
     *
     * @param param 分页查询参数
     * @return 分页查询结果
     */
    PageBean<SingerInfoDTO> pageSingerInfo(PageSingerInfoParamDTO param);


    /**
     * 批量获取歌手信息
     *
     * @param userIds          用户ID列表
     * @param singerStatusList 可以为 null，null 则查全部
     * @return 歌手信息列表
     */
    List<SingerInfoDTO> getSingerInfoByUserIds(List<Long> userIds, int appId, List<SingerStatusEnum> singerStatusList);

    /**
     * 批量获取歌手信息
     *
     * @param userIds          用户ID列表
     * @param singerStatusList 可以为 null，null 则查全部
     * @return 歌手信息列表
     */
    List<SingerInfoDTO> getSingerInfoByUserIds(List<Long> userIds, int appId, List<SingerStatusEnum> singerStatusList, SingerTypeEnum singerType);


    /**
     * 添加淘汰标记
     */
    void addEliminateSingerTag(int appId, Long userId);

    /**
     * 修改歌手信息
     *
     * @param param 修改参数
     * @return true: 成功，false: 失败
     */
    boolean updateSingerInfo(UpdateSingerInfoParamDTO param);

    /**
     * 根据奖励发放状态批量获取歌手
     *
     * @param rewardsIssued 奖励发放状态
     */
    List<SingerInfoDTO> getRewardsIssuedByUserIds(List<Long> userIds, int appId, boolean rewardsIssued);

    /**
     * 判断用户是否可以申请歌手认证
     *
     * @param appId      应用ID
     * @param userId     用户ID
     * @param singerType 申请的歌手类型
     * @return true: 可以申请，false: 不可以申请
     */
    boolean isCanApplySingerVerify(int appId, Long userId, Integer singerType);

    /**
     * 根据歌手类型获取歌手信息
     *
     * @param appId          应用ID
     * @param userId         用户ID
     * @param singerTypeList 歌手类型列表
     * @return 结果
     */
    List<SingerInfoDTO> getSingerInfoListByType(int appId, Long userId, List<Integer> singerTypeList);

    /**
     * 根据歌手记录 ID 查询歌手信息
     *
     * @param appId
     * @param ids
     * @return
     */
    List<SingerInfoDTO> getSingerInfoByIds(int appId, List<Long> ids);

    /**
     * 根据应用ID、歌手状态和类型，分页查询出歌手信息
     *
     * @param appId
     * @param singerStatus
     * @param singerType
     * @param pageNo
     * @param pageSize
     * @return
     */
    PageBean<SingerInfoDTO> pageSingerInfoByStatusAndType(int appId, SingerStatusEnum singerStatus, SingerTypeEnum singerType, int pageNo, int pageSize);

    /**
     * 批量修改歌手状态
     *
     * @param appId               应用ID
     * @param ids                 歌手记录ID
     * @param currentSingerStatus 当前歌手状态
     * @param targetSingerStatus  目标歌手状态
     * @param operator            操作人
     * @return 结果
     */
    boolean batchUpdateSingerStatus(int appId, List<Long> ids, SingerStatusEnum currentSingerStatus, SingerStatusEnum targetSingerStatus, String operator);
}
