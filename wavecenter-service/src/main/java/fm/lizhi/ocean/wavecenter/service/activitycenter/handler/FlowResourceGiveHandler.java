package fm.lizhi.ocean.wavecenter.service.activitycenter.handler;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.DeleteOfficialSeatParamDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.FlowResourceContext;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.GiveFlowResourceResDTO;

public interface FlowResourceGiveHandler {

    /**
     * 发放流量资源
     *
     * @param context 上下文
     * @return 结果
     */
    Result<GiveFlowResourceResDTO> giveFlowResource(FlowResourceContext context);

    /**
     * 取消发放流量资源
     * @param param 参数
     * @return 结果
     */
    Result<Void> cancelGiveFlowResource(DeleteOfficialSeatParamDTO param);

    /**
     * 获取资源code
     *
     * @return 资源code
     */
    String getResourceCode();

    /**
     * 失败
     */
    int GIVE_FLOW_RESOURCE_FAIL = 10;

    /**
     * 缺少配置
     */
    int GIVE_FLOW_RESOURCE_NO_CONFIG = 11;

    /**
     * 缺少直播信息
     */
    int GIVE_FLOW_RESOURCE_NO_LIVE_INFO = 12;

}
