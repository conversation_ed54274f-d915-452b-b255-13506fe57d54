package fm.lizhi.ocean.wavecenter.service.activitycenter.handler;

import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityRuleConfigBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.LastWeekHallIncomeThresholdRuleBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ReportCountRuleBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.RequestActivityApplyBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyRuleEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyTypeEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityApplyService;
import fm.lizhi.ocean.wavecenter.api.common.constants.DateType;
import fm.lizhi.ocean.wavecenter.api.common.constants.MetricsEnum;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.IndicatorBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomGetKeyIndicatorsParamBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.DateTimeUtils;
import fm.lizhi.ocean.wavecenter.service.activitycenter.constatns.ActivityApplyErrorTipConstant;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityApplyContext;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityParamDTO;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityRuleManager;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.RoomIncomeDetailDto;
import fm.lizhi.ocean.wavecenter.service.datacenter.handler.RoomDataHandler;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.PlayerDataManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Component
public class LastWeekHallIncomeRuleFilter implements ApplyRuleFilter {

    @Autowired
    private ActivityRuleManager activityRuleManager;

    @Autowired
    private RoomDataHandler roomDataHandler;


    @Override
    public Result<Void> filter(ActivityApplyContext context, ActivityRuleConfigBean rule) {
        ActivityParamDTO paramBean = context.getParamDTO();
        if (paramBean.getApplyType() != null && paramBean.getApplyType() == ActivityApplyTypeEnum.OFFICIAL_APPLY) {
            //非主播提报，不校验
            return RpcResult.success();
        }

        Date lastWeekStartTime = DateTimeUtils.getLastWeekStartTime();
        Date lastWeekEndTime = DateUtil.getDayAfter(lastWeekStartTime, 6);
        RoomGetKeyIndicatorsParamBean keyIndicatorsParamBean = RoomGetKeyIndicatorsParamBean.builder()
                .valueMetrics(Lists.newArrayList(MetricsEnum.ALL_INCOME.getValue()))
                .appId(paramBean.getAppId())
                .roomId(paramBean.getNjId())
                .startDate(DateUtil.formatDateToString(lastWeekStartTime, DateUtil.date_2))
                .endDate(DateUtil.formatDateToString(lastWeekEndTime, DateUtil.date_2))
                .dateType(DateType.WEEK)
                .build();
        List<IndicatorBean> keyIndicators = roomDataHandler.getKeyIndicators(keyIndicatorsParamBean);
        Optional<IndicatorBean> resultOption = keyIndicators.stream().filter(indicatorBean -> indicatorBean.getMetric().equals(MetricsEnum.ALL_INCOME.getValue())).findAny();
        LastWeekHallIncomeThresholdRuleBean ruleBean = activityRuleManager.getRuleBean(ActivityApplyRuleEnum.LAST_WEEK_HALL_INCOME_THRESHOLD, rule.getRuleJson());
        if (ruleBean != null) {
            Long income = ruleBean.getIncome();
            String msg = String.format(ActivityApplyErrorTipConstant.APPLY_ACTIVITY_APPLY_INCOME_LIMIT, income);
            if (income > 0 && !resultOption.isPresent()) {
                //有门槛，但是没有收入，就不让过了
                log.warn("income:{} less than threshold:{}, njId={}", 0, income, paramBean.getNjId());
                return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, msg);
            }

            //无门槛，也没有收入，那就放行吧
            if (!resultOption.isPresent()) {
                return RpcResult.success();
            }

            double value = Double.parseDouble(resultOption.get().getIndicator());
            if (value < income) {
                log.warn("income:{} less than threshold:{}, njId={}", value, income, paramBean.getNjId());
                //收入没有达标
                return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, msg);
            }
        }
        return RpcResult.success();
    }

    @Override
    public ActivityApplyRuleEnum getRuleTypeEnum() {
        return ActivityApplyRuleEnum.LAST_WEEK_HALL_INCOME_THRESHOLD;
    }
}
