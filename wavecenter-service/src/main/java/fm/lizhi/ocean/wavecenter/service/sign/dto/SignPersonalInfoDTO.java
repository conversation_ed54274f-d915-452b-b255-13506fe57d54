package fm.lizhi.ocean.wavecenter.service.sign.dto;

import fm.lizhi.ocean.wavecenter.api.sign.constant.SignAuthStatusEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/10/23 15:55
 */
@Data
@Accessors(chain = true)
public class SignPersonalInfoDTO {

    /**
     * 实名身份证号
     */
    private String identityNo;

    /**
     * 实名认证状态
     */
    private SignAuthStatusEnum authStatus;

}
