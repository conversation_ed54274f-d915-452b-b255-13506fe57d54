package fm.lizhi.ocean.wavecenter.service.activitycenter.impl;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseGetActivityNotice;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityNoticeService;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityLevelConfigBean;
import fm.lizhi.ocean.wavecenter.api.live.constants.RoomCategoryEnum;
import fm.lizhi.ocean.wavecenter.api.live.constants.RoomCategoryOptionConstants;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.activitycenter.convert.ActivityLevelConvert;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityNoticeConfigManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityNoticeConfigDTO;
import fm.lizhi.ocean.wavecenter.service.live.handler.LiveRoomHandler;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

@ServiceProvider
@Slf4j
public class ActivityNoticeServiceImpl implements ActivityNoticeService {

    @Autowired
    private ActivityNoticeConfigManager activityNoticeConfigManager;

    @Autowired
    private LiveRoomHandler liveRoomHandler;

    @Override
    public Result<ResponseGetActivityNotice> getNoticeConfig(Integer appId, Long userId) {

        return ResultHandler.handle(appId, () -> {
            if (userId == null) {
                return RpcResult.fail(ActivityNoticeService.GET_NOTICE_PARAM_INVALID, "参数异常，查询失败");
            }

            // 获取当前用户的厅品类
            Optional<RoomCategoryEnum> categoryOptional = liveRoomHandler.getUserSignRoomCategory(userId);

            // 获取公告
            Optional<ActivityNoticeConfigDTO> result = activityNoticeConfigManager.getNoticeConfig(appId,
                    categoryOptional.map(RoomCategoryEnum::getValue).orElse(RoomCategoryOptionConstants.UNLIMITED_VALUE)
            );

            if (result.isPresent()){
                ActivityNoticeConfigDTO noticeConfig = result.get();
                ResponseGetActivityNotice response = new ResponseGetActivityNotice();
                response.setId(noticeConfig.getId());
                response.setOperator(noticeConfig.getOperator());
                response.setContent(noticeConfig.getContent());
                response.setAppId(noticeConfig.getAppId());
                return RpcResult.success(response);
            }
            return RpcResult.success();

        });

    }
}
