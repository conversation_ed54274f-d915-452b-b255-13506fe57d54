package fm.lizhi.ocean.wavecenter.service.award.family.config;

import fm.lizhi.ocean.lamp.common.config.annotation.JsonStringProperty;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@ConfigurationProperties(prefix = "wavecenter-award.family")
@Data
public class FamilyAwardConfig {

    /**
     * 等级奖励结算任务默认的应用id列表
     */
    private List<Integer> levelAwardJobAppIds = Arrays.asList(BusinessEvnEnum.HEI_YE.getAppId(), BusinessEvnEnum.XIMI.getAppId());

    /**
     * 其他奖励结算任务默认的应用id列表
     */
    private List<Integer> otherAwardJobAppIds = Collections.singletonList(BusinessEvnEnum.PP.getAppId());

    /**
     * 其他奖励规则配置
     */
    @JsonStringProperty
    private OtherAwardConfig otherAwardConfig = new OtherAwardConfig();

    /**
     * 是否允许重新发放已成功的奖励, 该配置仅用于测试环境方便测试同事手动执行重复发放
     */
    private boolean reDeliverSuccessEnabled = false;
}
