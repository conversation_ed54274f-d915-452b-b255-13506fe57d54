package fm.lizhi.ocean.wavecenter.service.activitycenter.processor.pp;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityResourceGiveDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.DressUpGiveContext;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.SendDecorateParamDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.processor.IBackgroundGiveProcess;
import org.springframework.stereotype.Component;

@Component
public class PpBackgroundGiveProcess implements IBackgroundGiveProcess {

    @Override
    public void fillSendParam(DressUpGiveContext context, SendDecorateParamDTO param) {
        ActivityResourceGiveDTO resourceGiveDTO = context.getResourceGiveDTO();
        param.setCount(1);
        param.setCoverValid(false);
        //PP是按照开始结束时间来设置有效期
        param.setBeginTime(resourceGiveDTO.getStartTime().getTime());
        param.setEndTime(resourceGiveDTO.getEndTime().getTime());
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.PP;
    }
}
