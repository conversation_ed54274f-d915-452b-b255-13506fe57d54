package fm.lizhi.ocean.wavecenter.service.user.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.ContextRequest;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.FamilyTypeEnum;
import fm.lizhi.ocean.wavecenter.api.user.bean.*;
import fm.lizhi.ocean.wavecenter.api.user.request.RequestGetAllGuildRoomsV2;
import fm.lizhi.ocean.wavecenter.api.user.request.RequestSearchUser;
import fm.lizhi.ocean.wavecenter.api.user.response.ResponseSearchUser;
import fm.lizhi.ocean.wavecenter.api.user.service.UserCommonService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.dto.PageDto;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.permissions.manager.RoleManager;
import fm.lizhi.ocean.wavecenter.service.sign.dto.SignSettleDTO;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.SettleManager;
import fm.lizhi.ocean.wavecenter.service.user.convert.UserCommonConvert;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.sign.manager.RoomManager;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/4/10 11:56
 */
@ServiceProvider
public class UserCommonServiceImpl implements UserCommonService {

    @Autowired
    private RoomManager roomManager;
    @Autowired
    private UserManager userManager;
    @Autowired
    private RoleManager roleManager;
    @Autowired
    private FamilyManager familyManager;
    @Autowired
    private SettleManager settleManager;

    @Override
    public Result<UserRoleInfoBean> getUserRoleInfo(int appId, long userId) {
        LogContext.addReqLog("appId={}, userId={}", appId, userId);
        LogContext.addResLog("appId={}, userId={}", appId, userId);
        return ResultHandler.handle(appId, ()->{
            List<SimpleUserDto> userList = userManager.getSimpleUserByIds(Lists.newArrayList(userId));
            if (CollectionUtils.isEmpty(userList)) {
                return RpcResult.fail(USER_NOT_FOUND);
            }
            UserRoleInfoBean userRoleInfoBean = UserCommonConvert.I.simpleUserDto2UserRoleBean(userList.get(0));
            String roleCode = roleManager.getUserRoleCode(userId);
            userRoleInfoBean.setRoleCode(roleCode);
            return RpcResult.success(userRoleInfoBean);
        });
    }

    @Override
    public Result<PageBean<RoomSignBean>> getAllGuildRooms(int appId, long familyId, int pageNo, int pageSize) {
        LogContext.addReqLog("appId={},familyId={},pageNo={},pageSize={}", appId, familyId, pageNo, pageSize);
        LogContext.addResLog("appId={},familyId={},pageNo={},pageSize={}", appId, familyId, pageNo, pageSize);
        return ResultHandler.handle(appId, ()->{
            PageBean<RoomSignBean> pageDto = roomManager.getAllGuildRooms(familyId, null, pageNo, pageSize);
            return RpcResult.success(pageDto);
        });
    }

    @Override
    public Result<PageBean<RoomSignBean>> getAllGuildRoomsV2(RequestGetAllGuildRoomsV2 request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        PageBean<RoomSignBean> pageDto = roomManager.getAllGuildRooms(request.getFamilyId()
                , request.getRoomIds()
                , request.getPageNo(), request.getPageSize());
        return RpcResult.success(pageDto);
    }

    @Override
    public Result<PageBean<PlayerSignBean>> getAllRoomPlayers(int appId, long roomId, int pageNo, int pageSize) {
        LogContext.addReqLog("appId={},roomId={},pageNo={},pageSize={}", appId, roomId, pageNo, pageSize);
        LogContext.addResLog("appId={},roomId={},pageNo={},pageSize={}", appId, roomId, pageNo, pageSize);
        return ResultHandler.handle(appId, ()->{
            PageBean<PlayerSignBean> pageBean = roomManager.getAllRoomPlayers(roomId, pageNo, pageSize);
            return RpcResult.success(pageBean);
        });
    }

    @Override
    public Result<PlayerSignBean> getPlayerSignInfo(int appId, Long familyId, Long roomId, long userId) {
        LogContext.addReqLog("appId={},familyId={},roomId={},userId={}", appId, familyId, roomId, userId);
        LogContext.addResLog("appId={},familyId={},roomId={},userId={}", appId, familyId, roomId, userId);
        return ResultHandler.handle(appId, ()->{
            Optional<PlayerSignBean> playerSignInfoOp = roomManager.getPlayerSignInfo(familyId, roomId, userId);
            return playerSignInfoOp.map(RpcResult::success)
                    .orElseGet(()->RpcResult.fail(USER_NOT_FOUND));
        });
    }

    @Override
    public Result<PageBean<PlayerSignBean>> getAllGuildPlayer(QueryGuildPlayerBean req,int pageNo, int pageSize) {
        LogContext.addReqLog("req={},pageNo={},pageSize={}", JSONObject.toJSONString(req), pageNo, pageSize);
        LogContext.addResLog("req={},pageNo={},pageSize={}", JSONObject.toJSONString(req), pageNo, pageSize);
        return ResultHandler.handle(req.getAppId(), ()->{
            PageDto<PlayerSignBean> pageDto = roomManager.getAllGuildPlayer(req, pageNo, pageSize);
            return RpcResult.success(PageBean.of(pageDto.getTotal(), pageDto.getList()));
        });
    }

    @Override
    public Result<UserBean> getUserById(int appId, long userId) {
        LogContext.addReqLog("appId={},userId={}", appId, userId);
        LogContext.addResLog("appId={},userId={}", appId, userId);
        return ResultHandler.handle(appId, ()->{
            List<SimpleUserDto> userList = userManager.getSimpleUserByIds(Lists.newArrayList(userId));
            if (CollectionUtils.isEmpty(userList)) {
                return RpcResult.fail(USER_NOT_FOUND);
            }
            return RpcResult.success(UserCommonConvert.I.simpleUserDto2userBean(userList.get(0)));
        });
    }

    @Override
    public Result<List<UserBean>> getUserByIds(int appId, List<Long> userIds) {
        return ResultHandler.handle(appId, ()->{
            List<SimpleUserDto> userList = userManager.getSimpleUserByIds(userIds);
            if (CollectionUtils.isEmpty(userList)) {
                return RpcResult.fail(USER_NOT_FOUND);
            }
            return RpcResult.success(UserCommonConvert.I.simpleUserDtos2userBeans(userList));
        });
    }

    @Override
    public Result<UserBean> getUserByBand(int appId, String band) {
        LogContext.addReqLog("appId={},band={}", appId, band);
        LogContext.addResLog("appId={},band={}", appId, band);
        return ResultHandler.handle(appId, ()->{
            Optional<SimpleUserDto> userInfoOption = userManager.getUserInfoByBand(band);
            return userInfoOption.map(simpleUserDto -> RpcResult.success(UserCommonConvert.I.simpleUserDto2userBean(simpleUserDto))).orElseGet(() -> RpcResult.fail(USER_NOT_FOUND));
        });
    }

    @Override
    public Result<ResponseSearchUser> searchUser(RequestSearchUser request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        return ResultHandler.handle(request.getAppId(), ()->{

            //查询用户基本信息
            Optional<SimpleUserDto> userInfoOp = userManager.getUserInfoByBand(request.getBand());
            if (!userInfoOp.isPresent()) {
                LogContext.addResLog("band={} not found", request.getBand());
                return RpcResult.fail(USER_NOT_FOUND);
            }

            UserRoleInfoBean userRoleInfoBean = new UserRoleInfoBean();
            SimpleUserDto userInfo = userInfoOp.get();
            userRoleInfoBean.setId(userInfo.getId());
            userRoleInfoBean.setBand(userInfo.getBand());
            userRoleInfoBean.setPhoto(userInfo.getAvatar());
            userRoleInfoBean.setName(userInfo.getName());
            ResponseSearchUser res = new ResponseSearchUser().setUserInfo(userRoleInfoBean);

            //查询用户角色
            UserInFamilyBean userInFamily = familyManager.getUserInFamily(userInfo.getId());

            if (userInFamily.isFamily()) {
                userRoleInfoBean.setRoleCode(RoleEnum.FAMILY.getRoleCode());
                SearchFamilyBean searchFamilyInfo = getSearchFamilyInfo(userInFamily.getFamilyId());
                if (!Objects.equals(FamilyTypeEnum.C_FAMILY.getCode(), searchFamilyInfo.getFamilyType())) {
                    LogContext.addReqLog("not pgc");
                    return RpcResult.fail(USER_IS_UGC);
                }
                res.setFamilyInfo(searchFamilyInfo);
                return RpcResult.success(res);
            }

            if (userInFamily.isRoom()) {
                userRoleInfoBean.setRoleCode(RoleEnum.ROOM.getRoleCode());
                //查询厅结算方式
                res.setRoomInfo(getSearchRoomInfo(userInfo.getId()));
            }

            return RpcResult.success(res);
        });
    }

    private SearchRoomBean getSearchRoomInfo(Long njId) {
        SearchRoomBean roomBean = new SearchRoomBean();
        Optional<SignSettleDTO> settleOp = settleManager.querySettleByNj(njId);
        if (!settleOp.isPresent()) {
            return roomBean;
        }

        Optional<FamilyBean> userFamily = familyManager.getUserFamily(njId);
        userFamily.ifPresent(familyBean -> roomBean.setFamilyType(familyBean.getFamilyType()));

        return roomBean.setSettleType(settleOp.get().getSettleType())
                .setSettlePercentage(settleOp.get().getSettlePercentage());
    }

    private SearchFamilyBean getSearchFamilyInfo(Long familyId){
        //查询家族信息
        Optional<FamilyBean> familyOp = familyManager.getFamily(ContextUtils.getBusinessEvnEnum().appId(), familyId);
        if (!familyOp.isPresent()) {
            return null;
        }

        SearchFamilyBean family = new SearchFamilyBean();
        family.setFamilyId(familyId);
        family.setFamilyName(familyOp.get().getFamilyName());
        family.setFamilyIntro(familyOp.get().getFamilyNote());
        family.setFamilyIconUrl(familyOp.get().getFamilyIconUrl());
        family.setCreateTime(familyOp.get().getCreateTime());
        family.setFamilyType(familyOp.get().getFamilyType());

        return family;
    }

    @Override
    public Result<UserBean> getUserByKeyWord(ContextRequest contextRequest, String keyword) {
        LogContext.addReqLog("contextRequest={},keyword={}", contextRequest, keyword);
        LogContext.addResLog("contextRequest={},keyword={}", contextRequest, keyword);
        
        Optional<SimpleUserDto> userInfoOp = userManager.getUserByKeyWord(keyword);
        return userInfoOp.map(simpleUserDto -> RpcResult.success(UserCommonConvert.I.simpleUserDto2userBean(simpleUserDto))).orElseGet(() -> RpcResult.fail(USER_NOT_FOUND));
        
    }
}
