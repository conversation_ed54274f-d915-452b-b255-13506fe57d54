package fm.lizhi.ocean.wavecenter.service.datacenter.dto;

import fm.lizhi.ocean.wavecenter.common.utils.WcAssert;
import lombok.Builder;
import lombok.Getter;

import java.util.Date;

/**
 * 考核周期
 * <AUTHOR>
 * @date 2024/4/18 18:16
 */
@Getter
@Builder
public class AssessTimeDto {

    private Date startDate;

    private Date endDate;

    public static class AssessTimeDtoBuilder {
        public AssessTimeDto build(){
            WcAssert.notNull(startDate, "start date is null");
            WcAssert.notNull(endDate, "end date is null");
            return new AssessTimeDto(startDate, endDate);
        }
    }

}
