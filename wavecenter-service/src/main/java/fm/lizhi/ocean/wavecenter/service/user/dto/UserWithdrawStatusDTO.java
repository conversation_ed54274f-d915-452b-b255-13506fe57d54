package fm.lizhi.ocean.wavecenter.service.user.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 查询用户注销状态响应体
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserWithdrawStatusDTO {
    /**
     * 用户ID
     */
    private long userId;

    /**
     * 提示语
     */
    private String note;

    /**
     * 是否处于注销申请中
     */
    private boolean withdraw;
} 