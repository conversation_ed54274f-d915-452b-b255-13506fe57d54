package fm.lizhi.ocean.wavecenter.service.anchor.singer.processor;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerChatSceneEnum;
import fm.lizhi.ocean.wavecenter.base.processor.BusinessEnvAwareProcessor;

public interface ISingerChatProcessor extends BusinessEnvAwareProcessor {

    boolean needSendHallAuditingChat(SingerChatSceneEnum scene);

    @Override
    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return ISingerChatProcessor.class;
    }

}
