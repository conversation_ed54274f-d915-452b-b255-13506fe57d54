package fm.lizhi.ocean.wavecenter.service.live.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.live.bean.LiveInfoBean;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.GetRoomInfoByNjIdDTO;
import fm.lizhi.ocean.wavecenter.service.live.dto.EditRoomNoticeDTO;
import fm.lizhi.ocean.wavecenter.service.live.dto.GetRoomNoticeDTO;
import fm.lizhi.ocean.wavecenter.service.live.dto.GetRoomNoticeParamDTO;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/8/26 14:33
 */
public interface LiveManager {

    /**
     * 根据直播ID查询直播信息
     *
     * @param liveId
     * @return
     */
    Optional<LiveInfoBean> getLiveInfo(long liveId);

    /**
     * 根据用户ID查询用户最新的直播ID
     *
     * @param userId 用户ID
     * @return 直播节目ID
     */
    Optional<Long> getLatestLiveIdByUserId(Long userId);

    /**
     * 获取房间公告
     *
     * @param dto 请求参数
     * @return 结果
     */
    Result<GetRoomNoticeDTO> getRoomNotice(GetRoomNoticeParamDTO dto);

    /**
     * 编辑房间公告
     *
     * @param dto 编辑参数
     * @return 结果
     */
    Result<Void> editRoomNotice(EditRoomNoticeDTO dto);

    /**
     * 根据主播ID获取房间信息
     *
     * @param userId 主播ID
     * @return 结果
     */
    Result<GetRoomInfoByNjIdDTO> getRoomInfoByNjId(Long userId);

}
