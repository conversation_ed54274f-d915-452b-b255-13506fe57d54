package fm.lizhi.ocean.wavecenter.service.background.activitycenter.impl;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.RequestQueryEffectActivitiesByTemplateIdBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.RequestQueryUserActivitiesBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ResponseQueryActivitiesBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ResponseQueryUserActivitiesBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityClassificationConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityLevelConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityInfoQueryService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.activitycenter.convert.ActivityApplyConverter;
import fm.lizhi.ocean.wavecenter.service.activitycenter.convert.ActivityApplyMappingConvert;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivitySimpleInfoDT0;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivitySimpleQueryParamDTO;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityClassificationManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityInfoQueryManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityLevelManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@ServiceProvider
public class ActivityInfoQueryServiceImpl implements ActivityInfoQueryService {

    @Autowired
    private ActivityApplyConverter activityApplyConvert;

    @Autowired
    private ActivityClassificationManager activityClassificationManager;

    @Autowired
    private UserManager userManager;

    @Autowired
    private ActivityLevelManager activityLevelManager;


    @Autowired
    private FamilyManager familyManager;

    @Autowired
    private ActivityInfoQueryManager activityInfoQueryManager;

    @Override
    public Result<ResponseQueryUserActivitiesBean> queryUserActivities(RequestQueryUserActivitiesBean requestQueryUserActivities) {
        // 时间参数校验
        Result<Void> validateResult = validateQueryUserActivitiesTime(requestQueryUserActivities);
        if (RpcResult.isFail(validateResult)) {
            return RpcResult.fail(validateResult.rCode(), validateResult.getMessage());
        }

        return ResultHandler.handle(requestQueryUserActivities.getAppId(), () -> {
            ActivitySimpleQueryParamDTO paramDTO = ActivityApplyMappingConvert.I.queryUserActivitiesReq2DTO(requestQueryUserActivities);
            //查询波段号转ID
            boolean convertResult = convertQueryBandToId(paramDTO, requestQueryUserActivities);
            if (!convertResult) {
                log.warn("queryUserActivities, band not exist, njBrand:{}, applyBrand:{}", requestQueryUserActivities.getNjBrand(), requestQueryUserActivities.getApplyBrand());
                //波段号不存在，默认查不出数据
                return RpcResult.success(new ResponseQueryUserActivitiesBean());
            }
            PageBean<ActivitySimpleInfoDT0> infoList = activityInfoQueryManager.queryActivityInfoList(paramDTO);
            if (infoList.getList() == null || infoList.getList().isEmpty()) {
                return RpcResult.success(new ResponseQueryUserActivitiesBean());
            }

            //查询出活动分类
            List<Long> classIds = infoList.getList().stream().map(ActivitySimpleInfoDT0::getClassId).collect(Collectors.toList());
            Map<Long, ActivityClassificationConfigBean> classificationBeanMap = getClassificationBeanMap(classIds);

            //查询等级
            Map<Long, String> levleMap = getLevelMap(requestQueryUserActivities.getAppId(), classificationBeanMap);

            //查询用户信息
            Map<Long, SimpleUserDto> userMap = getUserInfoMap(infoList.getList());
            //查询工会信息
            Map<Long, FamilyBean> familyBeanMap = getActivityFamilyMap(infoList.getList());

            //数据转换
            ResponseQueryUserActivitiesBean responseQueryUserActivitiesBean = activityApplyConvert.buildQueryUserActivitiesResult(infoList, classificationBeanMap, levleMap, userMap, familyBeanMap,requestQueryUserActivities.getActivityStatus());

            return RpcResult.success(responseQueryUserActivitiesBean);
        });
    }

    @Override
    public Result<ResponseQueryActivitiesBean> queryEffectActivityInfoListByTemplateId(RequestQueryEffectActivitiesByTemplateIdBean requestQueryActivities) {
        ResponseQueryActivitiesBean respBean = new ResponseQueryActivitiesBean();

        List<ActivitySimpleInfoDT0> infoList = activityInfoQueryManager.queryEffectActivityInfoListByTemplateId(requestQueryActivities);
        if (CollectionUtils.isEmpty(infoList)) {
            return RpcResult.success(respBean);
        }

        //数据转换
        ResponseQueryActivitiesBean responseQueryActivitiesBean = activityApplyConvert.buildQueryUserActivitiesSimpleResult(infoList, requestQueryActivities.getActivityStatus());

        return RpcResult.success(responseQueryActivitiesBean);

    }

    private boolean convertQueryBandToId(ActivitySimpleQueryParamDTO paramDTO, RequestQueryUserActivitiesBean requestQueryUserActivities) {
        if (StringUtils.isNotEmpty(requestQueryUserActivities.getNjBrand())) {
            Long njId = userManager.getUserIdByBand(requestQueryUserActivities.getNjBrand());
            if (njId == null) {
                log.warn("queryUserActivities, njBrand:{} not exist", requestQueryUserActivities.getNjBrand());
                return false;
            }
            paramDTO.setNjId(njId);
        }
        if (StringUtils.isNotEmpty(requestQueryUserActivities.getApplyBrand())) {
            Long applyUserId = userManager.getUserIdByBand(requestQueryUserActivities.getApplyBrand());
            if (applyUserId == null) {
                log.warn("queryUserActivities, applyBrand:{} not exist", requestQueryUserActivities.getApplyBrand());
                return false;
            }
            paramDTO.setApplyUserId(userManager.getUserIdByBand(requestQueryUserActivities.getApplyBrand()));
        }
        return true;
    }

    private Map<Long, String> getLevelMap(Integer appId, Map<Long, ActivityClassificationConfigBean> classificationBeanMap) {
        List<Long> levels = classificationBeanMap.values().stream().map(ActivityClassificationConfigBean::getLevelId).collect(Collectors.toList());
        List<ActivityLevelConfigBean> levelConfigBeans = activityLevelManager.listByAppIdAndLevelIds(appId, levels);
        return levelConfigBeans.stream().collect(Collectors.toMap(ActivityLevelConfigBean::getId, ActivityLevelConfigBean::getLevel, (x, y) -> x));
    }

    private Map<Long, FamilyBean> getActivityFamilyMap(List<ActivitySimpleInfoDT0> list) {
        return list.stream().filter(activitySimpleInfoDTO -> activitySimpleInfoDTO.getFamilyId() != null).map(activitySimpleInfoDTO ->
                familyManager.getFamilyByCache(activitySimpleInfoDTO.getFamilyId())
        ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toMap(FamilyBean::getId, Function.identity(), (x, y) -> x));
    }

    private Map<Long, SimpleUserDto> getUserInfoMap(List<ActivitySimpleInfoDT0> list) {
        Set<Long> userIds = list.stream()
                .flatMap(info -> Stream.of(info.getNjId(), info.getApplicantUid()))
                .collect(Collectors.toSet());
        return userManager.getSimpleUserMapByIds(new ArrayList<>(userIds));
    }

    private Map<Long, ActivityClassificationConfigBean> getClassificationBeanMap(List<Long> classIds) {
        List<ActivityClassificationConfigBean> classificationBeans = activityClassificationManager.batchActivityClassification(classIds);
        Map<Long, ActivityClassificationConfigBean> classificationBeanMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(classificationBeans)) {
            classificationBeanMap = classificationBeans.stream()
                    .collect(Collectors.toMap(ActivityClassificationConfigBean::getClassId, Function.identity(), (x, y) -> x));
        }
        return classificationBeanMap;
    }

    private Result<Void> validateQueryUserActivitiesTime(RequestQueryUserActivitiesBean request) {
        // 校验是否有时间参数
        if ((request.getApplyStartTime() == null || request.getApplyStartTime() == 0) && (request.getApplyEndTime() == null || request.getApplyEndTime() == 0)
                && (request.getMaxStartTime() == null || request.getMaxStartTime() == 0) && (request.getMinStartTime() == null || request.getMinStartTime() == 0)) {
            return RpcResult.fail(QUERY_USER_ACTIVITY_LIST_PARAM_ERROR, "活动提报时间或者活动开始时间必须选填一个");
        }

        // 校验活动开始时间范围
        if (request.getMaxStartTime() != null && request.getMaxStartTime() > 0 && request.getMinStartTime() != null
                && request.getMinStartTime() > 0 && request.getMaxStartTime() < request.getMinStartTime()) {
            return RpcResult.fail(QUERY_USER_ACTIVITY_LIST_PARAM_ERROR, "直播结束时间不能小于开始时间");
        }

        // 校验活动提报时间范围
        if (request.getApplyStartTime() != null && request.getApplyStartTime() > 0
                && request.getApplyEndTime() != null && request.getApplyEndTime() > 0
                && request.getApplyStartTime() > request.getApplyEndTime()) {
            return RpcResult.fail(QUERY_USER_ACTIVITY_LIST_PARAM_ERROR, "活动提报名结束时间不能小于开始时间");
        }

        return RpcResult.success();
    }


}
