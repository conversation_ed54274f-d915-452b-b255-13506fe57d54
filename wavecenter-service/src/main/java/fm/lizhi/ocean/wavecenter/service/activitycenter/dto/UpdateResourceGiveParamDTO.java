package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class UpdateResourceGiveParamDTO {

    /**
     * 资源主表主键ID
     */
    private Long id;

    /**
     * 原始目标
     */
    private Integer originalStatus;

    /**
     * 资源发放主表目标状态
     */
    private Integer targetStatus;

    /**
     * 错误吗
     */
    private Integer errorCode;

    /**
     * 错误消息
     */
    private String errorMsg;

    /**
     * 资源类型
     */
    private Integer resourceType;

    private List<ResourceDetailGiveParam> detailParams;

    /**
     * 最大尝试次数
     */
    private Integer tryCount;
    

    /**
     * 资源明细表发放参数
     */
    @Data
    @Accessors(chain = true)
    public static class ResourceDetailGiveParam {

        private Long id;

        /**
         * 原始状态
         */
        private Integer originalStatus;

        /**
         * 目标状态
         */
        private Integer targetStatus;

        /**
         * 业务主键ID
         */
        private Long bizRecordId;
    }

}
