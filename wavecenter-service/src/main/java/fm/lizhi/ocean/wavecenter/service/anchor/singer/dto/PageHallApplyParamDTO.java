package fm.lizhi.ocean.wavecenter.service.anchor.singer.dto;

import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 点唱厅管理-分页查询
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PageHallApplyParamDTO {

    /**
     * 应用ID
     */
    private int appId;

    /**
     * 厅ID
     */
    private Long njId;

    /**
     * 厅主波段号
     */
    private String njBand;

    /**
     * 审核状态
     */
    private Integer auditStatus;

    /**
     * 开始时间戳
     */
    private Long startTime;

    /**
     * 结束时间戳
     */
    private Long endTime;

    /**
     * 排序字段
     * 支持：singerAuthC<PERSON>, seniorSingerAuthCnt, createTime, applyTime
     */
    private String orderMetrics;

    /**
     * 排序方向
     * ASC-升序, DESC-降序，默认DESC
     */
    private OrderType orderType;

}
