package fm.lizhi.ocean.wavecenter.service.activitycenter.processor.pp;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.service.activitycenter.processor.IProgrammeGiveProcess;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PpProgrammeGiveProcess implements IProgrammeGiveProcess {
    @Override
    public boolean isAutoGive() {
        return false;
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.PP;
    }
}
