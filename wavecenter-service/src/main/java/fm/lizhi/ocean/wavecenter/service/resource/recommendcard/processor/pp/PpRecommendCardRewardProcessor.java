package fm.lizhi.ocean.wavecenter.service.resource.recommendcard.processor.pp;

import java.util.List;

import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.request.RequestGetFamilyUseRecord;
import org.springframework.stereotype.Component;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.RecommendCardRewardBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.response.RewardResultBean;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.processor.RecommendCardRewardProcessor;

/**
 * <AUTHOR>
 * @date 2025/3/21 15:40
 */
@Component
public class PpRecommendCardRewardProcessor implements RecommendCardRewardProcessor {

    @Override
    public RewardResultBean checkCanReward(long operatorUserId, List<RecommendCardRewardBean> rewardBeans) {
        return new RewardResultBean().setCode(0).setRewardResult("success");
    }

    @Override
    public void familyUseRecordQueryUserId(List<Long> queryRoomIds, RequestGetFamilyUseRecord request, List<Long> njIds) {

    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.PP;
    }

}