package fm.lizhi.ocean.wavecenter.service.activitycenter.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.service.common.dto.ActivityApplyDataAuditParamDTO;
import fm.lizhi.ocean.wavecenter.service.common.dto.ActivityApplyDataAuditResultDTO;

public interface AuditContentManager {

    /**
     * 活动申请数据送审
     *
     * @param paramDTO 参数
     * @return 结果
     */
    Result<ActivityApplyDataAuditResultDTO> auditActivityApplyData(ActivityApplyDataAuditParamDTO paramDTO);

}
