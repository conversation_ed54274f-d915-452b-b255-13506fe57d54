package fm.lizhi.ocean.wavecenter.service.datacenter.constants;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/4/24 14:03
 */
public enum AccountOpEnum {

    /**
     * 减
     */
    MINUS("minus")

    /**
     * 加
     */
    , PLUS("plus")
    ;

    @Getter
    private String value;

    AccountOpEnum(String value) {
        this.value = value;
    }

    public static AccountOpEnum getByValue(String value) {
        for (AccountOpEnum accountOpEnum : values()) {
            if (accountOpEnum.getValue().equals(value)) {
                return accountOpEnum;
            }
        }
        return null;
    }
}
