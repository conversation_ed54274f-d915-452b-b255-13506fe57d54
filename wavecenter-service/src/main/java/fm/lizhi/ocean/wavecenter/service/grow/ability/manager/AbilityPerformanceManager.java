package fm.lizhi.ocean.wavecenter.service.grow.ability.manager;

import fm.lizhi.ocean.wavecenter.api.grow.ability.request.RequestGetPlayerPerformance;
import fm.lizhi.ocean.wavecenter.api.grow.ability.request.RequestGetRoomPerformance;
import fm.lizhi.ocean.wavecenter.api.grow.ability.request.RequestGetRoomPlayerRank;
import fm.lizhi.ocean.wavecenter.api.grow.ability.response.ResponseGetPlayerPerformance;
import fm.lizhi.ocean.wavecenter.api.grow.ability.response.ResponseGetRoomPerformance;
import fm.lizhi.ocean.wavecenter.api.grow.ability.response.ResponseGetRoomPlayerRank;

/**
 * 成长能力表现管理器接口
 */
public interface AbilityPerformanceManager {

    /**
     * 查询厅能力表现
     *
     * @param request 请求参数
     * @return 响应结果
     */
    ResponseGetRoomPerformance getRoomPerformance(RequestGetRoomPerformance request);

    /**
     * 查询厅主播排名
     *
     * @param request 请求参数
     * @return 响应结果
     */
    ResponseGetRoomPlayerRank getRoomPlayerRank(RequestGetRoomPlayerRank request);

    /**
     * 查询主播能力表现
     *
     * @param request 请求参数
     * @return 响应结果
     */
    ResponseGetPlayerPerformance getPlayerPerformance(RequestGetPlayerPerformance request);
}
