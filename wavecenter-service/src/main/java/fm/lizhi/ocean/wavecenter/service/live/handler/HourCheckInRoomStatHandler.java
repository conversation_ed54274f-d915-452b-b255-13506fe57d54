package fm.lizhi.ocean.wavecenter.service.live.handler;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.SignUtil;
import fm.lizhi.ocean.wavecenter.api.live.bean.LiveInfoBean;
import fm.lizhi.ocean.wavecenter.api.live.constants.CheckInDateTypeEnum;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.service.live.config.LiveConfig;
import fm.lizhi.ocean.wavecenter.service.live.manager.LiveManager;
import fm.lizhi.ocean.wavecenter.service.live.manager.WaveCheckInDataManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.AbstractMap.SimpleEntry;

@Component
@Slf4j
public class HourCheckInRoomStatHandler implements CheckInRoomPeriodStatHandler {


    @Autowired
    private WaveCheckInDataManager waveCheckInDataManager;
    @Autowired
    private UserManager userManager;
    @Autowired
    private LiveManager liveManager;
    @Autowired
    private LiveConfig liveConfig;


    @Override
    public boolean support(CheckInDateTypeEnum typeEnum) {
        return typeEnum == CheckInDateTypeEnum.HOUR;
    }

    @Override
    public String buildContent(Integer appId, Long njId, Date statTime) {
        List<SimpleUserDto> simpleUserByIds = userManager.getSimpleUserByIds(Collections.singletonList(njId));
        if(CollectionUtils.isEmpty(simpleUserByIds)) {
            log.error("buildContent fail;njId info no exist;appId={};njId={}", appId, njId);
            return StringUtils.EMPTY;
        }
        //获取次小时
        DateTime lastHour = DateUtil.offsetHour(statTime, -1);
        DateTime startDate = DateUtil.beginOfHour(lastHour);
        DateTime endDate = DateUtil.endOfHour(lastHour);
        String checkInReportUrl = liveConfig.getBizConfig(appId).getCheckInReportUrl();
        String name = simpleUserByIds.get(0).getName();
        String url = buildReportUrl(CheckInDateTypeEnum.HOUR, appId, njId, startDate, endDate, checkInReportUrl, liveConfig.getCheckInMD5SaltValue());
        // 格式化日期部分为"XXX年XXX月XX日"
        String datePart = DateUtil.format(lastHour, "yyyy年MM月dd日");
        int previousHour = DateUtil.hour(lastHour, true);
        String dateFormat = String.format("%s %02d-%02d(小时)", datePart, previousHour, previousHour + 1);
        String msg = liveConfig.getCheckInReportMsgModel();
        return String.format(msg, name, dateFormat, liveConfig.getHourChatPreviewUrl(), url);
    }

    @Override
    public List<Long> populateExtraReceiver(Integer appId, Long njId, Date statTime) {
        Date hourStart = MyDateUtil.getPreviousHourStart(statTime);
        Date hourEnd = MyDateUtil.getHourEnd(hourStart);
        List<Long> hostUserIds = waveCheckInDataManager.getHostUserIds(appId, njId, hourStart, hourEnd);
        log.info("populateExtraReceiver;appId={};njId={},start={};end={};hostSize={}",
                appId, njId, hourStart, hourEnd, hostUserIds.size());
        log.debug("populateExtraReceiver;appId={};njId={},start={};end={};hostUserIds={}",
                appId, njId, hourStart, hourEnd, hostUserIds);
        return hostUserIds;
    }

    @Override
    public boolean isTimeToReport(Date triggerTime) {
        return liveConfig.isOpenHourReport();
    }

    @Override
    public boolean canSend(Long njId) {
        //房主的直播间是否开启着
        Optional<Long> latestLiveIdByUserId = liveManager.getLatestLiveIdByUserId(njId);
        if(!latestLiveIdByUserId.isPresent()) {
            log.info("latestLive no found;njId={}", njId);
            return false;
        }
        Long liveId = latestLiveIdByUserId.get();
        Optional<LiveInfoBean> liveInfo = liveManager.getLiveInfo(liveId);
        if(!liveInfo.isPresent()) {
            log.info("liveInfo no found;njId={};liveId={};", njId, liveId);
            return false;
        }
        log.info("live on air;njId={};liveId={};onAir={}", njId, liveId, liveInfo.get().getOnAir());
        return liveInfo.get().getOnAir() != null && liveInfo.get().getOnAir();
    }
}
