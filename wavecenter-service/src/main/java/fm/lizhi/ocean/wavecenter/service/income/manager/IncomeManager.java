package fm.lizhi.ocean.wavecenter.service.income.manager;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.GuildIncomeSummaryBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomIncomeDetailBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomIncomeSummaryBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.*;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.GuildIncomeStatParamDTO;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.GuildIncomeStatDTO;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/4/23 15:20
 */
public interface IncomeManager {

    /**
     * 公会收益-签约厅收入明细
     * @return
     */
    PageBean<RoomIncomeDetailBean> signRoomIncomeDetail(SignRoomIncomeDetailParamBean paramBean);

    /**
     * 查询公会有收入主播数
     * @return
     */
    int getPlayerPayCount(long familyId, Date startDate, Date endDate);

    /**
     * 查询公会有收入主播数 可以通过roomIds进行过滤
     * @param familyId
     * @param roomIds
     * @param startDate
     * @param endDate
     * @return
     */
    int getPlayerPayCount(long familyId, List<Long> roomIds, Date startDate, Date endDate);

    /**
     * 查询厅有收入主播数
     * @param roomId
     * @param startDate
     * @param endDate
     * @return
     */
    int getPlayerPayCountByRoom(Long familyId, Long roomId, Date startDate, Date endDate);

    /**
     * 公会收益-收入账户明细记录-查询
     * @param paramBean
     * @return
     */
    PageBean<GuildIncomeDetailBean> getGuildIncomeDetail(GetGuildIncomeDetailParamBean paramBean);

    GuildIncomeDetailSumBean getGuildIncomeDetailSum(GetGuildIncomeDetailSumParamBean paramBean);

    GuildIncomeSummaryBean guildIncomeSummary(long familyId, List<Long> roomIds);

    RoomIncomeSummaryBean roomIncomeSummary(long familyId, long roomId, int appId);

    PageBean<PersonalIncomeDetailBean> getPersonalIncomeDetail(GetPersonalIncomeDetailParamBean paramBean);

    PageBean<PlayerIncomeDetailBean> getPlayerIncomeDetail(GetPlayerIncomeDetailParamBean paramBean);

    PersonalIncomeDetailSumBean getPersonalIncomeDetailSum(GetPersonalIncomeDetailSumParamBean paramBean);

    PlayerIncomeDetailSumBean getPlayerIncomeDetailSum(GetPlayerIncomeDetailSumParamBean paramBean);

    /**
     * 厅收益-签约主播收入-查询
     * @param paramBean
     * @return
     */
    PageBean<RoomSignPlayerIncomeBean> getRoomSignPlayerIncome(GetRoomSignPlayerIncomeParamBean paramBean);

    /**
     * 个人收益汇总
     * @param userId
     * @return
     */
    PlayerSumResBean playerSum(long userId);

    PageBean<PersonalGiftflowBean> playerPersonalGiftflow(PlayerPersonalGiftflowParamBean paramBean);

    PersonalGiftflowBean playerPersonalGiftflowSum(PlayerPersonalGiftflowParamBean paramBean);

    PageBean<PlayerRoomGiftflowBean> playerRoomGiftflow(PlayerRoomGiftflowParamBean paramBean);

    PlayerRoomGiftflowBean playerRoomGiftflowSum(PlayerRoomGiftflowParamBean paramBean);

    /**
     * 签约厅收入账户明细记录 - 查询
     *
     * @param paramBean
     * @return
     */
    PageBean<fm.lizhi.ocean.wavecenter.api.income.bean.RoomIncomeDetailBean> getRoomIncomeDetail(GetRoomIncomeDetailParamBean paramBean);

    /**
     * 签约厅收入账户明细记录 - 总和
     *
     * @param paramBean
     * @return
     */
    RoomIncomeDetailSumBean getRoomIncomeDetailSum(GetRoomIncomeDetailSumParamBean paramBean);

    /**
     * 根据流水号获取内容(流水备注)
     * @param flowIds
     * @return
     */
    Map<Long, String> getAccountHistoryRemark(Set<Long> flowIds);
    Map<Long, String> getAccountHistoryRemark(List<Long> flowIds);


    /**
     * 考核收入明细
     * @param paramBean
     * @return
     */
    PageBean<PersonalIncomeDetailBean> getRevenueIncomeDetail(GetPersonalRevenueIncomeDetailParamBean paramBean);


    PersonalIncomeDetailSumBean getRevenueIncomeDetailSum(GetPersonalRevenueIncomeDetailParamBean paramBean);

    /**
     * 查询公会一周的收入
     * @param familyId
     * @param monDay
     * @param sunDay
     * @return
     */
    Integer getFamilyWeekIncome(Long familyId, Date monDay, Date sunDay);

    /**
     * 查询公会某周期收入概览统计
     * @param param 查询参数
     * @return 分页结果
     */
    PageBean<GuildIncomeStatDTO> queryGuildIncomeStats(GuildIncomeStatParamDTO param);

}
