package fm.lizhi.ocean.wavecenter.service.message.impl;


import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.message.bean.UnReadMessageCountBean;
import fm.lizhi.ocean.wavecenter.api.message.request.RequestUnReadMessageCount;
import fm.lizhi.ocean.wavecenter.api.message.request.RequestWcNoticeConfigPage;
import fm.lizhi.ocean.wavecenter.api.message.request.RequestWcNoticeConfigSave;
import fm.lizhi.ocean.wavecenter.api.message.request.RequestWcNoticeUpdateStatus;
import fm.lizhi.ocean.wavecenter.api.message.response.ResponseUnReadMessageCount;
import fm.lizhi.ocean.wavecenter.api.message.response.ResponseWcNoticeConfigPage;
import fm.lizhi.ocean.wavecenter.api.message.service.WcNoticeConfigService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.service.message.convert.WcNoticeConfigConvert;
import fm.lizhi.ocean.wavecenter.service.message.dto.WcNoticeConfigPageResultDTO;
import fm.lizhi.ocean.wavecenter.service.message.dto.WcNoticeConfigQueryDTO;
import fm.lizhi.ocean.wavecenter.service.message.dto.WcNoticeConfigSaveParamDTO;
import fm.lizhi.ocean.wavecenter.service.message.dto.WcNoticeUpdateStatusDTO;
import fm.lizhi.ocean.wavecenter.service.message.manager.WcNoticeConfigManager;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 公告配置 Service 实现类
 *
 * <AUTHOR>
 */
@ServiceProvider
public class WcNoticeConfigServiceImpl implements WcNoticeConfigService {

    @Autowired
    private WcNoticeConfigManager wcNoticeConfigManager;


    @Override
    public Result<Void> saveWcNoticeConfig(RequestWcNoticeConfigSave request) {
        LogContext.addReqLog("saveWcNoticeConfig.request={}", request);
        LogContext.addResLog("saveWcNoticeConfig.request={}", request);
        WcNoticeConfigSaveParamDTO dto = WcNoticeConfigConvert.INSTANCE.reqToDto(request);
        boolean success = wcNoticeConfigManager.saveWcNoticeConfig(dto);
        return success ? RpcResult.success() : RpcResult.fail(SAVE_WC_NOTICE_CONFIG_ERROR, "保存公告配置失败");
    }

    @Override
    public Result<Void> deleteWcNoticeConfigById(Long id) {
        LogContext.addReqLog("deleteWcNoticeConfigById.id={}", id);
        LogContext.addResLog("deleteWcNoticeConfigById.id={}", id);
        boolean success = wcNoticeConfigManager.deleteWcNoticeConfigById(id);
        return success ? RpcResult.success() : RpcResult.fail(DELETE_WC_NOTICE_CONFIG_ERROR, "删除公告配置失败");
    }

    @Override
    public Result<ResponseWcNoticeConfigPage> queryWcNoticeConfigPage(RequestWcNoticeConfigPage request) {
        LogContext.addReqLog("queryWcNoticeConfigPage.request={}", request);
        LogContext.addResLog("queryWcNoticeConfigPage.request={}", request);
        WcNoticeConfigQueryDTO paramDto = WcNoticeConfigConvert.INSTANCE.pageReqToDto(request);
        WcNoticeConfigPageResultDTO result = wcNoticeConfigManager.queryWcNoticeConfigPage(paramDto);

        ResponseWcNoticeConfigPage response = WcNoticeConfigConvert.INSTANCE.dtoToPageRes(result);
        return RpcResult.success(response);
    }


    @Override
    public Result<ResponseUnReadMessageCount> getUnReadMessageCount(RequestUnReadMessageCount request) {
        LogContext.addReqLog("getUnReadMessageCount.request={}", request);
        LogContext.addResLog("getUnReadMessageCount.request={}", request);
        List<UnReadMessageCountBean> result = wcNoticeConfigManager.getUnReadMessageCount(request.getTargetUserId(), request.getAppId(), request.getType(), request.getRoleCode());
        List<UnReadMessageCountBean> unReadMessageCountBeans = WcNoticeConfigConvert.INSTANCE.unReadMessageCountToRes(result);
        return RpcResult.success(new ResponseUnReadMessageCount().setUnReadMessageCountList(unReadMessageCountBeans));
    }

    @Override
    public Result<Void> updateWcNoticeStatus(RequestWcNoticeUpdateStatus request) {
        LogContext.addReqLog("updateWcNoticeStatus.request={}", request);
        LogContext.addResLog("updateWcNoticeStatus.request={}", request);
        //参数转换
        WcNoticeUpdateStatusDTO dto = WcNoticeConfigConvert.INSTANCE.reqToDto(request);
        boolean res = wcNoticeConfigManager.updateNoticeStatus(dto);
        return res ? RpcResult.success() : RpcResult.fail(UPDATE_WC_NOTICE_STATUS_ERROR, "更新公告状态失败");
    }
}