package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/14 11:56
 */
@Data
@Accessors(chain = true)
public class ActivityTemplateBaseInfoDTO {

    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 活动名
     */
    private String name;

    /**
     * 分类ID，对应activity_class_config.id
     */
    private Long classId;

    /**
     * 活动目标
     */
    private String goal;

    /**
     * 活动介绍
     */
    private String introduction;

    /**
     * 辅助道具地址列表，逗号分隔，平台域名，相对路径，斜杆开头
     */
    private String auxiliaryPropUrl;

    /**
     * 活动海报地址，平台域名，相对路径，斜杆开头
     */
    private String posterUrl;

    /**
     * 玩法工具列表，逗号分隔.
     */
    private String activityTool;

    /**
     * 房间背景ID列表，逗号分隔
     */
    private String roomBackgroundIds;

    /**
     * 头像框ID列表，逗号分隔
     */
    private String avatarWidgetIds;

    /**
     * 房间公告
     */
    private String roomAnnouncement;

    /**
     * 房间公告图片列表，逗号分隔，平台域名，相对路径，斜杆开头
     */
    private String roomAnnouncementImage;

    /**
     * 封面地址，平台域名，相对路径，斜杠开头
     */
    private String cover;

    /**
     * 上下架状态，1：下架，2：上架
     */
    private Integer status;

    /**
     * 权重，权重值高的排在前面
     */
    private Integer weight;

    /**
     * 是否上热门推荐，0：否，1：是
     */
    private Boolean hotRec;

    /**
     * 热门权重，权重值高的排在前面，当筛选热门推荐时，按此权重排序
     */
    private Integer hotWeight;

    /**
     * 是否是 AI 生成的模板
     */
    private Boolean aiGen;

    /**
     * 允许选择的背景数,-1表示没有限制
     */
    private Integer roomBackgroundLimit;

    /**
     * 允许选择的头像框数量,-1表示没有限制
     */
    private Integer avatarWidgetLimit;

    /**
     * 活动时长限制，单位分钟
     */
    private Integer activityDurationLimit;

    /**
     * 活动举办开始时间限制
     */
    private Date activityStartTimeLimit;

    /**
     * 活动举办结束时间限制
     */
    private Date activityEndTimeLimit;

    /**
     * 上架开始时间
     */
    private Date upStartTime;

    /**
     * 上架结束时间
     */
    private Date upEndTime;

}
