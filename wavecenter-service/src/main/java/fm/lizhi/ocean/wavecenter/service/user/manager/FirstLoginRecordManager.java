package fm.lizhi.ocean.wavecenter.service.user.manager;

import fm.lizhi.ocean.wavecenter.service.user.dto.FirstLoginRecordDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.FirstLoginRecordParamDTO;

/**
 * 首次登录记录Manager接口
 * <AUTHOR>
 */
public interface FirstLoginRecordManager {
    /**
     * 新增首次登录记录
     * @param dto DTO
     * @return 记录ID
     */
    boolean addFirstLoginRecord(FirstLoginRecordParamDTO dto);

    /**
     * 查询首次登录记录
     * @param appId 应用ID
     * @param userId 用户ID
     * @return DTO
     */
    FirstLoginRecordDTO queryFirstLoginRecord(Integer appId, Long userId);
} 