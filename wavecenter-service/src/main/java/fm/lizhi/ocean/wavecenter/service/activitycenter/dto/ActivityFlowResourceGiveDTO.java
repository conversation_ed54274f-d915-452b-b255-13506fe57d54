package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.Data;

@Data
public class ActivityFlowResourceGiveDTO {

    private Long id;

    /**
     * 配置记录ID
     */
    private Long giveId;

    /**
     * 流量资源ID
     */
    private String imageUrl;

    /**
     * 获得流量的用户ID
     */
    private Long userId;

    /**
     * 资源配置ID
     */
    private Long resourceConfigId;

    /**
     * 资源code码
     */
    private String resourceCode;

    /**
     * 资源配置类型，1：自动，2：手动
     */
    private Integer deployType;

    /**
     * 资源名称
     */
    private String resourceName;

    /**
     * 0:待发放，1：发放失败，2：发放成功
     */
    private Integer status;

    private String extra;

}
