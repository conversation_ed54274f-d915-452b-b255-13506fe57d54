package fm.lizhi.ocean.wavecenter.service.award.family.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 奖励发放记录
 * <AUTHOR>
 * @date 2025/3/26 14:23
 */
@Data
@Accessors(chain = true)
public class FamilyAwardDeliverRecordDTO {

    private Long id;

    /**
     * 公会id
     */
    private Long familyId;

    /**
     * 公会名称, 冗余
     */
    private String familyName;

    /**
     * 公会长id
     */
    private Long familyUserId;

    /**
     * 公会长昵称, 冗余
     */
    private String familyUserName;

    /**
     * 奖励周期开始时间, 包含, 周一的00:00:00.000
     */
    private Date awardStartTime;

    /**
     * 奖励周期结束时间, 包含, 周日的23:59:59.999
     */
    private Date awardEndTime;

    /**
     * 发放时间
     */
    private Date deliverTime;

    /**
     * 发放日期, 方便按天查询
     */
    private Date deliverDate;

    /**
     * 状态, 1-发放中, 2-发放成功, 3-发放失败, 4-部分失败
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

}
