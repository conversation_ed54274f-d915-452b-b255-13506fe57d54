package fm.lizhi.ocean.wavecenter.service.user.impl;

import com.ctrip.framework.apollo.core.enums.Env;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.user.bean.*;
import fm.lizhi.ocean.wavecenter.api.user.constants.QrCodeStatus;
import fm.lizhi.ocean.wavecenter.api.user.service.UserLoginService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.permissions.dto.RoleAuthRefDto;
import fm.lizhi.ocean.wavecenter.service.permissions.handler.RoleHandler;
import fm.lizhi.ocean.wavecenter.service.permissions.manager.RoleManager;
import fm.lizhi.ocean.wavecenter.service.user.convert.UserCommonConvert;
import fm.lizhi.ocean.wavecenter.service.user.dto.LoginRoleInfoDto;
import fm.lizhi.ocean.wavecenter.service.user.dto.LoginUserInfoDto;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.handler.UserLoginHandler;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.user.manager.LoginManager;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/4/12 15:02
 */
@ServiceProvider
public class UserLoginServiceImpl implements UserLoginService {

    @Autowired
    private UserLoginHandler userLoginHandler;
    @Autowired
    private LoginManager loginManager;
    @Autowired
    private UserManager userManager;
    @Autowired
    private RoleManager roleManager;
    @Autowired
    private RoleHandler roleHandler;
    @Autowired
    private FamilyManager familyManager;

    @Override
    public Result<UserTokenBean> phoneLogin(PhoneLoginParamBean loginParam) {
        LogContext.addReqLog("loginParam={}", JsonUtil.dumps(loginParam));
        LogContext.addResLog("loginParam={}", JsonUtil.dumps(loginParam));
        return ResultHandler.handle(loginParam.getAppId(), () -> userLoginHandler.phoneLogin(loginParam));
    }

    @Override
    public Result<UserTokenBean> refreshToken(int appId, String refreshToken) {
        LogContext.addReqLog("refreshToken={}", refreshToken);
        LogContext.addResLog("refreshToken={}", refreshToken);
        return ResultHandler.handle(appId, ()->userLoginHandler.refreshToken(refreshToken));
    }

    @Override
    public Result<CreateQrCodeResBean> createQrCode(int appId, String oldQrCode, String deviceId) {
        LogContext.addReqLog("appId={},oldQrCode={}", appId, oldQrCode);
        LogContext.addResLog("appId={},oldQrCode={}", appId, oldQrCode);
        return userLoginHandler.createQrCode(appId, oldQrCode, deviceId);
    }

    @Override
    public Result<Void> beginScanQrCode(int appId, String qrCodeKey) {
        LogContext.addReqLog("appId={},qrCodeKey={}", appId, qrCodeKey);
        LogContext.addResLog("appId={},qrCodeKey={}", appId, qrCodeKey);
        return ResultHandler.handle(appId, ()->{
            int seconds = userLoginHandler.modifyQrCodeStatus(appId, qrCodeKey, QrCodeStatus.QRCODE_BEGIN_SCAN);
            if (seconds <= 0){
                LogContext.addResLog("QR_CODE_NOT_EXIST");
                return RpcResult.fail(UserLoginService.QR_CODE_NOT_EXIST);
            }
            return RpcResult.success();
        });
    }

    @Override
    public Result<Void> qrCodeLogin(QrCodeLoginParamBean qrCodeLoginParamBean) {
        LogContext.addReqLog("qcCodeLoginParam={}", JsonUtil.dumps(qrCodeLoginParamBean));
        LogContext.addResLog("qcCodeLoginParam={}", JsonUtil.dumps(qrCodeLoginParamBean));
        return ResultHandler.handle(qrCodeLoginParamBean.getAppId(), () -> userLoginHandler.qrCodeLogin(qrCodeLoginParamBean));
    }

    @Override
    public Result<QrCodeResultBean> getQrCodeToken(int appId, String qrCodeKey) {
        LogContext.addReqLog("appId={},qrCodeKey={}", appId, qrCodeKey);
        LogContext.addResLog("appId={},qrCodeKey={}", appId, qrCodeKey);
        return ResultHandler.handle(appId, ()->{
            Optional<Integer> qrCodeStatusOp = loginManager.getQrCodeStatus(appId, qrCodeKey);
            if (!qrCodeStatusOp.isPresent()) {
                LogContext.addResLog("QR_CODE_NOT_EXIST");
                return RpcResult.fail(QR_CODE_NOT_EXIST);
            }

            QrCodeResultBean qrCodeResultBean = new QrCodeResultBean();
            qrCodeResultBean.setLoginStatus(qrCodeStatusOp.get());

            Optional<UserTokenBean> tokenOp = loginManager.getTokenByQrCodeKey(appId, qrCodeKey);
            if (tokenOp.isPresent()) {
                qrCodeResultBean.setAccessToken(tokenOp.get().getAccessToken());
                qrCodeResultBean.setRefreshToken(tokenOp.get().getRefreshToken());
                qrCodeResultBean.setUserId(tokenOp.get().getUserId());
            }

            LogContext.addResLog("qrCodeResultBean={}", JsonUtil.dumps(qrCodeResultBean));
            return RpcResult.success(qrCodeResultBean);
        });
    }

    @Override
    public Result<Void> saveUserRole(SaveUserRoleReqBean roleReqBean) {
        LogContext.addReqLog("saveUserRole={}", JsonUtil.dumps(roleReqBean));
        LogContext.addResLog("saveUserRole={}", JsonUtil.dumps(roleReqBean));
        return ResultHandler.handle(roleReqBean.getAppId(), ()->userLoginHandler.saveUserRole(roleReqBean));
    }

    @Override
    public Result<Void> logout(int appId, long userId, String deviceId) {
        LogContext.addReqLog("appId={},userId={},deviceId={}", appId, userId, deviceId);
        LogContext.addResLog("appId={},userId={},deviceId={}", appId, userId, deviceId);
        return ResultHandler.handle(appId, ()->{
            loginManager.deleteAllLoginInfo(appId, userId, deviceId);
            loginManager.logoutPostProcessor(appId, userId);
            return RpcResult.success();
        });
    }

    @Override
    public Result<UserTokenBean> genUserToken(int appId, long userId, String deviceId) {
        LogContext.addReqLog("genUserToken. appId={},userId={},deviceId={}", appId, userId, deviceId);
        LogContext.addResLog("genUserToken. appId={},userId={},deviceId={}", appId, userId, deviceId);
        return ResultHandler.handle(appId, () -> {
            if (ConfigUtils.getEnv() == Env.PRO) {
                LogContext.addResLog("pro can not gen user token");
                return RpcResult.fail(1);
            }
            UserTokenBean userTokenBean = userLoginHandler.genToken(appId, userId, deviceId);
            LogContext.addResLog("userTokenBean={}", JsonUtil.dumps(userTokenBean));
            return RpcResult.success(userTokenBean);
        });
    }

    @Override
    public Result<Long> getUserIdByAccessToken(String accessToken) {
        LogContext.addReqLog("accessToken={}", accessToken);
        LogContext.addResLog("accessToken={}", accessToken);
        if (StringUtils.isBlank(accessToken)) {
            return RpcResult.fail(NOT_LOGIN);
        }
        Optional<Long> op = loginManager.getUserIdByAccessToken(accessToken);
        return op.map(RpcResult::success).orElseGet(() -> RpcResult.fail(ACCESS_TOKEN_EXPIRED));
    }

    @Override
    public Result<LoginRoleInfoBean> getUserLoginRoleInfo(String accessToken) {
        LogContext.addReqLog("accessToken={}", accessToken);
        LogContext.addResLog("accessToken={}", accessToken);
        //获取用户信息-业务线
        if (StringUtils.isBlank(accessToken)) {
            return RpcResult.fail(NOT_LOGIN);
        }
        Optional<LoginUserInfoDto> userInfoOp = loginManager.getUserByAccessToken(accessToken);
        if (!userInfoOp.isPresent()) {
            LogContext.addResLog("ACCESS_TOKEN_EXPIRED");
            return RpcResult.fail(ACCESS_TOKEN_EXPIRED);
        }
        LoginUserInfoDto loginUserInfoDto = userInfoOp.get();
        return ResultHandler.handle(loginUserInfoDto.getAppId(), ()->{
            //获取角色信息
            Optional<LoginRoleInfoDto> accessTokenRoleOp = loginManager.getAccessTokenRole(accessToken);
            if (!accessTokenRoleOp.isPresent()) {
                LogContext.addResLog("ROLE_AUTH_NOT_EXIST");
                return RpcResult.fail(ROLE_AUTH_NOT_EXIST);
            }

            Long subjectUserId = null;
            LoginRoleInfoDto loginRoleInfoDto = accessTokenRoleOp.get();
            Long roleConfigId = loginRoleInfoDto.getRoleConfigId();
            LogContext.addResLog("roleConfigId={}", roleConfigId);
            if (roleConfigId == null || Objects.equals(roleConfigId, UserLoginHandler.SELF_ROLE_CONFIG_ID)) {
                //当前授权角色是自己
                subjectUserId = loginUserInfoDto.getUserId();

                //校验当前签约角色是否依然有效, 防止用户被解约后在token有效期内依然可以访问
                String roleCode = roleManager.getUserRoleCode(loginUserInfoDto.getUserId());
                if (!Objects.equals(roleCode, loginRoleInfoDto.getRoleCode())) {
                    LogContext.addResLog("ROLE_AUTH_NOT_EXIST");
                    return RpcResult.fail(ROLE_AUTH_NOT_EXIST);
                }

            } else {
                //查询授权角色配置
                Optional<RoleAuthRefDto> authConfigOp = roleManager.getAuthConfig(roleConfigId);
                if (!authConfigOp.isPresent()) {
                    LogContext.addResLog("ROLE_AUTH_NOT_EXIST");
                    return RpcResult.fail(ROLE_AUTH_NOT_EXIST);
                }
                RoleAuthRefDto roleAuthRefDto = authConfigOp.get();
                subjectUserId = roleAuthRefDto.getSubjectUserId();
            }

            LoginRoleInfoBean resultBean = UserCommonConvert.I.loginRoleInfoDto2Bean(loginRoleInfoDto);

            //填充授权用户信息
            List<SimpleUserDto> userDtoList = userManager.getSimpleUserByIds(Collections.singletonList(subjectUserId));
            if (CollectionUtils.isNotEmpty(userDtoList)) {
                SimpleUserDto userDto = userDtoList.get(0);
                resultBean.setId(subjectUserId);
                resultBean.setBand(userDto.getBand());
                resultBean.setName(userDto.getName());
                resultBean.setPhoto(userDto.getAvatar());
            }
            return RpcResult.success(resultBean);
        });
    }

    @Override
    public Result<LoginRoleInfoBean> getUserLoginRoleSimpleInfo(String accessToken) {
        LogContext.addReqLog("accessToken={}", accessToken);
        LogContext.addResLog("accessToken={}", accessToken);
        //获取用户信息-业务线
        if (StringUtils.isBlank(accessToken)) {
            return RpcResult.fail(NOT_LOGIN);
        }
        Optional<LoginUserInfoDto> userInfoOp = loginManager.getUserByAccessToken(accessToken);
        if (!userInfoOp.isPresent()) {
            LogContext.addResLog("ACCESS_TOKEN_EXPIRED");
            return RpcResult.fail(ACCESS_TOKEN_EXPIRED);
        }
        LoginUserInfoDto loginUserInfoDto = userInfoOp.get();
        return ResultHandler.handle(loginUserInfoDto.getAppId(), ()->{
            //获取角色信息
            Optional<LoginRoleInfoDto> accessTokenRoleOp = loginManager.getAccessTokenRole(accessToken);
            if (!accessTokenRoleOp.isPresent()) {
                LogContext.addResLog("accessTokenRoleOp not persent");
                return RpcResult.fail(ROLE_AUTH_NOT_EXIST);
            }

            LoginRoleInfoDto loginRoleInfoDto = accessTokenRoleOp.get();
            Long roleConfigId = loginRoleInfoDto.getRoleConfigId();
            LogContext.addResLog("roleConfigId={}", roleConfigId);
            if (!Objects.equals(roleConfigId, UserLoginHandler.SELF_ROLE_CONFIG_ID)) {
                //查询授权角色配置
                Optional<RoleAuthRefDto> authConfigOp = roleManager.getAuthConfig(roleConfigId);
                if (!authConfigOp.isPresent()) {
                    LogContext.addResLog("authConfigOp not persent");
                    return RpcResult.fail(ROLE_AUTH_NOT_EXIST);
                }
                RoleAuthRefDto roleAuthRefDto = authConfigOp.get();
                //校验签约信息
                boolean checkRoleConfigSign = roleHandler.checkRoleConfigSign(roleAuthRefDto);
                if (!checkRoleConfigSign) {
                    LogContext.addResLog("checkRoleConfigSign={}", checkRoleConfigSign);
                    return RpcResult.fail(ROLE_AUTH_NOT_EXIST);
                }
            } else {
                //校验当前签约角色是否依然有效, 防止用户被解约后在token有效期内依然可以访问
                String roleCode = roleManager.getUserRoleCode(loginUserInfoDto.getUserId());
                if (!Objects.equals(roleCode, loginRoleInfoDto.getRoleCode())) {
                    LogContext.addResLog("ROLE_AUTH_NOT_EXIST");
                    return RpcResult.fail(ROLE_AUTH_NOT_EXIST);
                }
            }
            LoginRoleInfoBean resultBean = UserCommonConvert.I.loginRoleInfoDto2Bean(loginRoleInfoDto);
            return RpcResult.success(resultBean);
        });
    }

    @Override
    public Result<Long> getUserIdByBizToken(int appId, String bizToken) {
        LogContext.addReqLog("appId={},bizToken={}", appId, bizToken);
        LogContext.addResLog("appId={},bizToken={}", appId, bizToken);
        return ResultHandler.handle(appId, ()->{
            Optional<Long> userId = userManager.getUserIdByBusinessToken(bizToken);
            if (!userId.isPresent()) {
                LogContext.addResLog("BIZ_TOKEN_USER_NOT_EXIST");
                return RpcResult.fail(BIZ_TOKEN_USER_NOT_EXIST);
            }
            LogContext.addResLog("userId={}", userId);
            return RpcResult.success(userId.get());
        });
    }
}
