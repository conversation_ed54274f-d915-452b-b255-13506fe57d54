package fm.lizhi.ocean.wavecenter.service.activitycenter.impl;

import cn.hutool.core.collection.CollUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivitySimpleClassificationBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityTemplateGeneralPageBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityTemplateHotPageBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestCountGeneralActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestCountGeneralActivityTemplateByUserId;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestPageGeneralActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestPageHotActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseActivityClassification;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseGetGeneralActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityClassificationService;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityTemplateService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.live.constants.RoomCategoryOptionConstants;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityTemplateManager;
import fm.lizhi.ocean.wavecenter.service.live.handler.LiveRoomHandler;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 活动模板服务实现
 */
@ServiceProvider
@Slf4j
public class ActivityTemplateServiceImpl implements ActivityTemplateService {

    @Autowired
    private ActivityTemplateManager activityTemplateManager;

    @Autowired
    private FamilyManager familyManager;

    @Autowired
    private LiveRoomHandler liveRoomHandler;

    @Autowired
    private ActivityClassificationService activityClassificationService;

    @Override
    public Result<PageBean<ActivityTemplateHotPageBean>> pageHotTemplate(RequestPageHotActivityTemplate req) {
        try {
            LogContext.addResLog("`req={}`", req);

            UserInFamilyBean userInFamily = familyManager.getUserInFamily(req.getUserId());
            if (req.getCategoryValue() == null) {
                // 厅主和签约主播需要限制品类
                if (userInFamily != null && (userInFamily.isRoom() || userInFamily.isPlayer())) {
                    ArrayList<Integer> categoryList = CollUtil.newArrayList(RoomCategoryOptionConstants.UNLIMITED_VALUE);
                    liveRoomHandler.getUserSignRoomCategory(req.getUserId()).ifPresent(roomCategoryEnum -> {
                        categoryList.add(roomCategoryEnum.getValue());
                    });
                    req.setCategoryValue(categoryList);
                }
            }

            List<Long> njList = new ArrayList<>();
            if (userInFamily.isRoom() || userInFamily.isPlayer()) {
                log.info("njId={}", userInFamily.getNjId());
                njList.add(userInFamily.getNjId());
            }

            return activityTemplateManager.pageHotTemplate(req, njList);
        } catch (RuntimeException e) {
            log.error("pageHotTemplate error req={}", req, e);
            return RpcResult.fail(PAGE_HOT_TEMPLATE_FAIL, "分页查询热门活动模板失败");
        }
    }

    @Override
    public Result<PageBean<ActivityTemplateGeneralPageBean>> pageGeneralTemplate(RequestPageGeneralActivityTemplate req) {
        try {
            LogContext.addResLog("`req={}`", req);
            return activityTemplateManager.pageGeneralTemplate(req);
        } catch (RuntimeException e) {
            log.error("pageGeneralTemplate error req={}", req, e);
            return RpcResult.fail(PAGE_GENERAL_TEMPLATE_FAIL, "分页查询通用活动模板失败");
        }
    }

    @Override
    public Result<ResponseGetGeneralActivityTemplate> getGeneralTemplate(Long templateId) {
        try {
            LogContext.addResLog("`templateId={}`", templateId);
            return activityTemplateManager.getGeneralTemplate(templateId);
        } catch (RuntimeException e) {
            log.error("getGeneralTemplate error templateId={}", templateId, e);
            return RpcResult.fail(GET_GENERAL_TEMPLATE_FAIL, "获取通用活动模板详情失败");
        }
    }

    @Override
    public Result<Long> countGeneralTemplate(RequestCountGeneralActivityTemplate req) {
        try {
            LogContext.addResLog("`req={}`", req);
            return activityTemplateManager.countGeneralTemplate(req);
        } catch (RuntimeException e) {
            log.error("countGeneralTemplate error req={}", req, e);
            return RpcResult.fail(COUNT_GENERAL_TEMPLATE_FAIL, "获取通用活动模板数量失败");
        }
    }

    @Override
    public Result<Long> countGeneralTemplateByUserId(RequestCountGeneralActivityTemplateByUserId req) {
        Integer appId = req.getAppId();
        Long userId = req.getUserId();
        LogContext.addResLog("`appId={}, userId={}`", appId, userId);

        try {
            // 1. 获取用户可见的分类列表
            Result<List<ResponseActivityClassification>> classificationResult =
                activityClassificationService.getClassificationListByUserId(appId, userId);

            if (RpcResult.isFail(classificationResult)) {
                return RpcResult.fail(COUNT_GENERAL_TEMPLATE_FAIL, "获取用户分类列表失败");
            }

            // 2. 提取所有分类ID
            List<Long> classIds = new ArrayList<>();
            for (ResponseActivityClassification classification : classificationResult.target()) {
                for (ActivitySimpleClassificationBean classBean : classification.getClassList()) {
                    classIds.add(classBean.getId());
                }
            }

            // 3. 构建统计请求
            RequestCountGeneralActivityTemplate request = new RequestCountGeneralActivityTemplate();
            request.setAppId(appId);
            request.setRequestUserId(userId);
            request.setClassIds(classIds);

            // 4. 调用统计方法
            return countGeneralTemplate(request);

        } catch (RuntimeException e) {
            log.error("countGeneralTemplateByUserId error appId={}, userId={}", appId, userId, e);
            return RpcResult.fail(COUNT_GENERAL_TEMPLATE_FAIL, "根据用户ID获取通用活动模板数量失败");
        }
    }
}
