package fm.lizhi.ocean.wavecenter.service.grow.ability.handler;

import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.ConditionGroupTask;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.HaveResultTaskI;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.ShortCircuitingTask;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.TaskInfoI;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.condition.AbstractCondition;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.condition.ComparatorEnum;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.condition.LogicSymbolEnum;
import fm.lizhi.ocean.wavecenter.service.common.manager.IdManager;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto.ConditionDTO;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto.ConditionGroupDTO;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto.TaskTemplateCapabilityDTO;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto.TaskTemplateConditionDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 通过任务模板生成任务实例
 * <AUTHOR>
 * @date 2025/6/9 11:35
 */
@Slf4j
@Component
public class GrowTaskFactory {

    @Autowired
    private IdManager idManager;

    private final Map<ComparatorEnum, ConditionParser> conditionParserMap;

    @Autowired
    public GrowTaskFactory(List<ConditionParser> conditionParserList) {
        this.conditionParserMap = new EnumMap<>(ComparatorEnum.class);
        for (ConditionParser conditionParser : conditionParserList) {
            this.conditionParserMap.put(conditionParser.getComparator(), conditionParser);
        }
    }

    /**
     * 创建任务实例
     * @param taskTemplates
     * @return
     */
    public ShortCircuitingTask createResultTask(List<TaskTemplateConditionDTO> taskTemplates, Long userId){

        // 根据奖励排序 能力分从大到小
        List<TaskTemplateConditionDTO> sortedTemplateList = sortTaskTemplatesByCapabilityScoreDesc(taskTemplates);
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();

        ShortCircuitingTask shortCircuitingTask = new ShortCircuitingTask(idManager.genId());
        for (TaskTemplateConditionDTO taskTemplate : sortedTemplateList) {
            ConditionGroupDTO conditionGroup = taskTemplate.getConditionGroup();
            if (conditionGroup == null) {
                log.info("template id={} conditionGroup is null", taskTemplate.getId());
                continue;
            }

            String logicSymbol = conditionGroup.getLogicSymbol();
            LogicSymbolEnum logicSymbolEnum = LogicSymbolEnum.getByValue(logicSymbol);
            if (logicSymbolEnum == null) {
                log.info("template id={} logicSymbol={} is invalid", taskTemplate.getId(), logicSymbol);
                continue;
            }

            List<ConditionDTO> conditionList = conditionGroup.getConditionList();
            // 解析条件
            List<AbstractCondition<?, ?>> abstractConditions = parserConditions(conditionList);

            ConditionGroupTask conditionGroupTask = new ConditionGroupTask(idManager.genId()
                    , taskTemplate.getId()
                    , appId, userId
                    , abstractConditions, logicSymbolEnum);

            shortCircuitingTask.addLast(conditionGroupTask);
        }

        return shortCircuitingTask;
    }

    /**
     * 解析条件
     * @param conditionList
     * @return
     */
    private List<AbstractCondition<?, ?>> parserConditions(List<ConditionDTO> conditionList) {
        List<AbstractCondition<?, ?>> conditions = new ArrayList<>();

        for (ConditionDTO conditionDTO : conditionList) {
            String comparator = conditionDTO.getComparator();
            ComparatorEnum comparatorEnum = ComparatorEnum.getByValue(comparator);
            if (comparatorEnum == null) {
                log.info("comparator={} is invalid", comparator);
                continue;
            }

            ConditionParser conditionParser = conditionParserMap.get(comparatorEnum);
            if (conditionParser == null) {
                log.info("comparator={} is not supported", comparator);
                continue;
            }

            conditions.add(conditionParser.parse(conditionDTO));
        }

        return conditions;
    }

    /**
     * 根据奖励能力分数降序排序
     * @param taskTemplates
     * @return
     */
    public List<TaskTemplateConditionDTO> sortTaskTemplatesByCapabilityScoreDesc(List<TaskTemplateConditionDTO> taskTemplates) {
        return taskTemplates.stream()
                .sorted((t1, t2) -> {
                    BigDecimal score1 = getCapabilityScoreOrDefault(t1);
                    BigDecimal score2 = getCapabilityScoreOrDefault(t2);
                    // 降序排列
                    return score2.compareTo(score1);
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取 capabilityScore，若 capability 为 null 则返回 BigDecimal.ZERO
     */
    private BigDecimal getCapabilityScoreOrDefault(TaskTemplateConditionDTO template) {
        TaskTemplateCapabilityDTO capability = template.getCapability();
        return capability == null ? BigDecimal.ZERO : capability.getCapabilityScore();
    }

}
