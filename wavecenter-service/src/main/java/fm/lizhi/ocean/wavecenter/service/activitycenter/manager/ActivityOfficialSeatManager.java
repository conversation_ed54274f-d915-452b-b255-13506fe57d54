package fm.lizhi.ocean.wavecenter.service.activitycenter.manager;

import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateFlowResourceExtraBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseGetActivityTemplate;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityOfficialSeatTimeDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityOptionalOfficialTimeDTO;

import java.util.Date;
import java.util.List;

/**
 * 活动官频位管理
 */
public interface ActivityOfficialSeatManager {

    /**
     * 查询官频位已使用数量
     *
     * @param appId     应用ID
     * @param startTime 展示开始时间
     * @param endTime   官频位展示结束时间
     * @param seatNo    官频位编号
     * @return 数量
     */
    List<ActivityOfficialSeatTimeDTO> getOfficialSeatList(Integer appId, Date startTime, Date endTime, Integer seatNo);

    /**
     * 获取可用官频位时间表
     */
    ActivityOptionalOfficialTimeDTO getOptionalOfficialTime(Integer appId, Long activityStartTime, Long activityEndTime, ActivityTemplateFlowResourceExtraBean flowResourceExtraBean, ResponseGetActivityTemplate target);

    /**
     * 初始化官频位时间表
     *
     * @param list 官频位时间表
     * @return 结果
     */
    boolean initOfficialSeatTime(List<ActivityOfficialSeatTimeDTO> list);
}
