package fm.lizhi.ocean.wavecenter.service.resource.decorate.manager;

import java.util.List;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.DecorateInfoBean;

public interface DecorateManager {

    /**
     * 获取装饰信息
     * @param
     * @return
     */
    DecorateInfoBean getDecorateInfo(PlatformDecorateTypeEnum decorateType, Long decorateId);

    /**
     * 获取装饰列表
     * @param
     * @return
     */
    PageBean<DecorateInfoBean> getDecorates(PlatformDecorateTypeEnum decorateType, Long decorateId, String decorateName, int pageNum, int pageSize);

    /**
     * 批量获取装饰信息
     * @param decorateType
     * @param decorateIds
     * @return
     */
    List<DecorateInfoBean> batchGetDecorates(PlatformDecorateTypeEnum decorateType, List<Long> decorateIds);
}
