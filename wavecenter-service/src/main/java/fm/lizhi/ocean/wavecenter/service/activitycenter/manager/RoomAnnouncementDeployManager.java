package fm.lizhi.ocean.wavecenter.service.activitycenter.manager;

import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.AnnouncementDeployStatusEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.utils.RedisLock;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.ActivityConfig;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityResourceTransferResultDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityRoomAnnouncementDeployDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.GetRoomInfoByNjIdDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.SaveOriginalAnnouncementParamDTO;
import fm.lizhi.ocean.wavecenter.service.live.dto.EditRoomNoticeDTO;
import fm.lizhi.ocean.wavecenter.service.live.dto.GetRoomNoticeDTO;
import fm.lizhi.ocean.wavecenter.service.live.dto.GetRoomNoticeParamDTO;
import fm.lizhi.ocean.wavecenter.service.live.manager.LiveManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 房间公告设置定时任务
 */
@Slf4j
@Component
public class RoomAnnouncementDeployManager {

    @Autowired
    private LiveManager liveManager;

    @Autowired
    private ActivityMaterielGiveManager activityMaterielGiveManager;

    @Autowired
    private ActivityConfig activityConfig;

    @Autowired
    private ActivityRedisManager activityRedisManager;

    @Autowired
    private ActivityResourceTransferManager activityResourceTransferManager;

    private final int DEPLOY_ANNOUNCEMENT_FAIL = 1;

    public void deployAnnouncement() {
        List<ActivityRoomAnnouncementDeployDTO> waitDeployRecord = getWaitDeployRecord();
        if (CollectionUtils.isEmpty(waitDeployRecord)) {
            return;
        }

        int successCount = 0;
        for (ActivityRoomAnnouncementDeployDTO deployRecord : waitDeployRecord) {
            try (RedisLock lock = activityRedisManager.getResourceGiveLock(deployRecord.getAppId(), deployRecord.getId())) {
                if (!lock.tryLock()) {
                    continue;
                }
                Result<Void> result = ResultHandler.handle(deployRecord.getAppId(), () -> deployAnnouncementCore(deployRecord));
                successCount += RpcResult.isSuccess(result) ? 1 : 0;
            } catch (Exception e) {
                log.error("deployAnnouncement error, deployRecord:{}", deployRecord, e);
            }
        }
        log.info("deployAnnouncement totalSize:{}, successCount:{}", waitDeployRecord.size(), successCount);
    }

    /**
     * 房间公告设置
     */
    public Result<Void> deployAnnouncementCore(ActivityRoomAnnouncementDeployDTO deployRecord) {
        Optional<Long> roomIdRes = getRoomId(deployRecord.getAppId(), deployRecord.getNjId());
        if (!roomIdRes.isPresent()) {
            log.warn("获取房间ID失败，activityId:{}, njId:{}", deployRecord.getActivityId(), deployRecord.getNjId());
            return RpcResult.fail(DEPLOY_ANNOUNCEMENT_FAIL);
        }

        //设置房间公告
        Result<GetRoomNoticeDTO> roomNoticeRes = liveManager.getRoomNotice(new GetRoomNoticeParamDTO().setNjId(deployRecord.getNjId())
                .setRoomId(roomIdRes.get()));
        if (RpcResult.isFail(roomNoticeRes)) {
            log.warn("获取房间公告失败，activityId:{}, njId:{},原因：{}", deployRecord.getActivityId(), deployRecord.getNjId(), roomNoticeRes.getMessage());
            return RpcResult.fail(DEPLOY_ANNOUNCEMENT_FAIL);
        }


        //保存原房间公告
        SaveOriginalAnnouncementParamDTO paramDTO = buildSaveOriginalAnnouncementParam(deployRecord, roomNoticeRes.target());
        boolean updateRes = activityMaterielGiveManager.saveOriginalAnnouncement(paramDTO);
        if (!updateRes) {
            log.warn("保存房间原公告失败，activityId:{}, njId:{}, content:{}", deployRecord.getActivityId(), deployRecord.getNjId(), roomNoticeRes.target().getContent());
            return RpcResult.fail(DEPLOY_ANNOUNCEMENT_FAIL);
        }

        //开始设置主播的房间公告
        EditRoomNoticeDTO editRoomNoticeDTO = buildEditRoomNoticeDTO(deployRecord, roomIdRes.get());
        editRoomNoticeDTO.setContent(deployRecord.getAnnouncement());
        Result<Void> result = liveManager.editRoomNotice(editRoomNoticeDTO);

        int targetStatus = AnnouncementDeployStatusEnum.SET_SUCCESS.getStatus();
        if (RpcResult.isFail(result)) {
            log.warn("设置房间公告失败，activityId:{}, njId:{}，errorMsg:{}", deployRecord.getActivityId(), deployRecord.getNjId(), result.getMessage());
            targetStatus = AnnouncementDeployStatusEnum.SET_FAIL.getStatus();
        }
        //更改房间公告状态
        boolean res = activityMaterielGiveManager.updateAnnouncementDeployStatus(deployRecord.getId(), deployRecord.getStatus(), targetStatus);
        log.info("更新房间公告部署状态，activityId:{}, njId:{}, res:{}", deployRecord.getActivityId(), deployRecord.getNjId(), res);
        return res ? RpcResult.success() : RpcResult.fail(DEPLOY_ANNOUNCEMENT_FAIL);
    }

    /**
     * 恢复房间公告
     */
    public void recoveryAnnouncement() {
        List<ActivityRoomAnnouncementDeployDTO> waitDeployRecord = getWaitRecoveryRecord();
        if (CollectionUtils.isEmpty(waitDeployRecord)) {
            return;
        }

        int successCount = 0;
        for (ActivityRoomAnnouncementDeployDTO deployRecord : waitDeployRecord) {
            Result<Void> result = ResultHandler.handle(deployRecord.getAppId(), () -> recoveryAnnouncementCore(deployRecord));
            ;
            successCount += RpcResult.isSuccess(result) ? 1 : 0;
        }
        log.info("recoveryAnnouncement totalSize:{}, successCount:{}", waitDeployRecord.size(), successCount);
    }

    public Result<Void> recoveryAnnouncementCore(ActivityRoomAnnouncementDeployDTO deployRecord) {
        Optional<Long> roomIdRes = getRoomId(deployRecord.getAppId(), deployRecord.getNjId());
        if (!roomIdRes.isPresent()) {
            log.warn("获取房间ID失败，activityId:{}, njId:{}", deployRecord.getActivityId(), deployRecord.getNjId());
            return RpcResult.fail(DEPLOY_ANNOUNCEMENT_FAIL);
        }

        //开始设置主播的房间公告
        EditRoomNoticeDTO editRoomNoticeDTO = buildEditRoomNoticeDTO(deployRecord, roomIdRes.get());
        editRoomNoticeDTO.setContent(deployRecord.getOriginalAnnouncement());
        Result<Void> result = liveManager.editRoomNotice(editRoomNoticeDTO);

        int targetStatus = AnnouncementDeployStatusEnum.RECOVER_SUCCESS.getStatus();
        if (RpcResult.isFail(result)) {
            log.warn("恢复房间公告失败，activityId:{}, njId:{}，errorMsg:{}", deployRecord.getActivityId(), deployRecord.getNjId(), result.getMessage());
            targetStatus = AnnouncementDeployStatusEnum.RECOVER_FAIL.getStatus();
        }
        //更改房间公告状态
        boolean res = activityMaterielGiveManager.updateAnnouncementDeployStatus(deployRecord.getId(), deployRecord.getStatus(), targetStatus);
        log.info("更新房间公告部署状态，activityId:{}, njId:{}, res:{}", deployRecord.getActivityId(), deployRecord.getNjId(), res);
        return res ? RpcResult.success() : RpcResult.fail(DEPLOY_ANNOUNCEMENT_FAIL);
    }

    /**
     * 查询待部署的记录
     *
     * @return 结果列表
     */
    private List<ActivityRoomAnnouncementDeployDTO> getWaitRecoveryRecord() {
        long currentTimeMillis = System.currentTimeMillis();
        List<Integer> statusList = Lists.newArrayList(AnnouncementDeployStatusEnum.RECOVER_FAIL.getStatus(), AnnouncementDeployStatusEnum.SET_SUCCESS.getStatus());
        List<ActivityRoomAnnouncementDeployDTO> announcementDeployList = activityMaterielGiveManager.getAnnouncementDeployList(currentTimeMillis, statusList, 2);
        if (CollectionUtils.isNotEmpty(announcementDeployList)) {
            //过滤掉结束时间超过5分钟的记录
            return announcementDeployList.stream()
                    .filter(item -> (currentTimeMillis - item.getEndTime().getTime()) / 1000 / 60 <= activityConfig.getMaxPreactRecoveryAnnouncement())
                    .collect(Collectors.toList());
        }
        return announcementDeployList;
    }

    /**
     * 查询待部署的记录
     *
     * @return 结果列表
     */
    private List<ActivityRoomAnnouncementDeployDTO> getWaitDeployRecord() {
        long currentTimeMillis = System.currentTimeMillis();
        List<Integer> statusList = Lists.newArrayList(AnnouncementDeployStatusEnum.WAIT_SET.getStatus(), AnnouncementDeployStatusEnum.SET_FAIL.getStatus());
        List<ActivityRoomAnnouncementDeployDTO> announcementDeployList = activityMaterielGiveManager.getAnnouncementDeployList(currentTimeMillis, statusList, 1);
        if (CollectionUtils.isNotEmpty(announcementDeployList)) {
            //过滤掉开始时间大于5分钟的记录
            return announcementDeployList.stream()
                    .filter(item -> (item.getStartTime().getTime() - currentTimeMillis) / 1000 / 60 <= activityConfig.getMinPreactDeployAnnouncement())
                    .collect(Collectors.toList());
        }
        return announcementDeployList;
    }

    private EditRoomNoticeDTO buildEditRoomNoticeDTO(ActivityRoomAnnouncementDeployDTO deployRecord, Long roomId) {
        EditRoomNoticeDTO editRoomNoticeDTO = new EditRoomNoticeDTO()
                .setNjId(deployRecord.getNjId())
                .setRoomId(roomId);

        if (deployRecord.getAnnouncementImgUrl() != null) {
            List<String> list = new ArrayList<>();
            //将字符串转成list
            List<String> imageUrls = Arrays.stream(deployRecord.getAnnouncementImgUrl().split(",")).collect(Collectors.toList());
            for (String imageUrl : imageUrls) {
                ActivityResourceTransferResultDTO resourceTransfer = activityResourceTransferManager.getResourceTransfer(deployRecord.getAppId(), imageUrl);
                if (resourceTransfer != null) {
                    list.add(StringUtils.removeStart(resourceTransfer.getTargetUri(), "/"));
                }
            }
            editRoomNoticeDTO.setImageUrl(list);
        }
        return editRoomNoticeDTO;
    }

    public SaveOriginalAnnouncementParamDTO buildSaveOriginalAnnouncementParam(ActivityRoomAnnouncementDeployDTO deployRecord, GetRoomNoticeDTO roomNoticeRes) {
        //提前设置原房间公告
        List<String> imageUrls = roomNoticeRes.getImageUrls();
        if (CollectionUtils.isNotEmpty(imageUrls)) {
            imageUrls = imageUrls.stream().map(UrlUtils::removeAnyHost).collect(Collectors.toList());
        }


        return new SaveOriginalAnnouncementParamDTO()
                .setId(deployRecord.getId())
                .setContent(roomNoticeRes.getContent())
                .setImageUrl(imageUrls);
    }

    /**
     * 获取房间ID
     *
     * @param appId  应用ID
     * @param userId 用户ID
     * @return 房间ID
     */
    private Optional<Long> getRoomId(Integer appId, Long userId) {
        Result<GetRoomInfoByNjIdDTO> result = liveManager.getRoomInfoByNjId(userId);
        if (RpcResult.isFail(result)) {
            log.warn("获取房间信息失败，appId:{}, njId:{}, code:{}", appId, userId, result.target());
            return Optional.empty();
        }
        return Optional.of(result.target().getId());
    }
}
