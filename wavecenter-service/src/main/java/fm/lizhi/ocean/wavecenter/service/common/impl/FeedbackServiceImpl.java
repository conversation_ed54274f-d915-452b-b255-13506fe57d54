package fm.lizhi.ocean.wavecenter.service.common.impl;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.AddEvaluateRecordReq;
import fm.lizhi.ocean.wavecenter.api.common.bean.GetEvaluateRecordCountReq;
import fm.lizhi.ocean.wavecenter.api.common.service.FeedbackService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.common.manager.FeedbackManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2024/6/14 20:41
 */
@Slf4j
@ServiceProvider
public class FeedbackServiceImpl implements FeedbackService {

    @Autowired
    private FeedbackManager feedbackManager;

    @Override
    public Result<Void> addEvaluateRecord(AddEvaluateRecordReq req) {
        LogContext.addReqLog("req={}", JsonUtil.dumps(req));
        LogContext.addResLog("req={}", JsonUtil.dumps(req));
        return ResultHandler.handle(req.getAppId(), ()->{
            feedbackManager.addEvaluateRecord(req);
            return RpcResult.success();
        });
    }

    @Override
    public Result<Long> getEvaluateRecordCount(GetEvaluateRecordCountReq req) {
        LogContext.addReqLog("req={}", JsonUtil.dumps(req));
        LogContext.addResLog("req={}", JsonUtil.dumps(req));
        return ResultHandler.handle(req.getAppId(), ()->{
            return RpcResult.success(feedbackManager.getEvaluateRecordCount(req));
        });
    }
}
