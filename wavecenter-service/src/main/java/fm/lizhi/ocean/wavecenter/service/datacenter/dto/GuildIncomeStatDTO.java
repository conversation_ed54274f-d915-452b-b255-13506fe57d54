package fm.lizhi.ocean.wavecenter.service.datacenter.dto;

import lombok.Data;

/**
 * 公会收入概览统计结果DTO
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
@Data
public class GuildIncomeStatDTO {

    /**
     * 开始时间戳
     */
    private Long startTime;

    /**
     * 结束时间戳
     */
    private Long endTime;

    /**
     * 收入详情
     */
    private IncomeSummaryDTO info;

    /**
     * 构建数据
     */
    public static GuildIncomeStatDTO of(Long startTime, Long endTime, IncomeSummaryDTO info) {
        GuildIncomeStatDTO dto = new GuildIncomeStatDTO();
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        dto.setInfo(info);
        return dto;
    }

}