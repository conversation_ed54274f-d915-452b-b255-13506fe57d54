package fm.lizhi.ocean.wavecenter.service.sign.process.hy;

import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.bean.NjAndPlayerContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.service.SignErrorCode;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.service.sign.config.SignConfig;
import fm.lizhi.ocean.wavecenter.service.sign.dto.QueryNonContractDTO;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.NonContractManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.SignFlowManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserVerifyDataDTO;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/26 10:17
 */
public abstract class HySignAbstractProcessor {

    @Autowired
    protected NonContractManager nonContractManager;

    @Autowired
    protected UserManager userManager;

    @Autowired
    protected FamilyManager familyManager;

    @Autowired
    protected SignConfig signConfig;

    @Autowired
    protected SignFlowManager signFlowManager;

    /**
     * 检查PGC多实名账号签约
     * @return
     */
    public Pair<Integer, String> checkPGCUnionSign(Long playerSignId){
        //获取签约信息  检查冷冻期
        PageBean<NjAndPlayerContractBean> contractListPage = nonContractManager.queryList(QueryNonContractDTO.builder()
                .contractId(playerSignId)
                .build());
        List<NjAndPlayerContractBean> contractList = contractListPage.getList();
        if (CollectionUtils.isEmpty(contractList)) {
            LogContext.addResLog("contract is not exist");
            return Pair.of(-1, null);
        }
        NjAndPlayerContractBean contractBean = contractList.get(0);

        return checkAccountRelation(contractBean.getPlayerUserId(), contractBean.getNjUserId(), this::checkUnionPlayerSignLimit);
    }

    /**
     * 校验
     * @param njId
     * @param familyUserId
     * @return
     */
    public Pair<Integer, String> checkVerifyNjUnionFamily(Long njId, Long familyUserId){
        if (!signConfig.getBizConfig().isVerifyUnionFamily()) {
            return Pair.of(0, null);
        }

        return checkAccountRelation(njId, familyUserId, this::checkUnionAdminSignLimit);
    }

    private Pair<Integer, String> checkAccountRelation(Long userId, Long subjectId, BiFunction<Long, List<Long>, Boolean> verifyFunc){
        String idCardNumber = getIdCardNumber(userId);
        if (StringUtils.isBlank(idCardNumber)) {
            LogContext.addResLog("idCardNumber is blank");
            //业务忽略了该异常
            return Pair.of(0, null);
        }

        List<Long> userIds = userManager.getUserAuthUnionIdList(idCardNumber);
        if (CollectionUtils.isNotEmpty(userIds)) {
            userIds.removeIf(this::isUserBand);
        }
        if (CollectionUtils.isEmpty(userIds)) {
            LogContext.addResLog("union user list is empty");
            //业务忽略了该异常
            return Pair.of(0, null);
        }

        List<Long> familyIds = userIds.stream().map(this::getUserFamilyId)
                .filter(c -> c != 0).distinct().collect(Collectors.toList());
        if(familyIds.size() > 1) {
            LogContext.addResLog("familyIds more than one");
            return Pair.of(SignErrorCode.USER_SAME_ID_ACCOUNT_JOIN_FAMILY, "操作失败，同实名其他账号已加入家族");
        }

        // 判断uid列表的家族与 管理员的家族是否一致
        if (checkUnionFamilyId(subjectId, familyIds)) {
            LogContext.addResLog("checkUnionFamilyId true");
            return Pair.of(SignErrorCode.USER_SAME_ID_ACCOUNT_JOIN_FAMILY, "操作失败，同实名其他账号已加入家族");
        }

        if (verifyFunc.apply(subjectId, userIds)) {
            LogContext.addResLog("checkUnionPlayerSignLimit true");
            return Pair.of(SignErrorCode.USER_SAME_ID_ACCOUNT_FREEZE, "操作失败，同实名其他账号处于冷冻期");
        }

        return Pair.of(0, null);
    }

    private boolean checkUnionPlayerSignLimit(long njId, List<Long> userAuthUnionIdList) {
        for(Long uid : userAuthUnionIdList) {
            Pair<Integer, String> verify = nonContractManager.checkInviteSignLimit(uid, njId, RoleEnum.PLAYER, RoleEnum.PLAYER);
            if(verify.getKey() != 0) {
                return true;
            }
        }
        return false;
    }

    private boolean checkUnionAdminSignLimit(long familyUserId, List<Long> userAuthUnionIdList) {
        for(Long uid : userAuthUnionIdList) {
            Pair<Integer, String> verify = nonContractManager.checkInviteSignLimit(familyUserId, uid, RoleEnum.ROOM, RoleEnum.ROOM);
            if(verify.getKey() != 0) {
                return true;
            }
        }
        return false;
    }

    private boolean checkUnionFamilyId(long userId, List<Long> familyIdList) {
        long userBelongFamilyId = getUserFamilyId(userId);
        for(Long familyId : familyIdList) {
            if(userBelongFamilyId != familyId) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否为PGC家族
     * @param familyId
     * @return
     */
    protected boolean isPGC(Long familyId) {
        Optional<FamilyBean> familyOp = familyManager.getFamily(ContextUtils.getBusinessEvnEnum().appId(), familyId);
        if (!familyOp.isPresent()) {
            LogContext.addResLog("family is not pgc");
            return false;
        }
        return isPGC(familyOp.get());
    }

    protected boolean isPGC(FamilyBean familyBean) {
        return "C_FAMILY".equals(familyBean.getFamilyType());
    }

    /**
     * 获取用户家族ID
     * @param userId
     * @return
     */
    private Long getUserFamilyId(Long userId){
        Optional<FamilyBean> userFamilyOp = familyManager.getUserFamily(userId);
        if (!userFamilyOp.isPresent()) {
            LogContext.addResLog("family not found. userId={}", userId);
            return 0L;
        }
        FamilyBean bean = userFamilyOp.get();
        if (isPGC(bean)) {
            return bean.getId();
        }
        return 0L;
    }

    /**
     * 判断用户是否被封禁
     * @param userId
     * @return
     */
    protected boolean isUserBand(Long userId){
        return userManager.getUserBandStatus(userId) == 3;
    }

    /**
     * 性别信息是否完整
     * @param userId
     * @return
     */
    protected boolean genderPass(Long userId){
        List<SimpleUserDto> curUserList = userManager.getSimpleUserByIds(Lists.newArrayList(userId));
        if (CollectionUtils.isEmpty(curUserList)) {
            return false;
        }
        SimpleUserDto userDto = curUserList.get(0);
        if (userDto.getGender() == null || userDto.getGender() == 100) {
            return false;
        }
        return true;
    }

    /**
     * 获取身份证号码
     * @param userId
     * @return
     */
    private String getIdCardNumber(Long userId){
        Optional<UserVerifyDataDTO> verifyData = userManager.getVerifyData(userId);
        if (!verifyData.isPresent()) {
            return "";
        }
        return verifyData.get().getIdCardNumber();
    }

}
