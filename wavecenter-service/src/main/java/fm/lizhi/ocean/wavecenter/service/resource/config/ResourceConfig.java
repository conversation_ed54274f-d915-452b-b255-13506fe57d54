package fm.lizhi.ocean.wavecenter.service.resource.config;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.config.AbsBizConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @date 2025/3/25 19:11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ConfigurationProperties(prefix = "wavecenter-resource")
public class ResourceConfig extends AbsBizConfig<BizResourceConfig> {

    /**
     * 查询推荐卡使用记录列表的最大厅数
     */
    private int maxQueryRecommendCardUseRecordCount = 100;

    private HyResourceConfig hy = new HyResourceConfig();

    private PpResourceConfig pp = new PpResourceConfig();

    private XmResourceConfig xm = new XmResourceConfig();

    public ResourceConfig() {
        bizConfigMap.put(BusinessEvnEnum.HEI_YE.appId(), hy);
        bizConfigMap.put(BusinessEvnEnum.PP.appId(), pp);
        bizConfigMap.put(BusinessEvnEnum.XIMI.appId(), xm);
    }
}
