package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/4/16 17:24
 */
@Data
@Accessors(chain = true)
public class ActivityApplyDecorateDTO {

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 活动申请ID
     */
    private Long activityId;

    /**
     * 装扮ID
     */
    private Long decorateId;

    /**
     * 装扮类型 1=头像框,2=房间背景,3=座驾,4=勋章
     * @see fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum
     */
    private Integer decorateType;

}
