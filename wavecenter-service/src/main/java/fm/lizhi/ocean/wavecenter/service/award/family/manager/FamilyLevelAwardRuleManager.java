package fm.lizhi.ocean.wavecenter.service.award.family.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.ListFamilyLevelAwardRuleBean;
import fm.lizhi.ocean.wavecenter.api.award.family.request.*;
import fm.lizhi.ocean.wavecenter.api.award.family.response.ResponseGetFamilyLevelAwardRule;

import java.util.List;

public interface FamilyLevelAwardRuleManager {

    /**
     * 创建公会等级奖励规则
     *
     * @param request 请求参数
     * @return 创建结果
     */
    Result<Void> createRule(RequestCreateFamilyLevelAwardRule request);

    /**
     * 更新公会等级奖励规则
     *
     * @param request 请求参数
     * @return 更新结果
     */
    Result<Void> updateRule(RequestUpdateFamilyLevelAwardRule request);

    /**
     * 删除公会等级奖励规则
     *
     * @param request 请求参数
     * @return 删除结果
     */
    Result<Void> deleteRule(RequestDeleteFamilyLevelAwardRule request);

    /**
     * 列出公会等级奖励规则
     *
     * @param request 请求参数
     * @return 列表结果
     */
    Result<List<ListFamilyLevelAwardRuleBean>> listRule(RequestListFamilyLevelAwardRule request);

    /**
     * 获取公会等级奖励规则
     *
     * @param request 请求参数
     * @return 获取结果
     */
    Result<ResponseGetFamilyLevelAwardRule> getFamilyLevelAwardRule(RequestGetFamilyLevelAwardRule request);
}
