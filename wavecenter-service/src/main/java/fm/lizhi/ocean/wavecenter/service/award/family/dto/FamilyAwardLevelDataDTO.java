package fm.lizhi.ocean.wavecenter.service.award.family.dto;

import fm.lizhi.ocean.wavecenter.common.utils.DateTimeUtils;
import lombok.Data;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.beans.Transient;
import java.util.Date;

/**
 * 公会奖励依赖的等级数据DTO
 */
@Data
public class FamilyAwardLevelDataDTO {

    /**
     * 应用id
     */
    @NotNull(message = "appId不能为null")
    @Min(value = 1, message = "appId必须大于0")
    private Integer appId;

    /**
     * 公会id(家族id)
     */
    @NotNull(message = "familyId不能为null")
    @Min(value = 1, message = "familyId必须大于0")
    private Long familyId;

    /**
     * 等级id
     */
    @NotNull(message = "levelId不能为null")
    @Min(value = 1, message = "levelId必须大于0")
    private Long levelId;

    /**
     * 等级有效周期的开始时间
     */
    @NotNull(message = "startTime不能为null")
    private Date startTime;

    /**
     * 等级有效周期的结束时间
     */
    @NotNull(message = "endTime不能为null")
    private Date endTime;

    @Transient
    @AssertTrue(message = "startTime必须为周一的00:00:00.000")
    private boolean isStartTimeValid() {
        return DateTimeUtils.isMondayStartTime(startTime);
    }

    @Transient
    @AssertTrue(message = "endTime必须为周日的23:59:59.999")
    private boolean isEndTimeValid() {
        return DateTimeUtils.isSundayEndTime(endTime);
    }

    @Transient
    @AssertTrue(message = "startTime和endTime必须是同一周的开始和结束时间")
    private boolean isStartTimeEndTimeValid() {
        return DateTimeUtils.isSameWeekStartEnd(startTime, endTime);
    }
}
