package fm.lizhi.ocean.wavecenter.service.datacenter.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/12/17 16:21
 */
@Data
public class DataRoomDayDTO {

    private Long id;

    /**
     * 业务
     */
    private Integer appId;

    /**
     * 日期 格式 YYYY-MM-DD
     */
    private Date statDate;

    /**
     * 日期 格式  YYYYMMDD
     */
    private Integer statDateValue;

    /**
     * 厅主ID
     */
    private Long roomId;

    /**
     * 厅收入
     */
    private BigDecimal income;

    /**
     * 厅魅力值
     */
    private Integer charm;

    /**
     * 开播时长(分钟)
     */
    private BigDecimal openDuration;

    /**
     * 厅签约主播数
     */
    private Integer signPlayerCnt;

    /**
     * 有收入主播人数
     */
    private Integer incomePlayerCnt;

    /**
     * 有收入主播占比
     */
    private BigDecimal incomePlayerRate;

    /**
     * 人均收入
     */
    private BigDecimal playerAvgIncome;

    /**
     * 人均魅力值
     */
    private BigDecimal playerAvgCharm;

    /**
     * 送礼人数
     */
    private Integer giftUserCnt;

    /**
     * 送礼客单价
     */
    private BigDecimal giftUserPrice;

    /**
     * 私信人数
     */
    private Integer chatUserCnt;

    /**
     * 私信回复人数
     */
    private Integer replyChatUserCnt;

    /**
     * 私信进房人数
     */
    private Integer chatEnterRoomUserCnt;

    /**
     * 私信付费人数
     */
    private Integer chatGiftUserCnt;

    /**
     * 私信回复率
     */
    private BigDecimal replyChatRate;

    /**
     * 私信进房率
     */
    private BigDecimal chatEnterRoomRate;

    /**
     * 私信付费率
     */
    private BigDecimal chatGiftRate;

    /**
     * 邀请人数
     */
    private Integer inviteUserCnt;

    /**
     * 邀请进房人数
     */
    private Integer inviteEnterRoomUserCnt;

    /**
     * 邀请付费人数
     */
    private Integer inviteGiftUserCnt;

    /**
     * 邀请进房率
     */
    private BigDecimal inviteEnterRoomRate;

    /**
     * 邀请付费率
     */
    private BigDecimal inviteGiftRate;

    /**
     * 厅粉丝数
     */
    private Integer fansUserCnt;

    /**
     * 厅新增粉丝
     */
    private Integer newFansUserCnt;

    /**
     * 主播粉丝
     */
    private Integer playerFansUserCnt;

    /**
     * 主播新增粉丝
     */
    private Integer playerNewFansUserCnt;

    /**
     * 主播粉丝进房数
     */
    private Integer playerFansEnterUserCnt;

    /**
     * 主播粉丝送礼收入
     */
    private BigDecimal playerFansGiftIncome;

    /**
     * 主播粉丝送礼人数
     */
    private Integer playerFansGiftUserCnt;

    /**
     * 主播粉丝送礼客单价
     */
    private BigDecimal playerFansGiftUserPrice;

    /**
     * 进房人数
     */
    private Integer enterRoomUserCnt;

    /**
     * 上麦人数
     */
    private Integer upGuestPlayerCnt;

    /**
     * 直播间签约主播上麦人数
     */
    private Integer signUpGuestPlayerCnt;

    /**
     * 评论人数
     */
    private Integer commentUserCnt;

    /**
     * 上麦率
     */
    private BigDecimal upGuestRate;

    /**
     * 评论互动率
     */
    private BigDecimal commentRate;

    /**
     * 人均逗留时长(分钟)
     */
    private BigDecimal avgUserStayDuration;

    /**
     * 用户逗留人数(满1分钟)
     */
    private BigDecimal userFullOneMin;

    /**
     * 用户逗留人数(满3分钟)
     */
    private BigDecimal userFullThreeMin;

    /**
     * 用户逗留人数(满5分钟)
     */
    private BigDecimal userFullFiveMin;

    /**
     * 收入在公会的排名
     */
    private Integer incomeRank;

    /**
     * 魅力值在公会的排名
     */
    private Integer charmRank;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 总收入
     */
    private BigDecimal allIncome;

    /**
     * 签约厅收礼收入
     */
    private BigDecimal signHallIncome;

    /**
     * 官方厅收礼收入
     */
    private BigDecimal officialHallIncome;

    /**
     * 个播收礼收入
     */
    private BigDecimal personalHallIncome;

    /**
     * 贵族提成收入
     */
    private BigDecimal nobleIncome;

    /**
     * 个播贵族提成收入
     */
    private BigDecimal personalNobleIncome;

    /**
     * 主播上麦率
     */
    private BigDecimal upPlayerRate;

    /**
     * 厅评论互动率=评论人数/房间逗留人数(满1分钟)
     */
    private BigDecimal commentOneMinRate;

    /**
     * 厅嘉宾上麦率=上麦嘉宾数/房间逗留人数(满1分钟)
     */
    private BigDecimal upGuestOneMinRate;

    /**
     * 厅付费转化率(1min)
     */
    private BigDecimal giftOneMinRate;

    /**
     * 厅付费转化率(3min)
     */
    private BigDecimal giftThreeMinRate;

    /**
     * 厅付费转化率(5min)
     */
    private BigDecimal giftFiveMinRate;

    /**
     * 私信主播数 本厅签约主播中有私信行为的人数
     */
    private Integer chatPlayerCnt;

    /**
     * 有收入歌手数量
     */
    private Integer incomeSingerCnt;
}
