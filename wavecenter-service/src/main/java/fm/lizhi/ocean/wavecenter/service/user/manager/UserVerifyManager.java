package fm.lizhi.ocean.wavecenter.service.user.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.service.user.dto.GetUserVerifyResultDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.GetUserVerifyResultParamDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.SearchUserVerifyResultDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.SearchUserVerifyResultParamDTO;

public interface UserVerifyManager {

    /**
     * 获取用户认证结果
     *
     * @param param
     * @return
     */
    Result<GetUserVerifyResultDTO> getUserVerifyResult(GetUserVerifyResultParamDTO param);

    /**
     * 搜索用户认证结果
     *
     * @param param 参数
     * @return 结果
     */
    Result<SearchUserVerifyResultDTO> searchUserVerifyResult(SearchUserVerifyResultParamDTO param);

    /**
     * 搜索用户认证记录
     */
    public static final int SEARCH_USER_VERIFY_RESULT_FAIL = 1;

    /**
     * 搜索用户认证记录
     */
    public static final int SEARCH_USER_VERIFY_RESULT_NOT_FOUND = 2;

}
