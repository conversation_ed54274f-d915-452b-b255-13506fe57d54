package fm.lizhi.ocean.wavecenter.service.anchor.singer.handler;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerChatSceneEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerHallApplyStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.convert.SingerVerifyRecordConvert;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.*;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerChatManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerInfoManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerVerifyApplyManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SingerAuditPassedHandler extends AbstractSingerAuditHandler {

    @Autowired
    private SingerVerifyApplyManager singerVerifyApplyManager;
    @Autowired
    private SingerChatManager singerChatManager;
    @Autowired
    private SingerInfoManager singerInfoManager;

    @Override
    public SingerExecuteAuditDTO auditHandle(SingerAuditParamDTO param, SingerVerifyRecordDTO verifyRecord) {
        //查询是否已经是歌手了
        SingerInfoDTO singerInfo = singerInfoManager.getSingerInfo(verifyRecord.getAppId(), verifyRecord.getUserId(), verifyRecord.getSingerType());
        if (singerInfo != null && singerInfo.getSingerStatus() == SingerStatusEnum.EFFECTIVE.getStatus()) {
            //已经是歌手了，不能再提交申请
            return SingerExecuteAuditDTO.failure("用户已经是歌手了，不能审批通过");
        }
        if (singerInfo != null && singerInfo.getSingerStatus() == SingerStatusEnum.AUTHENTICATING.getStatus()
                && verifyRecord.getAuditStatus() != SingerAuditStatusEnum.SELECTED.getStatus()) {
            //如果歌手认证中，并且操作不是从：选中状态 -> 通过，则不能通过
            return SingerExecuteAuditDTO.failure("用户已经是在歌手认证中了，不能审批通过");
        }
        boolean success = false;
        SingerChatSceneEnum chatSceneEnum = null;
        if (param.getSingerHallStatus() == null || param.getSingerHallStatus() == SingerHallApplyStatusEnum.REJECTED) {
            //拒绝或者是不存在申请，直接设置预审不通过
            UpdateSingerVerifyStatusParamDTO paramDTO = SingerVerifyRecordConvert.I.buildUpdateParam(verifyRecord, param,
                    SingerAuditStatusEnum.PRE_AUDIT_REJECTED.getStatus(), SingerStatusEnum.ELIMINATED.getStatus(), true, param.getSingerType());
            paramDTO.setPreAuditRejectReason("【预审核】：点唱厅审核不通过");
            paramDTO.setTargetAuditStatus(SingerAuditStatusEnum.PRE_AUDIT_REJECTED.getStatus());
            boolean updateRes = singerVerifyApplyManager.updateSingerVerifyRecordStatus(paramDTO);
            chatSceneEnum = updateRes ? SingerChatSceneEnum.PRE_AUDIT_NOT_PASS_BY_SIGN : null;
            success = updateRes;
        } else if (param.getSingerHallStatus() == SingerHallApplyStatusEnum.APPLYED) {
            // 执行审核通过的操作
            boolean passRes = singerVerifyApplyManager.approveSingerVerifyRecord(verifyRecord.getId(), verifyRecord.getAuditStatus(), param.getSingerType(), param.getOperator());
            chatSceneEnum = passRes ? SingerChatSceneEnum.SINGER_AUDIT_PASS : null;
            success = passRes;
        } else if (param.getSingerHallStatus() == SingerHallApplyStatusEnum.APPLYING) {
            // 执行审核中的操作
            UpdateSingerVerifyStatusParamDTO paramDTO = SingerVerifyRecordConvert.I.buildUpdateParam(verifyRecord, param, SingerAuditStatusEnum.PASS.getStatus(),
                    SingerStatusEnum.AUTHENTICATING.getStatus(), true, param.getSingerType());
            boolean updateRes = singerVerifyApplyManager.updateSingerVerifyRecordStatus(paramDTO);
            chatSceneEnum = updateRes ? SingerChatSceneEnum.HOST_PASS_HALL_AUDITING : null;
            success = updateRes;
        }

        if (chatSceneEnum != null) {
            singerChatManager.sendAuditResultChat(verifyRecord.getAppId(), verifyRecord.getUserId(), 
                                  verifyRecord.getSingerType(), verifyRecord.getSongStyle(), chatSceneEnum);
        }
        return success ? SingerExecuteAuditDTO.success() : SingerExecuteAuditDTO.failure("修改状态失败，请稍候重试!");

    }

    @Override
    public SingerAuditStatusEnum getAuditStatusEnum() {
        return SingerAuditStatusEnum.PASS;
    }
}
