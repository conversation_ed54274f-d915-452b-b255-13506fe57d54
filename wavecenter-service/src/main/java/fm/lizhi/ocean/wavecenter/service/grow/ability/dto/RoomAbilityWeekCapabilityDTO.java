package fm.lizhi.ocean.wavecenter.service.grow.ability.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/10 19:12
 */
@Data
public class RoomAbilityWeekCapabilityDTO {

    private Long id;

    /**
     * 厅ID
     */
    private Long roomId;

    /**
     * 公会ID
     */
    private Long familyId;

    /**
     * 能力项code
     */
    private String capabilityCode;

    /**
     * 能力分
     */
    private BigDecimal abilityValue;

    /**
     * 厅在公会中的排名
     */
    private Integer roomInFamilyRank;

    /**
     * 跟上周期对比的排名变化情况
     */
    private Integer roomInFamilyCompareWeekRank;

}
