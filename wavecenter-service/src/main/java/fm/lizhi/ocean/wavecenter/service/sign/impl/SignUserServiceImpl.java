package fm.lizhi.ocean.wavecenter.service.sign.impl;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.bean.FamilyAndNjContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.IdentifyStatusEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.UserSignStatusEnum;
import fm.lizhi.ocean.wavecenter.api.sign.request.*;
import fm.lizhi.ocean.wavecenter.api.sign.response.*;
import fm.lizhi.ocean.wavecenter.api.sign.service.SignUserService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.base.processor.ProcessorFactory;
import fm.lizhi.ocean.wavecenter.service.sign.dto.OperateSignDTO;
import fm.lizhi.ocean.wavecenter.service.sign.dto.RequestFamilyAndNjContractDTO;
import fm.lizhi.ocean.wavecenter.service.sign.manager.ContractManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.NonContractManager;
import fm.lizhi.ocean.wavecenter.service.sign.process.UserSignProcessor;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@ServiceProvider
public class SignUserServiceImpl implements SignUserService {

    @Autowired
    private ProcessorFactory processorFactory;
    @Autowired
    private NonContractManager nonContractManager;
    @Autowired
    private UserManager userManager;
    @Autowired
    private ContractManager contractManager;

    @Override
    public Result<ResponseUserApplyPlayer> applyPlayer(RequestUserApplyPlayer request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        return ResultHandler.handle(request.getAppId(), () -> {
            UserSignProcessor processor = getProcessor();

            ResponseUserApplyPlayer checkResult = processor.userApplyPlayerCheck(request);
            if (checkResult.getCode() != 0) {
                LogContext.addResLog("checkResultCode={}", checkResult.getCode());
                return RpcResult.success(checkResult);
            }

            //是否完成平台实名认证
            boolean isVerify = userManager.checkUserRealNameAuthStatus(request.getCurUserId(), request.getAppId());
            if (!isVerify) {
                return RpcResult.success(new ResponseUserApplyPlayer().setCode(SignUserService.APPLY_PLAYER_REQ_USER_PLATFORM_VERIFY_NO_PASS));
            }

            ResponseUserApplyPlayer applyRes = nonContractManager.userApplyPlayer(request);
            LogContext.addResLog("applyResCode={}", applyRes.getCode());
            return RpcResult.success(applyRes);
        });
    }

    @Override
    public Result<ResponseUserSignAdminInvite> signAdminInvite(RequestUserSignAdminInvite request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);
        return ResultHandler.handle(request.getAppId(), () -> {
            UserSignProcessor signProcessor = getProcessor();

            ResponseUserSignAdminInvite checkResult = signProcessor.signAdminInviteCheck(request);
            if (checkResult.getCode() != 0) {
                LogContext.addResLog("checkResultCode={}", checkResult.getCode());
                return RpcResult.success(checkResult);
            }

            //是否完成平台实名认证
            boolean isVerify = userManager.checkUserRealNameAuthStatus(request.getCurUserId(), request.getAppId());
            if (!isVerify) {
                return RpcResult.success(new ResponseUserSignAdminInvite().setCode(SignUserService.SIGN_ADMIN_INVITE_NO_VERIFY));
            }

            OperateSignDTO opRes = nonContractManager.operateSign(request.getPlayerSignId(), request.getCurUserId()
                    , ContractTypeEnum.SIGN, RoleEnum.PLAYER, request.getOperateTypeEnum());
            LogContext.addResLog("opResCode={}", opRes.getCode());

            return RpcResult.success(new ResponseUserSignAdminInvite()
                    .setCode(opRes.getCode())
                    .setMsg(opRes.getMsg()));
        });
    }

    @Override
    public Result<ResponseUserIdentifyStatus> identifyStatus(RequestUserIdentifyStatus request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        return ResultHandler.handle(request.getAppId(), ()->{
            //平台认证状态
            boolean platformStatus = userManager.checkUserRealNameAuthStatus(request.getUserId(), request.getAppId());

            //上上签认证状态
            IdentifyStatusEnum signRealNameStatus = contractManager.getSignRealNameStatus(request.getUserId());

            return RpcResult.success(new ResponseUserIdentifyStatus()
                    .setPlatformStatus(platformStatus ? IdentifyStatusEnum.FINISHED.getCode() : IdentifyStatusEnum.UNFINISHED.getCode())
                    .setBestSignStatus(signRealNameStatus.getCode())
            );
        });
    }

    @Override
    public Result<ResponseUserInfoStatus> infoStatus(RequestUserInfoStatus request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        return ResultHandler.handle(request.getAppId(), ()->{
            UserSignProcessor processor = getProcessor();
            ResponseUserInfoStatus response = processor.signInfoCheck(request);
            LogContext.addResLog("infoStatus={}", response.getInfoStatus());
            return RpcResult.success(response);
        });
    }

    @Override
    public Result<ResponseUserApplyAdmin> applyAdmin(RequestUserApplyAdmin request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        return ResultHandler.handle(request.getAppId(), ()->{
            UserSignProcessor processor = getProcessor();

            //用户信息检查
            ResponseUserInfoStatus userInfoCheckRes = processor.signInfoCheck(RequestUserInfoStatus.builder()
                    .appId(request.getAppId())
                    .userId(request.getCurUserId())
                    .build());
            if (!IdentifyStatusEnum.FINISHED.getCode().equals(userInfoCheckRes.getInfoStatus())) {
                return RpcResult.success(new ResponseUserApplyAdmin()
                        .setCode(SignUserService.APPLY_ADMIN_USER_INFO_NOT_EXIST));
            }

            //是否存在其他家族的申请
            PageBean<FamilyAndNjContractBean> pageBean = contractManager.queryContract(RequestFamilyAndNjContractDTO.builder()
                    .njId(request.getCurUserId())
                    .otherFamilyId(request.getFamilyId())
                    .type(ContractTypeEnum.SIGN)
                    .type(ContractTypeEnum.SUBJECT_CHANGE)
                    .type(ContractTypeEnum.RENEW)
                    .relation(SignRelationEnum.SIGNING)
                    .relation(SignRelationEnum.WAIT_SIGN)
                    .build());
            if (CollectionUtils.isNotEmpty(pageBean.getList())) {
                return RpcResult.success(new ResponseUserApplyAdmin()
                        .setCode(-1).setMsg("当前用户正在申请成为家族管理员，请勿重复申请"));
            }

            ResponseUserApplyAdmin checkRes = processor.checkApplyAdmin(request);
            LogContext.addResLog("checkRes={}", checkRes.getCode());
            if (checkRes.getCode() != 0) {
                return RpcResult.success(checkRes);
            }

            Optional<FamilyAndNjContractBean> existContractOp = processor.existWaitSignContract(request);
            if (existContractOp.isPresent() && StringUtils.isNotBlank(existContractOp.get().getSignId())) {
                ResponseUserApplyAdmin res = new ResponseUserApplyAdmin().setCode(0);
                if (SignRelationEnum.SIGN_SUCCESS.getCode().equals(existContractOp.get().getStatus())) {
                    return RpcResult.success(res
                            .setCode(-1)
                            .setMsg("已签署")
                    );
                }

                //请求用户已签署也算完成
                Map<Long, UserSignStatusEnum> contractUserSignStatus = contractManager.getContractUserSignStatus(existContractOp.get().getSignId());
                if (contractUserSignStatus.get(request.getCurUserId()) == UserSignStatusEnum.SIGN_SUCCESS) {
                    return RpcResult.success(res
                            .setCode(-1)
                            .setMsg("已签署")
                    );
                }

                return RpcResult.success(res.setSignId(existContractOp.get().getSignId()));
            }

            ResponseUserApplyAdmin res = contractManager.userApplyAdmin(request);
            LogContext.addResLog("resCode={}", res.getCode());
            if (res.getCode() != ResponseSignResult.SUCCESS_CODE) {
                return RpcResult.success(res);
            }

            if (StringUtils.isBlank(res.getSignId())) {
                LogContext.addResLog("signId is blank");
                return RpcResult.success(res.setCode(-1));
            }

            Optional<String> urlOp = contractManager.genContractSignUrl(request.getCurUserId(), res.getSignId());
            if (!urlOp.isPresent()) {
                LogContext.addResLog("url is blank");
                return RpcResult.success(res.setCode(-1));
            }
            res.setContractUrl(urlOp.get());

            if (res.getContractId() != null) {
                //标记待同步签署状态
                processor.applyAdminSuccessProcessor(request, res.getContractId());
            }

            return RpcResult.success(res);
        });
    }

    @Override
    public Result<ResponseUserDoSign> doSign(RequestUserDoSign request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        return ResultHandler.handle(request.getAppId(), ()->{
            UserSignProcessor processor = getProcessor();

            ResponseUserDoSign checkRes = processor.doSignAdminCheck(request);
            if (checkRes.getCode() != 0) {
                LogContext.addResLog("checkResCode={}", checkRes.getCode());
                return RpcResult.success(checkRes);
            }

            Optional<String> signIdOp = processor.doSignGenSignId(request);
            if (!signIdOp.isPresent()) {
                LogContext.addResLog("signId is not present");
                return RpcResult.fail(SignUserService.DO_SIGN_FAIL);
            }

            Optional<String> contractUrlOp = contractManager.genContractSignUrl(request.getCurUserId(), signIdOp.get());
            if (!contractUrlOp.isPresent()) {
                LogContext.addResLog("contractUrl is not present");
                return RpcResult.fail(SignUserService.DO_SIGN_FAIL);
            }

            processor.doSignSuccessProcessor(request);

            return RpcResult.success(new ResponseUserDoSign().setContractUrl(contractUrlOp.get()));
        });
    }

    private UserSignProcessor getProcessor(){
        return processorFactory.getProcessor(UserSignProcessor.class);
    }
}
