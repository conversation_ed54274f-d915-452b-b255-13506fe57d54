package fm.lizhi.ocean.wavecenter.service.background.activitycenter.impl;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityBigClassBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityClassConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestDeleteActivityBigClass;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestDeleteActivityClassification;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityBigClass;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityClassification;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityBigClass;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityClassification;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityClassificationConfigService;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityClassificationManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 活动分类
 *
 * <AUTHOR>
 */
@ServiceProvider
@Slf4j
public class ActivityClassificationConfigServiceImpl implements ActivityClassificationConfigService {

    @Autowired
    private ActivityClassificationManager activityClassificationManager;

    @Override
    public Result<Void> saveBigClassification(RequestSaveActivityBigClass req) {
        LogContext.addReqLog("`req={}", req);
        LogContext.addResLog("`req={}", req);
        return ResultHandler.handle(req.getAppId(), () -> activityClassificationManager.saveBigClassification(req));
    }

    @Override
    public Result<Void> updateBigClassification(RequestUpdateActivityBigClass req) {
        LogContext.addReqLog("`req={}", req);
        LogContext.addResLog("`req={}", req);
        return ResultHandler.handle(req.getAppId(), () -> activityClassificationManager.updateBigClassification(req));
    }

    @Override
    public Result<Void> deleteBigClassification(RequestDeleteActivityBigClass req) {
        LogContext.addReqLog("`req={}", req);
        LogContext.addResLog("`req={}", req);
        return ResultHandler.handle(req.getAppId(), () -> activityClassificationManager.deleteBigClassification(req));
    }

    @Override
    public Result<List<ActivityBigClassBean>> listBigClassByAppId(int appId) {
        LogContext.addReqLog("`appId={}", appId);
        LogContext.addResLog("`appId={}", appId);
        return activityClassificationManager.listBigClassByAppId(appId);
    }

    @Override
    public Result<Void> saveClassification(RequestSaveActivityClassification req) {
        LogContext.addReqLog("`req={}", req);
        LogContext.addResLog("`req={}", req);
        return ResultHandler.handle(req.getAppId(), () -> activityClassificationManager.saveClassification(req));
    }

    @Override
    public Result<Void> updateClassification(RequestUpdateActivityClassification req) {
        LogContext.addReqLog("`req={}", req);
        LogContext.addResLog("`req={}", req);
        return ResultHandler.handle(req.getAppId(), () -> activityClassificationManager.updateClassification(req));
    }

    @Override
    public Result<Void> deleteClassification(RequestDeleteActivityClassification req) {
        LogContext.addReqLog("`req={}", req);
        LogContext.addResLog("`req={}", req);
        return ResultHandler.handle(req.getAppId(), () -> activityClassificationManager.deleteClassification(req));
    }

    @Override
    public Result<List<ActivityClassConfigBean>> listClassificationByBigClassId(long bigClassId) {
        LogContext.addReqLog("bigClassId={}", bigClassId);
        LogContext.addResLog("bigClassId={}", bigClassId);
        return activityClassificationManager.listClassificationByBigClassId(bigClassId);
    }
}
