package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.Data;

import java.util.Date;

@Data
public class ActivitySimpleInfoDT0 {

    /**
     * 活动ID
     */
    private Long id;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动报名类型
     */
    private Integer applyType;

    /**
     * 活动审核状态
     */
    private Integer auditStatus;

    /**
     * 活动联系方式
     */
    private String contact;

    /**
     * 活动开始时间
     */
    private Date startTime;

    /**
     * 活动结束时间
     */
    private Date endTime;

    /**
     * 活动提报时间
     */
    private Date createTime;

    /**
     * 活动联系方式号码
     */
    private String contactNumber;

    /**
     * 活动主持ID
     */
    private Long hostId;

    /**
     * 审核原因
     */
    private String auditReason;

    /**
     * 活动分类ID
     */
    private Long classId;

    /**
     * 提报人ID
     */
    private Long applicantUid;

    /**
     * 提报家族ID
     */
    private Long familyId;

    /**
     * 提报厅厅主ID
     */
    private Long njId;

    /**
     * 审核操作人
     */
    private String auditOperator;

       /**
     * 版本号
     */
    private Integer version;
}
