package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * 发送活动数据报告
 * <AUTHOR>
 */
@Data
@Builder
public class SendReportDataInfoDTO {

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 应用id
     */
    private Integer appId;

    /**
     * 厅主id
     */
    private Long njId;


    /**
     * 申请者uid
     */
    private Long applicantUid;

    /**
     * 结束时间
     */
    private Date endTime;

}
