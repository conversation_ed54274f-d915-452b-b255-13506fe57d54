package fm.lizhi.ocean.wavecenter.service.anchor.singer.handler;

import fm.lizhi.ocean.wavecenter.service.common.handler.BaseHandlerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class SingerAuditStatusHandlerFactory extends BaseHandlerFactory<Integer, SingerAuditStatusHandler> {

    @Autowired
    private List<SingerAuditStatusHandler> filters;

    @Override
    public void registerAllHandlers() {
        for (SingerAuditStatusHandler handler : filters) {
            registerHandler(handler.getAuditStatusEnum().getStatus(), handler);
        }
    }
}
