package fm.lizhi.ocean.wavecenter.service.grow.ability.handler.parser;

import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.condition.AbstractCondition;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.condition.ComparatorEnum;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.condition.number.NumberEqCondition;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.condition.number.NumberGtCondition;
import fm.lizhi.ocean.wavecenter.service.grow.ability.handler.ConditionParser;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto.ConditionDTO;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/9 14:46
 */
@Component
public class NumberGtParser implements ConditionParser {

    @Override
    public ComparatorEnum getComparator() {
        return ComparatorEnum.NUM_GT;
    }

    @Override
    public AbstractCondition<?, ?> parse(ConditionDTO conditionDTO) {
        Assert.notNull(conditionDTO, "conditionDTO is null");
        Assert.notNull(conditionDTO.getMetricCode(), "metricCode is null");
        Assert.notNull(conditionDTO.getValue(), "value is null");
        return new NumberGtCondition(conditionDTO.getMetricCode(), new BigDecimal(conditionDTO.getValue()));
    }

}
