package fm.lizhi.ocean.wavecenter.service.grow.ability.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/10 20:01
 */
@Data
public class GrowRoomMetricValueDTO {

    /**
     * 厅ID
     */
    private Long roomId;

    /**
     * 周开始日期 格式 YYYY-MM-DD
     */
    private Date startWeekDate;

    /**
     * 周结束日期 格式 YYYY-MM-DD
     */
    private Date endWeekDate;

    /**
     * 私信人数
     */
    private BigDecimal chatUserCnt;

    /**
     * 私信回复人数(有效私信用户)
     */
    private BigDecimal replyChatUserCnt;

    /**
     * 私信回复新用户人数(有效私信新用户数)
     */
    private BigDecimal replyChatNewUserCnt;

    /**
     * 送礼人数(付费用户数)
     */
    private BigDecimal giftUserCnt;

    /**
     * 送礼新用户人数(付费新用户数)
     */
    private BigDecimal giftNewUserCnt;

    /**
     * 总收入
     */
    private BigDecimal allIncome;

    /**
     * 上麦时长(分钟)
     */
    private BigDecimal upGuestDur;

    /**
     * 新增粉丝数(累计新增粉丝)
     */
    private BigDecimal newFansUserCnt;

    /**
     * 违规次数
     */
    private BigDecimal violationCnt;

    /**
     * 有效麦序数
     */
    private BigDecimal checkInCnt;

}
