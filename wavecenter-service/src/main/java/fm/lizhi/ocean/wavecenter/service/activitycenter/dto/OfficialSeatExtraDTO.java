package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 官频位跳转配置
 */
@Data
@Accessors(chain = true)
public class OfficialSeatExtraDTO {

    /**
     * 直播间标题颜色（PP才有）
     */
    private String liveTitleColor;


    /**
     * 配置备注
     */
    private String note;

    /**
     * 模板，0表示无模板
     */
    private int template;

    /**
     * 跳转action
     * PP的跳转信息都在这里
     */
    private String action;

    /**
     * 活动类型
     */
    private Integer activityType;
}
