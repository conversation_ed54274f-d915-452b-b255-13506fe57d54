package fm.lizhi.ocean.wavecenter.service.sign.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.sign.bean.EnterpriseInfoBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.GuildFullInfoBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyAuthBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.RoomSignBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.service.user.dto.PlayerSignInfoDto;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/4/10 17:33
 */
public interface FamilyManager {

    /**
     * 获取当前陪玩签约的厅主id
     *
     * @param userId 用户id
     * @return 厅主id
     */
    Optional<Long> playerCurSignNj(long userId);


    /**
     * 通过家族长用户ID查询家族
     * @param userId
     * @return
     */
    Optional<FamilyBean> getFamilyByUserId(long userId);

    /**
     * 查询用户家族信息
     *
     * @param userId
     * @return
     */
    Optional<FamilyBean> getUserFamily(long userId);

    /**
     * 查询用户在家族中的角色
     *
     * @param userId
     * @return
     */
    UserInFamilyBean getUserInFamily(long userId);

    /**
     * 获取厅签约信息
     *
     * @param
     * @return
     */
    Optional<RoomSignBean> getRoomSign(long familyId, long njId);

    /**
     * 获取厅主最近签约家族ID
     *
     * @param roomId
     * @return
     */
    Optional<Long> getRoomBestFamily(long roomId);

    /**
     * 查询厅指定时间签约的公会
     * @param roomId
     * @return
     */
    Optional<Long> getRoomSignFamilyInDate(long roomId, Date date);

    /**
     * 获取陪玩最近签约厅主
     *
     * @param userId
     * @return
     */
    Optional<Long> getUserBestNj(long userId);

    /**
     * 查询陪玩在公会下最近签约厅ID
     *
     * @param appId
     * @param familyId
     * @param playerId
     * @return
     */
    Long getPlayerLastRoom(int appId, long familyId, long playerId);

    /**
     * 查询家族认证信息
     *
     * @param familyId
     * @return
     */
    Optional<FamilyAuthBean> getUserFamilyAuth(long familyId);

    /**
     * 获取陪玩最新签约信息
     *
     * @param userId
     * @return
     */
    Optional<PlayerSignInfoDto> getLatestSignRecord(List<Long> roomIds, long userId);

    /**
     * 获取陪玩最近签约的家族ID
     *
     * @param playerId
     * @return
     */
    Optional<Long> getPlayerBestFamily(long playerId);

    Optional<FamilyBean> getFamily(int appId, long familyId);

    Optional<FamilyBean> getFamilyByCache(long familyId);

    /**
     * 获取厅主最近签约家族ID
     *
     * @param roomId
     * @return
     */
    Optional<Long> getRoomBestFamilyByCache(long roomId);

    /**
     * 获取公会下所有签约厅主ID
     * @param familyId
     * @return
     */
    Set<Long> getSignNjIds(Long familyId);

    /**
     * 查询公会完整信息
     * @param familyId
     * @return
     */
    GuildFullInfoBean getFullInfo(long familyId);

    /**
     * 统计家族可开厅数
     * @param familyId
     * @return
     */
    Integer countCanOpenRoomNum(long familyId);

    /**
     * 统计家族已签约厅数
     * @param familyId
     * @return
     */
    Integer countSignRoomNum(long familyId);

    /**
     * 家族是否已经通过认证
     * @param familyId
     * @return
     */
    boolean isFamilyVerifyPass(long familyId);

    /**
     * 家族认证的银行卡是否通过认证
     * @param familyId
     * @return
     */
    boolean isBankCardVerifyPass(long familyId);

    /**
     * 获取家族企业信息
     * @param familyId
     * @return
     */
    Optional<EnterpriseInfoBean> getFamilyEnterpriseInfo(long familyId);

    /**
     * 分页获取家族ID列表
     * @param lastFamilyId
     * @param pageSize
     * @return
     */
    List<Long> getFamilyIdsByPage(Long lastFamilyId, Integer pageSize);

    /**
     * 通过家族ID查询公会编码
     * @param familyId
     * @return
     */
    Optional<String> getSocietyCode(Long familyId);

}
