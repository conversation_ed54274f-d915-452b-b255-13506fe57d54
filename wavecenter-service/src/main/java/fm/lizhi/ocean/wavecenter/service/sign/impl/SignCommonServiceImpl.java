package fm.lizhi.ocean.wavecenter.service.sign.impl;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.FamilyAndNjContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestGetContractViewUrl;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseSignContractUrl;
import fm.lizhi.ocean.wavecenter.api.sign.service.SignCommonService;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.sign.dto.RequestFamilyAndNjContractDTO;
import fm.lizhi.ocean.wavecenter.service.sign.manager.ContractManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/10/22 20:09
 */
@Slf4j
@ServiceProvider
public class SignCommonServiceImpl implements SignCommonService {

    @Autowired
    private ContractManager contractManager;
    @Autowired
    private FamilyManager familyManager;

    @Override
    public Result<String> getSignToken(int appId, long userId) {
        LogContext.addReqLog("appId={},userId={}", appId, userId);
        LogContext.addResLog("appId={},userId={}", appId, userId);
        return ResultHandler.handle(appId, () -> {
            Optional<String> signTokenOp = contractManager.getSignToken(userId);
            return signTokenOp.map(RpcResult::success).orElseGet(() -> RpcResult.fail(GET_SIGN_TOKEN_NOT_EXIST));
        });
    }

    @Override
    public Result<String> getContractViewUrl(RequestGetContractViewUrl request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);
        return ResultHandler.handle(request.getAppId(), ()->{
            PageBean<FamilyAndNjContractBean> pageBean = contractManager.queryContract(RequestFamilyAndNjContractDTO.builder()
                    .contractId(request.getContractId())
                    .pageSize(1)
                    .build());

            if (CollectionUtils.isEmpty(pageBean.getList())) {
                LogContext.addResLog("pageList is empty");
                return RpcResult.fail(GET_CONTRACT_VIEW_URL_FAIL);
            }
            FamilyAndNjContractBean contract = pageBean.getList().get(0);

            Optional<FamilyBean> familyOp = familyManager.getFamily(request.getAppId(), contract.getFamilyId());
            if (!familyOp.isPresent()) {
                LogContext.addResLog("family is not present");
                return RpcResult.fail(GET_CONTRACT_VIEW_URL_FAIL);
            }
            FamilyBean family = familyOp.get();

            if (!(request.getCurUserId().equals(contract.getNjUserId() )
                    || request.getCurUserId().equals(family.getUserId()))) {
                LogContext.addResLog("no auth");
                return RpcResult.fail(GET_CONTRACT_VIEW_URL_AUTH_FAIL);
            }

            Optional<String> urlOp = contractManager.genContactViewUrl(contract.getSignId());
            if (!urlOp.isPresent()) {
                LogContext.addResLog("gen url fail");
                return RpcResult.fail(GET_CONTRACT_VIEW_URL_FAIL);
            }

            return RpcResult.success(urlOp.get());
        });
    }


    @Override
    public Result<ResponseSignContractUrl> getSignContractUrl(int appId, Long userId, String signId) {
        LogContext.addReqLog("signId={},userId={},appId={}", signId, userId, appId);
        LogContext.addResLog("signId={},userId={},appId={}", signId, userId, appId);

        return ResultHandler.handle(appId, () -> {
            Optional<String> contractSignUrl = contractManager.genContractSignUrl(userId, signId);
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, new ResponseSignContractUrl().setContractUrl(contractSignUrl.orElse("")));
        });



    }
}
