package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class SaveOfficialSeatParamDTO {

    /**
     * 活动名称
     */
    private String name;
    /**
     * 开始时间戳
     */
    private Date startTime;
    /**
     * 结束时间戳
     */
    private Date endTime;

    /**
     * 背景图url
     */
    private String backgroundUrl;

    /**
     * 位置
     */
    private Integer position;

    /**
     * tabId
     */
    private Long tabId;

    /**
     * 用户ids
     */
    private List<Long> userIds;


    /**
     * 扩展配置
     */
    private OfficialSeatExtraDTO extra;

}
