package fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityBigClassBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityClassConfigBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseActivityClassification;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityClassificationConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.*;
import fm.lizhi.ocean.wavecenter.api.live.constants.RoomCategoryEnum;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 活动分类
 * 活动大类 -> 活动分类 (1:N)
 *
 * <AUTHOR>
 */
public interface ActivityClassificationManager {

    /**
     * 保存活动大类
     */
    Result<Void> saveBigClassification(RequestSaveActivityBigClass req);

    /**
     * 更新活动大类
     */
    Result<Void> updateBigClassification(RequestUpdateActivityBigClass req);

    /**
     * 删除活动大类
     */
    Result<Void> deleteBigClassification(RequestDeleteActivityBigClass req);

    /**
     * 查询活动大类列表
     */
    Result<List<ActivityBigClassBean>> listBigClassByAppId(int appId);

    /**
     * 保存活动分类
     */
    Result<Void> saveClassification(RequestSaveActivityClassification req);

    /**
     * 更新活动分类
     */
    Result<Void> updateClassification(RequestUpdateActivityClassification req);

    /**
     * 删除活动分类
     */
    Result<Void> deleteClassification(RequestDeleteActivityClassification req);

    /**
     * 查询活动分类列表
     */
    Result<List<ActivityClassConfigBean>> listClassificationByBigClassId(long bigClassId);

    /**
     * 批量查询活动分类, 该接口会去重
     */
    List<ActivityClassificationConfigBean> batchActivityClassification(List<Long> classIds);

    /**
     * 根据分类ID查询活动分类信息
     */
    ActivityClassificationConfigBean getActivityClassification(Long classId);

    /**
     * 查询活动分类列表
     */
    Result<List<ResponseActivityClassification>> getClassificationList(int appId, ArrayList<Integer> categoryValue);

    /**
     * 根据等级ID查询活动分类列表ID
     */
    List<Long> getClassificationIdListByLevelId(Long levelId);

    /**
     * 查询子分类的品类值
     * @param appId
     * @param classId
     * @return
     */
    List<Integer> getClassCategoryValue(int appId, Long classId);
}
