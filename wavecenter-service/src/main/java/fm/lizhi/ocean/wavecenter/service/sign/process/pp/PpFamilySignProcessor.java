package fm.lizhi.ocean.wavecenter.service.sign.process.pp;

import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.EnterpriseInfoBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.FamilyAndNjContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignAuthStatusEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestFamilyApplyCancelAdmin;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestFamilyDoSign;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestFamilyInviteAdmin;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseFamilyApplyCancelAdmin;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseFamilyDoSign;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseFamilyInviteAdmin;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.service.sign.config.SignConfig;
import fm.lizhi.ocean.wavecenter.service.sign.dto.FamilyNjJoinRecordDTO;
import fm.lizhi.ocean.wavecenter.service.sign.dto.QueryFamilyNjJoinRecordDTO;
import fm.lizhi.ocean.wavecenter.service.sign.dto.RequestFamilyAndNjContractDTO;
import fm.lizhi.ocean.wavecenter.service.sign.handler.PpSignCheckJobHoppingHandler;
import fm.lizhi.ocean.wavecenter.service.sign.manager.ContractManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.sign.process.FamilySignProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/10/14 15:33
 */
@Slf4j
@Component
public class PpFamilySignProcessor implements FamilySignProcessor {

    @Autowired
    private SignConfig signConfig;
    @Autowired
    private ContractManager contractManager;
    @Autowired
    private FamilyManager familyManager;
    @Autowired
    private PpSignCheckJobHoppingHandler ppSignCheckJobHoppingHandler;

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public ResponseFamilyDoSign doSignCheck(RequestFamilyDoSign request) {
        return new ResponseFamilyDoSign();
    }

    @Override
    public ResponseFamilyInviteAdmin inviteAdminCheck(RequestFamilyInviteAdmin request) {
        ResponseFamilyInviteAdmin res = new ResponseFamilyInviteAdmin();

        if (!signConfig.getPp().isSignFunctionSwitch()) {
            return res.setCode(-1).setMsg("邀约功能暂时关闭，恢复时间等待官方通知");
        }

        if (request.getDuration() == null) {
            return res.setCode(-1).setMsg("签约周期为空");
        }
        if (!(request.getDuration() == 12 || request.getDuration() == 36)) {
            return res.setCode(-1).setMsg("签约周期最短1年，最长3年");
        }

        Long targetUserId = request.getTargetUserId();

        //黑名单
        String signAdminBackList = signConfig.getPp().getSignAdminBackList();
        if (signAdminBackList.contains(String.valueOf(targetUserId))) {
            LogContext.addResLog("user in backList. signAdminBackList={}", signAdminBackList);
            return res.setCode(-1).setMsg("该主播暂时不能被邀请或已经签约其他家族");
        }

        //家族长黑名单
        String signFamilyBackList = signConfig.getPp().getSignFamilyBackList();
        if (signFamilyBackList.contains(String.valueOf(request.getFamilyId()))) {
            LogContext.addResLog("family in backList. signFamilyBackList={}", signFamilyBackList);
            return res.setCode(-1).setMsg("你的家族不能邀约,请联系管理员");
        }

        //公司企业名称黑名单
        Optional<EnterpriseInfoBean> familyEnterpriseInfo = familyManager.getFamilyEnterpriseInfo(request.getFamilyId());
        if (!familyEnterpriseInfo.isPresent()) {
            LogContext.addResLog("family enterpriseInfo is not present");
            return res.setCode(-1).setMsg("家族没有认证信息");
        }
        EnterpriseInfoBean enterpriseInfoBean = familyEnterpriseInfo.get();
        String signEnterpriseBackList = signConfig.getPp().getSignEnterpriseBackList();
        if (signEnterpriseBackList.contains(enterpriseInfoBean.getEnterpriseName())) {
            LogContext.addResLog("enterprise in backList. signEnterpriseBackList={}", signEnterpriseBackList);
            return res.setCode(-1).setMsg("你的公司不能邀约,请联系管理员");
        }
        if (!SignAuthStatusEnum.AUTO_AUTH_PASS.getCode().equals(enterpriseInfoBean.getAuthStatus())) {
            LogContext.addResLog("enterprise not auth");
            return res.setCode(-1).setMsg("企业认证没有通过,不能邀约");
        }

        boolean bankCardVerifyPass = familyManager.isBankCardVerifyPass(request.getFamilyId());
        if (!bankCardVerifyPass) {
            LogContext.addResLog("bankcard not verify");
            return res.setCode(-1).setMsg("银行卡认证没有通过,不能邀约");
        }

        PageBean<FamilyAndNjContractBean> contractList = contractManager.queryContract(RequestFamilyAndNjContractDTO.builder()
                .relation(SignRelationEnum.SIGN_SUCCESS)
                .type(ContractTypeEnum.SIGN)
                .type(ContractTypeEnum.SUBJECT_CHANGE)
                .type(ContractTypeEnum.RENEW)
                .pageSize(1)
                .njId(targetUserId)
                .build());
        if (CollectionUtils.isNotEmpty(contractList.getList())) {
            LogContext.addResLog("contractList is not empty");
            return res.setCode(-1).setMsg("用户已签约其他家族");
        }

        List<FamilyNjJoinRecordDTO> familyNjList = contractManager.queryFamilyNjJoinRecord(QueryFamilyNjJoinRecordDTO.builder()
                .njId(targetUserId)
                .status("JOIN")
                .build());
        if (CollectionUtils.isNotEmpty(familyNjList)) {
            LogContext.addResLog("familyNjList is not empty");
            return res.setCode(-1).setMsg("用户已加入其他家族");
        }

        //判断目标用户是否为家族长
        Optional<FamilyBean> familyOp = familyManager.getFamilyByUserId(targetUserId);
        if (familyOp.isPresent()) {
            LogContext.addResLog("target user is family");
            return res.setCode(-1).setMsg("该用户已经是家族长");
        }

        UserInFamilyBean userInFamily = familyManager.getUserInFamily(targetUserId);
        if (userInFamily.isPlayer()) {
            LogContext.addResLog("target user is player");
            return res.setCode(-1).setMsg("该用户已签约陪玩，不能邀约");
        }

        //是否已存在邀约中的合同，需要待主播确认
        PageBean<FamilyAndNjContractBean> waitList = contractManager.queryContract(RequestFamilyAndNjContractDTO.builder()
                .relation(SignRelationEnum.SIGN_SUCCESS)
                .relation(SignRelationEnum.SIGNING)
                .relation(SignRelationEnum.WAIT_SIGN)
                .type(ContractTypeEnum.SIGN)
                .type(ContractTypeEnum.SUBJECT_CHANGE)
                .type(ContractTypeEnum.RENEW)
                .familyId(request.getFamilyId())
                .pageSize(1)
                .njId(targetUserId)
                .build());
        if (CollectionUtils.isNotEmpty(waitList.getList())) {
            LogContext.addResLog("contractList is not empty");
            return res.setCode(-1).setMsg("已经存在邀约中的合同，请等待主播确认");
        }

        // 防跳槽检查
        Pair<Integer, String> integerStringPair = ppSignCheckJobHoppingHandler.forFamilyInvite(request.getFamilyId(), targetUserId);
        if (integerStringPair.getKey() != 0) {
            LogContext.addResLog("checkJobHoppingCode={}", integerStringPair.getRight());
            return res.setCode(integerStringPair.getKey()).setMsg(integerStringPair.getValue());
        }

        return res;
    }

    @Override
    public Optional<FamilyAndNjContractBean> existWaitSignContract(RequestFamilyInviteAdmin request) {
        return Optional.empty();
    }

    @Override
    public ResponseFamilyApplyCancelAdmin applyCancelAdmin(RequestFamilyApplyCancelAdmin request) {
        return null;
    }
}
