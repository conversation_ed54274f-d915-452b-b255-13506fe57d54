package fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 包括条件实体的任务模板
 * <AUTHOR>
 * @date 2025/6/6 18:03
 */
@Data
@Accessors(chain = true)
public class TaskTemplateConditionDTO {

    private Long id;

    /**
     * 能力分奖励
     */
    private TaskTemplateCapabilityDTO capability;

    /**
     * 条件
     */
    private ConditionGroupDTO conditionGroup;

}
