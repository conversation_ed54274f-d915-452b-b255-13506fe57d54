package fm.lizhi.ocean.wavecenter.service.sign.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/13 15:21
 */
@Data
public class SMSignRoomPageListReqDto {
    private Integer appId;

    private Long familyId;

    /**
     * 签约时间-开始
     */
    private Date signStartDate;

    /**
     * 签约时间-结束
     */
    private Date signEndDate;

    /**
     * 到期时间-开始
     */
    private Date expireStartDate;

    /**
     * 到期时间-结束
     */
    private Date expireEndDate;

    /**
     * 解约时间-开始
     */
    private Date stopStartDate;

    /**
     * 解约时间-结束
     */
    private Date stopEndDate;

    /**
     * 签约厅ID
     */
    private Long roomId;

    /**
     * 签约状态
     * 1=已签约
     * 0=已解约
     */
    private Integer signStatus;

    private Integer pageNo;

    private Integer pageSize;

    private List<Long> roomIds;
}
