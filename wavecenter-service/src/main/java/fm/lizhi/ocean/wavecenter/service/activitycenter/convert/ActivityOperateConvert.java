package fm.lizhi.ocean.wavecenter.service.activitycenter.convert;

import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestUserCancelActivity;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestUserModifyActivityAfterAudit;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityUserCancelParamDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityUserModifyParamDTO;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActivityOperateConvert {

    ActivityOperateConvert I = Mappers.getMapper(ActivityOperateConvert.class);

    @Mappings({
            @Mapping(target = "targetAuditStatus", expression = "java(fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityAuditStatusEnum.USER_CANCEL.getStatus())"),
            @Mapping(target = "operator", ignore = true),
            @Mapping(target = "activityInfo", ignore = true),

    })
    ActivityUserCancelParamDTO buildActivityUserCancelParam(RequestUserCancelActivity request);

    ActivityUserModifyParamDTO buildActivityUserModifyParam(RequestUserModifyActivityAfterAudit request);

}
