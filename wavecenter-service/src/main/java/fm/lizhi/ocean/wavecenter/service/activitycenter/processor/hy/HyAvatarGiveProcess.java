package fm.lizhi.ocean.wavecenter.service.activitycenter.processor.hy;

import com.alibaba.csp.sentinel.util.AssertUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityResourceGiveDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.DecorateInfoDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.DressUpGiveContext;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.SendDecorateParamDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityMaterielManager;
import fm.lizhi.ocean.wavecenter.service.activitycenter.processor.IAvatarGiveProcess;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class HyAvatarGiveProcess implements IAvatarGiveProcess {

    @Autowired
    private ActivityMaterielManager activityMaterielManager;

    @Override
    public boolean isAutoGive() {
        return true;
    }

    @Override
    public void fillSendParam(DressUpGiveContext context, SendDecorateParamDTO param) {
        ActivityResourceGiveDTO resourceGiveDTO = context.getResourceGiveDTO();
        Result<DecorateInfoDTO> result = activityMaterielManager.getDecorateInfo(resourceGiveDTO.getResourceId());
        AssertUtil.assertState(RpcResult.isSuccess(result), "查询装扮信息失败");
        Integer vailMin = result.target().getVailMin();
        //根据头像框的有效期，算出活动期间内需要多少个头像框
        long activityMin = (resourceGiveDTO.getEndTime().getTime() - resourceGiveDTO.getStartTime().getTime()) / 1000 / 60;
        //activityMin/vailMin 结果有余数向上取整
        param.setCount((int) Math.ceil(activityMin / (double) vailMin));
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.HEI_YE;
    }
}
