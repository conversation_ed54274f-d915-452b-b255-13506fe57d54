package fm.lizhi.ocean.wavecenter.service.datacenter.manager;

import fm.lizhi.ocean.wavecenter.service.datacenter.dto.PlayerFlowChangeDTO;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.RoomFlowChangeDTO;

/**
 * <AUTHOR>
 * @date 2025/4/24 14:34
 */
public interface AccountFlowManager {

    /**
     * 保存厅流水变化
     * @param dto
     */
    void saveRoomFlowChange(RoomFlowChangeDTO dto);

    /**
     * 保存主播流水变化
     * @param dto
     */
    void savePlayerFlowChange(PlayerFlowChangeDTO dto);

}
