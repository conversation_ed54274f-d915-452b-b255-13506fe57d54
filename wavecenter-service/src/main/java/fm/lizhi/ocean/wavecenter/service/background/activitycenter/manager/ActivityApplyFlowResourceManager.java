package fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager;

import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityFlowResourceAuditBean;

import java.util.List;

public interface ActivityApplyFlowResourceManager {

    /**
     * 批量修改流量资源
     *
     * @param activityId    活动ID
     * @param flowResources 流量资源列表
     * @return 结果
     */
    boolean batchUpdateFlowResource(Long activityId, List<ActivityFlowResourceAuditBean> flowResources);

}
