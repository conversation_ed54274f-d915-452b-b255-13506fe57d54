package fm.lizhi.ocean.wavecenter.service.activitycenter.processor;

import fm.lizhi.ocean.wavecenter.base.processor.BusinessEnvAwareProcessor;

public interface IProgrammeGiveProcess extends BusinessEnvAwareProcessor {

    /**
     * 是否可以自动发放
     *
     * @return true: 可以自动发放
     */
    boolean isAutoGive();


    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return IProgrammeGiveProcess.class;
    }
}
