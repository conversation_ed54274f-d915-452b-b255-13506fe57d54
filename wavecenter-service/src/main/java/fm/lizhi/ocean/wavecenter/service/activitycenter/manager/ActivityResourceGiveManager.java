package fm.lizhi.ocean.wavecenter.service.activitycenter.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityResourceGiveDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ResourceGiveResultDTO;

import java.util.List;

public interface ActivityResourceGiveManager {

    Result<List<ResourceGiveResultDTO>> giveResourceJobExecute(Integer appId);

    /**
     * 审核通过后实时发放资源
     *
     * @param appId      应用ID
     * @param activityId 活动ID
     * @return 结果
     */
    Result<List<ResourceGiveResultDTO>> realTimeGiveResource(Integer appId, Long activityId);

    /**
     * 修改已经发放的流量资源
     *
     * @param activityId          活动ID
     * @return 结果
     */
    Result<String> deleteAlreadyGiveResource(Long activityId);

    /**
     * 流量资源发放操作
     *
     * @param giveResource   待发放的资源记录
     * @param isRealTimeGive 是否立即发放
     * @return 发放结果实体
     */
    ResourceGiveResultDTO flowResourceGiveExecute(ActivityResourceGiveDTO giveResource, boolean isRealTimeGive);

}
