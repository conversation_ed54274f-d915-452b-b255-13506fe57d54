package fm.lizhi.ocean.wavecenter.service.common.constants;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum AuditTypeEnum {

    /**
     * PP房间公告审核类型
     */
    PP_TYPE(BusinessEvnEnum.PP.getAppId(), 1160023, 1110011, "ANNOUNCEMENT"),

    /**
     * 黑叶房间公告审核类型
     */
    HY_TYPE(BusinessEvnEnum.HEI_YE.getAppId(), 1360027, 1310004, "ANNOUNCEMENT"),

    /**
     * 西米房间公告审核类型
     */
    XM_TYPE(BusinessEvnEnum.XIMI.getAppId(), 1560023, 1520014, "ANNOUNCEMENT"),


    ;

    private int appId;

    private int parentType;

    private int type;

    private String scene;

    /**
     * 获取审核类型
     *
     * @param appId 应用ID
     * @param scene 场景
     * @return 类型
     */
    public static int getType(int appId, String scene) {
        for (AuditTypeEnum auditTypeEnum : values()) {
            if (auditTypeEnum.getAppId() == appId && auditTypeEnum.getScene().equals(scene)) {
                return auditTypeEnum.getType();
            }
        }
        throw new RuntimeException("未找到合适的审核类型，请和审核产品核对");
    }

    /**
     * 获取房间公告审核类型
     *
     * @param appId 应用ID
     * @return 类型
     */
    public static AuditTypeEnum getAnnouncementType(int appId) {
        for (AuditTypeEnum auditTypeEnum : values()) {
            if (auditTypeEnum.getAppId() == appId && auditTypeEnum.getScene().equals("ANNOUNCEMENT")) {
                return auditTypeEnum;
            }
        }
        throw new RuntimeException("未找到合适的审核类型，请和审核产品核对");
    }

}
