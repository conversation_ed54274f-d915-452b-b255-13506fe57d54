package fm.lizhi.ocean.wavecenter.service.award.singer.config;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.config.AbsBizConfig;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix = "wavecenter-award.singer")
@Data
public class SingerAwardConfig extends AbsBizConfig<CommonSingerAwardConfig> {


    private PpSingerAwardConfig pp;

    private XmSingerAwardConfig xm;

    private HySingerAwardConfig hy;

    /**
     * PP补偿待发放的歌手认证装饰 一次性job中断开关
     */
    private boolean compensateSingerDecorateEnable = false;
    private boolean canRepeatCompensation = false;

    public SingerAwardConfig() {
        PpSingerAwardConfig ppConfig = new PpSingerAwardConfig();
        XmSingerAwardConfig xmConfig = new XmSingerAwardConfig();
        HySingerAwardConfig hyConfig = new HySingerAwardConfig();
        this.pp = ppConfig;
        this.xm = xmConfig;
        this.hy = hyConfig;
        bizConfigMap.put(BusinessEvnEnum.PP.appId(), ppConfig);
        bizConfigMap.put(BusinessEvnEnum.XIMI.appId(), xmConfig);
        bizConfigMap.put(BusinessEvnEnum.HEI_YE.appId(), hyConfig);
    }
}
