package fm.lizhi.ocean.wavecenter.service.anchor.singer.handler.machine;

import com.google.common.collect.Lists;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerAuditStatusEnum;
import lombok.Data;

import java.util.List;

import static fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerAuditStatusEnum.*;

@Data
public class SingerAuditStatusModel {

    // 支付状态机内容
    private static final StateMachine<SingerAuditStatusEnum> STATE_MACHINE = new StateMachine<>();

    static {
        // 申请认证事件
        STATE_MACHINE.accept(null, Lists.newArrayList(WAIT_AUDIT));
        // 审核操作
        STATE_MACHINE.accept(WAIT_AUDIT, Lists.newArrayList(SELECTED, PASS, REJECTED, WAIT_DECIDE));
        // 待定操作
        STATE_MACHINE.accept(WAIT_DECIDE, Lists.newArrayList(WAIT_AUDIT, SELECTED, PASS, REJECTED));
        // 选中操作
        STATE_MACHINE.accept(SELECTED, Lists.newArrayList(PASS, REJECTED));
        //预审不过误判
        STATE_MACHINE.accept(PRE_AUDIT_REJECTED, Lists.newArrayList(SELECTED, PASS, WAIT_DECIDE));

    }

    // 状态
    private final int status;

    // 描述
    private final String description;

    /**
     * 通过源状态和事件类型获取目标状态
     */
    public static List<SingerAuditStatusEnum> getTargetStatus(Integer sourceStatus) {
        return STATE_MACHINE.getTargetStatus(SingerAuditStatusEnum.getByType(sourceStatus));
    }

    /**
     * 是否允许状态流转
     *
     * @param sourceStatus 源状态
     * @param targetStatus 目标状态
     * @return 是否允许状态流转
     */
    public static boolean isAllowStatusChange(Integer sourceStatus, Integer targetStatus) {
        if (sourceStatus == null || targetStatus == null) {
            return false;
        }
        List<SingerAuditStatusEnum> targetStatusList = getTargetStatus(sourceStatus);
        if (targetStatusList == null) {
            return false;
        }
        return targetStatusList.contains(SingerAuditStatusEnum.getByType(targetStatus));
    }

}
