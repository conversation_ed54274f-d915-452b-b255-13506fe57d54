package fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.annotation.Nullable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/24 20:11
 */
@Data
@Accessors(chain = true)
public class RecommendAllocationRecordDTO {

    /**
     * 厅主
     */
    @Nullable
    private Long njId;

    /**
     * 详情
     */
    private String detail;

    /**
     * 分配时间
     */
    private Date allocationTime;

    /**
     * 分配数量
     */
    private Integer nums;

}
