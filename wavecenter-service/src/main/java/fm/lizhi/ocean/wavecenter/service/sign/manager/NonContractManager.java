package fm.lizhi.ocean.wavecenter.service.sign.manager;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.bean.AdminSignPlayerRecordBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.CancelPlayerRecordBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.NjAndPlayerContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.TodoSignPlayerBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.OperateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import fm.lizhi.ocean.wavecenter.api.sign.request.*;
import fm.lizhi.ocean.wavecenter.api.sign.response.*;
import fm.lizhi.ocean.wavecenter.service.sign.dto.OperateSignDTO;
import fm.lizhi.ocean.wavecenter.service.sign.dto.QueryNonContractDTO;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 非电子合同签约管理
 * <AUTHOR>
 * @date 2024/10/9 20:27
 */
public interface NonContractManager {

    /**
     * 查询用户最近的解约记录
     * @param userId
     * @return
     */
    Optional<NjAndPlayerContractBean> queryLastCancel(Long userId);

    /**
     * 查询用户最近的签约记录
     * @param userId
     * @return
     */
    Optional<NjAndPlayerContractBean> queryLastSign(Long userId);

    /**
     * 用户申请为主播
     */
    ResponseUserApplyPlayer userApplyPlayer(RequestUserApplyPlayer request);

    /**
     * 管理员邀请用户
     * @param request
     * @return
     */
    ResponseAdminInviteUser adminInviteUser(RequestAdminInviteUser request);

    /**
     * 当前是否在做主体变更
     * @return
     */
    boolean isInChangeCompany(Long curUserId);

    /**
     * 是否在主体变更准备阶段
     * @param curUserId
     * @return
     */
    boolean isInChangeCompanyPreparedStage(Long curUserId);

    /**
     * 检查邀请签约是否达到上限
     * @return 通过返回key=0 value=异常消息
     */
    Pair<Integer, String> checkInviteSignLimit(Long userId, Long njId, RoleEnum userType, RoleEnum checkType);

    /**
     * 查询签约记录
     * @param userId
     * @param status
     * @return
     */
    List<NjAndPlayerContractBean> queryUserSign(Long userId, List<ContractTypeEnum> types, List<SignRelationEnum> status);

    /**
     * 操作签约 管理员-主播
     * @return
     */
    OperateSignDTO operateSign(Long playSignId, Long curUserId
            , ContractTypeEnum type, RoleEnum role, OperateTypeEnum operateType);

    /**
     * 检查是否可以确认签约
     * @return
     */
    Pair<Integer, String> checkCanSignForConfirm(Long playSignId, Long curUserId, RoleEnum opRole, RoleEnum checkRole);

    /**
     * 查询签约列表
     * @param paramDTO
     * @return
     */
    PageBean<NjAndPlayerContractBean> queryList(QueryNonContractDTO paramDTO);

    /**
     * 主播申请解约
     * @param request
     * @return
     */
    ResponsePlayerApplyCancel playerApplyCancel(RequestPlayerApplyCancel request);

    /**
     * 管理员申请解约
     * @param request
     * @return
     */
    ResponseAdminApplyCancelPlayer adminApplyCancelPlayer(RequestAdminApplyCancelPlayer request);

    /**
     * 审核主播申请解约
     * @return
     */
    Pair<Integer, String> reviewCancel(Long playSignId, Long curUserId, OperateTypeEnum operateType);

    /**
     * 取消解约
     * @return
     */
    ResponseWithdrawCancel withdrawCancel(Long playerSignId, Long curUserId, RoleEnum opUserRole);

    /**
     * 统计厅的下级人数
     * @param njIds
     * @return
     */
    Map<Long, Integer> countNjSign(List<Long> njIds);

    /**
     * 查询陪玩待审批列表
     * @param request
     * @return
     */
    List<TodoSignPlayerBean> todoPlayerList(RequestFamilyTodoPlayerList request);

    /**
     * 管理员待办列表
     * @param request
     * @return
     */
    List<TodoSignPlayerBean> adminTodoList(RequestAdminTodoList request);

    /**
     * 查询家族长相关陪玩解约记录
     * @param request
     * @return
     */
    PageBean<CancelPlayerRecordBean> queryFamilyCancelPlayerList(RequestQueryRoomSignList request);

    /**
     * 管理员查询陪玩签约解约记录
     * @param request
     * @return
     */
    PageBean<AdminSignPlayerRecordBean> querySignPlayerList(RequestQuerySignPlayerList request);

    /**
     * 统计厅下签约的主播数
     * @param njId
     * @return
     */
    Integer countPlayerSignNum(Long njId);

    /**
     * 查询用户当前签约的厅主
     * @param userId
     * @return
     */
    Optional<Long> getPlayerCurSignNj(Long userId);

    /**
     * 获取用户最近一次解约的厅主
     * @return
     */
    Optional<Long> getPlayerLastCancelSign(Long userId);

    /**
     * 查询主播的签约时间
     * @param playerId
     * @param njId
     * @return
     */
    Date getPlayerSignTime(Long playerId, Long njId);

    /**
     * 主播是否首次签约厅主的公会
     * @param playerId
     * @param njId
     * @return
     */
    boolean isPlayerFirstSignNjFamily(Long playerId, Long njId, Long familyId);

}
