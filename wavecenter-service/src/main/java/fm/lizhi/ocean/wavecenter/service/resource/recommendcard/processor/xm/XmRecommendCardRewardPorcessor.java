package fm.lizhi.ocean.wavecenter.service.resource.recommendcard.processor.xm;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.request.RequestGetFamilyUseRecord;
import fm.lizhi.ocean.wavecenter.service.permissions.manager.RoleManager;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.RecommendCardRewardBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.response.RewardResultBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto.ContractInfoDto;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.processor.RecommendCardRewardProcessor;
import fm.lizhi.ocean.wavecenter.service.sign.manager.ContractManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;

@Component
public class XmRecommendCardRewardPorcessor implements RecommendCardRewardProcessor {

    private static final int FAMILY_USER_TYPE = 3;

    private static final String SIGN_SUCCEED = "SIGN_SUCCEED";

    private static final String CONTRACT_TYPE_SIGN = "SIGN";

    @Autowired
    private FamilyManager familyManager;

    @Autowired
    private ContractManager contractManager;

    @Autowired
    private RoleManager roleManager;

    @Override
    public RewardResultBean checkCanReward(long operatorUserId, List<RecommendCardRewardBean> rewardBeans) {
 
        Optional<RecommendCardRewardBean> rewardBean = rewardBeans.stream().filter(rewardRecommendationCard -> rewardRecommendationCard.getNum() <= 0).findAny();
        if (rewardBean.isPresent()) {
            return new RewardResultBean().setCode(NUM_NOT_VALID).setRewardResult("数量非法");
        }

        Optional<FamilyBean> family = familyManager.getUserFamily(operatorUserId);
        if (!family.isPresent() || family.get().getUserType() != FAMILY_USER_TYPE) {
            return new RewardResultBean().setCode(REWARD_FAMILY_NOT_VALID).setRewardResult("您不是工会长，无法发放推荐卡");
        }


        Date ninetyDaysAgo = DateUtil.getDayBefore(new Date(), 90);
        List<ContractInfoDto> contractInfos = contractManager.queryContractInfoByTime(family.get().getId(), ninetyDaysAgo, new Date());
        if (CollectionUtils.isEmpty(contractInfos)) {
            return new RewardResultBean().setCode(REWARD_FAMILY_NOT_VALID).setRewardResult("您没有签约厅，无法发放推荐卡");
        }

        Set<Long> userIdList = contractInfos.stream().filter(
                contractInfo -> SIGN_SUCCEED.equals(contractInfo.getStatus()) &&
                        CONTRACT_TYPE_SIGN.equals(contractInfo.getType())).map(ContractInfoDto::getNjId).collect(Collectors.toSet());
        List<Long> recUserIds = rewardBeans.stream().map(RecommendCardRewardBean::getTargetUserId).collect(Collectors.toList());
        if (!userIdList.containsAll(recUserIds)) {
            return new RewardResultBean().setCode(REWARD_FAMILY_NOT_VALID).setRewardResult("发放的房间中有非旗下工会");
        }
        return new RewardResultBean().setCode(0).setRewardResult("success");
    }

    @Override
    public void familyUseRecordQueryUserId(List<Long> queryRoomIds, RequestGetFamilyUseRecord request, List<Long> njIds) {
        // 需要查询超级管理员的使用记录
        List<Long> superAdminUserIds = roleManager.getSuperAdminUserIds(njIds);
        queryRoomIds.addAll(superAdminUserIds);
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.XIMI;
    }
}

