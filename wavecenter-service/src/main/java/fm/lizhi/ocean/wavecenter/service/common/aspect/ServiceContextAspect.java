package fm.lizhi.ocean.wavecenter.service.common.aspect;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.context.ServiceContext;
import fm.lizhi.ocean.wave.server.common.request.RequestAppIdAware;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 解析出请求参数中的上下文信息
 * <AUTHOR>
 * @date 2024/12/2 19:36
 */
@Aspect
@Component
@Order(1)
@Slf4j
public class ServiceContextAspect {

    @Around("@within(fm.lizhi.commons.service.client.annotation.ServiceProvider)" +
            " && execution(public fm.lizhi.commons.service.client.pojo.Result *(..))")
    public Object parseContext(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        ServiceContext context = ContextUtils.getContext();
        BusinessEvnEnum oldBusinessEnv = null;
        if (context != null) {
            oldBusinessEnv = context.getBusinessEvnEnum();
        }
        try {

            for (Object arg : args) {
                if (arg instanceof IContextRequest) {
                    IContextRequest contextRequest = (IContextRequest) arg;
                    Integer appId = contextRequest.foundIdAppId();
                    if (appId != null) {
                        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.from(appId));
                    }
                    break;
                }
                // 结构调整过渡期兼容
                if (arg instanceof RequestAppIdAware) {
                    RequestAppIdAware contextRequest = (RequestAppIdAware) arg;
                    Integer appId = contextRequest.foundIdAppId();
                    if (appId != null) {
                        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.from(appId));
                    }
                    break;
                }
            }

            return joinPoint.proceed();
        } finally {
            if (oldBusinessEnv != null) {
                ContextUtils.setBusinessEvnEnum(oldBusinessEnv);
            } else {
                ContextUtils.clearContext();
            }
        }
    }

}
