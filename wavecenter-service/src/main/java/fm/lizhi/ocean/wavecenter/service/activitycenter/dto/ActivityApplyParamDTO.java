package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class ActivityApplyParamDTO {

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 业务定义不超过10个字，稍微冗余
     */
    private String name;

    /**
     * 提报厅厅主ID
     */
    private Long njId;

    /**
     * 家族ID
     */
    private Long familyId;

    /**
     * 活动分类ID
     */
    private Long classId;

    /**
     * 活动开始时间
     */
    private Date startTime;

    /**
     * 活动结束时间
     */
    private Date endTime;

    /**
     * 活动状态，1：待审批，2：审批通过，3：审批不通过
     */
    private Integer auditStatus;

    /**
     * 申请类型，1：自主提报，2: 官方活动
     */
    private Integer applyType;

    /**
     * 联系人
     */
    private String contact;

    /**
     * 申请者uid
     */
    private Long applicantUid;

    /**
     * 联系方式
     */
    private String contactNumber;

    /**
     * 主持人ID
     */
    private Long hostId;

    /**
     * 陪档主播ID列表
     */
    private String accompanyNjIds;

    /**
     * 活动目标，不超过100字
     */
    private String goal;

    /**
     * 活动介绍，不超过100字
     */
    private String introduction;

    /**
     * 活动海报图片地址，多个逗号分隔
     */
    private String posterUrl;

    /**
     * 辅助道具图片地址，多个逗号分隔
     */
    private String auxiliaryPropUrl;

    /**
     * 玩法工具，多个逗号分隔
     */
    private String activityTool;

    /**
     * 房间公告，不超过500字
     */
    private String roomAnnouncement;

    /**
     * 房间公告图片，不超过3个
     */
    private String roomAnnouncementImgUrl;

    /**
     * 房间背景ID列表
     */
    private List<Long> roomBackgroundIds;

    /**
     * 头像框ID列表
     */
    private List<Long> avatarWidgetIds;

    /**
     * 可选值  TEST/PRE/PRO
     */
    private String deployEnv;

    /**
     * 流量资源列表
     */
    private List<ActivityFlowResourceDTO> flowResources;

    /**
     * 活动环节列表
     */
    private List<ActivityProcessDTO> processList;

    /**
     * 模板ID
     */
    private Long templateId;

    /**
     * 最大官频位数量
     */
    private int maxSeatCount;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 需要修改的资源配置ID
     */
    private List<Long> needModifyResourceConfigIds;

    /**
     * 提报模式
     * @see fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyModelEnum
     */
    private Integer model;
}
