package fm.lizhi.ocean.wavecenter.service.message.manager;


import java.util.List;

import fm.lizhi.ocean.wavecenter.api.message.bean.UnReadMessageCountBean;
import fm.lizhi.ocean.wavecenter.service.message.dto.*;

/**
 * <AUTHOR>
 */
public interface WcNoticeConfigManager {

    /**
     * 保存公告
     *
     * @param configDTO 公告配置DTO
     * @return 保存结果
     */
    boolean saveWcNoticeConfig(WcNoticeConfigSaveParamDTO configDTO);

    /**
     * 根据id删除公告
     *
     * @param id 公告id
     * @return 删除结果
     */
    boolean deleteWcNoticeConfigById(Long id);

    /**
     * 分页查询公告
     *
     * @param queryDTO 查询参数
     * @return 分页结果
     */
    WcNoticeConfigPageResultDTO queryWcNoticeConfigPage(WcNoticeConfigQueryDTO queryDTO);

    /**
     * 按类型滚动分页查询公告（按effectTime倒序）
     *
     * @param queryDTO 查询参数
     * @return 分页结果
     */
    WcNoticeConfigResultDTO queryWcNoticeConfig(WcNoticeConfigQueryParamDTO queryDTO);

    /**
     * 获取未读消息数
     *
     * @param targetUserId 目标用户ID
     * @param appId        应用ID
     * @param type         消息类型
     * @return 未读消息数
     */
    List<UnReadMessageCountBean> getUnReadMessageCount(Long targetUserId, Integer appId, Integer type, String roleCode);

    /**
     * 上下架公告
     *
     * @param configDTO 公告配置DTO
     * @return 保存结果
     */
    boolean updateNoticeStatus(WcNoticeUpdateStatusDTO configDTO);
}
