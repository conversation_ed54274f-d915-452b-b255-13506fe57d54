package fm.lizhi.ocean.wavecenter.service.anchor.singer.service;

import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.*;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeMappingEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestSaveApplyMenuConfig;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestUpdateSingerAuditConfig;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.*;
import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.convert.SingerConfigConvert;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerAuditConfigDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerChatSceneDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerMenuConfigDTO;
import org.springframework.beans.factory.annotation.Autowired;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.service.SingerConfigService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.config.CommonSingerConfig;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.config.SingerAnchorConfig;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerAuditConfigManager;
import lombok.extern.slf4j.Slf4j;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestSingerChatScene;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.convert.SingerChatSceneServiceConvert;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerChatManager;
import org.springframework.util.CollectionUtils;

/**
 * 歌手认证配置服务
 *
 * <AUTHOR>
 */
@Slf4j
@ServiceProvider
public class SingerConfigServiceImpl implements SingerConfigService {

    @Autowired
    private SingerAuditConfigManager singerAuditConfigManager;

    @Autowired
    private SingerAnchorConfig singerAnchorConfig;

    @Autowired
    private SingerChatManager singerChatManager;

    @Override
    public Result<ResponseGetSingerPreAuditConfig> getSingerAudioAuditConfig(int appId, int singerType) {
        LogContext.addReqLog("appId={}", appId);
        LogContext.addResLog("appId={}", appId);
        ResponseGetSingerPreAuditConfig response = new ResponseGetSingerPreAuditConfig();
        // 获取预审核配置
        List<SingerAuditConfigDTO> singerAuditConfig = singerAuditConfigManager.getSingerAuditConfig(appId);
        List<SingerPreAuditConfigBean> singerPreAuditConfigBeans = SingerConfigConvert.I.auditConfigList2BeanList(singerAuditConfig);
        response.setAuditConfigList(singerPreAuditConfigBeans);

        // 获取音频审核配置
        String audioAuditConfig = singerAnchorConfig.getBizConfig(appId).getAudioAuditConfig();
        SingerAuditConfigBean audioAuditConfigBean = JsonUtils.fromJsonString(audioAuditConfig, SingerAuditConfigBean.class);
        response.setAudioConfig(audioAuditConfigBean);

        // 获取歌手认证提报文案
        String singerAuditReportText = singerAnchorConfig.getBizConfig(appId).getSingerAuditReportText();
        SingerContentConfigBean singerContentConfigBean = JsonUtils.fromJsonString(singerAuditReportText, SingerContentConfigBean.class);
        response.setContentConfig(singerContentConfigBean);

        Integer maxStyleNum = singerAnchorConfig.getBizConfig(appId).getMaxStyleNumMap().getOrDefault(singerType, 0);
        response.setSongStyleConfig(new SongStyleConfig().setMaxStyleNum(maxStyleNum).setSingerType(singerType));

        LogContext.addResLog("response={}", JsonUtil.dumps(response));
        return RpcResult.success(response);
    }

    @Override
    public Result<ResponseSingerEnumerateConfig> getEnumerateConfig(int appId) {
        LogContext.addReqLog("appId={}", appId);
        LogContext.addResLog("appId={}", appId);
        ResponseSingerEnumerateConfig response = new ResponseSingerEnumerateConfig();
        BusinessEvnEnum evnEnum = BusinessEvnEnum.from(appId);

        CommonSingerConfig config = singerAnchorConfig.getBizConfig(appId);
        //构建歌曲风格枚举
        if (config != null && config.getSongStyleConfig() != null) {
            String songStyleConfig = config.getSongStyleConfig();
            List<SongStyleBean> songStyleList = JsonUtil.loadsArray(songStyleConfig, SongStyleBean.class);
            response.setSongStyle(songStyleList.stream().filter(SongStyleBean::isEnabled).collect(Collectors.toList()));
        }

        //构建歌手认证状态枚举
        List<SingerAuditStatusEnum> singerAuditStatusList = SingerAuditStatusEnum.getList();
        List<SingerVerifyAuditStatusBean> singerVerifyAuditStatusList = singerAuditStatusList.stream()
                .map(status -> new SingerVerifyAuditStatusBean(status.getStatus(), status.getName()))
                .collect(Collectors.toList());
        response.setSingerVerifyAuditStatus(singerVerifyAuditStatusList);

        // 构建歌手类型枚举
        List<SingerTypeBean> collect = Arrays.stream(SingerTypeEnum.values()).map(e -> {
            String bizName = SingerTypeMappingEnum.getBizSingerType(evnEnum, e);
            if (StrUtil.isBlank(bizName)) {
                return null;
            }
            return new SingerTypeBean().setIsSenior(e.isSenior()).setType(e.getType()).setName(e.getName()).setBizName(bizName);
        }).filter(Objects::nonNull).collect(Collectors.toList());
        response.setSingerType(collect);

        // 构建可发放装扮类型
        response.setDecorateType(buildDecorateType(evnEnum));
        LogContext.addResLog("response={}", JsonUtil.dumps(response));
        return RpcResult.success(response);
    }

    @Override
    public Result<Void> updateSingerAuditConfig(RequestUpdateSingerAuditConfig request) {
        return singerAuditConfigManager.updateSingerAuditConfig(request);
    }

    @Override
    public Result<ResponseSingerAuditConfig> getSingerPreAuditConfig(int appId) {
        List<SingerAuditConfigDTO> result = singerAuditConfigManager.getSingerAuditConfig(appId);
        List<SingerPreAuditBean> singerPreAuditBeans = SingerConfigConvert.I.auditDTO2PreAuditBean(result);
        return RpcResult.success(new ResponseSingerAuditConfig().setAuditConfigList(singerPreAuditBeans));
    }

    /**
     * 构建可发放装扮的枚举
     */
    private List<DecorateTypeBean> buildDecorateType(BusinessEvnEnum evnEnum) {
        if (evnEnum.equals(BusinessEvnEnum.PP)) {
            return CollUtil.newArrayList(
                    new DecorateTypeBean().setName(PlatformDecorateTypeEnum.USER_GLORY.getName()).setType(PlatformDecorateTypeEnum.USER_GLORY.getType()),
                    new DecorateTypeBean().setName(PlatformDecorateTypeEnum.BUBBLE.getName()).setType(PlatformDecorateTypeEnum.BUBBLE.getType()),
                    new DecorateTypeBean().setName(PlatformDecorateTypeEnum.AVATAR.getName()).setType(PlatformDecorateTypeEnum.AVATAR.getType())
            );
        }

        if (evnEnum.equals(BusinessEvnEnum.XIMI)) {
            return CollUtil.newArrayList(
                    new DecorateTypeBean().setName(PlatformDecorateTypeEnum.AVATAR.getName()).setType(PlatformDecorateTypeEnum.AVATAR.getType()),
                    new DecorateTypeBean().setName(PlatformDecorateTypeEnum.MEDAL.getName()).setType(PlatformDecorateTypeEnum.MEDAL.getType())
            );
        }

        if (evnEnum.equals(BusinessEvnEnum.HEI_YE)) {
            return CollUtil.newArrayList(
                    new DecorateTypeBean().setName(PlatformDecorateTypeEnum.AVATAR.getName()).setType(PlatformDecorateTypeEnum.AVATAR.getType()),
                    new DecorateTypeBean().setName(PlatformDecorateTypeEnum.MEDAL.getName()).setType(PlatformDecorateTypeEnum.MEDAL.getType()),
                    new DecorateTypeBean().setName(PlatformDecorateTypeEnum.USER_GLORY.getName()).setType(PlatformDecorateTypeEnum.USER_GLORY.getType())
            );
        }

        return Collections.emptyList();
    }

    @Override
    public Result<Void> addSingerChatScene(RequestSingerChatScene request) {
        try {
            List<SingerChatSceneDTO> dtos = SingerChatSceneServiceConvert.I.sceneChatRequestToDtos(request.getSceneChatList());
            for (SingerChatSceneDTO dto : dtos) {
                dto.setAppId(request.getAppId());
                // 先查询是否已存在记录
                List<SingerChatSceneDTO> existingConfigs = singerChatManager.getAuditChatConfig(
                        dto.getAppId(),
                        dto.getSingerType(),
                        dto.getSceneCode()
                );

                if (!CollectionUtils.isEmpty(existingConfigs)) {
                    // 如果存在记录，则更新
                    singerChatManager.updateAuditChatConfigByAppIdAndSceneCode(
                            dto.getAppId(),
                            dto.getSingerType(),
                            dto.getSceneCode(),
                            dto.getContent(),
                            dto.getActionUrl()
                    );
                } else {
                    // 如果不存在记录，则新增
                    singerChatManager.addAuditChatConfig(dto);
                }
            }
            return RpcResult.success();
        } catch (Exception e) {
            log.error("新增私信场景配置异常，request={}", request, e);
            return RpcResult.fail(SingerConfigService.SINGER_CONFIG_FAILED, "新增配置失败");
        }
    }

    @Override
    public Result<Void> deleteSingerChatScene(Integer appId, String sceneCode) {
        LogContext.addReqLog("appId={}, sceneCode={}", appId, sceneCode);
        LogContext.addResLog("appId={}, sceneCode={}", appId, sceneCode);
        try {
            singerChatManager.deleteAuditChatConfig(appId, sceneCode);
            return RpcResult.success();
        } catch (Exception e) {
            log.error("删除私信场景配置异常，appId={}, sceneCode={}", appId, sceneCode, e);
            return RpcResult.fail(SingerConfigService.SINGER_CONFIG_FAILED, "删除配置失败");
        }
    }

    @Override
    public Result<List<ResponseSingerChatScene>> getSingerChatScene(Integer appId, Integer singerType, String sceneCode) {
        LogContext.addReqLog("appId={}, singerType={}, sceneCode={}", appId, singerType, sceneCode);
        LogContext.addResLog("appId={}, singerType={}, sceneCode={}", appId, singerType, sceneCode);
        try {
            List<SingerChatSceneDTO> configs = singerChatManager.getAuditChatConfig(appId, singerType, sceneCode);
            List<ResponseSingerChatScene> responses = SingerChatSceneServiceConvert.I.dtoToResponses(configs);
            return RpcResult.success(responses);
        } catch (Exception e) {
            log.error("查询私信场景配置异常，appId={}, singerType={}, sceneCode={}", appId, singerType, sceneCode, e);
            return RpcResult.fail(SingerConfigService.SINGER_CONFIG_FAILED, "查询配置失败");
        }
    }

    @Override
    public Result<Void> saveApplyMenuConfig(RequestSaveApplyMenuConfig request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        singerAuditConfigManager.saveApplyMenuConfig(request);
        return RpcResult.success();
    }

    @Override
    public Result<ResponseApplyMenuConfig> getApplyMenuConfig(int appId, Integer singerType) {
        LogContext.addReqLog("appId={}, singerType={}", appId, singerType);
        LogContext.addResLog("appId={}, singerType={}", appId, singerType);
        SingerMenuConfigDTO dto = singerAuditConfigManager.getApplyMenuConfig(appId, singerType);
        LogContext.addResLog("dto={}", JsonUtil.dumps(dto));
        return RpcResult.success(SingerConfigConvert.I.convertResponseSaveApplyMenuConfig(dto));
    }

    @Override
    public Result<ResponseSongStyleConfig> getSongStyleConfig(int appId) {
        //获取歌曲风格配置
        Map<Integer, Integer> maxStyleNumMap = singerAnchorConfig.getBizConfig(appId).getMaxStyleNumMap();
        List<SongStyleConfig> res = Lists.newArrayList();
        if (maxStyleNumMap != null) {
            for (Map.Entry<Integer, Integer> entry : maxStyleNumMap.entrySet()) {
                SongStyleConfig songStyleConfig = new SongStyleConfig().setSingerType(entry.getKey()).setMaxStyleNum(entry.getValue()).setSupportMultiStyle(entry.getValue() > 1);
                res.add(songStyleConfig);
            }
        }
        return RpcResult.success(new ResponseSongStyleConfig().setSongAuditConfig(res));
    }
}
