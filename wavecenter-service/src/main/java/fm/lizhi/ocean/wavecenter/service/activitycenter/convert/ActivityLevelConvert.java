package fm.lizhi.ocean.wavecenter.service.activitycenter.convert;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityLevelInfoBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityLevelConfigBean;

@Mapper(
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface ActivityLevelConvert {

    ActivityLevelConvert I = Mappers.getMapper(ActivityLevelConvert.class);

    List<ActivityLevelInfoBean> convert2ActivityLevelInfoBeans(List<ActivityLevelConfigBean> list);
}
