package fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 条件DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConditionDTO {

    /**
     * 指标code
     */
    @NotBlank(message = "指标code不能为空")
    private String metricCode;

    /**
     * 比较符 (>=, >, <=, <, =, !=)
     */
    @NotBlank(message = "比较符不能为空")
    private String comparator;

    /**
     * 值
     */
    @NotBlank(message = "条件值不能为空")
    private String value;
}
