package fm.lizhi.ocean.wavecenter.service.live.convert;

import fm.lizhi.ocean.wavecenter.api.live.response.ResponseGetRoomInfoByNjId;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.GetRoomInfoByNjIdDTO;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

/**
 * 直播间转换器
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface LiveRoomConverter {

    ResponseGetRoomInfoByNjId toResponseGetRoomInfoByNjId(GetRoomInfoByNjIdDTO dto);
}
