package fm.lizhi.ocean.wavecenter.service.live.impl;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.live.bean.LiveInfoBean;
import fm.lizhi.ocean.wavecenter.api.live.response.ResponseGetRoomInfoByNjId;
import fm.lizhi.ocean.wavecenter.api.live.service.LiveService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.GetRoomInfoByNjIdDTO;
import fm.lizhi.ocean.wavecenter.service.live.convert.LiveRoomConverter;
import fm.lizhi.ocean.wavecenter.service.live.manager.LiveManager;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/8/26 14:31
 */
@ServiceProvider
public class LiveServiceImpl implements LiveService {

    @Autowired
    private LiveManager liveManager;

    @Autowired
    private LiveRoomConverter liveRoomConverter;

    @Override
    public Result<LiveInfoBean> getLiveInfo(int appId, long liveId) {
        LogContext.addReqLog("appId={},liveId={}", appId, liveId);
        LogContext.addReqLog("appId={},liveId={}", appId, liveId);
        return ResultHandler.handle(appId, ()->{
            Optional<LiveInfoBean> liveInfoOp = liveManager.getLiveInfo(liveId);
            return liveInfoOp.map(liveInfoBean -> RpcResult.success(new LiveInfoBean()
                            .setUserId(liveInfoBean.getUserId())
                            .setOnAir(liveInfoBean.getOnAir()))
                    )
                    .orElseGet(() -> RpcResult.fail(LIVE_NOT_FOUNT));
        });
    }

    @Override
    public Result<ResponseGetRoomInfoByNjId> getRoomInfoByNjId(int appId, long njId) {
        LogContext.addReqLog("appId={}`njId={}", appId, njId);
        LogContext.addResLog("appId={}`njId={}", appId, njId);
        return ResultHandler.handle(appId, () -> {
            Result<GetRoomInfoByNjIdDTO> result = liveManager.getRoomInfoByNjId(njId);
            if (RpcResult.isFail(result)) {
                return RpcResult.fail(result.rCode(), result.getMessage());
            }
            GetRoomInfoByNjIdDTO dto = result.target();
            ResponseGetRoomInfoByNjId resp = liveRoomConverter.toResponseGetRoomInfoByNjId(dto);
            return RpcResult.success(resp);
        });
    }
}
