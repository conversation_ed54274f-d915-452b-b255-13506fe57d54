package fm.lizhi.ocean.wavecenter.service.grow.ability.manager;

import fm.lizhi.ocean.wavecenter.service.datacenter.dto.PlayerDataForGrowDTO;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.GrowPlayerMetricValueDTO;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.SettlePeriodDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/10 14:31
 */
public interface GrowPlayerMetricManager {

    void savePlayerMetric(PlayerDataForGrowDTO data, SettlePeriodDTO settlePeriodDTO);

    /**
     * 查询主播指标数据合计
     * @param roomId
     * @param settlePeriodDTO
     * @return
     */
    GrowPlayerMetricValueDTO queryPlayerMetricSum(Long roomId, SettlePeriodDTO settlePeriodDTO);

}
