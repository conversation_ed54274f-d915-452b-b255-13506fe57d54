package fm.lizhi.ocean.wavecenter.service.sign.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/10/28 14:30
 */
@Data
@Accessors(chain = true)
public class SignStatusSyncDTO {

    private Long id;

    /**
     * 业务 ID
     */
    private Integer appId;

    /**
     * 签约ID,合同ID或者签约记录ID
     */
    private Long contractId;

    /**
     * 确认状态,WAIT_CREATE_SIGN=待发起人签署,WAIT_TARGET_SIGN=待接受方签署,CONFIRM=已确认
     */
    private String confirmStatus;

    /**
     * 类型：SIGN签约，CANCEL解约
     */
    private String type;

    /**
     * 发起人角色：ROOM,FAMILY,PLAYER,USER
     */
    private String createRole;

}
