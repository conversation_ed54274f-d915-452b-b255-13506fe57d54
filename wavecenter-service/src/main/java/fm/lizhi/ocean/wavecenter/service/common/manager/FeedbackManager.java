package fm.lizhi.ocean.wavecenter.service.common.manager;

import fm.lizhi.ocean.wavecenter.api.common.bean.AddEvaluateRecordReq;
import fm.lizhi.ocean.wavecenter.api.common.bean.GetEvaluateRecordCountReq;

/**
 * <AUTHOR>
 * @date 2024/6/14 20:42
 */
public interface FeedbackManager {

    void addEvaluateRecord(AddEvaluateRecordReq req);

    Long getEvaluateRecordCount(GetEvaluateRecordCountReq req);

}
