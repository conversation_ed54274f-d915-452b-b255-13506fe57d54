package fm.lizhi.ocean.wavecenter.service.income.convert;

import fm.lizhi.ocean.wavecenter.api.income.bean.RoomSignRoomBean;
import fm.lizhi.ocean.wavecenter.service.income.dto.GiveGiftFlowDto;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/24 16:01
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface IncomeConvert {

    IncomeConvert I = Mappers.getMapper(IncomeConvert.class);

    @Mappings({
            @Mapping(target = "recRoomInfo.id", source = "roomId"),
            @Mapping(target = "sendUserInfo.id", source = "sendUserId"),
            @Mapping(target = "recUserInfo.id", source = "recUserId"),
    })
    RoomSignRoomBean giveGiftFlowDto2RoomSignRoomBean(GiveGiftFlowDto dto);

    List<RoomSignRoomBean> giveGiftFlowDtos2RoomSignRoomBeans(List<GiveGiftFlowDto> dto);

}
