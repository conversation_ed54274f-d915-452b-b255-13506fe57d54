package fm.lizhi.ocean.wavecenter.service.permissions.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/4/16 19:27
 */
@Data
public class RoleAuthRefDto {

    private Long id;

    /**
     * 业务线
     */
    private Integer appId;

    /**
     * 被授权用户ID
     */
    private Long userId;

    /**
     * 角色编码
     */
    private String roleCode;

    /**
     * 授权范围主体ID, role_code=家族长, 该字段为family_id; role_code=厅长, 该字段为厅主ID
     */
    private Long subjectId;

    /**
     * 授权范围主体用户ID, role_code=家族长, 该字段为家族长用户ID; role_code=厅长, 该字段为厅主ID
     */
    private Long subjectUserId;

    /**
     * 0=禁用, 1=启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

}
