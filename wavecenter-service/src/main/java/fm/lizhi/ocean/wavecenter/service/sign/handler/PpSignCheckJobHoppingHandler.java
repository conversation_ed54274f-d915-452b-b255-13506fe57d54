package fm.lizhi.ocean.wavecenter.service.sign.handler;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wavecenter.service.sign.config.SignConfig;
import fm.lizhi.ocean.wavecenter.service.sign.manager.ContractManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.NonContractManager;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * PP签约防跳槽检查公共逻辑
 * <AUTHOR>
 * @date 2025/5/21 17:58
 */
@Slf4j
@Component
public class PpSignCheckJobHoppingHandler {

    @Autowired
    private SignConfig signConfig;
    @Autowired
    private ContractManager contractManager;
    @Autowired
    private FamilyManager familyManager;
    @Autowired
    private UserManager userManager;
    @Autowired
    private NonContractManager nonContractManager;

    /**
     * 校验通过
     */
    private int doCheckJobHoppingPass = 0;

    /**
     * 异常
     */
    private int doCheckJobHoppingE1 = 1;

    /**
     * 实名账号存在签约了，但是公会不一致
     */
    private int doCheckJobHoppingE2 = 2;

    /**
     * 实名账号存在冷冻期，且公会不一致
     */
    private int doCheckJobHoppingE3 = 3;

    /**
     * 厅主接受用户申请，让用户成为陪玩 检查
     * @param njId
     * @param userId
     * @return
     */
    public Pair<Integer, String> forAdminAcceptUserApply(Long njId, Long userId){
        log.info("forAdminAcceptUserApply njId={},userId={}", njId, userId);
        // 查询厅主签约家族
        Optional<Long> familyIdOp = contractManager.getNjSignFamilyId(njId);
        if (!familyIdOp.isPresent()) {
            // 没有签约, 直接通过
            return Pair.of(0, "");
        }
        Long familyId = familyIdOp.get();
        log.info("familyId={}", familyId);
        Optional<String> societyCodeOp = familyManager.getSocietyCode(familyId);
        if (!societyCodeOp.isPresent()) {
            log.error("forAdminAcceptUserApply but getSocietyCode is null, familyId={}", familyId);
            return Pair.of(-1, "服务异常");
        }

        int jobHoppingRes = doCheckJobHopping(userId, societyCodeOp.get());
        log.info("jobHoppingRes={}", jobHoppingRes);
        if (jobHoppingRes == doCheckJobHoppingE1) {
            log.error("forAdminAcceptUserApply but doCheckJobHopping error, societyCode={}", societyCodeOp.get());
            return Pair.of(-1, "服务异常");
        } else if (jobHoppingRes == doCheckJobHoppingE2) {
            return Pair.of(-1, "该用户其他实名账号已签约其他公会，请在对方解约后重试");
        } else if (jobHoppingRes == doCheckJobHoppingE3) {
            return Pair.of(-1, "该用户其他实名账号与其他公会处于解约中，请在对方完成解约后重试");
        }
        return Pair.of(0, "");
    }

    /**
     * 用户接受厅主邀请，签约为陪玩 检查
     * @param userId
     * @param njId
     * @return
     */
    public Pair<Integer, String> forUserAcceptToBePlayer(Long userId, Long njId) {
        log.info("forUserAcceptToBePlayer userId={}, njId={}", userId, njId);

        // 查询厅主签约家族
        Optional<Long> familyIdOp = contractManager.getNjSignFamilyId(njId);
        if (!familyIdOp.isPresent()) {
            // 没有签约, 直接通过
            return Pair.of(0, "");
        }
        Long familyId = familyIdOp.get();
        log.info("familyId={}", familyId);
        Optional<String> societyCodeOp = familyManager.getSocietyCode(familyId);
        if (!societyCodeOp.isPresent()) {
            log.error("forUserAcceptToBePlayer but getSocietyCode is null, familyId={}", familyId);
            return Pair.of(-1, "服务异常");
        }

        int jobHoppingRes = doCheckJobHopping(userId, societyCodeOp.get());
        log.info("jobHoppingRes={}", jobHoppingRes);
        if (jobHoppingRes == doCheckJobHoppingE1) {
            log.error("forUserApply but doCheckJobHopping error, societyCode={}", societyCodeOp.get());
            return Pair.of(-1, "服务异常");
        } else if (jobHoppingRes == doCheckJobHoppingE2) {
            return Pair.of(-1, "您的其他实名账号已签约其他公会，请在解约后重试");
        } else if (jobHoppingRes == doCheckJobHoppingE3) {
            return Pair.of(-1, "您的其他实名账号与其他公会所在的厅处于解约中，请在对方完成解约后重试");
        }
        return Pair.of(0, "");
    }

    /**
     * 用户接受家族长邀请，签约为管理员 检查
     * @return
     */
    public Pair<Integer, String> forUserAcceptToBeAdmin(Long userId, Long familyId){
        log.info("forUserAcceptToBeAdmin userId={}, familyId={}", userId, familyId);

        Optional<String> societyCodeOp = familyManager.getSocietyCode(familyId);
        if (!societyCodeOp.isPresent()) {
            log.error("forUserAcceptToBeAdmin but getSocietyCode is null, familyId={}", familyId);
            return Pair.of(-1, "服务异常");
        }

        int jobHoppingRes = doCheckJobHopping(userId, societyCodeOp.get());
        log.info("jobHoppingRes={}", jobHoppingRes);
        if (jobHoppingRes == doCheckJobHoppingE1) {
            log.error("forUserAcceptToBeAdmin but doCheckJobHopping error, societyCode={}", societyCodeOp.get());
            return Pair.of(-1, "服务异常");
        } else if (jobHoppingRes == doCheckJobHoppingE2) {
            return Pair.of(-1, "您的其他实名账号已签约其他公会，请在解约后重试");
        } else if (jobHoppingRes == doCheckJobHoppingE3) {
            return Pair.of(-1, "您的其他实名账号与其他公会处于解约中，请在对方完成解约后重试");
        }
        return Pair.of(0, "");
    }

    /**
     * 用户申请检查
     * @param userId
     * @param njId
     * @return
     */
    public Pair<Integer, String> forUserApply(Long userId, Long njId){
        log.info("forUserApply. userId={}, njId={}", userId, njId);
        // 查询厅主签约家族
        Optional<Long> familyIdOp = contractManager.getNjSignFamilyId(njId);
        if (!familyIdOp.isPresent()) {
            // 没有签约, 直接通过
            return Pair.of(0, "");
        }
        Long familyId = familyIdOp.get();
        log.info("familyId={}", familyId);
        Optional<String> societyCodeOp = familyManager.getSocietyCode(familyId);
        if (!societyCodeOp.isPresent()) {
            log.error("forUserApply but getSocietyCode is null, familyId={}", familyId);
            return Pair.of(-1, "服务异常");
        }

        int jobHoppingRes = doCheckJobHopping(userId, societyCodeOp.get());
        log.info("jobHoppingRes={}", jobHoppingRes);
        if (jobHoppingRes == doCheckJobHoppingE1) {
            log.error("forUserApply but doCheckJobHopping error, societyCode={}", societyCodeOp.get());
            return Pair.of(-1, "服务异常");
        } else if (jobHoppingRes == doCheckJobHoppingE2) {
            return Pair.of(-1, "您的其他实名账号已签约其他公会旗下的厅，请在完成解约后重试");
        } else if (jobHoppingRes == doCheckJobHoppingE3) {
            return Pair.of(-1, "您的其他实名账号与其他公会旗下的厅处于解约中，请在完成解约后重试");
        }
        return Pair.of(0, "");
    }

    /**
     * 厅主邀请用户检查
     * @param njId
     * @param userId
     * @return
     */
    public Pair<Integer, String> forNjInvite(Long njId, Long userId){
        log.info("forNjInvite. njId={}, userId={}", njId, userId);
        // 查询厅主签约家族
        Optional<Long> familyIdOp = contractManager.getNjSignFamilyId(njId);
        if (!familyIdOp.isPresent()) {
            // 没有签约, 直接通过
            return Pair.of(0, "");
        }
        Long familyId = familyIdOp.get();
        log.info("familyId={}", familyId);
        Optional<String> societyCodeOp = familyManager.getSocietyCode(familyId);
        if (!societyCodeOp.isPresent()) {
            log.error("forNjInvite but getSocietyCode is null, familyId={}", familyId);
            return Pair.of(-1, "服务异常");
        }

        int jobHoppingRes = doCheckJobHopping(userId, societyCodeOp.get());
        log.info("jobHoppingRes={}", jobHoppingRes);
        if (jobHoppingRes == doCheckJobHoppingE1) {
            log.error("forNjInvite but doCheckJobHopping error, societyCode={}", societyCodeOp.get());
            return Pair.of(-1, "服务异常");
        } else if (jobHoppingRes == doCheckJobHoppingE2) {
            return Pair.of(-1, "该用户其他实名账号已签约其他公会旗下的厅，请在对方完成解约后重试");
        } else if (jobHoppingRes == doCheckJobHoppingE3) {
            return Pair.of(-1, "该用户其他实名账号与其他公会旗下的厅处于解约中，请在对方完成解约后重试");
        }
        return Pair.of(0, "");
    }

    /**
     * 校验跳槽相关
     *
     * @param familyId 邀请者家族
     * @param inviteeId 被邀请者id
     * @return left=code,  right=msg
     */
    public Pair<Integer, String> forFamilyInvite(Long familyId, long inviteeId) {
        log.info("forFamilyInvite. familyId={}, inviteeId={}", familyId, inviteeId);

        // 发起邀请方所属的工会
        Optional<String> societyCodeOp = familyManager.getSocietyCode(familyId);
        if (!societyCodeOp.isPresent()) {
            log.error("forFamilyInvite but getSocietyCode is null");
            return Pair.of(-1, "服务异常");
        }
        int jobHoppingRes = doCheckJobHopping(inviteeId, societyCodeOp.get());
        log.info("jobHoppingRes={}", jobHoppingRes);
        if (jobHoppingRes == doCheckJobHoppingE1) {
            log.error("forFamilyInvite but doCheckJobHopping error, societyCode={}", societyCodeOp.get());
            return Pair.of(-1, "服务异常");
        } else if (jobHoppingRes == doCheckJobHoppingE2) {
            return Pair.of(-1, "该用户其他实名账号已签约其他公会，请在对方完成解约后重试");
        } else if (jobHoppingRes == doCheckJobHoppingE3) {
            return Pair.of(-1, "该用户其他实名账号与其他公会处于解约中，请在对方完成解约后重试");
        }
        return Pair.of(0, "");
    }

    /**
     * 校验跳槽相关
     *
     * @param targetUserId 要与厅、家族签约的目标用户id
     * @param societyCode 要签约的厅、家族所属的工会
     * @return
     */
    public int doCheckJobHopping(long targetUserId, String societyCode) {
        log.info("doCheckJobHopping targetUserId={}, societyCode={}", targetUserId, societyCode);
        if (!signConfig.getPp().isCheckJobHoppingSwitch()) {
            return doCheckJobHoppingPass;
        }

        // 通过实名证件获取所有关联的用户id
        List<Long> userIds = userManager.getUserIdFromVerifyResult(targetUserId);
        if (userIds == null) {
            return doCheckJobHoppingE1;
        }
        log.info("doCheckJobHopping userIds={}", JsonUtil.dumps(userIds));
        for (long userId : userIds) {
            // 目标用户签约厅主id
            Optional<Long> signNjOpt = nonContractManager.getPlayerCurSignNj(userId);

            // 处于解约冷冻期中的厅主id
            Long cancelSignNjId = 0L;
            // 未签约厅，则查询是否有处于解约冷冻期中的厅主id
            if (!signNjOpt.isPresent()) {
                Optional<Long> cancelSignNjIdOp = nonContractManager.getPlayerLastCancelSign(userId);
                if (!cancelSignNjIdOp.isPresent()) {
                    return doCheckJobHoppingE1;
                }
                cancelSignNjId = cancelSignNjIdOp.get();
                log.info("doCheckJobHopping userId={}, cancelSignNjId={}", userId, cancelSignNjId);
            }

            // 不在解约冷却期中
            if (cancelSignNjId <= 0) {
                // 有签约厅
                if (signNjOpt.isPresent()) {
                    log.info("doCheckJobHopping userId={}, signNjOpt={}", userId, signNjOpt.get());
                    // 目标用户签约厅所属家族所属工会
                    String inviteeSocietyCode = getSocietyCodeByUserId(signNjOpt.get());
                    log.info("doCheckJobHopping userId={}, signNjOpt={}, inviteeSocietyCode={}", userId, signNjOpt.get(), inviteeSocietyCode);
                    if (inviteeSocietyCode == null) {
                        // null 表示查询有签约，但是查询公会编码的时候异常
                        return doCheckJobHoppingE1;
                    } else if (!inviteeSocietyCode.isEmpty() && !societyCode.equals(inviteeSocietyCode)) {
                        return doCheckJobHoppingE2;
                    }
                }

                // 目标用户签约家族所属工会
                String inviteeSocietyCode = getSocietyCodeByUserId(userId);
                log.info("doCheckJobHopping down userId={}, inviteeSocietyCode={}", userId, inviteeSocietyCode);
                if (inviteeSocietyCode == null) {
                    // null 表示查询有签约，但是查询公会编码的时候异常
                    return doCheckJobHoppingE1;
                } else if (!inviteeSocietyCode.isEmpty() && !societyCode.equals(inviteeSocietyCode)) {
                    return doCheckJobHoppingE2;
                }
            } else {
                // 目标用户冷却期中的厅所属家族所属工会
                String inviteeSocietyCode = getSocietyCodeByUserId(cancelSignNjId);
                log.info("doCheckJobHopping cancelSignNjId exist. userId={}, cancelSignNjId={}, inviteeSocietyCode={}", userId, cancelSignNjId, inviteeSocietyCode);
                if (inviteeSocietyCode == null) {
                    // null 表示查询有签约，但是查询公会编码的时候异常
                    return doCheckJobHoppingE1;
                } else if (!inviteeSocietyCode.isEmpty() && !societyCode.equals(inviteeSocietyCode)) {
                    return doCheckJobHoppingE3;
                }
            }
        }
        return doCheckJobHoppingPass;
    }

    private String getSocietyCodeByUserId(Long userId){
        Optional<Long> familyIdOp = contractManager.getNjSignFamilyId(userId);
        if (!familyIdOp.isPresent()) {
            return "";
        }
        return familyManager.getSocietyCode(familyIdOp.get()).orElse(null);
    }

}
