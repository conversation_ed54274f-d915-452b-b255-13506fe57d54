package fm.lizhi.ocean.wavecenter.service.anchor.singer.handler;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerAuditConfigCodeEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestSingerVerifyApply;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestSingerVerifyApplyV2;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerAuditConfigDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerFilterParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerAuditConfigManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SingerPreAuditHandler {
    @Autowired
    private SingerPreAuditFilterFactory filterFactory;

    @Autowired
    private SingerAuditConfigManager singerAuditConfigManager;

    /**
     * 预审核处理器
     *
     * @param request 请求参数
     * @return 结果
     */
    public Result<Void> preAuditHandle(RequestSingerVerifyApplyV2 request) {
        SingerFilterParamDTO param = new SingerFilterParamDTO().setAppId(request.getAppId()).setUserId(request.getUserId());
        // 获取预审核配置
        List<SingerAuditConfigDTO> backendConfigBeans = singerAuditConfigManager.getSingerAuditConfigByScene(request.getAppId(), SingerAuditConfigCodeEnum.AuditSceneEnum.BACKEND.getScene());

        //过滤出审核场景是后端的对象
        backendConfigBeans = backendConfigBeans.stream()
                .filter(SingerAuditConfigDTO::getEnabled)
                .collect(Collectors.toList());
        for (SingerAuditConfigDTO config : backendConfigBeans) {
            SingerPreAuditFilter filter = filterFactory.getHandler(config.getConfigCode());
            if (filter == null) {
                continue;
            }
            Result<Void> filterRes = filter.filter(param);
            if (RpcResult.isFail(filterRes)) {
                //有一个失败了，直接返回
                log.warn("歌手认证预审核失败，configCode={}, filterRes={}", config.getConfigCode(), filterRes);
                return filterRes;
            }
        }
        return RpcResult.success();
    }

}
