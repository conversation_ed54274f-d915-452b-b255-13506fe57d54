package fm.lizhi.ocean.wavecenter.service.activitycenter.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityUserCancelParamDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityUserModifyParamDTO;

public interface ActivityOperateManager {

    /**
     * 用户在审批前取消活动
     *
     * @param cancelParamDTO 取消参数
     * @return 结果
     */
    Result<Void> cancelActivityBeforeAudit(ActivityUserCancelParamDTO cancelParamDTO);

    /**
     * 用户在审核后取消活动
     *
     * @param cancelParamDTO 取消参数
     * @return 结果
     */
    Result<String> cancelActivityAfterAudit(ActivityUserCancelParamDTO cancelParamDTO);

    /**
     * 修改活动
     *
     * @param paramDTO 修改参数
     * @return 结果
     */
    Result<Void> userModifyActivityAfterAudit(ActivityUserModifyParamDTO paramDTO);

    int CANCEL_ACTIVITY_FAIL = 1;

    int MODIFY_ACTIVITY_PARAM_ERROR = 1;

    int MODIFY_ACTIVITY_FAIL = 2;


}
