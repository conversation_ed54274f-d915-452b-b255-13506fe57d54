package fm.lizhi.ocean.wavecenter.service.income.impl;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.*;
import fm.lizhi.ocean.wavecenter.api.income.service.IncomePlayerService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.base.processor.ProcessorFactory;
import fm.lizhi.ocean.wavecenter.service.income.manager.FlowManager;
import fm.lizhi.ocean.wavecenter.service.income.manager.IncomeManager;
import fm.lizhi.ocean.wavecenter.service.income.process.PersonalProcess;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2024/4/23 15:19
 */
@ServiceProvider
public class IncomePlayerServiceImpl implements IncomePlayerService {

    @Autowired
    private IncomeManager incomeManager;

    @Autowired
    private FlowManager flowManager;


    @Autowired
    private ProcessorFactory processorFactory;


    @Override
    public Result<PageBean<PersonalIncomeDetailBean>> getPersonalIncomeDetail(GetPersonalIncomeDetailParamBean paramBean) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));
        return ResultHandler.handle(paramBean.getAppId(), () -> RpcResult.success(incomeManager.getPersonalIncomeDetail(paramBean)));
    }

    @Override
    public Result<PageBean<PersonalIncomeDetailBean>> getPersonalIncomeDetailOut(GetPersonalIncomeDetailParamBean paramBean) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));
        return ResultHandler.handle(paramBean.getAppId(), () -> RpcResult.success(flowManager.getPersonalIncomeDetailOut(paramBean)));
    }

    @Override
    public Result<PersonalIncomeDetailSumBean> getPersonalIncomeDetailSum(GetPersonalIncomeDetailSumParamBean paramBean) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));
        return ResultHandler.handle(paramBean.getAppId(), () -> RpcResult.success(incomeManager.getPersonalIncomeDetailSum(paramBean)));
    }

    @Override
    public Result<PageBean<PlayerIncomeDetailBean>> getPlayerIncomeDetail(GetPlayerIncomeDetailParamBean paramBean) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));
        return ResultHandler.handle(paramBean.getAppId(), () -> {
            PersonalProcess personalProcess = processorFactory.getProcessor(PersonalProcess.class);
            boolean enablePlayerIncome = personalProcess.enablePlayerIncome();
            if (!enablePlayerIncome) {
                // app端未开启 个播
                return RpcResult.success(PageBean.empty());
            }
            return RpcResult.success(incomeManager.getPlayerIncomeDetail(paramBean));
        });
    }

    @Override
    public Result<PageBean<PlayerIncomeDetailBean>> getPlayerIncomeDetailOut(GetPlayerIncomeDetailParamBean paramBean) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));
        return ResultHandler.handle(paramBean.getAppId(), () -> {
            PersonalProcess personalProcess = processorFactory.getProcessor(PersonalProcess.class);
            boolean enablePlayerIncome = personalProcess.enablePlayerIncome();
            if (!enablePlayerIncome) {
                // app端未开启 个播
                return RpcResult.success(PageBean.empty());
            }
            return RpcResult.success(flowManager.getPlayerIncomeDetailOut(paramBean));
        });
    }

    @Override
    public Result<PlayerIncomeDetailSumBean> getPlayerIncomeDetailSum(GetPlayerIncomeDetailSumParamBean paramBean) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));
        return ResultHandler.handle(paramBean.getAppId(), () -> {
            PersonalProcess personalProcess = processorFactory.getProcessor(PersonalProcess.class);
            boolean enablePlayerIncome = personalProcess.enablePlayerIncome();
            if (!enablePlayerIncome) {
                // app端未开启 个播
                return RpcResult.success();
            }
            return RpcResult.success(incomeManager.getPlayerIncomeDetailSum(paramBean));
        });
    }

    @Override
    public Result<PlayerSumResBean> playerSum(int appId, long userId) {
        LogContext.addReqLog("appId={},userId={}", appId, userId);
        LogContext.addResLog("appId={},userId={}", appId, userId);
        return ResultHandler.handle(appId, () -> RpcResult.success(incomeManager.playerSum(userId)));
    }

    @Override
    public Result<PageBean<PersonalGiftflowBean>> playerPersonalGiftflow(PlayerPersonalGiftflowParamBean paramBean) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));
        return ResultHandler.handle(paramBean.getAppId(), () -> {
            PageBean<PersonalGiftflowBean> pageBean = incomeManager.playerPersonalGiftflow(paramBean);
            return RpcResult.success(pageBean);
        });
    }

    @Override
    public Result<PersonalGiftflowBean> playerPersonalGiftflowSum(PlayerPersonalGiftflowParamBean paramBean) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));
        return ResultHandler.handle(paramBean.getAppId(), () -> RpcResult.success(incomeManager.playerPersonalGiftflowSum(paramBean)));
    }

    @Override
    public Result<PageBean<PlayerRoomGiftflowBean>> playerRoomGiftflow(PlayerRoomGiftflowParamBean paramBean) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));
        return ResultHandler.handle(paramBean.getAppId(), () -> {
            return RpcResult.success(incomeManager.playerRoomGiftflow(paramBean));
        });
    }

    @Override
    public Result<PlayerRoomGiftflowBean> playerRoomGiftflowSum(PlayerRoomGiftflowParamBean paramBean) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));
        return ResultHandler.handle(paramBean.getAppId(), () -> {
            return RpcResult.success(incomeManager.playerRoomGiftflowSum(paramBean));
        });
    }




    @Override
    public Result<PageBean<PersonalIncomeDetailBean>> getRevenueIncomeDetail(GetPersonalRevenueIncomeDetailParamBean paramBean) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));
        return ResultHandler.handle(paramBean.getAppId(), () -> RpcResult.success(incomeManager.getRevenueIncomeDetail(paramBean)));
    }

    @Override
    public Result<PageBean<PersonalIncomeDetailBean>> getRevenueIncomeDetailOut(GetPersonalIncomeDetailParamBean paramBean) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));
        return ResultHandler.handle(paramBean.getAppId(), () -> RpcResult.success(flowManager.getPersonalIncomeDetailOut(paramBean)));
    }

    @Override
    public Result<PersonalIncomeDetailSumBean> getRevenueIncomeDetailSum(GetPersonalRevenueIncomeDetailParamBean paramBean) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));
        return ResultHandler.handle(paramBean.getAppId(), () -> RpcResult.success(incomeManager.getRevenueIncomeDetailSum(paramBean)));
    }


}
