package fm.lizhi.ocean.wavecenter.service.live.handler;

import cn.hutool.crypto.SignUtil;
import fm.lizhi.ocean.wavecenter.api.common.constants.DateType;
import fm.lizhi.ocean.wavecenter.api.live.constants.CheckInDateTypeEnum;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface CheckInRoomPeriodStatHandler {

    boolean support(CheckInDateTypeEnum typeEnum);


    String buildContent(Integer appId, Long njId, Date statTime);


    List<Long> populateExtraReceiver(Integer appId, Long njId, Date statTime);

    boolean isTimeToReport(Date triggerTime);

    boolean canSend(Long njId);

    default String buildReportUrl(CheckInDateTypeEnum dateType, Integer appId, Long njId, Date startDate, Date endDate, String checkInReportUrl, String salt) {
        Map<String, String> params = new HashMap<>();
        params.put("dateType", dateType.name());
        params.put("njId", njId.toString());
        params.put("startDate", String.valueOf(startDate.getTime()));
        params.put("endDate", String.valueOf(endDate.getTime()));
        params.put("appId", appId.toString());
        String signCode = SignUtil.signParamsMd5(params, salt);
        for (Map.Entry<String, String> entry : params.entrySet()) {
            checkInReportUrl = checkInReportUrl.replace("$"+entry.getKey(), entry.getValue());
        }
        return checkInReportUrl.replace("$signCode", signCode);
    }
}
