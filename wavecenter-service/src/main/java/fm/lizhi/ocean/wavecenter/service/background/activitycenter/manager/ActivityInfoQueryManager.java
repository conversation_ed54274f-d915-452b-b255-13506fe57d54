package fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager;


import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.RequestQueryEffectActivitiesByTemplateIdBean;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivitySimpleInfoDT0;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivitySimpleQueryParamDTO;

import java.util.List;

public interface ActivityInfoQueryManager {

    /**
     * 查询活动信息列表
     *
     * @param params 参数
     * @return 结果
     */
    PageBean<ActivitySimpleInfoDT0> queryActivityInfoList(ActivitySimpleQueryParamDTO params);

    /**
     * 查询活动信息列表
     */
    List<ActivitySimpleInfoDT0> queryEffectActivityInfoListByTemplateId(RequestQueryEffectActivitiesByTemplateIdBean params);

}
