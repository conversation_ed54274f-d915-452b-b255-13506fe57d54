package fm.lizhi.ocean.wavecenter.service.grow.ability.manager;

import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.RoomAbilityWeekCapabilityDTO;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.SettlePeriodDTO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/11 14:55
 */
public interface GrowRoomAbilityManager {

    /**
     * 查询所有厅的能力项情况
     * @param familyId 可以为空
     * @param settlePeriod
     * @param minId
     * @param pageSize
     * @return
     */
    List<RoomAbilityWeekCapabilityDTO> getRoomAbilityWeekCapability(Long familyId, SettlePeriodDTO settlePeriod, Long minId, Integer pageSize);

    /**
     * 统计在公会中厅的能力分高于abilityValue的厅数
     * @param familyId
     * @param capabilityCode
     * @param abilityValue
     * @param settlePeriod
     * @return
     */
    Integer countInFamilyCapabilityHighRoomNum(Long familyId, String capabilityCode, BigDecimal abilityValue, SettlePeriodDTO settlePeriod);

    /**
     * 查询厅能力在指定周期的信息
     * @param settlePeriod
     * @param roomId
     * @param capabilityCode
     * @return
     */
    RoomAbilityWeekCapabilityDTO getRoomAbilityWeekCapability(SettlePeriodDTO settlePeriod, Long roomId, String capabilityCode);

    /**
     * 更新厅能力在指定周期的信息
     * @param dto
     */
    void updateRoomAbilityWeekCapability(RoomAbilityWeekCapabilityDTO dto);

}
