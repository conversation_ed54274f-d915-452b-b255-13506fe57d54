package fm.lizhi.ocean.wavecenter.service.sign.process.hy;

import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.FlowConfirmStatusEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestAdminApplyCancelFamily;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestAdminDoCancelFamily;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestAdminInviteUser;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestAdminOperateSign;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseAdminApplyCancelFamily;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseAdminInviteUser;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseAdminOperateSign;
import fm.lizhi.ocean.wavecenter.api.sign.service.SignAdminService;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.service.sign.dto.FamilyNjSignRecordDTO;
import fm.lizhi.ocean.wavecenter.service.sign.dto.QueryFamilyNjSignRecordDTO;
import fm.lizhi.ocean.wavecenter.service.sign.dto.SignStatusSyncDTO;
import fm.lizhi.ocean.wavecenter.service.sign.manager.ContractManager;
import fm.lizhi.ocean.wavecenter.service.sign.process.AdminSignProcessor;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserMediaDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/10/11 20:55
 */
@Component
public class HyAdminSignProcessor extends HySignAbstractProcessor implements AdminSignProcessor {

    @Autowired
    private ContractManager contractManager;

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.HEI_YE;
    }

    @Override
    public ResponseAdminInviteUser inviteUserCheck(RequestAdminInviteUser request) {
        ResponseAdminInviteUser res = new ResponseAdminInviteUser().setCode(0);

        boolean inChangeCompany = nonContractManager.isInChangeCompany(request.getCurUserId());
        if (inChangeCompany) {
            return res.setCode(-1).setMsg("系统升级，暂不支持签约~");
        }

        boolean isCurAdmin = contractManager.isUserSignAsRoom(request.getCurUserId());
        if (!isCurAdmin) {
            return res.setCode(SignAdminService.INVITE_USER_REQ_NOT_ADMIN);
        }

        Pair<Integer, String> limitRes = nonContractManager.checkInviteSignLimit(request.getTargetUserId(), request.getCurUserId(), RoleEnum.ROOM, RoleEnum.PLAYER);
        if (limitRes.getKey() != 0) {
            return res.setCode(-1).setMsg(limitRes.getValue());
        }

        //方便测试 增加信息检查开关
        if (!signConfig.getHy().isUserBaseInfoCheckSwitch()) {
            return res;
        }

        //媒体信息检查
        if (!mediaPass(request.getCurUserId())) {
            LogContext.addResLog("media is null");
            return res.setCode(SignAdminService.INVITE_USER_MEDIA_INFO_NOT_EXIST);
        }

        if (!genderPass(request.getCurUserId())) {
            LogContext.addResLog("gender is null");
            return res.setCode(SignAdminService.INVITE_USER_MEDIA_INFO_NOT_EXIST);
        }

        //是否完成平台实名认证
        boolean isVerify = userManager.checkUserRealNameAuthStatus(request.getCurUserId(), ContextUtils.getBusinessEvnEnum().appId());
        if (!isVerify) {
            return res.setCode(SignAdminService.INVITE_USER_PLATFORM_VERIFY_NO_PASS);
        }

        return res;
    }

    /**
     * 媒体信息是否通过
     * @param userId
     * @return
     */
    private boolean mediaPass(Long userId){
        Optional<UserMediaDto> media = userManager.getUserMediaById(userId);
        if (!media.isPresent()) {
            return false;
        }
        if (StringUtils.isBlank(media.get().getAlbumListJSON()) || "[]".equals(media.get().getAlbumListJSON())) {
            return false;
        }
        if (StringUtils.isBlank(media.get().getVoiceListJSON()) || "[]".equals(media.get().getVoiceListJSON())) {
            return false;
        }
        return true;
    }

    @Override
    public ResponseAdminOperateSign operateSignCheck(RequestAdminOperateSign request) {
        ResponseAdminOperateSign res = new ResponseAdminOperateSign().setCode(0);

        boolean inChangeCompany = nonContractManager.isInChangeCompany(request.getCurUserId());
        if (inChangeCompany) {
            return res.setCode(-1).setMsg("系统升级，暂不支持签约~");
        }

        //检查可签约人数
        Pair<Integer, String> checkRes = nonContractManager.checkCanSignForConfirm(request.getPlayerSignId(), request.getCurUserId()
                , RoleEnum.ROOM, RoleEnum.PLAYER);
        if (checkRes.getKey() != 0) {
            return res.setCode(-1).setMsg(checkRes.getValue());
        }

        Pair<Integer, String> unionCheckRes = super.checkPGCUnionSign(request.getPlayerSignId());
        if (unionCheckRes.getKey() != 0) {
            return res.setCode(unionCheckRes.getKey()).setMsg(unionCheckRes.getValue());
        }
        return res;
    }

    @Override
    public ResponseAdminApplyCancelFamily applyCancelFamilyCheck(RequestAdminApplyCancelFamily request) {
        ResponseAdminApplyCancelFamily res = new ResponseAdminApplyCancelFamily();

        Long curUserId = request.getCurUserId();
        boolean inChangeCompanyPreparedStage = nonContractManager.isInChangeCompanyPreparedStage(curUserId);
        boolean inChangeCompany = nonContractManager.isInChangeCompany(curUserId);
        if (inChangeCompanyPreparedStage || inChangeCompany) {
            LogContext.addResLog("changeCompany stage");
            return res.setCode(-1).setMsg("系统升级，暂不支持解约~");
        }

        Optional<FamilyBean> familyOp = familyManager.getFamily(getBusinessEnv().getAppId(), request.getFamilyId());
        if (!familyOp.isPresent()) {
            LogContext.addResLog("family not exist");
            return res.setCode(-1);
        }

        FamilyBean familyBean = familyOp.get();
        if (!isPGC(familyBean)) {
            LogContext.addResLog("is not pgc");
            return res.setCode(-1).setMsg("非PGC家族，暂不支持解约~");
        }

        boolean existChange = contractManager.existEffectChangeObjByNjId(request.getCurUserId());
        if (existChange) {
            return res.setCode(-1).setMsg("您正在主体变更，暂时无法解约");
        }

        //是否存在待签署解约记录
        PageBean<FamilyNjSignRecordDTO> signRecord = contractManager.querySignRecord(QueryFamilyNjSignRecordDTO.builder()
                .type(ContractTypeEnum.CANCEL)
                .status(SignRelationEnum.WAIT_SIGN)
                .status(SignRelationEnum.SIGNING)
                .njId(request.getCurUserId())
                .familyUserId(familyBean.getUserId())
                .build());
        if (CollectionUtils.isNotEmpty(signRecord.getList())) {
            LogContext.addResLog("signRecord is not empty");
            return res.setCode(-1).setMsg("已存在解约申请");
        }

        return res;
    }

    @Override
    public void applyCancelFamilySuccessProcessor(RequestAdminApplyCancelFamily request, Long contractId) {
        //标记待同步签署状态
        SignStatusSyncDTO dto = new SignStatusSyncDTO();
        dto.setType(ContractTypeEnum.CANCEL.getCode());
        dto.setAppId(request.getAppId());
        dto.setContractId(contractId);
        dto.setCreateRole(RoleEnum.ROOM.getRoleCode());
        dto.setConfirmStatus(FlowConfirmStatusEnum.WAIT_CREATE_SIGN.getCode());
        signFlowManager.addSignStatusSync(dto);
    }

    @Override
    public ResponseAdminApplyCancelFamily doApplyCancelFamily(RequestAdminApplyCancelFamily request) {
        ResponseAdminApplyCancelFamily res = new ResponseAdminApplyCancelFamily();

        Optional<String> signIdOp = contractManager.adminApplyCancelFamilyForSignId(request);
        if (!signIdOp.isPresent()) {
            LogContext.addResLog("signId is not present");
            return res.setCode(-1).setMsg("生成合同失败");
        }

        Optional<String> contractUrlOp = contractManager.genContractSignUrl(request.getCurUserId(), signIdOp.get());
        if (!contractUrlOp.isPresent()) {
            LogContext.addResLog("contactUrl is not present");
            return res.setCode(-1).setMsg("获取合同失败");
        }

        return res.setContractUrl(contractUrlOp.get()).setSignId(signIdOp.get());
    }

    @Override
    public void doCancelFamilySuccessProcessor(RequestAdminDoCancelFamily request) {
        SignStatusSyncDTO dto = new SignStatusSyncDTO();
        dto.setType(ContractTypeEnum.CANCEL.getCode());
        dto.setAppId(request.getAppId());
        dto.setContractId(request.getContractId());
        dto.setCreateRole(RoleEnum.ROOM.getRoleCode());
        dto.setConfirmStatus(FlowConfirmStatusEnum.WAIT_TARGET_SIGN.getCode());
        signFlowManager.addSignStatusSync(dto);
    }
}
