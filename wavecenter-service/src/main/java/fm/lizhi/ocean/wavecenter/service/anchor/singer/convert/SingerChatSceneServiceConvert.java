package fm.lizhi.ocean.wavecenter.service.anchor.singer.convert;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerChatSceneBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseSingerChatScene;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerChatSceneDTO;


@Mapper(unmappedTargetPolicy = ReportingPolicy.WARN)
public interface SingerChatSceneServiceConvert {

    SingerChatSceneServiceConvert I = Mappers.getMapper(SingerChatSceneServiceConvert.class);

    SingerChatSceneDTO sceneChatRequestToDto(SingerChatSceneBean bean);

    List<SingerChatSceneDTO> sceneChatRequestToDtos(List<SingerChatSceneBean> beans);

    List<ResponseSingerChatScene> dtoToResponses(List<SingerChatSceneDTO> dtos);

    ResponseSingerChatScene dtoToResponse(SingerChatSceneDTO dto);
} 