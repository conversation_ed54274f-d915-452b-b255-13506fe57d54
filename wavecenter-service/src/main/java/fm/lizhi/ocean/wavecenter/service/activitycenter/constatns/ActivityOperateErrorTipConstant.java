package fm.lizhi.ocean.wavecenter.service.activitycenter.constatns;

public class ActivityOperateErrorTipConstant {

    public static final String USER_CANCEL_ACTIVITY_NOT_EXIST = "活动不存在";
    public static final String USER_CANCEL_ACTIVITY_NOT_PERMISSION = "您不是活动的提报人，无权限取消活动";
    public static final String USER_CANCEL_ACTIVITY_OVER_TIME = "距离活动开始不足%d分钟，不支持取消活动";
    public static final String USER_CANCEL_ACTIVITY_ACTIVITY_GOING = "活动已开始，不支持取消活动";
    public static final String USER_CANCEL_ACTIVITY_ACTIVITY_END = "活动已结束，不支持取消活动";
    public static final String USER_CANCEL_ACTIVITY_AUDIT_STATUS_ERROR = "活动状态非待审核或者审核通过，不支持取消活动";
    public static final String USER_CANCEL_ACTIVITY_CHANGE = "当前活动正在改动，请刷新后查看活动状态";
    public static final String USER_CANCEL_ACTIVITY_FAIL = "取消失败，请稍候重试";
    public static final String ACTIVITY_MODIFY_TOO_FAST = "活动修改冲突，请刷新页面";
    public static final String ACTIVITY_MODIFY_FAIL = "活动修改失败，请稍候重试";
    public static final String ACTIVITY_MODIFY_NOT_PERMISSION = "您不是活动的提报人，无权限修改活动";
    public static final String ACTIVITY_MODIFY_NOT_EXIST_ACTIVITY = "活动不存在，无法修改";
    public static final String ACTIVITY_MODIFY_TIME_INVALID = "距离活动开始不足%d分钟，无法修改";
    public static final String ACTIVITY_MODIFY_PARAM_ERROR = "无修改内容，修改失败";
    public static final String ACTIVITY_RESOURCE_NOT_MODIFY = "活动资源除推荐卡外不允许修改";
    public static final String ACTIVITY_MODIFY_DELETE_RESOURCE_FAIL = "修改失败，失败原因：\"取消业务已配置流量资源失败\"，请稍候重试！";
    public static final String ACTIVITY_MODIFY_RECORD_MODIFY_FAIL = "修改失败，失败原因：\"活动数据记录变更失败\"，请稍候重试！";
    public static final String ACTIVITY_MODIFY_RESOURCE_GIVE_FAIL = "修改失败，失败原因：\"流量资源发放失败\"，请稍候重试！";
}
