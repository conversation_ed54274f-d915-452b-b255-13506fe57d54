package fm.lizhi.ocean.wavecenter.service.anchor.singer.handler.machine;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.BaseStatus;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class StateMachine<S extends BaseStatus> {

    private final Map<S, List<S>> statusMap = new HashMap<>();

    /**
     * 只接受指定的当前状态下，指定的事件触发，可以到达的指定目标状态
     */
    public void accept(S sourceStatus, List<S> targetStatus) {
        List<S> res = statusMap.get(sourceStatus);
        if (!statusMap.containsKey(sourceStatus)) {
            res = new ArrayList<>();
        }
        res.addAll(targetStatus);
        statusMap.put(sourceStatus, res);
    }

    /**
     * 通过源状态和事件，获取目标状态
     */
    public List<S> getTargetStatus(S sourceStatus) {
        return statusMap.get(sourceStatus);
    }

}
