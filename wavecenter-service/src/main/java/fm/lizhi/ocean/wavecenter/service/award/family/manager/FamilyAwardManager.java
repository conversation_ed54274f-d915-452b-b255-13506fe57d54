package fm.lizhi.ocean.wavecenter.service.award.family.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestGetFamilyWeekAwardV1;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestGetFamilyWeekAwardV2;
import fm.lizhi.ocean.wavecenter.api.award.family.response.ResponseGetFamilyWeekAwardV1;
import fm.lizhi.ocean.wavecenter.api.award.family.response.ResponseGetFamilyWeekAwardV2;

public interface FamilyAwardManager {

    /**
     * 获取公会周奖励V1
     *
     * @param request 请求参数
     * @return 获取结果
     */
    Result<ResponseGetFamilyWeekAwardV1> getFamilyWeekAwardV1(RequestGetFamilyWeekAwardV1 request);

    /**
     * 获取公会周奖励V2
     *
     * @param request 请求参数
     * @return 获取结果
     */
    Result<ResponseGetFamilyWeekAwardV2> getFamilyWeekAwardV2(RequestGetFamilyWeekAwardV2 request);
}
