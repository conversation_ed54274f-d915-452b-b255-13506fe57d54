package fm.lizhi.ocean.wavecenter.service.activitycenter.impl;

import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestBatchDeliverSingerAward;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestDeliverSingerAward;
import fm.lizhi.ocean.wavecenter.api.award.singer.serivce.SingerDecorateOperateService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerInfoDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerInfoManager;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowDTO;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowGenerateDTO;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowInitParamDTO;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowSendParamDTO;
import fm.lizhi.ocean.wavecenter.service.award.singer.manager.SingerDecorateFlowManager;
import fm.lizhi.ocean.wavecenter.service.award.singer.manager.SingerDecorateManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@ServiceProvider
public class SingerDecorateOperateServiceImpl implements SingerDecorateOperateService {

    @Autowired
    private SingerInfoManager singerInfoManager;

    @Autowired
    private SingerDecorateManager singerDecorateManager;

    @Autowired
    private SingerDecorateFlowManager singerDecorateFlowManager;


    @Override
    public Result<Void> singerDecorateAwardOperate(RequestDeliverSingerAward request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);
        SingerInfoDTO singerInfoDTO = singerInfoManager.getSingerInfo(request.getAppId(), request.getSingerId(), request.getSingerType());
        if (singerInfoDTO == null || singerInfoDTO.getSingerStatus() != SingerStatusEnum.EFFECTIVE.getStatus()) {
            return RpcResult.fail(SingerDecorateOperateService.DELIVER_SINGER_AWARD_FAIL, "歌手不存在或者不是有效歌手");
        }
        SingerDecorateFlowInitParamDTO initParamDTO = new SingerDecorateFlowInitParamDTO()
                .setSingerInfoList(Collections.singletonList(singerInfoDTO))
                .setAppId(request.getAppId())
                .setOperateType(request.getOperateType())
                .setOperator(request.getOperator())
                .setReason(request.getReason());
        Optional<SingerDecorateFlowGenerateDTO> singerDecorateFlowGenerateDTO = singerDecorateManager.generateSingerDecorateFlowList(initParamDTO);
        if (singerDecorateFlowGenerateDTO.isPresent()) {
            //查询一把事务ID对应的记录是否已经存在了
            List<SingerDecorateFlowDTO> flowList = singerDecorateFlowManager.getDecorateFlowByTransactionId(request.getAppId(), singerDecorateFlowGenerateDTO.get().getTransactionId());
            if (CollectionUtils.isNotEmpty(flowList)) {
                //存在就不往下执行了，定时任务会补偿到成功
                log.warn("singerDecorateAwardOperate transactionId:{} already exist", singerDecorateFlowGenerateDTO.get().getTransactionId());
                return RpcResult.success();
            }
            boolean res = singerDecorateFlowManager.batchInsert(singerDecorateFlowGenerateDTO.get());
            if (!res) {
                log.warn("singerDecorateAwardOperate batchInsert fail. transactionId:{}", singerDecorateFlowGenerateDTO.get().getTransactionId());
                return RpcResult.fail(SingerDecorateOperateService.DELIVER_SINGER_AWARD_FAIL, "装扮操作失败");
            }
            // 调用操作装扮方法
            singerDecorateManager.operateSingerDecorateAsync(new SingerDecorateFlowSendParamDTO().setTransactionId(singerDecorateFlowGenerateDTO.get().getTransactionId()));
        }
        return RpcResult.success();
    }

    @Override
    public Result<Void> batchSingerDecorateAwardOperate(RequestBatchDeliverSingerAward request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);
        List<SingerInfoDTO> singerInfoList = singerInfoManager.getSingerInfoByUserIds(request.getSingerIds(), request.getAppId(),
                Lists.newArrayList(SingerStatusEnum.EFFECTIVE), SingerTypeEnum.getByType(request.getSingerType()));
        if (CollectionUtils.isEmpty(singerInfoList)) {
            return RpcResult.success();
        }
        SingerDecorateFlowInitParamDTO initParamDTO = new SingerDecorateFlowInitParamDTO()
                .setSingerInfoList(singerInfoList)
                .setAppId(request.getAppId())
                .setOperateType(request.getOperateType())
                .setTransactionId(request.getTransactionId())
                .setOperator(request.getOperator())
                .setReason(request.getReason());
        Optional<SingerDecorateFlowGenerateDTO> singerDecorateFlowGenerateDTO = singerDecorateManager.generateSingerDecorateFlowList(initParamDTO);
        if (singerDecorateFlowGenerateDTO.isPresent()) {
            //查询一把事务ID对应的记录是否已经存在了
            List<SingerDecorateFlowDTO> flowList = singerDecorateFlowManager.getDecorateFlowByTransactionId(request.getAppId(), singerDecorateFlowGenerateDTO.get().getTransactionId());
            if (CollectionUtils.isNotEmpty(flowList)) {
                //存在就不往下执行了，定时任务会补偿到成功
                log.warn("batchSingerDecorateAwardOperate transactionId:{} already exist", singerDecorateFlowGenerateDTO.get().getTransactionId());
                return RpcResult.success();
            }
            boolean res = singerDecorateFlowManager.batchInsert(singerDecorateFlowGenerateDTO.get());
            if (!res) {
                log.warn("batchSingerDecorateAwardOperate batchInsert fail. transactionId:{}", singerDecorateFlowGenerateDTO.get().getTransactionId());
                return RpcResult.fail(SingerDecorateOperateService.DELIVER_SINGER_AWARD_FAIL, "装扮操作失败");
            }
            // 调用操作装扮方法
            singerDecorateManager.operateSingerDecorateAsync(new SingerDecorateFlowSendParamDTO().setTransactionId(singerDecorateFlowGenerateDTO.get().getTransactionId()));
        }
        return RpcResult.success();
    }
}
