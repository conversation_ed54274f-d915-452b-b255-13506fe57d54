package fm.lizhi.ocean.wavecenter.service.resource.recommendcard.processor;

import java.util.List;

import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.RecommendCardRewardBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.request.RequestGetFamilyUseRecord;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.response.RewardResultBean;
import fm.lizhi.ocean.wavecenter.base.processor.BusinessEnvAwareProcessor;

/**
 * <AUTHOR>
 * @date 2025/3/21 15:31
 */
public interface RecommendCardRewardProcessor extends BusinessEnvAwareProcessor{

    int NUM_NOT_VALID = 2330001;

    int REWARD_COUNT_IS_ZERO = 2330002;

    int REWARD_FAMILY_NOT_VALID = 2330003;

    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return RecommendCardRewardProcessor.class;
    }

    /**
     * 检查是否可以奖励
     * @param request
     * @return
     */
    RewardResultBean checkCanReward(long operatorUserId, List<RecommendCardRewardBean> rewardBeans);

    /**
     * 补充公会推荐卡使用明细的用户参数列表
     * @param queryRoomIds
     * @param request
     */
    void familyUseRecordQueryUserId(List<Long> queryRoomIds, RequestGetFamilyUseRecord request, List<Long> njIds);

}

