package fm.lizhi.ocean.wavecenter.service.message.impl;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.message.constant.WcNoticeConfigEnum;
import fm.lizhi.ocean.wavecenter.api.message.request.*;
import fm.lizhi.ocean.wavecenter.api.message.response.ResponseGetMessageList;
import fm.lizhi.ocean.wavecenter.api.message.response.ResponseQueryRecentMessages;
import fm.lizhi.ocean.wavecenter.api.message.service.WaveCenterMessageService;
import fm.lizhi.ocean.wavecenter.service.message.convert.WcNoticeConfigConvert;
import fm.lizhi.ocean.wavecenter.service.message.dto.WcNoticeConfigQueryParamDTO;
import fm.lizhi.ocean.wavecenter.service.message.dto.WcNoticeConfigResultDTO;
import fm.lizhi.ocean.wavecenter.service.message.manager.WaveCenterMessageManager;
import fm.lizhi.ocean.wavecenter.service.message.manager.WcNoticeConfigManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.FirstLoginRecordDTO;
import fm.lizhi.ocean.wavecenter.service.user.manager.FirstLoginRecordManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
@ServiceProvider
@Slf4j
public class WaveCenterMessageServiceImpl implements WaveCenterMessageService {

    @Autowired
    private WaveCenterMessageManager waveCenterMessageManager;

    @Autowired
    private WcNoticeConfigManager wcNoticeConfigManager;

    @Autowired
    private FirstLoginRecordManager firstLoginRecordManager;


    @Override
    public Result<Long> sendMessage(RequestSendMessage param) {
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, waveCenterMessageManager.sendMessage(param));
    }

    @Override
    public Result<List<Long>> sendMessageBatch(RequestSendMessageBatch param) {
        return waveCenterMessageManager.sendMessageBatch(param);
    }

    @Override
    public Result<List<Long>> sendMessage2Role(RequestSendMessageToRole param) {
        return waveCenterMessageManager.sendMessage2Role(param);
    }

    @Override
    public Result<ResponseGetMessageList> getMessageList(RequestGetMessageList param) {
        //查询出首次登录的时间
        FirstLoginRecordDTO loginRecord = firstLoginRecordManager.queryFirstLoginRecord(param.getAppId(), param.getUserId());
        ResponseGetMessageList messageList;
        if (param.getType() != null && param.getType() == WcNoticeConfigEnum.SIGN_APPLY.getCode()) {
            messageList = waveCenterMessageManager.getMessageList(param, loginRecord == null ? null : loginRecord.getCreateTime().getTime());
            //如果是首次登录，首次登录时间之前的消息都设为未读
            messageList.getMessageList().forEach(messageBean -> {
                //判断是否是不需要读的消息
                if (loginRecord != null) {
                    boolean isNotReadMsg = messageBean.getCreateTime() < loginRecord.getCreateTime().getTime();
                    if (!messageBean.getRead() && isNotReadMsg) {
                        messageBean.setRead(true);
                    }
                }
                //调整类型
                messageBean.setType(WcNoticeConfigEnum.SIGN_APPLY.getCode());
            });
        } else {
            //其他的走公告查询接口
            WcNoticeConfigQueryParamDTO paramDTO = WcNoticeConfigConvert.INSTANCE.queryParamReqToDto(param);
            WcNoticeConfigResultDTO noticeConfig = wcNoticeConfigManager.queryWcNoticeConfig(paramDTO);
            //如果是首次登录，首次登录时间之前的消息都设为未读
            if (loginRecord != null && noticeConfig.getList() != null) {
                noticeConfig.getList().forEach(config -> {
                    //判断是否是不需要读的消息
                    boolean isNotReadMsg = config.getEffectTime().getTime() < loginRecord.getCreateTime().getTime();
                    if (!config.getRead() && isNotReadMsg) {
                        config.setRead(true);
                    }
                });
            }
            messageList = WcNoticeConfigConvert.INSTANCE.configListDtoToRes(noticeConfig);
        }
        //转换成返回结果
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, messageList);
    }

    @Override
    public Result<Void> batchRead(RequestBatchReadMessage param) {
        waveCenterMessageManager.batchRead(param);
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    @Override
    public Result<ResponseQueryRecentMessages> queryRecentMessages(Integer appId, Long userId, Integer size, String roleCode) {
        LogContext.addReqLog("getMixMessageList.req,appId:{},userId:{},size:{},roleCode:{}", appId, userId, size, roleCode);
        LogContext.addResLog("getMixMessageList.res,appId:{},userId:{},size:{},roleCode:{}", appId, userId, size, roleCode);
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, waveCenterMessageManager.queryRecentMessages(appId, userId, size, roleCode));
    }

}
