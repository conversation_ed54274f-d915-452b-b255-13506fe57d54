package fm.lizhi.ocean.wavecenter.service.datacenter.impl;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.*;
import fm.lizhi.ocean.wavecenter.api.datacenter.service.RankDataService;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.RankDataManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/22 20:36
 */
@ServiceProvider
public class RankDataServiceImpl implements RankDataService {

    @Autowired
    private RankDataManager rankDataManager;
    @Autowired
    private UserManager userManager;


    @Override
    public Result<List<RankRoomBean>> room(RankGetRoomParamBean paramBean) {
        LogContext.addReqLog("paramBean{}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));
        return ResultHandler.handle(paramBean.getAppId(), () -> {
            List<RankRoomBean> roomBeans = rankDataManager.room(paramBean);
            if (CollectionUtils.isEmpty(roomBeans)) {
                return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, Collections.emptyList());
            }
            List<Long> userIds = roomBeans.stream().map(e -> e.getRoomInfo().getId()).collect(Collectors.toList());
            Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(userIds);
            OrderType rankType = paramBean.getRankType();

            List<RankRoomBean> roomBeanList = roomBeans.stream()
                    .filter(rank -> rank.getIncome().compareTo(BigDecimal.ZERO) != 0)
                    .peek(e -> {
                        SimpleUserDto simpleUserDto;
                        UserBean roomInfo = e.getRoomInfo();
                        if ((simpleUserDto = userMap.get(roomInfo.getId())) != null) {
                            roomInfo.setName(simpleUserDto.getName());
                            roomInfo.setPhoto(simpleUserDto.getAvatar());
                            roomInfo.setBand(simpleUserDto.getBand());
                        }
                    })
                    .sorted((e1, e2) -> OrderType.DESC.equals(rankType)
                            ? e2.getIncome().compareTo(e1.getIncome())
                            : e1.getIncome().compareTo(e2.getIncome()))
                    .collect(Collectors.toList());
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, roomBeanList);
        });
    }

    @Override
    public Result<RoomPlayerRankResBean> roomPlayer(RankGetRoomPlayerParamBean paramBean) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));

        return ResultHandler.handle(paramBean.getAppId(), () -> {
            Date date = formatDate(paramBean.getDate());
            List<RankBean> pageList = rankDataManager.roomPlayer(paramBean.getRoomId(), paramBean.getFamilyId(), date, paramBean.getRankType());

            List<Long> userIds = new ArrayList<>();
            for (RankBean bean : pageList) {
                userIds.add(bean.getPlayer().getId());
            }

            Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(userIds);
            for (RankBean rankBean : pageList) {

                UserBean player = rankBean.getPlayer();
                SimpleUserDto playerUser = userMap.get(player.getId());
                if (playerUser != null) {
                    player.setPhoto(playerUser.getAvatar());
                    player.setName(playerUser.getName());
                    player.setBand(playerUser.getBand());
                }
            }
            return RpcResult.success(new RoomPlayerRankResBean().setRanks(pageList).setDate(date));
        });
    }

    @Override
    public Result<GuildPlayerRankResBean> guildPlayer(RankGetGuildPlayerParamBean paramBean) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));
        return ResultHandler.handle(paramBean.getAppId(), () -> {
            Date date = formatDate(paramBean.getDate());
            List<RankBean> pageList = rankDataManager.guildPlayer(paramBean.getFamilyId()
                    , paramBean.getRoomIds()
                    , date
                    , paramBean.getRankType());

            List<Long> userIds = new ArrayList<>();
            for (RankBean bean : pageList) {
                userIds.add(bean.getPlayer().getId());
                userIds.add(bean.getRoom().getId());
            }

            Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(userIds);
            for (RankBean rankBean : pageList) {
                UserBean room = rankBean.getRoom();
                SimpleUserDto roomUser = userMap.get(room.getId());
                if (roomUser != null) {
                    room.setPhoto(roomUser.getAvatar());
                    room.setName(roomUser.getName());
                    room.setBand(roomUser.getBand());
                }

                UserBean player = rankBean.getPlayer();
                SimpleUserDto playerUser = userMap.get(player.getId());
                if (playerUser != null) {
                    player.setPhoto(playerUser.getAvatar());
                    player.setName(playerUser.getName());
                    player.setBand(playerUser.getBand());
                }
            }

            return RpcResult.success(new GuildPlayerRankResBean().setRanks(pageList).setDate(date));
        });
    }

    @Override
    public Result<PageBean<PlayerRankBean>> signPlayer(GetSignPlayerParamBean paramBean) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));

        return ResultHandler.handle(paramBean.getAppId(), () -> {

            //家族长则查询全部
            //厅主只能查询厅
            PageBean<PlayerRankBean> pageBean = rankDataManager.playerRankPageList(paramBean);

            //填充用户信息
            List<Long> userIds = new ArrayList<>();
            List<PlayerRankBean> list = pageBean.getList();
            for (PlayerRankBean bean : list) {
                userIds.add(bean.getRoomInfo().getId());
                userIds.add(bean.getPlayerInfo().getId());
            }

            Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(userIds);
            for (PlayerRankBean playerRankBean : list) {
                UserBean roomInfo = playerRankBean.getRoomInfo();
                SimpleUserDto roomUser = userMap.get(roomInfo.getId());
                if (roomUser != null) {
                    roomInfo.setPhoto(roomUser.getAvatar());
                    roomInfo.setName(roomUser.getName());
                    roomInfo.setBand(roomUser.getBand());
                }
                UserBean playerInfo = playerRankBean.getPlayerInfo();
                SimpleUserDto playerUser = userMap.get(playerInfo.getId());
                if (playerUser != null) {
                    playerInfo.setPhoto(playerUser.getAvatar());
                    playerInfo.setName(playerUser.getName());
                    playerInfo.setBand(playerUser.getBand());
                }
            }

            return RpcResult.success(pageBean);
        });
    }

    @Override
    public Result<PageBean<RoomRankBean>> signRoom(GetSignRoomParamBean paramBean) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));

        return ResultHandler.handle(paramBean.getAppId(), () -> {

            //家族长则查询全部
            //厅主只能查询厅
            PageBean<RoomRankBean> pageBean = rankDataManager.roomRankPageList(paramBean);

            //填充用户信息
            List<Long> userIds = new ArrayList<>();
            List<RoomRankBean> list = pageBean.getList();
            for (RoomRankBean bean : list) {
                userIds.add(bean.getRoomInfo().getId());
            }

            Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(userIds);
            for (RoomRankBean playerRankBean : list) {
                UserBean roomInfo = playerRankBean.getRoomInfo();
                SimpleUserDto roomUser = userMap.get(roomInfo.getId());
                if (roomUser != null) {
                    roomInfo.setPhoto(roomUser.getAvatar());
                    roomInfo.setName(roomUser.getName());
                    roomInfo.setBand(roomUser.getBand());
                }
            }

            return RpcResult.success(pageBean);
        });
    }

    /**
     * 格式化日期
     *
     * @param dateStr 日期字符串
     * @return 日期
     */
    private Date formatDate(String dateStr) {
        if (StringUtils.isNotBlank(dateStr)) {
            return DateUtil.formatStrToDate(dateStr, DateUtil.date_2);
        }
        return new Date();
    }
}
