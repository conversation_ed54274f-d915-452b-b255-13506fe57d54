package fm.lizhi.ocean.wavecenter.service.activitycenter.handler;

import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.*;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyRuleEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.AutoConfigResourceEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityApplyService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.dto.DateDTO;
import fm.lizhi.ocean.wavecenter.common.utils.DateTimeUtils;
import fm.lizhi.ocean.wavecenter.service.activitycenter.constatns.ActivityApplyErrorTipConstant;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityApplyContext;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityOfficialSeatTimeDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityParamDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityOfficialSeatManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityRuleManager;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class OfficialCountRuleFilter implements ApplyRuleFilter {

    @Autowired
    private ActivityOfficialSeatManager activityOfficialSeatManager;

    @Autowired
    private ActivityRuleManager activityRuleManager;

    @Override
    public Result<Void> filter(ActivityApplyContext context, ActivityRuleConfigBean rule) {
        ActivityParamDTO paramBean = context.getParamDTO();
        OfficialSeatExtraBean oldOfficialSeatExtraBean = context.getOldOfficialSeatExtraBean();

        Optional<ActivityFlowResourceBean> flowResourceBeanOption = paramBean.getFlowResources().stream()
                .filter(bean -> Objects.equals(bean.getResourceCode(), AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode()))
                .findAny();
        if (!flowResourceBeanOption.isPresent()) {
            return RpcResult.success();
        }

        ActivityFlowResourceBean flowResourceBean = flowResourceBeanOption.get();
        Integer finalOfficialSeatCount = getOfficialConfigSeatCount(rule);
        paramBean.setMaxSeatCount(finalOfficialSeatCount);

        List<ActivityOfficialSeatTimeDTO> officialSeatList = activityOfficialSeatManager.getOfficialSeatList(paramBean.getAppId(),
                new Date(flowResourceBean.getExtra().getStartTime()),
                new Date(flowResourceBean.getExtra().getEndTime()),
                flowResourceBean.getExtra().getSeat());
        //官频位使用列表为空，说明该时间段范围内都没有被使用
        if (CollectionUtils.isEmpty(officialSeatList)) {
            return RpcResult.success();
        }

        List<DateDTO> oldResourceDates = oldOfficialSeatExtraBean == null
                ? Lists.newArrayList()
                : DateTimeUtils.divideTimeSlots(oldOfficialSeatExtraBean.getStartTime(), oldOfficialSeatExtraBean.getEndTime());

        //转换成map
        Map<Date, DateDTO> oldResourceDateMap = oldResourceDates.stream().collect(Collectors.toMap(DateDTO::getStartTime, Function.identity()));

        List<ActivityOfficialSeatTimeDTO> collectRes = officialSeatList.stream()
                .filter(bean -> !oldResourceDateMap.containsKey(bean.getStartTime()))
                .filter(bean -> bean.getCount() >= finalOfficialSeatCount).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(collectRes)) {
            StringBuilder builder = new StringBuilder();
            for (ActivityOfficialSeatTimeDTO bean : collectRes) {
                if (builder.length() > 0) {
                    builder.append(",");
                }
                String startTimeStr = DateUtil.formatDateToString(bean.getStartTime(), "HH:mm");
                String endTimeStr = DateUtil.formatDateToString(bean.getStartTime(), "HH:mm");
                builder.append(startTimeStr).append("-").append(endTimeStr);
            }
            String msg = ActivityApplyErrorTipConstant.APPLY_ACTIVITY_OFFICIAL_SEAT_COUNT_INVALID;
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, String.format(msg, builder));
        }
        return RpcResult.success();
    }

    private Integer getOfficialConfigSeatCount(ActivityRuleConfigBean rule) {
        //默认不限制
        Integer officialSeatCount = Integer.MAX_VALUE;
        if (!Objects.isNull(rule)) {
            OfficialCountRuleBean ruleBean = activityRuleManager.getRuleBean(ActivityApplyRuleEnum.OFFICIAL_COUNT, rule.getRuleJson());
            if (ruleBean != null) {
                officialSeatCount = ruleBean.getCount();
            }
        }
        return officialSeatCount;
    }

    @Override
    public ActivityApplyRuleEnum getRuleTypeEnum() {
        return ActivityApplyRuleEnum.OFFICIAL_COUNT;
    }
}
