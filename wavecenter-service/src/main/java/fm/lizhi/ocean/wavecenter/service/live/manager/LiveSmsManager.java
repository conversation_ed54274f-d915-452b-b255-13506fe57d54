package fm.lizhi.ocean.wavecenter.service.live.manager;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.LiveSmsPlayerParamBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.LiveSmsRoomParamBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.PlayerSmsStatBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.RoomSmsStatBean;

/**
 * <AUTHOR>
 * @date 2024/4/20 17:18
 */
public interface LiveSmsManager {

    /**
     * 厅列表
     * @param family
     * @param paramBean
     * @return
     */
    PageBean<RoomSmsStatBean> roomList(long family, Long roomId, LiveSmsRoomParamBean paramBean);

    /**
     * 主播列表
     * @param family
     * @param roomId
     * @param paramBean
     * @return
     */
    PageBean<PlayerSmsStatBean> playerList(long family, long roomId, Long playerId, LiveSmsPlayerParamBean paramBean);

    /**
     * 主播列表-陪玩维度
     * @param playerId
     * @param paramBean
     * @return
     */
    PageBean<PlayerSmsStatBean> playerListForPlayer(long playerId, LiveSmsPlayerParamBean paramBean);

}
