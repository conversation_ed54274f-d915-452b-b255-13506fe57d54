package fm.lizhi.ocean.wavecenter.service.anchor.singer.manager;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerSingHallApplyRecordBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerSingHallApplyRecordSummaryBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestImportHallApply;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestOperateHallApply;
import fm.lizhi.ocean.wavecenter.common.dto.PageDto;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.PageHallApplyParamDTO;

/**
 * 点唱厅管理
 *
 * <AUTHOR>
 */
public interface SingerHallApplyManager {

    /**
     * 点唱厅列表
     */
    PageDto<SingerSingHallApplyRecordSummaryBean> pageHallApplyList(PageHallApplyParamDTO param, Integer pageNo,
            Integer pageSize);

    /**
     * 点唱厅导入
     */
    boolean importHallApply(RequestImportHallApply request);

    /**
     * 根据厅主ID和应用ID获取点唱厅申请记录
     */
    List<SingerSingHallApplyRecordBean> getSingerHallApplyRecordByNjIdsAndAppId(List<Long> njIds, int appId);

    /**
     * 点唱厅审核通过
     */
    Optional<SingerSingHallApplyRecordBean> passHallApply(RequestOperateHallApply request);

    /**
     * 点唱厅审核不通过, 同时会标记为已删除
     */
    Optional<SingerSingHallApplyRecordBean> rejectHallApply(RequestOperateHallApply request);

    /**
     * 查询当前用户所在厅是否是点唱厅
     *
     * @param appId  应用ID
     * @param userId 用户ID
     * @return 结果, true表示在点唱厅, false表示不在点唱厅
     */
    boolean isInSingingHall(Integer appId, Long userId);

    /**
     * 批量查询点唱厅审核状态
     * @param appId 应用ID
     * @param njIds 点唱厅主ID列表
     * @return key: 厅主ID, value: 点唱厅状态
     */
    Map<Long, Integer> batchGetSingerHallStatusMap(int appId, List<Long> njIds);
}
