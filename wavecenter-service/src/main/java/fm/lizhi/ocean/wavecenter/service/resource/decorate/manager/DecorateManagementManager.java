package fm.lizhi.ocean.wavecenter.service.resource.decorate.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestCreateAvatarWidget;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestCreateRoomBackground;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.response.ResponseCreateAvatarWidget;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.response.ResponseCreateRoomBackground;

/**
 * 装扮管理后台管理器, 提供装扮相关的管理功能
 */
public interface DecorateManagementManager {

    /**
     * 创建直播间背景
     *
     * @param request 请求参数
     * @return 响应结果, 成功则包含创建的直播间背景id和预览图片
     */
    Result<ResponseCreateRoomBackground> createRoomBackground(RequestCreateRoomBackground request);

    /**
     * 创建头像框
     *
     * @param request 请求参数
     * @return 响应结果, 成功则包含创建的头像框id和预览图片
     */
    Result<ResponseCreateAvatarWidget> createAvatarWidget(RequestCreateAvatarWidget request);
}
