package fm.lizhi.ocean.wavecenter.service.datacenter.manager;

import fm.lizhi.ocean.wavecenter.api.datacenter.bean.CountDataBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.GuildAssessmentInfoBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.GuildRoomPerformanceResBean;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.DataFamilyDayDTO;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.GetFamilyDayListParam;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.GuildIncomeStatParamDTO;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.GuildIncomeStatDTO;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/4/17 17:05
 */
public interface GuildDataManager {

    /**
     * 查询公会考核业绩
     * @param appId
     * @param familyId
     * @return
     */
    Optional<GuildAssessmentInfoBean> getAssessmentInfo(int appId, long familyId);

    /**
     * 公会数据-公会考核业绩
     * @param familyId
     * @param roomIds
     * @return
     */
    Optional<GuildAssessmentInfoBean> getAssessmentInfo(long familyId, List<Long> roomIds);

    /**
     * 签约厅业绩
     *
     * @param familyId
     * @return
     */
    GuildRoomPerformanceResBean roomPerformance(long familyId, List<Long> roomIds);

    /**
     * 查询指定天数的指标数据
     * @param familyId
     * @param metric
     * @param days
     * @return
     */
    List<CountDataBean> getIndicatorTrend(long familyId, String metric, int days);

    /**
     * 根据厅范围查询指标数据
     * @param familyId
     * @param roomIds
     * @param metric
     * @param days
     * @return
     */
    List<CountDataBean> getIndicatorTrend(long familyId, List<Long> roomIds, String metric, int days);

    /**
     * 查询厅日指标数据
     * @param appId
     * @param familyId
     * @param date
     * @return key=MetricsMeta#name, value=指标值字符串
     */
    Map<String, String> getDayKeyIndicators(int appId, long familyId, Date date, List<String> valueMetrics);

    /**
     * 查询厅周指标数据
     * @param appId
     * @param familyId
     * @param startDate
     * @param endDate
     * @return key=MetricsMeta#name, value=指标值字符串
     */
    Map<String, String> getWeekKeyIndicators(int appId, long familyId, Date startDate, Date endDate, List<String> valueMetrics);

    /**
     * 查询厅月指标数据
     * @param appId
     * @param familyId
     * @param monthDate
     * @return key=MetricsMeta#name, value=指标值字符串
     */
    Map<String, String> getMonthKeyIndicators(int appId, long familyId, Date monthDate, List<String> valueMetrics);


    List<DataFamilyDayDTO> getFamilyDayList(long familyId, int appId, Date startDate, Date endDate);

    /**
     * 查询公会日统计数据
     * @return
     */
    List<DataFamilyDayDTO> getFamilyDayList(GetFamilyDayListParam param);

    /**
     * 查询公会多日收入累加
     * @param familyId
     * @param startDate
     * @param endDate
     * @return
     */
    Integer getFamilyDayIncomeSum(Long familyId, Date startDate, Date endDate);

    /**
     * 查询公会某周期收入概览统计
     * @param param 查询参数
     * @return 分页结果
     */
    PageBean<GuildIncomeStatDTO> queryGuildIncomeStats(GuildIncomeStatParamDTO param);
}
