package fm.lizhi.ocean.wavecenter.service.sign.manager;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.bean.FamilyAndNjContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.RoomSignRecordBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.TodoSignBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.IdentifyStatusEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.UserSignStatusEnum;
import fm.lizhi.ocean.wavecenter.api.sign.request.*;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseAdminApplyCancelFamily;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseFamilyInviteAdmin;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseUserApplyAdmin;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto.ContractInfoDto;
import fm.lizhi.ocean.wavecenter.service.sign.dto.*;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 合同签约接口
 * <AUTHOR>
 * @date 2024/10/9 14:33
 */
public interface ContractManager {

    /**
     * 查询最近一条解约记录
     * @param userId
     * @return
     */
    Optional<FamilyAndNjContractBean> queryLastCancel(Long userId);

    /**
     * 查询最近一条签约记录
     * @param userId
     * @return
     */
    Optional<FamilyAndNjContractBean> queryLastSign(Long userId);

    /**
     * 用户是否签约为管理员
     * @param userId
     * @return
     */
    boolean isUserSignAsRoom(Long userId);

    /**
     * 查询合同
     * @return
     */
    PageBean<FamilyAndNjContractBean> queryContract(RequestFamilyAndNjContractDTO request);

    /**
     * 查询管理员相关待签约列表
     * @param request
     * @return
     */
    List<TodoSignBean> todoRoomList(RequestFamilyTodoRoomList request);

    /**
     * 查询签约解约列表
     * @param request
     * @return
     */
    PageBean<RoomSignRecordBean> queryRoomSignList(RequestQueryRoomSignList request);

    /**
     * 统计已签约厅数
     * @param familyId
     * @return
     */
    Integer countSignRoomNum(long familyId);

    /**
     * 是否已完成签约实名认证
     * @param userId
     * @return
     */
    boolean isPassSignRealNameVerify(long userId);

    /**
     * 获取签约实名认证状态
     * @param userId
     * @return
     */
    IdentifyStatusEnum getSignRealNameStatus(long userId);

    /**
     * 获取签约系统认证信息
     * @param userId
     * @return
     */
    Optional<SignPersonalInfoDTO> getSignPersonalInfo(long userId);

    /**
     * 获取签约token
     * @param userId
     * @return
     */
    Optional<String> getSignToken(long userId);

    /**
     * 用户申请成为管理员
     * @param request
     * @return
     */
    ResponseUserApplyAdmin userApplyAdmin(RequestUserApplyAdmin request);

    /**
     * 家族邀请管理员
     * @param request
     * @return
     */
    ResponseFamilyInviteAdmin familyInviteAdmin(RequestFamilyInviteAdmin request);

    /**
     * 查询合同的签约状态
     * @param signId
     * @return
     */
    Map<Long, UserSignStatusEnum> getContractUserSignStatus(String signId);

    /**
     * 查询身份证加入的family
     * @param identityNo
     * @return
     */
    List<FamilyAndNjContractBean> queryIdentityNoJoinFamily(String identityNo);

    /**
     * 生成合同签署链接地址
     * @param reqUid 请求用户ID
     * @param signId 合同签约ID
     * @return
     */
    Optional<String> genContractSignUrl(Long reqUid, String signId);

    /**
     * 生成查看合同页面地址
     * @param signId
     * @return
     */
    Optional<String> genContactViewUrl(String signId);

    /**
     * 查询管理员加入家族记录
     * @param request
     * @return
     */
    List<FamilyNjJoinRecordDTO> queryFamilyNjJoinRecord(QueryFamilyNjJoinRecordDTO request);

    /**
     * 生成签署合同ID
     * @param contractId
     * @return
     */
    Optional<String> doSignGenSignId(Long contractId);

    /**
     * 家族申请解约管理员
     * @param request
     * @return
     */
    Optional<String> familyApplyCancelAdmin(RequestFamilyApplyCancelAdmin request);

    /**
     * 管理员申请解约家族，并生成电子合同
     * @param request
     * @return
     */
    Optional<String> adminApplyCancelFamilyForSignId(RequestAdminApplyCancelFamily request);

    /**
     * 管理员申请解约家族
     * @param request
     * @return
     */
    ResponseAdminApplyCancelFamily adminApplyCancelFamily(RequestAdminApplyCancelFamily request);

    /**
     * 是否存在主体变更记录
     * @param njId
     * @return
     */
    boolean existEffectChangeObjByNjId(Long njId);

    /**
     * 家族长厅主邀请确认
     * @return
     */
    Pair<Integer, String> familyAdminInviteConfirm(Long curUserId, Long targetUserId, RoleEnum operateRole);

    /**
     * 解约邀请确认
     * @param familySignId
     * @param operateRole
     * @param curUserId
     * @return
     */
    Pair<Integer, String> familyAdminInviteCancelConfirm(Long familySignId, RoleEnum operateRole, Long curUserId);

    /**
     * 查询签署记录
     * @param param
     * @return
     */
    PageBean<FamilyNjSignRecordDTO> querySignRecord(QueryFamilyNjSignRecordDTO param);

    /**
     * 家族长和厅管理确认签约
     * @param familyNjSignId
     * @param curUserId
     * @param type
     * @param operateRole
     * @return
     */
    Pair<Integer, String> doFamilyNjConfirmSign(Long familyNjSignId, Long curUserId, ContractTypeEnum type, RoleEnum operateRole);

    /**
     * 查询解约记录
     * @param contractIds 解约合同id
     * @return
     */
    List<FamilyNjCancelRecordDTO> queryCancelRecordByContractId(List<Long> contractIds);

    /**
     * 查询签约信息
     * @param familyId
     * @param startDate
     * @param endDate
     * @return
     */
    List<ContractInfoDto> queryContractInfoByTime(long familyId, Date startDate, Date endDate);

    /**
     * 查询厅主当前签约的家族ID
     * @param njId
     * @return
     */
    Optional<Long> getNjSignFamilyId(Long njId);
}
