package fm.lizhi.ocean.wavecenter.service.permissions.constants;

import lombok.Getter;

/**
 * 组件权限
 * <AUTHOR>
 * @date 2024/3/27 16:06
 */
public enum PermissionTypeEnum {

    /** 未知 */
    UNKNOWN(99),
    /** 可写 */
    WRITE(1),
    /** 可读 */
    READ(2);

    @Getter
    private int value;

    PermissionTypeEnum(int value) {
        this.value = value;
    }

    public static PermissionTypeEnum from(int value) {
        for (PermissionTypeEnum permissionTypeEnum : PermissionTypeEnum.values()) {
            if (permissionTypeEnum.value == value) {
                return permissionTypeEnum;
            }
        }
        return UNKNOWN;
    }
}
