package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 根据主播ID查询房间信息
 */
@Data
@Accessors(chain = true)
public class GetRoomInfoByNjIdDTO {

    /**
     * 直播间ID
     */
    private Long id;

    /**
     * 电台ID
     */
    private Long radioId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 直播间名称
     */
    private String name;

    /**
     * 拉流URL
     */
    private Integer status;

}
