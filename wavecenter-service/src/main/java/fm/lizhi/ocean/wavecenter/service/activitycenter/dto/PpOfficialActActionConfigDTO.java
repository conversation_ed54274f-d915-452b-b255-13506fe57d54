package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 官方活动卡片跳转配置
 *
 * <AUTHOR>
 * @date 2024/6/24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PpOfficialActActionConfigDTO {
    /**
     * 跳转action
     */
    private String action;
    /**
     * 模板，0表示无模板
     */
    private int template;
    /**
     * 直播间标题颜色
     */
    private String liveTitleColor;
    /**
     * 封面图片
     */
    private String coverImg;
}
