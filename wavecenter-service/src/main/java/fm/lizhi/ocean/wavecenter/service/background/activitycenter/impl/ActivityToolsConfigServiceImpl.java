package fm.lizhi.ocean.wavecenter.service.background.activitycenter.impl;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityToolsInfoBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityToolStatusEnum;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityTools;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityTools;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityToolsConfigService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityToolsConfigManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
/**
 * 活动工具配置服务实现类
 * <AUTHOR>
 */
@ServiceProvider
@Slf4j
public class ActivityToolsConfigServiceImpl implements ActivityToolsConfigService {

    @Autowired
    private ActivityToolsConfigManager activityToolsConfigManager;


    @Override
    public Result<Boolean> saveTools(RequestSaveActivityTools param) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(param));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(param));

        return activityToolsConfigManager.saveTools(param);
    }

    @Override
    public Result<Boolean> updateTools(RequestUpdateActivityTools param) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(param));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(param));
        if (null == param.getId() || param.getId() <= 0) {
            return RpcResult.fail(ActivityToolsConfigService.UPDATE_TOOLS_FAIL, "参数异常");
        }

        return activityToolsConfigManager.updateTools(param);
    }


    @Override
    public Result<List<ActivityToolsInfoBean>> listByAppId(Integer appId) {
        LogContext.addReqLog("appId={}", JsonUtil.dumps(appId));
        LogContext.addResLog("appId={}", JsonUtil.dumps(appId));

        return activityToolsConfigManager.listByAppId(appId, null);
    }
}
