package fm.lizhi.ocean.wavecenter.service.activitycenter.processor;

import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.OfficialSeatExtraBean;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.FlowResourceContext;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.SaveOfficialSeatParamDTO;
import fm.lizhi.ocean.wavecenter.base.processor.BusinessEnvAwareProcessor;

public interface IOfficialSeatGiveProcess extends BusinessEnvAwareProcessor {

    void fillOfficialSeatParam(FlowResourceContext context, SaveOfficialSeatParamDTO param, OfficialSeatExtraBean extraBean);


    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return IOfficialSeatGiveProcess.class;
    }

}
