package fm.lizhi.ocean.wavecenter.service.anchor.singer.manager;

import fm.lizhi.ocean.wavecenter.common.utils.RedisLock;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerCountInHallDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerMenuConfigDTO;

import java.util.List;

public interface SingerRedisManager {

    /**
     * 判断用户是否是歌手
     *
     * @param appId  应用ID
     * @param userId 用户ID
     * @return 如果用户是歌手返回true，不是歌手返回false；如果缓存中无记录返回null
     */
    Boolean isSinger(Integer appId, Long userId);

    /**
     * 设置用户是否是歌手
     *
     * @param appId    应用ID
     * @param userId   用户ID
     * @param isSinger 是否是歌手
     */
    void setIsSinger(Integer appId, Long userId, boolean isSinger);

    /**
     * 删除用户是否是歌手的缓存
     *
     * @param appId  应用ID
     * @param userId 用户ID
     */
    void deleteIsSinger(Integer appId, Long userId);

    /**
     * 获取厅内歌手总数
     *
     * @param njId 厅主ID
     * @return 返回生效中和审核中的歌手数量
     */
    SingerCountInHallDTO getSingerTotalCountInHall(Integer appId, Long njId);

    /**
     * 设置厅内歌手总数
     *
     * @param appId 应用ID
     * @param njId  厅主ID
     * @param count 歌手数量
     */
    void setSingerTotalCountInHall(Integer appId, Long njId, SingerCountInHallDTO count);

    /**
     * 删除厅内歌手总数缓存
     *
     * @param njId 厅ID
     */
    void deleteSingerTotalCountInHall(Long njId);

    /**
     * 添加淘汰标记
     *
     * @param appId  应用ID
     * @param userId 用户ID
     */
    void addEliminateSingerTag(int appId, Long userId);

    /**
     * 删除淘汰标记
     *
     * @param appId   应用ID
     * @param userIds 用户ID列表
     */
    void removeEliminateSingerTag(int appId, List<Long> userIds);

    /**
     * 获取指定时间范围内的淘汰标记
     *
     * @param appId     应用ID
     * @param startTime 开始时间戳
     * @param endTime   结束时间戳
     * @return 淘汰标记对应的用户ID列表
     */
    List<Long> getEliminateSingerTag(int appId, long startTime, long endTime);

    /**
     * 尝试获取歌手信息修改锁
     *
     * @param appId  应用ID
     * @param userId 用户ID
     * @return 成功返回true，失败返回false
     */
    RedisLock tryGetSingerUpdateLock(int appId, long userId);

    /**
     * 尝试获取歌手认证申请锁
     *
     * @param appId        应用ID
     * @param idCardNumber 证件号
     * @return 成功返回true，失败返回false
     */
    RedisLock tryGetSingerVerifyApplyLock(int appId, String idCardNumber);

    /**
     * 保存申请菜单配置
     *
     * @param request 请求参数
     */
    void saveApplyMenuConfig(SingerMenuConfigDTO request);

    /**
     * 获取申请菜单配置
     *
     * @param appId      应用ID
     * @param singerType 歌手类型
     * @return 申请菜单配置
     */
    SingerMenuConfigDTO getApplyMenuConfig(int appId, Integer singerType);
}

