package fm.lizhi.ocean.wavecenter.service.user.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/4/12 15:59
 */
@Data
public class UserInfoDto extends SimpleUserDto {
    /**
     * 音频id
     */
    private Long radioId;
    /**
     * 性别
     */
    private Integer gender;
    /**
     * 头像
     */
    private String portrait;
    /**
     * 手机号
     */
    private String phoneNum;
    /**
     * 生日
     */
    private Long birthday;
    /**
     * 国家
     */
    private String country;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 个性签名
     */
    private String signature;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 修改时间
     */
    private Long modifyTime;
    /**
     * 用户appId
     */
    private Long appId;

    private String deviceId;

    private String voice;

}
