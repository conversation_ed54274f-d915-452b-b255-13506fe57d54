package fm.lizhi.ocean.wavecenter.service.resource.decorate.impl;

import org.springframework.beans.factory.annotation.Autowired;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.DecorateInfoBean;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestGetDecorateInfo;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestGetDecorates;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.service.DecorateService;
import fm.lizhi.ocean.wavecenter.service.resource.decorate.manager.DecorateManager;

@ServiceProvider
public class DecorateServiceImpl implements DecorateService {

    @Autowired
    private DecorateManager decorateManager;

    @Override
    public Result<DecorateInfoBean> getDecorateInfo(RequestGetDecorateInfo request) {
        PlatformDecorateTypeEnum decorateType = PlatformDecorateTypeEnum.getByType(request.getDecorateType());
        if (decorateType == null) {
            return new Result<>(DecorateService.DECORATE_TYPE_NOT_EXIST, null);
        }
        DecorateInfoBean decorateInfo = decorateManager.getDecorateInfo(decorateType, request.getDecorateId());
        if (decorateInfo == null) {
            return new Result<>(DecorateService.DECORATE_NOT_EXIST, null);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, decorateInfo);
    }

    @Override
    public Result<PageBean<DecorateInfoBean>> getDecorates(RequestGetDecorates request) {
        PlatformDecorateTypeEnum decorateType = PlatformDecorateTypeEnum.getByType(request.getDecorateType());
        if (decorateType == null) {
            return new Result<>(DecorateService.DECORATE_TYPE_NOT_EXIST, null);
        }
        PageBean<DecorateInfoBean> pageBean = decorateManager.getDecorates(decorateType, 0L, request.getDecorateName(), request.getPageNo(), request.getPageSize());
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, pageBean);
    }
}
