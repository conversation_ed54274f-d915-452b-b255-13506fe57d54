package fm.lizhi.ocean.wavecenter.service.common.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
public class ActivityApplyDataAuditParamDTO {

    private Integer appId;

    /**
     * 申请人id
     */
    private Long applicantUid;

    /**
     * 送审数据
     */
    private List<CheckDataInfo> checkDataList;

    /**
     * 父审核状态
     */
    private int parentInnerType;

    /**
     * 送审数据信息
     */
    @Data
    @Accessors(chain = true)
    public static class CheckDataInfo {
        /**
         * 审核内容，图片则是完整的url
         */
        private String content;

        /**
         * 审核数据类型，审核提供
         */
        private Integer innerType;

        /**
         * 审核数据id
         */
        private Long contentId;
    }

}
