package fm.lizhi.ocean.wavecenter.service.home.dto;


import fm.lizhi.ocean.wavecenter.api.datacenter.bean.EchelonBean;
import fm.lizhi.ocean.wavecenter.api.home.bean.MetricsDataBean;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 厅关键数据汇总
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RoomKeyDataSummaryDTO {


    /**
     * 厅总收入
     */
    private MetricsDataBean sumIncome;

    /**
     * 考核流水
     */
    private EchelonBean examinationFlow;

    /**
     * 距离下一梯队
     */
    private EchelonBean nextEchelon;

    /**
     * 厅上麦主播数
     */
    private MetricsDataBean signUpGuestPlayerCnt;

    /**
     * 厅收入主播数
     */
    private MetricsDataBean incomePlayerCnt;
}
