package fm.lizhi.ocean.wavecenter.service.user.convert;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.api.user.request.RequestAddFirstLoginRecord;
import fm.lizhi.ocean.wavecenter.api.user.response.ResponseFirstLoginRecord;
import fm.lizhi.ocean.wavecenter.service.user.dto.FirstLoginRecordDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.FirstLoginRecordParamDTO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR,
        imports = {
                ConfigUtils.class
        }
)
public interface FirstLoginRecordConvert {
    FirstLoginRecordConvert INSTANCE = Mappers.getMapper(FirstLoginRecordConvert.class);

    FirstLoginRecordParamDTO reqToDto(RequestAddFirstLoginRecord dto);
} 