package fm.lizhi.ocean.wavecenter.service.user.manager;


import fm.lizhi.ocean.wavecenter.service.user.dto.LoginContextDto;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserInfoDto;

/**
 * 风控
 * <AUTHOR>
 * @date 2024/4/12 16:44
 */
public interface RiskManager {

    /**
     * 登录防刷
     * @param userInfoDto
     * @param loginContextDto
     * @return true：有风险，false：无风险
     */
    boolean loginAntiRush(UserInfoDto userInfoDto, LoginContextDto loginContextDto);

}
