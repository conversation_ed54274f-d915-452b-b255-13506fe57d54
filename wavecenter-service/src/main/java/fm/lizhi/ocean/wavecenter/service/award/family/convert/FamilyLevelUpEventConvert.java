package fm.lizhi.ocean.wavecenter.service.award.family.convert;

import fm.lizhi.ocean.wavecenter.base.convert.CommonConvert;
import fm.lizhi.ocean.wavecenter.domain.grow.event.GrowFamilyLevelPeriodUpdateEvent;
import fm.lizhi.ocean.wavecenter.service.award.family.dto.FamilyAwardLevelDataDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR, uses = {CommonConvert.class})
public interface FamilyLevelUpEventConvert {

    FamilyLevelUpEventConvert I = Mappers.getMapper(FamilyLevelUpEventConvert.class);

    @Mapping(target = "appId", source = "family.appId")
    @Mapping(target = "familyId", source = "family.id")
    @Mapping(target = "levelId", source = "level.id")
    @Mapping(target = "startTime", source = "period.start")
    @Mapping(target = "endTime", source = "period.end")
    FamilyAwardLevelDataDTO eventToData(GrowFamilyLevelPeriodUpdateEvent event);
}
