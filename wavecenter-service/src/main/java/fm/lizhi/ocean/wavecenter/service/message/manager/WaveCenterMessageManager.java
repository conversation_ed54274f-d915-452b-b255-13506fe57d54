package fm.lizhi.ocean.wavecenter.service.message.manager;


import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.message.request.*;
import fm.lizhi.ocean.wavecenter.api.message.response.ResponseGetMessageList;
import fm.lizhi.ocean.wavecenter.api.message.response.ResponseQueryRecentMessages;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface WaveCenterMessageManager {


    /**
     * 向指定用户发送消息
     *
     * @return messageId
     */
    Long sendMessage(RequestSendMessage param);

    /**
     * 批量发送消息
     */
    Result<List<Long>> sendMessageBatch(RequestSendMessageBatch param);


    /**
     * 向指定角色发送消息
     */
    Result<List<Long>> sendMessage2Role(RequestSendMessageToRole param);

    /**
     * 获取消息列表
     */
    ResponseGetMessageList getMessageList(RequestGetMessageList param, Long showNoticeTimeStamp);

    /**
     * 批量已读
     *
     * @param param 参数
     */
    void batchRead(RequestBatchReadMessage param);

    /**
     * 查询最新的前N条消息
     *
     * @param appId  应用ID
     * @param userId 用户ID
     * @param size   条数
     * @return 消息公告混合列表
     */
    ResponseQueryRecentMessages queryRecentMessages(Integer appId, Long userId, Integer size, String roleCode);
}
