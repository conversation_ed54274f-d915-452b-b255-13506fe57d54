package fm.lizhi.ocean.wavecenter.service.datacenter.dto;

import fm.lizhi.ocean.wavecenter.api.datacenter.constants.GuildDataMetricsConstant;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 公会数据关键指标查询参数
 * <AUTHOR>
 */
@Data
public class GuildDataKeyIndicatorsParamDto {
    /**
     * 公会id
     */
    private Long familyId;
    /**
     * 应用id
     */
    private Integer appId;
    /**
     * 查询时间类型：day=日，week=周，month=月
     */
    private String dateType;
    /**
     * 开始时间，日和月可以只传开始时间
     */
    private Date startDate;
    /**
     * 结束时间
     */
    private Date endDate;
    /**
     * 只查询数值的指标 {@link GuildDataMetricsConstant}
     */
    private List<String> valueMetrics;
    /**
     * 同时查询环比和数值的指标 {@link GuildDataMetricsConstant}
     */
    private List<String> ratioMetrics;
}
