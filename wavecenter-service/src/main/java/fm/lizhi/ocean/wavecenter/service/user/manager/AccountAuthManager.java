package fm.lizhi.ocean.wavecenter.service.user.manager;

import fm.lizhi.ocean.wavecenter.service.user.dto.AccountCodeAuthResultDto;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserInfoDto;

import java.util.Optional;

/**
 * 账号鉴权
 * <AUTHOR>
 * @date 2024/4/12 15:45
 */
public interface AccountAuthManager {

    /**
     * 鉴权码转换为账号信息
     * @param authCode
     * @param appId
     * @return
     */
    Optional<AccountCodeAuthResultDto> codeConvertAccount(String authCode, int appId);

    /**
     * 通过鉴权账号ID获取用户信息
     * @param authAccountId
     * @return
     */
    Optional<UserInfoDto> getUserByAuthAccountId(Long authAccountId);

}
