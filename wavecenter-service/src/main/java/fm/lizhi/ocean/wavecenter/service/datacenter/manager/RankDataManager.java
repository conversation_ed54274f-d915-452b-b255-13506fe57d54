package fm.lizhi.ocean.wavecenter.service.datacenter.manager;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/22 20:39
 */
public interface RankDataManager {

    List<RankBean> roomPlayer(long roomId, long familyId, Date date, OrderType rankType);

    /**
     * 公会主播龙虎榜
     * @param familyId
     * @param roomIds
     * @param date
     * @param rankType
     * @return
     */
    List<RankBean> guildPlayer(long familyId, List<Long> roomIds, Date date, OrderType rankType);

    /**
     * 主播排行榜
     * @param paramBean
     * @return
     */
    PageBean<PlayerRankBean> playerRankPageList(GetSignPlayerParamBean paramBean);

    /**
     * 厅排行榜
     * @param paramBean
     * @return
     */
    PageBean<RoomRankBean> roomRankPageList(GetSignRoomParamBean paramBean);

    /**
     * 厅龙虎榜
     * @param paramBean
     * @return
     */
    List<RankRoomBean> room(RankGetRoomParamBean paramBean);
}
