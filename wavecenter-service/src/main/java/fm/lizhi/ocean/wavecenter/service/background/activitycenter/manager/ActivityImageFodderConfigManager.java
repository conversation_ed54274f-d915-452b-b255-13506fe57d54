package fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityImageFodderBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestPageActivityImageFodder;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityImageFodder;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityImageFodder;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseSaveActivityImageFodder;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;

/**
 * 图片素材
 * <AUTHOR>
 */
public interface ActivityImageFodderConfigManager {

    /**
     * 保存图片素材
     */
    Result<ResponseSaveActivityImageFodder> saveImageFodder(RequestSaveActivityImageFodder param);


    /**
     * 修改图片素材
     */
    Result<Void> updateImageFodder(RequestUpdateActivityImageFodder param);


    /**
     * 删除图片素材
     */
    Result<Void> deleteImageFodder(Long id, String operator);


    /**
     * 分页查询图片素材
     */
    Result<PageBean<ActivityImageFodderBean>> pageImageFodder(RequestPageActivityImageFodder param);

}
