package fm.lizhi.ocean.wavecenter.service.datacenter.manager;

import fm.lizhi.ocean.wavecenter.api.datacenter.bean.CountDataBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.PlayerAssessmentInfoBean;
import fm.lizhi.ocean.wavecenter.service.common.dto.PaySettlePeriodDto;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/4/21 20:20
 */
public interface PlayerDataManager {

    /**
     * 查询指定天数的指标数据
     * @param playerId
     * @param metric
     * @param days
     * @return
     */
    List<CountDataBean> getIndicatorTrend(long playerId, Long familyId, Long roomId, String metric, int days);

    /**
     * 查询日指标数据
     * @param appId
     * @param playerId
     * @param date
     * @return key=MetricsMeta#name, value=指标值字符串
     */
    Map<String, String> getDayKeyIndicators(int appId, Long familyId, Long roomId, long playerId, Date date, List<String> valueMetrics);

    /**
     * 查询周指标数据
     * @param appId
     * @param playerId
     * @param startDate
     * @param endDate
     * @return key=MetricsMeta#name, value=指标值字符串
     */
    Map<String, String> getWeekKeyIndicators(int appId, Long familyId, Long roomId, long playerId, Date startDate, Date endDate, List<String> valueMetrics);

    /**
     * 查询月指标数据
     * @param appId
     * @param playerId
     * @param monthDate
     * @return key=MetricsMeta#name, value=指标值字符串
     */
    Map<String, String> getMonthKeyIndicators(int appId, Long familyId, Long roomId, long playerId, Date monthDate, List<String> valueMetrics);

    /**
     * 查询厅收礼魅力值
     * @param playerId
     * @return
     */
    Integer getRoomIncomeCharm(List<Long> roomIds, long playerId, Date startDate, Date endDate);

    /**
     * 查询个播收入魅力值，本账号作为房主的流水的魅力值
     * @param playerId
     * @return
     */
    Integer getPersonIncomeCharm(long playerId, Date startDate, Date endDate);


    /**
     * 获取业绩考核
     * @param param
     * @return
     */
    PlayerAssessmentInfoBean getAssessmentInfo(PlayerGetAssessmentDto param);

    /**
     * 查询结算周期
     * @param appId
     * @param familyId
     * @return
     */
    PaySettlePeriodDto getPaySettlePeriod(int appId, long familyId);

    /**
     * 获取上一期结算周期
     * @param appId
     * @param familyId
     * @return
     */
    PaySettlePeriodDto getPaySettlePeriodPre(int appId, long familyId);

    /**
     * 厅主播日指标统计
     * @param family 公会
     * @param roomIds 需要进行统计的厅
     * @param valueMetrics 指标
     * @param day 日
     * @return
     */
    Map<String, String> countPlayerDay(Long family, List<Long> roomIds, List<String> valueMetrics, Date day);

    /**
     * 厅主播日指标统计
     * @param familyId
     * @param roomIds
     * @param valueMetrics
     * @param dayValues
     * @return
     */
    Map<Integer, Map<String, Integer>> countPlayerDays(Long familyId, List<Long> roomIds, List<String> valueMetrics, List<Integer> dayValues);

    /**
     * 厅主播周指标统计
     * @param familyId 公会
     * @param roomIds 需要进行统计的厅
     * @param startDate 周开始日
     * @param endDate 周结束日
     * @param valueMetrics 指标
     * @return
     */
    Map<String, String> countPlayerWeek(Long familyId, List<Long> roomIds, Date startDate, Date endDate, List<String> valueMetrics);

    /**
     * 厅主播月指标统计
     * @param familyId
     * @param roomIds
     * @param monthDate
     * @param valueMetrics
     * @return
     */
    Map<String, String> countPlayerMonth(Long familyId, List<Long> roomIds, Date monthDate, List<String> valueMetrics);

    /**
     * 查询主播日统计数据
     * @param param
     * @return
     */
    List<DataPlayerDayDTO> getPlayerDayList(GetPlayerRoomDayParam param);

    /**
     * 查询主播厅维度日统计数据
     * @param param
     * @return
     */
    List<DataPlayerRoomDayDTO> getPlayerRoomDayList(GetPlayerRoomDayParam param);

    /**
     * 查询周有收入的主播名单
     * @return
     */
    List<Long> getPlayerIdsWeekHasIncomeByMinPlayerId(Date weekStartDay, Date weekEndDay, Long minPlayerId, Integer pageSize);

    /**
     * 查询主播一周内成长体系相关指标数据
     * @param playerId
     * @param weekStartDay
     * @param weekEndDay
     * @return
     */
    PlayerDataForGrowDTO getPlayerGrowDataInWeek(Long playerId, Date weekStartDay, Date weekEndDay);
}
