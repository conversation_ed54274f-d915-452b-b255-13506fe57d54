package fm.lizhi.ocean.wavecenter.service.sign.dto;

import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.Singular;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/15 15:32
 */
@Getter
@Builder
public class RequestFamilyAndNjContractDTO {

    /**
     * 是否根据创建时间倒序
     */
    @Builder.Default
    private boolean descCreateTime = true;

    /**
     * 未到期合同
     */
    private boolean noExpire;

    @Singular
    private List<ContractTypeEnum> types;

    @Singular
    private List<SignRelationEnum> relations;

    private Long familyId;

    private Long otherFamilyId;

    private Long njId;

    @Builder.Default
    private Integer pageNo = 1;

    @Builder.Default
    private Integer pageSize = 50;

    private Long contractId;

    @Singular
    private List<Long> contractLists;

    private String signId;


}
