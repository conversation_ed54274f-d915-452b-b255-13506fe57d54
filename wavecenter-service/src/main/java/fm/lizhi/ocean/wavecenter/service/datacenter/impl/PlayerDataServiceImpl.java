package fm.lizhi.ocean.wavecenter.service.datacenter.impl;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.*;
import fm.lizhi.ocean.wavecenter.api.datacenter.service.PlayerDataService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.datacenter.config.DataCenterConfig;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.AssessTimeDto;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.PlayerGetAssessmentDto;
import fm.lizhi.ocean.wavecenter.service.datacenter.handler.PlayerDataHandler;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.AssessmentManager;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.PlayerDataManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.PlayerSignInfoDto;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.RoomManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/4/21 20:20
 */
@Slf4j
@ServiceProvider
public class PlayerDataServiceImpl implements PlayerDataService {

    @Autowired
    private DataCenterConfig dataCenterConfig;
    @Autowired
    private PlayerDataManager playerDataManager;
    @Autowired
    private PlayerDataHandler playerDataHandler;
    @Autowired
    private FamilyManager familyManager;
    @Autowired
    private RoomManager roomManager;
    @Autowired
    private AssessmentManager assessmentManager;

    @Override
    public Result<IndicatorTrendResBean> getIndicatorTrend(int appId, Long familyId, Long roomId, long playerId, String metric) {
        LogContext.addReqLog("appId={},familyId={},roomId={},playerId={},metric={}", appId, familyId, roomId, playerId, metric);
        LogContext.addResLog("appId={},familyId={},roomId={},playerId={},metric={}", appId, familyId, roomId, playerId, metric);
        return ResultHandler.handle(appId, ()->{
            int days = dataCenterConfig.getIndicatorTrendDays();
            List<CountDataBean> indicatorTrend = playerDataManager.getIndicatorTrend(playerId, familyId, roomId, metric, days);
            return RpcResult.success(new IndicatorTrendResBean()
                    .setCountDays(days)
                    .setCountData(indicatorTrend));
        });
    }

    @Override
    public Result<List<IndicatorBean>> getKeyIndicators(PlayerGetKeyIndicatorsParamBean paramBean) {
        LogContext.addReqLog("paramBean{}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean{}", JsonUtil.dumps(paramBean));
        return ResultHandler.handle(paramBean.getAppId(), ()->{
            return RpcResult.success(playerDataHandler.getKeyIndicators(paramBean));
        });
    }

    @Override
    public Result<PlayerAssessmentInfoBean> getAssessmentInfo(int appId, long playerId, long familyId, Long roomId) {
        LogContext.addReqLog("appId={},playerId={},familyId={},roomId={}", appId, playerId, familyId, roomId);
        LogContext.addResLog("appId={},playerId={},familyId={},roomId={}", appId, playerId, familyId, roomId);
        return ResultHandler.handle(appId, ()->{

            List<Long> roomIds = new ArrayList<>();
            if (roomId == null) {
                //如果为空，则查询所有厅下的数据
                roomIds = roomManager.getFamilyAllNjId(familyId);
            } else {
                roomIds.add(roomId);
            }

            // 支付结算周期
            AssessTimeDto currentTime = assessmentManager.getCurrentTime(appId, familyId);
            if (currentTime == null){
                log.info("Current time is null");
                return RpcResult.fail(ASSESSMENT_INFO_NOT_FOUND);
            }
            Date startDate = currentTime.getStartDate();
            Date endDate = currentTime.getEndDate();

            //上一期
            AssessTimeDto preTime = assessmentManager.getPreTime(appId, familyId);

            //根据最近有效签约，决定查询时间跨度
            Optional<PlayerSignInfoDto> latestSignRecord = familyManager.getLatestSignRecord(roomIds, playerId);
            if (latestSignRecord.isPresent()) {
                Date signEndTime = latestSignRecord.get().getEndTime();
                log.info("signEndTime={}", signEndTime);
                if (signEndTime != null) {
                    if (signEndTime.getTime() < startDate.getTime()) {
                        //已经解约
                        PlayerAssessmentInfoBean assessmentInfoBean = new PlayerAssessmentInfoBean();
                        assessmentInfoBean.setStartDate(startDate.getTime());
                        assessmentInfoBean.setEndDate(endDate.getTime());
                        return RpcResult.success(assessmentInfoBean);
                    }

                    //最近签约到期时间<考核周期结束时间 && 最近签约到期时间>考核周期开始时间  即考核周期的前半段时间是签约的
                    if (signEndTime.getTime() < endDate.getTime() && signEndTime.getTime() > startDate.getTime()) {
                        //只查询签约时间内的数据
                        currentTime = AssessTimeDto.builder().startDate(currentTime.getStartDate()).endDate(signEndTime).build();
                    }
                }
            }

            // 查询考核信息
            PlayerAssessmentInfoBean assessmentInfo = playerDataManager.getAssessmentInfo(new PlayerGetAssessmentDto()
                    .setFamilyId(familyId)
                    .setSignRoomId(roomId)
                    .setFamilySignRoomIds(roomIds)
                    .setPlayerId(playerId)
                    .setCurrentTime(currentTime)
                    .setPreTime(preTime)
            );
            assessmentInfo.setStartDate(startDate.getTime());
            assessmentInfo.setEndDate(endDate.getTime());
            return RpcResult.success(assessmentInfo);
        });
    }
}
