package fm.lizhi.ocean.wavecenter.service.income.process.pp;


import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.service.income.process.PersonalProcess;
import org.springframework.stereotype.Component;


@Component
public class PpPersonalProcess implements PersonalProcess {


    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public boolean enablePlayerIncome() {
        return true;
    }
}
