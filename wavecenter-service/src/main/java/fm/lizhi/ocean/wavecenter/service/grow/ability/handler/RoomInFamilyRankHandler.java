package fm.lizhi.ocean.wavecenter.service.grow.ability.handler;

import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.service.grow.ability.constants.AbilityConstant;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.RoomAbilityWeekCapabilityDTO;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.RoomSettleFlowDTO;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.SettlePeriodDTO;
import fm.lizhi.ocean.wavecenter.service.grow.ability.manager.GrowRoomAbilityManager;
import fm.lizhi.ocean.wavecenter.service.grow.ability.manager.GrowSettleFlowManager;
import fm.lizhi.ocean.wavecenter.service.grow.config.GrowConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/11 14:21
 */
@Slf4j
@Component
public class RoomInFamilyRankHandler {

    @Autowired
    private GrowSettleFlowManager growSettleFlowManager;
    @Autowired
    private GrowRoomAbilityManager growRoomAbilityManager;
    @Autowired
    private GrowConfig growConfig;

    /**
     * 结算
     *
     * @param settlePeriod
     */
    public void settle(SettlePeriodDTO settlePeriod) {
        // 确认厅周期结算流程是否已经完成
        RoomSettleFlowDTO roomSettleFlow = growSettleFlowManager.getRoomSettleFlow(settlePeriod);
        if (roomSettleFlow == null || roomSettleFlow.getStatus().equals(AbilityConstant.SETTLE_FLOW_STATUS_DOING)) {
            log.info("settleByPeriod roomSettleFlow is doing");
            return;
        }

        settle(settlePeriod, null);
    }

    private void doSettleRoom(SettlePeriodDTO settlePeriod, RoomAbilityWeekCapabilityDTO capabilityDTO) {
        try {
            // 查询公会中分数高的主播
            Integer count = growRoomAbilityManager.countInFamilyCapabilityHighRoomNum(capabilityDTO.getFamilyId(), capabilityDTO.getCapabilityCode(), capabilityDTO.getAbilityValue(), settlePeriod);

            // +1表示排名
            capabilityDTO.setRoomInFamilyRank(count == null ? 1 : count + 1);

            // 查询上周的排名
            RoomAbilityWeekCapabilityDTO prePeriod = growRoomAbilityManager.getRoomAbilityWeekCapability(buildPrePeriod(settlePeriod), capabilityDTO.getRoomId(), capabilityDTO.getCapabilityCode());

            // 对比排名变化
            if (prePeriod != null && prePeriod.getRoomInFamilyRank() != null) {
                capabilityDTO.setRoomInFamilyCompareWeekRank(prePeriod.getRoomInFamilyRank() - capabilityDTO.getRoomInFamilyRank());
            }

            growRoomAbilityManager.updateRoomAbilityWeekCapability(capabilityDTO);
        } catch (Exception e) {
            log.error("RoomInFamilyRankHandlerFamilyId={} do settle error:", capabilityDTO.getFamilyId(), e);
        }
    }

    /**
     * 上一个周期
     * @param settlePeriodDTO
     * @return
     */
    private SettlePeriodDTO buildPrePeriod(SettlePeriodDTO settlePeriodDTO){
        return new SettlePeriodDTO(DateUtil.getDayBefore(settlePeriodDTO.getStartDate(), 7)
                , DateUtil.getDayBefore(settlePeriodDTO.getEndDate(), 7)
        );
    }

    /**
     * 指定公会结算
     *
     * @param settlePeriod
     * @param familyId
     */
    public void settle(SettlePeriodDTO settlePeriod, Long familyId) {
        // 分页查询厅更新排名
        List<RoomAbilityWeekCapabilityDTO> roomCapabilityList = growRoomAbilityManager.getRoomAbilityWeekCapability(familyId, settlePeriod, 0L, growConfig.getAbilitySettlePageSize());
        while (CollectionUtils.isNotEmpty(roomCapabilityList)) {
            for (RoomAbilityWeekCapabilityDTO capabilityDTO : roomCapabilityList) {
                doSettleRoom(settlePeriod, capabilityDTO);
            }
            roomCapabilityList = growRoomAbilityManager.getRoomAbilityWeekCapability(familyId, settlePeriod, roomCapabilityList.get(roomCapabilityList.size() - 1).getId(), growConfig.getAbilitySettlePageSize());
        }
    }

}
