package fm.lizhi.ocean.wavecenter.service.datacenter.handler;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wavecenter.common.utils.WcAssert;
import fm.lizhi.ocean.wavecenter.service.datacenter.constants.AccountOpEnum;
import fm.lizhi.ocean.wavecenter.service.datacenter.constants.AccountTypeEnum;
import fm.lizhi.ocean.wavecenter.service.datacenter.convert.AccountFlowConvert;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.*;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.AccountFlowManager;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.AccountFlowRuleManager;
import fm.lizhi.ocean.wavecenter.service.income.config.IncomeConfig;
import fm.lizhi.ocean.wavecenter.service.income.config.PayAccountConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 支付账户流水采集器
 * <AUTHOR>
 * @date 2025/4/22 19:32
 */
@Slf4j
@Component
public class PayAccountFlowCollector {

    @Autowired
    private IncomeConfig incomeConfig;
    @Autowired
    private AccountFlowRuleManager accountFlowRuleManager;
    @Autowired
    private AccountFlowManager accountFlowManager;

    /**
     * 采集支付账户流水
     * @param dto
     */
    public void collectPayAccountFlow(PayAccountFlowDTO dto) {

        // 只关注加操作
        if (!AccountOpEnum.PLUS.getValue().equals(dto.getAccountOpType())) {
            log.info("accountOp not support");
            return;
        }

        // 确认是厅流水还是主播流水
        PayAccountConfig payAccountConfig = incomeConfig.getBizConfig(dto.getAppId()).getPayAccountConfig();
        Map<String, String> accountTypeMap = payAccountConfig.getAccountTypeMap();
        String type = accountTypeMap.get(dto.getAccountEngineCode());
        log.info("type={}", type);
        if (StringUtils.isBlank(type)) {
            return;
        }

        if (type.equals(AccountTypeEnum.ROOM.getValue())) {
            // 厅流水变更
            collectRoomFlow(AccountFlowConvert.I.flowToRoomFlow(dto));
        }

        if (type.equals(AccountTypeEnum.PLAYER.getValue())) {
            // 主播流水变更
            collectPlayerFlow(AccountFlowConvert.I.flowToPlayerFlow(dto));
        }
    }

    /**
     * 采集厅流水
     * @param dto
     */
    public void collectRoomFlow(RoomAccountFlowDTO dto){
        // 根据规则确认指标名列表
        List<AccountFlowRuleDTO> rules = getRules(dto.getTenantCode(), dto.getAccountEngineCode(), dto.getBizId());
        if (CollectionUtils.isEmpty(rules)) {
            log.info("rules is empty");
            return;
        }

        List<String> ruleCodes = rules.stream()
                .map(AccountFlowRuleDTO::getCode)
                .collect(Collectors.toList());

        AccountOpEnum accountOpEnum = AccountOpEnum.getByValue(dto.getAccountOpType());
        if (accountOpEnum == null) {
            log.info("accountOpType is null. accountOpType={}", dto.getAccountOpType());
            return;
        }

        // 创建指标对象
        RoomFlowChangeDTO roomFlowChangeDTO = new RoomFlowChangeDTO();
        roomFlowChangeDTO.setAppId(dto.getAppId());
        roomFlowChangeDTO.setAccountOpType(accountOpEnum);
        roomFlowChangeDTO.setAmount(dto.getAmount());
        roomFlowChangeDTO.setRoomId(dto.getRoomId());
        roomFlowChangeDTO.setRuleCodes(ruleCodes);
        roomFlowChangeDTO.setTradeDate(dto.getTradeDate());

        // 保存指标变化
        accountFlowManager.saveRoomFlowChange(roomFlowChangeDTO);
    }

    /**
     * 采集主播流水
     * @param dto
     */
    public void collectPlayerFlow(PlayerAccountFlowDTO dto){
        // 根据规则确认指标名列表
        List<AccountFlowRuleDTO> rules = getRules(dto.getTenantCode(), dto.getAccountEngineCode(), dto.getBizId());
        if (CollectionUtils.isEmpty(rules)) {
            log.info("rules is empty");
            return;
        }

        List<String> ruleCodes = rules.stream()
                .map(AccountFlowRuleDTO::getCode)
                .collect(Collectors.toList());

        // 创建指标对象
        PlayerFlowChangeDTO playerFlowChangeDTO = new PlayerFlowChangeDTO();
        playerFlowChangeDTO.setAppId(dto.getAppId());
        playerFlowChangeDTO.setAccountOpType(AccountOpEnum.getByValue(dto.getAccountOpType()));
        playerFlowChangeDTO.setAmount(dto.getAmount());
        playerFlowChangeDTO.setPlayerId(dto.getPlayerId());
        playerFlowChangeDTO.setRuleCodes(ruleCodes);
        playerFlowChangeDTO.setTradeDate(dto.getTradeDate());

        // 保存指标变化
        accountFlowManager.savePlayerFlowChange(playerFlowChangeDTO);
    }

    private List<AccountFlowRuleDTO> getRules(String tenantCode, String accountEngineCode, Integer bizId){
        log.info("getRules tenantCode={},accountEngineCode={},bizId={}", tenantCode, accountEngineCode, bizId);
        // 先查询没有明确bizId的规则
        List<AccountFlowRuleDTO> ignoreList = accountFlowRuleManager.getAccountEngineCodeRuleIgnoreBizId(tenantCode, accountEngineCode);
        if (log.isDebugEnabled()) {
            log.debug("ignoreList={}", JsonUtil.dumps(ignoreList));
        }
        if (CollectionUtils.isNotEmpty(ignoreList)) {
            return ignoreList;
        }

        // 如果没有在查询具体指定bizId的规则
        List<AccountFlowRuleDTO> ruleList = accountFlowRuleManager.getAccountEngineCodeRule(tenantCode, accountEngineCode, bizId);
        return ruleList;
    }

}
