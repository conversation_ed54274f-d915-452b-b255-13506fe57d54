package fm.lizhi.ocean.wavecenter.service.anchor.singer.dto;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.UserSingerGloryBean;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class UserSingerGloryConfig {

    /**
     * 认证歌手
     */
    private UserSingerGloryDTO newSingerGlory;

    /**
     * 优质歌手
     */
    private UserSingerGloryDTO qualitySingerGlory;

    /**
     * 原创优质歌手
     */
    private UserSingerGloryDTO originalQualitySingerGlory;

}
