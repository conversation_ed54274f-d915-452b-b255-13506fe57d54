package fm.lizhi.ocean.wavecenter.service.live.impl;

import java.util.List;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.live.request.RequestGetRecommendCardStatics;
import fm.lizhi.ocean.wavecenter.api.live.response.ResponseRecommendCardStaticsInfo;
import fm.lizhi.ocean.wavecenter.api.live.service.RecommendCardStaticsService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.service.live.manager.RecommendCardStaticsManager;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 推荐卡统计服务实现
 */
@ServiceProvider
public class ReCommendCardStaticsServiceImpl implements RecommendCardStaticsService {

    @Autowired
    private RecommendCardStaticsManager recommendCardStaticsManager;

    /**
     * 获取推荐卡曝光率
     * 根据传入的推荐卡记录ID列表，返回对应的曝光率信息
     */
    @Override
    public Result<List<ResponseRecommendCardStaticsInfo>> getRecommendCardStatics(RequestGetRecommendCardStatics request) {
        List<ResponseRecommendCardStaticsInfo> resultList = recommendCardStaticsManager.getRecommendCardStatics(request.getAppId(), request.getRecommendCardRecordIds());
        return RpcResult.success(resultList);
    }
}
