package fm.lizhi.ocean.wavecenter.service.award.singer.manager;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateFlowOperateEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateOperateStatusEnum;
import fm.lizhi.ocean.wavecenter.common.dto.PageDto;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowDTO;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowGenerateDTO;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowPageParamDTO;

import java.util.List;

/**
 * 歌手装扮流水管理
 *
 * <AUTHOR>
 */
public interface SingerDecorateFlowManager {

    /**
     * 分页查询歌手装扮流水
     */
    PageDto<SingerDecorateFlowDTO> pageSingerDecorateFlow(SingerDecorateFlowPageParamDTO param, int pageNo, int pageSize);


    List<SingerDecorateFlowDTO> getDecorateFlowByUserIdAndSingerType(Long userId, int singerType, SingerDecorateFlowOperateEnum flowOperateEnum);

    /**
     * 获取可回收的歌手装扮流水
     */
    List<SingerDecorateFlowDTO> getCanRecoverDecorateFlowByUserId(int appId, Long userId, Integer singerType);


    /**
     * 根据transactionId获取歌手装扮流水
     *
     * @return transactionId 发送批次流水
     */
    List<SingerDecorateFlowDTO> getDecorateFlowByTransactionIdAndLteRetryCount(Long transactionId, List<SingerDecorateOperateStatusEnum> statusList);


    /**
     * 更新歌手装扮流水
     */
    boolean updateDecorateFlowStatus(SingerDecorateFlowDTO flow, SingerDecorateOperateStatusEnum status);

    /**
     * 根据用户ID和规则ID列表查询歌手装扮流水
     */
    List<SingerDecorateFlowDTO> getDecorateFlowByUserIdAndRuleIds(Long userId, int appId, List<Long> ruleIds, SingerDecorateFlowOperateEnum operate, boolean recycled);

    /**
     * 新增批量插入流水接口
     */
    boolean batchInsert(SingerDecorateFlowGenerateDTO decorateFlowDTO);

    /**
     * 根据事务ID查询歌手装扮流水
     */
    List<SingerDecorateFlowDTO> getDecorateFlowByTransactionId(int appId, Long transactionId);
}
