package fm.lizhi.ocean.wavecenter.service.anchor.singer.service;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestSendSingerChat;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.service.SingerChatService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerChatManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerInfoManager;
import fm.lizhi.ocean.wavecenter.service.award.singer.manager.SingerDecorateFlowManager;
import fm.lizhi.ocean.wavecenter.service.award.singer.manager.SingerDecorateManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 */
@Slf4j
@ServiceProvider
public class SingerChatServiceImpl implements SingerChatService {

    @Autowired
    private SingerChatManager singerChatManager;

    @Autowired
    private SingerDecorateManager singerDecorateManager;

    @Autowired
    private SingerDecorateFlowManager singerDecorateFlowManager;

    @Autowired
    private SingerInfoManager singerInfoManager;

    @Override
    public Result<Void> sendSingerChat(RequestSendSingerChat request) {
        try {
            LogContext.addReqLog("request={}", request);
            LogContext.addResLog("request={}", request);
            singerChatManager.sendAuditResultChat(
                    request.getAppId(),
                    request.getSingerId(),
                    request.getSingerType(),
                    request.getSongStyle(),
                    request.getScene()
            );
            return RpcResult.success();
        } catch (Exception e) {
            log.error("发送歌手私信异常，request={}", request, e);
            return RpcResult.fail(SingerChatService.SEND_SINGER_CHAT_FAIL, "发送私信失败");
        }
    }

}