package fm.lizhi.ocean.wavecenter.service.anchor.singer.service;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerCountInHallBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.UserSingerGloryBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestBatchSingerTotalCountInHall;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestGetUserSingerGlory;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseBatchSingerCountInHall;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseGetUserSingerGlory;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.config.CommonSingerConfig;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.config.SingerAnchorConfig;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.convert.SingerInfoConvert;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerInfoDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.UserSingerGloryConfig;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.UserSingerGloryDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestSingerTotalCountInHall;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestUserIsSinger;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseSingerCountInHall;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.service.SingerInfoService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerCountInHallDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerInfoManager;

import java.util.*;

@ServiceProvider
@Slf4j
public class SingerInfoServiceImpl implements SingerInfoService {

    @Autowired
    private SingerInfoManager singerInfoManager;

    @Autowired
    private SingerAnchorConfig singerAnchorConfig;

    @Override
    public Result<ResponseGetUserSingerGlory> getUserSingerGlory(RequestGetUserSingerGlory request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));

        // 1. 验证用户是否为歌手
        if (!singerInfoManager.isSinger(request.getAppId(), request.getUserId())) {
            return RpcResult.success(null);
        }

        // 2. 获取荣誉配置
        UserSingerGloryConfig gloryConfig = getGloryConfig(request.getAppId());
        if (gloryConfig == null) {
            return RpcResult.success(null);
        }

        // 3. 获取歌手信息列表
        List<SingerInfoDTO> singerInfoList = singerInfoManager.getSingerInfoByUserId(
                request.getAppId(),
                request.getUserId(),
                SingerStatusEnum.EFFECTIVE
        );

        // 4. 按优先级处理歌手类型（高级 -> 初级）
        ResponseGetUserSingerGlory response = processQualitySinger(singerInfoList, gloryConfig)
                .orElseGet(() -> processNewSinger(singerInfoList, gloryConfig).orElse(null));

        return RpcResult.success(response);
    }




    @Override
    public Result<Boolean> userIsSinger(RequestUserIsSinger request) {
        boolean isSinger = singerInfoManager.isSinger(request.getAppId(), request.getUserId());
        return RpcResult.success(isSinger);
    }

    @Override
    public Result<ResponseSingerCountInHall> singerTotalCountInHall(RequestSingerTotalCountInHall request) {
        SingerCountInHallDTO count = singerInfoManager.singerTotalCountInHall(request.getAppId(), request.getNjId());
        ResponseSingerCountInHall response = new ResponseSingerCountInHall()
                .setEffectiveCount(count.getEffectiveCount())
                .setAuthenticatingCount(count.getAuditCount())
                .setIdentityCount(count.getAuthenticationCount());
        return RpcResult.success(response);
    }

    @Override
    public Result<ResponseBatchSingerCountInHall> batchSingerTotalCountInHall(RequestBatchSingerTotalCountInHall request) {
        Map<Long, SingerCountInHallBean> stringStringHashMap = new HashMap<>();
        for (Long njId : request.getNjId()) {
            SingerCountInHallDTO count = singerInfoManager.singerTotalCountInHall(request.getAppId(), njId);
            stringStringHashMap.put(njId, new SingerCountInHallBean()
                    .setEffectiveCount(count.getEffectiveCount())
                    .setAuthenticatingCount(count.getAuditCount()));
        }
        return RpcResult.success(new ResponseBatchSingerCountInHall()
                .setSingerCountInHallMap(stringStringHashMap));
    }


    /**
     * 获取荣誉配置
     */
    private UserSingerGloryConfig getGloryConfig(Integer appId) {
        CommonSingerConfig config = singerAnchorConfig.getBizConfig(appId);
        if (config == null || config.getUserSingerGloryConfig() == null) {
            log.warn("getUserSingerGlory config is null. appId:{}", appId);
            return null;
        }
        return config.getUserSingerGloryConfig();
    }


    /**
     * 处理高级歌手
     */
    private Optional<ResponseGetUserSingerGlory> processQualitySinger(List<SingerInfoDTO> singerInfoList, UserSingerGloryConfig gloryConfig) {

        return findSingerByType(singerInfoList, SingerTypeEnum.QUALITY)
                .map(singerInfo -> {
                    List<UserSingerGloryBean> badges = buildQualitySingerBadges(singerInfo, gloryConfig);
                    return buildResponse(singerInfo, badges);
                });
    }

    /**
     * 处理初级歌手
     */
    private Optional<ResponseGetUserSingerGlory> processNewSinger(
            List<SingerInfoDTO> singerInfoList,
            UserSingerGloryConfig gloryConfig) {

        return findSingerByType(singerInfoList, SingerTypeEnum.NEW)
                .map(singerInfo -> {
                    List<UserSingerGloryBean> badges = buildNewSingerBadges(gloryConfig);
                    return buildResponse(singerInfo, badges);
                });
    }

    /**
     * 根据歌手类型查找歌手信息
     */
    private Optional<SingerInfoDTO> findSingerByType(List<SingerInfoDTO> singerInfoList, SingerTypeEnum singerType) {
        return singerInfoList.stream()
                .filter(singerInfo -> singerInfo.getSingerType().equals(singerType.getType()))
                .findFirst();
    }

    /**
     * 构建高级歌手勋章列表
     */
    private List<UserSingerGloryBean> buildQualitySingerBadges(
            SingerInfoDTO singerInfo,
            UserSingerGloryConfig gloryConfig) {

        List<UserSingerGloryBean> badges = new ArrayList<>();

        // 添加高级歌手认证勋章
        Optional.ofNullable(gloryConfig.getQualitySingerGlory())
                .ifPresent(glory -> badges.add(SingerInfoConvert.INSTANCE.convertUserSingerGloryBadge(glory)));

        // 添加原创歌手认证勋章（仅当用户是原创歌手时）
        if (Boolean.TRUE.equals(singerInfo.getOriginalSinger())) {
            // 添加高级歌手认证勋章
            Optional.ofNullable(gloryConfig.getOriginalQualitySingerGlory())
                    .ifPresent(glory -> badges.add(SingerInfoConvert.INSTANCE.convertUserSingerGloryBadge(glory)));
        }

        return badges;
    }

    /**
     * 构建初级歌手勋章列表
     */
    private List<UserSingerGloryBean> buildNewSingerBadges(UserSingerGloryConfig gloryConfig) {
        List<UserSingerGloryBean> badges = new ArrayList<>();
        Optional.ofNullable(gloryConfig.getNewSingerGlory())
                .ifPresent(glory -> badges.add(SingerInfoConvert.INSTANCE.convertUserSingerGloryBadge(glory)));
        return badges;
    }


    /**
     * 构建响应对象
     */
    private ResponseGetUserSingerGlory buildResponse(SingerInfoDTO singerInfo, List<UserSingerGloryBean> badges) {
        return new ResponseGetUserSingerGlory()
                .setSingerType(singerInfo.getSingerType())
                .setOriginalSinger(singerInfo.getOriginalSinger())
                .setSingerGloryBadgeList(badges);
    }

}
