package fm.lizhi.ocean.wavecenter.service.user.service;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.user.request.RequestAddFirstLoginRecord;
import fm.lizhi.ocean.wavecenter.api.user.service.FirstLoginRecordService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.service.user.convert.FirstLoginRecordConvert;
import fm.lizhi.ocean.wavecenter.service.user.dto.FirstLoginRecordParamDTO;
import fm.lizhi.ocean.wavecenter.service.user.manager.FirstLoginRecordManager;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 首次登录记录Service实现
 *
 * <AUTHOR>
 */
@ServiceProvider
public class FirstLoginRecordServiceImpl implements FirstLoginRecordService {

    @Autowired
    private FirstLoginRecordManager manager;

    @Override
    public Result<Void> addFirstLoginRecord(RequestAddFirstLoginRecord request) {
        LogContext.addReqLog("addFirstLoginRecord.request={}", request);
        LogContext.addResLog("addFirstLoginRecord.request={}", request);
        FirstLoginRecordParamDTO dto = FirstLoginRecordConvert.INSTANCE.reqToDto(request);
        boolean success = manager.addFirstLoginRecord(dto);
        return success ? RpcResult.success() : RpcResult.fail(ADD_FIRST_LOGIN_RECORD_ERROR, "新增首次登录记录失败");
    }
} 