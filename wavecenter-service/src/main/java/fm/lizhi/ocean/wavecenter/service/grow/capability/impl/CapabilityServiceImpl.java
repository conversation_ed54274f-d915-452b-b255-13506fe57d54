package fm.lizhi.ocean.wavecenter.service.grow.capability.impl;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.grow.capability.request.RequestSaveCapability;
import fm.lizhi.ocean.wavecenter.api.grow.capability.response.ResponseCapability;
import fm.lizhi.ocean.wavecenter.api.grow.capability.service.CapabilityService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.service.grow.dto.WcGrowCapabilityDTO;
import fm.lizhi.ocean.wavecenter.service.grow.capability.manager.WcGrowCapabilityManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 能力项Service实现
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@ServiceProvider
public class CapabilityServiceImpl implements CapabilityService {
    @Autowired
    private WcGrowCapabilityManager capabilityManager;

    @Override
    public Result<List<ResponseCapability>> queryCapabilityList(Integer appId) {
        List<WcGrowCapabilityDTO> list = capabilityManager.queryAll(appId);
        List<ResponseCapability> result = list.stream().map(dto -> {
            ResponseCapability response = new ResponseCapability();
            response.setCode(dto.getCapabilityCode());
            response.setName(dto.getName());
            response.setId(dto.getId());
            response.setOperator(dto.getModifyUser());
            return response;
        }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    @Override
    public Result<Void> saveCapability(RequestSaveCapability request) {
        try {
            boolean capability;
            if (request.getId() == null) {
                capability = capabilityManager.addCapability(request.getCode(), request.getName(),
                        request.getOperator(), request.getAppId());
            } else {
                capability = capabilityManager.updateCapability(request.getId(), request.getCode(), request.getName(),
                        request.getOperator(), request.getAppId());
            }
            return capability ? RpcResult.success() : RpcResult.fail(SAVE_CAPABILITY_FAIL);
        } catch (Exception e) {
            return RpcResult.fail(SAVE_CAPABILITY_FAIL);
        }
    }
}