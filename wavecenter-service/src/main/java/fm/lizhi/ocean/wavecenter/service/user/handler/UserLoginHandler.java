package fm.lizhi.ocean.wavecenter.service.user.handler;

import com.google.common.base.Charsets;
import com.google.common.hash.HashFunction;
import com.google.common.hash.Hashing;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.user.bean.*;
import fm.lizhi.ocean.wavecenter.api.user.constants.QrCodeStatus;
import fm.lizhi.ocean.wavecenter.api.user.service.UserLoginService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.config.BizCommonConfig;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.service.common.manager.IdManager;
import fm.lizhi.ocean.wavecenter.service.permissions.constants.PermissionConstants;
import fm.lizhi.ocean.wavecenter.service.permissions.dto.RoleAuthRefDto;
import fm.lizhi.ocean.wavecenter.service.permissions.handler.RoleHandler;
import fm.lizhi.ocean.wavecenter.service.permissions.manager.RoleManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.user.config.BizUserConfig;
import fm.lizhi.ocean.wavecenter.service.user.config.UserConfig;
import fm.lizhi.ocean.wavecenter.service.user.constants.UserConstant;
import fm.lizhi.ocean.wavecenter.service.user.dto.*;
import fm.lizhi.ocean.wavecenter.service.user.manager.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/4/12 15:04
 */
@Slf4j
@Component
public class UserLoginHandler {

    @Autowired
    private CommonConfig commonConfig;
    @Autowired
    private AccountAuthManager accountAuthManager;
    @Autowired
    private UserConfig userConfig;
    @Autowired
    private UserManager userManager;
    @Autowired
    private RiskManager riskManager;
    @Autowired
    private LoginManager loginManager;
    @Autowired
    private IdManager idManager;
    @Autowired
    private RoleManager roleManager;
    @Autowired
    private FamilyManager familyManager;
    @Autowired
    private RoleHandler roleHandler;
    @Autowired
    private FirstLoginRecordManager firstLoginRecordManager;

    private final String ACCESS_TOKEN = "access_token";
    private final String REFRESH_TOKEN = "refresh_token";
    public static final long SELF_ROLE_CONFIG_ID = 1;

    /**
     * 保存用户角色
     *
     * @param roleReqBean
     * @return
     */
    public Result<Void> saveUserRole(SaveUserRoleReqBean roleReqBean) {
        Integer appId = roleReqBean.getAppId();
        Long userId = roleReqBean.getUserId();
        String deviceId = roleReqBean.getDeviceId();
        Long roleConfigId = roleReqBean.getRoleConfigId();

        //如果角色配置为空，则设置为自己
        if (roleConfigId == null) {
            roleConfigId = SELF_ROLE_CONFIG_ID;
        }

        Optional<UserTokenBean> tokenOp = loginManager.getToken(appId, userId, deviceId);
        if (!tokenOp.isPresent()) {
            return RpcResult.fail(UserLoginService.NOT_LOGIN);
        }
        UserTokenBean userTokenBean = tokenOp.get();

        //检查配置有效性
        //角色配置必须是启用的
        RoleAuthRefDto roleAuthRefDto = null;
        if (!roleConfigId.equals(SELF_ROLE_CONFIG_ID)) {
            Optional<RoleAuthRefDto> authConfigOp = roleManager.getAuthConfig(roleConfigId);
            if ((!authConfigOp.isPresent())
                    || authConfigOp.get().getStatus() != PermissionConstants.RoleRefStatus.ACT) {
                return RpcResult.fail(UserLoginService.ROLE_AUTH_NOT_EXIST);
            }
            roleAuthRefDto = authConfigOp.get();

            //校验签约关系
            boolean checkRoleConfigSign = roleHandler.checkRoleConfigSign(roleAuthRefDto);
            if (!checkRoleConfigSign) {
                LogContext.addResLog("checkRoleConfigSign={}", checkRoleConfigSign);
                return RpcResult.fail(UserLoginService.ROLE_AUTH_NOT_EXIST);
            }
        }

        //关联角色配置
        loginManager.refTokenRoleConfigId(appId, userTokenBean, roleConfigId);
        loginManager.saveAccessTokenRole(appId, userId, deviceId, roleConfigId, userTokenBean.getAccessToken(), userTokenBean.getAccessTokenExpiresIn());

        //缓存角色信息
        LoginRoleExpiresInfoDto loginRoleInfoDto = new LoginRoleExpiresInfoDto();
        loginRoleInfoDto.setAccessTokenExpiresIn(userTokenBean.getAccessTokenExpiresIn());
        loginRoleInfoDto.setRefreshTokenExpiresIn(userTokenBean.getRefreshTokenExpiresIn());
        loginRoleInfoDto.setRoleConfigId(roleConfigId);
        if (roleConfigId.equals(SELF_ROLE_CONFIG_ID)) {
            loginRoleInfoDto.setLoginType(UserConstant.LoginType.SELF);
            //如果是自己，查询自己的角色的roleCode, 保存授权roleCode=自己的roleCode
            //查询角色对应的subjectId
            UserInFamilyBean userInFamily = familyManager.getUserInFamily(userId);
            if (userInFamily.isFamily()) {
                loginRoleInfoDto.setRoleCode(RoleEnum.FAMILY.getRoleCode());
                loginRoleInfoDto.setSubjectId(userInFamily.getFamilyId());
            } else if (userInFamily.isRoom()) {
                loginRoleInfoDto.setRoleCode(RoleEnum.ROOM.getRoleCode());
                loginRoleInfoDto.setSubjectId(userInFamily.getNjId());
            } else {
                loginRoleInfoDto.setRoleCode(userInFamily.isPlayer()
                        ? RoleEnum.PLAYER.getRoleCode()
                        : RoleEnum.USER.getRoleCode());
                loginRoleInfoDto.setSubjectId(userId);
            }
        } else {
            //如果是授权角色，保存授权角色的roleCode, 以及subjectId
            loginRoleInfoDto.setRoleCode(roleAuthRefDto.getRoleCode());
            loginRoleInfoDto.setSubjectId(roleAuthRefDto.getSubjectId());
            loginRoleInfoDto.setLoginType(UserConstant.LoginType.AUTH);
        }
        loginManager.refTokenRole(loginRoleInfoDto, userTokenBean.getAccessToken(), userTokenBean.getRefreshToken());

        return RpcResult.success();
    }

    /**
     * 二维码登录
     *
     * @param paramBean
     * @return
     */
    public Result<Void> qrCodeLogin(QrCodeLoginParamBean paramBean) {
        Integer appId = paramBean.getAppId();
        String qrCodeKey = paramBean.getQrCodeKey();
        String businessToken = paramBean.getBusinessToken();
        //检查二维码是否有效
        int seconds = modifyQrCodeStatus(appId, qrCodeKey, QrCodeStatus.QRCODE_AUTHENTICATION_ING);
        if (seconds <= 0) {
            LogContext.addResLog("qrCodeKey qrCode not exist qrCodeKey={}", qrCodeKey);
            return RpcResult.fail(UserLoginService.QR_CODE_NOT_EXIST);
        }

        //获取二维码设备ID
        Optional<String> deviceOp = loginManager.getQrCodeKeyDevice(appId, qrCodeKey);
        if (!deviceOp.isPresent()) {
            LogContext.addResLog("qrCodeKey deviceId not exist qrCodeKey={}", qrCodeKey);
            return RpcResult.fail(UserLoginService.QR_CODE_NOT_EXIST);
        }
        String deviceId = deviceOp.get();

        //通过token获取用户id
        Optional<Long> userIdOp = userManager.getUserIdByBusinessToken(businessToken);
        if (!userIdOp.isPresent()) {
            LogContext.addResLog("businessToken user not exist businessToken={}", businessToken);
            return RpcResult.fail(UserLoginService.USER_NOT_EXIST);
        }
        Long userId = userIdOp.get();

        //通过id获取用户信息
        Optional<UserInfoDto> userInfoDtoOp = userManager.getUserInfoById(userId);
        if (!userInfoDtoOp.isPresent()) {
            LogContext.addResLog("userId user not exist userId={}", userId);
            return RpcResult.fail(UserLoginService.USER_NOT_EXIST);
        }
        UserInfoDto userInfoDto = userInfoDtoOp.get();

        //获取业务配置，包括风控配置
        BizCommonConfig bizCommonConfig = commonConfig.getBizConfig();
        if (bizCommonConfig == null) {
            LogContext.addResLog("bizConfig is null");
            return RpcResult.fail(UserLoginService.LOGIN_ERROR);
        }

        //登录前置检查
        LoginContextDto.LoginContextDtoBuilder loginContextBuilder = LoginContextDto.builder()
                .deviceId(deviceId)
                .appId(String.valueOf(appId))
                .deviceType(paramBean.getDeviceType())
                .clientIp(paramBean.getIp())
                .clientVersion(paramBean.getClientVersion())
                .userId(userId)
                .phoneNum(userInfoDto.getPhoneNum())
                .authAccountId(0L)
                .riskAppId(bizCommonConfig.getRiskAppId());
        fileRiskDefaultValue(loginContextBuilder);
        Result<Void> checkResult = loginCheck(appId, userInfoDto, loginContextBuilder.build());
        if (RpcResult.isFail(checkResult)) {
            return RpcResult.fail(checkResult.rCode(), checkResult.getMessage());
        }

        //登录处理
        UserTokenBean userTokenBean = genToken(appId, userInfoDto.getId(), deviceId);
        //token关联二维码, 方便获取结果通过qrCode获取accessToken和refreshToken
        loginManager.refQrCodeAndToken(appId, qrCodeKey, userTokenBean);

        //登录后置处理
        loginManager.loginPostProcessor(userInfoDto, loginContextBuilder.build());

        //更新二维码状态
        modifyQrCodeStatus(appId, qrCodeKey, QrCodeStatus.SUCCESS);
        return RpcResult.success();
    }

    /**
     * 修改二维码状态
     *
     * @param appId
     * @param qrCodeKey
     * @param status
     * @return
     */
    public int modifyQrCodeStatus(int appId, String qrCodeKey, int status) {
        //校验二维码是否存在
        int seconds = loginManager.qrCodeExpireSeconds(appId, qrCodeKey);
        if (seconds > 0) {
            //更新状态
            loginManager.saveQrCodeKey(appId, qrCodeKey, seconds, status);
        }
        return seconds;
    }

    /**
     * 创建二维码
     *
     * @param appId
     * @param oldQrCode
     * @return
     */
    public Result<CreateQrCodeResBean> createQrCode(int appId, String oldQrCode, String deviceId) {
        // 删除旧的
        if (StringUtils.isNotBlank(oldQrCode)) {
            loginManager.deleteQrCodeKey(appId, oldQrCode);
        }
        // 创建新的
        long qrCodeId = idManager.genId();
        // 转换成16进制字符串缩短
        String qrCodeKey = Long.toHexString(qrCodeId);
        // 保存二维码
        loginManager.saveQrCodeKey(appId, qrCodeKey, userConfig.getQcCodeExpiresIn(), QrCodeStatus.QRCODE_AUTHENTICATION);
        loginManager.refQrCodeKeyDevice(appId, qrCodeKey, userConfig.getQcCodeExpiresIn(), deviceId);
        return RpcResult.success(CreateQrCodeResBean.builder()
                .loginH5Url(userConfig.getBizConfig(appId).getQrCodeLoginUrl())
                .qrcodeKey(qrCodeKey)
                .build());
    }

    /**
     * 刷新token
     *
     * @param refreshToken
     * @return
     */
    public Result<UserTokenBean> refreshToken(String refreshToken) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();

        //获取旧的accessToken
        Optional<String> accessTokenOp = loginManager.getAccessTokenByRefreshToken(refreshToken);
        if ((!accessTokenOp.isPresent()) || StringUtils.isBlank(accessTokenOp.get())) {
            LogContext.addResLog("refreshTokenExpired={}", refreshToken);
            return RpcResult.fail(UserLoginService.REFRESH_TOKEN_EXPIRED);
        }
        String accessToken = accessTokenOp.get();

        //获取用户信息
        Optional<TokenUserInfoDto> userInfoDtoOp = loginManager.getUserByRefreshToken(refreshToken);
        if (!userInfoDtoOp.isPresent()) {
            LogContext.addResLog("refreshTokenUserNotFound={}", refreshToken);
            return RpcResult.fail(UserLoginService.REFRESH_TOKEN_USER_NOT_FOUND);
        }
        TokenUserInfoDto userInfoDto = userInfoDtoOp.get();

        //通过refreshToken获取关联的角色配置
        Optional<LoginRoleInfoDto> refreshTokenRoleOp = loginManager.getRefreshTokenRole(refreshToken);

        //删除旧的Token
        loginManager.deleteToken(accessToken, refreshToken);

        //生成新的token
        UserTokenBean userTokenBean = genUserToken(userInfoDto.getUserId(), userInfoDto.getDeviceId());

        //保存新的token
        loginManager.saveToken(appId, userInfoDto.getUserId(), userInfoDto.getDeviceId(), userTokenBean);

        //保存关联角色配置
        if (refreshTokenRoleOp.isPresent()) {
            LoginRoleExpiresInfoDto expiresInfoDto = new LoginRoleExpiresInfoDto();
            expiresInfoDto.setRoleCode(refreshTokenRoleOp.get().getRoleCode());
            expiresInfoDto.setSubjectId(refreshTokenRoleOp.get().getSubjectId());
            expiresInfoDto.setRoleConfigId(refreshTokenRoleOp.get().getRoleConfigId());
            expiresInfoDto.setLoginType(refreshTokenRoleOp.get().getLoginType());
            expiresInfoDto.setAccessTokenExpiresIn(userTokenBean.getAccessTokenExpiresIn());
            expiresInfoDto.setRefreshTokenExpiresIn(userTokenBean.getRefreshTokenExpiresIn());
            loginManager.refTokenRole(expiresInfoDto, userTokenBean.getAccessToken(), userTokenBean.getRefreshToken());
        }

        LogContext.addResLog("userTokenBean={}", JsonUtil.dumps(userTokenBean));
        return RpcResult.success(userTokenBean);
    }

    /**
     * 手机号码登录
     *
     * @param loginParam
     * @return
     */
    public Result<UserTokenBean> phoneLogin(PhoneLoginParamBean loginParam) {
        Integer appId = loginParam.getAppId();
        String authCode = loginParam.getAuthCode();

        //获取业务配置，包括风控配置
        BizCommonConfig bizCommonConfig = commonConfig.getBizConfig();
        if (bizCommonConfig == null) {
            LogContext.addResLog("bizConfig is null");
            return RpcResult.fail(UserLoginService.LOGIN_ERROR);
        }

        //增长鉴权码兑换结果
        Optional<AccountCodeAuthResultDto> accountCodeAuthResultDtoOp = accountAuthManager.codeConvertAccount(authCode, appId);
        if (!accountCodeAuthResultDtoOp.isPresent()) {
            LogContext.addResLog("accountCodeAuthResultDto is null");
            return RpcResult.fail(UserLoginService.AUTH_FAIL);
        }
        AccountCodeAuthResultDto accountCodeAuthResultDto = accountCodeAuthResultDtoOp.get();

        //获取用户信息
        Optional<UserInfoDto> userInfoOp = accountAuthManager.getUserByAuthAccountId(accountCodeAuthResultDto.getAccountId());
        if (!userInfoOp.isPresent()) {
            LogContext.addResLog("userInfo is null");
            return RpcResult.fail(UserLoginService.USER_NOT_EXIST);
        }
        UserInfoDto userInfoDto = userInfoOp.get();

        //登录检查
        LoginContextDto.LoginContextDtoBuilder loginContextBuilder = LoginContextDto.builder()
                .deviceId(loginParam.getDeviceId())
                .appId(String.valueOf(appId))
                .deviceType(loginParam.getDeviceType())
                .clientIp(loginParam.getIp())
                .clientVersion(loginParam.getClientVersion())
                .userId(userInfoDto.getId())
                .phoneNum(userInfoDto.getPhoneNum())
                .authAccountId(accountCodeAuthResultDto.getAccountId())
                .riskAppId(bizCommonConfig.getRiskAppId());
        fileRiskDefaultValue(loginContextBuilder);
        Result<Void> checkResult = loginCheck(appId, userInfoDto, loginContextBuilder.build());
        if (RpcResult.isFail(checkResult)) {
            return RpcResult.fail(checkResult.rCode(), checkResult.getMessage());
        }

        //实际登录处理
        UserTokenBean userTokenBean = genToken(appId, userInfoDto.getId(), loginParam.getDeviceId());
        //登录后置处理
        loginManager.loginPostProcessor(userInfoDto, loginContextBuilder.build());
        //登录成功
        LogContext.addResLog("userTokenBean={}", JsonUtil.dumps(userTokenBean));
        return RpcResult.success(userTokenBean);
    }

    private void fileRiskDefaultValue(LoginContextDto.LoginContextDtoBuilder loginContextBuilder) {
        loginContextBuilder.register(false);
        loginContextBuilder.subAppId("");
        loginContextBuilder.channelId("");
        loginContextBuilder.smId("");
        loginContextBuilder.network("");
    }

    /**
     * 登录检查
     *
     * @param appId
     * @param userInfoDto
     * @param loginContextDto
     * @return
     */
    private Result<Void> loginCheck(int appId, UserInfoDto userInfoDto, LoginContextDto loginContextDto) {
        BizUserConfig bizUserConfig = userConfig.getBizConfig();
        if (bizUserConfig == null) {
            LogContext.addResLog("bizConfig is null");
            return RpcResult.fail(UserLoginService.LOGIN_ERROR);
        }

        Long userId = userInfoDto.getId();

        //登录公会白名单
        List<Long> loginWhitelist = bizUserConfig.getLoginWhitelist();
        //白名单有值&在白名单中才生效
        if (CollectionUtils.isNotEmpty(loginWhitelist)) {
            UserInFamilyBean userInFamily = familyManager.getUserInFamily(userId);
            Long familyId = userInFamily.getFamilyId();
            if (familyId == null) {
                LogContext.addResLog("user familyId is null. userId={}", userId);
                return RpcResult.fail(UserLoginService.NOT_IN_WHITELIST);
            }
            if (!loginWhitelist.contains(familyId)) {
                LogContext.addResLog("user not in whitelist. userId={},familyId={}", userId, familyId);
                return RpcResult.fail(UserLoginService.NOT_IN_WHITELIST);
            }
        }

        //登录前置检查
        //用户注销状态检查
        Pair<Boolean, String> withdrawStatus = checkUserWithdrawStatus(userInfoDto);
        if (!withdrawStatus.getKey()) {
            LogContext.addResLog("userStatus={}", userInfoDto.getStatus());
            return RpcResult.fail(UserLoginService.USER_INVALID, withdrawStatus.getRight());
        }

        //主播是否通过认证
        List<Long> playerAuthWhitelist = bizUserConfig.getPlayerAuthWhitelist();
        if (CollectionUtils.isNotEmpty(playerAuthWhitelist) && !playerAuthWhitelist.contains(userId)) {
            boolean checkPlayerAuth = userManager.checkPlayerAuth(userId);
            if (!checkPlayerAuth) {
                LogContext.addResLog("checkPlayerAuth={}", checkPlayerAuth);
                return RpcResult.fail(UserLoginService.USER_NO_VERIFY);
            }
        }

        //用户是否实名 签约1期，改为允许普通用户登录后进行认证和签约
//        boolean realCheck = userManager.checkUserRealNameAuthStatus(userId, appId);
//        if (!realCheck) {
//            LogContext.addResLog("realCheck={}", realCheck);
//            return RpcResult.fail(UserLoginService.USER_NO_VERIFY);
//        }

        //用户是否被封禁
        boolean banStatus = userManager.checkUserBanStatus(userId);
        if (banStatus) {
            LogContext.addResLog("banStatus={}", banStatus);
            return RpcResult.fail(UserLoginService.USER_BAN);
        }

        //风控检查
        boolean loginAntiRush = riskManager.loginAntiRush(userInfoDto, loginContextDto);
        if (loginAntiRush) {
            LogContext.addResLog("loginAntiRush={}", loginAntiRush);
            return RpcResult.fail(UserLoginService.RISK_FAILED);
        }

        return RpcResult.success();
    }

    /**
     * 生成token
     *
     * @param userId
     * @param deviceId
     * @return
     */
    public UserTokenBean genToken(Integer appId, Long userId, String deviceId) {
        //踢出用户旧token
        loginManager.deleteAllLoginInfo(appId, userId, deviceId);

        //保存新token
        UserTokenBean userTokenBean = genUserToken(userId, deviceId);
        loginManager.saveToken(appId, userId, deviceId, userTokenBean);
        return userTokenBean;
    }

    private UserTokenBean genUserToken(long userId, String deviceId) {
        String accessToken = genMd5Token(userId, deviceId, ACCESS_TOKEN);
        String refreshToken = genMd5Token(userId, deviceId, REFRESH_TOKEN);

        return new UserTokenBean()
                .setUserId(userId)
                .setAccessToken(accessToken)
                .setAccessTokenExpiresIn(userConfig.getAccessTokenExpiresIn())
                .setRefreshToken(refreshToken)
                .setRefreshTokenExpiresIn(userConfig.getRefreshTokenExpiresIn());
    }

    /**
     * 生产新的Token
     *
     * @param userId 用户ID
     * @return token
     */
    private String genMd5Token(long userId, String adviceId, String seed) {
        HashFunction hf = Hashing.md5();
        return hf.hashString(userId + adviceId + seed + System.currentTimeMillis(),
                Charsets.UTF_8).toString();
    }

    /**
     * 检查用户注销状态
     *
     * @param userInfoDto 用户信息
     * @return 结果, left=true表示检查通过，left=false表示检查不通过，right为提示信息
     */
    private Pair<Boolean, String> checkUserWithdrawStatus(UserInfoDto userInfoDto) {
        if (userInfoDto.getStatus() == UserConstant.Status.APPLY_WITHDRAW) {
            return Pair.of(false, "您的账号正在注销申请中，请前往手机APP操作");
        } else if (userInfoDto.getStatus() == UserConstant.Status.WITHDRAW) {
            String msg = "用户已注销。";
            return Pair.of(false, msg);
        } else {
            Optional<UserWithdrawStatusDTO> result = userManager.getUserWithdrawStatus(userInfoDto.getId());
            if (result.isPresent()) {
                if (result.get().isWithdraw()) {
                    return Pair.of(false, "您的账号正在注销申请中，请前往手机APP操作");
                }
            }
        }
        return Pair.of(true, "");
    }

}
