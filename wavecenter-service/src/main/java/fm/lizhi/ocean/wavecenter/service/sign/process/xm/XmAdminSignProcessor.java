package fm.lizhi.ocean.wavecenter.service.sign.process.xm;

import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestAdminApplyCancelFamily;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestAdminInviteUser;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseAdminApplyCancelFamily;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseAdminInviteUser;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.service.sign.manager.ContractManager;
import fm.lizhi.ocean.wavecenter.service.sign.process.AdminSignProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/10/12 18:15
 */
@Component
public class XmAdminSignProcessor implements AdminSignProcessor {

    @Autowired
    private ContractManager contractManager;

    @Override
    public ResponseAdminInviteUser inviteUserCheck(RequestAdminInviteUser request) {
        //西米不支持
        return new ResponseAdminInviteUser().setCode(-1);
    }

    @Override
    public ResponseAdminApplyCancelFamily applyCancelFamilyCheck(RequestAdminApplyCancelFamily request) {
        return new ResponseAdminApplyCancelFamily();
    }

    @Override
    public ResponseAdminApplyCancelFamily doApplyCancelFamily(RequestAdminApplyCancelFamily request) {
        ResponseAdminApplyCancelFamily res = new ResponseAdminApplyCancelFamily();

        Optional<String> signIdOp = contractManager.adminApplyCancelFamilyForSignId(request);
        if (!signIdOp.isPresent()) {
            LogContext.addResLog("signId is not present");
            return res.setCode(-1).setMsg("生成合同失败");
        }

        Optional<String> contractUrlOp = contractManager.genContractSignUrl(request.getCurUserId(), signIdOp.get());
        if (!contractUrlOp.isPresent()) {
            LogContext.addResLog("contactUrl is not present");
            return res.setCode(-1).setMsg("获取合同失败");
        }

        return res.setContractUrl(contractUrlOp.get()).setSignId(signIdOp.get());
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.XIMI;
    }
}
