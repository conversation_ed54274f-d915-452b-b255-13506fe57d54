package fm.lizhi.ocean.wavecenter.service.sign.dto;

import fm.lizhi.ocean.wavecenter.api.sign.constant.FlowConfirmStatusEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.Singular;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/26 16:38
 */
@Getter
@Builder
public class QueryFlowParamDTO {

    @Singular
    private List<FlowConfirmStatusEnum> confirmStatuses;

    @Builder.Default
    private int pageNo = 1;

    @Builder.Default
    private int pageSize = 1;

}
