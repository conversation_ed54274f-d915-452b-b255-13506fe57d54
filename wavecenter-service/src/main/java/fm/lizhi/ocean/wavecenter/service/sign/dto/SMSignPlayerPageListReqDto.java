package fm.lizhi.ocean.wavecenter.service.sign.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/13 20:40
 */
@Data
public class SMSignPlayerPageListReqDto {

    private Integer appId;

    private Long familyId;

    /**
     * 签约厅ID
     */
    private List<Long> roomIds;

    /**
     * 主播ID
     */
    private Long playerId;

    /**
     * 签约时间-开始
     */
    private Date signStartDate;

    /**
     * 签约时间-结束
     */
    private Date signEndDate;

    /**
     * 到期时间-开始
     */
    private Date expireStartDate;

    /**
     * 到期时间-结束
     */
    private Date expireEndDate;

    /**
     * 解约时间-开始
     */
    private Date stopStartDate;

    /**
     * 解约时间-结束
     */
    private Date stopEndDate;

    /**
     * 签约状态
     * 1=已签约
     * 0=已解约
     */
    private Integer signStatus;

    /**
     * 结算比例-最小值
     */
    private Integer settleMin;

    /**
     * 结算比例-最大值
     */
    private Integer settleMax;

    private Integer pageNo;

    private Integer pageSize;

}
