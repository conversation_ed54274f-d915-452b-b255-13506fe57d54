package fm.lizhi.ocean.wavecenter.service.anchor.singer.manager;

import java.util.List;

import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.BatchBuildSingerOperateRecordParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.BuildSingerOperateRecordParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerOperateRecordDTO;

/**
 * 歌手库操作记录
 * <AUTHOR>
 */
public interface SingerOperateRecordManager {

    /**
     * 根据应用ID，歌手类型和用户ID列表查询歌手操作记录
     * @param appId 应用ID
     * @param singerType 歌手类型
     * @param userIds 用户ID列表
     * @return 歌手操作记录列表
     */
    List<SingerOperateRecordDTO> getSingerOperateRecordList(Integer appId, Integer singerType, List<Long> userIds, List<Integer> operateTypeList);


    /**
     * 构建歌手操作记录
     */
    SingerOperateRecordDTO buildSingerOperateRecord(BuildSingerOperateRecordParamDTO param);

    /**
     * 批量构建歌手操作记录
     */
    List<SingerOperateRecordDTO> batchBuildSingerOperateRecord(BatchBuildSingerOperateRecordParamDTO param);

    Long getSingerOperateRecordCount(Integer appId, Integer singerType, List<Long> userIds, List<Integer> operateTypeList);
}
