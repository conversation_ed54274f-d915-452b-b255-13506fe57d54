package fm.lizhi.ocean.wavecenter.service.activitycenter.handler;

import fm.lizhi.ocean.wavecenter.service.common.handler.BaseHandlerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 头像框处理工厂
 */
@Component
public class DressUpHandlerFactory extends BaseHandlerFactory<Integer, DressUpGiveHandler> {

    @Autowired
    private List<DressUpGiveHandler> handlers;

    @Override
    public void registerAllHandlers() {
        for (DressUpGiveHandler handler : handlers) {
            registerHandler(handler.getDressUpType(), handler);
        }
    }
}
