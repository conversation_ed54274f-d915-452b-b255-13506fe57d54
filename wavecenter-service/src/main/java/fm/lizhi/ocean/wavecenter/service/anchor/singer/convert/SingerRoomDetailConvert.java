package fm.lizhi.ocean.wavecenter.service.anchor.singer.convert;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.RoomDetailUserInfoBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerFamilyInfoBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseSingerRoomDetails;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerRoomDetailDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;

@Mapper(collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface SingerRoomDetailConvert {

    SingerRoomDetailConvert INSTANCE = Mappers.getMapper(SingerRoomDetailConvert.class);

    @Mapping(target = "familyInfo", ignore = true)
    @Mapping(target = "njInfo", ignore = true)
    ResponseSingerRoomDetails convertToResponse(SingerRoomDetailDTO dto);

    @Mapping(target = "photo", source = "avatar")
    RoomDetailUserInfoBean buildSingerInfo(SimpleUserDto user);

    SingerFamilyInfoBean buildFamilyInfo(FamilyBean family);

    default List<ResponseSingerRoomDetails> convertToResponseList(List<SingerRoomDetailDTO> dtoList,
                                                                Map<Long, SimpleUserDto> userMap,
                                                                Map<Long, FamilyBean> familyMap) {
        return dtoList.stream()
                .map(dto -> {
                    ResponseSingerRoomDetails response = convertToResponse(dto);
                    // 设置歌手信息
                    SimpleUserDto user = userMap.get(dto.getNjId());
                    if (user != null) {
                        response.setNjInfo(buildSingerInfo(user));
                    }
                    // 设置家族信息
                    FamilyBean family = familyMap.get(dto.getFamilyId());
                    if (family != null) {
                        response.setFamilyInfo(buildFamilyInfo(family));
                    }
                    return response;
                })
                .collect(Collectors.toList());
    }
} 