package fm.lizhi.ocean.wavecenter.service.datacenter.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 大数据公会日统计表
 *
 * <AUTHOR>
 * @date 2024-04-29 02:59:17
 */
@Data
public class DataFamilyDayDTO {

    private Long id;

    /**
     * 业务
     */
    private Integer appId;

    /**
     * 日期 格式 YYYY-MM-DD
     */
    private Date statDate;

    /**
     * 日期 格式  YYYYMMDD
     */
    private Integer statDateValue;

    /**
     * 公会ID
     */
    private Long familyId;

    /**
     * 公会收入,公会考核期间总收入，单位：钻（结算币)
     */

    private BigDecimal income;

    /**
     * 公会魅力值        公会考核期间总魅力值，单位：魅力值
     */
    private Integer charm;

    /**
     * 签约厅数        公会签约厅数
     */
    private Integer signRoomCnt;

    /**
     * 开播厅数        公会开播厅数
     */
    private Integer openRoomCnt;

    /**
     * 厅均收入 公会收入/开播厅数
     */
    private BigDecimal roomAvgIncome;

    /**
     * 厅均魅力值 公会魅力值/开播厅数
     */
    private BigDecimal roomAvgCharm;

    /**
     * 签约主播数
     */
    private Integer signPlayerCnt;

    /**
     * 上麦主播数 直播间上麦人数
     */
    private Integer upGuestPlayerCnt;

    /**
     * 有收入主播数 直播间有收入签约主播人数
     */
    private Integer incomePlayerCnt;

    /**
     * 人均收入 公会收入/有收入主播数
     */
    private BigDecimal playerAvgIncome;

    /**
     * 人均魅力值 公会魅力值/有收入主播数
     */
    private BigDecimal playerAvgCharm;

    /**
     * 角色创建时间
     */
    private Date createTime;

    /**
     * 角色修改时间
     */
    private Date modifyTime;

    /**
     * 总收入
     */
    private BigDecimal allIncome;

    /**
     * 签约厅收礼收入
     */
    private BigDecimal signHallIncome;

    /**
     * 官方厅收礼收入
     */
    private BigDecimal officialHallIncome;

    /**
     * 个播收礼收入
     */
    private BigDecimal personalHallIncome;

    /**
     * 贵族提成收入
     */
    private BigDecimal nobleIncome;

    /**
     * 个播贵族提成收入
     */
    private BigDecimal personalNobleIncome;

    /**
     * 上麦率
     */
    private BigDecimal upPlayerRate;

    /**
     * 开播率
     */
    private BigDecimal openRate;

    /**
     * 有收入主播占比
     */
    private BigDecimal incomePlayerRate;

    /**
     * 有收入厅数
     */
    private Integer incomeRoomCnt;

    /**
     * 有收入厅占比
     */
    private BigDecimal incomeRoomRate;

    /**
     * 有收入歌手数量
     */
    private Integer incomeSingerCnt;

}