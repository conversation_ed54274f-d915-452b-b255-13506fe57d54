package fm.lizhi.ocean.wavecenter.service.activitycenter.impl;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataDetailBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataGiftBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataPlayerBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataSummaryBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityReportDataService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityReportDataManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@ServiceProvider
@Slf4j
public class ActivityReportDataServiceImpl implements ActivityReportDataService {

    @Autowired
    private ActivityReportDataManager activityReportDataManager;

    @Autowired
    private UserManager userManager;


    @Override
    public Result<ActivityReportDataSummaryBean> getReportSummary(Long activityId, int appId) {
        return activityReportDataManager.getReportSummary(activityId, appId);
    }

    @Override
    public Result<List<ActivityReportDataDetailBean>> getReportDetail(Long activityId, int appId) {
        return activityReportDataManager.getReportDetail(activityId, appId);
    }

    @Override
    public Result<PageBean<ActivityReportDataPlayerBean>> pageReportPlayer(Long activityId, int appId, int pageNo, int pageSize) {

        return ResultHandler.handle(appId, () -> {
            Result<PageBean<ActivityReportDataPlayerBean>> result = activityReportDataManager.pageReportPlayer(activityId, appId, pageNo, pageSize);
            if (RpcResult.isFail(result) || CollectionUtils.isEmpty(result.target().getList())) {
                return result;
            }

            List<Long> list = result.target().getList().stream().map(ActivityReportDataPlayerBean::getPlayerId).collect(Collectors.toList());
            Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(list);
            result.target().getList().forEach(item -> {
                SimpleUserDto user = userMap.get(item.getPlayerId());
                if (user != null) {
                    item.setPlayerBand(user.getBand());
                    item.setPlayerName(user.getName());
                }
            });
            return result;
        });
    }

    @Override
    public Result<PageBean<ActivityReportDataGiftBean>> pageReportGift(Long activityId, int appId, int pageNo, int pageSize) {

        return ResultHandler.handle(appId, () ->{
            Result<PageBean<ActivityReportDataGiftBean>> result = activityReportDataManager.pageReportGift(activityId, appId, pageNo, pageSize);
            if (RpcResult.isFail(result) || CollectionUtils.isEmpty(result.target().getList())) {
                return result;
            }

            List<Long> list = result.target().getList().stream().map(ActivityReportDataGiftBean::getUserId).collect(Collectors.toList());
            Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(list);
            result.target().getList().forEach(item -> {
                SimpleUserDto user = userMap.get(item.getUserId());
                if (user != null) {
                    item.setBand(user.getBand());
                    item.setUserName(user.getName());
                }
            });
            return result;
        });
    }
}
