package fm.lizhi.ocean.wavecenter.service.award.family.impl;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestGetFamilyWeekAwardV1;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestGetFamilyWeekAwardV2;
import fm.lizhi.ocean.wavecenter.api.award.family.response.ResponseGetFamilyWeekAwardV1;
import fm.lizhi.ocean.wavecenter.api.award.family.response.ResponseGetFamilyWeekAwardV2;
import fm.lizhi.ocean.wavecenter.api.award.family.service.FamilyAwardService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.service.award.family.manager.FamilyAwardManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@ServiceProvider
public class FamilyAwardServiceImpl implements FamilyAwardService {

    @Autowired
    private FamilyAwardManager familyAwardManager;

    @Override
    public Result<ResponseGetFamilyWeekAwardV1> getFamilyWeekAwardV1(RequestGetFamilyWeekAwardV1 request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));
        LogContext.addResLog("request={}", JsonUtils.toJsonString(request));
        Result<ResponseGetFamilyWeekAwardV1> result = familyAwardManager.getFamilyWeekAwardV1(request);
        if (RpcResult.isSuccess(result)) {
            log.debug("getFamilyWeekAwardV1 success, response={}", result.target());
        }
        return result;
    }

    @Override
    public Result<ResponseGetFamilyWeekAwardV2> getFamilyWeekAwardV2(RequestGetFamilyWeekAwardV2 request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));
        LogContext.addResLog("request={}", JsonUtils.toJsonString(request));
        Result<ResponseGetFamilyWeekAwardV2> result = familyAwardManager.getFamilyWeekAwardV2(request);
        if (RpcResult.isSuccess(result)) {
            log.debug("getFamilyWeekAwardV2 success, response={}", result.target());
        }
        return result;
    }
}
