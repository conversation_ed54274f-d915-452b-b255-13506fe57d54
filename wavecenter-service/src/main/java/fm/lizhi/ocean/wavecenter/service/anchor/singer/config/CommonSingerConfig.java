package fm.lizhi.ocean.wavecenter.service.anchor.singer.config;

import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.UserSingerGloryConfig;

import java.util.Map;

/**
 * <AUTHOR>
 */
public interface CommonSingerConfig {

    /**
     * 歌曲风格配置 不允许包含逗号和下划线
     */
    String getSongStyleConfig();

    /**
     * 获取音频审核配置
     */
    String getAudioAuditConfig();

    /**
     * 获取歌手认证申请文案
     */
    String getSingerAuditReportText();

    /**
     * 是否开启点唱厅导入
     */
    boolean isEnableImportSingHall();

    /**
     * 是否开启点唱厅清理
     */
    boolean isEnableCleanSingHall();

    /**
     * 导入点唱厅要求的收入流水
     */
    Long getImportSingHallGtAllIncome();

    /**
     * 用户歌手勋章配置
     */
    UserSingerGloryConfig getUserSingerGloryConfig();

    /**
     * 弱关联开关
     *
     * @return 开关, true: 弱关联，false: 强关联
     */
    boolean isLessRelevanceSwitch();

    /**
     * 歌手认证申请被拒绝后，多少天内不允许再次申请,0则是存在记录则不允许
     */
    Integer getRejectRecordDay();

    /**
     * 歌手风格数量限制
     */
    Map<Integer, Integer> getMaxStyleNumMap();
}
