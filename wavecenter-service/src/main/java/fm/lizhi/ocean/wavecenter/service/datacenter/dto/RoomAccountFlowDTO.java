package fm.lizhi.ocean.wavecenter.service.datacenter.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/4/22 19:33
 */
@Data
@Accessors(chain = true)
public class RoomAccountFlowDTO {

    /**
     * 业务线appId
     */
    private Integer appId;

    private String accountEngineCode;

    /**
     * 操作 minus - 减 plus - 加  frozen -冻结 unfrozen-解冻
     */
    private String accountOpType;

    /**
     * 金额
     */
    private String amount;

    /**
     * bizId
     */
    private Integer bizId;

    /**
     * 流水ID
     */
    private Long flowId;

    /**
     * 厅主ID
     */
    private Long roomId;

    /**
     * lizhi_ppapp_live - PP约玩  lizhi_heiye_common-黑叶 lizhi_ximi_common-西米
     */
    private String tenantCode;

    /**
     * 流水日期
     */
    private Date tradeDate;

}
