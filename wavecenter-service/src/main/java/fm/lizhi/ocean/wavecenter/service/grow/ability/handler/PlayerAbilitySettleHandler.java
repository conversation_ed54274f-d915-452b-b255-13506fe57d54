package fm.lizhi.ocean.wavecenter.service.grow.ability.handler;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.Period;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.PlayerAbility;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.ShortCircuitingTask;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.TaskExecuteContext;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.TaskInfoI;
import fm.lizhi.ocean.wavecenter.domain.grow.repository.GrowPlayerAbilityRepository;
import fm.lizhi.ocean.wavecenter.service.common.manager.IdManager;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.PlayerDataForGrowDTO;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.PlayerDataManager;
import fm.lizhi.ocean.wavecenter.service.grow.ability.constants.AbilityConstant;
import fm.lizhi.ocean.wavecenter.service.grow.ability.convert.GrowMetricConvert;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.PlayerSettleFlowDTO;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.SettlePeriodDTO;
import fm.lizhi.ocean.wavecenter.service.grow.ability.manager.GrowPlayerMetricManager;
import fm.lizhi.ocean.wavecenter.service.grow.ability.manager.GrowPlayerTaskManager;
import fm.lizhi.ocean.wavecenter.service.grow.ability.manager.GrowSettleFlowManager;
import fm.lizhi.ocean.wavecenter.service.grow.config.GrowConfig;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto.TaskTemplateCapabilityDTO;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto.TaskTemplateConditionDTO;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.manager.TaskTemplateManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 主播能力结算
 * <AUTHOR>
 * @date 2025/6/6 15:57
 */
@Slf4j
@Component
public class PlayerAbilitySettleHandler {

    @Autowired
    private GrowSettleFlowManager growSettleFlowManager;
    @Autowired
    private PlayerDataManager playerDataManager;
    @Autowired
    private GrowConfig growConfig;
    @Autowired
    private TaskTemplateManager taskTemplateManager;
    @Autowired
    private GrowPlayerTaskManager growPlayerTaskManager;
    @Autowired
    private GrowTaskFactory growTaskFactory;
    @Autowired
    private IdManager idManager;
    @Autowired
    private GrowPlayerAbilityRepository growPlayerAbilityRepository;
    @Autowired
    private GrowPlayerMetricManager growPlayerMetricManager;

    /**
     * 根据周期结算所有主播
     * @param settlePeriod
     */
    public void settleByPeriod(SettlePeriodDTO settlePeriod){
        // 方法启动日志
        log.info("Starting settleByPeriod for settlePeriod: {}", settlePeriod);

        // 获取当前周期的结算流程
        PlayerSettleFlowDTO flow = growSettleFlowManager.getOrInitPlayerSettleFlow(settlePeriod);
        if (AbilityConstant.SETTLE_FLOW_STATUS_FINISH.equals(flow.getStatus())) {
            log.info("settle flow is already finished. Flow status: {}", flow.getStatus());
            return;
        }

        // 结算
        doSettle(flow, settlePeriod);

        // 结算流程结束
        log.info("settle flow is finishing. Updating flow status to FINISH.");
        flow.setStatus(AbilityConstant.SETTLE_FLOW_STATUS_FINISH);
        growSettleFlowManager.updatePlayerSettleFlow(flow);
        log.info("settle flow has finished successfully.");
    }

    /**
     * 指定主播结算
     * @param settlePeriod
     * @param playerIds
     */
    public void settleByPeriod(SettlePeriodDTO settlePeriod, List<Long> playerIds){
        List<TaskTemplateConditionDTO> taskTemplates = taskTemplateManager.queryEnableTaskTemplateConditionList();
        for (Long playerId : playerIds) {
            // 结算
            doSettle(playerId, settlePeriod, taskTemplates);
        }
    }

    /**
     * 结算
     * @param flow
     */
    private void doSettle(PlayerSettleFlowDTO flow, SettlePeriodDTO settlePeriod){
        List<Long> toBeSettlePlayer = getToBeSettlePlayerIdsByFlow(flow);
        log.info("Retrieved toBeSettlePlayer list: {}", toBeSettlePlayer);
        if (CollectionUtils.isEmpty(toBeSettlePlayer)) {
            return;
        }

        // 查询任务模板
        List<TaskTemplateConditionDTO> taskTemplates = taskTemplateManager.queryEnableTaskTemplateConditionList();
        if (log.isDebugEnabled()) {
            log.debug("PlayerAbilitySettle taskTemplates={}", JsonUtil.dumps(taskTemplates));
        }

        while (CollectionUtils.isNotEmpty(toBeSettlePlayer)) {
            for (Long playerId : toBeSettlePlayer) {
                // 结算
                doSettle(playerId, settlePeriod, taskTemplates);
                // 更新结算流程
                flow.setLastSettleId(playerId);
                growSettleFlowManager.updatePlayerSettleFlow(flow);
            }
            // 查询下一批待结算主播
            toBeSettlePlayer = getToBeSettlePlayerIdsByFlow(flow);
        }
    }

    /**
     * 结算主播能力
     * @param playerId
     * @param settlePeriodDTO
     * @param systemAllTaskTemplates
     */
    private void doSettle(Long playerId, SettlePeriodDTO settlePeriodDTO, List<TaskTemplateConditionDTO> systemAllTaskTemplates){
        try {
            log.info("PlayerAbilitySettleHandlerPlayerId={}, Starting settlement for settlePeriod: {}", playerId, settlePeriodDTO);
            // 主播本周期能力
            PlayerAbility playerAbility = buildPlayerAbility(playerId, settlePeriodDTO);

            // 根据能力项分组 key=能力项code value=每个能力项对应的任务模板列表
            Map<String, List<TaskTemplateConditionDTO>> capabilityMap = systemAllTaskTemplates.stream()
                    .collect(Collectors.groupingBy(v -> v.getCapability().getCapabilityCode()));

            // 主播周指标数据
            PlayerDataForGrowDTO playerDataInWeek = playerDataManager.getPlayerGrowDataInWeek(playerId, settlePeriodDTO.getStartDate(), settlePeriodDTO.getEndDate());
            log.info("PlayerAbilitySettleHandlerPlayerId={}, Retrieved player data: {}", playerId, playerDataInWeek);

            // 删除历史任务
            growPlayerTaskManager.deletePeriodTask(playerId, settlePeriodDTO);

            // 遍历每个能力项 进行结算
            for (Map.Entry<String, List<TaskTemplateConditionDTO>> entry : capabilityMap.entrySet()) {
                // 能力项code
                String capabilityCode = entry.getKey();
                // 任务模板列表
                List<TaskTemplateConditionDTO> taskTemplates = entry.getValue();
                // 结算能力分
                BigDecimal score = doSettleCapability(playerId, settlePeriodDTO, taskTemplates, playerDataInWeek);
                playerAbility.addCapabilityScore(capabilityCode, score);
                log.info("PlayerAbilitySettleHandlerPlayerId={}, Calculated score for capabilityCode: {}, score: {}", playerId, capabilityCode, score);
            }

            // 上期数据对比
            PlayerAbility preAbility = growPlayerAbilityRepository.getPlayerAbility(playerId, buildPrePeriod(settlePeriodDTO));
            playerAbility.comparePrePeriodScore(preAbility);
            log.info("PlayerAbilitySettleHandlerPlayerId={}, Compared with previous period score", playerId);

            // 保存能力对象
            growPlayerAbilityRepository.savePlayerAbility(playerAbility);
            growPlayerMetricManager.savePlayerMetric(playerDataInWeek, settlePeriodDTO);
            log.info("PlayerAbilitySettleHandlerPlayerId={}, Saved player ability and metrics", playerId);

        } catch (Exception e) {
            log.error("playerAbilitySettle playerId={} error:", playerId, e);
        }
    }

    /**
     * 上一个周期
     * @param settlePeriodDTO
     * @return
     */
    private Period buildPrePeriod(SettlePeriodDTO settlePeriodDTO){
        return new Period(DateUtil.getDayBefore(settlePeriodDTO.getStartDate(), 7)
                , DateUtil.getDayBefore(settlePeriodDTO.getEndDate(), 7)
        );
    }

    private PlayerAbility buildPlayerAbility(Long playerId, SettlePeriodDTO settlePeriodDTO){
        return new PlayerAbility(idManager.genId()
                , ContextUtils.getBusinessEvnEnum().getAppId()
                , playerId
                , new Period(settlePeriodDTO.getStartDate(), settlePeriodDTO.getEndDate()));
    }

    /**
     * 结算单项能力项的分数
     * @param playerId
     * @param capabilityTaskTemplates
     */
    private BigDecimal doSettleCapability(Long playerId, SettlePeriodDTO settlePeriodDTO, List<TaskTemplateConditionDTO> capabilityTaskTemplates, PlayerDataForGrowDTO playerDataInWeek){
        try {
            log.info("PlayerAbilitySettleHandlerPlayerId={}, Starting doSettleCapability, capabilityTaskTemplates size: {}", playerId, capabilityTaskTemplates.size());
            // 根据任务模板解析出ShortCircuitingTask任务实例
            ShortCircuitingTask resultTask = growTaskFactory.createResultTask(capabilityTaskTemplates, playerId);

            // 任务上下文构建
            TaskExecuteContext.TaskExecuteContextBuilder taskContextBuilder = TaskExecuteContext.builder();
            taskContextBuilder.playerSupportMetric(GrowMetricConvert.I.convert(playerDataInWeek));

            // 执行任务
            resultTask.execute(taskContextBuilder.build());
            log.info("PlayerAbilitySettleHandlerPlayerId={}, Executed task, resultTask isExecuteFinish: {}", playerId, resultTask.isExecuteFinish());

            // 执行过
            if (!resultTask.isExecuteFinish()) {
                log.warn("PlayerAbilitySettleHandlerPlayerId={}, doSettleCapability task execute fail.", playerId);
                return BigDecimal.ZERO;
            }
            // 获取执行通过的任务
            TaskInfoI task = resultTask.getResult();
            growPlayerTaskManager.savePlayerTask(task, settlePeriodDTO);
            log.info("PlayerAbilitySettleHandlerPlayerId={}, Saved task, taskTemplateId: {}", playerId, task.getTemplateId());

            // 奖励先不抽象到任务中，目前只是一个值对象
            for (TaskTemplateConditionDTO taskTemplate : capabilityTaskTemplates) {
                // 获取任务奖励
                if (taskTemplate.getId().equals(task.getTemplateId())) {
                    TaskTemplateCapabilityDTO capability = taskTemplate.getCapability();
                    log.info("PlayerAbilitySettleHandlerPlayerId={}, Found matching taskTemplate, capabilityCode: {}, score: {}", playerId, capability.getCapabilityCode(), capability.getCapabilityScore());
                    return capability.getCapabilityScore();
                }
            }
            log.warn("PlayerAbilitySettleHandlerPlayerId={}, No matching taskTemplate found", playerId);
        } catch (Exception e) {
            log.error("playerAbilitySettle doSettleCapability playerId={} error:", playerId, e);
        }
        return BigDecimal.ZERO;
    }

    /**
     * 查询流程的待结算主播
     * @param flow
     * @return
     */
    private List<Long> getToBeSettlePlayerIdsByFlow(PlayerSettleFlowDTO flow){
        // 上一次的结算主播ID
        Long lastSettleId = flow.getLastSettleId();
        // 从数据中心获取名单
        return playerDataManager.getPlayerIdsWeekHasIncomeByMinPlayerId(flow.getStartWeekDate()
                , flow.getEndWeekDate()
                , lastSettleId
                , growConfig.getAbilitySettlePageSize());
    }

}
