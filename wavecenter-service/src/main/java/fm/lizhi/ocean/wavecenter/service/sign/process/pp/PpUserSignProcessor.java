package fm.lizhi.ocean.wavecenter.service.sign.process.pp;

import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.FamilyAndNjContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.NjAndPlayerContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.*;
import fm.lizhi.ocean.wavecenter.api.sign.request.*;
import fm.lizhi.ocean.wavecenter.api.sign.response.*;
import fm.lizhi.ocean.wavecenter.api.sign.service.SignUserService;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.service.sign.config.SignConfig;
import fm.lizhi.ocean.wavecenter.service.sign.dto.*;
import fm.lizhi.ocean.wavecenter.service.sign.handler.PpSignCheckJobHoppingHandler;
import fm.lizhi.ocean.wavecenter.service.sign.manager.ContractManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.NonContractManager;
import fm.lizhi.ocean.wavecenter.service.sign.process.UserSignProcessor;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserVerifyDataDTO;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/10/10 17:39
 */
@Component
public class PpUserSignProcessor implements UserSignProcessor {

    @Autowired
    private ContractManager contractManager;
    @Autowired
    private UserManager userManager;
    @Autowired
    private SignConfig signConfig;
    @Autowired
    private PpSignCheckJobHoppingHandler ppSignCheckJobHoppingHandler;
    @Autowired
    private NonContractManager nonContractManager;

    @Override
    public ResponseUserSignAdminInvite signAdminInviteCheck(RequestUserSignAdminInvite request) {
        ResponseUserSignAdminInvite res = new ResponseUserSignAdminInvite().setCode(0);

        // 防跳槽检查
        if (request.getOperateTypeEnum() == OperateTypeEnum.AGREE) {
            PageBean<NjAndPlayerContractBean> contractListPage = nonContractManager.queryList(QueryNonContractDTO.builder()
                    .contractId(request.getPlayerSignId())
                    .build());
            List<NjAndPlayerContractBean> contractList = contractListPage.getList();
            if (CollectionUtils.isEmpty(contractList)) {
                LogContext.addResLog("contract is not exist");
                return res.setCode(-1);
            }
            NjAndPlayerContractBean contractBean = contractList.get(0);

            Pair<Integer, String> integerStringPair = ppSignCheckJobHoppingHandler.forUserAcceptToBePlayer(contractBean.getPlayerUserId(), contractBean.getNjUserId());
            if (integerStringPair.getKey() != 0) {
                LogContext.addResLog("checkJobHoppingCode={}", integerStringPair.getRight());
                return res.setCode(integerStringPair.getKey()).setMsg(integerStringPair.getValue());
            }
        }

        return res;
    }

    @Override
    public ResponseUserApplyPlayer userApplyPlayerCheck(RequestUserApplyPlayer request) {
        ResponseUserApplyPlayer res = new ResponseUserApplyPlayer().setCode(0);

        //管理员是否存在
        List<SimpleUserDto> targetUserList = userManager.getSimpleUserByIds(Lists.newArrayList(request.getTargetUserId()));
        if (CollectionUtils.isEmpty(targetUserList)) {
            return res.setCode(SignUserService.APPLY_PLAYER_TARGET_USER_NOT_EXIST);
        }

        //目标厅是否为管理员
        boolean userSignAsRoom = contractManager.isUserSignAsRoom(request.getTargetUserId());
        if (!userSignAsRoom) {
            return res.setCode(SignUserService.APPLY_PLAYER_TARGET_NOT_ROOM);
        }

        //当前用户是否存在签约成功的记录
        PageBean<FamilyAndNjContractBean> signList = contractManager.queryContract(RequestFamilyAndNjContractDTO.builder()
                .njId(request.getCurUserId())
                .type(ContractTypeEnum.SIGN)
                .type(ContractTypeEnum.SUBJECT_CHANGE)
                .type(ContractTypeEnum.RENEW)
                .relation(SignRelationEnum.SIGN_SUCCESS)
                .build());
        if (CollectionUtils.isNotEmpty(signList.getList())) {
            return res.setCode(SignUserService.APPLY_PLAYER_REQ_SIGNED);
        }

        // 防跳槽检查
        Pair<Integer, String> integerStringPair = ppSignCheckJobHoppingHandler.forUserApply(request.getCurUserId(), request.getTargetUserId());
        if (integerStringPair.getKey() != 0) {
            LogContext.addResLog("checkJobHoppingCode={}", integerStringPair.getRight());
            return res.setCode(integerStringPair.getKey()).setMsg(integerStringPair.getValue());
        }

        return res;
    }

    @Override
    public ResponseUserInfoStatus signInfoCheck(RequestUserInfoStatus request) {
        //PP没有需要检查的信息，直接通过
        return new ResponseUserInfoStatus().setInfoStatus(IdentifyStatusEnum.FINISHED.getCode());
    }

    @Override
    public ResponseUserApplyAdmin checkApplyAdmin(RequestUserApplyAdmin request) {
        //PP暂不支持
        return new ResponseUserApplyAdmin().setCode(-1).setMsg("暂不支持");
    }

    @Override
    public Optional<FamilyAndNjContractBean> existWaitSignContract(RequestUserApplyAdmin request) {
        return Optional.empty();
    }

    @Override
    public ResponseUserDoSign doSignAdminCheck(RequestUserDoSign request) {
        ResponseUserDoSign res = new ResponseUserDoSign();

        if (!signConfig.getPp().isSignFunctionSwitch()) {
            return res.setCode(-1).setMsg("签约功能暂时关闭，恢复时间等待官方通知");
        }

        PageBean<FamilyAndNjContractBean> pageList = contractManager.queryContract(RequestFamilyAndNjContractDTO.builder()
                .contractId(request.getContractId())
                .build());
        if (CollectionUtils.isEmpty(pageList.getList())) {
            LogContext.addResLog("contract pageList is empty");
            return res.setCode(-1).setMsg("该合同不存在");
        }
        FamilyAndNjContractBean contract = pageList.getList().get(0);

        if (!contract.getNjUserId().equals(request.getCurUserId())) {
            LogContext.addResLog("curUserId not eq njUserId");
            return res.setCode(-1).setMsg("您不是合同所有人");
        }

        //检查上上签实名和平台实名是否一致
        Optional<SignPersonalInfoDTO> signPersonalInfoOp = contractManager.getSignPersonalInfo(request.getCurUserId());
        if ((!signPersonalInfoOp.isPresent())
                || SignAuthStatusEnum.AUTO_AUTH_PASS != signPersonalInfoOp.get().getAuthStatus()) {
            return res.setCode(SignUserService.APPLY_ADMIN_NO_SIGN_VERIFY).setMsg("用户未完成上上签认证");
        }
        String identityNo = signPersonalInfoOp.get().getIdentityNo();

        Optional<UserVerifyDataDTO> verifyDataOp = userManager.getVerifyData(request.getCurUserId());
        if ((!verifyDataOp.isPresent()) || verifyDataOp.get().getVerifyStatus() != 2) {
            return res.setCode(SignUserService.APPLY_ADMIN_NO_PLATFORM_VERIFY).setMsg("用户未完成平台实名认证");
        }
        String idCardNumber = verifyDataOp.get().getIdCardNumber();
        Long familyId = contract.getFamilyId();

        //检查该身份证是否签约了其他家族
        List<FamilyAndNjContractBean> joinList = contractManager.queryIdentityNoJoinFamily(identityNo);
        if (CollectionUtils.isNotEmpty(joinList)) {
            for (FamilyAndNjContractBean c : joinList) {
                if (!familyId.equals(c.getFamilyId())) {
                    return res.setCode(-1).setMsg("实名身份证签约了其他家族,不能再次签约");
                }
            }
        }

        if (!Objects.equals(idCardNumber, identityNo)) {
            LogContext.addResLog("idCardNum={}, idCardNo={}", idCardNumber, identityNo);
            return res.setCode(-1).setMsg("上上签和平台实名信息不同");
        }

        List<FamilyNjJoinRecordDTO> recordList = contractManager.queryFamilyNjJoinRecord(QueryFamilyNjJoinRecordDTO.builder()
                .njId(request.getCurUserId())
                .status("JOIN")
                .build());
        if (CollectionUtils.isNotEmpty(recordList)) {
            LogContext.addResLog("familyNjJoinRecord is not empty");
            return res.setCode(-1).setMsg("你已签约其他家族,不能再次签约");
        }

        // 防跳槽检查
        Pair<Integer, String> integerStringPair = ppSignCheckJobHoppingHandler.forUserAcceptToBeAdmin(request.getCurUserId(), familyId);
        if (integerStringPair.getKey() != 0) {
            LogContext.addResLog("checkJobHoppingCode={}", integerStringPair.getRight());
            return res.setCode(integerStringPair.getKey()).setMsg(integerStringPair.getValue());
        }

        return res;
    }

    @Override
    public Optional<String> doSignGenSignId(RequestUserDoSign request) {
        //先查询是否存在，存在直接返回，不调业务，因为业务即使存在也不会返回
        PageBean<FamilyAndNjContractBean> pageList = contractManager.queryContract(RequestFamilyAndNjContractDTO.builder()
                .contractId(request.getContractId())
                .pageSize(1)
                .build());
        if (CollectionUtils.isNotEmpty(pageList.getList()) && StringUtils.isNotBlank(pageList.getList().get(0).getSignId())) {
            return Optional.of(pageList.getList().get(0).getSignId());
        }

        return contractManager.doSignGenSignId(request.getContractId());
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.PP;
    }
}
