package fm.lizhi.ocean.wavecenter.service.resource.recommendcard.impl;

import com.google.common.collect.Lists;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.live.response.ResponseRecommendCardStaticsInfo;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.*;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.request.*;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.response.RewardResultBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.service.RecommendCardService;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.RoomSignBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.base.processor.ProcessorFactory;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.DataRoomFamilyWeekDTO;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.RoomDataManager;
import fm.lizhi.ocean.wavecenter.service.live.manager.RecommendCardStaticsManager;
import fm.lizhi.ocean.wavecenter.service.resource.config.ResourceConfig;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.convert.RecommendCardConvert;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto.GetUseRecordParamDTO;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto.RecommendAllocationRecordDTO;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto.RecommendCardUseRecordDTO;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto.UserRecommendCardStockDTO;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.manager.RecommendCardManager;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.processor.RecommendCardRewardProcessor;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.RoomManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/21 14:34
 */
@ServiceProvider
public class RecommendCardServiceImpl implements RecommendCardService {

    @Autowired
    private RecommendCardManager recommendCardManager;
    @Autowired
    private UserManager userManager;
    @Autowired
    private FamilyManager familyManager;
    @Autowired
    private RecommendCardStaticsManager recommendCardStaticsManager;
    @Autowired
    private RoomManager roomManager;
    @Autowired
    private RoomDataManager roomDataManager;
    @Autowired
    private ResourceConfig resourceConfig;

    @Autowired
    private ProcessorFactory processorFactory;

    @Override
    public Result<RecommendCardUserStockBean> getUserStock(RequestGetUserStock request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);
        Long userId = request.getUserId();

        RecommendCardUserStockBean bean = new RecommendCardUserStockBean();

        // 库存
        List<UserRecommendCardStockDTO> userStock = recommendCardManager.getUserStock(Lists.newArrayList(userId));
        if (CollectionUtils.isNotEmpty(userStock)) {
            Integer stock = userStock.get(0).getStock();
            LogContext.addResLog("stock={}", stock);
            bean.setStock(stock == null ? 0 : stock);
        }

        // 过期数
        Integer expireNum = recommendCardManager.getExpireNum(userId, null, new Date());
        LogContext.addResLog("expireNum={}", expireNum);
        bean.setExpireNum(expireNum);

        // 即将过期数
        if (request.getBeExpireDay() != null) {
            Integer beExpireNum = recommendCardManager.getExpireNum(userId, new Date(), DateUtil.getDayAfter(new Date(), request.getBeExpireDay()));
            LogContext.addResLog("beExpireNum={}", beExpireNum);
            bean.setBeExpireNum(beExpireNum);
        }

        return RpcResult.success(bean);
    }

    @Override
    public Result<PageBean<RecommendCardUseRecordBean>> getUseRecordForManagement(RequestGetUseRecord request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);

        PageBean<RecommendCardUseRecordBean> beanPage = recommendCardManager.getUseRecordForManagement(request);
        if (CollectionUtils.isEmpty(beanPage.getList())) {
            LogContext.addResLog("beanPage is empty");
            return RpcResult.success(PageBean.empty());
        }

        // 查询曝光数据
        List<RecommendCardUseRecordBean> list = beanPage.getList();
        List<Long> recordIds = list.stream().map(RecommendCardUseRecordBean::getId).collect(Collectors.toList());
        List<ResponseRecommendCardStaticsInfo> recommendCardStatics = recommendCardStaticsManager.getRecommendCardStatics(request.getAppId(), recordIds);
        Map<Long, Integer> staticsMap = recommendCardStatics.stream().collect(Collectors.toMap(ResponseRecommendCardStaticsInfo::getId, ResponseRecommendCardStaticsInfo::getUseCount));

        for (RecommendCardUseRecordBean bean : list) {
            // 查询家族信息
            if (bean.getNjId() != null) {
                Optional<FamilyBean> userFamily = familyManager.getUserFamily(bean.getNjId());
                if (userFamily.isPresent()) {
                    bean.setFamilyName(userFamily.get().getFamilyName());
                    bean.setFamilyId(userFamily.get().getId());
                }
            }

            Integer useCount = staticsMap.get(bean.getId());
            bean.setExposure(useCount);
        }

        return RpcResult.success(beanPage);
    }

    @Override
    public Result<PageBean<RecommendCardSendRecordBean>> getSendRecord(RequestGetSendRecord request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);

        PageBean<RecommendCardSendRecordBean> pageBean = recommendCardManager.getSendRecord(request);
        LogContext.addResLog("total={}", pageBean.getTotal());

        // 查询角色和家族信息
        for (RecommendCardSendRecordBean bean : pageBean.getList()) {
            // 角色
            UserInFamilyBean userInFamily = familyManager.getUserInFamily(bean.getUserId());
            bean.setUserRole(getUserRole(userInFamily));

            // 家族信息
            if (userInFamily.getFamilyId() != null) {
                Optional<FamilyBean> familyOp = familyManager.getFamily(request.getAppId(), userInFamily.getFamilyId());
                if (familyOp.isPresent()) {
                    bean.setFamilyName(familyOp.get().getFamilyName());
                }
            }
        }

        return RpcResult.success(pageBean);
    }

    @Override
    public Result<List<BatchSendUserResultBean>> batchSend(RequestBatchSend request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);
        List<BatchSendUserResultBean> inValidateUserList = checkUserValidate(request);
        if (CollectionUtils.isNotEmpty(inValidateUserList)) {
            return RpcResult.success(inValidateUserList);
        }

        List<BatchSendUserResultBean> resultList = recommendCardManager.batchSend(request);
        return RpcResult.success(resultList);
    }

    @Nullable
    private List<BatchSendUserResultBean> checkUserValidate(RequestBatchSend request) {
        List<Long> userIds = request.getSendRecommendCards().stream().map(SendRecommendCardBean::getUserId).distinct().collect(Collectors.toList());
        List<SimpleUserDto> existUsers = userManager.getSimpleUserByIds(userIds);
        if (userIds.size() != existUsers.size()) {
            List<Long> existUserIds = existUsers.stream()
                    .map(SimpleUserDto::getId)
                    .collect(Collectors.toList());
            List<Long> invalidUserIds = userIds.stream()
                    .filter(id -> !existUserIds.contains(id))
                    .collect(Collectors.toList());

            List<BatchSendUserResultBean> resultList = new ArrayList<>();
            for (Long invalidUserId : invalidUserIds) {
                LogContext.addResLog("invalidUserId={}", invalidUserId);
                resultList.add(new BatchSendUserResultBean()
                        .setUserId(invalidUserId)
                        .setResultCode(BATCH_SEND_USER_NOT_VALIDATE)
                        .setMsg("用户不存在"));
            }
            return resultList;
        }
        return Collections.emptyList();
    }

    private String getUserRole(UserInFamilyBean userFamily){
        if (userFamily.isFamily()) {
            return "家族长";
        } else if (userFamily.isRoom()) {
            return "厅主";
        } else if (userFamily.isPlayer()) {
            return "主播";
        } else {
            return "普通用户";
        }
    }

    @Override
    public Result<Void> recycle(RequestRecycle request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);
        return recommendCardManager.recycle(request) ? RpcResult.success() : RpcResult.fail(RECYCLE_FAIL);
    }

    @Override
    public Result<PageBean<RecommendAllocationRecordBean>> getAllocationRecord(RequestGetAllocationRecord request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);
        PageBean<RecommendAllocationRecordDTO> pageBean = recommendCardManager.getAllocationRecord(request);

        List<RecommendAllocationRecordBean> beanList = new ArrayList<>();
        for (RecommendAllocationRecordDTO dto : pageBean.getList()) {
            RecommendAllocationRecordBean bean = new RecommendAllocationRecordBean();
            bean.setDetail(dto.getDetail());
            bean.setAllocationTime(dto.getAllocationTime());
            bean.setNums(dto.getNums());
            beanList.add(bean);
        }

        return RpcResult.success(PageBean.of(pageBean.getTotal(), beanList));
    }

    @Override
    public Result<RewardResultBean> rewardRecommendCard(@Valid RequestRewardRecommendCard request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));

        RecommendCardRewardProcessor rewardProcessor = processorFactory.getProcessor(RecommendCardRewardProcessor.class);
        RewardResultBean result = rewardProcessor.checkCanReward(request.getOperatorUserId(), request.getRewardBeans());
        if (result.getCode() != 0) {
            LogContext.addResLog("checkCode={}", result.getCode());
            return RpcResult.fail(result.getCode(), result.getRewardResult());
        }

        RewardResultBean rewardResultBean = recommendCardManager.rewardRecommendCard(request.getOperatorUserId(), request.getRewardBeans());

        return RpcResult.success(rewardResultBean);
    }

    @Override
    public Result<PageBean<AllocationItemBean>> getAllocationItemList(RequestGetAllocationItemList request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);

        //查询公会签约厅列表
        PageBean<RoomSignBean> roomPageList = roomManager.getAllSingGuildRooms(request.getFamilyId(), request.getPageNo(), request.getPageSize());
        if (CollectionUtils.isEmpty(roomPageList.getList())) {
            LogContext.addResLog("roomPageList is empty");
            return RpcResult.success(PageBean.empty());
        }
        List<Long> roomIds = roomPageList.getList().stream().map(UserBean::getId).collect(Collectors.toList());

        //上周收入
        List<DataRoomFamilyWeekDTO> incomeList = roomDataManager.getRoomWeekData(request.getFamilyId(), roomIds, MyDateUtil.getLastWeekStartDay(), MyDateUtil.getLastWeekEndDay());
        Map<Long, BigDecimal> incomeMap = incomeList.stream().collect(Collectors.toMap(DataRoomFamilyWeekDTO::getRoomId, DataRoomFamilyWeekDTO::getAllIncome));

        //库存
        List<UserRecommendCardStockDTO> stockList = recommendCardManager.getUserStock(roomIds);
        Map<Long, Integer> stockMap = stockList.stream().collect(Collectors.toMap(UserRecommendCardStockDTO::getUserId, UserRecommendCardStockDTO::getStock));

        List<AllocationItemBean> allocationItemBeanList = new ArrayList<>();
        for (RoomSignBean roomSignBean : roomPageList.getList()) {
            AllocationItemBean allocationItemBean = new AllocationItemBean();
            allocationItemBean.setRoom(roomSignBean);

            BigDecimal income = incomeMap.get(roomSignBean.getId());
            if (income != null) {
                allocationItemBean.setIncome(income.intValue());
            }

            Integer stock = stockMap.get(roomSignBean.getId());
            allocationItemBean.setNum(stock == null ? 0 : stock);


            allocationItemBeanList.add(allocationItemBean);
        }

        return RpcResult.success(PageBean.of(roomPageList.getTotal(), allocationItemBeanList));
    }

    @Override
    public Result<PageBean<RecommendCardUseRecordBean>> getFamilyUseRecord(RequestGetFamilyUseRecord request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);

        // 需要查询的厅
        List<Long> queryRoomIds = new ArrayList<>();

        if (request.getNjId() == null) {
            // 查询签约厅数 大于100默认不查询
            int maxCount = resourceConfig.getMaxQueryRecommendCardUseRecordCount();
            PageBean<RoomSignBean> singRoomList = roomManager.getAllSingGuildRooms(request.getFamilyId(), 1, 100);
            if (singRoomList.getTotal() >= maxCount) {
                return RpcResult.fail(GET_FAMILY_USE_RECORD_MAX_ROOM_COUNT);
            }
            List<Long> njIds = singRoomList.getList().stream()
                    .map(UserBean::getId)
                    .collect(Collectors.toList());
            queryRoomIds.addAll(njIds);

            RecommendCardRewardProcessor processor = processorFactory.getProcessor(RecommendCardRewardProcessor.class);
            processor.familyUseRecordQueryUserId(queryRoomIds, request, njIds);
            if (queryRoomIds.size() >= maxCount) {
                return RpcResult.fail(GET_FAMILY_USE_RECORD_MAX_ROOM_COUNT);
            }

        } else {
            queryRoomIds.add(request.getNjId());
        }

        // 查询使用记录
        PageBean<RecommendCardUseRecordDTO> pageBean = recommendCardManager.getUseRecord(new GetUseRecordParamDTO()
                .setUserIds(queryRoomIds)
                .setCreateTimeAsc("asc".equals(request.getUseTimeOrderType()))
                .setPageNumber(request.getPageNo())
                .setPageSize(request.getPageSize())
        );
        if (CollectionUtils.isEmpty(pageBean.getList())) {
            return RpcResult.success(PageBean.empty());
        }

        // 查询曝光数据
        List<Long> recordIds = pageBean.getList().stream().map(RecommendCardUseRecordDTO::getId).collect(Collectors.toList());
        List<ResponseRecommendCardStaticsInfo> staticsList = recommendCardStaticsManager.getRecommendCardStatics(request.getAppId(), recordIds);
        Map<Long, ResponseRecommendCardStaticsInfo> dataMap = staticsList.stream().collect(Collectors.toMap(ResponseRecommendCardStaticsInfo::getId, v -> v));

        // 厅主信息
        Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(queryRoomIds);

        List<RecommendCardUseRecordBean> resultList = RecommendCardConvert.I.useRecordDtos2Beans(pageBean.getList());
        for (RecommendCardUseRecordBean bean : resultList) {
            ResponseRecommendCardStaticsInfo data = dataMap.get(bean.getId());
            if (data != null) {
                bean.setExposureRate(data.getUseCountRate());
            }
            SimpleUserDto njUser = userMap.get(bean.getNjId());
            if (njUser != null) {
                bean.setNjName(njUser.getName());
                bean.setNjBand(njUser.getBand());
            }
        }

        return RpcResult.success(PageBean.of(pageBean.getTotal(), resultList));
    }
}
