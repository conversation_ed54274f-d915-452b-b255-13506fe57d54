package fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager;

import fm.lizhi.ocean.wavecenter.service.background.activitycenter.constants.TemplateStatusTaskStatusEnum;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityTemplateStatusTaskDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/8 17:47
 */
public interface ActivityTemplateStatusTaskManager {

    /**
     * 查询抵达执行时间待执行的任务
     * @param taskNums
     * @return
     */
    List<ActivityTemplateStatusTaskDTO> getWaitingTask(Integer taskNums);

    /**
     * 更新任务状态
     * @param taskId
     * @param taskStatus
     */
    void updateTaskStatus(Long taskId, TemplateStatusTaskStatusEnum taskStatus);

}
