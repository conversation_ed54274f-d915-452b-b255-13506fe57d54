package fm.lizhi.ocean.wavecenter.service.anchor.singer.constant;

/**
 * <AUTHOR>
 */
public class SingerVerifyApplyErrorTipConstant {

    public static final String SINGER_VERIFY_APPLY_IN_BLACK_LIST = "当前唱功仍需提升，请勿重复换号考核";
    public static final String SINGER_VERIFY_APPLY_FAIL = "歌手认证申请失败，请稍候重试！";
    public static final String SINGER_VERIFY_APPLY_HALL_STATUS_PRE_AUDIT_FAIL = "您所在的厅的未申请点唱厅，无法提交申请";
    public static final String SINGER_VERIFY_APPLY_ALREADY_SINGER = "您无法申请认证%s，请确认是否已是歌手或者是否越级申请。";
    public static final String SINGER_VERIFY_APPLY_EXIST_AUDITING_RECORD = "您已提交歌手认证记录，请耐心等待管理员审核！";
    public static final String SINGER_VERIFY_APPLY_TOO_FAST = "您已发起认证申请，请勿重复发起!";
    public static final String SINGER_VERIFY_APPLY_HAS_OTHER_VERIFY = "您的实名信息存在其他认证记录.";
    public static final String SINGER_VERIFY_APPLY_MENU_IS_CLOSE_FAIL = "申请入口已关闭，无法提交认证申请.";
    public static final String SINGER_VERIFY_APPLY_NO_PASS_DAY_LIMIT = "请多加练习好好准备，%s后再来提交";



}
