package fm.lizhi.ocean.wavecenter.service.anchor.singer.manager;

import java.util.Date;
import java.util.List;
import java.util.Map;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerVerifySongInfoBean;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerVerifyApplyDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerVerifyRecordDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.UpdateSingerVerifyStatusParamDTO;
import org.apache.commons.lang3.tuple.Pair;

import javax.annotation.Nullable;

/**
 * 歌手认证申请管理
 */
public interface SingerVerifyApplyManager {

    /**
     * 歌手认证申请
     *
     * @param param 歌手认证申请参数
     * @return 结果，成功返回 true，失败返回 false
     */
    boolean saveSingerVerifyApply(SingerVerifyApplyDTO param);

    /**
     * 获取用户最新的认证记录
     *
     * @param appId  应用ID
     * @param userId 用户ID
     * @return 最新的歌手认证申请记录
     */
    List<SingerVerifyRecordDTO> getSingerVerifyRecordList(int appId, Long userId);

    /**
     * 根据ID列表查询出歌手认证记录
     *
     * @param ids 歌手认证记录ID列表
     * @return 歌手认证记录列表
     */
    List<SingerVerifyRecordDTO> getSingerVerifyRecordListByIds(List<Long> ids);

    /**
     * 审批通过操作
     *
     * @param recordId           认证记录ID
     * @param currentAuditStatus 当前审核状态
     * @param passSingerType     通过的歌手类型
     * @param operator           操作人
     * @return 结果，成功返回 true，失败返回 false
     */
    boolean approveSingerVerifyRecord(Long recordId, Integer currentAuditStatus, Integer passSingerType, String operator);

    /**
     * 修改认证记录状态
     *
     * @param param 修改参数
     * @return 结果
     */
    boolean updateSingerVerifyRecordStatus(UpdateSingerVerifyStatusParamDTO param);

    /**
     * 分页查询歌手认证记录
     *
     * @param appId          应用ID
     * @param userId         用户ID
     * @param njId           厅ID
     * @param singerType     歌手类型
     * @param minApplyTime   最小申请时间
     * @param maxApplyTime   最大申请时间
     * @param songStyle      歌曲风格
     * @param auditStatus    审核状态
     * @param originalSinger 是否原创歌手
     * @param pageNo         页码
     * @param pageSize       每页大小
     * @param orderMetrics
     * @param orderType
     * @return 分页结果
     */
    PageBean<SingerVerifyRecordDTO> pageQuerySingerVerifyRecord(Integer appId, Long userId, Long njId,
                                                                Integer singerType, Long minApplyTime, Long maxApplyTime,
                                                                List<String> songStyle, List<Integer> auditStatus,
                                                                Boolean originalSinger, Integer pageNo, Integer pageSize, String orderMetrics, OrderType orderType);

    /**
     * 分页查询歌手认证记录（包含黑名单）
     *
     * @param appId          应用ID
     * @param userId         用户ID
     * @param njId           厅ID
     * @param singerType     歌手类型
     * @param minApplyTime   最小申请时间
     * @param maxApplyTime   最大申请时间
     * @param songStyle      歌曲风格
     * @param auditStatus    审核状态
     * @param originalSinger 是否原创歌手
     * @param pageNo         页码
     * @param pageSize       每页大小
     * @param orderMetrics
     * @param orderType
     * @return 分页结果
     */
    PageBean<SingerVerifyRecordDTO> pageQuerySingerVerifyRecordWithBlackList(Integer appId, Long userId, Long njId,
                                                                             Integer singerType, Long minApplyTime, Long maxApplyTime,
                                                                             List<String> songStyle, List<Integer> auditStatus,
                                                                             Boolean originalSinger, Integer pageNo, Integer pageSize, String orderMetrics, OrderType orderType);

    /**
     * 修改歌手认证申请备注
     *
     * @param id     主键ID
     * @param remark 备注
     * @return 是否成功
     */
    boolean modifyVerifyApplyRemark(Long id, String remark);

    /**
     * 根据用户ID查询同证件号下的所有认证用户ID
     *
     * @param appId  应用ID
     * @param userId 用户ID
     * @return 同证件号下的所有认证用户ID
     */
    List<Long> getSameIdCardUserIdByUserId(Integer appId, Long userId, Integer singerType);

    /**
     * 按修改时间和审核状态查询认证记录表
     *
     * @param appId         应用ID
     * @param minModifyTime 最小修改时间
     * @param maxModifyTime 最大修改时间
     * @param auditStatus   审核状态
     */
    List<SingerVerifyRecordDTO> getSingerVerifyRecordByTime(Integer appId, Date minModifyTime, Date maxModifyTime, Integer auditStatus);


    List<Long> getSameIdCardUserIdByUserId(Integer appId, Long userId);

    /**
     * 是否存在审核中的记录
     *
     * @param appId  应用ID
     * @param userId 用户ID
     * @return 结果
     */
    @Deprecated
    boolean existAuditingRecord(Integer appId, Long userId, Integer singerType);

    /**
     * 检查历史认证记录结果，判断是否可以申请，这里额外判断了时间
     *
     * @param appId  应用ID
     * @param userId 用户ID
     * @return 结果, left: 结果，right: 不过的文案
     */
    Pair<Boolean, String> checkAuditRecordCanApply(Integer appId, Long userId, Integer singerType);



    /**
     * 检查是否存在有效的认证记录结果，判断是否可以申请
     *
     * @param appId  应用ID
     * @param userId 用户ID
     * @return 结果
     */
    boolean checkAuditEffectiveRecordCanApply(Integer appId, Long userId, Integer singerType);


    /**
     * 检查用户实名信息关联账号是否已经认证过
     *
     * @param appId        应用ID
     * @param idCardNumber 身份证号码
     * @param userId
     * @return 结果
     */
    Boolean checkUserVerify(Integer appId, String idCardNumber, Long userId, @Nullable Integer checkSingerType);

    /**
     * 根据应用ID、用户ID列表，批量查询出用户证件号，结果是map, key: 用户ID, value: 身份证号
     *
     * @param appId   应用ID
     * @param userIds 用户ID列表
     * @return 用户证件号列表
     */
    Map<Long, String> getIdCardNumberMapByUserIds(Integer appId, List<Long> userIds);

    /**
     * 修改歌手认证记录和歌手库家族长ID、厅ID
     *
     * @param appId    应用ID
     * @param singerId 歌手ID
     * @param familyId 家族ID
     * @param njId     厅ID
     * @return 结果
     */
    boolean updateSignInfo(Integer appId, Long singerId, Long familyId, Long njId);

    /**
     * 修改歌手签约信息
     *
     * @param appId    应用ID
     * @param singerId 歌手ID
     * @return 结果
     */
    boolean updateSingerSignInfo(Integer appId, Long singerId);

    /**
     * 根据认证申请ID列表查询出歌曲信息
     *
     * @param applyIds 认证申请ID列表
     * @return 歌曲信息列表
     */
    List<SingerVerifySongInfoBean> getSingerVerifySongInfoList(List<Long> applyIds);

    /**
     * 根据用户ID，歌手类型列表和状态列表，批量查询出认证记录
     *
     * @param userIds 用户ID列表
     * @param singerTypes 歌手类型列表
     * @param statusList 状态列表
     * @return 认证记录列表
     */
    List<SingerVerifyRecordDTO> batchGetSingerVerifyRecordList(int appId, List<Long> userIds, List<Integer> singerTypes, List<Integer> statusList);
}
