package fm.lizhi.ocean.wavecenter.service.datacenter.manager;

import fm.lizhi.ocean.wavecenter.service.datacenter.dto.AccountFlowRuleDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/23 14:40
 */
public interface AccountFlowRuleManager {

    /**
     * 查询账户的结算规则, 没有特定bizId的情况
     * bizId可以理解为场景id
     * @param tenantCode
     * @param accountEngineCode
     * @return
     */
    List<AccountFlowRuleDTO> getAccountEngineCodeRuleIgnoreBizId(String tenantCode, String accountEngineCode);

    /**
     * 查询账户在特定bizId下的结算规则
     * bizId可以理解为场景id
     * @param tenantCode
     * @param accountEngineCode
     * @param bizId
     * @return
     */
    List<AccountFlowRuleDTO> getAccountEngineCodeRule(String tenantCode, String accountEngineCode, Integer bizId);

}
