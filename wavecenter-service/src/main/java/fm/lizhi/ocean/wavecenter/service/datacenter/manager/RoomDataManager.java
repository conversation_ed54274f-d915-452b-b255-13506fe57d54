package fm.lizhi.ocean.wavecenter.service.datacenter.manager;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.CountDataBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.PlayerPerformanceBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomAssessmentInfoBean;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.*;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 签约厅数据查询
 * <AUTHOR>
 * @date 2024/4/17 17:05
 */
public interface RoomDataManager {

    /**
     * 查询考核业绩
     * @param appId
     * @param familyId
     * @param roomId
     * @return
     */
    Optional<RoomAssessmentInfoBean> getAssessmentInfo(int appId, long familyId, long roomId);

    /**
     * 厅数据-主播业绩列表
     * @param paramDto
     * @return
     */
    PageBean<PlayerPerformanceBean> getPlayerPerformance(GetRoomPlayerPerformanceParamDto paramDto);

    /**
     * 查询指定主播业绩
     * @param appId
     * @param playerId
     * @param assessTimeDto
     * @return
     */
    List<PlayerPerformanceBean> getPlayerPerformanceList(int appId, long familyId, long roomId, List<Long> playerId, AssessTimeDto assessTimeDto);

    /**
     * 查询厅日指标数据
     * @param appId
     * @param roomId
     * @param date
     * @return key=MetricsMeta#name, value=指标值字符串
     */
    Map<String, String> getRoomDayKeyIndicators(int appId, Long familyId, long roomId, Date date, List<String> valueMetrics);

    /**
     * 查询多个厅日指标数据合计
     * @param appId
     * @param familyId
     * @param roomIds
     * @param date
     * @param valueMetrics
     * @return
     */
    Map<String, String> getRoomDayKeyIndicatorsSum(int appId, Long familyId, List<Long> roomIds, Date date, List<String> valueMetrics);

    /**
     * 查询多个厅日指标数据合计
     * @param familyId
     * @param roomIds
     * @param dayValues
     * @param valueMetrics
     * @return
     */
    List<DataRoomFamilyDayDTO> getRoomDayKeyIndicatorsSum(Long familyId, List<Long> roomIds, List<Integer> dayValues, List<String> valueMetrics);

    /**
     * 查询厅周指标数据
     * @param appId
     * @param roomId
     * @param startDate
     * @param endDate
     * @return key=MetricsMeta#name, value=指标值字符串
     */
    Map<String, String> getRoomWeekKeyIndicators(int appId, Long familyId, long roomId, Date startDate, Date endDate, List<String> valueMetrics);

    /**
     * 查询多个厅周指标数据合计
     * @param familyId
     * @param roomIds
     * @param startDate
     * @param endDate
     * @param valueMetrics
     * @return
     */
    Map<String, String> getRoomWeekKeyIndicatorsSum(Long familyId, List<Long> roomIds, Date startDate, Date endDate, List<String> valueMetrics);

    /**
     * 查询厅月指标数据
     * @param appId
     * @param roomId
     * @param monthDate
     * @return key=MetricsMeta#name, value=指标值字符串
     */
    Map<String, String> getRoomMonthKeyIndicators(int appId, Long familyId, long roomId, Date monthDate, List<String> valueMetrics);

    /**
     * 查询多个厅月指标数据合计
     * @param familyId
     * @param roomIds
     * @param monthDate
     * @param valueMetrics
     * @return
     */
    Map<String, String> getRoomMonthKeyIndicatorsSum(Long familyId, List<Long> roomIds, Date monthDate, List<String> valueMetrics);

    /**
     * 查询指定天数的指标数据
     * @param roomId
     * @param metric
     * @param days
     * @return
     */
    List<CountDataBean> getIndicatorTrend(Long familyId, long roomId, String metric, int days);


    /**
     * 查询指定日期的指标数据
     */
    List<DataRoomFamilyDayDTO> getRoomDayData(Integer appId, Long familyId, Date startDate, Date endDate);
    List<DataRoomFamilyDayDTO> getRoomDayData(Integer appId, Long familyId, Long roomId, Date startDate, Date endDate);

    /**
     * 查询指定日期的指标数据
     * @param familyId
     * @param roomIds
     * @param day
     * @return
     */
    List<DataRoomFamilyDayDTO> getRoomDayData(Long familyId, List<Long> roomIds, Date day);

    /**
     * 查询指定周的指标数据
     * @param familyId
     * @param roomIds
     * @param startDay
     * @param endDay
     * @return
     */
    List<DataRoomFamilyWeekDTO> getRoomWeekData(Long familyId, List<Long> roomIds, Date startDay, Date endDay);

    /**
     * 查询指定月份的指标数据
     * @param familyId
     * @param roomIds
     * @param month
     * @return
     */
    List<DataRoomFamilyMonthDTO> getRoomMonthData(Long familyId, List<Long> roomIds, Date month);

    /**
     * 查询厅工会维度日统计数据
     * @param param
     * @return
     */
    List<DataRoomFamilyDayDTO> getRoomFamilyDayList(GetRoomDayListParam param);

    /**
     * 查询厅日统计数据
     * @param param
     * @return
     */
    List<DataRoomDayDTO> getRoomDayList(GetRoomDayListParam param);


    /**
     *连 续天数有总收入的PGC厅
     * @param param
     * @return
     */
    List<Long> getHasIncomeRoomIdList(GetHasIncomeRoomsParam param);

    /**
     * 查询厅某周期收入概览统计
     * @param param 查询参数
     * @return 分页结果
     */
    PageBean<RoomIncomeStatDTO> queryRoomIncomeStats(RoomIncomeStatParamDTO param);
}
