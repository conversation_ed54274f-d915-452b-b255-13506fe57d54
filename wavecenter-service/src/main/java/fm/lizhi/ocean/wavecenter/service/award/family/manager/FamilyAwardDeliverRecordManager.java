package fm.lizhi.ocean.wavecenter.service.award.family.manager;

import fm.lizhi.ocean.wavecenter.common.dto.PageDto;
import fm.lizhi.ocean.wavecenter.service.award.family.dto.FamilyAwardDeliverItemDTO;
import fm.lizhi.ocean.wavecenter.service.award.family.dto.FamilyAwardDeliverRecordDTO;
import fm.lizhi.ocean.wavecenter.service.award.family.dto.GetFamilyAwardDeliverRecordParamDTO;

import java.util.List;

public interface FamilyAwardDeliverRecordManager {

    /**
     * 分页查询奖励发放记录
     * @param param
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageDto<FamilyAwardDeliverRecordDTO> getFamilyRecord(GetFamilyAwardDeliverRecordParamDTO param, int pageNum, int pageSize);

    /**
     * 查询奖励发放记录的奖励发放明细
     * @param awardDeliverRecordIds
     * @return
     */
    List<FamilyAwardDeliverItemDTO> getFamilyAwardDeliverItem(List<Long> awardDeliverRecordIds);

}
