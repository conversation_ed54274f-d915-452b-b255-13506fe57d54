package fm.lizhi.ocean.wavecenter.service.anchor.singer.dto;

import java.util.List;

import lombok.Data;

@Data
public class SingerVerifyApplyDTO {

    /**
     * 厅主ID
     */
    private Long njId;

    /**
     * 家族ID
     */
    private Long familyId;


    /**
     * 是否为原唱
     */
    private Boolean originalSinger;

    /**
     * 原唱链接
     */
    private String originalSongUrl;

    /**
     * 社交认证图片列表，最多三个
     */
    private List<String> socialVerifyImageList;

    /**
     * 歌手类型
     */
    private Integer singerType;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 实名证件号
     */
    private String idCardNumber;

    /**
     * 审核状态
     */
    private Integer auditStatus;

    /**
     * 预审核不过原因
     */
    private String preAuditRejectReason;


    /**
     * 不通过原因
     */
    private String rejectReason;

    /**
     * 歌曲信息列表
     */
    private List<SongInfoDTO> songInfos;

    /**
     * 联系方式
     */
    private String contactNumber;

}
