package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class ResourceGiveResultDTO {

    /**
     * 成功的话，
     */
    private boolean success;

    /**
     * 资源
     */
    private List<ResourceResultDetailBean> errorList;

    @Data
    @Accessors(chain = true)
    public static class ResourceResultDetailBean {
        /**
         * 资源ID
         */
        private Long id;

        /**
         * 资源名称
         */
        private String name;

        /**
         * 错误消息
         */
        private String errorMessage;
    }

    public static ResourceGiveResultDTO fail() {
        return new ResourceGiveResultDTO().setSuccess(false);
    }

    public static ResourceGiveResultDTO success() {
        return new ResourceGiveResultDTO().setSuccess(true);
    }
}
