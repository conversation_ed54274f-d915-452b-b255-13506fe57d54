package fm.lizhi.ocean.wavecenter.service.activitycenter.constatns;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 歌手数量统计维度
 */
@Getter
@AllArgsConstructor
public enum SingerCountMetricEnum {
    /**
     * 生效中
     */
    EFFECTING(1),


    /**
     * 认证中
     */
    AUTHENTICATING(2),

    /**
     * 审核中(歌手库-认证中 + 歌手认证库-待审核、待定、选中 )
     */
    IN_AUDIT(3),
    ;

    private final int status;
}
