package fm.lizhi.ocean.wavecenter.service.anchor.singer.service;

import cn.hutool.core.collection.CollUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeMappingEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.*;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseGetAllSingerStatics;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponsePageSingerInfo;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseSingerRoomDetails;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.service.SingerInfoAdminService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.RedisLock;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.convert.SingerInfoConvert;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.convert.SingerRoomDetailConvert;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerInfoDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerRoomDetailDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerInfoManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerRedisManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerWhiteListManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.processor.ISingerProcessor;
import fm.lizhi.ocean.wavecenter.base.processor.ProcessorFactory;
import fm.lizhi.ocean.wavecenter.service.message.manager.SingerPushManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

@ServiceProvider
@Slf4j
public class SingerInfoAdminServiceImpl implements SingerInfoAdminService {

    @Autowired
    private SingerInfoManager singerInfoManager;

    @Autowired
    private UserManager userManager;

    @Autowired
    private FamilyManager familyManager;

    @Autowired
    private ProcessorFactory processorFactory;

    @Autowired
    private SingerPushManager singerPushManager;

    @Autowired
    private SingerRedisManager singerRedisManager;

    @Autowired
    private SingerWhiteListManager singerWhiteListManager;

    @Override
    public Result<PageBean<ResponseSingerRoomDetails>> singerRoomDetails(RequestSingerRoomDetails request) {
        // 添加日志记录
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);

        // 处理njBand查询
        if (StringUtils.isNotEmpty(request.getNjBand())) {
            Long njId = userManager.getUserIdByBand(request.getNjBand());
            request.setNjId(njId == null ? 1 : njId);
        }

        // 调用Manager层获取分页数据
        PageBean<SingerRoomDetailDTO> pageBean = singerInfoManager.singerRoomDetails(request.getAppId(), request.getNjId(),
                request.getPageNo(), request.getPageSize(), request.getOrderMetrics(), request.getOrderType());

        if (pageBean == null || pageBean.getList() == null || pageBean.getList().isEmpty()) {
            return RpcResult.success(PageBean.empty());
        }

        Map<Long, FamilyBean> familyMap = getFamilyMap(pageBean);

        Map<Long, SimpleUserDto> userMap = getUserMap(pageBean);

        // 转换响应结果
        List<ResponseSingerRoomDetails> responseList = SingerRoomDetailConvert.INSTANCE.convertToResponseList(pageBean.getList(), userMap, familyMap);

        // 构建分页结果
        PageBean<ResponseSingerRoomDetails> result = PageBean.of(pageBean.getTotal(), responseList);

        return RpcResult.success(result);
    }

    private Map<Long, SimpleUserDto> getUserMap(PageBean<SingerRoomDetailDTO> pageBean) {
        // 收集所有需要查询的厅主ID
        List<Long> njIds = pageBean.getList().stream()
                .map(SingerRoomDetailDTO::getNjId)
                .filter(id -> id != null)
                .distinct()
                .collect(Collectors.toList());
        // 查询厅主信息
        Map<Long, SimpleUserDto> userMap = new HashMap<>();
        if (!njIds.isEmpty()) {
            userMap = userManager.getSimpleUserMapByIds(njIds);
        }
        return userMap;
    }

    private Map<Long, FamilyBean> getFamilyMap(PageBean<SingerRoomDetailDTO> pageBean) {
        // 收集所有需要查询的家族ID
        List<Long> familyIds = pageBean.getList().stream()
                .map(SingerRoomDetailDTO::getFamilyId)
                .filter(id -> id != null)
                .distinct()
                .collect(Collectors.toList());

        // 查询家族信息
        Map<Long, FamilyBean> familyMap = new HashMap<>();
        for (Long familyId : familyIds) {
            Optional<FamilyBean> familyInfo = familyManager.getFamilyByCache(familyId);
            familyInfo.ifPresent(family -> familyMap.put(familyId, family));
        }
        return familyMap;
    }

    @Override
    public Result<ResponseGetAllSingerStatics> getAllSingerStatics(RequestGetAllSingerStatics request) {
        // 添加日志记录
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);

        // 调用Manager层获取全平台歌手汇总信息
        ResponseGetAllSingerStatics result = singerInfoManager.getAllSingerStatics(request.getAppId());

        return RpcResult.success(result);
    }

    @Override
    public Result<PageBean<ResponsePageSingerInfo>> pageSingerInfo(RequestPageSingerInfo request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);

        // 处理njBand查询
        if (StringUtils.isNotEmpty(request.getNjBand())) {
            Long njId = userManager.getUserIdByBand(request.getNjBand());
            request.setNjId(njId == null ? 1 : njId);
        }

        // 处理singerBand查询
        if (StringUtils.isNotEmpty(request.getSingerBand())) {
            Long singerId = userManager.getUserIdByBand(request.getSingerBand());
            request.setUserId(singerId == null ? 1 : singerId);
        }

        // 分页获取数据
        PageBean<SingerInfoDTO> pageBean = singerInfoManager.pageSingerInfo(SingerInfoConvert.INSTANCE.convertPageSingerInfoParamDTO(request));
        if (CollUtil.isEmpty(pageBean.getList())) {
            return RpcResult.success(PageBean.empty());
        }

        List<SingerInfoDTO> list = pageBean.getList();

        // 收集家族ID
        List<Long> familyIds = list.stream().map(SingerInfoDTO::getFamilyId).collect(Collectors.toList());

        // 合并去重歌手&厅主ID
        List<Long> singerIds = list.stream().map(SingerInfoDTO::getUserId).collect(Collectors.toList());
        List<Long> njIds = list.stream().map(SingerInfoDTO::getNjId).collect(Collectors.toList());
        CollUtil.addAllIfNotContains(singerIds, njIds);

        // 查询厅主&歌手信息
        Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(singerIds);

        // 查询家族信息
        Map<Long, FamilyBean> familyMap = new HashMap<>();
        for (Long familyId : familyIds) {
            Optional<FamilyBean> familyInfo = familyManager.getFamilyByCache(familyId);
            familyInfo.ifPresent(family -> familyMap.put(familyId, family));
        }
        // 查询白名单
        List<Long> whiteList = singerWhiteListManager.filterSingerWhiteList(request.foundIdAppId(), singerIds, request.getSingerType());

        for (Long familyId : familyIds) {
            Optional<FamilyBean> familyInfo = familyManager.getFamilyByCache(familyId);
            familyInfo.ifPresent(family -> familyMap.put(familyId, family));
        }

        // 转换响应结果
        List<ResponsePageSingerInfo> responsePageSingerInfoList = SingerInfoConvert.INSTANCE.convertResponsePageSingerInfoList(list, userMap, familyMap, whiteList);
        return RpcResult.success(PageBean.of(pageBean.getTotal(), responsePageSingerInfoList));
    }

    @Override
    public Result<Void> eliminateSinger(RequestEliminateSinger request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);
        Boolean success = singerInfoManager.eliminateSinger(request.getAppId(), request.getIds(), request.getOperator(), request.getEliminateReason(), true);
        return success ? RpcResult.success() : RpcResult.fail(ELIMINATE_SINGER_FAIL, "淘汰歌手失败");
    }

    @Override
    public Result<Void> upgradeSinger(RequestUpgradeSinger request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);


        int appId = request.getAppId();
        List<SingerInfoDTO> singerInfoList = singerInfoManager.getSingerInfoByIds(appId, request.getIds());
        if (CollUtil.isEmpty(singerInfoList)) {
            log.info("singer info list is empty. ids:{}", request.getIds());
            return RpcResult.success();
        }

        List<Long> eliminatedUserIds = singerInfoList.stream()
                .filter(info -> SingerStatusEnum.ELIMINATED.getStatus() == info.getSingerStatus())
                .map(SingerInfoDTO::getUserId).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(eliminatedUserIds)) {
            log.info("singer is eliminated. ids:{}", eliminatedUserIds);
            return RpcResult.fail(UPGRADE_SINGER_FAIL, "存在已淘汰的歌手:" + CollUtil.join(eliminatedUserIds, ","));
        }

        // 认证歌手无法晋升
        List<Long> newSingerUserIds = singerInfoList.stream()
                .filter(info -> SingerTypeEnum.NEW.getType() == info.getSingerType())
                .map(SingerInfoDTO::getUserId).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(newSingerUserIds)) {
            log.info("singer is new. ids:{}", newSingerUserIds);
            return RpcResult.fail(UPGRADE_SINGER_FAIL,
                    String.format("存在%s，无法晋升. 歌手ID:%s",
                            SingerTypeMappingEnum.getBizSingerType(BusinessEvnEnum.from(appId), SingerTypeEnum.NEW),
                            CollUtil.join(newSingerUserIds, ",")
                    )
            );
        }

        // 检查是否存在更高等级歌手
        List<SingerInfoDTO> hasNextSingerInfoList = singerInfoManager.getSingerInfoByUserIds(
                singerInfoList.stream().map(SingerInfoDTO::getUserId).collect(Collectors.toList()),
                appId,
                CollUtil.newArrayList(SingerStatusEnum.EFFECTIVE, SingerStatusEnum.AUTHENTICATING),
                request.getSingerType()
        );
        if (CollUtil.isNotEmpty(hasNextSingerInfoList)) {
            List<Long> nextSingerIds = hasNextSingerInfoList.stream().map(SingerInfoDTO::getUserId).distinct().collect(Collectors.toList());
            log.info("has next singer. userIds:{}", nextSingerIds);
            return RpcResult.fail(UPGRADE_SINGER_FAIL,
                    String.format("存在更高等级歌手，无法晋升. 歌手ID:%s",
                            CollUtil.join(nextSingerIds, ",")
                    )
            );
        }

        // 将singerInfoList按 njId 分组，并进行加锁，调用
        Map<Long, List<SingerInfoDTO>> njIdToSingerInfoMap = singerInfoList.stream()
                .collect(Collectors.groupingBy(SingerInfoDTO::getNjId));

        boolean allSuccess = true;
        for (Map.Entry<Long, List<SingerInfoDTO>> entry : njIdToSingerInfoMap.entrySet()) {
            Long njId = entry.getKey();
            List<SingerInfoDTO> singerInfosInNj = entry.getValue();

            try (RedisLock lock = singerRedisManager.tryGetSingerUpdateLock(appId, njId)) {
                if (!lock.tryLock()) {
                    log.warn("upgradeSinger lock failed. njId:{}, appId:{}", njId, appId);
                    allSuccess = false;
                    continue;
                }

                Boolean success = singerInfoManager.upgradeSinger(appId,
                        singerInfosInNj.stream().map(SingerInfoDTO::getId).collect(Collectors.toList()),
                        request.getSingerType(), request.getOperator()
                );
                if (success) {
                    List<Long> singerIds = singerInfosInNj.stream().map(SingerInfoDTO::getUserId).collect(Collectors.toList());
                    singerPushManager.batchPushVerifyStatusChange(appId, singerIds);
                }

                if (!success) {
                    allSuccess = false;
                }
            } catch (Exception e) {
                log.error("eliminateSinger lock error. njId:{}, appId:{}", njId, appId, e);
                allSuccess = false;
            }
        }

        return allSuccess ? RpcResult.success() : RpcResult.fail(UPGRADE_SINGER_FAIL, "晋升歌手失败");
    }


    /**
     * 判断是否存在认证中的歌手记录
     */
    private List<Long> checkHasAuthenticating(List<Long> userIds, int appId) {
        List<SingerInfoDTO> authenticatingSingerList = singerInfoManager.getSingerInfoByUserIds(userIds, appId, CollUtil.newArrayList(SingerStatusEnum.AUTHENTICATING));
        if (CollUtil.isNotEmpty(authenticatingSingerList)) {
            return authenticatingSingerList.stream().map(SingerInfoDTO::getUserId).collect(Collectors.toList());
        }

        return null;
    }
}
