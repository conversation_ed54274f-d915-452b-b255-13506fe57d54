package fm.lizhi.ocean.wavecenter.service.activitycenter.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.google.common.base.Joiner;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.*;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.*;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestGetInTimeRangeActivityApply;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseActivityInfoDetail;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityApplyService;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityClassificationConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityLevelConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseActivityResource;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.live.constants.RoomCategoryEnum;
import fm.lizhi.ocean.wavecenter.api.live.constants.RoomCategoryOptionConstants;
import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wavecenter.base.constants.TimeConstant;
import fm.lizhi.ocean.wavecenter.common.utils.RedisLock;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.ActivityConfig;
import fm.lizhi.ocean.wavecenter.service.activitycenter.constatns.ActivityApplyErrorTipConstant;
import fm.lizhi.ocean.wavecenter.service.activitycenter.convert.ActivityApplyConvert;
import fm.lizhi.ocean.wavecenter.service.activitycenter.convert.ActivityApplyConverter;
import fm.lizhi.ocean.wavecenter.service.activitycenter.convert.ActivityApplyMappingConvert;
import fm.lizhi.ocean.wavecenter.service.activitycenter.convert.ActivityParamConvert;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.*;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.*;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.*;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityResourceSimpleInfoDTO;
import fm.lizhi.ocean.wavecenter.service.live.manager.LiveRoomManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.user.convert.UserCommonConvert;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 活动申请服务实现
 */
@Slf4j
@ServiceProvider
public class ActivityApplyServiceImpl implements ActivityApplyService {

    private static final int ACTIVITY_DELETE_STATUS = 1;

    private static final int MAX_CLASS_ID_SIZE = 500;


    @Autowired
    private ActivityTemplateManager activityTemplateManager;

    @Autowired
    private ActivityApplyManager activityApplyManager;

    @Autowired
    private ActivityConfig config;

    @Autowired
    private ActivityRedisManager activityOperationRedisManager;

    @Autowired
    private ActivityApplyConverter activityApplyConvert;

    @Autowired
    private ActivityClassificationManager activityClassificationManager;

    @Autowired
    private ActivityToolsManager activityToolManager;

    @Autowired
    private CommonConfig commonConfig;

    @Autowired
    private UserManager userManager;

    @Autowired
    private ActivityDecorateManager activityDecorateManager;

    @Autowired
    private ActivityMaterielGiveManager activityMaterielGiveManager;

    @Autowired
    private ActivityResourceManager activityResourceManager;

    @Autowired
    private ActivityLevelManager activityLevelManager;

    @Autowired
    private FamilyManager familyManager;

    @Autowired
    private ActivityRedisManager activityApplyRedisManager;

    @Autowired
    private ActivityApplyCheckManager activityApplyCheckManager;

    @Autowired
    private ActivityTemplateForCenterManager activityTemplateForCenterManager;

    @Autowired
    private ActivityChatManager activityChatManager;

    @Autowired
    private ActivityAdminOperateManager activityAdminOperateManager;

    @Autowired
    private ActivityInfoQueryManager activityInfoQueryManager;

    @Autowired
    private LiveRoomManager liveRoomManager;

    @Override
    public Result<Void> activityApply(RequestActivityApplyBean paramBean) {
        LogContext.addReqLog("activityApply.paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("activityApply.paramBean={}", JsonUtil.dumps(paramBean));
        //各种校验：参数校验、官频位校验、幂等校验
        return ResultHandler.handle(paramBean.getAppId(), () -> {

            // 检查模板
            Optional<ActivityTemplateBaseInfoDTO> templateBaseInfoOp = activityTemplateForCenterManager.getTemplateBaseInfo(paramBean.getTemplateId());
            if (!templateBaseInfoOp.isPresent()) {
                return RpcResult.fail(ACTIVITY_APPLY_PARAM_ERROR);
            }
            ActivityTemplateBaseInfoDTO templateInfo = templateBaseInfoOp.get();

            if (paramBean.getApplyType() == ActivityApplyTypeEnum.NJ_APPLY) {
                if (!ActivityTemplateStatusEnum.ON_SHELF.getStatus().equals(templateInfo.getStatus())) {
                    return RpcResult.fail(ACTIVITY_APPLY_STATUS_ERROR, "该活动模板已下架，请选择其他模板。");
                }

                // 如果模板存在白名单，则校验白名单
                if (!checkWhitelist(paramBean.getTemplateId(), paramBean.getNjId())) {
                    return RpcResult.fail(ACTIVITY_APPLY_WHITELIST_ERROR, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_WHITELIST_ERROR);
                }

                // 校验模板时间和活动时间限制
                if (!checkTemplateDurationLimit(templateInfo, paramBean.getStartTime(), paramBean.getEndTime())){
                    return RpcResult.fail(ACTIVITY_APPLY_TEMPLATE_DURATION_LIMIT, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_TEMPLATE_DURATION_LIMIT);
                }

                // 品类检查
                Pair<Boolean, String> categoryCheck = categoryCheck(templateInfo, paramBean.getNjId(), paramBean.getAppId());
                if (!categoryCheck.getLeft()) {
                    return RpcResult.fail(ACTIVITY_APPLY_CATEGORY_ERROR, "您无法使用该品类（"+categoryCheck.getRight()+"）活动模板，请选择其他模板。");
                }
            }

            // 加用户级别分布式锁
            try (RedisLock lock = activityOperationRedisManager.getApplyLock(paramBean.getAppId(), paramBean.getNjId())) {
                if (!lock.tryLock()) {
                    log.warn("applyLock error, applicantUid:{}", paramBean.getNjId());
                    return RpcResult.fail(ACTIVITY_APPLY_REQ_TOO_FAST, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_REQ_TOO_FAST_INVALID);
                }

                ActivityParamDTO activityParamDTO = ActivityParamConvert.INSTANCE.convertApplyBeanToDTO(paramBean);

                //最先黑名单校验
                Result<Void> result = activityApplyCheckManager.preBlackFilter(activityParamDTO);
                if (RpcResult.isFail(result)) {
                    log.warn("applyCheck.preBlackFilter fail, paramBean:{}, errorMsg:{}", paramBean, result.getMessage());
                    return result;
                }

                Result<Void> checkRes = activityApplyCheckManager.applyCheck(activityParamDTO);
                if (RpcResult.isFail(checkRes)) {
                    return checkRes;
                }

                Result<Long> checkFamilyIdRes = activityApplyCheckManager.checkAndGetFamilyId(activityParamDTO.getNjId(), activityParamDTO.getApplyType());
                if (RpcResult.isFail(checkFamilyIdRes)) {
                    return RpcResult.fail(checkFamilyIdRes.rCode(), checkFamilyIdRes.getMessage());
                }

                //提报规则过滤
                Result<Void> ruleFilterRes = activityApplyCheckManager.applyRuleFilter(activityParamDTO);
                if (RpcResult.isFail(ruleFilterRes)) {
                    return RpcResult.fail(ruleFilterRes.rCode(), ruleFilterRes.getMessage());
                }

                //内容数据审核校
                Result<Void> auditRes = activityApplyCheckManager.checkActivityApplyData(activityParamDTO);
                if (RpcResult.isFail(auditRes)) {
                    return RpcResult.fail(auditRes.rCode(), auditRes.getMessage());
                }

                // 构建落库数据
                ActivityApplyParamDTO applyParamDTO = activityApplyConvert.buildApplyParam(activityParamDTO, checkFamilyIdRes.target());
                // 数据库操作
                Result<Boolean> applyRes = activityApplyManager.activityApplyDateSave(applyParamDTO);
                log.info("activityApplyDataSave, resCode:{}, param:{}", applyRes.rCode(), JsonUtil.dumps(applyParamDTO));
                if (applyRes.rCode() == 0) {
                    return RpcResult.success();
                }
                return RpcResult.fail(ACTIVITY_APPLY_FAIL, applyRes.getMessage());
            } catch (Exception e) {
                log.error("activityApply happen error: ", e);
            }
            return RpcResult.fail(ACTIVITY_APPLY_FAIL, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_FAIL);
        });
    }

    /**
     * 品类检查
     * @param templateInfo
     * @param njId
     * @param appId
     * @return
     */
    private Pair<Boolean, String> categoryCheck(ActivityTemplateBaseInfoDTO templateInfo, Long njId, int appId){
        List<Integer> tempCategorys = activityClassificationManager.getClassCategoryValue(appId, templateInfo.getClassId());
        if (CollectionUtils.isEmpty(tempCategorys)) {
            return Pair.of(true, "");
        }
        if (tempCategorys.contains(RoomCategoryOptionConstants.UNLIMITED_VALUE)) {
            return Pair.of(true, "");
        }

        Optional<RoomCategoryEnum> roomCategoryOp = liveRoomManager.getUserRoomCategory(njId);
        if (!roomCategoryOp.isPresent()) {
            return Pair.of(false, getCategoryNames(tempCategorys));
        }
        if (tempCategorys.contains(roomCategoryOp.get().getValue())) {
            return Pair.of(true, "");
        }

        return Pair.of(false, getCategoryNames(tempCategorys));
    }

    private String getCategoryNames(List<Integer> categoryValues){
        List<String> names = new ArrayList<>();
        for (RoomCategoryEnum value : RoomCategoryEnum.values()) {
            if (categoryValues.contains(value.getValue())) {
                names.add(value.getDisplayName());
            }
        }
        return Joiner.on(",").join(names);
    }

    /**
     * 校验模板时间和活动时间限制
     * @param templateInfo
     * @param startTime
     * @param endTime
     * @return true 符合时间限制
     */
    private boolean checkTemplateDurationLimit(ActivityTemplateBaseInfoDTO templateInfo, Long startTime, Long endTime) {
        if (templateInfo.getActivityStartTimeLimit() != null && templateInfo.getActivityEndTimeLimit() != null){
            boolean isInStartTime = DateUtil.isIn(new Date(startTime), templateInfo.getActivityStartTimeLimit(), templateInfo.getActivityEndTimeLimit());
            boolean isInEndTime = DateUtil.isIn(new Date(endTime), templateInfo.getActivityStartTimeLimit(), templateInfo.getActivityEndTimeLimit());
            return isInStartTime && isInEndTime;
        }

        long minuteBetween = DateUtil.between(new Date(startTime), new Date(endTime), DateUnit.MINUTE);
        if (templateInfo.getActivityDurationLimit() != null ){
            return minuteBetween == templateInfo.getActivityDurationLimit();
        }
        return true;
    }

    /**
     * 白名单检查
     * @param templateId
     * @return
     */
    private boolean checkWhitelist(Long templateId, Long njId){
        List<Long> templateNjWhitelist = activityTemplateManager.getTemplateNjWhitelist(templateId);
        if (CollectionUtils.isNotEmpty(templateNjWhitelist)) {
            return templateNjWhitelist.contains(njId);
        }
        return true;
    }

    @Override
    public Result<ResponseQueryActivityListBean> queryActivityList(RequestQueryActivityListBean param) {
        return ResultHandler.handle(param.getAppId(), () -> {
            // 时间参数校验
            Result<Void> validateResult = validateQueryActivityListTime(param);
            if (RpcResult.isFail(validateResult)) {
                return RpcResult.fail(validateResult.rCode(), validateResult.getMessage());
            }

            ActivitySimpleQueryParamDTO paramDTO = ActivityApplyMappingConvert.I.queryActivityListReq2DTO(param);

            // 构建分类查询条件
            List<Long> classQueryIds = buildClassificationQueryIds(param.getLevelId(), param.getClassId());
            if ((param.getLevelId() != null || param.getClassId() != null) && CollUtil.isEmpty(classQueryIds)) {
                // 如果指定了等级或分类但未找到匹配项,直接返回空结果
                return RpcResult.success(new ResponseQueryActivityListBean().setList(Collections.emptyList()).setTotal(0));
            }

            paramDTO.setClassIds(classQueryIds);

            PageBean<ActivitySimpleInfoDT0> infoList = activityInfoQueryManager.queryActivityInfoList(paramDTO);
            if (CollectionUtils.isEmpty(infoList.getList())) {
                return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, new ResponseQueryActivityListBean().setList(Collections.emptyList()).setTotal(0));
            }

            //查询出活动分类
            List<Long> classIds = infoList.getList().stream().map(ActivitySimpleInfoDT0::getClassId).collect(Collectors.toList());
            Map<Long, ActivityClassificationConfigBean> classificationBeanMap = getClassificationBeanMap(classIds);
            //查询等级
            Map<Long, String> levleMap = getLevelMap(param.getAppId(), classificationBeanMap);
            //查询模板
            Map<Long, ActivityTemplateInfoDTO> templateMap = getTemplateMap(infoList.getList());
            List<Long> userIds = infoList.getList().stream()
                .flatMap(info -> Stream.of(info.getHostId(), info.getApplicantUid()))
                .distinct()
                .collect(Collectors.toList());
            Map<Long, SimpleUserDto> userInfoMap = userManager.getSimpleUserMapByIds(userIds);
            //数据转换
            ResponseQueryActivityListBean bean = activityApplyConvert.buildQueryActivityListResult(infoList, classificationBeanMap, userInfoMap, levleMap, templateMap);
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, bean);
        });
    }

    private Map<Long, ActivityTemplateInfoDTO> getTemplateMap(List<ActivitySimpleInfoDT0> activityList) {
        List<Long> activityIds = activityList.stream().map(ActivitySimpleInfoDT0::getId).collect(Collectors.toList());
        List<ActivityTemplateInfoDTO> templateList = activityTemplateForCenterManager.getActivityTemplateInfoList(activityIds);
        return templateList.stream().collect(Collectors.toMap(ActivityTemplateInfoDTO::getActivityId, Function.identity(), (a, b) -> a));
    }

    @Override
    public Result<Void> deleteActivityApply(Long activityId, int appId) {
        LogContext.addReqLog("deleteActivityApply activityId={},appId={}", activityId, appId);
        LogContext.addResLog("deleteActivityApply activityId={},appId={}", activityId, appId);
        return ResultHandler.handle(appId, () -> {
            if (activityId == null || activityId <= 0) {
                log.warn("deleteActivityApply param error, activityId:{}", activityId);
                return RpcResult.fail(DELETE_ACTIVITY_APPLY_PARAM_ERROR);
            }
            Boolean success = activityApplyManager.deleteActivityApply(activityId);
            return success ? RpcResult.success() : RpcResult.fail(DELETE_ACTIVITY_APPLY_FAIL, "删除活动失败");
        });
    }


    @Override
    public Result<ResponseActivityInfoDetail> queryActivityInfoDetail(Long activityId, int appId) {

        return ResultHandler.handle(appId, () -> {
            ActivityInfoDTO activityInfo = activityApplyManager.getActivityInfoById(activityId);

            if (Objects.isNull(activityInfo)) {
                log.warn("query activity detail is null, activityId:{}, appId:{}", activityId, appId);
                return RpcResult.fail(QUERY_ACTIVITY_INFO_DETAIL_PARAM_ERROR, "活动不存在");
            }

            ResponseActivityInfoDetail detail = ActivityApplyConvert.I.convertResponseActivityInfoDetail(activityInfo);
            //根据活动时间设置活动状态
            detail.setActivityStatus(getActivityStatus(activityInfo.getStartTime(), activityInfo.getEndTime()));


            // 查询模板封面
            Long templateId = activityApplyManager.getActivityTemplateIdByActivityId(activityId);
            if (templateId != null) {
                detail.setTemplateId(templateId);
                ActivityTemplateInfoBean templateInfoBean = activityTemplateManager.getTemplateInfoBean(templateId);
                if (templateInfoBean != null) {
                    detail.setCover(templateInfoBean.getCover());
                }
            } else {
                log.warn("query activity templateId is null, activityId:{}, appId:{}", activityId, appId);
            }

            // 查询分类&大类
            ActivityClassificationConfigBean classification = activityClassificationManager.getActivityClassification(detail.getClassId());
            if (Objects.nonNull(classification)) {
                detail.setClassName(classification.getClassName());
                detail.setBigClassId(classification.getBigClassId());
                detail.setBigClassName(classification.getBigClassName());
                detail.setBigClassType(classification.getBigClassType());
            }

            // 查询活动工具
            if (StrUtil.isNotBlank(activityInfo.getActivityTool())) {
                List<Integer> toolTypes = StrUtil.splitTrim(activityInfo.getActivityTool(), StrPool.COMMA)
                        .stream().map(Integer::parseInt)
                        .collect(Collectors.toList());
                List<ActivityToolBean> activityTool = activityToolManager.batchGetTools(appId, toolTypes);
                detail.setActivityToolList(activityTool);
            }

            // 分割活动海报图片
            detail.setPosterUrl(UrlUtils.addHostOrEmpty(activityInfo.getPosterUrl(), commonConfig.getRomeFsDownloadCdn()));

            // 分割活动道具图片
            List<String> auxiliaryPropUrls = StrUtil.splitTrim(activityInfo.getAuxiliaryPropUrl(), StrPool.COMMA)
                    .stream().map(url -> UrlUtils.addHostOrEmpty(url, commonConfig.getRomeFsDownloadCdn()))
                    .collect(Collectors.toList());
            detail.setAuxiliaryPropUrls(auxiliaryPropUrls);


            // 构建陪档主播 & 主持信息 & 厅主信息
            List<Long> accompanyNjIds = StrUtil.splitTrim(activityInfo.getAccompanyNjIds(), StrPool.COMMA)
                    .stream().map(Long::parseLong).distinct().collect(Collectors.toList());

            List<Long> userIds = CollUtil.newArrayList(activityInfo.getHostId(), activityInfo.getNjId());
            Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(CollUtil.addAllIfNotContains(userIds, accompanyNjIds));

            detail.setNjInfo(UserCommonConvert.I.simpleUserDto2userBean(userMap.get(activityInfo.getNjId())));
            detail.setHostInfo(UserCommonConvert.I.simpleUserDto2userBean(userMap.get(activityInfo.getHostId())));
            detail.setAccompanyNjs(MapUtil.valuesOfKeys(userMap, accompanyNjIds.iterator())
                    .stream().filter(Objects::nonNull).map(UserCommonConvert.I::simpleUserDto2userBean)
                    .collect(Collectors.toList())
            );

            // 查询活动环节
            List<ActivityProcessDTO> processes = activityApplyManager.getActivityProcessByActivityId(activityId);
            detail.setProcesses(ActivityApplyConvert.I.convertActivityProcessBean(processes));

            // 查询房间公告图片
            List<String> roomAnnouncementImages = StrUtil.splitTrim(activityInfo.getRoomAnnouncementImgUrl(), StrPool.COMMA)
                    .stream().map(url -> UrlUtils.addHostOrEmpty(url, commonConfig.getRomeFsDownloadCdn()))
                    .collect(Collectors.toList());
            detail.setRoomAnnouncementImages(roomAnnouncementImages);

            // 查询房间背景信息 & 头像框信息
            List<DecorateBean> decorateList = activityDecorateManager.getDecorateListById(activityId, appId);
            if (CollectionUtils.isNotEmpty(decorateList)) {
                Map<Integer, List<DecorateBean>> typeMap = decorateList.stream().filter(v->v.getType()!=null).collect(Collectors.groupingBy(DecorateBean::getType));
                if (typeMap.containsKey(PlatformDecorateTypeEnum.BACKGROUND.getType())) {
                    List<DecorateBean> decorateBeans = typeMap.get(PlatformDecorateTypeEnum.BACKGROUND.getType());
                    detail.setRoomBackgroundInfos(decorateBeans);
                    detail.setRoomBackgroundInfo(decorateBeans.get(0));
                }
                if (typeMap.containsKey(PlatformDecorateTypeEnum.AVATAR.getType())) {
                    List<DecorateBean> decorateBeans = typeMap.get(PlatformDecorateTypeEnum.AVATAR.getType());
                    detail.setAvatarWidgetInfos(decorateBeans);
                    detail.setAvatarWidgetInfo(decorateBeans.get(0));
                }
            }

            // 获取资源审批结果
            Result<List<ActivityFlowResourceDetailBean>> result = getFlowResourceAuditResult(activityId, appId);
            if (RpcResult.isFail(result)) {
                log.error("get flow resource audit result fail, activityId:{}, appId:{}", activityId, appId);
            } else {
                detail.setFlowResourceDetails(result.target());
            }

            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, detail);
        });
    }

    @Override
    public Result<List<ActivityFlowResourceDetailBean>> getFlowResourceAuditResult(Long activityId, int appId) {

        return ResultHandler.handle(appId, () -> {
            ActivityInfoDTO info = activityApplyManager.getActivityInfoById(activityId);
            if (info == null) {
                return RpcResult.fail(GET_FLOW_RESOURCE_AUDIT_RESULT_FAIL);
            }
            List<ActivityFlowResourceDTO> flowResourceList = activityApplyManager.getActivityFlowResourceByActivityId(activityId);
            if (CollUtil.isEmpty(flowResourceList)) {
                log.info("flow resource list is empty, activityId:{}, appId:{}", activityId, appId);
                return RpcResult.success(Collections.emptyList());
            }

            Map<Long, ActivityResourceSimpleInfoDTO> resourceMap = activityResourceManager.batchResourceByIds(
                    flowResourceList.stream().map(ActivityFlowResourceDTO::getResourceConfigId).collect(Collectors.toList())
            ).stream().collect(Collectors.toMap(ActivityResourceSimpleInfoDTO::getId, Function.identity()));


            List<ActivityFlowResourceDetailBean> detailBeanList = flowResourceList.stream().map(flowResource -> {
                ActivityResourceSimpleInfoDTO resourceSimpleInfoDTO = resourceMap.get(flowResource.getResourceConfigId());
                if (resourceSimpleInfoDTO == null) {
                    return null;
                }

                ActivityFlowResourceDetailBean bean = ActivityApplyConvert.I.convertFlowResourceDetailBean(resourceSimpleInfoDTO);
                if (AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode().equals(bean.getResourceCode())) {
                    OfficialSeatExtraBean extra = ResourceExtraMapping.convertJsonToExtra(flowResource.getExtra(), AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode());
                    bean.setExtra(ActivityApplyConvert.I.convertOfficialSeatExtraBean(extra));
                }

                // 处理审核状态
                bean.setResourceAuditStatus(flowResource.getStatus());

                //计算活动状态
                boolean isActivityStart = info.getStartTime().getTime() <= System.currentTimeMillis();

                // 处理发放状态
                if (resourceSimpleInfoDTO.getDeployType().equals(ActivityResourceDeployTypeConstants.MANUAL_CONFIG)) {
                    // 手动资源，只有通过和不通过状态， 审核通过 == 已发放， 审核不通过 == 发放失败
                    bean.setGiveStatus(flowResource.getStatus());
                } else {
                    ActivityResourceGiveDTO record = activityMaterielGiveManager.getGiveResource(appId, flowResource.getId());
                    int giveStatus = record == null ? ActivityResourceGiveStatusEnum.WAIT_GIVE.getStatus() : record.getStatus();
                    //活动都开始，还是等待发放，将状态修改成发放成功
                    if (record != null && isActivityStart && record.getStatus() == ActivityResourceGiveStatusEnum.WAIT_GIVE.getStatus()) {
                        giveStatus = ActivityResourceGiveStatusEnum.SUCCESS.getStatus();
                    }

                    bean.setGiveStatus(giveStatus);
                }

                // 处理流量资源图片
                bean.setResourceImageUrl(UrlUtils.addHostOrEmpty(flowResource.getImageUrl(), commonConfig.getRomeFsDownloadCdn()));
                return bean;
            }).filter(Objects::nonNull).collect(Collectors.toList());
            return RpcResult.success(detailBeanList);
        });
    }

    private Result<Void> baseCheck(ActivityInfoDTO activityInfo, Integer version) {
        long now = System.currentTimeMillis();
        //检查活动是否删除
        if (activityInfo.getDeleted() == ACTIVITY_DELETE_STATUS) {
            return RpcResult.fail(AGREE_ACTIVITY_APPLY_PARAM_ERROR, "活动已删除，不可审批");
        }

        if (activityInfo.getStartTime().getTime() <= now) {
            return RpcResult.fail(AGREE_ACTIVITY_APPLY_PARAM_ERROR, "活动已开始，不可审批");
        }

        //版本号不一致不允许修改
        if (!Objects.equals(activityInfo.getVersion(), version)) {
            return RpcResult.fail(ACTIVITY_VERSION_NOT_MATCH, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_AGREE_VERSION_NOT_MATCH);
        }

        //检查时间
        if ((activityInfo.getStartTime().getTime() - now) / TimeConstant.ONE_MINUTE_MILLISECOND < config.getPreactTimeMin()) {
            return RpcResult.fail(AGREE_ACTIVITY_APPLY_PARAM_ERROR, String.format("距离活动开始时间不足%d分钟，不可审批", config.getPreactTimeMin()));
        }

        if (((now - activityInfo.getCreateTime().getTime()) / TimeConstant.ONE_MINUTE_MILLISECOND) < config.getMinuteAfterAudit()) {
            return RpcResult.fail(AGREE_ACTIVITY_APPLY_PARAM_ERROR, String.format("请在活动提报%d分钟后再审批", config.getMinuteAfterAudit()));
        }

        //检查活动审核状态状态
        if (!activityInfo.getAuditStatus().equals(ActivityAuditStatusEnum.WAITING_AUDIT.getStatus())) {
            return RpcResult.fail(AGREE_ACTIVITY_APPLY_PARAM_ERROR, "活动状态不正确，不可审批");
        }
        return RpcResult.success();
    }

    private Map<Long, ActivityClassificationConfigBean> getClassificationBeanMap(List<Long> classIds) {
        List<ActivityClassificationConfigBean> classificationBeans = activityClassificationManager.batchActivityClassification(classIds);
        Map<Long, ActivityClassificationConfigBean> classificationBeanMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(classificationBeans)) {
            classificationBeanMap = classificationBeans.stream()
                    .collect(Collectors.toMap(ActivityClassificationConfigBean::getClassId, Function.identity(), (x, y) -> x));
        }
        return classificationBeanMap;
    }

    private Map<Long, String> getLevelMap(Integer appId, Map<Long, ActivityClassificationConfigBean> classificationBeanMap) {
        List<Long> levels = classificationBeanMap.values().stream().map(ActivityClassificationConfigBean::getLevelId).collect(Collectors.toList());
        List<ActivityLevelConfigBean> levelConfigBeans = activityLevelManager.listByAppIdAndLevelIds(appId, levels);
        return levelConfigBeans.stream().collect(Collectors.toMap(ActivityLevelConfigBean::getId, ActivityLevelConfigBean::getLevel, (x, y) -> x));
    }

    private Map<Long, FamilyBean> getActivityFamilyMap(List<ActivitySimpleInfoDT0> list) {
        return list.stream().filter(activitySimpleInfoDTO -> activitySimpleInfoDTO.getFamilyId() != null).map(activitySimpleInfoDTO ->
                familyManager.getFamilyByCache(activitySimpleInfoDTO.getFamilyId())
        ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toMap(FamilyBean::getId, Function.identity(), (x, y) -> x));
    }

    private Map<Long, SimpleUserDto> getUserInfoMap(List<ActivitySimpleInfoDT0> list) {
        Set<Long> userIds = list.stream()
                .flatMap(info -> Stream.of(info.getNjId(), info.getApplicantUid()))
                .collect(Collectors.toSet());
        return userManager.getSimpleUserMapByIds(new ArrayList<>(userIds));
    }

    private boolean convertQueryBandToId(ActivitySimpleQueryParamDTO paramDTO, RequestQueryUserActivitiesBean requestQueryUserActivities) {
        if (StringUtils.isNotEmpty(requestQueryUserActivities.getNjBrand())) {
            Long njId = userManager.getUserIdByBand(requestQueryUserActivities.getNjBrand());
            if (njId == null) {
                log.warn("queryUserActivities, njBrand:{} not exist", requestQueryUserActivities.getNjBrand());
                return false;
            }
            paramDTO.setNjId(njId);
        }
        if (StringUtils.isNotEmpty(requestQueryUserActivities.getApplyBrand())) {
            Long applyUserId = userManager.getUserIdByBand(requestQueryUserActivities.getApplyBrand());
            if (applyUserId == null) {
                log.warn("queryUserActivities, applyBrand:{} not exist", requestQueryUserActivities.getApplyBrand());
                return false;
            }
            paramDTO.setApplyUserId(userManager.getUserIdByBand(requestQueryUserActivities.getApplyBrand()));
        }
        return true;
    }

    @Override
    public Result<ResponseGetActivityApplyDetailForApp> getActivityApplyDetailForApp(
            RequestGetActivityApplyDetailForApp request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));

        if (request == null) {
            return RpcResult.fail(GET_ACTIVITY_APPLY_PARAM_ERROR);
        }

        List<ActivityApplyDetailForAppDTO> activityApplyDetailForApp = new ArrayList<>();
        if (CollectionUtils.isEmpty(request.getActivityIds()) && (request.getNjId() == null || request.getNjId() <= 0)) {
            activityApplyDetailForApp = activityApplyManager.getActivityApplyDetailForApp(
                    request.getBeginDate(), request.getEndDate(), request.getAppId());
        }

        if (CollectionUtils.isNotEmpty(request.getActivityIds())) {
            activityApplyDetailForApp = activityApplyManager.getActivityApplyDetailForAppByActivityIds(
                    request.getActivityIds(), request.getAppId());
        }

        if (request.getNjId() != null && request.getNjId() > 0) {
            activityApplyDetailForApp = activityApplyManager.getActivityApplyDetailForAppByNjId(
                    request.getNjId(), request.getBeginDate(), request.getEndDate(), request.getAppId());
        }

        ResponseGetActivityApplyDetailForApp response =
                buildGetActivityApplyDetailForAppResult(activityApplyDetailForApp, request.getAppId());
        return RpcResult.success(response);
    }

    @Override
    public Result<ResponseGetActivityApplyInfo> getActivityApplyInfo(RequestGetActivityApplyInfo request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));

        if (request == null) {
            return RpcResult.fail(GET_ACTIVITY_APPLY_INFO_PARAM_ERROR);
        }

        List<ActivityApplyInfoDTO> applyInfoDTOList = new ArrayList<>();
        if (request.getNjId() != null && request.getNjId() > 0) {
            applyInfoDTOList = activityApplyManager.getActivityApplyInfoByNjId(
                    request.getNjId(), request.getBeginDate(), request.getEndDate(), request.getAppId());
        }else{
            applyInfoDTOList = activityApplyManager.getActivityApplyInfoWithTimeRange(
                    request.getBeginDate(), request.getEndDate(), request.getAppId());
        }

        ResponseGetActivityApplyInfo response = this.buildGetActivityApplyInfoResult(applyInfoDTOList);
        return RpcResult.success(response);
    }




    private ResponseGetActivityApplyDetailForApp buildGetActivityApplyDetailForAppResult(List<ActivityApplyDetailForAppDTO> dtos, int appId) {
        //查询出活动工具
        List<ActivityToolBean> allActivityTool = activityToolManager.getAllActivityTool(appId);
        Map<Integer, String> activityToolMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(allActivityTool)) {
            activityToolMap = allActivityTool.stream()
                    .collect(Collectors.toMap(ActivityToolBean::getType, ActivityToolBean::getName, (e1, e2) -> e1));
        }

        List<ActivityApplyDetailForAppBean> beans = new ArrayList<>();
        for (ActivityApplyDetailForAppDTO dto : dtos) {
            ActivityApplyDetailForAppBean bean = new ActivityApplyDetailForAppBean();
            bean.setId(dto.getId());
            bean.setName(dto.getName());
            bean.setNjId(dto.getNjId());
            Long classId = dto.getClassId();
            bean.setClassId(classId);
            bean.setClassName(dto.getClassName());
            bean.setActivityBigClassType(dto.getActivityBigClassType());
            bean.setStartTime(dto.getStartTime());
            bean.setEndTime(dto.getEndTime());
            bean.setAccompanyNjIds(dto.getAccompanyNjIds());
            bean.setPosterUrl(dto.getPosterUrl());

            bean.setIntroduction(dto.getIntroduction());
            bean.setFlowResourceImageUrl(dto.getFlowResourceImageUrl());

            List<String> activityToolNameList = new ArrayList<>();
            if (StringUtils.isNotBlank(dto.getActivityTool())) {
                List<Integer> activityToolList = Arrays.stream(dto.getActivityTool().split(","))
                        .map(Integer::valueOf)
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(activityToolList)) {
                    for (Integer tool : activityToolList) {
                        String toolName = activityToolMap.getOrDefault(tool, null);
                        if (StringUtils.isNotEmpty(toolName)) {
                            activityToolNameList.add(toolName);
                        }
                    }
                }
            }
            bean.setActivityTools(activityToolNameList);

            //查询活动等级
            ActivityLevelConfigBean levelInfo = activityLevelManager.getLevelByClassId(appId, classId);
            if (levelInfo != null) {
                bean.setLevelId(levelInfo.getId());
                bean.setLevelName(levelInfo.getLevel());
            }

            beans.add(bean);
        }

        return new ResponseGetActivityApplyDetailForApp().setBeans(beans);
    }

    @Override
    public Result<Void> activityModify(@NotNull @Valid RequestActivityApplyBean activityModifyBean) {
        LogContext.addReqLog("activityModify.paramBean={}", JsonUtil.dumps(activityModifyBean));
        LogContext.addResLog("activityModify.paramBean={}", JsonUtil.dumps(activityModifyBean));
        return ResultHandler.handle(activityModifyBean.getAppId(), () -> {

            Optional<ActivityTemplateBaseInfoDTO> templateBaseInfoOp = activityTemplateForCenterManager.getTemplateBaseInfo(activityModifyBean.getTemplateId());
            if (!templateBaseInfoOp.isPresent()) {
                return RpcResult.fail(ACTIVITY_APPLY_PARAM_ERROR);
            }

            // 校验模板时间和活动时间限制
            if (!checkTemplateDurationLimit(templateBaseInfoOp.get(), activityModifyBean.getStartTime(), activityModifyBean.getEndTime())){
                return RpcResult.fail(ACTIVITY_MODIFY_TEMPLATE_DURATION_LIMIT, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_TEMPLATE_DURATION_LIMIT);
            }

            // 1. 增加用户级别分布式锁
            try (RedisLock redisLock = activityOperationRedisManager.getModifyLock(activityModifyBean.getAppId(), activityModifyBean.getActivityId())) {
                if (!redisLock.tryLock()) {
                    return RpcResult.fail(ActivityApplyService.ACTIVITY_MODIFY_REQ_TOO_FAST, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_MODIFY_REQ_TOO_FAST_INVALID);
                }

                ActivityParamDTO activityParamDTO = ActivityParamConvert.INSTANCE.convertApplyBeanToDTO(activityModifyBean);

                //最先黑名单校验
                Result<Void> preResult = activityApplyCheckManager.preBlackFilter(activityParamDTO);
                if (RpcResult.isFail(preResult)) {
                    log.warn("activityModify.preBlackFilter fail, paramBean:{}, errorMsg:{}", activityParamDTO, preResult.getMessage());
                    return preResult;
                }

                //基础信息校验
                Result<Void> result = activityApplyCheckManager.validateBaseActivityModifyInfo(activityParamDTO);
                if (RpcResult.isFail(result)) {
                    return result;
                }

                // 获取旧官频位资源
                List<ActivityFlowResourceDTO> oldFlowResourceDTOS = activityApplyManager.getActivityFlowResourceByActivityId(activityModifyBean.getActivityId());
                ActivityFlowResourceDTO oldOfficialSeatResource = getOldOfficialSeatResource(oldFlowResourceDTOS);
                log.info("activityModify, get oldOfficialSeatResource,activityId:{},extra:{}", activityModifyBean.getActivityId(), Optional.ofNullable(oldOfficialSeatResource).map(ActivityFlowResourceDTO::getExtra).orElse(""));

                //资源校验(含官频位)
                Result<Void> officeSeatAndResourceResult = activityApplyCheckManager.validateOfficeSeatAndResource(activityParamDTO, oldOfficialSeatResource);
                if (RpcResult.isFail(officeSeatAndResourceResult)) {
                    return officeSeatAndResourceResult;
                }

                //家族ID校验
                Result<Long> familyIdResult = activityApplyCheckManager.checkAndGetFamilyId(activityParamDTO.getNjId(), activityParamDTO.getApplyType());
                if (RpcResult.isFail(familyIdResult)) {
                    return RpcResult.fail(familyIdResult.rCode(), familyIdResult.getMessage());
                }

                //内容数据审核校验，低频，这里不再比对旧数据，直接全部送审
                Result<Void> auditRes = activityApplyCheckManager.checkActivityApplyData(activityParamDTO);
                if (RpcResult.isFail(auditRes)) {
                    return RpcResult.fail(auditRes.rCode(), auditRes.getMessage());
                }

                // 构建落库数据，全量构建
                ActivityApplyParamDTO applyParamDTO = activityApplyConvert.buildApplyParam(activityParamDTO, familyIdResult.target());
                return activityApplyManager.modifyActivityApply(applyParamDTO, oldOfficialSeatResource);

            } catch (Exception e) {
                log.error("modify activity apply error, activityId:{}", activityModifyBean.getActivityId(), e);
                return RpcResult.fail(ACTIVITY_MODIFY_ERROR, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_MODIFY_ERROR);
            }
        });
    }

    @Override
    public Result<List<ActivityApplyToolBean>> getInTimeRangeActivityApply(RequestGetInTimeRangeActivityApply request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);

        // 查询活动列表
        List<ActivityApplyInfoSimpleDTO> dtoList = activityApplyManager.getInTimeRangeActivityApply(request);
        if (CollectionUtils.isEmpty(dtoList)) {
            LogContext.addResLog("dtoList is empty");
            return RpcResult.success(Collections.emptyList());
        }
        if (log.isDebugEnabled()) {
            log.debug("dtoList={}", JsonUtil.dumps(dtoList));
        }

        List<Integer> toolValues = dtoList.stream().map(ActivityApplyInfoSimpleDTO::getActivityTool)
                .filter(StringUtils::isNotBlank)
                .flatMap(v -> Arrays.stream(v.split(",")))
                .map(Integer::parseInt)
                .distinct()
                .collect(Collectors.toList());

        // 查询工具名称
        List<ActivityToolBean> activityToolBeans = activityToolManager.batchGetTools(request.getAppId(), toolValues);
        Map<Integer, ActivityToolBean> toolMap = activityToolBeans.stream().collect(Collectors.toMap(ActivityToolBean::getType, v -> v, (k1, k2) -> k1));

        List<ActivityApplyToolBean> beanList = new ArrayList<>();
        for (ActivityApplyInfoSimpleDTO dto : dtoList) {
            ActivityApplyToolBean bean = ActivityApplyConvert.I.simpleDTO2ToolBean(dto);
            beanList.add(bean);
            if (StringUtils.isBlank(bean.getActivityTool())) {
                continue;
            }
            List<ActivityToolBean> toolList = new ArrayList<>();
            bean.setActivityToolList(toolList);
            for (String toolValue : bean.getActivityTool().split(",")) {
                ActivityToolBean toolBean = toolMap.get(Integer.parseInt(toolValue));
                if (toolBean != null) {
                    toolList.add(toolBean);
                }
            }
        }

        return RpcResult.success(beanList);
    }

    private ActivityFlowResourceDTO getOldOfficialSeatResource(List<ActivityFlowResourceDTO> oldFlowResourceDTOS) {
        List<Long> oldResourceConfigIds = oldFlowResourceDTOS.stream().map(ActivityFlowResourceDTO::getResourceConfigId).collect(Collectors.toList());
        List<ResponseActivityResource> oldResources = activityResourceManager.getActivityResourcesByIds(oldResourceConfigIds);
        //找到官频位资源配置信息
        Optional<ResponseActivityResource> oldOfficialSeatResource = oldResources.stream()
                .filter(resource -> Objects.equals(resource.getResourceCode(), AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode()))
                .findAny();

        //找到旧官频位资源
        return oldFlowResourceDTOS.stream()
                .filter(dto -> oldOfficialSeatResource.isPresent() &&
                        Objects.equals(dto.getResourceConfigId(), oldOfficialSeatResource.get().getId()))
                .findAny().orElse(null);
    }

    private ResponseGetActivityApplyInfo buildGetActivityApplyInfoResult(List<ActivityApplyInfoDTO> dtos) {
        List<ActivityApplyInfoBean> beans = new ArrayList<>();
        for (ActivityApplyInfoDTO dto : dtos) {
            ActivityApplyInfoBean bean = new ActivityApplyInfoBean();
            bean.setId(dto.getId());
            bean.setName(dto.getName());
            bean.setNjId(dto.getNjId());
            bean.setBigClassId(dto.getBigClassId());
            bean.setClassId(dto.getClassId());
            bean.setClassName(dto.getClassName());
            bean.setStartTime(dto.getStartTime());
            bean.setEndTime(dto.getEndTime());
            bean.setLevelId(dto.getLevelId());
            bean.setLevelName(dto.getLevelName());
            beans.add(bean);
        }
        return new ResponseGetActivityApplyInfo().setBeans(beans);
    }

    private Result<Void> validateQueryUserActivitiesTime(RequestQueryUserActivitiesBean request) {
        // 校验是否有时间参数
        if ((request.getApplyStartTime() == null || request.getApplyStartTime() == 0) && (request.getApplyEndTime() == null || request.getApplyEndTime() == 0)
            && (request.getMaxStartTime() == null || request.getMaxStartTime() == 0) && (request.getMinStartTime() == null || request.getMinStartTime() == 0)) {
            return RpcResult.fail(QUERY_USER_ACTIVITY_LIST_PARAM_ERROR, "活动提报时间或者活动开始时间必须选填一个");
        }

        // 校验活动开始时间范围
        if (request.getMaxStartTime() != null && request.getMaxStartTime() > 0 && request.getMinStartTime() != null
            && request.getMinStartTime() > 0 && request.getMaxStartTime() < request.getMinStartTime()) {
            return RpcResult.fail(QUERY_USER_ACTIVITY_LIST_PARAM_ERROR, "直播结束时间不能小于开始时间");
        }

        // 校验活动提报时间范围
        if (request.getApplyStartTime() != null && request.getApplyStartTime() > 0
            && request.getApplyEndTime() != null && request.getApplyEndTime() > 0
            && request.getApplyStartTime() > request.getApplyEndTime()) {
            return RpcResult.fail(QUERY_USER_ACTIVITY_LIST_PARAM_ERROR, "活动提报名结束时间不能小于开始时间");
        }

        return RpcResult.success();
    }

    private Result<Void> validateQueryActivityListTime(RequestQueryActivityListBean request) {
        // 校验是否有时间参数
        if (request.getApplyStartTime() == null && request.getApplyEndTime() == null
            && request.getMinStartTime() == null && request.getMaxStartTime() == null) {
            return RpcResult.fail(QUERY_ACTIVITY_LIST_PARAM_ERROR, "提报时间与活动时间必填其一");
        }

        // 校验活动提报时间范围
        if (request.getApplyStartTime() != null && request.getApplyEndTime() != null
            && request.getApplyStartTime() > request.getApplyEndTime()) {
            return RpcResult.fail(QUERY_ACTIVITY_LIST_PARAM_ERROR, "结束时间不能小于开始时间");
        }

        // 校验活动开始时间范围
        if (request.getMaxStartTime() != null && request.getMinStartTime() != null
            && request.getMaxStartTime() < request.getMinStartTime()) {
            return RpcResult.fail(QUERY_ACTIVITY_LIST_PARAM_ERROR, "结束时间不能小于开始时间");
        }

        return RpcResult.success();
    }

    // 处理活动分类ID查询条件
    private List<Long> buildClassificationQueryIds(Long levelId, Long classId) {
        List<Long> classQueryIds = new ArrayList<>();

        // 1. 如果有等级ID,获取该等级下的所有分类ID
        if (levelId != null) {
            classQueryIds = activityClassificationManager.getClassificationIdListByLevelId(levelId);
        }

        // 2. 如果有具体分类ID进行过滤
        if (classId != null) {
            // 2.1 如果没有等级过滤,直接使用分类ID
            if (classQueryIds.isEmpty() || classQueryIds.contains(classId)) {
                return Collections.singletonList(classId);
            }

            // 2.3 分类ID不在等级过滤结果中,返回空列表
            return Collections.emptyList();
        }

        return classQueryIds.size() > MAX_CLASS_ID_SIZE ? Collections.emptyList() : classQueryIds;
    }

    /**
     * 根据活动时间设置活动状态
     * @param startTime 活动开始时间
     * @param endTime 活动结束时间
     * @return 活动状态
     */
    private int getActivityStatus(Date startTime, Date endTime) {
        long currentTime = System.currentTimeMillis();
        if (endTime.getTime() < currentTime) {
            return ActivityStatusEnum.END.getStatus();
        } else if (startTime.getTime() > currentTime) {
            return ActivityStatusEnum.UN_START.getStatus();
        }
        return ActivityStatusEnum.START.getStatus();
    }
}
