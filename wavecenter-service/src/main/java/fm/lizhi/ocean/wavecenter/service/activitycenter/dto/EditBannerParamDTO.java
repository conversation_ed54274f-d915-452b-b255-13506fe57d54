package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class EditBannerParamDTO {

    /**
     * banner id
     */
    private Long id;

    /**
     * banner 标题
     */
    private String title;

    /**
     * banner 图片地址
     */
    private String imgUrl;

    /**
     * banner 跳转地址
     */
    private String action;

    /**
     * 图片宽高比
     */
    private String scale;

    /**
     * 排序
     */
    private Integer seq;

    /**
     * 活动开始时间
     */
    private Long startTime;

    /**
     * 活动结束时间
     */
    private Long expireTime;

    /**
     * 板块位置信息
     */
    private BannerPlateDTO bannerPlateDTO;

}
