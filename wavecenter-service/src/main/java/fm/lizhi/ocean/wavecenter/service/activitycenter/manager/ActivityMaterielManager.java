package fm.lizhi.ocean.wavecenter.service.activitycenter.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.*;

/**
 * 活动物资管理，包含流量资源、装扮和房间公告
 */
public interface ActivityMaterielManager {


    /**
     * 装扮发放
     *
     * @param paramDTO 参数
     * @return 结果
     */
    Result<Void> sendDecorate(SendDecorateParamDTO paramDTO);

    /**
     * 根据装扮ID查询装扮信息
     *
     * @param decorateId 装扮ID
     * @return 结果
     */
    Result<DecorateInfoDTO> getDecorateInfo(long decorateId);

    /**
     * 修改banner
     *
     * @param param 参数
     * @return 结果
     */
    Result<Long> editBannerConfig(EditBannerParamDTO param);

    /**
     * 修改banner
     *
     * @param param 参数
     * @return 结果
     */
    Result<Void> deleteBannerConfig(DeleteBannerParamDTO param);

    /**
     * 发送推荐卡
     *
     * @param param 参数
     * @return 结果
     */
    Result<Void> sendRecommendCard(SendRecommendCardParamDTO param);

    /**
     * 官频位保存
     *
     * @param param 参数
     * @return 结果
     */
    Result<SaveOfficialSeatResDTO> saveOfficialSeat(SaveOfficialSeatParamDTO param);

    /**
     * 同步活动到报名记录
     *
     * @param dto 同步参数
     * @return 结果
     */
    Result<Long> syncWaveActivityToApplyRecord(SyncWaveActivityToApplyRecordDTO dto);

    /**
     * 删除同步活动到报名记录
     *
     * @param dto 同步参数
     * @return 结果
     */
    Result<Void> deleteWaveActivityToApplyRecord(DeleteWaveActivityToApplyRecordDTO dto);

    /**
     * 删除官频位
     *
     * @param param 参数
     * @return 结果
     */
    Result<Void> deleteOfficialSeat(DeleteOfficialSeatParamDTO param);
}
