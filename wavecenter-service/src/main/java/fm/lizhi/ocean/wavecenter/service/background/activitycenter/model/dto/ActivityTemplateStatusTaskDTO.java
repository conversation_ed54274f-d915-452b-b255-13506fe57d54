package fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/8 17:48
 */
@Data
@Accessors(chain = true)
public class ActivityTemplateStatusTaskDTO {

    private Long id;

    /**
     * 模板ID
     */
    private Long templateId;

    /**
     * 目标状态，1：下架，2：上架
     */
    private Integer templateTargetStatus;

    /**
     * 任务执行时间
     */
    private Date executeTime;

    /**
     * 任务状态，0=待执行，1=已执行
     */
    private Integer taskStatus;

    /**
     * 业务
     */
    private Integer appId;

}
