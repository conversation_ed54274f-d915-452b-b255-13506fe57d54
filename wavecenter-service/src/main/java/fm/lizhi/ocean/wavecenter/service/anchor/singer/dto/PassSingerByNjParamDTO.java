package fm.lizhi.ocean.wavecenter.service.anchor.singer.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 按厅进行通过歌手
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PassSingerByNjParamDTO {

    /**
     * 应用ID
     */
    private int appId;

    /**
     * 厅ID
     */
    private Long njId;

    /**
     * 歌手类型
     */
    private Integer singerType;

    /**
     * 装饰流水原因
     *
     */
    private String decorateFlowReason;

    /**
     * 操作人
     */
    private String operator;
}
