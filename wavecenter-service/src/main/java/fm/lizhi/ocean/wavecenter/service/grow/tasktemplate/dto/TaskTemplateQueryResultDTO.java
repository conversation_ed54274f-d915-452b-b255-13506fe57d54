package fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 任务模版查询结果DTO
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskTemplateQueryResultDTO {

    /**
     * 任务模版列表
     */
    private List<TaskTemplateQueryItemDTO> list;

    /**
     * 总数
     */
    private Long total;

}