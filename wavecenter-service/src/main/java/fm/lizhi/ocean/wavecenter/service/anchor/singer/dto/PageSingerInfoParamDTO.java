package fm.lizhi.ocean.wavecenter.service.anchor.singer.dto;

import java.util.List;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PageSingerInfoParamDTO {

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 页码
     */
    private int pageNo;

    /**
     * 页数
     */
    private int pageSize;


    /**
     * 歌手类型
     */
    private SingerTypeEnum singerType;

    /**
     * 厅ID
     */
     private Long njId;

    /**
     * 主播 ID
     */
    private Long userId;

    /**
     * 起始通过时间
     */
    private Long startAuditTime;

    /**
     * 结束通过时间
     */
    private Long endAuditTime;

    /**
     * 起始淘汰时间
     */
    private Long startEliminationTime;

    /**
     * 结束淘汰时间
     */
    private Long endEliminationTime;

    /**
     * 歌手状态
     */
    private List<SingerStatusEnum> singerStatus;

    /**
     * 歌曲风格
     */
    private List<String> songStyle;

    /**
     * 厅主波段号
     */
    private String njBand;

    /**
     * 歌手波段号
     */
    private String singerBand;

    /**
     * 是否原创歌手
     */
    private Boolean originalSinger;

    /**
     * 排序字段
     * 支持：auditTime, eliminationTime, createTime, modifyTime
     */
    private String orderMetrics;

    /**
     * 排序方向
     * ASC-升序, DESC-降序，默认DESC
     */
    private OrderType orderType;

    private Boolean whiteListSinger;



}
