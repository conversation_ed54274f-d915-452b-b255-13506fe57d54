package fm.lizhi.ocean.wavecenter.service.anchor.singer.handler;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerAuditConfigCodeEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.config.SingerAnchorConfig;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.constant.SingerVerifyApplyErrorTipConstant;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerFilterParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerHallApplyManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 签约厅状态检测
 * <AUTHOR>
 */
@Component
public class SignedHallStatusFilter implements SingerPreAuditFilter {

    @Autowired
    private SingerHallApplyManager singerHallApplyManager;

    @Autowired
    private SingerAnchorConfig singerAnchorConfig;

    @Override
    public Result<Void> filter(SingerFilterParamDTO param) {
        // 不校验厅关系的业务，直接返回成功
        if (singerAnchorConfig.getBizConfig().isLessRelevanceSwitch()) {
            return RpcResult.success();
        }

        boolean inSingingHall = singerHallApplyManager.isInSingingHall(param.getAppId(), param.getUserId());
        return inSingingHall ? RpcResult.success() : RpcResult.fail(SingerPreAuditFilter.HALL_STATUS_AUDIT_FAIL_CODE,
                SingerVerifyApplyErrorTipConstant.SINGER_VERIFY_APPLY_HALL_STATUS_PRE_AUDIT_FAIL);
    }

    @Override
    public SingerAuditConfigCodeEnum getCodeEnum() {
        return SingerAuditConfigCodeEnum.SIGNED_HALL_STATUS;
    }

}
