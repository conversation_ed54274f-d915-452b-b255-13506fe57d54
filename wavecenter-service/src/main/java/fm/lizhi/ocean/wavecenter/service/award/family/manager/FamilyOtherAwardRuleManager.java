package fm.lizhi.ocean.wavecenter.service.award.family.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.ListFamilySpecialRecommendCardNameBean;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestClearFamilySpecialRecommendCardName;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestListFamilySpecialRecommendCardName;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestUploadFamilySpecialRecommendCardName;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;

public interface FamilyOtherAwardRuleManager {

    /**
     * 上传公会特殊推荐卡名单
     *
     * @param request 请求参数
     * @return 上传结果
     */
    Result<Void> uploadFamilySpecialRecommendCardName(RequestUploadFamilySpecialRecommendCardName request);

    /**
     * 清空公会特殊推荐卡名单
     *
     * @param request 请求参数
     * @return 清空结果
     */
    Result<Void> clearFamilySpecialRecommendCardName(RequestClearFamilySpecialRecommendCardName request);

    /**
     * 列出公会特殊推荐卡名单
     *
     * @param request 请求参数
     * @return 列表结果
     */
    Result<PageBean<ListFamilySpecialRecommendCardNameBean>> listFamilySpecialRecommendCardName(RequestListFamilySpecialRecommendCardName request);
}
