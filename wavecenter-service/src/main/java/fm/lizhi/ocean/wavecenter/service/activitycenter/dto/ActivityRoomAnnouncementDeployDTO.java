package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.Data;

import java.util.Date;

@Data
public class ActivityRoomAnnouncementDeployDTO {

    private Long id;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 应用ID
     */
    private Integer appId;


    /**
     * 厅主ID
     */
    private Long njId;

    /**
     * 原始房间公告
     */
    private String originalAnnouncement;

    /**
     * 原始房间公告url
     */
    private String originalAnnouncementImgUrl;

    /**
     * 要配置的房间公告
     */
    private String announcement;

    /**
     * 要配置的房间公告图片
     */
    private String announcementImgUrl;

    /**
     * 重试次数
     */
    private Integer tryCount;

    /**
     * 状态，0：待设置，1：设置失败，2：设置成功，3：恢复失败，4：恢复成功
     */
    private Integer status;

    /**
     * 活动开始时间
     */
    private Date startTime;

    /**
     * 活动结束时间
     */
    private Date endTime;

    /**
     * 可选值  TEST/PRE/PRO
     */
    private String deployEnv;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 创建时间
     */
    private Date createTime;

}
