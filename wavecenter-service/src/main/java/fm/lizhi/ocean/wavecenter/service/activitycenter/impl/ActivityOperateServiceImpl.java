package fm.lizhi.ocean.wavecenter.service.activitycenter.impl;

import com.alibaba.csp.sentinel.util.AssertUtil;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestUserCancelActivity;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestUserModifyActivityAfterAudit;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseUserCancelActivity;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityChatManager;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityOperateManager;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityOperateService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.constants.TimeConstant;
import fm.lizhi.ocean.wavecenter.common.utils.RedisLock;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.ActivityConfig;
import fm.lizhi.ocean.wavecenter.service.activitycenter.constatns.ActivityOperateErrorTipConstant;
import fm.lizhi.ocean.wavecenter.service.activitycenter.convert.ActivityOperateConvert;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityInfoDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityUserCancelParamDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityUserModifyParamDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityApplyManager;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityRedisManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

@Slf4j
@ServiceProvider
public class ActivityOperateServiceImpl implements ActivityOperateService {

    @Autowired
    private ActivityRedisManager activityRedisManager;

    @Autowired
    private ActivityApplyManager activityApplyManager;

    @Autowired
    private ActivityConfig activityConfig;

    @Autowired
    private ActivityOperateManager activityOperateManager;

    @Autowired
    private ActivityChatManager activityChatManager;

    @Override
    public Result<ResponseUserCancelActivity> userCancelActivityV2(RequestUserCancelActivity request) {
        LogContext.addReqLog("userCancelActivity request:{}", JsonUtil.dumps(request));
        LogContext.addResLog("userCancelActivity request:{}", JsonUtil.dumps(request));
        return ResultHandler.handle(request.getAppId(), () -> {
            // 获取不到锁直接返回,这里跟修改和审批通过后运营修改加的是同一把锁
            try (RedisLock lock = activityRedisManager.getModifyLock(request.getAppId(), request.getActivityId())) {
                if (!lock.tryLock()) {
                    //尝试获取锁，如果获取不到就不等了，可能这个活动运营正在审批或者其他的用户正在操作，失败，提示给用户
                    log.info("userCancelActivity failed. activityId:{}, appId:{}, userId:{}", request.getActivityId(), request.getAppId(), request.getOperateUserId());
                    return RpcResult.fail(ActivityOperateService.CANCEL_ACTIVITY_FAIL, ActivityOperateErrorTipConstant.USER_CANCEL_ACTIVITY_CHANGE);
                }

                //查询活动信息
                ActivityInfoDTO info = activityApplyManager.getActivityInfoById(request.getActivityId());
                Result<Void> checkRes = cancelOperateCheck(info, request);
                if (RpcResult.isFail(checkRes)) {
                    log.info("userCancelActivity failed. activityId:{}, appId:{}, userId:{}, checkMsg:{}", request.getActivityId(), request.getAppId(), request.getOperateUserId(), checkRes.getMessage());
                    return RpcResult.fail(checkRes.rCode(), checkRes.getMessage());
                }

                // 版本号不一致不允许取消
                if (!Objects.equals(info.getVersion(), request.getVersion())) {
                    return RpcResult.fail(ActivityOperateService.CANCEL_ACTIVITY_PARAM_ERROR, ActivityOperateErrorTipConstant.ACTIVITY_MODIFY_TOO_FAST);
                }

                ActivityAuditStatusEnum auditStatusEnum = ActivityAuditStatusEnum.getByStatus(info.getAuditStatus());
                AssertUtil.notNull(auditStatusEnum, ActivityOperateErrorTipConstant.USER_CANCEL_ACTIVITY_FAIL);
                ActivityUserCancelParamDTO cancelParamDTO = ActivityOperateConvert.I.buildActivityUserCancelParam(request);
                cancelParamDTO.setActivityInfo(info);
                switch (auditStatusEnum) {
                    case WAITING_AUDIT:
                        Result<Void> result = activityOperateManager.cancelActivityBeforeAudit(cancelParamDTO);
                        return new Result<>(result.rCode(), new ResponseUserCancelActivity());
                    case AUDIT_PASS:
                        Result<String> cancelRes = activityOperateManager.cancelActivityAfterAudit(cancelParamDTO);
                        if (RpcResult.isFail(cancelRes)) {
                            log.warn("userCancelActivity failed. activityId:{}, appId:{}, userId:{}，msg={}", request.getActivityId(), request.getAppId(), request.getOperateUserId(), cancelRes.getMessage());
                            return RpcResult.fail(cancelRes.rCode(), cancelRes.getMessage());
                        }
                        //发送通知给申请人
                        activityChatManager.userCancelActivityNotice(cancelParamDTO.getActivityInfo(), cancelParamDTO.getOperateUserId());
                        return RpcResult.success();
                    default:
                        return RpcResult.fail(ActivityOperateService.CANCEL_ACTIVITY_FAIL, ActivityOperateErrorTipConstant.USER_CANCEL_ACTIVITY_FAIL);
                }
            } catch (Exception e) {
                log.error("userCancelActivity failed. activityId:{}, appId:{}, userId:{}", request.getActivityId(), request.getAppId(), request.getOperateUserId(), e);
                return RpcResult.fail(ActivityOperateService.CANCEL_ACTIVITY_FAIL, ActivityOperateErrorTipConstant.USER_CANCEL_ACTIVITY_FAIL);
            }
        });
    }

    @Override
    public Result<Void> userModifyActivityAfterAudit(RequestUserModifyActivityAfterAudit request) {
        LogContext.addReqLog("userModifyActivityAfterAudit request:{}", JsonUtil.dumps(request));
        LogContext.addResLog("userModifyActivityAfterAudit request:{}", JsonUtil.dumps(request));
        // 获取不到锁直接返回,这里跟修改和审批通过后运营修改加的是同一把锁
        try (RedisLock lock = activityRedisManager.getModifyLock(request.getAppId(), request.getActivityId())) {
            if (!lock.tryLock()) {
                log.info("userModifyActivityAfterAudit failed. activityId:{}, appId:{}, userId:{}",
                        request.getActivityId(), request.getAppId(), request.getOperateUserId());
                return RpcResult.fail(ActivityOperateService.CANCEL_ACTIVITY_FAIL, ActivityOperateErrorTipConstant.ACTIVITY_MODIFY_TOO_FAST);
            }

            //查询活动信息
            ActivityInfoDTO info = activityApplyManager.getActivityInfoById(request.getActivityId());
            if (info == null) {
                log.warn("userModifyActivityAfterAudit failed, activity no exist. activityId:{}, appId:{}", request.getActivityId(), request.getAppId());
                return RpcResult.fail(ActivityOperateService.MODIFY_ACTIVITY_PARAM_ERROR, ActivityOperateErrorTipConstant.ACTIVITY_MODIFY_NOT_EXIST_ACTIVITY);
            }

            Result<Void> checkRes = modifyActivityCheck(info, request);
            if (RpcResult.isFail(checkRes)) {
                log.warn("modifyActivityCheck failed. activityId:{}, appId:{}, userId:{}，msg={}",
                        request.getActivityId(), request.getAppId(), request.getOperateUserId(), checkRes.getMessage());
                return checkRes;
            }

            ActivityUserModifyParamDTO modifyParamDTO = ActivityOperateConvert.I.buildActivityUserModifyParam(request);
            return activityOperateManager.userModifyActivityAfterAudit(modifyParamDTO);
        } catch (Exception e) {
            log.error("userModifyActivityAfterAudit failed. activityId:{}, userId:{}", request.getActivityId(), request.getOperateUserId(), e);
            return RpcResult.fail(ActivityOperateService.CANCEL_ACTIVITY_FAIL, ActivityOperateErrorTipConstant.ACTIVITY_MODIFY_FAIL);
        }
    }


    /**
     * 取消活动审批前置检查
     *
     * @param info    活动信息
     * @param request 请求参数
     * @return 结果
     */
    private Result<Void> cancelOperateCheck(ActivityInfoDTO info, RequestUserCancelActivity request) {
        long currentTimeMillis = System.currentTimeMillis();
        if (info == null) {
            return RpcResult.fail(ActivityOperateService.CANCEL_ACTIVITY_PARAM_ERROR, ActivityOperateErrorTipConstant.USER_CANCEL_ACTIVITY_NOT_EXIST);
        }

        //申请人和取消人不是同一个，不让操作
        if (!info.getApplicantUid().equals(request.getOperateUserId()) && !info.getNjId().equals(request.getOperateUserId())) {
            return RpcResult.fail(ActivityOperateService.CANCEL_ACTIVITY_NO_PERMISSION, ActivityOperateErrorTipConstant.USER_CANCEL_ACTIVITY_NOT_PERMISSION);
        }

        //活动状态非待审核和审核通过，不能操作
        if (!Objects.equals(info.getAuditStatus(), ActivityAuditStatusEnum.WAITING_AUDIT.getStatus()) && !Objects.equals(info.getAuditStatus(), ActivityAuditStatusEnum.AUDIT_PASS.getStatus())) {
            return RpcResult.fail(ActivityOperateService.CANCEL_ACTIVITY_AUDIT_STATUS_ERROR, ActivityOperateErrorTipConstant.USER_CANCEL_ACTIVITY_AUDIT_STATUS_ERROR);
        }

        //未审批状态
        if (Objects.equals(info.getAuditStatus(), ActivityAuditStatusEnum.WAITING_AUDIT.getStatus())) {
            if (info.getEndTime().getTime() < currentTimeMillis) {
                return RpcResult.fail(ActivityOperateService.CANCEL_ACTIVITY_TIME_ERROR, ActivityOperateErrorTipConstant.USER_CANCEL_ACTIVITY_ACTIVITY_END);
            }

            if (info.getStartTime().getTime() < currentTimeMillis) {
                return RpcResult.fail(ActivityOperateService.CANCEL_ACTIVITY_TIME_ERROR, ActivityOperateErrorTipConstant.USER_CANCEL_ACTIVITY_ACTIVITY_GOING);
            }
        }

        //已审批，校验时间
        if (Objects.equals(info.getAuditStatus(), ActivityAuditStatusEnum.AUDIT_PASS.getStatus())) {
            //活动开始前N分钟，允许取消
            if (((info.getStartTime().getTime() - currentTimeMillis) / TimeConstant.ONE_MINUTE_MILLISECOND) < activityConfig.getPreactCancelActivityTimeMin()) {
                String msg = String.format(ActivityOperateErrorTipConstant.USER_CANCEL_ACTIVITY_OVER_TIME, activityConfig.getPreactCancelActivityTimeMin());
                return RpcResult.fail(ActivityOperateService.CANCEL_ACTIVITY_TIME_ERROR, msg);
            }
        }

        return RpcResult.success();
    }

    /**
     * 修改活动校验
     *
     * @param info    活动信息
     * @param request 请求参数
     * @return 结果
     */
    private Result<Void> modifyActivityCheck(ActivityInfoDTO info, RequestUserModifyActivityAfterAudit request) {
        // 校验距离活动开始是否剩余不到10分钟
        if ((info.getStartTime().getTime() - System.currentTimeMillis()) / TimeConstant.ONE_MINUTE_MILLISECOND < activityConfig.getPreactModifyActivityTimeMin()) {
            String msg = String.format(ActivityOperateErrorTipConstant.ACTIVITY_MODIFY_TIME_INVALID, activityConfig.getPreactModifyActivityTimeMin());
            return RpcResult.fail(ActivityOperateService.MODIFY_ACTIVITY_PARAM_ERROR, msg);
        }

        //版本号不一致不允许修改
        if (!Objects.equals(info.getVersion(), request.getVersion())) {
            return RpcResult.fail(ActivityOperateService.MODIFY_ACTIVITY_PARAM_ERROR, ActivityOperateErrorTipConstant.ACTIVITY_MODIFY_TOO_FAST);
        }

        //活动申请人不是当前用户，无法修改
        if (!info.getApplicantUid().equals(request.getOperateUserId())) {
            return RpcResult.fail(ActivityOperateService.MODIFY_ACTIVITY_PARAM_ERROR, ActivityOperateErrorTipConstant.ACTIVITY_MODIFY_NOT_PERMISSION);
        }

        return RpcResult.success();
    }


}
