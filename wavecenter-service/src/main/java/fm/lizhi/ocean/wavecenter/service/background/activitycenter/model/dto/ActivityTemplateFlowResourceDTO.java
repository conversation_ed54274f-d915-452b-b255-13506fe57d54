package fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto;

import lombok.Data;

import java.util.List;

/**
 * 活动模板流量资源DTO
 */
@Data
public class ActivityTemplateFlowResourceDTO {

    /**
     * 资源配置id
     */
    private Long resourceConfigId;

    /**
     * 资源名称
     */
    private String name;

    /**
     * 资源介绍
     */
    private String introduction;

    /**
     * 资源预览图片
     */
    private String imageUrl;

    /**
     * 资源配置类型
     *
     * @see fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityResourceDeployTypeConstants
     */
    private Integer deployType;

    /**
     * 资源是否必选
     */
    private Boolean required;

    /**
     * 资源code，只有自动配置的资源有
     */
    private String resourceCode;

    /**
     * 资源状态
     */
    private Integer resourceStatus;

    /**
     * 资源是否已删除
     */
    private Boolean resourceDeleted;

    /**
     * 资源额外信息
     */
    private ActivityTemplateFlowResourceExtraDTO extra;

    /**
     * 资源物料图片列表
     */
    private List<ActivityTemplateFlowResourceImageDTO> images;
}
