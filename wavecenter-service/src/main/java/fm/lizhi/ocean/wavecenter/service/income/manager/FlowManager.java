package fm.lizhi.ocean.wavecenter.service.income.manager;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.*;
import fm.lizhi.ocean.wavecenter.common.dto.PageDto;
import fm.lizhi.ocean.wavecenter.service.income.dto.GiveGiftFlowDto;
import fm.lizhi.ocean.wavecenter.service.income.dto.RoomRecFlowSumDto;

/**
 * 流水
 * <AUTHOR>
 * @date 2024/4/23 18:19
 */
public interface FlowManager {

    /**
     * 查询公会收益离线数据
     * @param paramBean
     * @return
     */
    PageBean<GuildIncomeDetailBean> getGuildIncomeDetailOut(GetGuildIncomeDetailParamBean paramBean);

    /**
     * 查询个人收益离线数据
     * @param paramBean
     * @return
     */
    PageBean<PersonalIncomeDetailBean> getPersonalIncomeDetailOut(GetPersonalIncomeDetailParamBean paramBean);

    /**
     * 查询个播收益离线数据
     * @param paramBean
     * @return
     */
    PageBean<PlayerIncomeDetailBean> getPlayerIncomeDetailOut(GetPlayerIncomeDetailParamBean paramBean);

    /**
     * 查询厅流水
     * @return
     */
    PageDto<GiveGiftFlowDto> getRoomRecFlow(GetRoomSignRoomParamBean paramBean);

    /**
     * 查询厅流水-合计
     * @return
     */
    RoomRecFlowSumDto getRoomRecFlowSum(GetRoomSignRoomParamBean paramBean);


    /**
     * 获取厅收益明细离线数据
     * @param paramBean
     * @return
     */
    PageBean<RoomIncomeDetailBean> getRoomIncomeDetailOut(GetRoomIncomeDetailParamBean paramBean);
}
