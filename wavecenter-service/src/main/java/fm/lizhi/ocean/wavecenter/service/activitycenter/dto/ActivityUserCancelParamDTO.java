package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ActivityUserCancelParamDTO {

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 取消原因
     */
    private String reason;

    /**
     * 操作的用户ID
     */
    private Long operateUserId;

    private Integer appId;

    /**
     * 目标审核状态
     */
    private Integer targetAuditStatus;

    /**
     * 活动信息
     */
    private ActivityInfoDTO activityInfo;

    /**
     * 操作人
     */
    private String operator;


    /**
     * 版本号
     */
    private Integer version;

}
