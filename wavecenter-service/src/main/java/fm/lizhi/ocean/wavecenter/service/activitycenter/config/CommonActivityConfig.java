package fm.lizhi.ocean.wavecenter.service.activitycenter.config;

import java.util.List;

public interface CommonActivityConfig {

    String getBannerActionJson();

    String getBannerPlateId();

    String getBannerPlateName();

    Integer getPlateTypeId();

    /**
     * 官方联系方式
     */
    String getOfficialContractNumber();

    /**
     * 官方联系人
     */
    String getOfficialContract();

    /**
     * 获取转存资源的前缀
     */
    String getResourceTransferUrlPrefix(String fileName);

    /**
     * 最少提前多少分钟申请活动
     */
    Integer getMinApplyPreactMin();

    /**
     * 官方提报，活动开始前N分钟不允许修改
     */
    Integer getOfficialModifyActivityBeforeStartMin();

    /**
     * 最大提前几天申请活动
     */
    Integer getMaxPreactApplyDay();

    /**
     * 活动最大举办分钟数
     */
    Integer getMaxActivityPeriodMin();

    /**
     * banner所在排序
     */
    Integer getBannerPlateIndex();

    /**
     * 获取官频位时长限制列表, 单位为分钟
     *
     * @return 官频位时长限制列表
     */
    List<Integer> getOfficialSeatDurationLimits();

    /**
     * 获取官频位可选座位号列表
     *
     * @return 官频位可选座位号列表
     */
    List<Integer> getOfficialSeatAvailableNumbers();

    /**
     * 获取官频位默认选中的座位号列表, 必须是可选座位号列表的子集
     *
     * @return 官频位默认选中的座位号列表
     */
    List<Integer> getOfficialSeatDefaultNumbers();

     /**
     * web站域名
     */
    String getWaveCenterDomain();

    /**
     * web站活动记录链接
     */
    String getWebActivityRecordUrl();

    /**
     * 是否支持查询时间表节目单资源
     */
    boolean isSupportProgramResourceTime();


    /**
     * 支持的厅品类值列表
     * @return
     */
    List<Integer> getSupportRoomCategoryValues();
}
