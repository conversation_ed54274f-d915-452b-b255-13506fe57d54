package fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 保存任务模版请求DTO
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaveTaskTemplateDTO {

    /**
     * 任务模版列表
     */
    private List<TaskTemplateItemDTO> taskTemplateList;

    /**
     * 业务ID
     */
    @NotNull(message = "业务ID不能为空")
    private Integer appId;

}