package fm.lizhi.ocean.wavecenter.service.activitycenter.processor.xm;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.service.activitycenter.processor.IRecommendCardGiveProcess;
import org.springframework.stereotype.Component;

@Component
public class XmRecommendCardGiveProcess implements IRecommendCardGiveProcess {
    @Override
    public String getGiveReason(String activityName) {
        return activityName + "发放";
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.XIMI;
    }
}
