package fm.lizhi.ocean.wavecenter.service.anchor.singer.convert;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerPreAuditBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseApplyMenuConfig;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerMenuConfigDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;

import java.util.List;

import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerPreAuditConfigBean;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerAuditConfigDTO;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface SingerConfigConvert {

    SingerConfigConvert I = Mappers.getMapper(SingerConfigConvert.class);

    @Mapping(target = "code", source = "configCode")
    SingerPreAuditConfigBean auditConfig2Bean(SingerAuditConfigDTO auditConfig);

    
    List<SingerPreAuditConfigBean> auditConfigList2BeanList(List<SingerAuditConfigDTO> auditConfigList);

    List<SingerPreAuditBean> auditDTO2PreAuditBean(List<SingerAuditConfigDTO> auditConfigList);

    @Mapping(target = "openWeakLinkVocalRoom", ignore = true)
    ResponseApplyMenuConfig convertResponseSaveApplyMenuConfig(SingerMenuConfigDTO dto);
}
