package fm.lizhi.ocean.wavecenter.service.activitycenter.manager;

import com.google.common.collect.Lists;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.*;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.*;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityApplyService;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityClassificationConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseActivityResource;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.constants.TimeConstant;
import fm.lizhi.ocean.wavecenter.common.dto.DateDTO;
import fm.lizhi.ocean.wavecenter.common.utils.DateTimeUtils;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.ActivityConfig;
import fm.lizhi.ocean.wavecenter.service.activitycenter.constatns.ActivityApplyErrorTipConstant;
import fm.lizhi.ocean.wavecenter.service.activitycenter.convert.ActivityApplyConverter;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityApplyContext;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityFlowResourceDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityInfoDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityOfficialSeatTimeDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityParamDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.handler.ApplyRuleFilter;
import fm.lizhi.ocean.wavecenter.service.activitycenter.handler.ApplyRuleFilterFactory;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityClassificationManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityResourceManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityRuleManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityTemplateManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityTemplateFlowResourceDTO;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityTemplateFlowResourceExtraDTO;
import fm.lizhi.ocean.wavecenter.service.common.dto.ActivityApplyDataAuditParamDTO;
import fm.lizhi.ocean.wavecenter.service.common.dto.ActivityApplyDataAuditResultDTO;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityApplyService.*;

@Slf4j
@Component
public class ActivityApplyCheckManager {

    @Autowired
    private ActivityRuleManager activityRuleManager;

    @Autowired
    private ApplyRuleFilterFactory applyRuleFilterFactory;

    @Autowired
    private ActivityApplyConverter activityApplyConvert;

    @Autowired
    private AuditContentManager auditContentManager;

    @Autowired
    private ActivityConfig config;

    @Autowired
    private ActivityTemplateManager activityTemplateManager;

    @Autowired
    private ActivityApplyManager activityApplyManager;

    @Autowired
    private ActivityOfficialSeatManager activityOfficialSeatManager;

    @Autowired
    private ActivityClassificationManager activityClassificationManager;

    @Autowired
    private ActivityResourceManager activityResourceManager;


    @Autowired
    private FamilyManager familyManager;

    /**
     * 提报校验
     *
     * @param activityParamDTO 参数
     * @return 结果
     */
    public Result<Void> applyCheck(ActivityParamDTO activityParamDTO) {
        //查询出活动分类的类型
        ActivityClassificationConfigBean classification = activityClassificationManager.getActivityClassification(activityParamDTO.getClassId());
        if (classification == null) {
            log.warn("applyCheck.classification no exist paramBean:{}", activityParamDTO);
            return RpcResult.fail(ACTIVITY_APPLY_PARAM_ERROR, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_APPLY_CLASS_ERROR);
        }

        //参数校验
        Result<Void> paramCheckRes = checkActivityApplyParam(activityParamDTO, classification);
        if (RpcResult.isFail(paramCheckRes)) {
            log.warn("参数校验不通过，paramBean:{}, errorMsg:{}", activityParamDTO, paramCheckRes.getMessage());
            return RpcResult.fail(paramCheckRes.rCode(), paramCheckRes.getMessage());
        }

        // 校验活动是否存在
        boolean isExist = activityApplyManager.existSameTimeAndHallActivity(new Date(activityParamDTO.getStartTime()), new Date(activityParamDTO.getEndTime()), activityParamDTO.getNjId());
        if (isExist) {
            return RpcResult.fail(ACTIVITY_APPLY_TIME_RANGE_EXIST_ACTIVITY, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_EXIST_INVALID);
        }

        // 官频位校验
        Result<Void> seatCheckRes = checkActivityOfficialSeat(activityParamDTO);
        if (RpcResult.isFail(seatCheckRes)) {
            log.info("官频位校验不通过，paramBean:{}, errorMsg:{}", activityParamDTO, seatCheckRes.target());
            return RpcResult.fail(seatCheckRes.rCode(), seatCheckRes.getMessage());
        }

        //资源有效性校验
        return checkActivityFlowResourceIsEnable(activityParamDTO);
    }

    /**
     * 查询并返回家族ID
     *
     * @param njId      厅主ID
     * @param applyType 申请类型
     * @return 结果
     */
    public Result<Long> checkAndGetFamilyId(Long njId, ActivityApplyTypeEnum applyType) {
        UserInFamilyBean userInFamily = familyManager.getUserInFamily(njId);
        if (userInFamily == null) {
            return RpcResult.fail(ACTIVITY_APPLY_ERROR, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_FAIL);
        }
        if (userInFamily.getFamilyId() == null || userInFamily.getFamilyId() <= 0L) {
            // 官方提报有可能是普通用户。如果在家族中，返回家族ID；不在家族中，直接返回0
            if (Objects.equals(applyType, ActivityApplyTypeEnum.OFFICIAL_APPLY)) {
                return RpcResult.success(0L);
            }
            return RpcResult.fail(ACTIVITY_APPLY_PARAM_ERROR, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_FAMILY_INFO_INVALID);
        }

        return RpcResult.success(userInFamily.getFamilyId());
    }

    public Result<Void> validateOfficeSeatAndResource(ActivityParamDTO activityParamDTO, ActivityFlowResourceDTO oldOfficialSeatResource) {
        // 官频位校验
        Result<Void> result = checkActivityModifyOfficialSeat(activityParamDTO, oldOfficialSeatResource);
        if (RpcResult.isFail(result)) {
            log.info("官频位校验不通过，paramBean:{}, errorMsg:{}", activityParamDTO, result.getMessage());
            return RpcResult.fail(result.rCode(), result.getMessage());
        }

        //修改无需校验提报次数，仅做资源有效性校验
        return checkActivityFlowResourceIsEnable(activityParamDTO);
    }

    /**
     * 黑名单过滤
     *
     * @param activityParamDTO 参数
     * @return 结果
     */
    public Result<Void> preBlackFilter(ActivityParamDTO activityParamDTO) {
        ActivityApplyContext context = new ActivityApplyContext().setParamDTO(activityParamDTO);
        ActivityRuleConfigBean rule = activityRuleManager.getActivityRuleByRuleTypeAndAppId(activityParamDTO.getAppId(), ActivityApplyRuleEnum.BLACK_LIST);
        if (rule != null) {
            ApplyRuleFilter handler = applyRuleFilterFactory.getHandler(rule.getRuleType());
            if (handler == null) {
                return RpcResult.success();
            }

            Result<Void> filterResult = handler.filter(context, rule);
            if (RpcResult.isFail(filterResult)) {
                log.info("apply rule filter fail. activityName:{}, ruleType:{}", activityParamDTO.getName(), rule.getRuleType());
                return filterResult;
            }
        }
        return RpcResult.success();
    }

    public Result<Void> applyRuleFilter(ActivityParamDTO activityParamDTO) {
        Result<List<ActivityRuleConfigBean>> ruleResult = activityRuleManager.listActivityRule(activityParamDTO.getAppId());
        if (RpcResult.isFail(ruleResult)) {
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_FAIL, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_FAIL);
        }

        ActivityApplyContext context = new ActivityApplyContext().setParamDTO(activityParamDTO);
        List<ActivityRuleConfigBean> ruleConfigBeanList = ruleResult.target();
        for (ActivityRuleConfigBean ruleConfigBean : ruleConfigBeanList) {
            if (Objects.equals(ruleConfigBean.getRuleType(), ActivityApplyRuleEnum.BLACK_LIST.getType())) {
                //黑名单在请求入口处就校验了，不用重复校验
                continue;
            }
            ApplyRuleFilter handler = applyRuleFilterFactory.getHandler(ruleConfigBean.getRuleType());
            if (handler == null) {
                continue;
            }

            Result<Void> filterResult = handler.filter(context, ruleConfigBean);
            if (RpcResult.isFail(filterResult)) {
                log.info("apply rule filter fail. activityName:{}, ruleType:{}", activityParamDTO.getName(), ruleConfigBean.getRuleType());
                return filterResult;
            }
        }

        return RpcResult.success();
    }

    /**
     * 送审活动申请数据
     *
     * @param activityParamDTO 参数
     * @return 送审结果
     */
    public Result<Void> checkActivityApplyData(ActivityParamDTO activityParamDTO) {
        if (!config.getEnableAuditActivityData()) {
            return RpcResult.success();
        }
        ActivityApplyDataAuditParamDTO paramDTO = activityApplyConvert.buildAuditParamDTO(activityParamDTO);
        Result<ActivityApplyDataAuditResultDTO> result = auditContentManager.auditActivityApplyData(paramDTO);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            //不成功，直接返回不通过
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_ERROR, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_FAIL);
        }

        //审核不通过
        if (!result.target().isResult()) {
            return RpcResult.fail(ACTIVITY_APPLY_DATA_AUDIT_NO_PASS, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_AUDIT_DATA_INVALID);
        }
        return RpcResult.success();
    }

    /**
     * 参数校验
     *
     * @param activityParamDTO 参数
     * @param classification   分类
     * @return 校验结果，不通过时，返回
     */
    public Result<Void> checkActivityApplyParam(ActivityParamDTO activityParamDTO, ActivityClassificationConfigBean classification) {
        boolean isRoomWar = classification == null ? false : classification.getBigClassType() == ActivityBigClassTypeEnum.ROOM_WAR.getType();
        //名字不能太长
        if (activityParamDTO.getName().length() > config.getMaxActivityNameLength()) {
            String msg = String.format(ActivityApplyErrorTipConstant.APPLY_ACTIVITY_NAME_INVALID, config.getMaxActivityNameLength());
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, msg);
        }
        if (activityParamDTO.getRoomAnnouncement() != null && activityParamDTO.getRoomAnnouncement().length() > config.getMaxRoomAnnouncementLength()) {
            String msg = String.format(ActivityApplyErrorTipConstant.APPLY_ANNOUNCEMENT_INVALID, config.getMaxRoomAnnouncementLength());
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, msg);
        }

        if (activityParamDTO.getGoal() != null && activityParamDTO.getGoal().length() > config.getMaxActivityGoalLength()) {
            String msg = String.format(ActivityApplyErrorTipConstant.APPLY_ACTIVITY_GOAL_INVALID, config.getMaxActivityGoalLength());
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, msg);
        }

//        if (activityParamDTO.getIntroduction() != null && activityParamDTO.getIntroduction().length() > config.getMaxActivityIntroductionLength()) {
//            String msg = String.format(ActivityApplyErrorTipConstant.APPLY_ACTIVITY_INTRODUCTION_INVALID, config.getMaxActivityIntroductionLength());
//            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, msg);
//        }

        if (activityParamDTO.getAuxiliaryPropUrl() != null && activityParamDTO.getAuxiliaryPropUrl().size() > config.getMaxAuxiliaryPropUrlCount()) {
            String msg = String.format(ActivityApplyErrorTipConstant.APPLY_ACTIVITY_AUXILIARY_PROP_INVALID, config.getMaxAuxiliaryPropUrlCount());
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, msg);
        }

        if (activityParamDTO.getRoomAnnouncementImgUrl() != null && activityParamDTO.getRoomAnnouncementImgUrl().size() > config.getMaxRoomAnnouncementImgUrlCount()) {
            String msg = String.format(ActivityApplyErrorTipConstant.APPLY_ACTIVITY_ANNOUNCEMENT_URL_INVALID, config.getMaxAuxiliaryPropUrlCount());
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, msg);
        }

        if (activityParamDTO.getAccompanyNjIds() != null && activityParamDTO.getAccompanyNjIds().size() > config.getMaxAccompanyNjIdsCount()) {
            String msg = String.format(ActivityApplyErrorTipConstant.APPLY_ACTIVITY_ACCOMPANY_NJ_IDS_COUNT_INVALID, config.getMaxAccompanyNjIdsCount());
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, msg);
        }

        //如果是厅战，必须传海报
        if (isRoomWar && StringUtils.isBlank(activityParamDTO.getPosterUrl())) {
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_APPLY_POSTER_URL_EMPTY);
        }


        Long startTime = activityParamDTO.getStartTime();
        Long endTime = activityParamDTO.getEndTime();
        if (endTime <= startTime) {
            String msg = ActivityApplyErrorTipConstant.APPLY_ACTIVITY_TIME_INVALID;
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, msg);
        }

        long currentTime = System.currentTimeMillis();
        long absTime = startTime - currentTime;
        if (startTime < currentTime) {
            String msg = ActivityApplyErrorTipConstant.APPLY_ACTIVITY_TIME_RANGE_INVALID;
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, msg);
        }


        //主播提报方式，活动开始N分钟前，不允许提交申请
        if (activityParamDTO.getApplyType() == ActivityApplyTypeEnum.NJ_APPLY
                && (absTime / TimeConstant.ONE_MINUTE_MILLISECOND) < config.getBizConfig().getMinApplyPreactMin()) {
            String msg = String.format(ActivityApplyErrorTipConstant.APPLY_ACTIVITY_APPLY_PREACT_MIN_INVALID, config.getBizConfig().getMinApplyPreactMin() / 60);
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, msg);
        }

        //主播提报方式，活动最多提前N天举办
        if (activityParamDTO.getApplyType() == ActivityApplyTypeEnum.NJ_APPLY &&
                (absTime / TimeConstant.ONE_DAY_MILLISECOND) > config.getBizConfig().getMaxPreactApplyDay()) {
            String msg = String.format(ActivityApplyErrorTipConstant.APPLY_ACTIVITY_APPLY_PREACT_MAX_INVALID, config.getBizConfig().getMaxPreactApplyDay());
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, msg);
        }

        //主播提报方式，活动举办时长不超过N分钟
        if (activityParamDTO.getApplyType() == ActivityApplyTypeEnum.NJ_APPLY &&
                ((endTime - startTime) / TimeConstant.ONE_MINUTE_MILLISECOND) > config.getBizConfig().getMaxActivityPeriodMin()) {
            String msg = String.format(ActivityApplyErrorTipConstant.APPLY_ACTIVITY_APPLY_PERIOD_MAX_INVALID, config.getBizConfig().getMaxActivityPeriodMin() / 60);
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, msg);
        }

        List<ActivityFlowResourceBean> flowResources = activityParamDTO.getFlowResources();
        if (CollectionUtils.isNotEmpty(flowResources)) {
            for (ActivityFlowResourceBean bean : flowResources) {
                AutoConfigResourceEnum configResourceEnum = AutoConfigResourceEnum.getByResourceCode(bean.getResourceCode());
                if (configResourceEnum == null) {
                    continue;
                }
                if (configResourceEnum == AutoConfigResourceEnum.BANNER && (bean.getExtra() == null || StringUtils.isBlank(bean.getExtra().getScale()))) {
                    log.info("流量资源参数校验不通过，banner图的宽高比不能为空，paramBean:{}", bean);
                    return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_FLOW_RESOURCE_BANNER_SCALE_INVALID);
                }

                boolean isProgramme = configResourceEnum == AutoConfigResourceEnum.PROGRAMME || configResourceEnum == AutoConfigResourceEnum.HALL_PROGRAMME;
                //非厅战，图片不能不传
                if (configResourceEnum.isSupportUpload() && StringUtils.isBlank(bean.getImageUrl())) {
                    if (isRoomWar && isProgramme) {
                        //是厅战，且是节目单，允许不传图片
                        continue;
                    }
                    log.info("流量资源参数校验不通过，paramBean:{}", bean);
                    return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_FLOW_RESOURCE_IMAGE_INVALID);
                }
            }
        }

        List<ActivityProcessBean> processList = activityParamDTO.getProcessList();
        if (CollectionUtils.isNotEmpty(processList)) {
            for (ActivityProcessBean bean : processList) {
                if (StringUtils.isBlank(bean.getName())) {
                    return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_PROCESS_NAME_INVALID);
                }

                if (StringUtils.isNotBlank(bean.getExplanation()) && bean.getExplanation().length() > config.getMaxProcessExplanationLength()) {
                    String msg = String.format(ActivityApplyErrorTipConstant.APPLY_ACTIVITY_PROCESS_EXPLANATION_INVALID, config.getMaxProcessExplanationLength());
                    return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, msg);
                }
            }
        }

        if (activityParamDTO.getHostId() == null) {
            activityParamDTO.setHostId(0L);
        }
        return RpcResult.success();
    }

    /**
     * 参数校验
     *
     * @param activityParamDTO 参数
     * @param classification   分类
     * @return 校验结果，不通过时，返回
     */
    public Result<Void> checkModifyActivityParam(ActivityParamDTO activityParamDTO, ActivityClassificationConfigBean classification) {
        boolean isRoomWar = classification == null ? false : classification.getBigClassType() == ActivityBigClassTypeEnum.ROOM_WAR.getType();
        //名字不能太长
        if (activityParamDTO.getName().length() > config.getMaxActivityNameLength()) {
            String msg = String.format(ActivityApplyErrorTipConstant.APPLY_ACTIVITY_NAME_INVALID, config.getMaxActivityNameLength());
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, msg);
        }
        if (activityParamDTO.getRoomAnnouncement() != null && activityParamDTO.getRoomAnnouncement().length() > config.getMaxRoomAnnouncementLength()) {
            String msg = String.format(ActivityApplyErrorTipConstant.APPLY_ANNOUNCEMENT_INVALID, config.getMaxRoomAnnouncementLength());
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, msg);
        }

        if (activityParamDTO.getGoal() != null && activityParamDTO.getGoal().length() > config.getMaxActivityGoalLength()) {
            String msg = String.format(ActivityApplyErrorTipConstant.APPLY_ACTIVITY_GOAL_INVALID, config.getMaxActivityGoalLength());
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, msg);
        }

//        if (activityParamDTO.getIntroduction() != null && activityParamDTO.getIntroduction().length() > config.getMaxActivityIntroductionLength()) {
//            String msg = String.format(ActivityApplyErrorTipConstant.APPLY_ACTIVITY_INTRODUCTION_INVALID, config.getMaxActivityIntroductionLength());
//            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, msg);
//        }

        if (activityParamDTO.getAuxiliaryPropUrl() != null && activityParamDTO.getAuxiliaryPropUrl().size() > config.getMaxAuxiliaryPropUrlCount()) {
            String msg = String.format(ActivityApplyErrorTipConstant.APPLY_ACTIVITY_AUXILIARY_PROP_INVALID, config.getMaxAuxiliaryPropUrlCount());
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, msg);
        }

        if (activityParamDTO.getRoomAnnouncementImgUrl() != null && activityParamDTO.getRoomAnnouncementImgUrl().size() > config.getMaxRoomAnnouncementImgUrlCount()) {
            String msg = String.format(ActivityApplyErrorTipConstant.APPLY_ACTIVITY_ANNOUNCEMENT_URL_INVALID, config.getMaxAuxiliaryPropUrlCount());
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, msg);
        }

        if (activityParamDTO.getAccompanyNjIds() != null && activityParamDTO.getAccompanyNjIds().size() > config.getMaxAccompanyNjIdsCount()) {
            String msg = String.format(ActivityApplyErrorTipConstant.APPLY_ACTIVITY_ACCOMPANY_NJ_IDS_COUNT_INVALID, config.getMaxAccompanyNjIdsCount());
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, msg);
        }

        //如果是厅战，必须传海报
        if (isRoomWar && StringUtils.isBlank(activityParamDTO.getPosterUrl())) {
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_APPLY_POSTER_URL_EMPTY);
        }

        Long startTime = activityParamDTO.getStartTime();
        Long endTime = activityParamDTO.getEndTime();
        if (endTime <= startTime) {
            String msg = ActivityApplyErrorTipConstant.APPLY_ACTIVITY_TIME_INVALID;
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, msg);
        }

        long currentTime = System.currentTimeMillis();
        if (startTime < currentTime) {
            String msg = ActivityApplyErrorTipConstant.APPLY_ACTIVITY_TIME_RANGE_INVALID;
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, msg);
        }

        //主播提报方式，活动举办时长不超过N分钟
        if (activityParamDTO.getApplyType() == ActivityApplyTypeEnum.NJ_APPLY &&
                ((endTime - startTime) / TimeConstant.ONE_MINUTE_MILLISECOND) > config.getBizConfig().getMaxActivityPeriodMin()) {
            String msg = String.format(ActivityApplyErrorTipConstant.APPLY_ACTIVITY_APPLY_PERIOD_MAX_INVALID, config.getBizConfig().getMaxActivityPeriodMin() / 60);
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, msg);
        }

        List<ActivityFlowResourceBean> flowResources = activityParamDTO.getFlowResources();
        if (CollectionUtils.isNotEmpty(flowResources)) {
            for (ActivityFlowResourceBean bean : flowResources) {
                AutoConfigResourceEnum configResourceEnum = AutoConfigResourceEnum.getByResourceCode(bean.getResourceCode());
                if (configResourceEnum == null) {
                    continue;
                }
                if (configResourceEnum == AutoConfigResourceEnum.BANNER && (bean.getExtra() == null || StringUtils.isBlank(bean.getExtra().getScale()))) {
                    log.info("流量资源参数校验不通过，banner图的宽高比不能为空，paramBean:{}", bean);
                    return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_FLOW_RESOURCE_BANNER_SCALE_INVALID);
                }

                boolean isProgramme = configResourceEnum == AutoConfigResourceEnum.PROGRAMME || configResourceEnum == AutoConfigResourceEnum.HALL_PROGRAMME;
                //非厅战，图片不能不传
                if (configResourceEnum.isSupportUpload() && StringUtils.isBlank(bean.getImageUrl())) {
                    if (isRoomWar && isProgramme) {
                        //是厅战，且是节目单，允许不传图片
                        continue;
                    }
                    log.info("流量资源参数校验不通过，paramBean:{}", bean);
                    return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_FLOW_RESOURCE_IMAGE_INVALID);
                }
            }
        }

        List<ActivityProcessBean> processList = activityParamDTO.getProcessList();
        if (CollectionUtils.isNotEmpty(processList)) {
            for (ActivityProcessBean bean : processList) {
                if (StringUtils.isBlank(bean.getName())) {
                    return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_PROCESS_NAME_INVALID);
                }

                if (StringUtils.isNotBlank(bean.getExplanation()) && bean.getExplanation().length() > config.getMaxProcessExplanationLength()) {
                    String msg = String.format(ActivityApplyErrorTipConstant.APPLY_ACTIVITY_PROCESS_EXPLANATION_INVALID, config.getMaxProcessExplanationLength());
                    return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, msg);
                }
            }
        }

        if (activityParamDTO.getHostId() == null) {
            activityParamDTO.setHostId(0L);
        }
        return RpcResult.success();
    }

    /**
     * 校验官频位资源是否还有可用的
     *
     * @param activityParamDTO 参数
     * @return 结果
     */
    public Result<Void> checkActivityOfficialSeat(ActivityParamDTO activityParamDTO) {
        if (!existOfficialSeat(activityParamDTO)) {
            return RpcResult.success();
        }

        ActivityFlowResourceBean flowResourceBean = activityParamDTO.getFlowResources().stream()
                .filter(bean -> Objects.equals(bean.getResourceCode(), AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode()))
                .findAny().get();

        Result<Void> result = checkOfficialSeatTimeAndPositionInfo(flowResourceBean, activityParamDTO);
        if (RpcResult.isFail(result)) {
            return result;
        }

        return RpcResult.success();
    }

    public Result<Void> validateBaseActivityModifyInfo(ActivityParamDTO activityParamDTO) {
        //查询出活动分类的类型
        ActivityClassificationConfigBean classification = null;
        if (activityParamDTO.getClassId() != null) {
            classification = activityClassificationManager.getActivityClassification(activityParamDTO.getClassId());
            if (classification == null) {
                log.warn("applyCheck.classification no exist paramBean:{}", activityParamDTO);
                return RpcResult.fail(ACTIVITY_APPLY_PARAM_ERROR, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_APPLY_CLASS_ERROR);
            }
        }

        // 校验活动是否存在
        ActivityInfoDTO activityInfo = activityApplyManager.getActivityInfoById(activityParamDTO.getActivityId());
        if (activityInfo == null) {
            return RpcResult.fail(ACTIVITY_MODIFY_ACTIVITY_NOT_EXIST, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_MODIFY_ACTIVITY_NOT_EXIST);
        }

        // 校验活动是否存在
        List<Long> activityIds = activityApplyManager.getSameTimeAndHallActivityId(new Date(activityParamDTO.getStartTime()), new Date(activityParamDTO.getEndTime()), activityInfo.getNjId());
        //如果已经有多个时间交叉的活动，或者仅有一个时间交叉的活动，但是不是当前活动，则说明冲突了
        if (activityIds.size() > 1 || (activityIds.size() == 1 && !activityIds.contains(activityParamDTO.getActivityId()))) {
            return RpcResult.fail(ACTIVITY_APPLY_TIME_RANGE_EXIST_ACTIVITY, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_EXIST_INVALID);
        }

        //活动取消不允许修改
        if (Objects.equals(activityInfo.getAuditStatus(), ActivityAuditStatusEnum.USER_CANCEL.getStatus())
                || Objects.equals(activityInfo.getAuditStatus(), ActivityAuditStatusEnum.OFFICIAL_CANCEL.getStatus())) {
            return RpcResult.fail(ACTIVITY_MODIFY_ACTIVITY_CANCEL, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_MODIFY_ACTIVITY_CANCEL);
        }

        //非后台的修改，非待审核状态不允许修改
        if (!Objects.equals(activityInfo.getAuditStatus(), ActivityAuditStatusEnum.WAITING_AUDIT.getStatus()) && !activityParamDTO.isFromBackend()) {
            return RpcResult.fail(ACTIVITY_MODIFY_ACTIVITY_NOT_AUDIT, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_MODIFY_ACTIVITY_NOT_AUDIT);
        }

        long now = System.currentTimeMillis();
        if (activityInfo.getStartTime().getTime() < now) {
            return RpcResult.fail(ACTIVITY_NOT_MODIFY_ACTIVITY_BEFORE_START, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_NOT_MODIFY_ACTIVITY_ALREADY_END);
        }

        long intervalTime = activityParamDTO.getStartTime() - now;
        //官方提报，活动开始前N分钟不允许修改
        if (intervalTime < (long) config.getBizConfig().getOfficialModifyActivityBeforeStartMin() * TimeConstant.ONE_MINUTE_MILLISECOND) {
            String msg = String.format(ActivityApplyErrorTipConstant.APPLY_ACTIVITY_NOT_MODIFY_ACTIVITY_BEFORE_START, config.getBizConfig().getOfficialModifyActivityBeforeStartMin());
            return RpcResult.fail(ACTIVITY_NOT_MODIFY_ACTIVITY_BEFORE_START, msg);
        }

        //版本号不一致不允许修改
        if (!Objects.equals(activityInfo.getVersion(), activityParamDTO.getVersion())) {
            return RpcResult.fail(ACTIVITY_VERSION_NOT_MATCH, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_MODIFY_VERSION_NOT_MATCH);
        }

        // 基础参数校验
        Result<Void> result = checkModifyActivityParam(activityParamDTO, classification);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.info("参数校验不通过，paramBean:{}, errorMsg:{}", activityParamDTO, result.getMessage());
            return result;
        }

        return RpcResult.success();
    }

    private Result<Void> checkOfficialMaxSeatCount(Integer finalOfficialSeatCount, ActivityParamDTO paramDTO, ActivityFlowResourceBean flowResourceBean, OfficialSeatExtraBean oldOfficialSeatExtraBean) {
        List<ActivityOfficialSeatTimeDTO> officialSeatList = activityOfficialSeatManager.getOfficialSeatList(paramDTO.getAppId(),
                new Date(flowResourceBean.getExtra().getStartTime()),
                new Date(flowResourceBean.getExtra().getEndTime()),
                flowResourceBean.getExtra().getSeat());
        //官频位使用列表为空，说明该时间段范围内都没有被使用
        if (CollectionUtils.isEmpty(officialSeatList)) {
            return RpcResult.success();
        }

        List<DateDTO> oldResourceDates = oldOfficialSeatExtraBean == null
                ? Lists.newArrayList()
                : DateTimeUtils.divideTimeSlots(oldOfficialSeatExtraBean.getStartTime(), oldOfficialSeatExtraBean.getEndTime());
        //转换成map
        Map<Date, DateDTO> oldResourceDateMap = oldResourceDates.stream().collect(Collectors.toMap(DateDTO::getStartTime, Function.identity()));

        List<ActivityOfficialSeatTimeDTO> collectRes = officialSeatList.stream()
                .filter(bean -> !oldResourceDateMap.containsKey(bean.getStartTime()))
                .filter(bean -> bean.getCount() >= finalOfficialSeatCount).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(collectRes)) {
            StringBuilder builder = new StringBuilder();
            for (ActivityOfficialSeatTimeDTO bean : collectRes) {
                if (builder.length() > 0) {
                    builder.append(",");
                }
                String startTimeStr = DateUtil.formatDateToString(bean.getStartTime(), "HH:mm");
                String endTimeStr = DateUtil.formatDateToString(bean.getStartTime(), "HH:mm");
                builder.append(startTimeStr).append("-").append(endTimeStr);
            }
            String msg = ActivityApplyErrorTipConstant.APPLY_ACTIVITY_OFFICIAL_SEAT_COUNT_INVALID;
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, String.format(msg, builder));
        }
        return RpcResult.success();
    }

    private Boolean existOfficialSeat(ActivityParamDTO activityParamDTO) {
        List<ActivityFlowResourceBean> flowResources = activityParamDTO.getFlowResources();
        if (CollectionUtils.isEmpty(flowResources)) {
            return false;
        }

        //校验官频位信息
        List<ActivityFlowResourceBean> officialSeat = flowResources.stream()
                .filter(bean -> Objects.equals(bean.getResourceCode(), AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode()))
                .collect(Collectors.toList());

        return !CollectionUtils.isEmpty(officialSeat);
    }

    private Result<Void> checkOfficialSeatTimeAndPositionInfo(ActivityFlowResourceBean flowResourceBean, ActivityParamDTO activityParamDTO) {
        FlowResourceExtra extra = flowResourceBean.getExtra();
        if (extra == null || extra.getStartTime() == null || extra.getEndTime() == null || extra.getSeat() == null) {
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_OFFICIAL_SEAT_INVALID);
        }

        if (extra.getStartTime() >= extra.getEndTime()) {
            //开始时间不能大于结束时间
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_OFFICIAL_SEAT_TIME_INVALID);
        }

        //官频位时间和活动时间比较，官频位的时间一定在活动时间范围内
        if (!(extra.getStartTime() >= activityParamDTO.getStartTime() && extra.getEndTime() <= activityParamDTO.getEndTime())) {
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_OFFICIAL_SEAT_TIME_RANG_INVALID);
        }

        ActivityTemplateFlowResourceExtraDTO officialSeatExtra = getTemplateFlowResourceExtraBean(activityParamDTO.getTemplateId());
        if (officialSeatExtra == null) {
            log.info("activity apply template not have extra info, templateId:{}", activityParamDTO.getTemplateId());
            return RpcResult.fail(ACTIVITY_APPLY_FAIL, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_FAIL);
        }

        // 官频位座位号限制
        List<Integer> officialSeatNumbers = officialSeatExtra.getOfficialSeatNumbers();
        if (!officialSeatNumbers.contains(extra.getSeat())) {
            String msg = String.format(ActivityApplyErrorTipConstant.APPLY_ACTIVITY_OFFICIAL_SEAT_SEAT_INVALID, officialSeatNumbers);
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, msg);
        }

        return RpcResult.success();
    }

    private ActivityTemplateFlowResourceExtraDTO getTemplateFlowResourceExtraBean(Long templateId) {
        ActivityTemplateFlowResourceDTO getGeneralTemplate = activityTemplateManager.getTemplateOfficialSeat(templateId);
        if (getGeneralTemplate == null) {
            log.info("activity apply template not contain official seat, templateId:{}", templateId);
            return null;
        }
        return getGeneralTemplate.getExtra();
    }

    /**
     * 校验官频位信息
     *
     * @param activityParamDTO        修改活动参数
     * @param oldOfficialSeatResource 旧官频位资源
     * @return 结果
     */
    private Result<Void> checkActivityModifyOfficialSeat(ActivityParamDTO activityParamDTO, ActivityFlowResourceDTO oldOfficialSeatResource) {
        if (!existOfficialSeat(activityParamDTO)) {
            return RpcResult.success();
        }

        ActivityFlowResourceBean flowResourceBean = activityParamDTO.getFlowResources().stream()
                .filter(bean -> Objects.equals(bean.getResourceCode(), AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode()))
                .findAny().get();

        Result<Void> result = checkOfficialSeatTimeAndPositionInfo(flowResourceBean, activityParamDTO);
        if (RpcResult.isFail(result)) {
            return result;
        }

        //否则走提交校验逻辑
        Integer officialSeatCount = getOfficialConfigSeatCount(activityParamDTO);
        activityParamDTO.setMaxSeatCount(officialSeatCount);

        OfficialSeatExtraBean oldExtra = JsonUtil.loads(Optional.ofNullable(oldOfficialSeatResource)
                .map(ActivityFlowResourceDTO::getExtra).orElse(null), OfficialSeatExtraBean.class);
        //校验官频位时段、位置是否变动,无变动不用校验
        FlowResourceExtra newExtra = flowResourceBean.getExtra();
        if (oldExtra != null && newExtra.getStartTime() == oldExtra.getStartTime().getTime()
                && newExtra.getEndTime() == oldExtra.getEndTime().getTime() && Objects.equals(newExtra.getSeat(), oldExtra.getSeat())) {
            return RpcResult.success();
        }
        return checkOfficialMaxSeatCount(officialSeatCount, activityParamDTO, flowResourceBean, oldExtra);
    }

    private Integer getOfficialConfigSeatCount(ActivityParamDTO paramDTO) {
        ActivityRuleConfigBean rule = activityRuleManager.getActivityRuleByRuleTypeAndAppId(paramDTO.getAppId(), ActivityApplyRuleEnum.OFFICIAL_COUNT);
        //默认不限制
        Integer officialSeatCount = Integer.MAX_VALUE;
        if (!Objects.isNull(rule)) {
            OfficialCountRuleBean ruleBean = activityRuleManager.getRuleBean(ActivityApplyRuleEnum.OFFICIAL_COUNT, rule.getRuleJson());
            if (ruleBean != null) {
                officialSeatCount = ruleBean.getCount();
            }
        }
        return officialSeatCount;
    }

    private Result<Void> checkActivityFlowResourceIsEnable(ActivityParamDTO activityParamDTO) {
        ActivityClassificationConfigBean classification = activityClassificationManager.getActivityClassification(activityParamDTO.getClassId());
        if (classification == null) {
            log.warn("checkActivityFlowResourceIsEnable, classification:{} not exist, name:{}", activityParamDTO.getClassId(), activityParamDTO.getName());
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_CLASS_NOT_EXIST_INVALID);
        }

        Result<List<ResponseActivityResource>> levelResourceList = activityResourceManager.listActivityResourceByLevelId(classification.getLevelId(), activityParamDTO.getAppId());
        if (RpcResult.isFail(levelResourceList)) {
            log.warn("listActivityResourceByLevelId is fail. levelId:{}, appId:{}", classification.getLevelId(), activityParamDTO.getAppId());
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_RESOURCE_NOT_EXIST_INVALID);
        }

        //配置了资源，但是实际上对应的等级没有资源配置，可能选错了
        if (CollectionUtils.isNotEmpty(activityParamDTO.getFlowResources()) && CollectionUtils.isEmpty(levelResourceList.target())) {
            log.warn("checkActivityFlowResourceIsEnable, levelResourceList is empty, levelId:{}, appId:{}", classification.getLevelId(), activityParamDTO.getAppId());
            return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_RESOURCE_NOT_EXIST_INVALID);
        }

        Map<Long, ResponseActivityResource> resourceMap = levelResourceList.target().stream()
                .collect(Collectors.toMap(ResponseActivityResource::getId, Function.identity(), (x, y) -> x));

        for (ActivityFlowResourceBean resource : activityParamDTO.getFlowResources()) {
            ResponseActivityResource activityResource = resourceMap.get(resource.getResourceConfigId());
            if (activityResource == null) {
                //参数的资源配置，在资源库中不存在，所以不能保存
                log.warn("checkActivityFlowResourceIsEnable resource is not exist, resource:{} not exist, configId:{}", resource.getResourceConfigId(), resource.getResourceConfigId());
                return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_RESOURCE_CONFIG_INVALID);
            }
            if (activityResource.getStatus() == 0) {
                log.warn("checkActivityFlowResourceIsEnable resource is disable, resource code:{}, resource id:{}", activityResource.getResourceCode(), activityResource.getId());
                return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, String.format(ActivityApplyErrorTipConstant.APPLY_ACTIVITY_RESOURCE_DISABLE_INVALID, activityResource.getName()));
            }
        }
        return RpcResult.success();
    }

}
