package fm.lizhi.ocean.wavecenter.service.activitycenter.impl;

import cn.hutool.core.collection.CollUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseActivityClassification;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityClassificationService;
import fm.lizhi.ocean.wavecenter.api.live.constants.RoomCategoryOptionConstants;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityClassificationManager;
import fm.lizhi.ocean.wavecenter.service.live.handler.LiveRoomHandler;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 */
@ServiceProvider
@Slf4j
public class ActivityClassificationServiceImpl implements ActivityClassificationService {

    @Autowired
    private ActivityClassificationManager activityClassificationManager;

    @Autowired
    private FamilyManager familyManager;

    @Autowired
    private LiveRoomHandler liveRoomHandler;

    @Override
    public Result<List<ResponseActivityClassification>> getClassificationList(int appId) {
        return activityClassificationManager.getClassificationList(appId, null);
    }

    @Override
    public Result<List<ResponseActivityClassification>> getClassificationListByUserId(int appId, Long userId) {
        return ResultHandler.handle(appId, ()->{
            UserInFamilyBean userInFamily = familyManager.getUserInFamily(userId);
            // 厅主和签约主播需要限制品类
            if (userInFamily != null && (userInFamily.isRoom() || userInFamily.isPlayer())) {
                ArrayList<Integer> categoryList = CollUtil.newArrayList(RoomCategoryOptionConstants.UNLIMITED_VALUE);
                liveRoomHandler.getUserSignRoomCategory(userId).ifPresent(roomCategoryEnum -> {
                    categoryList.add(roomCategoryEnum.getValue());
                });
                return activityClassificationManager.getClassificationList(appId, categoryList);
            }
            return activityClassificationManager.getClassificationList(appId, null);
        });
    }
}
