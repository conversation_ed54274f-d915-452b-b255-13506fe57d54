package fm.lizhi.ocean.wavecenter.service.grow.capability.manager;

import fm.lizhi.ocean.wavecenter.service.grow.dto.WcGrowCapabilityDTO;

import java.util.List;

/**
 * 能力项Manager接口
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
public interface WcGrowCapabilityManager {

    /**
     * 查询全部能力项（按appId和环境）
     *
     * @param appId 业务ID
     * @return 能力项列表
     */
    List<WcGrowCapabilityDTO> queryAll(Integer appId);

    /**
     * 新增能力项，参数校验
     *
     * @param code     能力项code
     * @param name     能力项名称
     * @param operator 操作人
     * @param appId    业务ID
     * @return 是否新增成功
     */
    boolean addCapability(String code, String name, String operator, Integer appId);

    /**
     * 修改能力项，参数校验
     *
     * @param id       能力项ID
     * @param code     能力项code
     * @param name     能力项名称
     * @param operator 操作人
     * @param appId    业务ID
     * @return 是否修改成功
     */
    boolean updateCapability(Long id, String code, String name, String operator, Integer appId);
} 