package fm.lizhi.ocean.wavecenter.service.live.config;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.config.AbsBizConfig;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @date 2024/10/27 10:19
 */
@Data
@ConfigurationProperties(prefix = "wavecenter-live")
public class LiveConfig extends AbsBizConfig<CommonLiveConfig> {

    /**
     * 音频切片内网域名
     */
    private String voiceFileInternalHost = "http://doremerecordhl.lzfm.com";

    private PpLiveConfig pp;

    private XmLiveConfig xm;

    private HyLiveConfig hy;

    /**
     * 最近X天有收入的打卡厅
     */
    private Integer checkInRoomRecentRevenueDays = 15;
    private String hourChatPreviewUrl = "https://download-wavecenter.lzpscn1.com/public/wave/d971314f6fb80bd1b9a47ee0e43c453f.png";
    private String dayChatPreviewUrl = "https://download-wavecenter.lzpscn1.com/public/wave/d971314f6fb80bd1b9a47ee0e43c453f.png";
    private String weekChatPreviewUrl = "https://download-wavecenter.lzpscn1.com/public/wave/d971314f6fb80bd1b9a47ee0e43c453f.png";

    /**
     * 打卡私信报告模版
     */
    private String checkInReportMsgModel = "{\"text\":\"尊敬的【%s厅】的厅管理，%s打卡结果如下图, 每小时发送时报,每日0点后发送日报,每周一10点后发送周报。\",\"card\":{\"imageUrl\":\"%s\",\"aspect\":0.381,\"type\":1,\"action\":\"{\\\"bizType\\\":0,\\\"action\\\":\\\"{\\\\\\\"type\\\\\\\":\\\\\\\"7\\\\\\\",\\\\\\\"url\\\\\\\":\\\\\\\"%s\\\\\\\",\\\\\\\"wk\\\\\\\":true,\\\\\\\"isFull\\\\\\\":true,\\\\\\\"isLight\\\\\\\":true,\\\\\\\"urlShareable\\\\\\\":false,\\\\\\\"from\\\\\\\":1}\\\"}\"}}";

    /**
     * 打卡私信报告结束语
     */
    private String checkInReportEndMsgModel = "{\"text\":\"更多厅打卡明细数据（包括未完成任务、排档数、收光/全麦）查看、设置&导出，可查看创作服务中心>>>\",\"clickActionType\":2,\"clickAction\":\"{\\\"bizType\\\":0,\\\"action\\\":\\\"{\\\\\\\"type\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"url\\\\\\\":\\\\\\\"%s\\\\\\\",\\\\\\\"wk\\\\\\\":true,\\\\\\\"isFull\\\\\\\":true,\\\\\\\"isLight\\\\\\\":true,\\\\\\\"urlShareable\\\\\\\":false,\\\\\\\"from\\\\\\\":1}\\\"}\"}";

    /**
     * 是否开启小时报告
     */
    private boolean openHourReport = true;
    /**
     * 是否开启打卡私信中小时的web站跳转通知
     */
    private boolean openHourWebCenterChat = true;

    /**
     * 报告的过期时间 1hour
     */
    private int checkInRoomReportDataExpireSeconds = 60 * 60;

    /**
     * 打卡报告url md5 盐值
     */
    private String checkInMD5SaltValue = "432346Ebuf#*(BF#$^GFB";

    private String checkInReportWhitelistStr = "";

    /**
     * 每分钟发送的厅数量
     */
    private int checkInReportCountPerMin = 1;
    private boolean closeBatchSend = false;
    private boolean closeCheckInReportDataCache = false;

    public LiveConfig() {
        PpLiveConfig ppConfig = new PpLiveConfig();
        XmLiveConfig xmConfig = new XmLiveConfig();
        HyLiveConfig hyConfig = new HyLiveConfig();
        this.pp = ppConfig;
        this.xm = xmConfig;
        this.hy = hyConfig;
        bizConfigMap.put(BusinessEvnEnum.PP.appId(), ppConfig);
        bizConfigMap.put(BusinessEvnEnum.XIMI.appId(), xmConfig);
        bizConfigMap.put(BusinessEvnEnum.HEI_YE.appId(), hyConfig);
    }
}
