package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class PpOfficialActListItemDTO {

    private Long id;
    /**
     * 活动名称
     */
    private String name;
    /**
     * 开始时间戳
     */
    private Long startTime;
    /**
     * 结束时间戳
     */
    private Long endTime;
    /**
     * 状态
     */
    private int status;

}
