package fm.lizhi.ocean.wavecenter.service.live.handler;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import fm.lizhi.ocean.wavecenter.api.live.constants.CheckInDateTypeEnum;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.service.live.config.LiveConfig;
import fm.lizhi.ocean.wavecenter.service.live.manager.WaveCheckInDataManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class DayCheckInRoomStatHandler implements CheckInRoomPeriodStatHandler {

    @Autowired
    private UserManager userManager;
    @Autowired
    private LiveConfig liveConfig;


    @Override
    public boolean support(CheckInDateTypeEnum typeEnum) {
        return typeEnum == CheckInDateTypeEnum.DAY;
    }

    @Override
    public String buildContent(Integer appId, Long njId, Date statTime) {
        List<SimpleUserDto> simpleUserByIds = userManager.getSimpleUserByIds(Collections.singletonList(njId));
        if(CollectionUtils.isEmpty(simpleUserByIds)) {
            log.error("buildContent fail;njId info no exist;appId={};njId={}", appId, njId);
            return StringUtils.EMPTY;
        }
        // 格式化日期部分为"XXX年XXX月XX日"
        DateTime previousDay = DateUtil.offsetDay(statTime, -1);
        DateTime startDate = DateUtil.beginOfDay(previousDay);
        DateTime endDate = DateUtil.endOfDay(previousDay);
        String checkInReportUrl = liveConfig.getBizConfig(appId).getCheckInReportUrl();
        String name = simpleUserByIds.get(0).getName();
        String url = buildReportUrl(CheckInDateTypeEnum.DAY, appId, njId, startDate, endDate, checkInReportUrl, liveConfig.getCheckInMD5SaltValue());
        String dateFormat = DateUtil.format(previousDay, "yyyy年MM月dd日");
        return String.format(liveConfig.getCheckInReportMsgModel(), name, dateFormat, liveConfig.getDayChatPreviewUrl(), url);
    }


    @Override
    public List<Long> populateExtraReceiver(Integer appId, Long njId, Date statTime) {
        return Collections.emptyList();
    }

    @Override
    public boolean isTimeToReport(Date triggerTime) {
        int hour = DateUtil.hour(triggerTime, true);
        //是否是0点
        return hour == 0;
    }

    @Override
    public boolean canSend(Long njId) {
        return true;
    }
}
