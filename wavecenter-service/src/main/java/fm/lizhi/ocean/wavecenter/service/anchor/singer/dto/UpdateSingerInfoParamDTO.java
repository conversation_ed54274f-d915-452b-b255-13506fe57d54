package fm.lizhi.ocean.wavecenter.service.anchor.singer.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class UpdateSingerInfoParamDTO {

    /**
     * 业务ID
     */
    private Integer appId;

    /**
     * 歌手ID
     */
    private Long userId;

    /**
     * 歌手状态 1: 认证中 2: 生效中  3: 已淘汰
     * @see fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum
     */
    private Integer singerStatus;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 淘汰原因
     */
    private String eliminationReason;

    /**
     * 歌手类型
     */
    private Integer singerType;

    /**
     * 家族ID
     */
    private Long familyId;

}
