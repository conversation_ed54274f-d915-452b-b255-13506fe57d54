package fm.lizhi.ocean.wavecenter.service.activitycenter.handler;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityRuleConfigBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyRuleEnum;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityApplyContext;

/**
 * 提报规则过滤器
 */
public interface ApplyRuleFilter {

    /**
     * 过滤器
     *
     * @param context 上下文
     * @return 结果
     */
    Result<Void> filter(ActivityApplyContext context, ActivityRuleConfigBean rule);

    ActivityApplyRuleEnum getRuleTypeEnum();
}
