package fm.lizhi.ocean.wavecenter.service.background.activitycenter.impl;

import com.google.common.collect.Lists;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityFlowResourceAuditBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityFlowResourceBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityResourceAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.*;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseCancelActivity;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseBatchActivityAuditData;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityBatchItemBean;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityFlowResourceDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.*;
import fm.lizhi.ocean.wavecenter.service.activitycenter.convert.ActivityParamConvert;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.AutoConfigResourceEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityOperateService;
import com.alibaba.csp.sentinel.util.AssertUtil;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseActivityResource;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityAdminOperateService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.constants.TimeConstant;
import fm.lizhi.ocean.wavecenter.common.utils.RedisLock;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.ActivityConfig;
import fm.lizhi.ocean.wavecenter.service.activitycenter.constatns.ActivityApplyErrorTipConstant;
import fm.lizhi.ocean.wavecenter.service.activitycenter.constatns.ActivityOperateErrorTipConstant;
import fm.lizhi.ocean.wavecenter.service.activitycenter.convert.ActivityApplyConverter;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityApplyCheckManager;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityApplyManager;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityChatManager;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityOperateManager;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityRedisManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.convert.ActivityAdminOperateConvert;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityAdminOperateManager;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.*;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityApplyFlowResourceManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityResourceManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Slf4j
@ServiceProvider
public class ActivityAdminOperateServiceImpl implements ActivityAdminOperateService {

    private static final int ACTIVITY_DELETE_STATUS = 1;

    @Autowired
    private ActivityApplyManager activityApplyManager;

    @Autowired
    private ActivityChatManager activityChatManager;

    @Autowired
    private ActivityAdminOperateManager activityAdminOperateManager;

    @Autowired
    private ActivityRedisManager activityRedisManager;

    @Autowired
    private ActivityOperateManager activityOperateManager;

    @Autowired
    private ActivityConfig activityConfig;

    @Autowired
    private ActivityRedisManager activityOperationRedisManager;

    @Autowired
    private ActivityApplyCheckManager activityApplyCheckManager;

    @Autowired
    private ActivityResourceManager activityResourceManager;

    @Autowired
    private ActivityApplyConverter activityApplyConvert;

    @Autowired
    private ActivityResourceGiveManager activityResourceGiveManager;

    @Autowired
    private ActivityMaterielGiveManager activityMaterielGiveManager;

    @Autowired
    private ActivityApplyFlowResourceManager activityApplyFlowResourceManager;


    @Override
    public Result<Void> rejectActivityApply(RequestActivityAuditReject param) {
        LogContext.addReqLog("rejectActivityApply.param={}", param);
        LogContext.addResLog("rejectActivityApply.param={}", param);
        return ResultHandler.handle(param.getAppId(), () -> {
            if (param.getActivityId() == null) {
                return RpcResult.fail(REJECT_ACTIVITY_APPLY_PARAM_ERROR);
            }

            ActivityInfoDTO activityInfoDTO = activityApplyManager.getActivityInfoById(param.getActivityId());
            if (activityInfoDTO == null || !Objects.equals(activityInfoDTO.getVersion(), param.getVersion())) {
                return RpcResult.fail(ACTIVITY_VERSION_NOT_MATCH, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_AGREE_VERSION_NOT_MATCH);
            }
            //拒绝操作
            boolean res = activityAdminOperateManager.rejectActivityApply(param.getActivityId(), param.getReason(), param.getOperator(), param.getVersion());
            if (res) {
                //发送拒绝通知
                activityChatManager.rejectedActivityNotice(activityInfoDTO);
            }
            return res ? RpcResult.success() : RpcResult.fail(REJECT_ACTIVITY_APPLY_FAIL);
        });
    }

    @Override
    public Result<String> agreeActivityApply(RequestActivityAuditAgree param) {
        LogContext.addReqLog("agreeActivityApply.param={}", param);
        LogContext.addResLog("agreeActivityApply.param={}", param);

        return ResultHandler.handle(param.getAppId(), () -> {
            try {
                ActivityInfoDTO activityInfo = activityApplyManager.getActivityInfoById(param.getActivityId());
                //基础业务校验
                Result<Void> checkResult = baseCheck(activityInfo, param.getVersion());
                if (RpcResult.isFail(checkResult)) {
                    return RpcResult.fail(checkResult.rCode(), checkResult.getMessage());
                }
                //审批活动
                Result<String> activityApplyResult = activityAdminOperateManager.agreeActivityApply(param.getAppId(), param.getActivityId(), param.getOperator(), param.getFlowResourceList(), param.getVersion());
                if (RpcResult.isFail(activityApplyResult)) {
                    return RpcResult.fail(AGREE_ACTIVITY_APPLY_FAIL, activityApplyResult.getMessage());
                }

                //发送通知给申请人
                activityChatManager.agreeActivityNotice(activityInfo);
                return RpcResult.success(activityApplyResult.target());
            } catch (Exception e) {
                log.error("agreeActivityApply error, param:{}", JsonUtil.dumps(param), e);
                return RpcResult.fail(AGREE_ACTIVITY_APPLY_FAIL, "审核失败，请联系管理员");
            }

        });
    }

    @Override
    public Result<ResponseCancelActivity> adminCancelActivity(RequestCancelActivity request) {
        LogContext.addReqLog("adminCancelActivity.request={}", request);
        LogContext.addResLog("adminCancelActivity.request={}", request);
        return ResultHandler.handle(request.getAppId(), () -> {
            try (RedisLock lock = activityRedisManager.getActivityOperateLock(request.getAppId(), request.getActivityId())) {
                if (!lock.tryLock()) {
                    //尝试获取锁，如果获取不到就不等了，可能这个活动运营正在审批或者其他的用户正在操作，失败，提示给用户
                    log.info("cancelActivity failed. activityId:{}, appId:{}, userId:{}", request.getActivityId(), request.getAppId(), request.getOperator());
                    return RpcResult.fail(ActivityOperateService.CANCEL_ACTIVITY_FAIL, ActivityOperateErrorTipConstant.USER_CANCEL_ACTIVITY_CHANGE);
                }

                //查询活动信息
                ActivityInfoDTO info = activityApplyManager.getActivityInfoById(request.getActivityId());
                Result<Void> checkRes = cancelOperateCheck(info, request);
                if (RpcResult.isFail(checkRes)) {
                    log.info("cancelActivity failed. activityId:{}, appId:{}, userId:{}, checkMsg:{}", request.getActivityId(), request.getAppId(), request.getOperator(), checkRes.getMessage());
                    return RpcResult.fail(checkRes.rCode(), checkRes.getMessage());
                }

                ActivityAuditStatusEnum auditStatusEnum = ActivityAuditStatusEnum.getByStatus(info.getAuditStatus());
                AssertUtil.notNull(auditStatusEnum, ActivityOperateErrorTipConstant.USER_CANCEL_ACTIVITY_FAIL);
                ActivityUserCancelParamDTO cancelParamDTO = ActivityAdminOperateConvert.I.buildActivityUserCancelParam(request);
                cancelParamDTO.setActivityInfo(info);
                //取消活动操作
                Result<String> cancelRes = activityOperateManager.cancelActivityAfterAudit(cancelParamDTO);
                if (RpcResult.isFail(cancelRes)) {
                    log.error("cancelActivity failed. activityId:{}, appId:{}, userId:{}", request.getActivityId(), request.getAppId(), cancelRes.getMessage());
                    return RpcResult.fail(cancelRes.rCode(), cancelRes.getMessage());
                }

                //发送通知给申请人
                activityChatManager.adminCancelActivityNotice(cancelParamDTO.getActivityInfo());
                ResponseCancelActivity resp = new ResponseCancelActivity();
                resp.setErrorMsg(cancelRes.getMessage());
                return RpcResult.success(resp);
            } catch (Exception e) {
                log.error("cancelActivity failed. activityId:{}, appId:{}, userId:{}", request.getActivityId(), request.getAppId(), request.getOperator(), e);
                return RpcResult.fail(ActivityOperateService.CANCEL_ACTIVITY_FAIL, ActivityOperateErrorTipConstant.USER_CANCEL_ACTIVITY_FAIL);
            }
        });
    }

    /**
     * 管理员同意活动审批前置检查
     *
     * @param activityInfo 活动信息
     * @param version      版本号
     * @return 结果
     */
    private Result<Void> baseCheck(ActivityInfoDTO activityInfo, Integer version) {
        long now = System.currentTimeMillis();
        //检查活动是否删除
        if (activityInfo.getDeleted() == ACTIVITY_DELETE_STATUS) {
            return RpcResult.fail(AGREE_ACTIVITY_APPLY_PARAM_ERROR, "活动已删除，不可审批");
        }

        if (activityInfo.getStartTime().getTime() <= now) {
            return RpcResult.fail(AGREE_ACTIVITY_APPLY_PARAM_ERROR, "活动已开始，不可审批");
        }

        //版本号不一致不允许修改
        if (!Objects.equals(activityInfo.getVersion(), version)) {
            return RpcResult.fail(ACTIVITY_VERSION_NOT_MATCH, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_AGREE_VERSION_NOT_MATCH);
        }

        //检查时间
        if ((activityInfo.getStartTime().getTime() - now) / TimeConstant.ONE_MINUTE_MILLISECOND < activityConfig.getPreactTimeMin()) {
            return RpcResult.fail(AGREE_ACTIVITY_APPLY_PARAM_ERROR, String.format("距离活动开始时间不足%d分钟，不可审批", activityConfig.getPreactTimeMin()));
        }

        if (((now - activityInfo.getCreateTime().getTime()) / TimeConstant.ONE_MINUTE_MILLISECOND) < activityConfig.getMinuteAfterAudit()) {
            return RpcResult.fail(AGREE_ACTIVITY_APPLY_PARAM_ERROR, String.format("请在活动提报%d分钟后再审批", activityConfig.getMinuteAfterAudit()));
        }

        //检查活动审核状态状态
        if (!activityInfo.getAuditStatus().equals(ActivityAuditStatusEnum.WAITING_AUDIT.getStatus())) {
            return RpcResult.fail(AGREE_ACTIVITY_APPLY_PARAM_ERROR, "活动状态不正确，不可审批");
        }
        return RpcResult.success();
    }

    /**
     * 取消活动审批前置检查
     *
     * @param info    活动信息
     * @param request 请求参数
     * @return 结果
     */
    private Result<Void> cancelOperateCheck(ActivityInfoDTO info, RequestCancelActivity request) {
        long currentTimeMillis = System.currentTimeMillis();
        if (info == null) {
            return RpcResult.fail(ActivityAdminOperateService.CANCEL_ACTIVITY_PARAM_ERROR, ActivityOperateErrorTipConstant.USER_CANCEL_ACTIVITY_NOT_EXIST);
        }

        //版本号不一致不允许取消
        if (!Objects.equals(info.getVersion(), request.getVersion())) {
            return RpcResult.fail(ActivityAdminOperateService.CANCEL_ACTIVITY_VERSION_NOT_MATCH, ActivityApplyErrorTipConstant.APPLY_ACTIVITY_CANCEL_VERSION_NOT_MATCH);
        }

        //活动状态非待审核或审核通过，不能操作
        if (!Objects.equals(info.getAuditStatus(), ActivityAuditStatusEnum.AUDIT_PASS.getStatus())
                && !Objects.equals(info.getAuditStatus(), ActivityAuditStatusEnum.WAITING_AUDIT.getStatus())) {
            return RpcResult.fail(ActivityAdminOperateService.CANCEL_ACTIVITY_AUDIT_STATUS_ERROR, ActivityOperateErrorTipConstant.USER_CANCEL_ACTIVITY_AUDIT_STATUS_ERROR);
        }

        //已审批，校验时间
        if (Objects.equals(info.getAuditStatus(), ActivityAuditStatusEnum.AUDIT_PASS.getStatus())) {
            //活动开始前N分钟，允许取消
            if (((info.getStartTime().getTime() - currentTimeMillis) / TimeConstant.ONE_MINUTE_MILLISECOND) < activityConfig.getPreactCancelActivityTimeMin()) {
                String msg = String.format(ActivityOperateErrorTipConstant.USER_CANCEL_ACTIVITY_OVER_TIME, activityConfig.getPreactCancelActivityTimeMin());
                return RpcResult.fail(ActivityAdminOperateService.CANCEL_ACTIVITY_TIME_ERROR, msg);
            }
        }

        return RpcResult.success();
    }

    @Override
    public Result<ResponseActivityModifyBean> adminModifyActivity(@NotNull @Valid RequestActivityModifyBean activityModifyBean) {
        LogContext.addReqLog("activityModify.paramBean={}", JsonUtil.dumps(activityModifyBean));
        LogContext.addResLog("activityModify.paramBean={}", JsonUtil.dumps(activityModifyBean));
        return ResultHandler.handle(activityModifyBean.getAppId(), () -> {
            // 兼容旧参数
            if (activityModifyBean.getAvatarWidgetId() != null) {
                activityModifyBean.setAvatarWidgetIds(Lists.newArrayList(activityModifyBean.getAvatarWidgetId()));
            }
            if (activityModifyBean.getRoomBackgroundId() != null) {
                activityModifyBean.setRoomBackgroundIds(Lists.newArrayList(activityModifyBean.getRoomBackgroundId()));
            }

            // 1. 增加活动级别分布式锁
            try (RedisLock redisLock = activityOperationRedisManager.getModifyLock(activityModifyBean.getAppId(), activityModifyBean.getActivityId())) {
                if (!redisLock.tryLock()) {
                    return RpcResult.fail(ActivityAdminOperateService.ACTIVITY_MODIFY_TOO_FAST, ActivityOperateErrorTipConstant.ACTIVITY_MODIFY_TOO_FAST);
                }

                Long activityId = activityModifyBean.getActivityId();

                ActivityParamDTO activityParamDTO = ActivityParamConvert.INSTANCE.convertModifyBeanToDTO(activityModifyBean, true);

                //基础信息校验
                Result<Void> result = activityApplyCheckManager.validateBaseActivityModifyInfo(activityParamDTO);
                if (RpcResult.isFail(result)) {
                    return RpcResult.fail(result.rCode(), result.getMessage());
                }

                //内容数据审核校验，低频，这里不再比对旧数据，直接全部送审
                Result<Void> auditRes = activityApplyCheckManager.checkActivityApplyData(activityParamDTO);
                if (RpcResult.isFail(auditRes)) {
                    return RpcResult.fail(auditRes.rCode(), auditRes.getMessage());
                }

                List<ActivityFlowResourceDTO> oldFlowResourceDTOS = activityApplyManager.getActivityFlowResourceByActivityId(activityId);
                ActivityFlowResourceDTO oldOfficialSeatResource = getOldOfficialSeatResource(oldFlowResourceDTOS);

                //资源校验(含官频位)
                Result<Void> officeSeatAndResourceResult = activityApplyCheckManager.validateOfficeSeatAndResource(activityParamDTO, oldOfficialSeatResource);
                // 构建落库数据，全量构建
                ActivityApplyParamDTO applyParamDTO = activityApplyConvert.buildApplyParam(activityParamDTO, null);
                applyParamDTO.setAuditStatus(activityModifyBean.getAuditStatus());

                if (RpcResult.isFail(officeSeatAndResourceResult)) {
                    return RpcResult.fail(officeSeatAndResourceResult.rCode(), officeSeatAndResourceResult.getMessage());
                }

                //查询旧的流量资源的发放记录，并删掉已经发放的资源
                Result<String> deleteRes = activityResourceGiveManager.deleteAlreadyGiveResource(activityId);
                if (RpcResult.isFail(deleteRes)) {
                    log.warn("adminModifyActivity.deleteAlreadyGiveResource fail, activityId:{}, code={}, msg={}", applyParamDTO.getActivityId(), deleteRes.rCode(), deleteRes.getMessage());
                    return RpcResult.fail(ActivityAdminOperateService.ACTIVITY_MODIFY_DELETE_RESOURCE_FAIL, ActivityOperateErrorTipConstant.ACTIVITY_MODIFY_DELETE_RESOURCE_FAIL);
                }

                //修改操作
                Result<Void> modifyActivity = activityApplyManager.modifyActivityApply(applyParamDTO, oldOfficialSeatResource);
                if (RpcResult.isFail(modifyActivity)) {
                    //失败了直接返回
                    log.warn("adminModifyActivity.modifyActivityApply fail, activityId:{}, code={}, msg={}", applyParamDTO.getActivityId(), modifyActivity.rCode(), modifyActivity.getMessage());
                    return RpcResult.fail(ActivityAdminOperateService.ACTIVITY_MODIFY_RECORD_FAIL, ActivityOperateErrorTipConstant.ACTIVITY_MODIFY_RECORD_MODIFY_FAIL);
                }

                if (Objects.equals(applyParamDTO.getAuditStatus(), ActivityAuditStatusEnum.AUDIT_PASS.getStatus())) {
                    //审批通过的，需要再重新初始化和发放资源
                    // 初始化资源
                    List<ActivityFlowResourceAuditBean> activityFlowResourceAuditBeans = buildFlowResourceAuditBean(activityParamDTO.getFlowResources());
                    Result<Void> initResult = activityMaterielGiveManager.initActivityMateriel(activityId, activityFlowResourceAuditBeans);
                    if (RpcResult.isFail(initResult)) {
                        log.error("adminModifyActivity.initActivityMateriel failed. activityId:{}，code={}, msg={}", activityId, initResult.rCode(), initResult.getMessage());
                        return RpcResult.fail(ActivityAdminOperateService.ACTIVITY_MODIFY_RECORD_FAIL, ActivityOperateErrorTipConstant.ACTIVITY_MODIFY_RECORD_MODIFY_FAIL);
                    }

                    boolean updateRes = activityApplyFlowResourceManager.batchUpdateFlowResource(activityId, activityFlowResourceAuditBeans);
                    log.info("adminModifyActivity.batchUpdateFlowResource status. activityId:{},res={}", activityId, updateRes);

                    //发放改动后的资源
                    Result<List<ResourceGiveResultDTO>> giveResult = activityResourceGiveManager.realTimeGiveResource(activityModifyBean.getAppId(), activityModifyBean.getActivityId());
                    //资源发放失败了，就直接提示即可
                    String errorMsg = buildErrorMessage(giveResult.target());
                    if (StringUtils.isNotBlank(errorMsg)) {
                        return RpcResult.fail(ActivityAdminOperateService.ACTIVITY_MODIFY_RESOURCE_GIVE_FAIL, errorMsg);
                    }
                }
                return RpcResult.success(new ResponseActivityModifyBean());
            } catch (Exception e) {
                log.error("activityModify failed. activityId:{}, appId:{}, userId:{}", activityModifyBean.getActivityId(), activityModifyBean.getAppId(), activityModifyBean.getApplicantUid(), e);
                return RpcResult.fail(ActivityAdminOperateService.ACTIVITY_MODIFY_FAIL, ActivityOperateErrorTipConstant.ACTIVITY_MODIFY_FAIL);
            }
        });
    }

    /**
     * 获取旧官频位资源信息
     *
     * @param oldFlowResourceDTOS 旧的官频位资源
     * @return 官频位资源对象
     */
    private ActivityFlowResourceDTO getOldOfficialSeatResource(List<ActivityFlowResourceDTO> oldFlowResourceDTOS) {
        List<Long> oldResourceConfigIds = oldFlowResourceDTOS.stream().map(ActivityFlowResourceDTO::getResourceConfigId).collect(Collectors.toList());
        List<ResponseActivityResource> oldResources = activityResourceManager.getActivityResourcesByIds(oldResourceConfigIds);
        //找到官频位资源配置信息
        Optional<ResponseActivityResource> oldOfficialSeatResource = oldResources.stream()
                .filter(resource -> Objects.equals(resource.getResourceCode(), AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode()))
                .findAny();

        //找到旧官频位资源
        return oldFlowResourceDTOS.stream()
                .filter(dto -> oldOfficialSeatResource.isPresent() &&
                        Objects.equals(dto.getResourceConfigId(), oldOfficialSeatResource.get().getId()))
                .findAny().orElse(null);
    }

    /**
     * 构建错误消息
     *
     * @param resourceGiveResultList 发放结果
     * @return 错误消息
     */
    private String buildErrorMessage(List<ResourceGiveResultDTO> resourceGiveResultList) {
        if (CollectionUtils.isEmpty(resourceGiveResultList)) {
            return "";
        }
        // 如果整体是成功的，则判断列表里面是否有error信息，有则返回失败
        return resourceGiveResultList.stream().filter(dto -> dto.getErrorList() != null)
                .flatMap(dto -> dto.getErrorList().stream())
                .map(dto -> dto.getName() + "发放错误，错误原因:" + dto.getErrorMessage())
                .collect(Collectors.joining(", "));
    }

    /**
     * 构建资源审核列表
     *
     * @param flowResourceDTOS 资源列表
     * @return 结果
     */
    private List<ActivityFlowResourceAuditBean> buildFlowResourceAuditBean(List<ActivityFlowResourceBean> flowResourceDTOS) {
        if (CollectionUtils.isEmpty(flowResourceDTOS)) {
            return new ArrayList<>();
        }
        return flowResourceDTOS.stream().map(dto -> {
            ActivityFlowResourceAuditBean auditBean = new ActivityFlowResourceAuditBean();
            auditBean.setFlowResourceId(dto.getResourceConfigId());
            auditBean.setStatus(ActivityResourceAuditStatusEnum.ABLE_GIVE.getStatus());
            return auditBean;
        }).collect(Collectors.toList());
    }

    @Override
    public Result<ResponseBatchActivityAuditData> batchRejectActivityApply(RequestBatchActivityAuditReject param) {
        LogContext.addReqLog("batchRejectActivityApply.param={}", param);
        LogContext.addResLog("batchRejectActivityApply.param={}", param);

        if (CollectionUtils.isEmpty(param.getActivity())) {
            return RpcResult.fail(BATCH_REJECT_ACTIVITY_APPLY_FAIL, "活动列表不能为空");
        }

        // 获取活动的流量资源列表
        List<Long> activityIds = param.getActivity().stream().map(ActivityBatchItemBean::getId).collect(Collectors.toList());
        // 批量获取ActivityInfo
        Map<Long, ActivityInfoDTO> activityInfoMap = activityApplyManager.getActivityInfoByIds(activityIds).stream()
                .collect(Collectors.toMap(ActivityInfoDTO::getId, Function.identity()));

        List<ResponseBatchActivityAuditData.AuditStatus> auditStatusList = new ArrayList<>();

        for (ActivityBatchItemBean item : param.getActivity()) {
            try {
                // 构建单个驳回请求参数
                RequestActivityAuditReject rejectParam = new RequestActivityAuditReject()
                        .setActivityId(item.getId())
                        .setReason(param.getReason())
                        .setAppId(param.getAppId())
                        .setOperator(param.getOperator())
                        .setVersion(item.getVersion());

                // 调用单个驳回方法
                Result<Void> result = rejectActivityApply(rejectParam);
                // 获取活动信息用于错误提示
                ActivityInfoDTO activityInfo = activityInfoMap.get(item.getId());
                ResponseBatchActivityAuditData.AuditStatus auditStatus = buildResponseBatchActivityAuditDataErrorItem(activityInfo, new Result<>(result.rCode(), result.getMessage()));
                auditStatusList.add(auditStatus);
            } catch (Exception e) {
                log.error("batch reject activity apply error, activityId:{}", item.getId(), e);

                // 获取活动信息用于错误提示
                ActivityInfoDTO activityInfo = activityInfoMap.get(item.getId());
                ResponseBatchActivityAuditData.AuditStatus errorItem = buildResponseBatchActivityAuditDataErrorItem(activityInfo, RpcResult.fail(BATCH_REJECT_ACTIVITY_APPLY_FAIL, "操作失败：" + e.getMessage()));
                auditStatusList.add(errorItem);
            }
        }

        return RpcResult.success(new ResponseBatchActivityAuditData()
                .setList(auditStatusList));
    }

    @Override
    public Result<ResponseBatchActivityAuditData> batchAgreeActivityApply(RequestBatchActivityAuditAgree param) {
        LogContext.addReqLog("batchAgreeActivityApply.param={}", param);
        LogContext.addResLog("batchAgreeActivityApply.param={}", param);

        if (CollectionUtils.isEmpty(param.getActivity())) {
            return RpcResult.fail(BATCH_AGREE_ACTIVITY_APPLY_FAIL, "活动列表不能为空");
        }

        List<ResponseBatchActivityAuditData.AuditStatus> auditStatusList = new ArrayList<>();
        // 获取活动的流量资源列表
        List<Long> activityIds = param.getActivity().stream().map(ActivityBatchItemBean::getId).collect(Collectors.toList());
        // 批量获取并根据活动 ID 分组
        Map<Long, List<ActivityFlowResourceDTO>> flowResourceMap = activityApplyManager.getActivityFlowResourceByActivityIds(activityIds).stream()
                .collect(Collectors.groupingBy(ActivityFlowResourceDTO::getActivityId));
        // 批量获取ActivityInfo
        Map<Long, ActivityInfoDTO> activityInfoMap = activityApplyManager.getActivityInfoByIds(activityIds).stream()
                .collect(Collectors.toMap(ActivityInfoDTO::getId, Function.identity()));

        for (ActivityBatchItemBean item : param.getActivity()) {
            try {
                List<ActivityFlowResourceDTO> flowResourceDTOList = flowResourceMap.get(item.getId());
                List<ActivityFlowResourceAuditBean> flowResourceList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(flowResourceDTOList)) {
                    // 将 ActivityFlowResourceDTO 转换为 ActivityFlowResourceAuditBean
                    flowResourceList = flowResourceDTOList.stream().map(flowResource -> {
                        ActivityFlowResourceAuditBean auditBean = new ActivityFlowResourceAuditBean();
                        auditBean.setFlowResourceId(flowResource.getResourceConfigId());
                        auditBean.setStatus(ActivityResourceAuditStatusEnum.ABLE_GIVE.getStatus());
                        return auditBean;
                    }).collect(Collectors.toList());
                }

                // 构建单个同意请求参数
                RequestActivityAuditAgree agreeParam = new RequestActivityAuditAgree();
                agreeParam.setActivityId(item.getId());
                agreeParam.setAppId(param.getAppId());
                agreeParam.setOperator(param.getOperator());
                agreeParam.setVersion(item.getVersion());
                agreeParam.setFlowResourceList(flowResourceList);

                // 调用单个同意方法
                Result<String> result = agreeActivityApply(agreeParam);

                // 构造响应
                ActivityInfoDTO activityInfo = activityInfoMap.get(item.getId());
                ResponseBatchActivityAuditData.AuditStatus auditStatus = buildResponseBatchActivityAuditDataErrorItem(activityInfo, result);
                if (auditStatus != null) {
                    auditStatusList.add(auditStatus);
                }

            } catch (Exception e) {
                log.error("batch agree activity apply error, activityId:{}", item.getId(), e);

                // 获取活动信息用于错误提示
                ActivityInfoDTO activityInfo = activityInfoMap.get(item.getId());
                ResponseBatchActivityAuditData.AuditStatus auditStatus = buildResponseBatchActivityAuditDataErrorItem(activityInfo, RpcResult.fail(BATCH_AGREE_ACTIVITY_APPLY_FAIL, "操作失败：" + e.getMessage()));
                if (auditStatus != null) {
                    auditStatusList.add(auditStatus);
                }
            }
        }

        return RpcResult.success(new ResponseBatchActivityAuditData()
                .setList(auditStatusList)
        );
    }


    private ResponseBatchActivityAuditData.AuditStatus buildResponseBatchActivityAuditDataErrorItem(ActivityInfoDTO activityInfo, Result<String> result) {
        if (activityInfo == null) {
            return null;
        }

        ResponseBatchActivityAuditData.AuditStatus auditStatusList = new ResponseBatchActivityAuditData.AuditStatus()
                .setId(activityInfo.getId())
                .setName(activityInfo.getName())
                .setStatus(true);

        if (RpcResult.isFail(result)) {
            // 获取活动信息用于错误提示
            auditStatusList.setMessage(result.getMessage());
            auditStatusList.setStatus(false);
        }else if (StringUtils.isNotBlank(result.target())) {
            // 即使审批成功，也有可能有错误提示，从 Target 里面获取错误提示返回
            auditStatusList.setMessage(result.target());
        }
        return auditStatusList;
    }

}
