package fm.lizhi.ocean.wavecenter.service.activitycenter.handler;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityRuleConfigBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.BlackListRuleBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyRuleEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityApplyService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.service.activitycenter.constatns.ActivityApplyErrorTipConstant;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityApplyContext;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityRuleManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ApplyBlackListRuleFilter implements ApplyRuleFilter {

    @Autowired
    private ActivityRuleManager activityRuleManager;


    @Override
    public Result<Void> filter(ActivityApplyContext context, ActivityRuleConfigBean rule) {
        BlackListRuleBean ruleBean = activityRuleManager.getRuleBean(ActivityApplyRuleEnum.BLACK_LIST, rule.getRuleJson());
        if (ruleBean != null) {
            String[] userIds = ruleBean.getUserIds().split("\n");
            for (String userIdStr : userIds) {
                if (StringUtils.isBlank(userIdStr)) {
                    continue;
                }

                Long userId = Long.parseLong(userIdStr);
                if (userId.equals(context.getParamDTO().getNjId())) {
                    //命中黑名单
                    log.info("命中黑名单, njId={}", context.getParamDTO().getNjId());
                    String msg = ActivityApplyErrorTipConstant.APPLY_ACTIVITY_APPLY_BLACK_LIST;
                    return RpcResult.fail(ActivityApplyService.ACTIVITY_APPLY_PARAM_ERROR, msg);
                }
            }
        }
        return RpcResult.success();
    }

    @Override
    public ActivityApplyRuleEnum getRuleTypeEnum() {
        return ActivityApplyRuleEnum.BLACK_LIST;
    }
}
