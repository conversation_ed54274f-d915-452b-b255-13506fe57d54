package fm.lizhi.ocean.wavecenter.service.sign.dto;

import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.Singular;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/12 14:26
 */
@Getter
@Builder
public class QueryNonContractDTO {

    @Builder.Default
    private boolean descCreateTime = true;

    /**
     * 签约ID
     */
    private Long contractId;

    @Singular
    private List<Long> contractIdLists;

    /**
     * 主播ID
     */
    @Singular
    private List<Long> njIds;

    /**
     * 签约类型
     */
    @Singular
    private List<ContractTypeEnum> types;

    /**
     * 状态
     */
    @Singular
    private List<SignRelationEnum> statuses;

    @Builder.Default
    private Integer pageNo = 1;

    @Builder.Default
    private Integer pageSize = 1;

    /**
     * 用户或者厅主ID
     */
    private Long userOrNjId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 父合同ID
     */
    private Long parentId;

}
