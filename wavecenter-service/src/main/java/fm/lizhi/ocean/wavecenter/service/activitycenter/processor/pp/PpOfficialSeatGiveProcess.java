package fm.lizhi.ocean.wavecenter.service.activitycenter.processor.pp;

import com.alibaba.fastjson.JSONObject;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.OfficialSeatExtraBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.ActivityConfig;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.PpActivityConfig;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.*;
import fm.lizhi.ocean.wavecenter.service.activitycenter.processor.IOfficialSeatGiveProcess;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PpOfficialSeatGiveProcess implements IOfficialSeatGiveProcess {

    @Autowired
    private ActivityConfig activityConfig;

    @Override
    public void fillOfficialSeatParam(FlowResourceContext context, SaveOfficialSeatParamDTO param, OfficialSeatExtraBean extraBean) {
        ActivityFlowResourceGiveDTO flowResourceGiveDTO = context.getFlowResourceGiveDTO();
        PpActivityConfig ppActivityConfig = activityConfig.getPp();

        String officialSeatActionJson = ppActivityConfig.getOfficialSeatActionJson();
        JSONObject officialSeatAction =  JSONObject.parseObject(officialSeatActionJson);
        officialSeatAction.put("userId", String.valueOf(flowResourceGiveDTO.getUserId()));
        OfficialSeatExtraDTO extraDTO =  new OfficialSeatExtraDTO()
                .setTemplate(ppActivityConfig.getOfficialSeatTemplateId())
                .setLiveTitleColor(extraBean.getColor())
                .setAction(JsonUtils.toJsonStringLegacy(officialSeatAction));

        param.setExtra(extraDTO);
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.PP;
    }
}
