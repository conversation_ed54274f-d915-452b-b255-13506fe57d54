package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class XmOfficialSeatDetailDTO implements OfficialSeatDetailDTO {

    private Long id;

    /**
     * tabid
     */
    private Long tabId;

    /**
     * app推荐位，从1开始
     */
    private Integer position;

    /**
     * 用户组id
     */
    private List<Long> userGroupId;

    /**
     * 背景图
     */
    private String backgroundUrl;

    /**
     * 活动类型
     */
    private Integer type;

    /**
     * 活动备注
     */
    private String remark;

    /**
     * 生效时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 操作者
     */
    private String operator;

    /**
     * 用户id集合，换行分割
     */
    private List<Long> userIds;

}
