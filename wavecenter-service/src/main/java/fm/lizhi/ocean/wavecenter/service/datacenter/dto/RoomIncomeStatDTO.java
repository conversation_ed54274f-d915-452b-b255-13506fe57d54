package fm.lizhi.ocean.wavecenter.service.datacenter.dto;

import lombok.Data;

/**
 * 厅收入概览统计结果DTO
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
@Data
public class RoomIncomeStatDTO {

    /**
     * 开始时间戳
     */
    private Long startTime;

    /**
     * 结束时间戳
     */
    private Long endTime;

    /**
     * 收入详情
     */
    private IncomeSummaryDTO info;

    /**
     * 构建数据
     */
    public static RoomIncomeStatDTO of(Long startTime, Long endTime, IncomeSummaryDTO info) {
        RoomIncomeStatDTO dto = new RoomIncomeStatDTO();
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        dto.setInfo(info);
        return dto;
    }
} 