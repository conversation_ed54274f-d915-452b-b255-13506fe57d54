package fm.lizhi.ocean.wavecenter.service.activitycenter.handler;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.AutoConfigResourceEnum;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.ActivityConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @description: 西米厅战节目单方法，逻辑同节目单
 * @author: guoyibin
 * @create: 2024/11/20 16:21
 */
@Slf4j
@Component
public class HallProgrammeGiveHandler extends AbstractProgrammeGiveHandler{

    @Autowired
    private ActivityConfig activityConfig;

    @Override
    public String getResourceCode() {
        return AutoConfigResourceEnum.HALL_PROGRAMME.getResourceCode();
    }

    @Override
    protected Map<String, Long> getPrimaryClassMap() {
        try {
            String primaryClassConfig = activityConfig.getXm().getPrimaryHallBattleClassMapping();
            return JsonUtils.fromJsonStringLegacy(primaryClassConfig, Map.class);
        } catch (Exception e) {
            log.warn("getHallPrimaryClassMap error", e);
        }
        return new HashMap<>();

    }

    @Override
    protected Map<String, Long> getSecondaryClassMap() {
        try {
            String secondaryClassConfig = activityConfig.getXm().getSecondaryHallBattleClassMapping();
            return JsonUtils.fromJsonStringLegacy(secondaryClassConfig, Map.class);
        } catch (Exception e) {
            log.warn("getHallSecondaryClassMap error", e);
        }

        return new HashMap<>();
    }
}
