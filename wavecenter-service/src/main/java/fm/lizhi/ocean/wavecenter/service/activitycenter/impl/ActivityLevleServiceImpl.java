package fm.lizhi.ocean.wavecenter.service.activitycenter.impl;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityLevelService;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityLevelConfigBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.activitycenter.convert.ActivityLevelConvert;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityLevelManager;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityLevelInfoBean;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

@ServiceProvider
public class ActivityLevleServiceImpl implements ActivityLevelService   {

    //逻辑一样，manager不区分是否后台专用manager，后续有必要再拆分
    @Autowired
    private ActivityLevelManager activityLevelManager;

    @Override
    public Result<List<ActivityLevelInfoBean>> listByAppId(Integer appId) {
        return ResultHandler.handle(appId, () -> {
            List<ActivityLevelConfigBean> activityLevelConfigBeans = activityLevelManager.listByAppId(appId);
            return RpcResult.success(ActivityLevelConvert.I.convert2ActivityLevelInfoBeans(activityLevelConfigBeans));
        });
    }
}
