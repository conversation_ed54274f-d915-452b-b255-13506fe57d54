package fm.lizhi.ocean.wavecenter.service.common.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
public class ActivityApplyDataAuditResultDTO {
    private boolean result;

    private List<DiscernResult> discernResults;

    @Data
    @Accessors(chain = true)
    public static class DiscernResult {

        /**
         * 内容ID
         */
        private Long contentId;

        /**
         * 风险类型
         */
        private Integer riskType;

        /**
         * 风险描述
         */
        private String riskDesc;

        /**
         * 命中敏感词
         */
        private String hitDetails;
    }
}
