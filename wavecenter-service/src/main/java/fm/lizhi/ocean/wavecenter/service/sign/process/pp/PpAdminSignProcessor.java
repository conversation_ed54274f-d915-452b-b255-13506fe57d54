package fm.lizhi.ocean.wavecenter.service.sign.process.pp;

import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.FamilyAndNjContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.NjAndPlayerContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.OperateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestAdminApplyCancelFamily;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestAdminInviteUser;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestAdminOperateSign;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseAdminApplyCancelFamily;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseAdminInviteUser;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseAdminOperateSign;
import fm.lizhi.ocean.wavecenter.api.sign.service.SignAdminService;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.service.sign.config.SignConfig;
import fm.lizhi.ocean.wavecenter.service.sign.dto.QueryNonContractDTO;
import fm.lizhi.ocean.wavecenter.service.sign.dto.RequestFamilyAndNjContractDTO;
import fm.lizhi.ocean.wavecenter.service.sign.handler.PpSignCheckJobHoppingHandler;
import fm.lizhi.ocean.wavecenter.service.sign.manager.ContractManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.NonContractManager;
import fm.lizhi.ocean.wavecenter.service.sign.process.AdminSignProcessor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/12 18:10
 */
@Component
public class PpAdminSignProcessor implements AdminSignProcessor {

    @Autowired
    private ContractManager contractManager;
    @Autowired
    private SignConfig signConfig;
    @Autowired
    private PpSignCheckJobHoppingHandler ppSignCheckJobHoppingHandler;
    @Autowired
    private NonContractManager nonContractManager;

    @Override
    public ResponseAdminOperateSign operateSignCheck(RequestAdminOperateSign request) {
        ResponseAdminOperateSign res = new ResponseAdminOperateSign().setCode(0);

        if (request.getOperateType() == OperateTypeEnum.AGREE) {
            // 防跳槽检查
            PageBean<NjAndPlayerContractBean> contractListPage = nonContractManager.queryList(QueryNonContractDTO.builder()
                    .contractId(request.getPlayerSignId())
                    .build());
            List<NjAndPlayerContractBean> contractList = contractListPage.getList();
            if (CollectionUtils.isEmpty(contractList)) {
                LogContext.addResLog("contract is not exist");
                return res.setCode(-1);
            }
            NjAndPlayerContractBean contractBean = contractList.get(0);

            Pair<Integer, String> integerStringPair = ppSignCheckJobHoppingHandler.forAdminAcceptUserApply(contractBean.getNjUserId(), contractBean.getPlayerUserId());
            if (integerStringPair.getKey() != 0) {
                LogContext.addResLog("checkJobHoppingCode={}", integerStringPair.getRight());
                return res.setCode(integerStringPair.getKey()).setMsg(integerStringPair.getValue());
            }
        }

        return res;
    }

    @Override
    public ResponseAdminInviteUser inviteUserCheck(RequestAdminInviteUser request) {
        ResponseAdminInviteUser res = new ResponseAdminInviteUser().setCode(0);

        //目标厅是否为管理员
        boolean userSignAsRoom = contractManager.isUserSignAsRoom(request.getCurUserId());
        if (!userSignAsRoom) {
            return res.setCode(SignAdminService.INVITE_USER_REQ_NOT_ADMIN);
        }

        //当前用户是否存在签约成功的记录
        PageBean<FamilyAndNjContractBean> signList = contractManager.queryContract(
                RequestFamilyAndNjContractDTO.builder()
                        .njId(request.getTargetUserId())
                        .type(ContractTypeEnum.SIGN)
                        .type(ContractTypeEnum.SUBJECT_CHANGE)
                        .type(ContractTypeEnum.RENEW)
                        .relation(SignRelationEnum.SIGN_SUCCESS)
                        .build()
        );
        if (CollectionUtils.isNotEmpty(signList.getList())) {
            return res.setCode(SignAdminService.INVITE_USER_USER_SIGNED);
        }

        // 防跳槽检查
        Pair<Integer, String> integerStringPair = ppSignCheckJobHoppingHandler.forNjInvite(request.getCurUserId(), request.getTargetUserId());
        if (integerStringPair.getKey() != 0) {
            LogContext.addResLog("checkJobHoppingCode={}", integerStringPair.getRight());
            return res.setCode(integerStringPair.getKey()).setMsg(integerStringPair.getValue());
        }

        return res;
    }

    @Override
    public ResponseAdminApplyCancelFamily applyCancelFamilyCheck(RequestAdminApplyCancelFamily request) {
        ResponseAdminApplyCancelFamily res = new ResponseAdminApplyCancelFamily();
        if (!signConfig.getPp().isSignFunctionSwitch()) {
            return res.setCode(-1).setMsg("解约功能暂时关闭，恢复时间等待官方通知");
        }
        return res;
    }

    @Override
    public ResponseAdminApplyCancelFamily doApplyCancelFamily(RequestAdminApplyCancelFamily request) {
        return contractManager.adminApplyCancelFamily(request);
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.PP;
    }
}
