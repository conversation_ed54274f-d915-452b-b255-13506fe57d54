package fm.lizhi.ocean.wavecenter.service.anchor.singer.convert;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerFamilyInfoBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerUserInfoBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.UserSingerGloryBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestPageSingerInfo;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponsePageSingerInfo;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.PageSingerInfoParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerInfoDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.UserSingerGloryDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Mapper(collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        imports = {SingerTypeEnum.class}
)
public interface SingerInfoConvert {

    SingerInfoConvert INSTANCE = Mappers.getMapper(SingerInfoConvert.class);

    PageSingerInfoParamDTO convertPageSingerInfoParamDTO(RequestPageSingerInfo request);

    @Mapping(target = "whiteListSinger", ignore = true)
    @Mapping(target = "singerInfo", ignore = true)
    @Mapping(target = "njInfo", ignore = true)
    @Mapping(target = "familyInfo", ignore = true)
    @Mapping(target = "singerType", expression = "java(SingerTypeEnum.getByType(dto.getSingerType()))")
    ResponsePageSingerInfo convertSingerInfoDTO(SingerInfoDTO dto);

    SingerUserInfoBean buildSingerInfo(SimpleUserDto user);

    SingerFamilyInfoBean buildFamilyInfo(FamilyBean family);

    default List<ResponsePageSingerInfo> convertResponsePageSingerInfoList(List<SingerInfoDTO> list, Map<Long, SimpleUserDto> userMap, Map<Long, FamilyBean> familyMap, List<Long> whiteSingerIds){

        return list.stream().map(dto -> {
            ResponsePageSingerInfo info = convertSingerInfoDTO(dto);
            // 设置歌手信息
            Optional.ofNullable(userMap.get(dto.getUserId())).ifPresent(user -> {
                info.setSingerInfo(buildSingerInfo(user));
            });

            // 设置家族信息
            Optional.ofNullable(familyMap.get(dto.getFamilyId())).ifPresent(family -> {
                info.setFamilyInfo(buildFamilyInfo(family));
            });

            // 设置厅主信息
            Optional.ofNullable(userMap.get(dto.getNjId())).ifPresent(user -> {
                info.setNjInfo(buildSingerInfo(user));
            });

            info.setWhiteListSinger(whiteSingerIds.contains(dto.getUserId()));
            return info;

        }).collect(Collectors.toList());
    }

    UserSingerGloryBean convertUserSingerGloryBadge(UserSingerGloryDTO qualitySingerGlory);
}