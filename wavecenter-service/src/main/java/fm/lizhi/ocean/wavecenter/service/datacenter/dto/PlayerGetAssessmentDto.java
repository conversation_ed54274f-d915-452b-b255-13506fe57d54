package fm.lizhi.ocean.wavecenter.service.datacenter.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/24 11:55
 */
@Data
@Accessors(chain = true)
public class PlayerGetAssessmentDto {

    public Long familyId;

    /**
     * 陪玩签约厅
     */
    private Long signRoomId;

    /**
     * 公会所有签约厅
     */
    private List<Long> familySignRoomIds;

    private Long playerId;

    /**
     * 当前考核周期
     */
    private AssessTimeDto currentTime;

    /**
     * 上一个考核周期
     */
    private AssessTimeDto preTime;

}
