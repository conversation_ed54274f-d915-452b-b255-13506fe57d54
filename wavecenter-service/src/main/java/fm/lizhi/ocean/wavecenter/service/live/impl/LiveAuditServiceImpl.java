package fm.lizhi.ocean.wavecenter.service.live.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.*;
import fm.lizhi.ocean.wavecenter.api.live.service.LiveAuditService;
import fm.lizhi.ocean.wavecenter.api.user.bean.RoomBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.common.config.BizCommonConfig;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.AuditVoiceFileTransferManager;
import fm.lizhi.ocean.wavecenter.service.live.dto.GuildAuditRecordDto;
import fm.lizhi.ocean.wavecenter.service.live.dto.RoomAuditRecordDto;
import fm.lizhi.ocean.wavecenter.service.live.manager.LiveAuditManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 审核记录相关查询
 */
@ServiceProvider
public class LiveAuditServiceImpl implements LiveAuditService {


    @Autowired
    private LiveAuditManager liveAuditManager;

    @Autowired
    private UserManager userManager;

    @Autowired
    private CommonConfig commonConfig;

    @Autowired
    private AuditVoiceFileTransferManager auditVoiceFileTransferManager;


    @Override
    public Result<PageBean<UserBean>> signRoomPushPlayer(RoomPushParamBean paramBean, int page, int pageSize) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));
        return ResultHandler.handle(paramBean.getAppId(), ()->{
            PageBean<Long> pageBean = liveAuditManager.signRoomPushPlayer(paramBean, page, pageSize);

            List<Long> userIds = pageBean.getList();
            if(CollectionUtils.isEmpty(userIds)){
                return new Result(GeneralRCode.GENERAL_RCODE_SUCCESS,PageBean.empty());
            }

            List<SimpleUserDto> userInfoList = userManager.getSimpleUserByIds(userIds);
            List<UserBean> userBeanList = userInfoList.stream().map(simpleUserDto -> {
                UserBean player = new UserBean();
                player.setId(simpleUserDto.getId());
                player.setBand(simpleUserDto.getBand());
                player.setName(simpleUserDto.getName());
                player.setPhoto(simpleUserDto.getAvatar());
                return player;
            }).collect(Collectors.toList());

            return new Result(GeneralRCode.GENERAL_RCODE_SUCCESS,PageBean.of(pageBean.getTotal(), userBeanList));
        });
    }

    @Override
    public Result<PageBean<PlayerAuditRecordBean>> playerAuditRecordDetail(PlayerAuditRecordSearchParamBean paramBean, int page, int pageSize) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));

        return ResultHandler.handle(paramBean.getAppId(), ()->{
            AuditRecordSearchParamBean searchParamBean = AuditRecordSearchParamBean.builder()
                    .appId(paramBean.getAppId())
                    .endTime(paramBean.getEndTime())
                    .startTime(paramBean.getStartTime())
                    .op(paramBean.getOp())
                    .userId(paramBean.getUserId())
                    .build();
            PageBean<AuditRecordBean> auditRecordDetails = liveAuditManager.getAuditRecordDetail(searchParamBean, page, pageSize);
            List<AuditRecordBean> auditRecordBeans = auditRecordDetails.getList();

            Set<Long> userIds = auditRecordBeans.stream().map(AuditRecordBean::getUserId).collect(Collectors.toSet());

            if(CollectionUtils.isEmpty(userIds)){
                return new Result(GeneralRCode.GENERAL_RCODE_SUCCESS,PageBean.empty());
            }

            List<SimpleUserDto> userInfoList = userManager.getSimpleUserByIds(new ArrayList<>(userIds));
            Map<Long, SimpleUserDto> userInfoMap = userInfoList.stream().collect(Collectors.toMap(SimpleUserDto::getId, v -> v));

            List<PlayerAuditRecordBean> recordBeanList = auditRecordBeans.stream().map(auditRecordBean -> {
                SimpleUserDto simpleUserDto;
                UserBean player = new UserBean();
                if( (simpleUserDto = userInfoMap.get(auditRecordBean.getUserId())) !=null ){
                    player.setId(auditRecordBean.getUserId());
                    player.setBand(simpleUserDto.getBand());
                    player.setName(simpleUserDto.getName());
                    player.setPhoto(simpleUserDto.getAvatar());
                }

                return PlayerAuditRecordBean.builder()
                        .insertTime(auditRecordBean.getInsertTime())
                        .op(auditRecordBean.getOp())
                        .reason(auditRecordBean.getReason())
                        .pushTime(auditRecordBean.getPushTime())
                        .scene(transSceneName(auditRecordBean.getSceneType(), auditRecordBean.getScene()))
                        .publicContentUrl(UrlUtils.addHostOrEmpty(auditRecordBean.getPublicContentUrl(), commonConfig.getBizConfig(paramBean.getAppId()).getCdnHost()))
                        .player(player).build();
            }).collect(Collectors.toList());
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, PageBean.of(auditRecordDetails.getTotal(),recordBeanList));
        });
    }

    @Override
    public Result<PageBean<RoomAuditRecordBean>> roomAuditRecordDetail(RoomAuditRecordSearchParamBean paramBean, int page, int pageSize) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));

        return ResultHandler.handle(paramBean.getAppId(), ()->{

            AuditRecordSearchParamBean searchParamBean = AuditRecordSearchParamBean.builder()
                    .appId(paramBean.getAppId())
                    .endTime(paramBean.getEndTime())
                    .startTime(paramBean.getStartTime())
                    .roomId(paramBean.getRoomId())
                    .op(paramBean.getOp())
                    .userId(paramBean.getUserId())
                    .build();
            PageBean<AuditRecordBean> auditRecordDetails = liveAuditManager.getAuditRecordDetail(searchParamBean, page, pageSize);

            List<AuditRecordBean> auditRecordBeans = auditRecordDetails.getList();

            HashSet<Long> userIds = new HashSet<>();
            auditRecordBeans.forEach(e->{
                if (e.getRoomId() != null) {
                    userIds.add(e.getRoomId());
                }
                if (e.getUserId() != null) {
                    userIds.add(e.getUserId());
                }
            });

            if(CollectionUtils.isEmpty(userIds)){
                return new Result(GeneralRCode.GENERAL_RCODE_SUCCESS,PageBean.empty());
            }

            List<SimpleUserDto> userInfoList = userManager.getSimpleUserByIds(new ArrayList<>(userIds));
            Map<Long, SimpleUserDto> userInfoMap = userInfoList.stream().collect(Collectors.toMap(SimpleUserDto::getId, v -> v));

            List<RoomAuditRecordBean> recordBeanList = auditRecordBeans.stream().map(auditRecordBean -> {
                SimpleUserDto simpleUserDto;
                UserBean player = new UserBean();
                if( (simpleUserDto = userInfoMap.get(auditRecordBean.getUserId())) !=null ){
                    player.setId(auditRecordBean.getUserId());
                    player.setBand(simpleUserDto.getBand());
                    player.setName(simpleUserDto.getName());
                    player.setPhoto(simpleUserDto.getAvatar());
                }

                RoomBean roomBean = new RoomBean();
                SimpleUserDto roomDto = userInfoMap.get(auditRecordBean.getRoomId());
                if (roomDto != null) {
                    roomBean.setId(roomDto.getId());
                    roomBean.setBand(roomDto.getBand());
                    roomBean.setName(roomDto.getName());
                    roomBean.setPhoto(roomDto.getAvatar());
                }

                return RoomAuditRecordBean.builder()
                        .insertTime(auditRecordBean.getInsertTime())
                        .op(auditRecordBean.getOp())
                        .reason(auditRecordBean.getReason())
                        .pushTime(auditRecordBean.getPushTime())
                        .scene(transSceneName(auditRecordBean.getSceneType(), auditRecordBean.getScene()))
                        .roomBean(roomBean)
                        .publicContentUrl(UrlUtils.addHostOrEmpty(auditRecordBean.getPublicContentUrl(), commonConfig.getBizConfig(paramBean.getAppId()).getCdnHost()))
                        .player(player).build();
            }).collect(Collectors.toList());
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, PageBean.of(auditRecordDetails.getTotal(),recordBeanList));
        });
    }

    @Override
    public Result<PageBean<GuildAuditRecordBean>> guildAuditRecordDetail(GuildAuditRecordSearchParamBean paramBean, int page, int pageSize) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));

        return ResultHandler.handle(paramBean.getAppId(), ()->{

            AuditRecordSearchParamBean searchParamBean = AuditRecordSearchParamBean.builder()
                    .appId(paramBean.getAppId())
                    .endTime(paramBean.getEndTime())
                    .startTime(paramBean.getStartTime())
                    .familyId(paramBean.getFamilyId())
                    .roomId(paramBean.getRoomId())
                    .op(paramBean.getOp())
                    .userId(paramBean.getUserId())
                    .roomIds(paramBean.getRoomIds())
                    .build();
            PageBean<AuditRecordBean> auditRecordDetails = liveAuditManager.getAuditRecordDetail(searchParamBean, page, pageSize);

            List<AuditRecordBean> auditRecordBeans = auditRecordDetails.getList();

            HashSet<Long> userIds = new HashSet<>();
            auditRecordBeans.forEach(e->{
                if (e.getRoomId() != null) {
                    userIds.add(e.getRoomId());
                }
                if (e.getUserId() != null) {
                    userIds.add(e.getUserId());
                }
            });

            if(CollectionUtils.isEmpty(userIds)){
                return new Result(GeneralRCode.GENERAL_RCODE_SUCCESS,PageBean.empty());
            }

            List<SimpleUserDto> userInfoList = userManager.getSimpleUserByIds(new ArrayList<>(userIds));
            Map<Long, SimpleUserDto> userInfoMap = userInfoList.stream().collect(Collectors.toMap(SimpleUserDto::getId, v -> v));

            List<GuildAuditRecordBean> recordBeanList = auditRecordBeans.stream().map(auditRecordBean -> {
                RoomBean roomBean = new RoomBean();
                SimpleUserDto simpleUserDto;
                if( (simpleUserDto = userInfoMap.get(auditRecordBean.getRoomId())) !=null ){
                    roomBean.setId(auditRecordBean.getRoomId());
                    roomBean.setBand(simpleUserDto.getBand());
                    roomBean.setName(simpleUserDto.getName());
                    roomBean.setPhoto(simpleUserDto.getAvatar());
                }

                UserBean player = new UserBean();
                if( (simpleUserDto = userInfoMap.get(auditRecordBean.getUserId())) !=null ){
                    player.setId(auditRecordBean.getUserId());
                    player.setBand(simpleUserDto.getBand());
                    player.setName(simpleUserDto.getName());
                    player.setPhoto(simpleUserDto.getAvatar());
                }

                return GuildAuditRecordBean.builder()
                        .insertTime(auditRecordBean.getInsertTime())
                        .op(auditRecordBean.getOp())
                        .reason(auditRecordBean.getReason())
                        .roomBean(roomBean)
                        .pushTime(auditRecordBean.getPushTime())
                        .scene(transSceneName(auditRecordBean.getSceneType(), auditRecordBean.getScene()))
                        .publicContentUrl(UrlUtils.addHostOrEmpty(auditRecordBean.getPublicContentUrl(), commonConfig.getBizConfig(paramBean.getAppId()).getCdnHost()))
                        .player(player).build();
            }).collect(Collectors.toList());

            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, PageBean.of(auditRecordDetails.getTotal(),recordBeanList));
        });
    }

    @Override
    public Result<Void> addAuditRecord(AddAuditRecordBean recordBean) {
        LogContext.addReqLog("recordBean={}", JsonUtil.dumps(recordBean));
        LogContext.addResLog("recordBean={}", JsonUtil.dumps(recordBean));

        return ResultHandler.handle(recordBean.getAppId(), ()->{
            boolean result = liveAuditManager.addAuditRecord(
                    AddAuditRecordBean.builder()
                            .appId(recordBean.getAppId())
                            .familyId(recordBean.getFamilyId())
                            .insertTime(recordBean.getInsertTime())
                            .op(recordBean.getOp())
                            .reason(recordBean.getReason())
                            .roomId(recordBean.getRoomId())
                            .pushTime(recordBean.getPushTime())
                            .userId(recordBean.getUserId())
                            .build()
            );
            return result? new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null):new Result<>(ADD_AUDIT_RECORD_ERROR, null);
        });
    }

    @Override
    public Result<PageBean<GuildAuditRecordStatsBean>> guildAuditRecordStats(GuildAuditStatsParamBean paramVo, int page, int pageSize) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramVo));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramVo));

        return ResultHandler.handle(paramVo.getAppId(), ()->{
            PageBean<GuildAuditRecordDto> recordPageBean = liveAuditManager.guildAuditRecordStats(paramVo, page, pageSize);

            List<GuildAuditRecordDto> auditRecords = recordPageBean.getList();
            List<GuildAuditRecordStatsBean> auditRecordBeans = auditRecords.stream().map(e -> {
                Long roomId = e.getRoomId();
                //厅主信息
                List<SimpleUserDto> userInfoList = userManager.getSimpleUserByIds(Arrays.asList(roomId));
                Map<Long, SimpleUserDto> userInfoMap = userInfoList.stream().collect(Collectors.toMap(SimpleUserDto::getId, v -> v));
                SimpleUserDto simpleUserDto;
                RoomBean roomBean = new RoomBean();
                if( (simpleUserDto = userInfoMap.get(roomId)) !=null){
                    roomBean.setId(roomId);
                    roomBean.setBand(simpleUserDto.getBand());
                    roomBean.setName(simpleUserDto.getName());
                    roomBean.setPhoto(simpleUserDto.getAvatar());
                }
                return GuildAuditRecordStatsBean.builder()
                        .pushPeopleNumber(e.getPushPeopleNumber())
                        .pushNumber(e.getPushNumber())
                        .roomBean(roomBean).build();
            }).collect(Collectors.toList());

            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, PageBean.of(recordPageBean.getTotal(),auditRecordBeans));
        });
    }



    @Override
    public Result<RoomAuditRecordStatsBean> roomAuditRecordStats(RoomAuditStatsParamBean paramVo) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramVo));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramVo));

        return ResultHandler.handle(paramVo.getAppId(), ()->{
            RoomAuditRecordDto roomAuditRecordDto = liveAuditManager.roomAuditRecordStats(paramVo);
            Long roomId = paramVo.getRoomId();
            //厅主信息
            List<SimpleUserDto> userInfoList = userManager.getSimpleUserByIds(Arrays.asList(roomId));
            Map<Long, SimpleUserDto> userInfoMap = userInfoList.stream().collect(Collectors.toMap(SimpleUserDto::getId, v -> v));
            SimpleUserDto simpleUserDto;
            RoomBean roomBean = new RoomBean();
            if( (simpleUserDto = userInfoMap.get(roomId)) !=null ){
                roomBean.setId(roomId);
                roomBean.setBand(simpleUserDto.getBand());
                roomBean.setName(simpleUserDto.getName());
                roomBean.setPhoto(simpleUserDto.getAvatar());
            }
            RoomAuditRecordStatsBean recordBean = RoomAuditRecordStatsBean.builder()
                    .pushNumber(roomAuditRecordDto.getPushNumber())
                    .pushPeopleNumber(roomAuditRecordDto.getPushPeopleNumber())
                    .roomBean(roomBean)
                    .build();
            return new Result(GeneralRCode.GENERAL_RCODE_SUCCESS,recordBean);
        });
    }

    @Override
    public Result<Void> addAuditRecordFullInfo(AddAuditRecordFullParamBean recordBean) {
        LogContext.addReqLog("recordBean={}", JsonUtil.dumps(recordBean));
        LogContext.addResLog("recordBean={}", JsonUtil.dumps(recordBean));
        return ResultHandler.handle(recordBean.getAppId(), () -> {
            //判断是否已经存在
            if (liveAuditManager.existAuditRecord(recordBean.getAuditId())) {
                return new Result<>(AUDIT_RECORD_EXIST, null);
            }
            boolean success = liveAuditManager.addAuditRecordFull(recordBean);
            if (success) {
                //保存成功后异步转储文件
                auditVoiceFileTransferManager.transferFile(recordBean.getAppId(), recordBean.getRecordId(), recordBean.getSourceContentUrl());
            }
            return success ? new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null)
                    : new Result<>(ADD_AUDIT_RECORD_ERROR, null);
        });
    }

    private String transSceneName(Integer type, String name){
        if (type == null) {
            return name;
        }

        BizCommonConfig configBizConfig = commonConfig.getBizConfig();
        if (configBizConfig == null) {
            return name;
        }

        String auditSceneNameConfig = configBizConfig.getAuditSceneName();
        if (StringUtils.isBlank(auditSceneNameConfig)) {
            return name;
        }

        JSONObject jsonConfig = JSON.parseObject(auditSceneNameConfig);

        String newName = jsonConfig.getString(String.valueOf(type));
        if (StringUtils.isBlank(newName)) {
            return name;
        }

        return newName;
    }
}
