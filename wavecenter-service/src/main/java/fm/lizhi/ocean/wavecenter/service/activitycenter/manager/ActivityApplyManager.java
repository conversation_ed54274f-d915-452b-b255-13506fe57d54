package fm.lizhi.ocean.wavecenter.service.activitycenter.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityFlowResourceAuditBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestGetInTimeRangeActivityApply;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.*;

import java.util.Date;
import java.util.List;

/**
 * 活动提报管理
 */
public interface ActivityApplyManager {

    /**
     * 查询装扮列表
     * @param activityId
     * @return
     */
    List<ActivityApplyDecorateDTO> getDecorateListByActivityId(Long activityId);

    /**
     * 活动提报数据存储
     *
     * @param params 参数
     * @return 返回结果，true为成功
     */
    Result<Boolean> activityApplyDateSave(ActivityApplyParamDTO params);

    /**
     * 相同时间内，同个厅是否存在活动提报记录（非审核不通过状态）
     *
     * @param startTime 活动开始时间
     * @param endTime   活动结束时间
     * @param njId      厅主ID
     * @return 存在相同时间的数量
     */
    boolean existSameTimeAndHallActivity(Date startTime, Date endTime, Long njId);

    /**
     * 相同时间内，同个厅是否存在活动提报记录（非审核不通过状态）
     *
     * @param startTime 活动开始时间
     * @param endTime   活动结束时间
     * @param njId      厅主ID
     * @return 存在相同时间的数量
     */
    List<Long> getSameTimeAndHallActivityId(Date startTime, Date endTime, Long njId);

    /**
     * 获取可发送数据报表通知的活动列表
     */
    List<SendReportDataInfoDTO> querySendReportDataInfoList(int recentMin);

    /**
     * 根据活动ID获取活动等级ID
     *
     * @param activityId 活动ID
     * @return 等级ID
     */
    Long getActivityLevelById(Long activityId);

    /**
     * 获取活动信息
     */
    ActivityInfoDTO getActivityInfoById(Long id);

    /**
     * 批量获取活动信息
     */
    List<ActivityInfoDTO> getActivityInfoByIds(List<Long> ids);


    /**
     * 获取大于指定时间的活动列表
     */
    List<ActivityInfoDTO> getActivityInfoByStartTimeGte(Date startTime);


    /**
     * 批量获取流量资源
     */
    List<ActivityFlowResourceDTO> getActivityFlowResourceByActivityIds(List<Long> activityIds);

    /**
     * 根据活动 ID 获取流量资源
     */
    List<ActivityFlowResourceDTO> getActivityFlowResourceByActivityId(Long activityId);

    /**
     * 获取活动流程
     */
    List<ActivityProcessDTO> getActivityProcessByActivityId(Long activityId);


    /**
     * 删除活动
     */
    Boolean deleteActivityApply(Long activityId);

    /**
     * 根据活动id获取其对应的模板id
     *
     * @param activityId 活动id
     * @return 模板id
     */
    Long getActivityTemplateIdByActivityId(Long activityId);

    /**
     * 活动提报详细信息，用于APP端查看
     *
     * @param beginDate 开始时间
     * @param endDate   结束时间
     * @param appId     应用ID
     * @return 结果
     */
    List<ActivityApplyDetailForAppDTO> getActivityApplyDetailForApp(
            Date beginDate, Date endDate, Integer appId);

    /**
     * 根据活动ID列表获取活动提报详细信息，用于APP端查看
     *
     * @param activityIds 活动ID列表
     * @param appId       应用ID
     * @return 结果
     */
    List<ActivityApplyDetailForAppDTO> getActivityApplyDetailForAppByActivityIds(
            List<Long> activityIds, Integer appId);

    /**
     * 根据厅主ID获取活动提报详细信息，用于APP端查看
     *
     * @param njId      厅主ID
     * @param beginDate 开始时间
     * @param endDate   结束时间
     * @param appId     应用ID
     * @return 结果
     */
    List<ActivityApplyDetailForAppDTO> getActivityApplyDetailForAppByNjId(
            Long njId, Date beginDate, Date endDate, Integer appId);

    /**
     * 修改活动提报
     *
     * @param oldOfficialSeatResource
     */
    Result<Void> modifyActivityApply(ActivityApplyParamDTO applyParamDTO, ActivityFlowResourceDTO oldOfficialSeatResource);


    /**
     * 根据厅主ID获取活动提报简单信息
     *
     * @param njId      厅主ID
     * @param beginDate 开始时间
     * @param endDate   结束时间
     * @param appId     应用ID
     * @return 结果
     */
    List<ActivityApplyInfoDTO> getActivityApplyInfoByNjId(
            Long njId, Date beginDate, Date endDate, Integer appId);

    /**
     * 获取时间范围内活动提报信息
     * @param request
     * @return
     */
    List<ActivityApplyInfoSimpleDTO> getInTimeRangeActivityApply(RequestGetInTimeRangeActivityApply request);


    /**
     * 获取活动提报简单信息(不限njId)
     * @param beginDate 开始时间
     * @param endDate 结束时间
     * @param appId 应用ID
     * @return 结果
     */
    List<ActivityApplyInfoDTO> getActivityApplyInfoWithTimeRange(
            Date beginDate, Date endDate, Integer appId);


    /**
     * 获取时间范围内配置了节目单的活动提报信息
     */
    List<ActivityApplyInfoSimpleDTO> getInTimeRangeActivityApplyByProgramme(Date startTime, Date endTime, Integer appId, Long resourceConfigId);


    /** 批量更新活动的通知状态
     * @param activityIds 活动提报表id
     * @return 成功更新数量
     */
    int batchUpdateSendReportStatus(List<Long> activityIds);

    /**
     * 查询活动申请数量
     */
    Integer getApplyCountByWeekly(Integer appId, Long njId, Date startTime);


    int ACTIVITY_APPLY_DATE_SAVE_FAIL = 1;

    int AGREE_ACTIVITY_APPLY_FAIL = 2;

    int AGREE_ACTIVITY_APPLY_REPEAT = 3;

}
