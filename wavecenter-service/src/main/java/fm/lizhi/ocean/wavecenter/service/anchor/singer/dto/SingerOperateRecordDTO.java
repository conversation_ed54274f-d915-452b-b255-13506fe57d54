package fm.lizhi.ocean.wavecenter.service.anchor.singer.dto;

import java.util.Date;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerRecordOperateTypeEnum;
import lombok.Data;

@Data
public class SingerOperateRecordDTO {

    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 厅主ID
     */
    private Long njId;

    /**
     * 歌手类型, 1: 新锐歌手，2：优质歌手，3：明星歌手
     */
    private Integer singerType;

    /**
     * 操作类型, 1 认证通过; 2 淘汰; 3 晋升
     * @see SingerRecordOperateTypeEnum
     */
    private Integer operateType;

    /**
     * 家族ID
     */
    private Long familyId;

    /**
     * 歌曲名称
     */
    private String songName;

    /**
     * 歌曲风格
     */
    private String songStyle;

    /**
     * 是否原创
     */
    private Boolean originalSinger;

    /**
     * 淘汰原因
     */
    private String eliminationReason;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 通过时间
     */
    private Date passTime;

    /**
     * 环境：TEST/PRE/PRO
     */
    private String deployEnv;

}
