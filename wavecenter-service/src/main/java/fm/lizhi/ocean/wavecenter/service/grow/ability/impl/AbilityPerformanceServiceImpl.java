package fm.lizhi.ocean.wavecenter.service.grow.ability.impl;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.grow.ability.request.RequestGetPlayerPerformance;
import fm.lizhi.ocean.wavecenter.api.grow.ability.request.RequestGetRoomPerformance;
import fm.lizhi.ocean.wavecenter.api.grow.ability.request.RequestGetRoomPlayerRank;
import fm.lizhi.ocean.wavecenter.api.grow.ability.response.ResponseGetPlayerPerformance;
import fm.lizhi.ocean.wavecenter.api.grow.ability.response.ResponseGetRoomPerformance;
import fm.lizhi.ocean.wavecenter.api.grow.ability.response.ResponseGetRoomPlayerRank;
import fm.lizhi.ocean.wavecenter.api.grow.ability.service.AbilityPerformanceService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.service.grow.ability.manager.AbilityPerformanceManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

@ServiceProvider
@Slf4j
public class AbilityPerformanceServiceImpl implements AbilityPerformanceService {

    @Autowired
    private AbilityPerformanceManager abilityPerformanceManager;

    @Override
    public Result<ResponseGetRoomPerformance> getRoomPerformance(RequestGetRoomPerformance request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));
        ResponseGetRoomPerformance response = abilityPerformanceManager.getRoomPerformance(request);
        LogContext.addResLog("abilitiesSize={}", CollectionUtils.size(response.getAbilities()));
        if (log.isDebugEnabled()) {
            log.debug("getRoomPerformance request={}, response={}", JsonUtils.toJsonString(request), JsonUtils.toJsonString(response));
        }
        return RpcResult.success(response);
    }

    @Override
    public Result<ResponseGetRoomPlayerRank> getRoomPlayerRank(RequestGetRoomPlayerRank request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));
        ResponseGetRoomPlayerRank response = abilityPerformanceManager.getRoomPlayerRank(request);
        LogContext.addResLog("listSize={}", CollectionUtils.size(response.getList()));
        if (log.isDebugEnabled()) {
            log.debug("getRoomPlayerRank request={}, response={}", JsonUtils.toJsonString(request), JsonUtils.toJsonString(response));
        }
        return RpcResult.success(response);
    }

    @Override
    public Result<ResponseGetPlayerPerformance> getPlayerPerformance(RequestGetPlayerPerformance request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));
        ResponseGetPlayerPerformance response = abilityPerformanceManager.getPlayerPerformance(request);
        LogContext.addResLog("weeksSize={}", CollectionUtils.size(response.getWeeks()));
        if (log.isDebugEnabled()) {
            log.debug("getPlayerPerformance request={}, response={}", JsonUtils.toJsonString(request), JsonUtils.toJsonString(response));
        }
        return RpcResult.success(response);
    }
}
