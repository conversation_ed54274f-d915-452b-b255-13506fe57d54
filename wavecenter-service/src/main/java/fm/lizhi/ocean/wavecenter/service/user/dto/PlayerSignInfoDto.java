package fm.lizhi.ocean.wavecenter.service.user.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/4/24 18:03
 */
@Data
public class PlayerSignInfoDto {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 房主id
     */
    private Long njId;

    /**
     * 陪玩用户ID
     */
    private Long userId;

    /**
     * ADMIN:厅管侧 PLAYER:用户侧
     */
    private String userType;

    /**
     * SIGN签约 CANCEL解约
     */
    private String type;

    /**
     * WAIT_SIGN待签署 OVERDUE逾期未签 SIGN_SUCCEED签约成功 STOP_CONTRACT已解约
     */
    private String status;

    /**
     * 签署截止时间
     */
    private Date signDeadline;

    /**
     * 签约生效开始时间
     */
    private Date startTime;

    /**
     * 签约生效结束时间
     */
    private Date endTime;

    /**
     * 主键ID
     */
    private Long parentId;

    /**
     * 解约时间
     */
    private Date stopTime;

    /**
     * 额外信息
     */
    private String extra;

    /**
     * 签约申请发起时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date modifyTime;
}
