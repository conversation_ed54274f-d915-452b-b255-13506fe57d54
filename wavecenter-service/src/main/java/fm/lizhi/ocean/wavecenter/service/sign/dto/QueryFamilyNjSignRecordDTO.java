package fm.lizhi.ocean.wavecenter.service.sign.dto;

import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.Singular;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/28 15:06
 */
@Getter
@Builder
public class QueryFamilyNjSignRecordDTO {

    private Long njId;

    private Long familyUserId;

    @Singular
    private List<SignRelationEnum> statuses;

    @Singular
    private List<ContractTypeEnum> types;

    private RoleEnum role;

    @Builder.Default
    private Integer pageNo = 1;

    @Builder.Default
    private Integer pageSize = 1;

    private Long parentId;

    private Long id;

}
