package fm.lizhi.ocean.wavecenter.service.permissions.manager;

import fm.lizhi.ocean.wavecenter.service.permissions.dto.ComponentDto;

import javax.annotation.Nonnull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/27 16:01
 */
public interface ComponentManager {

    /**
     * 获取角色的组件
     * @param roleCode
     * @return
     */
    @Nonnull
    List<ComponentDto> getRoleComponent(String roleCode);

}
