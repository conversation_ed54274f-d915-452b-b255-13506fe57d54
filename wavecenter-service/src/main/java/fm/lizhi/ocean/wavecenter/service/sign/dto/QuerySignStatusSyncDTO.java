package fm.lizhi.ocean.wavecenter.service.sign.dto;

import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.FlowConfirmStatusEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.Singular;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/28 14:30
 */
@Getter
@Builder
public class QuerySignStatusSyncDTO {

    @Singular
    private List<FlowConfirmStatusEnum> confirmStatuses;

    private ContractTypeEnum type;

    private Integer appId;

    @Builder.Default
    private int pageNo = 1;

    @Builder.Default
    private int pageSize = 1;

    private Long contractId;

    private RoleEnum role;

}
