package fm.lizhi.ocean.wavecenter.service.background.activitycenter.impl;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestPageActivityResources;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityResource;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityResource;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseActivityResource;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityResourceConfigService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityResourceManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 */
@ServiceProvider
@Slf4j
public class ActivityResourceConfigServiceImpl implements ActivityResourceConfigService {

    @Autowired
    private ActivityResourceManager activityResourceManager;


    @Override
    public Result<Void> saveActivityResource(RequestSaveActivityResource param) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(param));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(param));

        return ResultHandler.handle(param.getAppId(), () -> activityResourceManager.saveActivityResource(param));
    }

    @Override
    public Result<Void> updateActivityResource(RequestUpdateActivityResource param) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(param));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(param));

        return ResultHandler.handle(param.getAppId(), () -> activityResourceManager.updateActivityResource(param));
    }

    @Override
    public Result<Void> deleteActivityResource(Long id, int appId, String operator) {
        LogContext.addReqLog("id={}, appId={}, operator={}", id, appId, operator);
        LogContext.addResLog("id={}, appId={}, operator={}", id, appId, operator);
        return ResultHandler.handle(appId, () -> activityResourceManager.deleteActivityResource(id, appId, operator));
    }

    @Override
    public Result<PageBean<ResponseActivityResource>> listActivityResource(RequestPageActivityResources param) {
        LogContext.addReqLog("param={}", JsonUtil.dumps(param));
        LogContext.addResLog("param={}", JsonUtil.dumps(param));

        return ResultHandler.handle(param.getAppId(), () -> activityResourceManager.listActivityResource(param));
    }

    @Override
    public Result<List<ResponseActivityResource>> listActivityResourceByLevelId(Long levelId, int appId) {
        LogContext.addReqLog("levelId={}, appId={}", levelId, appId);
        LogContext.addResLog("levelId={}, appId={}", levelId, appId);

        return ResultHandler.handle(appId, () -> activityResourceManager.listActivityResourceByLevelId(levelId, appId));
    }
}

