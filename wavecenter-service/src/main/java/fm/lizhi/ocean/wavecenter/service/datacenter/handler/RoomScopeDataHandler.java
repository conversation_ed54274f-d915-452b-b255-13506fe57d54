package fm.lizhi.ocean.wavecenter.service.datacenter.handler;

import com.google.common.collect.Lists;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.ocean.wavecenter.api.common.constants.MetricsEnum;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.GuildGetKeyIndicatorsParamBean;
import fm.lizhi.ocean.wavecenter.base.util.CalculateUtil;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.*;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.PlayerDataManager;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.RoomDataManager;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 厅数据范围的关键指标查询
 * <AUTHOR>
 * @date 2024/12/12 11:42
 */
@Component
public class RoomScopeDataHandler extends AbsDataHandler<GuildGetKeyIndicatorsParamBean, DataFamilyDayDTO>{

    @Autowired
    private RoomDataManager roomDataManager;
    @Autowired
    private PlayerDataManager playerDataManager;

    /**
     * 可通过每个厅的统计数据累加的指标
     */
    private final List<String> roomAddMetrics = Lists.newArrayList(
            MetricsEnum.ALL_INCOME.getValue()
            , MetricsEnum.SIGN_HALL_INCOME.getValue()
            , MetricsEnum.PERSONAL_HALL_INCOME.getValue()
            , MetricsEnum.OFFICIAL_HALL_INCOME.getValue()
            , MetricsEnum.CHARM.getValue()
            , MetricsEnum.NOBLE_INCOME.getValue()
            , MetricsEnum.PERSONAL_NOBLE_INCOME.getValue()
            , MetricsEnum.SIGN_UP_GUEST_PLAYER_CNT.getValue()
            , MetricsEnum.INCOME_PLAYER_CNT.getValue()
            , MetricsEnum.UP_PLAYER_RATE.getValue()
            , MetricsEnum.INCOME_PLAYER_RATE.getValue()
            , MetricsEnum.PLAYER_AVG_INCOME.getValue()
            , MetricsEnum.PLAYER_AVG_CHARM.getValue()
    );

    /**
     * 基于厅维度数据计算的指标
     */
    private final List<String> baseRoomMetrics = Lists.newArrayList(
            MetricsEnum.OPEN_ROOM_CNT.getValue()
            , MetricsEnum.INCOME_ROOM_CNT.getValue()
            , MetricsEnum.OPEN_RATE.getValue()
            , MetricsEnum.INCOME_ROOM_RATE.getValue()
            , MetricsEnum.ROOM_AVG_INCOME.getValue()
            , MetricsEnum.ROOM_AVG_CHARM.getValue()
    );

    /**
     * 基于主播维度数据计算的指标
     */
    private final List<String> basePlayerMetrics = Lists.newArrayList(
           MetricsEnum.SIGN_PLAYER_CNT.getValue()
            , MetricsEnum.UP_PLAYER_RATE.getValue()
    );

    @Override
    protected Map<String, String> getDayKeyIndicators(GuildGetKeyIndicatorsParamBean paramBean, Date day, List<String> queryValueMetrics) {
        Integer appId = paramBean.getAppId();
        Long familyId = paramBean.getFamilyId();
        List<Long> roomIds = paramBean.getRoomIds();
        Map<String, String> result = new HashMap<>();

        //可通过每个厅的统计数据累加的指标
        Set<String> addMetrics = intersectionAddMetrics(queryValueMetrics);
        LogContext.addResLog("addMetrics={}", JsonUtil.dumps(addMetrics));
        Map<String, String> roomAddMetricsMap = roomDataManager.getRoomDayKeyIndicatorsSum(appId, familyId, roomIds, day, new ArrayList<>(addMetrics));
        result.putAll(roomAddMetricsMap);

        //厅维度统计指标
        List<DataRoomFamilyDayDTO> roomDataList = roomDataManager.getRoomDayData(familyId, roomIds, day);
        List<IDataRoomFamily> iRoomDataList = roomDataList.stream().map(v -> (IDataRoomFamily) v).collect(Collectors.toList());
        computeByRoomData(result, queryValueMetrics, iRoomDataList, roomIds);

        //主播维度统计指标
        Map<String, String> playerMetricsMap = playerDataManager.countPlayerDay(familyId, roomIds, needCountMetrics(queryValueMetrics), day);
        LogContext.addResLog("playerMetricsMap={}", JsonUtil.dumps(playerMetricsMap));
        if (MapUtils.isNotEmpty(playerMetricsMap)) {
            result.putAll(playerMetricsMap);
        }
        computeByPlayerMetrics(result, playerMetricsMap, queryValueMetrics);

        return result;
    }

    @Override
    protected Map<String, String> getWeekKeyIndicators(GuildGetKeyIndicatorsParamBean paramBean, Date startDay, Date endDay, List<String> queryValueMetrics) {

        Long familyId = paramBean.getFamilyId();
        List<Long> roomIds = paramBean.getRoomIds();
        Map<String, String> result = new HashMap<>();

        //可通过每个厅的统计数据累加的指标
        Set<String> addMetrics = intersectionAddMetrics(queryValueMetrics);
        LogContext.addResLog("addMetrics={}", JsonUtil.dumps(addMetrics));
        Map<String, String> roomAddMetricsMap = roomDataManager.getRoomWeekKeyIndicatorsSum(familyId, roomIds, startDay, endDay, new ArrayList<>(addMetrics));
        result.putAll(roomAddMetricsMap);

        //厅维度统计指标
        List<DataRoomFamilyWeekDTO> roomDataList = roomDataManager.getRoomWeekData(familyId, roomIds, startDay, endDay);
        List<IDataRoomFamily> iRoomDataList = roomDataList.stream().map(v -> (IDataRoomFamily) v).collect(Collectors.toList());
        computeByRoomData(result, queryValueMetrics, iRoomDataList, roomIds);

        //主播维度统计指标
        Map<String, String> playerMetricsMap = playerDataManager.countPlayerWeek(familyId, roomIds, startDay, endDay, needCountMetrics(queryValueMetrics));
        LogContext.addResLog("playerMetricsMap={}", JsonUtil.dumps(playerMetricsMap));
        if (MapUtils.isNotEmpty(playerMetricsMap)) {
            result.putAll(playerMetricsMap);
        }
        computeByPlayerMetrics(result, playerMetricsMap, queryValueMetrics);

        return result;
    }

    @Override
    protected Map<String, String> getMonthKeyIndicators(GuildGetKeyIndicatorsParamBean paramBean, Date month, List<String> queryValueMetrics) {
        Long familyId = paramBean.getFamilyId();
        List<Long> roomIds = paramBean.getRoomIds();
        Map<String, String> result = new HashMap<>();

        //可通过每个厅的统计数据累加的指标
        Set<String> addMetrics = intersectionAddMetrics(queryValueMetrics);
        LogContext.addResLog("addMetrics={}", JsonUtil.dumps(addMetrics));
        Map<String, String> roomAddMetricsMap = roomDataManager.getRoomMonthKeyIndicatorsSum(familyId, roomIds, month, new ArrayList<>(addMetrics));
        result.putAll(roomAddMetricsMap);

        //厅维度统计指标
        List<DataRoomFamilyMonthDTO> roomDataList = roomDataManager.getRoomMonthData(familyId, roomIds, month);
        List<IDataRoomFamily> iRoomDataList = roomDataList.stream().map(v -> (IDataRoomFamily) v).collect(Collectors.toList());
        computeByRoomData(result, queryValueMetrics, iRoomDataList, roomIds);

        //主播维度统计指标
        Map<String, String> playerMetricsMap = playerDataManager.countPlayerMonth(familyId, roomIds, month, needCountMetrics(queryValueMetrics));
        LogContext.addResLog("playerMetricsMap={}", JsonUtil.dumps(playerMetricsMap));
        if (MapUtils.isNotEmpty(playerMetricsMap)) {
            result.putAll(playerMetricsMap);
        }
        computeByPlayerMetrics(result, playerMetricsMap, queryValueMetrics);

        return result;
    }

    @Override
    protected Map<Integer, DataFamilyDayDTO> getDaysData(IndicatorTrendParam paramBean, List<String> metrics, List<Integer> dayValues) {
        List<Long> roomIds = paramBean.getRoomIds();
        Long familyId = paramBean.getFamilyId();

        // <dayValue, data>
        Map<Integer, DataFamilyDayDTO> result = new HashMap<>();

        //可通过每个厅的统计数据累加的指标
        Set<String> addMetrics = intersectionAddMetrics(metrics);
        if (CollectionUtils.isNotEmpty(addMetrics)) {
            List<DataRoomFamilyDayDTO> dtoList = roomDataManager.getRoomDayKeyIndicatorsSum(familyId, roomIds, dayValues, new ArrayList<>(addMetrics));
            processDaysSumData(result, dtoList);
        }

        //签约厅数
        if (metrics.contains(MetricsEnum.SIGN_ROOM_CNT.getValue())) {
            for (Integer dayValue : dayValues) {
                DataFamilyDayDTO rDto = result.computeIfAbsent(dayValue, k -> new DataFamilyDayDTO());
                rDto.setSignRoomCnt(roomIds.size());
            }
        }

        //厅维度统计指标
        if (existBaseRoomMetrics(metrics)) {
            List<DataRoomFamilyDayDTO> roomDtoList = roomDataManager.getRoomFamilyDayList(new GetRoomDayListParam()
                    .setFamilyId(familyId)
                    .setRoomIds(roomIds)
                    .setDayValues(dayValues)
            );
            computeDaysByRoomData(result, roomDtoList, metrics, roomIds);
        }

        //主播维度统计指标
        if (existBasePlayerMetrics(metrics)) {
            List<String> needCountMetrics = needCountMetrics(metrics);
            Map<Integer, Map<String, Integer>> dayMetricsCntMap = playerDataManager.countPlayerDays(familyId, roomIds, needCountMetrics, dayValues);
            computeDaysByPlayerMetrics(result, dayMetricsCntMap, metrics);
        }

        return result;
    }

    /**
     * 需要查询主播统计数据的指标
     * @param queryValueMetrics
     * @return
     */
    private List<String> needCountMetrics(List<String> queryValueMetrics){
        List<String> result = new ArrayList<>();
        result.addAll(queryValueMetrics);

        //主播上麦率=上麦主播数/签约主播数
        if (queryValueMetrics.contains(MetricsEnum.UP_PLAYER_RATE.getValue())) {
            result.add(MetricsEnum.SIGN_PLAYER_CNT.getValue());
        }

        return result;
    }

    /**
     * 处理多日合计指标
     * @param result
     * @param dtoList
     */
    private void processDaysSumData(Map<Integer, DataFamilyDayDTO> result, List<DataRoomFamilyDayDTO> dtoList){
        if (CollectionUtils.isEmpty(dtoList)) {
            return;
        }

        for (DataRoomFamilyDayDTO dataDto : dtoList) {
            DataFamilyDayDTO rDto = result.computeIfAbsent(dataDto.getStatDateValue(), v -> new DataFamilyDayDTO());
            BeanUtils.copyProperties(dataDto, rDto);
            rDto.setUpGuestPlayerCnt(dataDto.getSignUpGuestPlayerCnt());
            result.put(dataDto.getStatDateValue(), rDto);
        }
    }

    /**
     * 统计多日指标合计
     * @param result
     * @param dayMetricsCntMap
     * @param queryValueMetrics
     */
    private void computeDaysByPlayerMetrics(Map<Integer, DataFamilyDayDTO> result, Map<Integer, Map<String, Integer>> dayMetricsCntMap, List<String> queryValueMetrics){
        //把dayMetricsCntMap转为result

        for (Map.Entry<Integer, Map<String, Integer>> entry : dayMetricsCntMap.entrySet()) {

            Integer dayValue = entry.getKey();
            Map<String, Integer> metricsCntMap = entry.getValue();

            DataFamilyDayDTO rDto = result.computeIfAbsent(dayValue, k -> new DataFamilyDayDTO());

            //上麦主播数
            Integer upPlayerCnt = rDto.getUpGuestPlayerCnt();
            //签约主播数
            Integer signPlayerCnt = metricsCntMap.get(MetricsEnum.SIGN_PLAYER_CNT.getValue());
            rDto.setSignPlayerCnt(signPlayerCnt);
            //有收入主播数
            Integer incomePlayerCnt = rDto.getIncomePlayerCnt();

            //主播上麦率=上麦主播数/签约主播数
            if (queryValueMetrics.contains(MetricsEnum.UP_PLAYER_RATE.getValue())) {
                rDto.setUpPlayerRate(doDevide(upPlayerCnt, signPlayerCnt));
            }

            //有收入主播占比=有收入主播数/上麦主播数
            if (queryValueMetrics.contains(MetricsEnum.INCOME_PLAYER_RATE.getValue())) {
                rDto.setIncomePlayerRate(doDevide(incomePlayerCnt, upPlayerCnt));
            }

            BigDecimal allIncome = rDto.getAllIncome();
            Integer charm = rDto.getCharm();

            //人均收入=公会总收入/有收入主播数
            if (queryValueMetrics.contains(MetricsEnum.PLAYER_AVG_INCOME.getValue())) {
                rDto.setPlayerAvgIncome(doDevide(allIncome, incomePlayerCnt));
            }

            //人均魅力值=公会总魅力值/有收入主播数
            if (queryValueMetrics.contains(MetricsEnum.PLAYER_AVG_CHARM.getValue())) {
                rDto.setPlayerAvgCharm(doDevide(charm, incomePlayerCnt));
            }

        }

    }

    /**
     * 通过主播数据计算的指标
     * @param result
     * @param playerMetricsMap
     * @param queryValueMetrics
     */
    private void computeByPlayerMetrics(Map<String, String> result
            , Map<String, String> playerMetricsMap, List<String> queryValueMetrics) {
        //上麦主播数
        String upPlayerCnt = result.get(MetricsEnum.SIGN_UP_GUEST_PLAYER_CNT.getValue());
        //签约主播数
        String signPlayerCnt = playerMetricsMap.get(MetricsEnum.SIGN_PLAYER_CNT.getValue());
        //有收入主播数
        String incomePlayerCnt = result.get(MetricsEnum.INCOME_PLAYER_CNT.getValue());

        //主播上麦率=上麦主播数/签约主播数
        if (queryValueMetrics.contains(MetricsEnum.UP_PLAYER_RATE.getValue())) {
            result.put(MetricsEnum.UP_PLAYER_RATE.getValue()
                    , CalculateUtil.formatRate(doDevide(upPlayerCnt, signPlayerCnt)));
        }

        //有收入主播占比=有收入主播数/上麦主播数
        if (queryValueMetrics.contains(MetricsEnum.INCOME_PLAYER_RATE.getValue())) {
            result.put(MetricsEnum.INCOME_PLAYER_RATE.getValue()
                    , CalculateUtil.formatRate(doDevide(incomePlayerCnt, upPlayerCnt)));
        }

        String allIncome = result.get(MetricsEnum.ALL_INCOME.getValue());
        String charm = result.get(MetricsEnum.CHARM.getValue());

        //人均收入=公会总收入/有收入主播数
        if (queryValueMetrics.contains(MetricsEnum.PLAYER_AVG_INCOME.getValue())) {
            result.put(MetricsEnum.PLAYER_AVG_INCOME.getValue()
                    , CalculateUtil.formatDecimal(doDevide(allIncome, incomePlayerCnt)));
        }

        //人均魅力值=公会总魅力值/有收入主播数
        if (queryValueMetrics.contains(MetricsEnum.PLAYER_AVG_CHARM.getValue())) {
            result.put(MetricsEnum.PLAYER_AVG_CHARM.getValue()
                    , CalculateUtil.formatDecimal(doDevide(charm, incomePlayerCnt)));
        }
        LogContext.addResLog("computeByPlayerMetrics.result={}", JsonUtil.dumps(result));
    }

    /**
     * 统计多日指标合计
     * @param result
     * @param roomDtoList
     * @param queryValueMetrics
     * @param roomIds
     */
    private void computeDaysByRoomData(Map<Integer, DataFamilyDayDTO> result, List<DataRoomFamilyDayDTO> roomDtoList, List<String> queryValueMetrics, List<Long> roomIds){
        //把roomDtoList转为result
        Map<Integer, List<DataRoomFamilyDayDTO>> dtoDayMap = roomDtoList.stream().collect(Collectors.groupingBy(DataRoomFamilyDayDTO::getStatDateValue));
        for (Map.Entry<Integer, List<DataRoomFamilyDayDTO>> entry : dtoDayMap.entrySet()) {
            Integer dayValue = entry.getKey();

            //一天所有厅的数据
            List<IDataRoomFamily> dayDataList = entry.getValue().stream().map(v -> (IDataRoomFamily) v).collect(Collectors.toList());

            DataFamilyDayDTO rDto = result.computeIfAbsent(dayValue, k -> new DataFamilyDayDTO());

            Pair<Integer, Integer> cntPair = countRoomCnt(dayDataList);
            //开播厅数
            int openRoomCnt = cntPair.getLeft();
            //有收入厅数
            int incomeRoomCnt = cntPair.getRight();

            //等于roomIds长度的指标
            int roomCnt = roomIds.size();
            if (queryValueMetrics.contains(MetricsEnum.OPEN_ROOM_CNT.getValue())) {
                rDto.setOpenRoomCnt(openRoomCnt);
            }
            if (queryValueMetrics.contains(MetricsEnum.INCOME_ROOM_CNT.getValue())) {
                rDto.setIncomeRoomCnt(incomeRoomCnt);
            }

            //独立计算的指标
            //开播率=开播厅数/授权厅数
            if (queryValueMetrics.contains(MetricsEnum.OPEN_RATE.getValue())) {
                rDto.setOpenRate(doDevide(openRoomCnt, roomCnt));
            }

            //有收入厅占比=有收入厅数 / 开播厅数
            if (queryValueMetrics.contains(MetricsEnum.INCOME_ROOM_RATE.getValue())) {
                rDto.setIncomeRoomRate(doDevide(incomeRoomCnt, openRoomCnt));
            }

            //厅均收入=总收入/有收入厅数
            BigDecimal allIncome = rDto.getAllIncome();
            if (queryValueMetrics.contains(MetricsEnum.ROOM_AVG_INCOME.getValue())) {
                rDto.setRoomAvgIncome(doDevide(allIncome, incomeRoomCnt));
            }

            //厅均魅力值=魅力值/有收入厅数
            Integer charm = rDto.getCharm();
            if (queryValueMetrics.contains(MetricsEnum.ROOM_AVG_CHARM.getValue())) {
                rDto.setRoomAvgCharm(doDevide(charm, incomeRoomCnt));
            }

        }
    }

    /**
     * 通过厅数据计算的指标
     */
    private void computeByRoomData(Map<String, String> result
            , List<String> queryValueMetrics, List<IDataRoomFamily> roomDataList, List<Long> roomIds){
        Pair<Integer, Integer> cntPair = countRoomCnt(roomDataList);
        //开播厅数
        int openRoomCnt = cntPair.getLeft();
        LogContext.addResLog("openRoomCnt={}", openRoomCnt);
        //有收入厅数
        int incomeRoomCnt = cntPair.getRight();
        LogContext.addResLog("incomeRoomCnt={}", incomeRoomCnt);

        //等于roomIds长度的指标
        int roomCnt = roomIds.size();
        LogContext.addResLog("roomCnt={}", roomCnt);
        if (queryValueMetrics.contains(MetricsEnum.SIGN_ROOM_CNT.getValue())) {
            result.put(MetricsEnum.SIGN_ROOM_CNT.getValue(), String.valueOf(roomCnt));
        }

        if (queryValueMetrics.contains(MetricsEnum.OPEN_ROOM_CNT.getValue())) {
            result.put(MetricsEnum.OPEN_ROOM_CNT.getValue(), String.valueOf(openRoomCnt));
        }
        if (queryValueMetrics.contains(MetricsEnum.INCOME_ROOM_CNT.getValue())) {
            result.put(MetricsEnum.INCOME_ROOM_CNT.getValue(), String.valueOf(incomeRoomCnt));
        }

        //独立计算的指标
        //开播率=开播厅数/授权厅数
        if (queryValueMetrics.contains(MetricsEnum.OPEN_RATE.getValue())) {
            result.put(MetricsEnum.OPEN_RATE.getValue(), CalculateUtil.formatRate(doDevide(openRoomCnt, roomCnt)));
        }

        //有收入厅占比=有收入厅数 / 开播厅数
        if (queryValueMetrics.contains(MetricsEnum.INCOME_ROOM_RATE.getValue())) {
            if (openRoomCnt == 0) {
                result.put(MetricsEnum.INCOME_ROOM_RATE.getValue(), CalculateUtil.formatRate(BigDecimal.ZERO));
            } else {
                result.put(MetricsEnum.INCOME_ROOM_RATE.getValue()
                        , CalculateUtil.formatRate(doDevide(incomeRoomCnt, openRoomCnt)));
            }
        }

        //厅均收入=总收入/有收入厅数
        String allIncome = result.get(MetricsEnum.ALL_INCOME.getValue());
        if (queryValueMetrics.contains(MetricsEnum.ROOM_AVG_INCOME.getValue())) {
            result.put(MetricsEnum.ROOM_AVG_INCOME.getValue()
                    , CalculateUtil.formatDecimal(doDevide(allIncome, incomeRoomCnt)));
        }

        //厅均魅力值=魅力值/有收入厅数
        String charm = result.get(MetricsEnum.CHARM.getValue());
        if (queryValueMetrics.contains(MetricsEnum.ROOM_AVG_CHARM.getValue())) {
            result.put(MetricsEnum.ROOM_AVG_CHARM.getValue()
                    , CalculateUtil.formatDecimal(doDevide(charm, incomeRoomCnt)));
        }

        LogContext.addResLog("result={}", JsonUtil.dumps(result));
    }

    private Pair<Integer, Integer> countRoomCnt(List<IDataRoomFamily> roomDataList){
        //开播厅数
        Set<Long> openRoomSet = new HashSet<>();
        //有收入厅数
        Set<Long> incomeRoomSet = new HashSet<>();
        for (IDataRoomFamily roomData : roomDataList) {
            if (roomData.getOpenDuration() != null && roomData.getOpenDuration().intValue() > 0) {
                openRoomSet.add(roomData.getRoomId());
            }
            if (roomData.getAllIncome() != null && roomData.getAllIncome().intValue() > 0) {
                incomeRoomSet.add(roomData.getRoomId());
            }
        }
        return Pair.of(openRoomSet.size(), incomeRoomSet.size());
    }

    /**
     * 是否需要查询主播维度指标数据
     * @param metrics
     * @return
     */
    private boolean existBasePlayerMetrics(List<String> metrics){
        for (String metric : metrics) {
            if (basePlayerMetrics.contains(metric)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 是否需要查询基础厅维度指标
     * @param metrics
     * @return
     */
    private boolean existBaseRoomMetrics(List<String> metrics){
        for (String metric : metrics) {
            if (baseRoomMetrics.contains(metric)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 过滤出参数中需要累加的指标
     * @param queryValueMetrics
     * @return
     */
    private Set<String> intersectionAddMetrics(List<String> queryValueMetrics){
        if (CollectionUtils.isEmpty(queryValueMetrics)) {
            return Collections.emptySet();
        }

        Set<String> result = new HashSet<>();
        for (String queryValueMetric : queryValueMetrics) {
            if (roomAddMetrics.contains(queryValueMetric)){
                result.add(queryValueMetric);
            }
        }

        if (queryValueMetrics.contains(MetricsEnum.ROOM_AVG_INCOME.getValue())
                || queryValueMetrics.contains(MetricsEnum.PLAYER_AVG_INCOME.getValue())) {
            //计算需要使用到
            result.add(MetricsEnum.ALL_INCOME.getValue());
        }
        if (queryValueMetrics.contains(MetricsEnum.ROOM_AVG_CHARM.getValue())
                || queryValueMetrics.contains(MetricsEnum.PLAYER_AVG_CHARM.getValue())) {
            //计算需要使用到
            result.add(MetricsEnum.CHARM.getValue());
        }

        //主播上麦率=上麦主播数/签约主播数
        if (queryValueMetrics.contains(MetricsEnum.UP_PLAYER_RATE.getValue())) {
            result.add(MetricsEnum.SIGN_UP_GUEST_PLAYER_CNT.getValue());
        }

        //有收入主播占比=有收入主播数/上麦主播数
        if (queryValueMetrics.contains(MetricsEnum.INCOME_PLAYER_RATE.getValue())) {
            result.add(MetricsEnum.INCOME_PLAYER_CNT.getValue());
            result.add(MetricsEnum.SIGN_UP_GUEST_PLAYER_CNT.getValue());
        }

        //人均收入=公会总收入/有收入主播数
        if (queryValueMetrics.contains(MetricsEnum.PLAYER_AVG_INCOME.getValue())) {
            result.add(MetricsEnum.INCOME_PLAYER_CNT.getValue());
        }

        //人均魅力值=公会总魅力值/有收入主播数
        if (queryValueMetrics.contains(MetricsEnum.PLAYER_AVG_CHARM.getValue())) {
            result.add(MetricsEnum.INCOME_PLAYER_CNT.getValue());
        }

        return result;
    }

    private BigDecimal doDevide(String a, Integer b){
        if (StringUtils.isBlank(a) || b == null) {
            return BigDecimal.ZERO;
        }
        if (BigDecimal.ZERO.compareTo(new BigDecimal(b)) == 0) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(a).divide(new BigDecimal(b), 4, RoundingMode.HALF_UP);
    }

    private BigDecimal doDevide(BigDecimal a, Integer b){
        if (a == null || b == null) {
            return BigDecimal.ZERO;
        }
        if (BigDecimal.ZERO.compareTo(new BigDecimal(b)) == 0) {
            return BigDecimal.ZERO;
        }
        return a.divide(new BigDecimal(b), 4, RoundingMode.HALF_UP);
    }

    private BigDecimal doDevide(String a, String b){
        if (StringUtils.isBlank(a) || StringUtils.isBlank(b)) {
            return BigDecimal.ZERO;
        }
        if (BigDecimal.ZERO.compareTo(new BigDecimal(b)) == 0) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(a).divide(new BigDecimal(b), 4, RoundingMode.HALF_UP);
    }

    private BigDecimal doDevide(Integer a, Integer b){
        if (a == null || b == null) {
            return BigDecimal.ZERO;
        }
        if (BigDecimal.ZERO.compareTo(new BigDecimal(b)) == 0) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(a).divide(new BigDecimal(b), 4, RoundingMode.HALF_UP);
    }
}
