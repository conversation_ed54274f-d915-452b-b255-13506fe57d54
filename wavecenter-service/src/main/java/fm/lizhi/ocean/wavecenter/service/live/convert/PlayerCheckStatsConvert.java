package fm.lizhi.ocean.wavecenter.service.live.convert;

import fm.lizhi.ocean.wavecenter.api.live.bean.*;
import fm.lizhi.ocean.wavecenter.service.live.dto.*;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/11 16:22
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PlayerCheckStatsConvert {

    PlayerCheckStatsConvert I = Mappers.getMapper(PlayerCheckStatsConvert.class);

    PlayerCheckDayStatsReqDto playerCheckDayStatsReq2Dto(PlayerCheckDayStatsReq req);

    PlayerCheckDayStatsBean dayStatsDto2Bean(PlayerCheckDayStatsDto dto);

    List<PlayerCheckDayStatsBean> dayStatsDtos2Beans(List<PlayerCheckDayStatsDto> dtos);

    PlayerCheckHourStatsReqDto playerCheckHourStatsReq2Dto(PlayerCheckHourStatsReq req);

    @Mappings({
            @Mapping(source = "roomId", target = "room.id")
    })
    PlayerCheckHourStatsBean hourStatsDto2Bean(PlayerCheckHourStatsDto dto);

    List<PlayerCheckHourStatsBean> hourStatsDtos2Beans(List<PlayerCheckHourStatsDto> dtos);

    PlayerCheckHourStatsDayBean hourStatsDayDto2Bean(PlayerCheckHourStatsDayDto dto);

    List<PlayerCheckHourStatsDayBean> hourStatsDayDtos2Beans(List<PlayerCheckHourStatsDayDto> dtos);

    PlayerCheckStatsSumBean sumDto2Bean(PlayerCheckStatsSumDto dto);

}
