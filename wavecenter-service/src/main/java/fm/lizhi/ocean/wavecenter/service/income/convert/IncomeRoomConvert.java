package fm.lizhi.ocean.wavecenter.service.income.convert;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.IncomeSummaryBean;
import fm.lizhi.ocean.wavecenter.api.income.request.RequestRoomIncomeStats;
import fm.lizhi.ocean.wavecenter.api.income.bean.IncomeStatBean;
import fm.lizhi.ocean.wavecenter.api.income.response.ResponseRoomIncomeStats;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.RoomIncomeStatParamDTO;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.RoomIncomeStatDTO;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.IncomeSummaryDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 厅收入转换器
 * 
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface IncomeRoomConvert {

    IncomeRoomConvert I = Mappers.getMapper(IncomeRoomConvert.class);

    /**
     * 转换请求参数
     *
     * @param request 请求参数
     * @return 参数DTO
     */
    RoomIncomeStatParamDTO convertToParamDto(RequestRoomIncomeStats request);

    /**
     * 转换响应结果
     *
     * @param pageResult 分页结果
     * @return 响应结果
     */
    @Mapping(target = "total", source = "total")
    @Mapping(target = "list", source = "list")
    ResponseRoomIncomeStats convertToResponse(PageBean<RoomIncomeStatDTO> pageResult);

    /**
     * 转换统计项
     *
     * @param dto 统计DTO
     * @return 响应统计项
     */
    @Mapping(target = "startTime", source = "startTime")
    @Mapping(target = "endTime", source = "endTime")
    @Mapping(target = "info", source = "info")
    IncomeStatBean convertToStatItem(RoomIncomeStatDTO dto);

    /**
     * 转换收入信息
     *
     * @param incomeInfo 收入信息DTO
     * @return 响应收入信息
     */
    IncomeSummaryBean convertToIncomeInfo(IncomeSummaryDTO incomeInfo);

    /**
     * 转换统计项列表
     *
     * @param dtoList 统计DTO列表
     * @return 响应统计项列表
     */
    List<IncomeStatBean> convertToStatItemList(List<RoomIncomeStatDTO> dtoList);
}
