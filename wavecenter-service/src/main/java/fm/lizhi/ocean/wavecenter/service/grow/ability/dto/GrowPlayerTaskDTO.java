package fm.lizhi.ocean.wavecenter.service.grow.ability.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/6 18:18
 */
@Data
@Accessors(chain = true)
public class GrowPlayerTaskDTO {

    private Long id;

    private Long templateId;

    private Long playerId;

    private Date finishTime;

    /**
     * 周期开始时间
     */
    private Date startWeekDate;

    /**
     * 周期结束时间
     */
    private Date endWeekDate;

}
