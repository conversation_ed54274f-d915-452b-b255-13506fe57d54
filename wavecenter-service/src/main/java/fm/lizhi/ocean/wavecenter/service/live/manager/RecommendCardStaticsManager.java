package fm.lizhi.ocean.wavecenter.service.live.manager;

import fm.lizhi.ocean.wavecenter.api.live.response.ResponseRecommendCardStaticsInfo;

import java.util.List;

/**
 * 推荐卡统计数据管理接口
 */
public interface RecommendCardStaticsManager {

    /**
     * 获取推荐卡曝光率统计信息
     * 
     * @param recommendCardRecordIds 推荐卡业务使用记录ID列表
     * @return 推荐卡曝光率统计信息列表
     */
    List<ResponseRecommendCardStaticsInfo> getRecommendCardStatics(int appId, List<Long> recommendCardRecordIds);
} 