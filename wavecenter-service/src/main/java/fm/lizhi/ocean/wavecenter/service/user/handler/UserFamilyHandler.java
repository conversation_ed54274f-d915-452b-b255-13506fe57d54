package fm.lizhi.ocean.wavecenter.service.user.handler;

import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/5/11 18:02
 */
@Component
public class UserFamilyHandler {

    @Autowired
    private FamilyManager familyManager;

    /**
     * 获取用户家族ID，优先获取已签约
     * @param userId
     * @return
     */
    public Long getUserFamilyId(Long userId){
        UserInFamilyBean userInFamily = familyManager.getUserInFamily(userId);
        if (userInFamily.getFamilyId() != null) {
            return userInFamily.getFamilyId();
        }
        if (userInFamily.isFamily()) {
            return userInFamily.getFamilyId();
        }
        if (userInFamily.isRoom()) {
            //查询厅主的签约信息
            Optional<Long> roomBestFamily = familyManager.getRoomBestFamily(userId);
            return roomBestFamily.orElse(null);
        }
        if (userInFamily.isPlayer()) {
            //查询主播的签约信息
            Optional<Long> playerBestFamily = familyManager.getPlayerBestFamily(userId);
            return playerBestFamily.orElse(null);
        }
        LogContext.addResLog("user role not found");
        return null;
    }

}
