package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 同步活动（平台用）请求体
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SyncWaveActivityToApplyRecordDTO {
    /**
     * 申请记录ID
     */
    private long id;

    /**
     * 一级活动ID
     */
    private long primaryActivityId;

    /**
     * 二级活动ID
     */
    private long secondaryActivityId;
    /**
     * 上官频位的房主Ids
     */
    private List<Long> njIds;
    /**
     * 活动说明图
     */
    private String customUrl;
    /**
     * 活动主题
     */
    private String activityTheme;
    /**
     * 活动形式
     */
    private String activityWay;
    /**
     * 活动工具
     */
    private String activityTool;
    /**
     * 活动流程
     */
    private String activityProcess;

    /**
     * 活动-背景图url(也是首页资源位背景图)
     */
    private String officialActivityBackgroundUrl;

    /**
     * 上官频开始时间
     */
    private long startTime;

    /**
     * 上官频结束时间
     */
    private long endTime;

    /**
     * 申请人ID
     */
    private long applicantId;

    /**
     * 家族ID
     */
    private long familyId;

    /**
     * 操作者
     */
    private String operator;
}
