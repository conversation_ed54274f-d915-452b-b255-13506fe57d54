package fm.lizhi.ocean.wavecenter.service.user.config;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/10 15:56
 */
@Data
public class PpBizUserConfig implements BizUserConfig{
    /**
     * 打印缓存统计日志开关
     */
    boolean printCacheStatLogSwitch = false;

    /**
     * 登录白名单，公会id列表
     */
    private List<Long> loginWhitelist = new ArrayList<>();

    /**
     * 登录认证校验白名单
     */
    private List<Long> playerAuthWhitelist = new ArrayList<>();

    /**
     * 二维码登录地址
     */
    private String qrCodeLoginUrl = "https://ppliveh5.yfxn.lizhi.fm/static/producer-platform/scan-login-web-platform/index.html";

    private Long officialRoomGroupId = 5350788901545974399L;

}
