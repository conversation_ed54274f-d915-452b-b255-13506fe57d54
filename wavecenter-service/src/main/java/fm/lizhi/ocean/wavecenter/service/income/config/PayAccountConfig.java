package fm.lizhi.ocean.wavecenter.service.income.config;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 支付账户配置
 * <AUTHOR>
 * @date 2025/4/22 15:34
 */
@Data
public class PayAccountConfig {

    /**
     * 账户类型映射
     * key=accountEngineCode
     * value=accountType room或player
     * <a href="https://lizhi2021.feishu.cn/wiki/THFKwoCH8iy2Qykfw9LclTl4nLg">参考链接</a>
     */
    private Map<String, String> accountTypeMap = new HashMap<>();

}
