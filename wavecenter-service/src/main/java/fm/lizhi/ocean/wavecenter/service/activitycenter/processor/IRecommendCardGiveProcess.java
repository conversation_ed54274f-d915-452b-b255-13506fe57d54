package fm.lizhi.ocean.wavecenter.service.activitycenter.processor;

import fm.lizhi.ocean.wavecenter.base.processor.BusinessEnvAwareProcessor;

public interface IRecommendCardGiveProcess extends BusinessEnvAwareProcessor {

    String getGiveReason(String activityName);


    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return IRecommendCardGiveProcess.class;
    }

}
