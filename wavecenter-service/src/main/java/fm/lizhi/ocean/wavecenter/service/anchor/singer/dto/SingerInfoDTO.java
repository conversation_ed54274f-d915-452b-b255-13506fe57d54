package fm.lizhi.ocean.wavecenter.service.anchor.singer.dto;

import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 *
 * 歌手库
 *
 * <AUTHOR>
 * @date 2025-03-27 03:27:23
 */
@Data
@Accessors(chain = true)
@ToString
public class SingerInfoDTO {
    /**
     * ID
     */
    private Long id;

    /**
     * 业务ID
     */
    private Integer appId;

    /**
     * 歌手ID
     */
    private Long userId;

    /**
     * 厅主ID
     */
    private Long njId;

    /**
     * 家族ID
     */
    private Long familyId;

    /**
     * 审核表 ID
     */
    private Long singerVerifyId;

    /**
     * 歌手状态 1: 认证中 2: 生效中  3: 已淘汰
     */
    private Integer singerStatus;

    /**
     * 歌曲风格
     */
    private String songStyle;

    /**
     * 是否原创歌手
     */
    private Boolean originalSinger;

    /**
     * 是否已发放奖励 0 未发放 1 已发放
     */
    private Boolean rewardsIssued;

    /**
     * 1: 新锐歌手，2：优质歌手，3：明星歌手
     */
    private Integer singerType;

    /**
     * 淘汰时间
     */
    private Long eliminationTime;

    /**
     * 通过时间
     */
    private Long auditTime;

    /**
     * 淘汰原因
     */
    private String eliminationReason;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 修改时间
     */
    private Long modifyTime;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 联系方式
     */
    private String contactNumber;


}