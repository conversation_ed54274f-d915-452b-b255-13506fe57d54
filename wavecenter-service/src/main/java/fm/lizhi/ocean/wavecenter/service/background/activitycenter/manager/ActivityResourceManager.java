package fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestPageActivityResources;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityResource;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityResource;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseActivityResource;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityResourceSimpleInfoDTO;

import java.util.List;

/**
 * 活动资源
 *
 * <AUTHOR>
 */
public interface ActivityResourceManager {

    /**
     * 保存活动资源
     */
    Result<Void> saveActivityResource(RequestSaveActivityResource param);


    /**
     * 修改活动资源
     */
    Result<Void> updateActivityResource(RequestUpdateActivityResource param);


    /**
     * 删除活动资源
     */
    Result<Void> deleteActivityResource(Long id, Integer appId, String operator);


    /**
     * 分页查询活动资源
     */
    Result<PageBean<ResponseActivityResource>> listActivityResource(RequestPageActivityResources param);

    /**
     * 批量查询活动资源配置
     *
     * @param ids 资源id列表
     * @return 结果列表
     */
    List<ActivityResourceSimpleInfoDTO> batchResourceByIds(List<Long> ids);

    /**
     * 根据ID和资源状态批量查询活动资源配置
     *
     * @param ids    资源id列表
     * @param status 状态
     * @return 结果列表
     */
    List<ActivityResourceSimpleInfoDTO> batchValidateResourceByIdsAndStatus(List<Long> ids, int status);

    /**
     * 根据等级获取活动资源列表
     */
    Result<List<ResponseActivityResource>> listActivityResourceByLevelId(Long levelId, int appId);

    /**
     * 根据资源ID获取活动资源
     */
    List<ResponseActivityResource> getActivityResourcesByIds(List<Long> ids);

    /**
     * 检查流量资源等级是否存在重叠
     */
    Result<Void> checkResourceLevelRepeat(List<Long> levelIds, String targetResouceCode);

}
