package fm.lizhi.ocean.wavecenter.service.live.impl;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.live.request.*;
import fm.lizhi.ocean.wavecenter.api.live.response.*;
import fm.lizhi.ocean.wavecenter.api.live.service.WaveCheckInDataService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.live.manager.WaveCheckInDataManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@ServiceProvider
@Slf4j
public class WaveCheckInDataServiceImpl implements WaveCheckInDataService {

    @Autowired
    private WaveCheckInDataManager waveCheckInDataManager;

    @Override
    public Result<ResponseGetCheckInRoomSum> getCheckInRoomSum(RequestGetCheckInRoomSum req) {
        LogContext.addReqLog("`req={}", JsonUtil.dumps(req));
        return ResultHandler.handle(req.getAppId(), () -> {
            ResponseGetCheckInRoomSum resp = waveCheckInDataManager.getCheckInRoomSum(req);
            LogContext.addResLog("`resp={}", JsonUtil.dumps(resp));
            return RpcResult.success(resp);
        });
    }

    @Override
    public Result<ResponseGetCheckInRoomStatistic> getCheckInRoomStatistic(RequestGetCheckInRoomStatistic req) {
        LogContext.addReqLog("`req={}", JsonUtil.dumps(req));
        return ResultHandler.handle(req.getAppId(), () -> {
            ResponseGetCheckInRoomStatistic resp = waveCheckInDataManager.getCheckInRoomStatistic(req);
            if (log.isDebugEnabled()) {
                log.debug("getCheckInRoomStatistic resp:{}", JsonUtil.dumps(resp));
            }
            return RpcResult.success(resp);
        });
    }

    @Override
    public Result<ResponseGetCheckInPlayerSum> getCheckInPlayerSum(RequestGetCheckInPlayerSum req) {
        LogContext.addReqLog("`req={}", JsonUtil.dumps(req));
        return ResultHandler.handle(req.getAppId(), () -> {
            ResponseGetCheckInPlayerSum resp = waveCheckInDataManager.getCheckInPlayerSum(req);
            LogContext.addResLog("`resp={}", JsonUtil.dumps(resp));
            return RpcResult.success(resp);
        });
    }

    @Override
    public Result<ResponseGetCheckInPlayerStatistic> getCheckInPlayerStatistic(RequestGetCheckInPlayerStatistic req) {
        LogContext.addReqLog("`req={}", JsonUtil.dumps(req));
        return ResultHandler.handle(req.getAppId(), () -> {
            ResponseGetCheckInPlayerStatistic resp = waveCheckInDataManager.getCheckInPlayerStatistic(req);
            if (log.isDebugEnabled()) {
                log.debug("getCheckInPlayerStatistic resp:{}", JsonUtil.dumps(resp));
            }
            return RpcResult.success(resp);
        });
    }

    @Override
    public Result<ResponseGetCheckInRoomStatisticReport> getCheckInRoomStatisticReport(RequestGetCheckInRoomStatisticReport req) {
        LogContext.addReqLog("`req={}", JsonUtil.dumps(req));
        return ResultHandler.handle(req.getAppId(), () -> {
            ResponseGetCheckInRoomStatisticReport resp = waveCheckInDataManager.getCheckInRoomStatisticReport(req);
            if (log.isDebugEnabled()) {
                log.debug("getCheckInRoomStatisticReport resp:{}", JsonUtil.dumps(resp));
            }
            return RpcResult.success(resp);
        });
    }
}
