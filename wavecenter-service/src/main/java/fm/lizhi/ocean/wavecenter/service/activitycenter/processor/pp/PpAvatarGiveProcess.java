package fm.lizhi.ocean.wavecenter.service.activitycenter.processor.pp;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.ActivityConfig;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityResourceGiveDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.DressUpGiveContext;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.SendDecorateParamDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.processor.IAvatarGiveProcess;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PpAvatarGiveProcess implements IAvatarGiveProcess {

    @Autowired
    private ActivityConfig activityConfig;

    @Override
    public boolean isAutoGive() {
        return activityConfig.getPp().isAutoSendAvatar();
    }

    @Override
    public void fillSendParam(DressUpGiveContext context, SendDecorateParamDTO param) {
        ActivityResourceGiveDTO resourceGiveDTO = context.getResourceGiveDTO();
        param.setCount(1);
        param.setCoverValid(false);
        param.setBeginTime(resourceGiveDTO.getStartTime().getTime());
        param.setEndTime(resourceGiveDTO.getEndTime().getTime());
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.PP;
    }
}
