package fm.lizhi.ocean.wavecenter.service.income.process;

import fm.lizhi.ocean.wavecenter.base.processor.BusinessEnvAwareProcessor;

public interface PersonalProcess extends BusinessEnvAwareProcessor {

    /**
     * 是否存在个播收入
     *
     * @return
     */
    boolean enablePlayerIncome();


    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return PersonalProcess.class;
    }
}
