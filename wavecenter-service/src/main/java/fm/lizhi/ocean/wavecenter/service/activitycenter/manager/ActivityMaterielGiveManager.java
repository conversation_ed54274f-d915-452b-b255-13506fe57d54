package fm.lizhi.ocean.wavecenter.service.activitycenter.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityFlowResourceAuditBean;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.*;

import java.util.List;

/**
 * 活动物资发放管理，包含流量资源、装扮和房间公告
 */
public interface ActivityMaterielGiveManager {

    /**
     * 初始化活动物资（流量资源、装扮、房间公告）-可重入
     *
     * @param activityId 活动ID
     * @return 成功装填吗
     */
    Result<Void> initActivityMateriel(long activityId, List<ActivityFlowResourceAuditBean> resourceAuditList);


    /**
     * 查询出等待发放资源的记录
     *
     * @param appId        应用ID
     * @param minStartTime 最小开始时间
     * @param maxTryCount  最大重试次数
     * @return 结果列表
     */
    List<ActivityResourceGiveDTO> getWaitGiveResourceList(int appId, long minStartTime, int maxTryCount);

    /**
     * 查询出等待发放资源的记录
     *
     * @param appId      应用ID
     * @param activityId 活动ID
     * @param statusList 状态列表
     * @return 结果列表
     */
    List<ActivityResourceGiveDTO> getGiveResourceListByActivityId(int appId, long activityId, List<Integer> statusList);

    /**
     * 查询等待发放的装扮记录明细
     *
     * @param giveId 发放ID
     * @return 结果
     */
    List<ActivityDressUpGiveDTO> getWaitGiveDressUpList(long giveId);

    /**
     * 查询等待发放的流量资源记录明细
     *
     * @param giveId 发放ID
     * @return 结果
     */
    List<ActivityFlowResourceGiveDTO> getWaitGiveFlowResouceList(long giveId);

    /**
     * 修改资源发放记录
     *
     * @param id             主键ID
     * @param originalStatus 原始状态
     * @param targetStatus   更改的状态
     * @return 结果
     */
    boolean updateDressUpRecordStatus(long id, int originalStatus, int targetStatus);

    /**
     * 修改资源主表状态
     *
     * @param param 参数
     * @return 结果
     */
    boolean updateResourceGiveRecord(UpdateResourceGiveParamDTO param);

    /**
     * 查询公告配置列表
     *
     * @param time           时间
     * @param statusList     状态列表
     * @param activityStatus 活动状态，1: 活动未开始，2：活动已结束
     * @return 结果列表
     */
    List<ActivityRoomAnnouncementDeployDTO> getAnnouncementDeployList(Long time, List<Integer> statusList, int activityStatus);

    boolean updateAnnouncementDeployStatus(long id, int originalStatus, int targetStatus);

    /**
     * 保存原公告
     *
     * @param param 参数
     * @return 结果
     */
    boolean saveOriginalAnnouncement(SaveOriginalAnnouncementParamDTO param);

    /**
     * 查询资源发放记录
     *
     * @param appId
     * @param resourceId
     * @return
     */
    ActivityResourceGiveDTO getGiveResource(int appId, Long resourceId);


    int INIT_ACTIVITY_MATERIEL_FAIL = 1;
}
