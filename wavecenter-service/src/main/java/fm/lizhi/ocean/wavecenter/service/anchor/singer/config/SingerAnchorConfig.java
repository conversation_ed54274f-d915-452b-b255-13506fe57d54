package fm.lizhi.ocean.wavecenter.service.anchor.singer.config;

import com.google.common.collect.Lists;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.config.AbsBizConfig;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ConfigurationProperties(prefix = "wavecenter-anchor.singer")
public class SingerAnchorConfig extends AbsBizConfig<CommonSingerConfig> {

    /**
     * 歌手总数缓存过期时间（秒）
     */
    private Integer singerTotalCountInHallExpireSeconds = 60;

    /**
     * 歌手是否是歌手缓存过期时间（秒）
     */
    private Integer singerIsSingerExpireSeconds = 300;

    /**
     * 回滚歌手认证状态最小时间
     * 默认七天
     */
    private Integer rollbackVerifyStatusMinTime = 7 * 24 * 60;

    /**
     * 回滚歌手认证状态最大时间
     * 默认七天+10min
     */
    private Integer rollbackVerifyStatusMaxTime = 7 * 24 * 60 + 10;

    /**
     * 淘汰解约X分钟后的歌手，单位分钟
     * 线上是24h, 测试环境设置分钟级别
     */
    private Integer eliminateSingerAfterMinutes = 24 * 60;

    /**
     * 关闭点唱厅状态校验的应用
     */
    private List<Integer> disableHallValidateApp = Lists.newArrayList(BusinessEvnEnum.PP.getAppId(), BusinessEvnEnum.XIMI.getAppId());



    private PpSingerConfig pp;

    private XmSingerConfig xm;

    private HySingerConfig hy;

    public SingerAnchorConfig() {
        PpSingerConfig ppConfig = new PpSingerConfig();
        XmSingerConfig xmConfig = new XmSingerConfig();
        HySingerConfig hyConfig = new HySingerConfig();
        this.pp = ppConfig;
        this.xm = xmConfig;
        this.hy = hyConfig;
        bizConfigMap.put(BusinessEvnEnum.PP.appId(), ppConfig);
        bizConfigMap.put(BusinessEvnEnum.XIMI.appId(), xmConfig);
        bizConfigMap.put(BusinessEvnEnum.HEI_YE.appId(), hyConfig);
    }

}
