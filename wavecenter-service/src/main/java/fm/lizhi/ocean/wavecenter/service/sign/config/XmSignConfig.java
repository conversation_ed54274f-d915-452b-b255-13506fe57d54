package fm.lizhi.ocean.wavecenter.service.sign.config;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/10/19 10:21
 */
@Data
public class XmSignConfig implements BizSignConfig{

    /**
     * 是否限制实名多账号只能加入一个公会
     */
    private boolean limitVerifyJoinFamilySwitch = true;

    /**
     * 是否校验同一实名下只能加入一个家族
     */
    private boolean verifyUnionFamily = true;

    /**
     * 签约功能开关，关闭后，系统将不提供签约功能
     */
    private boolean signFunctionSwitch = true;

}
