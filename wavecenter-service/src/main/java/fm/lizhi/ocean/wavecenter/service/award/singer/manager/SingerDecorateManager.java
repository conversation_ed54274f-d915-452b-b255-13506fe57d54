package fm.lizhi.ocean.wavecenter.service.award.singer.manager;

import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowGenerateDTO;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowInitParamDTO;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowSendParamDTO;

import java.util.Optional;

/**
 * 歌手装扮管理器
 * <AUTHOR>
 */
public interface SingerDecorateManager {

    /**
     * 生成歌手装扮流水信息
     * <p>
     * 提供给调用方写入，保证事务, 写入成功后，请调用一下 SingerDecorateManager#operateSingerDecorateAsync 进行操作装扮
     */
    Optional<SingerDecorateFlowGenerateDTO> generateSingerDecorateFlowList(SingerDecorateFlowInitParamDTO param);

    /**
     * 操作歌手装扮
     * <p>
     * 通过 多线程 异步发送
     *
     * @param param 发放参数
     */
    void operateSingerDecorateAsync(SingerDecorateFlowSendParamDTO param);

    /**
     * 操作歌手装扮
     * <p>
     * 同步发送
     * @param param 发放参数
     */
    void operateSingerDecorate(SingerDecorateFlowSendParamDTO param);

    /**
     * 初始化歌手装扮流水并且发送装扮发放事件，异步发送/回收装扮;
     * 此方法组合了 generateAndInsertSingerDecorateFlow 以及 operateSingerDecorateAsync 方法
     */
    Optional<SingerDecorateFlowGenerateDTO> generateAndInsertSingerDecorateFlowAndSendEvent(SingerDecorateFlowInitParamDTO param);

}
