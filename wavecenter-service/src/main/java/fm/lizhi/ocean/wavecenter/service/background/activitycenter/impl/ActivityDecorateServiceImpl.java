package fm.lizhi.ocean.wavecenter.service.background.activitycenter.impl;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.DecorateBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestBatchGetDecorate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestGetDecorate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityDecorateService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.DecorateInfoBean;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.convert.ActivityDecorateConvert;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityDecorateManager;
import fm.lizhi.ocean.wavecenter.service.resource.decorate.manager.DecorateManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 */
@ServiceProvider
@Slf4j
public class ActivityDecorateServiceImpl implements ActivityDecorateService {

    @Autowired
    private ActivityDecorateManager activityDecorateManager;

    @Override
    public Result<PageBean<DecorateBean>> getDecorateList(RequestGetDecorate param) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(param));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(param));

        fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.request.RequestGetDecorate request =
                ActivityDecorateConvert.I.convertRequestGetDecorate(param);
        return ResultHandler.handle(param.getAppId(), () -> activityDecorateManager.getDecorateList(request));
    }

    @Override
    public Result<List<DecorateBean>> batchGetDecorateList(RequestBatchGetDecorate param) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(param));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(param));

        return ResultHandler.handle(param.getAppId(), () -> activityDecorateManager.batchGetDecorateList(param));

    }
}
