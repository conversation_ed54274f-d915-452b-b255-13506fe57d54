package fm.lizhi.ocean.wavecenter.service.datacenter.dto;

import fm.lizhi.ocean.wavecenter.common.utils.WcAssert;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import fm.lizhi.ocean.wavecenter.api.common.constants.MetricsEnum;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/4/18 18:23
 */
@Getter
@Builder
public class GetRoomPlayerPerformanceParamDto {

    private Integer appId;
    private Long roomId;
    private Long familyId;
    private Integer pageNo;
    private Integer pageSize;

    /**
     * 排序指标
     */
    private MetricsEnum orderMetrics;
    /**
     * desc,asc
     */
    private OrderType orderType;

    /**
     * 时间范围
     */
    private AssessTimeDto assessTime;

    public static class GetRoomPlayerPerformanceParamDtoBuilder{

        public GetRoomPlayerPerformanceParamDtoBuilder orderMetricsStr(String orderMetrics){
            this.orderMetrics = MetricsEnum.fromValue(orderMetrics);
            return this;
        }

        public GetRoomPlayerPerformanceParamDtoBuilder orderTypeStr(String orderType){
            this.orderType = OrderType.valueOf(orderType);
            return this;
        }

        public GetRoomPlayerPerformanceParamDto build(){
            WcAssert.notNull(appId, "appId is null");
            WcAssert.notNull(roomId, "roomId is null");
            WcAssert.notNull(familyId, "familyId is null");
            WcAssert.notNull(assessTime, "assessTime is null");

            if (pageNo == null || pageNo < 0) {
                pageNo = 1;
            }

            if (pageSize == null || pageSize < 0) {
                pageSize = 20;
            }

            if (orderMetrics == null) {
                orderMetrics = MetricsEnum.CHARM;
            }
            if (orderType == null) {
                orderType = OrderType.DESC;
            }

            return new GetRoomPlayerPerformanceParamDto(appId
                    , roomId
                    , familyId
                    , pageNo
                    , pageSize
                    , orderMetrics
                    , orderType
                    , assessTime);
        }
    }


}
