package fm.lizhi.ocean.wavecenter.service.message.dto;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class WcNoticeConfigQueryParamDTO {
    private Integer type;
    /**
     * 上次查询的最大生效时间
     */
    private Long lastMaxEffectTime;

    /**
     * 每页大小
     */
    @NotNull(message = "每页大小不能为空")
    @Min(10)
    private Integer pageSize;

    private Long userId;

    private Integer appId;
} 