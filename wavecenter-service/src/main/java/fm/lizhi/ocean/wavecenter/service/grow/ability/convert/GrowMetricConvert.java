package fm.lizhi.ocean.wavecenter.service.grow.ability.convert;

import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.PlayerSupportMetric;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.PlayerDataForGrowDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2025/6/9 15:59
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface GrowMetricConvert {

    GrowMetricConvert I = Mappers.getMapper(GrowMetricConvert.class);

    PlayerSupportMetric convert(PlayerDataForGrowDTO dto);

}
