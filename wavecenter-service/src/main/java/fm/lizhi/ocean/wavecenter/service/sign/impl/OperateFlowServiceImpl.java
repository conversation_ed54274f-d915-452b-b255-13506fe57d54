package fm.lizhi.ocean.wavecenter.service.sign.impl;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignFlowStatusEnum;
import fm.lizhi.ocean.wavecenter.api.sign.service.OperateFlowService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.sign.manager.SignFlowManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2024/10/10 15:50
 */
@Slf4j
@ServiceProvider
public class OperateFlowServiceImpl implements OperateFlowService {

    @Autowired
    private SignFlowManager signFlowManager;

    @Override
    public Result<Void> changeStatus(int appId, long flowId, SignFlowStatusEnum status) {
        LogContext.addReqLog("appId={},flowId={},status={}", appId, flowId, status.getCode());
        LogContext.addResLog("appId={},flowId={},status={}", appId, flowId, status.getCode());
        return ResultHandler.handle(appId, () -> {
            signFlowManager.changeStatus(flowId, status.getCode());
            return RpcResult.success();
        });
    }
}
