package fm.lizhi.ocean.wavecenter.service.sign.process.hy;

import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.bean.FamilyAndNjContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.FlowConfirmStatusEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignAuthStatusEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import fm.lizhi.ocean.wavecenter.api.sign.request.*;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseFamilyApplyCancelAdmin;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseFamilyDoSign;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseFamilyInviteAdmin;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseFamilyReviewCancel;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.service.sign.dto.*;
import fm.lizhi.ocean.wavecenter.service.sign.manager.ContractManager;
import fm.lizhi.ocean.wavecenter.service.sign.process.FamilySignProcessor;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/10/14 15:31
 */
@Component
public class HyFamilySignProcessor extends HySignAbstractProcessor implements FamilySignProcessor {

    @Autowired
    private ContractManager contractManager;

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.HEI_YE;
    }

    @Override
    public ResponseFamilyReviewCancel reviewCancelCheck(RequestFamilyReviewCancel request) {
        boolean inChangeCompany = nonContractManager.isInChangeCompany(request.getCurUserId());
        if (inChangeCompany) {
            return new ResponseFamilyReviewCancel().setCode(-1).setMsg("系统升级，暂不支持签约~");
        }
        return new ResponseFamilyReviewCancel().setCode(0);
    }

    @Override
    public ResponseFamilyDoSign doSignCheck(RequestFamilyDoSign request) {
        ResponseFamilyDoSign res = new ResponseFamilyDoSign();

        Optional<FamilyBean> familyOp = familyManager.getFamily(getBusinessEnv().getAppId(), request.getFamilyId());
        if (!familyOp.isPresent()) {
            LogContext.addResLog("family not exist");
            return res.setCode(-1);
        }

        FamilyBean familyBean = familyOp.get();
        Long familyUserId = familyBean.getUserId();

        PageBean<FamilyAndNjContractBean> pageBean = contractManager.queryContract(RequestFamilyAndNjContractDTO.builder()
                .contractId(request.getContractId()).pageSize(1)
                .build());
        if (CollectionUtils.isEmpty(pageBean.getList())) {
            LogContext.addResLog("contract not exist");
            return res.setCode(-1);
        }
        Long njId = pageBean.getList().get(0).getNjUserId();

        //查询判断当前用户是否存在冻结期
        Pair<Integer, String> limitCheckRes = nonContractManager.checkInviteSignLimit(familyUserId, njId, RoleEnum.FAMILY, RoleEnum.ROOM);
        if (limitCheckRes.getKey() != 0) {
            LogContext.addResLog("limitCheckCode={}", limitCheckRes.getKey());
            return res.setCode(limitCheckRes.getKey()).setMsg(limitCheckRes.getValue());
        }

        UserInFamilyBean userInFamily = familyManager.getUserInFamily(njId);
        if (userInFamily.isRoom() || userInFamily.isPlayer()) {
            return res.setCode(-1).setMsg("该用户已有签约身份，无法操作哦~");
        }

        Pair<Integer, String> unionFamily = super.checkVerifyNjUnionFamily(njId, familyUserId);
        if (unionFamily.getKey() != 0) {
            LogContext.addResLog("unionFamilyCode={}", unionFamily.getKey());
            return res.setCode(unionFamily.getKey()).setMsg(unionFamily.getValue());
        }

        return res;
    }

    @Override
    public void doSignSuccessProcessor(RequestFamilyDoSign request) {
//        SignStatusSyncDTO dto = new SignStatusSyncDTO();
//        dto.setType(ContractTypeEnum.SIGN.getCode());
//        dto.setAppId(request.getAppId());
//        dto.setContractId(request.getContractId());
//        dto.setCreateRole(RoleEnum.FAMILY.getRoleCode());
//        dto.setConfirmStatus(FlowConfirmStatusEnum.WAIT_TARGET_SIGN.getCode());
//        signFlowManager.addSignStatusSync(dto);
    }

    @Override
    public ResponseFamilyInviteAdmin inviteAdminCheck(RequestFamilyInviteAdmin request) {
        ResponseFamilyInviteAdmin res = new ResponseFamilyInviteAdmin();

        boolean familyVerifyPass = familyManager.isFamilyVerifyPass(request.getFamilyId());
        if (!familyVerifyPass) {
            return res.setCode(-1).setMsg("家族未完成企业认证");
        }

        long familyUserId = request.getCurUserId();
        Long njId = request.getTargetUserId();

        Pair<Integer, String> limitCheck = nonContractManager.checkInviteSignLimit(familyUserId, njId, RoleEnum.FAMILY, RoleEnum.ROOM);
        if (limitCheck.getKey() != 0) {
            LogContext.addResLog("limitCheck fail. code={}", limitCheck.getKey());
            return res.setCode(limitCheck.getKey()).setMsg(limitCheck.getValue());
        }

        UserInFamilyBean userInFamily = familyManager.getUserInFamily(njId);
        if (userInFamily.isPlayer() || userInFamily.isRoom()) {
            LogContext.addResLog("user is join");
            return res.setCode(-1).setMsg("该用户已有签约身份，无法操作哦~");
        }

        Pair<Integer, String> unionFamily = super.checkVerifyNjUnionFamily(njId, familyUserId);
        if (unionFamily.getKey() != 0) {
            LogContext.addResLog("unionFamily fail. code={}", unionFamily.getKey());
            return res.setCode(unionFamily.getKey()).setMsg(unionFamily.getValue());
        }

        Optional<SignPersonalInfoDTO> signPersonalInfoOp = contractManager.getSignPersonalInfo(njId);
        if ((!signPersonalInfoOp.isPresent())
                || (SignAuthStatusEnum.AUTO_AUTH_PASS != signPersonalInfoOp.get().getAuthStatus())) {
            return res.setCode(-1).setMsg("对方暂时未进行上上签个人认证，暂时无法进行签约");
        }

        String identityNo = signPersonalInfoOp.get().getIdentityNo();
        //检查该身份证是否签约了其他家族
        List<FamilyAndNjContractBean> joinList = contractManager.queryIdentityNoJoinFamily(identityNo);
        if (CollectionUtils.isNotEmpty(joinList)) {
            for (FamilyAndNjContractBean contract : joinList) {
                if (super.isUserBand(contract.getNjUserId())) {
                    continue;
                }
                if (!request.getFamilyId().equals(contract.getFamilyId())) {
                    return res.setCode(-1).setMsg("实名身份证签约了其他家族,不能再次签约");
                }
            }
        }

        return res;
    }

    @Override
    public void inviteAdminSuccessProcessor(RequestFamilyInviteAdmin request, Long contractId) {
        //标记待同步签署状态
        SignStatusSyncDTO dto = new SignStatusSyncDTO();
        dto.setType(ContractTypeEnum.SIGN.getCode());
        dto.setAppId(request.getAppId());
        dto.setContractId(contractId);
        dto.setCreateRole(RoleEnum.FAMILY.getRoleCode());
        dto.setConfirmStatus(FlowConfirmStatusEnum.WAIT_CREATE_SIGN.getCode());
        signFlowManager.addSignStatusSync(dto);
    }

    @Override
    public Optional<FamilyAndNjContractBean> existWaitSignContract(RequestFamilyInviteAdmin request) {
        PageBean<FamilyAndNjContractBean> pageBean = contractManager.queryContract(RequestFamilyAndNjContractDTO.builder()
                .pageSize(1)
                .pageNo(1)
                .familyId(request.getFamilyId())
                .njId(request.getTargetUserId())
                .type(ContractTypeEnum.SIGN)
                .type(ContractTypeEnum.RENEW)
                .type(ContractTypeEnum.SUBJECT_CHANGE)
                .relation(SignRelationEnum.SIGN_SUCCESS)
                .relation(SignRelationEnum.WAIT_SIGN)
                .relation(SignRelationEnum.SIGNING)
                .build());
        if (CollectionUtils.isEmpty(pageBean.getList())) {
            return Optional.empty();
        }

        return Optional.ofNullable(pageBean.getList().get(0));
    }

    @Override
    public ResponseFamilyApplyCancelAdmin applyCancelAdmin(RequestFamilyApplyCancelAdmin request) {
        ResponseFamilyApplyCancelAdmin res = new ResponseFamilyApplyCancelAdmin();

        Long curUserId = request.getCurUserId();
        boolean inChangeCompanyPreparedStage = nonContractManager.isInChangeCompanyPreparedStage(curUserId);
        boolean inChangeCompany = nonContractManager.isInChangeCompany(curUserId);
        if (inChangeCompanyPreparedStage || inChangeCompany) {
            LogContext.addResLog("changeCompany stage");
            return res.setCode(-1).setMsg("系统升级，暂不支持解约~");
        }

        Optional<FamilyBean> familyOp = familyManager.getFamily(getBusinessEnv().getAppId(), request.getFamilyId());
        if (!familyOp.isPresent()) {
            LogContext.addResLog("family not exist");
            return res.setCode(-1);
        }

        FamilyBean familyBean = familyOp.get();
        if (!isPGC(familyBean)) {
            LogContext.addResLog("is not pgc");
            return res.setCode(-1).setMsg("非PGC家族，暂不支持解约~");
        }

        PageBean<FamilyAndNjContractBean> pageList = contractManager.queryContract(RequestFamilyAndNjContractDTO.builder()
                .pageSize(1)
                .contractId(request.getContractId())
                .build());
        if (CollectionUtils.isEmpty(pageList.getList())) {
            LogContext.addResLog("contract is not exist");
            return res.setCode(-1).setMsg("合同不存在");
        }
        FamilyAndNjContractBean contract = pageList.getList().get(0);

        if ((!contract.getFamilyId().equals(request.getFamilyId())) || (!contract.getNjUserId().equals(request.getTargetUserId()))) {
            LogContext.addResLog("contract not match");
            return res.setCode(-1).setMsg("合同信息不匹配");
        }

        boolean existChange = contractManager.existEffectChangeObjByNjId(contract.getNjUserId());
        if (existChange) {
            return res.setCode(-1).setMsg("该房间管理员正在主体变更，暂时无法解约");
        }

        //是否存在待签署的解约记录
        PageBean<FamilyNjSignRecordDTO> signRecord = contractManager.querySignRecord(QueryFamilyNjSignRecordDTO.builder()
                .type(ContractTypeEnum.CANCEL)
                .status(SignRelationEnum.WAIT_SIGN)
                .status(SignRelationEnum.SIGNING)
                .njId(contract.getNjUserId())
                .familyUserId(familyBean.getUserId())
                .build());
        if (CollectionUtils.isNotEmpty(signRecord.getList())) {
            LogContext.addResLog("signRecord is not empty");
            return res.setCode(-1).setMsg("已存在解约申请");
        }

        return res;
    }

    @Override
    public void applyCancelAdminSuccessProcessor(RequestFamilyApplyCancelAdmin request, Long contractId) {
        SignStatusSyncDTO dto = new SignStatusSyncDTO();
        dto.setType(ContractTypeEnum.CANCEL.getCode());
        dto.setAppId(request.getAppId());
        dto.setContractId(contractId);
        dto.setCreateRole(RoleEnum.FAMILY.getRoleCode());
        dto.setConfirmStatus(FlowConfirmStatusEnum.WAIT_CREATE_SIGN.getCode());
        signFlowManager.addSignStatusSync(dto);
    }

    @Override
    public void doCancelAdminSuccessProcessor(RequestFamilyDoCancelAdmin request) {
        SignStatusSyncDTO dto = new SignStatusSyncDTO();
        dto.setType(ContractTypeEnum.CANCEL.getCode());
        dto.setAppId(request.getAppId());
        dto.setContractId(request.getContractId());
        dto.setCreateRole(RoleEnum.FAMILY.getRoleCode());
        dto.setConfirmStatus(FlowConfirmStatusEnum.WAIT_TARGET_SIGN.getCode());
        signFlowManager.addSignStatusSync(dto);
    }
}
