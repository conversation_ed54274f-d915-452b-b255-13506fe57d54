package fm.lizhi.ocean.wavecenter.service.sign.manager;

import fm.lizhi.ocean.wavecenter.api.sign.bean.FamilyAndNjContractBean;
import fm.lizhi.ocean.wavecenter.service.sign.dto.SignSettleDTO;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/10/17 18:25
 */
public interface SettleManager {

    /**
     * 查询结算信息
     * key=合同ID
     * @param contractList
     * @return
     */
    Map<Long, SignSettleDTO> querySettle(List<FamilyAndNjContractBean> contractList);


    /**
     * 查询厅对应的结算信息
     * @param njId
     * @return
     */
    Optional<SignSettleDTO> querySettleByNj(Long njId);

}
