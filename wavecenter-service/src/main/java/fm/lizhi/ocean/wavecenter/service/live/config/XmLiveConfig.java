package fm.lizhi.ocean.wavecenter.service.live.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @date 2024/10/27 10:19
 */
@Data
public class XmLiveConfig implements CommonLiveConfig {

    /**
     * 点唱厅用户组id
     */
    private Long signRoomGroupId = 5330004444568355967L;

    /**
     * 打卡小秘书
     */
    private long checkInReportSender = 0L;
    /**
     * 打卡报告页URL
     */
    private String checkInReportUrl = "https://wavecenter-public.yfxn.lizhi.fm/static/ximi/index.html#/common/report/checkIn?dateType=$dateType&roomId=$roomId&startDate=$startDate&endDate=$endDate&appId=$appId&signCode=$signCode";

    private String checkInDetailUrl = "https://wavecenter-public.yfxn.lizhi.fm/static/ximi/index.html#/app/home";
}
