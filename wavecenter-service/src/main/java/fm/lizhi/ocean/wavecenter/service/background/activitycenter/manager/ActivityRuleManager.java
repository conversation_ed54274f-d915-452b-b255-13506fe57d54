package fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyRuleEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityRuleBaseAbstractBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityRuleConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityRule;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityRule;

import java.util.List;

/**
 * 活动提报规则
 * <AUTHOR>
 */
public interface ActivityRuleManager {

    /**
     * 保存活动提报规则
     */
    Result<Void> saveActivityRule(RequestSaveActivityRule param);


    /**
     * 修改活动提报规则
     */
    Result<Void> updateActivityRule(RequestUpdateActivityRule param);


    /**
     * 删除活动提报规则
     */
    Result<Void> deleteActivityRule(Long id, String operator);


    /**
     * 分页查询活动提报规则
     */
    Result<List<ActivityRuleConfigBean>> listActivityRule(int appId);

    /**
     * 获取提报规则
     * @param appId
     * @param ruleEnum
     * @return
     */
    ActivityRuleConfigBean getActivityRuleByRuleTypeAndAppId(int appId, ActivityApplyRuleEnum ruleEnum);

    /**
     * 获取规则Bean
     * 返回值可以直接设置为{@link ActivityApplyRuleEnum#getClazz()} 具体的子类,
     * 但是调用方必须自己保证接收的子类正确
     */
    <T extends ActivityRuleBaseAbstractBean> T getRuleBean(ActivityApplyRuleEnum ruleEnum, String jsonString);
}
