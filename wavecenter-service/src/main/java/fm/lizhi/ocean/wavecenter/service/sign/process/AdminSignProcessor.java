package fm.lizhi.ocean.wavecenter.service.sign.process;

import fm.lizhi.ocean.wavecenter.api.sign.request.RequestAdminApplyCancelFamily;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestAdminDoCancelFamily;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestAdminInviteUser;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestAdminOperateSign;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseAdminApplyCancelFamily;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseAdminInviteUser;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseAdminOperateSign;
import fm.lizhi.ocean.wavecenter.base.processor.BusinessEnvAwareProcessor;

/**
 * <AUTHOR>
 * @date 2024/10/11 20:42
 */
public interface AdminSignProcessor extends BusinessEnvAwareProcessor {

    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return AdminSignProcessor.class;
    }

    /**
     * 签约检查
     * @param request
     * @return
     */
    default ResponseAdminOperateSign operateSignCheck(RequestAdminOperateSign request) {
        return new ResponseAdminOperateSign().setCode(0);
    }

    /**
     * 邀请用户检查
     * @param request
     * @return
     */
    ResponseAdminInviteUser inviteUserCheck(RequestAdminInviteUser request);

    /**
     * 申请解约家族检查
     * @param request
     * @return
     */
    ResponseAdminApplyCancelFamily applyCancelFamilyCheck(RequestAdminApplyCancelFamily request);

    default void applyCancelFamilySuccessProcessor(RequestAdminApplyCancelFamily request, Long contractId){

    }

    /**
     * 申请解约家族
     * @param request
     * @return
     */
    ResponseAdminApplyCancelFamily doApplyCancelFamily(RequestAdminApplyCancelFamily request);

    default void doCancelFamilySuccessProcessor(RequestAdminDoCancelFamily request){

    }

}
