package fm.lizhi.ocean.wavecenter.service.permissions.manager;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageParamBean;
import fm.lizhi.ocean.wavecenter.api.permissions.bean.AddRoleAuthRefBean;
import fm.lizhi.ocean.wavecenter.api.permissions.bean.RoleAuthRefBean;
import fm.lizhi.ocean.wavecenter.api.permissions.bean.RoleInfoAuthRefBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.service.permissions.dto.RoleAuthRefDto;
import fm.lizhi.ocean.wavecenter.service.permissions.dto.RoleDto;

import javax.annotation.Nonnull;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/3/27 15:25
 */
public interface RoleManager {

    /**
     * 获取用户的角色
     * @param userId
     * @return
     */
    @Nonnull
    String getUserRoleCode(long userId);

    /**
     * 获取系统所有角色
     * @return
     */
    List<RoleDto> getAllRoles();

    /**
     * 通过角色code获取角色
     * @param roleCodes
     * @return
     */
    List<RoleDto> getRolesByCodes(List<String> roleCodes);

    /**
     * 查询所有角色授权配置
     * @param pageParamBean
     * @return
     */
    PageBean<RoleAuthRefBean> getAllAuthConfig(int appId, long createUserId, Long userId, String roleCode, PageParamBean pageParamBean);

    /**
     * 通过ID获取配置
     * @param authConfigId
     * @return
     */
    Optional<RoleAuthRefDto> getAuthConfig(long authConfigId);

    /**
     * 新增授权配置
     * @param appId
     * @param addRoleAuthRefBean
     */
    void addAuthConfig(int appId, AddRoleAuthRefBean addRoleAuthRefBean, long subjectId);

    /**
     * 判断授权配置是否存在
     * @param appId
     * @param userId
     * @param roleCode
     * @param subjectId
     * @return
     */
    boolean existsAuthConfig(int appId, long familyId, long userId, String roleCode, long subjectId);

    /**
     * 修改授权配置状态
     * @param configId
     * @param status
     */
    void modifyAuthConfigStatus(long configId, int status);

    /**
     * 获取用户授权角色
     * @param appId
     * @param userId
     * @return
     */
    List<RoleInfoAuthRefBean> getUserAuthRoles(int appId, long userId);

    /**
     * 查询用户的角色的厅数据范围
     * @return 厅id列表
     */
    List<Long> getRoleRoomDataScope(RoleEnum role, Long userId, Long familyId);

    /**
     * 移除用户在家族中的授权记录
     * @param familyId
     * @param userIds
     */
    void removeUsersWithFamilyAuth(Long familyId, List<Long> userIds);

    /**
     * 移除厅数据范围
     * @param familyId
     * @param roomId
     */
    void removeFamilyRoomDataScope(Long familyId, Long roomId);

    /**
     * 查询多个厅下的超级管理员列表
     * @param njIds
     * @return
     */
    List<Long> getSuperAdminUserIds(List<Long> njIds);

    /**
     * 根据授权主体查询指定授权角色的用户列表
     * @param subjectId 授权主体
     * @param targetRoleCode 指定授权角色
     * @return 用户列表
     */
    List<Long> getRoomRoleAuthUserIds(Integer appId, Long subjectId, RoleEnum targetRoleCode);

}
