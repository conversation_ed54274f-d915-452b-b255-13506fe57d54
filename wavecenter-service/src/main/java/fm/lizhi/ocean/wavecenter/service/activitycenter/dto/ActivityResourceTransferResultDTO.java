package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.*;

import java.util.Date;

/**
 *
 * 活动资源转存记录
 *
 * <AUTHOR>
 * @date 2024-10-21 09:58:47
 */
@Data
public class ActivityResourceTransferResultDTO {
    /**
     * ID
     */

    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 来源 URI
     */
    private String sourceUri;

    /**
     * 转存 URI
     */
    private String targetUri;

    /**
     * 类型
     * @see ActivityResourceTransferStatusEnum
     */
    private Integer type;

    /**
     * 转存状态 0: 失败 1: 成功
     */
    private Integer status;

}