package fm.lizhi.ocean.wavecenter.service.activitycenter.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataDetailBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataGiftBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataPlayerBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataSummaryBean;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;

import java.util.List;

/**
 * 活动数据
 * <AUTHOR>
 */
public interface ActivityReportDataManager {


    /**
     * 活动数据汇总
     */
    Result<ActivityReportDataSummaryBean> getReportSummary(Long activityId, int appId);


    /**
     * 活动数据趋势图
     */
    Result<List<ActivityReportDataDetailBean>> getReportDetail(Long activityId, int appId);


    /**
     * 活动数据主播表现
     */
    Result<PageBean<ActivityReportDataPlayerBean>> pageReportPlayer(Long activityId, int appId, int pageNo, int pageSize);


    /**
     * 活动数据送礼明细
     */
    Result<PageBean<ActivityReportDataGiftBean>> pageReportGift(Long activityId, int appId, int pageNo, int pageSize);

    /**
     * 批量获取活动数据汇总
     */
    List<ActivityReportDataSummaryBean> batchGetReportSummary(List<Long> activityIds);
}
