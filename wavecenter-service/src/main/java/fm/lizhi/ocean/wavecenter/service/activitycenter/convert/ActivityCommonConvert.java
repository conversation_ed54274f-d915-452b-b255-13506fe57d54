package fm.lizhi.ocean.wavecenter.service.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.RequestGetOfficialSeatTimeBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.RequestGetResourceTimeBean;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActivityCommonConvert {

    ActivityCommonConvert I = Mappers.getMapper(ActivityCommonConvert.class);

    RequestGetOfficialSeatTimeBean convertRequestGetOfficialSeatTimeBean(RequestGetResourceTimeBean request);

}
