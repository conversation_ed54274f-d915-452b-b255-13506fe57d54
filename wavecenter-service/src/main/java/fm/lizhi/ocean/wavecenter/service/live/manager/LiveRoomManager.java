package fm.lizhi.ocean.wavecenter.service.live.manager;

import fm.lizhi.ocean.wavecenter.api.live.constants.RoomCategoryEnum;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/5/9 15:27
 */
@Component
public interface LiveRoomManager {

    /**
     * 查询用户的厅品类
     * @param userId
     * @return
     */
    Optional<RoomCategoryEnum> getUserRoomCategory(Long userId);

}
