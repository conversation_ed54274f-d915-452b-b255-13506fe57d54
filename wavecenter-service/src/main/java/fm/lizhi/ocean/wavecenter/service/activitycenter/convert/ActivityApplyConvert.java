package fm.lizhi.ocean.wavecenter.service.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.*;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseActivityInfoDetail;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityApplyInfoSimpleDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityInfoDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityProcessDTO;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityResourceSimpleInfoDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.*;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActivityApplyConvert {

    ActivityApplyConvert I = Mappers.getMapper(ActivityApplyConvert.class);


    @Mappings({
            @Mapping(ignore = true, target = "activityToolList")

    })
    ResponseActivityInfoDetail convertResponseActivityInfoDetail(ActivityInfoDTO activityInfo);

    default Long map(Date date) {
        return date.getTime();
    }

    List<ActivityProcessBean> convertActivityProcessBean(List<ActivityProcessDTO> processes);


    ActivityFlowResourceDetailBean convertFlowResourceDetailBean(ActivityResourceSimpleInfoDTO activityResourceSimpleInfoDTO);

    FlowResourceExtra convertOfficialSeatExtraBean(OfficialSeatExtraBean extra);

    ActivityApplyToolBean simpleDTO2ToolBean(ActivityApplyInfoSimpleDTO dto);

}
