package fm.lizhi.ocean.wavecenter.service.activitycenter.impl;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.*;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyRuleEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.AutoConfigResourceEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestActivityOptionOfficialTime;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseActivityOptionOfficialTime;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityOfficialSeatTimeService;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateDetailFlowResourceBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateFlowResourceExtraBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseGetActivityTemplate;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.constants.TimeConstant;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.ActivityConfig;
import fm.lizhi.ocean.wavecenter.service.activitycenter.convert.ActivityOfficialTimeConvert;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityOfficialSeatTimeDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityOptionalOfficialTimeDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityOfficialSeatManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityRuleManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityTemplateManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

@Slf4j
@ServiceProvider
public class ActivityOfficialSeatTimeServiceImpl implements ActivityOfficialSeatTimeService {

    @Autowired
    private ActivityOfficialSeatManager activityOfficialSeatManager;

    @Autowired
    private ActivityRuleManager activityRulesManager;

    @Autowired
    private ActivityConfig config;

    @Autowired
    private ActivityTemplateManager activityTemplateManager;

    @Override
    public Result<ResponseGetOfficialSeatTimeBean> getOfficialSeatTimeList(RequestGetOfficialSeatTimeBean request) {
        LogContext.addReqLog("getOfficialSeatTimeList.paramBean={}", JsonUtil.dumps(request));
        LogContext.addResLog("getOfficialSeatTimeList.paramBean={}", JsonUtil.dumps(request));
        if (request.getStartDate() == null || request.getEndDate() == null) {
            request.setStartDate(DateUtil.getDayStart(new Date()));
            request.setEndDate(DateUtil.getDayAfter(request.getStartDate(), config.getMaxQueryOfficialSeatTimeDayRange()));
        }
        if (request.getAppId() == null || request.getEndDate().getTime() < request.getStartDate().getTime() || request.getSeat() == null ||
                (request.getEndDate().getTime() - request.getStartDate().getTime()) / 1000 / 60 / 60 / 24 > config.getMaxQueryOfficialSeatTimeDayRange()) {
            return RpcResult.fail(ActivityOfficialSeatTimeService.GET_OFFICIAL_SEAT_TIME_LIST);
        }

        List<ActivityOfficialSeatTimeDTO> seatList = activityOfficialSeatManager.getOfficialSeatList(request.getAppId(), request.getStartDate(), request.getEndDate(), request.getSeat());
        List<OfficialSeatTimeBean> officialSeatTimeBeans = buildSeatTimeBeans(seatList);
        ActivityRuleConfigBean rule = activityRulesManager.getActivityRuleByRuleTypeAndAppId(request.getAppId(), ActivityApplyRuleEnum.OFFICIAL_COUNT);
        int count = 5;
        if (!Objects.isNull(rule)) {
            OfficialCountRuleBean ruleBean = activityRulesManager.getRuleBean(ActivityApplyRuleEnum.OFFICIAL_COUNT, rule.getRuleJson());
            if (ruleBean != null) {
                count = ruleBean.getCount();
            }
        }

        ResponseGetOfficialSeatTimeBean seatTimeBean = new ResponseGetOfficialSeatTimeBean()
                .setTimeList(officialSeatTimeBeans).setMaxOfficialSeatHallCount(count);
        return RpcResult.success(seatTimeBean);
    }

    @Override
    public Result<ResponseActivityOptionOfficialTime> getOptionalOfficialTime(RequestActivityOptionOfficialTime request) {
        Result<ResponseGetActivityTemplate> template = activityTemplateManager.getTemplate(request.getTemplateId());
        if (RpcResult.isFail(template)) {
            return RpcResult.fail(GET_OPTIONAL_OFFICIAL_TIME_TEMPLATE_NOT_EXIST);
        }

        List<ActivityTemplateDetailFlowResourceBean> flowResources = template.target().getFlowResources();
        if (CollectionUtils.isEmpty(flowResources)) {
            //没有流量资源，就不管了
            log.info("getOptionalOfficialTime.templateId={} templateFlowResource is empty", request.getTemplateId());
            return RpcResult.success(new ResponseActivityOptionOfficialTime());
        }

        //过滤出官频位资源
        ActivityTemplateDetailFlowResourceBean resource = flowResources.stream()
                .filter(bean -> Objects.equals(bean.getResourceCode(), AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode()))
                .findFirst().orElse(null);
        if (resource == null) {
            //没有配置官频位，不管了
            log.info("getOptionalOfficialTime.templateId={} official_seat resource is empty", request.getTemplateId());
            return RpcResult.success(new ResponseActivityOptionOfficialTime());
        }

        if ((request.getActivityEndTime() - request.getActivityStartTime()) / TimeConstant.ONE_MINUTE_MILLISECOND < Optional.ofNullable(resource.getExtra()).map(ActivityTemplateFlowResourceExtraBean::getDurationLimit).orElse(0)) {
            //活动时间范围比小于限定的官频位时长，不处理
            log.info("getOptionalOfficialTime.templateId={} activity time range less than limit", request.getTemplateId());
            return RpcResult.success(new ResponseActivityOptionOfficialTime());
        }

        ActivityTemplateFlowResourceExtraBean extra = resource.getExtra();
        ActivityOptionalOfficialTimeDTO optionalTime = activityOfficialSeatManager.getOptionalOfficialTime(request.getAppId(), request.getActivityStartTime(), request.getActivityEndTime(), extra, template.target());
        //返回可选的官频位开始时间和结束时间
        OfficialOptionalTimeBean bean = ActivityOfficialTimeConvert.I.officialOptionalTimeDTO2Bean(optionalTime);
        return RpcResult.success(new ResponseActivityOptionOfficialTime().setOfficialOptionalTime(bean));
    }

    private List<OfficialSeatTimeBean> buildSeatTimeBeans(List<ActivityOfficialSeatTimeDTO> seatList) {
        if (CollectionUtils.isEmpty(seatList)) {
            return Collections.emptyList();
        }

        Map<Long, List<OfficialSeatTimeItemBean>> map = new HashMap<>();
        for (ActivityOfficialSeatTimeDTO seat : seatList) {
            List<OfficialSeatTimeItemBean> beans = map.computeIfAbsent(seat.getShowDate().getTime(), k -> new ArrayList<>());
            beans.add(new OfficialSeatTimeItemBean()
                    .setSeat(seat.getSeat())
                    .setStartTime(seat.getStartTime().getTime())
                    .setEndTime(seat.getEndTime().getTime())
                    .setCount(seat.getCount()));
        }

        //遍历map
        List<OfficialSeatTimeBean> list = new ArrayList<>();
        for (Map.Entry<Long, List<OfficialSeatTimeItemBean>> entry : map.entrySet()) {
            entry.getValue().sort(Comparator.comparing(OfficialSeatTimeItemBean::getStartTime));
            OfficialSeatTimeBean timeBean = new OfficialSeatTimeBean().setDate(entry.getKey()).setList(entry.getValue());
            list.add(timeBean);
        }
        list.sort(Comparator.comparing(OfficialSeatTimeBean::getDate));
        return list;
    }
}
