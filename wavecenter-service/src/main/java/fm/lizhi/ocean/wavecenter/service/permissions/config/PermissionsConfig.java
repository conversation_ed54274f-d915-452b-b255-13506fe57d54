package fm.lizhi.ocean.wavecenter.service.permissions.config;

import com.google.common.collect.Lists;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/27 10:19
 */
@Data
@ConfigurationProperties(prefix = "wavecenter-permission")
public class PermissionsConfig {

    /**
     * 角色授权可以提供授权的角色
     */
    private List<String> authRoleCodes = Lists.newArrayList(RoleEnum.FAMILY.getRoleCode(), RoleEnum.ROOM.getRoleCode());

    /**
     * 授权后不公开的菜单
     */
    private List<String> filterAuthMenu = Lists.newArrayList("income/member", "secure/authorize");

}
