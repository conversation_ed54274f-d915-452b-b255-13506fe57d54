package fm.lizhi.ocean.wavecenter.service.user.manager;

import fm.lizhi.ocean.wavecenter.api.user.bean.UserTokenBean;
import fm.lizhi.ocean.wavecenter.service.user.dto.*;

import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/4/12 17:30
 */
public interface LoginManager {

    /**
     * 清除用户登录的角色
     * @param appId
     * @param userId
     * @param deviceId
     */
    void resetLoginRole(int appId, long userId, String deviceId);

    /**
     * 清除所有登录信息
     * @param userId
     * @param deviceId
     */
    void deleteAllLoginInfo(int appId, long userId, String deviceId);

    /**
     * 根据用户ID删除所有登录信息
     * @param appId 应用ID
     * @param userId 用户ID
     */
    void deleteAllLoginInfo(int appId, long userId);

    /**
     * 删除token
     * @param accessToken
     * @param refreshToken
     */
    void deleteToken(String accessToken, String refreshToken);

    /**
     * 添加新的token
     * @param userId 用户ID
     * @param deviceId 设备ID
     */
    void saveToken(int appId, long userId, String deviceId, UserTokenBean userTokenBean);

    /**
     * 登录后置处理
     * @param userInfoDto
     * @param loginContextDto
     */
    void loginPostProcessor(UserInfoDto userInfoDto, LoginContextDto loginContextDto);

    void logoutPostProcessor(int appId, long userId);

    /**
     * 获取accessToken
     * @param refreshToken
     * @return
     */
    Optional<String> getAccessTokenByRefreshToken(String refreshToken);

    /**
     * 获取refreshToken关联用户信息
     * @param refreshToken
     * @return
     */
    Optional<TokenUserInfoDto> getUserByRefreshToken(String refreshToken);

    /**
     * 删除二维码key
     * @param appId
     * @param qrCodeKey
     */
    void deleteQrCodeKey(int appId, String qrCodeKey);

    /**
     * 保存二维码key
     * @param appId
     * @param qrCodeKey
     * @param expireSeconds 过期时间，秒
     * @param status 二维码状态
     */
    void saveQrCodeKey(int appId, String qrCodeKey, int expireSeconds, int status);

    /**
     * 关联二维码设备
     * @param appId
     * @param qrCodeKey
     * @param expireSeconds
     * @param deviceId
     */
    void refQrCodeKeyDevice(int appId, String qrCodeKey, int expireSeconds, String deviceId);

    /**
     * 获取二维码设备
     * @param appId
     * @param qrCodeKey
     * @return
     */
    Optional<String> getQrCodeKeyDevice(int appId, String qrCodeKey);

    /**
     * 二维码有效时间
     * @param appId
     * @param qrCodeKey
     * @return
     */
    int qrCodeExpireSeconds(int appId, String qrCodeKey);

    /**
     * 关联二维码和token
     * @param appId
     * @param qrCodeKey
     * @param userTokenBean
     */
    void refQrCodeAndToken(int appId, String qrCodeKey, UserTokenBean userTokenBean);

    /**
     * 获取二维码状态
     * @param appId
     * @param qrCodeKey
     * @return
     */
    Optional<Integer> getQrCodeStatus(int appId, String qrCodeKey);

    /**
     * 通过qrcode获取token
     * @param appId
     * @param qrCodeKey
     * @return
     */
    Optional<UserTokenBean> getTokenByQrCodeKey(int appId, String qrCodeKey);

    /**
     * 获取Token
     * @param appId
     * @param userId
     * @param deviceId
     * @return
     */
    Optional<UserTokenBean> getToken(int appId, long userId, String deviceId);

    /**
     * 关联token和角色配置ID
     * @param token
     * @param roleConfigId
     */
    void refTokenRoleConfigId(int appId, UserTokenBean token, Long roleConfigId);

    /**
     * 关联accessToken用户角色
     * @param appId
     * @param userId
     * @param roleConfigId
     * @param accessToken
     */
    void saveAccessTokenRole(int appId, long userId, String deviceId, Long roleConfigId, String accessToken, int accessTokenExpireSeconds);

    /**
     * 获取用户的所有设备
     * @param appId
     * @param userId
     * @return
     */
    Set<String> getUserDevice(int appId, long userId);

    Optional<Long> getUserRoleConfigId(int appId, long userId, String deviceId);

    Optional<Long> getUserIdByAccessToken(String accessToken);

    Optional<LoginUserInfoDto> getUserByAccessToken(String accessToken);

    /**
     * 关联accessToken和登录角色
     * @param loginRoleExpiresInfoDto
     * @param accessToken
     */
    void refTokenRole(LoginRoleExpiresInfoDto loginRoleExpiresInfoDto, String accessToken, String refreshToken);

    /**
     * 获取accessToken登录角色信息
     * @param accessToken
     * @return
     */
    Optional<LoginRoleInfoDto> getAccessTokenRole(String accessToken);

    /**
     * 获取refreshToken登录角色信息
     * @param refreshToken
     * @return
     */
    Optional<LoginRoleInfoDto> getRefreshTokenRole(String refreshToken);

    /**
     * 获取用户角色code
     * @param appId
     * @param userId
     * @param deviceId
     * @return
     */
    Optional<String> getUserRoleCode(int appId, long userId, String deviceId);

    /**
     * 获取用户登录类型
     * @param appId
     * @param userId
     * @param deviceId
     * @return
     */
    int getLoginType(int appId, long userId, String deviceId);

}
