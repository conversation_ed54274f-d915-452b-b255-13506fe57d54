package fm.lizhi.ocean.wavecenter.service.income.impl;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.GuildIncomeSummaryBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomIncomeDetailBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.*;
import fm.lizhi.ocean.wavecenter.api.income.request.RequestGuildIncomeStats;
import fm.lizhi.ocean.wavecenter.api.income.request.RequestGuildIncomeSummary;
import fm.lizhi.ocean.wavecenter.api.income.response.ResponseGuildIncomeStats;
import fm.lizhi.ocean.wavecenter.api.income.service.IncomeGuildService;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.GuildIncomeStatParamDTO;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.GuildIncomeStatDTO;
import fm.lizhi.ocean.wavecenter.service.income.convert.IncomeGuildConvert;
import fm.lizhi.ocean.wavecenter.service.income.manager.FlowManager;
import fm.lizhi.ocean.wavecenter.service.income.manager.IncomeManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/5/10 08:18
 */
@ServiceProvider
public class IncomeGuildServiceImpl implements IncomeGuildService {

    @Autowired
    private IncomeManager incomeManager;

    @Autowired
    private UserManager userManager;

    @Autowired
    private FlowManager flowManager;

    @Override
    public Result<PageBean<RoomIncomeDetailBean>> signRoomIncomeDetail(SignRoomIncomeDetailParamBean paramBean) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));
        return ResultHandler.handle(paramBean.getAppId(), () -> {
            PageBean<RoomIncomeDetailBean> roomIncomeBeanPageBean = incomeManager.signRoomIncomeDetail(paramBean);
            return RpcResult.success(roomIncomeBeanPageBean);
        });
    }

    @Override
    public Result<GuildIncomeSummaryBean> guildIncomeSummary(long familyId, int appid) {
        LogContext.addReqLog("appId={}, familyId={}", appid, familyId);
        LogContext.addResLog("appId={}, familyId={}", appid, familyId);
        return ResultHandler.handle(appid, () -> {
            GuildIncomeSummaryBean guildIncomeSummaryBean = incomeManager.guildIncomeSummary(familyId, null);
            return RpcResult.success(guildIncomeSummaryBean);
        });
    }

    @Override
    public Result<GuildIncomeSummaryBean> guildIncomeSummaryV2(RequestGuildIncomeSummary request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));

        GuildIncomeSummaryBean guildIncomeSummaryBean = incomeManager.guildIncomeSummary(request.getFamilyId(), request.getRoomIds());
        return RpcResult.success(guildIncomeSummaryBean);
    }

    @Override
    public Result<PageBean<GuildIncomeDetailBean>> getGuildIncomeDetail(GetGuildIncomeDetailParamBean paramBean) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));
        return ResultHandler.handle(paramBean.getAppId(), () -> {
            PageBean<GuildIncomeDetailBean> pageList = incomeManager.getGuildIncomeDetail(paramBean);
            //查询厅主信息
            List<GuildIncomeDetailBean> list = pageList.getList();
            List<Long> userIds = Arrays.asList(paramBean.getRoomId());
            Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(userIds);
            for (GuildIncomeDetailBean bean : list) {
                UserBean roomInfo = bean.getRoomInfo();
                SimpleUserDto simpleUserDto = userMap.get(roomInfo.getId());
                if (simpleUserDto == null) {
                    continue;
                }
                roomInfo.setBand(simpleUserDto.getBand());
                roomInfo.setName(simpleUserDto.getName());
            }
            return RpcResult.success(pageList);
        });
    }

    @Override
    public Result<PageBean<GuildIncomeDetailBean>> getGuildIncomeDetailOut(GetGuildIncomeDetailParamBean paramBean) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));
        return ResultHandler.handle(paramBean.getAppId(), () -> {
            return RpcResult.success(flowManager.getGuildIncomeDetailOut(paramBean));
        });
    }

    @Override
    public Result<GuildIncomeDetailSumBean> getGuildIncomeDetailSum(GetGuildIncomeDetailSumParamBean paramBean) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));
        return ResultHandler.handle(paramBean.getAppId(), () -> RpcResult.success(incomeManager.getGuildIncomeDetailSum(paramBean)));
    }

    @Override
    public Result<ResponseGuildIncomeStats> queryGuildIncomeStats(RequestGuildIncomeStats request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        // 转换请求参数
        GuildIncomeStatParamDTO param = IncomeGuildConvert.I.convertToParamDto(request);
        // 调用Manager层查询数据
        PageBean<GuildIncomeStatDTO> pageResult = incomeManager.queryGuildIncomeStats(param);
        // 转换响应结果
        ResponseGuildIncomeStats response = IncomeGuildConvert.I.convertToResponse(pageResult);
        return RpcResult.success(response);
    }

}
