package fm.lizhi.ocean.wavecenter.service.permissions.impl;

import com.google.common.collect.Lists;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageParamBean;
import fm.lizhi.ocean.wavecenter.api.permissions.bean.*;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.permissions.request.RequestGetRoleRoomDataScope;
import fm.lizhi.ocean.wavecenter.api.permissions.service.RoleService;
import fm.lizhi.ocean.wavecenter.api.user.bean.RoomBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.permissions.config.PermissionsConfig;
import fm.lizhi.ocean.wavecenter.service.permissions.constants.PermissionConstants;
import fm.lizhi.ocean.wavecenter.service.permissions.convert.RoleConvert;
import fm.lizhi.ocean.wavecenter.service.permissions.dto.RoleAuthRefDto;
import fm.lizhi.ocean.wavecenter.service.permissions.dto.RoleDto;
import fm.lizhi.ocean.wavecenter.service.permissions.manager.RoleManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.LoginManager;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/4/11 11:07
 */
@ServiceProvider
@Slf4j
public class RoleServiceImpl implements RoleService {

    @Autowired
    private RoleManager roleManager;
    @Autowired
    private PermissionsConfig permissionsConfig;
    @Autowired
    private FamilyManager familyManager;
    @Autowired
    private LoginManager loginManager;
    @Autowired
    private UserManager userManager;

    @Override
    public Result<List<RoleBean>> getAllRoles() {
        LogContext.addReqLog("getAllRoles");
        LogContext.addResLog("getAllRoles");
        List<RoleDto> dtos = roleManager.getAllRoles();
        return RpcResult.success(RoleConvert.I.roleDtos2Beans(dtos));
    }

    @Override
    public Result<List<RoleBean>> getAuthRoles() {
        LogContext.addReqLog("getAuthRoles");
        LogContext.addResLog("getAuthRoles");
        List<RoleDto> dtoList = roleManager.getRolesByCodes(permissionsConfig.getAuthRoleCodes());
        return RpcResult.success(RoleConvert.I.roleDtos2Beans(dtoList));
    }

    @Override
    public Result<PageBean<RoleAuthRefBean>> getAllAuthConfig(int appId, long createUserId, Long userId, String roleCode, PageParamBean pageParamBean) {
        LogContext.addReqLog("appId={},pageNo={},pageSize={}", appId, pageParamBean.getPageNo(), pageParamBean.getPageSize());
        LogContext.addResLog("appId={},pageNo={},pageSize={}", appId, pageParamBean.getPageNo(), pageParamBean.getPageSize());
        return ResultHandler.handle(appId, ()->{
            PageBean<RoleAuthRefBean> pageList = roleManager.getAllAuthConfig(appId, createUserId, userId, roleCode, pageParamBean);
            return RpcResult.success(pageList);
        });
    }

    @Override
    public Result<RoleAuthRefBean> getAuthConfig(long roleConfigId) {
        LogContext.addReqLog("roleConfigId={}", roleConfigId);
        LogContext.addResLog("roleConfigId={}", roleConfigId);
        Optional<RoleAuthRefDto> op = roleManager.getAuthConfig(roleConfigId);
        if (!op.isPresent()) {
            return RpcResult.fail(AUTH_CONFIG_NOT_EXIST);
        }

        RoleAuthRefDto dto = op.get();

        return ResultHandler.handle(dto.getAppId(), ()->{
            RoleAuthRefBean bean = RoleConvert.I.roleAuthRefDto2Bean(dto);
            Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(Lists.newArrayList(dto.getUserId(), dto.getSubjectUserId()));
            SimpleUserDto userDto = userMap.get(dto.getUserId());
            if (userDto != null) {
                bean.getUserInfo().setBand(userDto.getBand());
                bean.getUserInfo().setName(userDto.getName());
            }
            SimpleUserDto subjectDto = userMap.get(dto.getSubjectUserId());
            if (subjectDto != null) {
                bean.getSubject().setName(subjectDto.getName());
                bean.getSubject().setBand(subjectDto.getBand());
            }
            return RpcResult.success(bean);
        });
    }

    @Override
    public Result<Void> addAuthConfig(int appId, AddRoleAuthRefBean addRoleAuthRefBean) {
        LogContext.addReqLog("appId={},addRoleAuthRefBean={}", appId, JsonUtil.dumps(addRoleAuthRefBean));
        LogContext.addResLog("appId={},addRoleAuthRefBean={}", appId, JsonUtil.dumps(addRoleAuthRefBean));

        return ResultHandler.handle(appId, ()->{
            //校验主体的角色是否符合
            String roleCode = addRoleAuthRefBean.getRoleCode();
            Long subjectUserId = addRoleAuthRefBean.getSubjectUserId();
            UserInFamilyBean userInFamily = familyManager.getUserInFamily(subjectUserId);
            Long subjectId = null;

            if (RoleEnum.FAMILY.getRoleCode().equals(roleCode)) {
                if (!userInFamily.isFamily()){
                    LogContext.addResLog("is not family");
                    return RpcResult.fail(ADD_AUTH_CONFIG_ROLE_ERROR);
                }
                subjectId = userInFamily.getFamilyId();
            }

            if (RoleEnum.ROOM.getRoleCode().equals(roleCode)) {
                if (!userInFamily.isRoom()) {
                    LogContext.addResLog("is not room");
                    return RpcResult.fail(ADD_AUTH_CONFIG_ROLE_ERROR);
                }
                subjectId = subjectUserId;
            }

            if (RoleEnum.FAMILY_ADMIN.getRoleCode().equals(roleCode)) {
                subjectId = addRoleAuthRefBean.getFamilyId();
            }

            if (subjectId == null) {
                return RpcResult.fail(ADD_AUTH_CONFIG_SUBJECT_ERROR);
            }

            // 判断是否已存在
            if (roleManager.existsAuthConfig(appId, addRoleAuthRefBean.getFamilyId(), addRoleAuthRefBean.getUserId(), roleCode, subjectId)) {
                if (!RoleEnum.FAMILY_ADMIN.getRoleCode().equals(roleCode)) {
                    return RpcResult.fail(ADD_AUTH_CONFIG_EXIST);
                }
            }

            roleManager.addAuthConfig(appId, addRoleAuthRefBean, subjectId);
            return RpcResult.success();
        });
    }

    @Override
    public Result<List<ModifyAuthConfigUserVo>> modifyAuthConfigStatus(long configId, int status) {
        LogContext.addReqLog("configId={},status={}", configId, status);
        LogContext.addResLog("configId={},status={}", configId, status);

        roleManager.modifyAuthConfigStatus(configId, status);
        if (status != PermissionConstants.RoleRefStatus.STOP) {
            return RpcResult.success();
        }

        //退出用户登录状态
        List<ModifyAuthConfigUserVo> list = new ArrayList<>();

        //查询配置
        Optional<RoleAuthRefDto> authConfigOp = roleManager.getAuthConfig(configId);
        if (!authConfigOp.isPresent()) {
            LogContext.addResLog("authConfig not exist");
            return RpcResult.success();
        }
        RoleAuthRefDto roleAuthRefDto = authConfigOp.get();
        Integer appId = roleAuthRefDto.getAppId();
        Long userId = roleAuthRefDto.getUserId();

        Set<String> userDevice = loginManager.getUserDevice(appId, userId);
        for (String deviceId : userDevice) {
            //如果该设备使用该角色，则退出
            Optional<Long> userRoleConfigId = loginManager.getUserRoleConfigId(appId, userId, deviceId);
            if (userRoleConfigId.isPresent() && userRoleConfigId.get().equals(configId)) {
                loginManager.resetLoginRole(appId, userId, deviceId);
                list.add(new ModifyAuthConfigUserVo().setUserId(userId).setAppId(appId).setDeviceId(deviceId));
            }
        }
        LogContext.addResLog("list={}", JsonUtil.dumps(list));
        return RpcResult.success(list);
    }

    @Override
    public Result<List<RoleInfoAuthRefBean>> getUserAuthRoles(int appId, long userId) {
        LogContext.addReqLog("appId={},userId={}", appId, userId);
        LogContext.addResLog("appId={},userId={}", appId, userId);
        return ResultHandler.handle(appId, ()->{
            List<RoleInfoAuthRefBean> list = roleManager.getUserAuthRoles(appId, userId);
            if (CollectionUtils.isEmpty(list)) {
                return RpcResult.success(list);
            }

            List<RoleInfoAuthRefBean> familyList = new ArrayList<>();
            List<RoleInfoAuthRefBean> roomList = new ArrayList<>();
            for (RoleInfoAuthRefBean roleInfoAuthRefBean : list) {
                if (RoleEnum.FAMILY.getRoleCode().equals(roleInfoAuthRefBean.getRoleCode())
                        || RoleEnum.FAMILY_ADMIN.getRoleCode().equals(roleInfoAuthRefBean.getRoleCode())) {
                    familyList.add(roleInfoAuthRefBean);
                }
                if (RoleEnum.ROOM.getRoleCode().equals(roleInfoAuthRefBean.getRoleCode())) {
                    roomList.add(roleInfoAuthRefBean);
                }
            }

            //校验签约关系
            UserInFamilyBean userInFamily = familyManager.getUserInFamily(userId);
            Long familyId = userInFamily.getFamilyId();
            if (log.isDebugEnabled()) {
                log.debug("getUserAuthRoles, appId={}, userId={}, familyId={}, familyList={}, roomList={}, list={}",
                        appId, userId, familyId, JsonUtils.toJsonString(familyList), JsonUtils.toJsonString(roomList), JsonUtils.toJsonString(list));
            }

            //familyList校验：校验当前用户的familyId是否和授权记录的subjectId一致
            //roomList校验：授权记录的subjectId的familyId是否和当前用户的familyId一致
            if (CollectionUtils.isNotEmpty(familyList)) {
                //查询出用户的家族ID，授权列表不多直接循环查询
                for (RoleInfoAuthRefBean roleInfoAuthRefBean : familyList) {
                    if (familyId == null) {
                        list.remove(roleInfoAuthRefBean);
                        continue;
                    }

                    if (!Objects.equals(familyId, roleInfoAuthRefBean.getSubjectId())) {
                        list.remove(roleInfoAuthRefBean);
                        log.debug("getUserAuthRoles familyId not match, appId={}, userId={}, familyId={}, subjectId={}, refId={}",
                                appId, userId, familyId, roleInfoAuthRefBean.getSubjectId(), roleInfoAuthRefBean.getId());
                    }
                }
            }

            //厅主授权，获取授权用户的厅主ID，是否和授权记录一致
            //过滤掉不一致的
            if (CollectionUtils.isNotEmpty(roomList)) {
                for (RoleInfoAuthRefBean roleInfoAuthRefBean : roomList) {
                    if (familyId == null) {
                        list.remove(roleInfoAuthRefBean);
                        continue;
                    }
                    //授权账号的厅是否依然属于该公会
                    Long subjectUserId = roleInfoAuthRefBean.getSubject().getId();
                    //授权列表不多直接循环查询
                    UserInFamilyBean subjectFamily = familyManager.getUserInFamily(subjectUserId);
                    Long subjectFamilyId = subjectFamily.getFamilyId();
                    if (subjectFamilyId == null) {
                        list.remove(roleInfoAuthRefBean);
                        log.debug("getUserAuthRoles subjectFamilyId is null, appId={}, userId={}, subjectUserId={}, refId={}",
                                appId, userId, subjectUserId, roleInfoAuthRefBean.getId());
                        continue;
                    }

                    if (!Objects.equals(subjectFamilyId, familyId)) {
                        list.remove(roleInfoAuthRefBean);
                        log.debug("getUserAuthRoles subjectFamilyId not match, appId={}, userId={}, subjectUserId={}, refId={}, subjectFamilyId={}, requiredFamilyId={}",
                                appId, userId, subjectUserId, roleInfoAuthRefBean.getId(), subjectFamilyId, familyId);
                    }
                }
            }

            log.info("getUserAuthRoles, appId={}, userId={}, list={}", appId, userId, JsonUtils.toJsonString(list));
            return RpcResult.success(list);
        });
    }

    @Override
    public Result<List<RoomBean>> getRoleRoomDataScope(RequestGetRoleRoomDataScope request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));

        List<Long> roomIds = roleManager.getRoleRoomDataScope(request.getRole()
                , request.getUserId(), request.getFamilyId());
        if (CollectionUtils.isEmpty(roomIds)) {
            LogContext.addResLog("roomIds is empty");
            return RpcResult.success(Collections.emptyList());
        }

        Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(roomIds);
        List<RoomBean> roomBeans = new ArrayList<>();
        for (Long roomId : roomIds) {
            RoomBean roomBean = new RoomBean();
            roomBean.setId(roomId);
            SimpleUserDto simpleUserDto = userMap.get(roomId);
            if (simpleUserDto != null) {
                roomBean.setName(simpleUserDto.getName());
                roomBean.setBand(simpleUserDto.getBand());
                roomBean.setPhoto(simpleUserDto.getAvatar());
            }
            roomBeans.add(roomBean);
        }
        return RpcResult.success(roomBeans);
    }
}
