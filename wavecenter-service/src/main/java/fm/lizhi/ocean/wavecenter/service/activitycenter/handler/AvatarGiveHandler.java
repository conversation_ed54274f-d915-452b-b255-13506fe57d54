package fm.lizhi.ocean.wavecenter.service.activitycenter.handler;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.DecorateEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.service.activitycenter.constatns.ResourceGiveErrorTipConstant;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityDressUpGiveDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityResourceGiveDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.DressUpGiveContext;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.SendDecorateParamDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityMaterielManager;
import fm.lizhi.ocean.wavecenter.service.activitycenter.processor.IAvatarGiveProcess;
import fm.lizhi.ocean.wavecenter.base.processor.ProcessorFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 头像框发放处理器
 */
@Slf4j
@Component
public class AvatarGiveHandler implements DressUpGiveHandler {

    @Autowired
    private ActivityMaterielManager activityMaterielManager;

    @Autowired
    private ProcessorFactory processorFactory;

    @Override
    public Result<Void> giveDressUp(DressUpGiveContext context) {
        try {
            IAvatarGiveProcess processor = processorFactory.getProcessor(IAvatarGiveProcess.class);
            if (!processor.isAutoGive()) {
                //不能自动发放，默认是成功
                return RpcResult.success();
            }
            ActivityResourceGiveDTO resourceGiveDTO = context.getResourceGiveDTO();
            ActivityDressUpGiveDTO dressUpGiveDTO = context.getDressUpGiveDTO();
            SendDecorateParamDTO paramDTO = new SendDecorateParamDTO();
            paramDTO.setDecorateId(resourceGiveDTO.getResourceId());
            paramDTO.setDecorateRecordId(dressUpGiveDTO.getId());
            paramDTO.setUseImmediately(1);
            paramDTO.setOwnerId(dressUpGiveDTO.getUserId());

            processor.fillSendParam(context, paramDTO);
            Result<Void> result = activityMaterielManager.sendDecorate(paramDTO);
            return RpcResult.isSuccess(result) ? RpcResult.success() : RpcResult.fail(result.rCode());
        } catch (Exception e) {
            log.error("AvatarGiveHandler.giveDressUp happen error: dressUpGiveDTO={}", JsonUtils.toJsonStringLegacy(context.getDressUpGiveDTO()), e);
            return RpcResult.fail(GIVE_DRESS_UP, ResourceGiveErrorTipConstant.AVATAR_GIVE_FAIL);
        }
    }

    @Override
    public Integer getDressUpType() {
        return DecorateEnum.AVATAR.getType();
    }
}
