package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class HyOfficialSeatDetailDTO implements OfficialSeatDetailDTO {

    private Long id;

    /**
     * tabid
     */
    private Long tabId;

    /**
     * app推荐位，从1开始
     */
    private Integer position;

    /**
     * 用户组id
     */
    private Long userGroupId;

    /**
     * 背景图
     */
    private String bgImageUrl;

    /**
     * 活动类型
     */
    private Integer type;

    /**
     * 活动备注
     */
    private String remark;

    /**
     * 生效时间
     */
    private Long startTime;

    /**
     * 结束时间
     */
    private Long endTime;

    /**
     * 操作者
     */
    private String operator;

    /**
     * 用户id集合，换行分割
     */
    private String userIds;

}
