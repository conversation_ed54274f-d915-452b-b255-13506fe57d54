package fm.lizhi.ocean.wavecenter.service.user.manager;

import fm.lizhi.ocean.wavecenter.service.user.dto.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/4/11 10:10
 */
public interface UserManager {

    /**
     * 获取简单的用户信息
     * @param ids
     * @return
     */
    List<SimpleUserDto> getSimpleUserByIds(List<Long> ids);

    /**
     * 获取简单的用户信息 使用map信息
     * @param ids
     * @return
     */
    Map<Long, SimpleUserDto> getSimpleUserMapByIds(List<Long> ids);

    /**
     * 检查主播认证状态
     * @param userId
     * @return
     */
    boolean checkPlayerAuth(long userId);

    /**
     * 检查用户实名状态
     * @param userId
     * @param appId
     * @return
     */
    boolean checkUserRealNameAuthStatus(long userId, int appId);

    /**
     * 检查用户封禁状态
     * @param userId
     * @return true=被封禁
     */
    boolean checkUserBanStatus(long userId);

    /**
     * 通过businessToken
     * @param businessToken
     * @return
     */
    Optional<Long> getUserIdByBusinessToken(String businessToken);

    /**
     * 通过用户ID获取用户
     * @param userId
     * @return
     */
    Optional<UserInfoDto> getUserInfoById(long userId);

    /**
     * 获取用户信息
     * @param userId
     * @return
     */
    Optional<UserInfoDto> getUserInfoById2(long userId);

    /**
     * 获取用户信息
     * @param userIds
     * @return
     */
    List<UserInfoDto> getUserInfoByIds(List<Long> userIds);


    /**
     * 通过波段号 获取用户
     * @param band
     * @return
     */
    Optional<SimpleUserDto> getUserInfoByBand(String band);

    /**
     * 将波段号转为ID
     * @param band
     * @return
     */
    Long getUserIdByBand(String band);

    /**
     * 查询用户媒体信息
     * @param userId
     * @return
     */
    Optional<UserMediaDto> getUserMediaById(long userId);

    /**
     * 查询用户认证信息
     * @param userId
     * @return
     */
    Optional<UserVerifyDataDTO> getVerifyData(long userId);

    /**
     * 获取用户封禁状态
     * @param userId
     * @return
     */
    int getUserBandStatus(Long userId);

    /**
     * 获取认证成功的用户ID
     * @param idCardNumber
     * @return
     */
    List<Long> getUserIdFromVerifyResult(String idCardNumber);

    /**
     * 查询用户所有实名账号ID
     * @param userId
     * @return
     */
    List<Long> getUserIdFromVerifyResult(Long userId);

    /**
     * 是否完成主播中心认证
     * @param userId
     * @return
     */
    boolean finishPlayerCenterAuth(Long userId);

    /**
     * 获取用户实名认证
     * @param idCardNumber
     * @return
     */
    List<Long> getUserAuthUnionIdList(String idCardNumber);

    /**
     * 根据关键词搜索用户
     * @param keyword
     * @return
     */
    Optional<SimpleUserDto> getUserByKeyWord(String keyword);

    /**
     * 查询用户注销状态
     */
    Optional<UserWithdrawStatusDTO> getUserWithdrawStatus(long userId);
}
