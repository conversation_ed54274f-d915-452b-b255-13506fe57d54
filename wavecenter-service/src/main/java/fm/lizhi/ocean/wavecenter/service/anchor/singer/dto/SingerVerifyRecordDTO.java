package fm.lizhi.ocean.wavecenter.service.anchor.singer.dto;

import java.util.Date;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SingerVerifyRecordDTO {

    private Long id;

    /**
     * 歌手ID
     */
    private Long userId;

    /**
     * 签约厅主ID
     */
    private Long njId;

    /**
     * 家族ID
     */
    private Long familyId;

    /**
     * appId
     */
    private Integer appId;

    /**
     * 审核状态，1：待审核，2：待定，3：选中，4：审核通过，5： 审核不通过
     */
    private Integer auditStatus;

    /**
     * 是否原创歌手
     */
    private Boolean originalSinger;

    /**
     * 歌曲名称
     */
    private String songName;

    /**
     * 原创作品链接，是原创歌手才有
     */
    private String originalSongUrl;

    /**
     * 社交媒体认证图，是原创歌手才有，最多三张，多个逗号分隔
     */
    private String socialVerifyImage;

    /**
     * 歌曲风格
     */
    private String songStyle;

    /**
     * 1: 新锐歌手，2：优质歌手，3：明星歌手
     */
    private Integer singerType;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 音频文件地址
     */
    private String audioPath;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 实名认证证件号，不同证件类型号码长度不一样，不会冲突
     */
    private String idCardNumber;

    /**
     * 预审核不过原因
     */
    private String preAuditRejectReason;

    /**
     * 不通过原因
     */
    private String rejectReason;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否通过审核
     */
    private boolean hasPassVerify;

    /**
     * 是否在黑名单中
     */
    private boolean inBlackList;

    private String contactNumber;
}
