package fm.lizhi.ocean.wavecenter.service.message.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 上下架公告配置请求参数
 * <AUTHOR>
 */
@Data
public class WcNoticeUpdateStatusDTO {
    /**
     * 标题
     */
    @NotNull(message = "标题不能为空")
    private Long id;
    /**
     * 上下架状态
     */
    @NotNull(message = "状态")
    private Integer status;

    @NotNull(message = "操作人不能为空")
    private String operator;
} 