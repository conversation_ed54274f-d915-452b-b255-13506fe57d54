package fm.lizhi.ocean.wavecenter.service.live.config;

import com.alibaba.fastjson.JSONObject;
import fm.lizhi.ocean.lamp.common.config.annotation.JsonStringProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/10/27 10:19
 */
@Data
public class HyLiveConfig implements CommonLiveConfig {

    /**
     * 直播分类映射
     * key=业务品类值 value=平台品类值
     * @see fm.lizhi.ocean.wavecenter.api.live.constants.RoomCategoryEnum
     * @see fm.lizhi.live.room.hy.enums.LiveRoomType
     * {@code
     * {
     *     "100001":1
     * }
     * }
     */
    @JsonStringProperty
    private JSONObject categoryMapping = new JSONObject();

    /**
     * 打卡小秘书
     */
    private long checkInReportSender = 0L;

    /**
     * 打卡报告页URL
     */
    private String checkInReportUrl = "https://wavecenter-public.yfxn.lizhi.fm/static/heiye/index.html#/common/report/checkIn?dateType=$dateType&roomId=$roomId&startDate=$startDate&endDate=$endDate&appId=$appId&signCode=$signCode";


    private String checkInDetailUrl = "https://wavecenter-public.yfxn.lizhi.fm/static/heiye/index.html#/app/home";
}
