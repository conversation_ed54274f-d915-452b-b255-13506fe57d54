package fm.lizhi.ocean.wavecenter.service.activitycenter.constatns;

public class ActivityApplyErrorTipConstant {

    public static final String APPLY_ACTIVITY_NAME_INVALID = "活动名称不能超过%d个字";
    public static final String APPLY_ANNOUNCEMENT_INVALID = "房间公告不能超过%d个字";
    public static final String APPLY_ACTIVITY_GOAL_INVALID = "活动目标不能超过%d个字";
//    public static final String APPLY_ACTIVITY_INTRODUCTION_INVALID = "活动介绍不能超过%d个字";
    public static final String APPLY_ACTIVITY_AUXILIARY_PROP_INVALID = "活动辅助道具不能超过%d张";
    public static final String APPLY_ACTIVITY_ANNOUNCEMENT_URL_INVALID = "房间公告图片不能超过%d张";
    public static final String APPLY_ACTIVITY_ACCOMPANY_NJ_IDS_COUNT_INVALID = "房间陪档人数不能超过%d人";
    public static final String APPLY_ACTIVITY_TIME_INVALID = "活动开始时间要大于活动结束时间";
    public static final String APPLY_ACTIVITY_TIME_RANGE_INVALID = "活动开始时间要大于当前时间";
    public static final String APPLY_ACTIVITY_APPLY_PREACT_MIN_INVALID = "当前时间距离活动开始时间不足%d小时";
    public static final String APPLY_ACTIVITY_APPLY_PREACT_MAX_INVALID = "最多提前%d天提报活动";
    public static final String APPLY_ACTIVITY_APPLY_PERIOD_MAX_INVALID = "活动时长不能超过%d小时";
    public static final String APPLY_ACTIVITY_PROCESS_NAME_INVALID = "流程名称不能为空";
    public static final String APPLY_ACTIVITY_FLOW_RESOURCE_INVALID = "流量资源配置异常";
    public static final String APPLY_ACTIVITY_FLOW_RESOURCE_IMAGE_INVALID = "流量资源图片不能为空";
    public static final String APPLY_ACTIVITY_EXIST_INVALID = "活动时间范围内，存在本厅已提交活动";
    public static final String APPLY_ACTIVITY_REQ_TOO_FAST_INVALID = "重复请求，请确认是否已提交";
    public static final String APPLY_ACTIVITY_FAIL = "活动申请失败，请稍候重试";
    public static final String APPLY_ACTIVITY_FAMILY_INFO_INVALID = "没有家族信息，活动提交失败";
    public static final String APPLY_ACTIVITY_COUNT_INVALID = "您的签约直播间本周提报次数已满%d次，请下周再提报活动，详情请咨询平台运营。";
    public static final String APPLY_ACTIVITY_OFFICIAL_SEAT_INVALID = "官频位展示时间、官频位位置不能为空";
    public static final String APPLY_ACTIVITY_OFFICIAL_SEAT_TIME_INVALID = "官频位展示开始时间不能大于结束时间";
    public static final String APPLY_ACTIVITY_OFFICIAL_SEAT_TIME_RANG_INVALID = "官频位展示时间需要在活动举办时间范围内";
    public static final String APPLY_ACTIVITY_OFFICIAL_SEAT_COUNT_INVALID = "您选择的官频位时段（%s）已满，请选择其他时间。";
    public static final String APPLY_ACTIVITY_OFFICIAL_SEAT_INTERVAL_INVALID = "官频位时长需达到%d分钟";
    public static final String APPLY_ACTIVITY_OFFICIAL_SEAT_SEAT_INVALID = "官频位座位号必须从%s中选择";
    public static final String APPLY_ACTIVITY_AUDIT_DATA_INVALID = "房间公告存在敏感词汇，请重新修改";
    public static final String APPLY_ACTIVITY_PROCESS_EXPLANATION_INVALID = "流程说明不能超过%d个字";
    public static final String APPLY_ACTIVITY_CLASS_NOT_EXIST_INVALID = "分类信息异常，请刷新后重试";
    public static final String APPLY_ACTIVITY_RESOURCE_NOT_EXIST_INVALID = "当前活动等级下无资源配置，请刷新后重试";
    public static final String APPLY_ACTIVITY_RESOURCE_CONFIG_INVALID = "所选流量资源不属于当前活动等级资源，请刷新后重试";
    public static final String APPLY_ACTIVITY_RESOURCE_DISABLE_INVALID = "资源：%s 已被禁用，请刷新后重选模板";
    public static final String APPLY_ACTIVITY_MODIFY_REQ_TOO_FAST_INVALID = "重复请求，请确认是否已提交";
    public static final String APPLY_ACTIVITY_MODIFY_ACTIVITY_NOT_EXIST = "活动不存在";
    public static final String APPLY_ACTIVITY_MODIFY_ACTIVITY_NOT_AUDIT = "非待审核状态不允许修改";
    public static final String APPLY_ACTIVITY_NOT_MODIFY_ACTIVITY_BEFORE_START = "官方提报，活动开始前%d分钟不允许修改";
    public static final String APPLY_ACTIVITY_NOT_MODIFY_ACTIVITY_ALREADY_END = "活动已过期，无法进行修改";
    public static final String APPLY_ACTIVITY_MODIFY_VERSION_NOT_MATCH = "当前活动信息有变更，请重新提交";
    public static final String APPLY_ACTIVITY_AGREE_VERSION_NOT_MATCH = "当前活动信息有变更，请重新审批";
    public static final String APPLY_ACTIVITY_CANCEL_VERSION_NOT_MATCH = "当前活动信息有变更，请重新取消";
    public static final String APPLY_ACTIVITY_MODIFY_ERROR = "活动修改失败";
    public static final String APPLY_ACTIVITY_FLOW_RESOURCE_BANNER_SCALE_INVALID = "banner图的宽高比不能为空";
    public static final String APPLY_ACTIVITY_APPLY_BLACK_LIST = "您的签约直播间处于提报管控名单中，暂时无法提报活动，详情请咨询平台运营";
    public static final String APPLY_ACTIVITY_APPLY_INCOME_LIMIT = "您的签约直播间上周收入低于提报门槛要求，暂时无法提报活动，详情请咨询平台运营";
    public static final String APPLY_ACTIVITY_APPLY_CLASS_ERROR = "活动类型异常，请换个模板再进行操作";
    public static final String APPLY_ACTIVITY_APPLY_POSTER_URL_EMPTY = "您当前的活动分类属于厅战，海报不能为空";
    public static final String APPLY_ACTIVITY_MODIFY_ACTIVITY_CANCEL = "活动取消不允许修改";
    public static final String APPLY_ACTIVITY_TEMPLATE_DURATION_LIMIT = "活动时间不符合模板限时要求";
    public static final String APPLY_ACTIVITY_WHITELIST_ERROR = "当前用户不在模板白名单";
}
