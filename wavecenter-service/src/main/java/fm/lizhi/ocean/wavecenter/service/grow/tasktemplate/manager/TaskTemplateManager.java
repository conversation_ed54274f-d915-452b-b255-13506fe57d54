package fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.manager;

import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto.SaveTaskTemplateDTO;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto.TaskTemplateConditionDTO;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto.TaskTemplateQueryItemDTO;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto.TaskTemplateQueryResultDTO;

import java.util.List;

/**
 * 任务模版Manager接口
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
public interface TaskTemplateManager {

    /**
     * 保存任务模版
     *
     * @param saveTaskTemplateDTO 保存请求DTO
     * @param operator            操作人
     * @return 保存结果
     */
    Boolean saveTaskTemplate(SaveTaskTemplateDTO saveTaskTemplateDTO, String operator);

    /**
     * 能力项分页查询，返回主表数据和总条数
     *
     * @param capabilityCode 能力项code
     * @param appId          业务ID
     * @param pageNum        页码
     * @param pageSize       页面大小
     * @return 结果
     */
    TaskTemplateQueryResultDTO pageQueryTaskTemplateList(Integer appId, int pageNum, int pageSize);

      /**
     * 任务模版列表
     */
    List<TaskTemplateQueryItemDTO> queryTaskTemplateList(String capabilityCode, Integer appId);

    /**
     * 查询当前所有启用的任务模板
     * @return
     */
    List<TaskTemplateConditionDTO> queryEnableTaskTemplateConditionList();

    /**
     * 根据任务模版ID查询任务模版
     * @param templateIds
     * @return
     */
    List<TaskTemplateConditionDTO> queryTaskTemplateConditionByIds(List<Long> templateIds);

    /**
     * 根据ID逻辑删除任务模版
     *
     * @return 删除结果
     */
    Boolean deleteGrowTaskTemplate(List<Long> ids);
}