package fm.lizhi.ocean.wavecenter.service.anchor.singer.service;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerTypeInfo;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SongInfo;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SongStyleBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.*;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestSingerVerifyApply;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestSingerVerifyApplyV2;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseGetSingerEntranceInfo;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseGetSingerStatus;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.service.SingerVerifyService;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.RedisLock;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.config.SingerAnchorConfig;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.constant.SingerVerifyApplyErrorTipConstant;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.convert.SingerVerifyApplyConvert;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.*;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.handler.SingerPreAuditHandler;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.*;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.user.constants.SearchType;
import fm.lizhi.ocean.wavecenter.service.user.constants.VerifyStatusConstant;
import fm.lizhi.ocean.wavecenter.service.user.dto.SearchUserVerifyResultDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.SearchUserVerifyResultParamDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserVerifyResultDTO;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserVerifyManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.zookeeper.Op;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@ServiceProvider
public class SingerVerifyServiceImpl implements SingerVerifyService {

    @Autowired
    private SingerBlackListManager singerBlackListManager;

    @Autowired
    private SingerVerifyApplyManager singerVerifyApplyManager;

    @Autowired
    private SingerPreAuditHandler singerPreAuditHandler;

    @Autowired
    private FamilyManager familyManager;

    @Autowired
    private SingerInfoManager singerInfoManager;

    @Autowired
    private SingerAnchorConfig singerAnchorConfig;

    @Autowired
    private SingerRedisManager singerRedisManager;

    @Autowired
    private SingerChatManager singerChatManager;

    @Autowired
    private UserVerifyManager userVerifyManager;

    @Autowired
    private SingerAuditConfigManager singerAuditConfigManager;

    @Override
    public Result<Void> singerVerifyApply(RequestSingerVerifyApply request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);

        RequestSingerVerifyApplyV2 requestV2 = SingerVerifyApplyConvert.I.toRequestSingerVerifyApplyV2(request);
        return doSingerVerifyApply(requestV2);
    }

    @Override
    public Result<Void> singerVerifyApplyV2(RequestSingerVerifyApplyV2 request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);

        return doSingerVerifyApply(request);
    }

    @Override
    public Result<ResponseGetSingerStatus> getSingerStatus(Integer appId, Long userId) {
        LogContext.addReqLog("getSingerStatus, appId={}, userId={}", appId, userId);
        LogContext.addResLog("getSingerStatus={}, appId={}, userId={}", appId, userId);
        List<SingerVerifyRecordDTO> singerVerifyRecordList = singerVerifyApplyManager.getSingerVerifyRecordList(appId, userId);
        List<SingerStatusEnum> singerStatusEnums = Lists.newArrayList(SingerStatusEnum.EFFECTIVE, SingerStatusEnum.AUTHENTICATING);
        List<SingerInfoDTO> singerInfoList = singerInfoManager.getSingerInfoByUserId(appId, userId, singerStatusEnums);
        if (CollectionUtils.isEmpty(singerVerifyRecordList) && CollectionUtils.isEmpty(singerInfoList)) {
            return RpcResult.success(buildSingerApplyStatus(appId, -1, SingerApplyVerifyStatusEnum.UN_SUBMIT));
        }

        SingerApplyVerifyStatusEnum verifyStatus = SingerApplyVerifyStatusEnum.UN_SUBMIT;
        int currentSingerType = -1;
        //先查询当前的歌手类型
        if (CollectionUtils.isNotEmpty(singerInfoList)) {
            //一个用户存在多个歌手类型
            singerInfoList.sort(Comparator.comparing(SingerInfoDTO::getSingerType).reversed());
            SingerInfoDTO singerInfo = singerInfoList.get(0);
            //如果当前歌手状态是认证中并且歌手类型不是高级歌手或者当前歌手的状态是有效的，则直接返回当前歌手类型
            if ((singerInfo.getSingerStatus() == SingerStatusEnum.AUTHENTICATING.getStatus() && !SingerTypeEnum.isPrimaryType(singerInfo.getSingerType()))) {
                currentSingerType = SingerTypeEnum.getPreType(singerInfo.getSingerType());
            } else if (singerInfo.getSingerStatus() == SingerStatusEnum.EFFECTIVE.getStatus()) {
                currentSingerType = singerInfo.getSingerType();
            }
            verifyStatus = singerInfo.getSingerStatus() == SingerStatusEnum.EFFECTIVE.getStatus()
                    ? SingerApplyVerifyStatusEnum.PASSED : SingerApplyVerifyStatusEnum.SUBMITTED;
        }

        // 如果歌手库都没有，说明他的认证还没有通过
        if (CollectionUtils.isNotEmpty(singerVerifyRecordList)) {
            //过滤出singerVerifyRecordList中待审核、选中、待定的记录
            singerVerifyRecordList = singerVerifyRecordList.stream()
                    .filter(singerVerifyRecord -> SingerAuditStatusEnum.isWaitPass(singerVerifyRecord.getAuditStatus()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(singerVerifyRecordList)) {
                //再把审核中的记录按歌手类型排序，选择最大的歌手类型
                singerVerifyRecordList.sort(Comparator.comparing(SingerVerifyRecordDTO::getSingerType).reversed());
                SingerVerifyRecordDTO singerVerifyRecord = singerVerifyRecordList.get(0);
                verifyStatus = SingerAuditStatusEnum.isWaitPass(singerVerifyRecord.getAuditStatus()) ? SingerApplyVerifyStatusEnum.SUBMITTED : verifyStatus;
            }
        }

        return RpcResult.success(buildSingerApplyStatus(appId, currentSingerType, verifyStatus));
    }

    @Override
    public Result<ResponseGetSingerEntranceInfo> getSingerEntranceInfo(Integer appId, Long userId) {
        LogContext.addReqLog("getSingerEntranceInfo, appId={}, userId={}", appId, userId);
        LogContext.addResLog("getSingerEntranceInfo={}, appId={}, userId={}", appId, userId);
        Result<ResponseGetSingerStatus> result = this.getSingerStatus(appId, userId);
        if (RpcResult.isFail(result)) {
            return RpcResult.fail(result.rCode(), result.getMessage());
        }

        //初级歌手开关
        boolean newSingerTypeSwitch = singerAuditConfigManager.checkMenuConfig(singerAuditConfigManager.getApplyMenuConfig(appId, SingerTypeEnum.NEW.getType()), SingerTypeEnum.NEW);
        //高级歌手开关
        boolean qualitySingerTypeSwitch = singerAuditConfigManager.checkMenuConfig(singerAuditConfigManager.getApplyMenuConfig(appId, SingerTypeEnum.QUALITY.getType()), SingerTypeEnum.QUALITY);
        ResponseGetSingerStatus singerStatus = result.target();
        SingerTypeInfo currentSingerType = singerStatus.getCurrentSingerType();
        SingerTypeInfo nextSingerType = singerStatus.getNextSingerType();
        //当前是否是初级歌手
        boolean isNewSinger = currentSingerType != null && currentSingerType.getSingerType() == SingerTypeEnum.NEW.getType();
        //当前是否是高级歌手
        boolean isQualitySinger = currentSingerType != null && currentSingerType.getSingerType() >= SingerTypeEnum.QUALITY.getType();

        //当前歌手类型是否是初级歌手
        boolean nexIsNewSinger = nextSingerType != null && nextSingerType.getSingerType() == SingerTypeEnum.NEW.getType();
        //下一个歌手类型是否是高级歌手
        boolean nexIsQualitySinger = nextSingerType != null && nextSingerType.getSingerType() >= SingerTypeEnum.QUALITY.getType();
        boolean currentSingerEntranceSwitch = false;
        boolean nextSingerEntranceSwitch = false;

        // 当前歌手无等级，入口都开了
        if (currentSingerType == null && newSingerTypeSwitch) {
            nextSingerEntranceSwitch = true;
            log.info("isNewSinger.当前无等级，入口都开了, userId:{}", userId);
        } else if (isNewSinger && newSingerTypeSwitch && !qualitySingerTypeSwitch) {
            //当前是初级歌手，初级开关开了，高级开关关了，展示初级歌手入口，不展示高级歌手
            currentSingerEntranceSwitch = true;
            log.info("isNewSinger.当前是初级歌手，初级开关开了，高级开关关了，展示初级歌手入口，不展示高级歌手, userId:{}", userId);
        } else if (isNewSinger && !newSingerTypeSwitch && qualitySingerTypeSwitch) {
            // 当前是初级歌手，初级开关关了，高级开关开了，展示高级歌手入口，不展示初级级歌手
            nextSingerEntranceSwitch = true;
            log.info("isNewSinger.当前是初级歌手，初级开关关了，高级开关开了，展示高级歌手入口，不展示初级级歌手, userId:{}", userId);
        } else if (isNewSinger && newSingerTypeSwitch) {
            currentSingerEntranceSwitch = true;
            nextSingerEntranceSwitch = true;
            log.info("isNewSinger.当前是初级歌手，初级开关开了，高级开关开了，展示初级歌手入口，展示高级歌手入口, userId:{}", userId);
        } else if (isQualitySinger  && qualitySingerTypeSwitch) {
            currentSingerEntranceSwitch = true;
            log.info("isQualitySinger.当前是高级歌手，高级开关开着，展示高级歌手入口, userId:{}", userId);
        }


        ResponseGetSingerEntranceInfo responseGetSingerEntranceInfo = SingerVerifyApplyConvert.I.statusInfo2EntranceInfo(singerStatus, currentSingerEntranceSwitch, nextSingerEntranceSwitch);
        if (responseGetSingerEntranceInfo.getNextSingerEntranceInfo() != null) {
            responseGetSingerEntranceInfo.getNextSingerEntranceInfo().setEntranceShowSwitch(nextSingerEntranceSwitch);
        }
        if (responseGetSingerEntranceInfo.getCurrentSingerEntranceInfo() != null) {
            responseGetSingerEntranceInfo.getCurrentSingerEntranceInfo().setEntranceShowSwitch(currentSingerEntranceSwitch);
        }

        return RpcResult.success(responseGetSingerEntranceInfo);
    }

    /**
     * 构建歌手认证状态
     *
     * @param appId             应用ID
     * @param currentSingerType 当前的歌手类型
     * @param statusEnum        状态枚举
     * @return 歌手认证状态
     */
    public ResponseGetSingerStatus buildSingerApplyStatus(Integer appId, int currentSingerType, SingerApplyVerifyStatusEnum statusEnum) {
        // 如果未提交未审核，下一个就是歌手认证
        SingerTypeEnum nextSingerType = SingerTypeEnum.getNextType(currentSingerType);
        //当前认证中的权限
        SingerTypeEnum currentAuditType = SingerTypeEnum.getByType(currentSingerType);
        SingerTypeInfo nextSingerTypeInfo = null;
        SingerTypeInfo currentSingerTypeInfo = null;

        if (currentAuditType != null) {
            currentSingerTypeInfo = new SingerTypeInfo().setSingerType(currentSingerType)
                    .setSingerBizName(SingerTypeMappingEnum.getBizSingerType(BusinessEvnEnum.from(appId), currentAuditType));
        }

        String nextSingerTypeName = SingerTypeMappingEnum.getBizSingerType(BusinessEvnEnum.from(appId), nextSingerType);
        if (StringUtils.isNotEmpty(nextSingerTypeName)) {
            nextSingerTypeInfo = new SingerTypeInfo().setSingerType(nextSingerType.getType())
                    .setSingerBizName(nextSingerTypeName);
            if (currentAuditType != null && currentAuditType.isSenior()) {
                //如果当前已经是高级歌手了，下一个歌手类型信息就没有了
                nextSingerTypeInfo = null;
            }
        }

        // 构建返回结果
        return new ResponseGetSingerStatus()
                .setStatus(statusEnum.getStatus())
                .setMsg(statusEnum.getMsg())
                .setCurrentSingerType(currentSingerTypeInfo)
                .setNextSingerType(nextSingerTypeInfo);
    }


    /**
     * 校验歌手认证参数
     *
     * @param request 请求参数
     * @return 结果
     */
    private Result<Void> checkSingerVerifyParam(RequestSingerVerifyApplyV2 request) {
        // 如果是原唱歌手，则需要校验原唱歌曲链接和社交媒体认证图
        if (request.getOriginalSingerInfo() != null && request.getOriginalSingerInfo().getOriginalSinger()) {
            if (StringUtils.isEmpty(request.getOriginalSingerInfo().getOriginalSongUrl())) {
                return RpcResult.fail(CommonService.INTERNAL_ERROR, "原唱歌曲链接不能为空");
            }
            if (CollectionUtils.isEmpty(request.getOriginalSingerInfo().getSocialVerifyImageList())) {
                return RpcResult.fail(CommonService.INTERNAL_ERROR, "社交媒体认证图不能为空");
            }
            if (request.getOriginalSingerInfo().getSocialVerifyImageList().size() > 3) {
                return RpcResult.fail(CommonService.INTERNAL_ERROR, "社交媒体认证图不能超过3张");
            }
        }

        // 校验歌手类型是否是合法值
        if (!SingerTypeEnum.isValid(request.getSingerType())) {
            return RpcResult.fail(CommonService.INTERNAL_ERROR, "歌手类型不合法");
        }

        //校验曲风是否是对的
        String songStyleConfig = singerAnchorConfig.getBizConfig().getSongStyleConfig();
        List<SongStyleBean> songStyleBeans = JsonUtil.loadsArray(songStyleConfig, SongStyleBean.class);
        if (songStyleBeans == null || songStyleBeans.isEmpty()) {
            return RpcResult.fail(CommonService.INTERNAL_ERROR, "歌曲风格不合法");
        }
        songStyleBeans = songStyleBeans.stream().filter(SongStyleBean::isEnabled).collect(Collectors.toList());
        if (songStyleBeans.isEmpty()) {
            return RpcResult.fail(CommonService.INTERNAL_ERROR, "歌曲风格不合法");
        }
        for(SongInfo songInfo : request.getSongInfos()) {
            Optional<SongStyleBean> first = songStyleBeans.stream().filter(songStyleBean -> songStyleBean.getName().equals(songInfo.getSongStyle())).findFirst();
            if (!first.isPresent()) {
                return RpcResult.fail(CommonService.INTERNAL_ERROR, "歌曲风格不存在");
            }
        }

        return RpcResult.success();
    }

    /**
     * 预审核失败参数设置
     *
     * @param request 请求参数
     */
    private void setPreAuditFailedStatus(SingerVerifyApplyDTO singerVerifyApplyDTO, RequestSingerVerifyApplyV2 request) {

        for(SongInfoDTO songInfo : singerVerifyApplyDTO.getSongInfos()) {
            SingerPreAuditStatusEnum statusEnum = SingerPreAuditStatusEnum.getByStatus(songInfo.getPreAuditStatus());
            if (statusEnum != null && statusEnum.equals(SingerPreAuditStatusEnum.UNABLE_DETERMINE)) {
                singerVerifyApplyDTO.setAuditStatus(SingerAuditStatusEnum.WAIT_DECIDE.getStatus());
                singerVerifyApplyDTO.setPreAuditRejectReason("【预审核】：" + statusEnum.getMsg());
                songInfo.setPreAuditRejectReason("【预审核】：" + statusEnum.getMsg());
            } else if (statusEnum != null && (statusEnum.equals(SingerPreAuditStatusEnum.REJECT) || statusEnum.equals(SingerPreAuditStatusEnum.NOISE_TOO_LARGE))) {
                singerVerifyApplyDTO.setAuditStatus(SingerAuditStatusEnum.PRE_AUDIT_REJECTED.getStatus());
                singerVerifyApplyDTO.setPreAuditRejectReason("【预审核】：" + statusEnum.getMsg());
                songInfo.setPreAuditRejectReason("【预审核】：" + statusEnum.getMsg());
            }
        }
    }

    /**
     * 获取用户的实名认证结果
     *
     * @param userId 用户ID
     * @param appId  应用ID
     * @return 实名认证结果
     */
    private Optional<UserVerifyResultDTO> getVerifyResult(Long userId, Integer appId) {
        // 先查询这个用户的认证结果
        SearchUserVerifyResultParamDTO param = new SearchUserVerifyResultParamDTO()
                .setUserId(userId).setAppId(appId)
                .setAppId(appId)
                .setSearchType(SearchType.RESULT.getValue())
                .setVerifyStatus(VerifyStatusConstant.VERIFY_PASS);
        Result<SearchUserVerifyResultDTO> result = userVerifyManager.searchUserVerifyResult(param);
        if (RpcResult.noBusinessData(result) || CollectionUtils.isEmpty(result.target().getUserVerifyResultList())) {
            // 如果查询实名认证结果失败，则直接返回false
            log.warn("searchUserVerifyResult failed, rCode: {}, req: {}", result.rCode(), JsonUtil.dumps(param));
            return Optional.empty();
        }

        // 一般一个用户只有一条认证 通过的
        return Optional.of(result.target().getUserVerifyResultList().get(0));
    }

    private Result<Void> doSingerVerifyApply(RequestSingerVerifyApplyV2 request) {
        // 校验歌手认证参数
        Result<Void> checkRes = checkSingerVerifyParam(request);
        if (RpcResult.isFail(checkRes)) {
            return checkRes;
        }

        Optional<UserVerifyResultDTO> verifyResult = getVerifyResult(request.getUserId(), request.getAppId());
        if (!verifyResult.isPresent()) {
            log.warn("singerVerifyApply.userId={} is not verify", request.getUserId());
            return RpcResult.fail(SingerVerifyService.SINGER_VERIFY_APPLY_FAIL, SingerVerifyApplyErrorTipConstant.SINGER_VERIFY_APPLY_FAIL);
        }

        boolean menuConfig = singerAuditConfigManager.checkMenuConfig(request.getAppId(), request.getSingerType());
        if (!menuConfig) {
            log.warn("singerVerifyApply.userId={} singer type {} is not enabled", request.getUserId(), request.getSingerType());
            return RpcResult.fail(SingerVerifyService.SINGER_VERIFY_APPLY_MENU_IS_CLOSE_FAIL, SingerVerifyApplyErrorTipConstant.SINGER_VERIFY_APPLY_MENU_IS_CLOSE_FAIL);
        }

        try (RedisLock lock = singerRedisManager.tryGetSingerVerifyApplyLock(request.getAppId(), verifyResult.get().getIdCardNumber())) {
            if (!lock.tryLock()) {
                return RpcResult.fail(SingerVerifyService.SINGER_VERIFY_APPLY_TOO_FAST, SingerVerifyApplyErrorTipConstant.SINGER_VERIFY_APPLY_TOO_FAST);
            }

            // 校验是否被拉黑了
            Result<SingerInBlackResultDTO> isInBlackListRes = singerBlackListManager.isInBlackList(request.getAppId(), request.getUserId(), verifyResult.get().getIdCardNumber());
            if (RpcResult.isFail(isInBlackListRes)) {
                log.warn("singerVerifyApply.userId={} is in black list", request.getUserId());
                return RpcResult.fail(SingerVerifyService.SINGER_VERIFY_APPLY_FAIL, SingerVerifyApplyErrorTipConstant.SINGER_VERIFY_APPLY_FAIL);
            }
            //在黑名单中
            if (isInBlackListRes.target().isInBlackList()) {
                return RpcResult.fail(SingerVerifyService.SINGER_VERIFY_APPLY_IN_BLACK_LIST, SingerVerifyApplyErrorTipConstant.SINGER_VERIFY_APPLY_IN_BLACK_LIST);
            }

            //校验是否存在审核中的记录
            Pair<Boolean, String> canApplyRes = singerVerifyApplyManager.checkAuditRecordCanApply(request.getAppId(), request.getUserId(), request.getSingerType());
            if (!canApplyRes.getLeft()) {
                log.warn("singerVerifyApply.userId={} exist auditing record", request.getUserId());
                return RpcResult.fail(SingerVerifyService.SINGER_VERIFY_APPLY_EXIST_AUDITING_RECORD, canApplyRes.getRight());
            }

            //校验是否可以申请当前歌手类型
            boolean canApplySingerVerify = singerInfoManager.isCanApplySingerVerify(request.getAppId(), request.getUserId(), request.getSingerType());
            if (!canApplySingerVerify) {
                String bizSingerName = SingerTypeMappingEnum.getBizSingerType(BusinessEvnEnum.from(request.getAppId()), SingerTypeEnum.getByType(request.getSingerType()));
                String msg = String.format(SingerVerifyApplyErrorTipConstant.SINGER_VERIFY_APPLY_ALREADY_SINGER, bizSingerName);
                log.warn("singerVerifyApply.already singer, userId={},bizSingerName={}", request.getUserId(), bizSingerName);
                return RpcResult.fail(SingerVerifyService.SINGER_VERIFY_APPLY_ALREADY_SINGER, msg);
            }

            // 校验用户的实名信息关联账号是否存在歌手库信息或者认证信息
            Boolean existUserVerify = singerVerifyApplyManager.checkUserVerify(request.getAppId(), verifyResult.get().getIdCardNumber(), request.getUserId(), null);
            if (existUserVerify) {
                log.warn("singerVerifyApply.userId={} exist user verify", request.getUserId());
                return RpcResult.fail(SingerVerifyService.SINGER_VERIFY_APPLY_FAIL, SingerVerifyApplyErrorTipConstant.SINGER_VERIFY_APPLY_HAS_OTHER_VERIFY);
            }

            // 过滤器
            Result<Void> preAuditRes = singerPreAuditHandler.preAuditHandle(request);
            if (RpcResult.isFail(preAuditRes)) {
                // 预审核失败，直接返回
                return RpcResult.fail(SingerVerifyService.SINGER_VERIFY_APPLY_PRE_AUDIT_NO_PASS, preAuditRes.getMessage());
            }
            //查询用户所在的家族相关信息
            UserInFamilyBean userInFamily = familyManager.getUserInFamily(request.getUserId());
            if (userInFamily == null || userInFamily.getNjId() == null) {
                log.warn("singerVerifyApply.userId={} is not in nj", request.getUserId());
                return RpcResult.fail(SingerVerifyService.SINGER_VERIFY_APPLY_FAIL, SingerVerifyApplyErrorTipConstant.SINGER_VERIFY_APPLY_FAIL);
            }
            SingerVerifyApplyDTO req2Dto = SingerVerifyApplyConvert.I.req2Dto(request, userInFamily, verifyResult.get().getIdCardNumber());
            // 设置预审核失败状态
            setPreAuditFailedStatus(req2Dto, request);
            // 保存歌手认证申请数据
            boolean res = singerVerifyApplyManager.saveSingerVerifyApply(req2Dto);
            if (res && req2Dto.getAuditStatus() == SingerAuditStatusEnum.PRE_AUDIT_REJECTED.getStatus()) {
                String songStyle = buildSongStyle(req2Dto.getSongInfos());
                //发送私信
                singerChatManager.sendAuditResultChat(req2Dto.getAppId(), req2Dto.getUserId(), req2Dto.getSingerType(), songStyle, SingerChatSceneEnum.PRE_AUDIT_NOT_PASS_BY_AUDIO);
            }
            return res ? RpcResult.success() : RpcResult.fail(SingerVerifyService.SINGER_VERIFY_APPLY_FAIL, SingerVerifyApplyErrorTipConstant.SINGER_VERIFY_APPLY_FAIL);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private String buildSongStyle(List<SongInfoDTO> songInfos) {
        return songInfos.stream().map(SongInfoDTO::getSongStyle).collect(Collectors.joining(","));
    }


}