package fm.lizhi.ocean.wavecenter.service.award.singer.manager;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.bean.SingerDecorateRuleBean;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestPageSingerDecorateRule;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestSaveSingerDecorateRule;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestUpdateSingerDecorateRule;
import fm.lizhi.ocean.wavecenter.common.dto.PageDto;

import java.util.List;

/**
 * 歌手装扮规则配置
 * <AUTHOR>
 */
public interface SingerDecorateRuleManager {


    /**
     * 分页获取歌手装扮规则配置列表
     */
    PageDto<SingerDecorateRuleBean> pageSingerDecorateRule(RequestPageSingerDecorateRule request);


    /**
     * 保存 歌手装扮规则配置
     */
    Boolean saveSingerDecorateRule(RequestSaveSingerDecorateRule request);

    /**
     * 更新 歌手装扮规则配置
     */
    Boolean updateSingerDecorateRule(RequestUpdateSingerDecorateRule request);


    /**
     * 获取歌手装扮规则配置
     */
    List<SingerDecorateRuleBean> getSingerDecorateRule(int appId, SingerTypeEnum singerType, String songStyle, boolean originalSinger);
}
