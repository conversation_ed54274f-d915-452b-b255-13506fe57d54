package fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.convert;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.bean.ConditionGroupBean;
import fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.bean.TaskTemplateQueryBean;
import fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.request.RequestQueryTaskTemplate;
import fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.request.RequestSaveTaskTemplate;
import fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.response.ResponseQueryTaskTemplate;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto.QueryTaskTemplateDTO;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto.SaveTaskTemplateDTO;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto.TaskTemplateQueryItemDTO;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto.TaskTemplateQueryResultDTO;

import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 任务模版Service层转换器
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TaskTemplateServiceConvert {

    TaskTemplateServiceConvert I = Mappers.getMapper(TaskTemplateServiceConvert.class);
    ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 请求转DTO
     *
     * @param request 请求
     * @return SaveTaskTemplateDTO
     */
    SaveTaskTemplateDTO requestToDto(RequestSaveTaskTemplate request);

    /**
     * 查询请求转DTO
     *
     * @param request 查询请求
     * @return QueryTaskTemplateDTO
     */
    QueryTaskTemplateDTO requestToQueryDTO(RequestQueryTaskTemplate request);

    /**
     * 查询结果DTO转响应
     *
     * @param resultDTO 查询结果DTO
     * @return ResponseQueryTaskTemplate
     */
    default ResponseQueryTaskTemplate queryResultToResponse(TaskTemplateQueryResultDTO resultDTO) {
        if (resultDTO == null) {
            return null;
        }

        // 转换列表项
        List<TaskTemplateQueryBean> queryBeanList = null;
        if (resultDTO.getList() != null) {
            queryBeanList = resultDTO.getList().stream()
                    .map(this::convertToQueryBean)
                    .collect(Collectors.toList());
        }

        return ResponseQueryTaskTemplate.builder()
                .list(queryBeanList)
                .total(resultDTO.getTotal())
                .build();
    }

    List<TaskTemplateQueryBean> queryItemListToResponse(List<TaskTemplateQueryItemDTO> resultDTO);

    /**
     * 转换查询结果项为Bean
     *
     * @param item 查询结果项
     * @return TaskTemplateQueryBean
     */
    default TaskTemplateQueryBean convertToQueryBean(TaskTemplateQueryItemDTO item) {
        if (item == null) {
            return null;
        }

        // 解析条件JSON
        ConditionGroupBean conditionGroup = null;
        try {
            if (item.getConditionJson() != null) {
                conditionGroup = JsonUtil.loads(item.getConditionJson(), ConditionGroupBean.class);
            }
        } catch (Exception e) {
            // 忽略JSON解析错误
        }

        return TaskTemplateQueryBean.builder()
                .id(item.getId())
                .capabilityCode(item.getCapabilityCode())
                .capabilityScore(item.getCapabilityScore())
                .conditionGroup(conditionGroup)
                .capabilityName(item.getCapabilityName())
                .build();
    }
}