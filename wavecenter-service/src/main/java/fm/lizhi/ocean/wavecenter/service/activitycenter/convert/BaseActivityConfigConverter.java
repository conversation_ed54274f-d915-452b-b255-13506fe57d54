package fm.lizhi.ocean.wavecenter.service.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseGetBaseActivityConfig;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.CommonActivityConfig;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface BaseActivityConfigConverter {

    /**
     * 将通用活动配置转换为获取基础活动配置响应
     *
     * @param commonActivityConfig 通用活动配置
     * @return 获取基础活动配置响应
     */
    ResponseGetBaseActivityConfig toResponseGetBaseActivityConfig(CommonActivityConfig commonActivityConfig);
}
