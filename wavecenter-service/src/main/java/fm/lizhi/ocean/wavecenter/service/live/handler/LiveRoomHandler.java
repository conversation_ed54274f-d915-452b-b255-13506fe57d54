package fm.lizhi.ocean.wavecenter.service.live.handler;

import fm.lizhi.ocean.wavecenter.api.live.constants.RoomCategoryEnum;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.service.live.manager.LiveRoomManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/5/9 15:30
 */
@Component
public class LiveRoomHandler {

    @Autowired
    private LiveRoomManager liveRoomManager;

    @Autowired
    private FamilyManager familyManager;

    /**
     * 查询用户签约厅品类
     * @param userId
     * @return
     */
    public Optional<RoomCategoryEnum> getUserSignRoomCategory(Long userId) {
        UserInFamilyBean userInFamily = familyManager.getUserInFamily(userId);
        Long njId = userInFamily.getNjId();
        if (njId == null) {
            return Optional.empty();
        }

        return liveRoomManager.getUserRoomCategory(njId);
    }

}
