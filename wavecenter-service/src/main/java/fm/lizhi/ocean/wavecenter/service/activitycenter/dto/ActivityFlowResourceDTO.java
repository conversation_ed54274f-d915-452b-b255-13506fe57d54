package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 活动提报流量资源信息
 */
@Data
@Accessors(chain = true)
public class ActivityFlowResourceDTO {

    /**
     * 流量资源申请ID
     */
    private Long id;

    private Long activityId;

    /**
     *
     */
    private Long resourceConfigId;

    /**
     * 图片地址
     */
    private String imageUrl;

    /**
     * 资源额外信息
     */
    private String extra;

    /**
     * 状态，0：待审核，1：不发放，2：可发放
     */
    private Integer status;
}
