package fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityLevelConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityLevel;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityLevel;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ActivityLevelManager {

    /**
     * 保存
     */
    Result<Boolean> saveLevel(RequestSaveActivityLevel param);

    /**
     * 更新
     */
    Result<Boolean>  updateLevel(RequestUpdateActivityLevel param);

    /**
     * 删除
     */
    Result<Boolean> deleteLevel(Long id, String operator);


    /**
     * 查询列表
     */
    List<ActivityLevelConfigBean> listByAppId(Integer appId);

    /**
     * 批量查询等级
     */
    List<ActivityLevelConfigBean> listByAppIdAndLevelIds(Integer appId, List<Long> levelIds);

    /**
     * 批量检查活动等级是否存在
     * @return 返回不存在的活动等级ID
     */
    List<Long> batchExistLevel(Integer appId, List<Long> levelIds);

    /**
     * 检查等级是否存在
     */
    Boolean existLevelById(Integer appId, Long levelId);


    /**
     * 根据 ID 查等级
     */
    ActivityLevelConfigBean getLevelById(Integer appId, Long levelId);

    /**
     * 根据分类 ID 查询等级
     */
    ActivityLevelConfigBean getLevelByClassId(Integer appId, Long classId);
}
