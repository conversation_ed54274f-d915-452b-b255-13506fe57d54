package fm.lizhi.ocean.wavecenter.service.sign.manager;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.PlayerSignBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.QueryGuildPlayerBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.RoomSignBean;
import fm.lizhi.ocean.wavecenter.common.dto.PageDto;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/4/10 14:33
 */
public interface RoomManager {


    /**
     * 获取所有签约厅
     * @param familyId
     * @param pageNo
     * @param pageSize
     * @return
     */
    PageBean<RoomSignBean> getAllSingGuildRooms(long familyId, int pageNo, int pageSize);

    /**
     * 获取所有的厅信息 （签约+非签约 ）
     * @param familyId
     * @param pageNo
     * @param pageSize
     * @return
     */
    PageBean<RoomSignBean> getAllGuildRooms(long familyId, List<Long> roomIds, int pageNo, int pageSize);


    /**
     * 厅下面的所有主播
     *
     * @param roomId
     * @param pageNo
     * @param pageSize
     * @return
     */
    PageBean<PlayerSignBean> getAllRoomPlayers(long roomId, int pageNo, int pageSize);

    /**
     * 查询厅下所有签约主播ID列表
     * @param roomId
     * @return
     */
    List<Long> getAllSignRoomPlayerIds(long roomId);

    /**
     * 获取工会下面的所有主播id
     *
     * @param req
     * @param pageNo
     * @param pageSize
     * @return
     */
    PageDto<PlayerSignBean> getAllGuildPlayer(QueryGuildPlayerBean req, int pageNo, int pageSize);

    /**
     * 获取陪玩签约信息
     * @param userId
     * @return
     */
    Optional<PlayerSignBean> getPlayerSignInfo(Long familyId, Long roomId, long userId);

    /**
     * 公会下所有厅
     * @param familyId
     * @return
     */
    List<Long> getFamilyAllNjId(Long familyId);



}
