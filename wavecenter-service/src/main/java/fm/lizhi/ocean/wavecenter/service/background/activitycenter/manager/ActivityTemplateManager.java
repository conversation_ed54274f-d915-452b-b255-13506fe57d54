package fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityTemplateGeneralPageBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityTemplateHotPageBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityTemplateInfoBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityTemplateStatusEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestCountGeneralActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestPageGeneralActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestPageHotActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseGetGeneralActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplatePageBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.*;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseGetActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseGetActivityTemplateShelfStatus;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityTemplateFlowResourceDTO;

import java.util.List;

/**
 * 活动模板管理器接口
 */
public interface ActivityTemplateManager {

    /**
     * 创建活动模板
     *
     * @param req 请求
     * @return 创建的活动模板id
     */
    Result<Long> createTemplate(RequestCreateActivityTemplate req);

    /**
     * 更新活动模板
     *
     * @param req 请求
     * @return 结果
     */
    Result<Void> updateTemplate(RequestUpdateActivityTemplate req);

    /**
     * 删除活动模板
     *
     * @param req 请求
     * @return 结果
     */
    Result<Void> deleteTemplate(RequestDeleteActivityTemplate req);

    /**
     * 更新活动模板上下架状态
     *
     * @param req 请求
     * @return 结果
     */
    Result<Void> updateShelfStatus(RequestUpdateActivityTemplateShelfStatus req);

    /**
     * 更新模板状态
     * @param templateId
     * @param status
     */
    void updateStatus(Long templateId, ActivityTemplateStatusEnum status);

    /**
     * 获取活动模板上下架状态
     *
     * @param templateId 活动模板id
     * @return 结果
     */
    Result<ResponseGetActivityTemplateShelfStatus> getShelfStatus(long templateId);

    /**
     * 分页查询活动模板
     *
     * @param req 请求
     * @return 结果
     */
    Result<PageBean<ActivityTemplatePageBean>> pageTemplate(RequestPageActivityTemplate req);

    /**
     * 获取活动模板
     *
     * @param templateId 活动模板id
     * @return 结果
     */
    Result<ResponseGetActivityTemplate> getTemplate(long templateId);

    /**
     * 分页查询热门活动模板, 用于web站
     *
     * @param req 请求参数
     * @return 响应结果
     */
    Result<PageBean<ActivityTemplateHotPageBean>> pageHotTemplate(RequestPageHotActivityTemplate req, List<Long> njList);

    /**
     * 分页查询通用活动模板, 用于web站
     *
     * @param req 请求参数
     * @return 响应结果
     */
    Result<PageBean<ActivityTemplateGeneralPageBean>> pageGeneralTemplate(RequestPageGeneralActivityTemplate req);

    /**
     * 获取通用活动模板详情, 用于web站
     *
     * @param templateId 模板id
     * @return 响应结果
     */
    Result<ResponseGetGeneralActivityTemplate> getGeneralTemplate(long templateId);

    /**
     * 获取通用活动模板数量, 用于web站
     *
     * @param req 请求参数
     * @return 响应结果
     */
    Result<Long> countGeneralTemplate(RequestCountGeneralActivityTemplate req);

    /**
     * 获取活动模板信息
     *
     * @param templateId 活动模板id
     * @return 活动模板信息
     */
    ActivityTemplateInfoBean getTemplateInfoBean(Long templateId);

    /**
     * 获取活动模板官频位配置信息
     *
     * @param templateId 活动模板id
     * @return 活动模板官频位配置信息
     */
    ActivityTemplateFlowResourceDTO getTemplateOfficialSeat(long templateId);

    /**
     * 查询模板的厅主白名单
     * @param templateId
     * @return
     */
    List<Long> getTemplateNjWhitelist(long templateId);

    /**
     * 获取活动模板节目单配置信息
     *
     * @param templateId 活动模板id
     * @return 活动模板节目配置信息
     */
    ActivityTemplateFlowResourceDTO getTemplateProgramme(long templateId);

}
