package fm.lizhi.ocean.wavecenter.service.activitycenter.config;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.utils.PlaceholderUtils;
import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Data
public class PpActivityConfig implements CommonActivityConfig {

    /**
     * banner图跳转直播间配置
     */
    private String bannerActionJson = "{\"type\":16,\"userId\":\"0\",\"id\":\"0\"}";

    /**
     * 板块ID
     */
    private String bannerPlateId = "2:10010";

    /**
     * 板块名称
     */
    private String bannerPlateName = "进房玩-点唱-1";

    /**
     * 板块分类ID
     */
    private Integer plateTypeId = 2;

    /**
     * banner所在排序
     */
    private Integer bannerPlateIndex = 1;

    /**
     * 官频位模板ID
     */
    private Integer officialSeatTemplateId = 2;

    /**
     * 官频位跳转直播间配置
     */
    private String officialSeatActionJson = "{\"type\":16,\"userId\":\"0\"}";

    /**
     * 官方联系方式
     */
    private String officialContractNumber = "";

    /**
     * 官方联系人
     */
    private String officialContract = "";

    /**
     * 资源转存路径
     */
    private String resourceTransferUrlPrefix = "/publicTransfer/${businessName}/${fileName}";

    /**
     * 最少提前多少分钟申请活动
     */
    private Integer minApplyPreactMin = 10;

    /**
     * 官方提报，活动开始前N分钟不允许修改
     */
    private Integer officialModifyActivityBeforeStartMin = 60;

    /**
     * 最大提前几天申请活动
     */
    private Integer maxPreactApplyDay = 7;

    /**
     * 活动最大举办分钟数
     */
    private Integer maxActivityPeriodMin = 360;

    /**
     * 是否自动发放Avatar
     */
    private boolean autoSendAvatar = false;

    /**
     * 官频位时长限制列表, 单位为分钟
     */
    private List<Integer> officialSeatDurationLimits = Arrays.asList(30, 60, 90, 120, 150, 180, 210, 240, 270, 300, 330, 360);

    /**
     * 官频位可选座位号列表
     */
    private List<Integer> officialSeatAvailableNumbers = Arrays.asList(1, 2, 3, 4);

    /**
     * 官频位默认选中的座位号列表, 必须是可选座位号列表的子集
     */
    private List<Integer> officialSeatDefaultNumbers = Collections.singletonList(2);

    /**
     * web站域名
     */
    private String waveCenterDomain = "https://wavecenter-public.yfxn.lizhi.fm";

     /**
     * web站活动记录链接
     */
    private String webActivityRecordUrl = "/static/pplive/index.html#/app/action";

    private List<Integer> supportRoomCategoryValues = new ArrayList<>();

    /**
     * 是否支持查询时间表节目单资源
     */
    private boolean supportProgramResourceTime = true;

    @Override
    public String getResourceTransferUrlPrefix(String fileName) {
        return PlaceholderUtils.replace(
                resourceTransferUrlPrefix,
                "businessName", BusinessEvnEnum.PP.getName(),
                "fileName", fileName
        );
    }
}
