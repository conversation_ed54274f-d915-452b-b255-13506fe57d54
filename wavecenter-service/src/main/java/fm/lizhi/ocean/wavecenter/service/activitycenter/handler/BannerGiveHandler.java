package fm.lizhi.ocean.wavecenter.service.activitycenter.handler;

import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.BannerExtraBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.AutoConfigResourceEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ResourceExtraMapping;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.ActivityConfig;
import fm.lizhi.ocean.wavecenter.service.activitycenter.constatns.ResourceGiveErrorTipConstant;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.*;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityMaterielManager;
import fm.lizhi.ocean.wavecenter.service.common.manager.IdManager;
import fm.lizhi.ocean.wavecenter.service.live.manager.LiveManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * banner发放处理器
 */
@Slf4j
@Component
public class BannerGiveHandler implements FlowResourceGiveHandler {

    @Autowired
    private ActivityMaterielManager activityMaterielManager;

    @Autowired
    private ActivityConfig activityConfig;

    @Autowired
    private LiveManager liveManager;

    @Override
    public Result<GiveFlowResourceResDTO> giveFlowResource(FlowResourceContext context) {
        try {
            ActivityFlowResourceGiveDTO flowResourceGiveDTO = context.getFlowResourceGiveDTO();
            ActivityResourceGiveDTO resourceGiveDTO = context.getResourceGiveDTO();

            Optional<Long> liveIdOption = liveManager.getLatestLiveIdByUserId(flowResourceGiveDTO.getUserId());
            if (!liveIdOption.isPresent() || liveIdOption.get() <= 0) {
                log.warn("BannerGiveHandler.giveFlowResource fail, activityId:{}", resourceGiveDTO.getActivityId());
                return RpcResult.fail(GIVE_FLOW_RESOURCE_FAIL, ResourceGiveErrorTipConstant.BANNER_GIVE_NJ_NOT_LIVE);
            }

            String extra = flowResourceGiveDTO.getExtra();
            BannerExtraBean bean = ResourceExtraMapping.convertJsonToExtra(extra, AutoConfigResourceEnum.BANNER.getResourceCode());
            if (extra == null || bean == null) {
                log.warn("BannerGiveHandler.buildBannerParamDTO fail, activityId:{}, resourceId:{}, extra:{}", resourceGiveDTO.getActivityId(), resourceGiveDTO.getResourceId(), extra);
                return RpcResult.fail(GIVE_FLOW_RESOURCE_FAIL, ResourceGiveErrorTipConstant.BANNER_GIVE_CONFIG_ERROR);
            }

            EditBannerParamDTO editBannerParamDTO = buildBannerParamDTO(context, liveIdOption.get(), bean.getScale());
            if (editBannerParamDTO == null) {
                return RpcResult.fail(GIVE_FLOW_RESOURCE_FAIL, ResourceGiveErrorTipConstant.BANNER_GIVE_CONFIG_ERROR);
            }

            log.info("BannerGiveHandler.giveFlowResource param:{}", editBannerParamDTO);
            Result<Long> result = activityMaterielManager.editBannerConfig(editBannerParamDTO);
            return result.rCode() == 0 ? RpcResult.success(new GiveFlowResourceResDTO().setBizRecordId(result.target())) : RpcResult.fail(GIVE_FLOW_RESOURCE_FAIL, ResourceGiveErrorTipConstant.BANNER_GIVE_FAIL);
        } catch (Exception e) {
            log.error("BannerGiveHandler.giveFlowResource happen error: context={}", JSONObject.toJSONString(context), e);
            return RpcResult.fail(GIVE_FLOW_RESOURCE_FAIL, ResourceGiveErrorTipConstant.BANNER_GIVE_FAIL);
        }
    }

    @Override
    public Result<Void> cancelGiveFlowResource(DeleteOfficialSeatParamDTO param) {
        DeleteBannerParamDTO paramDTO = new DeleteBannerParamDTO();
        paramDTO.setId(param.getBizRecordId());
        return activityMaterielManager.deleteBannerConfig(paramDTO);
    }

    @Override
    public String getResourceCode() {
        return AutoConfigResourceEnum.BANNER.getResourceCode();
    }

    /**
     * 构建banner参数
     *
     * @param context 请求上下文
     * @param liveId  直播间id
     * @param scale   宽高比
     * @return banner参数
     */
    private EditBannerParamDTO buildBannerParamDTO(FlowResourceContext context, Long liveId, String scale) {
        ActivityFlowResourceGiveDTO flowResourceGiveDTO = context.getFlowResourceGiveDTO();
        ActivityResourceGiveDTO resourceGiveDTO = context.getResourceGiveDTO();
        String bannerActionLiveJson = activityConfig.getBizConfig().getBannerActionJson();
        JSONObject action = JSONObject.parseObject(bannerActionLiveJson);
        action.put("userId", String.valueOf(flowResourceGiveDTO.getUserId()));
        action.put("id", String.valueOf(liveId));

        BannerPlateDTO bannerPlateDTO = new BannerPlateDTO()
                .setBannerPlateId(activityConfig.getBizConfig().getBannerPlateId())
                .setBannerPlateName(activityConfig.getBizConfig().getBannerPlateName())
                .setPlateTypeId(Long.valueOf(activityConfig.getBizConfig().getPlateTypeId()))
                .setIndex(activityConfig.getBizConfig().getBannerPlateIndex());

        flowResourceGiveDTO.setImageUrl(flowResourceGiveDTO.getImageUrl() == null ? "" : StringUtils.removeStart(flowResourceGiveDTO.getImageUrl(), "/"));
        return new EditBannerParamDTO()
                .setTitle(resourceGiveDTO.getActivityName())
                .setImgUrl(flowResourceGiveDTO.getImageUrl())
                .setScale(scale)
                .setAction(JSONObject.toJSONString(action))
                .setBannerPlateDTO(bannerPlateDTO)
                .setSeq(activityConfig.getBannerSeq())
                .setStartTime(resourceGiveDTO.getStartTime().getTime())
                .setExpireTime(resourceGiveDTO.getEndTime().getTime());
    }

}
