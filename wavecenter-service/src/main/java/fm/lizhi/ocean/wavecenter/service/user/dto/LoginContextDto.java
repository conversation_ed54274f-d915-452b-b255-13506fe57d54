package fm.lizhi.ocean.wavecenter.service.user.dto;

import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/4/12 16:48
 */
@Getter
@Builder
public class LoginContextDto {

    /**
     * 是否为注册
     */
    private Boolean register;
    /**
     * 应用id
     */
    private String appId;
    /**
     * 子应用id
     */
    private String subAppId;
    /**
     * 设备id
     */
    private String deviceId;
    /**
     * 设备类型
     */
    private String deviceType;
    /**
     * 客户端ip
     */
    private String clientIp;
    /**
     * 客户端版本号
     */
    private String clientVersion;
    /**
     * 客户端渠道id
     */
    private String channelId;
    /**
     * 数美id
     */
    private String smId;

    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户手机号
     */
    private String phoneNum;
    /**
     * 网络
     */
    private String network;
    /**
     * 鉴权账户id
     */
    private Long authAccountId;
    /**
     * 风控appId
     */
    private String riskAppId;

}
