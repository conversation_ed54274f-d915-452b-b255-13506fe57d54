package fm.lizhi.ocean.wavecenter.service.anchor.singer.service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerHallApplySourceEnum;
import org.springframework.beans.factory.annotation.Autowired;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import org.apache.commons.lang3.StringUtils;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerSingHallApplyRecordBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerSingHallApplyRecordSummaryBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerHallApplyStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestImportHallApply;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestOperateHallApply;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestPageHallApply;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponsePageHallApply;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.service.SingerHallApplyService;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.dto.PageDto;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.convert.SingerHallApplyConvert;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.PageHallApplyParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerHallApplyManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerInfoManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;


/**
 * 点唱厅管理
 * <AUTHOR>
 */
@Slf4j
@ServiceProvider
public class SingerHallApplyServiceImpl implements SingerHallApplyService {

    @Autowired
    private SingerHallApplyManager singerHallApplyManager;

    @Autowired
    private UserManager userManager;

    @Autowired
    private FamilyManager familyManager;


    @Override
    public Result<ResponsePageHallApply> pageHallApplyList(RequestPageHallApply request) {
        LogContext.addReqLog("request:{}", request);
        LogContext.addResLog("request:{}", request);

        // 处理njBand查询
        if (StringUtils.isNotEmpty(request.getNjBand())) {
            Long njId = userManager.getUserIdByBand(request.getNjBand());
            request.setNjId(njId == null ? 1 : njId);
        }

        PageHallApplyParamDTO param = SingerHallApplyConvert.I.convertPageHallApplyParamDTO(request);
        PageDto<SingerSingHallApplyRecordSummaryBean> pageDto = singerHallApplyManager.pageHallApplyList(param, request.getPageNo(), request.getPageSize());
        ResponsePageHallApply response = SingerHallApplyConvert.I.convertResponsePageHallApply(pageDto);
        if (null == response || CollUtil.isEmpty(response.getList())){
            return RpcResult.success();
        }

        List<Long> userIds = pageDto.getList().stream().map(SingerSingHallApplyRecordSummaryBean::getNjId).collect(Collectors.toList());
        Map<Long, SimpleUserDto> userMapByIds = userManager.getSimpleUserMapByIds(userIds);

        response.getList().forEach(bean -> {
            bean.setNjInfo(MapUtil.get(userMapByIds, bean.getNjId(), UserBean.class));
            Optional<FamilyBean> family = familyManager.getFamilyByCache(bean.getFamilyId());
            family.ifPresent(bean::setFamilyInfo);
        });

        return RpcResult.success(response);
    }

    @Override
    public Result<List<Long>> importHallApply(RequestImportHallApply request) {
        LogContext.addReqLog("request:{}", request);
        LogContext.addResLog("request:{}", request);

        if (CollUtil.isEmpty(request.getNjIds())){
            log.info("hall apply record njIds is empty");
            return RpcResult.fail(SingerHallApplyService.IMPORT_HALL_APPLY_FAIL, "ID不能为空");
        }

        // 去重
        request.setNjIds(CollUtil.distinct(request.getNjIds()));

        List<SimpleUserDto> njUserInfoList = userManager.getSimpleUserByIds(request.getNjIds());
        if (CollUtil.isEmpty(njUserInfoList) || njUserInfoList.size() != request.getNjIds().size()){
            log.warn("nj user list is empty or size not match,njUserSize:{}, njIdSize: {}", njUserInfoList.size(), request.getNjIds().size());
            return RpcResult.fail(SingerHallApplyService.IMPORT_HALL_APPLY_FAIL, "存在非法厅ID");
        }


        List<SingerSingHallApplyRecordBean> existRecordList =
                singerHallApplyManager.getSingerHallApplyRecordByNjIdsAndAppId(request.getNjIds(), request.getAppId());

        List<Long> existNjIds = existRecordList.stream().map(SingerSingHallApplyRecordBean::getNjId).collect(Collectors.toList());
        Collection<Long> insertNjIds = CollUtil.disjunction(existNjIds, request.getNjIds());
        if (CollUtil.isEmpty(insertNjIds)){
            log.info("hall apply record already exist, roomIds: {}", existNjIds);
            return RpcResult.success(existNjIds);
        }

        request.setNjIds(new ArrayList<>(insertNjIds));
        request.setSource(SingerHallApplySourceEnum.WAVE_CENTER);
        boolean success = singerHallApplyManager.importHallApply(request);
        return success ? RpcResult.success(existNjIds) : RpcResult.fail(SingerHallApplyService.IMPORT_HALL_APPLY_FAIL, "导入失败");
    }

    @Override
    public Result<Void> operateHallApply(RequestOperateHallApply request) {
        LogContext.addReqLog("request:{}", request);
        LogContext.addResLog("request:{}", request);

        Optional<SingerSingHallApplyRecordBean> applyRecordBean;
        if (SingerHallApplyStatusEnum.APPLYED.equals(request.getStatus())){
            applyRecordBean = singerHallApplyManager.passHallApply(request);
        }else if (SingerHallApplyStatusEnum.REJECTED.equals(request.getStatus())){
            applyRecordBean = singerHallApplyManager.rejectHallApply(request);
        }else {
            log.info("operate hall apply not support status: {}, id:{}", request.getStatus(), request.getId());
            return RpcResult.fail(SingerHallApplyService.OPERATE_HALL_APPLY_NOT_SUPPORT_STATUS, "操作失败，暂不支持该操作");
        }

        if (!applyRecordBean.isPresent()){
            log.info("operate hall apply fail, id:{}", request.getId());
            return RpcResult.fail(SingerHallApplyService.OPERATE_HALL_APPLY_FAIL, "操作失败");
        }
        return RpcResult.success();
    }
}
