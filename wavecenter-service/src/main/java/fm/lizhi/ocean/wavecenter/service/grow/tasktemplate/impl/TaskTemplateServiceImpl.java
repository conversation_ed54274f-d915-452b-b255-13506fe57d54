package fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.impl;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.bean.ConditionBean;
import fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.bean.ConditionGroupBean;
import fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.bean.TaskTemplateBean;
import fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.bean.TaskTemplateQueryBean;
import fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.request.RequestDeleteTaskTemplate;
import fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.request.RequestQueryTaskTemplate;
import fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.request.RequestSaveTaskTemplate;
import fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.response.ResponseQueryTaskTemplate;
import fm.lizhi.ocean.wavecenter.api.grow.tasktemplate.service.TaskTemplateService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.convert.TaskTemplateServiceConvert;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto.SaveTaskTemplateDTO;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto.TaskTemplateQueryItemDTO;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto.TaskTemplateQueryResultDTO;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.manager.TaskTemplateManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 任务模版Service实现类
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Slf4j
@ServiceProvider
public class TaskTemplateServiceImpl implements TaskTemplateService {

    @Autowired
    private TaskTemplateManager taskTemplateManager;

    @Override
    public Result<Void> saveTaskTemplate(RequestSaveTaskTemplate request) {
        log.info("saveTaskTemplate request={}", request);
        try {
            // 参数业务逻辑校验
            Result<Void> validateResult = validateSaveTaskTemplateRequest(request);
            if (RpcResult.isFail(validateResult)) {
                return RpcResult.fail(validateResult.rCode(), validateResult.getMessage());
            }

            // 转换请求参数为DTO
            SaveTaskTemplateDTO saveTaskTemplateDTO = TaskTemplateServiceConvert.I.requestToDto(request);
            // 调用Manager层保存
            Boolean result = taskTemplateManager.saveTaskTemplate(saveTaskTemplateDTO, request.getOperator());
            return result ? RpcResult.success() : RpcResult.fail(SAVE_TASK_TEMPLATE_FAIL, "保存任务模版失败");
        } catch (Exception e) {
            log.error("saveTaskTemplate error", e);
            return RpcResult.fail(SAVE_TASK_TEMPLATE_FAIL, "保存任务模版异常：" + e.getMessage());
        }
    }

    @Override
    public Result<ResponseQueryTaskTemplate> queryTaskTemplateList(@Valid RequestQueryTaskTemplate request) {
        log.info("queryTaskTemplateList request={}", request);
        try {
            // 查询
            if (StringUtils.isNotBlank(request.getCapabilityCode())) {
                List<TaskTemplateQueryItemDTO> resultDTO = taskTemplateManager.queryTaskTemplateList(request.getCapabilityCode(), request.getAppId());
                List<TaskTemplateQueryBean> queryItemListToResponse = TaskTemplateServiceConvert.I.queryItemListToResponse(resultDTO);
                return RpcResult.success(ResponseQueryTaskTemplate.builder().list(queryItemListToResponse).total(Long.valueOf(resultDTO.size())).build());
            } else {
                TaskTemplateQueryResultDTO resultDTO = taskTemplateManager.pageQueryTaskTemplateList(
                        request.getAppId(), request.getPageNum(), request.getPageSize());
                return RpcResult.success(TaskTemplateServiceConvert.I.queryResultToResponse(resultDTO));
            }
        } catch (Exception e) {
            log.error("queryTaskTemplateList error", e);
            return RpcResult.fail(GET_TASK_TEMPLATE_FAIL, "查询任务模版列表失败");
        }
    }

    @Override
    public Result<Void> deleteGrowTaskTemplate(RequestDeleteTaskTemplate request) {
        log.info("deleteGrowTaskTemplate id={}", request);
        try {
            Boolean result = taskTemplateManager.deleteGrowTaskTemplate(request.getIds());
            return result ? RpcResult.success() : RpcResult.fail(DELETE_TASK_TEMPLATE_FAIL);
        } catch (Exception e) {
            log.error("deleteGrowTaskTemplate error", e);
            return RpcResult.fail(DELETE_TASK_TEMPLATE_FAIL, "删除任务模版异常：" + e.getMessage());
        }
    }

    /**
     * 参数业务逻辑校验
     *
     * @param request 保存请求
     * @return 校验结果
     */
    private Result<Void> validateSaveTaskTemplateRequest(RequestSaveTaskTemplate request) {
        // 校验任务模版列表不能为空（注解已校验）
        List<TaskTemplateBean> templateList = request.getTaskTemplateList();

        Set<Integer> scoreSet = new HashSet<>();
        // 校验能力项code不能重复
        for (TaskTemplateBean template : templateList) {
            if (scoreSet.contains(template.getCapabilityScore())) {
                return RpcResult.fail(PARAM_INVALID, "能力分不能重复");
            }
            scoreSet.add(template.getCapabilityScore());

            // 校验条件组
            Result<Void> conditionValidateResult = validateConditionGroup(template.getConditionGroup());
            if (RpcResult.isFail(conditionValidateResult)) {
                return conditionValidateResult;
            }
        }
        return RpcResult.success();
    }

    /**
     * 校验条件组
     *
     * @param conditionGroup 条件组
     * @return 校验结果
     */
    private Result<Void> validateConditionGroup(ConditionGroupBean conditionGroup) {
        // 校验逻辑符号
        String logicSymbol = conditionGroup.getLogicSymbol();
        Set<String> validLogicSymbols = new HashSet<>(Arrays.asList("AND", "OR"));
        if (!validLogicSymbols.contains(logicSymbol.toUpperCase())) {
            return RpcResult.fail(PARAM_INVALID, "逻辑符号只能是AND或OR：" + logicSymbol);
        }

        // 校验条件列表
        List<ConditionBean> conditionList = conditionGroup.getConditionList();
        Set<String> metricCodeSet = new HashSet<>();
        Set<String> validComparators = new HashSet<>(Arrays.asList(">=", ">", "<=", "<", "=", "!="));

        for (ConditionBean condition : conditionList) {
            // 校验指标code不能重复
            String metricCode = condition.getMetricCode();
            if (metricCodeSet.contains(metricCode)) {
                return RpcResult.fail(PARAM_INVALID, "同一条件组内指标code不能重复：" + metricCode);
            }
            metricCodeSet.add(metricCode);

            // 校验比较符
            String comparator = condition.getComparator();
            if (!validComparators.contains(comparator)) {
                return RpcResult.fail(PARAM_INVALID, "比较符只能是 >=, >, <=, <, =, != 中的一种：" + comparator);
            }
        }

        return RpcResult.success();
    }


} 