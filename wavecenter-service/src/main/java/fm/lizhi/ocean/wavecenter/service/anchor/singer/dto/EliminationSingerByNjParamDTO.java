package fm.lizhi.ocean.wavecenter.service.anchor.singer.dto;

import fm.lizhi.ocean.wavecenter.service.anchor.singer.constant.SingerEliminationReasonConstant;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 按厅进行淘汰歌手
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class EliminationSingerByNjParamDTO {

    /**
     * 应用ID
     */
    private int appId;

    /**
     * 厅ID
     */
    private Long njId;

    /**
     * 淘汰原因
     * @see SingerEliminationReasonConstant
     */
    private String eliminationReason;

    /**
     * 装饰流水原因
     */
    private String decorateFlowReason;

    /**
     * 操作人
     */
    private String operator;
}
