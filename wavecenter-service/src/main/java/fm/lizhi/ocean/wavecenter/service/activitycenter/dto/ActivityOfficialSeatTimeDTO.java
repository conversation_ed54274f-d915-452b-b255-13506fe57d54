package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 管频位厅数量记录
 *
 * @date 2024-10-11 07:26:46
 */
@Data
@Accessors(chain = true)
public class ActivityOfficialSeatTimeDTO {

    private Long id;

    private Integer appId;

    /**
     * 官频位
     */
    private Integer seat;

    /**
     * 日期
     */
    private Date showDate;

    /**
     * 官频位展示开始时间
     */
    private Date startTime;

    /**
     * 官频位展示结束时间
     */
    private Date endTime;

    /**
     * 档期官频位厅数量
     */
    private Integer count;

    /**
     * 可选值  TEST/PRE/PRO
     */
    private String deployEnv;
}