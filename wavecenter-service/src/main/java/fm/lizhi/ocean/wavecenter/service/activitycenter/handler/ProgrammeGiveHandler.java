package fm.lizhi.ocean.wavecenter.service.activitycenter.handler;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.AutoConfigResourceEnum;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.ActivityConfig;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 节目单发放
 */
@Slf4j
@Component
public class ProgrammeGiveHandler extends AbstractProgrammeGiveHandler {


    @Autowired
    private ActivityConfig activityConfig;

    @Override
    public String getResourceCode() {
        return AutoConfigResourceEnum.PROGRAMME.getResourceCode();
    }


    @Override
    protected Map<String, Long> getPrimaryClassMap() {
        try {
            String primaryClassConfig = activityConfig.getXm().getPrimaryClassMapping();
            return JsonUtils.fromJsonStringLegacy(primaryClassConfig, Map.class);
        } catch (Exception e) {
            log.warn("getPrimaryClassMap error", e);
        }
        return new HashMap<>();
    }

    @Override
    protected Map<String, Long> getSecondaryClassMap() {
        try {
            String secondaryClassConfig = activityConfig.getXm().getSecondaryClassMapping();
            return JsonUtils.fromJsonStringLegacy(secondaryClassConfig, Map.class);
        } catch (Exception e) {
            log.warn("getSecondaryClassMap error", e);
        }
        return new HashMap<>();
    }
}
