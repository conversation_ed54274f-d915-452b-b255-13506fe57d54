package fm.lizhi.ocean.wavecenter.service.user.impl;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.ContextRequest;
import fm.lizhi.ocean.wavecenter.api.user.bean.*;
import fm.lizhi.ocean.wavecenter.api.user.service.UserFamilyService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/4/10 17:31
 */
@ServiceProvider
public class UserFamilyServiceImpl implements UserFamilyService {

    @Autowired
    private FamilyManager familyManager;

    @Override
    public Result<UserInFamilyBean> getUserInFamily(int appId, long userId) {
        LogContext.addReqLog("appId={},userId={}", appId, userId);
        LogContext.addResLog("appId={},userId={}", appId, userId);
        return ResultHandler.handle(appId, () -> {
            UserInFamilyBean bean = familyManager.getUserInFamily(userId);
            return RpcResult.success(bean);
        });
    }

    @Override
    public Result<UserInFamilyDetailBean> getUserInFamilyDetail(long userId, ContextRequest contextRequest) {
        LogContext.addReqLog("userId={},contextRequest={}", userId, contextRequest);
        LogContext.addResLog("userId={},contextRequest={}", userId, contextRequest);

        UserInFamilyDetailBean bean = new UserInFamilyDetailBean();

        // 角色
        UserInFamilyBean userInFamily = familyManager.getUserInFamily(userId);
        bean.setFamily(userInFamily.isFamily());
        bean.setRoom(userInFamily.isRoom());
        bean.setPlayer(userInFamily.isPlayer());
        bean.setNjId(userInFamily.getNjId());
        bean.setFamilyId(userInFamily.getFamilyId());

        // 家族信息
        if (userInFamily.getFamilyId() != null) {
            Optional<FamilyBean> familyOp = familyManager.getFamilyByCache(userInFamily.getFamilyId());
            if (familyOp.isPresent()) {
                bean.setFamilyName(familyOp.get().getFamilyName());
            }
        }

        LogContext.addResLog("userInFamily={}", userInFamily);
        return RpcResult.success(bean);
    }

    @Override
    public Result<FamilyBean> getUserFamily(int appId, long userId) {
        LogContext.addReqLog("appId={},userId={}", appId, userId);
        LogContext.addResLog("appId={},userId={}", appId, userId);
        return ResultHandler.handle(appId, () -> {
            Optional<FamilyBean> userFamilyOp = familyManager.getUserFamily(userId);
            return userFamilyOp.map(RpcResult::success)
                    .orElseGet(() -> RpcResult.fail(USER_FAMILY_NOT_FOUND));
        });
    }

    @Override
    public Result<Long> getUserNj(int appId, long userId) {
        LogContext.addReqLog("appId={},userId={}", appId, userId);
        LogContext.addResLog("appId={},userId={}", appId, userId);
        return ResultHandler.handle(appId, () -> {
            Optional<Long> userBestNj = familyManager.getUserBestNj(userId);
            return userBestNj.map(RpcResult::success).orElseGet(() -> RpcResult.success(null));
        });
    }

    @Override
    public Result<Long> getPlayerLastRoom(int appId, long familyId, long playerId) {
        LogContext.addReqLog("appId={},familyId={},playerId={}", appId, familyId, playerId);
        LogContext.addResLog("appId={},familyId={},playerId={}", appId, familyId, playerId);
        return ResultHandler.handle(appId, () -> RpcResult.success(familyManager.getPlayerLastRoom(appId, familyId, playerId)));
    }

    @Override
    public Result<Long> getUserFamilyId(int appId, long userId) {
        LogContext.addReqLog("appId={},userId={}", appId, userId);
        LogContext.addResLog("appId={},userId={}", appId, userId);
        return ResultHandler.handle(appId, () -> {
            UserInFamilyBean userInFamily = familyManager.getUserInFamily(userId);
            if (userInFamily.getFamilyId() != null) {
                return RpcResult.success(userInFamily.getFamilyId());
            }
            if (userInFamily.isFamily()) {
                return RpcResult.success(userInFamily.getFamilyId());
            }
            if (userInFamily.isRoom()) {
                //查询厅主的签约信息
                Optional<Long> roomBestFamily = familyManager.getRoomBestFamily(userId);
                return roomBestFamily.map(RpcResult::success).orElseGet(() -> RpcResult.success(null));
            }
            if (userInFamily.isPlayer()) {
                //查询主播的签约信息
                Optional<Long> playerBestFamily = familyManager.getPlayerBestFamily(userId);
                return playerBestFamily.map(RpcResult::success).orElseGet(() -> RpcResult.success(null));
            }
            LogContext.addResLog("user role not found");
            return RpcResult.success(userInFamily.getFamilyId());
        });
    }

    @Override
    public Result<RoomSignBean> getRoomSignInfo(int appId, long familyId, long njId) {
        LogContext.addReqLog("appId={},familyId={},njId={}", appId, familyId, njId);
        LogContext.addResLog("appId={},familyId={},njId={}", appId, familyId, njId);
        return ResultHandler.handle(appId, () -> {
            Optional<RoomSignBean> roomSign = familyManager.getRoomSign(familyId, njId);
            return roomSign.map(RpcResult::success)
                    .orElseGet(() -> RpcResult.fail(USER_FAMILY_NOT_FOUND));
        });
    }

    @Override
    public Result<FamilyAuthBean> getUserFamilyAuth(int appId, long familyId) {
        LogContext.addReqLog("appId={},familyId={}", appId, familyId);
        LogContext.addResLog("appId={},familyId={}", appId, familyId);
        return ResultHandler.handle(appId, () -> {
            Optional<FamilyAuthBean> userFamilyAuth = familyManager.getUserFamilyAuth(familyId);
            return userFamilyAuth.map(RpcResult::success)
                    .orElseGet(() -> RpcResult.fail(USER_FAMILY_NOT_FOUND));
        });
    }

    @Override
    public Result<FamilyBean> getFamily(int appId, long familyId) {
        LogContext.addReqLog("appId={},familyId={}", appId, familyId);
        LogContext.addResLog("appId={},familyId={}", appId, familyId);
        return ResultHandler.handle(appId, () -> {
            Optional<FamilyBean> family = familyManager.getFamily(appId, familyId);
            return family.map(RpcResult::success).orElseGet(() -> RpcResult.fail(FAMILY_NOT_FOUND));
        });
    }
}
