package fm.lizhi.ocean.wavecenter.service.live.handler;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.Week;
import fm.lizhi.ocean.wavecenter.api.live.constants.CheckInDateTypeEnum;
import fm.lizhi.ocean.wavecenter.service.live.config.LiveConfig;
import fm.lizhi.ocean.wavecenter.service.live.manager.WaveCheckInDataManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class WeekCheckInRoomStatHandler implements CheckInRoomPeriodStatHandler {


    @Autowired
    private UserManager userManager;
    @Autowired
    private LiveConfig liveConfig;


    @Override
    public boolean support(CheckInDateTypeEnum typeEnum) {
        return typeEnum == CheckInDateTypeEnum.WEEK;
    }

    @Override
    public String buildContent(Integer appId, Long njId, Date statTime) {
        List<SimpleUserDto> simpleUserByIds = userManager.getSimpleUserByIds(Collections.singletonList(njId));
        if(CollectionUtils.isEmpty(simpleUserByIds)) {
            log.error("buildContent fail;njId info no exist;appId={};njId={}", appId, njId);
            return StringUtils.EMPTY;
        }
        String msg = liveConfig.getCheckInReportMsgModel();
        DateTime previousWeek = DateUtil.offsetWeek(statTime, -1);
        DateTime beginOfWeek = DateUtil.beginOfWeek(previousWeek, true);
        DateTime endOfWeek = DateUtil.endOfWeek(previousWeek, true);
        String datePart = DateUtil.format(beginOfWeek, "yyyy年MM月dd日");
        // 格式化日期部分为"XX年XXX月XX日-XX日"
        String dateFormat = String.format("%s-%02d日", datePart, DateUtil.dayOfMonth(endOfWeek));
        String name = simpleUserByIds.get(0).getName();
        String checkInReportUrl = liveConfig.getBizConfig(appId).getCheckInReportUrl();
        String url = buildReportUrl(CheckInDateTypeEnum.WEEK, appId, njId, beginOfWeek, endOfWeek, checkInReportUrl, liveConfig.getCheckInMD5SaltValue());
        return String.format(msg, name, dateFormat, liveConfig.getWeekChatPreviewUrl(), url);
    }


    @Override
    public List<Long> populateExtraReceiver(Integer appId, Long njId, Date statTime) {
        return Collections.emptyList();
    }

    @Override
    public boolean isTimeToReport(Date triggerTime) {
        int hour = DateUtil.hour(triggerTime, true);
        Week week = DateUtil.dayOfWeekEnum(triggerTime);
        //是否是周一10点
        return week == Week.MONDAY && hour == 10;
    }

    @Override
    public boolean canSend(Long njId) {
        return true;
    }
}
