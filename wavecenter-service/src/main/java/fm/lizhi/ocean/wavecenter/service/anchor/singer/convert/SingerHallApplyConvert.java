package fm.lizhi.ocean.wavecenter.service.anchor.singer.convert;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerSingHallApplyRecordSummaryBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestPageHallApply;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponsePageHallApply;
import fm.lizhi.ocean.wavecenter.base.convert.CommonConvert;
import fm.lizhi.ocean.wavecenter.common.dto.PageDto;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.PageHallApplyParamDTO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR, uses = {CommonConvert.class})
public interface SingerHallApplyConvert {

    SingerHallApplyConvert I = Mappers.getMapper(SingerHallApplyConvert.class);

    ResponsePageHallApply convertResponsePageHallApply(PageDto<SingerSingHallApplyRecordSummaryBean> pageDto);

    PageHallApplyParamDTO convertPageHallApplyParamDTO(RequestPageHallApply request);

}
