package fm.lizhi.ocean.wavecenter.service.activitycenter.processor.pp;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.service.activitycenter.processor.IRecommendCardGiveProcess;
import org.springframework.stereotype.Component;

@Component
public class PpRecommendCardGiveProcess implements IRecommendCardGiveProcess {
    @Override
    public String getGiveReason(String activityName) {
        return activityName + "专用推荐卡";
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.PP;
    }
}
