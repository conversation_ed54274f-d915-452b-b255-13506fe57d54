package fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityFlowResourceAuditBean;

import java.util.List;

public interface ActivityAdminOperateManager {

    /**
     * 驳回活动申请
     *
     * @param activityId 活动ID
     * @param reason     原因
     * @param operator   操作者
     * @return 结果
     */
    boolean rejectActivityApply(Long activityId, String reason, String operator, Integer version);

    /**
     * 同意活动申请&资源发放
     *
     * @param appId             appID
     * @param activityId        活动ID
     * @param operator          操作者
     * @param resourceAuditList 资源审批列表
     * @return 结果
     */
    Result<String> agreeActivityApply(Integer appId, Long activityId, String operator, List<ActivityFlowResourceAuditBean> resourceAuditList, Integer version);

    int AGREE_ACTIVITY_APPLY_REPEAT = 3;

}
