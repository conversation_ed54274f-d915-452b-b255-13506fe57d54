package fm.lizhi.ocean.wavecenter.service.anchor.singer.config;

import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.UserSingerGloryConfig;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.UserSingerGloryDTO;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class PpSingerConfig implements CommonSingerConfig {

    /**
     * 歌曲风格配置 不允许包含逗号和下划线
     */
    private String songStyleConfig = "[{\"id\":1,\"name\":\"说唱\",\"conditionIndex\":1},{\"id\":2,\"name\":\"流行\",\"conditionIndex\":2},{\"id\":3,\"name\":\"国风\",\"conditionIndex\":3},{\"id\":4,\"name\":\"R&B\",\"conditionIndex\":4},{\"id\":5,\"name\":\"民谣\",\"conditionIndex\":5},{\"id\":6,\"name\":\"摇滚\",\"conditionIndex\":6}]";

    /**
     * 点唱厅品类 ID
     */
    private Long singHallCategoryId = 10010L;

    /**
     * 点唱厅品类名称
     */
    private String singRoomCategoryName = "点唱";

    /**
     * 歌手导入名单
     * key:歌手等级
     * value:用户ID列表
     */
    private Map<Integer, List<Long>> importSingerMap;

    /**
     * 音频审核配置
     */
    private String audioAuditConfig= "{\"durationSec\":10,\"enabledVerifyProportion\":true,\"thresholdMusicProportion\":94,\"thresholdNoiseDB\":-45,\"thresholdNoiseDBV2\":-55,\"thresholdSpeechProportion\":50}";

    /**
     * 歌手认证提报文案
     */
    private String singerAuditReportText="{\"prologue\":\"评委老师好，现在是「几点几分」我是「主播呢称」,我的考核歌曲名称为「歌曲名称」\",\"recordNotice\":\"唱歌前，请按以下格式完成自我介绍，并录制1分半钟的考核歌曲\",\"announcement\":\"&lt;p&gt;1.唱歌前，请按以下格式完成自我介绍：评委老师好，&lt;span style=&quot;color: #FF8481;&quot;&gt;现在是「几点几分」，我是「昵称」，我的考核歌是「歌曲名」&lt;/span&gt;&lt;/p&gt; &lt;p&gt;2.演唱时倾立麦克风近一点，确保可听见清晰的人声&lt;/p&gt;\"}";

    /**
     * 歌手风格数量限制
     */
    private Map<Integer, Integer> maxStyleNumMap;

    /**
     * 是否开启点唱厅导入
     */
    private boolean enableImportSingHall = false;

    /**
     * 是否开启点唱厅淘汰
     */
    private boolean enableCleanSingHall = false;

    /**
     * 导入点唱厅要求的收入流水
     */
    private Long importSingHallGtAllIncome = 200000L;

    /**
     * 弱关联开关
     */
    private boolean lessRelevanceSwitch = true;

    /**
     * 歌手认证申请被拒绝后，多少天内不允许再次申请,0则是存在记录则不允许
     */
    private Integer rejectRecordDay = 1;

    /**
     * 用户歌手勋章配置
     */
    private UserSingerGloryConfig userSingerGloryConfig = new UserSingerGloryConfig()
            .setNewSingerGlory(new UserSingerGloryDTO()
                    .setUrl("https://cdn.zhiyalive.com/sociality/2024/04/24/3072410275596088380.png")
                    .setRatio(0.28)
            )
            .setQualitySingerGlory(new UserSingerGloryDTO()
                    .setUrl("https://cdn101.zhiyalive.com/sociality/2025/06/04/3147801717740378172.png")
                    .setRatio(0.27)
            )
            .setOriginalQualitySingerGlory(new UserSingerGloryDTO()
                    .setUrl("https://cdn101.zhiyalive.com/sociality/2025/04/02/3136040312726168636.png")
                    .setRatio(0.20)
            );

}
