package fm.lizhi.ocean.wavecenter.service.resource.decorate.impl;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestCreateAvatarWidget;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestCreateRoomBackground;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.response.ResponseCreateAvatarWidget;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.response.ResponseCreateRoomBackground;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.service.DecorateManagementService;
import fm.lizhi.ocean.wavecenter.service.resource.decorate.manager.DecorateManagementManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@ServiceProvider
@Slf4j
public class DecorateManagementServiceImpl implements DecorateManagementService {

    @Autowired
    private DecorateManagementManager decorateManagementManager;

    @Override
    public Result<ResponseCreateRoomBackground> createRoomBackground(RequestCreateRoomBackground request) {
        return decorateManagementManager.createRoomBackground(request);
    }

    @Override
    public Result<ResponseCreateAvatarWidget> createAvatarWidget(RequestCreateAvatarWidget request) {
        return decorateManagementManager.createAvatarWidget(request);
    }
}
