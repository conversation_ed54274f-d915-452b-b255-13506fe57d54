package fm.lizhi.ocean.wavecenter.service.sign.impl;

import com.google.common.collect.Lists;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.*;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestPlayerHallInfo;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponsePlayerHallInfo;
import fm.lizhi.ocean.wavecenter.api.sign.service.GuildManageService;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.sign.convert.SignConvert;
import fm.lizhi.ocean.wavecenter.service.sign.dto.SMSignPlayerPageListReqDto;
import fm.lizhi.ocean.wavecenter.service.sign.dto.SMSignRoomPageListReqDto;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.RoomManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.SignManager;
import fm.lizhi.ocean.wavecenter.service.user.convert.UserCommonConvert;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/13 15:12
 */
@Slf4j
@ServiceProvider
public class GuildManageServiceImpl implements GuildManageService {

    @Autowired
    private SignManager signManager;
    @Autowired
    private FamilyManager familyManager;
    @Autowired
    private UserManager userManager;

    @Autowired
    private RoomManager roomManager;

    @Override
    public Result<PageBean<RoomSignInfoBean>> signRoomPageList(GMSSignRoomPageListReq req) {
        LogContext.addReqLog("req={}", JsonUtil.dumps(req));
        LogContext.addResLog("req={}", JsonUtil.dumps(req));
        return ResultHandler.handle(req.getAppId(), () -> {
            SMSignRoomPageListReqDto reqDto = SignConvert.I.gMSSignRoomPageListReq2Dto(req);
            PageBean<RoomSignInfoBean> pageBean = signManager.signRoomPageList(reqDto);
            return RpcResult.success(pageBean);
        });
    }

    @Override
    public Result<PageBean<SignPlayerInfoBean>> signPlayerPageList(GMSSignPlayerPageListReq req) {
        LogContext.addReqLog("req={}", JsonUtil.dumps(req));
        LogContext.addResLog("req={}", JsonUtil.dumps(req));
        return ResultHandler.handle(req.getAppId(), () -> {
            SMSignPlayerPageListReqDto reqDto = SignConvert.I.gMSSignPlayerPageListReq2Dto(req);

            if (req.getRoomId() == null) {
                // 查询公会所有厅
                Set<Long> signNjIds = familyManager.getSignNjIds(req.getFamilyId());
                List<Long> queryRoomIds = new ArrayList<>(signNjIds);

                if (CollectionUtils.isNotEmpty(req.getRoomIds())) {
                    // 权限查询转交集
                    queryRoomIds = queryRoomIds.stream().filter(req.getRoomIds()::contains).collect(Collectors.toList());
                }

                reqDto.setRoomIds(queryRoomIds);
            } else {
                reqDto.setRoomIds(Lists.newArrayList(req.getRoomId()));
            }

            PageBean<SignPlayerInfoBean> pageBean = signManager.signPlayerPageList(reqDto);
            return RpcResult.success(pageBean);
        });
    }

    @Override
    public Result<GuildFullInfoBean> getFullInfo(GetFullInfoReq req) {
        LogContext.addReqLog("req={}", JsonUtil.dumps(req));
        LogContext.addResLog("req={}", JsonUtil.dumps(req));
        return ResultHandler.handle(req.getAppId(), () -> {
            GuildFullInfoBean fullInfo = familyManager.getFullInfo(req.getFamilyId());
            return RpcResult.success(fullInfo);
        });
    }

    @Override
    public Result<ResponsePlayerHallInfo> playerHallInfo(RequestPlayerHallInfo req) {
        return ResultHandler.handle(req.getAppId(), () -> {
            Optional<Long> userBestNjRes = familyManager.getUserBestNj(req.getPlayerId());
            if (!userBestNjRes.isPresent()) {
                return RpcResult.fail(PLAYER_HALL_INFO_HALL_NOT_EXIST, "当前用户未查到签约厅主，请确认是否存在签约厅");
            }

            List<Long> ids = Lists.newArrayList(userBestNjRes.get());

            List<Long> playerIds = roomManager.getAllSignRoomPlayerIds(userBestNjRes.get());
            if (playerIds != null) {
                ids.addAll(playerIds);
            }

            Map<Long, SimpleUserDto> userDtoMap = userManager.getSimpleUserMapByIds(ids);
            //取出厅主的信息
            SimpleUserDto roomUser = userDtoMap.get(userBestNjRes.get());
            //去掉厅主的信息
            userDtoMap.remove(userBestNjRes.get());
            List<SimpleUserDto> playerUserDtoList = new ArrayList<>(userDtoMap.values());
            List<UserBean> playerUserBeanList = playerUserDtoList.stream().map(UserCommonConvert.I::simpleUserDto2userBean).collect(Collectors.toList());
            return RpcResult.success(new ResponsePlayerHallInfo().setRoomInfo(UserCommonConvert.I.simpleUserDto2userBean(roomUser)).setPlayerList(playerUserBeanList));
        });
    }
}
