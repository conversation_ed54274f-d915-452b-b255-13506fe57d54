package fm.lizhi.ocean.wavecenter.service.anchor.singer.handler;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerChatSceneEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.convert.SingerVerifyRecordConvert;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerAuditParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerExecuteAuditDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerVerifyRecordDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.UpdateSingerVerifyStatusParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerChatManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerVerifyApplyManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 歌手认证拒绝处理器
 */
@Slf4j
@Component
public class SingerAuditRejectHandler extends AbstractSingerAuditHandler {

    @Autowired
    private SingerVerifyApplyManager singerVerifyApplyManager;
    @Autowired
    private SingerChatManager singerChatManager;

    @Override
    public SingerExecuteAuditDTO auditHandle(SingerAuditParamDTO param, SingerVerifyRecordDTO verifyRecord) {
        // 如果原状态是选中，需要修改歌手库的状态
        Integer targetSingerStatus = verifyRecord.getAuditStatus() == SingerAuditStatusEnum.SELECTED.getStatus() ? SingerStatusEnum.ELIMINATED.getStatus() : null;
        UpdateSingerVerifyStatusParamDTO paramDTO = SingerVerifyRecordConvert.I.buildUpdateParam(verifyRecord, param, SingerAuditStatusEnum.REJECTED.getStatus(),
                targetSingerStatus, targetSingerStatus != null, verifyRecord.getSingerType());
        boolean updateRes = singerVerifyApplyManager.updateSingerVerifyRecordStatus(paramDTO);
        if (updateRes) {
            SingerChatSceneEnum chatSceneEnum = verifyRecord.getAuditStatus() == SingerAuditStatusEnum.SELECTED.getStatus()
                    ? SingerChatSceneEnum.SELECTED_THAN_NO_PASS : SingerChatSceneEnum.HUMAN_AUDIT_NOT_PASS;
            singerChatManager.sendAuditResultChat(verifyRecord.getAppId(), verifyRecord.getUserId(), 
                                  verifyRecord.getSingerType(), verifyRecord.getSongStyle(), chatSceneEnum);
        }
        return updateRes ? SingerExecuteAuditDTO.success() : SingerExecuteAuditDTO.failure("修改状态失败，请稍候重试!");
    }

    @Override
    public SingerAuditStatusEnum getAuditStatusEnum() {
        return SingerAuditStatusEnum.REJECTED;
    }
}
