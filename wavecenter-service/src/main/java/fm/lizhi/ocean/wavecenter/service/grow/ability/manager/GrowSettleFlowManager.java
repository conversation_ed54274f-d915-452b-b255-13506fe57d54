package fm.lizhi.ocean.wavecenter.service.grow.ability.manager;

import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.PlayerSettleFlowDTO;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.RoomSettleFlowDTO;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.SettlePeriodDTO;

/**
 * <AUTHOR>
 * @date 2025/6/6 16:18
 */
public interface GrowSettleFlowManager {

    /**
     * 获取指定周期的主播结算流程，如果不存在则初始化
     * @param dto
     * @return
     */
    PlayerSettleFlowDTO getOrInitPlayerSettleFlow(SettlePeriodDTO dto);

    /**
     * 获取指定周期的厅结算流程，如果不存在则初始化
     * @param dto
     * @return
     */
    RoomSettleFlowDTO getOrInitRoomSettleFlow(SettlePeriodDTO dto);

    /**
     * 更新流程
     * @param dto
     */
    void updatePlayerSettleFlow(PlayerSettleFlowDTO dto);

    /**
     * 更新流程
     * @param dto
     */
    void updateRoomSettleFlow(RoomSettleFlowDTO dto);

    /**
     * 获取主播结算流程
     * @param dto
     * @return
     */
    PlayerSettleFlowDTO getPlayerSettleFlow(SettlePeriodDTO dto);

    /**
     * 查询厅结算流程
     * @param dto
     * @return
     */
    RoomSettleFlowDTO getRoomSettleFlow(SettlePeriodDTO dto);

}
