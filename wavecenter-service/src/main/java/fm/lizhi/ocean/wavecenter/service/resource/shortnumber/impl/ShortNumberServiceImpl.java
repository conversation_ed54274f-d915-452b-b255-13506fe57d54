package fm.lizhi.ocean.wavecenter.service.resource.shortnumber.impl;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.api.resource.shortnumber.request.RequestGetShortNumber;
import fm.lizhi.ocean.wavecenter.api.resource.shortnumber.request.RequestListShortNumber;
import fm.lizhi.ocean.wavecenter.api.resource.shortnumber.response.ResponseGetShortNumber;
import fm.lizhi.ocean.wavecenter.api.resource.shortnumber.response.ResponseListShortNumber;
import fm.lizhi.ocean.wavecenter.api.resource.shortnumber.service.ShortNumberService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.service.resource.shortnumber.convert.ShortNumberConvert;
import fm.lizhi.ocean.wavecenter.service.resource.shortnumber.dto.ShortNumberDTO;
import fm.lizhi.ocean.wavecenter.service.resource.shortnumber.manager.ShortNumberManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Slf4j
@ServiceProvider
public class ShortNumberServiceImpl implements ShortNumberService {

    @Autowired
    private ShortNumberManager shortNumberManager;

    @Override
    public Result<List<ResponseListShortNumber>> listShortNumber(RequestListShortNumber request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));
        LogContext.addResLog("request={}", JsonUtils.toJsonString(request));
        Integer appId = request.getAppId();
        List<ShortNumberDTO> shortNumberDTOS = shortNumberManager.listShortNumber(appId);
        List<ResponseListShortNumber> responseList = ShortNumberConvert.I.toResponseListShortNumbers(shortNumberDTOS);
        LogContext.addResLog("responseListSize={}", CollectionUtils.size(responseList));
        log.debug("listShortNumber response={}", responseList);
        return RpcResult.success(responseList);
    }

    @Override
    public Result<ResponseGetShortNumber> getShortNumber(RequestGetShortNumber request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));
        LogContext.addResLog("request={}", JsonUtils.toJsonString(request));
        Integer appId = request.getAppId();
        Long id = request.getId();
        ShortNumberDTO shortNumberDTO = shortNumberManager.getShortNumber(appId, id);
        if (shortNumberDTO == null) {
            return RpcResult.fail(CommonService.PARAM_ERROR, "shortNumber not found");
        }
        ResponseGetShortNumber response = ShortNumberConvert.I.toResponseGetShortNumber(shortNumberDTO);
        LogContext.addResLog("response={}", JsonUtils.toJsonString(response));
        return RpcResult.success(response);
    }
}
