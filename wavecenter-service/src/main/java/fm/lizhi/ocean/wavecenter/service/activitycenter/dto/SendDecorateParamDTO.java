package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.Data;

@Data
public class SendDecorateParamDTO {

    /**
     * 装饰发放 记录ID，即幂等ID
     */
    private long decorateRecordId;

    /**
     * 装饰ID
     */
    private long decorateId;

    /**
     * 发放给谁
     */
    private Long ownerId;

    /**
     * 发放装饰个数
     */
    private int count;

    /**
     * 发放分钟数
     */
    private int validMin;

    /**
     * 马上使用，1是 0否 (默认立即使用)
     */
    private int useImmediately = 1;

    /**
     * 有效期开始时间（有值会覆盖之前的有效期开始时间,不支持发放多个）
     * 非必传
     */
    private long beginTime;

    /**
     * 有效期结束时间（有值会覆盖之前的有效期结束时间,不支持发放多个）
     * 非必传
     */
    private long endTime;

    /**
     * beginTime和endTime有值时，是否覆盖之前的有效期
     * true-是 false-否
     */
    private boolean coverValid = true;

}
