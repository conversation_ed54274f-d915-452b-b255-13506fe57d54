package fm.lizhi.ocean.wavecenter.service.message.convert;

import fm.lizhi.ocean.wavecenter.api.message.bean.UnReadMessageCountBean;
import fm.lizhi.ocean.wavecenter.api.message.request.*;
import fm.lizhi.ocean.wavecenter.api.message.bean.MessageBean;
import fm.lizhi.ocean.wavecenter.api.message.response.ResponseGetMessageList;
import fm.lizhi.ocean.wavecenter.api.message.bean.WcNoticeConfigBean;
import fm.lizhi.ocean.wavecenter.api.message.response.ResponseWcNoticeConfigPage;
import fm.lizhi.ocean.wavecenter.service.message.dto.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR, imports = {Date.class})
public interface WcNoticeConfigConvert {

    WcNoticeConfigConvert INSTANCE = Mappers.getMapper(WcNoticeConfigConvert.class);

    @Mapping(target = "effectTime", expression = "java(new Date(request.getEffectTime()))")
    WcNoticeConfigSaveParamDTO reqToDto(RequestWcNoticeConfigSave request);

    @Mapping(target = "minEffectTime", expression = "java(new Date(request.getMinEffectTime()))")
    @Mapping(target = "maxEffectTime", expression = "java(new Date(request.getMaxEffectTime()))")
    WcNoticeConfigQueryDTO pageReqToDto(RequestWcNoticeConfigPage request);

    ResponseWcNoticeConfigPage dtoToPageRes(WcNoticeConfigPageResultDTO result);

    WcNoticeConfigQueryParamDTO effectTimePageReqToDto(RequestWcNoticeConfigEffectTimePage request);

    List<WcNoticeConfigBean> configListDtoToRes(List<WcNoticeConfigDTO> list);

    @Mapping(target = "effectTime", expression = "java(dto.getEffectTime().getTime())")
    @Mapping(target = "modifyTime", expression = "java(dto.getModifyTime().getTime())")
    WcNoticeConfigBean configDtoToRes(WcNoticeConfigDTO dto);


    List<UnReadMessageCountBean> unReadMessageCountToRes(List<UnReadMessageCountBean> result);

    /**
     * 查询参数转换
     *
     * @param request 查询参数
     * @return 查询参数
     */
    @Mapping(target = "lastMaxEffectTime", source = "performanceId")
    @Mapping(target = "pageSize", source = "size")
    WcNoticeConfigQueryParamDTO queryParamReqToDto(RequestGetMessageList request);

    /**
     * 响应参数转换
     *
     * @param dto 响应参数
     * @return 响应参数
     */
    @Mapping(target = "messageList", source = "list")
    @Mapping(target = "unRead", constant = "0L")
    ResponseGetMessageList configListDtoToRes(WcNoticeConfigResultDTO dto);

    List<MessageBean> resDtoToMsgList(List<WcNoticeConfigQueryResDTO> list);

    /**
     * 响应参数转换
     *
     * @param dto 响应参数
     * @return 响应参数
     */
    @Mapping(target = "targetUserId", ignore = true)
    @Mapping(target = "visibleRoleCode", ignore = true)
    @Mapping(target = "sendUserId", ignore = true)
    @Mapping(target = "targetLink", ignore = true)
    @Mapping(target = "bizId", ignore = true)
    @Mapping(target = "linkType", ignore = true)
    @Mapping(target = "updateTime", expression = "java(dto.getModifyTime().getTime())")
    @Mapping(target = "createTime", expression = "java(dto.getCreateTime().getTime())")
    @Mapping(target = "effectTime", expression = "java(dto.getEffectTime().getTime())")
    MessageBean resDtoToMsg(WcNoticeConfigQueryResDTO dto);

    WcNoticeUpdateStatusDTO reqToDto(RequestWcNoticeUpdateStatus request);
}
