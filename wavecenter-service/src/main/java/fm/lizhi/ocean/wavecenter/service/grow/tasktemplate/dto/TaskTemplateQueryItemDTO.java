package fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 任务模版查询项DTO
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskTemplateQueryItemDTO {

    /**
     * 模版ID
     */
    private String id;

    /**
     * 能力项code
     */
    private String capabilityCode;

    /**
     * 能力分
     */
    private Integer capabilityScore;

    /**
     * 条件组JSON
     */
    private String conditionJson;

    /**
     * 能力名称
     */
    private String capabilityName;
}
