package fm.lizhi.ocean.wavecenter.service.user.convert;

import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2024/10/12 15:42
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface RoleConvert {

    RoleConvert I = Mappers.getMapper(RoleConvert.class);

    default String waveRole2HyRoleCode(String waveRole) {
        if (RoleEnum.ROOM.getRoleCode().equals(waveRole)) {
            return "ADMIN";
        }
        if (RoleEnum.PLAYER.getRoleCode().equals(waveRole) || RoleEnum.USER.getRoleCode().equals(waveRole)) {
            return "PLAYER";
        }
        if (RoleEnum.FAMILY.getRoleCode().equals(waveRole)) {
            return "FAMILY";
        }
        return "";
    }

    default String waveRole2XmRoleCode(String waveRole) {
        if (RoleEnum.ROOM.getRoleCode().equals(waveRole)) {
            return "ADMIN";
        }
        if (RoleEnum.PLAYER.getRoleCode().equals(waveRole) || RoleEnum.USER.getRoleCode().equals(waveRole)) {
            return "PLAYER";
        }
        if (RoleEnum.FAMILY.getRoleCode().equals(waveRole)) {
            return "FAMILY";
        }
        return "";
    }

    default String waveRole2PpRoleCode(String waveRole) {
        if (RoleEnum.ROOM.getRoleCode().equals(waveRole)) {
            return "ADMIN";
        }
        if (RoleEnum.PLAYER.getRoleCode().equals(waveRole) || RoleEnum.USER.getRoleCode().equals(waveRole)) {
            return "PLAYER";
        }
        if (RoleEnum.FAMILY.getRoleCode().equals(waveRole)) {
            return "FAMILY";
        }
        return "";
    }

}
