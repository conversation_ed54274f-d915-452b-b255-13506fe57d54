package fm.lizhi.ocean.wavecenter.service.activitycenter.handler;

import fm.lizhi.ocean.wavecenter.service.common.handler.BaseHandlerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ApplyRuleFilterFactory extends BaseHandlerFactory<Integer, ApplyRuleFilter> {

    @Autowired
    private List<ApplyRuleFilter> filters;

    @Override
    public void registerAllHandlers() {
        for (ApplyRuleFilter filter : filters) {
            registerHandler(filter.getRuleTypeEnum().getType(), filter);
        }
    }
}
