package fm.lizhi.ocean.wavecenter.service.anchor.singer.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 厅内歌手数量 DTO
 */
@Data
@Accessors(chain = true)
public class SingerCountInHallDTO {
    /**
     * 生效中的歌手数量
     */
    private Integer effectiveCount;

    /**
     * 审核中的歌手数量
     * PS:(从歌手审核库拿出这三种审核状态(待审核 + 待定 + 选中)的歌手数据 +(歌手库中状态=认证中的歌手数据数量))
     */
    private Integer auditCount;

    /**
     * 认证中的歌手数量
     */
    private Integer authenticationCount;
} 