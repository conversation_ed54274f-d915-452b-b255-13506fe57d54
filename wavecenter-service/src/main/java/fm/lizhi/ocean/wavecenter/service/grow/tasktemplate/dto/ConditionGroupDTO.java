package fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 条件组DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConditionGroupDTO {

    /**
     * 条件列表
     */
    @NotEmpty(message = "条件列表不能为空")
    @Valid
    private List<ConditionDTO> conditionList;

    /**
     * 逻辑符号 (AND/OR)
     */
    @NotBlank(message = "逻辑符号不能为空")
    private String logicSymbol;
}
