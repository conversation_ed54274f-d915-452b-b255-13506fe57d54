package fm.lizhi.ocean.wavecenter.service.datacenter.dto;

import lombok.Data;

import java.util.List;

/**
 * 厅收入概览统计结果DTO
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
@Data
public class QueryRoomIncomeResDTO {

    /**
     * 总数
     */
    private Integer total;

    /**
     * 收入详情
     */
    private List<RoomIncomeStatDTO> resultList;

    /**
     * 构建数据
     */
    public static QueryRoomIncomeResDTO of(Integer total, List<RoomIncomeStatDTO> resultList) {
        QueryRoomIncomeResDTO dto = new QueryRoomIncomeResDTO();
        dto.setTotal(total);
        dto.setResultList(resultList);
        return dto;
    }
} 