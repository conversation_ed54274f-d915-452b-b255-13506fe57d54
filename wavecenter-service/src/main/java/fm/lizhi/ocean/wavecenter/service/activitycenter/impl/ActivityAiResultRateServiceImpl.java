package fm.lizhi.ocean.wavecenter.service.activitycenter.impl;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.ActivityAiResultRateRequest;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityAiResultRateService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.service.activitycenter.convert.ActivityAiResultRateConvert;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityAiResultRateDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityAiResultRateManager;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * AI结果评分 Service 实现类
 */
@Slf4j
@ServiceProvider
public class ActivityAiResultRateServiceImpl implements ActivityAiResultRateService {

    @Resource
    private ActivityAiResultRateManager activityAiResultRateManager;

    @Override
    public Result<Void> saveActivityAiResultRate(ActivityAiResultRateRequest request) {
        log.info("保存AI结果评分，参数：{}", request);
        ActivityAiResultRateDTO dto = ActivityAiResultRateConvert.INSTANCE.requestToDTO(request);
        boolean result = activityAiResultRateManager.saveActivityAiResultRate(dto);
        return result ? RpcResult.success() : RpcResult.fail(SAVE_ACTIVITY_AI_RESULT_RATE_FAILED);
    }
} 