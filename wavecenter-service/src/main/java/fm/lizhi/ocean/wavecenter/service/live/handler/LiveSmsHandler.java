package fm.lizhi.ocean.wavecenter.service.live.handler;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.LiveSmsRoomParamBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.RoomSmsStatBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import org.springframework.stereotype.Component;

/**
 * 私信分析
 * <AUTHOR>
 * @date 2024/4/20 17:11
 */
@Component
public class LiveSmsHandler {

    /**
     * 厅列表
     * @param paramBean
     * @return
     */
    public Result<PageBean<RoomSmsStatBean>> roomList(LiveSmsRoomParamBean paramBean){
        return RpcResult.success();
    }

}
