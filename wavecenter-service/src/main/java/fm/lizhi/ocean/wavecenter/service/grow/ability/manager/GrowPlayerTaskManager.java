package fm.lizhi.ocean.wavecenter.service.grow.ability.manager;

import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.TaskInfoI;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.GrowPlayerTaskDTO;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.SettlePeriodDTO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/6 18:18
 */
public interface GrowPlayerTaskManager {

    /**
     * 删除指定周期的任务
     */
    void deletePeriodTask(Long playerId, SettlePeriodDTO periodDTO);

    /**
     * 保存任务
     * @param taskInfo
     */
    void savePlayerTask(TaskInfoI taskInfo, SettlePeriodDTO periodDTO);

    /**
     * 查询陪玩在指定周期内完成的任务列表
     * @param startTime
     * @param endTime
     * @return
     */
    List<GrowPlayerTaskDTO> getPlayerFinishByTime(Long playerId, Date startTime, Date endTime);

}
