package fm.lizhi.ocean.wavecenter.service.background.activitycenter.handler;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityTemplateStatusEnum;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.constants.TemplateStatusTaskStatusEnum;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityTemplateManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityTemplateStatusTaskManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityTemplateStatusTaskDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/8 17:45
 */
@Slf4j
@Component
public class TemplateStatusTaskHandler {

    @Autowired
    private ActivityTemplateStatusTaskManager activityTemplateStatusTaskManager;
    @Autowired
    private ActivityTemplateManager activityTemplateManager;

    /**
     * 执行任务
     * @param taskNums 任务量
     */
    public void executeTask(Integer taskNums){
        // 查询未执行的任务
        List<ActivityTemplateStatusTaskDTO> waitingTask = activityTemplateStatusTaskManager.getWaitingTask(taskNums);
        if (CollectionUtils.isEmpty(waitingTask)) {
            return;
        }

        // 执行
        for (ActivityTemplateStatusTaskDTO taskDTO : waitingTask) {
            try {
                ActivityTemplateStatusEnum status = ActivityTemplateStatusEnum.getByStatus(taskDTO.getTemplateTargetStatus());
                if (status == null) {
                    log.error("executeTask error. taskId={}, templateTargetStatus={} is not exist", taskDTO.getId(), taskDTO.getTemplateTargetStatus());
                    activityTemplateStatusTaskManager.updateTaskStatus(taskDTO.getId(), TemplateStatusTaskStatusEnum.FAILED);
                    continue;
                }
                // 更新模板状态
                activityTemplateManager.updateStatus(taskDTO.getTemplateId(), status);
                // 更新任务状态
                activityTemplateStatusTaskManager.updateTaskStatus(taskDTO.getId(), TemplateStatusTaskStatusEnum.FINISHED);
            } catch (Exception e) {
                log.error("executeTask error. taskId={}", taskDTO.getId(), e);
                activityTemplateStatusTaskManager.updateTaskStatus(taskDTO.getId(), TemplateStatusTaskStatusEnum.FAILED);
            }
        }
    }

}
