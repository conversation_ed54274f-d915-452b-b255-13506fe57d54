package fm.lizhi.ocean.wavecenter.service.award.family.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/3/26 14:25
 */
@Data
@Accessors(chain = true)
public class GetFamilyAwardDeliverRecordParamDTO {

    /**
     * 公会长用户id
     */
    private Long familyUserId;

    /**
     * 公会id
     */
    private Long familyId;

    /**
     * 公会名称
     */
    private String familyName;

    /**
     * 最小发放时间, 毫秒时间戳, 包含
     */
    private Long minDeliverTime;

    /**
     * 最大发放时间, 毫秒时间戳, 包含
     */
    private Long maxDeliverTime;

}
