package fm.lizhi.ocean.wavecenter.service.sign.manager;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.RoomSignInfoBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.SignPlayerInfoBean;
import fm.lizhi.ocean.wavecenter.service.sign.dto.SMSignPlayerPageListReqDto;
import fm.lizhi.ocean.wavecenter.service.sign.dto.SMSignRoomPageListReqDto;

/**
 * <AUTHOR>
 * @date 2024/6/13 15:19
 */
public interface SignManager {

    /**
     * 查询公会签约厅列表
     * @param reqDto
     * @return
     */
    PageBean<RoomSignInfoBean> signRoomPageList(SMSignRoomPageListReqDto reqDto);

    /**
     * 查询厅签约主播
     * @param reqDto
     * @return
     */
    PageBean<SignPlayerInfoBean> signPlayerPageList(SMSignPlayerPageListReqDto reqDto);

}
