package fm.lizhi.ocean.wavecenter.service.background.activitycenter.impl;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityLevelConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityLevel;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityLevel;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityLevelConfigService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityLevelManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 */
@ServiceProvider
@Slf4j
public class ActivityLevelConfigServiceImpl implements ActivityLevelConfigService {

    @Autowired
    private ActivityLevelManager activityLevelManager;



    @Override
    public Result<Boolean> saveLevel(RequestSaveActivityLevel param) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(param));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(param));
        return ResultHandler.handle(param.getAppId(), () -> activityLevelManager.saveLevel(param));
    }

    @Override
    public Result<Boolean> updateLevel(RequestUpdateActivityLevel param) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(param));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(param));
        return ResultHandler.handle(param.getAppId(), () -> activityLevelManager.updateLevel(param));
    }

    @Override
    public Result<Boolean> deleteLevel(Long id, Integer appId, String operator) {
        LogContext.addReqLog("appId={}, id={}", appId, id);
        LogContext.addResLog("appId={}, id={}", appId, id);
        return ResultHandler.handle(appId, () -> activityLevelManager.deleteLevel(id, operator));
    }

    @Override
    public Result<List<ActivityLevelConfigBean>> listByAppId(Integer appId) {
        LogContext.addReqLog("appId={}", appId);
        LogContext.addResLog("appId={}", appId);
        return ResultHandler.handle(appId, () -> RpcResult.success(activityLevelManager.listByAppId(appId)));
    }

    @Override
    public Result<ActivityLevelConfigBean> getLevelByClassId(Long classId, Integer appId) {
        LogContext.addReqLog("appId={}, classId={}", appId, classId);
        LogContext.addResLog("appId={}, classId={}", appId, classId);

        return ResultHandler.handle(appId, () -> RpcResult.success(activityLevelManager.getLevelByClassId(appId, classId)));
    }
}
