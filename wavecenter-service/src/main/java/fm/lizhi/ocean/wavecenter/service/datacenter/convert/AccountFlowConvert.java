package fm.lizhi.ocean.wavecenter.service.datacenter.convert;

import fm.lizhi.ocean.wavecenter.service.datacenter.dto.PayAccountFlowDTO;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.PlayerAccountFlowDTO;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.RoomAccountFlowDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2025/4/22 20:03
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface AccountFlowConvert {

    AccountFlowConvert I = Mappers.getMapper(AccountFlowConvert.class);

    @Mapping(source = "identity", target = "roomId")
    RoomAccountFlowDTO flowToRoomFlow(PayAccountFlowDTO dto);

    @Mapping(source = "identity", target = "playerId")
    PlayerAccountFlowDTO flowToPlayerFlow(PayAccountFlowDTO dto);

}
