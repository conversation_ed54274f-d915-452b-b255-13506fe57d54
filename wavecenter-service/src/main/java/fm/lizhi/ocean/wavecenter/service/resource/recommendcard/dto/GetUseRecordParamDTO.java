package fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/25 19:22
 */
@Data
@Accessors(chain = true)
public class GetUseRecordParamDTO {

    /**
     * 所属用户ID
     */
    private List<Long> userIds;

    /**
     * 是否根据时间升序
     */
    private boolean createTimeAsc = false;

    private Integer pageNumber = 1;

    private Integer pageSize = 20;

}
