package fm.lizhi.ocean.wavecenter.service.sign.impl;

import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.bean.*;
import fm.lizhi.ocean.wavecenter.api.sign.constant.*;
import fm.lizhi.ocean.wavecenter.api.sign.service.QueryFlowService;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.sign.dto.*;
import fm.lizhi.ocean.wavecenter.service.sign.manager.*;
import fm.lizhi.ocean.wavecenter.service.user.convert.UserCommonConvert;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/10/9 11:46
 */
@Slf4j
@ServiceProvider
public class QueryFlowServiceImpl implements QueryFlowService {

    @Autowired
    private ContractManager contractManager;
    @Autowired
    private SignFlowManager signFlowManager;
    @Autowired
    private NonContractManager nonContractManager;
    @Autowired
    private FamilyManager familyManager;
    @Autowired
    private UserManager userManager;
    @Autowired
    private SettleManager settleManager;

    /**
     * 查询管理员激活的解约流程
     * @param appId
     * @param curUserId 管理员用户id
     * @return
     */
    @Override
    public Result<ActFlowBean> adminActCancelFlow(int appId, long curUserId) {
        LogContext.addReqLog("appId={},curUserId={}", appId, curUserId);
        LogContext.addResLog("appId={},curUserId={}", appId, curUserId);
        return ResultHandler.handle(appId, ()->{
            ActFlowBean actFlowBean = new ActFlowBean();

            FlowInfoBean flowInfo = getAdminActCancelFlow(curUserId);
            actFlowBean.setFlowInfo(flowInfo);

            //查询家族信息
            if (flowInfo != null) {
                actFlowBean.setFamilyInfo(getAdminFlowInfoFamily(RequestFamilyAndNjContractDTO.builder()
                        .contractId(flowInfo.getContractId())));
            }

            actFlowBean.setSignedFamilyInfo(getAdminFlowInfoFamily(RequestFamilyAndNjContractDTO.builder()
                    .relation(SignRelationEnum.SIGN_SUCCESS)
                    .type(ContractTypeEnum.SIGN)
                    .type(ContractTypeEnum.SUBJECT_CHANGE)
                    .type(ContractTypeEnum.RENEW)
                    .njId(curUserId)));

            if (actFlowBean.getFamilyInfo() != null && actFlowBean.getSignedFamilyInfo() != null) {
                //存在解约流程，重新填充原合同签约时间
                MyFamilyBean oldContract = actFlowBean.getSignedFamilyInfo();
                actFlowBean.getFamilyInfo().setSignStartTime(oldContract.getSignStartTime());
                actFlowBean.getFamilyInfo().setSignExpireTime(oldContract.getSignExpireTime());

                PageBean<FamilyAndNjContractBean> pageBean = contractManager.queryContract(RequestFamilyAndNjContractDTO.builder().contractId(oldContract.getContractId()).pageSize(1).build());
                if (CollectionUtils.isNotEmpty(pageBean.getList())) {
                    Map<Long, SignSettleDTO> settleMap = settleManager.querySettle(Lists.newArrayList(
                            pageBean.getList().get(0)
                    ));
                    if (settleMap.containsKey(oldContract.getContractId())) {
                        SignSettleDTO settle = settleMap.get(oldContract.getContractId());
                        actFlowBean.getFamilyInfo().setSettleType(settle.getSettleType());
                        actFlowBean.getFamilyInfo().setSettlePercentage(settle.getSettlePercentage());
                    }
                }
            }

            return RpcResult.success(actFlowBean);
        });
    }

    /**
     * 查询流程相关联的家族信息
     * @return
     */
    private MyFamilyBean getAdminFlowInfoFamily(RequestFamilyAndNjContractDTO.RequestFamilyAndNjContractDTOBuilder builder){

        PageBean<FamilyAndNjContractBean> pageBean = contractManager.queryContract(builder.build());
        if (CollectionUtils.isEmpty(pageBean.getList())) {
            LogContext.addResLog("contract not exist.");
            return null;
        }

        FamilyAndNjContractBean contract = pageBean.getList().get(0);

        Optional<FamilyBean> familyOp = familyManager.getFamily(ContextUtils.getBusinessEvnEnum().appId(), contract.getFamilyId());
        if (!familyOp.isPresent()) {
            LogContext.addResLog("family not exist.");
            return null;
        }

        FamilyBean familyBean = familyOp.get();

        List<SimpleUserDto> userList = userManager.getSimpleUserByIds(Lists.newArrayList(familyBean.getUserId()));
        if (CollectionUtils.isEmpty(userList)) {
            LogContext.addResLog("userList is empty");
            return null;
        }
        SimpleUserDto familyUser = userList.get(0);

        MyFamilyBean myFamily = new MyFamilyBean();
        myFamily.setFamilyUser(UserCommonConvert.I.simpleUserDto2userBean(familyUser));
        myFamily.setFamilyId(familyBean.getId());
        myFamily.setFamilyName(familyBean.getFamilyName());
        myFamily.setFamilyIntro(familyBean.getFamilyNote());
        myFamily.setFamilyIconUrl(familyBean.getFamilyIconUrl());
        myFamily.setSignDealLineTime(contract.getSignDeadline());
        myFamily.setSignStartTime(contract.getBeginTime());
        myFamily.setSignExpireTime(contract.getExpireTime());
        myFamily.setCreateTime(contract.getCreateTime());
        myFamily.setContractId(contract.getContractId());
        myFamily.setSignId(contract.getSignId());

        Map<Long, SignSettleDTO> settleMap = settleManager.querySettle(Lists.newArrayList(contract));
        if (settleMap.containsKey(contract.getContractId())) {
            SignSettleDTO settle = settleMap.get(contract.getContractId());
            myFamily.setSettleType(settle.getSettleType());
            myFamily.setSettlePercentage(settle.getSettlePercentage());
        }

        return myFamily;
    }

    /**
     * 管理员查询激活的解约流程信息
     * @param njId
     * @return
     */
    private FlowInfoBean getAdminActCancelFlow(long njId){
        //查询最近一条解约流程
        Optional<FamilyAndNjContractBean> lastCancelOp = contractManager.queryLastCancel(njId);
        if (!lastCancelOp.isPresent()) {
            return null;
        }
        FamilyAndNjContractBean lastCancel = lastCancelOp.get();
        LogContext.addResLog("contractId={},id={}", lastCancel.getContractId(), lastCancel.getId());

        //已完成 忽略
        //忽略解约的终止
        if (SignRelationEnum.SIGN_SUCCESS.getCode().equals(lastCancel.getStatus())
                || SignRelationEnum.STOP_CONTRACT.getCode().equals(lastCancel.getStatus()) ) {
            LogContext.addResLog("status is success");
            return null;
        }


        //流程状态
        Long flowId = queryFlowStatus(lastCancel.getId(), FlowTypeEnum.CANCEL, 1);
        if (flowId == null) {
            return null;
        }

        FlowInfoBean actFlowBean = new FlowInfoBean()
                .setFlowId(flowId)
                .setSignId(lastCancel.getSignId())
                .setContractId(lastCancel.getContractId())
                .setType(FlowTypeEnum.CANCEL.getCode())
                .setCreateUser(lastCancel.getCreateUser())
                .setRelationType("WITH_FAMILY")
                ;
        if (StringUtils.isNotBlank(lastCancel.getCreateUser())) {
            actFlowBean.setSelfCreate(RoleEnum.ROOM.getRoleCode().equals(lastCancel.getCreateUser()));
        }
        adminCancelFlowStatusConvert(actFlowBean, lastCancel);
        return actFlowBean;
    }

    /**
     * 解约激活流程状态转换
     * @param actFlowBean
     * @param contractBean
     */
    private void adminCancelFlowStatusConvert(FlowInfoBean actFlowBean, FamilyAndNjContractBean contractBean){
        LogContext.addResLog("contract status={}", contractBean.getStatus());
        if (SignRelationEnum.REJECT.getCode().equals(contractBean.getStatus())) {
            if (UserSignStatusEnum.REJECT.getCode().equals(contractBean.getFamilySignStatus())) {
                actFlowBean.setStatus(SignRelationEnum.PARTNER_REJECT.getCode());
                return;
            }
        }

        //当前合同是待签署状态
        if (!SignRelationEnum.WAIT_SIGN.getCode().equals(contractBean.getStatus())) {
            actFlowBean.setStatus(contractBean.getStatus());
            return;
        }
        //管理员未签署->待签署
        if (UserSignStatusEnum.WAIT_SIGN.getCode().equals(contractBean.getNjSignStatus())) {
            actFlowBean.setStatus(SignRelationEnum.WAIT_SIGN.getCode());
            return;
        }
        //管理员已签署->待对方签署 或者 其他情况
        actFlowBean.setStatus(SignRelationEnum.WAIT_PARTNER_SIGN.getCode());
    }

    @Override
    public Result<ActFlowBean> playerActCancelFlow(int appId, long curUserId) {
        LogContext.addReqLog("appId={},curUserId={}", appId, curUserId);
        LogContext.addResLog("appId={},curUserId={}", appId, curUserId);
        return ResultHandler.handle(appId, ()->{
            ActFlowBean result = new ActFlowBean();
            FlowInfoBean flowInfoBean = getPlayerActCancelFlow(curUserId);
            result.setFlowInfo(flowInfoBean);
            //查询厅信息
            if (flowInfoBean != null) {
                result.setRoomInfo(getPlayerFlowRoomInfo(QueryNonContractDTO.builder().contractId(flowInfoBean.getContractId())));
            }

            result.setSignedRoomInfo(getPlayerFlowRoomInfo(QueryNonContractDTO.builder()
                    .type(ContractTypeEnum.SIGN)
                    .type(ContractTypeEnum.SUBJECT_CHANGE)
                    .status(SignRelationEnum.SIGN_SUCCESS)
                    .userId(curUserId)));

            if (result.getRoomInfo() != null && result.getSignedRoomInfo() != null) {
                result.getRoomInfo().setSignStartTime(result.getSignedRoomInfo().getSignStartTime());
            }

            return RpcResult.success(result);
        });
    }

    private MyRoomBean getPlayerFlowRoomInfo(QueryNonContractDTO.QueryNonContractDTOBuilder paramBuilder){
        PageBean<NjAndPlayerContractBean> contractList = nonContractManager.queryList(paramBuilder.build());
        if (CollectionUtils.isEmpty(contractList.getList())) {
            LogContext.addResLog("contractList is empty");
            return null;
        }

        NjAndPlayerContractBean contract = contractList.getList().get(0);

        Long njUserId = contract.getNjUserId();
        List<SimpleUserDto> userList = userManager.getSimpleUserByIds(Lists.newArrayList(njUserId));
        if (CollectionUtils.isEmpty(userList)) {
            LogContext.addResLog("userList is empty");
            return null;
        }

        MyRoomBean myRoomBean = new MyRoomBean();
        myRoomBean.setContractId(contract.getContractId());
        myRoomBean.setRoomUser(UserCommonConvert.I.simpleUserDto2userBean(userList.get(0)));
        myRoomBean.setSignDealLineTime(contract.getSignDeadline());
        myRoomBean.setSignStartTime(contract.getStartTime());
        myRoomBean.setCreateTime(contract.getCreateTime());

        Optional<SignSettleDTO> signSettleDTO = settleManager.querySettleByNj(njUserId);
        if (signSettleDTO.isPresent()) {
            myRoomBean.setSettlePercentage(signSettleDTO.get().getSettlePercentage());
            myRoomBean.setSettleType(signSettleDTO.get().getSettleType());
        }

        //厅主家族类型
        UserInFamilyBean userInFamily = familyManager.getUserInFamily(njUserId);
        if (userInFamily.getFamilyId() != null) {
            Optional<FamilyBean> familyOp = familyManager.getFamily(ContextUtils.getBusinessEvnEnum().appId(), userInFamily.getFamilyId());
            familyOp.ifPresent(v->myRoomBean.setFamilyType(v.getFamilyType()));
        }

        return myRoomBean;
    }

    private FlowInfoBean getPlayerActCancelFlow(long playerUserId){
        //查询最近一条解约流程
        Optional<NjAndPlayerContractBean> contractBeanOp = nonContractManager.queryLastCancel(playerUserId);
        if (!contractBeanOp.isPresent()) {
            return null;
        }
        NjAndPlayerContractBean contractBean = contractBeanOp.get();
        LogContext.addResLog("contractId={}", contractBean.getContractId());

        //已完成 忽略
        if (SignRelationEnum.SIGN_SUCCESS.getCode().equals(contractBean.getStatus())
                || SignRelationEnum.CANCEL_CONTRACT.getCode().equals(contractBean.getStatus()) ) {
            LogContext.addResLog("status is success");
            return null;
        }

        //流程状态
        Long flowId = queryFlowStatus(contractBean.getContractId(), FlowTypeEnum.CANCEL, 0);
        if (flowId == null) {
            return null;
        }

        //状态转换
        FlowInfoBean actFlowBean = new FlowInfoBean()
                .setFlowId(flowId)
                .setContractId(contractBean.getContractId())
                .setType(FlowTypeEnum.CANCEL.getCode())
                .setRelationType("WITH_ROOM");
        if (StringUtils.isNotBlank(contractBean.getCreateUser())) {
            actFlowBean.setSelfCreate(RoleEnum.PLAYER.getRoleCode().equals(contractBean.getCreateUser()));
        }
        playerCancelFlowStatusConvert(actFlowBean, contractBean);
        return actFlowBean;
    }

    private void playerCancelFlowStatusConvert(FlowInfoBean actFlowBean, NjAndPlayerContractBean contractBean){
        if (SignRelationEnum.WAIT_SIGN.getCode().equals(contractBean.getStatus())) {
            if (RoleEnum.ROOM.getRoleCode().equals(contractBean.getCreateUser())) {
                //管理员发起->待签署
                actFlowBean.setStatus(SignRelationEnum.WAIT_SIGN.getCode());
            }
            if (RoleEnum.PLAYER.getRoleCode().equals(contractBean.getCreateUser())) {
                //管理员发起->待对方签署
                actFlowBean.setStatus(SignRelationEnum.WAIT_PARTNER_SIGN.getCode());
            }
            return;
        }
        if (SignRelationEnum.REJECT.getCode().equals(contractBean.getStatus())) {
            if (RoleEnum.PLAYER.getRoleCode().equals(contractBean.getCreateUser())) {
                actFlowBean.setStatus(SignRelationEnum.PARTNER_REJECT.getCode());
                return;
            }
        }

        actFlowBean.setStatus(contractBean.getStatus());
    }

    @Override
    public Result<ActFlowBean> userActSignFlow(int appId, long curUserId) {
        LogContext.addReqLog("appId={},curUserId={}", appId, curUserId);
        LogContext.addResLog("appId={},curUserId={}", appId, curUserId);
        return ResultHandler.handle(appId, () -> {
            ActFlowBean actFlowBean = new ActFlowBean();
            FlowInfoBean flowInfo = null;

            //是否存在申请为管理员的记录
            boolean applyNj = true;

            //查询最新一条签约为管理员的记录
            Optional<FamilyAndNjContractBean> njContractBeanOp = contractManager.queryLastSign(curUserId);
            if (njContractBeanOp.isPresent()) {
                flowInfo = new FlowInfoBean();
                FamilyAndNjContractBean njContractBean = njContractBeanOp.get();
                LogContext.addResLog("njContractId={}", njContractBean.getContractId());
                flowInfo.setSignId(njContractBean.getSignId());
                flowInfo.setContractId(njContractBean.getContractId());
                flowInfo.setCreateUser(njContractBean.getCreateUser());

                Optional<FamilyBean> familyOp = familyManager.getFamily(appId, njContractBean.getFamilyId());
                if (familyOp.isPresent()) {
                    //如果是成功的，先确认签约记录是否为待签署，如果是待签署，则继续展示流程，因为黑叶签约会有预计20分钟的同步时间
                    PageBean<FamilyNjSignRecordDTO> signRecordList = contractManager.querySignRecord(QueryFamilyNjSignRecordDTO.builder()
                            .status(SignRelationEnum.WAIT_SIGN)
                            .status(SignRelationEnum.SIGNING)
                            .type(ContractTypeEnum.SIGN)
                            .njId(njContractBean.getNjUserId())
                            .familyUserId(familyOp.get().getUserId())
                            .build());
                    if (CollectionUtils.isNotEmpty(signRecordList.getList()) && SignRelationEnum.SIGN_SUCCESS.getCode().equals(njContractBean.getStatus())) {
                        njContractBean.setStatus(SignRelationEnum.WAIT_CONTRACT_SYNC.getCode());
                    }
                }

                //有->进行中,返回该记录  签署失败且未关闭
                if (SignRelationEnum.SIGN_SUCCESS.getCode().equals(njContractBean.getStatus())
                        || SignRelationEnum.STOP_CONTRACT.getCode().equals(njContractBean.getStatus())
                        || SignRelationEnum.OVERDUE_CONTRACT.getCode().equals(njContractBean.getStatus())
                ) {
                    applyNj = false;
                } else {
                    //检查是否已关闭
                    Long flowId = queryFlowStatus(njContractBean.getContractId(), FlowTypeEnum.SIGN, 1);
                    if (flowId == null) {
                        applyNj = false;
                    }
                    flowInfo.setFlowId(flowId);
                }
            } else {
                applyNj = false;
            }
            LogContext.addResLog("applyNj={}", applyNj);

            if (applyNj) {
                //状态转换
                userSignFlowStatusConvert(flowInfo, njContractBeanOp.get());
                flowInfo.setType(FlowTypeEnum.SIGN.getCode())
                        .setRelationType("WITH_FAMILY");
                if (StringUtils.isNotBlank(njContractBeanOp.get().getCreateUser())) {
                    flowInfo.setSelfCreate(RoleEnum.ROOM.getRoleCode().equals(njContractBeanOp.get().getCreateUser()));
                }

                actFlowBean.setFamilyInfo(getAdminFlowInfoFamily(RequestFamilyAndNjContractDTO.builder()
                        .contractId(flowInfo.getContractId())));

                actFlowBean.setSignedFamilyInfo(getAdminFlowInfoFamily(RequestFamilyAndNjContractDTO.builder()
                        .relation(SignRelationEnum.SIGN_SUCCESS)
                        .type(ContractTypeEnum.SIGN)
                        .type(ContractTypeEnum.RENEW)
                        .type(ContractTypeEnum.SUBJECT_CHANGE)
                        .njId(curUserId)));

                return RpcResult.success(actFlowBean.setFlowInfo(flowInfo));
            }

            //无->查询最新一条签约为主播的记录  已完成 不返回
            Optional<NjAndPlayerContractBean> playerContractOp = nonContractManager.queryLastSign(curUserId);
            if (!playerContractOp.isPresent()) {
                LogContext.addResLog("playerContract is null");
                return RpcResult.success(actFlowBean);
            }
            NjAndPlayerContractBean contractBean = playerContractOp.get();

            //已完成 忽略
            if (SignRelationEnum.SIGN_SUCCESS.getCode().equals(contractBean.getStatus())
                    || SignRelationEnum.STOP_CONTRACT.getCode().equals(contractBean.getStatus())) {
                LogContext.addResLog("status is success");
                return RpcResult.success(actFlowBean);
            }

            //流程状态
            Long flowId = queryFlowStatus(contractBean.getContractId(), FlowTypeEnum.SIGN, 0);
            if (flowId == null) {
                LogContext.addResLog("flowId is null");
                return RpcResult.success(actFlowBean);
            }

            flowInfo = new FlowInfoBean().setFlowId(flowId)
                    .setContractId(contractBean.getContractId())
                    .setType(FlowTypeEnum.SIGN.getCode())
                    .setRelationType("WITH_ROOM");
            if (StringUtils.isNotBlank(contractBean.getCreateUser())) {
                flowInfo.setSelfCreate(RoleEnum.PLAYER.getRoleCode().equals(contractBean.getCreateUser()));
            }
            userSignFlowStatusConvert(flowInfo, contractBean);

            //查询厅信息
            actFlowBean.setRoomInfo(getPlayerFlowRoomInfo(QueryNonContractDTO.builder().contractId(flowInfo.getContractId())));
            actFlowBean.setSignedRoomInfo(getPlayerFlowRoomInfo(QueryNonContractDTO.builder()
                    .type(ContractTypeEnum.SIGN)
                    .type(ContractTypeEnum.SUBJECT_CHANGE)
                    .status(SignRelationEnum.SIGN_SUCCESS)
                    .userId(curUserId)));

            return RpcResult.success(actFlowBean.setFlowInfo(flowInfo));
        });
    }

    private void userSignFlowStatusConvert(FlowInfoBean actFlowBean, FamilyAndNjContractBean njContractBean){
        if (SignRelationEnum.OVERDUE.getCode().equals(njContractBean.getStatus())
                && RoleEnum.ROOM.getRoleCode().equals(njContractBean.getCreateUser())) {
            actFlowBean.setStatus(SignRelationEnum.PARTNER_OVERDUE.getCode());
            return;
        }

        if (SignRelationEnum.REJECT.getCode().equals(njContractBean.getStatus())) {
            if (UserSignStatusEnum.REJECT.getCode().equals(njContractBean.getFamilySignStatus())) {
                actFlowBean.setStatus(SignRelationEnum.PARTNER_REJECT.getCode());
            } else {
                actFlowBean.setStatus(SignRelationEnum.REJECT.getCode());
            }
            return;
        }

        if (!SignRelationEnum.WAIT_SIGN.getCode().equals(njContractBean.getStatus())) {
            actFlowBean.setStatus(njContractBean.getStatus());
            return;
        }
        //自己未签署 -> 签署
        if (UserSignStatusEnum.WAIT_SIGN.getCode().equals(njContractBean.getNjSignStatus())
                ||UserSignStatusEnum.SIGNING.getCode().equals(njContractBean.getNjSignStatus())) {
            actFlowBean.setStatus(SignRelationEnum.WAIT_SIGN.getCode());
            return;
        }
        //家族长未签署 或 其他情况 -> 待对方签署
        actFlowBean.setStatus(SignRelationEnum.WAIT_PARTNER_SIGN.getCode());
    }

    private void userSignFlowStatusConvert(FlowInfoBean actFlowBean, NjAndPlayerContractBean playerContractBean){
        if (SignRelationEnum.OVERDUE.getCode().equals(playerContractBean.getStatus())) {
            if (RoleEnum.PLAYER.getRoleCode().equals(playerContractBean.getCreateUser())) {
                actFlowBean.setStatus(SignRelationEnum.PARTNER_OVERDUE.getCode());
            }
            actFlowBean.setStatus(playerContractBean.getStatus());
            return;
        }

        if (!SignRelationEnum.WAIT_SIGN.getCode().equals(playerContractBean.getStatus())) {
            //异常情况 签署失败
            actFlowBean.setStatus(playerContractBean.getStatus());
            return;
        }
        if (RoleEnum.ROOM.getRoleCode().equals(playerContractBean.getCreateUser())) {
            //管理员发起
            actFlowBean.setStatus(SignRelationEnum.WAIT_SIGN.getCode());
            return;
        }
        //其他情况 -> 待对方签署
        actFlowBean.setStatus(SignRelationEnum.WAIT_PARTNER_SIGN.getCode());
    }

    /**
     * 查询流程 并保存流程状态
     * @param contractId
     * @param hasContract
     * @return
     */
    private Long queryFlowStatus(Long contractId, FlowTypeEnum flowTypeEnum, Integer hasContract){
        //查询流程关闭状态判断流程是否已被关闭
        Optional<SignFlowBean> signFlowBeanOp = signFlowManager.queryContractFlowBySignId(contractId, flowTypeEnum.getCode(), hasContract);
        if (signFlowBeanOp.isPresent() && SignFlowStatusEnum.CLOSED.getCode().equals(signFlowBeanOp.get().getStatus())) {
            LogContext.addResLog("flow is closed");
            return null;
        }

        Long flowId = null;
        if (signFlowBeanOp.isPresent()) {
            flowId = signFlowBeanOp.get().getFlowId();
        } else {
            SignFlowBean signFlowBean = new SignFlowBean()
                    .setContractId(contractId)
                    .setType(flowTypeEnum.getCode())
                    .setHasContract(hasContract)
                    .setStatus(SignFlowStatusEnum.RUNNING.getCode());
            flowId = signFlowManager.addSignFlow(signFlowBean);
        }

        return flowId;
    }
}