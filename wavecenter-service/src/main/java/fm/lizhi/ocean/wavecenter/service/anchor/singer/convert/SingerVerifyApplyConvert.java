package fm.lizhi.ocean.wavecenter.service.anchor.singer.convert;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SongInfo;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestSingerVerifyApplyV2;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseGetSingerEntranceInfo;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseGetSingerStatus;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SongInfoDTO;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestSingerVerifyApply;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerVerifyApplyDTO;

import java.util.Collections;
import java.util.List;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface SingerVerifyApplyConvert {

    SingerVerifyApplyConvert I = Mappers.getMapper(SingerVerifyApplyConvert.class);

    @Mapping(target = "socialVerifyImageList", source = "req.originalSingerInfo.socialVerifyImageList")
    @Mapping(target = "originalSongUrl", source = "req.originalSingerInfo.originalSongUrl")
    @Mapping(target = "originalSinger", source = "req.originalSingerInfo.originalSinger")
    @Mapping(target = "familyId", source = "userInFamily.familyId")
    @Mapping(target = "idCardNumber", source = "idCardNumber")
    @Mapping(target = "njId", source = "userInFamily.njId")
    @Mapping(target = "preAuditRejectReason", constant = "")
    @Mapping(target = "rejectReason", constant = "")
    @Mapping(target = "auditStatus", expression = "java(fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerAuditStatusEnum.WAIT_AUDIT.getStatus())")
    SingerVerifyApplyDTO req2Dto(RequestSingerVerifyApplyV2 req, UserInFamilyBean userInFamily, String idCardNumber);


    @Mapping(target = "preAuditRejectReason", constant = "")
    SongInfoDTO songInfo2Dto(SongInfo songInfo);

    @Mapping(target = "nextSingerEntranceInfo", source = "info.nextSingerType")
    @Mapping(target = "currentSingerEntranceInfo", source = "info.currentSingerType")
    @Mapping(target = "currentSingerEntranceInfo.entranceShowSwitch", ignore = true)
    @Mapping(target = "nextSingerEntranceInfo.entranceShowSwitch", ignore = true)
    ResponseGetSingerEntranceInfo statusInfo2EntranceInfo(ResponseGetSingerStatus info, boolean currentSingerEntranceSwitch, boolean nextSingerEntranceSwitch);

    //TODO 编译报错临时处理
    @Mapping(target = "contactNumber", ignore = true)
    @Mapping(target = "originalSingerInfo", source = "request.originalSingerInfo")
    @Mapping(target = "songInfos", expression = "java(buildSongInfoList(request))")
    @Mapping(target = "singerType", source = "request.singerType")
    @Mapping(target = "userId", source = "request.userId")
    @Mapping(target = "appId", source = "request.appId")
    RequestSingerVerifyApplyV2 toRequestSingerVerifyApplyV2(RequestSingerVerifyApply request);

    default List<SongInfo> buildSongInfoList(RequestSingerVerifyApply request) {
        SongInfo songInfo = new SongInfo();
        songInfo.setSongName(request.getSongName());
        songInfo.setAudioPath(request.getAudioPath());
        songInfo.setSongStyle(request.getSongStyle());
        songInfo.setPreAuditStatus(request.getPreAuditStatus());
        return Collections.singletonList(songInfo);
    }

}
