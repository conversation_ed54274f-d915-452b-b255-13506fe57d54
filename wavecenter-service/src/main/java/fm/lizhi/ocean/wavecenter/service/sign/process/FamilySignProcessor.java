package fm.lizhi.ocean.wavecenter.service.sign.process;

import fm.lizhi.ocean.wavecenter.api.sign.bean.FamilyAndNjContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.request.*;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseFamilyApplyCancelAdmin;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseFamilyDoSign;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseFamilyInviteAdmin;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseFamilyReviewCancel;
import fm.lizhi.ocean.wavecenter.base.processor.BusinessEnvAwareProcessor;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/10/14 15:29
 */
public interface FamilySignProcessor extends BusinessEnvAwareProcessor {

    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return FamilySignProcessor.class;
    }

    /**
     * 审批解约申请检查
     * @param request
     * @return
     */
    default ResponseFamilyReviewCancel reviewCancelCheck(RequestFamilyReviewCancel request) {
        return new ResponseFamilyReviewCancel().setCode(-1);
    }

    /**
     * 签署前置检查
     * @param request
     * @return
     */
    ResponseFamilyDoSign doSignCheck(RequestFamilyDoSign request);

    /**
     * 签署成功后置处理
     * @param request
     */
    default void doSignSuccessProcessor(RequestFamilyDoSign request) {

    }

    /**
     * 邀请用户签约为管理员检查
     * @param request
     * @return
     */
    ResponseFamilyInviteAdmin inviteAdminCheck(RequestFamilyInviteAdmin request);

    /**
     * 邀请成功后置处理
     * @param request
     */
    default void inviteAdminSuccessProcessor(RequestFamilyInviteAdmin request, Long contractId) {

    }

    /**
     * 检查是否存在待签署合同
     * @param request
     * @return
     */
    Optional<FamilyAndNjContractBean> existWaitSignContract(RequestFamilyInviteAdmin request);

    /**
     * 申请解约管理员检查
     * @param request
     * @return
     */
    ResponseFamilyApplyCancelAdmin applyCancelAdmin(RequestFamilyApplyCancelAdmin request);

    /**
     * 申请解约管理员成功后置处理
     * @param request
     * @param contractId
     */
    default void applyCancelAdminSuccessProcessor(RequestFamilyApplyCancelAdmin request, Long contractId){

    }

    default void doCancelAdminSuccessProcessor(RequestFamilyDoCancelAdmin request) {

    }


}
