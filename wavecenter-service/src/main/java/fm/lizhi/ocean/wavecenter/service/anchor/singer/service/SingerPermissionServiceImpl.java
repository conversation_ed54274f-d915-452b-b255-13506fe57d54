package fm.lizhi.ocean.wavecenter.service.anchor.singer.service;

import cn.hutool.core.collection.CollUtil;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestCheckSingerPermission;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.service.SingerPermissionService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerMenuConfigDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerInfoDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerAuditConfigManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerBlackListManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerHallApplyManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerInfoManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@ServiceProvider
@Slf4j
public class SingerPermissionServiceImpl implements SingerPermissionService {


    @Autowired
    private SingerHallApplyManager singerHallApplyManager;

    @Autowired
    private SingerInfoManager singerInfoManager;

    @Autowired
    private SingerAuditConfigManager singerAuditConfigManager;

    @Autowired
    private SingerBlackListManager singerBlackListManager;

    @Override
    public Result<Boolean> checkSingerPermission(RequestCheckSingerPermission request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));

        final Long userId = request.getUserId();
        final int appId = request.getAppId();

        Boolean inBlackList = singerBlackListManager.isInBlackList(appId, userId);
        if (inBlackList) {
            // 检查是否在黑名单中
            log.info("User in black list, userId:{}, appId:{}", userId, appId);
            return RpcResult.success(false);
        }

        // 检查菜单配置和权限
        SingerMenuConfigDTO newConfig = singerAuditConfigManager.getApplyMenuConfig(appId, SingerTypeEnum.NEW.getType());
        SingerMenuConfigDTO qualityConfig = singerAuditConfigManager.getApplyMenuConfig(appId, SingerTypeEnum.QUALITY.getType());
        boolean newMenuEnable = singerAuditConfigManager.checkMenuConfig(newConfig, SingerTypeEnum.NEW);
        boolean qualityMenuEnable = singerAuditConfigManager.checkMenuConfig(qualityConfig, SingerTypeEnum.QUALITY);

        if (!newMenuEnable && !qualityMenuEnable){
            // 入口时间不符合，直接不开启
            log.info("New and quality menu not enabled, userId:{}, appId:{}", userId, appId);
            return RpcResult.success(false);
        }

        // 前置条件检查：必须在点唱厅中
        if (!singerHallApplyManager.isInSingingHall(appId, userId)) {
            log.info("User not in singing hall, userId:{}, appId:{}", userId, appId);
            return RpcResult.success(false);
        }

        // 获取当前用户的歌手信息
        List<SingerInfoDTO> currentUserSingers = getCurrentUserSingerInfo(userId, appId);
        boolean hasQuality = checkQualityPermission(qualityConfig, currentUserSingers, userId);
        boolean hasNew = checkNewPermission(newConfig, currentUserSingers, userId);

        return RpcResult.success(hasQuality || hasNew);
    }

    /**
     * 获取当前用户的歌手信息
     *
     * @param userId 当前用户ID
     * @param appId 应用ID
     * @return 当前用户的歌手信息列表
     */
    private List<SingerInfoDTO> getCurrentUserSingerInfo(Long userId, int appId) {
        return singerInfoManager.getSingerInfoByUserId(appId, userId,
                Arrays.asList(SingerStatusEnum.EFFECTIVE, SingerStatusEnum.AUTHENTICATING)
        );

    }

    /**
     * 检查是否有优质歌手权限
     *
     * @param config  配置信息
     * @param singers 当前用户的歌手信息列表
     * @param userId
     * @return 是否有优质歌手权限
     */
    private boolean checkQualityPermission(SingerMenuConfigDTO config, List<SingerInfoDTO> singers, Long userId) {
        // 配置检查
        boolean configValid = singerAuditConfigManager.checkMenuConfig(config, SingerTypeEnum.QUALITY);

        if (CollUtil.isEmpty(singers)) {
            // 没有符合条件的歌手信息，直接返回false
            log.info("quality type no singer info found for user. userId:{}", userId);
            return false;
        }

        // 用户身份检查
        boolean hasQualityIdentity = singers.stream()
                .anyMatch(s -> s.getSingerType() == SingerTypeEnum.QUALITY.getType()
                        || s.getSingerType() == SingerTypeEnum.STAR.getType());

        return configValid || hasQualityIdentity;
    }

    /**
     * 检查是否有新锐歌手权限
     *
     * @param config  配置信息
     * @param singers 当前用户的歌手信息列表
     * @param userId
     * @return 是否有新锐歌手权限
     */
    private boolean checkNewPermission(SingerMenuConfigDTO config, List<SingerInfoDTO> singers, Long userId) {
        // 配置检查
        boolean configValid = singerAuditConfigManager.checkMenuConfig(config, SingerTypeEnum.NEW);
        if (CollUtil.isEmpty(singers) && !configValid) {
            // 没有符合条件的歌手信息，直接返回false
            log.info("new type no singer info found for user. userId:{}", userId);
            return false;
        }

        // 用户身份检查
        boolean hasNewIdentity = singers.stream()
                .anyMatch(s -> s.getSingerType() == SingerTypeEnum.NEW.getType());

        return configValid || hasNewIdentity;
    }
}
