package fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto;

import lombok.Data;

import java.util.Date;

@Data
public class ActivityResourceSimpleInfoDTO {

    private Long id;

    /**
     * 资源名称
     */
    private String name;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 资源code，只有自动配置的资源有
     */
    private String resourceCode;

    /**
     * 资源介绍
     */
    private String introduction;

    /**
     * 资源图片，斜杠开头
     */
    private String imageUrl;

    /**
     * 资源配置类型，1：自动，2：手动
     */
    private Integer deployType;

    /**
     * 状态，0：禁用，1：启用
     */
    private Integer status;

    /**
     * 是否必选
     */
    private Boolean required;

    /**
     * 操作者
     */
    private String operator;


    /**
     * 修改时间
     */
    private Date modifyTime;

}
