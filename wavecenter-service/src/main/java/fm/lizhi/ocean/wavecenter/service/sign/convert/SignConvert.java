package fm.lizhi.ocean.wavecenter.service.sign.convert;

import fm.lizhi.ocean.wavecenter.api.sign.bean.*;
import fm.lizhi.ocean.wavecenter.service.sign.dto.FamilyNjSignRecordDTO;
import fm.lizhi.ocean.wavecenter.service.sign.dto.SMSignPlayerPageListReqDto;
import fm.lizhi.ocean.wavecenter.service.sign.dto.SMSignRoomPageListReqDto;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/13 15:23
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface SignConvert {

    SignConvert I = Mappers.getMapper(SignConvert.class);

    SMSignRoomPageListReqDto gMSSignRoomPageListReq2Dto(GMSSignRoomPageListReq bean);

    SMSignPlayerPageListReqDto gMSSignPlayerPageListReq2Dto(GMSSignPlayerPageListReq bean);

    @Mappings({
            @Mapping(target = "roomInfo.id", source = "njUserId")
    })
    PlayerSignHistoryRecordBean njAndPlayerContractBean2HistoryBean(NjAndPlayerContractBean bean);

    @Mappings({
            @Mapping(source = "roomInfo.id", target = "njUserId"),
    })
    FamilyAndNjContractBean roomSignRecordBean2familyNjSign(RoomSignRecordBean bean);

    List<FamilyAndNjContractBean> roomSignRecordBeans2familyNjSigns(List<RoomSignRecordBean> bean);

    FamilyNjSignRecordBean toFamilyNjSignRecordBean(FamilyNjSignRecordDTO dto);
}
