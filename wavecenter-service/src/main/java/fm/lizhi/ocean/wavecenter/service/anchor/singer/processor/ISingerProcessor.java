package fm.lizhi.ocean.wavecenter.service.anchor.singer.processor;

import cn.hutool.core.util.StrUtil;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeMappingEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseGetAllSingerStatics;
import fm.lizhi.ocean.wavecenter.base.processor.BusinessEnvAwareProcessor;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerInfoDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ISingerProcessor extends BusinessEnvAwareProcessor {

    @Override
    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return ISingerProcessor.class;
    }

    /**
     * 导入歌手
     *
     * @param ppImportSingerMap 平台ID-歌手ID
     * @return 导入成功的数量
     */
    int importSinger(Map<Integer, List<Long>> ppImportSingerMap);


    /**
     * 获取需要关联淘汰的歌手库 ID
     */
    List<Long> getRelatedEliminateSingerIds(List<Long> userIds, int appId, List<Long> ids);


    /**
     * 填充歌手统计信息
     */
    ResponseGetAllSingerStatics fillSingerStatics(Integer appId, ResponseGetAllSingerStatics result);

    /**
     * 查询出待自动淘汰的歌手信息
     *
     * @param appId  应用ID
     * @param userId 用户ID
     * @return 歌手信息列表
     */
    List<SingerInfoDTO> getWaitAutoEliminateSingerInfo(Integer appId, long userId);


    /**
     * 校验歌手等级是否存在
     *
     * @param singerType 歌手等级
     * @param evnEnum    业务环境
     * @return 是否存在
     */
    default boolean isInvalidSingerType(Integer singerType, BusinessEvnEnum evnEnum) {
        String bizSingerType = SingerTypeMappingEnum.getBizSingerType(evnEnum, SingerTypeEnum.getByType(singerType));
        return StrUtil.isEmpty(bizSingerType);
    }


}
