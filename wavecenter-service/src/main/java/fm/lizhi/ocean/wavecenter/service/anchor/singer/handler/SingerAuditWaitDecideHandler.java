package fm.lizhi.ocean.wavecenter.service.anchor.singer.handler;

import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerExecuteAuditDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.convert.SingerVerifyRecordConvert;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerAuditParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerVerifyRecordDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.UpdateSingerVerifyStatusParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerVerifyApplyManager;
import lombok.extern.slf4j.Slf4j;

/**
 * 歌手认证待定状态处理器
 */
@Slf4j
@Component
public class SingerAuditWaitDecideHandler extends AbstractSingerAuditHandler {

    @Autowired
    private SingerVerifyApplyManager singerVerifyApplyManager;

    @Override
    public SingerExecuteAuditDTO auditHandle(SingerAuditParamDTO param, SingerVerifyRecordDTO verifyRecord) {
        // 选中时，歌手库状态设置为认证中
        UpdateSingerVerifyStatusParamDTO paramDTO = SingerVerifyRecordConvert.I.buildUpdateParam(verifyRecord, param, SingerAuditStatusEnum.WAIT_DECIDE.getStatus(),
                SingerStatusEnum.AUTHENTICATING.getStatus(), false, verifyRecord.getSingerType());
        boolean res = singerVerifyApplyManager.updateSingerVerifyRecordStatus(paramDTO);
        return res ? SingerExecuteAuditDTO.success() : SingerExecuteAuditDTO.failure("修改状态失败，请稍候重试!");
    }

    @Override
    public SingerAuditStatusEnum getAuditStatusEnum() {
        return SingerAuditStatusEnum.WAIT_DECIDE;
    }
}
