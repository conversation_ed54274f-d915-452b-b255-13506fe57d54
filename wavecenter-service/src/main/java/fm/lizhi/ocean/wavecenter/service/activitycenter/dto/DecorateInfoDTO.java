package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class DecorateInfoDTO {

    /**
     * id
     */
    private Long id;

    /**
     * 装扮名
     */
    private String name;

    /**
     * 装扮类型
     */
    private Integer type;


    /**
     * 价格
     */
    private Integer coin;

    /**
     * 缩略图地址
     */
    private String thumbUrl;

    /**
     * 有效期，分钟
     */
    private Integer vailMin;

}
