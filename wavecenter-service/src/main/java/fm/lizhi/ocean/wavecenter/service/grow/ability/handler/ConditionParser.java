package fm.lizhi.ocean.wavecenter.service.grow.ability.handler;

import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.condition.AbstractCondition;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.ability.task.condition.ComparatorEnum;
import fm.lizhi.ocean.wavecenter.service.grow.tasktemplate.dto.ConditionDTO;

/**
 * <AUTHOR>
 * @date 2025/6/9 14:44
 */
public interface ConditionParser {

    ComparatorEnum getComparator();

    AbstractCondition<?, ?> parse(ConditionDTO conditionDTO);

}
