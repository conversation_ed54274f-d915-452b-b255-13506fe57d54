package fm.lizhi.ocean.wavecenter.service.datacenter.manager;

import fm.lizhi.ocean.wavecenter.service.datacenter.dto.AssessTimeDto;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.PerformanceInfoDto;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/4/18 18:16
 */
public interface AssessmentManager {

    /**
     * 查询本期考核周期
     *
     * @param appId
     * @param familyId
     * @return
     */
    AssessTimeDto getCurrentTime(int appId, long familyId);

    /**
     * 查询上期考核周期
     * @param appId
     * @param familyId
     * @return
     */
    AssessTimeDto getPreTime(int appId, long familyId);

    /**
     * 查询考核信息
     * @param familyId
     * @param roomId
     * @return
     */
    Optional<PerformanceInfoDto> getPerformanceInfo(Long familyId, Long roomId);

}
