package fm.lizhi.ocean.wavecenter.service.sign.manager;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.SignFlowBean;
import fm.lizhi.ocean.wavecenter.service.sign.dto.QueryFlowParamDTO;
import fm.lizhi.ocean.wavecenter.service.sign.dto.QuerySignStatusSyncDTO;
import fm.lizhi.ocean.wavecenter.service.sign.dto.SignStatusSyncDTO;

import java.util.Optional;

/**
 * 签约流程管理
 * <AUTHOR>
 * @date 2024/10/9 14:47
 */
public interface SignFlowManager {

    /**
     * 查询电子签流程列表
     * @param contractId
     * @return
     */
    Optional<SignFlowBean> queryContractFlowBySignId(Long contractId, String type, Integer hasContract);

    /**
     * 保存流程
     * @param signFlowBean
     */
    Long addSignFlow(SignFlowBean signFlowBean);

    /**
     * 修改状态
     * @param flowId
     * @param status
     */
    void changeStatus(Long flowId, String status);

    /**
     * 修改确认状态
     * @param syncId
     * @param confirmStatus
     */
    void changeConfirmStatus(Long syncId, String confirmStatus);

    /**
     * 流程查询
     * @param param
     * @return
     */
    PageBean<SignFlowBean> queryFlow(QueryFlowParamDTO param);

    /**
     * 查询同步状态
     * @param param
     * @return
     */
    PageBean<SignStatusSyncDTO> querySignStatusSync(QuerySignStatusSyncDTO param);

    /**
     * 保存同步记录
     * @param dto
     */
    void addSignStatusSync(SignStatusSyncDTO dto);

    /**
     * 锁住流程
     * @param flowId
     * @return
     */
    boolean lockFlow(Long flowId);

    /**
     * 释放锁
     * @param flowId
     */
    void unlockFlow(Long flowId);

}
