package fm.lizhi.ocean.wavecenter.service.anchor.singer.handler;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.common.utils.RedisLock;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerAuditParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerExecuteAuditDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerVerifyRecordDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.handler.machine.SingerAuditStatusModel;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerRedisManager;
import fm.lizhi.ocean.wavecenter.service.message.manager.SingerPushManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 歌手认证状态处理器抽象类
 */
@Slf4j
public abstract class AbstractSingerAuditHandler implements SingerAuditStatusHandler {

    @Autowired
    private SingerRedisManager singerRedisManager;

    @Autowired
    private SingerPushManager singerPushManager;

    @Override
    public SingerExecuteAuditDTO executeAudit(SingerAuditParamDTO param, SingerVerifyRecordDTO verifyRecord) {
        try (RedisLock lock = singerRedisManager.tryGetSingerUpdateLock(verifyRecord.getAppId(), verifyRecord.getNjId())) {
            if (!lock.tryLock()) {
                log.warn("executeAudit.tryGetSingerUpdateLock，userId:{}, appId:{}", verifyRecord.getUserId(), verifyRecord.getAppId());
                //加锁失败，结束
                return SingerExecuteAuditDTO.failure("记录信息发生变更，请刷新后重试！");
            }
            if (!isAllowStatusChange(verifyRecord.getAuditStatus(), param.getTargetAuditStatus())) {
                log.warn("executeAudit.isAllowStatusChange fail，userId:{}, auditStatus:{},targetAuditStatus:{}",
                        verifyRecord.getUserId(), verifyRecord.getAuditStatus(), param.getTargetAuditStatus());
                SingerAuditStatusEnum originalStatus = SingerAuditStatusEnum.getByType(verifyRecord.getAuditStatus());
                SingerAuditStatusEnum targetStatus = SingerAuditStatusEnum.getByType(param.getTargetAuditStatus());
                if (originalStatus == null || targetStatus == null) {
                    return SingerExecuteAuditDTO.failure("非法状态，无法流转到目标状态");
                }
                String reason = String.format("无法从:%s 状态流转到：%s 状态", originalStatus.getName(), targetStatus.getName());
                return SingerExecuteAuditDTO.failure(reason);
            }

            //具体审核执行操作
            SingerExecuteAuditDTO singerExecuteAuditDTO = auditHandle(param, verifyRecord);
            if (singerExecuteAuditDTO.isSuccess()) {
                singerPushManager.pushVerifyStatusChange(verifyRecord.getAppId(), verifyRecord.getUserId());
            }
            return singerExecuteAuditDTO;
        } catch (Exception e) {
            log.error("AbstractSingerAuditHandler.executeAudit happen error: param={}", JsonUtil.dumps(param), e);
            return SingerExecuteAuditDTO.failure("发生未知异常，请联系管理员");
        }
    }

    /**
     * 状态流转处理
     *
     * @param param        请求参数
     * @param verifyRecord 认证记录
     * @return 是否处理成功
     */
    abstract SingerExecuteAuditDTO auditHandle(SingerAuditParamDTO param, SingerVerifyRecordDTO verifyRecord);

    /**
     * 状态流转是否允许
     *
     * @param currentStatus 当前状态
     * @param targetStatus  目标状态
     * @return 是否允许
     */
    protected boolean isAllowStatusChange(Integer currentStatus, Integer targetStatus) {
        return SingerAuditStatusModel.isAllowStatusChange(currentStatus, targetStatus);
    }

}
