package fm.lizhi.ocean.wavecenter.service.anchor.singer.processor;

import java.util.List;
import java.util.Map;
import java.util.Set;

import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.base.processor.BusinessEnvAwareProcessor;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerInfoDTO;

/**
 * <AUTHOR>
 */
public interface ISingerHallApplyProcessor extends BusinessEnvAwareProcessor {

    /**
     * 查询当前用户所在厅是否是点唱厅
     * 
     * @param appId  应用ID
     * @param njId 用户ID
     * @return 结果, true表示在点唱厅, false表示不在点唱厅
     */
    boolean isInSingingHall(Integer appId, Long njId);

    /**
     * 批量查询点唱厅审核状态
     * 
     * @param appId 应用ID
     * @param ids 厅主ID列表
     * @return 结果, key为厅主ID, value: 点唱厅状态
     */
    Map<Long, Integer> batchGetSingerHallStatusMap(int appId, List<Long> ids);

    /**
     * 点唱厅审核状态异常需淘汰
     *
     * @param singerInfoList     歌手信息列表
     * @param familyBean         用户家族信息
     * @param eliminateSingerIds 淘汰歌手ID列表
     * @param deleteUserTagIds   删除淘汰标记的用户ID列表
     */
    void handleWaitEliminateSingerByHallStatus(List<SingerInfoDTO> singerInfoList, UserInFamilyBean familyBean, Set<Long> eliminateSingerIds, Set<Long> deleteUserTagIds);

    /**
     * 是否拒绝认证申请
     *
     * @param familyBean            用户家族信息
     * @return 结果, true表示拒绝认证申请, false表示不拒绝
     */
    boolean isRejectWaitAuditVerify(UserInFamilyBean familyBean);

    @Override
    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return ISingerHallApplyProcessor.class;
    }

}
