package fm.lizhi.ocean.wavecenter.service.live.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.live.bean.*;
import fm.lizhi.ocean.wavecenter.api.live.service.LiveGuildCheckInService;
import fm.lizhi.ocean.wavecenter.api.user.bean.RoomBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.live.manager.LiveGuildCheckInManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@ServiceProvider
@Deprecated
public class LiveGuildCheckInServiceImpl implements LiveGuildCheckInService {


    @Autowired
    private LiveGuildCheckInManager liveGuildCheckInManager;

    @Autowired
    private UserManager userManager;

    @Override
    public Result<LGCSRoomDayStatsRes> roomDayStats(GuildRoomDayCheckStatsReq req) {
        LogContext.addReqLog("req={}", JsonUtil.dumps(req));
        LogContext.addResLog("req={}", JsonUtil.dumps(req));
        return ResultHandler.handle(req.getAppId(), () -> {
            int total = MyDateUtil.getRangeTotal(req.getStartDate(), req.getEndDate());
            LGCSRoomDayStatsRes roomDayStatsResResult = liveGuildCheckInManager.roomDayStats(req);
            roomDayStatsResResult.setTotal(total);
            List<GuildRoomDayStatsRes> roomDayStatsRes = roomDayStatsResResult.getList();
            Set<Long> userIds = roomDayStatsRes.stream().map(e->e.getRoom().getId()).collect(Collectors.toSet());

            if(CollectionUtils.isEmpty(userIds)){
                return RpcResult.success(new LGCSRoomDayStatsRes());
            }

            List<SimpleUserDto> userInfoList = userManager.getSimpleUserByIds(new ArrayList<>(userIds));
            Map<Long, SimpleUserDto> userInfoMap = userInfoList.stream().collect(Collectors.toMap(SimpleUserDto::getId, v -> v));

            for (GuildRoomDayStatsRes e : roomDayStatsRes) {
                RoomBean room = e.getRoom();
                SimpleUserDto simpleUserDto = userInfoMap.get(room.getId());
                if(simpleUserDto!=null){
                    room.setBand(simpleUserDto.getBand());
                    room.setPhoto(simpleUserDto.getAvatar());
                    room.setName(simpleUserDto.getName());
                }
            }

            return RpcResult.success(roomDayStatsResResult);
        });
    }


    @Override
    public Result<LGCSRoomHourStatsRes> roomHourStats(GuildRoomHourCheckStatsReq req) {
        LogContext.addReqLog("req={}", JsonUtil.dumps(req));
        LogContext.addResLog("req={}", JsonUtil.dumps(req));
        return ResultHandler.handle(req.getAppId(), () -> {

            LGCSRoomHourStatsRes statsResResult = liveGuildCheckInManager.roomHourStats(req);
            List<GuildRoomHourStatsRes> statsRes = statsResResult.getList();
            Set<Long> userIds = statsRes.stream().map(e->e.getRoom().getId()).collect(Collectors.toSet());

            if(CollectionUtils.isEmpty(userIds)){
                return RpcResult.success(new LGCSRoomHourStatsRes());
            }

            List<SimpleUserDto> userInfoList = userManager.getSimpleUserByIds(new ArrayList<>(userIds));
            Map<Long, SimpleUserDto> userInfoMap = userInfoList.stream().collect(Collectors.toMap(SimpleUserDto::getId, v -> v));

            for (GuildRoomHourStatsRes e : statsRes) {
                RoomBean room = e.getRoom();
                SimpleUserDto simpleUserDto = userInfoMap.get(room.getId());
                if(simpleUserDto!=null){
                    room.setBand(simpleUserDto.getBand());
                    room.setPhoto(simpleUserDto.getAvatar());
                    room.setName(simpleUserDto.getName());
                }
            }

            return RpcResult.success(statsResResult);
        });
    }


    @Override
    public Result<GuildRoomDayStatsSummaryRes> roomDayStatsSummary(GuildRoomDayCheckStatsReq req) {
        LogContext.addReqLog("req={}", JsonUtil.dumps(req));
        LogContext.addResLog("req={}", JsonUtil.dumps(req));
        return ResultHandler.handle(req.getAppId(), () -> RpcResult.success(liveGuildCheckInManager.roomDayStatsSummary(req)));
    }

    @Override
    public Result<GuildRoomHourStatsSummaryRes> roomHourStatsSummary(GuildRoomHourCheckStatsReq req) {
        LogContext.addReqLog("req={}", JsonUtil.dumps(req));
        LogContext.addResLog("req={}", JsonUtil.dumps(req));
        return ResultHandler.handle(req.getAppId(), () -> RpcResult.success(liveGuildCheckInManager.roomHourStatsSummary(req)));
    }
}
