package fm.lizhi.ocean.wavecenter.service.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.*;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityStatusEnum;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityClassificationConfigBean;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityApplyParamDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityFlowResourceDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityParamDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityProcessDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivitySimpleInfoDT0;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityTemplateInfoDTO;
import fm.lizhi.ocean.wavecenter.service.common.constants.AuditTypeEnum;
import fm.lizhi.ocean.wavecenter.service.common.dto.ActivityApplyDataAuditParamDTO;
import fm.lizhi.ocean.wavecenter.service.common.manager.IdManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


@Component
public class ActivityApplyConverter {

    @Autowired
    private IdManager idManager;

    private static final int GET_FAMILY_FAIL = 1;

    /**
     * 构建申请信息
     *
     * @param activityParamDTO 参数
     * @param familyId         家族ID
     * @return 构建结果
     */
    public ActivityApplyParamDTO buildApplyParam(ActivityParamDTO activityParamDTO, Long familyId) {
        ActivityApplyParamDTO applyParamDTO = ActivityApplyMappingConvert.I.applyInfoBean2DTO(activityParamDTO);
        applyParamDTO.setAccompanyNjIds(StringUtils.join(activityParamDTO.getAccompanyNjIds(), ","));
        applyParamDTO.setActivityTool(StringUtils.join(activityParamDTO.getActivityTool(), ","));
        applyParamDTO.setMaxSeatCount(activityParamDTO.getMaxSeatCount() == null ? 0 : activityParamDTO.getMaxSeatCount());

        //处理图片地址
        if (CollectionUtils.isNotEmpty(activityParamDTO.getRoomAnnouncementImgUrl())) {
            Set<String> collect = activityParamDTO.getRoomAnnouncementImgUrl().stream().map(UrlUtils::removeAnyHost).collect(Collectors.toSet());
            applyParamDTO.setRoomAnnouncementImgUrl(StringUtils.join(collect, ","));
        } else {
            applyParamDTO.setRoomAnnouncementImgUrl("");
        }

        //处理海报
        if (StringUtils.isNotEmpty(activityParamDTO.getPosterUrl())) {
            applyParamDTO.setPosterUrl(UrlUtils.removeAnyHost(activityParamDTO.getPosterUrl()));
        }

        //处理辅助道具
        if (CollectionUtils.isNotEmpty(activityParamDTO.getAuxiliaryPropUrl())) {
            Set<String> collect = activityParamDTO.getAuxiliaryPropUrl().stream().map(UrlUtils::removeAnyHost).collect(Collectors.toSet());
            applyParamDTO.setAuxiliaryPropUrl(StringUtils.join(collect, ","));
        } else {
            applyParamDTO.setAuxiliaryPropUrl("");
        }

        //处理流量资源
        List<ActivityFlowResourceBean> flowResources = activityParamDTO.getFlowResources();
        if (CollectionUtils.isNotEmpty(flowResources)) {
            List<ActivityFlowResourceDTO> list = new ArrayList<>();
            for (ActivityFlowResourceBean bean : flowResources) {
                ActivityFlowResourceDTO resourceDTO = new ActivityFlowResourceDTO();
                resourceDTO.setResourceConfigId(bean.getResourceConfigId());
                resourceDTO.setImageUrl(StringUtils.isBlank(bean.getImageUrl()) ? "" : UrlUtils.removeAnyHost(bean.getImageUrl()));
                resourceDTO.setExtra(bean.getExtra() == null ? "" : JsonUtils.toJsonString(bean.getExtra()));
                list.add(resourceDTO);
            }
            applyParamDTO.setFlowResources(list);
        }

        List<ActivityProcessBean> processList = activityParamDTO.getProcessList();
        if (CollectionUtils.isNotEmpty(processList)) {
            List<ActivityProcessDTO> list = new ArrayList<>();
            for (int i = 0; i < processList.size(); i++) {
                ActivityProcessBean bean = processList.get(i);
                ActivityProcessDTO processDto = new ActivityProcessDTO();
                processDto.setName(bean.getName());
                processDto.setExplanation(bean.getExplanation());
                processDto.setDuration(bean.getDuration());
                processDto.setIndex(i + 1);
                list.add(processDto);
            }
            applyParamDTO.setProcessList(list);
        } else {
            applyParamDTO.setProcessList(new ArrayList<>());
        }

        applyParamDTO.setFamilyId(familyId);
        return applyParamDTO;
    }

    /**
     * 构建送审参数
     *
     * @param activityParamDTO 参数bean
     * @return 结果
     */
    public ActivityApplyDataAuditParamDTO buildAuditParamDTO(ActivityParamDTO activityParamDTO) {
        if (activityParamDTO == null || (StringUtils.isBlank(activityParamDTO.getRoomAnnouncement()) && CollectionUtils.isEmpty(activityParamDTO.getRoomAnnouncementImgUrl()))) {
            return null;
        }
        AuditTypeEnum announcementType = AuditTypeEnum.getAnnouncementType(activityParamDTO.getAppId());
        ActivityApplyDataAuditParamDTO paramDTO = new ActivityApplyDataAuditParamDTO();
        paramDTO.setAppId(activityParamDTO.getAppId());
        paramDTO.setApplicantUid(activityParamDTO.getApplicantUid());
        paramDTO.setParentInnerType(announcementType.getParentType());
        List<ActivityApplyDataAuditParamDTO.CheckDataInfo> checkDataInfoList = new ArrayList<>();
        if (StringUtils.isNotBlank(activityParamDTO.getRoomAnnouncement())) {
            ActivityApplyDataAuditParamDTO.CheckDataInfo checkDataInfo = new ActivityApplyDataAuditParamDTO.CheckDataInfo()
                    .setContent(activityParamDTO.getRoomAnnouncement())
                    .setInnerType(announcementType.getType())
                    .setContentId(idManager.genId());
            checkDataInfoList.add(checkDataInfo);
        }

        if (CollectionUtils.isNotEmpty(activityParamDTO.getRoomAnnouncementImgUrl())) {
            for (String url : activityParamDTO.getRoomAnnouncementImgUrl()) {
                ActivityApplyDataAuditParamDTO.CheckDataInfo imageCheckDataInfo = new ActivityApplyDataAuditParamDTO.CheckDataInfo()
                        .setContent(url)
                        .setInnerType(announcementType.getType())
                        .setContentId(idManager.genId());
                checkDataInfoList.add(imageCheckDataInfo);
            }
        }
        paramDTO.setCheckDataList(checkDataInfoList);
        return paramDTO;
    }

    public ResponseQueryActivityListBean buildQueryActivityListResult(PageBean<ActivitySimpleInfoDT0> infoList,
                                                                      Map<Long, ActivityClassificationConfigBean> classificationBeanMap,
                                                                      Map<Long, SimpleUserDto> userInfoMap,
                                                                      Map<Long, String> levleMap,
                                                                      Map<Long, ActivityTemplateInfoDTO> templateMap) {
        //查询出活动分类
        List<ActivitySimpleInfoBean> activitySimpleInfoBeans = ActivityApplyMappingConvert.I.activitySimpleDTOs2Beans(infoList.getList());
        if (CollectionUtils.isNotEmpty(activitySimpleInfoBeans)) {
            long currentTime = System.currentTimeMillis();
            for (ActivitySimpleInfoBean bean : activitySimpleInfoBeans) {
                if (bean.getAuditStatus().equals(ActivityAuditStatusEnum.WAITING_AUDIT.getStatus()) && currentTime >= bean.getStartTime()) {
                    bean.setActivityStatus(ActivityStatusEnum.INVALID.getStatus());
                } else if (bean.getEndTime() < currentTime) {
                    bean.setActivityStatus(ActivityStatusEnum.END.getStatus());
                } else if (bean.getStartTime() > currentTime) {
                    bean.setActivityStatus(ActivityStatusEnum.UN_START.getStatus());
                } else {
                    bean.setActivityStatus(ActivityStatusEnum.START.getStatus());
                }

                //设置活动分类&等级信息
                ActivityClassificationConfigBean classificationBean = classificationBeanMap.getOrDefault(bean.getClassId(), null);
                if (classificationBean != null) {
                    bean.setClassName(classificationBean.getClassName());
                    bean.setBigClassName(classificationBean.getBigClassName());
                    bean.setLevelName(levleMap.getOrDefault(classificationBean.getLevelId(), ""));
                }

                SimpleUserDto userDto = bean.getHostId() != null && bean.getHostId() > 0 ? userInfoMap.get(bean.getHostId()) : null;
                //设置主持人信息
                if (userDto != null) {
                    UserBean userBean = new UserBean().setBand(userDto.getBand()).setName(userDto.getName()).setPhoto(userDto.getAvatar());
                    bean.setHostInfo(userBean);
                }
                // 设置提报人信息
                SimpleUserDto applyUserDto = userInfoMap.get(bean.getApplyUserInfo().getId());
                if (applyUserDto != null) {
                    UserBean applyUserBean = new UserBean().setBand(applyUserDto.getBand()).setName(applyUserDto.getName()).setPhoto(applyUserDto.getAvatar());
                    bean.setApplyUserInfo(applyUserBean);
                }
                //设置活动模板
                if (templateMap.containsKey(bean.getId())) {
                    bean.setTemplateName(templateMap.get(bean.getId()).getName());
                }

            }
        }
        return new ResponseQueryActivityListBean().setTotal(infoList.getTotal()).setList(activitySimpleInfoBeans);
    }

    public ResponseQueryUserActivitiesBean buildQueryUserActivitiesResult(PageBean<ActivitySimpleInfoDT0> infoList,
                                                                          Map<Long, ActivityClassificationConfigBean> classificationBeanMap,
                                                                          Map<Long, String> levleMap,
                                                                          Map<Long, SimpleUserDto> userMap,
                                                                          Map<Long, FamilyBean> familyBeanMap,
                                                                          Integer activityStatus) {

        List<UserActivitySimpleInfoBean> userActivitySimpleInfoBeans = ActivityApplyMappingConvert.I.userActivitySimpleInfoDTOs2Beans(infoList.getList());
        for (UserActivitySimpleInfoBean userActivitySimpleInfoBean : userActivitySimpleInfoBeans) {
            //设置活动分类
            ActivityClassificationConfigBean classificationBean = classificationBeanMap.getOrDefault(userActivitySimpleInfoBean.getClassId(), null);
            if (classificationBean != null) {
                userActivitySimpleInfoBean.setClassName(classificationBean.getClassName());
                userActivitySimpleInfoBean.setBigClassName(classificationBean.getBigClassName());
                //设置等级信息
                if (levleMap.containsKey(classificationBean.getLevelId())) {
                    userActivitySimpleInfoBean.setLevelName(levleMap.get(classificationBean.getLevelId()));
                }
            }
            //设置工会信息
            if (familyBeanMap.containsKey(userActivitySimpleInfoBean.getFamilyId())) {
                userActivitySimpleInfoBean.setFamilyName(familyBeanMap.get(userActivitySimpleInfoBean.getFamilyId()).getFamilyName());
            }

            //设置提报人信息
            if (userMap.containsKey(userActivitySimpleInfoBean.getApplicantUid())) {
                userActivitySimpleInfoBean.setApplicantName(userMap.get(userActivitySimpleInfoBean.getApplicantUid()).getName());
                userActivitySimpleInfoBean.setApplicantBand(userMap.get(userActivitySimpleInfoBean.getApplicantUid()).getBand());
            }
            //设置厅主信息
            if (userMap.containsKey(userActivitySimpleInfoBean.getNjId())) {
                userActivitySimpleInfoBean.setNjName(userMap.get(userActivitySimpleInfoBean.getNjId()).getName());
                userActivitySimpleInfoBean.setNjBrand(userMap.get(userActivitySimpleInfoBean.getNjId()).getBand());
            }
            //设置活动状态
            setActivityStatus(userActivitySimpleInfoBean, activityStatus);
        }

        return new ResponseQueryUserActivitiesBean().setTotal(infoList.getTotal()).setList(userActivitySimpleInfoBeans);
    }


    public ResponseQueryActivitiesBean buildQueryUserActivitiesSimpleResult(List<ActivitySimpleInfoDT0> infoList,
                                                                          Integer activityStatus) {

        List<UserActivitySimpleInfoBean> userActivitySimpleInfoBeans = ActivityApplyMappingConvert.I.userActivitySimpleInfoDTOs2Beans(infoList);
        for (UserActivitySimpleInfoBean userActivitySimpleInfoBean : userActivitySimpleInfoBeans) {
            //设置活动状态
            setActivityStatus(userActivitySimpleInfoBean, activityStatus);
        }

        return new ResponseQueryActivitiesBean().setList(userActivitySimpleInfoBeans);
    }


    private void setActivityStatus(UserActivitySimpleInfoBean userActivitySimpleInfoBean, Integer activityStatus) {
        // 如果传入了指定的活动状态，则直接使用
        if (activityStatus != null) {
            userActivitySimpleInfoBean.setActivityStatus(activityStatus);
            return;
        }

        boolean isCancelActivity = userActivitySimpleInfoBean.getAuditStatus().equals(ActivityAuditStatusEnum.USER_CANCEL.getStatus())
                || userActivitySimpleInfoBean.getAuditStatus().equals(ActivityAuditStatusEnum.OFFICIAL_CANCEL.getStatus());

        // 否则根据时间判断活动状态
        long currentTime = System.currentTimeMillis();
        if (userActivitySimpleInfoBean.getEndTime() < currentTime) {
            userActivitySimpleInfoBean.setActivityStatus(ActivityStatusEnum.END.getStatus());
        } else if (userActivitySimpleInfoBean.getStartTime() > currentTime || isCancelActivity
                || userActivitySimpleInfoBean.getAuditStatus().equals(ActivityAuditStatusEnum.AUDIT_REJECTED.getStatus())) {
            userActivitySimpleInfoBean.setActivityStatus(ActivityStatusEnum.UN_START.getStatus());
        } else {
            userActivitySimpleInfoBean.setActivityStatus(ActivityStatusEnum.START.getStatus());
        }
    }

    public fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseQueryUserActivitiesBean buildQueryUserActivitiesResultV2(PageBean<ActivitySimpleInfoDT0> infoList,
                                                                                                                                             Map<Long, ActivityClassificationConfigBean> classificationBeanMap,
                                                                                                                                             Map<Long, String> levleMap,
                                                                                                                                             Map<Long, SimpleUserDto> userMap,
                                                                                                                                             Map<Long, FamilyBean> familyBeanMap) {

        List<UserActivitySimpleInfoBean> userActivitySimpleInfoBeans = ActivityApplyMappingConvert.I.userActivitySimpleInfoDTOs2Beans(infoList.getList());
        for (UserActivitySimpleInfoBean userActivitySimpleInfoBean : userActivitySimpleInfoBeans) {
            //设置活动分类
            ActivityClassificationConfigBean classificationBean = classificationBeanMap.getOrDefault(userActivitySimpleInfoBean.getClassId(), null);
            if (classificationBean != null) {
                userActivitySimpleInfoBean.setClassName(classificationBean.getClassName());
                userActivitySimpleInfoBean.setBigClassName(classificationBean.getBigClassName());
                //设置等级信息
                if (levleMap.containsKey(classificationBean.getLevelId())) {
                    userActivitySimpleInfoBean.setLevelName(levleMap.get(classificationBean.getLevelId()));
                }
            }
            //设置工会信息
            if (familyBeanMap.containsKey(userActivitySimpleInfoBean.getFamilyId())) {
                userActivitySimpleInfoBean.setFamilyName(familyBeanMap.get(userActivitySimpleInfoBean.getFamilyId()).getFamilyName());
            }

            //设置提报人信息
            if (userMap.containsKey(userActivitySimpleInfoBean.getApplicantUid())) {
                userActivitySimpleInfoBean.setApplicantName(userMap.get(userActivitySimpleInfoBean.getApplicantUid()).getName());
                userActivitySimpleInfoBean.setApplicantBand(userMap.get(userActivitySimpleInfoBean.getApplicantUid()).getBand());
            }
            //设置厅主信息
            if (userMap.containsKey(userActivitySimpleInfoBean.getNjId())) {
                userActivitySimpleInfoBean.setNjName(userMap.get(userActivitySimpleInfoBean.getNjId()).getName());
                userActivitySimpleInfoBean.setNjBrand(userMap.get(userActivitySimpleInfoBean.getNjId()).getBand());
            }
        }

        return new fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseQueryUserActivitiesBean().setTotal(infoList.getTotal()).setList(userActivitySimpleInfoBeans);
    }
}
