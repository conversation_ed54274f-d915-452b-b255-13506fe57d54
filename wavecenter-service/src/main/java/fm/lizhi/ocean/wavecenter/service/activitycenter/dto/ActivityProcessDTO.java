package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 活动环节信息
 */
@Data
@Accessors(chain = true)
public class ActivityProcessDTO {

    private Integer index;

    /**
     * 活动环节名
     */
    private String name;

    /**
     * 活动环节说明
     */
    private String explanation;

    /**
     * 时长
     */
    private String duration;

    /**
     * ID
     */
    private Long id;
}
