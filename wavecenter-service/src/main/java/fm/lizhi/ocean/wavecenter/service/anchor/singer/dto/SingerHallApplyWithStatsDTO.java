package fm.lizhi.ocean.wavecenter.service.anchor.singer.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 点唱厅申请记录带统计信息DTO
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SingerHallApplyWithStatsDTO {

    /**
     * ID
     */
    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 家族ID
     */
    private Long familyId;

    /**
     * 厅主ID
     */
    private Long njId;

    /**
     * 审核状态，0-审核中，1-审核通过，2-审核未通过
     */
    private Integer auditStatus;

    /**
     * 提交时间
     */
    private Date applyTime;

    /**
     * 部署环境
     */
    private String deployEnv;

    /**
     * 来源
     */
    private Integer source;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 是否删除
     */
    private Boolean deleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 认证歌手数（singer_type=1的数量）
     */
    private Long singerAuthCnt;

    /**
     * 优质歌手数（singer_type=2和3的数量）
     */
    private Long seniorSingerAuthCnt;
}
