package fm.lizhi.ocean.wavecenter.service.user.impl;


import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.user.service.UserRoomService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;

@ServiceProvider
public class UserRoomServiceImpl implements UserRoomService {

    @Autowired
    private FamilyManager familyManager;

    @Override
    public Result<Long> playerCurSignNj(int appId, long userId) {
        LogContext.addReqLog("appId={},userId={}", appId, userId);
        LogContext.addResLog("appId={},userId={}", appId, userId);
        return ResultHandler.handle(appId, () -> {
            Optional<Long> userBestNj = familyManager.getUserBestNj(userId);
            return userBestNj.map(RpcResult::success).orElseGet(() -> RpcResult.success(-1L));
        });
    }
}
