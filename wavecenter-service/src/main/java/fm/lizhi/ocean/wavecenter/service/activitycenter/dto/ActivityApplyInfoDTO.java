package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 活动提报简单信息
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ActivityApplyInfoDTO {

    /**
     * 主键ID
     */
    private Long id;
    /**
     * 业务定义不超过10个字，稍微冗余
     */
    private String name;
    /**
     * 提报厅厅主ID
     */
    private Long njId;

    /**
     * 活动大分类ID - 大类
     */
    private Long bigClassId;

    /**
     * 活动子分类ID - 子分类 - 用于查询活动对应的等级信息
     */
    private Long classId;
    /**
     * 活动大分类名称 - 这里显示的是大类名称
     */
    private String className;
    /**
     * 活动开始时间
     */
    private Date startTime;
    /**
     * 活动结束时间
     */
    private Date endTime;

    /**
     * 活动等级ID
     */
    private Long levelId;

    /**
     * 活动等级名字
     */
    private String levelName;
}
