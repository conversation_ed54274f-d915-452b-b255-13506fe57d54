package fm.lizhi.ocean.wavecenter.service.activitycenter.manager;


import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityInfoDTO;

public interface ActivityChatManager {

    /**
     * 取消活动时IM通知
     *
     * @param activityInfo 活动信息
     * @return 结果
     */
    void userCancelActivityNotice(ActivityInfoDTO activityInfo, Long operateUserId);

    /**
     * 管理员取消活动时IM通知
     *
     * @param activityInfo 活动信息
     * @return 结果
     */
    void adminCancelActivityNotice(ActivityInfoDTO activityInfo);

    /**
     * 同意活动时IM通知
     * @param activityInfo 活动信息
     */
    void agreeActivityNotice(ActivityInfoDTO activityInfo);

    /**
     * 拒绝活动时IM通知
     * @param activityInfo 活动信息
     */
    void rejectedActivityNotice(ActivityInfoDTO activityInfo);

    /**
     * 活动开始前IM通知
     */
    void startActivityNotice();

    /**
     * 活动结束后IM通知
     */
    void endActivityNotice();
}
