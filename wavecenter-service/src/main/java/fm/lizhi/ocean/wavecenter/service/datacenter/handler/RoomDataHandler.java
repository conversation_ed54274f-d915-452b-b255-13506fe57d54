package fm.lizhi.ocean.wavecenter.service.datacenter.handler;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.GetRoomPlayerPerformanceBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.PlayerPerformanceBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomGetKeyIndicatorsParamBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomPlayerPerformanceResBean;
import fm.lizhi.ocean.wavecenter.base.util.CalculateUtil;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.*;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.AssessmentManager;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.RoomDataManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/18 18:13
 */
@Component
public class RoomDataHandler extends AbsDataHandler<RoomGetKeyIndicatorsParamBean, Object>{

    @Autowired
    private AssessmentManager assessmentManager;
    @Autowired
    private RoomDataManager roomDataManager;
    @Autowired
    private UserManager userManager;

    @Override
    protected Map<String, String> getDayKeyIndicators(RoomGetKeyIndicatorsParamBean paramBean, Date day, List<String> queryValueMetrics) {
        Integer appId = paramBean.getAppId();
        Long roomId = paramBean.getRoomId();
        Long familyId = paramBean.getFamilyId();
        return roomDataManager.getRoomDayKeyIndicators(appId, familyId, roomId, day, queryValueMetrics);
    }

    @Override
    protected Map<String, String> getWeekKeyIndicators(RoomGetKeyIndicatorsParamBean paramBean, Date startDay, Date endDay, List<String> queryValueMetrics) {
        Integer appId = paramBean.getAppId();
        Long roomId = paramBean.getRoomId();
        Long familyId = paramBean.getFamilyId();
        return roomDataManager.getRoomWeekKeyIndicators(appId, familyId, roomId, startDay, endDay, queryValueMetrics);
    }

    @Override
    protected Map<String, String> getMonthKeyIndicators(RoomGetKeyIndicatorsParamBean paramBean, Date month, List<String> queryValueMetrics) {
        Integer appId = paramBean.getAppId();
        Long roomId = paramBean.getRoomId();
        Long familyId = paramBean.getFamilyId();
        return roomDataManager.getRoomMonthKeyIndicators(appId, familyId, roomId, month, queryValueMetrics);
    }

    @Override
    protected Map<Integer, Object> getDaysData(IndicatorTrendParam paramBean, List<String> metrics, List<Integer> dayValues) {
        Map<Integer, Object> valueMap;
        Long familyId = paramBean.getFamilyId();
        Long roomId = paramBean.getRoomId();

        GetRoomDayListParam param = new GetRoomDayListParam();
        param.setDayValues(dayValues).setRoomId(roomId);
        if (familyId != null) {
            param.setFamilyId(familyId);
            List<DataRoomFamilyDayDTO> dtoList = roomDataManager.getRoomFamilyDayList(param);
            valueMap = dtoList.stream().collect(Collectors.toMap(DataRoomFamilyDayDTO::getStatDateValue, v -> v, (k1, k2) -> k2));
        } else {
            List<DataRoomDayDTO> dtoList = roomDataManager.getRoomDayList(param);
            valueMap = dtoList.stream().collect(Collectors.toMap(DataRoomDayDTO::getStatDateValue, v -> v, (k1, k2) -> k2));
        }

        return valueMap;
    }

    /**
     * 厅数据-考核周期业绩列表
     * @param paramBean
     * @return
     */
    public RoomPlayerPerformanceResBean getPlayerPerformance(GetRoomPlayerPerformanceBean paramBean) {
        RoomPlayerPerformanceResBean res = new RoomPlayerPerformanceResBean();

        Integer appId = paramBean.getAppId();
        Long roomId = paramBean.getRoomId();
        Long familyId = paramBean.getFamilyId();

        //查询当前考核周期
        AssessTimeDto currentTime = assessmentManager.getCurrentTime(appId, familyId);

        //查询上期考核周期
        AssessTimeDto preTime = assessmentManager.getPreTime(appId, familyId);

        //查询本期魅力值列表
        PageBean<PlayerPerformanceBean> currentPageList = roomDataManager.getPlayerPerformance(GetRoomPlayerPerformanceParamDto.builder()
                .appId(appId)
                .roomId(roomId)
                .familyId(familyId)
                .orderMetrics(paramBean.getOrderMetrics())
                .orderType(paramBean.getOrderType())
                .pageNo(paramBean.getPageNo())
                .pageSize(paramBean.getPageSize())
                .assessTime(currentTime)
                .build());
        List<PlayerPerformanceBean> currentList = currentPageList.getList();
        if (CollectionUtils.isEmpty(currentList)) {
            return res;
        }

        //查询当前页主播上一期魅力值
        List<Long> playerIds = currentList.stream().map(v -> v.getPlayerInfo().getId()).collect(Collectors.toList());
        List<PlayerPerformanceBean> preList = roomDataManager.getPlayerPerformanceList(appId, familyId, roomId, playerIds, preTime);
        Map<Long, PlayerPerformanceBean> preMap = preList.stream()
                .filter(v-> v!=null && v.getPlayerInfo()!=null && v.getPlayerInfo().getId() != null)
                .collect(Collectors.toMap(k -> k.getPlayerInfo().getId(), v -> v, (k1, k2) -> k2));

        //主播信息
        List<SimpleUserDto> userList = userManager.getSimpleUserByIds(playerIds);
        Map<Long, SimpleUserDto> userMap = userList.stream().collect(Collectors.toMap(SimpleUserDto::getId, v -> v, (k1, k2) -> k2));

        //计算环比
        for (PlayerPerformanceBean bean : currentList) {
            Long userId = bean.getPlayerInfo().getId();

            Optional.ofNullable(userMap.get(userId)).ifPresent(user -> {
                bean.getPlayerInfo().setBand(user.getBand());
                bean.getPlayerInfo().setName(user.getName());
            });

            Optional.ofNullable(preMap.get(userId)).ifPresent(preBean -> {
                bean.setPreCharm(preBean.getCurrentCharm());
                bean.setCharmRatio(CalculateUtil.relativeRatio(bean.getPreCharm(), bean.getCurrentCharm()));
            });
            bean.setIncomeRatio(CalculateUtil.relativeRatio(bean.getPreIncome(), bean.getSignIncome()));
        }

        return res.setTotal(currentPageList.getTotal())
                .setPlayers(currentList)
                .setStartDate(currentTime.getStartDate())
                .setEndDate(currentTime.getEndDate());
    }

}
