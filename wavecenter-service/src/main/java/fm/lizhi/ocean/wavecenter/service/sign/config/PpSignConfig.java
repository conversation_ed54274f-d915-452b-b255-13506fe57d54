package fm.lizhi.ocean.wavecenter.service.sign.config;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/10/19 10:21
 */
@Data
public class PpSignConfig implements BizSignConfig{

    /**
     * 是否校验同一实名下只能加入一个家族
     */
    private boolean verifyUnionFamily = true;

    /**
     * 签约功能开关，关闭后，系统将不提供签约功能
     */
    private boolean signFunctionSwitch = true;

    /**
     * 管理员黑名单
     * 在黑名单中的用户不能被邀请为管理员
     */
    private String signAdminBackList = "";

    /**
     * 家族黑名单
     * 在黑名单中的家族ID不能邀请用户
     */
    private String signFamilyBackList = "";

    /**
     * 企业黑名单
     * 在黑名单中的企业不能邀请用户
     */
    private String signEnterpriseBackList = "";

    /**
     * 跳槽检查开关
     */
    private boolean checkJobHoppingSwitch = true;

}
