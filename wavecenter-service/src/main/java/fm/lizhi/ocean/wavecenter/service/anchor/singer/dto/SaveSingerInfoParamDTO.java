package fm.lizhi.ocean.wavecenter.service.anchor.singer.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SaveSingerInfoParamDTO {

    /**
     * 业务ID
     */
    private Integer appId;

    /**
     * 歌手ID
     */
    private Long userId;

    /**
     * 厅主ID
     */
    private Long njId;

    /**
     * 家族ID
     */
    private Long familyId;

    /**
     * 审核表 ID
     */
    private Long singerVerifyId;

    /**
     * 歌手状态 1: 认证中 2: 生效中  3: 已淘汰
     * @see fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum
     */
    private Integer singerStatus;

    /**
     * 歌曲风格
     */
    private String songStyle;

    /**
     * 是否原创歌手
     */
    private Boolean originalSinger;

    /**
     * 1: 新锐歌手，2：优质歌手，3：明星歌手
     * @see fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum
     */
    private Integer singerType;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 联系方式
     */
    private String contactNumber;
}
