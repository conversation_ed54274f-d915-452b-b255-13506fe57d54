package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ResultDTO {

    /**
     * 错误码
     */
    private int code;

    /**
     * 错误提示
     */
    private String msg;

    /**
     * 业务主键ID
     */
    private long bizRecordId;

    public static ResultDTO fail(int code, String msg) {
        return new ResultDTO().setCode(code).setMsg(msg);
    }

    public static ResultDTO success() {
        return new ResultDTO().setCode(0).setMsg("");
    }

    public static ResultDTO success(long bizRecordId) {
        return new ResultDTO().setCode(0).setMsg("").setBizRecordId(bizRecordId);
    }

}
