package fm.lizhi.ocean.wavecenter.service.sign.config;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.config.AbsBizConfig;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @date 2024/10/19 10:19
 */
@Data
@ConfigurationProperties(prefix = "wavecenter-sign")
public class SignConfig extends AbsBizConfig<BizSignConfig> {

    private PpSignConfig pp;

    private HySignConfig hy;

    private XmSignConfig xm;

    public SignConfig() {
        PpSignConfig ppConfig = new PpSignConfig();
        this.pp = ppConfig;

        HySignConfig hyBizConfig = new HySignConfig();
        this.hy = hyBizConfig;

        XmSignConfig xmBizConfig = new XmSignConfig();
        this.xm = xmBizConfig;

        bizConfigMap.put(BusinessEvnEnum.XIMI.appId(), xmBizConfig);
        bizConfigMap.put(BusinessEvnEnum.HEI_YE.appId(), hyBizConfig);
        bizConfigMap.put(BusinessEvnEnum.PP.appId(), ppConfig);
    }
}
