package fm.lizhi.ocean.wavecenter.service.user.config;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.StrPool;
import fm.lizhi.ocean.wavecenter.api.message.constant.MessageTargetLinkEnum;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.config.AbsBizConfig;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/4/10 15:54
 */
@Data
@ConfigurationProperties(prefix = "wavecenter-user")
public class UserConfig extends AbsBizConfig<BizUserConfig> {

    /**
     * 访问token过期时间，默认2小时
     */
    private int accessTokenExpiresIn = 2 * 60 * 60;

    /**
     * 刷新token过期时间，默认7天
     */
    private int refreshTokenExpiresIn = 7 * 24 * 60 * 60;

    /**
     * 二维码有效时间，秒
     * 默认1分钟
     */
    private int qcCodeExpiresIn = 60;

    /**
     * 用户每日可下载文件数量
     */
    private int downFileDayLimit = 50;

    /**
     * 快捷入口配置
     * <p>
     * key=roleCode, value=linkType list
     */
    private Map<String, String> quickEntry = MapUtil.<String, String>builder()
            .put(RoleEnum.FAMILY.getRoleCode(), String.join(StrPool.COMMA, MessageTargetLinkEnum.FAMILY_DATA.name(), MessageTargetLinkEnum.FAMILY_INCOME.name(), MessageTargetLinkEnum.ROOM_DATA.name()))
            .put(RoleEnum.ROOM.getRoleCode(), String.join(StrPool.COMMA, MessageTargetLinkEnum.ROOM_DATA.name(), MessageTargetLinkEnum.ROOM_INCOME.name(), MessageTargetLinkEnum.ILLEGAL_STATISTIC.name()))
            .build();

    private PpBizUserConfig pp;

    private HyBizUserConfig hy;

    private XmBizUserConfig xm;

    public UserConfig() {
        PpBizUserConfig ppConfig = new PpBizUserConfig();
        this.pp = ppConfig;

        HyBizUserConfig hyBizUserConfig = new HyBizUserConfig();
        this.hy = hyBizUserConfig;

        XmBizUserConfig xmBizUserConfig = new XmBizUserConfig();
        this.xm = xmBizUserConfig;

        bizConfigMap.put(BusinessEvnEnum.XIMI.appId(), xmBizUserConfig);
        bizConfigMap.put(BusinessEvnEnum.HEI_YE.appId(), hyBizUserConfig);
        bizConfigMap.put(BusinessEvnEnum.PP.appId(), ppConfig);
    }

}
