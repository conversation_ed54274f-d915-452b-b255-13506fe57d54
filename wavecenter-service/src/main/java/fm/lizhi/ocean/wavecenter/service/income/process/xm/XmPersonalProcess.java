package fm.lizhi.ocean.wavecenter.service.income.process.xm;



import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.service.income.process.PersonalProcess;
import org.springframework.stereotype.Component;


@Component
public class XmPersonalProcess implements PersonalProcess {


    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.XIMI;
    }

    @Override
    public boolean enablePlayerIncome() {
        return false;
    }
}
