package fm.lizhi.ocean.wavecenter.service.user.dto;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SearchUserVerifyResultParamDTO {

     /**
      * 应用ID
      */
     private Integer appId;
     /**
      * 用户ID
      */
     private Long userId;
     /**
      * 证件号
      */
     private String idCardNumber;
     /**
      * 认证状态
      */
     private Integer verifyStatus;
     /**
      * 认证类型
      */
     private Integer verifyType;
     /**
      * 开始时间
      */
     private Long beginDate;
     /**
      * 结束时间
      */
     private Long endDate;

     /**
      * 查询类型
      */
     private Integer searchType;

}
