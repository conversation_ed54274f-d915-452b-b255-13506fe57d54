package fm.lizhi.ocean.wavecenter.service.activitycenter.processor.hy;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.service.activitycenter.processor.IRecommendCardGiveProcess;
import org.springframework.stereotype.Component;

@Component
public class HyRecommendCardGiveProcess implements IRecommendCardGiveProcess {
    @Override
    public String getGiveReason(String activityName) {
        // 黑叶发放理由最多10个字符
        if (activityName.length() <= 8) {
            return activityName + "发放";
        } else {
            return activityName.substring(0, 7) + "…发放";
        }
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.HEI_YE;
    }
}
