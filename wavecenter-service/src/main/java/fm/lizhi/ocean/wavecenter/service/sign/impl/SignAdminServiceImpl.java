package fm.lizhi.ocean.wavecenter.service.sign.impl;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.bean.*;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import fm.lizhi.ocean.wavecenter.api.sign.request.*;
import fm.lizhi.ocean.wavecenter.api.sign.response.*;
import fm.lizhi.ocean.wavecenter.api.sign.service.SignAdminService;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.base.processor.ProcessorFactory;
import fm.lizhi.ocean.wavecenter.service.sign.dto.OperateSignDTO;
import fm.lizhi.ocean.wavecenter.service.sign.dto.QueryNonContractDTO;
import fm.lizhi.ocean.wavecenter.service.sign.dto.RequestFamilyAndNjContractDTO;
import fm.lizhi.ocean.wavecenter.service.sign.manager.ContractManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.NonContractManager;
import fm.lizhi.ocean.wavecenter.service.sign.process.AdminSignProcessor;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@ServiceProvider
public class SignAdminServiceImpl implements SignAdminService {

    @Autowired
    private ProcessorFactory processorFactory;
    @Autowired
    private NonContractManager nonContractManager;
    @Autowired
    private UserManager userManager;
    @Autowired
    private ContractManager contractManager;

    @Override
    public Result<ResponseAdminOperateSign> operateSign(RequestAdminOperateSign request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        return ResultHandler.handle(request.getAppId(), ()->{
            AdminSignProcessor processor = getSignProcessor();
            ResponseAdminOperateSign checkResp = processor.operateSignCheck(request);
            if (checkResp.getCode() != 0) {
                return RpcResult.success(checkResp);
            }

            OperateSignDTO result = nonContractManager.operateSign(request.getPlayerSignId()
                    , request.getCurUserId()
                    , ContractTypeEnum.SIGN
                    , RoleEnum.ROOM
                    , request.getOperateType()
            );
            LogContext.addResLog("resultCode={}", result.getCode());

            return RpcResult.success(new ResponseAdminOperateSign()
                    .setCode(result.getCode())
                    .setMsg(result.getMsg())
            );
        });
    }

    @Override
    public Result<ResponseAdminInviteUser> inviteUser(RequestAdminInviteUser request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        return ResultHandler.handle(request.getAppId(), () -> {
            AdminSignProcessor signProcessor = getSignProcessor();
            ResponseAdminInviteUser checkResp = signProcessor.inviteUserCheck(request);
            if (checkResp.getCode() != 0) {
                return RpcResult.success(checkResp);
            }

            ResponseAdminInviteUser resp = nonContractManager.adminInviteUser(request);
            LogContext.addResLog("respCode={}", resp.getCode());

            return RpcResult.success(resp);
        });
    }

    @Override
    public Result<ResponseAdminOperateCancel> operateCancel(RequestAdminOperateCancel request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        return ResultHandler.handle(request.getAppId(), ()->{

            OperateSignDTO result = nonContractManager.operateSign(request.getPlayerSignId()
                    , request.getCurUserId()
                    , ContractTypeEnum.CANCEL
                    , RoleEnum.ROOM
                    , request.getOperateType());
            LogContext.addResLog("resultCode={}", result.getCode());

            return RpcResult.success(new ResponseAdminOperateCancel().setCode(result.getCode()).setMsg(result.getMsg()));
        });
    }

    @Override
    public Result<ResponseWithdrawCancel> withdrawCancel(RequestWithdrawCancel request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        return ResultHandler.handle(request.getAppId(), () -> {

            ResponseWithdrawCancel res = nonContractManager.withdrawCancel(request.getPlayerSignId()
                    , request.getCurUserId(), RoleEnum.ROOM);
            LogContext.addResLog("resCode={}", res.getCode());

            return RpcResult.success(res);
        });
    }

    @Override
    public Result<ResponseAdminApplyCancelPlayer> applyCancelPlayer(RequestAdminApplyCancelPlayer request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        return ResultHandler.handle(request.getAppId(), () -> {
            ResponseAdminApplyCancelPlayer res = nonContractManager.adminApplyCancelPlayer(request);
            LogContext.addResLog("resCode={}", res.getCode());
            return RpcResult.success(res);
        });
    }

    @Override
    public Result<List<TodoSignPlayerBean>> getTodoList(RequestAdminTodoList request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        return ResultHandler.handle(request.getAppId(), () -> {
            //查询列表
            List<TodoSignPlayerBean> list = nonContractManager.adminTodoList(request);

            //解约的原合同签约时间
            List<Long> oldIds = list.stream()
                    .filter(v -> ContractTypeEnum.CANCEL.getCode().equals(v.getSignType()))
                    .map(TodoSignPlayerBean::getOldContractId)
                    .collect(Collectors.toList());
            Map<Long, NjAndPlayerContractBean> oldMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(oldIds)) {
                PageBean<NjAndPlayerContractBean> oldPageBean = nonContractManager.queryList(QueryNonContractDTO.builder().contractIdLists(oldIds).pageSize(100).build());
                oldMap = oldPageBean.getList().stream().collect(Collectors.toMap(NjAndPlayerContractBean::getContractId, v -> v));
            }

            List<Long> userIds = new ArrayList<>();
            for (TodoSignPlayerBean todoSignPlayerBean : list) {
                userIds.add(todoSignPlayerBean.getPlayerInfo().getId());
            }

            Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(userIds);
            for (TodoSignPlayerBean todoSignPlayerBean : list) {
                UserBean playerInfo = todoSignPlayerBean.getPlayerInfo();
                SimpleUserDto player = userMap.get(playerInfo.getId());
                if (player != null) {
                    playerInfo.setName(player.getName());
                    playerInfo.setBand(player.getBand());
                    playerInfo.setPhoto(player.getAvatar());
                }
                todoSignPlayerBean.setSelfCreate(RoleEnum.ROOM.getRoleCode().equals(todoSignPlayerBean.getCreateUser()));
                //详细状态转换
                detailStatus(todoSignPlayerBean);

                if (ContractTypeEnum.CANCEL.getCode().equals(todoSignPlayerBean.getSignType())
                        && todoSignPlayerBean.getOldContractId() != null) {
                    NjAndPlayerContractBean old = oldMap.get(todoSignPlayerBean.getOldContractId());
                    if (old != null) {
                        todoSignPlayerBean.setOldStartTime(old.getStartTime());
                    }
                }
            }

            return RpcResult.success(list);
        });
    }

    @Override
    public Result<PageBean<AdminSignPlayerRecordBean>> querySignPlayerList(RequestQuerySignPlayerList request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        return ResultHandler.handle(request.getAppId(), () -> {
            //查询列表
            PageBean<AdminSignPlayerRecordBean> pageList = nonContractManager.querySignPlayerList(request);
            if (CollectionUtils.isEmpty(pageList.getList())) {
                LogContext.addResLog("pageList is empty");
                return RpcResult.success(pageList);
            }
            List<AdminSignPlayerRecordBean> list = pageList.getList();

            //解约的原合同签约时间
            Map<Long, NjAndPlayerContractBean> oldMap = new HashMap<>();
            List<Long> oldIds = list.stream()
                    .filter(v -> ContractTypeEnum.CANCEL.getCode().equals(v.getType()))
                    .map(AdminSignPlayerRecordBean::getOldContractId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(oldIds)) {
                PageBean<NjAndPlayerContractBean> oldPageBean = nonContractManager.queryList(QueryNonContractDTO.builder().contractIdLists(oldIds).pageSize(100).build());
                oldMap = oldPageBean.getList().stream().collect(Collectors.toMap(NjAndPlayerContractBean::getContractId, v -> v));
            }

            List<Long> userIds = new ArrayList<>();
            for (AdminSignPlayerRecordBean ab : list) {
                userIds.add(ab.getPlayerInfo().getId());
            }

            Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(userIds);
            for (AdminSignPlayerRecordBean ab : list) {
                UserBean playerInfo = ab.getPlayerInfo();
                SimpleUserDto player = userMap.get(playerInfo.getId());
                if (player != null) {
                    playerInfo.setName(player.getName());
                    playerInfo.setBand(player.getBand());
                    playerInfo.setPhoto(player.getAvatar());
                }
                //详细状态转换
                ab.setSelfCreate(RoleEnum.ROOM.getRoleCode().equals(ab.getCreateUser()));
                detailStatus(ab);

                if (request.getType() == ContractTypeEnum.CANCEL
                        && ab.getOldContractId() != null) {
                    NjAndPlayerContractBean old = oldMap.get(ab.getOldContractId());
                    if (old != null) {
                        ab.setOldStartTime(old.getStartTime());
                        ab.setStopTime(old.getStopTime());
                    }
                }

            }

            return RpcResult.success(pageList);
        });
    }

    @Override
    public Result<Integer> countSignPlayerNum(int appId, long njId) {
        LogContext.addReqLog("appId={},njId={}", appId, njId);
        LogContext.addResLog("appId={},njId={}", appId, njId);
        return ResultHandler.handle(appId, () -> {
            Integer num = nonContractManager.countPlayerSignNum(njId);
            LogContext.addResLog("num={}", num);
            return RpcResult.success(num);
        });
    }

    @Override
    public Result<ResponseAdminDoCancelFamily> doCancelFamily(RequestAdminDoCancelFamily request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        return ResultHandler.handle(request.getAppId(), ()->{
            ResponseAdminDoCancelFamily res = new ResponseAdminDoCancelFamily();

            PageBean<FamilyAndNjContractBean> pageBean = contractManager.queryContract(RequestFamilyAndNjContractDTO.builder()
                    .contractId(request.getContractId())
                    .pageSize(1)
                    .build());
            if (CollectionUtils.isEmpty(pageBean.getList())) {
                LogContext.addResLog("pageList is empty");
                return RpcResult.success(res.setCode(-1).setMsg("合同不存在"));
            }

            FamilyAndNjContractBean contract = pageBean.getList().get(0);
            if (!contract.getNjUserId().equals(request.getCurUserId())) {
                LogContext.addResLog("curUserId not eq njUserId");
                return RpcResult.success(res.setCode(-1).setMsg("您不是合同所有人"));
            }

            Optional<String> urlOp = contractManager.genContractSignUrl(request.getCurUserId(), request.getSignId());
            if (!urlOp.isPresent()) {
                LogContext.addResLog("genContractSignUrl fail");
                return RpcResult.success(res.setCode(-1).setMsg("合同签署链接生成失败"));
            }

            AdminSignProcessor signProcessor = getSignProcessor();
            signProcessor.doCancelFamilySuccessProcessor(request);

            return RpcResult.success(res.setContractUrl(urlOp.get()));

        });
    }

    @Override
    public Result<ResponseAdminApplyCancelFamily> applyCancelFamily(RequestAdminApplyCancelFamily request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        return ResultHandler.handle(request.getAppId(), ()->{
            ResponseAdminApplyCancelFamily res = new ResponseAdminApplyCancelFamily();

            //合同校验
            PageBean<FamilyAndNjContractBean> pageList = contractManager.queryContract(RequestFamilyAndNjContractDTO.builder()
                    .pageSize(1)
                    .contractId(request.getContractId())
                    .build());
            if (CollectionUtils.isEmpty(pageList.getList())) {
                LogContext.addResLog("contract is not exist");
                return RpcResult.success(res.setCode(-1).setMsg("合同不存在"));
            }
            FamilyAndNjContractBean contract = pageList.getList().get(0);
            if ((!contract.getNjUserId().equals(request.getCurUserId()))) {
                LogContext.addResLog("contract not match");
                return RpcResult.success(res.setCode(-1).setMsg("合同信息不匹配"));
            }

            //家族信息
            RequestAdminApplyCancelFamily fullRequest = request.toBuilder()
                    .familyId(contract.getFamilyId())
                    .build();

            //前置检查
            AdminSignProcessor signProcessor = getSignProcessor();
            ResponseAdminApplyCancelFamily checkRes = signProcessor.applyCancelFamilyCheck(fullRequest);
            if (checkRes.getCode() != ResponseSignResult.SUCCESS_CODE) {
                LogContext.addResLog("checkResCode={}", checkRes.getCode());
                return RpcResult.success(checkRes);
            }

            //发起申请
            ResponseAdminApplyCancelFamily doRes = signProcessor.doApplyCancelFamily(fullRequest);
            LogContext.addResLog("resCode={}", doRes.getCode());

            if (doRes.getCode() != ResponseSignResult.SUCCESS_CODE) {
                return RpcResult.success(doRes);
            }

            if (StringUtils.isNotBlank(doRes.getSignId())) {
                PageBean<FamilyAndNjContractBean> pageBean = contractManager.queryContract(RequestFamilyAndNjContractDTO.builder()
                        .signId(doRes.getSignId()).pageSize(1)
                        .build());
                if (CollectionUtils.isNotEmpty(pageBean.getList())) {
                    FamilyAndNjContractBean sign = pageBean.getList().get(0);
                    doRes.setContractId(sign.getContractId());
                    signProcessor.applyCancelFamilySuccessProcessor(request, sign.getContractId());
                }
            }

            return RpcResult.success(doRes);
        });
    }

    private AdminSignProcessor getSignProcessor(){
        return processorFactory.getProcessor(AdminSignProcessor.class);
    }

    /**
     * 区分详细状态
     * @param todoSignBean
     */
    private void detailStatus(IAdminSignRecord todoSignBean){
        //待签署 -> 管理员发起，待对方签署
        if ((SignRelationEnum.WAIT_SIGN.getCode().equals(todoSignBean.findStatus()) || SignRelationEnum.SIGNING.getCode().equals(todoSignBean.findStatus()))
                && RoleEnum.ROOM.getRoleCode().equals(todoSignBean.findCreateUser())) {
            todoSignBean.changeStatus(SignRelationEnum.WAIT_PARTNER_SIGN.getCode());
        }

        //逾期 -> 管理员发起，对方逾期
        if (SignRelationEnum.OVERDUE.getCode().equals(todoSignBean.findStatus())
                && RoleEnum.ROOM.getRoleCode().equals(todoSignBean.findCreateUser())) {
            todoSignBean.changeStatus(SignRelationEnum.PARTNER_OVERDUE.getCode());
        }

        //拒绝 -> 如果是陪玩发起，则是对方拒绝的
        if (SignRelationEnum.REJECT.getCode().equals(todoSignBean.findStatus())
                && RoleEnum.PLAYER.getRoleCode().equals(todoSignBean.findCreateUser())) {
            todoSignBean.changeStatus(SignRelationEnum.PARTNER_REJECT.getCode());
        }
    }
}
