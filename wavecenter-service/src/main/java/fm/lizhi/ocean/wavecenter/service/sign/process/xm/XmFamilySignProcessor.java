package fm.lizhi.ocean.wavecenter.service.sign.process.xm;

import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.ocean.wavecenter.api.sign.bean.FamilyAndNjContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestFamilyApplyCancelAdmin;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestFamilyDoSign;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestFamilyInviteAdmin;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseFamilyApplyCancelAdmin;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseFamilyDoSign;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseFamilyInviteAdmin;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.service.sign.config.SignConfig;
import fm.lizhi.ocean.wavecenter.service.sign.process.FamilySignProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/10/14 15:33
 */
@Component
public class XmFamilySignProcessor implements FamilySignProcessor {

    @Autowired
    private SignConfig signConfig;

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.XIMI;
    }

    @Override
    public ResponseFamilyDoSign doSignCheck(RequestFamilyDoSign request) {
        return new ResponseFamilyDoSign();
    }

    @Override
    public ResponseFamilyInviteAdmin inviteAdminCheck(RequestFamilyInviteAdmin request) {
        //西米不支持
        return new ResponseFamilyInviteAdmin().setCode(-1);
    }

    @Override
    public Optional<FamilyAndNjContractBean> existWaitSignContract(RequestFamilyInviteAdmin request) {
        return Optional.empty();
    }

    @Override
    public ResponseFamilyApplyCancelAdmin applyCancelAdmin(RequestFamilyApplyCancelAdmin request) {

        if (!signConfig.getXm().isSignFunctionSwitch()) {
            LogContext.addResLog("sign function is close");
            return new ResponseFamilyApplyCancelAdmin()
                    .setCode(-1)
                    .setMsg("解约功能暂时关闭，恢复时间等待官方通知");
        }

        return new ResponseFamilyApplyCancelAdmin();
    }
}
