package fm.lizhi.ocean.wavecenter.service.anchor.singer.handler;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerChatSceneEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.convert.SingerVerifyRecordConvert;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.*;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerChatManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerInfoManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerVerifyApplyManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 歌手认证选中状态处理器
 * 只有高级歌手认证才有选中状态
 */
@Slf4j
@Component
public class SingerAuditSelectedHandler extends AbstractSingerAuditHandler {

    @Autowired
    private SingerVerifyApplyManager singerVerifyApplyManager;
    @Autowired
    private SingerChatManager singerChatManager;
    @Autowired
    private SingerInfoManager singerInfoManager;

    @Override
    public SingerExecuteAuditDTO auditHandle(SingerAuditParamDTO param, SingerVerifyRecordDTO verifyRecord) {
        //选中操作时，已经是认证歌手了或者歌手在审核中，不能再提交申请
        SingerInfoDTO singerInfo = singerInfoManager.getSingerInfo(verifyRecord.getAppId(), verifyRecord.getUserId(), verifyRecord.getSingerType());
        if (singerInfo != null && (singerInfo.getSingerStatus() == SingerStatusEnum.AUTHENTICATING.getStatus() || singerInfo.getSingerStatus() == SingerStatusEnum.EFFECTIVE.getStatus())) {
            //已经是歌手了，不能再提交申请
            return SingerExecuteAuditDTO.failure("用户已经是歌手或者歌手认证中了，不能审批选中");
        }
        // 选中时，歌手库状态设置为认证中
        UpdateSingerVerifyStatusParamDTO paramDTO = SingerVerifyRecordConvert.I.buildUpdateParam(verifyRecord, param, SingerAuditStatusEnum.SELECTED.getStatus(),
                SingerStatusEnum.AUTHENTICATING.getStatus(), true, verifyRecord.getSingerType());
        boolean updateRes = singerVerifyApplyManager.updateSingerVerifyRecordStatus(paramDTO);
        if (updateRes) {
            singerChatManager.sendAuditResultChat(verifyRecord.getAppId(), verifyRecord.getUserId(), 
                                verifyRecord.getSingerType(), verifyRecord.getSongStyle(), SingerChatSceneEnum.SELECTED);
        }
        return updateRes ? SingerExecuteAuditDTO.success() : SingerExecuteAuditDTO.failure("修改状态失败，请稍候重试!");
    }

    @Override
    public SingerAuditStatusEnum getAuditStatusEnum() {
        return SingerAuditStatusEnum.SELECTED;
    }
}
