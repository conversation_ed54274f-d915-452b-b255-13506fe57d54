package fm.lizhi.ocean.wavecenter.service.common.impl;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageConfigBean;
import fm.lizhi.ocean.wavecenter.api.common.bean.SaveConfigReqBean;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonConfigService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.common.manager.CommonConfigManager;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/20 14:39
 */
@ServiceProvider
public class CommonConfigServiceImpl implements CommonConfigService {

    @Autowired
    private CommonConfigManager commonConfigManager;

    @Override
    public Result<Void> savePageConfig(SaveConfigReqBean reqBean) {
        LogContext.addReqLog("reqBean={}", JsonUtil.dumps(reqBean));
        LogContext.addResLog("reqBean={}", JsonUtil.dumps(reqBean));
        return ResultHandler.handle(reqBean.getAppId(), () -> {
            commonConfigManager.savePageConfig(reqBean);
            return RpcResult.success();
        });
    }

    @Override
    public Result<List<PageConfigBean>> getPageConfig(int appId, long userId, String pageCode) {
        LogContext.addReqLog("appId={}, userId={}, pageCode={}", appId, userId, pageCode);
        LogContext.addResLog("appId={}, userId={}, pageCode={}", appId, userId, pageCode);
        return ResultHandler.handle(appId, ()->{
            List<PageConfigBean> list = commonConfigManager.getPageCode(appId, userId, pageCode);
            return RpcResult.success(list);
        });
    }

}
