package fm.lizhi.ocean.wavecenter.service.sign.process.hy;

import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.bean.FamilyAndNjContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.*;
import fm.lizhi.ocean.wavecenter.api.sign.request.*;
import fm.lizhi.ocean.wavecenter.api.sign.response.*;
import fm.lizhi.ocean.wavecenter.api.sign.service.SignUserService;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.service.sign.dto.RequestFamilyAndNjContractDTO;
import fm.lizhi.ocean.wavecenter.service.sign.dto.SignPersonalInfoDTO;
import fm.lizhi.ocean.wavecenter.service.sign.dto.SignStatusSyncDTO;
import fm.lizhi.ocean.wavecenter.service.sign.manager.ContractManager;
import fm.lizhi.ocean.wavecenter.service.sign.process.UserSignProcessor;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserMediaDto;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserVerifyDataDTO;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/10/10 17:38
 */
@Component
public class HyUserSignProcessor extends HySignAbstractProcessor implements UserSignProcessor {

    @Autowired
    private ContractManager contractManager;
    @Autowired
    private UserManager userManager;

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.HEI_YE;
    }

    @Override
    public ResponseUserSignAdminInvite signAdminInviteCheck(RequestUserSignAdminInvite request) {
        ResponseUserSignAdminInvite res = new ResponseUserSignAdminInvite().setCode(0);

        boolean inChangeCompany = nonContractManager.isInChangeCompany(request.getCurUserId());
        if (inChangeCompany) {
            return res.setCode(-1).setMsg("系统升级，暂不支持签约~");
        }

        //检查签约人数限制
        Pair<Integer, String> limitRes = nonContractManager.checkCanSignForConfirm(request.getPlayerSignId()
                , request.getCurUserId()
                , RoleEnum.PLAYER
                , RoleEnum.PLAYER
        );
        if (limitRes.getKey() != 0) {
            return res.setCode(limitRes.getKey()).setMsg(limitRes.getValue());
        }

        //冷冻期检查
        Pair<Integer, String> signUnionResult = super.checkPGCUnionSign(request.getPlayerSignId());
        if (signUnionResult.getKey() != 0) {
            return res.setCode(signUnionResult.getKey()).setMsg(signUnionResult.getValue());
        }

        return res;
    }

    @Override
    public ResponseUserApplyPlayer userApplyPlayerCheck(RequestUserApplyPlayer request) {
        ResponseUserApplyPlayer res = new ResponseUserApplyPlayer().setCode(0);

        //检查功能开关
        boolean inChangeCompany = nonContractManager.isInChangeCompany(request.getCurUserId());
        if (inChangeCompany) {
            return res.setCode(SignUserService.APPLY_PLAYER_SYSTEM_FUNCTION_CLOSE).setMsg("系统升级，暂不支持签约~");
        }

        //检查对方是否为管理员
        boolean userSignAsRoom = contractManager.isUserSignAsRoom(request.getTargetUserId());
        if (!userSignAsRoom) {
            return res.setCode(SignUserService.APPLY_PLAYER_TARGET_NOT_ROOM);
        }

        //判断厅是否有签约限制
        Pair<Integer, String> limitRes = nonContractManager.checkInviteSignLimit(request.getCurUserId(), request.getTargetUserId(), RoleEnum.PLAYER, RoleEnum.PLAYER);
        if (limitRes.getKey() != 0) {
            LogContext.addResLog("limitResKey={}", limitRes.getKey());
            return res.setCode(SignUserService.APPLY_PLAYER_SIGN_LIMIT_CHECK_FAIL)
                    .setMsg(limitRes.getValue());
        }

        //检查当前用户平台信息是否完整
        if (!genderPass(request.getCurUserId())) {
            return res.setCode(SignUserService.APPLY_PLAYER_REQ_USER_GENDER_NOT_SET);
        }

        //媒体信息检查
        if (!mediaPass(request.getCurUserId())) {
            return res.setCode(SignUserService.APPLY_PLAYER_REQ_USER_MEDIA_INFO_NOT_EXIST);
        }

        //主播中心认证
        if (!userManager.finishPlayerCenterAuth(request.getCurUserId())) {
            return res.setCode(SignUserService.APPLY_PLAYER_PLAYER_CENTER);
        }

        return res;
    }

    @Override
    public ResponseUserInfoStatus signInfoCheck(RequestUserInfoStatus request) {
        ResponseUserInfoStatus res = new ResponseUserInfoStatus();
        boolean infoStatus = mediaPass(request.getUserId()) && genderPass(request.getUserId());
        res.setInfoStatus(infoStatus ? IdentifyStatusEnum.FINISHED.getCode() : IdentifyStatusEnum.UNFINISHED.getCode());
        boolean playerCenterAuth = userManager.finishPlayerCenterAuth(request.getUserId());
        res.setPlayerCenterStatus(playerCenterAuth ? IdentifyStatusEnum.FINISHED.getCode() : IdentifyStatusEnum.UNFINISHED.getCode());
        return res;
    }

    @Override
    public ResponseUserApplyAdmin checkApplyAdmin(RequestUserApplyAdmin request) {
        ResponseUserApplyAdmin res = new ResponseUserApplyAdmin().setCode(0);
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();

        if (request.getFamilyId() == null) {
            LogContext.addResLog("familyId is null");
            return res.setCode(-1).setMsg("参数错误");
        }

        Optional<FamilyBean> familyOp = familyManager.getFamily(appId, request.getFamilyId());
        if (!familyOp.isPresent()) {
            LogContext.addResLog("family not present");
            return res.setCode(-1).setMsg("家族不存在");
        }

        FamilyBean familyBean = familyOp.get();
        if (!"C_FAMILY".equals(familyBean.getFamilyType())) {
            LogContext.addResLog("family is not pgc");
            return res.setCode(-1).setMsg("仅支持PGC");
        }

        if (request.getCurUserId().equals(familyBean.getUserId())) {
            LogContext.addResLog("req user is family user");
            return res.setCode(-1).setMsg("家族长不能申请成为管理员");
        }

        //查询判断当前用户是否存在冻结期
        Pair<Integer, String> limitCheckRes = nonContractManager.checkInviteSignLimit(familyBean.getUserId(), request.getCurUserId(), RoleEnum.ROOM, RoleEnum.ROOM);
        if (limitCheckRes.getKey() != 0) {
            LogContext.addResLog("limitCheckCode={}", limitCheckRes.getKey());
            return res.setCode(limitCheckRes.getKey()).setMsg(limitCheckRes.getValue());
        }

        UserInFamilyBean userInFamily = familyManager.getUserInFamily(request.getCurUserId());
        if (userInFamily.isRoom() || userInFamily.isPlayer()) {
            return res.setCode(-1).setMsg("该用户已有签约身份，无法操作哦~");
        }

        Pair<Integer, String> unionFamily = super.checkVerifyNjUnionFamily(request.getCurUserId(), familyBean.getUserId());
        if (unionFamily.getKey() != 0) {
            LogContext.addResLog("unionFamilyCode={}", unionFamily.getKey());
            return res.setCode(unionFamily.getKey()).setMsg(unionFamily.getValue());
        }

        Pair<Integer, String> checkUserVerify = checkUserVerify(request.getCurUserId(), request.getFamilyId());
        if (checkUserVerify.getKey() != 0) {
            return res.setCode(checkUserVerify.getKey()).setMsg(checkUserVerify.getValue());
        }

        //检查家族是否已经完成认证
        boolean familyVerifyPass = familyManager.isFamilyVerifyPass(request.getFamilyId());
        if (!familyVerifyPass) {
            return res.setCode(-1).setMsg("对方还没完成家族认证，暂时无法申请成为管理员");
        }

        return res;
    }

    @Override
    public void applyAdminSuccessProcessor(RequestUserApplyAdmin request, Long contractId) {
        SignStatusSyncDTO dto = new SignStatusSyncDTO();
        dto.setType(ContractTypeEnum.SIGN.getCode());
        dto.setAppId(request.getAppId());
        dto.setContractId(contractId);
        dto.setCreateRole(RoleEnum.ROOM.getRoleCode());
        dto.setConfirmStatus(FlowConfirmStatusEnum.WAIT_CREATE_SIGN.getCode());
        signFlowManager.addSignStatusSync(dto);
    }

    @Override
    public Optional<FamilyAndNjContractBean> existWaitSignContract(RequestUserApplyAdmin request) {
        PageBean<FamilyAndNjContractBean> pageBean = contractManager.queryContract(RequestFamilyAndNjContractDTO.builder()
                .pageSize(1)
                .pageNo(1)
                .familyId(request.getFamilyId())
                .njId(request.getCurUserId())
                .type(ContractTypeEnum.SIGN)
                .type(ContractTypeEnum.SUBJECT_CHANGE)
                .type(ContractTypeEnum.RENEW)
                .relation(SignRelationEnum.SIGN_SUCCESS)
                .relation(SignRelationEnum.WAIT_SIGN)
                .relation(SignRelationEnum.SIGNING)
                .build());
        if (CollectionUtils.isEmpty(pageBean.getList())) {
            return Optional.empty();
        }

        return Optional.ofNullable(pageBean.getList().get(0));
    }

    @Override
    public ResponseUserDoSign doSignAdminCheck(RequestUserDoSign request) {
        ResponseUserDoSign res = new ResponseUserDoSign();
        boolean inChangeCompany = nonContractManager.isInChangeCompany(request.getCurUserId());
        if (inChangeCompany) {
            return res.setCode(-1).setMsg("系统升级，暂不支持签约~");
        }

        //先判断是否已经签约
        PageBean<FamilyAndNjContractBean> pageList = contractManager.queryContract(RequestFamilyAndNjContractDTO.builder()
                .contractId(request.getContractId())
                .build());
        if (CollectionUtils.isEmpty(pageList.getList())) {
            LogContext.addResLog("contract pageList is empty");
            return res.setCode(-1).setMsg("该合同不存在");
        }

        //黑叶的用户签署 signId已经存在
        FamilyAndNjContractBean contract = pageList.getList().get(0);

        //请求用户与合同是否一致
        if (!contract.getNjUserId().equals(request.getCurUserId())) {
            LogContext.addResLog("contract njId is not curUserId");
            return res.setCode(-1).setMsg("您不是合同所有人");
        }

        String signId = contract.getSignId();
        if (StringUtils.isBlank(signId)) {
            LogContext.addResLog("signId is blank");
            return res.setCode(-1).setMsg("合同异常");
        }

        Map<Long, UserSignStatusEnum> signStatus = contractManager.getContractUserSignStatus(signId);
        if (MapUtils.isEmpty(signStatus)) {
            LogContext.addResLog("signStatus is empty");
            return res.setCode(-1).setMsg("合同异常");
        }
        UserSignStatusEnum userSignStatusEnum = signStatus.get(request.getCurUserId());
        if (userSignStatusEnum == UserSignStatusEnum.SIGN_SUCCESS) {
            LogContext.addResLog("userSignStatusEnum is SIGN_SUCCESS");
            return res.setCode(-1).setMsg("你已签约成功，请等待家族签约");
        }

        Pair<Integer, String> checkUserVerify = checkUserVerify(request.getCurUserId(), contract.getFamilyId());
        if (checkUserVerify.getKey() != 0) {
            LogContext.addResLog("checkUserVerifyCode={}", checkUserVerify.getKey());
            return res.setCode(checkUserVerify.getKey()).setMsg(checkUserVerify.getValue());
        }

        Optional<FamilyBean> familyOp = familyManager.getFamily(request.getAppId(), contract.getFamilyId());
        if (!familyOp.isPresent()) {
            LogContext.addResLog("family not exist");
            return res.setCode(-1);
        }

        Pair<Integer, String> limitCheck = nonContractManager.checkInviteSignLimit(familyOp.get().getUserId(), request.getCurUserId(), RoleEnum.ROOM, RoleEnum.ROOM);
        if (limitCheck.getKey() != 0) {
            LogContext.addResLog("limitCheckCode={}", limitCheck.getKey());
            return res.setCode(limitCheck.getKey()).setMsg(limitCheck.getValue());
        }

        UserInFamilyBean userInFamily = familyManager.getUserInFamily(request.getCurUserId());
        if (userInFamily.isPlayer() || userInFamily.isRoom()) {
            return res.setCode(-1).setMsg("该用户已有签约身份，无法操作哦~");
        }

        Pair<Integer, String> unionFamily = super.checkVerifyNjUnionFamily(request.getCurUserId(), familyOp.get().getUserId());
        if (unionFamily.getKey() != 0) {
            LogContext.addResLog("unionFamilyCode={}", unionFamily.getKey());
            return res.setCode(unionFamily.getKey()).setMsg(unionFamily.getValue());
        }

        return res;
    }

    @Override
    public void doSignSuccessProcessor(RequestUserDoSign request) {
//        SignStatusSyncDTO dto = new SignStatusSyncDTO();
//        dto.setType(ContractTypeEnum.SIGN.getCode());
//        dto.setAppId(request.getAppId());
//        dto.setContractId(request.getContractId());
//        dto.setCreateRole(RoleEnum.ROOM.getRoleCode());
//        dto.setConfirmStatus(FlowConfirmStatusEnum.WAIT_TARGET_SIGN.getCode());
//        signFlowManager.addSignStatusSync(dto);
    }

    @Override
    public Optional<String> doSignGenSignId(RequestUserDoSign request) {
        PageBean<FamilyAndNjContractBean> pageList = contractManager.queryContract(RequestFamilyAndNjContractDTO.builder()
                .contractId(request.getContractId())
                .build());
        if (CollectionUtils.isEmpty(pageList.getList())) {
            LogContext.addResLog("contract pageList is empty");
            return Optional.empty();
        }

        //黑叶的用户签署 signId已经存在
        FamilyAndNjContractBean contract = pageList.getList().get(0);
        String signId = contract.getSignId();
        if (StringUtils.isBlank(signId)) {
            LogContext.addResLog("signId is blank");
            return Optional.empty();
        }
        return Optional.of(signId);
    }

    /**
     * 检查用户实名信息
     * @param userId
     * @param familyId
     * @return
     */
    private Pair<Integer, String> checkUserVerify(Long userId, Long familyId){
        if (signConfig.getHy().isCheckUserVerify()) {
            //检查上上签实名和平台实名是否一致
            Optional<SignPersonalInfoDTO> signPersonalInfoOp = contractManager.getSignPersonalInfo(userId);
            if ((!signPersonalInfoOp.isPresent())
                    || SignAuthStatusEnum.AUTO_AUTH_PASS != signPersonalInfoOp.get().getAuthStatus()) {
                return Pair.of(SignUserService.APPLY_ADMIN_NO_SIGN_VERIFY, "用户未完成上上签认证");
            }
            String identityNo = signPersonalInfoOp.get().getIdentityNo();

            //检查该身份证是否签约了其他家族
            List<FamilyAndNjContractBean> joinList = contractManager.queryIdentityNoJoinFamily(identityNo);
            if (CollectionUtils.isNotEmpty(joinList)) {
                for (FamilyAndNjContractBean c : joinList) {
                    if (super.isUserBand(c.getNjUserId())) {
                        continue;
                    }
                    if (!familyId.equals(c.getFamilyId())) {
                        return Pair.of(-1, "实名身份证签约了其他家族,不能再次签约");
                    }
                }
            }

            Optional<UserVerifyDataDTO> verifyDataOp = userManager.getVerifyData(userId);
            if ((!verifyDataOp.isPresent()) || verifyDataOp.get().getVerifyStatus() != 2) {
                return Pair.of(SignUserService.APPLY_ADMIN_NO_PLATFORM_VERIFY, "用户未完成平台实名认证");
            }
            String idCardNumber = verifyDataOp.get().getIdCardNumber();

            if (!Objects.equals(idCardNumber, identityNo)) {
                LogContext.addResLog("idCardNum={}, idCardNo={}", idCardNumber, identityNo);
                return Pair.of(-1, "上上签和平台实名信息不同");
            }
        }

        return Pair.of(0, "");
    }

    /**
     * 媒体信息是否通过
     * @param userId
     * @return
     */
    private boolean mediaPass(Long userId){
        Optional<UserMediaDto> media = userManager.getUserMediaById(userId);
        if (!media.isPresent()) {
            return false;
        }
        //相册
        if (StringUtils.isBlank(media.get().getAlbumListJSON()) || "[]".equals(media.get().getAlbumListJSON())) {
            return false;
        }
        //语音条
        if (StringUtils.isBlank(media.get().getVoiceListJSON()) || "[]".equals(media.get().getVoiceListJSON())) {
            return false;
        }
        return true;
    }
}
