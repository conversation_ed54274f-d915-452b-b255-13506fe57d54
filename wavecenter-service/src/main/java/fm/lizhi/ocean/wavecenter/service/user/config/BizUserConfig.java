package fm.lizhi.ocean.wavecenter.service.user.config;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/10 15:56
 */
public interface BizUserConfig {

    /**
     * 是否打印缓存日志
     * @return
     */
    boolean isPrintCacheStatLogSwitch();

    /**
     * 登录认证校验白名单
     * @return
     */
    List<Long> getPlayerAuthWhitelist();

    /**
     * 登录白名单
     * @return
     */
    List<Long> getLoginWhitelist();

    /**
     * 二维码登录链接
     * @return
     */
    String getQrCodeLoginUrl();

    /**
     * 官方厅用户组ID
     * @return
     */
    Long getOfficialRoomGroupId();

}
