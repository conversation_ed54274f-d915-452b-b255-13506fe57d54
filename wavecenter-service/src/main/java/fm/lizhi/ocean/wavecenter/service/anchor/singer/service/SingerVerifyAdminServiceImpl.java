package fm.lizhi.ocean.wavecenter.service.anchor.singer.service;

import java.util.*;
import java.util.stream.Collectors;

import javax.validation.Valid;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerVerifySongInfoBean;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.google.common.collect.Lists;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerLosingBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerHallApplyStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerOperateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestBatchAddBlackList;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestBatchCancelBlackList;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestGetSingerVerifyRecord;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestModifyVerifyApplyRemark;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestQueryHistoryVerifyRecord;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestVerifyAudit;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseBatchCancelBlackList;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseGetSingerVerifyRecord;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseQueryHistoryVerifyRecord;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseVerifyAudit;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.service.SingerVerifyAdminService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.convert.SingerVerifyRecordConvert;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.handler.SingerAuditStatusHandler;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.handler.SingerAuditStatusHandlerFactory;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerBlackListManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerHallApplyManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerOperateRecordManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerVerifyApplyManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;

@ServiceProvider
public class SingerVerifyAdminServiceImpl implements SingerVerifyAdminService {

    @Autowired
    private SingerVerifyApplyManager singerVerifyApplyManager;

    @Autowired
    private SingerHallApplyManager singerHallApplyManager;

    @Autowired
    private UserManager userManager;

    @Autowired
    private FamilyManager familyManager;

    @Autowired
    private SingerAuditStatusHandlerFactory handlerFactory;

    @Autowired
    private SingerOperateRecordManager singerOperateRecordManager;

    @Autowired
    private SingerBlackListManager singerBlackListManager;

    @Override
    public Result<ResponseVerifyAudit> verifyAudit(RequestVerifyAudit request) {
        // 参数校验
        if (!SingerTypeEnum.isValid(request.getSingerType())) {
            return RpcResult.fail(CommonService.PARAM_ERROR, "歌手类型不合法");
        }

        if (!SingerAuditStatusEnum.isValid(request.getAuditStatus())) {
            return RpcResult.fail(CommonService.PARAM_ERROR, "认证状态不合法");
        }

        // 获取歌手认证配置
        List<SingerVerifyRecordDTO> verifyRecordList = singerVerifyApplyManager.getSingerVerifyRecordListByIds(request.getIds());
        if (verifyRecordList.isEmpty()) {
            return RpcResult.fail(CommonService.PARAM_ERROR, "歌手认证记录不存在");
        }

        // 批量查询点唱厅审核状态
        List<Long> njIds = verifyRecordList.stream().map(SingerVerifyRecordDTO::getNjId).collect(Collectors.toList());
        Map<Long, Integer> singerHallMap = singerHallApplyManager.batchGetSingerHallStatusMap(request.getAppId(), njIds);
        // 失败的歌手认证记录ID
        List<SingerLosingBean> failedIds = Lists.newArrayList();
        // 获取歌手认证记录
        Map<Long, SingerVerifyRecordDTO> verifyRecordMap = verifyRecordList.stream().collect(Collectors.toMap(SingerVerifyRecordDTO::getId, v -> v));
        SingerAuditParamDTO param = new SingerAuditParamDTO().setOperator(request.getOperator()).setRejectReason(request.getRejectedCause())
                .setTargetAuditStatus(request.getAuditStatus()).setSingerType(request.getSingerType());

        //状态处理器
        SingerAuditStatusHandler handler = handlerFactory.getHandler(request.getAuditStatus());
        if (handler == null) {
            return RpcResult.fail(CommonService.PARAM_ERROR, "认证状态不合法");
        }
        for (Long id : request.getIds()) {
            SingerVerifyRecordDTO verifyRecord = verifyRecordMap.get(id);
            if (verifyRecord == null) {
                failedIds.add(SingerLosingBean.failure(id, "歌手认证记录不存在"));
                continue;
            }

            Integer singerHallApplyStatus = singerHallMap.get(verifyRecord.getNjId());
            param.setSingerHallStatus(singerHallApplyStatus == null ? null : SingerHallApplyStatusEnum.getByStatus(singerHallApplyStatus));
            SingerExecuteAuditDTO executedRes = handler.executeAudit(param, verifyRecord);
            if (!executedRes.isSuccess()) {
                failedIds.add(SingerLosingBean.failure(id, executedRes.getReason()));
            }
        }
        // 返回审核结果
        return RpcResult.success(new ResponseVerifyAudit().setLosingList(failedIds));

    }

    @Override
    public Result<PageBean<ResponseGetSingerVerifyRecord>> getSingerVerifyRecord(RequestGetSingerVerifyRecord request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);

        if (StringUtils.isNotEmpty(request.getNjBand())) {
            Long njId = userManager.getUserIdByBand(request.getNjBand());
            request.setNjId(njId == null ? 1 : njId);
        }

        if (StringUtils.isNotEmpty(request.getSingerBand())) {
            Long singerId = userManager.getUserIdByBand(request.getSingerBand());
            request.setUserId(singerId == null ? 1 : singerId);
        }

        // 调用 Manager 层进行分页查询
        PageBean<SingerVerifyRecordDTO> pageBean;
        if (Boolean.TRUE.equals(request.getInBlackList())) {
            pageBean = singerVerifyApplyManager.pageQuerySingerVerifyRecordWithBlackList(
                    request.getAppId(), request.getUserId(), request.getNjId(),
                    request.getSingerType(), request.getMinApplyTime(), request.getMaxApplyTime(),
                    request.getSongStyle(), request.getAuditStatus(), request.getOriginalSinger(),
                    request.getPageNo(), request.getPageSize(), request.getOrderMetrics(), request.getOrderType());
        } else {
            pageBean = singerVerifyApplyManager.pageQuerySingerVerifyRecord(
                    request.getAppId(), request.getUserId(), request.getNjId(),
                    request.getSingerType(), request.getMinApplyTime(), request.getMaxApplyTime(),
                    request.getSongStyle(), request.getAuditStatus(), request.getOriginalSinger(),
                    request.getPageNo(), request.getPageSize(), request.getOrderMetrics(), request.getOrderType());
        }

        List<Long> allUserIds = getAllUserIds(pageBean);

        // 批量查询用户信息
        Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(allUserIds);

        // 收集所有需要查询的家族ID
        List<Long> familyIds = pageBean.getList().stream()
                .map(SingerVerifyRecordDTO::getFamilyId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        Map<Long, FamilyBean> familyMap = getFamilyMap(familyIds);

        // 将 DTO 转换为 Response
        List<ResponseGetSingerVerifyRecord> responseList = SingerVerifyRecordConvert.I.toResponseGetSingerVerifyRecordList(pageBean.getList(), userMap, familyMap);

        buildSingerVerifySongInfo(responseList);

        // 构建分页结果
        PageBean<ResponseGetSingerVerifyRecord> responsePageBean = PageBean.of(pageBean.getTotal(), responseList);
        return RpcResult.success(responsePageBean);
    }

    private void buildSingerVerifySongInfo(List<ResponseGetSingerVerifyRecord> responseList) {
        if (CollectionUtils.isEmpty(responseList)) {
            return;
        }
        
        // 1. 分离有songName和无songName的记录
        Map<Boolean, List<ResponseGetSingerVerifyRecord>> partitionedRecords = responseList.stream()
                .collect(Collectors.partitioningBy(response -> StringUtils.isNotEmpty(response.getSongName())));
        
        List<ResponseGetSingerVerifyRecord> recordsWithSongName = partitionedRecords.get(true);
        List<ResponseGetSingerVerifyRecord> recordsWithoutSongName = partitionedRecords.get(false);
        
        // 2. 处理有songName的记录 - 直接构建，兼容旧数据
        buildSongInfoForRecordsWithSongName(recordsWithSongName);
        
        // 3. 处理无songName的记录 - 查询数据库
        buildSongInfoForRecordsWithoutSongName(recordsWithoutSongName);
    }

    private void buildSongInfoForRecordsWithSongName(List<ResponseGetSingerVerifyRecord> records) {
        records.forEach(response -> {
            SingerVerifySongInfoBean songInfoBean = createSongInfoBean(response);
            response.setSingerVerifySongInfoBeans(Collections.singletonList(songInfoBean));
        });
    }

    private void buildSongInfoForRecordsWithoutSongName(List<ResponseGetSingerVerifyRecord> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        
        // 查询数据库获取歌曲信息
        List<Long> applyIds = records.stream()
                .map(ResponseGetSingerVerifyRecord::getId)
                .collect(Collectors.toList());
        
        List<SingerVerifySongInfoBean> songInfoBeans = singerVerifyApplyManager.getSingerVerifySongInfoList(applyIds);
        
        // 按applyId分组并设置到对应记录
        Map<Long, List<SingerVerifySongInfoBean>> songInfoMap = songInfoBeans.stream()
                .collect(Collectors.groupingBy(SingerVerifySongInfoBean::getApplyId));
        
        records.forEach(response -> {
            List<SingerVerifySongInfoBean> songInfoList = songInfoMap.get(response.getId());
            response.setSingerVerifySongInfoBeans(songInfoList);
        });
    }

    private SingerVerifySongInfoBean createSongInfoBean(ResponseGetSingerVerifyRecord response) {
        SingerVerifySongInfoBean songInfoBean = new SingerVerifySongInfoBean();
        songInfoBean.setApplyId(response.getId());
        songInfoBean.setSongName(response.getSongName());
        songInfoBean.setSongStyle(response.getSongStyle());
        songInfoBean.setAudioPath(response.getAudioPath());
        songInfoBean.setPreAuditStatus(response.getAuditStatus());
        songInfoBean.setPreAuditRejectReason(response.getPreAuditRejectReason());
        return songInfoBean;
    }

    @Override
    public Result<List<ResponseQueryHistoryVerifyRecord>> queryHistoryVerifyRecord(RequestQueryHistoryVerifyRecord request) {
        List<Long> userIds = singerVerifyApplyManager.getSameIdCardUserIdByUserId(request.getAppId(), request.getUserId(), request.getSingerType());
        //根据用户ID列表批量查询歌手信息
        List<Integer> typeList = Lists.newArrayList(SingerOperateTypeEnum.ELIMINATE.getType());
        List<SingerOperateRecordDTO> recordList = singerOperateRecordManager.getSingerOperateRecordList(request.getAppId(), request.getSingerType(), userIds, typeList);
        // 批量查询用户信息
        Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(userIds);
        // 收集所有需要查询的家族ID
        List<Long> familyIds = recordList.stream().map(SingerOperateRecordDTO::getFamilyId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Long, FamilyBean> familyMap = getFamilyMap(familyIds);
        List<ResponseQueryHistoryVerifyRecord> responseList = SingerVerifyRecordConvert.I.toResponseQueryHistoryVerifyRecordList(recordList, userMap, familyMap);
        return RpcResult.success(responseList);
    }

    @Override
    public Result<Void> modifyVerifyApplyRemark(RequestModifyVerifyApplyRemark request) {
        boolean success = singerVerifyApplyManager.modifyVerifyApplyRemark(request.getId(), request.getRemark());
        return success ? RpcResult.success() : RpcResult.fail(MODIFY_VERIFY_APPLY_REMARK_FAIL, "更新备注失败");
    }


    private Map<Long, FamilyBean> getFamilyMap(List<Long> familyIds) {
        // 查询家族信息
        Map<Long, FamilyBean> familyMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(familyIds)) {
            for (Long familyId : familyIds) {
                Optional<FamilyBean> familyInfo = familyManager.getFamilyByCache(familyId);
                familyInfo.ifPresent(familyBean -> familyMap.put(familyId, familyBean));
            }
        }
        return familyMap;
    }

    @Override
    public Result<Void> batchAddBlackList(@Valid RequestBatchAddBlackList request) {
        // 1. 去重userIds
        List<Long> uniqueUserIds = request.getUserIds().stream().distinct().collect(Collectors.toList());
        //查询出用户的身份证信息
        Map<Long, String> idCardNumberMap = singerVerifyApplyManager.getIdCardNumberMapByUserIds(request.getAppId(), uniqueUserIds);
        if (idCardNumberMap.isEmpty()) {
            return RpcResult.fail(BATCH_ADD_BLACK_USER_NOT_FOUND, "拉黑用户未找到");
        }

        Map<String, SingerBlackListDTO> blackMap = singerBlackListManager.searchBlackListByCertNo(request.getAppId(), new ArrayList<>(idCardNumberMap.values()));
        //如果idCardNumber在blackMap中存在了，从idCardNumberMap中剔除
        for (Map.Entry<Long, String> entry : idCardNumberMap.entrySet()) {
            if (blackMap.containsKey(entry.getValue())) {
                idCardNumberMap.remove(entry.getKey());
            }
        }
        boolean batchAddRes = singerBlackListManager.batchAddBlackList(request.getAppId(), idCardNumberMap);
        return batchAddRes ? RpcResult.success() : RpcResult.fail(BATCH_ADD_BLACK_LIST_FAIL, "批量拉黑失败");
    }

    @Override
    public Result<ResponseBatchCancelBlackList> batchCancelBlackList(@Valid RequestBatchCancelBlackList request) {
        // 1. 去重userIds
        List<Long> uniqueUserIds = request.getUserIds().stream().distinct().collect(Collectors.toList());
        Map<Long, String> idCardNumberMap = singerVerifyApplyManager.getIdCardNumberMapByUserIds(request.getAppId(), uniqueUserIds);
        List<Long> losingUserIds = singerBlackListManager.batchCancelBlackList(request.getAppId(), idCardNumberMap);
        ResponseBatchCancelBlackList response = new ResponseBatchCancelBlackList();
        response.setLosingUserIds(losingUserIds);
        return RpcResult.success(response);
    }


    private List<Long> getAllUserIds(PageBean<SingerVerifyRecordDTO> pageBean) {
        // 收集所有需要查询的用户ID
        List<Long> userIds = pageBean.getList().stream()
                .map(SingerVerifyRecordDTO::getUserId)
                .collect(Collectors.toList());

        // 收集所有需要查询的NJ ID
        List<Long> njIds = pageBean.getList().stream()
                .map(SingerVerifyRecordDTO::getNjId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 合并所有需要查询的用户ID
        List<Long> allUserIds = new ArrayList<>();
        allUserIds.addAll(userIds);
        allUserIds.addAll(njIds);
        return allUserIds;
    }

}
