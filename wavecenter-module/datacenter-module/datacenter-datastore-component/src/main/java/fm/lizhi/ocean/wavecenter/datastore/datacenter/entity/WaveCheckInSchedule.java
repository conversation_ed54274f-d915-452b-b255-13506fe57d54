package fm.lizhi.ocean.wavecenter.datastore.datacenter.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 打卡档期表
 *
 * @date 2025-01-09 04:06:57
 */
@Table(name = "`wave_check_in_schedule`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WaveCheckInSchedule {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 档期主持人ID
     */
    @Column(name= "`host_id`")
    private Long hostId;

    /**
     * 主播ID，冗余
     */
    @Column(name= "`nj_id`")
    private Long njId;

    /**
     * 直播房间ID
     */
    @Column(name= "`room_id`")
    private Long roomId;

    /**
     * 家族ID，冗余给大数据用
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 档期开始时间
     */
    @Column(name= "`start_time`")
    private Date startTime;

    /**
     * 档期结束时间
     */
    @Column(name= "`end_time`")
    private Date endTime;

    /**
     * 0：未打卡，1：已打卡
     */
    @Column(name= "`status`")
    private Integer status;

    /**
     * 考核魅力值
     */
    @Column(name= "`exam_charm`")
    private Integer examCharm;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    @Column(name= "`remark`")
    private String remark;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", hostId=").append(hostId);
        sb.append(", njId=").append(njId);
        sb.append(", roomId=").append(roomId);
        sb.append(", familyId=").append(familyId);
        sb.append(", appId=").append(appId);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", status=").append(status);
        sb.append(", examCharm=").append(examCharm);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", createTime=").append(createTime);
        sb.append(", remark=").append(remark);
        sb.append("]");
        return sb.toString();
    }
}