package fm.lizhi.ocean.wavecenter.datastore.datacenter.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class WcDataPayRoomDayExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_pay_room_day
     *
     * @mbg.generated Thu Apr 24 14:52:57 CST 2025
     */
    public WcDataPayRoomDayExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_pay_room_day
     *
     * @mbg.generated Thu Apr 24 14:52:57 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_pay_room_day
     *
     * @mbg.generated Thu Apr 24 14:52:57 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_pay_room_day
     *
     * @mbg.generated Thu Apr 24 14:52:57 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_pay_room_day
     *
     * @mbg.generated Thu Apr 24 14:52:57 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_pay_room_day
     *
     * @mbg.generated Thu Apr 24 14:52:57 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_pay_room_day
     *
     * @mbg.generated Thu Apr 24 14:52:57 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_pay_room_day
     *
     * @mbg.generated Thu Apr 24 14:52:57 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_pay_room_day
     *
     * @mbg.generated Thu Apr 24 14:52:57 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_pay_room_day
     *
     * @mbg.generated Thu Apr 24 14:52:57 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_pay_room_day
     *
     * @mbg.generated Thu Apr 24 14:52:57 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Class getEntityClass() {
        return WcDataPayRoomDay.class;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wavecenter_data_pay_room_day
     *
     * @mbg.generated Thu Apr 24 14:52:57 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(Integer value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(Integer value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(Integer value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(Integer value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(Integer value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<Integer> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<Integer> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(Integer value1, Integer value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(Integer value1, Integer value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andStatDateIsNull() {
            addCriterion("stat_date is null");
            return (Criteria) this;
        }

        public Criteria andStatDateIsNotNull() {
            addCriterion("stat_date is not null");
            return (Criteria) this;
        }

        public Criteria andStatDateEqualTo(Date value) {
            addCriterionForJDBCDate("stat_date =", value, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("stat_date <>", value, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateGreaterThan(Date value) {
            addCriterionForJDBCDate("stat_date >", value, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("stat_date >=", value, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateLessThan(Date value) {
            addCriterionForJDBCDate("stat_date <", value, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("stat_date <=", value, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateIn(List<Date> values) {
            addCriterionForJDBCDate("stat_date in", values, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("stat_date not in", values, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("stat_date between", value1, value2, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("stat_date not between", value1, value2, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateValueIsNull() {
            addCriterion("stat_date_value is null");
            return (Criteria) this;
        }

        public Criteria andStatDateValueIsNotNull() {
            addCriterion("stat_date_value is not null");
            return (Criteria) this;
        }

        public Criteria andStatDateValueEqualTo(Integer value) {
            addCriterion("stat_date_value =", value, "statDateValue");
            return (Criteria) this;
        }

        public Criteria andStatDateValueNotEqualTo(Integer value) {
            addCriterion("stat_date_value <>", value, "statDateValue");
            return (Criteria) this;
        }

        public Criteria andStatDateValueGreaterThan(Integer value) {
            addCriterion("stat_date_value >", value, "statDateValue");
            return (Criteria) this;
        }

        public Criteria andStatDateValueGreaterThanOrEqualTo(Integer value) {
            addCriterion("stat_date_value >=", value, "statDateValue");
            return (Criteria) this;
        }

        public Criteria andStatDateValueLessThan(Integer value) {
            addCriterion("stat_date_value <", value, "statDateValue");
            return (Criteria) this;
        }

        public Criteria andStatDateValueLessThanOrEqualTo(Integer value) {
            addCriterion("stat_date_value <=", value, "statDateValue");
            return (Criteria) this;
        }

        public Criteria andStatDateValueIn(List<Integer> values) {
            addCriterion("stat_date_value in", values, "statDateValue");
            return (Criteria) this;
        }

        public Criteria andStatDateValueNotIn(List<Integer> values) {
            addCriterion("stat_date_value not in", values, "statDateValue");
            return (Criteria) this;
        }

        public Criteria andStatDateValueBetween(Integer value1, Integer value2) {
            addCriterion("stat_date_value between", value1, value2, "statDateValue");
            return (Criteria) this;
        }

        public Criteria andStatDateValueNotBetween(Integer value1, Integer value2) {
            addCriterion("stat_date_value not between", value1, value2, "statDateValue");
            return (Criteria) this;
        }

        public Criteria andRoomIdIsNull() {
            addCriterion("room_id is null");
            return (Criteria) this;
        }

        public Criteria andRoomIdIsNotNull() {
            addCriterion("room_id is not null");
            return (Criteria) this;
        }

        public Criteria andRoomIdEqualTo(Long value) {
            addCriterion("room_id =", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotEqualTo(Long value) {
            addCriterion("room_id <>", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdGreaterThan(Long value) {
            addCriterion("room_id >", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdGreaterThanOrEqualTo(Long value) {
            addCriterion("room_id >=", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdLessThan(Long value) {
            addCriterion("room_id <", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdLessThanOrEqualTo(Long value) {
            addCriterion("room_id <=", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdIn(List<Long> values) {
            addCriterion("room_id in", values, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotIn(List<Long> values) {
            addCriterion("room_id not in", values, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdBetween(Long value1, Long value2) {
            addCriterion("room_id between", value1, value2, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotBetween(Long value1, Long value2) {
            addCriterion("room_id not between", value1, value2, "roomId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIsNull() {
            addCriterion("family_id is null");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIsNotNull() {
            addCriterion("family_id is not null");
            return (Criteria) this;
        }

        public Criteria andFamilyIdEqualTo(Long value) {
            addCriterion("family_id =", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotEqualTo(Long value) {
            addCriterion("family_id <>", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdGreaterThan(Long value) {
            addCriterion("family_id >", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("family_id >=", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdLessThan(Long value) {
            addCriterion("family_id <", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdLessThanOrEqualTo(Long value) {
            addCriterion("family_id <=", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIn(List<Long> values) {
            addCriterion("family_id in", values, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotIn(List<Long> values) {
            addCriterion("family_id not in", values, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdBetween(Long value1, Long value2) {
            addCriterion("family_id between", value1, value2, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotBetween(Long value1, Long value2) {
            addCriterion("family_id not between", value1, value2, "familyId");
            return (Criteria) this;
        }

        public Criteria andIncomeIsNull() {
            addCriterion("income is null");
            return (Criteria) this;
        }

        public Criteria andIncomeIsNotNull() {
            addCriterion("income is not null");
            return (Criteria) this;
        }

        public Criteria andIncomeEqualTo(BigDecimal value) {
            addCriterion("income =", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeNotEqualTo(BigDecimal value) {
            addCriterion("income <>", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeGreaterThan(BigDecimal value) {
            addCriterion("income >", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("income >=", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeLessThan(BigDecimal value) {
            addCriterion("income <", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("income <=", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeIn(List<BigDecimal> values) {
            addCriterion("income in", values, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeNotIn(List<BigDecimal> values) {
            addCriterion("income not in", values, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income between", value1, value2, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income not between", value1, value2, "income");
            return (Criteria) this;
        }

        public Criteria andAllIncomeIsNull() {
            addCriterion("all_income is null");
            return (Criteria) this;
        }

        public Criteria andAllIncomeIsNotNull() {
            addCriterion("all_income is not null");
            return (Criteria) this;
        }

        public Criteria andAllIncomeEqualTo(BigDecimal value) {
            addCriterion("all_income =", value, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeNotEqualTo(BigDecimal value) {
            addCriterion("all_income <>", value, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeGreaterThan(BigDecimal value) {
            addCriterion("all_income >", value, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("all_income >=", value, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeLessThan(BigDecimal value) {
            addCriterion("all_income <", value, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("all_income <=", value, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeIn(List<BigDecimal> values) {
            addCriterion("all_income in", values, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeNotIn(List<BigDecimal> values) {
            addCriterion("all_income not in", values, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("all_income between", value1, value2, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("all_income not between", value1, value2, "allIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeIsNull() {
            addCriterion("sign_hall_income is null");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeIsNotNull() {
            addCriterion("sign_hall_income is not null");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeEqualTo(BigDecimal value) {
            addCriterion("sign_hall_income =", value, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeNotEqualTo(BigDecimal value) {
            addCriterion("sign_hall_income <>", value, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeGreaterThan(BigDecimal value) {
            addCriterion("sign_hall_income >", value, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("sign_hall_income >=", value, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeLessThan(BigDecimal value) {
            addCriterion("sign_hall_income <", value, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("sign_hall_income <=", value, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeIn(List<BigDecimal> values) {
            addCriterion("sign_hall_income in", values, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeNotIn(List<BigDecimal> values) {
            addCriterion("sign_hall_income not in", values, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sign_hall_income between", value1, value2, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sign_hall_income not between", value1, value2, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeIsNull() {
            addCriterion("official_hall_income is null");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeIsNotNull() {
            addCriterion("official_hall_income is not null");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeEqualTo(BigDecimal value) {
            addCriterion("official_hall_income =", value, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeNotEqualTo(BigDecimal value) {
            addCriterion("official_hall_income <>", value, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeGreaterThan(BigDecimal value) {
            addCriterion("official_hall_income >", value, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("official_hall_income >=", value, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeLessThan(BigDecimal value) {
            addCriterion("official_hall_income <", value, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("official_hall_income <=", value, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeIn(List<BigDecimal> values) {
            addCriterion("official_hall_income in", values, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeNotIn(List<BigDecimal> values) {
            addCriterion("official_hall_income not in", values, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("official_hall_income between", value1, value2, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("official_hall_income not between", value1, value2, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeIsNull() {
            addCriterion("personal_hall_income is null");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeIsNotNull() {
            addCriterion("personal_hall_income is not null");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeEqualTo(BigDecimal value) {
            addCriterion("personal_hall_income =", value, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeNotEqualTo(BigDecimal value) {
            addCriterion("personal_hall_income <>", value, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeGreaterThan(BigDecimal value) {
            addCriterion("personal_hall_income >", value, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("personal_hall_income >=", value, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeLessThan(BigDecimal value) {
            addCriterion("personal_hall_income <", value, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("personal_hall_income <=", value, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeIn(List<BigDecimal> values) {
            addCriterion("personal_hall_income in", values, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeNotIn(List<BigDecimal> values) {
            addCriterion("personal_hall_income not in", values, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("personal_hall_income between", value1, value2, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("personal_hall_income not between", value1, value2, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeIsNull() {
            addCriterion("noble_income is null");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeIsNotNull() {
            addCriterion("noble_income is not null");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeEqualTo(BigDecimal value) {
            addCriterion("noble_income =", value, "nobleIncome");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeNotEqualTo(BigDecimal value) {
            addCriterion("noble_income <>", value, "nobleIncome");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeGreaterThan(BigDecimal value) {
            addCriterion("noble_income >", value, "nobleIncome");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("noble_income >=", value, "nobleIncome");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeLessThan(BigDecimal value) {
            addCriterion("noble_income <", value, "nobleIncome");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("noble_income <=", value, "nobleIncome");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeIn(List<BigDecimal> values) {
            addCriterion("noble_income in", values, "nobleIncome");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeNotIn(List<BigDecimal> values) {
            addCriterion("noble_income not in", values, "nobleIncome");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("noble_income between", value1, value2, "nobleIncome");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("noble_income not between", value1, value2, "nobleIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeIsNull() {
            addCriterion("personal_noble_income is null");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeIsNotNull() {
            addCriterion("personal_noble_income is not null");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeEqualTo(BigDecimal value) {
            addCriterion("personal_noble_income =", value, "personalNobleIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeNotEqualTo(BigDecimal value) {
            addCriterion("personal_noble_income <>", value, "personalNobleIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeGreaterThan(BigDecimal value) {
            addCriterion("personal_noble_income >", value, "personalNobleIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("personal_noble_income >=", value, "personalNobleIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeLessThan(BigDecimal value) {
            addCriterion("personal_noble_income <", value, "personalNobleIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("personal_noble_income <=", value, "personalNobleIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeIn(List<BigDecimal> values) {
            addCriterion("personal_noble_income in", values, "personalNobleIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeNotIn(List<BigDecimal> values) {
            addCriterion("personal_noble_income not in", values, "personalNobleIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("personal_noble_income between", value1, value2, "personalNobleIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("personal_noble_income not between", value1, value2, "personalNobleIncome");
            return (Criteria) this;
        }

        public Criteria andRoomNobleIncomeIsNull() {
            addCriterion("room_noble_income is null");
            return (Criteria) this;
        }

        public Criteria andRoomNobleIncomeIsNotNull() {
            addCriterion("room_noble_income is not null");
            return (Criteria) this;
        }

        public Criteria andRoomNobleIncomeEqualTo(BigDecimal value) {
            addCriterion("room_noble_income =", value, "roomNobleIncome");
            return (Criteria) this;
        }

        public Criteria andRoomNobleIncomeNotEqualTo(BigDecimal value) {
            addCriterion("room_noble_income <>", value, "roomNobleIncome");
            return (Criteria) this;
        }

        public Criteria andRoomNobleIncomeGreaterThan(BigDecimal value) {
            addCriterion("room_noble_income >", value, "roomNobleIncome");
            return (Criteria) this;
        }

        public Criteria andRoomNobleIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("room_noble_income >=", value, "roomNobleIncome");
            return (Criteria) this;
        }

        public Criteria andRoomNobleIncomeLessThan(BigDecimal value) {
            addCriterion("room_noble_income <", value, "roomNobleIncome");
            return (Criteria) this;
        }

        public Criteria andRoomNobleIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("room_noble_income <=", value, "roomNobleIncome");
            return (Criteria) this;
        }

        public Criteria andRoomNobleIncomeIn(List<BigDecimal> values) {
            addCriterion("room_noble_income in", values, "roomNobleIncome");
            return (Criteria) this;
        }

        public Criteria andRoomNobleIncomeNotIn(List<BigDecimal> values) {
            addCriterion("room_noble_income not in", values, "roomNobleIncome");
            return (Criteria) this;
        }

        public Criteria andRoomNobleIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("room_noble_income between", value1, value2, "roomNobleIncome");
            return (Criteria) this;
        }

        public Criteria andRoomNobleIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("room_noble_income not between", value1, value2, "roomNobleIncome");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIsNull() {
            addCriterion("deploy_env is null");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIsNotNull() {
            addCriterion("deploy_env is not null");
            return (Criteria) this;
        }

        public Criteria andDeployEnvEqualTo(String value) {
            addCriterion("deploy_env =", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotEqualTo(String value) {
            addCriterion("deploy_env <>", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvGreaterThan(String value) {
            addCriterion("deploy_env >", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvGreaterThanOrEqualTo(String value) {
            addCriterion("deploy_env >=", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLessThan(String value) {
            addCriterion("deploy_env <", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLessThanOrEqualTo(String value) {
            addCriterion("deploy_env <=", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLike(String value) {
            addCriterion("deploy_env like", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotLike(String value) {
            addCriterion("deploy_env not like", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIn(List<String> values) {
            addCriterion("deploy_env in", values, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotIn(List<String> values) {
            addCriterion("deploy_env not in", values, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvBetween(String value1, String value2) {
            addCriterion("deploy_env between", value1, value2, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotBetween(String value1, String value2) {
            addCriterion("deploy_env not between", value1, value2, "deployEnv");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wavecenter_data_pay_room_day
     *
     * @mbg.generated do_not_delete_during_merge Thu Apr 24 14:52:57 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wavecenter_data_pay_room_day
     *
     * @mbg.generated Thu Apr 24 14:52:57 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}