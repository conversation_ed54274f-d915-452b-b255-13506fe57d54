package fm.lizhi.ocean.wavecenter.datastore.datacenter.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 *
 * @date 2024-11-13 10:33:43
 */
@Table(name = "`wavecenter_audit_record_full`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcAuditRecordFull {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 签约厅主ID
     */
    @Column(name= "`sign_nj_id`")
    private Long signNjId;

    /**
     * 原录音文件 URL
     */
    @Column(name= "`source_content_url`")
    private String sourceContentUrl;

    /**
     * 公开录音文件 URL
     */
    @Column(name= "`public_content_url`")
    private String publicContentUrl;

    /**
     * 签约家族ID
     */
    @Column(name= "`sign_family_id`")
    private Long signFamilyId;

    /**
     * 关联的业务场景ID: 直播场景为liveId
     */
    @Column(name= "`biz_id`")
    private Long bizId;

    /**
     * 关联的业务场景对应的厅主ID,比如直播的厅主ID
     */
    @Column(name= "`biz_nj_id`")
    private Long bizNjId;

    /**
     * 关联的业务场景对应的家族ID,比如直播的家族ID
     */
    @Column(name= "`biz_family_id`")
    private Long bizFamilyId;

    /**
     * 违规用户ID
     */
    @Column(name= "`user_id`")
    private Long userId;

    /**
     * 受害者用户ID
     */
    @Column(name= "`to_user_id`")
    private Long toUserId;

    /**
     * 审核处罚类型
     */
    @Column(name= "`op`")
    private Integer op;

    /**
     * 操作理由
     */
    @Column(name= "`reason`")
    private String reason;

    /**
     * 审核时间
     */
    @Column(name= "`audit_end_time`")
    private Date auditEndTime;

    /**
     * 送审时间
     */
    @Column(name= "`audit_start_time`")
    private Date auditStartTime;

    /**
     * 送审id
     */
    @Column(name= "`audit_id`")
    private String auditId;

    /**
     * 审核记录ID
     */
    @Column(name= "`record_id`")
    private String recordId;

    /**
     * 接审业务场景值
     */
    @Column(name= "`scene_type`")
    private Integer sceneType;

    /**
     * 接审业务场景名称
     */
    @Column(name= "`scene_name`")
    private String sceneName;

    /**
     * 处罚有效时间:1天
     */
    @Column(name= "`push_time`")
    private String pushTime;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", signNjId=").append(signNjId);
        sb.append(", sourceContentUrl=").append(sourceContentUrl);
        sb.append(", publicContentUrl=").append(publicContentUrl);
        sb.append(", signFamilyId=").append(signFamilyId);
        sb.append(", bizId=").append(bizId);
        sb.append(", bizNjId=").append(bizNjId);
        sb.append(", bizFamilyId=").append(bizFamilyId);
        sb.append(", userId=").append(userId);
        sb.append(", toUserId=").append(toUserId);
        sb.append(", op=").append(op);
        sb.append(", reason=").append(reason);
        sb.append(", auditEndTime=").append(auditEndTime);
        sb.append(", auditStartTime=").append(auditStartTime);
        sb.append(", auditId=").append(auditId);
        sb.append(", recordId=").append(recordId);
        sb.append(", sceneType=").append(sceneType);
        sb.append(", sceneName=").append(sceneName);
        sb.append(", pushTime=").append(pushTime);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}