package fm.lizhi.ocean.wavecenter.datastore.datacenter.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 大数据主播日统计表
 *
 * @date 2025-06-09 10:56:33
 */
@Table(name = "`wavecenter_data_player_day_real_time`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcDataPlayerDayRealTime {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private String id;

    /**
     * 业务
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 日期 格式 YYYY-MM-DD
     */
    @Column(name= "`stat_date`")
    private Date statDate;

    /**
     * 日期 格式  YYYYMMDD
     */
    @Column(name= "`stat_date_value`")
    private Integer statDateValue;

    /**
     * 签约主播ID
     */
    @Column(name= "`player_id`")
    private Long playerId;

    /**
     * 私信人数
     */
    @Column(name= "`chat_user_cnt`")
    private Integer chatUserCnt;

    /**
     * 私信回复人数
     */
    @Column(name= "`reply_chat_user_cnt`")
    private Long replyChatUserCnt;

    /**
     * 私信回复率
     */
    @Column(name= "`reply_chat_rate`")
    private BigDecimal replyChatRate;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", statDate=").append(statDate);
        sb.append(", statDateValue=").append(statDateValue);
        sb.append(", playerId=").append(playerId);
        sb.append(", chatUserCnt=").append(chatUserCnt);
        sb.append(", replyChatUserCnt=").append(replyChatUserCnt);
        sb.append(", replyChatRate=").append(replyChatRate);
        sb.append("]");
        return sb.toString();
    }
}