package fm.lizhi.ocean.wavecenter.datastore.datacenter.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WcDataRoomMonthExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_room_month
     *
     * @mbg.generated Mon Mar 24 18:23:10 CST 2025
     */
    public WcDataRoomMonthExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_room_month
     *
     * @mbg.generated Mon Mar 24 18:23:10 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_room_month
     *
     * @mbg.generated Mon Mar 24 18:23:10 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_room_month
     *
     * @mbg.generated Mon Mar 24 18:23:10 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_room_month
     *
     * @mbg.generated Mon Mar 24 18:23:10 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_room_month
     *
     * @mbg.generated Mon Mar 24 18:23:10 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_room_month
     *
     * @mbg.generated Mon Mar 24 18:23:10 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_room_month
     *
     * @mbg.generated Mon Mar 24 18:23:10 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_room_month
     *
     * @mbg.generated Mon Mar 24 18:23:10 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_room_month
     *
     * @mbg.generated Mon Mar 24 18:23:10 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_room_month
     *
     * @mbg.generated Mon Mar 24 18:23:10 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Class getEntityClass() {
        return WcDataRoomMonth.class;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wavecenter_data_room_month
     *
     * @mbg.generated Mon Mar 24 18:23:10 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(Integer value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(Integer value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(Integer value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(Integer value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(Integer value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<Integer> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<Integer> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(Integer value1, Integer value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(Integer value1, Integer value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andStatYearIsNull() {
            addCriterion("stat_year is null");
            return (Criteria) this;
        }

        public Criteria andStatYearIsNotNull() {
            addCriterion("stat_year is not null");
            return (Criteria) this;
        }

        public Criteria andStatYearEqualTo(Integer value) {
            addCriterion("stat_year =", value, "statYear");
            return (Criteria) this;
        }

        public Criteria andStatYearNotEqualTo(Integer value) {
            addCriterion("stat_year <>", value, "statYear");
            return (Criteria) this;
        }

        public Criteria andStatYearGreaterThan(Integer value) {
            addCriterion("stat_year >", value, "statYear");
            return (Criteria) this;
        }

        public Criteria andStatYearGreaterThanOrEqualTo(Integer value) {
            addCriterion("stat_year >=", value, "statYear");
            return (Criteria) this;
        }

        public Criteria andStatYearLessThan(Integer value) {
            addCriterion("stat_year <", value, "statYear");
            return (Criteria) this;
        }

        public Criteria andStatYearLessThanOrEqualTo(Integer value) {
            addCriterion("stat_year <=", value, "statYear");
            return (Criteria) this;
        }

        public Criteria andStatYearIn(List<Integer> values) {
            addCriterion("stat_year in", values, "statYear");
            return (Criteria) this;
        }

        public Criteria andStatYearNotIn(List<Integer> values) {
            addCriterion("stat_year not in", values, "statYear");
            return (Criteria) this;
        }

        public Criteria andStatYearBetween(Integer value1, Integer value2) {
            addCriterion("stat_year between", value1, value2, "statYear");
            return (Criteria) this;
        }

        public Criteria andStatYearNotBetween(Integer value1, Integer value2) {
            addCriterion("stat_year not between", value1, value2, "statYear");
            return (Criteria) this;
        }

        public Criteria andStatMonthIsNull() {
            addCriterion("stat_month is null");
            return (Criteria) this;
        }

        public Criteria andStatMonthIsNotNull() {
            addCriterion("stat_month is not null");
            return (Criteria) this;
        }

        public Criteria andStatMonthEqualTo(Integer value) {
            addCriterion("stat_month =", value, "statMonth");
            return (Criteria) this;
        }

        public Criteria andStatMonthNotEqualTo(Integer value) {
            addCriterion("stat_month <>", value, "statMonth");
            return (Criteria) this;
        }

        public Criteria andStatMonthGreaterThan(Integer value) {
            addCriterion("stat_month >", value, "statMonth");
            return (Criteria) this;
        }

        public Criteria andStatMonthGreaterThanOrEqualTo(Integer value) {
            addCriterion("stat_month >=", value, "statMonth");
            return (Criteria) this;
        }

        public Criteria andStatMonthLessThan(Integer value) {
            addCriterion("stat_month <", value, "statMonth");
            return (Criteria) this;
        }

        public Criteria andStatMonthLessThanOrEqualTo(Integer value) {
            addCriterion("stat_month <=", value, "statMonth");
            return (Criteria) this;
        }

        public Criteria andStatMonthIn(List<Integer> values) {
            addCriterion("stat_month in", values, "statMonth");
            return (Criteria) this;
        }

        public Criteria andStatMonthNotIn(List<Integer> values) {
            addCriterion("stat_month not in", values, "statMonth");
            return (Criteria) this;
        }

        public Criteria andStatMonthBetween(Integer value1, Integer value2) {
            addCriterion("stat_month between", value1, value2, "statMonth");
            return (Criteria) this;
        }

        public Criteria andStatMonthNotBetween(Integer value1, Integer value2) {
            addCriterion("stat_month not between", value1, value2, "statMonth");
            return (Criteria) this;
        }

        public Criteria andRoomIdIsNull() {
            addCriterion("room_id is null");
            return (Criteria) this;
        }

        public Criteria andRoomIdIsNotNull() {
            addCriterion("room_id is not null");
            return (Criteria) this;
        }

        public Criteria andRoomIdEqualTo(Long value) {
            addCriterion("room_id =", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotEqualTo(Long value) {
            addCriterion("room_id <>", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdGreaterThan(Long value) {
            addCriterion("room_id >", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdGreaterThanOrEqualTo(Long value) {
            addCriterion("room_id >=", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdLessThan(Long value) {
            addCriterion("room_id <", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdLessThanOrEqualTo(Long value) {
            addCriterion("room_id <=", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdIn(List<Long> values) {
            addCriterion("room_id in", values, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotIn(List<Long> values) {
            addCriterion("room_id not in", values, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdBetween(Long value1, Long value2) {
            addCriterion("room_id between", value1, value2, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotBetween(Long value1, Long value2) {
            addCriterion("room_id not between", value1, value2, "roomId");
            return (Criteria) this;
        }

        public Criteria andIncomeIsNull() {
            addCriterion("income is null");
            return (Criteria) this;
        }

        public Criteria andIncomeIsNotNull() {
            addCriterion("income is not null");
            return (Criteria) this;
        }

        public Criteria andIncomeEqualTo(BigDecimal value) {
            addCriterion("income =", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeNotEqualTo(BigDecimal value) {
            addCriterion("income <>", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeGreaterThan(BigDecimal value) {
            addCriterion("income >", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("income >=", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeLessThan(BigDecimal value) {
            addCriterion("income <", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("income <=", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeIn(List<BigDecimal> values) {
            addCriterion("income in", values, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeNotIn(List<BigDecimal> values) {
            addCriterion("income not in", values, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income between", value1, value2, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income not between", value1, value2, "income");
            return (Criteria) this;
        }

        public Criteria andCharmIsNull() {
            addCriterion("charm is null");
            return (Criteria) this;
        }

        public Criteria andCharmIsNotNull() {
            addCriterion("charm is not null");
            return (Criteria) this;
        }

        public Criteria andCharmEqualTo(Integer value) {
            addCriterion("charm =", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmNotEqualTo(Integer value) {
            addCriterion("charm <>", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmGreaterThan(Integer value) {
            addCriterion("charm >", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmGreaterThanOrEqualTo(Integer value) {
            addCriterion("charm >=", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmLessThan(Integer value) {
            addCriterion("charm <", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmLessThanOrEqualTo(Integer value) {
            addCriterion("charm <=", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmIn(List<Integer> values) {
            addCriterion("charm in", values, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmNotIn(List<Integer> values) {
            addCriterion("charm not in", values, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmBetween(Integer value1, Integer value2) {
            addCriterion("charm between", value1, value2, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmNotBetween(Integer value1, Integer value2) {
            addCriterion("charm not between", value1, value2, "charm");
            return (Criteria) this;
        }

        public Criteria andOpenDurationIsNull() {
            addCriterion("open_duration is null");
            return (Criteria) this;
        }

        public Criteria andOpenDurationIsNotNull() {
            addCriterion("open_duration is not null");
            return (Criteria) this;
        }

        public Criteria andOpenDurationEqualTo(BigDecimal value) {
            addCriterion("open_duration =", value, "openDuration");
            return (Criteria) this;
        }

        public Criteria andOpenDurationNotEqualTo(BigDecimal value) {
            addCriterion("open_duration <>", value, "openDuration");
            return (Criteria) this;
        }

        public Criteria andOpenDurationGreaterThan(BigDecimal value) {
            addCriterion("open_duration >", value, "openDuration");
            return (Criteria) this;
        }

        public Criteria andOpenDurationGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("open_duration >=", value, "openDuration");
            return (Criteria) this;
        }

        public Criteria andOpenDurationLessThan(BigDecimal value) {
            addCriterion("open_duration <", value, "openDuration");
            return (Criteria) this;
        }

        public Criteria andOpenDurationLessThanOrEqualTo(BigDecimal value) {
            addCriterion("open_duration <=", value, "openDuration");
            return (Criteria) this;
        }

        public Criteria andOpenDurationIn(List<BigDecimal> values) {
            addCriterion("open_duration in", values, "openDuration");
            return (Criteria) this;
        }

        public Criteria andOpenDurationNotIn(List<BigDecimal> values) {
            addCriterion("open_duration not in", values, "openDuration");
            return (Criteria) this;
        }

        public Criteria andOpenDurationBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("open_duration between", value1, value2, "openDuration");
            return (Criteria) this;
        }

        public Criteria andOpenDurationNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("open_duration not between", value1, value2, "openDuration");
            return (Criteria) this;
        }

        public Criteria andSignPlayerCntIsNull() {
            addCriterion("sign_player_cnt is null");
            return (Criteria) this;
        }

        public Criteria andSignPlayerCntIsNotNull() {
            addCriterion("sign_player_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andSignPlayerCntEqualTo(Integer value) {
            addCriterion("sign_player_cnt =", value, "signPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andSignPlayerCntNotEqualTo(Integer value) {
            addCriterion("sign_player_cnt <>", value, "signPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andSignPlayerCntGreaterThan(Integer value) {
            addCriterion("sign_player_cnt >", value, "signPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andSignPlayerCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("sign_player_cnt >=", value, "signPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andSignPlayerCntLessThan(Integer value) {
            addCriterion("sign_player_cnt <", value, "signPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andSignPlayerCntLessThanOrEqualTo(Integer value) {
            addCriterion("sign_player_cnt <=", value, "signPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andSignPlayerCntIn(List<Integer> values) {
            addCriterion("sign_player_cnt in", values, "signPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andSignPlayerCntNotIn(List<Integer> values) {
            addCriterion("sign_player_cnt not in", values, "signPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andSignPlayerCntBetween(Integer value1, Integer value2) {
            addCriterion("sign_player_cnt between", value1, value2, "signPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andSignPlayerCntNotBetween(Integer value1, Integer value2) {
            addCriterion("sign_player_cnt not between", value1, value2, "signPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerCntIsNull() {
            addCriterion("income_player_cnt is null");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerCntIsNotNull() {
            addCriterion("income_player_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerCntEqualTo(Integer value) {
            addCriterion("income_player_cnt =", value, "incomePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerCntNotEqualTo(Integer value) {
            addCriterion("income_player_cnt <>", value, "incomePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerCntGreaterThan(Integer value) {
            addCriterion("income_player_cnt >", value, "incomePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("income_player_cnt >=", value, "incomePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerCntLessThan(Integer value) {
            addCriterion("income_player_cnt <", value, "incomePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerCntLessThanOrEqualTo(Integer value) {
            addCriterion("income_player_cnt <=", value, "incomePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerCntIn(List<Integer> values) {
            addCriterion("income_player_cnt in", values, "incomePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerCntNotIn(List<Integer> values) {
            addCriterion("income_player_cnt not in", values, "incomePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerCntBetween(Integer value1, Integer value2) {
            addCriterion("income_player_cnt between", value1, value2, "incomePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerCntNotBetween(Integer value1, Integer value2) {
            addCriterion("income_player_cnt not between", value1, value2, "incomePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerRateIsNull() {
            addCriterion("income_player_rate is null");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerRateIsNotNull() {
            addCriterion("income_player_rate is not null");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerRateEqualTo(BigDecimal value) {
            addCriterion("income_player_rate =", value, "incomePlayerRate");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerRateNotEqualTo(BigDecimal value) {
            addCriterion("income_player_rate <>", value, "incomePlayerRate");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerRateGreaterThan(BigDecimal value) {
            addCriterion("income_player_rate >", value, "incomePlayerRate");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("income_player_rate >=", value, "incomePlayerRate");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerRateLessThan(BigDecimal value) {
            addCriterion("income_player_rate <", value, "incomePlayerRate");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("income_player_rate <=", value, "incomePlayerRate");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerRateIn(List<BigDecimal> values) {
            addCriterion("income_player_rate in", values, "incomePlayerRate");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerRateNotIn(List<BigDecimal> values) {
            addCriterion("income_player_rate not in", values, "incomePlayerRate");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income_player_rate between", value1, value2, "incomePlayerRate");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income_player_rate not between", value1, value2, "incomePlayerRate");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgIncomeIsNull() {
            addCriterion("player_avg_income is null");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgIncomeIsNotNull() {
            addCriterion("player_avg_income is not null");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgIncomeEqualTo(BigDecimal value) {
            addCriterion("player_avg_income =", value, "playerAvgIncome");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgIncomeNotEqualTo(BigDecimal value) {
            addCriterion("player_avg_income <>", value, "playerAvgIncome");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgIncomeGreaterThan(BigDecimal value) {
            addCriterion("player_avg_income >", value, "playerAvgIncome");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("player_avg_income >=", value, "playerAvgIncome");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgIncomeLessThan(BigDecimal value) {
            addCriterion("player_avg_income <", value, "playerAvgIncome");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("player_avg_income <=", value, "playerAvgIncome");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgIncomeIn(List<BigDecimal> values) {
            addCriterion("player_avg_income in", values, "playerAvgIncome");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgIncomeNotIn(List<BigDecimal> values) {
            addCriterion("player_avg_income not in", values, "playerAvgIncome");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("player_avg_income between", value1, value2, "playerAvgIncome");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("player_avg_income not between", value1, value2, "playerAvgIncome");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgCharmIsNull() {
            addCriterion("player_avg_charm is null");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgCharmIsNotNull() {
            addCriterion("player_avg_charm is not null");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgCharmEqualTo(BigDecimal value) {
            addCriterion("player_avg_charm =", value, "playerAvgCharm");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgCharmNotEqualTo(BigDecimal value) {
            addCriterion("player_avg_charm <>", value, "playerAvgCharm");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgCharmGreaterThan(BigDecimal value) {
            addCriterion("player_avg_charm >", value, "playerAvgCharm");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgCharmGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("player_avg_charm >=", value, "playerAvgCharm");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgCharmLessThan(BigDecimal value) {
            addCriterion("player_avg_charm <", value, "playerAvgCharm");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgCharmLessThanOrEqualTo(BigDecimal value) {
            addCriterion("player_avg_charm <=", value, "playerAvgCharm");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgCharmIn(List<BigDecimal> values) {
            addCriterion("player_avg_charm in", values, "playerAvgCharm");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgCharmNotIn(List<BigDecimal> values) {
            addCriterion("player_avg_charm not in", values, "playerAvgCharm");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgCharmBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("player_avg_charm between", value1, value2, "playerAvgCharm");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgCharmNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("player_avg_charm not between", value1, value2, "playerAvgCharm");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntIsNull() {
            addCriterion("gift_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntIsNotNull() {
            addCriterion("gift_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntEqualTo(Integer value) {
            addCriterion("gift_user_cnt =", value, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntNotEqualTo(Integer value) {
            addCriterion("gift_user_cnt <>", value, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntGreaterThan(Integer value) {
            addCriterion("gift_user_cnt >", value, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("gift_user_cnt >=", value, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntLessThan(Integer value) {
            addCriterion("gift_user_cnt <", value, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("gift_user_cnt <=", value, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntIn(List<Integer> values) {
            addCriterion("gift_user_cnt in", values, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntNotIn(List<Integer> values) {
            addCriterion("gift_user_cnt not in", values, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntBetween(Integer value1, Integer value2) {
            addCriterion("gift_user_cnt between", value1, value2, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("gift_user_cnt not between", value1, value2, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserPriceIsNull() {
            addCriterion("gift_user_price is null");
            return (Criteria) this;
        }

        public Criteria andGiftUserPriceIsNotNull() {
            addCriterion("gift_user_price is not null");
            return (Criteria) this;
        }

        public Criteria andGiftUserPriceEqualTo(BigDecimal value) {
            addCriterion("gift_user_price =", value, "giftUserPrice");
            return (Criteria) this;
        }

        public Criteria andGiftUserPriceNotEqualTo(BigDecimal value) {
            addCriterion("gift_user_price <>", value, "giftUserPrice");
            return (Criteria) this;
        }

        public Criteria andGiftUserPriceGreaterThan(BigDecimal value) {
            addCriterion("gift_user_price >", value, "giftUserPrice");
            return (Criteria) this;
        }

        public Criteria andGiftUserPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("gift_user_price >=", value, "giftUserPrice");
            return (Criteria) this;
        }

        public Criteria andGiftUserPriceLessThan(BigDecimal value) {
            addCriterion("gift_user_price <", value, "giftUserPrice");
            return (Criteria) this;
        }

        public Criteria andGiftUserPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("gift_user_price <=", value, "giftUserPrice");
            return (Criteria) this;
        }

        public Criteria andGiftUserPriceIn(List<BigDecimal> values) {
            addCriterion("gift_user_price in", values, "giftUserPrice");
            return (Criteria) this;
        }

        public Criteria andGiftUserPriceNotIn(List<BigDecimal> values) {
            addCriterion("gift_user_price not in", values, "giftUserPrice");
            return (Criteria) this;
        }

        public Criteria andGiftUserPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("gift_user_price between", value1, value2, "giftUserPrice");
            return (Criteria) this;
        }

        public Criteria andGiftUserPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("gift_user_price not between", value1, value2, "giftUserPrice");
            return (Criteria) this;
        }

        public Criteria andChatUserCntIsNull() {
            addCriterion("chat_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andChatUserCntIsNotNull() {
            addCriterion("chat_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andChatUserCntEqualTo(Integer value) {
            addCriterion("chat_user_cnt =", value, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntNotEqualTo(Integer value) {
            addCriterion("chat_user_cnt <>", value, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntGreaterThan(Integer value) {
            addCriterion("chat_user_cnt >", value, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("chat_user_cnt >=", value, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntLessThan(Integer value) {
            addCriterion("chat_user_cnt <", value, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("chat_user_cnt <=", value, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntIn(List<Integer> values) {
            addCriterion("chat_user_cnt in", values, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntNotIn(List<Integer> values) {
            addCriterion("chat_user_cnt not in", values, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntBetween(Integer value1, Integer value2) {
            addCriterion("chat_user_cnt between", value1, value2, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("chat_user_cnt not between", value1, value2, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntIsNull() {
            addCriterion("reply_chat_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntIsNotNull() {
            addCriterion("reply_chat_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntEqualTo(Integer value) {
            addCriterion("reply_chat_user_cnt =", value, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntNotEqualTo(Integer value) {
            addCriterion("reply_chat_user_cnt <>", value, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntGreaterThan(Integer value) {
            addCriterion("reply_chat_user_cnt >", value, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("reply_chat_user_cnt >=", value, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntLessThan(Integer value) {
            addCriterion("reply_chat_user_cnt <", value, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("reply_chat_user_cnt <=", value, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntIn(List<Integer> values) {
            addCriterion("reply_chat_user_cnt in", values, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntNotIn(List<Integer> values) {
            addCriterion("reply_chat_user_cnt not in", values, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntBetween(Integer value1, Integer value2) {
            addCriterion("reply_chat_user_cnt between", value1, value2, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("reply_chat_user_cnt not between", value1, value2, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomUserCntIsNull() {
            addCriterion("chat_enter_room_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomUserCntIsNotNull() {
            addCriterion("chat_enter_room_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomUserCntEqualTo(Integer value) {
            addCriterion("chat_enter_room_user_cnt =", value, "chatEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomUserCntNotEqualTo(Integer value) {
            addCriterion("chat_enter_room_user_cnt <>", value, "chatEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomUserCntGreaterThan(Integer value) {
            addCriterion("chat_enter_room_user_cnt >", value, "chatEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("chat_enter_room_user_cnt >=", value, "chatEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomUserCntLessThan(Integer value) {
            addCriterion("chat_enter_room_user_cnt <", value, "chatEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("chat_enter_room_user_cnt <=", value, "chatEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomUserCntIn(List<Integer> values) {
            addCriterion("chat_enter_room_user_cnt in", values, "chatEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomUserCntNotIn(List<Integer> values) {
            addCriterion("chat_enter_room_user_cnt not in", values, "chatEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomUserCntBetween(Integer value1, Integer value2) {
            addCriterion("chat_enter_room_user_cnt between", value1, value2, "chatEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("chat_enter_room_user_cnt not between", value1, value2, "chatEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatGiftUserCntIsNull() {
            addCriterion("chat_gift_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andChatGiftUserCntIsNotNull() {
            addCriterion("chat_gift_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andChatGiftUserCntEqualTo(Integer value) {
            addCriterion("chat_gift_user_cnt =", value, "chatGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatGiftUserCntNotEqualTo(Integer value) {
            addCriterion("chat_gift_user_cnt <>", value, "chatGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatGiftUserCntGreaterThan(Integer value) {
            addCriterion("chat_gift_user_cnt >", value, "chatGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatGiftUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("chat_gift_user_cnt >=", value, "chatGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatGiftUserCntLessThan(Integer value) {
            addCriterion("chat_gift_user_cnt <", value, "chatGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatGiftUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("chat_gift_user_cnt <=", value, "chatGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatGiftUserCntIn(List<Integer> values) {
            addCriterion("chat_gift_user_cnt in", values, "chatGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatGiftUserCntNotIn(List<Integer> values) {
            addCriterion("chat_gift_user_cnt not in", values, "chatGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatGiftUserCntBetween(Integer value1, Integer value2) {
            addCriterion("chat_gift_user_cnt between", value1, value2, "chatGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatGiftUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("chat_gift_user_cnt not between", value1, value2, "chatGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateIsNull() {
            addCriterion("reply_chat_rate is null");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateIsNotNull() {
            addCriterion("reply_chat_rate is not null");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateEqualTo(BigDecimal value) {
            addCriterion("reply_chat_rate =", value, "replyChatRate");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateNotEqualTo(BigDecimal value) {
            addCriterion("reply_chat_rate <>", value, "replyChatRate");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateGreaterThan(BigDecimal value) {
            addCriterion("reply_chat_rate >", value, "replyChatRate");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("reply_chat_rate >=", value, "replyChatRate");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateLessThan(BigDecimal value) {
            addCriterion("reply_chat_rate <", value, "replyChatRate");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("reply_chat_rate <=", value, "replyChatRate");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateIn(List<BigDecimal> values) {
            addCriterion("reply_chat_rate in", values, "replyChatRate");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateNotIn(List<BigDecimal> values) {
            addCriterion("reply_chat_rate not in", values, "replyChatRate");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("reply_chat_rate between", value1, value2, "replyChatRate");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("reply_chat_rate not between", value1, value2, "replyChatRate");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomRateIsNull() {
            addCriterion("chat_enter_room_rate is null");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomRateIsNotNull() {
            addCriterion("chat_enter_room_rate is not null");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomRateEqualTo(BigDecimal value) {
            addCriterion("chat_enter_room_rate =", value, "chatEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomRateNotEqualTo(BigDecimal value) {
            addCriterion("chat_enter_room_rate <>", value, "chatEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomRateGreaterThan(BigDecimal value) {
            addCriterion("chat_enter_room_rate >", value, "chatEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("chat_enter_room_rate >=", value, "chatEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomRateLessThan(BigDecimal value) {
            addCriterion("chat_enter_room_rate <", value, "chatEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("chat_enter_room_rate <=", value, "chatEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomRateIn(List<BigDecimal> values) {
            addCriterion("chat_enter_room_rate in", values, "chatEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomRateNotIn(List<BigDecimal> values) {
            addCriterion("chat_enter_room_rate not in", values, "chatEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("chat_enter_room_rate between", value1, value2, "chatEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("chat_enter_room_rate not between", value1, value2, "chatEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andChatGiftRateIsNull() {
            addCriterion("chat_gift_rate is null");
            return (Criteria) this;
        }

        public Criteria andChatGiftRateIsNotNull() {
            addCriterion("chat_gift_rate is not null");
            return (Criteria) this;
        }

        public Criteria andChatGiftRateEqualTo(BigDecimal value) {
            addCriterion("chat_gift_rate =", value, "chatGiftRate");
            return (Criteria) this;
        }

        public Criteria andChatGiftRateNotEqualTo(BigDecimal value) {
            addCriterion("chat_gift_rate <>", value, "chatGiftRate");
            return (Criteria) this;
        }

        public Criteria andChatGiftRateGreaterThan(BigDecimal value) {
            addCriterion("chat_gift_rate >", value, "chatGiftRate");
            return (Criteria) this;
        }

        public Criteria andChatGiftRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("chat_gift_rate >=", value, "chatGiftRate");
            return (Criteria) this;
        }

        public Criteria andChatGiftRateLessThan(BigDecimal value) {
            addCriterion("chat_gift_rate <", value, "chatGiftRate");
            return (Criteria) this;
        }

        public Criteria andChatGiftRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("chat_gift_rate <=", value, "chatGiftRate");
            return (Criteria) this;
        }

        public Criteria andChatGiftRateIn(List<BigDecimal> values) {
            addCriterion("chat_gift_rate in", values, "chatGiftRate");
            return (Criteria) this;
        }

        public Criteria andChatGiftRateNotIn(List<BigDecimal> values) {
            addCriterion("chat_gift_rate not in", values, "chatGiftRate");
            return (Criteria) this;
        }

        public Criteria andChatGiftRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("chat_gift_rate between", value1, value2, "chatGiftRate");
            return (Criteria) this;
        }

        public Criteria andChatGiftRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("chat_gift_rate not between", value1, value2, "chatGiftRate");
            return (Criteria) this;
        }

        public Criteria andInviteUserCntIsNull() {
            addCriterion("invite_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andInviteUserCntIsNotNull() {
            addCriterion("invite_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andInviteUserCntEqualTo(Integer value) {
            addCriterion("invite_user_cnt =", value, "inviteUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteUserCntNotEqualTo(Integer value) {
            addCriterion("invite_user_cnt <>", value, "inviteUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteUserCntGreaterThan(Integer value) {
            addCriterion("invite_user_cnt >", value, "inviteUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("invite_user_cnt >=", value, "inviteUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteUserCntLessThan(Integer value) {
            addCriterion("invite_user_cnt <", value, "inviteUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("invite_user_cnt <=", value, "inviteUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteUserCntIn(List<Integer> values) {
            addCriterion("invite_user_cnt in", values, "inviteUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteUserCntNotIn(List<Integer> values) {
            addCriterion("invite_user_cnt not in", values, "inviteUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteUserCntBetween(Integer value1, Integer value2) {
            addCriterion("invite_user_cnt between", value1, value2, "inviteUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("invite_user_cnt not between", value1, value2, "inviteUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomUserCntIsNull() {
            addCriterion("invite_enter_room_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomUserCntIsNotNull() {
            addCriterion("invite_enter_room_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomUserCntEqualTo(Integer value) {
            addCriterion("invite_enter_room_user_cnt =", value, "inviteEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomUserCntNotEqualTo(Integer value) {
            addCriterion("invite_enter_room_user_cnt <>", value, "inviteEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomUserCntGreaterThan(Integer value) {
            addCriterion("invite_enter_room_user_cnt >", value, "inviteEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("invite_enter_room_user_cnt >=", value, "inviteEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomUserCntLessThan(Integer value) {
            addCriterion("invite_enter_room_user_cnt <", value, "inviteEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("invite_enter_room_user_cnt <=", value, "inviteEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomUserCntIn(List<Integer> values) {
            addCriterion("invite_enter_room_user_cnt in", values, "inviteEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomUserCntNotIn(List<Integer> values) {
            addCriterion("invite_enter_room_user_cnt not in", values, "inviteEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomUserCntBetween(Integer value1, Integer value2) {
            addCriterion("invite_enter_room_user_cnt between", value1, value2, "inviteEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("invite_enter_room_user_cnt not between", value1, value2, "inviteEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteGiftUserCntIsNull() {
            addCriterion("invite_gift_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andInviteGiftUserCntIsNotNull() {
            addCriterion("invite_gift_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andInviteGiftUserCntEqualTo(Integer value) {
            addCriterion("invite_gift_user_cnt =", value, "inviteGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteGiftUserCntNotEqualTo(Integer value) {
            addCriterion("invite_gift_user_cnt <>", value, "inviteGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteGiftUserCntGreaterThan(Integer value) {
            addCriterion("invite_gift_user_cnt >", value, "inviteGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteGiftUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("invite_gift_user_cnt >=", value, "inviteGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteGiftUserCntLessThan(Integer value) {
            addCriterion("invite_gift_user_cnt <", value, "inviteGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteGiftUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("invite_gift_user_cnt <=", value, "inviteGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteGiftUserCntIn(List<Integer> values) {
            addCriterion("invite_gift_user_cnt in", values, "inviteGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteGiftUserCntNotIn(List<Integer> values) {
            addCriterion("invite_gift_user_cnt not in", values, "inviteGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteGiftUserCntBetween(Integer value1, Integer value2) {
            addCriterion("invite_gift_user_cnt between", value1, value2, "inviteGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteGiftUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("invite_gift_user_cnt not between", value1, value2, "inviteGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomRateIsNull() {
            addCriterion("invite_enter_room_rate is null");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomRateIsNotNull() {
            addCriterion("invite_enter_room_rate is not null");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomRateEqualTo(BigDecimal value) {
            addCriterion("invite_enter_room_rate =", value, "inviteEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomRateNotEqualTo(BigDecimal value) {
            addCriterion("invite_enter_room_rate <>", value, "inviteEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomRateGreaterThan(BigDecimal value) {
            addCriterion("invite_enter_room_rate >", value, "inviteEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("invite_enter_room_rate >=", value, "inviteEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomRateLessThan(BigDecimal value) {
            addCriterion("invite_enter_room_rate <", value, "inviteEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("invite_enter_room_rate <=", value, "inviteEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomRateIn(List<BigDecimal> values) {
            addCriterion("invite_enter_room_rate in", values, "inviteEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomRateNotIn(List<BigDecimal> values) {
            addCriterion("invite_enter_room_rate not in", values, "inviteEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("invite_enter_room_rate between", value1, value2, "inviteEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("invite_enter_room_rate not between", value1, value2, "inviteEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andInviteGiftRateIsNull() {
            addCriterion("invite_gift_rate is null");
            return (Criteria) this;
        }

        public Criteria andInviteGiftRateIsNotNull() {
            addCriterion("invite_gift_rate is not null");
            return (Criteria) this;
        }

        public Criteria andInviteGiftRateEqualTo(BigDecimal value) {
            addCriterion("invite_gift_rate =", value, "inviteGiftRate");
            return (Criteria) this;
        }

        public Criteria andInviteGiftRateNotEqualTo(BigDecimal value) {
            addCriterion("invite_gift_rate <>", value, "inviteGiftRate");
            return (Criteria) this;
        }

        public Criteria andInviteGiftRateGreaterThan(BigDecimal value) {
            addCriterion("invite_gift_rate >", value, "inviteGiftRate");
            return (Criteria) this;
        }

        public Criteria andInviteGiftRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("invite_gift_rate >=", value, "inviteGiftRate");
            return (Criteria) this;
        }

        public Criteria andInviteGiftRateLessThan(BigDecimal value) {
            addCriterion("invite_gift_rate <", value, "inviteGiftRate");
            return (Criteria) this;
        }

        public Criteria andInviteGiftRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("invite_gift_rate <=", value, "inviteGiftRate");
            return (Criteria) this;
        }

        public Criteria andInviteGiftRateIn(List<BigDecimal> values) {
            addCriterion("invite_gift_rate in", values, "inviteGiftRate");
            return (Criteria) this;
        }

        public Criteria andInviteGiftRateNotIn(List<BigDecimal> values) {
            addCriterion("invite_gift_rate not in", values, "inviteGiftRate");
            return (Criteria) this;
        }

        public Criteria andInviteGiftRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("invite_gift_rate between", value1, value2, "inviteGiftRate");
            return (Criteria) this;
        }

        public Criteria andInviteGiftRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("invite_gift_rate not between", value1, value2, "inviteGiftRate");
            return (Criteria) this;
        }

        public Criteria andFansUserCntIsNull() {
            addCriterion("fans_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andFansUserCntIsNotNull() {
            addCriterion("fans_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andFansUserCntEqualTo(Integer value) {
            addCriterion("fans_user_cnt =", value, "fansUserCnt");
            return (Criteria) this;
        }

        public Criteria andFansUserCntNotEqualTo(Integer value) {
            addCriterion("fans_user_cnt <>", value, "fansUserCnt");
            return (Criteria) this;
        }

        public Criteria andFansUserCntGreaterThan(Integer value) {
            addCriterion("fans_user_cnt >", value, "fansUserCnt");
            return (Criteria) this;
        }

        public Criteria andFansUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("fans_user_cnt >=", value, "fansUserCnt");
            return (Criteria) this;
        }

        public Criteria andFansUserCntLessThan(Integer value) {
            addCriterion("fans_user_cnt <", value, "fansUserCnt");
            return (Criteria) this;
        }

        public Criteria andFansUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("fans_user_cnt <=", value, "fansUserCnt");
            return (Criteria) this;
        }

        public Criteria andFansUserCntIn(List<Integer> values) {
            addCriterion("fans_user_cnt in", values, "fansUserCnt");
            return (Criteria) this;
        }

        public Criteria andFansUserCntNotIn(List<Integer> values) {
            addCriterion("fans_user_cnt not in", values, "fansUserCnt");
            return (Criteria) this;
        }

        public Criteria andFansUserCntBetween(Integer value1, Integer value2) {
            addCriterion("fans_user_cnt between", value1, value2, "fansUserCnt");
            return (Criteria) this;
        }

        public Criteria andFansUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("fans_user_cnt not between", value1, value2, "fansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntIsNull() {
            addCriterion("new_fans_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntIsNotNull() {
            addCriterion("new_fans_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntEqualTo(Integer value) {
            addCriterion("new_fans_user_cnt =", value, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntNotEqualTo(Integer value) {
            addCriterion("new_fans_user_cnt <>", value, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntGreaterThan(Integer value) {
            addCriterion("new_fans_user_cnt >", value, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("new_fans_user_cnt >=", value, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntLessThan(Integer value) {
            addCriterion("new_fans_user_cnt <", value, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("new_fans_user_cnt <=", value, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntIn(List<Integer> values) {
            addCriterion("new_fans_user_cnt in", values, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntNotIn(List<Integer> values) {
            addCriterion("new_fans_user_cnt not in", values, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntBetween(Integer value1, Integer value2) {
            addCriterion("new_fans_user_cnt between", value1, value2, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("new_fans_user_cnt not between", value1, value2, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerFansUserCntIsNull() {
            addCriterion("player_fans_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andPlayerFansUserCntIsNotNull() {
            addCriterion("player_fans_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andPlayerFansUserCntEqualTo(Integer value) {
            addCriterion("player_fans_user_cnt =", value, "playerFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerFansUserCntNotEqualTo(Integer value) {
            addCriterion("player_fans_user_cnt <>", value, "playerFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerFansUserCntGreaterThan(Integer value) {
            addCriterion("player_fans_user_cnt >", value, "playerFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerFansUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("player_fans_user_cnt >=", value, "playerFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerFansUserCntLessThan(Integer value) {
            addCriterion("player_fans_user_cnt <", value, "playerFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerFansUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("player_fans_user_cnt <=", value, "playerFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerFansUserCntIn(List<Integer> values) {
            addCriterion("player_fans_user_cnt in", values, "playerFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerFansUserCntNotIn(List<Integer> values) {
            addCriterion("player_fans_user_cnt not in", values, "playerFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerFansUserCntBetween(Integer value1, Integer value2) {
            addCriterion("player_fans_user_cnt between", value1, value2, "playerFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerFansUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("player_fans_user_cnt not between", value1, value2, "playerFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerNewFansUserCntIsNull() {
            addCriterion("player_new_fans_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andPlayerNewFansUserCntIsNotNull() {
            addCriterion("player_new_fans_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andPlayerNewFansUserCntEqualTo(Integer value) {
            addCriterion("player_new_fans_user_cnt =", value, "playerNewFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerNewFansUserCntNotEqualTo(Integer value) {
            addCriterion("player_new_fans_user_cnt <>", value, "playerNewFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerNewFansUserCntGreaterThan(Integer value) {
            addCriterion("player_new_fans_user_cnt >", value, "playerNewFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerNewFansUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("player_new_fans_user_cnt >=", value, "playerNewFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerNewFansUserCntLessThan(Integer value) {
            addCriterion("player_new_fans_user_cnt <", value, "playerNewFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerNewFansUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("player_new_fans_user_cnt <=", value, "playerNewFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerNewFansUserCntIn(List<Integer> values) {
            addCriterion("player_new_fans_user_cnt in", values, "playerNewFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerNewFansUserCntNotIn(List<Integer> values) {
            addCriterion("player_new_fans_user_cnt not in", values, "playerNewFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerNewFansUserCntBetween(Integer value1, Integer value2) {
            addCriterion("player_new_fans_user_cnt between", value1, value2, "playerNewFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerNewFansUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("player_new_fans_user_cnt not between", value1, value2, "playerNewFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerFansEnterUserCntIsNull() {
            addCriterion("player_fans_enter_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andPlayerFansEnterUserCntIsNotNull() {
            addCriterion("player_fans_enter_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andPlayerFansEnterUserCntEqualTo(Integer value) {
            addCriterion("player_fans_enter_user_cnt =", value, "playerFansEnterUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerFansEnterUserCntNotEqualTo(Integer value) {
            addCriterion("player_fans_enter_user_cnt <>", value, "playerFansEnterUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerFansEnterUserCntGreaterThan(Integer value) {
            addCriterion("player_fans_enter_user_cnt >", value, "playerFansEnterUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerFansEnterUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("player_fans_enter_user_cnt >=", value, "playerFansEnterUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerFansEnterUserCntLessThan(Integer value) {
            addCriterion("player_fans_enter_user_cnt <", value, "playerFansEnterUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerFansEnterUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("player_fans_enter_user_cnt <=", value, "playerFansEnterUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerFansEnterUserCntIn(List<Integer> values) {
            addCriterion("player_fans_enter_user_cnt in", values, "playerFansEnterUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerFansEnterUserCntNotIn(List<Integer> values) {
            addCriterion("player_fans_enter_user_cnt not in", values, "playerFansEnterUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerFansEnterUserCntBetween(Integer value1, Integer value2) {
            addCriterion("player_fans_enter_user_cnt between", value1, value2, "playerFansEnterUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerFansEnterUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("player_fans_enter_user_cnt not between", value1, value2, "playerFansEnterUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftIncomeIsNull() {
            addCriterion("player_fans_gift_income is null");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftIncomeIsNotNull() {
            addCriterion("player_fans_gift_income is not null");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftIncomeEqualTo(BigDecimal value) {
            addCriterion("player_fans_gift_income =", value, "playerFansGiftIncome");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftIncomeNotEqualTo(BigDecimal value) {
            addCriterion("player_fans_gift_income <>", value, "playerFansGiftIncome");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftIncomeGreaterThan(BigDecimal value) {
            addCriterion("player_fans_gift_income >", value, "playerFansGiftIncome");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("player_fans_gift_income >=", value, "playerFansGiftIncome");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftIncomeLessThan(BigDecimal value) {
            addCriterion("player_fans_gift_income <", value, "playerFansGiftIncome");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("player_fans_gift_income <=", value, "playerFansGiftIncome");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftIncomeIn(List<BigDecimal> values) {
            addCriterion("player_fans_gift_income in", values, "playerFansGiftIncome");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftIncomeNotIn(List<BigDecimal> values) {
            addCriterion("player_fans_gift_income not in", values, "playerFansGiftIncome");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("player_fans_gift_income between", value1, value2, "playerFansGiftIncome");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("player_fans_gift_income not between", value1, value2, "playerFansGiftIncome");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftUserCntIsNull() {
            addCriterion("player_fans_gift_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftUserCntIsNotNull() {
            addCriterion("player_fans_gift_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftUserCntEqualTo(Integer value) {
            addCriterion("player_fans_gift_user_cnt =", value, "playerFansGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftUserCntNotEqualTo(Integer value) {
            addCriterion("player_fans_gift_user_cnt <>", value, "playerFansGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftUserCntGreaterThan(Integer value) {
            addCriterion("player_fans_gift_user_cnt >", value, "playerFansGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("player_fans_gift_user_cnt >=", value, "playerFansGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftUserCntLessThan(Integer value) {
            addCriterion("player_fans_gift_user_cnt <", value, "playerFansGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("player_fans_gift_user_cnt <=", value, "playerFansGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftUserCntIn(List<Integer> values) {
            addCriterion("player_fans_gift_user_cnt in", values, "playerFansGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftUserCntNotIn(List<Integer> values) {
            addCriterion("player_fans_gift_user_cnt not in", values, "playerFansGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftUserCntBetween(Integer value1, Integer value2) {
            addCriterion("player_fans_gift_user_cnt between", value1, value2, "playerFansGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("player_fans_gift_user_cnt not between", value1, value2, "playerFansGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftUserPriceIsNull() {
            addCriterion("player_fans_gift_user_price is null");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftUserPriceIsNotNull() {
            addCriterion("player_fans_gift_user_price is not null");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftUserPriceEqualTo(BigDecimal value) {
            addCriterion("player_fans_gift_user_price =", value, "playerFansGiftUserPrice");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftUserPriceNotEqualTo(BigDecimal value) {
            addCriterion("player_fans_gift_user_price <>", value, "playerFansGiftUserPrice");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftUserPriceGreaterThan(BigDecimal value) {
            addCriterion("player_fans_gift_user_price >", value, "playerFansGiftUserPrice");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftUserPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("player_fans_gift_user_price >=", value, "playerFansGiftUserPrice");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftUserPriceLessThan(BigDecimal value) {
            addCriterion("player_fans_gift_user_price <", value, "playerFansGiftUserPrice");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftUserPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("player_fans_gift_user_price <=", value, "playerFansGiftUserPrice");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftUserPriceIn(List<BigDecimal> values) {
            addCriterion("player_fans_gift_user_price in", values, "playerFansGiftUserPrice");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftUserPriceNotIn(List<BigDecimal> values) {
            addCriterion("player_fans_gift_user_price not in", values, "playerFansGiftUserPrice");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftUserPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("player_fans_gift_user_price between", value1, value2, "playerFansGiftUserPrice");
            return (Criteria) this;
        }

        public Criteria andPlayerFansGiftUserPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("player_fans_gift_user_price not between", value1, value2, "playerFansGiftUserPrice");
            return (Criteria) this;
        }

        public Criteria andEnterRoomUserCntIsNull() {
            addCriterion("enter_room_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andEnterRoomUserCntIsNotNull() {
            addCriterion("enter_room_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andEnterRoomUserCntEqualTo(Integer value) {
            addCriterion("enter_room_user_cnt =", value, "enterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andEnterRoomUserCntNotEqualTo(Integer value) {
            addCriterion("enter_room_user_cnt <>", value, "enterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andEnterRoomUserCntGreaterThan(Integer value) {
            addCriterion("enter_room_user_cnt >", value, "enterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andEnterRoomUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("enter_room_user_cnt >=", value, "enterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andEnterRoomUserCntLessThan(Integer value) {
            addCriterion("enter_room_user_cnt <", value, "enterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andEnterRoomUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("enter_room_user_cnt <=", value, "enterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andEnterRoomUserCntIn(List<Integer> values) {
            addCriterion("enter_room_user_cnt in", values, "enterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andEnterRoomUserCntNotIn(List<Integer> values) {
            addCriterion("enter_room_user_cnt not in", values, "enterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andEnterRoomUserCntBetween(Integer value1, Integer value2) {
            addCriterion("enter_room_user_cnt between", value1, value2, "enterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andEnterRoomUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("enter_room_user_cnt not between", value1, value2, "enterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andUpGuestPlayerCntIsNull() {
            addCriterion("up_guest_player_cnt is null");
            return (Criteria) this;
        }

        public Criteria andUpGuestPlayerCntIsNotNull() {
            addCriterion("up_guest_player_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andUpGuestPlayerCntEqualTo(Integer value) {
            addCriterion("up_guest_player_cnt =", value, "upGuestPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andUpGuestPlayerCntNotEqualTo(Integer value) {
            addCriterion("up_guest_player_cnt <>", value, "upGuestPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andUpGuestPlayerCntGreaterThan(Integer value) {
            addCriterion("up_guest_player_cnt >", value, "upGuestPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andUpGuestPlayerCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("up_guest_player_cnt >=", value, "upGuestPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andUpGuestPlayerCntLessThan(Integer value) {
            addCriterion("up_guest_player_cnt <", value, "upGuestPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andUpGuestPlayerCntLessThanOrEqualTo(Integer value) {
            addCriterion("up_guest_player_cnt <=", value, "upGuestPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andUpGuestPlayerCntIn(List<Integer> values) {
            addCriterion("up_guest_player_cnt in", values, "upGuestPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andUpGuestPlayerCntNotIn(List<Integer> values) {
            addCriterion("up_guest_player_cnt not in", values, "upGuestPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andUpGuestPlayerCntBetween(Integer value1, Integer value2) {
            addCriterion("up_guest_player_cnt between", value1, value2, "upGuestPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andUpGuestPlayerCntNotBetween(Integer value1, Integer value2) {
            addCriterion("up_guest_player_cnt not between", value1, value2, "upGuestPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andSignUpGuestPlayerCntIsNull() {
            addCriterion("sign_up_guest_player_cnt is null");
            return (Criteria) this;
        }

        public Criteria andSignUpGuestPlayerCntIsNotNull() {
            addCriterion("sign_up_guest_player_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andSignUpGuestPlayerCntEqualTo(Integer value) {
            addCriterion("sign_up_guest_player_cnt =", value, "signUpGuestPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andSignUpGuestPlayerCntNotEqualTo(Integer value) {
            addCriterion("sign_up_guest_player_cnt <>", value, "signUpGuestPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andSignUpGuestPlayerCntGreaterThan(Integer value) {
            addCriterion("sign_up_guest_player_cnt >", value, "signUpGuestPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andSignUpGuestPlayerCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("sign_up_guest_player_cnt >=", value, "signUpGuestPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andSignUpGuestPlayerCntLessThan(Integer value) {
            addCriterion("sign_up_guest_player_cnt <", value, "signUpGuestPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andSignUpGuestPlayerCntLessThanOrEqualTo(Integer value) {
            addCriterion("sign_up_guest_player_cnt <=", value, "signUpGuestPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andSignUpGuestPlayerCntIn(List<Integer> values) {
            addCriterion("sign_up_guest_player_cnt in", values, "signUpGuestPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andSignUpGuestPlayerCntNotIn(List<Integer> values) {
            addCriterion("sign_up_guest_player_cnt not in", values, "signUpGuestPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andSignUpGuestPlayerCntBetween(Integer value1, Integer value2) {
            addCriterion("sign_up_guest_player_cnt between", value1, value2, "signUpGuestPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andSignUpGuestPlayerCntNotBetween(Integer value1, Integer value2) {
            addCriterion("sign_up_guest_player_cnt not between", value1, value2, "signUpGuestPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andCommentUserCntIsNull() {
            addCriterion("comment_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andCommentUserCntIsNotNull() {
            addCriterion("comment_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andCommentUserCntEqualTo(Integer value) {
            addCriterion("comment_user_cnt =", value, "commentUserCnt");
            return (Criteria) this;
        }

        public Criteria andCommentUserCntNotEqualTo(Integer value) {
            addCriterion("comment_user_cnt <>", value, "commentUserCnt");
            return (Criteria) this;
        }

        public Criteria andCommentUserCntGreaterThan(Integer value) {
            addCriterion("comment_user_cnt >", value, "commentUserCnt");
            return (Criteria) this;
        }

        public Criteria andCommentUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("comment_user_cnt >=", value, "commentUserCnt");
            return (Criteria) this;
        }

        public Criteria andCommentUserCntLessThan(Integer value) {
            addCriterion("comment_user_cnt <", value, "commentUserCnt");
            return (Criteria) this;
        }

        public Criteria andCommentUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("comment_user_cnt <=", value, "commentUserCnt");
            return (Criteria) this;
        }

        public Criteria andCommentUserCntIn(List<Integer> values) {
            addCriterion("comment_user_cnt in", values, "commentUserCnt");
            return (Criteria) this;
        }

        public Criteria andCommentUserCntNotIn(List<Integer> values) {
            addCriterion("comment_user_cnt not in", values, "commentUserCnt");
            return (Criteria) this;
        }

        public Criteria andCommentUserCntBetween(Integer value1, Integer value2) {
            addCriterion("comment_user_cnt between", value1, value2, "commentUserCnt");
            return (Criteria) this;
        }

        public Criteria andCommentUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("comment_user_cnt not between", value1, value2, "commentUserCnt");
            return (Criteria) this;
        }

        public Criteria andUpGuestRateIsNull() {
            addCriterion("up_guest_rate is null");
            return (Criteria) this;
        }

        public Criteria andUpGuestRateIsNotNull() {
            addCriterion("up_guest_rate is not null");
            return (Criteria) this;
        }

        public Criteria andUpGuestRateEqualTo(BigDecimal value) {
            addCriterion("up_guest_rate =", value, "upGuestRate");
            return (Criteria) this;
        }

        public Criteria andUpGuestRateNotEqualTo(BigDecimal value) {
            addCriterion("up_guest_rate <>", value, "upGuestRate");
            return (Criteria) this;
        }

        public Criteria andUpGuestRateGreaterThan(BigDecimal value) {
            addCriterion("up_guest_rate >", value, "upGuestRate");
            return (Criteria) this;
        }

        public Criteria andUpGuestRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("up_guest_rate >=", value, "upGuestRate");
            return (Criteria) this;
        }

        public Criteria andUpGuestRateLessThan(BigDecimal value) {
            addCriterion("up_guest_rate <", value, "upGuestRate");
            return (Criteria) this;
        }

        public Criteria andUpGuestRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("up_guest_rate <=", value, "upGuestRate");
            return (Criteria) this;
        }

        public Criteria andUpGuestRateIn(List<BigDecimal> values) {
            addCriterion("up_guest_rate in", values, "upGuestRate");
            return (Criteria) this;
        }

        public Criteria andUpGuestRateNotIn(List<BigDecimal> values) {
            addCriterion("up_guest_rate not in", values, "upGuestRate");
            return (Criteria) this;
        }

        public Criteria andUpGuestRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("up_guest_rate between", value1, value2, "upGuestRate");
            return (Criteria) this;
        }

        public Criteria andUpGuestRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("up_guest_rate not between", value1, value2, "upGuestRate");
            return (Criteria) this;
        }

        public Criteria andCommentRateIsNull() {
            addCriterion("comment_rate is null");
            return (Criteria) this;
        }

        public Criteria andCommentRateIsNotNull() {
            addCriterion("comment_rate is not null");
            return (Criteria) this;
        }

        public Criteria andCommentRateEqualTo(BigDecimal value) {
            addCriterion("comment_rate =", value, "commentRate");
            return (Criteria) this;
        }

        public Criteria andCommentRateNotEqualTo(BigDecimal value) {
            addCriterion("comment_rate <>", value, "commentRate");
            return (Criteria) this;
        }

        public Criteria andCommentRateGreaterThan(BigDecimal value) {
            addCriterion("comment_rate >", value, "commentRate");
            return (Criteria) this;
        }

        public Criteria andCommentRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("comment_rate >=", value, "commentRate");
            return (Criteria) this;
        }

        public Criteria andCommentRateLessThan(BigDecimal value) {
            addCriterion("comment_rate <", value, "commentRate");
            return (Criteria) this;
        }

        public Criteria andCommentRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("comment_rate <=", value, "commentRate");
            return (Criteria) this;
        }

        public Criteria andCommentRateIn(List<BigDecimal> values) {
            addCriterion("comment_rate in", values, "commentRate");
            return (Criteria) this;
        }

        public Criteria andCommentRateNotIn(List<BigDecimal> values) {
            addCriterion("comment_rate not in", values, "commentRate");
            return (Criteria) this;
        }

        public Criteria andCommentRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("comment_rate between", value1, value2, "commentRate");
            return (Criteria) this;
        }

        public Criteria andCommentRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("comment_rate not between", value1, value2, "commentRate");
            return (Criteria) this;
        }

        public Criteria andAvgUserStayDurationIsNull() {
            addCriterion("avg_user_stay_duration is null");
            return (Criteria) this;
        }

        public Criteria andAvgUserStayDurationIsNotNull() {
            addCriterion("avg_user_stay_duration is not null");
            return (Criteria) this;
        }

        public Criteria andAvgUserStayDurationEqualTo(BigDecimal value) {
            addCriterion("avg_user_stay_duration =", value, "avgUserStayDuration");
            return (Criteria) this;
        }

        public Criteria andAvgUserStayDurationNotEqualTo(BigDecimal value) {
            addCriterion("avg_user_stay_duration <>", value, "avgUserStayDuration");
            return (Criteria) this;
        }

        public Criteria andAvgUserStayDurationGreaterThan(BigDecimal value) {
            addCriterion("avg_user_stay_duration >", value, "avgUserStayDuration");
            return (Criteria) this;
        }

        public Criteria andAvgUserStayDurationGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("avg_user_stay_duration >=", value, "avgUserStayDuration");
            return (Criteria) this;
        }

        public Criteria andAvgUserStayDurationLessThan(BigDecimal value) {
            addCriterion("avg_user_stay_duration <", value, "avgUserStayDuration");
            return (Criteria) this;
        }

        public Criteria andAvgUserStayDurationLessThanOrEqualTo(BigDecimal value) {
            addCriterion("avg_user_stay_duration <=", value, "avgUserStayDuration");
            return (Criteria) this;
        }

        public Criteria andAvgUserStayDurationIn(List<BigDecimal> values) {
            addCriterion("avg_user_stay_duration in", values, "avgUserStayDuration");
            return (Criteria) this;
        }

        public Criteria andAvgUserStayDurationNotIn(List<BigDecimal> values) {
            addCriterion("avg_user_stay_duration not in", values, "avgUserStayDuration");
            return (Criteria) this;
        }

        public Criteria andAvgUserStayDurationBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("avg_user_stay_duration between", value1, value2, "avgUserStayDuration");
            return (Criteria) this;
        }

        public Criteria andAvgUserStayDurationNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("avg_user_stay_duration not between", value1, value2, "avgUserStayDuration");
            return (Criteria) this;
        }

        public Criteria andUserFullOneMinIsNull() {
            addCriterion("user_full_one_min is null");
            return (Criteria) this;
        }

        public Criteria andUserFullOneMinIsNotNull() {
            addCriterion("user_full_one_min is not null");
            return (Criteria) this;
        }

        public Criteria andUserFullOneMinEqualTo(BigDecimal value) {
            addCriterion("user_full_one_min =", value, "userFullOneMin");
            return (Criteria) this;
        }

        public Criteria andUserFullOneMinNotEqualTo(BigDecimal value) {
            addCriterion("user_full_one_min <>", value, "userFullOneMin");
            return (Criteria) this;
        }

        public Criteria andUserFullOneMinGreaterThan(BigDecimal value) {
            addCriterion("user_full_one_min >", value, "userFullOneMin");
            return (Criteria) this;
        }

        public Criteria andUserFullOneMinGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("user_full_one_min >=", value, "userFullOneMin");
            return (Criteria) this;
        }

        public Criteria andUserFullOneMinLessThan(BigDecimal value) {
            addCriterion("user_full_one_min <", value, "userFullOneMin");
            return (Criteria) this;
        }

        public Criteria andUserFullOneMinLessThanOrEqualTo(BigDecimal value) {
            addCriterion("user_full_one_min <=", value, "userFullOneMin");
            return (Criteria) this;
        }

        public Criteria andUserFullOneMinIn(List<BigDecimal> values) {
            addCriterion("user_full_one_min in", values, "userFullOneMin");
            return (Criteria) this;
        }

        public Criteria andUserFullOneMinNotIn(List<BigDecimal> values) {
            addCriterion("user_full_one_min not in", values, "userFullOneMin");
            return (Criteria) this;
        }

        public Criteria andUserFullOneMinBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("user_full_one_min between", value1, value2, "userFullOneMin");
            return (Criteria) this;
        }

        public Criteria andUserFullOneMinNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("user_full_one_min not between", value1, value2, "userFullOneMin");
            return (Criteria) this;
        }

        public Criteria andUserFullThreeMinIsNull() {
            addCriterion("user_full_three_min is null");
            return (Criteria) this;
        }

        public Criteria andUserFullThreeMinIsNotNull() {
            addCriterion("user_full_three_min is not null");
            return (Criteria) this;
        }

        public Criteria andUserFullThreeMinEqualTo(BigDecimal value) {
            addCriterion("user_full_three_min =", value, "userFullThreeMin");
            return (Criteria) this;
        }

        public Criteria andUserFullThreeMinNotEqualTo(BigDecimal value) {
            addCriterion("user_full_three_min <>", value, "userFullThreeMin");
            return (Criteria) this;
        }

        public Criteria andUserFullThreeMinGreaterThan(BigDecimal value) {
            addCriterion("user_full_three_min >", value, "userFullThreeMin");
            return (Criteria) this;
        }

        public Criteria andUserFullThreeMinGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("user_full_three_min >=", value, "userFullThreeMin");
            return (Criteria) this;
        }

        public Criteria andUserFullThreeMinLessThan(BigDecimal value) {
            addCriterion("user_full_three_min <", value, "userFullThreeMin");
            return (Criteria) this;
        }

        public Criteria andUserFullThreeMinLessThanOrEqualTo(BigDecimal value) {
            addCriterion("user_full_three_min <=", value, "userFullThreeMin");
            return (Criteria) this;
        }

        public Criteria andUserFullThreeMinIn(List<BigDecimal> values) {
            addCriterion("user_full_three_min in", values, "userFullThreeMin");
            return (Criteria) this;
        }

        public Criteria andUserFullThreeMinNotIn(List<BigDecimal> values) {
            addCriterion("user_full_three_min not in", values, "userFullThreeMin");
            return (Criteria) this;
        }

        public Criteria andUserFullThreeMinBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("user_full_three_min between", value1, value2, "userFullThreeMin");
            return (Criteria) this;
        }

        public Criteria andUserFullThreeMinNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("user_full_three_min not between", value1, value2, "userFullThreeMin");
            return (Criteria) this;
        }

        public Criteria andUserFullFiveMinIsNull() {
            addCriterion("user_full_five_min is null");
            return (Criteria) this;
        }

        public Criteria andUserFullFiveMinIsNotNull() {
            addCriterion("user_full_five_min is not null");
            return (Criteria) this;
        }

        public Criteria andUserFullFiveMinEqualTo(BigDecimal value) {
            addCriterion("user_full_five_min =", value, "userFullFiveMin");
            return (Criteria) this;
        }

        public Criteria andUserFullFiveMinNotEqualTo(BigDecimal value) {
            addCriterion("user_full_five_min <>", value, "userFullFiveMin");
            return (Criteria) this;
        }

        public Criteria andUserFullFiveMinGreaterThan(BigDecimal value) {
            addCriterion("user_full_five_min >", value, "userFullFiveMin");
            return (Criteria) this;
        }

        public Criteria andUserFullFiveMinGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("user_full_five_min >=", value, "userFullFiveMin");
            return (Criteria) this;
        }

        public Criteria andUserFullFiveMinLessThan(BigDecimal value) {
            addCriterion("user_full_five_min <", value, "userFullFiveMin");
            return (Criteria) this;
        }

        public Criteria andUserFullFiveMinLessThanOrEqualTo(BigDecimal value) {
            addCriterion("user_full_five_min <=", value, "userFullFiveMin");
            return (Criteria) this;
        }

        public Criteria andUserFullFiveMinIn(List<BigDecimal> values) {
            addCriterion("user_full_five_min in", values, "userFullFiveMin");
            return (Criteria) this;
        }

        public Criteria andUserFullFiveMinNotIn(List<BigDecimal> values) {
            addCriterion("user_full_five_min not in", values, "userFullFiveMin");
            return (Criteria) this;
        }

        public Criteria andUserFullFiveMinBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("user_full_five_min between", value1, value2, "userFullFiveMin");
            return (Criteria) this;
        }

        public Criteria andUserFullFiveMinNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("user_full_five_min not between", value1, value2, "userFullFiveMin");
            return (Criteria) this;
        }

        public Criteria andIncomeRankIsNull() {
            addCriterion("income_rank is null");
            return (Criteria) this;
        }

        public Criteria andIncomeRankIsNotNull() {
            addCriterion("income_rank is not null");
            return (Criteria) this;
        }

        public Criteria andIncomeRankEqualTo(Integer value) {
            addCriterion("income_rank =", value, "incomeRank");
            return (Criteria) this;
        }

        public Criteria andIncomeRankNotEqualTo(Integer value) {
            addCriterion("income_rank <>", value, "incomeRank");
            return (Criteria) this;
        }

        public Criteria andIncomeRankGreaterThan(Integer value) {
            addCriterion("income_rank >", value, "incomeRank");
            return (Criteria) this;
        }

        public Criteria andIncomeRankGreaterThanOrEqualTo(Integer value) {
            addCriterion("income_rank >=", value, "incomeRank");
            return (Criteria) this;
        }

        public Criteria andIncomeRankLessThan(Integer value) {
            addCriterion("income_rank <", value, "incomeRank");
            return (Criteria) this;
        }

        public Criteria andIncomeRankLessThanOrEqualTo(Integer value) {
            addCriterion("income_rank <=", value, "incomeRank");
            return (Criteria) this;
        }

        public Criteria andIncomeRankIn(List<Integer> values) {
            addCriterion("income_rank in", values, "incomeRank");
            return (Criteria) this;
        }

        public Criteria andIncomeRankNotIn(List<Integer> values) {
            addCriterion("income_rank not in", values, "incomeRank");
            return (Criteria) this;
        }

        public Criteria andIncomeRankBetween(Integer value1, Integer value2) {
            addCriterion("income_rank between", value1, value2, "incomeRank");
            return (Criteria) this;
        }

        public Criteria andIncomeRankNotBetween(Integer value1, Integer value2) {
            addCriterion("income_rank not between", value1, value2, "incomeRank");
            return (Criteria) this;
        }

        public Criteria andCharmRankIsNull() {
            addCriterion("charm_rank is null");
            return (Criteria) this;
        }

        public Criteria andCharmRankIsNotNull() {
            addCriterion("charm_rank is not null");
            return (Criteria) this;
        }

        public Criteria andCharmRankEqualTo(Integer value) {
            addCriterion("charm_rank =", value, "charmRank");
            return (Criteria) this;
        }

        public Criteria andCharmRankNotEqualTo(Integer value) {
            addCriterion("charm_rank <>", value, "charmRank");
            return (Criteria) this;
        }

        public Criteria andCharmRankGreaterThan(Integer value) {
            addCriterion("charm_rank >", value, "charmRank");
            return (Criteria) this;
        }

        public Criteria andCharmRankGreaterThanOrEqualTo(Integer value) {
            addCriterion("charm_rank >=", value, "charmRank");
            return (Criteria) this;
        }

        public Criteria andCharmRankLessThan(Integer value) {
            addCriterion("charm_rank <", value, "charmRank");
            return (Criteria) this;
        }

        public Criteria andCharmRankLessThanOrEqualTo(Integer value) {
            addCriterion("charm_rank <=", value, "charmRank");
            return (Criteria) this;
        }

        public Criteria andCharmRankIn(List<Integer> values) {
            addCriterion("charm_rank in", values, "charmRank");
            return (Criteria) this;
        }

        public Criteria andCharmRankNotIn(List<Integer> values) {
            addCriterion("charm_rank not in", values, "charmRank");
            return (Criteria) this;
        }

        public Criteria andCharmRankBetween(Integer value1, Integer value2) {
            addCriterion("charm_rank between", value1, value2, "charmRank");
            return (Criteria) this;
        }

        public Criteria andCharmRankNotBetween(Integer value1, Integer value2) {
            addCriterion("charm_rank not between", value1, value2, "charmRank");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andAllIncomeIsNull() {
            addCriterion("all_income is null");
            return (Criteria) this;
        }

        public Criteria andAllIncomeIsNotNull() {
            addCriterion("all_income is not null");
            return (Criteria) this;
        }

        public Criteria andAllIncomeEqualTo(BigDecimal value) {
            addCriterion("all_income =", value, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeNotEqualTo(BigDecimal value) {
            addCriterion("all_income <>", value, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeGreaterThan(BigDecimal value) {
            addCriterion("all_income >", value, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("all_income >=", value, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeLessThan(BigDecimal value) {
            addCriterion("all_income <", value, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("all_income <=", value, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeIn(List<BigDecimal> values) {
            addCriterion("all_income in", values, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeNotIn(List<BigDecimal> values) {
            addCriterion("all_income not in", values, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("all_income between", value1, value2, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("all_income not between", value1, value2, "allIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeIsNull() {
            addCriterion("sign_hall_income is null");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeIsNotNull() {
            addCriterion("sign_hall_income is not null");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeEqualTo(BigDecimal value) {
            addCriterion("sign_hall_income =", value, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeNotEqualTo(BigDecimal value) {
            addCriterion("sign_hall_income <>", value, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeGreaterThan(BigDecimal value) {
            addCriterion("sign_hall_income >", value, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("sign_hall_income >=", value, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeLessThan(BigDecimal value) {
            addCriterion("sign_hall_income <", value, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("sign_hall_income <=", value, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeIn(List<BigDecimal> values) {
            addCriterion("sign_hall_income in", values, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeNotIn(List<BigDecimal> values) {
            addCriterion("sign_hall_income not in", values, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sign_hall_income between", value1, value2, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sign_hall_income not between", value1, value2, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeIsNull() {
            addCriterion("official_hall_income is null");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeIsNotNull() {
            addCriterion("official_hall_income is not null");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeEqualTo(BigDecimal value) {
            addCriterion("official_hall_income =", value, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeNotEqualTo(BigDecimal value) {
            addCriterion("official_hall_income <>", value, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeGreaterThan(BigDecimal value) {
            addCriterion("official_hall_income >", value, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("official_hall_income >=", value, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeLessThan(BigDecimal value) {
            addCriterion("official_hall_income <", value, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("official_hall_income <=", value, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeIn(List<BigDecimal> values) {
            addCriterion("official_hall_income in", values, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeNotIn(List<BigDecimal> values) {
            addCriterion("official_hall_income not in", values, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("official_hall_income between", value1, value2, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("official_hall_income not between", value1, value2, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeIsNull() {
            addCriterion("personal_hall_income is null");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeIsNotNull() {
            addCriterion("personal_hall_income is not null");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeEqualTo(BigDecimal value) {
            addCriterion("personal_hall_income =", value, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeNotEqualTo(BigDecimal value) {
            addCriterion("personal_hall_income <>", value, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeGreaterThan(BigDecimal value) {
            addCriterion("personal_hall_income >", value, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("personal_hall_income >=", value, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeLessThan(BigDecimal value) {
            addCriterion("personal_hall_income <", value, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("personal_hall_income <=", value, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeIn(List<BigDecimal> values) {
            addCriterion("personal_hall_income in", values, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeNotIn(List<BigDecimal> values) {
            addCriterion("personal_hall_income not in", values, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("personal_hall_income between", value1, value2, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("personal_hall_income not between", value1, value2, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeIsNull() {
            addCriterion("noble_income is null");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeIsNotNull() {
            addCriterion("noble_income is not null");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeEqualTo(BigDecimal value) {
            addCriterion("noble_income =", value, "nobleIncome");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeNotEqualTo(BigDecimal value) {
            addCriterion("noble_income <>", value, "nobleIncome");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeGreaterThan(BigDecimal value) {
            addCriterion("noble_income >", value, "nobleIncome");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("noble_income >=", value, "nobleIncome");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeLessThan(BigDecimal value) {
            addCriterion("noble_income <", value, "nobleIncome");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("noble_income <=", value, "nobleIncome");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeIn(List<BigDecimal> values) {
            addCriterion("noble_income in", values, "nobleIncome");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeNotIn(List<BigDecimal> values) {
            addCriterion("noble_income not in", values, "nobleIncome");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("noble_income between", value1, value2, "nobleIncome");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("noble_income not between", value1, value2, "nobleIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeIsNull() {
            addCriterion("personal_noble_income is null");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeIsNotNull() {
            addCriterion("personal_noble_income is not null");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeEqualTo(BigDecimal value) {
            addCriterion("personal_noble_income =", value, "personalNobleIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeNotEqualTo(BigDecimal value) {
            addCriterion("personal_noble_income <>", value, "personalNobleIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeGreaterThan(BigDecimal value) {
            addCriterion("personal_noble_income >", value, "personalNobleIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("personal_noble_income >=", value, "personalNobleIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeLessThan(BigDecimal value) {
            addCriterion("personal_noble_income <", value, "personalNobleIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("personal_noble_income <=", value, "personalNobleIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeIn(List<BigDecimal> values) {
            addCriterion("personal_noble_income in", values, "personalNobleIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeNotIn(List<BigDecimal> values) {
            addCriterion("personal_noble_income not in", values, "personalNobleIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("personal_noble_income between", value1, value2, "personalNobleIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("personal_noble_income not between", value1, value2, "personalNobleIncome");
            return (Criteria) this;
        }

        public Criteria andUpPlayerRateIsNull() {
            addCriterion("up_player_rate is null");
            return (Criteria) this;
        }

        public Criteria andUpPlayerRateIsNotNull() {
            addCriterion("up_player_rate is not null");
            return (Criteria) this;
        }

        public Criteria andUpPlayerRateEqualTo(BigDecimal value) {
            addCriterion("up_player_rate =", value, "upPlayerRate");
            return (Criteria) this;
        }

        public Criteria andUpPlayerRateNotEqualTo(BigDecimal value) {
            addCriterion("up_player_rate <>", value, "upPlayerRate");
            return (Criteria) this;
        }

        public Criteria andUpPlayerRateGreaterThan(BigDecimal value) {
            addCriterion("up_player_rate >", value, "upPlayerRate");
            return (Criteria) this;
        }

        public Criteria andUpPlayerRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("up_player_rate >=", value, "upPlayerRate");
            return (Criteria) this;
        }

        public Criteria andUpPlayerRateLessThan(BigDecimal value) {
            addCriterion("up_player_rate <", value, "upPlayerRate");
            return (Criteria) this;
        }

        public Criteria andUpPlayerRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("up_player_rate <=", value, "upPlayerRate");
            return (Criteria) this;
        }

        public Criteria andUpPlayerRateIn(List<BigDecimal> values) {
            addCriterion("up_player_rate in", values, "upPlayerRate");
            return (Criteria) this;
        }

        public Criteria andUpPlayerRateNotIn(List<BigDecimal> values) {
            addCriterion("up_player_rate not in", values, "upPlayerRate");
            return (Criteria) this;
        }

        public Criteria andUpPlayerRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("up_player_rate between", value1, value2, "upPlayerRate");
            return (Criteria) this;
        }

        public Criteria andUpPlayerRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("up_player_rate not between", value1, value2, "upPlayerRate");
            return (Criteria) this;
        }

        public Criteria andCommentOneMinRateIsNull() {
            addCriterion("comment_one_min_rate is null");
            return (Criteria) this;
        }

        public Criteria andCommentOneMinRateIsNotNull() {
            addCriterion("comment_one_min_rate is not null");
            return (Criteria) this;
        }

        public Criteria andCommentOneMinRateEqualTo(BigDecimal value) {
            addCriterion("comment_one_min_rate =", value, "commentOneMinRate");
            return (Criteria) this;
        }

        public Criteria andCommentOneMinRateNotEqualTo(BigDecimal value) {
            addCriterion("comment_one_min_rate <>", value, "commentOneMinRate");
            return (Criteria) this;
        }

        public Criteria andCommentOneMinRateGreaterThan(BigDecimal value) {
            addCriterion("comment_one_min_rate >", value, "commentOneMinRate");
            return (Criteria) this;
        }

        public Criteria andCommentOneMinRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("comment_one_min_rate >=", value, "commentOneMinRate");
            return (Criteria) this;
        }

        public Criteria andCommentOneMinRateLessThan(BigDecimal value) {
            addCriterion("comment_one_min_rate <", value, "commentOneMinRate");
            return (Criteria) this;
        }

        public Criteria andCommentOneMinRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("comment_one_min_rate <=", value, "commentOneMinRate");
            return (Criteria) this;
        }

        public Criteria andCommentOneMinRateIn(List<BigDecimal> values) {
            addCriterion("comment_one_min_rate in", values, "commentOneMinRate");
            return (Criteria) this;
        }

        public Criteria andCommentOneMinRateNotIn(List<BigDecimal> values) {
            addCriterion("comment_one_min_rate not in", values, "commentOneMinRate");
            return (Criteria) this;
        }

        public Criteria andCommentOneMinRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("comment_one_min_rate between", value1, value2, "commentOneMinRate");
            return (Criteria) this;
        }

        public Criteria andCommentOneMinRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("comment_one_min_rate not between", value1, value2, "commentOneMinRate");
            return (Criteria) this;
        }

        public Criteria andUpGuestOneMinRateIsNull() {
            addCriterion("up_guest_one_min_rate is null");
            return (Criteria) this;
        }

        public Criteria andUpGuestOneMinRateIsNotNull() {
            addCriterion("up_guest_one_min_rate is not null");
            return (Criteria) this;
        }

        public Criteria andUpGuestOneMinRateEqualTo(BigDecimal value) {
            addCriterion("up_guest_one_min_rate =", value, "upGuestOneMinRate");
            return (Criteria) this;
        }

        public Criteria andUpGuestOneMinRateNotEqualTo(BigDecimal value) {
            addCriterion("up_guest_one_min_rate <>", value, "upGuestOneMinRate");
            return (Criteria) this;
        }

        public Criteria andUpGuestOneMinRateGreaterThan(BigDecimal value) {
            addCriterion("up_guest_one_min_rate >", value, "upGuestOneMinRate");
            return (Criteria) this;
        }

        public Criteria andUpGuestOneMinRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("up_guest_one_min_rate >=", value, "upGuestOneMinRate");
            return (Criteria) this;
        }

        public Criteria andUpGuestOneMinRateLessThan(BigDecimal value) {
            addCriterion("up_guest_one_min_rate <", value, "upGuestOneMinRate");
            return (Criteria) this;
        }

        public Criteria andUpGuestOneMinRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("up_guest_one_min_rate <=", value, "upGuestOneMinRate");
            return (Criteria) this;
        }

        public Criteria andUpGuestOneMinRateIn(List<BigDecimal> values) {
            addCriterion("up_guest_one_min_rate in", values, "upGuestOneMinRate");
            return (Criteria) this;
        }

        public Criteria andUpGuestOneMinRateNotIn(List<BigDecimal> values) {
            addCriterion("up_guest_one_min_rate not in", values, "upGuestOneMinRate");
            return (Criteria) this;
        }

        public Criteria andUpGuestOneMinRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("up_guest_one_min_rate between", value1, value2, "upGuestOneMinRate");
            return (Criteria) this;
        }

        public Criteria andUpGuestOneMinRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("up_guest_one_min_rate not between", value1, value2, "upGuestOneMinRate");
            return (Criteria) this;
        }

        public Criteria andGiftOneMinRateIsNull() {
            addCriterion("gift_one_min_rate is null");
            return (Criteria) this;
        }

        public Criteria andGiftOneMinRateIsNotNull() {
            addCriterion("gift_one_min_rate is not null");
            return (Criteria) this;
        }

        public Criteria andGiftOneMinRateEqualTo(BigDecimal value) {
            addCriterion("gift_one_min_rate =", value, "giftOneMinRate");
            return (Criteria) this;
        }

        public Criteria andGiftOneMinRateNotEqualTo(BigDecimal value) {
            addCriterion("gift_one_min_rate <>", value, "giftOneMinRate");
            return (Criteria) this;
        }

        public Criteria andGiftOneMinRateGreaterThan(BigDecimal value) {
            addCriterion("gift_one_min_rate >", value, "giftOneMinRate");
            return (Criteria) this;
        }

        public Criteria andGiftOneMinRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("gift_one_min_rate >=", value, "giftOneMinRate");
            return (Criteria) this;
        }

        public Criteria andGiftOneMinRateLessThan(BigDecimal value) {
            addCriterion("gift_one_min_rate <", value, "giftOneMinRate");
            return (Criteria) this;
        }

        public Criteria andGiftOneMinRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("gift_one_min_rate <=", value, "giftOneMinRate");
            return (Criteria) this;
        }

        public Criteria andGiftOneMinRateIn(List<BigDecimal> values) {
            addCriterion("gift_one_min_rate in", values, "giftOneMinRate");
            return (Criteria) this;
        }

        public Criteria andGiftOneMinRateNotIn(List<BigDecimal> values) {
            addCriterion("gift_one_min_rate not in", values, "giftOneMinRate");
            return (Criteria) this;
        }

        public Criteria andGiftOneMinRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("gift_one_min_rate between", value1, value2, "giftOneMinRate");
            return (Criteria) this;
        }

        public Criteria andGiftOneMinRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("gift_one_min_rate not between", value1, value2, "giftOneMinRate");
            return (Criteria) this;
        }

        public Criteria andGiftThreeMinRateIsNull() {
            addCriterion("gift_three_min_rate is null");
            return (Criteria) this;
        }

        public Criteria andGiftThreeMinRateIsNotNull() {
            addCriterion("gift_three_min_rate is not null");
            return (Criteria) this;
        }

        public Criteria andGiftThreeMinRateEqualTo(BigDecimal value) {
            addCriterion("gift_three_min_rate =", value, "giftThreeMinRate");
            return (Criteria) this;
        }

        public Criteria andGiftThreeMinRateNotEqualTo(BigDecimal value) {
            addCriterion("gift_three_min_rate <>", value, "giftThreeMinRate");
            return (Criteria) this;
        }

        public Criteria andGiftThreeMinRateGreaterThan(BigDecimal value) {
            addCriterion("gift_three_min_rate >", value, "giftThreeMinRate");
            return (Criteria) this;
        }

        public Criteria andGiftThreeMinRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("gift_three_min_rate >=", value, "giftThreeMinRate");
            return (Criteria) this;
        }

        public Criteria andGiftThreeMinRateLessThan(BigDecimal value) {
            addCriterion("gift_three_min_rate <", value, "giftThreeMinRate");
            return (Criteria) this;
        }

        public Criteria andGiftThreeMinRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("gift_three_min_rate <=", value, "giftThreeMinRate");
            return (Criteria) this;
        }

        public Criteria andGiftThreeMinRateIn(List<BigDecimal> values) {
            addCriterion("gift_three_min_rate in", values, "giftThreeMinRate");
            return (Criteria) this;
        }

        public Criteria andGiftThreeMinRateNotIn(List<BigDecimal> values) {
            addCriterion("gift_three_min_rate not in", values, "giftThreeMinRate");
            return (Criteria) this;
        }

        public Criteria andGiftThreeMinRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("gift_three_min_rate between", value1, value2, "giftThreeMinRate");
            return (Criteria) this;
        }

        public Criteria andGiftThreeMinRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("gift_three_min_rate not between", value1, value2, "giftThreeMinRate");
            return (Criteria) this;
        }

        public Criteria andGiftFiveMinRateIsNull() {
            addCriterion("gift_five_min_rate is null");
            return (Criteria) this;
        }

        public Criteria andGiftFiveMinRateIsNotNull() {
            addCriterion("gift_five_min_rate is not null");
            return (Criteria) this;
        }

        public Criteria andGiftFiveMinRateEqualTo(BigDecimal value) {
            addCriterion("gift_five_min_rate =", value, "giftFiveMinRate");
            return (Criteria) this;
        }

        public Criteria andGiftFiveMinRateNotEqualTo(BigDecimal value) {
            addCriterion("gift_five_min_rate <>", value, "giftFiveMinRate");
            return (Criteria) this;
        }

        public Criteria andGiftFiveMinRateGreaterThan(BigDecimal value) {
            addCriterion("gift_five_min_rate >", value, "giftFiveMinRate");
            return (Criteria) this;
        }

        public Criteria andGiftFiveMinRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("gift_five_min_rate >=", value, "giftFiveMinRate");
            return (Criteria) this;
        }

        public Criteria andGiftFiveMinRateLessThan(BigDecimal value) {
            addCriterion("gift_five_min_rate <", value, "giftFiveMinRate");
            return (Criteria) this;
        }

        public Criteria andGiftFiveMinRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("gift_five_min_rate <=", value, "giftFiveMinRate");
            return (Criteria) this;
        }

        public Criteria andGiftFiveMinRateIn(List<BigDecimal> values) {
            addCriterion("gift_five_min_rate in", values, "giftFiveMinRate");
            return (Criteria) this;
        }

        public Criteria andGiftFiveMinRateNotIn(List<BigDecimal> values) {
            addCriterion("gift_five_min_rate not in", values, "giftFiveMinRate");
            return (Criteria) this;
        }

        public Criteria andGiftFiveMinRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("gift_five_min_rate between", value1, value2, "giftFiveMinRate");
            return (Criteria) this;
        }

        public Criteria andGiftFiveMinRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("gift_five_min_rate not between", value1, value2, "giftFiveMinRate");
            return (Criteria) this;
        }

        public Criteria andChatPlayerCntIsNull() {
            addCriterion("chat_player_cnt is null");
            return (Criteria) this;
        }

        public Criteria andChatPlayerCntIsNotNull() {
            addCriterion("chat_player_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andChatPlayerCntEqualTo(Integer value) {
            addCriterion("chat_player_cnt =", value, "chatPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andChatPlayerCntNotEqualTo(Integer value) {
            addCriterion("chat_player_cnt <>", value, "chatPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andChatPlayerCntGreaterThan(Integer value) {
            addCriterion("chat_player_cnt >", value, "chatPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andChatPlayerCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("chat_player_cnt >=", value, "chatPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andChatPlayerCntLessThan(Integer value) {
            addCriterion("chat_player_cnt <", value, "chatPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andChatPlayerCntLessThanOrEqualTo(Integer value) {
            addCriterion("chat_player_cnt <=", value, "chatPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andChatPlayerCntIn(List<Integer> values) {
            addCriterion("chat_player_cnt in", values, "chatPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andChatPlayerCntNotIn(List<Integer> values) {
            addCriterion("chat_player_cnt not in", values, "chatPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andChatPlayerCntBetween(Integer value1, Integer value2) {
            addCriterion("chat_player_cnt between", value1, value2, "chatPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andChatPlayerCntNotBetween(Integer value1, Integer value2) {
            addCriterion("chat_player_cnt not between", value1, value2, "chatPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomeSingerCntIsNull() {
            addCriterion("income_singer_cnt is null");
            return (Criteria) this;
        }

        public Criteria andIncomeSingerCntIsNotNull() {
            addCriterion("income_singer_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andIncomeSingerCntEqualTo(Integer value) {
            addCriterion("income_singer_cnt =", value, "incomeSingerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomeSingerCntNotEqualTo(Integer value) {
            addCriterion("income_singer_cnt <>", value, "incomeSingerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomeSingerCntGreaterThan(Integer value) {
            addCriterion("income_singer_cnt >", value, "incomeSingerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomeSingerCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("income_singer_cnt >=", value, "incomeSingerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomeSingerCntLessThan(Integer value) {
            addCriterion("income_singer_cnt <", value, "incomeSingerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomeSingerCntLessThanOrEqualTo(Integer value) {
            addCriterion("income_singer_cnt <=", value, "incomeSingerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomeSingerCntIn(List<Integer> values) {
            addCriterion("income_singer_cnt in", values, "incomeSingerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomeSingerCntNotIn(List<Integer> values) {
            addCriterion("income_singer_cnt not in", values, "incomeSingerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomeSingerCntBetween(Integer value1, Integer value2) {
            addCriterion("income_singer_cnt between", value1, value2, "incomeSingerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomeSingerCntNotBetween(Integer value1, Integer value2) {
            addCriterion("income_singer_cnt not between", value1, value2, "incomeSingerCnt");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wavecenter_data_room_month
     *
     * @mbg.generated do_not_delete_during_merge Mon Mar 24 18:23:10 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wavecenter_data_room_month
     *
     * @mbg.generated Mon Mar 24 18:23:10 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}