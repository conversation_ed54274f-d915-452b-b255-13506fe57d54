package fm.lizhi.ocean.wavecenter.datastore.datacenter.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 日麦序奖励记录表
 *
 * @date 2025-03-05 07:56:38
 */
@Table(name = "`wave_check_in_day_mic_record`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WaveCheckInDayMicRecord {
    /**
     * ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 用户ID
     */
    @Column(name= "`user_id`")
    private Long userId;

    /**
     * 房间ID
     */
    @Column(name= "`room_id`")
    private Long roomId;

    /**
     * 家族ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 统计日期
     */
    @Column(name= "`calc_date`")
    private Date calcDate;

    /**
     * 魅力值
     */
    @Column(name= "`charm`")
    private Long charm;

    /**
     * 有效麦序个数
     */
    @Column(name= "`valid_mic_count`")
    private Long validMicCount;

    /**
     * 参与计算麦序个数
     */
    @Column(name= "`calc_mic_count`")
    private Long calcMicCount;

    /**
     * 计算方式, 1-固定金额计算, 2-麦序计算
     */
    @Column(name= "`calc_type`")
    private Integer calcType;

    /**
     * 总奖励金额
     */
    @Column(name= "`reward_amount_sum`")
    private Long rewardAmountSum;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`update_time`")
    private Date updateTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", userId=").append(userId);
        sb.append(", roomId=").append(roomId);
        sb.append(", familyId=").append(familyId);
        sb.append(", calcDate=").append(calcDate);
        sb.append(", charm=").append(charm);
        sb.append(", validMicCount=").append(validMicCount);
        sb.append(", calcMicCount=").append(calcMicCount);
        sb.append(", calcType=").append(calcType);
        sb.append(", rewardAmountSum=").append(rewardAmountSum);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }
}