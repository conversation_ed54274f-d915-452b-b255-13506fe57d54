package fm.lizhi.ocean.wavecenter.datastore.datacenter.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 *
 * @date 2024-06-11 03:31:34
 */
@Table(name = "`wavecenter_player_check_in_day_stats`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcPlayerCheckInDayStats {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 主播ID
     */
    @Column(name= "`user_id`")
    private Long userId;

    @Column(name= "`income`")
    private BigDecimal income;

    /**
     * 打卡所在的厅主ID
     */
    @Column(name= "`nj_id`")
    private Long njId;

    @Column(name= "`charm`")
    private Long charm;

    /**
     * 麦序
     */
    @Column(name= "`seat_order`")
    private Integer seatOrder;

    /**
     * 主持次数
     */
    @Column(name= "`host_num`")
    private Integer hostNum;

    /**
     * 上麦时长（单位分钟）
     */
    @Column(name= "`up_guest_dur`")
    private Integer upGuestDur;

    /**
     * 统计天
     */
    @Column(name= "`stat_date`")
    private Date statDate;

    @Column(name= "`create_time`")
    private Date createTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", userId=").append(userId);
        sb.append(", income=").append(income);
        sb.append(", njId=").append(njId);
        sb.append(", charm=").append(charm);
        sb.append(", seatOrder=").append(seatOrder);
        sb.append(", hostNum=").append(hostNum);
        sb.append(", upGuestDur=").append(upGuestDur);
        sb.append(", statDate=").append(statDate);
        sb.append(", createTime=").append(createTime);
        sb.append("]");
        return sb.toString();
    }
}