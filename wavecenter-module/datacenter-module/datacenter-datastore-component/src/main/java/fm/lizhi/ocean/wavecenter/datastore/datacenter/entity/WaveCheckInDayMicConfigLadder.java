package fm.lizhi.ocean.wavecenter.datastore.datacenter.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 麦序福利全麦奖励配置阶梯
 *
 * @date 2025-03-05 05:16:09
 */
@Table(name = "`wave_check_in_day_mic_config_ladder`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WaveCheckInDayMicConfigLadder {
    /**
     * ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 业务ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 家族ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 房间ID
     */
    @Column(name= "`room_id`")
    private Long roomId;

    /**
     * 日麦序配置ID
     */
    @Column(name= "`config_id`")
    private Long configId;

    /**
     * 魅力值大于等于
     */
    @Column(name= "`charm_greater_equal`")
    private Integer charmGreaterEqual;

    /**
     * 奖励金额, 单位元.
     */
    @Column(name= "`reward_amount`")
    private Integer rewardAmount;

    /**
     * 有效麦序个数
     */
    @Column(name= "`valid_mic_count`")
    private Integer validMicCount;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", familyId=").append(familyId);
        sb.append(", roomId=").append(roomId);
        sb.append(", configId=").append(configId);
        sb.append(", charmGreaterEqual=").append(charmGreaterEqual);
        sb.append(", rewardAmount=").append(rewardAmount);
        sb.append(", validMicCount=").append(validMicCount);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}