package fm.lizhi.ocean.wavecenter.datastore.datacenter.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 *
 * @date 2024-04-24 11:19:00
 */
@Table(name = "`wavecenter_audit_record`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AuditRecord {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Long appId;

    /**
     * 厅ID
     */
    @Column(name= "`room_id`")
    private Long roomId;

    /**
     * 工会ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 主播ID
     */
    @Column(name= "`user_id`")
    private Long userId;

    /**
     * 操作类型
     */
    @Column(name= "`op`")
    private Integer op;

    /**
     * 操作理由
     */
    @Column(name= "`reason`")
    private String reason;

    /**
     * 处罚时间
     */
    @Column(name= "`insert_time`")
    private Date insertTime;

    /**
     * 送审id
     */
    @Column(name= "`audit_id`")
    private String auditId;

    /**
     * 处罚时效时间
     */
    @Column(name= "`push_time`")
    private String pushTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", roomId=").append(roomId);
        sb.append(", familyId=").append(familyId);
        sb.append(", userId=").append(userId);
        sb.append(", op=").append(op);
        sb.append(", reason=").append(reason);
        sb.append(", insertTime=").append(insertTime);
        sb.append(", auditId=").append(auditId);
        sb.append(", pushTime=").append(pushTime);
        sb.append("]");
        return sb.toString();
    }
}