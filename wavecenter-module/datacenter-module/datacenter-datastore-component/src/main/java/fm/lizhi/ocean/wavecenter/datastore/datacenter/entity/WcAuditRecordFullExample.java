package fm.lizhi.ocean.wavecenter.datastore.datacenter.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WcAuditRecordFullExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_audit_record_full
     *
     * @mbg.generated Wed Nov 13 10:33:43 CST 2024
     */
    public WcAuditRecordFullExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_audit_record_full
     *
     * @mbg.generated Wed Nov 13 10:33:43 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_audit_record_full
     *
     * @mbg.generated Wed Nov 13 10:33:43 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_audit_record_full
     *
     * @mbg.generated Wed Nov 13 10:33:43 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_audit_record_full
     *
     * @mbg.generated Wed Nov 13 10:33:43 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_audit_record_full
     *
     * @mbg.generated Wed Nov 13 10:33:43 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_audit_record_full
     *
     * @mbg.generated Wed Nov 13 10:33:43 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_audit_record_full
     *
     * @mbg.generated Wed Nov 13 10:33:43 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_audit_record_full
     *
     * @mbg.generated Wed Nov 13 10:33:43 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_audit_record_full
     *
     * @mbg.generated Wed Nov 13 10:33:43 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_audit_record_full
     *
     * @mbg.generated Wed Nov 13 10:33:43 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Class getEntityClass() {
        return WcAuditRecordFull.class;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wavecenter_audit_record_full
     *
     * @mbg.generated Wed Nov 13 10:33:43 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(Integer value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(Integer value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(Integer value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(Integer value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(Integer value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<Integer> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<Integer> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(Integer value1, Integer value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(Integer value1, Integer value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andSignNjIdIsNull() {
            addCriterion("sign_nj_id is null");
            return (Criteria) this;
        }

        public Criteria andSignNjIdIsNotNull() {
            addCriterion("sign_nj_id is not null");
            return (Criteria) this;
        }

        public Criteria andSignNjIdEqualTo(Long value) {
            addCriterion("sign_nj_id =", value, "signNjId");
            return (Criteria) this;
        }

        public Criteria andSignNjIdNotEqualTo(Long value) {
            addCriterion("sign_nj_id <>", value, "signNjId");
            return (Criteria) this;
        }

        public Criteria andSignNjIdGreaterThan(Long value) {
            addCriterion("sign_nj_id >", value, "signNjId");
            return (Criteria) this;
        }

        public Criteria andSignNjIdGreaterThanOrEqualTo(Long value) {
            addCriterion("sign_nj_id >=", value, "signNjId");
            return (Criteria) this;
        }

        public Criteria andSignNjIdLessThan(Long value) {
            addCriterion("sign_nj_id <", value, "signNjId");
            return (Criteria) this;
        }

        public Criteria andSignNjIdLessThanOrEqualTo(Long value) {
            addCriterion("sign_nj_id <=", value, "signNjId");
            return (Criteria) this;
        }

        public Criteria andSignNjIdIn(List<Long> values) {
            addCriterion("sign_nj_id in", values, "signNjId");
            return (Criteria) this;
        }

        public Criteria andSignNjIdNotIn(List<Long> values) {
            addCriterion("sign_nj_id not in", values, "signNjId");
            return (Criteria) this;
        }

        public Criteria andSignNjIdBetween(Long value1, Long value2) {
            addCriterion("sign_nj_id between", value1, value2, "signNjId");
            return (Criteria) this;
        }

        public Criteria andSignNjIdNotBetween(Long value1, Long value2) {
            addCriterion("sign_nj_id not between", value1, value2, "signNjId");
            return (Criteria) this;
        }

        public Criteria andSourceContentUrlIsNull() {
            addCriterion("source_content_url is null");
            return (Criteria) this;
        }

        public Criteria andSourceContentUrlIsNotNull() {
            addCriterion("source_content_url is not null");
            return (Criteria) this;
        }

        public Criteria andSourceContentUrlEqualTo(String value) {
            addCriterion("source_content_url =", value, "sourceContentUrl");
            return (Criteria) this;
        }

        public Criteria andSourceContentUrlNotEqualTo(String value) {
            addCriterion("source_content_url <>", value, "sourceContentUrl");
            return (Criteria) this;
        }

        public Criteria andSourceContentUrlGreaterThan(String value) {
            addCriterion("source_content_url >", value, "sourceContentUrl");
            return (Criteria) this;
        }

        public Criteria andSourceContentUrlGreaterThanOrEqualTo(String value) {
            addCriterion("source_content_url >=", value, "sourceContentUrl");
            return (Criteria) this;
        }

        public Criteria andSourceContentUrlLessThan(String value) {
            addCriterion("source_content_url <", value, "sourceContentUrl");
            return (Criteria) this;
        }

        public Criteria andSourceContentUrlLessThanOrEqualTo(String value) {
            addCriterion("source_content_url <=", value, "sourceContentUrl");
            return (Criteria) this;
        }

        public Criteria andSourceContentUrlLike(String value) {
            addCriterion("source_content_url like", value, "sourceContentUrl");
            return (Criteria) this;
        }

        public Criteria andSourceContentUrlNotLike(String value) {
            addCriterion("source_content_url not like", value, "sourceContentUrl");
            return (Criteria) this;
        }

        public Criteria andSourceContentUrlIn(List<String> values) {
            addCriterion("source_content_url in", values, "sourceContentUrl");
            return (Criteria) this;
        }

        public Criteria andSourceContentUrlNotIn(List<String> values) {
            addCriterion("source_content_url not in", values, "sourceContentUrl");
            return (Criteria) this;
        }

        public Criteria andSourceContentUrlBetween(String value1, String value2) {
            addCriterion("source_content_url between", value1, value2, "sourceContentUrl");
            return (Criteria) this;
        }

        public Criteria andSourceContentUrlNotBetween(String value1, String value2) {
            addCriterion("source_content_url not between", value1, value2, "sourceContentUrl");
            return (Criteria) this;
        }

        public Criteria andPublicContentUrlIsNull() {
            addCriterion("public_content_url is null");
            return (Criteria) this;
        }

        public Criteria andPublicContentUrlIsNotNull() {
            addCriterion("public_content_url is not null");
            return (Criteria) this;
        }

        public Criteria andPublicContentUrlEqualTo(String value) {
            addCriterion("public_content_url =", value, "publicContentUrl");
            return (Criteria) this;
        }

        public Criteria andPublicContentUrlNotEqualTo(String value) {
            addCriterion("public_content_url <>", value, "publicContentUrl");
            return (Criteria) this;
        }

        public Criteria andPublicContentUrlGreaterThan(String value) {
            addCriterion("public_content_url >", value, "publicContentUrl");
            return (Criteria) this;
        }

        public Criteria andPublicContentUrlGreaterThanOrEqualTo(String value) {
            addCriterion("public_content_url >=", value, "publicContentUrl");
            return (Criteria) this;
        }

        public Criteria andPublicContentUrlLessThan(String value) {
            addCriterion("public_content_url <", value, "publicContentUrl");
            return (Criteria) this;
        }

        public Criteria andPublicContentUrlLessThanOrEqualTo(String value) {
            addCriterion("public_content_url <=", value, "publicContentUrl");
            return (Criteria) this;
        }

        public Criteria andPublicContentUrlLike(String value) {
            addCriterion("public_content_url like", value, "publicContentUrl");
            return (Criteria) this;
        }

        public Criteria andPublicContentUrlNotLike(String value) {
            addCriterion("public_content_url not like", value, "publicContentUrl");
            return (Criteria) this;
        }

        public Criteria andPublicContentUrlIn(List<String> values) {
            addCriterion("public_content_url in", values, "publicContentUrl");
            return (Criteria) this;
        }

        public Criteria andPublicContentUrlNotIn(List<String> values) {
            addCriterion("public_content_url not in", values, "publicContentUrl");
            return (Criteria) this;
        }

        public Criteria andPublicContentUrlBetween(String value1, String value2) {
            addCriterion("public_content_url between", value1, value2, "publicContentUrl");
            return (Criteria) this;
        }

        public Criteria andPublicContentUrlNotBetween(String value1, String value2) {
            addCriterion("public_content_url not between", value1, value2, "publicContentUrl");
            return (Criteria) this;
        }

        public Criteria andSignFamilyIdIsNull() {
            addCriterion("sign_family_id is null");
            return (Criteria) this;
        }

        public Criteria andSignFamilyIdIsNotNull() {
            addCriterion("sign_family_id is not null");
            return (Criteria) this;
        }

        public Criteria andSignFamilyIdEqualTo(Long value) {
            addCriterion("sign_family_id =", value, "signFamilyId");
            return (Criteria) this;
        }

        public Criteria andSignFamilyIdNotEqualTo(Long value) {
            addCriterion("sign_family_id <>", value, "signFamilyId");
            return (Criteria) this;
        }

        public Criteria andSignFamilyIdGreaterThan(Long value) {
            addCriterion("sign_family_id >", value, "signFamilyId");
            return (Criteria) this;
        }

        public Criteria andSignFamilyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("sign_family_id >=", value, "signFamilyId");
            return (Criteria) this;
        }

        public Criteria andSignFamilyIdLessThan(Long value) {
            addCriterion("sign_family_id <", value, "signFamilyId");
            return (Criteria) this;
        }

        public Criteria andSignFamilyIdLessThanOrEqualTo(Long value) {
            addCriterion("sign_family_id <=", value, "signFamilyId");
            return (Criteria) this;
        }

        public Criteria andSignFamilyIdIn(List<Long> values) {
            addCriterion("sign_family_id in", values, "signFamilyId");
            return (Criteria) this;
        }

        public Criteria andSignFamilyIdNotIn(List<Long> values) {
            addCriterion("sign_family_id not in", values, "signFamilyId");
            return (Criteria) this;
        }

        public Criteria andSignFamilyIdBetween(Long value1, Long value2) {
            addCriterion("sign_family_id between", value1, value2, "signFamilyId");
            return (Criteria) this;
        }

        public Criteria andSignFamilyIdNotBetween(Long value1, Long value2) {
            addCriterion("sign_family_id not between", value1, value2, "signFamilyId");
            return (Criteria) this;
        }

        public Criteria andBizIdIsNull() {
            addCriterion("biz_id is null");
            return (Criteria) this;
        }

        public Criteria andBizIdIsNotNull() {
            addCriterion("biz_id is not null");
            return (Criteria) this;
        }

        public Criteria andBizIdEqualTo(Long value) {
            addCriterion("biz_id =", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdNotEqualTo(Long value) {
            addCriterion("biz_id <>", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdGreaterThan(Long value) {
            addCriterion("biz_id >", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdGreaterThanOrEqualTo(Long value) {
            addCriterion("biz_id >=", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdLessThan(Long value) {
            addCriterion("biz_id <", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdLessThanOrEqualTo(Long value) {
            addCriterion("biz_id <=", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdIn(List<Long> values) {
            addCriterion("biz_id in", values, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdNotIn(List<Long> values) {
            addCriterion("biz_id not in", values, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdBetween(Long value1, Long value2) {
            addCriterion("biz_id between", value1, value2, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdNotBetween(Long value1, Long value2) {
            addCriterion("biz_id not between", value1, value2, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizNjIdIsNull() {
            addCriterion("biz_nj_id is null");
            return (Criteria) this;
        }

        public Criteria andBizNjIdIsNotNull() {
            addCriterion("biz_nj_id is not null");
            return (Criteria) this;
        }

        public Criteria andBizNjIdEqualTo(Long value) {
            addCriterion("biz_nj_id =", value, "bizNjId");
            return (Criteria) this;
        }

        public Criteria andBizNjIdNotEqualTo(Long value) {
            addCriterion("biz_nj_id <>", value, "bizNjId");
            return (Criteria) this;
        }

        public Criteria andBizNjIdGreaterThan(Long value) {
            addCriterion("biz_nj_id >", value, "bizNjId");
            return (Criteria) this;
        }

        public Criteria andBizNjIdGreaterThanOrEqualTo(Long value) {
            addCriterion("biz_nj_id >=", value, "bizNjId");
            return (Criteria) this;
        }

        public Criteria andBizNjIdLessThan(Long value) {
            addCriterion("biz_nj_id <", value, "bizNjId");
            return (Criteria) this;
        }

        public Criteria andBizNjIdLessThanOrEqualTo(Long value) {
            addCriterion("biz_nj_id <=", value, "bizNjId");
            return (Criteria) this;
        }

        public Criteria andBizNjIdIn(List<Long> values) {
            addCriterion("biz_nj_id in", values, "bizNjId");
            return (Criteria) this;
        }

        public Criteria andBizNjIdNotIn(List<Long> values) {
            addCriterion("biz_nj_id not in", values, "bizNjId");
            return (Criteria) this;
        }

        public Criteria andBizNjIdBetween(Long value1, Long value2) {
            addCriterion("biz_nj_id between", value1, value2, "bizNjId");
            return (Criteria) this;
        }

        public Criteria andBizNjIdNotBetween(Long value1, Long value2) {
            addCriterion("biz_nj_id not between", value1, value2, "bizNjId");
            return (Criteria) this;
        }

        public Criteria andBizFamilyIdIsNull() {
            addCriterion("biz_family_id is null");
            return (Criteria) this;
        }

        public Criteria andBizFamilyIdIsNotNull() {
            addCriterion("biz_family_id is not null");
            return (Criteria) this;
        }

        public Criteria andBizFamilyIdEqualTo(Long value) {
            addCriterion("biz_family_id =", value, "bizFamilyId");
            return (Criteria) this;
        }

        public Criteria andBizFamilyIdNotEqualTo(Long value) {
            addCriterion("biz_family_id <>", value, "bizFamilyId");
            return (Criteria) this;
        }

        public Criteria andBizFamilyIdGreaterThan(Long value) {
            addCriterion("biz_family_id >", value, "bizFamilyId");
            return (Criteria) this;
        }

        public Criteria andBizFamilyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("biz_family_id >=", value, "bizFamilyId");
            return (Criteria) this;
        }

        public Criteria andBizFamilyIdLessThan(Long value) {
            addCriterion("biz_family_id <", value, "bizFamilyId");
            return (Criteria) this;
        }

        public Criteria andBizFamilyIdLessThanOrEqualTo(Long value) {
            addCriterion("biz_family_id <=", value, "bizFamilyId");
            return (Criteria) this;
        }

        public Criteria andBizFamilyIdIn(List<Long> values) {
            addCriterion("biz_family_id in", values, "bizFamilyId");
            return (Criteria) this;
        }

        public Criteria andBizFamilyIdNotIn(List<Long> values) {
            addCriterion("biz_family_id not in", values, "bizFamilyId");
            return (Criteria) this;
        }

        public Criteria andBizFamilyIdBetween(Long value1, Long value2) {
            addCriterion("biz_family_id between", value1, value2, "bizFamilyId");
            return (Criteria) this;
        }

        public Criteria andBizFamilyIdNotBetween(Long value1, Long value2) {
            addCriterion("biz_family_id not between", value1, value2, "bizFamilyId");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(Long value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(Long value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(Long value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(Long value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Long value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<Long> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<Long> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(Long value1, Long value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(Long value1, Long value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andToUserIdIsNull() {
            addCriterion("to_user_id is null");
            return (Criteria) this;
        }

        public Criteria andToUserIdIsNotNull() {
            addCriterion("to_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andToUserIdEqualTo(Long value) {
            addCriterion("to_user_id =", value, "toUserId");
            return (Criteria) this;
        }

        public Criteria andToUserIdNotEqualTo(Long value) {
            addCriterion("to_user_id <>", value, "toUserId");
            return (Criteria) this;
        }

        public Criteria andToUserIdGreaterThan(Long value) {
            addCriterion("to_user_id >", value, "toUserId");
            return (Criteria) this;
        }

        public Criteria andToUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("to_user_id >=", value, "toUserId");
            return (Criteria) this;
        }

        public Criteria andToUserIdLessThan(Long value) {
            addCriterion("to_user_id <", value, "toUserId");
            return (Criteria) this;
        }

        public Criteria andToUserIdLessThanOrEqualTo(Long value) {
            addCriterion("to_user_id <=", value, "toUserId");
            return (Criteria) this;
        }

        public Criteria andToUserIdIn(List<Long> values) {
            addCriterion("to_user_id in", values, "toUserId");
            return (Criteria) this;
        }

        public Criteria andToUserIdNotIn(List<Long> values) {
            addCriterion("to_user_id not in", values, "toUserId");
            return (Criteria) this;
        }

        public Criteria andToUserIdBetween(Long value1, Long value2) {
            addCriterion("to_user_id between", value1, value2, "toUserId");
            return (Criteria) this;
        }

        public Criteria andToUserIdNotBetween(Long value1, Long value2) {
            addCriterion("to_user_id not between", value1, value2, "toUserId");
            return (Criteria) this;
        }

        public Criteria andOpIsNull() {
            addCriterion("op is null");
            return (Criteria) this;
        }

        public Criteria andOpIsNotNull() {
            addCriterion("op is not null");
            return (Criteria) this;
        }

        public Criteria andOpEqualTo(Integer value) {
            addCriterion("op =", value, "op");
            return (Criteria) this;
        }

        public Criteria andOpNotEqualTo(Integer value) {
            addCriterion("op <>", value, "op");
            return (Criteria) this;
        }

        public Criteria andOpGreaterThan(Integer value) {
            addCriterion("op >", value, "op");
            return (Criteria) this;
        }

        public Criteria andOpGreaterThanOrEqualTo(Integer value) {
            addCriterion("op >=", value, "op");
            return (Criteria) this;
        }

        public Criteria andOpLessThan(Integer value) {
            addCriterion("op <", value, "op");
            return (Criteria) this;
        }

        public Criteria andOpLessThanOrEqualTo(Integer value) {
            addCriterion("op <=", value, "op");
            return (Criteria) this;
        }

        public Criteria andOpIn(List<Integer> values) {
            addCriterion("op in", values, "op");
            return (Criteria) this;
        }

        public Criteria andOpNotIn(List<Integer> values) {
            addCriterion("op not in", values, "op");
            return (Criteria) this;
        }

        public Criteria andOpBetween(Integer value1, Integer value2) {
            addCriterion("op between", value1, value2, "op");
            return (Criteria) this;
        }

        public Criteria andOpNotBetween(Integer value1, Integer value2) {
            addCriterion("op not between", value1, value2, "op");
            return (Criteria) this;
        }

        public Criteria andReasonIsNull() {
            addCriterion("reason is null");
            return (Criteria) this;
        }

        public Criteria andReasonIsNotNull() {
            addCriterion("reason is not null");
            return (Criteria) this;
        }

        public Criteria andReasonEqualTo(String value) {
            addCriterion("reason =", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonNotEqualTo(String value) {
            addCriterion("reason <>", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonGreaterThan(String value) {
            addCriterion("reason >", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonGreaterThanOrEqualTo(String value) {
            addCriterion("reason >=", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonLessThan(String value) {
            addCriterion("reason <", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonLessThanOrEqualTo(String value) {
            addCriterion("reason <=", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonLike(String value) {
            addCriterion("reason like", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonNotLike(String value) {
            addCriterion("reason not like", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonIn(List<String> values) {
            addCriterion("reason in", values, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonNotIn(List<String> values) {
            addCriterion("reason not in", values, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonBetween(String value1, String value2) {
            addCriterion("reason between", value1, value2, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonNotBetween(String value1, String value2) {
            addCriterion("reason not between", value1, value2, "reason");
            return (Criteria) this;
        }

        public Criteria andAuditEndTimeIsNull() {
            addCriterion("audit_end_time is null");
            return (Criteria) this;
        }

        public Criteria andAuditEndTimeIsNotNull() {
            addCriterion("audit_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andAuditEndTimeEqualTo(Date value) {
            addCriterion("audit_end_time =", value, "auditEndTime");
            return (Criteria) this;
        }

        public Criteria andAuditEndTimeNotEqualTo(Date value) {
            addCriterion("audit_end_time <>", value, "auditEndTime");
            return (Criteria) this;
        }

        public Criteria andAuditEndTimeGreaterThan(Date value) {
            addCriterion("audit_end_time >", value, "auditEndTime");
            return (Criteria) this;
        }

        public Criteria andAuditEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("audit_end_time >=", value, "auditEndTime");
            return (Criteria) this;
        }

        public Criteria andAuditEndTimeLessThan(Date value) {
            addCriterion("audit_end_time <", value, "auditEndTime");
            return (Criteria) this;
        }

        public Criteria andAuditEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("audit_end_time <=", value, "auditEndTime");
            return (Criteria) this;
        }

        public Criteria andAuditEndTimeIn(List<Date> values) {
            addCriterion("audit_end_time in", values, "auditEndTime");
            return (Criteria) this;
        }

        public Criteria andAuditEndTimeNotIn(List<Date> values) {
            addCriterion("audit_end_time not in", values, "auditEndTime");
            return (Criteria) this;
        }

        public Criteria andAuditEndTimeBetween(Date value1, Date value2) {
            addCriterion("audit_end_time between", value1, value2, "auditEndTime");
            return (Criteria) this;
        }

        public Criteria andAuditEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("audit_end_time not between", value1, value2, "auditEndTime");
            return (Criteria) this;
        }

        public Criteria andAuditStartTimeIsNull() {
            addCriterion("audit_start_time is null");
            return (Criteria) this;
        }

        public Criteria andAuditStartTimeIsNotNull() {
            addCriterion("audit_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andAuditStartTimeEqualTo(Date value) {
            addCriterion("audit_start_time =", value, "auditStartTime");
            return (Criteria) this;
        }

        public Criteria andAuditStartTimeNotEqualTo(Date value) {
            addCriterion("audit_start_time <>", value, "auditStartTime");
            return (Criteria) this;
        }

        public Criteria andAuditStartTimeGreaterThan(Date value) {
            addCriterion("audit_start_time >", value, "auditStartTime");
            return (Criteria) this;
        }

        public Criteria andAuditStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("audit_start_time >=", value, "auditStartTime");
            return (Criteria) this;
        }

        public Criteria andAuditStartTimeLessThan(Date value) {
            addCriterion("audit_start_time <", value, "auditStartTime");
            return (Criteria) this;
        }

        public Criteria andAuditStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("audit_start_time <=", value, "auditStartTime");
            return (Criteria) this;
        }

        public Criteria andAuditStartTimeIn(List<Date> values) {
            addCriterion("audit_start_time in", values, "auditStartTime");
            return (Criteria) this;
        }

        public Criteria andAuditStartTimeNotIn(List<Date> values) {
            addCriterion("audit_start_time not in", values, "auditStartTime");
            return (Criteria) this;
        }

        public Criteria andAuditStartTimeBetween(Date value1, Date value2) {
            addCriterion("audit_start_time between", value1, value2, "auditStartTime");
            return (Criteria) this;
        }

        public Criteria andAuditStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("audit_start_time not between", value1, value2, "auditStartTime");
            return (Criteria) this;
        }

        public Criteria andAuditIdIsNull() {
            addCriterion("audit_id is null");
            return (Criteria) this;
        }

        public Criteria andAuditIdIsNotNull() {
            addCriterion("audit_id is not null");
            return (Criteria) this;
        }

        public Criteria andAuditIdEqualTo(String value) {
            addCriterion("audit_id =", value, "auditId");
            return (Criteria) this;
        }

        public Criteria andAuditIdNotEqualTo(String value) {
            addCriterion("audit_id <>", value, "auditId");
            return (Criteria) this;
        }

        public Criteria andAuditIdGreaterThan(String value) {
            addCriterion("audit_id >", value, "auditId");
            return (Criteria) this;
        }

        public Criteria andAuditIdGreaterThanOrEqualTo(String value) {
            addCriterion("audit_id >=", value, "auditId");
            return (Criteria) this;
        }

        public Criteria andAuditIdLessThan(String value) {
            addCriterion("audit_id <", value, "auditId");
            return (Criteria) this;
        }

        public Criteria andAuditIdLessThanOrEqualTo(String value) {
            addCriterion("audit_id <=", value, "auditId");
            return (Criteria) this;
        }

        public Criteria andAuditIdLike(String value) {
            addCriterion("audit_id like", value, "auditId");
            return (Criteria) this;
        }

        public Criteria andAuditIdNotLike(String value) {
            addCriterion("audit_id not like", value, "auditId");
            return (Criteria) this;
        }

        public Criteria andAuditIdIn(List<String> values) {
            addCriterion("audit_id in", values, "auditId");
            return (Criteria) this;
        }

        public Criteria andAuditIdNotIn(List<String> values) {
            addCriterion("audit_id not in", values, "auditId");
            return (Criteria) this;
        }

        public Criteria andAuditIdBetween(String value1, String value2) {
            addCriterion("audit_id between", value1, value2, "auditId");
            return (Criteria) this;
        }

        public Criteria andAuditIdNotBetween(String value1, String value2) {
            addCriterion("audit_id not between", value1, value2, "auditId");
            return (Criteria) this;
        }

        public Criteria andRecordIdIsNull() {
            addCriterion("record_id is null");
            return (Criteria) this;
        }

        public Criteria andRecordIdIsNotNull() {
            addCriterion("record_id is not null");
            return (Criteria) this;
        }

        public Criteria andRecordIdEqualTo(String value) {
            addCriterion("record_id =", value, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdNotEqualTo(String value) {
            addCriterion("record_id <>", value, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdGreaterThan(String value) {
            addCriterion("record_id >", value, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdGreaterThanOrEqualTo(String value) {
            addCriterion("record_id >=", value, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdLessThan(String value) {
            addCriterion("record_id <", value, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdLessThanOrEqualTo(String value) {
            addCriterion("record_id <=", value, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdLike(String value) {
            addCriterion("record_id like", value, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdNotLike(String value) {
            addCriterion("record_id not like", value, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdIn(List<String> values) {
            addCriterion("record_id in", values, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdNotIn(List<String> values) {
            addCriterion("record_id not in", values, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdBetween(String value1, String value2) {
            addCriterion("record_id between", value1, value2, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdNotBetween(String value1, String value2) {
            addCriterion("record_id not between", value1, value2, "recordId");
            return (Criteria) this;
        }

        public Criteria andSceneTypeIsNull() {
            addCriterion("scene_type is null");
            return (Criteria) this;
        }

        public Criteria andSceneTypeIsNotNull() {
            addCriterion("scene_type is not null");
            return (Criteria) this;
        }

        public Criteria andSceneTypeEqualTo(Integer value) {
            addCriterion("scene_type =", value, "sceneType");
            return (Criteria) this;
        }

        public Criteria andSceneTypeNotEqualTo(Integer value) {
            addCriterion("scene_type <>", value, "sceneType");
            return (Criteria) this;
        }

        public Criteria andSceneTypeGreaterThan(Integer value) {
            addCriterion("scene_type >", value, "sceneType");
            return (Criteria) this;
        }

        public Criteria andSceneTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("scene_type >=", value, "sceneType");
            return (Criteria) this;
        }

        public Criteria andSceneTypeLessThan(Integer value) {
            addCriterion("scene_type <", value, "sceneType");
            return (Criteria) this;
        }

        public Criteria andSceneTypeLessThanOrEqualTo(Integer value) {
            addCriterion("scene_type <=", value, "sceneType");
            return (Criteria) this;
        }

        public Criteria andSceneTypeIn(List<Integer> values) {
            addCriterion("scene_type in", values, "sceneType");
            return (Criteria) this;
        }

        public Criteria andSceneTypeNotIn(List<Integer> values) {
            addCriterion("scene_type not in", values, "sceneType");
            return (Criteria) this;
        }

        public Criteria andSceneTypeBetween(Integer value1, Integer value2) {
            addCriterion("scene_type between", value1, value2, "sceneType");
            return (Criteria) this;
        }

        public Criteria andSceneTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("scene_type not between", value1, value2, "sceneType");
            return (Criteria) this;
        }

        public Criteria andSceneNameIsNull() {
            addCriterion("scene_name is null");
            return (Criteria) this;
        }

        public Criteria andSceneNameIsNotNull() {
            addCriterion("scene_name is not null");
            return (Criteria) this;
        }

        public Criteria andSceneNameEqualTo(String value) {
            addCriterion("scene_name =", value, "sceneName");
            return (Criteria) this;
        }

        public Criteria andSceneNameNotEqualTo(String value) {
            addCriterion("scene_name <>", value, "sceneName");
            return (Criteria) this;
        }

        public Criteria andSceneNameGreaterThan(String value) {
            addCriterion("scene_name >", value, "sceneName");
            return (Criteria) this;
        }

        public Criteria andSceneNameGreaterThanOrEqualTo(String value) {
            addCriterion("scene_name >=", value, "sceneName");
            return (Criteria) this;
        }

        public Criteria andSceneNameLessThan(String value) {
            addCriterion("scene_name <", value, "sceneName");
            return (Criteria) this;
        }

        public Criteria andSceneNameLessThanOrEqualTo(String value) {
            addCriterion("scene_name <=", value, "sceneName");
            return (Criteria) this;
        }

        public Criteria andSceneNameLike(String value) {
            addCriterion("scene_name like", value, "sceneName");
            return (Criteria) this;
        }

        public Criteria andSceneNameNotLike(String value) {
            addCriterion("scene_name not like", value, "sceneName");
            return (Criteria) this;
        }

        public Criteria andSceneNameIn(List<String> values) {
            addCriterion("scene_name in", values, "sceneName");
            return (Criteria) this;
        }

        public Criteria andSceneNameNotIn(List<String> values) {
            addCriterion("scene_name not in", values, "sceneName");
            return (Criteria) this;
        }

        public Criteria andSceneNameBetween(String value1, String value2) {
            addCriterion("scene_name between", value1, value2, "sceneName");
            return (Criteria) this;
        }

        public Criteria andSceneNameNotBetween(String value1, String value2) {
            addCriterion("scene_name not between", value1, value2, "sceneName");
            return (Criteria) this;
        }

        public Criteria andPushTimeIsNull() {
            addCriterion("push_time is null");
            return (Criteria) this;
        }

        public Criteria andPushTimeIsNotNull() {
            addCriterion("push_time is not null");
            return (Criteria) this;
        }

        public Criteria andPushTimeEqualTo(String value) {
            addCriterion("push_time =", value, "pushTime");
            return (Criteria) this;
        }

        public Criteria andPushTimeNotEqualTo(String value) {
            addCriterion("push_time <>", value, "pushTime");
            return (Criteria) this;
        }

        public Criteria andPushTimeGreaterThan(String value) {
            addCriterion("push_time >", value, "pushTime");
            return (Criteria) this;
        }

        public Criteria andPushTimeGreaterThanOrEqualTo(String value) {
            addCriterion("push_time >=", value, "pushTime");
            return (Criteria) this;
        }

        public Criteria andPushTimeLessThan(String value) {
            addCriterion("push_time <", value, "pushTime");
            return (Criteria) this;
        }

        public Criteria andPushTimeLessThanOrEqualTo(String value) {
            addCriterion("push_time <=", value, "pushTime");
            return (Criteria) this;
        }

        public Criteria andPushTimeLike(String value) {
            addCriterion("push_time like", value, "pushTime");
            return (Criteria) this;
        }

        public Criteria andPushTimeNotLike(String value) {
            addCriterion("push_time not like", value, "pushTime");
            return (Criteria) this;
        }

        public Criteria andPushTimeIn(List<String> values) {
            addCriterion("push_time in", values, "pushTime");
            return (Criteria) this;
        }

        public Criteria andPushTimeNotIn(List<String> values) {
            addCriterion("push_time not in", values, "pushTime");
            return (Criteria) this;
        }

        public Criteria andPushTimeBetween(String value1, String value2) {
            addCriterion("push_time between", value1, value2, "pushTime");
            return (Criteria) this;
        }

        public Criteria andPushTimeNotBetween(String value1, String value2) {
            addCriterion("push_time not between", value1, value2, "pushTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wavecenter_audit_record_full
     *
     * @mbg.generated do_not_delete_during_merge Wed Nov 13 10:33:43 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wavecenter_audit_record_full
     *
     * @mbg.generated Wed Nov 13 10:33:43 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}