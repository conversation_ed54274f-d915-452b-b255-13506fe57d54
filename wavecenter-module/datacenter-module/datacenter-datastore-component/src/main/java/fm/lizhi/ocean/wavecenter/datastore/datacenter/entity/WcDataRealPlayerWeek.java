package fm.lizhi.ocean.wavecenter.datastore.datacenter.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 主播实时数据表周
 *
 * @date 2025-06-10 02:04:46
 */
@Table(name = "`wavecenter_data_real_player_week`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcDataRealPlayerWeek {
    /**
     * 大数据清洗实时数据,主键只能为字符串
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private String id;

    /**
     * 业务 ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 主播ID
     */
    @Column(name= "`player_id`")
    private Long playerId;

    /**
     * 周开始日期 格式 YYYY-MM-DD
     */
    @Column(name= "`start_week_date`")
    private Date startWeekDate;

    /**
     * 周结束日期 格式 YYYY-MM-DD
     */
    @Column(name= "`end_week_date`")
    private Date endWeekDate;

    /**
     * 私信人数
     */
    @Column(name= "`chat_user_cnt`")
    private Integer chatUserCnt;

    /**
     * 私信回复人数(有效私信用户)
     */
    @Column(name= "`reply_chat_user_cnt`")
    private Integer replyChatUserCnt;

    /**
     * 私信回复新用户人数(有效私信新用户数)
     */
    @Column(name= "`reply_chat_new_user_cnt`")
    private Integer replyChatNewUserCnt;

    /**
     * 送礼人数(付费用户数)
     */
    @Column(name= "`gift_user_cnt`")
    private Integer giftUserCnt;

    /**
     * 送礼新用户人数(付费新用户数)
     */
    @Column(name= "`gift_new_user_cnt`")
    private Integer giftNewUserCnt;

    /**
     * 总收入
     */
    @Column(name= "`all_income`")
    private BigDecimal allIncome;

    /**
     * 上麦时长(分钟)
     */
    @Column(name= "`up_guest_dur`")
    private BigDecimal upGuestDur;

    /**
     * 新增粉丝数(累计新增粉丝)
     */
    @Column(name= "`new_fans_user_cnt`")
    private Integer newFansUserCnt;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", playerId=").append(playerId);
        sb.append(", startWeekDate=").append(startWeekDate);
        sb.append(", endWeekDate=").append(endWeekDate);
        sb.append(", chatUserCnt=").append(chatUserCnt);
        sb.append(", replyChatUserCnt=").append(replyChatUserCnt);
        sb.append(", replyChatNewUserCnt=").append(replyChatNewUserCnt);
        sb.append(", giftUserCnt=").append(giftUserCnt);
        sb.append(", giftNewUserCnt=").append(giftNewUserCnt);
        sb.append(", allIncome=").append(allIncome);
        sb.append(", upGuestDur=").append(upGuestDur);
        sb.append(", newFansUserCnt=").append(newFansUserCnt);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}