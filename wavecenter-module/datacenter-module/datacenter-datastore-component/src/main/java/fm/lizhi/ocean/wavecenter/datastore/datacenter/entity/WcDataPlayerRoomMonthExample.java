package fm.lizhi.ocean.wavecenter.datastore.datacenter.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WcDataPlayerRoomMonthExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_player_room_month
     *
     * @mbg.generated Mon Apr 29 14:59:17 CST 2024
     */
    public WcDataPlayerRoomMonthExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_player_room_month
     *
     * @mbg.generated Mon Apr 29 14:59:17 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_player_room_month
     *
     * @mbg.generated Mon Apr 29 14:59:17 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_player_room_month
     *
     * @mbg.generated Mon Apr 29 14:59:17 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_player_room_month
     *
     * @mbg.generated Mon Apr 29 14:59:17 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_player_room_month
     *
     * @mbg.generated Mon Apr 29 14:59:17 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_player_room_month
     *
     * @mbg.generated Mon Apr 29 14:59:17 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_player_room_month
     *
     * @mbg.generated Mon Apr 29 14:59:17 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_player_room_month
     *
     * @mbg.generated Mon Apr 29 14:59:17 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_player_room_month
     *
     * @mbg.generated Mon Apr 29 14:59:17 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_player_room_month
     *
     * @mbg.generated Mon Apr 29 14:59:17 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Class getEntityClass() {
        return WcDataPlayerRoomMonth.class;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wavecenter_data_player_room_month
     *
     * @mbg.generated Mon Apr 29 14:59:17 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(Integer value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(Integer value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(Integer value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(Integer value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(Integer value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<Integer> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<Integer> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(Integer value1, Integer value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(Integer value1, Integer value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andStatYearIsNull() {
            addCriterion("stat_year is null");
            return (Criteria) this;
        }

        public Criteria andStatYearIsNotNull() {
            addCriterion("stat_year is not null");
            return (Criteria) this;
        }

        public Criteria andStatYearEqualTo(Integer value) {
            addCriterion("stat_year =", value, "statYear");
            return (Criteria) this;
        }

        public Criteria andStatYearNotEqualTo(Integer value) {
            addCriterion("stat_year <>", value, "statYear");
            return (Criteria) this;
        }

        public Criteria andStatYearGreaterThan(Integer value) {
            addCriterion("stat_year >", value, "statYear");
            return (Criteria) this;
        }

        public Criteria andStatYearGreaterThanOrEqualTo(Integer value) {
            addCriterion("stat_year >=", value, "statYear");
            return (Criteria) this;
        }

        public Criteria andStatYearLessThan(Integer value) {
            addCriterion("stat_year <", value, "statYear");
            return (Criteria) this;
        }

        public Criteria andStatYearLessThanOrEqualTo(Integer value) {
            addCriterion("stat_year <=", value, "statYear");
            return (Criteria) this;
        }

        public Criteria andStatYearIn(List<Integer> values) {
            addCriterion("stat_year in", values, "statYear");
            return (Criteria) this;
        }

        public Criteria andStatYearNotIn(List<Integer> values) {
            addCriterion("stat_year not in", values, "statYear");
            return (Criteria) this;
        }

        public Criteria andStatYearBetween(Integer value1, Integer value2) {
            addCriterion("stat_year between", value1, value2, "statYear");
            return (Criteria) this;
        }

        public Criteria andStatYearNotBetween(Integer value1, Integer value2) {
            addCriterion("stat_year not between", value1, value2, "statYear");
            return (Criteria) this;
        }

        public Criteria andStatMonthIsNull() {
            addCriterion("stat_month is null");
            return (Criteria) this;
        }

        public Criteria andStatMonthIsNotNull() {
            addCriterion("stat_month is not null");
            return (Criteria) this;
        }

        public Criteria andStatMonthEqualTo(Integer value) {
            addCriterion("stat_month =", value, "statMonth");
            return (Criteria) this;
        }

        public Criteria andStatMonthNotEqualTo(Integer value) {
            addCriterion("stat_month <>", value, "statMonth");
            return (Criteria) this;
        }

        public Criteria andStatMonthGreaterThan(Integer value) {
            addCriterion("stat_month >", value, "statMonth");
            return (Criteria) this;
        }

        public Criteria andStatMonthGreaterThanOrEqualTo(Integer value) {
            addCriterion("stat_month >=", value, "statMonth");
            return (Criteria) this;
        }

        public Criteria andStatMonthLessThan(Integer value) {
            addCriterion("stat_month <", value, "statMonth");
            return (Criteria) this;
        }

        public Criteria andStatMonthLessThanOrEqualTo(Integer value) {
            addCriterion("stat_month <=", value, "statMonth");
            return (Criteria) this;
        }

        public Criteria andStatMonthIn(List<Integer> values) {
            addCriterion("stat_month in", values, "statMonth");
            return (Criteria) this;
        }

        public Criteria andStatMonthNotIn(List<Integer> values) {
            addCriterion("stat_month not in", values, "statMonth");
            return (Criteria) this;
        }

        public Criteria andStatMonthBetween(Integer value1, Integer value2) {
            addCriterion("stat_month between", value1, value2, "statMonth");
            return (Criteria) this;
        }

        public Criteria andStatMonthNotBetween(Integer value1, Integer value2) {
            addCriterion("stat_month not between", value1, value2, "statMonth");
            return (Criteria) this;
        }

        public Criteria andPlayerIdIsNull() {
            addCriterion("player_id is null");
            return (Criteria) this;
        }

        public Criteria andPlayerIdIsNotNull() {
            addCriterion("player_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlayerIdEqualTo(Long value) {
            addCriterion("player_id =", value, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdNotEqualTo(Long value) {
            addCriterion("player_id <>", value, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdGreaterThan(Long value) {
            addCriterion("player_id >", value, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdGreaterThanOrEqualTo(Long value) {
            addCriterion("player_id >=", value, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdLessThan(Long value) {
            addCriterion("player_id <", value, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdLessThanOrEqualTo(Long value) {
            addCriterion("player_id <=", value, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdIn(List<Long> values) {
            addCriterion("player_id in", values, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdNotIn(List<Long> values) {
            addCriterion("player_id not in", values, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdBetween(Long value1, Long value2) {
            addCriterion("player_id between", value1, value2, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdNotBetween(Long value1, Long value2) {
            addCriterion("player_id not between", value1, value2, "playerId");
            return (Criteria) this;
        }

        public Criteria andRoomIdIsNull() {
            addCriterion("room_id is null");
            return (Criteria) this;
        }

        public Criteria andRoomIdIsNotNull() {
            addCriterion("room_id is not null");
            return (Criteria) this;
        }

        public Criteria andRoomIdEqualTo(Long value) {
            addCriterion("room_id =", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotEqualTo(Long value) {
            addCriterion("room_id <>", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdGreaterThan(Long value) {
            addCriterion("room_id >", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdGreaterThanOrEqualTo(Long value) {
            addCriterion("room_id >=", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdLessThan(Long value) {
            addCriterion("room_id <", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdLessThanOrEqualTo(Long value) {
            addCriterion("room_id <=", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdIn(List<Long> values) {
            addCriterion("room_id in", values, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotIn(List<Long> values) {
            addCriterion("room_id not in", values, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdBetween(Long value1, Long value2) {
            addCriterion("room_id between", value1, value2, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotBetween(Long value1, Long value2) {
            addCriterion("room_id not between", value1, value2, "roomId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIsNull() {
            addCriterion("family_id is null");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIsNotNull() {
            addCriterion("family_id is not null");
            return (Criteria) this;
        }

        public Criteria andFamilyIdEqualTo(Long value) {
            addCriterion("family_id =", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotEqualTo(Long value) {
            addCriterion("family_id <>", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdGreaterThan(Long value) {
            addCriterion("family_id >", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("family_id >=", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdLessThan(Long value) {
            addCriterion("family_id <", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdLessThanOrEqualTo(Long value) {
            addCriterion("family_id <=", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIn(List<Long> values) {
            addCriterion("family_id in", values, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotIn(List<Long> values) {
            addCriterion("family_id not in", values, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdBetween(Long value1, Long value2) {
            addCriterion("family_id between", value1, value2, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotBetween(Long value1, Long value2) {
            addCriterion("family_id not between", value1, value2, "familyId");
            return (Criteria) this;
        }

        public Criteria andIncomeIsNull() {
            addCriterion("income is null");
            return (Criteria) this;
        }

        public Criteria andIncomeIsNotNull() {
            addCriterion("income is not null");
            return (Criteria) this;
        }

        public Criteria andIncomeEqualTo(BigDecimal value) {
            addCriterion("income =", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeNotEqualTo(BigDecimal value) {
            addCriterion("income <>", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeGreaterThan(BigDecimal value) {
            addCriterion("income >", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("income >=", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeLessThan(BigDecimal value) {
            addCriterion("income <", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("income <=", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeIn(List<BigDecimal> values) {
            addCriterion("income in", values, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeNotIn(List<BigDecimal> values) {
            addCriterion("income not in", values, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income between", value1, value2, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income not between", value1, value2, "income");
            return (Criteria) this;
        }

        public Criteria andCharmIsNull() {
            addCriterion("charm is null");
            return (Criteria) this;
        }

        public Criteria andCharmIsNotNull() {
            addCriterion("charm is not null");
            return (Criteria) this;
        }

        public Criteria andCharmEqualTo(Integer value) {
            addCriterion("charm =", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmNotEqualTo(Integer value) {
            addCriterion("charm <>", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmGreaterThan(Integer value) {
            addCriterion("charm >", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmGreaterThanOrEqualTo(Integer value) {
            addCriterion("charm >=", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmLessThan(Integer value) {
            addCriterion("charm <", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmLessThanOrEqualTo(Integer value) {
            addCriterion("charm <=", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmIn(List<Integer> values) {
            addCriterion("charm in", values, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmNotIn(List<Integer> values) {
            addCriterion("charm not in", values, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmBetween(Integer value1, Integer value2) {
            addCriterion("charm between", value1, value2, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmNotBetween(Integer value1, Integer value2) {
            addCriterion("charm not between", value1, value2, "charm");
            return (Criteria) this;
        }

        public Criteria andUpGuestDurIsNull() {
            addCriterion("up_guest_dur is null");
            return (Criteria) this;
        }

        public Criteria andUpGuestDurIsNotNull() {
            addCriterion("up_guest_dur is not null");
            return (Criteria) this;
        }

        public Criteria andUpGuestDurEqualTo(BigDecimal value) {
            addCriterion("up_guest_dur =", value, "upGuestDur");
            return (Criteria) this;
        }

        public Criteria andUpGuestDurNotEqualTo(BigDecimal value) {
            addCriterion("up_guest_dur <>", value, "upGuestDur");
            return (Criteria) this;
        }

        public Criteria andUpGuestDurGreaterThan(BigDecimal value) {
            addCriterion("up_guest_dur >", value, "upGuestDur");
            return (Criteria) this;
        }

        public Criteria andUpGuestDurGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("up_guest_dur >=", value, "upGuestDur");
            return (Criteria) this;
        }

        public Criteria andUpGuestDurLessThan(BigDecimal value) {
            addCriterion("up_guest_dur <", value, "upGuestDur");
            return (Criteria) this;
        }

        public Criteria andUpGuestDurLessThanOrEqualTo(BigDecimal value) {
            addCriterion("up_guest_dur <=", value, "upGuestDur");
            return (Criteria) this;
        }

        public Criteria andUpGuestDurIn(List<BigDecimal> values) {
            addCriterion("up_guest_dur in", values, "upGuestDur");
            return (Criteria) this;
        }

        public Criteria andUpGuestDurNotIn(List<BigDecimal> values) {
            addCriterion("up_guest_dur not in", values, "upGuestDur");
            return (Criteria) this;
        }

        public Criteria andUpGuestDurBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("up_guest_dur between", value1, value2, "upGuestDur");
            return (Criteria) this;
        }

        public Criteria andUpGuestDurNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("up_guest_dur not between", value1, value2, "upGuestDur");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntIsNull() {
            addCriterion("gift_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntIsNotNull() {
            addCriterion("gift_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntEqualTo(Integer value) {
            addCriterion("gift_user_cnt =", value, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntNotEqualTo(Integer value) {
            addCriterion("gift_user_cnt <>", value, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntGreaterThan(Integer value) {
            addCriterion("gift_user_cnt >", value, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("gift_user_cnt >=", value, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntLessThan(Integer value) {
            addCriterion("gift_user_cnt <", value, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("gift_user_cnt <=", value, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntIn(List<Integer> values) {
            addCriterion("gift_user_cnt in", values, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntNotIn(List<Integer> values) {
            addCriterion("gift_user_cnt not in", values, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntBetween(Integer value1, Integer value2) {
            addCriterion("gift_user_cnt between", value1, value2, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("gift_user_cnt not between", value1, value2, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserPriceIsNull() {
            addCriterion("gift_user_price is null");
            return (Criteria) this;
        }

        public Criteria andGiftUserPriceIsNotNull() {
            addCriterion("gift_user_price is not null");
            return (Criteria) this;
        }

        public Criteria andGiftUserPriceEqualTo(BigDecimal value) {
            addCriterion("gift_user_price =", value, "giftUserPrice");
            return (Criteria) this;
        }

        public Criteria andGiftUserPriceNotEqualTo(BigDecimal value) {
            addCriterion("gift_user_price <>", value, "giftUserPrice");
            return (Criteria) this;
        }

        public Criteria andGiftUserPriceGreaterThan(BigDecimal value) {
            addCriterion("gift_user_price >", value, "giftUserPrice");
            return (Criteria) this;
        }

        public Criteria andGiftUserPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("gift_user_price >=", value, "giftUserPrice");
            return (Criteria) this;
        }

        public Criteria andGiftUserPriceLessThan(BigDecimal value) {
            addCriterion("gift_user_price <", value, "giftUserPrice");
            return (Criteria) this;
        }

        public Criteria andGiftUserPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("gift_user_price <=", value, "giftUserPrice");
            return (Criteria) this;
        }

        public Criteria andGiftUserPriceIn(List<BigDecimal> values) {
            addCriterion("gift_user_price in", values, "giftUserPrice");
            return (Criteria) this;
        }

        public Criteria andGiftUserPriceNotIn(List<BigDecimal> values) {
            addCriterion("gift_user_price not in", values, "giftUserPrice");
            return (Criteria) this;
        }

        public Criteria andGiftUserPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("gift_user_price between", value1, value2, "giftUserPrice");
            return (Criteria) this;
        }

        public Criteria andGiftUserPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("gift_user_price not between", value1, value2, "giftUserPrice");
            return (Criteria) this;
        }

        public Criteria andChatUserCntIsNull() {
            addCriterion("chat_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andChatUserCntIsNotNull() {
            addCriterion("chat_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andChatUserCntEqualTo(Integer value) {
            addCriterion("chat_user_cnt =", value, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntNotEqualTo(Integer value) {
            addCriterion("chat_user_cnt <>", value, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntGreaterThan(Integer value) {
            addCriterion("chat_user_cnt >", value, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("chat_user_cnt >=", value, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntLessThan(Integer value) {
            addCriterion("chat_user_cnt <", value, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("chat_user_cnt <=", value, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntIn(List<Integer> values) {
            addCriterion("chat_user_cnt in", values, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntNotIn(List<Integer> values) {
            addCriterion("chat_user_cnt not in", values, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntBetween(Integer value1, Integer value2) {
            addCriterion("chat_user_cnt between", value1, value2, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("chat_user_cnt not between", value1, value2, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntIsNull() {
            addCriterion("reply_chat_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntIsNotNull() {
            addCriterion("reply_chat_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntEqualTo(Integer value) {
            addCriterion("reply_chat_user_cnt =", value, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntNotEqualTo(Integer value) {
            addCriterion("reply_chat_user_cnt <>", value, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntGreaterThan(Integer value) {
            addCriterion("reply_chat_user_cnt >", value, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("reply_chat_user_cnt >=", value, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntLessThan(Integer value) {
            addCriterion("reply_chat_user_cnt <", value, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("reply_chat_user_cnt <=", value, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntIn(List<Integer> values) {
            addCriterion("reply_chat_user_cnt in", values, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntNotIn(List<Integer> values) {
            addCriterion("reply_chat_user_cnt not in", values, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntBetween(Integer value1, Integer value2) {
            addCriterion("reply_chat_user_cnt between", value1, value2, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("reply_chat_user_cnt not between", value1, value2, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomUserCntIsNull() {
            addCriterion("chat_enter_room_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomUserCntIsNotNull() {
            addCriterion("chat_enter_room_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomUserCntEqualTo(Integer value) {
            addCriterion("chat_enter_room_user_cnt =", value, "chatEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomUserCntNotEqualTo(Integer value) {
            addCriterion("chat_enter_room_user_cnt <>", value, "chatEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomUserCntGreaterThan(Integer value) {
            addCriterion("chat_enter_room_user_cnt >", value, "chatEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("chat_enter_room_user_cnt >=", value, "chatEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomUserCntLessThan(Integer value) {
            addCriterion("chat_enter_room_user_cnt <", value, "chatEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("chat_enter_room_user_cnt <=", value, "chatEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomUserCntIn(List<Integer> values) {
            addCriterion("chat_enter_room_user_cnt in", values, "chatEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomUserCntNotIn(List<Integer> values) {
            addCriterion("chat_enter_room_user_cnt not in", values, "chatEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomUserCntBetween(Integer value1, Integer value2) {
            addCriterion("chat_enter_room_user_cnt between", value1, value2, "chatEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("chat_enter_room_user_cnt not between", value1, value2, "chatEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatGiftUserCntIsNull() {
            addCriterion("chat_gift_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andChatGiftUserCntIsNotNull() {
            addCriterion("chat_gift_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andChatGiftUserCntEqualTo(Integer value) {
            addCriterion("chat_gift_user_cnt =", value, "chatGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatGiftUserCntNotEqualTo(Integer value) {
            addCriterion("chat_gift_user_cnt <>", value, "chatGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatGiftUserCntGreaterThan(Integer value) {
            addCriterion("chat_gift_user_cnt >", value, "chatGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatGiftUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("chat_gift_user_cnt >=", value, "chatGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatGiftUserCntLessThan(Integer value) {
            addCriterion("chat_gift_user_cnt <", value, "chatGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatGiftUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("chat_gift_user_cnt <=", value, "chatGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatGiftUserCntIn(List<Integer> values) {
            addCriterion("chat_gift_user_cnt in", values, "chatGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatGiftUserCntNotIn(List<Integer> values) {
            addCriterion("chat_gift_user_cnt not in", values, "chatGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatGiftUserCntBetween(Integer value1, Integer value2) {
            addCriterion("chat_gift_user_cnt between", value1, value2, "chatGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatGiftUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("chat_gift_user_cnt not between", value1, value2, "chatGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateIsNull() {
            addCriterion("reply_chat_rate is null");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateIsNotNull() {
            addCriterion("reply_chat_rate is not null");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateEqualTo(BigDecimal value) {
            addCriterion("reply_chat_rate =", value, "replyChatRate");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateNotEqualTo(BigDecimal value) {
            addCriterion("reply_chat_rate <>", value, "replyChatRate");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateGreaterThan(BigDecimal value) {
            addCriterion("reply_chat_rate >", value, "replyChatRate");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("reply_chat_rate >=", value, "replyChatRate");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateLessThan(BigDecimal value) {
            addCriterion("reply_chat_rate <", value, "replyChatRate");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("reply_chat_rate <=", value, "replyChatRate");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateIn(List<BigDecimal> values) {
            addCriterion("reply_chat_rate in", values, "replyChatRate");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateNotIn(List<BigDecimal> values) {
            addCriterion("reply_chat_rate not in", values, "replyChatRate");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("reply_chat_rate between", value1, value2, "replyChatRate");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("reply_chat_rate not between", value1, value2, "replyChatRate");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomRateIsNull() {
            addCriterion("chat_enter_room_rate is null");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomRateIsNotNull() {
            addCriterion("chat_enter_room_rate is not null");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomRateEqualTo(BigDecimal value) {
            addCriterion("chat_enter_room_rate =", value, "chatEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomRateNotEqualTo(BigDecimal value) {
            addCriterion("chat_enter_room_rate <>", value, "chatEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomRateGreaterThan(BigDecimal value) {
            addCriterion("chat_enter_room_rate >", value, "chatEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("chat_enter_room_rate >=", value, "chatEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomRateLessThan(BigDecimal value) {
            addCriterion("chat_enter_room_rate <", value, "chatEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("chat_enter_room_rate <=", value, "chatEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomRateIn(List<BigDecimal> values) {
            addCriterion("chat_enter_room_rate in", values, "chatEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomRateNotIn(List<BigDecimal> values) {
            addCriterion("chat_enter_room_rate not in", values, "chatEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("chat_enter_room_rate between", value1, value2, "chatEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andChatEnterRoomRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("chat_enter_room_rate not between", value1, value2, "chatEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andChatGiftRateIsNull() {
            addCriterion("chat_gift_rate is null");
            return (Criteria) this;
        }

        public Criteria andChatGiftRateIsNotNull() {
            addCriterion("chat_gift_rate is not null");
            return (Criteria) this;
        }

        public Criteria andChatGiftRateEqualTo(BigDecimal value) {
            addCriterion("chat_gift_rate =", value, "chatGiftRate");
            return (Criteria) this;
        }

        public Criteria andChatGiftRateNotEqualTo(BigDecimal value) {
            addCriterion("chat_gift_rate <>", value, "chatGiftRate");
            return (Criteria) this;
        }

        public Criteria andChatGiftRateGreaterThan(BigDecimal value) {
            addCriterion("chat_gift_rate >", value, "chatGiftRate");
            return (Criteria) this;
        }

        public Criteria andChatGiftRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("chat_gift_rate >=", value, "chatGiftRate");
            return (Criteria) this;
        }

        public Criteria andChatGiftRateLessThan(BigDecimal value) {
            addCriterion("chat_gift_rate <", value, "chatGiftRate");
            return (Criteria) this;
        }

        public Criteria andChatGiftRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("chat_gift_rate <=", value, "chatGiftRate");
            return (Criteria) this;
        }

        public Criteria andChatGiftRateIn(List<BigDecimal> values) {
            addCriterion("chat_gift_rate in", values, "chatGiftRate");
            return (Criteria) this;
        }

        public Criteria andChatGiftRateNotIn(List<BigDecimal> values) {
            addCriterion("chat_gift_rate not in", values, "chatGiftRate");
            return (Criteria) this;
        }

        public Criteria andChatGiftRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("chat_gift_rate between", value1, value2, "chatGiftRate");
            return (Criteria) this;
        }

        public Criteria andChatGiftRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("chat_gift_rate not between", value1, value2, "chatGiftRate");
            return (Criteria) this;
        }

        public Criteria andInviteUserCntIsNull() {
            addCriterion("invite_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andInviteUserCntIsNotNull() {
            addCriterion("invite_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andInviteUserCntEqualTo(Integer value) {
            addCriterion("invite_user_cnt =", value, "inviteUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteUserCntNotEqualTo(Integer value) {
            addCriterion("invite_user_cnt <>", value, "inviteUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteUserCntGreaterThan(Integer value) {
            addCriterion("invite_user_cnt >", value, "inviteUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("invite_user_cnt >=", value, "inviteUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteUserCntLessThan(Integer value) {
            addCriterion("invite_user_cnt <", value, "inviteUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("invite_user_cnt <=", value, "inviteUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteUserCntIn(List<Integer> values) {
            addCriterion("invite_user_cnt in", values, "inviteUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteUserCntNotIn(List<Integer> values) {
            addCriterion("invite_user_cnt not in", values, "inviteUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteUserCntBetween(Integer value1, Integer value2) {
            addCriterion("invite_user_cnt between", value1, value2, "inviteUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("invite_user_cnt not between", value1, value2, "inviteUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomUserCntIsNull() {
            addCriterion("invite_enter_room_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomUserCntIsNotNull() {
            addCriterion("invite_enter_room_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomUserCntEqualTo(Integer value) {
            addCriterion("invite_enter_room_user_cnt =", value, "inviteEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomUserCntNotEqualTo(Integer value) {
            addCriterion("invite_enter_room_user_cnt <>", value, "inviteEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomUserCntGreaterThan(Integer value) {
            addCriterion("invite_enter_room_user_cnt >", value, "inviteEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("invite_enter_room_user_cnt >=", value, "inviteEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomUserCntLessThan(Integer value) {
            addCriterion("invite_enter_room_user_cnt <", value, "inviteEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("invite_enter_room_user_cnt <=", value, "inviteEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomUserCntIn(List<Integer> values) {
            addCriterion("invite_enter_room_user_cnt in", values, "inviteEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomUserCntNotIn(List<Integer> values) {
            addCriterion("invite_enter_room_user_cnt not in", values, "inviteEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomUserCntBetween(Integer value1, Integer value2) {
            addCriterion("invite_enter_room_user_cnt between", value1, value2, "inviteEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("invite_enter_room_user_cnt not between", value1, value2, "inviteEnterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteGiftUserCntIsNull() {
            addCriterion("invite_gift_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andInviteGiftUserCntIsNotNull() {
            addCriterion("invite_gift_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andInviteGiftUserCntEqualTo(Integer value) {
            addCriterion("invite_gift_user_cnt =", value, "inviteGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteGiftUserCntNotEqualTo(Integer value) {
            addCriterion("invite_gift_user_cnt <>", value, "inviteGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteGiftUserCntGreaterThan(Integer value) {
            addCriterion("invite_gift_user_cnt >", value, "inviteGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteGiftUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("invite_gift_user_cnt >=", value, "inviteGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteGiftUserCntLessThan(Integer value) {
            addCriterion("invite_gift_user_cnt <", value, "inviteGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteGiftUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("invite_gift_user_cnt <=", value, "inviteGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteGiftUserCntIn(List<Integer> values) {
            addCriterion("invite_gift_user_cnt in", values, "inviteGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteGiftUserCntNotIn(List<Integer> values) {
            addCriterion("invite_gift_user_cnt not in", values, "inviteGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteGiftUserCntBetween(Integer value1, Integer value2) {
            addCriterion("invite_gift_user_cnt between", value1, value2, "inviteGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteGiftUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("invite_gift_user_cnt not between", value1, value2, "inviteGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomRateIsNull() {
            addCriterion("invite_enter_room_rate is null");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomRateIsNotNull() {
            addCriterion("invite_enter_room_rate is not null");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomRateEqualTo(BigDecimal value) {
            addCriterion("invite_enter_room_rate =", value, "inviteEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomRateNotEqualTo(BigDecimal value) {
            addCriterion("invite_enter_room_rate <>", value, "inviteEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomRateGreaterThan(BigDecimal value) {
            addCriterion("invite_enter_room_rate >", value, "inviteEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("invite_enter_room_rate >=", value, "inviteEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomRateLessThan(BigDecimal value) {
            addCriterion("invite_enter_room_rate <", value, "inviteEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("invite_enter_room_rate <=", value, "inviteEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomRateIn(List<BigDecimal> values) {
            addCriterion("invite_enter_room_rate in", values, "inviteEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomRateNotIn(List<BigDecimal> values) {
            addCriterion("invite_enter_room_rate not in", values, "inviteEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("invite_enter_room_rate between", value1, value2, "inviteEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andInviteEnterRoomRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("invite_enter_room_rate not between", value1, value2, "inviteEnterRoomRate");
            return (Criteria) this;
        }

        public Criteria andInviteGiftRateIsNull() {
            addCriterion("invite_gift_rate is null");
            return (Criteria) this;
        }

        public Criteria andInviteGiftRateIsNotNull() {
            addCriterion("invite_gift_rate is not null");
            return (Criteria) this;
        }

        public Criteria andInviteGiftRateEqualTo(BigDecimal value) {
            addCriterion("invite_gift_rate =", value, "inviteGiftRate");
            return (Criteria) this;
        }

        public Criteria andInviteGiftRateNotEqualTo(BigDecimal value) {
            addCriterion("invite_gift_rate <>", value, "inviteGiftRate");
            return (Criteria) this;
        }

        public Criteria andInviteGiftRateGreaterThan(BigDecimal value) {
            addCriterion("invite_gift_rate >", value, "inviteGiftRate");
            return (Criteria) this;
        }

        public Criteria andInviteGiftRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("invite_gift_rate >=", value, "inviteGiftRate");
            return (Criteria) this;
        }

        public Criteria andInviteGiftRateLessThan(BigDecimal value) {
            addCriterion("invite_gift_rate <", value, "inviteGiftRate");
            return (Criteria) this;
        }

        public Criteria andInviteGiftRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("invite_gift_rate <=", value, "inviteGiftRate");
            return (Criteria) this;
        }

        public Criteria andInviteGiftRateIn(List<BigDecimal> values) {
            addCriterion("invite_gift_rate in", values, "inviteGiftRate");
            return (Criteria) this;
        }

        public Criteria andInviteGiftRateNotIn(List<BigDecimal> values) {
            addCriterion("invite_gift_rate not in", values, "inviteGiftRate");
            return (Criteria) this;
        }

        public Criteria andInviteGiftRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("invite_gift_rate between", value1, value2, "inviteGiftRate");
            return (Criteria) this;
        }

        public Criteria andInviteGiftRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("invite_gift_rate not between", value1, value2, "inviteGiftRate");
            return (Criteria) this;
        }

        public Criteria andFansUserCntIsNull() {
            addCriterion("fans_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andFansUserCntIsNotNull() {
            addCriterion("fans_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andFansUserCntEqualTo(Integer value) {
            addCriterion("fans_user_cnt =", value, "fansUserCnt");
            return (Criteria) this;
        }

        public Criteria andFansUserCntNotEqualTo(Integer value) {
            addCriterion("fans_user_cnt <>", value, "fansUserCnt");
            return (Criteria) this;
        }

        public Criteria andFansUserCntGreaterThan(Integer value) {
            addCriterion("fans_user_cnt >", value, "fansUserCnt");
            return (Criteria) this;
        }

        public Criteria andFansUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("fans_user_cnt >=", value, "fansUserCnt");
            return (Criteria) this;
        }

        public Criteria andFansUserCntLessThan(Integer value) {
            addCriterion("fans_user_cnt <", value, "fansUserCnt");
            return (Criteria) this;
        }

        public Criteria andFansUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("fans_user_cnt <=", value, "fansUserCnt");
            return (Criteria) this;
        }

        public Criteria andFansUserCntIn(List<Integer> values) {
            addCriterion("fans_user_cnt in", values, "fansUserCnt");
            return (Criteria) this;
        }

        public Criteria andFansUserCntNotIn(List<Integer> values) {
            addCriterion("fans_user_cnt not in", values, "fansUserCnt");
            return (Criteria) this;
        }

        public Criteria andFansUserCntBetween(Integer value1, Integer value2) {
            addCriterion("fans_user_cnt between", value1, value2, "fansUserCnt");
            return (Criteria) this;
        }

        public Criteria andFansUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("fans_user_cnt not between", value1, value2, "fansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntIsNull() {
            addCriterion("new_fans_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntIsNotNull() {
            addCriterion("new_fans_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntEqualTo(Integer value) {
            addCriterion("new_fans_user_cnt =", value, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntNotEqualTo(Integer value) {
            addCriterion("new_fans_user_cnt <>", value, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntGreaterThan(Integer value) {
            addCriterion("new_fans_user_cnt >", value, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("new_fans_user_cnt >=", value, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntLessThan(Integer value) {
            addCriterion("new_fans_user_cnt <", value, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("new_fans_user_cnt <=", value, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntIn(List<Integer> values) {
            addCriterion("new_fans_user_cnt in", values, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntNotIn(List<Integer> values) {
            addCriterion("new_fans_user_cnt not in", values, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntBetween(Integer value1, Integer value2) {
            addCriterion("new_fans_user_cnt between", value1, value2, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("new_fans_user_cnt not between", value1, value2, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andFansGiftIncomeIsNull() {
            addCriterion("fans_gift_income is null");
            return (Criteria) this;
        }

        public Criteria andFansGiftIncomeIsNotNull() {
            addCriterion("fans_gift_income is not null");
            return (Criteria) this;
        }

        public Criteria andFansGiftIncomeEqualTo(BigDecimal value) {
            addCriterion("fans_gift_income =", value, "fansGiftIncome");
            return (Criteria) this;
        }

        public Criteria andFansGiftIncomeNotEqualTo(BigDecimal value) {
            addCriterion("fans_gift_income <>", value, "fansGiftIncome");
            return (Criteria) this;
        }

        public Criteria andFansGiftIncomeGreaterThan(BigDecimal value) {
            addCriterion("fans_gift_income >", value, "fansGiftIncome");
            return (Criteria) this;
        }

        public Criteria andFansGiftIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("fans_gift_income >=", value, "fansGiftIncome");
            return (Criteria) this;
        }

        public Criteria andFansGiftIncomeLessThan(BigDecimal value) {
            addCriterion("fans_gift_income <", value, "fansGiftIncome");
            return (Criteria) this;
        }

        public Criteria andFansGiftIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("fans_gift_income <=", value, "fansGiftIncome");
            return (Criteria) this;
        }

        public Criteria andFansGiftIncomeIn(List<BigDecimal> values) {
            addCriterion("fans_gift_income in", values, "fansGiftIncome");
            return (Criteria) this;
        }

        public Criteria andFansGiftIncomeNotIn(List<BigDecimal> values) {
            addCriterion("fans_gift_income not in", values, "fansGiftIncome");
            return (Criteria) this;
        }

        public Criteria andFansGiftIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("fans_gift_income between", value1, value2, "fansGiftIncome");
            return (Criteria) this;
        }

        public Criteria andFansGiftIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("fans_gift_income not between", value1, value2, "fansGiftIncome");
            return (Criteria) this;
        }

        public Criteria andFansGiftUserCntIsNull() {
            addCriterion("fans_gift_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andFansGiftUserCntIsNotNull() {
            addCriterion("fans_gift_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andFansGiftUserCntEqualTo(Integer value) {
            addCriterion("fans_gift_user_cnt =", value, "fansGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andFansGiftUserCntNotEqualTo(Integer value) {
            addCriterion("fans_gift_user_cnt <>", value, "fansGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andFansGiftUserCntGreaterThan(Integer value) {
            addCriterion("fans_gift_user_cnt >", value, "fansGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andFansGiftUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("fans_gift_user_cnt >=", value, "fansGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andFansGiftUserCntLessThan(Integer value) {
            addCriterion("fans_gift_user_cnt <", value, "fansGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andFansGiftUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("fans_gift_user_cnt <=", value, "fansGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andFansGiftUserCntIn(List<Integer> values) {
            addCriterion("fans_gift_user_cnt in", values, "fansGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andFansGiftUserCntNotIn(List<Integer> values) {
            addCriterion("fans_gift_user_cnt not in", values, "fansGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andFansGiftUserCntBetween(Integer value1, Integer value2) {
            addCriterion("fans_gift_user_cnt between", value1, value2, "fansGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andFansGiftUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("fans_gift_user_cnt not between", value1, value2, "fansGiftUserCnt");
            return (Criteria) this;
        }

        public Criteria andFansGiftUserPriceIsNull() {
            addCriterion("fans_gift_user_price is null");
            return (Criteria) this;
        }

        public Criteria andFansGiftUserPriceIsNotNull() {
            addCriterion("fans_gift_user_price is not null");
            return (Criteria) this;
        }

        public Criteria andFansGiftUserPriceEqualTo(BigDecimal value) {
            addCriterion("fans_gift_user_price =", value, "fansGiftUserPrice");
            return (Criteria) this;
        }

        public Criteria andFansGiftUserPriceNotEqualTo(BigDecimal value) {
            addCriterion("fans_gift_user_price <>", value, "fansGiftUserPrice");
            return (Criteria) this;
        }

        public Criteria andFansGiftUserPriceGreaterThan(BigDecimal value) {
            addCriterion("fans_gift_user_price >", value, "fansGiftUserPrice");
            return (Criteria) this;
        }

        public Criteria andFansGiftUserPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("fans_gift_user_price >=", value, "fansGiftUserPrice");
            return (Criteria) this;
        }

        public Criteria andFansGiftUserPriceLessThan(BigDecimal value) {
            addCriterion("fans_gift_user_price <", value, "fansGiftUserPrice");
            return (Criteria) this;
        }

        public Criteria andFansGiftUserPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("fans_gift_user_price <=", value, "fansGiftUserPrice");
            return (Criteria) this;
        }

        public Criteria andFansGiftUserPriceIn(List<BigDecimal> values) {
            addCriterion("fans_gift_user_price in", values, "fansGiftUserPrice");
            return (Criteria) this;
        }

        public Criteria andFansGiftUserPriceNotIn(List<BigDecimal> values) {
            addCriterion("fans_gift_user_price not in", values, "fansGiftUserPrice");
            return (Criteria) this;
        }

        public Criteria andFansGiftUserPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("fans_gift_user_price between", value1, value2, "fansGiftUserPrice");
            return (Criteria) this;
        }

        public Criteria andFansGiftUserPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("fans_gift_user_price not between", value1, value2, "fansGiftUserPrice");
            return (Criteria) this;
        }

        public Criteria andRoomSignPlayerCntIsNull() {
            addCriterion("room_sign_player_cnt is null");
            return (Criteria) this;
        }

        public Criteria andRoomSignPlayerCntIsNotNull() {
            addCriterion("room_sign_player_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andRoomSignPlayerCntEqualTo(Integer value) {
            addCriterion("room_sign_player_cnt =", value, "roomSignPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andRoomSignPlayerCntNotEqualTo(Integer value) {
            addCriterion("room_sign_player_cnt <>", value, "roomSignPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andRoomSignPlayerCntGreaterThan(Integer value) {
            addCriterion("room_sign_player_cnt >", value, "roomSignPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andRoomSignPlayerCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("room_sign_player_cnt >=", value, "roomSignPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andRoomSignPlayerCntLessThan(Integer value) {
            addCriterion("room_sign_player_cnt <", value, "roomSignPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andRoomSignPlayerCntLessThanOrEqualTo(Integer value) {
            addCriterion("room_sign_player_cnt <=", value, "roomSignPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andRoomSignPlayerCntIn(List<Integer> values) {
            addCriterion("room_sign_player_cnt in", values, "roomSignPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andRoomSignPlayerCntNotIn(List<Integer> values) {
            addCriterion("room_sign_player_cnt not in", values, "roomSignPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andRoomSignPlayerCntBetween(Integer value1, Integer value2) {
            addCriterion("room_sign_player_cnt between", value1, value2, "roomSignPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andRoomSignPlayerCntNotBetween(Integer value1, Integer value2) {
            addCriterion("room_sign_player_cnt not between", value1, value2, "roomSignPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomeRankRoomIsNull() {
            addCriterion("income_rank_room is null");
            return (Criteria) this;
        }

        public Criteria andIncomeRankRoomIsNotNull() {
            addCriterion("income_rank_room is not null");
            return (Criteria) this;
        }

        public Criteria andIncomeRankRoomEqualTo(Integer value) {
            addCriterion("income_rank_room =", value, "incomeRankRoom");
            return (Criteria) this;
        }

        public Criteria andIncomeRankRoomNotEqualTo(Integer value) {
            addCriterion("income_rank_room <>", value, "incomeRankRoom");
            return (Criteria) this;
        }

        public Criteria andIncomeRankRoomGreaterThan(Integer value) {
            addCriterion("income_rank_room >", value, "incomeRankRoom");
            return (Criteria) this;
        }

        public Criteria andIncomeRankRoomGreaterThanOrEqualTo(Integer value) {
            addCriterion("income_rank_room >=", value, "incomeRankRoom");
            return (Criteria) this;
        }

        public Criteria andIncomeRankRoomLessThan(Integer value) {
            addCriterion("income_rank_room <", value, "incomeRankRoom");
            return (Criteria) this;
        }

        public Criteria andIncomeRankRoomLessThanOrEqualTo(Integer value) {
            addCriterion("income_rank_room <=", value, "incomeRankRoom");
            return (Criteria) this;
        }

        public Criteria andIncomeRankRoomIn(List<Integer> values) {
            addCriterion("income_rank_room in", values, "incomeRankRoom");
            return (Criteria) this;
        }

        public Criteria andIncomeRankRoomNotIn(List<Integer> values) {
            addCriterion("income_rank_room not in", values, "incomeRankRoom");
            return (Criteria) this;
        }

        public Criteria andIncomeRankRoomBetween(Integer value1, Integer value2) {
            addCriterion("income_rank_room between", value1, value2, "incomeRankRoom");
            return (Criteria) this;
        }

        public Criteria andIncomeRankRoomNotBetween(Integer value1, Integer value2) {
            addCriterion("income_rank_room not between", value1, value2, "incomeRankRoom");
            return (Criteria) this;
        }

        public Criteria andIncomeRankFamilyIsNull() {
            addCriterion("income_rank_family is null");
            return (Criteria) this;
        }

        public Criteria andIncomeRankFamilyIsNotNull() {
            addCriterion("income_rank_family is not null");
            return (Criteria) this;
        }

        public Criteria andIncomeRankFamilyEqualTo(Integer value) {
            addCriterion("income_rank_family =", value, "incomeRankFamily");
            return (Criteria) this;
        }

        public Criteria andIncomeRankFamilyNotEqualTo(Integer value) {
            addCriterion("income_rank_family <>", value, "incomeRankFamily");
            return (Criteria) this;
        }

        public Criteria andIncomeRankFamilyGreaterThan(Integer value) {
            addCriterion("income_rank_family >", value, "incomeRankFamily");
            return (Criteria) this;
        }

        public Criteria andIncomeRankFamilyGreaterThanOrEqualTo(Integer value) {
            addCriterion("income_rank_family >=", value, "incomeRankFamily");
            return (Criteria) this;
        }

        public Criteria andIncomeRankFamilyLessThan(Integer value) {
            addCriterion("income_rank_family <", value, "incomeRankFamily");
            return (Criteria) this;
        }

        public Criteria andIncomeRankFamilyLessThanOrEqualTo(Integer value) {
            addCriterion("income_rank_family <=", value, "incomeRankFamily");
            return (Criteria) this;
        }

        public Criteria andIncomeRankFamilyIn(List<Integer> values) {
            addCriterion("income_rank_family in", values, "incomeRankFamily");
            return (Criteria) this;
        }

        public Criteria andIncomeRankFamilyNotIn(List<Integer> values) {
            addCriterion("income_rank_family not in", values, "incomeRankFamily");
            return (Criteria) this;
        }

        public Criteria andIncomeRankFamilyBetween(Integer value1, Integer value2) {
            addCriterion("income_rank_family between", value1, value2, "incomeRankFamily");
            return (Criteria) this;
        }

        public Criteria andIncomeRankFamilyNotBetween(Integer value1, Integer value2) {
            addCriterion("income_rank_family not between", value1, value2, "incomeRankFamily");
            return (Criteria) this;
        }

        public Criteria andCharmRankRoomIsNull() {
            addCriterion("charm_rank_room is null");
            return (Criteria) this;
        }

        public Criteria andCharmRankRoomIsNotNull() {
            addCriterion("charm_rank_room is not null");
            return (Criteria) this;
        }

        public Criteria andCharmRankRoomEqualTo(Integer value) {
            addCriterion("charm_rank_room =", value, "charmRankRoom");
            return (Criteria) this;
        }

        public Criteria andCharmRankRoomNotEqualTo(Integer value) {
            addCriterion("charm_rank_room <>", value, "charmRankRoom");
            return (Criteria) this;
        }

        public Criteria andCharmRankRoomGreaterThan(Integer value) {
            addCriterion("charm_rank_room >", value, "charmRankRoom");
            return (Criteria) this;
        }

        public Criteria andCharmRankRoomGreaterThanOrEqualTo(Integer value) {
            addCriterion("charm_rank_room >=", value, "charmRankRoom");
            return (Criteria) this;
        }

        public Criteria andCharmRankRoomLessThan(Integer value) {
            addCriterion("charm_rank_room <", value, "charmRankRoom");
            return (Criteria) this;
        }

        public Criteria andCharmRankRoomLessThanOrEqualTo(Integer value) {
            addCriterion("charm_rank_room <=", value, "charmRankRoom");
            return (Criteria) this;
        }

        public Criteria andCharmRankRoomIn(List<Integer> values) {
            addCriterion("charm_rank_room in", values, "charmRankRoom");
            return (Criteria) this;
        }

        public Criteria andCharmRankRoomNotIn(List<Integer> values) {
            addCriterion("charm_rank_room not in", values, "charmRankRoom");
            return (Criteria) this;
        }

        public Criteria andCharmRankRoomBetween(Integer value1, Integer value2) {
            addCriterion("charm_rank_room between", value1, value2, "charmRankRoom");
            return (Criteria) this;
        }

        public Criteria andCharmRankRoomNotBetween(Integer value1, Integer value2) {
            addCriterion("charm_rank_room not between", value1, value2, "charmRankRoom");
            return (Criteria) this;
        }

        public Criteria andCharmRankFamilyIsNull() {
            addCriterion("charm_rank_family is null");
            return (Criteria) this;
        }

        public Criteria andCharmRankFamilyIsNotNull() {
            addCriterion("charm_rank_family is not null");
            return (Criteria) this;
        }

        public Criteria andCharmRankFamilyEqualTo(Integer value) {
            addCriterion("charm_rank_family =", value, "charmRankFamily");
            return (Criteria) this;
        }

        public Criteria andCharmRankFamilyNotEqualTo(Integer value) {
            addCriterion("charm_rank_family <>", value, "charmRankFamily");
            return (Criteria) this;
        }

        public Criteria andCharmRankFamilyGreaterThan(Integer value) {
            addCriterion("charm_rank_family >", value, "charmRankFamily");
            return (Criteria) this;
        }

        public Criteria andCharmRankFamilyGreaterThanOrEqualTo(Integer value) {
            addCriterion("charm_rank_family >=", value, "charmRankFamily");
            return (Criteria) this;
        }

        public Criteria andCharmRankFamilyLessThan(Integer value) {
            addCriterion("charm_rank_family <", value, "charmRankFamily");
            return (Criteria) this;
        }

        public Criteria andCharmRankFamilyLessThanOrEqualTo(Integer value) {
            addCriterion("charm_rank_family <=", value, "charmRankFamily");
            return (Criteria) this;
        }

        public Criteria andCharmRankFamilyIn(List<Integer> values) {
            addCriterion("charm_rank_family in", values, "charmRankFamily");
            return (Criteria) this;
        }

        public Criteria andCharmRankFamilyNotIn(List<Integer> values) {
            addCriterion("charm_rank_family not in", values, "charmRankFamily");
            return (Criteria) this;
        }

        public Criteria andCharmRankFamilyBetween(Integer value1, Integer value2) {
            addCriterion("charm_rank_family between", value1, value2, "charmRankFamily");
            return (Criteria) this;
        }

        public Criteria andCharmRankFamilyNotBetween(Integer value1, Integer value2) {
            addCriterion("charm_rank_family not between", value1, value2, "charmRankFamily");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeIsNull() {
            addCriterion("sign_hall_income is null");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeIsNotNull() {
            addCriterion("sign_hall_income is not null");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeEqualTo(BigDecimal value) {
            addCriterion("sign_hall_income =", value, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeNotEqualTo(BigDecimal value) {
            addCriterion("sign_hall_income <>", value, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeGreaterThan(BigDecimal value) {
            addCriterion("sign_hall_income >", value, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("sign_hall_income >=", value, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeLessThan(BigDecimal value) {
            addCriterion("sign_hall_income <", value, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("sign_hall_income <=", value, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeIn(List<BigDecimal> values) {
            addCriterion("sign_hall_income in", values, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeNotIn(List<BigDecimal> values) {
            addCriterion("sign_hall_income not in", values, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sign_hall_income between", value1, value2, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sign_hall_income not between", value1, value2, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeIsNull() {
            addCriterion("official_hall_income is null");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeIsNotNull() {
            addCriterion("official_hall_income is not null");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeEqualTo(BigDecimal value) {
            addCriterion("official_hall_income =", value, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeNotEqualTo(BigDecimal value) {
            addCriterion("official_hall_income <>", value, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeGreaterThan(BigDecimal value) {
            addCriterion("official_hall_income >", value, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("official_hall_income >=", value, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeLessThan(BigDecimal value) {
            addCriterion("official_hall_income <", value, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("official_hall_income <=", value, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeIn(List<BigDecimal> values) {
            addCriterion("official_hall_income in", values, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeNotIn(List<BigDecimal> values) {
            addCriterion("official_hall_income not in", values, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("official_hall_income between", value1, value2, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("official_hall_income not between", value1, value2, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeIsNull() {
            addCriterion("personal_hall_income is null");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeIsNotNull() {
            addCriterion("personal_hall_income is not null");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeEqualTo(BigDecimal value) {
            addCriterion("personal_hall_income =", value, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeNotEqualTo(BigDecimal value) {
            addCriterion("personal_hall_income <>", value, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeGreaterThan(BigDecimal value) {
            addCriterion("personal_hall_income >", value, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("personal_hall_income >=", value, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeLessThan(BigDecimal value) {
            addCriterion("personal_hall_income <", value, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("personal_hall_income <=", value, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeIn(List<BigDecimal> values) {
            addCriterion("personal_hall_income in", values, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeNotIn(List<BigDecimal> values) {
            addCriterion("personal_hall_income not in", values, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("personal_hall_income between", value1, value2, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("personal_hall_income not between", value1, value2, "personalHallIncome");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wavecenter_data_player_room_month
     *
     * @mbg.generated do_not_delete_during_merge Mon Apr 29 14:59:17 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wavecenter_data_player_room_month
     *
     * @mbg.generated Mon Apr 29 14:59:17 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}