package fm.lizhi.ocean.wavecenter.datastore.datacenter.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 收光奖励记录表
 *
 * @date 2025-01-09 04:06:57
 */
@Table(name = "`wave_check_in_light_gift_record`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WaveCheckInLightGiftRecord {
    /**
     * ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 档期ID
     */
    @Column(name= "`schedule_id`")
    private Long scheduleId;

    /**
     * 厅主ID
     */
    @Column(name= "`nj_id`")
    private Long njId;

    /**
     * 送礼人 ID
     */
    @Column(name= "`send_user_id`")
    private Long sendUserId;

    /**
     * 收礼人 ID
     */
    @Column(name= "`rec_user_id`")
    private Long recUserId;

    /**
     * 礼物ID
     */
    @Column(name= "`gift_id`")
    private Long giftId;

    /**
     * 礼物名称
     */
    @Column(name= "`gift_name`")
    private String giftName;

    /**
     * 礼物价值
     */
    @Column(name= "`gift_coin`")
    private Long giftCoin;

    /**
     * 礼物数量
     */
    @Column(name= "`gift_amount`")
    private Integer giftAmount;

    /**
     * 直播ID
     */
    @Column(name= "`live_id`")
    private Long liveId;

    /**
     * 房间ID
     */
    @Column(name= "`room_id`")
    private Long roomId;

    /**
     * 家族ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 魅力值
     */
    @Column(name= "`charm`")
    private Long charm;

    /**
     * 收入
     */
    @Column(name= "`income`")
    private Long income;

    /**
     * 奖励档位
     */
    @Column(name= "`reward_ladder`")
    private Long rewardLadder;

    /**
     * 奖励金额
     */
    @Column(name= "`reward_amount`")
    private Long rewardAmount;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`update_time`")
    private Date updateTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", scheduleId=").append(scheduleId);
        sb.append(", njId=").append(njId);
        sb.append(", sendUserId=").append(sendUserId);
        sb.append(", recUserId=").append(recUserId);
        sb.append(", giftId=").append(giftId);
        sb.append(", giftName=").append(giftName);
        sb.append(", giftCoin=").append(giftCoin);
        sb.append(", giftAmount=").append(giftAmount);
        sb.append(", liveId=").append(liveId);
        sb.append(", roomId=").append(roomId);
        sb.append(", familyId=").append(familyId);
        sb.append(", charm=").append(charm);
        sb.append(", income=").append(income);
        sb.append(", rewardLadder=").append(rewardLadder);
        sb.append(", rewardAmount=").append(rewardAmount);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }
}