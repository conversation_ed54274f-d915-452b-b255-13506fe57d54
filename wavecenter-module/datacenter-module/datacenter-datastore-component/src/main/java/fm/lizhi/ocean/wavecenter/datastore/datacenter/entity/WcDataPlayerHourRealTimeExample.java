package fm.lizhi.ocean.wavecenter.datastore.datacenter.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WcDataPlayerHourRealTimeExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_player_hour_real_time
     *
     * @mbg.generated Mon Jun 09 10:56:33 CST 2025
     */
    public WcDataPlayerHourRealTimeExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_player_hour_real_time
     *
     * @mbg.generated Mon Jun 09 10:56:33 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_player_hour_real_time
     *
     * @mbg.generated Mon Jun 09 10:56:33 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_player_hour_real_time
     *
     * @mbg.generated Mon Jun 09 10:56:33 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_player_hour_real_time
     *
     * @mbg.generated Mon Jun 09 10:56:33 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_player_hour_real_time
     *
     * @mbg.generated Mon Jun 09 10:56:33 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_player_hour_real_time
     *
     * @mbg.generated Mon Jun 09 10:56:33 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_player_hour_real_time
     *
     * @mbg.generated Mon Jun 09 10:56:33 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_player_hour_real_time
     *
     * @mbg.generated Mon Jun 09 10:56:33 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_player_hour_real_time
     *
     * @mbg.generated Mon Jun 09 10:56:33 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_player_hour_real_time
     *
     * @mbg.generated Mon Jun 09 10:56:33 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Class getEntityClass() {
        return WcDataPlayerHourRealTime.class;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wavecenter_data_player_hour_real_time
     *
     * @mbg.generated Mon Jun 09 10:56:33 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(Integer value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(Integer value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(Integer value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(Integer value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(Integer value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<Integer> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<Integer> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(Integer value1, Integer value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(Integer value1, Integer value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andStatDateIsNull() {
            addCriterion("stat_date is null");
            return (Criteria) this;
        }

        public Criteria andStatDateIsNotNull() {
            addCriterion("stat_date is not null");
            return (Criteria) this;
        }

        public Criteria andStatDateEqualTo(Date value) {
            addCriterion("stat_date =", value, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateNotEqualTo(Date value) {
            addCriterion("stat_date <>", value, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateGreaterThan(Date value) {
            addCriterion("stat_date >", value, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateGreaterThanOrEqualTo(Date value) {
            addCriterion("stat_date >=", value, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateLessThan(Date value) {
            addCriterion("stat_date <", value, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateLessThanOrEqualTo(Date value) {
            addCriterion("stat_date <=", value, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateIn(List<Date> values) {
            addCriterion("stat_date in", values, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateNotIn(List<Date> values) {
            addCriterion("stat_date not in", values, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateBetween(Date value1, Date value2) {
            addCriterion("stat_date between", value1, value2, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateNotBetween(Date value1, Date value2) {
            addCriterion("stat_date not between", value1, value2, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatHourValueIsNull() {
            addCriterion("stat_hour_value is null");
            return (Criteria) this;
        }

        public Criteria andStatHourValueIsNotNull() {
            addCriterion("stat_hour_value is not null");
            return (Criteria) this;
        }

        public Criteria andStatHourValueEqualTo(Integer value) {
            addCriterion("stat_hour_value =", value, "statHourValue");
            return (Criteria) this;
        }

        public Criteria andStatHourValueNotEqualTo(Integer value) {
            addCriterion("stat_hour_value <>", value, "statHourValue");
            return (Criteria) this;
        }

        public Criteria andStatHourValueGreaterThan(Integer value) {
            addCriterion("stat_hour_value >", value, "statHourValue");
            return (Criteria) this;
        }

        public Criteria andStatHourValueGreaterThanOrEqualTo(Integer value) {
            addCriterion("stat_hour_value >=", value, "statHourValue");
            return (Criteria) this;
        }

        public Criteria andStatHourValueLessThan(Integer value) {
            addCriterion("stat_hour_value <", value, "statHourValue");
            return (Criteria) this;
        }

        public Criteria andStatHourValueLessThanOrEqualTo(Integer value) {
            addCriterion("stat_hour_value <=", value, "statHourValue");
            return (Criteria) this;
        }

        public Criteria andStatHourValueIn(List<Integer> values) {
            addCriterion("stat_hour_value in", values, "statHourValue");
            return (Criteria) this;
        }

        public Criteria andStatHourValueNotIn(List<Integer> values) {
            addCriterion("stat_hour_value not in", values, "statHourValue");
            return (Criteria) this;
        }

        public Criteria andStatHourValueBetween(Integer value1, Integer value2) {
            addCriterion("stat_hour_value between", value1, value2, "statHourValue");
            return (Criteria) this;
        }

        public Criteria andStatHourValueNotBetween(Integer value1, Integer value2) {
            addCriterion("stat_hour_value not between", value1, value2, "statHourValue");
            return (Criteria) this;
        }

        public Criteria andPlayerIdIsNull() {
            addCriterion("player_id is null");
            return (Criteria) this;
        }

        public Criteria andPlayerIdIsNotNull() {
            addCriterion("player_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlayerIdEqualTo(Long value) {
            addCriterion("player_id =", value, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdNotEqualTo(Long value) {
            addCriterion("player_id <>", value, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdGreaterThan(Long value) {
            addCriterion("player_id >", value, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdGreaterThanOrEqualTo(Long value) {
            addCriterion("player_id >=", value, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdLessThan(Long value) {
            addCriterion("player_id <", value, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdLessThanOrEqualTo(Long value) {
            addCriterion("player_id <=", value, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdIn(List<Long> values) {
            addCriterion("player_id in", values, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdNotIn(List<Long> values) {
            addCriterion("player_id not in", values, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdBetween(Long value1, Long value2) {
            addCriterion("player_id between", value1, value2, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdNotBetween(Long value1, Long value2) {
            addCriterion("player_id not between", value1, value2, "playerId");
            return (Criteria) this;
        }

        public Criteria andChatUserCntIsNull() {
            addCriterion("chat_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andChatUserCntIsNotNull() {
            addCriterion("chat_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andChatUserCntEqualTo(Integer value) {
            addCriterion("chat_user_cnt =", value, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntNotEqualTo(Integer value) {
            addCriterion("chat_user_cnt <>", value, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntGreaterThan(Integer value) {
            addCriterion("chat_user_cnt >", value, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("chat_user_cnt >=", value, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntLessThan(Integer value) {
            addCriterion("chat_user_cnt <", value, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntLessThanOrEqualTo(Integer value) {
            addCriterion("chat_user_cnt <=", value, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntIn(List<Integer> values) {
            addCriterion("chat_user_cnt in", values, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntNotIn(List<Integer> values) {
            addCriterion("chat_user_cnt not in", values, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntBetween(Integer value1, Integer value2) {
            addCriterion("chat_user_cnt between", value1, value2, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andChatUserCntNotBetween(Integer value1, Integer value2) {
            addCriterion("chat_user_cnt not between", value1, value2, "chatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntIsNull() {
            addCriterion("reply_chat_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntIsNotNull() {
            addCriterion("reply_chat_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntEqualTo(Long value) {
            addCriterion("reply_chat_user_cnt =", value, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntNotEqualTo(Long value) {
            addCriterion("reply_chat_user_cnt <>", value, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntGreaterThan(Long value) {
            addCriterion("reply_chat_user_cnt >", value, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntGreaterThanOrEqualTo(Long value) {
            addCriterion("reply_chat_user_cnt >=", value, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntLessThan(Long value) {
            addCriterion("reply_chat_user_cnt <", value, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntLessThanOrEqualTo(Long value) {
            addCriterion("reply_chat_user_cnt <=", value, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntIn(List<Long> values) {
            addCriterion("reply_chat_user_cnt in", values, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntNotIn(List<Long> values) {
            addCriterion("reply_chat_user_cnt not in", values, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntBetween(Long value1, Long value2) {
            addCriterion("reply_chat_user_cnt between", value1, value2, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatUserCntNotBetween(Long value1, Long value2) {
            addCriterion("reply_chat_user_cnt not between", value1, value2, "replyChatUserCnt");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateIsNull() {
            addCriterion("reply_chat_rate is null");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateIsNotNull() {
            addCriterion("reply_chat_rate is not null");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateEqualTo(BigDecimal value) {
            addCriterion("reply_chat_rate =", value, "replyChatRate");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateNotEqualTo(BigDecimal value) {
            addCriterion("reply_chat_rate <>", value, "replyChatRate");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateGreaterThan(BigDecimal value) {
            addCriterion("reply_chat_rate >", value, "replyChatRate");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("reply_chat_rate >=", value, "replyChatRate");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateLessThan(BigDecimal value) {
            addCriterion("reply_chat_rate <", value, "replyChatRate");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("reply_chat_rate <=", value, "replyChatRate");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateIn(List<BigDecimal> values) {
            addCriterion("reply_chat_rate in", values, "replyChatRate");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateNotIn(List<BigDecimal> values) {
            addCriterion("reply_chat_rate not in", values, "replyChatRate");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("reply_chat_rate between", value1, value2, "replyChatRate");
            return (Criteria) this;
        }

        public Criteria andReplyChatRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("reply_chat_rate not between", value1, value2, "replyChatRate");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wavecenter_data_player_hour_real_time
     *
     * @mbg.generated do_not_delete_during_merge Mon Jun 09 10:56:33 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wavecenter_data_player_hour_real_time
     *
     * @mbg.generated Mon Jun 09 10:56:33 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}