package fm.lizhi.ocean.wavecenter.datastore.datacenter.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WaveCheckInAllMicGiftRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_all_mic_gift_record
     *
     * @mbg.generated Wed Mar 05 17:16:09 CST 2025
     */
    public WaveCheckInAllMicGiftRecordExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_all_mic_gift_record
     *
     * @mbg.generated Wed Mar 05 17:16:09 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_all_mic_gift_record
     *
     * @mbg.generated Wed Mar 05 17:16:09 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_all_mic_gift_record
     *
     * @mbg.generated Wed Mar 05 17:16:09 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_all_mic_gift_record
     *
     * @mbg.generated Wed Mar 05 17:16:09 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_all_mic_gift_record
     *
     * @mbg.generated Wed Mar 05 17:16:09 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_all_mic_gift_record
     *
     * @mbg.generated Wed Mar 05 17:16:09 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_all_mic_gift_record
     *
     * @mbg.generated Wed Mar 05 17:16:09 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_all_mic_gift_record
     *
     * @mbg.generated Wed Mar 05 17:16:09 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_all_mic_gift_record
     *
     * @mbg.generated Wed Mar 05 17:16:09 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_all_mic_gift_record
     *
     * @mbg.generated Wed Mar 05 17:16:09 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Class getEntityClass() {
        return WaveCheckInAllMicGiftRecord.class;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wave_check_in_all_mic_gift_record
     *
     * @mbg.generated Wed Mar 05 17:16:09 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(Integer value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(Integer value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(Integer value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(Integer value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(Integer value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<Integer> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<Integer> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(Integer value1, Integer value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(Integer value1, Integer value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdIsNull() {
            addCriterion("schedule_id is null");
            return (Criteria) this;
        }

        public Criteria andScheduleIdIsNotNull() {
            addCriterion("schedule_id is not null");
            return (Criteria) this;
        }

        public Criteria andScheduleIdEqualTo(Long value) {
            addCriterion("schedule_id =", value, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdNotEqualTo(Long value) {
            addCriterion("schedule_id <>", value, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdGreaterThan(Long value) {
            addCriterion("schedule_id >", value, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdGreaterThanOrEqualTo(Long value) {
            addCriterion("schedule_id >=", value, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdLessThan(Long value) {
            addCriterion("schedule_id <", value, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdLessThanOrEqualTo(Long value) {
            addCriterion("schedule_id <=", value, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdIn(List<Long> values) {
            addCriterion("schedule_id in", values, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdNotIn(List<Long> values) {
            addCriterion("schedule_id not in", values, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdBetween(Long value1, Long value2) {
            addCriterion("schedule_id between", value1, value2, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdNotBetween(Long value1, Long value2) {
            addCriterion("schedule_id not between", value1, value2, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andNjIdIsNull() {
            addCriterion("nj_id is null");
            return (Criteria) this;
        }

        public Criteria andNjIdIsNotNull() {
            addCriterion("nj_id is not null");
            return (Criteria) this;
        }

        public Criteria andNjIdEqualTo(Long value) {
            addCriterion("nj_id =", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdNotEqualTo(Long value) {
            addCriterion("nj_id <>", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdGreaterThan(Long value) {
            addCriterion("nj_id >", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdGreaterThanOrEqualTo(Long value) {
            addCriterion("nj_id >=", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdLessThan(Long value) {
            addCriterion("nj_id <", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdLessThanOrEqualTo(Long value) {
            addCriterion("nj_id <=", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdIn(List<Long> values) {
            addCriterion("nj_id in", values, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdNotIn(List<Long> values) {
            addCriterion("nj_id not in", values, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdBetween(Long value1, Long value2) {
            addCriterion("nj_id between", value1, value2, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdNotBetween(Long value1, Long value2) {
            addCriterion("nj_id not between", value1, value2, "njId");
            return (Criteria) this;
        }

        public Criteria andSendUserIdIsNull() {
            addCriterion("send_user_id is null");
            return (Criteria) this;
        }

        public Criteria andSendUserIdIsNotNull() {
            addCriterion("send_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andSendUserIdEqualTo(Long value) {
            addCriterion("send_user_id =", value, "sendUserId");
            return (Criteria) this;
        }

        public Criteria andSendUserIdNotEqualTo(Long value) {
            addCriterion("send_user_id <>", value, "sendUserId");
            return (Criteria) this;
        }

        public Criteria andSendUserIdGreaterThan(Long value) {
            addCriterion("send_user_id >", value, "sendUserId");
            return (Criteria) this;
        }

        public Criteria andSendUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("send_user_id >=", value, "sendUserId");
            return (Criteria) this;
        }

        public Criteria andSendUserIdLessThan(Long value) {
            addCriterion("send_user_id <", value, "sendUserId");
            return (Criteria) this;
        }

        public Criteria andSendUserIdLessThanOrEqualTo(Long value) {
            addCriterion("send_user_id <=", value, "sendUserId");
            return (Criteria) this;
        }

        public Criteria andSendUserIdIn(List<Long> values) {
            addCriterion("send_user_id in", values, "sendUserId");
            return (Criteria) this;
        }

        public Criteria andSendUserIdNotIn(List<Long> values) {
            addCriterion("send_user_id not in", values, "sendUserId");
            return (Criteria) this;
        }

        public Criteria andSendUserIdBetween(Long value1, Long value2) {
            addCriterion("send_user_id between", value1, value2, "sendUserId");
            return (Criteria) this;
        }

        public Criteria andSendUserIdNotBetween(Long value1, Long value2) {
            addCriterion("send_user_id not between", value1, value2, "sendUserId");
            return (Criteria) this;
        }

        public Criteria andGiftIdIsNull() {
            addCriterion("gift_id is null");
            return (Criteria) this;
        }

        public Criteria andGiftIdIsNotNull() {
            addCriterion("gift_id is not null");
            return (Criteria) this;
        }

        public Criteria andGiftIdEqualTo(Long value) {
            addCriterion("gift_id =", value, "giftId");
            return (Criteria) this;
        }

        public Criteria andGiftIdNotEqualTo(Long value) {
            addCriterion("gift_id <>", value, "giftId");
            return (Criteria) this;
        }

        public Criteria andGiftIdGreaterThan(Long value) {
            addCriterion("gift_id >", value, "giftId");
            return (Criteria) this;
        }

        public Criteria andGiftIdGreaterThanOrEqualTo(Long value) {
            addCriterion("gift_id >=", value, "giftId");
            return (Criteria) this;
        }

        public Criteria andGiftIdLessThan(Long value) {
            addCriterion("gift_id <", value, "giftId");
            return (Criteria) this;
        }

        public Criteria andGiftIdLessThanOrEqualTo(Long value) {
            addCriterion("gift_id <=", value, "giftId");
            return (Criteria) this;
        }

        public Criteria andGiftIdIn(List<Long> values) {
            addCriterion("gift_id in", values, "giftId");
            return (Criteria) this;
        }

        public Criteria andGiftIdNotIn(List<Long> values) {
            addCriterion("gift_id not in", values, "giftId");
            return (Criteria) this;
        }

        public Criteria andGiftIdBetween(Long value1, Long value2) {
            addCriterion("gift_id between", value1, value2, "giftId");
            return (Criteria) this;
        }

        public Criteria andGiftIdNotBetween(Long value1, Long value2) {
            addCriterion("gift_id not between", value1, value2, "giftId");
            return (Criteria) this;
        }

        public Criteria andGiftNameIsNull() {
            addCriterion("gift_name is null");
            return (Criteria) this;
        }

        public Criteria andGiftNameIsNotNull() {
            addCriterion("gift_name is not null");
            return (Criteria) this;
        }

        public Criteria andGiftNameEqualTo(String value) {
            addCriterion("gift_name =", value, "giftName");
            return (Criteria) this;
        }

        public Criteria andGiftNameNotEqualTo(String value) {
            addCriterion("gift_name <>", value, "giftName");
            return (Criteria) this;
        }

        public Criteria andGiftNameGreaterThan(String value) {
            addCriterion("gift_name >", value, "giftName");
            return (Criteria) this;
        }

        public Criteria andGiftNameGreaterThanOrEqualTo(String value) {
            addCriterion("gift_name >=", value, "giftName");
            return (Criteria) this;
        }

        public Criteria andGiftNameLessThan(String value) {
            addCriterion("gift_name <", value, "giftName");
            return (Criteria) this;
        }

        public Criteria andGiftNameLessThanOrEqualTo(String value) {
            addCriterion("gift_name <=", value, "giftName");
            return (Criteria) this;
        }

        public Criteria andGiftNameLike(String value) {
            addCriterion("gift_name like", value, "giftName");
            return (Criteria) this;
        }

        public Criteria andGiftNameNotLike(String value) {
            addCriterion("gift_name not like", value, "giftName");
            return (Criteria) this;
        }

        public Criteria andGiftNameIn(List<String> values) {
            addCriterion("gift_name in", values, "giftName");
            return (Criteria) this;
        }

        public Criteria andGiftNameNotIn(List<String> values) {
            addCriterion("gift_name not in", values, "giftName");
            return (Criteria) this;
        }

        public Criteria andGiftNameBetween(String value1, String value2) {
            addCriterion("gift_name between", value1, value2, "giftName");
            return (Criteria) this;
        }

        public Criteria andGiftNameNotBetween(String value1, String value2) {
            addCriterion("gift_name not between", value1, value2, "giftName");
            return (Criteria) this;
        }

        public Criteria andGiftCoinIsNull() {
            addCriterion("gift_coin is null");
            return (Criteria) this;
        }

        public Criteria andGiftCoinIsNotNull() {
            addCriterion("gift_coin is not null");
            return (Criteria) this;
        }

        public Criteria andGiftCoinEqualTo(Long value) {
            addCriterion("gift_coin =", value, "giftCoin");
            return (Criteria) this;
        }

        public Criteria andGiftCoinNotEqualTo(Long value) {
            addCriterion("gift_coin <>", value, "giftCoin");
            return (Criteria) this;
        }

        public Criteria andGiftCoinGreaterThan(Long value) {
            addCriterion("gift_coin >", value, "giftCoin");
            return (Criteria) this;
        }

        public Criteria andGiftCoinGreaterThanOrEqualTo(Long value) {
            addCriterion("gift_coin >=", value, "giftCoin");
            return (Criteria) this;
        }

        public Criteria andGiftCoinLessThan(Long value) {
            addCriterion("gift_coin <", value, "giftCoin");
            return (Criteria) this;
        }

        public Criteria andGiftCoinLessThanOrEqualTo(Long value) {
            addCriterion("gift_coin <=", value, "giftCoin");
            return (Criteria) this;
        }

        public Criteria andGiftCoinIn(List<Long> values) {
            addCriterion("gift_coin in", values, "giftCoin");
            return (Criteria) this;
        }

        public Criteria andGiftCoinNotIn(List<Long> values) {
            addCriterion("gift_coin not in", values, "giftCoin");
            return (Criteria) this;
        }

        public Criteria andGiftCoinBetween(Long value1, Long value2) {
            addCriterion("gift_coin between", value1, value2, "giftCoin");
            return (Criteria) this;
        }

        public Criteria andGiftCoinNotBetween(Long value1, Long value2) {
            addCriterion("gift_coin not between", value1, value2, "giftCoin");
            return (Criteria) this;
        }

        public Criteria andGiftAmountIsNull() {
            addCriterion("gift_amount is null");
            return (Criteria) this;
        }

        public Criteria andGiftAmountIsNotNull() {
            addCriterion("gift_amount is not null");
            return (Criteria) this;
        }

        public Criteria andGiftAmountEqualTo(Integer value) {
            addCriterion("gift_amount =", value, "giftAmount");
            return (Criteria) this;
        }

        public Criteria andGiftAmountNotEqualTo(Integer value) {
            addCriterion("gift_amount <>", value, "giftAmount");
            return (Criteria) this;
        }

        public Criteria andGiftAmountGreaterThan(Integer value) {
            addCriterion("gift_amount >", value, "giftAmount");
            return (Criteria) this;
        }

        public Criteria andGiftAmountGreaterThanOrEqualTo(Integer value) {
            addCriterion("gift_amount >=", value, "giftAmount");
            return (Criteria) this;
        }

        public Criteria andGiftAmountLessThan(Integer value) {
            addCriterion("gift_amount <", value, "giftAmount");
            return (Criteria) this;
        }

        public Criteria andGiftAmountLessThanOrEqualTo(Integer value) {
            addCriterion("gift_amount <=", value, "giftAmount");
            return (Criteria) this;
        }

        public Criteria andGiftAmountIn(List<Integer> values) {
            addCriterion("gift_amount in", values, "giftAmount");
            return (Criteria) this;
        }

        public Criteria andGiftAmountNotIn(List<Integer> values) {
            addCriterion("gift_amount not in", values, "giftAmount");
            return (Criteria) this;
        }

        public Criteria andGiftAmountBetween(Integer value1, Integer value2) {
            addCriterion("gift_amount between", value1, value2, "giftAmount");
            return (Criteria) this;
        }

        public Criteria andGiftAmountNotBetween(Integer value1, Integer value2) {
            addCriterion("gift_amount not between", value1, value2, "giftAmount");
            return (Criteria) this;
        }

        public Criteria andLiveIdIsNull() {
            addCriterion("live_id is null");
            return (Criteria) this;
        }

        public Criteria andLiveIdIsNotNull() {
            addCriterion("live_id is not null");
            return (Criteria) this;
        }

        public Criteria andLiveIdEqualTo(Long value) {
            addCriterion("live_id =", value, "liveId");
            return (Criteria) this;
        }

        public Criteria andLiveIdNotEqualTo(Long value) {
            addCriterion("live_id <>", value, "liveId");
            return (Criteria) this;
        }

        public Criteria andLiveIdGreaterThan(Long value) {
            addCriterion("live_id >", value, "liveId");
            return (Criteria) this;
        }

        public Criteria andLiveIdGreaterThanOrEqualTo(Long value) {
            addCriterion("live_id >=", value, "liveId");
            return (Criteria) this;
        }

        public Criteria andLiveIdLessThan(Long value) {
            addCriterion("live_id <", value, "liveId");
            return (Criteria) this;
        }

        public Criteria andLiveIdLessThanOrEqualTo(Long value) {
            addCriterion("live_id <=", value, "liveId");
            return (Criteria) this;
        }

        public Criteria andLiveIdIn(List<Long> values) {
            addCriterion("live_id in", values, "liveId");
            return (Criteria) this;
        }

        public Criteria andLiveIdNotIn(List<Long> values) {
            addCriterion("live_id not in", values, "liveId");
            return (Criteria) this;
        }

        public Criteria andLiveIdBetween(Long value1, Long value2) {
            addCriterion("live_id between", value1, value2, "liveId");
            return (Criteria) this;
        }

        public Criteria andLiveIdNotBetween(Long value1, Long value2) {
            addCriterion("live_id not between", value1, value2, "liveId");
            return (Criteria) this;
        }

        public Criteria andRoomIdIsNull() {
            addCriterion("room_id is null");
            return (Criteria) this;
        }

        public Criteria andRoomIdIsNotNull() {
            addCriterion("room_id is not null");
            return (Criteria) this;
        }

        public Criteria andRoomIdEqualTo(Long value) {
            addCriterion("room_id =", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotEqualTo(Long value) {
            addCriterion("room_id <>", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdGreaterThan(Long value) {
            addCriterion("room_id >", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdGreaterThanOrEqualTo(Long value) {
            addCriterion("room_id >=", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdLessThan(Long value) {
            addCriterion("room_id <", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdLessThanOrEqualTo(Long value) {
            addCriterion("room_id <=", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdIn(List<Long> values) {
            addCriterion("room_id in", values, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotIn(List<Long> values) {
            addCriterion("room_id not in", values, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdBetween(Long value1, Long value2) {
            addCriterion("room_id between", value1, value2, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotBetween(Long value1, Long value2) {
            addCriterion("room_id not between", value1, value2, "roomId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIsNull() {
            addCriterion("family_id is null");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIsNotNull() {
            addCriterion("family_id is not null");
            return (Criteria) this;
        }

        public Criteria andFamilyIdEqualTo(Long value) {
            addCriterion("family_id =", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotEqualTo(Long value) {
            addCriterion("family_id <>", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdGreaterThan(Long value) {
            addCriterion("family_id >", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("family_id >=", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdLessThan(Long value) {
            addCriterion("family_id <", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdLessThanOrEqualTo(Long value) {
            addCriterion("family_id <=", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIn(List<Long> values) {
            addCriterion("family_id in", values, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotIn(List<Long> values) {
            addCriterion("family_id not in", values, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdBetween(Long value1, Long value2) {
            addCriterion("family_id between", value1, value2, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotBetween(Long value1, Long value2) {
            addCriterion("family_id not between", value1, value2, "familyId");
            return (Criteria) this;
        }

        public Criteria andCharmIsNull() {
            addCriterion("charm is null");
            return (Criteria) this;
        }

        public Criteria andCharmIsNotNull() {
            addCriterion("charm is not null");
            return (Criteria) this;
        }

        public Criteria andCharmEqualTo(Long value) {
            addCriterion("charm =", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmNotEqualTo(Long value) {
            addCriterion("charm <>", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmGreaterThan(Long value) {
            addCriterion("charm >", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmGreaterThanOrEqualTo(Long value) {
            addCriterion("charm >=", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmLessThan(Long value) {
            addCriterion("charm <", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmLessThanOrEqualTo(Long value) {
            addCriterion("charm <=", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmIn(List<Long> values) {
            addCriterion("charm in", values, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmNotIn(List<Long> values) {
            addCriterion("charm not in", values, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmBetween(Long value1, Long value2) {
            addCriterion("charm between", value1, value2, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmNotBetween(Long value1, Long value2) {
            addCriterion("charm not between", value1, value2, "charm");
            return (Criteria) this;
        }

        public Criteria andIncomeIsNull() {
            addCriterion("income is null");
            return (Criteria) this;
        }

        public Criteria andIncomeIsNotNull() {
            addCriterion("income is not null");
            return (Criteria) this;
        }

        public Criteria andIncomeEqualTo(Long value) {
            addCriterion("income =", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeNotEqualTo(Long value) {
            addCriterion("income <>", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeGreaterThan(Long value) {
            addCriterion("income >", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeGreaterThanOrEqualTo(Long value) {
            addCriterion("income >=", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeLessThan(Long value) {
            addCriterion("income <", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeLessThanOrEqualTo(Long value) {
            addCriterion("income <=", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeIn(List<Long> values) {
            addCriterion("income in", values, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeNotIn(List<Long> values) {
            addCriterion("income not in", values, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeBetween(Long value1, Long value2) {
            addCriterion("income between", value1, value2, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeNotBetween(Long value1, Long value2) {
            addCriterion("income not between", value1, value2, "income");
            return (Criteria) this;
        }

        public Criteria andRewardLadderIsNull() {
            addCriterion("reward_ladder is null");
            return (Criteria) this;
        }

        public Criteria andRewardLadderIsNotNull() {
            addCriterion("reward_ladder is not null");
            return (Criteria) this;
        }

        public Criteria andRewardLadderEqualTo(Long value) {
            addCriterion("reward_ladder =", value, "rewardLadder");
            return (Criteria) this;
        }

        public Criteria andRewardLadderNotEqualTo(Long value) {
            addCriterion("reward_ladder <>", value, "rewardLadder");
            return (Criteria) this;
        }

        public Criteria andRewardLadderGreaterThan(Long value) {
            addCriterion("reward_ladder >", value, "rewardLadder");
            return (Criteria) this;
        }

        public Criteria andRewardLadderGreaterThanOrEqualTo(Long value) {
            addCriterion("reward_ladder >=", value, "rewardLadder");
            return (Criteria) this;
        }

        public Criteria andRewardLadderLessThan(Long value) {
            addCriterion("reward_ladder <", value, "rewardLadder");
            return (Criteria) this;
        }

        public Criteria andRewardLadderLessThanOrEqualTo(Long value) {
            addCriterion("reward_ladder <=", value, "rewardLadder");
            return (Criteria) this;
        }

        public Criteria andRewardLadderIn(List<Long> values) {
            addCriterion("reward_ladder in", values, "rewardLadder");
            return (Criteria) this;
        }

        public Criteria andRewardLadderNotIn(List<Long> values) {
            addCriterion("reward_ladder not in", values, "rewardLadder");
            return (Criteria) this;
        }

        public Criteria andRewardLadderBetween(Long value1, Long value2) {
            addCriterion("reward_ladder between", value1, value2, "rewardLadder");
            return (Criteria) this;
        }

        public Criteria andRewardLadderNotBetween(Long value1, Long value2) {
            addCriterion("reward_ladder not between", value1, value2, "rewardLadder");
            return (Criteria) this;
        }

        public Criteria andRewardAmountIsNull() {
            addCriterion("reward_amount is null");
            return (Criteria) this;
        }

        public Criteria andRewardAmountIsNotNull() {
            addCriterion("reward_amount is not null");
            return (Criteria) this;
        }

        public Criteria andRewardAmountEqualTo(Long value) {
            addCriterion("reward_amount =", value, "rewardAmount");
            return (Criteria) this;
        }

        public Criteria andRewardAmountNotEqualTo(Long value) {
            addCriterion("reward_amount <>", value, "rewardAmount");
            return (Criteria) this;
        }

        public Criteria andRewardAmountGreaterThan(Long value) {
            addCriterion("reward_amount >", value, "rewardAmount");
            return (Criteria) this;
        }

        public Criteria andRewardAmountGreaterThanOrEqualTo(Long value) {
            addCriterion("reward_amount >=", value, "rewardAmount");
            return (Criteria) this;
        }

        public Criteria andRewardAmountLessThan(Long value) {
            addCriterion("reward_amount <", value, "rewardAmount");
            return (Criteria) this;
        }

        public Criteria andRewardAmountLessThanOrEqualTo(Long value) {
            addCriterion("reward_amount <=", value, "rewardAmount");
            return (Criteria) this;
        }

        public Criteria andRewardAmountIn(List<Long> values) {
            addCriterion("reward_amount in", values, "rewardAmount");
            return (Criteria) this;
        }

        public Criteria andRewardAmountNotIn(List<Long> values) {
            addCriterion("reward_amount not in", values, "rewardAmount");
            return (Criteria) this;
        }

        public Criteria andRewardAmountBetween(Long value1, Long value2) {
            addCriterion("reward_amount between", value1, value2, "rewardAmount");
            return (Criteria) this;
        }

        public Criteria andRewardAmountNotBetween(Long value1, Long value2) {
            addCriterion("reward_amount not between", value1, value2, "rewardAmount");
            return (Criteria) this;
        }

        public Criteria andContainNjRewardAmountIsNull() {
            addCriterion("contain_nj_reward_amount is null");
            return (Criteria) this;
        }

        public Criteria andContainNjRewardAmountIsNotNull() {
            addCriterion("contain_nj_reward_amount is not null");
            return (Criteria) this;
        }

        public Criteria andContainNjRewardAmountEqualTo(Boolean value) {
            addCriterion("contain_nj_reward_amount =", value, "containNjRewardAmount");
            return (Criteria) this;
        }

        public Criteria andContainNjRewardAmountNotEqualTo(Boolean value) {
            addCriterion("contain_nj_reward_amount <>", value, "containNjRewardAmount");
            return (Criteria) this;
        }

        public Criteria andContainNjRewardAmountGreaterThan(Boolean value) {
            addCriterion("contain_nj_reward_amount >", value, "containNjRewardAmount");
            return (Criteria) this;
        }

        public Criteria andContainNjRewardAmountGreaterThanOrEqualTo(Boolean value) {
            addCriterion("contain_nj_reward_amount >=", value, "containNjRewardAmount");
            return (Criteria) this;
        }

        public Criteria andContainNjRewardAmountLessThan(Boolean value) {
            addCriterion("contain_nj_reward_amount <", value, "containNjRewardAmount");
            return (Criteria) this;
        }

        public Criteria andContainNjRewardAmountLessThanOrEqualTo(Boolean value) {
            addCriterion("contain_nj_reward_amount <=", value, "containNjRewardAmount");
            return (Criteria) this;
        }

        public Criteria andContainNjRewardAmountIn(List<Boolean> values) {
            addCriterion("contain_nj_reward_amount in", values, "containNjRewardAmount");
            return (Criteria) this;
        }

        public Criteria andContainNjRewardAmountNotIn(List<Boolean> values) {
            addCriterion("contain_nj_reward_amount not in", values, "containNjRewardAmount");
            return (Criteria) this;
        }

        public Criteria andContainNjRewardAmountBetween(Boolean value1, Boolean value2) {
            addCriterion("contain_nj_reward_amount between", value1, value2, "containNjRewardAmount");
            return (Criteria) this;
        }

        public Criteria andContainNjRewardAmountNotBetween(Boolean value1, Boolean value2) {
            addCriterion("contain_nj_reward_amount not between", value1, value2, "containNjRewardAmount");
            return (Criteria) this;
        }

        public Criteria andGiftBatchIdIsNull() {
            addCriterion("gift_batch_id is null");
            return (Criteria) this;
        }

        public Criteria andGiftBatchIdIsNotNull() {
            addCriterion("gift_batch_id is not null");
            return (Criteria) this;
        }

        public Criteria andGiftBatchIdEqualTo(Long value) {
            addCriterion("gift_batch_id =", value, "giftBatchId");
            return (Criteria) this;
        }

        public Criteria andGiftBatchIdNotEqualTo(Long value) {
            addCriterion("gift_batch_id <>", value, "giftBatchId");
            return (Criteria) this;
        }

        public Criteria andGiftBatchIdGreaterThan(Long value) {
            addCriterion("gift_batch_id >", value, "giftBatchId");
            return (Criteria) this;
        }

        public Criteria andGiftBatchIdGreaterThanOrEqualTo(Long value) {
            addCriterion("gift_batch_id >=", value, "giftBatchId");
            return (Criteria) this;
        }

        public Criteria andGiftBatchIdLessThan(Long value) {
            addCriterion("gift_batch_id <", value, "giftBatchId");
            return (Criteria) this;
        }

        public Criteria andGiftBatchIdLessThanOrEqualTo(Long value) {
            addCriterion("gift_batch_id <=", value, "giftBatchId");
            return (Criteria) this;
        }

        public Criteria andGiftBatchIdIn(List<Long> values) {
            addCriterion("gift_batch_id in", values, "giftBatchId");
            return (Criteria) this;
        }

        public Criteria andGiftBatchIdNotIn(List<Long> values) {
            addCriterion("gift_batch_id not in", values, "giftBatchId");
            return (Criteria) this;
        }

        public Criteria andGiftBatchIdBetween(Long value1, Long value2) {
            addCriterion("gift_batch_id between", value1, value2, "giftBatchId");
            return (Criteria) this;
        }

        public Criteria andGiftBatchIdNotBetween(Long value1, Long value2) {
            addCriterion("gift_batch_id not between", value1, value2, "giftBatchId");
            return (Criteria) this;
        }

        public Criteria andAllocationUserIdIsNull() {
            addCriterion("allocation_user_id is null");
            return (Criteria) this;
        }

        public Criteria andAllocationUserIdIsNotNull() {
            addCriterion("allocation_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andAllocationUserIdEqualTo(Long value) {
            addCriterion("allocation_user_id =", value, "allocationUserId");
            return (Criteria) this;
        }

        public Criteria andAllocationUserIdNotEqualTo(Long value) {
            addCriterion("allocation_user_id <>", value, "allocationUserId");
            return (Criteria) this;
        }

        public Criteria andAllocationUserIdGreaterThan(Long value) {
            addCriterion("allocation_user_id >", value, "allocationUserId");
            return (Criteria) this;
        }

        public Criteria andAllocationUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("allocation_user_id >=", value, "allocationUserId");
            return (Criteria) this;
        }

        public Criteria andAllocationUserIdLessThan(Long value) {
            addCriterion("allocation_user_id <", value, "allocationUserId");
            return (Criteria) this;
        }

        public Criteria andAllocationUserIdLessThanOrEqualTo(Long value) {
            addCriterion("allocation_user_id <=", value, "allocationUserId");
            return (Criteria) this;
        }

        public Criteria andAllocationUserIdIn(List<Long> values) {
            addCriterion("allocation_user_id in", values, "allocationUserId");
            return (Criteria) this;
        }

        public Criteria andAllocationUserIdNotIn(List<Long> values) {
            addCriterion("allocation_user_id not in", values, "allocationUserId");
            return (Criteria) this;
        }

        public Criteria andAllocationUserIdBetween(Long value1, Long value2) {
            addCriterion("allocation_user_id between", value1, value2, "allocationUserId");
            return (Criteria) this;
        }

        public Criteria andAllocationUserIdNotBetween(Long value1, Long value2) {
            addCriterion("allocation_user_id not between", value1, value2, "allocationUserId");
            return (Criteria) this;
        }

        public Criteria andAllocationTimeIsNull() {
            addCriterion("allocation_time is null");
            return (Criteria) this;
        }

        public Criteria andAllocationTimeIsNotNull() {
            addCriterion("allocation_time is not null");
            return (Criteria) this;
        }

        public Criteria andAllocationTimeEqualTo(Date value) {
            addCriterion("allocation_time =", value, "allocationTime");
            return (Criteria) this;
        }

        public Criteria andAllocationTimeNotEqualTo(Date value) {
            addCriterion("allocation_time <>", value, "allocationTime");
            return (Criteria) this;
        }

        public Criteria andAllocationTimeGreaterThan(Date value) {
            addCriterion("allocation_time >", value, "allocationTime");
            return (Criteria) this;
        }

        public Criteria andAllocationTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("allocation_time >=", value, "allocationTime");
            return (Criteria) this;
        }

        public Criteria andAllocationTimeLessThan(Date value) {
            addCriterion("allocation_time <", value, "allocationTime");
            return (Criteria) this;
        }

        public Criteria andAllocationTimeLessThanOrEqualTo(Date value) {
            addCriterion("allocation_time <=", value, "allocationTime");
            return (Criteria) this;
        }

        public Criteria andAllocationTimeIn(List<Date> values) {
            addCriterion("allocation_time in", values, "allocationTime");
            return (Criteria) this;
        }

        public Criteria andAllocationTimeNotIn(List<Date> values) {
            addCriterion("allocation_time not in", values, "allocationTime");
            return (Criteria) this;
        }

        public Criteria andAllocationTimeBetween(Date value1, Date value2) {
            addCriterion("allocation_time between", value1, value2, "allocationTime");
            return (Criteria) this;
        }

        public Criteria andAllocationTimeNotBetween(Date value1, Date value2) {
            addCriterion("allocation_time not between", value1, value2, "allocationTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wave_check_in_all_mic_gift_record
     *
     * @mbg.generated do_not_delete_during_merge Wed Mar 05 17:16:09 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wave_check_in_all_mic_gift_record
     *
     * @mbg.generated Wed Mar 05 17:16:09 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}