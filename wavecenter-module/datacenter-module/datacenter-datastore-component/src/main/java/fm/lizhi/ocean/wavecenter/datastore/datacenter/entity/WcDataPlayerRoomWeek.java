package fm.lizhi.ocean.wavecenter.datastore.datacenter.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 大数据主播周统计表-主播厅公会维度
 *
 * @date 2024-04-29 02:59:17
 */
@Table(name = "`wavecenter_data_player_room_week`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcDataPlayerRoomWeek {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 业务
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 日期 格式 YYYY-MM-DD
     */
    @Column(name= "`start_week_date`")
    private Date startWeekDate;

    /**
     * 日期 格式 YYYY-MM-DD
     */
    @Column(name= "`end_week_date`")
    private Date endWeekDate;

    /**
     * 陪玩ID
     */
    @Column(name= "`player_id`")
    private Long playerId;

    /**
     * 厅主ID
     */
    @Column(name= "`room_id`")
    private Long roomId;

    /**
     * 公会ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 公会收入,公会考核期间总收入，单位：钻（结算币)
     */
    @Column(name= "`income`")
    private BigDecimal income;

    /**
     * 公会魅力值        公会考核期间总魅力值，单位：魅力值
     */
    @Column(name= "`charm`")
    private Integer charm;

    /**
     * 上麦时长(分钟)
     */
    @Column(name= "`up_guest_dur`")
    private BigDecimal upGuestDur;

    /**
     * 送礼人数
     */
    @Column(name= "`gift_user_cnt`")
    private Integer giftUserCnt;

    /**
     * 送礼客单价
     */
    @Column(name= "`gift_user_price`")
    private BigDecimal giftUserPrice;

    /**
     * 私信人数
     */
    @Column(name= "`chat_user_cnt`")
    private Integer chatUserCnt;

    /**
     * 私信回复人数
     */
    @Column(name= "`reply_chat_user_cnt`")
    private Integer replyChatUserCnt;

    /**
     * 私信进房人数
     */
    @Column(name= "`chat_enter_room_user_cnt`")
    private Integer chatEnterRoomUserCnt;

    /**
     * 私信付费人数
     */
    @Column(name= "`chat_gift_user_cnt`")
    private Integer chatGiftUserCnt;

    /**
     * 私信回复率
     */
    @Column(name= "`reply_chat_rate`")
    private BigDecimal replyChatRate;

    /**
     * 私信进房率
     */
    @Column(name= "`chat_enter_room_rate`")
    private BigDecimal chatEnterRoomRate;

    /**
     * 私信付费率
     */
    @Column(name= "`chat_gift_rate`")
    private BigDecimal chatGiftRate;

    /**
     * 邀请人数
     */
    @Column(name= "`invite_user_cnt`")
    private Integer inviteUserCnt;

    /**
     * 邀请进房人数
     */
    @Column(name= "`invite_enter_room_user_cnt`")
    private Integer inviteEnterRoomUserCnt;

    /**
     * 邀请付费人数
     */
    @Column(name= "`invite_gift_user_cnt`")
    private Integer inviteGiftUserCnt;

    /**
     * 邀请进房率
     */
    @Column(name= "`invite_enter_room_rate`")
    private BigDecimal inviteEnterRoomRate;

    /**
     * 邀请付费率
     */
    @Column(name= "`invite_gift_rate`")
    private BigDecimal inviteGiftRate;

    /**
     * 主播粉丝数
     */
    @Column(name= "`fans_user_cnt`")
    private Integer fansUserCnt;

    /**
     * 新增粉丝数
     */
    @Column(name= "`new_fans_user_cnt`")
    private Integer newFansUserCnt;

    /**
     * 粉丝送礼收入
     */
    @Column(name= "`fans_gift_income`")
    private BigDecimal fansGiftIncome;

    /**
     * 粉丝送礼人数
     */
    @Column(name= "`fans_gift_user_cnt`")
    private Integer fansGiftUserCnt;

    /**
     * 粉丝送礼客单价
     */
    @Column(name= "`fans_gift_user_price`")
    private BigDecimal fansGiftUserPrice;

    /**
     * 主播签约厅的主播数
     */
    @Column(name= "`room_sign_player_cnt`")
    private Integer roomSignPlayerCnt;

    /**
     * 主播的收入在厅的排名
     */
    @Column(name= "`income_rank_room`")
    private Integer incomeRankRoom;

    /**
     * 主播的收入在工会的排名
     */
    @Column(name= "`income_rank_family`")
    private Integer incomeRankFamily;

    /**
     * 主播的魅力值在厅的排名
     */
    @Column(name= "`charm_rank_room`")
    private Integer charmRankRoom;

    /**
     * 主播的魅力值在工会的排名
     */
    @Column(name= "`charm_rank_family`")
    private Integer charmRankFamily;

    /**
     * 角色创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 角色修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 签约厅收礼收入
     */
    @Column(name= "`sign_hall_income`")
    private BigDecimal signHallIncome;

    /**
     * 官方厅收礼收入
     */
    @Column(name= "`official_hall_income`")
    private BigDecimal officialHallIncome;

    /**
     * 个播收礼收入
     */
    @Column(name= "`personal_hall_income`")
    private BigDecimal personalHallIncome;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", startWeekDate=").append(startWeekDate);
        sb.append(", endWeekDate=").append(endWeekDate);
        sb.append(", playerId=").append(playerId);
        sb.append(", roomId=").append(roomId);
        sb.append(", familyId=").append(familyId);
        sb.append(", income=").append(income);
        sb.append(", charm=").append(charm);
        sb.append(", upGuestDur=").append(upGuestDur);
        sb.append(", giftUserCnt=").append(giftUserCnt);
        sb.append(", giftUserPrice=").append(giftUserPrice);
        sb.append(", chatUserCnt=").append(chatUserCnt);
        sb.append(", replyChatUserCnt=").append(replyChatUserCnt);
        sb.append(", chatEnterRoomUserCnt=").append(chatEnterRoomUserCnt);
        sb.append(", chatGiftUserCnt=").append(chatGiftUserCnt);
        sb.append(", replyChatRate=").append(replyChatRate);
        sb.append(", chatEnterRoomRate=").append(chatEnterRoomRate);
        sb.append(", chatGiftRate=").append(chatGiftRate);
        sb.append(", inviteUserCnt=").append(inviteUserCnt);
        sb.append(", inviteEnterRoomUserCnt=").append(inviteEnterRoomUserCnt);
        sb.append(", inviteGiftUserCnt=").append(inviteGiftUserCnt);
        sb.append(", inviteEnterRoomRate=").append(inviteEnterRoomRate);
        sb.append(", inviteGiftRate=").append(inviteGiftRate);
        sb.append(", fansUserCnt=").append(fansUserCnt);
        sb.append(", newFansUserCnt=").append(newFansUserCnt);
        sb.append(", fansGiftIncome=").append(fansGiftIncome);
        sb.append(", fansGiftUserCnt=").append(fansGiftUserCnt);
        sb.append(", fansGiftUserPrice=").append(fansGiftUserPrice);
        sb.append(", roomSignPlayerCnt=").append(roomSignPlayerCnt);
        sb.append(", incomeRankRoom=").append(incomeRankRoom);
        sb.append(", incomeRankFamily=").append(incomeRankFamily);
        sb.append(", charmRankRoom=").append(charmRankRoom);
        sb.append(", charmRankFamily=").append(charmRankFamily);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", signHallIncome=").append(signHallIncome);
        sb.append(", officialHallIncome=").append(officialHallIncome);
        sb.append(", personalHallIncome=").append(personalHallIncome);
        sb.append("]");
        return sb.toString();
    }
}