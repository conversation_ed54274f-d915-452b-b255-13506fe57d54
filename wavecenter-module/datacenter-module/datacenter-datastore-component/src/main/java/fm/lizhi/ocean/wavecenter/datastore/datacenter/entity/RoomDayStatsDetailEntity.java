package fm.lizhi.ocean.wavecenter.datastore.datacenter.entity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class RoomDayStatsDetailEntity {

    /**
     * 统计时长
     */
    private Date statDate;

    private Long userId;

    private String income;


    private Integer charm;

    /**
     * 麦序
     */
    private Integer seatOrder;


    /**
     * 主持档
     */
    private Integer hostCnt;


}
