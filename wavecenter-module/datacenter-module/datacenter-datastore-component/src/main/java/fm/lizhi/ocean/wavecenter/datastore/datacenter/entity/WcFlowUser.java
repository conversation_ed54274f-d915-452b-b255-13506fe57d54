package fm.lizhi.ocean.wavecenter.datastore.datacenter.entity;

import lombok.*;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 支付流水表-主播维度
 *
 * @date 2024-04-24 11:28:12
 */
@Table(name = "`wavecenter_flow_user`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcFlowUser {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 业务
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 流水时间
     */
    @Column(name= "`flow_date`")
    private Date flowDate;

    /**
     * 主播ID
     */
    @Column(name= "`user_id`")
    private Long userId;

    /**
     * 收入类型ID
     */
    @Column(name= "`biz_id`")
    private Integer bizId;

    /**
     * 收入类型名称
     */
    @Column(name= "`biz_name`")
    private String bizName;

    /**
     * 收入
     */
    @Column(name= "`income`")
    private BigDecimal income;

    /**
     * 收益
     */
    @Column(name="`income_amount`")
    private BigDecimal incomeAmount;

    /**
     * 流水内容
     */
    @Column(name= "`remark`")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", flowDate=").append(flowDate);
        sb.append(", userId=").append(userId);
        sb.append(", bizId=").append(bizId);
        sb.append(", bizName=").append(bizName);
        sb.append(", income=").append(income);
        sb.append(", incomeAmount=").append(incomeAmount);
        sb.append(", remark=").append(remark);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}
