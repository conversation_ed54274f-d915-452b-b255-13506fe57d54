package fm.lizhi.ocean.wavecenter.datastore.datacenter.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class WaveCheckInDayMicRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_day_mic_record
     *
     * @mbg.generated Wed Mar 05 19:56:38 CST 2025
     */
    public WaveCheckInDayMicRecordExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_day_mic_record
     *
     * @mbg.generated Wed Mar 05 19:56:38 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_day_mic_record
     *
     * @mbg.generated Wed Mar 05 19:56:38 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_day_mic_record
     *
     * @mbg.generated Wed Mar 05 19:56:38 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_day_mic_record
     *
     * @mbg.generated Wed Mar 05 19:56:38 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_day_mic_record
     *
     * @mbg.generated Wed Mar 05 19:56:38 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_day_mic_record
     *
     * @mbg.generated Wed Mar 05 19:56:38 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_day_mic_record
     *
     * @mbg.generated Wed Mar 05 19:56:38 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_day_mic_record
     *
     * @mbg.generated Wed Mar 05 19:56:38 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_day_mic_record
     *
     * @mbg.generated Wed Mar 05 19:56:38 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_day_mic_record
     *
     * @mbg.generated Wed Mar 05 19:56:38 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Class getEntityClass() {
        return WaveCheckInDayMicRecord.class;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wave_check_in_day_mic_record
     *
     * @mbg.generated Wed Mar 05 19:56:38 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(Integer value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(Integer value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(Integer value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(Integer value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(Integer value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<Integer> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<Integer> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(Integer value1, Integer value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(Integer value1, Integer value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(Long value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(Long value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(Long value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(Long value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Long value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<Long> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<Long> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(Long value1, Long value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(Long value1, Long value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andRoomIdIsNull() {
            addCriterion("room_id is null");
            return (Criteria) this;
        }

        public Criteria andRoomIdIsNotNull() {
            addCriterion("room_id is not null");
            return (Criteria) this;
        }

        public Criteria andRoomIdEqualTo(Long value) {
            addCriterion("room_id =", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotEqualTo(Long value) {
            addCriterion("room_id <>", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdGreaterThan(Long value) {
            addCriterion("room_id >", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdGreaterThanOrEqualTo(Long value) {
            addCriterion("room_id >=", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdLessThan(Long value) {
            addCriterion("room_id <", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdLessThanOrEqualTo(Long value) {
            addCriterion("room_id <=", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdIn(List<Long> values) {
            addCriterion("room_id in", values, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotIn(List<Long> values) {
            addCriterion("room_id not in", values, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdBetween(Long value1, Long value2) {
            addCriterion("room_id between", value1, value2, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotBetween(Long value1, Long value2) {
            addCriterion("room_id not between", value1, value2, "roomId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIsNull() {
            addCriterion("family_id is null");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIsNotNull() {
            addCriterion("family_id is not null");
            return (Criteria) this;
        }

        public Criteria andFamilyIdEqualTo(Long value) {
            addCriterion("family_id =", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotEqualTo(Long value) {
            addCriterion("family_id <>", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdGreaterThan(Long value) {
            addCriterion("family_id >", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("family_id >=", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdLessThan(Long value) {
            addCriterion("family_id <", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdLessThanOrEqualTo(Long value) {
            addCriterion("family_id <=", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIn(List<Long> values) {
            addCriterion("family_id in", values, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotIn(List<Long> values) {
            addCriterion("family_id not in", values, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdBetween(Long value1, Long value2) {
            addCriterion("family_id between", value1, value2, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotBetween(Long value1, Long value2) {
            addCriterion("family_id not between", value1, value2, "familyId");
            return (Criteria) this;
        }

        public Criteria andCalcDateIsNull() {
            addCriterion("calc_date is null");
            return (Criteria) this;
        }

        public Criteria andCalcDateIsNotNull() {
            addCriterion("calc_date is not null");
            return (Criteria) this;
        }

        public Criteria andCalcDateEqualTo(Date value) {
            addCriterionForJDBCDate("calc_date =", value, "calcDate");
            return (Criteria) this;
        }

        public Criteria andCalcDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("calc_date <>", value, "calcDate");
            return (Criteria) this;
        }

        public Criteria andCalcDateGreaterThan(Date value) {
            addCriterionForJDBCDate("calc_date >", value, "calcDate");
            return (Criteria) this;
        }

        public Criteria andCalcDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("calc_date >=", value, "calcDate");
            return (Criteria) this;
        }

        public Criteria andCalcDateLessThan(Date value) {
            addCriterionForJDBCDate("calc_date <", value, "calcDate");
            return (Criteria) this;
        }

        public Criteria andCalcDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("calc_date <=", value, "calcDate");
            return (Criteria) this;
        }

        public Criteria andCalcDateIn(List<Date> values) {
            addCriterionForJDBCDate("calc_date in", values, "calcDate");
            return (Criteria) this;
        }

        public Criteria andCalcDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("calc_date not in", values, "calcDate");
            return (Criteria) this;
        }

        public Criteria andCalcDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("calc_date between", value1, value2, "calcDate");
            return (Criteria) this;
        }

        public Criteria andCalcDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("calc_date not between", value1, value2, "calcDate");
            return (Criteria) this;
        }

        public Criteria andCharmIsNull() {
            addCriterion("charm is null");
            return (Criteria) this;
        }

        public Criteria andCharmIsNotNull() {
            addCriterion("charm is not null");
            return (Criteria) this;
        }

        public Criteria andCharmEqualTo(Long value) {
            addCriterion("charm =", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmNotEqualTo(Long value) {
            addCriterion("charm <>", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmGreaterThan(Long value) {
            addCriterion("charm >", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmGreaterThanOrEqualTo(Long value) {
            addCriterion("charm >=", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmLessThan(Long value) {
            addCriterion("charm <", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmLessThanOrEqualTo(Long value) {
            addCriterion("charm <=", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmIn(List<Long> values) {
            addCriterion("charm in", values, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmNotIn(List<Long> values) {
            addCriterion("charm not in", values, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmBetween(Long value1, Long value2) {
            addCriterion("charm between", value1, value2, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmNotBetween(Long value1, Long value2) {
            addCriterion("charm not between", value1, value2, "charm");
            return (Criteria) this;
        }

        public Criteria andValidMicCountIsNull() {
            addCriterion("valid_mic_count is null");
            return (Criteria) this;
        }

        public Criteria andValidMicCountIsNotNull() {
            addCriterion("valid_mic_count is not null");
            return (Criteria) this;
        }

        public Criteria andValidMicCountEqualTo(Long value) {
            addCriterion("valid_mic_count =", value, "validMicCount");
            return (Criteria) this;
        }

        public Criteria andValidMicCountNotEqualTo(Long value) {
            addCriterion("valid_mic_count <>", value, "validMicCount");
            return (Criteria) this;
        }

        public Criteria andValidMicCountGreaterThan(Long value) {
            addCriterion("valid_mic_count >", value, "validMicCount");
            return (Criteria) this;
        }

        public Criteria andValidMicCountGreaterThanOrEqualTo(Long value) {
            addCriterion("valid_mic_count >=", value, "validMicCount");
            return (Criteria) this;
        }

        public Criteria andValidMicCountLessThan(Long value) {
            addCriterion("valid_mic_count <", value, "validMicCount");
            return (Criteria) this;
        }

        public Criteria andValidMicCountLessThanOrEqualTo(Long value) {
            addCriterion("valid_mic_count <=", value, "validMicCount");
            return (Criteria) this;
        }

        public Criteria andValidMicCountIn(List<Long> values) {
            addCriterion("valid_mic_count in", values, "validMicCount");
            return (Criteria) this;
        }

        public Criteria andValidMicCountNotIn(List<Long> values) {
            addCriterion("valid_mic_count not in", values, "validMicCount");
            return (Criteria) this;
        }

        public Criteria andValidMicCountBetween(Long value1, Long value2) {
            addCriterion("valid_mic_count between", value1, value2, "validMicCount");
            return (Criteria) this;
        }

        public Criteria andValidMicCountNotBetween(Long value1, Long value2) {
            addCriterion("valid_mic_count not between", value1, value2, "validMicCount");
            return (Criteria) this;
        }

        public Criteria andCalcMicCountIsNull() {
            addCriterion("calc_mic_count is null");
            return (Criteria) this;
        }

        public Criteria andCalcMicCountIsNotNull() {
            addCriterion("calc_mic_count is not null");
            return (Criteria) this;
        }

        public Criteria andCalcMicCountEqualTo(Long value) {
            addCriterion("calc_mic_count =", value, "calcMicCount");
            return (Criteria) this;
        }

        public Criteria andCalcMicCountNotEqualTo(Long value) {
            addCriterion("calc_mic_count <>", value, "calcMicCount");
            return (Criteria) this;
        }

        public Criteria andCalcMicCountGreaterThan(Long value) {
            addCriterion("calc_mic_count >", value, "calcMicCount");
            return (Criteria) this;
        }

        public Criteria andCalcMicCountGreaterThanOrEqualTo(Long value) {
            addCriterion("calc_mic_count >=", value, "calcMicCount");
            return (Criteria) this;
        }

        public Criteria andCalcMicCountLessThan(Long value) {
            addCriterion("calc_mic_count <", value, "calcMicCount");
            return (Criteria) this;
        }

        public Criteria andCalcMicCountLessThanOrEqualTo(Long value) {
            addCriterion("calc_mic_count <=", value, "calcMicCount");
            return (Criteria) this;
        }

        public Criteria andCalcMicCountIn(List<Long> values) {
            addCriterion("calc_mic_count in", values, "calcMicCount");
            return (Criteria) this;
        }

        public Criteria andCalcMicCountNotIn(List<Long> values) {
            addCriterion("calc_mic_count not in", values, "calcMicCount");
            return (Criteria) this;
        }

        public Criteria andCalcMicCountBetween(Long value1, Long value2) {
            addCriterion("calc_mic_count between", value1, value2, "calcMicCount");
            return (Criteria) this;
        }

        public Criteria andCalcMicCountNotBetween(Long value1, Long value2) {
            addCriterion("calc_mic_count not between", value1, value2, "calcMicCount");
            return (Criteria) this;
        }

        public Criteria andCalcTypeIsNull() {
            addCriterion("calc_type is null");
            return (Criteria) this;
        }

        public Criteria andCalcTypeIsNotNull() {
            addCriterion("calc_type is not null");
            return (Criteria) this;
        }

        public Criteria andCalcTypeEqualTo(Integer value) {
            addCriterion("calc_type =", value, "calcType");
            return (Criteria) this;
        }

        public Criteria andCalcTypeNotEqualTo(Integer value) {
            addCriterion("calc_type <>", value, "calcType");
            return (Criteria) this;
        }

        public Criteria andCalcTypeGreaterThan(Integer value) {
            addCriterion("calc_type >", value, "calcType");
            return (Criteria) this;
        }

        public Criteria andCalcTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("calc_type >=", value, "calcType");
            return (Criteria) this;
        }

        public Criteria andCalcTypeLessThan(Integer value) {
            addCriterion("calc_type <", value, "calcType");
            return (Criteria) this;
        }

        public Criteria andCalcTypeLessThanOrEqualTo(Integer value) {
            addCriterion("calc_type <=", value, "calcType");
            return (Criteria) this;
        }

        public Criteria andCalcTypeIn(List<Integer> values) {
            addCriterion("calc_type in", values, "calcType");
            return (Criteria) this;
        }

        public Criteria andCalcTypeNotIn(List<Integer> values) {
            addCriterion("calc_type not in", values, "calcType");
            return (Criteria) this;
        }

        public Criteria andCalcTypeBetween(Integer value1, Integer value2) {
            addCriterion("calc_type between", value1, value2, "calcType");
            return (Criteria) this;
        }

        public Criteria andCalcTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("calc_type not between", value1, value2, "calcType");
            return (Criteria) this;
        }

        public Criteria andRewardAmountSumIsNull() {
            addCriterion("reward_amount_sum is null");
            return (Criteria) this;
        }

        public Criteria andRewardAmountSumIsNotNull() {
            addCriterion("reward_amount_sum is not null");
            return (Criteria) this;
        }

        public Criteria andRewardAmountSumEqualTo(Long value) {
            addCriterion("reward_amount_sum =", value, "rewardAmountSum");
            return (Criteria) this;
        }

        public Criteria andRewardAmountSumNotEqualTo(Long value) {
            addCriterion("reward_amount_sum <>", value, "rewardAmountSum");
            return (Criteria) this;
        }

        public Criteria andRewardAmountSumGreaterThan(Long value) {
            addCriterion("reward_amount_sum >", value, "rewardAmountSum");
            return (Criteria) this;
        }

        public Criteria andRewardAmountSumGreaterThanOrEqualTo(Long value) {
            addCriterion("reward_amount_sum >=", value, "rewardAmountSum");
            return (Criteria) this;
        }

        public Criteria andRewardAmountSumLessThan(Long value) {
            addCriterion("reward_amount_sum <", value, "rewardAmountSum");
            return (Criteria) this;
        }

        public Criteria andRewardAmountSumLessThanOrEqualTo(Long value) {
            addCriterion("reward_amount_sum <=", value, "rewardAmountSum");
            return (Criteria) this;
        }

        public Criteria andRewardAmountSumIn(List<Long> values) {
            addCriterion("reward_amount_sum in", values, "rewardAmountSum");
            return (Criteria) this;
        }

        public Criteria andRewardAmountSumNotIn(List<Long> values) {
            addCriterion("reward_amount_sum not in", values, "rewardAmountSum");
            return (Criteria) this;
        }

        public Criteria andRewardAmountSumBetween(Long value1, Long value2) {
            addCriterion("reward_amount_sum between", value1, value2, "rewardAmountSum");
            return (Criteria) this;
        }

        public Criteria andRewardAmountSumNotBetween(Long value1, Long value2) {
            addCriterion("reward_amount_sum not between", value1, value2, "rewardAmountSum");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wave_check_in_day_mic_record
     *
     * @mbg.generated do_not_delete_during_merge Wed Mar 05 19:56:38 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wave_check_in_day_mic_record
     *
     * @mbg.generated Wed Mar 05 19:56:38 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}