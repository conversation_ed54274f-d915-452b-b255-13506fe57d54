package fm.lizhi.ocean.wavecenter.datastore.datacenter.entity;

import lombok.*;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 *
 * @date 2024-06-11 03:31:34
 */
@Table(name = "`waveconter_room_check_in_day_stats`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcRoomCheckInDayStats {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 家族ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 厅主ID
     */
    @Column(name= "`nj_id`")
    private Long njId;

    /**
     * 收入
     */
    @Column(name= "`income`")
    private BigDecimal income;

    /**
     * 魅力值
     */
    @Column(name= "`charm`")
    private Long charm;

    /**
     * 有收入主播数
     */
    @Column(name= "`income_player_num`")
    private Integer incomePlayerNum;

    /**
     * 打卡主播数
     */
    @Column(name= "`check_in_player`")
    private Long checkInPlayer;

    /**
     * 打卡麦序
     */
    @Column(name= "`seat_order`")
    private Long seatOrder;

    @Column(name= "`stat_date`")
    private Date statDate;

    @Column(name= "`create_time`")
    private Date createTime;

    @Column(name = "host_num")
    private Integer hostNum;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", familyId=").append(familyId);
        sb.append(", hostNum=").append(hostNum);
        sb.append(", njId=").append(njId);
        sb.append(", income=").append(income);
        sb.append(", charm=").append(charm);
        sb.append(", incomePlayerNum=").append(incomePlayerNum);
        sb.append(", checkInPlayer=").append(checkInPlayer);
        sb.append(", seatOrder=").append(seatOrder);
        sb.append(", statDate=").append(statDate);
        sb.append(", createTime=").append(createTime);
        sb.append("]");
        return sb.toString();
    }
}
