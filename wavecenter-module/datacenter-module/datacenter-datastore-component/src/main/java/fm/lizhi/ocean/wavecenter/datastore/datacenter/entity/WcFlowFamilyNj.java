package fm.lizhi.ocean.wavecenter.datastore.datacenter.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 支付流水表-厅&公会维度
 *
 * @date 2024-04-24 11:28:12
 */
@Table(name = "`wavecenter_flow_family_nj`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcFlowFamilyNj {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 业务
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 流水时间
     */
    @Column(name= "`flow_date`")
    private Date flowDate;

    /**
     * 公会ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 厅主ID
     */
    @Column(name= "`nj_id`")
    private Long njId;

    /**
     * 厅主波段号
     */
    @Column(name= "`nj_band`")
    private String njBand;

    /**
     * 厅主昵称
     */
    @Column(name= "`nj_name`")
    private String njName;

    /**
     * 收入类型ID
     */
    @Column(name= "`biz_id`")
    private Integer bizId;

    /**
     * 收入类型名称
     */
    @Column(name= "`biz_name`")
    private String bizName;

    /**
     * 收入
     */
    @Column(name= "`income`")
    private BigDecimal income;

    /**
     * 流水内容
     */
    @Column(name= "`remark`")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", flowDate=").append(flowDate);
        sb.append(", familyId=").append(familyId);
        sb.append(", njId=").append(njId);
        sb.append(", njBand=").append(njBand);
        sb.append(", njName=").append(njName);
        sb.append(", bizId=").append(bizId);
        sb.append(", bizName=").append(bizName);
        sb.append(", income=").append(income);
        sb.append(", remark=").append(remark);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}