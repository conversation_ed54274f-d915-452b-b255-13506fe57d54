package fm.lizhi.ocean.wavecenter.datastore.datacenter.entity;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/4/24 14:58
 */
@Data
public class LiveGiveGiftActionPo {

    private Date createTime;

    /**
     * 厅主播ID
     */
    private Long njId;

    /**
     * 送礼人ID
     */
    private Long userId;

    /**
     * 收礼人
     */
    private Long recUserId;

    private Long liveId;

    private Long giftId;

    private Integer giftType;

    private Integer totalLitchiAmount;

    /**
     * 值，礼物类型为3时表示魅力值，可正可负
     */
    private Integer value;

    /**
     * 流水ID
     */
    private Long id;

}
