package fm.lizhi.ocean.wavecenter.datastore.datacenter.entity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class RoomDayCalendarEntity {


    private Date statDate;

    private BigDecimal income;


    private Integer charm;

    /**
     * 麦序
     */
    private Integer seatOrder;

    /**
     * 打卡主播数
     */
    private Integer checkPlayerNumber;


}
