package fm.lizhi.ocean.wavecenter.datastore.datacenter.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 大数据公会周统计表
 *
 * @date 2025-03-25 10:23:22
 */
@Table(name = "`wavecenter_data_family_week`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FieldNameConstants
public class WcDataFamilyWeek {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 业务
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 日期 格式 YYYY-MM-DD
     */
    @Column(name= "`start_week_date`")
    private Date startWeekDate;

    /**
     * 日期 格式 YYYY-MM-DD
     */
    @Column(name= "`end_week_date`")
    private Date endWeekDate;

    /**
     * 公会ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 公会收入,公会考核期间总收入，单位：钻（结算币)
     */
    @Column(name= "`income`")
    private BigDecimal income;

    /**
     * 公会魅力值        公会考核期间总魅力值，单位：魅力值
     */
    @Column(name= "`charm`")
    private Integer charm;

    /**
     * 签约厅数        公会签约厅数
     */
    @Column(name= "`sign_room_cnt`")
    private Integer signRoomCnt;

    /**
     * 开播厅数        公会开播厅数
     */
    @Column(name= "`open_room_cnt`")
    private Integer openRoomCnt;

    /**
     * 厅均收入 公会收入/开播厅数
     */
    @Column(name= "`room_avg_income`")
    private BigDecimal roomAvgIncome;

    /**
     * 厅均魅力值 公会魅力值/开播厅数
     */
    @Column(name= "`room_avg_charm`")
    private BigDecimal roomAvgCharm;

    /**
     * 签约主播数
     */
    @Column(name= "`sign_player_cnt`")
    private Integer signPlayerCnt;

    /**
     * 上麦主播数 直播间上麦人数
     */
    @Column(name= "`up_guest_player_cnt`")
    private Integer upGuestPlayerCnt;

    /**
     * 有收入主播数 直播间有收入签约主播人数
     */
    @Column(name= "`income_player_cnt`")
    private Integer incomePlayerCnt;

    /**
     * 人均收入 公会收入/有收入主播数
     */
    @Column(name= "`player_avg_income`")
    private BigDecimal playerAvgIncome;

    /**
     * 人均魅力值 公会魅力值/有收入主播数
     */
    @Column(name= "`player_avg_charm`")
    private BigDecimal playerAvgCharm;

    /**
     * 角色创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 角色修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 总收入
     */
    @Column(name= "`all_income`")
    private BigDecimal allIncome;

    /**
     * 签约厅收礼收入
     */
    @Column(name= "`sign_hall_income`")
    private BigDecimal signHallIncome;

    /**
     * 官方厅收礼收入
     */
    @Column(name= "`official_hall_income`")
    private BigDecimal officialHallIncome;

    /**
     * 个播收礼收入
     */
    @Column(name= "`personal_hall_income`")
    private BigDecimal personalHallIncome;

    /**
     * 贵族提成收入
     */
    @Column(name= "`noble_income`")
    private BigDecimal nobleIncome;

    /**
     * 个播贵族提成收入
     */
    @Column(name= "`personal_noble_income`")
    private BigDecimal personalNobleIncome;

    /**
     * 上麦率
     */
    @Column(name= "`up_player_rate`")
    private BigDecimal upPlayerRate;

    /**
     * 开播率
     */
    @Column(name= "`open_rate`")
    private BigDecimal openRate;

    /**
     * 有收入主播占比
     */
    @Column(name= "`income_player_rate`")
    private BigDecimal incomePlayerRate;

    /**
     * 有收入厅数
     */
    @Column(name= "`income_room_cnt`")
    private Integer incomeRoomCnt;

    /**
     * 有收入厅占比
     */
    @Column(name= "`income_room_rate`")
    private BigDecimal incomeRoomRate;

    /**
     * 有收入歌手数量
     */
    @Column(name= "`income_singer_cnt`")
    private Integer incomeSingerCnt;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", startWeekDate=").append(startWeekDate);
        sb.append(", endWeekDate=").append(endWeekDate);
        sb.append(", familyId=").append(familyId);
        sb.append(", income=").append(income);
        sb.append(", charm=").append(charm);
        sb.append(", signRoomCnt=").append(signRoomCnt);
        sb.append(", openRoomCnt=").append(openRoomCnt);
        sb.append(", roomAvgIncome=").append(roomAvgIncome);
        sb.append(", roomAvgCharm=").append(roomAvgCharm);
        sb.append(", signPlayerCnt=").append(signPlayerCnt);
        sb.append(", upGuestPlayerCnt=").append(upGuestPlayerCnt);
        sb.append(", incomePlayerCnt=").append(incomePlayerCnt);
        sb.append(", playerAvgIncome=").append(playerAvgIncome);
        sb.append(", playerAvgCharm=").append(playerAvgCharm);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", allIncome=").append(allIncome);
        sb.append(", signHallIncome=").append(signHallIncome);
        sb.append(", officialHallIncome=").append(officialHallIncome);
        sb.append(", personalHallIncome=").append(personalHallIncome);
        sb.append(", nobleIncome=").append(nobleIncome);
        sb.append(", personalNobleIncome=").append(personalNobleIncome);
        sb.append(", upPlayerRate=").append(upPlayerRate);
        sb.append(", openRate=").append(openRate);
        sb.append(", incomePlayerRate=").append(incomePlayerRate);
        sb.append(", incomeRoomCnt=").append(incomeRoomCnt);
        sb.append(", incomeRoomRate=").append(incomeRoomRate);
        sb.append(", incomeSingerCnt=").append(incomeSingerCnt);
        sb.append("]");
        return sb.toString();
    }
}