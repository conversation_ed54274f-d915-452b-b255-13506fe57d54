package fm.lizhi.ocean.wavecenter.datastore.datacenter.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 打卡记录表
 *
 * @date 2025-01-09 04:06:57
 */
@Table(name = "`wave_check_in_record`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WaveCheckInRecord {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 档期ID
     */
    @Column(name= "`schedule_id`")
    private Long scheduleId;

    @Column(name= "`room_id`")
    private Long roomId;

    /**
     * 主播ID
     */
    @Column(name= "`user_id`")
    private Long userId;

    /**
     * 收入
     */
    @Column(name= "`income`")
    private Long income;

    /**
     * 调整后最终的魅力值
     */
    @Column(name= "`charm_value`")
    private Long charmValue;

    /**
     * 0：未打卡，1：已打卡，2：确认打卡
     */
    @Column(name= "`status`")
    private Integer status;

    /**
     * 原魅力值
     */
    @Column(name= "`original_value`")
    private Long originalValue;

    /**
     * 调账差值
     */
    @Column(name= "`charm_diff_value`")
    private Integer charmDiffValue;

    /**
     * 是否是定档主播, 0 非定档主播, 1 定档主播
     */
    @Column(name= "`scheduled`")
    private Integer scheduled;

    /**
     * 备注
     */
    @Column(name= "`remark`")
    private String remark;

    /**
     * 来源类型，0：系统添加，1：主播添加
     */
    @Column(name= "`source_type`")
    private Integer sourceType;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", scheduleId=").append(scheduleId);
        sb.append(", roomId=").append(roomId);
        sb.append(", userId=").append(userId);
        sb.append(", income=").append(income);
        sb.append(", charmValue=").append(charmValue);
        sb.append(", status=").append(status);
        sb.append(", originalValue=").append(originalValue);
        sb.append(", charmDiffValue=").append(charmDiffValue);
        sb.append(", scheduled=").append(scheduled);
        sb.append(", remark=").append(remark);
        sb.append(", sourceType=").append(sourceType);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", createTime=").append(createTime);
        sb.append("]");
        return sb.toString();
    }
}