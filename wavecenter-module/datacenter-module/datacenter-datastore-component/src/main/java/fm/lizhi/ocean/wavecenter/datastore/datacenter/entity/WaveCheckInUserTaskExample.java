package fm.lizhi.ocean.wavecenter.datastore.datacenter.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WaveCheckInUserTaskExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_user_task
     *
     * @mbg.generated Thu Jan 09 16:06:57 CST 2025
     */
    public WaveCheckInUserTaskExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_user_task
     *
     * @mbg.generated Thu Jan 09 16:06:57 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_user_task
     *
     * @mbg.generated Thu Jan 09 16:06:57 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_user_task
     *
     * @mbg.generated Thu Jan 09 16:06:57 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_user_task
     *
     * @mbg.generated Thu Jan 09 16:06:57 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_user_task
     *
     * @mbg.generated Thu Jan 09 16:06:57 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_user_task
     *
     * @mbg.generated Thu Jan 09 16:06:57 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_user_task
     *
     * @mbg.generated Thu Jan 09 16:06:57 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_user_task
     *
     * @mbg.generated Thu Jan 09 16:06:57 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_user_task
     *
     * @mbg.generated Thu Jan 09 16:06:57 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wave_check_in_user_task
     *
     * @mbg.generated Thu Jan 09 16:06:57 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Class getEntityClass() {
        return WaveCheckInUserTask.class;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wave_check_in_user_task
     *
     * @mbg.generated Thu Jan 09 16:06:57 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andRecordIdIsNull() {
            addCriterion("record_id is null");
            return (Criteria) this;
        }

        public Criteria andRecordIdIsNotNull() {
            addCriterion("record_id is not null");
            return (Criteria) this;
        }

        public Criteria andRecordIdEqualTo(Long value) {
            addCriterion("record_id =", value, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdNotEqualTo(Long value) {
            addCriterion("record_id <>", value, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdGreaterThan(Long value) {
            addCriterion("record_id >", value, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdGreaterThanOrEqualTo(Long value) {
            addCriterion("record_id >=", value, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdLessThan(Long value) {
            addCriterion("record_id <", value, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdLessThanOrEqualTo(Long value) {
            addCriterion("record_id <=", value, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdIn(List<Long> values) {
            addCriterion("record_id in", values, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdNotIn(List<Long> values) {
            addCriterion("record_id not in", values, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdBetween(Long value1, Long value2) {
            addCriterion("record_id between", value1, value2, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdNotBetween(Long value1, Long value2) {
            addCriterion("record_id not between", value1, value2, "recordId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdIsNull() {
            addCriterion("schedule_id is null");
            return (Criteria) this;
        }

        public Criteria andScheduleIdIsNotNull() {
            addCriterion("schedule_id is not null");
            return (Criteria) this;
        }

        public Criteria andScheduleIdEqualTo(Long value) {
            addCriterion("schedule_id =", value, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdNotEqualTo(Long value) {
            addCriterion("schedule_id <>", value, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdGreaterThan(Long value) {
            addCriterion("schedule_id >", value, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdGreaterThanOrEqualTo(Long value) {
            addCriterion("schedule_id >=", value, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdLessThan(Long value) {
            addCriterion("schedule_id <", value, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdLessThanOrEqualTo(Long value) {
            addCriterion("schedule_id <=", value, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdIn(List<Long> values) {
            addCriterion("schedule_id in", values, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdNotIn(List<Long> values) {
            addCriterion("schedule_id not in", values, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdBetween(Long value1, Long value2) {
            addCriterion("schedule_id between", value1, value2, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdNotBetween(Long value1, Long value2) {
            addCriterion("schedule_id not between", value1, value2, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(Long value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(Long value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(Long value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(Long value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Long value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<Long> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<Long> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(Long value1, Long value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(Long value1, Long value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andNjIdIsNull() {
            addCriterion("nj_id is null");
            return (Criteria) this;
        }

        public Criteria andNjIdIsNotNull() {
            addCriterion("nj_id is not null");
            return (Criteria) this;
        }

        public Criteria andNjIdEqualTo(Long value) {
            addCriterion("nj_id =", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdNotEqualTo(Long value) {
            addCriterion("nj_id <>", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdGreaterThan(Long value) {
            addCriterion("nj_id >", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdGreaterThanOrEqualTo(Long value) {
            addCriterion("nj_id >=", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdLessThan(Long value) {
            addCriterion("nj_id <", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdLessThanOrEqualTo(Long value) {
            addCriterion("nj_id <=", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdIn(List<Long> values) {
            addCriterion("nj_id in", values, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdNotIn(List<Long> values) {
            addCriterion("nj_id not in", values, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdBetween(Long value1, Long value2) {
            addCriterion("nj_id between", value1, value2, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdNotBetween(Long value1, Long value2) {
            addCriterion("nj_id not between", value1, value2, "njId");
            return (Criteria) this;
        }

        public Criteria andTaskScoreIsNull() {
            addCriterion("task_score is null");
            return (Criteria) this;
        }

        public Criteria andTaskScoreIsNotNull() {
            addCriterion("task_score is not null");
            return (Criteria) this;
        }

        public Criteria andTaskScoreEqualTo(Long value) {
            addCriterion("task_score =", value, "taskScore");
            return (Criteria) this;
        }

        public Criteria andTaskScoreNotEqualTo(Long value) {
            addCriterion("task_score <>", value, "taskScore");
            return (Criteria) this;
        }

        public Criteria andTaskScoreGreaterThan(Long value) {
            addCriterion("task_score >", value, "taskScore");
            return (Criteria) this;
        }

        public Criteria andTaskScoreGreaterThanOrEqualTo(Long value) {
            addCriterion("task_score >=", value, "taskScore");
            return (Criteria) this;
        }

        public Criteria andTaskScoreLessThan(Long value) {
            addCriterion("task_score <", value, "taskScore");
            return (Criteria) this;
        }

        public Criteria andTaskScoreLessThanOrEqualTo(Long value) {
            addCriterion("task_score <=", value, "taskScore");
            return (Criteria) this;
        }

        public Criteria andTaskScoreIn(List<Long> values) {
            addCriterion("task_score in", values, "taskScore");
            return (Criteria) this;
        }

        public Criteria andTaskScoreNotIn(List<Long> values) {
            addCriterion("task_score not in", values, "taskScore");
            return (Criteria) this;
        }

        public Criteria andTaskScoreBetween(Long value1, Long value2) {
            addCriterion("task_score between", value1, value2, "taskScore");
            return (Criteria) this;
        }

        public Criteria andTaskScoreNotBetween(Long value1, Long value2) {
            addCriterion("task_score not between", value1, value2, "taskScore");
            return (Criteria) this;
        }

        public Criteria andTaskScoreDiffIsNull() {
            addCriterion("task_score_diff is null");
            return (Criteria) this;
        }

        public Criteria andTaskScoreDiffIsNotNull() {
            addCriterion("task_score_diff is not null");
            return (Criteria) this;
        }

        public Criteria andTaskScoreDiffEqualTo(Long value) {
            addCriterion("task_score_diff =", value, "taskScoreDiff");
            return (Criteria) this;
        }

        public Criteria andTaskScoreDiffNotEqualTo(Long value) {
            addCriterion("task_score_diff <>", value, "taskScoreDiff");
            return (Criteria) this;
        }

        public Criteria andTaskScoreDiffGreaterThan(Long value) {
            addCriterion("task_score_diff >", value, "taskScoreDiff");
            return (Criteria) this;
        }

        public Criteria andTaskScoreDiffGreaterThanOrEqualTo(Long value) {
            addCriterion("task_score_diff >=", value, "taskScoreDiff");
            return (Criteria) this;
        }

        public Criteria andTaskScoreDiffLessThan(Long value) {
            addCriterion("task_score_diff <", value, "taskScoreDiff");
            return (Criteria) this;
        }

        public Criteria andTaskScoreDiffLessThanOrEqualTo(Long value) {
            addCriterion("task_score_diff <=", value, "taskScoreDiff");
            return (Criteria) this;
        }

        public Criteria andTaskScoreDiffIn(List<Long> values) {
            addCriterion("task_score_diff in", values, "taskScoreDiff");
            return (Criteria) this;
        }

        public Criteria andTaskScoreDiffNotIn(List<Long> values) {
            addCriterion("task_score_diff not in", values, "taskScoreDiff");
            return (Criteria) this;
        }

        public Criteria andTaskScoreDiffBetween(Long value1, Long value2) {
            addCriterion("task_score_diff between", value1, value2, "taskScoreDiff");
            return (Criteria) this;
        }

        public Criteria andTaskScoreDiffNotBetween(Long value1, Long value2) {
            addCriterion("task_score_diff not between", value1, value2, "taskScoreDiff");
            return (Criteria) this;
        }

        public Criteria andTaskProgressIsNull() {
            addCriterion("task_progress is null");
            return (Criteria) this;
        }

        public Criteria andTaskProgressIsNotNull() {
            addCriterion("task_progress is not null");
            return (Criteria) this;
        }

        public Criteria andTaskProgressEqualTo(Long value) {
            addCriterion("task_progress =", value, "taskProgress");
            return (Criteria) this;
        }

        public Criteria andTaskProgressNotEqualTo(Long value) {
            addCriterion("task_progress <>", value, "taskProgress");
            return (Criteria) this;
        }

        public Criteria andTaskProgressGreaterThan(Long value) {
            addCriterion("task_progress >", value, "taskProgress");
            return (Criteria) this;
        }

        public Criteria andTaskProgressGreaterThanOrEqualTo(Long value) {
            addCriterion("task_progress >=", value, "taskProgress");
            return (Criteria) this;
        }

        public Criteria andTaskProgressLessThan(Long value) {
            addCriterion("task_progress <", value, "taskProgress");
            return (Criteria) this;
        }

        public Criteria andTaskProgressLessThanOrEqualTo(Long value) {
            addCriterion("task_progress <=", value, "taskProgress");
            return (Criteria) this;
        }

        public Criteria andTaskProgressIn(List<Long> values) {
            addCriterion("task_progress in", values, "taskProgress");
            return (Criteria) this;
        }

        public Criteria andTaskProgressNotIn(List<Long> values) {
            addCriterion("task_progress not in", values, "taskProgress");
            return (Criteria) this;
        }

        public Criteria andTaskProgressBetween(Long value1, Long value2) {
            addCriterion("task_progress between", value1, value2, "taskProgress");
            return (Criteria) this;
        }

        public Criteria andTaskProgressNotBetween(Long value1, Long value2) {
            addCriterion("task_progress not between", value1, value2, "taskProgress");
            return (Criteria) this;
        }

        public Criteria andTaskProgressDiffIsNull() {
            addCriterion("task_progress_diff is null");
            return (Criteria) this;
        }

        public Criteria andTaskProgressDiffIsNotNull() {
            addCriterion("task_progress_diff is not null");
            return (Criteria) this;
        }

        public Criteria andTaskProgressDiffEqualTo(Long value) {
            addCriterion("task_progress_diff =", value, "taskProgressDiff");
            return (Criteria) this;
        }

        public Criteria andTaskProgressDiffNotEqualTo(Long value) {
            addCriterion("task_progress_diff <>", value, "taskProgressDiff");
            return (Criteria) this;
        }

        public Criteria andTaskProgressDiffGreaterThan(Long value) {
            addCriterion("task_progress_diff >", value, "taskProgressDiff");
            return (Criteria) this;
        }

        public Criteria andTaskProgressDiffGreaterThanOrEqualTo(Long value) {
            addCriterion("task_progress_diff >=", value, "taskProgressDiff");
            return (Criteria) this;
        }

        public Criteria andTaskProgressDiffLessThan(Long value) {
            addCriterion("task_progress_diff <", value, "taskProgressDiff");
            return (Criteria) this;
        }

        public Criteria andTaskProgressDiffLessThanOrEqualTo(Long value) {
            addCriterion("task_progress_diff <=", value, "taskProgressDiff");
            return (Criteria) this;
        }

        public Criteria andTaskProgressDiffIn(List<Long> values) {
            addCriterion("task_progress_diff in", values, "taskProgressDiff");
            return (Criteria) this;
        }

        public Criteria andTaskProgressDiffNotIn(List<Long> values) {
            addCriterion("task_progress_diff not in", values, "taskProgressDiff");
            return (Criteria) this;
        }

        public Criteria andTaskProgressDiffBetween(Long value1, Long value2) {
            addCriterion("task_progress_diff between", value1, value2, "taskProgressDiff");
            return (Criteria) this;
        }

        public Criteria andTaskProgressDiffNotBetween(Long value1, Long value2) {
            addCriterion("task_progress_diff not between", value1, value2, "taskProgressDiff");
            return (Criteria) this;
        }

        public Criteria andTaskRemainingScoreIsNull() {
            addCriterion("task_remaining_score is null");
            return (Criteria) this;
        }

        public Criteria andTaskRemainingScoreIsNotNull() {
            addCriterion("task_remaining_score is not null");
            return (Criteria) this;
        }

        public Criteria andTaskRemainingScoreEqualTo(Long value) {
            addCriterion("task_remaining_score =", value, "taskRemainingScore");
            return (Criteria) this;
        }

        public Criteria andTaskRemainingScoreNotEqualTo(Long value) {
            addCriterion("task_remaining_score <>", value, "taskRemainingScore");
            return (Criteria) this;
        }

        public Criteria andTaskRemainingScoreGreaterThan(Long value) {
            addCriterion("task_remaining_score >", value, "taskRemainingScore");
            return (Criteria) this;
        }

        public Criteria andTaskRemainingScoreGreaterThanOrEqualTo(Long value) {
            addCriterion("task_remaining_score >=", value, "taskRemainingScore");
            return (Criteria) this;
        }

        public Criteria andTaskRemainingScoreLessThan(Long value) {
            addCriterion("task_remaining_score <", value, "taskRemainingScore");
            return (Criteria) this;
        }

        public Criteria andTaskRemainingScoreLessThanOrEqualTo(Long value) {
            addCriterion("task_remaining_score <=", value, "taskRemainingScore");
            return (Criteria) this;
        }

        public Criteria andTaskRemainingScoreIn(List<Long> values) {
            addCriterion("task_remaining_score in", values, "taskRemainingScore");
            return (Criteria) this;
        }

        public Criteria andTaskRemainingScoreNotIn(List<Long> values) {
            addCriterion("task_remaining_score not in", values, "taskRemainingScore");
            return (Criteria) this;
        }

        public Criteria andTaskRemainingScoreBetween(Long value1, Long value2) {
            addCriterion("task_remaining_score between", value1, value2, "taskRemainingScore");
            return (Criteria) this;
        }

        public Criteria andTaskRemainingScoreNotBetween(Long value1, Long value2) {
            addCriterion("task_remaining_score not between", value1, value2, "taskRemainingScore");
            return (Criteria) this;
        }

        public Criteria andTaskDoneIsNull() {
            addCriterion("task_done is null");
            return (Criteria) this;
        }

        public Criteria andTaskDoneIsNotNull() {
            addCriterion("task_done is not null");
            return (Criteria) this;
        }

        public Criteria andTaskDoneEqualTo(Boolean value) {
            addCriterion("task_done =", value, "taskDone");
            return (Criteria) this;
        }

        public Criteria andTaskDoneNotEqualTo(Boolean value) {
            addCriterion("task_done <>", value, "taskDone");
            return (Criteria) this;
        }

        public Criteria andTaskDoneGreaterThan(Boolean value) {
            addCriterion("task_done >", value, "taskDone");
            return (Criteria) this;
        }

        public Criteria andTaskDoneGreaterThanOrEqualTo(Boolean value) {
            addCriterion("task_done >=", value, "taskDone");
            return (Criteria) this;
        }

        public Criteria andTaskDoneLessThan(Boolean value) {
            addCriterion("task_done <", value, "taskDone");
            return (Criteria) this;
        }

        public Criteria andTaskDoneLessThanOrEqualTo(Boolean value) {
            addCriterion("task_done <=", value, "taskDone");
            return (Criteria) this;
        }

        public Criteria andTaskDoneIn(List<Boolean> values) {
            addCriterion("task_done in", values, "taskDone");
            return (Criteria) this;
        }

        public Criteria andTaskDoneNotIn(List<Boolean> values) {
            addCriterion("task_done not in", values, "taskDone");
            return (Criteria) this;
        }

        public Criteria andTaskDoneBetween(Boolean value1, Boolean value2) {
            addCriterion("task_done between", value1, value2, "taskDone");
            return (Criteria) this;
        }

        public Criteria andTaskDoneNotBetween(Boolean value1, Boolean value2) {
            addCriterion("task_done not between", value1, value2, "taskDone");
            return (Criteria) this;
        }

        public Criteria andTaskDailyDoneScoreIsNull() {
            addCriterion("task_daily_done_score is null");
            return (Criteria) this;
        }

        public Criteria andTaskDailyDoneScoreIsNotNull() {
            addCriterion("task_daily_done_score is not null");
            return (Criteria) this;
        }

        public Criteria andTaskDailyDoneScoreEqualTo(String value) {
            addCriterion("task_daily_done_score =", value, "taskDailyDoneScore");
            return (Criteria) this;
        }

        public Criteria andTaskDailyDoneScoreNotEqualTo(String value) {
            addCriterion("task_daily_done_score <>", value, "taskDailyDoneScore");
            return (Criteria) this;
        }

        public Criteria andTaskDailyDoneScoreGreaterThan(String value) {
            addCriterion("task_daily_done_score >", value, "taskDailyDoneScore");
            return (Criteria) this;
        }

        public Criteria andTaskDailyDoneScoreGreaterThanOrEqualTo(String value) {
            addCriterion("task_daily_done_score >=", value, "taskDailyDoneScore");
            return (Criteria) this;
        }

        public Criteria andTaskDailyDoneScoreLessThan(String value) {
            addCriterion("task_daily_done_score <", value, "taskDailyDoneScore");
            return (Criteria) this;
        }

        public Criteria andTaskDailyDoneScoreLessThanOrEqualTo(String value) {
            addCriterion("task_daily_done_score <=", value, "taskDailyDoneScore");
            return (Criteria) this;
        }

        public Criteria andTaskDailyDoneScoreLike(String value) {
            addCriterion("task_daily_done_score like", value, "taskDailyDoneScore");
            return (Criteria) this;
        }

        public Criteria andTaskDailyDoneScoreNotLike(String value) {
            addCriterion("task_daily_done_score not like", value, "taskDailyDoneScore");
            return (Criteria) this;
        }

        public Criteria andTaskDailyDoneScoreIn(List<String> values) {
            addCriterion("task_daily_done_score in", values, "taskDailyDoneScore");
            return (Criteria) this;
        }

        public Criteria andTaskDailyDoneScoreNotIn(List<String> values) {
            addCriterion("task_daily_done_score not in", values, "taskDailyDoneScore");
            return (Criteria) this;
        }

        public Criteria andTaskDailyDoneScoreBetween(String value1, String value2) {
            addCriterion("task_daily_done_score between", value1, value2, "taskDailyDoneScore");
            return (Criteria) this;
        }

        public Criteria andTaskDailyDoneScoreNotBetween(String value1, String value2) {
            addCriterion("task_daily_done_score not between", value1, value2, "taskDailyDoneScore");
            return (Criteria) this;
        }

        public Criteria andTaskDailyUnDoneIsNull() {
            addCriterion("task_daily_un_done is null");
            return (Criteria) this;
        }

        public Criteria andTaskDailyUnDoneIsNotNull() {
            addCriterion("task_daily_un_done is not null");
            return (Criteria) this;
        }

        public Criteria andTaskDailyUnDoneEqualTo(Long value) {
            addCriterion("task_daily_un_done =", value, "taskDailyUnDone");
            return (Criteria) this;
        }

        public Criteria andTaskDailyUnDoneNotEqualTo(Long value) {
            addCriterion("task_daily_un_done <>", value, "taskDailyUnDone");
            return (Criteria) this;
        }

        public Criteria andTaskDailyUnDoneGreaterThan(Long value) {
            addCriterion("task_daily_un_done >", value, "taskDailyUnDone");
            return (Criteria) this;
        }

        public Criteria andTaskDailyUnDoneGreaterThanOrEqualTo(Long value) {
            addCriterion("task_daily_un_done >=", value, "taskDailyUnDone");
            return (Criteria) this;
        }

        public Criteria andTaskDailyUnDoneLessThan(Long value) {
            addCriterion("task_daily_un_done <", value, "taskDailyUnDone");
            return (Criteria) this;
        }

        public Criteria andTaskDailyUnDoneLessThanOrEqualTo(Long value) {
            addCriterion("task_daily_un_done <=", value, "taskDailyUnDone");
            return (Criteria) this;
        }

        public Criteria andTaskDailyUnDoneIn(List<Long> values) {
            addCriterion("task_daily_un_done in", values, "taskDailyUnDone");
            return (Criteria) this;
        }

        public Criteria andTaskDailyUnDoneNotIn(List<Long> values) {
            addCriterion("task_daily_un_done not in", values, "taskDailyUnDone");
            return (Criteria) this;
        }

        public Criteria andTaskDailyUnDoneBetween(Long value1, Long value2) {
            addCriterion("task_daily_un_done between", value1, value2, "taskDailyUnDone");
            return (Criteria) this;
        }

        public Criteria andTaskDailyUnDoneNotBetween(Long value1, Long value2) {
            addCriterion("task_daily_un_done not between", value1, value2, "taskDailyUnDone");
            return (Criteria) this;
        }

        public Criteria andTaskDailyUnDoneDetailIsNull() {
            addCriterion("task_daily_un_done_detail is null");
            return (Criteria) this;
        }

        public Criteria andTaskDailyUnDoneDetailIsNotNull() {
            addCriterion("task_daily_un_done_detail is not null");
            return (Criteria) this;
        }

        public Criteria andTaskDailyUnDoneDetailEqualTo(String value) {
            addCriterion("task_daily_un_done_detail =", value, "taskDailyUnDoneDetail");
            return (Criteria) this;
        }

        public Criteria andTaskDailyUnDoneDetailNotEqualTo(String value) {
            addCriterion("task_daily_un_done_detail <>", value, "taskDailyUnDoneDetail");
            return (Criteria) this;
        }

        public Criteria andTaskDailyUnDoneDetailGreaterThan(String value) {
            addCriterion("task_daily_un_done_detail >", value, "taskDailyUnDoneDetail");
            return (Criteria) this;
        }

        public Criteria andTaskDailyUnDoneDetailGreaterThanOrEqualTo(String value) {
            addCriterion("task_daily_un_done_detail >=", value, "taskDailyUnDoneDetail");
            return (Criteria) this;
        }

        public Criteria andTaskDailyUnDoneDetailLessThan(String value) {
            addCriterion("task_daily_un_done_detail <", value, "taskDailyUnDoneDetail");
            return (Criteria) this;
        }

        public Criteria andTaskDailyUnDoneDetailLessThanOrEqualTo(String value) {
            addCriterion("task_daily_un_done_detail <=", value, "taskDailyUnDoneDetail");
            return (Criteria) this;
        }

        public Criteria andTaskDailyUnDoneDetailLike(String value) {
            addCriterion("task_daily_un_done_detail like", value, "taskDailyUnDoneDetail");
            return (Criteria) this;
        }

        public Criteria andTaskDailyUnDoneDetailNotLike(String value) {
            addCriterion("task_daily_un_done_detail not like", value, "taskDailyUnDoneDetail");
            return (Criteria) this;
        }

        public Criteria andTaskDailyUnDoneDetailIn(List<String> values) {
            addCriterion("task_daily_un_done_detail in", values, "taskDailyUnDoneDetail");
            return (Criteria) this;
        }

        public Criteria andTaskDailyUnDoneDetailNotIn(List<String> values) {
            addCriterion("task_daily_un_done_detail not in", values, "taskDailyUnDoneDetail");
            return (Criteria) this;
        }

        public Criteria andTaskDailyUnDoneDetailBetween(String value1, String value2) {
            addCriterion("task_daily_un_done_detail between", value1, value2, "taskDailyUnDoneDetail");
            return (Criteria) this;
        }

        public Criteria andTaskDailyUnDoneDetailNotBetween(String value1, String value2) {
            addCriterion("task_daily_un_done_detail not between", value1, value2, "taskDailyUnDoneDetail");
            return (Criteria) this;
        }

        public Criteria andTaskDailyScoreChangeIsNull() {
            addCriterion("task_daily_score_change is null");
            return (Criteria) this;
        }

        public Criteria andTaskDailyScoreChangeIsNotNull() {
            addCriterion("task_daily_score_change is not null");
            return (Criteria) this;
        }

        public Criteria andTaskDailyScoreChangeEqualTo(String value) {
            addCriterion("task_daily_score_change =", value, "taskDailyScoreChange");
            return (Criteria) this;
        }

        public Criteria andTaskDailyScoreChangeNotEqualTo(String value) {
            addCriterion("task_daily_score_change <>", value, "taskDailyScoreChange");
            return (Criteria) this;
        }

        public Criteria andTaskDailyScoreChangeGreaterThan(String value) {
            addCriterion("task_daily_score_change >", value, "taskDailyScoreChange");
            return (Criteria) this;
        }

        public Criteria andTaskDailyScoreChangeGreaterThanOrEqualTo(String value) {
            addCriterion("task_daily_score_change >=", value, "taskDailyScoreChange");
            return (Criteria) this;
        }

        public Criteria andTaskDailyScoreChangeLessThan(String value) {
            addCriterion("task_daily_score_change <", value, "taskDailyScoreChange");
            return (Criteria) this;
        }

        public Criteria andTaskDailyScoreChangeLessThanOrEqualTo(String value) {
            addCriterion("task_daily_score_change <=", value, "taskDailyScoreChange");
            return (Criteria) this;
        }

        public Criteria andTaskDailyScoreChangeLike(String value) {
            addCriterion("task_daily_score_change like", value, "taskDailyScoreChange");
            return (Criteria) this;
        }

        public Criteria andTaskDailyScoreChangeNotLike(String value) {
            addCriterion("task_daily_score_change not like", value, "taskDailyScoreChange");
            return (Criteria) this;
        }

        public Criteria andTaskDailyScoreChangeIn(List<String> values) {
            addCriterion("task_daily_score_change in", values, "taskDailyScoreChange");
            return (Criteria) this;
        }

        public Criteria andTaskDailyScoreChangeNotIn(List<String> values) {
            addCriterion("task_daily_score_change not in", values, "taskDailyScoreChange");
            return (Criteria) this;
        }

        public Criteria andTaskDailyScoreChangeBetween(String value1, String value2) {
            addCriterion("task_daily_score_change between", value1, value2, "taskDailyScoreChange");
            return (Criteria) this;
        }

        public Criteria andTaskDailyScoreChangeNotBetween(String value1, String value2) {
            addCriterion("task_daily_score_change not between", value1, value2, "taskDailyScoreChange");
            return (Criteria) this;
        }

        public Criteria andTaskValidScoresIsNull() {
            addCriterion("task_valid_scores is null");
            return (Criteria) this;
        }

        public Criteria andTaskValidScoresIsNotNull() {
            addCriterion("task_valid_scores is not null");
            return (Criteria) this;
        }

        public Criteria andTaskValidScoresEqualTo(Long value) {
            addCriterion("task_valid_scores =", value, "taskValidScores");
            return (Criteria) this;
        }

        public Criteria andTaskValidScoresNotEqualTo(Long value) {
            addCriterion("task_valid_scores <>", value, "taskValidScores");
            return (Criteria) this;
        }

        public Criteria andTaskValidScoresGreaterThan(Long value) {
            addCriterion("task_valid_scores >", value, "taskValidScores");
            return (Criteria) this;
        }

        public Criteria andTaskValidScoresGreaterThanOrEqualTo(Long value) {
            addCriterion("task_valid_scores >=", value, "taskValidScores");
            return (Criteria) this;
        }

        public Criteria andTaskValidScoresLessThan(Long value) {
            addCriterion("task_valid_scores <", value, "taskValidScores");
            return (Criteria) this;
        }

        public Criteria andTaskValidScoresLessThanOrEqualTo(Long value) {
            addCriterion("task_valid_scores <=", value, "taskValidScores");
            return (Criteria) this;
        }

        public Criteria andTaskValidScoresIn(List<Long> values) {
            addCriterion("task_valid_scores in", values, "taskValidScores");
            return (Criteria) this;
        }

        public Criteria andTaskValidScoresNotIn(List<Long> values) {
            addCriterion("task_valid_scores not in", values, "taskValidScores");
            return (Criteria) this;
        }

        public Criteria andTaskValidScoresBetween(Long value1, Long value2) {
            addCriterion("task_valid_scores between", value1, value2, "taskValidScores");
            return (Criteria) this;
        }

        public Criteria andTaskValidScoresNotBetween(Long value1, Long value2) {
            addCriterion("task_valid_scores not between", value1, value2, "taskValidScores");
            return (Criteria) this;
        }

        public Criteria andTaskRuleIsNull() {
            addCriterion("task_rule is null");
            return (Criteria) this;
        }

        public Criteria andTaskRuleIsNotNull() {
            addCriterion("task_rule is not null");
            return (Criteria) this;
        }

        public Criteria andTaskRuleEqualTo(Integer value) {
            addCriterion("task_rule =", value, "taskRule");
            return (Criteria) this;
        }

        public Criteria andTaskRuleNotEqualTo(Integer value) {
            addCriterion("task_rule <>", value, "taskRule");
            return (Criteria) this;
        }

        public Criteria andTaskRuleGreaterThan(Integer value) {
            addCriterion("task_rule >", value, "taskRule");
            return (Criteria) this;
        }

        public Criteria andTaskRuleGreaterThanOrEqualTo(Integer value) {
            addCriterion("task_rule >=", value, "taskRule");
            return (Criteria) this;
        }

        public Criteria andTaskRuleLessThan(Integer value) {
            addCriterion("task_rule <", value, "taskRule");
            return (Criteria) this;
        }

        public Criteria andTaskRuleLessThanOrEqualTo(Integer value) {
            addCriterion("task_rule <=", value, "taskRule");
            return (Criteria) this;
        }

        public Criteria andTaskRuleIn(List<Integer> values) {
            addCriterion("task_rule in", values, "taskRule");
            return (Criteria) this;
        }

        public Criteria andTaskRuleNotIn(List<Integer> values) {
            addCriterion("task_rule not in", values, "taskRule");
            return (Criteria) this;
        }

        public Criteria andTaskRuleBetween(Integer value1, Integer value2) {
            addCriterion("task_rule between", value1, value2, "taskRule");
            return (Criteria) this;
        }

        public Criteria andTaskRuleNotBetween(Integer value1, Integer value2) {
            addCriterion("task_rule not between", value1, value2, "taskRule");
            return (Criteria) this;
        }

        public Criteria andCharmIsNull() {
            addCriterion("charm is null");
            return (Criteria) this;
        }

        public Criteria andCharmIsNotNull() {
            addCriterion("charm is not null");
            return (Criteria) this;
        }

        public Criteria andCharmEqualTo(Long value) {
            addCriterion("charm =", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmNotEqualTo(Long value) {
            addCriterion("charm <>", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmGreaterThan(Long value) {
            addCriterion("charm >", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmGreaterThanOrEqualTo(Long value) {
            addCriterion("charm >=", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmLessThan(Long value) {
            addCriterion("charm <", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmLessThanOrEqualTo(Long value) {
            addCriterion("charm <=", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmIn(List<Long> values) {
            addCriterion("charm in", values, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmNotIn(List<Long> values) {
            addCriterion("charm not in", values, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmBetween(Long value1, Long value2) {
            addCriterion("charm between", value1, value2, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmNotBetween(Long value1, Long value2) {
            addCriterion("charm not between", value1, value2, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmDiffIsNull() {
            addCriterion("charm_diff is null");
            return (Criteria) this;
        }

        public Criteria andCharmDiffIsNotNull() {
            addCriterion("charm_diff is not null");
            return (Criteria) this;
        }

        public Criteria andCharmDiffEqualTo(Long value) {
            addCriterion("charm_diff =", value, "charmDiff");
            return (Criteria) this;
        }

        public Criteria andCharmDiffNotEqualTo(Long value) {
            addCriterion("charm_diff <>", value, "charmDiff");
            return (Criteria) this;
        }

        public Criteria andCharmDiffGreaterThan(Long value) {
            addCriterion("charm_diff >", value, "charmDiff");
            return (Criteria) this;
        }

        public Criteria andCharmDiffGreaterThanOrEqualTo(Long value) {
            addCriterion("charm_diff >=", value, "charmDiff");
            return (Criteria) this;
        }

        public Criteria andCharmDiffLessThan(Long value) {
            addCriterion("charm_diff <", value, "charmDiff");
            return (Criteria) this;
        }

        public Criteria andCharmDiffLessThanOrEqualTo(Long value) {
            addCriterion("charm_diff <=", value, "charmDiff");
            return (Criteria) this;
        }

        public Criteria andCharmDiffIn(List<Long> values) {
            addCriterion("charm_diff in", values, "charmDiff");
            return (Criteria) this;
        }

        public Criteria andCharmDiffNotIn(List<Long> values) {
            addCriterion("charm_diff not in", values, "charmDiff");
            return (Criteria) this;
        }

        public Criteria andCharmDiffBetween(Long value1, Long value2) {
            addCriterion("charm_diff between", value1, value2, "charmDiff");
            return (Criteria) this;
        }

        public Criteria andCharmDiffNotBetween(Long value1, Long value2) {
            addCriterion("charm_diff not between", value1, value2, "charmDiff");
            return (Criteria) this;
        }

        public Criteria andSeatScoreIsNull() {
            addCriterion("seat_score is null");
            return (Criteria) this;
        }

        public Criteria andSeatScoreIsNotNull() {
            addCriterion("seat_score is not null");
            return (Criteria) this;
        }

        public Criteria andSeatScoreEqualTo(Integer value) {
            addCriterion("seat_score =", value, "seatScore");
            return (Criteria) this;
        }

        public Criteria andSeatScoreNotEqualTo(Integer value) {
            addCriterion("seat_score <>", value, "seatScore");
            return (Criteria) this;
        }

        public Criteria andSeatScoreGreaterThan(Integer value) {
            addCriterion("seat_score >", value, "seatScore");
            return (Criteria) this;
        }

        public Criteria andSeatScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("seat_score >=", value, "seatScore");
            return (Criteria) this;
        }

        public Criteria andSeatScoreLessThan(Integer value) {
            addCriterion("seat_score <", value, "seatScore");
            return (Criteria) this;
        }

        public Criteria andSeatScoreLessThanOrEqualTo(Integer value) {
            addCriterion("seat_score <=", value, "seatScore");
            return (Criteria) this;
        }

        public Criteria andSeatScoreIn(List<Integer> values) {
            addCriterion("seat_score in", values, "seatScore");
            return (Criteria) this;
        }

        public Criteria andSeatScoreNotIn(List<Integer> values) {
            addCriterion("seat_score not in", values, "seatScore");
            return (Criteria) this;
        }

        public Criteria andSeatScoreBetween(Integer value1, Integer value2) {
            addCriterion("seat_score between", value1, value2, "seatScore");
            return (Criteria) this;
        }

        public Criteria andSeatScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("seat_score not between", value1, value2, "seatScore");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wave_check_in_user_task
     *
     * @mbg.generated do_not_delete_during_merge Thu Jan 09 16:06:57 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wave_check_in_user_task
     *
     * @mbg.generated Thu Jan 09 16:06:57 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}