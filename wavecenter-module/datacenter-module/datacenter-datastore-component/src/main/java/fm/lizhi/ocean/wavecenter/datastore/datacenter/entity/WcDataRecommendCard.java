package fm.lizhi.ocean.wavecenter.datastore.datacenter.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 推荐卡使用效果数据
 *
 * @date 2025-03-18 04:43:48
 */
@Table(name = "`wavecenter_data_recommend_card`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcDataRecommendCard {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 业务推荐卡使用记录ID
     */
    @Column(name= "`use_record_id`")
    private Long useRecordId;

    /**
     * 被推荐njId
     */
    @Column(name= "`nj_id`")
    private Long njId;

    /**
     * 推荐开始时间
     */
    @Column(name= "`start_date`")
    private Date startDate;

    /**
     * 推荐结束时间
     */
    @Column(name= "`end_date`")
    private Date endDate;

    /**
     * 业务 ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 使用前半小时曝光数
     */
    @Column(name= "`before_half_count`")
    private Integer beforeHalfCount;

    /**
     * 使用时曝光数
     */
    @Column(name= "`use_count`")
    private Integer useCount;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`update_time`")
    private Date updateTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", useRecordId=").append(useRecordId);
        sb.append(", njId=").append(njId);
        sb.append(", startDate=").append(startDate);
        sb.append(", endDate=").append(endDate);
        sb.append(", appId=").append(appId);
        sb.append(", beforeHalfCount=").append(beforeHalfCount);
        sb.append(", useCount=").append(useCount);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }
}