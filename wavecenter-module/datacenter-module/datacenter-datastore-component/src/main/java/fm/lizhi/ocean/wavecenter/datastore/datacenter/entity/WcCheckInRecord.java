package fm.lizhi.ocean.wavecenter.datastore.datacenter.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 *
 * @date 2024-06-11 03:31:34
 */
@Table(name = "`wavecenter_check_in_record`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcCheckInRecord {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 应用标识
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 家族id
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 主持ID
     */
    @Column(name= "`host_id`")
    private Long hostId;

    /**
     * 直播房间ID
     */
    @Column(name= "`room_id`")
    private Long roomId;

    /**
     * 主播id
     */
    @Column(name= "`user_id`")
    private Long userId;

    /**
     * 厅主ID
     */
    @Column(name= "`nj_id`")
    private Long njId;

    /**
     * 收益值
     */
    @Column(name= "`income`")
    private BigDecimal income;

    /**
     *  0：未打卡，1：已打卡，2：确认打卡
     */
    @Column(name= "`status`")
    private Integer status;

    /**
     * 调整之后的魅力值
     */
    @Column(name= "`charm_value`")
    private Long charmValue;

    /**
     * 魅力值
     */
    @Column(name= "`charm`")
    private Long charm;

    /**
     * 档期结束时间
     */
    @Column(name= "`end_time`")
    private Date endTime;

    /**
     * 档期开始时间
     */
    @Column(name= "`start_time`")
    private Date startTime;

    /**
     * 0: 不是主持  1：主持
     */
    @Column(name= "`is_host`")
    private Integer isHost;

    /**
     * 备注
     */
    @Column(name= "`remark`")
    private String remark;

    /**
     * 上麦时长(单位：分钟)
     */
    @Column(name= "`up_guest_dur`")
    private Integer upGuestDur;

    @Column(name= "`create_time`")
    private Date createTime;

    @Column(name= "`modiy_time`")
    private Date modiyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", familyId=").append(familyId);
        sb.append(", hostId=").append(hostId);
        sb.append(", roomId=").append(roomId);
        sb.append(", userId=").append(userId);
        sb.append(", njId=").append(njId);
        sb.append(", income=").append(income);
        sb.append(", status=").append(status);
        sb.append(", charmValue=").append(charmValue);
        sb.append(", charm=").append(charm);
        sb.append(", endTime=").append(endTime);
        sb.append(", startTime=").append(startTime);
        sb.append(", isHost=").append(isHost);
        sb.append(", remark=").append(remark);
        sb.append(", upGuestDur=").append(upGuestDur);
        sb.append(", createTime=").append(createTime);
        sb.append(", modiyTime=").append(modiyTime);
        sb.append("]");
        return sb.toString();
    }
}