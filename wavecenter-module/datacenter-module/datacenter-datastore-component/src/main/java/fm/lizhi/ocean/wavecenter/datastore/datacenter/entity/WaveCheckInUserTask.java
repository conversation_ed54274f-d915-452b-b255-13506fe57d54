package fm.lizhi.ocean.wavecenter.datastore.datacenter.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 麦序福利任务表
 *
 * @date 2025-01-09 04:06:57
 */
@Table(name = "`wave_check_in_user_task`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WaveCheckInUserTask {
    /**
     * ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 记录ID
     */
    @Column(name= "`record_id`")
    private Long recordId;

    /**
     * 档期ID
     */
    @Column(name= "`schedule_id`")
    private Long scheduleId;

    /**
     * 用户ID
     */
    @Column(name= "`user_id`")
    private Long userId;

    /**
     * 厅主ID
     */
    @Column(name= "`nj_id`")
    private Long njId;

    /**
     * 本档任务分
     */
    @Column(name= "`task_score`")
    private Long taskScore;

    /**
     * 本档任务分差额
     */
    @Column(name= "`task_score_diff`")
    private Long taskScoreDiff;

    /**
     * 本档任务进度
     */
    @Column(name= "`task_progress`")
    private Long taskProgress;

    /**
     * 本档任务进度差额
     */
    @Column(name= "`task_progress_diff`")
    private Long taskProgressDiff;

    /**
     * 可补分数(可为负数，负数则代表本次需要多少分完成任务)
     */
    @Column(name= "`task_remaining_score`")
    private Long taskRemainingScore;

    /**
     * 任务是否完成, 0 未完成，1 已完成
     */
    @Column(name= "`task_done`")
    private Boolean taskDone;

    /**
     * 本档已补历史分
     */
    @Column(name= "`task_daily_done_score`")
    private String taskDailyDoneScore;

    /**
     * 今日未完成任务分(档期结束之后不会变动)
     */
    @Column(name= "`task_daily_un_done`")
    private Long taskDailyUnDone;

    /**
     * 今日未完成任务分明细,格式 200,300,200
     */
    @Column(name= "`task_daily_un_done_detail`")
    private String taskDailyUnDoneDetail;

    /**
     * 今日任务分变动,任务三分数变动会以,号分割存储
     */
    @Column(name= "`task_daily_score_change`")
    private String taskDailyScoreChange;

    /**
     * 本档有效分数（本档任务为0或者未过本档任务需要先扣麦序 100）
     */
    @Column(name= "`task_valid_scores`")
    private Long taskValidScores;

    /**
     * 任务计算规则
     */
    @Column(name= "`task_rule`")
    private Integer taskRule;

    /**
     * 原始魅力值
     */
    @Column(name= "`charm`")
    private Long charm;

    /**
     * 魅力值差额
     */
    @Column(name= "`charm_diff`")
    private Long charmDiff;

    /**
     * 麦序分
     */
    @Column(name= "`seat_score`")
    private Integer seatScore;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", recordId=").append(recordId);
        sb.append(", scheduleId=").append(scheduleId);
        sb.append(", userId=").append(userId);
        sb.append(", njId=").append(njId);
        sb.append(", taskScore=").append(taskScore);
        sb.append(", taskScoreDiff=").append(taskScoreDiff);
        sb.append(", taskProgress=").append(taskProgress);
        sb.append(", taskProgressDiff=").append(taskProgressDiff);
        sb.append(", taskRemainingScore=").append(taskRemainingScore);
        sb.append(", taskDone=").append(taskDone);
        sb.append(", taskDailyDoneScore=").append(taskDailyDoneScore);
        sb.append(", taskDailyUnDone=").append(taskDailyUnDone);
        sb.append(", taskDailyUnDoneDetail=").append(taskDailyUnDoneDetail);
        sb.append(", taskDailyScoreChange=").append(taskDailyScoreChange);
        sb.append(", taskValidScores=").append(taskValidScores);
        sb.append(", taskRule=").append(taskRule);
        sb.append(", charm=").append(charm);
        sb.append(", charmDiff=").append(charmDiff);
        sb.append(", seatScore=").append(seatScore);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}