package fm.lizhi.ocean.wavecenter.datastore.datacenter.entity;

import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 已废弃, 麦序福利1.0的web站打卡明细接口用到, 从麦序福利2.0开始废弃.
 * <p>
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 *
 * @date 2024-06-11 03:31:34
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WaveCheckInRecordEntity {


    private Long id;

    /**
     * 应用标识
     */
    private Integer appId;

    /**
     * 家族id
     */
    private Long familyId;

    /**
     * 主持ID
     */
    private Long hostId;

    /**
     * 直播房间ID
     */
    private Long roomId;

    /**
     * 主播id
     */
    private Long userId;

    /**
     * 厅主ID
     */
    private Long njId;

    /**
     * 收益值
     */
    private BigDecimal income;

    /**
     *  0：未打卡，1：已打卡，2：确认打卡
     */
    private Integer status;

    /**
     * 调整之后的魅力值
     */
    private Long charmValue;

    /**
     * 魅力值
     */
    private Long charm;

    /**
     * 档期结束时间
     */
    private Date endTime;

    /**
     * 档期开始时间
     */
    private Date startTime;

    /**
     * 0: 不是主持  1：主持
     */
    private Integer isHost;

    /**
     * 备注
     */
    private String remark;


    private Date createTime;

    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", familyId=").append(familyId);
        sb.append(", hostId=").append(hostId);
        sb.append(", roomId=").append(roomId);
        sb.append(", userId=").append(userId);
        sb.append(", njId=").append(njId);
        sb.append(", income=").append(income);
        sb.append(", status=").append(status);
        sb.append(", charmValue=").append(charmValue);
        sb.append(", charm=").append(charm);
        sb.append(", endTime=").append(endTime);
        sb.append(", startTime=").append(startTime);
        sb.append(", isHost=").append(isHost);
        sb.append(", remark=").append(remark);
        sb.append(", createTime=").append(createTime);
        sb.append(", modiyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}
