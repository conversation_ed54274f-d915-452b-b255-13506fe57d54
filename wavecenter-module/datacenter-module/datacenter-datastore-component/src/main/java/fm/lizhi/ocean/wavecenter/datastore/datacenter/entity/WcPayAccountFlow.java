package fm.lizhi.ocean.wavecenter.datastore.datacenter.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 支付账户流水
 *
 * @date 2025-04-27 07:38:13
 */
@Table(name = "`wavecenter_pay_account_flow`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcPayAccountFlow {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 账户编码
     */
    @Column(name= "`account_code`")
    private String accountCode;

    /**
     * 操作 minus - 减 plus - 加  frozen -冻结 unfrozen-解冻
     */
    @Column(name= "`account_op_type`")
    private String accountOpType;

    /**
     * 金额
     */
    @Column(name= "`amount`")
    private String amount;

    /**
     * 支付的appId
     */
    @Column(name= "`pay_app_id`")
    private String payAppId;

    /**
     * bizId
     */
    @Column(name= "`biz_id`")
    private Integer bizId;

    /**
     * 记账日
     */
    @Column(name= "`bookkeeping_date`")
    private String bookkeepingDate;

    /**
     * 业务订单号
     */
    @Column(name= "`business_no`")
    private String businessNo;

    /**
     * 流水ID
     */
    @Column(name= "`flow_id`")
    private Long flowId;

    /**
     * 用户标识或者组织标识
     */
    @Column(name= "`identity`")
    private String identity;

    /**
     * lizhi_ppapp_live - PP约玩  lizhi_heiye_common-黑叶 lizhi_ximi_common-西米
     */
    @Column(name= "`tenant_code`")
    private String tenantCode;

    /**
     * 服务部署环境, TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`update_time`")
    private Date updateTime;

    /**
     * 0=未删除，1=已删除
     */
    @Column(name= "`deleted`")
    private Integer deleted;

    /**
     * 消费次数
     */
    @Column(name= "`consumer_times`")
    private Integer consumerTimes;

    /**
     * 最后一次消费时间
     */
    @Column(name= "`last_consumer_time`")
    private Date lastConsumerTime;

    /**
     * 流水时间
     */
    @Column(name= "`trade_time`")
    private String tradeTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", accountCode=").append(accountCode);
        sb.append(", accountOpType=").append(accountOpType);
        sb.append(", amount=").append(amount);
        sb.append(", payAppId=").append(payAppId);
        sb.append(", bizId=").append(bizId);
        sb.append(", bookkeepingDate=").append(bookkeepingDate);
        sb.append(", businessNo=").append(businessNo);
        sb.append(", flowId=").append(flowId);
        sb.append(", identity=").append(identity);
        sb.append(", tenantCode=").append(tenantCode);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", deleted=").append(deleted);
        sb.append(", consumerTimes=").append(consumerTimes);
        sb.append(", lastConsumerTime=").append(lastConsumerTime);
        sb.append(", tradeTime=").append(tradeTime);
        sb.append("]");
        return sb.toString();
    }
}