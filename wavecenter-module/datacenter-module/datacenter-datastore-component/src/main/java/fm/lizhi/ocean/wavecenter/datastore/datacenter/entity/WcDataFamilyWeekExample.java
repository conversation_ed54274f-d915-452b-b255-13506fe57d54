package fm.lizhi.ocean.wavecenter.datastore.datacenter.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class WcDataFamilyWeekExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_family_week
     *
     * @mbg.generated Tue Mar 25 10:23:22 CST 2025
     */
    public WcDataFamilyWeekExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_family_week
     *
     * @mbg.generated Tue Mar 25 10:23:22 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_family_week
     *
     * @mbg.generated Tue Mar 25 10:23:22 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_family_week
     *
     * @mbg.generated Tue Mar 25 10:23:22 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_family_week
     *
     * @mbg.generated Tue Mar 25 10:23:22 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_family_week
     *
     * @mbg.generated Tue Mar 25 10:23:22 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_family_week
     *
     * @mbg.generated Tue Mar 25 10:23:22 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_family_week
     *
     * @mbg.generated Tue Mar 25 10:23:22 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_family_week
     *
     * @mbg.generated Tue Mar 25 10:23:22 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_family_week
     *
     * @mbg.generated Tue Mar 25 10:23:22 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_family_week
     *
     * @mbg.generated Tue Mar 25 10:23:22 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Class getEntityClass() {
        return WcDataFamilyWeek.class;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wavecenter_data_family_week
     *
     * @mbg.generated Tue Mar 25 10:23:22 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(Integer value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(Integer value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(Integer value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(Integer value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(Integer value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<Integer> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<Integer> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(Integer value1, Integer value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(Integer value1, Integer value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateIsNull() {
            addCriterion("start_week_date is null");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateIsNotNull() {
            addCriterion("start_week_date is not null");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateEqualTo(Date value) {
            addCriterionForJDBCDate("start_week_date =", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("start_week_date <>", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateGreaterThan(Date value) {
            addCriterionForJDBCDate("start_week_date >", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("start_week_date >=", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateLessThan(Date value) {
            addCriterionForJDBCDate("start_week_date <", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("start_week_date <=", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateIn(List<Date> values) {
            addCriterionForJDBCDate("start_week_date in", values, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("start_week_date not in", values, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("start_week_date between", value1, value2, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("start_week_date not between", value1, value2, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateIsNull() {
            addCriterion("end_week_date is null");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateIsNotNull() {
            addCriterion("end_week_date is not null");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateEqualTo(Date value) {
            addCriterionForJDBCDate("end_week_date =", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("end_week_date <>", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateGreaterThan(Date value) {
            addCriterionForJDBCDate("end_week_date >", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("end_week_date >=", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateLessThan(Date value) {
            addCriterionForJDBCDate("end_week_date <", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("end_week_date <=", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateIn(List<Date> values) {
            addCriterionForJDBCDate("end_week_date in", values, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("end_week_date not in", values, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("end_week_date between", value1, value2, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("end_week_date not between", value1, value2, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIsNull() {
            addCriterion("family_id is null");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIsNotNull() {
            addCriterion("family_id is not null");
            return (Criteria) this;
        }

        public Criteria andFamilyIdEqualTo(Long value) {
            addCriterion("family_id =", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotEqualTo(Long value) {
            addCriterion("family_id <>", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdGreaterThan(Long value) {
            addCriterion("family_id >", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("family_id >=", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdLessThan(Long value) {
            addCriterion("family_id <", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdLessThanOrEqualTo(Long value) {
            addCriterion("family_id <=", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIn(List<Long> values) {
            addCriterion("family_id in", values, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotIn(List<Long> values) {
            addCriterion("family_id not in", values, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdBetween(Long value1, Long value2) {
            addCriterion("family_id between", value1, value2, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotBetween(Long value1, Long value2) {
            addCriterion("family_id not between", value1, value2, "familyId");
            return (Criteria) this;
        }

        public Criteria andIncomeIsNull() {
            addCriterion("income is null");
            return (Criteria) this;
        }

        public Criteria andIncomeIsNotNull() {
            addCriterion("income is not null");
            return (Criteria) this;
        }

        public Criteria andIncomeEqualTo(BigDecimal value) {
            addCriterion("income =", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeNotEqualTo(BigDecimal value) {
            addCriterion("income <>", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeGreaterThan(BigDecimal value) {
            addCriterion("income >", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("income >=", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeLessThan(BigDecimal value) {
            addCriterion("income <", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("income <=", value, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeIn(List<BigDecimal> values) {
            addCriterion("income in", values, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeNotIn(List<BigDecimal> values) {
            addCriterion("income not in", values, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income between", value1, value2, "income");
            return (Criteria) this;
        }

        public Criteria andIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income not between", value1, value2, "income");
            return (Criteria) this;
        }

        public Criteria andCharmIsNull() {
            addCriterion("charm is null");
            return (Criteria) this;
        }

        public Criteria andCharmIsNotNull() {
            addCriterion("charm is not null");
            return (Criteria) this;
        }

        public Criteria andCharmEqualTo(Integer value) {
            addCriterion("charm =", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmNotEqualTo(Integer value) {
            addCriterion("charm <>", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmGreaterThan(Integer value) {
            addCriterion("charm >", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmGreaterThanOrEqualTo(Integer value) {
            addCriterion("charm >=", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmLessThan(Integer value) {
            addCriterion("charm <", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmLessThanOrEqualTo(Integer value) {
            addCriterion("charm <=", value, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmIn(List<Integer> values) {
            addCriterion("charm in", values, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmNotIn(List<Integer> values) {
            addCriterion("charm not in", values, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmBetween(Integer value1, Integer value2) {
            addCriterion("charm between", value1, value2, "charm");
            return (Criteria) this;
        }

        public Criteria andCharmNotBetween(Integer value1, Integer value2) {
            addCriterion("charm not between", value1, value2, "charm");
            return (Criteria) this;
        }

        public Criteria andSignRoomCntIsNull() {
            addCriterion("sign_room_cnt is null");
            return (Criteria) this;
        }

        public Criteria andSignRoomCntIsNotNull() {
            addCriterion("sign_room_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andSignRoomCntEqualTo(Integer value) {
            addCriterion("sign_room_cnt =", value, "signRoomCnt");
            return (Criteria) this;
        }

        public Criteria andSignRoomCntNotEqualTo(Integer value) {
            addCriterion("sign_room_cnt <>", value, "signRoomCnt");
            return (Criteria) this;
        }

        public Criteria andSignRoomCntGreaterThan(Integer value) {
            addCriterion("sign_room_cnt >", value, "signRoomCnt");
            return (Criteria) this;
        }

        public Criteria andSignRoomCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("sign_room_cnt >=", value, "signRoomCnt");
            return (Criteria) this;
        }

        public Criteria andSignRoomCntLessThan(Integer value) {
            addCriterion("sign_room_cnt <", value, "signRoomCnt");
            return (Criteria) this;
        }

        public Criteria andSignRoomCntLessThanOrEqualTo(Integer value) {
            addCriterion("sign_room_cnt <=", value, "signRoomCnt");
            return (Criteria) this;
        }

        public Criteria andSignRoomCntIn(List<Integer> values) {
            addCriterion("sign_room_cnt in", values, "signRoomCnt");
            return (Criteria) this;
        }

        public Criteria andSignRoomCntNotIn(List<Integer> values) {
            addCriterion("sign_room_cnt not in", values, "signRoomCnt");
            return (Criteria) this;
        }

        public Criteria andSignRoomCntBetween(Integer value1, Integer value2) {
            addCriterion("sign_room_cnt between", value1, value2, "signRoomCnt");
            return (Criteria) this;
        }

        public Criteria andSignRoomCntNotBetween(Integer value1, Integer value2) {
            addCriterion("sign_room_cnt not between", value1, value2, "signRoomCnt");
            return (Criteria) this;
        }

        public Criteria andOpenRoomCntIsNull() {
            addCriterion("open_room_cnt is null");
            return (Criteria) this;
        }

        public Criteria andOpenRoomCntIsNotNull() {
            addCriterion("open_room_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andOpenRoomCntEqualTo(Integer value) {
            addCriterion("open_room_cnt =", value, "openRoomCnt");
            return (Criteria) this;
        }

        public Criteria andOpenRoomCntNotEqualTo(Integer value) {
            addCriterion("open_room_cnt <>", value, "openRoomCnt");
            return (Criteria) this;
        }

        public Criteria andOpenRoomCntGreaterThan(Integer value) {
            addCriterion("open_room_cnt >", value, "openRoomCnt");
            return (Criteria) this;
        }

        public Criteria andOpenRoomCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("open_room_cnt >=", value, "openRoomCnt");
            return (Criteria) this;
        }

        public Criteria andOpenRoomCntLessThan(Integer value) {
            addCriterion("open_room_cnt <", value, "openRoomCnt");
            return (Criteria) this;
        }

        public Criteria andOpenRoomCntLessThanOrEqualTo(Integer value) {
            addCriterion("open_room_cnt <=", value, "openRoomCnt");
            return (Criteria) this;
        }

        public Criteria andOpenRoomCntIn(List<Integer> values) {
            addCriterion("open_room_cnt in", values, "openRoomCnt");
            return (Criteria) this;
        }

        public Criteria andOpenRoomCntNotIn(List<Integer> values) {
            addCriterion("open_room_cnt not in", values, "openRoomCnt");
            return (Criteria) this;
        }

        public Criteria andOpenRoomCntBetween(Integer value1, Integer value2) {
            addCriterion("open_room_cnt between", value1, value2, "openRoomCnt");
            return (Criteria) this;
        }

        public Criteria andOpenRoomCntNotBetween(Integer value1, Integer value2) {
            addCriterion("open_room_cnt not between", value1, value2, "openRoomCnt");
            return (Criteria) this;
        }

        public Criteria andRoomAvgIncomeIsNull() {
            addCriterion("room_avg_income is null");
            return (Criteria) this;
        }

        public Criteria andRoomAvgIncomeIsNotNull() {
            addCriterion("room_avg_income is not null");
            return (Criteria) this;
        }

        public Criteria andRoomAvgIncomeEqualTo(BigDecimal value) {
            addCriterion("room_avg_income =", value, "roomAvgIncome");
            return (Criteria) this;
        }

        public Criteria andRoomAvgIncomeNotEqualTo(BigDecimal value) {
            addCriterion("room_avg_income <>", value, "roomAvgIncome");
            return (Criteria) this;
        }

        public Criteria andRoomAvgIncomeGreaterThan(BigDecimal value) {
            addCriterion("room_avg_income >", value, "roomAvgIncome");
            return (Criteria) this;
        }

        public Criteria andRoomAvgIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("room_avg_income >=", value, "roomAvgIncome");
            return (Criteria) this;
        }

        public Criteria andRoomAvgIncomeLessThan(BigDecimal value) {
            addCriterion("room_avg_income <", value, "roomAvgIncome");
            return (Criteria) this;
        }

        public Criteria andRoomAvgIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("room_avg_income <=", value, "roomAvgIncome");
            return (Criteria) this;
        }

        public Criteria andRoomAvgIncomeIn(List<BigDecimal> values) {
            addCriterion("room_avg_income in", values, "roomAvgIncome");
            return (Criteria) this;
        }

        public Criteria andRoomAvgIncomeNotIn(List<BigDecimal> values) {
            addCriterion("room_avg_income not in", values, "roomAvgIncome");
            return (Criteria) this;
        }

        public Criteria andRoomAvgIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("room_avg_income between", value1, value2, "roomAvgIncome");
            return (Criteria) this;
        }

        public Criteria andRoomAvgIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("room_avg_income not between", value1, value2, "roomAvgIncome");
            return (Criteria) this;
        }

        public Criteria andRoomAvgCharmIsNull() {
            addCriterion("room_avg_charm is null");
            return (Criteria) this;
        }

        public Criteria andRoomAvgCharmIsNotNull() {
            addCriterion("room_avg_charm is not null");
            return (Criteria) this;
        }

        public Criteria andRoomAvgCharmEqualTo(BigDecimal value) {
            addCriterion("room_avg_charm =", value, "roomAvgCharm");
            return (Criteria) this;
        }

        public Criteria andRoomAvgCharmNotEqualTo(BigDecimal value) {
            addCriterion("room_avg_charm <>", value, "roomAvgCharm");
            return (Criteria) this;
        }

        public Criteria andRoomAvgCharmGreaterThan(BigDecimal value) {
            addCriterion("room_avg_charm >", value, "roomAvgCharm");
            return (Criteria) this;
        }

        public Criteria andRoomAvgCharmGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("room_avg_charm >=", value, "roomAvgCharm");
            return (Criteria) this;
        }

        public Criteria andRoomAvgCharmLessThan(BigDecimal value) {
            addCriterion("room_avg_charm <", value, "roomAvgCharm");
            return (Criteria) this;
        }

        public Criteria andRoomAvgCharmLessThanOrEqualTo(BigDecimal value) {
            addCriterion("room_avg_charm <=", value, "roomAvgCharm");
            return (Criteria) this;
        }

        public Criteria andRoomAvgCharmIn(List<BigDecimal> values) {
            addCriterion("room_avg_charm in", values, "roomAvgCharm");
            return (Criteria) this;
        }

        public Criteria andRoomAvgCharmNotIn(List<BigDecimal> values) {
            addCriterion("room_avg_charm not in", values, "roomAvgCharm");
            return (Criteria) this;
        }

        public Criteria andRoomAvgCharmBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("room_avg_charm between", value1, value2, "roomAvgCharm");
            return (Criteria) this;
        }

        public Criteria andRoomAvgCharmNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("room_avg_charm not between", value1, value2, "roomAvgCharm");
            return (Criteria) this;
        }

        public Criteria andSignPlayerCntIsNull() {
            addCriterion("sign_player_cnt is null");
            return (Criteria) this;
        }

        public Criteria andSignPlayerCntIsNotNull() {
            addCriterion("sign_player_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andSignPlayerCntEqualTo(Integer value) {
            addCriterion("sign_player_cnt =", value, "signPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andSignPlayerCntNotEqualTo(Integer value) {
            addCriterion("sign_player_cnt <>", value, "signPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andSignPlayerCntGreaterThan(Integer value) {
            addCriterion("sign_player_cnt >", value, "signPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andSignPlayerCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("sign_player_cnt >=", value, "signPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andSignPlayerCntLessThan(Integer value) {
            addCriterion("sign_player_cnt <", value, "signPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andSignPlayerCntLessThanOrEqualTo(Integer value) {
            addCriterion("sign_player_cnt <=", value, "signPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andSignPlayerCntIn(List<Integer> values) {
            addCriterion("sign_player_cnt in", values, "signPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andSignPlayerCntNotIn(List<Integer> values) {
            addCriterion("sign_player_cnt not in", values, "signPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andSignPlayerCntBetween(Integer value1, Integer value2) {
            addCriterion("sign_player_cnt between", value1, value2, "signPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andSignPlayerCntNotBetween(Integer value1, Integer value2) {
            addCriterion("sign_player_cnt not between", value1, value2, "signPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andUpGuestPlayerCntIsNull() {
            addCriterion("up_guest_player_cnt is null");
            return (Criteria) this;
        }

        public Criteria andUpGuestPlayerCntIsNotNull() {
            addCriterion("up_guest_player_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andUpGuestPlayerCntEqualTo(Integer value) {
            addCriterion("up_guest_player_cnt =", value, "upGuestPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andUpGuestPlayerCntNotEqualTo(Integer value) {
            addCriterion("up_guest_player_cnt <>", value, "upGuestPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andUpGuestPlayerCntGreaterThan(Integer value) {
            addCriterion("up_guest_player_cnt >", value, "upGuestPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andUpGuestPlayerCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("up_guest_player_cnt >=", value, "upGuestPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andUpGuestPlayerCntLessThan(Integer value) {
            addCriterion("up_guest_player_cnt <", value, "upGuestPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andUpGuestPlayerCntLessThanOrEqualTo(Integer value) {
            addCriterion("up_guest_player_cnt <=", value, "upGuestPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andUpGuestPlayerCntIn(List<Integer> values) {
            addCriterion("up_guest_player_cnt in", values, "upGuestPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andUpGuestPlayerCntNotIn(List<Integer> values) {
            addCriterion("up_guest_player_cnt not in", values, "upGuestPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andUpGuestPlayerCntBetween(Integer value1, Integer value2) {
            addCriterion("up_guest_player_cnt between", value1, value2, "upGuestPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andUpGuestPlayerCntNotBetween(Integer value1, Integer value2) {
            addCriterion("up_guest_player_cnt not between", value1, value2, "upGuestPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerCntIsNull() {
            addCriterion("income_player_cnt is null");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerCntIsNotNull() {
            addCriterion("income_player_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerCntEqualTo(Integer value) {
            addCriterion("income_player_cnt =", value, "incomePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerCntNotEqualTo(Integer value) {
            addCriterion("income_player_cnt <>", value, "incomePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerCntGreaterThan(Integer value) {
            addCriterion("income_player_cnt >", value, "incomePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("income_player_cnt >=", value, "incomePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerCntLessThan(Integer value) {
            addCriterion("income_player_cnt <", value, "incomePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerCntLessThanOrEqualTo(Integer value) {
            addCriterion("income_player_cnt <=", value, "incomePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerCntIn(List<Integer> values) {
            addCriterion("income_player_cnt in", values, "incomePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerCntNotIn(List<Integer> values) {
            addCriterion("income_player_cnt not in", values, "incomePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerCntBetween(Integer value1, Integer value2) {
            addCriterion("income_player_cnt between", value1, value2, "incomePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerCntNotBetween(Integer value1, Integer value2) {
            addCriterion("income_player_cnt not between", value1, value2, "incomePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgIncomeIsNull() {
            addCriterion("player_avg_income is null");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgIncomeIsNotNull() {
            addCriterion("player_avg_income is not null");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgIncomeEqualTo(BigDecimal value) {
            addCriterion("player_avg_income =", value, "playerAvgIncome");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgIncomeNotEqualTo(BigDecimal value) {
            addCriterion("player_avg_income <>", value, "playerAvgIncome");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgIncomeGreaterThan(BigDecimal value) {
            addCriterion("player_avg_income >", value, "playerAvgIncome");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("player_avg_income >=", value, "playerAvgIncome");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgIncomeLessThan(BigDecimal value) {
            addCriterion("player_avg_income <", value, "playerAvgIncome");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("player_avg_income <=", value, "playerAvgIncome");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgIncomeIn(List<BigDecimal> values) {
            addCriterion("player_avg_income in", values, "playerAvgIncome");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgIncomeNotIn(List<BigDecimal> values) {
            addCriterion("player_avg_income not in", values, "playerAvgIncome");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("player_avg_income between", value1, value2, "playerAvgIncome");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("player_avg_income not between", value1, value2, "playerAvgIncome");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgCharmIsNull() {
            addCriterion("player_avg_charm is null");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgCharmIsNotNull() {
            addCriterion("player_avg_charm is not null");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgCharmEqualTo(BigDecimal value) {
            addCriterion("player_avg_charm =", value, "playerAvgCharm");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgCharmNotEqualTo(BigDecimal value) {
            addCriterion("player_avg_charm <>", value, "playerAvgCharm");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgCharmGreaterThan(BigDecimal value) {
            addCriterion("player_avg_charm >", value, "playerAvgCharm");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgCharmGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("player_avg_charm >=", value, "playerAvgCharm");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgCharmLessThan(BigDecimal value) {
            addCriterion("player_avg_charm <", value, "playerAvgCharm");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgCharmLessThanOrEqualTo(BigDecimal value) {
            addCriterion("player_avg_charm <=", value, "playerAvgCharm");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgCharmIn(List<BigDecimal> values) {
            addCriterion("player_avg_charm in", values, "playerAvgCharm");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgCharmNotIn(List<BigDecimal> values) {
            addCriterion("player_avg_charm not in", values, "playerAvgCharm");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgCharmBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("player_avg_charm between", value1, value2, "playerAvgCharm");
            return (Criteria) this;
        }

        public Criteria andPlayerAvgCharmNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("player_avg_charm not between", value1, value2, "playerAvgCharm");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andAllIncomeIsNull() {
            addCriterion("all_income is null");
            return (Criteria) this;
        }

        public Criteria andAllIncomeIsNotNull() {
            addCriterion("all_income is not null");
            return (Criteria) this;
        }

        public Criteria andAllIncomeEqualTo(BigDecimal value) {
            addCriterion("all_income =", value, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeNotEqualTo(BigDecimal value) {
            addCriterion("all_income <>", value, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeGreaterThan(BigDecimal value) {
            addCriterion("all_income >", value, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("all_income >=", value, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeLessThan(BigDecimal value) {
            addCriterion("all_income <", value, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("all_income <=", value, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeIn(List<BigDecimal> values) {
            addCriterion("all_income in", values, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeNotIn(List<BigDecimal> values) {
            addCriterion("all_income not in", values, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("all_income between", value1, value2, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("all_income not between", value1, value2, "allIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeIsNull() {
            addCriterion("sign_hall_income is null");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeIsNotNull() {
            addCriterion("sign_hall_income is not null");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeEqualTo(BigDecimal value) {
            addCriterion("sign_hall_income =", value, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeNotEqualTo(BigDecimal value) {
            addCriterion("sign_hall_income <>", value, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeGreaterThan(BigDecimal value) {
            addCriterion("sign_hall_income >", value, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("sign_hall_income >=", value, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeLessThan(BigDecimal value) {
            addCriterion("sign_hall_income <", value, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("sign_hall_income <=", value, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeIn(List<BigDecimal> values) {
            addCriterion("sign_hall_income in", values, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeNotIn(List<BigDecimal> values) {
            addCriterion("sign_hall_income not in", values, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sign_hall_income between", value1, value2, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andSignHallIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sign_hall_income not between", value1, value2, "signHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeIsNull() {
            addCriterion("official_hall_income is null");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeIsNotNull() {
            addCriterion("official_hall_income is not null");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeEqualTo(BigDecimal value) {
            addCriterion("official_hall_income =", value, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeNotEqualTo(BigDecimal value) {
            addCriterion("official_hall_income <>", value, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeGreaterThan(BigDecimal value) {
            addCriterion("official_hall_income >", value, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("official_hall_income >=", value, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeLessThan(BigDecimal value) {
            addCriterion("official_hall_income <", value, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("official_hall_income <=", value, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeIn(List<BigDecimal> values) {
            addCriterion("official_hall_income in", values, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeNotIn(List<BigDecimal> values) {
            addCriterion("official_hall_income not in", values, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("official_hall_income between", value1, value2, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfficialHallIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("official_hall_income not between", value1, value2, "officialHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeIsNull() {
            addCriterion("personal_hall_income is null");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeIsNotNull() {
            addCriterion("personal_hall_income is not null");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeEqualTo(BigDecimal value) {
            addCriterion("personal_hall_income =", value, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeNotEqualTo(BigDecimal value) {
            addCriterion("personal_hall_income <>", value, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeGreaterThan(BigDecimal value) {
            addCriterion("personal_hall_income >", value, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("personal_hall_income >=", value, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeLessThan(BigDecimal value) {
            addCriterion("personal_hall_income <", value, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("personal_hall_income <=", value, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeIn(List<BigDecimal> values) {
            addCriterion("personal_hall_income in", values, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeNotIn(List<BigDecimal> values) {
            addCriterion("personal_hall_income not in", values, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("personal_hall_income between", value1, value2, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalHallIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("personal_hall_income not between", value1, value2, "personalHallIncome");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeIsNull() {
            addCriterion("noble_income is null");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeIsNotNull() {
            addCriterion("noble_income is not null");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeEqualTo(BigDecimal value) {
            addCriterion("noble_income =", value, "nobleIncome");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeNotEqualTo(BigDecimal value) {
            addCriterion("noble_income <>", value, "nobleIncome");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeGreaterThan(BigDecimal value) {
            addCriterion("noble_income >", value, "nobleIncome");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("noble_income >=", value, "nobleIncome");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeLessThan(BigDecimal value) {
            addCriterion("noble_income <", value, "nobleIncome");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("noble_income <=", value, "nobleIncome");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeIn(List<BigDecimal> values) {
            addCriterion("noble_income in", values, "nobleIncome");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeNotIn(List<BigDecimal> values) {
            addCriterion("noble_income not in", values, "nobleIncome");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("noble_income between", value1, value2, "nobleIncome");
            return (Criteria) this;
        }

        public Criteria andNobleIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("noble_income not between", value1, value2, "nobleIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeIsNull() {
            addCriterion("personal_noble_income is null");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeIsNotNull() {
            addCriterion("personal_noble_income is not null");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeEqualTo(BigDecimal value) {
            addCriterion("personal_noble_income =", value, "personalNobleIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeNotEqualTo(BigDecimal value) {
            addCriterion("personal_noble_income <>", value, "personalNobleIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeGreaterThan(BigDecimal value) {
            addCriterion("personal_noble_income >", value, "personalNobleIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("personal_noble_income >=", value, "personalNobleIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeLessThan(BigDecimal value) {
            addCriterion("personal_noble_income <", value, "personalNobleIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("personal_noble_income <=", value, "personalNobleIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeIn(List<BigDecimal> values) {
            addCriterion("personal_noble_income in", values, "personalNobleIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeNotIn(List<BigDecimal> values) {
            addCriterion("personal_noble_income not in", values, "personalNobleIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("personal_noble_income between", value1, value2, "personalNobleIncome");
            return (Criteria) this;
        }

        public Criteria andPersonalNobleIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("personal_noble_income not between", value1, value2, "personalNobleIncome");
            return (Criteria) this;
        }

        public Criteria andUpPlayerRateIsNull() {
            addCriterion("up_player_rate is null");
            return (Criteria) this;
        }

        public Criteria andUpPlayerRateIsNotNull() {
            addCriterion("up_player_rate is not null");
            return (Criteria) this;
        }

        public Criteria andUpPlayerRateEqualTo(BigDecimal value) {
            addCriterion("up_player_rate =", value, "upPlayerRate");
            return (Criteria) this;
        }

        public Criteria andUpPlayerRateNotEqualTo(BigDecimal value) {
            addCriterion("up_player_rate <>", value, "upPlayerRate");
            return (Criteria) this;
        }

        public Criteria andUpPlayerRateGreaterThan(BigDecimal value) {
            addCriterion("up_player_rate >", value, "upPlayerRate");
            return (Criteria) this;
        }

        public Criteria andUpPlayerRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("up_player_rate >=", value, "upPlayerRate");
            return (Criteria) this;
        }

        public Criteria andUpPlayerRateLessThan(BigDecimal value) {
            addCriterion("up_player_rate <", value, "upPlayerRate");
            return (Criteria) this;
        }

        public Criteria andUpPlayerRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("up_player_rate <=", value, "upPlayerRate");
            return (Criteria) this;
        }

        public Criteria andUpPlayerRateIn(List<BigDecimal> values) {
            addCriterion("up_player_rate in", values, "upPlayerRate");
            return (Criteria) this;
        }

        public Criteria andUpPlayerRateNotIn(List<BigDecimal> values) {
            addCriterion("up_player_rate not in", values, "upPlayerRate");
            return (Criteria) this;
        }

        public Criteria andUpPlayerRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("up_player_rate between", value1, value2, "upPlayerRate");
            return (Criteria) this;
        }

        public Criteria andUpPlayerRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("up_player_rate not between", value1, value2, "upPlayerRate");
            return (Criteria) this;
        }

        public Criteria andOpenRateIsNull() {
            addCriterion("open_rate is null");
            return (Criteria) this;
        }

        public Criteria andOpenRateIsNotNull() {
            addCriterion("open_rate is not null");
            return (Criteria) this;
        }

        public Criteria andOpenRateEqualTo(BigDecimal value) {
            addCriterion("open_rate =", value, "openRate");
            return (Criteria) this;
        }

        public Criteria andOpenRateNotEqualTo(BigDecimal value) {
            addCriterion("open_rate <>", value, "openRate");
            return (Criteria) this;
        }

        public Criteria andOpenRateGreaterThan(BigDecimal value) {
            addCriterion("open_rate >", value, "openRate");
            return (Criteria) this;
        }

        public Criteria andOpenRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("open_rate >=", value, "openRate");
            return (Criteria) this;
        }

        public Criteria andOpenRateLessThan(BigDecimal value) {
            addCriterion("open_rate <", value, "openRate");
            return (Criteria) this;
        }

        public Criteria andOpenRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("open_rate <=", value, "openRate");
            return (Criteria) this;
        }

        public Criteria andOpenRateIn(List<BigDecimal> values) {
            addCriterion("open_rate in", values, "openRate");
            return (Criteria) this;
        }

        public Criteria andOpenRateNotIn(List<BigDecimal> values) {
            addCriterion("open_rate not in", values, "openRate");
            return (Criteria) this;
        }

        public Criteria andOpenRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("open_rate between", value1, value2, "openRate");
            return (Criteria) this;
        }

        public Criteria andOpenRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("open_rate not between", value1, value2, "openRate");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerRateIsNull() {
            addCriterion("income_player_rate is null");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerRateIsNotNull() {
            addCriterion("income_player_rate is not null");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerRateEqualTo(BigDecimal value) {
            addCriterion("income_player_rate =", value, "incomePlayerRate");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerRateNotEqualTo(BigDecimal value) {
            addCriterion("income_player_rate <>", value, "incomePlayerRate");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerRateGreaterThan(BigDecimal value) {
            addCriterion("income_player_rate >", value, "incomePlayerRate");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("income_player_rate >=", value, "incomePlayerRate");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerRateLessThan(BigDecimal value) {
            addCriterion("income_player_rate <", value, "incomePlayerRate");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("income_player_rate <=", value, "incomePlayerRate");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerRateIn(List<BigDecimal> values) {
            addCriterion("income_player_rate in", values, "incomePlayerRate");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerRateNotIn(List<BigDecimal> values) {
            addCriterion("income_player_rate not in", values, "incomePlayerRate");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income_player_rate between", value1, value2, "incomePlayerRate");
            return (Criteria) this;
        }

        public Criteria andIncomePlayerRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income_player_rate not between", value1, value2, "incomePlayerRate");
            return (Criteria) this;
        }

        public Criteria andIncomeRoomCntIsNull() {
            addCriterion("income_room_cnt is null");
            return (Criteria) this;
        }

        public Criteria andIncomeRoomCntIsNotNull() {
            addCriterion("income_room_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andIncomeRoomCntEqualTo(Integer value) {
            addCriterion("income_room_cnt =", value, "incomeRoomCnt");
            return (Criteria) this;
        }

        public Criteria andIncomeRoomCntNotEqualTo(Integer value) {
            addCriterion("income_room_cnt <>", value, "incomeRoomCnt");
            return (Criteria) this;
        }

        public Criteria andIncomeRoomCntGreaterThan(Integer value) {
            addCriterion("income_room_cnt >", value, "incomeRoomCnt");
            return (Criteria) this;
        }

        public Criteria andIncomeRoomCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("income_room_cnt >=", value, "incomeRoomCnt");
            return (Criteria) this;
        }

        public Criteria andIncomeRoomCntLessThan(Integer value) {
            addCriterion("income_room_cnt <", value, "incomeRoomCnt");
            return (Criteria) this;
        }

        public Criteria andIncomeRoomCntLessThanOrEqualTo(Integer value) {
            addCriterion("income_room_cnt <=", value, "incomeRoomCnt");
            return (Criteria) this;
        }

        public Criteria andIncomeRoomCntIn(List<Integer> values) {
            addCriterion("income_room_cnt in", values, "incomeRoomCnt");
            return (Criteria) this;
        }

        public Criteria andIncomeRoomCntNotIn(List<Integer> values) {
            addCriterion("income_room_cnt not in", values, "incomeRoomCnt");
            return (Criteria) this;
        }

        public Criteria andIncomeRoomCntBetween(Integer value1, Integer value2) {
            addCriterion("income_room_cnt between", value1, value2, "incomeRoomCnt");
            return (Criteria) this;
        }

        public Criteria andIncomeRoomCntNotBetween(Integer value1, Integer value2) {
            addCriterion("income_room_cnt not between", value1, value2, "incomeRoomCnt");
            return (Criteria) this;
        }

        public Criteria andIncomeRoomRateIsNull() {
            addCriterion("income_room_rate is null");
            return (Criteria) this;
        }

        public Criteria andIncomeRoomRateIsNotNull() {
            addCriterion("income_room_rate is not null");
            return (Criteria) this;
        }

        public Criteria andIncomeRoomRateEqualTo(BigDecimal value) {
            addCriterion("income_room_rate =", value, "incomeRoomRate");
            return (Criteria) this;
        }

        public Criteria andIncomeRoomRateNotEqualTo(BigDecimal value) {
            addCriterion("income_room_rate <>", value, "incomeRoomRate");
            return (Criteria) this;
        }

        public Criteria andIncomeRoomRateGreaterThan(BigDecimal value) {
            addCriterion("income_room_rate >", value, "incomeRoomRate");
            return (Criteria) this;
        }

        public Criteria andIncomeRoomRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("income_room_rate >=", value, "incomeRoomRate");
            return (Criteria) this;
        }

        public Criteria andIncomeRoomRateLessThan(BigDecimal value) {
            addCriterion("income_room_rate <", value, "incomeRoomRate");
            return (Criteria) this;
        }

        public Criteria andIncomeRoomRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("income_room_rate <=", value, "incomeRoomRate");
            return (Criteria) this;
        }

        public Criteria andIncomeRoomRateIn(List<BigDecimal> values) {
            addCriterion("income_room_rate in", values, "incomeRoomRate");
            return (Criteria) this;
        }

        public Criteria andIncomeRoomRateNotIn(List<BigDecimal> values) {
            addCriterion("income_room_rate not in", values, "incomeRoomRate");
            return (Criteria) this;
        }

        public Criteria andIncomeRoomRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income_room_rate between", value1, value2, "incomeRoomRate");
            return (Criteria) this;
        }

        public Criteria andIncomeRoomRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income_room_rate not between", value1, value2, "incomeRoomRate");
            return (Criteria) this;
        }

        public Criteria andIncomeSingerCntIsNull() {
            addCriterion("income_singer_cnt is null");
            return (Criteria) this;
        }

        public Criteria andIncomeSingerCntIsNotNull() {
            addCriterion("income_singer_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andIncomeSingerCntEqualTo(Integer value) {
            addCriterion("income_singer_cnt =", value, "incomeSingerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomeSingerCntNotEqualTo(Integer value) {
            addCriterion("income_singer_cnt <>", value, "incomeSingerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomeSingerCntGreaterThan(Integer value) {
            addCriterion("income_singer_cnt >", value, "incomeSingerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomeSingerCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("income_singer_cnt >=", value, "incomeSingerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomeSingerCntLessThan(Integer value) {
            addCriterion("income_singer_cnt <", value, "incomeSingerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomeSingerCntLessThanOrEqualTo(Integer value) {
            addCriterion("income_singer_cnt <=", value, "incomeSingerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomeSingerCntIn(List<Integer> values) {
            addCriterion("income_singer_cnt in", values, "incomeSingerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomeSingerCntNotIn(List<Integer> values) {
            addCriterion("income_singer_cnt not in", values, "incomeSingerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomeSingerCntBetween(Integer value1, Integer value2) {
            addCriterion("income_singer_cnt between", value1, value2, "incomeSingerCnt");
            return (Criteria) this;
        }

        public Criteria andIncomeSingerCntNotBetween(Integer value1, Integer value2) {
            addCriterion("income_singer_cnt not between", value1, value2, "incomeSingerCnt");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wavecenter_data_family_week
     *
     * @mbg.generated do_not_delete_during_merge Tue Mar 25 10:23:22 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wavecenter_data_family_week
     *
     * @mbg.generated Tue Mar 25 10:23:22 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}