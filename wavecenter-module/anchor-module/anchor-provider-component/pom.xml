<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>fm.lizhi.ocean.wavecenter</groupId>
        <artifactId>anchor-module</artifactId>
        <version>2.0.3-SNAPSHOT</version>
    </parent>
    <artifactId>anchor-provider-component</artifactId>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <!--region =================本项目模块依赖=================-->
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>anchor-api-component</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>anchor-datastore-component</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>anchor-domain-component</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- 引入公共 -->
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>wavecenter-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>wavecenter-infrastructure</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- 引入service层 -->
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>wavecenter-service</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>award-api-component</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- 其他依赖复制自grow-provider-component -->
        <!--region =================基础架构的依赖=================-->
        <dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>lz-commons-serviceclient</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>lz-commons-util</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.common</groupId>
            <artifactId>lz-common-romefs-javasdk</artifactId>
        </dependency>
        <!-- Okhttp是罗马上传要求的4.x版本 -->
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <!-- kotlin是罗马上传要求的版本 -->
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.common</groupId>
            <artifactId>datastore-redis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.common</groupId>
            <artifactId>dispatcher-executor</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.common</groupId>
            <artifactId>kafka-client-spring-config</artifactId>
        </dependency>
        <!--endregion-->
    </dependencies>
</project>