package fm.lizhi.ocean.wavecenter.provider.anchor.singer.manager;

import javax.annotation.Resource;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import fm.lizhi.common.datastore.redis.client.RedisClient;
import fm.lizhi.ocean.wavecenter.base.constants.TimeConstant;
import fm.lizhi.ocean.wavecenter.common.utils.RedisLock;
import fm.lizhi.ocean.wavecenter.service.activitycenter.constatns.SingerCountMetricEnum;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerMenuConfigDTO;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.rediskey.SingerRedisKey;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.config.SingerAnchorConfig;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerCountInHallDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerRedisManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 歌手相关Redis操作管理
 */
@Component
public class SingerRedisManagerImpl implements SingerRedisManager {

    @Resource(name = "redis_ocean_wavecenter")
    private RedisClient redisClient;

    @Autowired
    private SingerAnchorConfig singerAnchorConfig;

    /**
     * 获取用户是否是歌手
     *
     * @param appId  应用ID
     * @param userId 用户ID
     * @return 是否是歌手
     */
    @Override
    public Boolean isSinger(Integer appId, Long userId) {
        String key = SingerRedisKey.IS_SINGER.getKey(appId, userId);
        String value = redisClient.get(key);
        if (value == null) {
            return null;
        }
        return "1".equals(value);
    }

    /**
     * 设置用户是否是歌手
     *
     * @param appId    应用ID
     * @param userId   用户ID
     * @param isSinger 是否是歌手
     */
    @Override
    public void setIsSinger(Integer appId, Long userId, boolean isSinger) {
        String key = SingerRedisKey.IS_SINGER.getKey(appId, userId);
        redisClient.setex(key, singerAnchorConfig.getSingerIsSingerExpireSeconds(), isSinger ? "1" : "0");
    }

    /**
     * 删除用户是否是歌手的缓存
     *
     * @param appId  应用ID
     * @param userId 用户ID
     */
    @Override
    public void deleteIsSinger(Integer appId, Long userId) {
        String key = SingerRedisKey.IS_SINGER.getKey(appId, userId);
        redisClient.del(key);
    }

    /**
     * 获取厅内歌手总数
     *
     * @param njId  厅主ID
     * @param appId 应用ID
     * @return 返回生效中和审核中的歌手数量
     */
    @Override
    public SingerCountInHallDTO getSingerTotalCountInHall(Integer appId, Long njId) {
        String key = SingerRedisKey.SINGER_TOTAL_COUNT_METRIC_IN_HALL_KEY.getKey(appId, njId);
        String value = redisClient.get(key);
        if (value == null) {
            return null;
        }
        Map<Integer, Integer> count = JSON.parseObject(value, new TypeReference<Map<Integer, Integer>>() {
        });
        return new SingerCountInHallDTO()
                .setEffectiveCount(count.getOrDefault(SingerCountMetricEnum.EFFECTING.getStatus(), 0))
                .setAuditCount(count.getOrDefault(SingerCountMetricEnum.IN_AUDIT.getStatus(), 0))
                .setAuthenticationCount(count.getOrDefault(SingerCountMetricEnum.AUTHENTICATING.getStatus(), 0));
    }

    /**
     * 设置厅内歌手总数
     *
     * @param appId 应用ID
     * @param njId  厅主ID
     * @param count 歌手数量
     */
    @Override
    public void setSingerTotalCountInHall(Integer appId, Long njId, SingerCountInHallDTO count) {
        String key = SingerRedisKey.SINGER_TOTAL_COUNT_METRIC_IN_HALL_KEY.getKey(appId, njId);
        Map<Integer, Integer> value = new HashMap<>();
        value.put(SingerCountMetricEnum.EFFECTING.getStatus(), count.getEffectiveCount());
        value.put(SingerCountMetricEnum.AUTHENTICATING.getStatus(), count.getAuthenticationCount());
        value.put(SingerCountMetricEnum.IN_AUDIT.getStatus(), count.getAuditCount());
        redisClient.setex(key, singerAnchorConfig.getSingerTotalCountInHallExpireSeconds(), JSON.toJSONString(value));
    }

    /**
     * 删除厅内歌手总数缓存
     *
     * @param njId 厅ID
     */
    @Override
    public void deleteSingerTotalCountInHall(Long njId) {
        String key = SingerRedisKey.SINGER_TOTAL_COUNT_IN_HALL.getKey(njId);
        redisClient.del(key);
    }

    /**
     * 添加淘汰标记
     *
     * @param appId  应用ID
     * @param userId 用户ID
     */
    @Override
    public void addEliminateSingerTag(int appId, Long userId) {
        String key = SingerRedisKey.ELIMINATE_SINGER_ZSET.getKey(appId);
        redisClient.zadd(key, System.currentTimeMillis(), String.valueOf(userId));
        redisClient.expire(key, TimeConstant.ONE_DAY_MILLISECOND * 3);
    }

    /**
     * 删除淘汰标记
     *
     * @param appId   应用ID
     * @param userIds 用户ID
     */
    @Override
    public void removeEliminateSingerTag(int appId, List<Long> userIds) {
        String key = SingerRedisKey.ELIMINATE_SINGER_ZSET.getKey(appId);
        redisClient.zrem(key, userIds.stream().map(String::valueOf).toArray(String[]::new));
    }

    /**
     * 获取指定时间范围内的淘汰标记
     *
     * @param appId 应用ID
     */
    @Override
    public List<Long> getEliminateSingerTag(int appId, long startTime, long endTime) {
        String key = SingerRedisKey.ELIMINATE_SINGER_ZSET.getKey(appId);
        Set<String> uidSet = redisClient.zrangeByScore(key, startTime, endTime, 0, -1);
        return uidSet.stream().map(Long::parseLong).collect(Collectors.toList());
    }


    /**
     * 尝试获取歌手信息修改锁（包含认证记录和歌手信息修改，共用一把锁）
     *
     * @param appId  应用ID
     * @param njId 厅主 ID
     * @return 结果，true: 成功，false：失败
     */
    @Override
    public RedisLock tryGetSingerUpdateLock(int appId, long njId) {
        //获取key
        String key = SingerRedisKey.SINGER_INFO_UPDATE_LOCK.getKey(appId, njId);
        int timeout = TimeConstant.ONE_MINUTE * 1000;
        return new RedisLock(redisClient, key, timeout / 3, timeout);
    }

    @Override
    public RedisLock tryGetSingerVerifyApplyLock(int appId, String idCardNumber) {
        //获取key
        String key = SingerRedisKey.SINGER_VERIFY_APPLY_LOCK.getKey(appId, idCardNumber);
        int timeout = TimeConstant.ONE_MINUTE * 1000;
        return new RedisLock(redisClient, key, timeout / 3, timeout);
    }

    @Override
    public void saveApplyMenuConfig(SingerMenuConfigDTO dto) {
        String key = SingerRedisKey.APPLY_MENU_CONFIG.getKey(dto.getAppId(), dto.getSingerType(), ConfigUtils.getEnvRequired().name());
        redisClient.set(key, JSON.toJSONString(dto));
    }

    @Override
    public SingerMenuConfigDTO getApplyMenuConfig(int appId, Integer singerType) {
        String key = SingerRedisKey.APPLY_MENU_CONFIG.getKey(appId, singerType, ConfigUtils.getEnvRequired().name());
        String value = redisClient.get(key);
        if (value == null) {
            return null;
        }
        return JsonUtils.fromJsonString(value, SingerMenuConfigDTO.class);
    }

}