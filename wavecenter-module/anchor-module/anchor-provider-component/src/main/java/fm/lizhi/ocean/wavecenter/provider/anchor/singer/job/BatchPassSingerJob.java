package fm.lizhi.ocean.wavecenter.provider.anchor.singer.job;

import cn.hutool.core.collection.CollUtil;
import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerChatSceneEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateFlowOperatorEnum;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.bean.SingerPassMsg;
import fm.lizhi.ocean.wavecenter.module.api.award.singer.constants.SingerDecorateOperateReasonConstant;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.kafka.producer.SingerKafkaProducer;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.config.SingerAnchorConfig;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.constant.SingerOperatorConstant;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerInfoDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerChatManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerInfoManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 批量通过歌手认证定时任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class BatchPassSingerJob implements JobHandler {

    @Autowired
    private SingerInfoManager singerInfoManager;

    @Autowired
    private SingerChatManager singerChatManager;

    @Autowired
    private SingerKafkaProducer singerKafkaProducer;

    @Autowired
    private SingerAnchorConfig singerAnchorConfig;

    @Override
    public void execute(JobExecuteContext jobExecuteContext) throws Exception {
        for (BusinessEvnEnum evnEnum : BusinessEvnEnum.values()) {
            if (evnEnum.getOnline() != 1) {
                continue;
            }
            try {
                log.info("BatchPassSingerJob start, appId:{}", evnEnum.getAppId());
                ContextUtils.setBusinessEvnEnum(evnEnum);
                if (!singerAnchorConfig.getBizConfig().isLessRelevanceSwitch()) {
                    //不是弱关联，不处理
                    log.info("BatchPassSingerJob not less relevance switch, skip, appId:{}", evnEnum.getAppId());
                    continue;
                }
                // 处理认证歌手
                processSingers(evnEnum.getAppId());
            } catch (Exception e) {
                log.error("BatchPassSingerJob error, appId:{}", evnEnum.getAppId(), e);
            } finally {
                log.info("BatchPassSingerJob end, appId:{}", evnEnum.getAppId());
                ContextUtils.clearContext();
            }
        }
    }

    /**
     * 处理歌手
     *
     * @param appId 应用ID
     */
    private void processSingers(int appId) {
        int executeCount = 0;
        int total = 0;
        int pageNo = 1;
        int pageSize = 50;
        int successCount = 0;
        do {
            // 1. 查询认证中状态的歌手
            PageBean<SingerInfoDTO> pageBean = singerInfoManager.pageSingerInfoByStatusAndType(appId, SingerStatusEnum.AUTHENTICATING, SingerTypeEnum.NEW, pageNo, pageSize);
            if (CollUtil.isEmpty(pageBean.getList())) {
                break;
            }

            List<Long> ids = pageBean.getList().stream()
                    .map(SingerInfoDTO::getId)
                    .collect(Collectors.toList());

            boolean success = singerInfoManager.batchUpdateSingerStatus(appId, ids, SingerStatusEnum.AUTHENTICATING, SingerStatusEnum.EFFECTIVE, SingerOperatorConstant.SYSTEM);
            successCount += success ? ids.size() : 0;
            total = pageBean.getTotal();
            executeCount += pageBean.getList().size();
            pageNo++;

            if (success) {
                //发送kafka消息
                sendKafkaMessage(appId, pageBean.getList());
                //批量发送私信
                singerChatManager.batchSendAuditResultChat(appId, pageBean.getList(), SingerChatSceneEnum.SINGER_AUDIT_PASS);
            }
        } while (executeCount < total);
        log.info("processSingers, appId:{}, total:{}, successCount:{}", appId, total, successCount);
    }

    /**
     * 发送Kafka消息
     *
     * @param appId   业务环境
     * @param singers 歌手列表
     */
    private void sendKafkaMessage(int appId, List<SingerInfoDTO> singers) {
        try {
            for (SingerInfoDTO singerInfoDTO : singers) {
                SingerPassMsg singerPassMsg = new SingerPassMsg()
                        .setAppId(appId)
                        .setSingerIds(Collections.singletonList(singerInfoDTO.getUserId()))
                        .setSingerType(SingerTypeEnum.NEW.getType())
                        .setSongStyle(singerInfoDTO.getSongStyle())
                        .setOperator(SingerDecorateFlowOperatorEnum.SYSTEM.getOperator())
                        .setReason(SingerDecorateOperateReasonConstant.AUTH_PASS)
                        .setTransactionId(String.valueOf(singerInfoDTO.getId()));

                String message = JsonUtil.dumps(singerPassMsg);
                singerKafkaProducer.send("lz_topic_singer_pass_msg", String.valueOf(appId), message);
            }

        } catch (Exception e) {
            log.error("Failed to send kafka message, appId:{}, singerType:{}", appId, SingerTypeEnum.NEW.getType(), e);
        }
    }
}