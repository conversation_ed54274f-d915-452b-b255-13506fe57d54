package fm.lizhi.ocean.wavecenter.provider.anchor.singer.kafka.consumer;

import fm.lizhi.common.kafka.common.strategy.AutoOffsetReset;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaHandler;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaListener;
import fm.lizhi.common.kafka.ioc.api.strategy.annotation.AutoOffsetResetStrategy;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateFlowOperateEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateFlowOperatorEnum;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.bean.SingerPassMsg;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestBatchDeliverSingerAward;
import fm.lizhi.ocean.wavecenter.api.award.singer.serivce.SingerDecorateOperateService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.KafkaMsgUtils;
import fm.lizhi.ocean.wavecenter.module.api.award.singer.constants.SingerDecorateOperateReasonConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 歌手通过信息Kafka消费者 - HY环境
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@KafkaListener(clusterNamespace = "hy-kafka250-bootstrap-server")
public class SingerPassMsgConsumer {

    @Autowired
    private SingerDecorateOperateService singerDecorateOperateService;

    /**
     * 消费歌手通过信息 - HY环境
     *
     * @param body 消息体
     */
    @KafkaHandler(topic = "lz_topic_singer_pass_msg", group = "lz_topic_wavecenter_singer_pass_msg_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleSingerPassMsg(String body) {
        SingerPassMsg singerPassMsg = null;
        try {
            //解析消息
            String msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            log.info("handleSingerPassMsg - receive singer pass msg, msg={}", msg);
            // 解析消息
            singerPassMsg = JsonUtil.loads(msg, SingerPassMsg.class);
            if (singerPassMsg == null) {
                log.warn("handleSingerPassMsg - parse message failed, msg={}", msg);
                return;
            }

            // 验证消息内容
            if (!validateSingerPassMsg(singerPassMsg)) {
                log.warn("handleSingerPassMsg - validate message failed, transactionId={}", singerPassMsg.getTransactionId());
                return;
            }
        } catch (Exception e) {
            log.error("handleSingerPassMsg - parse message failed, msg={}", body, e);
            return;
        }

        //处理歌手信息
        handleSingerPassMsgInternal(singerPassMsg);
    }

    /**
     * 处理歌手通过信息
     *
     * @param singerPassMsg 歌手通过消息
     */
    private void handleSingerPassMsgInternal(SingerPassMsg singerPassMsg) {
        BusinessEvnEnum businessEvnEnum = BusinessEvnEnum.from(singerPassMsg.getAppId());
        // 设置业务环境上下文
        ContextUtils.reSetBusinessEvnEnum(businessEvnEnum);

        // 构建批量发放请求
        RequestBatchDeliverSingerAward request = buildBatchDeliverRequest(singerPassMsg);

        // 调用批量发放服务
        Result<Void> result = singerDecorateOperateService.batchSingerDecorateAwardOperate(request);

        if (RpcResult.isFail(result)) {
            log.error("{}.handleSingerPassMsg - batch singer decorate award operate failed, request={}, rCode={}",
                    businessEvnEnum.name(), request, result.rCode());
            // 抛出异常，不提交kafka消息
            throw new RuntimeException(String.format("batch singer decorate award operate failed, rCode=%s",
                    result.rCode()));
        }

        log.info("{}.handleSingerPassMsg - batch singer decorate award operate success, transactionId={}",
                businessEvnEnum.name(), singerPassMsg.getTransactionId());
    }

    /**
     * 验证歌手通过消息
     *
     * @param singerPassMsg 歌手通过消息
     * @return 验证结果
     */
    private boolean validateSingerPassMsg(SingerPassMsg singerPassMsg) {
        if (singerPassMsg.getAppId() == null || BusinessEvnEnum.from(singerPassMsg.getAppId()) == null) {
            log.warn("validateSingerPassMsg - appId is null, transactionId={}", singerPassMsg.getTransactionId());
            return false;
        }

        if (CollectionUtils.isEmpty(singerPassMsg.getSingerIds())) {
            log.warn("validateSingerPassMsg - singerIds is empty, transactionId={}", singerPassMsg.getTransactionId());
            return false;
        }

        if (SingerTypeEnum.getByType(singerPassMsg.getSingerType()) == null) {
            log.warn("validateSingerPassMsg - singerType is null, transactionId={}", singerPassMsg.getTransactionId());
            return false;
        }

        if (StringUtils.isEmpty(singerPassMsg.getSongStyle())) {
            log.warn("validateSingerPassMsg - operateType is null, transactionId={}", singerPassMsg.getTransactionId());
            return false;
        }

        return true;
    }

    /**
     * 构建批量发放请求
     *
     * @param singerPassMsg 歌手通过消息
     * @return 批量发放请求
     */
    private RequestBatchDeliverSingerAward buildBatchDeliverRequest(SingerPassMsg singerPassMsg) {

        // 处理操作者默认值：为空时设置为"system"
        String operator = StringUtils.isBlank(singerPassMsg.getOperator()) ? SingerDecorateFlowOperatorEnum.SYSTEM.getOperator() : singerPassMsg.getOperator();
        // 处理原因默认值：为空时设置为空字符串
        String reason = singerPassMsg.getReason() == null ? SingerDecorateOperateReasonConstant.AUTH_PASS : singerPassMsg.getReason();

        return new RequestBatchDeliverSingerAward()
                .setAppId(singerPassMsg.getAppId())
                .setSingerIds(singerPassMsg.getSingerIds())
                .setSingerType(singerPassMsg.getSingerType())
                .setOperator(operator)
                .setReason(reason)
                .setOperateType(SingerDecorateFlowOperateEnum.GRANT)
                .setTransactionId(Long.valueOf(singerPassMsg.getTransactionId()));
    }
}
