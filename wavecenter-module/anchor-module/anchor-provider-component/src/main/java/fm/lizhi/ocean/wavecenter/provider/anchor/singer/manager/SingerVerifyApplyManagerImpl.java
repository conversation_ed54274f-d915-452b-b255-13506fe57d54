package fm.lizhi.ocean.wavecenter.provider.anchor.singer.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.verify.constant.SearchType;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerVerifySongInfoBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerRecordOperateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateFlowOperateEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateFlowOperatorEnum;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerVerifyApplySongInfo;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.convert.SingerInfoConvert;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.convert.SingerOperateRecordConvert;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.convert.SingerOrderConvert;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.convert.SingerVerifyConvert;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.datastore.dao.SingerInfoDao;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.datastore.dao.SingerVerifyApplyDao;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerInfo;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerOperateRecord;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerVerifyRecord;
import fm.lizhi.ocean.wavecenter.infrastructure.award.singer.convert.SingerDecorateConvert;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.entity.SingerDecorateFlow;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.datastore.mapper.ext.SingerVerifyRecordExtraMapper;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.config.SingerAnchorConfig;
import fm.lizhi.ocean.wavecenter.module.api.award.singer.constants.SingerDecorateOperateReasonConstant;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.constant.SingerVerifyApplyErrorTipConstant;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.user.constants.VerifyStatusConstant;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.*;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerInfoManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerOperateRecordManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerVerifyApplyManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerBlackListManager;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowGenerateDTO;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowInitParamDTO;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowSendParamDTO;
import fm.lizhi.ocean.wavecenter.service.award.singer.manager.SingerDecorateManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.*;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserVerifyManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SingerVerifyApplyManagerImpl implements SingerVerifyApplyManager {

    @Autowired
    private SingerVerifyApplyDao singerVerifyApplyDao;

    @Autowired
    private SingerInfoManager singerInfoManager;

    @Autowired
    private SingerDecorateManager singerDecorateManager;

    @Autowired
    private SingerOperateRecordManager singerOperateRecordManager;

    @Autowired
    private SingerVerifyConvert singerVerifyConvert;

    @Autowired
    private UserVerifyManager userVerifyManager;

    @Autowired
    private SingerBlackListManager singerBlackListManager;

    @Autowired
    private SingerAnchorConfig singerAnchorConfig;

    @Autowired
    private FamilyManager familyManager;
    @Autowired
    private SingerInfoDao singerInfoDao;

    @Autowired
    private SingerVerifyRecordExtraMapper singerVerifyRecordExtraMapper;

    @Override
    public boolean saveSingerVerifyApply(SingerVerifyApplyDTO param) {
        SingerVerifyRecord singerVerifyApply = singerVerifyConvert.DTO2Entity(param);
        List<SingerVerifyApplySongInfo> singerVerifyApplySongInfos = singerVerifyConvert.songInfo2Entities(param.getSongInfos(), param.getAppId(), param.getUserId());
        return singerVerifyApplyDao.insert(singerVerifyApply, singerVerifyApplySongInfos);
    }

    @Override
    public List<SingerVerifyRecordDTO> getSingerVerifyRecordList(int appId, Long userId) {
        List<SingerVerifyRecord> singerVerifyRecords = singerVerifyApplyDao.getSingerVerifyRecord(appId, userId);
        return singerVerifyConvert.entity2DTO(singerVerifyRecords);
    }

    @Override
    public List<SingerVerifyRecordDTO> getSingerVerifyRecordListByIds(List<Long> ids) {
        List<SingerVerifyRecord> singerVerifyRecords = singerVerifyApplyDao.getSingerVerifyRecordByIds(ids);
        return singerVerifyConvert.entity2DTO(singerVerifyRecords);
    }

    @Override
    public boolean approveSingerVerifyRecord(Long recordId, Integer currentAuditStatus, Integer passSingerType, String operator) {
        // 查询歌手认证记录
        SingerVerifyRecord singerVerifyRecord = singerVerifyApplyDao.getSingerVerifyRecordById(recordId);
        if (singerVerifyRecord == null) {
            return false;
        }

        // 根据认证申请的用户ID和歌手类型查询歌手信息
        SingerInfoDTO singerInfoDTO = singerInfoManager.getSingerInfo(singerVerifyRecord.getAppId(), singerVerifyRecord.getUserId(), passSingerType);

        // 构建歌手信息
        SingerInfo singerInfo = SingerInfoConvert.I.buildSingerInfo(singerVerifyRecord, singerInfoDTO, SingerStatusEnum.EFFECTIVE.getStatus(), passSingerType, operator);
        // 构建如果要通过的歌手类型和查出来的歌手类型不一致，优先使用通过的歌手类型（存在审核时，直接从把资深的申请通过成明星类型的歌手）
        //singerInfo.setSingerType(!Objects.equals(passSingerType, singerVerifyRecord.getSingerType()) ? passSingerType : singerVerifyRecord.getSingerType());

        // 构建歌手装扮发放流水
        SingerDecorateFlowInitParamDTO initParamDTO = buildSingerDecorateFlowInitParamDTO(singerInfo, singerVerifyRecord);
        Optional<SingerDecorateFlowGenerateDTO> decorateFlowOptional = singerDecorateManager.generateSingerDecorateFlowList(initParamDTO);
        List<SingerDecorateFlow> flowList = decorateFlowOptional.map(SingerDecorateFlowGenerateDTO::getDecorateFlowList)
                .map(SingerDecorateConvert.I::toSingerDecorateFlowList)
                .orElse(new ArrayList<>());

        //构建当前审核的用户状态的操作流水
        SingerInfoDTO newSingerInfoDTO = SingerInfoConvert.I.convertSingerInfoDTO(singerInfo);
        SingerOperateRecordDTO operateRecordDto = singerOperateRecordManager.buildSingerOperateRecord(new BuildSingerOperateRecordParamDTO()
                .setSingerInfo(newSingerInfoDTO).setOperateType(SingerRecordOperateTypeEnum.PASS).setOperator(operator)
        );
        SingerOperateRecord singerOperateRecord = SingerOperateRecordConvert.I.convertOperateRecord(operateRecordDto);
        try {
            // 审核通过操作
            Integer currentSingerStatus = singerInfoDTO == null ? null : singerInfoDTO.getSingerStatus();
            Integer needDeleteSingerType = !singerVerifyRecord.getSingerType().equals(passSingerType) ? singerVerifyRecord.getSingerType() : null;
            boolean success = singerVerifyApplyDao.approveSingerVerifyRecord(recordId, currentAuditStatus, currentSingerStatus,
                    operator, singerInfo, flowList, singerOperateRecord, needDeleteSingerType);
            if (success && decorateFlowOptional.isPresent()) {
                // 调用操作装扮方法
                singerDecorateManager.operateSingerDecorateAsync(new SingerDecorateFlowSendParamDTO().setTransactionId(decorateFlowOptional.get().getTransactionId()));
            }
            return success;
        } catch (Exception e) {
            log.error("approve singer verify record failed, recordId:{}, currentStatus:{}", recordId, currentAuditStatus, e);
            return false;
        }
    }

    @Override
    public boolean updateSingerVerifyRecordStatus(UpdateSingerVerifyStatusParamDTO param) {
        // 查询歌手认证记录
        SingerVerifyRecord singerVerifyRecord = singerVerifyApplyDao.getSingerVerifyRecordById(param.getId());
        if (singerVerifyRecord == null) {
            return false;
        }

        Integer currentSingerStatus = null;
        SingerInfo singerInfo = null;
        SingerOperateRecord singerOperateRecord = null;
        Integer needDeleteSingerType = null;
        if (param.isNeedUpdateSingerInfo()) {
            //歌手状态不为空，构建歌手信息
            SingerInfoDTO singerInfoDTO = singerInfoManager.getSingerInfo(singerVerifyRecord.getAppId(), singerVerifyRecord.getUserId(), singerVerifyRecord.getSingerType());
            // 构建歌手信息
            singerInfo = SingerInfoConvert.I.buildSingerInfo(singerVerifyRecord, singerInfoDTO, param.getTargetSingerStatus(), singerVerifyRecord.getSingerType(), param.getOperator());
            singerInfo.setSingerType(!Objects.equals(param.getPassSingerType(), singerVerifyRecord.getSingerType()) ? param.getPassSingerType() : singerVerifyRecord.getSingerType());
            currentSingerStatus = singerInfoDTO == null ? null : singerInfoDTO.getSingerStatus();

            //下面场景不需要淘汰记录
            boolean notNeedEliminateRecord = singerInfoDTO != null && singerInfoDTO.getSingerStatus() == SingerStatusEnum.AUTHENTICATING.getStatus()
                    && param.getTargetSingerStatus() == SingerStatusEnum.ELIMINATED.getStatus();
            // 构建操作流水，
            SingerInfoDTO newSingerInfoDTO = SingerInfoConvert.I.convertSingerInfoDTO(singerInfo);
            SingerRecordOperateTypeEnum operateType = SingerInfoConvert.I.singerStatusConvertOperateType(param.getTargetSingerStatus());
            needDeleteSingerType = !singerVerifyRecord.getSingerType().equals(param.getPassSingerType()) ? singerVerifyRecord.getSingerType() : null;
            //如果是从选中状态到
            if (notNeedEliminateRecord) {
                //歌手状态从认证中->淘汰，不需要淘汰记录
                log.info("not need eliminate record, recordId:{}, currentStatus:{}, targetStatus:{}", singerVerifyRecord.getId(), currentSingerStatus, param.getTargetSingerStatus());
            } else {
                SingerOperateRecordDTO operateRecordList = singerOperateRecordManager.buildSingerOperateRecord(
                        new BuildSingerOperateRecordParamDTO()
                                .setSingerInfo(newSingerInfoDTO)
                                .setOperateType(operateType)
                                .setOperator(param.getOperator())
                                .setEliminationReason(param.getRejectReason())
                );
                singerOperateRecord = SingerOperateRecordConvert.I.convertOperateRecord(operateRecordList);
            }
        }

        // 修改歌手认证记录状态
        try {
            return singerVerifyApplyDao.updateSingerVerifyRecordStatus(param.getId(), param.getCurrentAuditStatus(),
                    param.getTargetAuditStatus(), currentSingerStatus, singerInfo, param.getOperator(), param.getRejectReason(),
                    singerOperateRecord, param.getPreAuditRejectReason(), needDeleteSingerType);
        } catch (Exception e) {
            log.error("update singer verify record status failed, param:{}", JsonUtils.toJsonString(param), e);
            return false;
        }
    }

    /**
     * 将时间戳转换为Date类型
     */
    private Date convertToDate(Long timestamp) {
        return timestamp != null ? new Date(timestamp) : null;
    }

    /**
     * 设置DTO的hasPassVerify字段
     */
    private void setHasPassVerifyFields(List<SingerVerifyRecordDTO> dtoList, Map<String, List<Long>> cardNumberMap, Integer appId) {
        dtoList.forEach(dto -> {
            if (cardNumberMap.containsKey(dto.getIdCardNumber())) {
                Long count = singerOperateRecordManager.getSingerOperateRecordCount(appId, dto.getSingerType(),
                        cardNumberMap.get(dto.getIdCardNumber()), Lists.newArrayList(SingerRecordOperateTypeEnum.ELIMINATE.getOperateType()));
                dto.setHasPassVerify(count != null && count > 0);
            }
        });
    }

    /**
     * 设置DTO的inBlackList字段
     */
    private void setInBlackListFields(List<SingerVerifyRecordDTO> dtoList, Map<String, SingerBlackListDTO> blackListMap) {
        dtoList.forEach(dto -> dto.setInBlackList(blackListMap.containsKey(dto.getIdCardNumber())));
    }

    /**
     * 获取身份证号对应的用户ID映射
     */
    private Map<String, List<Long>> getIdCardNumberUserIdsMap(Integer appId, List<SingerVerifyRecord> records, Integer singerType) {
        List<String> idCardNumbers = records.stream()
                .map(SingerVerifyRecord::getIdCardNumber)
                .distinct()
                .collect(Collectors.toList());
        return singerVerifyApplyDao.batchGetSameIdCardNumberUserIds(appId, idCardNumbers, singerType);
    }

    @Override
    public PageBean<SingerVerifyRecordDTO> pageQuerySingerVerifyRecord(Integer appId, Long userId, Long njId,
                                                                       Integer singerType, Long minApplyTime, Long maxApplyTime,
                                                                       List<String> songStyle, List<Integer> auditStatus,
                                                                       Boolean originalSinger, Integer pageNo, Integer pageSize,
                                                                       String orderMetrics, OrderType orderType) {
        // 计算偏移量
        int offset = (pageNo - 1) * pageSize;
        // 获取环境配置
        String deployEnv = ConfigUtils.getEnvRequired().name();

        if (orderType == null) {
            orderType = OrderType.DESC;
        }

        // 将时间戳转换为Date类型
        Date minApplyDate = convertToDate(minApplyTime);
        Date maxApplyDate = convertToDate(maxApplyTime);

        orderMetrics = SingerOrderConvert.getOrderMetricsByPageQuerySingerVerifyRecordWithBlackList(orderMetrics);

        // 查询分页数据
        List<SingerVerifyRecord> records = singerVerifyRecordExtraMapper.pageQuerySingerVerifyRecord(appId, userId, njId,
                singerType, minApplyDate, maxApplyDate, songStyle, auditStatus, originalSinger,
                deployEnv, offset, pageSize, orderMetrics, orderType.getValue());

        // 查询总数
        Long total = singerVerifyRecordExtraMapper.countSingerVerifyRecord(appId, userId, njId, singerType, minApplyDate,
                maxApplyDate, songStyle, auditStatus, originalSinger, deployEnv);


        // 获取身份证号对应的用户ID映射
        Map<String, List<Long>> cardNumberMap = getIdCardNumberUserIdsMap(appId, records, singerType);

        // 查询黑名单列表
        Map<String, SingerBlackListDTO> blackListMap = singerBlackListManager.searchBlackListByCertNo(appId,
                records.stream().map(SingerVerifyRecord::getIdCardNumber).distinct().collect(Collectors.toList()));

        // 将实体转换为 DTO
        List<SingerVerifyRecordDTO> dtoList = singerVerifyConvert.entity2DTO(records);

        // 设置hasPassVerify和inBlackList字段
        setHasPassVerifyFields(dtoList, cardNumberMap, appId);
        setInBlackListFields(dtoList, blackListMap);

        // 构建新的分页结果
        return PageBean.of(Math.toIntExact(total), dtoList);
    }

    @Override
    public PageBean<SingerVerifyRecordDTO> pageQuerySingerVerifyRecordWithBlackList(Integer appId, Long userId, Long njId,
                                                                                    Integer singerType, Long minApplyTime, Long maxApplyTime,
                                                                                    List<String> songStyle, List<Integer> auditStatus,
                                                                                    Boolean originalSinger, Integer pageNo, Integer pageSize, String orderMetrics, OrderType orderType) {
        // 将时间戳转换为Date类型
        Date minApplyDate = convertToDate(minApplyTime);
        Date maxApplyDate = convertToDate(maxApplyTime);

        // 调用 DAO 层进行分页查询
        PageBean<SingerVerifyRecord> pageBean = singerVerifyApplyDao.pageQuerySingerVerifyRecordWithBlackList(
                appId, userId, njId, singerType, minApplyDate, maxApplyDate,
                songStyle, auditStatus, originalSinger, pageNo, pageSize, orderMetrics, orderType);

        // 获取身份证号对应的用户ID映射
        Map<String, List<Long>> cardNumberMap = getIdCardNumberUserIdsMap(appId, pageBean.getList(), singerType);

        // 将实体转换为 DTO
        List<SingerVerifyRecordDTO> dtoList = singerVerifyConvert.entity2DTO(pageBean.getList());

        // 设置hasPassVerify和inBlackList字段
        setHasPassVerifyFields(dtoList, cardNumberMap, appId);
        dtoList.forEach(dto -> dto.setInBlackList(true));

        // 构建新的分页结果
        return PageBean.of(pageBean.getTotal(), dtoList);
    }

    /**
     * 构建歌手装扮发放流水初始化参数
     *
     * @param singerInfo         歌手信息
     * @param singerVerifyRecord 歌手认证记录
     * @return 歌手装扮发放流水初始化参数
     */
    private SingerDecorateFlowInitParamDTO buildSingerDecorateFlowInitParamDTO(SingerInfo singerInfo, SingerVerifyRecord singerVerifyRecord) {
        SingerInfoDTO singerInfoDTO = SingerInfoConvert.I.convertSingerInfoDTO(singerInfo);
        return new SingerDecorateFlowInitParamDTO()
                .setSingerInfoList(Collections.singletonList(singerInfoDTO))
                .setAppId(singerVerifyRecord.getAppId())
                .setOperateType(SingerDecorateFlowOperateEnum.GRANT)
                .setOperator(SingerDecorateFlowOperatorEnum.SYSTEM.getOperator())
                .setReason(SingerDecorateOperateReasonConstant.AUTH_PASS);
    }

    @Override
    public boolean modifyVerifyApplyRemark(Long id, String remark) {
        return singerVerifyApplyDao.updateRemark(id, remark);
    }

    @Override
    public List<Long> getSameIdCardUserIdByUserId(Integer appId, Long userId, Integer singerType) {
        //查询业务的账号的实名认证证件号
        Result<GetUserVerifyResultDTO> result = userVerifyManager.getUserVerifyResult(new GetUserVerifyResultParamDTO().setUserId(userId).setAppId(appId));
        if (RpcResult.noTarget(result) || result.target().getUserVerifyResult() == null) {
            return Collections.emptyList();
        }
        return singerVerifyApplyDao.getSameIdCardUserIdByIdCardNumber(appId, result.target().getUserVerifyResult().getIdCardNumber(), singerType);
    }

    @Override
    public List<SingerVerifyRecordDTO> getSingerVerifyRecordByTime(Integer appId, Date minModifyTime, Date maxModifyTime, Integer auditStatus) {
        List<SingerVerifyRecord> singerVerifyRecords = singerVerifyApplyDao.getSingerVerifyRecordByTime(appId, minModifyTime, maxModifyTime, auditStatus);
        return singerVerifyConvert.entity2DTO(singerVerifyRecords);
    }

    @Override
    public List<Long> getSameIdCardUserIdByUserId(Integer appId, Long userId) {
        return this.getSameIdCardUserIdByUserId(appId, userId, null);
    }

    @Override
    public boolean existAuditingRecord(Integer appId, Long userId, Integer singerType) {
        List<Integer> statusList = Lists.newArrayList(SingerAuditStatusEnum.WAIT_AUDIT.getStatus(),
                SingerAuditStatusEnum.WAIT_DECIDE.getStatus(), SingerAuditStatusEnum.SELECTED.getStatus());
        List<SingerVerifyRecord> list = singerVerifyApplyDao.getSingerVerifyRecordListByStatus(appId, userId, singerType, statusList);
        return CollectionUtils.isNotEmpty(list);
    }

    @Override
    public Pair<Boolean, String> checkAuditRecordCanApply(Integer appId, Long userId, Integer singerType) {
        List<Integer> statusList = Lists.newArrayList(SingerAuditStatusEnum.WAIT_AUDIT.getStatus(),
                SingerAuditStatusEnum.WAIT_DECIDE.getStatus(), SingerAuditStatusEnum.SELECTED.getStatus(), SingerAuditStatusEnum.REJECTED.getStatus());
        List<SingerVerifyRecord> list = singerVerifyApplyDao.getSingerVerifyRecordListByStatus(appId, userId, singerType, statusList);

        // 没有记录，可以申请
        if (CollectionUtils.isEmpty(list)) {
            return Pair.of(true, "");
        }

        // 存在非REJECTED状态的记录，直接不允许申请
        boolean hasNonRejectedRecord = list.stream()
                .anyMatch(record -> !Objects.equals(record.getAuditStatus(), SingerAuditStatusEnum.REJECTED.getStatus()));
        if (hasNonRejectedRecord) {
            return Pair.of(false, SingerVerifyApplyErrorTipConstant.SINGER_VERIFY_APPLY_EXIST_AUDITING_RECORD);
        }

        // 只有REJECTED状态的记录
        Integer rejectRecordDay = singerAnchorConfig.getBizConfig().getRejectRecordDay();
        if (rejectRecordDay == null || rejectRecordDay == 0) {
            //0或者没有配置，则不做限制
            return Pair.of(true, "");
        }

        //过滤出最新一条审批不过的记录
        // 检查最近一次拒绝的记录是否超过了rejectRecordDay天
        Date now = new Date();
        Optional<SingerVerifyRecord> lastRejectRecord = list.stream()
                .filter(record -> record.getAuditStatus() == SingerAuditStatusEnum.REJECTED.getStatus())
                .max(Comparator.comparing(SingerVerifyRecord::getAuditTime));
        if (lastRejectRecord.isPresent()) {
            Date lastRejectAuditTime = lastRejectRecord.get().getAuditTime();
            long daysBetween = DateUtil.between(lastRejectAuditTime, now, DateUnit.DAY);
            if (daysBetween <= rejectRecordDay) {
                String msg = String.format(SingerVerifyApplyErrorTipConstant.SINGER_VERIFY_APPLY_NO_PASS_DAY_LIMIT, DateUtil.format(DateUtil.offsetDay(lastRejectAuditTime, rejectRecordDay), DatePattern.NORM_DATETIME_PATTERN));
                return Pair.of(false, msg);
            }
        }
        return Pair.of(true, "");
    }

    @Override
    public boolean checkAuditEffectiveRecordCanApply(Integer appId, Long userId, Integer singerType) {
        List<Integer> statusList = Lists.newArrayList(SingerAuditStatusEnum.WAIT_AUDIT.getStatus(),
                SingerAuditStatusEnum.WAIT_DECIDE.getStatus(), SingerAuditStatusEnum.SELECTED.getStatus(), SingerAuditStatusEnum.REJECTED.getStatus());
        List<SingerVerifyRecord> list = singerVerifyApplyDao.getSingerVerifyRecordListByStatus(appId, userId, singerType, statusList);

        // 没有记录，可以申请
        if (CollectionUtils.isEmpty(list)) {
            return true;
        }

        // 存在非REJECTED状态的记录，直接不允许申请
        boolean hasNonRejectedRecord = list.stream()
                .anyMatch(record -> !Objects.equals(record.getAuditStatus(), SingerAuditStatusEnum.REJECTED.getStatus()));
        return !hasNonRejectedRecord;
    }

    @Override
    public Map<Long, String> getIdCardNumberMapByUserIds(Integer appId, List<Long> userIds) {
        return singerVerifyApplyDao.getIdCardNumberMapByUserIds(appId, userIds);
    }

    @Override
    public boolean updateSignInfo(Integer appId, Long singerId, Long familyId, Long njId) {
        try {
            //查询出用户待审核、选中状态的认证记录
            List<Integer> statusList = Lists.newArrayList(SingerAuditStatusEnum.WAIT_AUDIT.getStatus(), SingerAuditStatusEnum.WAIT_DECIDE.getStatus(), SingerAuditStatusEnum.SELECTED.getStatus());
            List<SingerVerifyRecord> singerVerifyRecords = singerVerifyApplyDao.getSingerVerifyRecordListByStatus(appId, singerId, null, statusList);
            //存在歌手认证记录，修改认证记录签约信息
            boolean singer = singerInfoDao.isSinger(appId, singerId);
            return singerVerifyApplyDao.updateFamilyIdAndNjId(appId, singerId, familyId, njId, !singerVerifyRecords.isEmpty(), singer);
        } catch (Exception e) {
            log.error("updateFamilyIdAndNjId happen error, singerId:{}, familyId:{}, njId:{}", singerId, familyId, njId, e);
            return false;
        }
    }

    @Override
    public boolean updateSingerSignInfo(Integer appId, Long singerId) {
        UserInFamilyBean userInFamily = familyManager.getUserInFamily(singerId);
        long njId = 0;
        long familyId = 0;
        if (userInFamily != null && userInFamily.getFamilyId() != null && userInFamily.getNjId() != null) {
            njId = userInFamily.getNjId();
            familyId = userInFamily.getFamilyId();
        }
        return updateSignInfo(appId, singerId, familyId, njId);
    }

    @Override
    public Boolean checkUserVerify(Integer appId, String idCardNumber, Long userId, @Nullable Integer checkSingerType) {
        // 先查询这个用户的认证结果
        SearchUserVerifyResultParamDTO param = new SearchUserVerifyResultParamDTO()
                .setIdCardNumber(idCardNumber)
                .setAppId(appId)
                .setSearchType(SearchType.RESULT.getValue())
                .setVerifyStatus(VerifyStatusConstant.VERIFY_PASS);
        Result<SearchUserVerifyResultDTO> result = userVerifyManager.searchUserVerifyResult(param);

        if (RpcResult.isFail(result) || RpcResult.noTarget(result)) {
            log.warn(" check user verify failed, appId:{}, idCardNumber:{}", appId, idCardNumber);
            // 查询失败，直接返回，让上游拒绝
            return true;
        }

        List<Long> userIdList = result.target().getUserVerifyResultList().stream()
                .map(UserVerifyResultDTO::getUserId)
                .filter(id -> !userId.equals(id))
                .collect(Collectors.toList());
        List<SingerInfoDTO> singerInfoList = singerInfoManager.getSingerInfoByUserIds(userIdList, appId, CollUtil.newArrayList(SingerStatusEnum.AUTHENTICATING, SingerStatusEnum.EFFECTIVE));
        if (CollectionUtils.isNotEmpty(singerInfoList)) {
            log.warn(" check user verify has other singer info, appId:{}, idCardNumber:{}, singerUserIds:{}",
                    appId, idCardNumber, singerInfoList.stream().map(SingerInfoDTO::getUserId).collect(Collectors.toList()));
            return true;
        }

        // 查询认证记录
        List<SingerAuditStatusEnum> statusList = Lists.newArrayList(SingerAuditStatusEnum.WAIT_DECIDE,
                SingerAuditStatusEnum.WAIT_AUDIT, SingerAuditStatusEnum.SELECTED);
        List<Long> sameIdCardUserIdByIdCardNumber = singerVerifyApplyDao.getSameIdCardUserIdByIdCardNumber(appId, idCardNumber, checkSingerType, statusList);
        if (CollectionUtils.isNotEmpty(sameIdCardUserIdByIdCardNumber)) {
            log.warn(" check user verify has other singer verify record, appId:{}, idCardNumber:{}, singerUserIds:{}",
                    appId, idCardNumber, sameIdCardUserIdByIdCardNumber);
            return true;
        }

        return false;
    }

    @Override
    public List<SingerVerifySongInfoBean> getSingerVerifySongInfoList(List<Long> applyIds) {
        List<SingerVerifyApplySongInfo> singerVerifyApplySongInfos = singerVerifyApplyDao.getSingerVerifyApplySongInfoListByApplyId(applyIds);
        return singerVerifyConvert.songInfo2Bean(singerVerifyApplySongInfos);
    }

    @Override
    public List<SingerVerifyRecordDTO> batchGetSingerVerifyRecordList(int appId, List<Long> userIds,
                                                                      List<Integer> singerTypes, List<Integer> statusList) {
        List<SingerVerifyRecord> singerVerifyRecords = singerVerifyApplyDao.batchGetSingerVerifyRecordListByStatus(appId, userIds, singerTypes, statusList);
        return singerVerifyConvert.entity2DTO(singerVerifyRecords);
    }
}
