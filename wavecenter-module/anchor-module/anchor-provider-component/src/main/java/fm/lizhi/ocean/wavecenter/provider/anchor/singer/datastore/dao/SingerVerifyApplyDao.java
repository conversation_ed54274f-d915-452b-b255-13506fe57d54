package fm.lizhi.ocean.wavecenter.provider.anchor.singer.datastore.dao;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.csp.sentinel.util.AssertUtil;
import com.google.common.collect.Lists;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.*;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.mapper.SingerVerifyApplySongInfoMapper;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.convert.SingerOrderConvert;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.mapper.SingerOperateRecordMapper;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.mapper.SingerVerifyRecordMapper;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.datastore.mapper.ext.SingerVerifyRecordExtraMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.award.singer.datastore.dao.SingerDecorateDao;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.entity.SingerDecorateFlow;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class SingerVerifyApplyDao {

    @Autowired
    private SingerVerifyRecordMapper singerVerifyApplyMapper;

    @Autowired
    private SingerInfoDao singerInfoDao;

    @Autowired
    private SingerDecorateDao singerDecorateDao;

    @Autowired
    private SingerOperateRecordMapper singerOperateRecordMapper;

    @Autowired
    private SingerVerifyRecordExtraMapper singerVerifyRecordExtraMapper;

    @Autowired
    private SingerVerifyApplySongInfoMapper singerVerifyApplySongInfoMapper;

    /**
     * 插入歌手认证申请
     *
     * @param singerVerifyApply 歌手认证申请
     * @return 是否插入成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(SingerVerifyRecord singerVerifyApply, List<SingerVerifyApplySongInfo> singerVerifyApplySongInfos) {
        boolean insertResult = singerVerifyApplyMapper.insert(singerVerifyApply) > 0;
        if (insertResult) {
            singerVerifyApplySongInfos.forEach(x -> x.setApplyId(singerVerifyApply.getId()));
            int insertSize = singerVerifyApplySongInfoMapper.batchInsert(singerVerifyApplySongInfos);
            if (insertSize < singerVerifyApplySongInfos.size()) {
                log.warn("insert singer verify apply song info failed. insertSize={}, songInfoSize={}, userId={}, njId={}, singerType={}",
                        insertSize, singerVerifyApplySongInfos.size(), singerVerifyApply.getUserId(), singerVerifyApply.getNjId(), singerVerifyApply.getSingerType());
                throw new RuntimeException("插入歌手认证申请歌曲信息失败");
            }
        }
        log.info("insert singer verify apply result={}, userId={}, njId={}, singerType={}",
                insertResult, singerVerifyApply.getUserId(), singerVerifyApply.getNjId(), singerVerifyApply.getSingerType());
        return insertResult;
    }

    /**
     * 根据用户ID和应用ID查询歌手认证申请记录
     *
     * @param appId  应用ID
     * @param userId 用户ID
     * @return 歌手认证申请记录
     */
    public List<SingerVerifyRecord> getSingerVerifyRecord(int appId, Long userId) {
        SingerVerifyRecordExample example = new SingerVerifyRecordExample();
        example.createCriteria().andAppIdEqualTo(appId).andUserIdEqualTo(userId).andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        return singerVerifyApplyMapper.selectByExample(example);
    }

    /**
     * 根据ID列表查询出歌手认证记录
     *
     * @param ids 歌手认证记录ID列表
     * @return 歌手认证记录列表
     */
    public List<SingerVerifyRecord> getSingerVerifyRecordByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        SingerVerifyRecordExample example = new SingerVerifyRecordExample();
        example.createCriteria().andIdIn(ids);
        return singerVerifyApplyMapper.selectByExample(example);
    }


    /**
     * 根据ID和原始状态修改认证记录状态
     *
     * @param id            歌手认证记录ID
     * @param currentStatus 当前状态
     * @param auditStatus   目标状态
     * @param operator      操作人
     * @param rejectReason  拒绝原因
     * @return 是否修改成功
     */
    public boolean updateSingerVerifyRecordStatus(Long id, List<Integer> currentStatus, Integer auditStatus, String operator, String rejectReason, String preAuditRejectReason) {
        SingerVerifyRecord singerVerifyRecord = new SingerVerifyRecord();
        singerVerifyRecord.setId(id);
        singerVerifyRecord.setAuditStatus(auditStatus);
        singerVerifyRecord.setOperator(operator);
        singerVerifyRecord.setAuditTime(new Date());
        if (Objects.equals(auditStatus, SingerAuditStatusEnum.REJECTED.getStatus())) {
            singerVerifyRecord.setRejectReason(rejectReason == null ? "" : rejectReason);
        }
        if (Objects.equals(auditStatus, SingerAuditStatusEnum.PRE_AUDIT_REJECTED.getStatus())) {
            singerVerifyRecord.setPreAuditRejectReason(preAuditRejectReason == null ? "" : preAuditRejectReason);
        }

        SingerVerifyRecordExample example = new SingerVerifyRecordExample();
        example.createCriteria().andIdEqualTo(id).andAuditStatusIn(currentStatus);
        return singerVerifyApplyMapper.updateByExample(singerVerifyRecord, example) > 0;
    }

    /**
     * 根据ID和原始状态修改认证记录状态
     *
     * @param id            歌手认证记录ID
     * @param currentStatus 当前状态
     * @param auditStatus   目标状态
     * @param operator      操作人
     * @param rejectReason  拒绝原因
     * @return 是否修改成功
     */
    public boolean updateSingerVerifyRecordStatus(Long id, Integer currentStatus, Integer auditStatus, String operator, String rejectReason, String preAuditRejectReason) {
        return updateSingerVerifyRecordStatus(id, CollUtil.newArrayList(currentStatus), auditStatus, operator, rejectReason, preAuditRejectReason);
    }


    /**
     * 根据ID和原始状态修改认证记录状态
     *
     * @param userId        歌手ID
     * @param currentStatus 当前状态
     * @param auditStatus   目标状态
     * @param operator      操作人
     * @param rejectReason  拒绝原因
     * @return 是否修改成功
     */
    public boolean updateSingerVerifyRecordStatusByUserId(Long userId, List<Integer> currentStatus, Integer auditStatus, String operator, String rejectReason, String preAuditRejectReason) {
        SingerVerifyRecord singerVerifyRecord = new SingerVerifyRecord();
        singerVerifyRecord.setUserId(userId);
        singerVerifyRecord.setAuditStatus(auditStatus);
        singerVerifyRecord.setOperator(operator);
        singerVerifyRecord.setAuditTime(new Date());
        if (Objects.equals(auditStatus, SingerAuditStatusEnum.REJECTED.getStatus())) {
            singerVerifyRecord.setRejectReason(rejectReason == null ? "" : rejectReason);
        }
        if (Objects.equals(auditStatus, SingerAuditStatusEnum.PRE_AUDIT_REJECTED.getStatus())) {
            singerVerifyRecord.setPreAuditRejectReason(preAuditRejectReason == null ? "" : preAuditRejectReason);
        }

        SingerVerifyRecordExample example = new SingerVerifyRecordExample();
        example.createCriteria().andUserIdEqualTo(userId).andAuditStatusIn(currentStatus);
        return singerVerifyApplyMapper.updateByExample(singerVerifyRecord, example) > 0;
    }

    /**
     * 事务操作审核通过
     *
     * @param id                   歌手认证记录ID
     * @param currentAuditStatus   当前审核状态
     * @param currentSingerStatus  当前歌手状态
     * @param operator             操作人
     * @param singerInfo           歌手信息
     * @param decorateFlowList     装扮流水列表
     * @param singerOperateRecord  歌手操作记录
     * @param needDeleteSingerType 需要修改歌手类型
     * @return 是否审核通过
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean approveSingerVerifyRecord(Long id, Integer currentAuditStatus, Integer currentSingerStatus, String operator, SingerInfo singerInfo, List<SingerDecorateFlow> decorateFlowList, SingerOperateRecord singerOperateRecord, Integer needDeleteSingerType) {
        boolean success = updateSingerVerifyRecordStatus(id, currentAuditStatus, SingerAuditStatusEnum.PASS.getStatus(), operator, null, null);
        AssertUtil.assertState(success, "审核通过失败");
        if (needDeleteSingerType != null) {
            boolean res = singerInfoDao.deleteSingerInfo(singerInfo.getAppId(), singerInfo.getUserId(), Lists.newArrayList(needDeleteSingerType));
            AssertUtil.assertState(res, "删除歌手信息失败");
        }
        boolean singerUpdateRes = singerInfoDao.updateSingerStatusOrInsert(currentSingerStatus, singerInfo);
        AssertUtil.assertState(singerUpdateRes, "歌手信息更新失败");
        if (CollectionUtils.isNotEmpty(decorateFlowList)) {
            boolean decorateRes = singerDecorateDao.batchInsert(decorateFlowList);
            AssertUtil.assertState(decorateRes, "发放装扮失败");
        }
        if (singerOperateRecord != null) {
            int count = singerOperateRecordMapper.insert(singerOperateRecord);
            AssertUtil.assertState(count > 0, "插入歌手操作记录失败");
        }
        return true;
    }

    /**
     * 修改认证记录状态
     *
     * @param id                   认证记录ID
     * @param currentAuditStatus   当前审核状态
     * @param currentSingerStatus  当前歌手状态
     * @param singerInfo           歌手信息
     * @param targetAuditStatus    目标审核状态
     * @param operator             操作人
     * @param rejectReason         拒绝原因
     * @param preAuditRejectReason 预审核拒绝原因
     * @param needDeleteSingerType 需要修改歌手类型
     * @return 是否修改成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSingerVerifyRecordStatus(Long id, Integer currentAuditStatus, Integer targetAuditStatus, Integer currentSingerStatus, SingerInfo singerInfo, String operator, String rejectReason, SingerOperateRecord singerOperateRecord, String preAuditRejectReason, Integer needDeleteSingerType) {
        boolean success = updateSingerVerifyRecordStatus(id, currentAuditStatus, targetAuditStatus, operator, rejectReason, preAuditRejectReason);
        AssertUtil.assertState(success, "审核状态修改失败");
        if (needDeleteSingerType != null) {
            boolean res = singerInfoDao.deleteSingerInfo(singerInfo.getAppId(), singerInfo.getUserId(), Lists.newArrayList(needDeleteSingerType));
            AssertUtil.assertState(res, "删除歌手信息失败");
        }
        //新增
        boolean singerUpdateRes = singerInfoDao.updateSingerStatusOrInsert(currentSingerStatus, singerInfo);
        AssertUtil.assertState(singerUpdateRes, "歌手信息更新失败");
        if (singerOperateRecord != null) {
            int count = singerOperateRecordMapper.insert(singerOperateRecord);
            AssertUtil.assertState(count > 0, "插入歌手操作记录失败");
        }
        return true;
    }

    /**
     * 根据ID查询歌手认证记录
     *
     * @param id 歌手认证记录ID
     * @return 歌手认证记录
     */
    public SingerVerifyRecord getSingerVerifyRecordById(Long id) {
        SingerVerifyRecord entity = new SingerVerifyRecord();
        entity.setId(id);
        entity.setDeployEnv(ConfigUtils.getEnvRequired().name());
        return singerVerifyApplyMapper.selectOne(entity);
    }

    /**
     * 分页查询歌手认证记录
     *
     * @param appId        应用ID
     * @param userId       用户ID
     * @param njId         厅ID
     * @param singerType   歌手类型
     * @param minApplyTime 最小申请时间
     * @param maxApplyTime 最大申请时间
     * @param songStyle    歌曲风格
     * @param auditStatus  审核状态
     * @param pageNo       页码
     * @param pageSize     每页大小
     * @param orderMetrics
     * @param orderType
     * @return 分页结果
     */
    public PageBean<SingerVerifyRecord> pageQuerySingerVerifyRecord(Integer appId, Long userId, Long njId, Integer singerType, Long minApplyTime, Long maxApplyTime, List<String> songStyle, List<Integer> auditStatus, Boolean originalSinger, Integer pageNo, Integer pageSize, String orderMetrics, OrderType orderType) {
        SingerVerifyRecordExample example = new SingerVerifyRecordExample();
        SingerVerifyRecordExample.Criteria criteria = example.createCriteria();

        // 设置查询条件
        if (appId != null) {
            criteria.andAppIdEqualTo(appId);
        }
        if (userId != null) {
            criteria.andUserIdEqualTo(userId);
        }
        if (njId != null) {
            criteria.andNjIdEqualTo(njId);
        }
        if (singerType != null) {
            criteria.andSingerTypeEqualTo(singerType);
        }
        if (minApplyTime != null) {
            criteria.andCreateTimeGreaterThanOrEqualTo(new java.util.Date(minApplyTime));
        }
        if (maxApplyTime != null) {
            criteria.andCreateTimeLessThanOrEqualTo(new java.util.Date(maxApplyTime));
        }
        if (CollectionUtils.isNotEmpty(songStyle)) {
            criteria.andSongStyleIn(songStyle);
        }
        if (CollectionUtils.isNotEmpty(auditStatus)) {
            criteria.andAuditStatusIn(auditStatus);
        }

        if (originalSinger != null) {
            criteria.andOriginalSingerEqualTo(originalSinger);
        }

        // 设置环境
        criteria.andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());

        // 设置排序
        example.setOrderByClause(SingerOrderConvert.buildOrderByClause4pageQuerySingerVerifyRecord(orderMetrics, orderType));

        // 查询分页数据
        PageList<SingerVerifyRecord> pageList = singerVerifyApplyMapper.pageByExample(example, pageNo, pageSize);

        // 构建分页结果
        return PageBean.of(pageList.getTotal(), pageList);
    }

    /**
     * 更新备注
     *
     * @param id     主键ID
     * @param remark 备注
     * @return 是否成功
     */
    public boolean updateRemark(Long id, String remark) {
        SingerVerifyRecordExample example = new SingerVerifyRecordExample();
        SingerVerifyRecordExample.Criteria criteria = example.createCriteria();
        criteria.andIdEqualTo(id);

        SingerVerifyRecord record = new SingerVerifyRecord();
        record.setRemark(remark);
        return singerVerifyApplyMapper.updateByExample(record, example) > 0;
    }

    /**
     * 根据用户ID查询出同证件号下的所有认证用户ID
     *
     * @param appId        应用ID
     * @param idCardNumber 用户ID
     * @return 同证件号下的所有认证用户ID
     */
    public List<Long> getSameIdCardUserIdByIdCardNumber(Integer appId, String idCardNumber, Integer singerType) {
        //先查询出用户ID对应的idCardNumber
        List<SingerAuditStatusEnum> statusList = Lists.newArrayList(SingerAuditStatusEnum.PASS, SingerAuditStatusEnum.WAIT_DECIDE, SingerAuditStatusEnum.WAIT_AUDIT, SingerAuditStatusEnum.SELECTED);
        return getSameIdCardUserIdByIdCardNumber(appId, idCardNumber, singerType, statusList);
    }

    /**
     * 根据用户ID查询出同证件号下的所有认证用户ID
     *
     * @param appId        应用ID
     * @param idCardNumber 用户ID
     * @return 同证件号下的所有认证用户ID
     */
    public List<Long> getSameIdCardUserIdByIdCardNumber(Integer appId, String idCardNumber, Integer singerType, List<SingerAuditStatusEnum> statusList) {
        //先查询出用户ID对应的idCardNumber
        SingerVerifyRecordExample example = new SingerVerifyRecordExample();
        SingerVerifyRecordExample.Criteria criteria = example.createCriteria();
        criteria.andIdCardNumberEqualTo(idCardNumber);
        criteria.andAppIdEqualTo(appId);
        criteria.andAuditStatusIn(statusList.stream().map(SingerAuditStatusEnum::getStatus).collect(Collectors.toList()));
        criteria.andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        if (singerType != null) {
            criteria.andSingerTypeEqualTo(singerType);
        }
        List<SingerVerifyRecord> singerVerifyRecords = singerVerifyApplyMapper.selectByExample(example);
        //过滤出用户ID并去重
        return singerVerifyRecords.stream().map(SingerVerifyRecord::getUserId).distinct().collect(Collectors.toList());
    }


    /**
     * 按修改时间和审核状态查询认证记录表
     *
     * @param appId       应用ID
     * @param startTime   开始时间 开区间
     * @param endTime     结束时间 闭区间
     * @param auditStatus 审核状态
     */
    public List<SingerVerifyRecord> getSingerVerifyRecordByTime(Integer appId, Date startTime, Date endTime, Integer auditStatus) {
        SingerVerifyRecordExample example = new SingerVerifyRecordExample();
        SingerVerifyRecordExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(appId);
        criteria.andAuditStatusEqualTo(auditStatus);
        criteria.andModifyTimeGreaterThanOrEqualTo(startTime);
        criteria.andModifyTimeLessThanOrEqualTo(endTime);
        criteria.andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        example.setOrderByClause("modify_time desc");
        return singerVerifyApplyMapper.selectByExample(example);
    }

    /**
     * 根据身份证号列表查询已通过审核的记录的身份证号
     *
     * @param idCardNumbers 身份证号列表
     * @return 已通过审核的身份证号列表
     */
    public List<String> getPassedVerifyIdCardNumbers(List<String> idCardNumbers, Integer singerType) {
        if (CollUtil.isEmpty(idCardNumbers) || singerType == null) {
            return new ArrayList<>();
        }
        SingerVerifyRecordExample example = new SingerVerifyRecordExample();
        example.createCriteria().andIdCardNumberIn(idCardNumbers).andAuditStatusEqualTo(SingerAuditStatusEnum.PASS.getStatus()).andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name()).andSingerTypeEqualTo(singerType);
        List<SingerVerifyRecord> records = singerVerifyApplyMapper.selectByExample(example);
        return records.stream().map(SingerVerifyRecord::getIdCardNumber).collect(Collectors.toList());
    }


    /**
     * 更新厅下的审核状态
     *
     * @param njId
     * @param currentStatus
     * @param auditStatus
     * @param verifyRejectReason
     * @param operator
     * @return
     */
    public void updateSingerVerifyRecordStatusByNjId(Long njId, List<Integer> currentStatus, int auditStatus, String verifyRejectReason, String operator) {
        SingerVerifyRecord singerVerifyRecord = new SingerVerifyRecord();
        singerVerifyRecord.setNjId(njId);
        singerVerifyRecord.setAuditStatus(auditStatus);
        singerVerifyRecord.setOperator(operator);
        singerVerifyRecord.setAuditTime(new Date());
        singerVerifyRecord.setModifyTime(new Date());
        if (Objects.equals(auditStatus, SingerAuditStatusEnum.REJECTED.getStatus())) {
            singerVerifyRecord.setRejectReason(verifyRejectReason == null ? "" : verifyRejectReason);
        }
        if (Objects.equals(auditStatus, SingerAuditStatusEnum.PRE_AUDIT_REJECTED.getStatus())) {
            singerVerifyRecord.setPreAuditRejectReason(verifyRejectReason == null ? "" : verifyRejectReason);
        }
        SingerVerifyRecordExample example = new SingerVerifyRecordExample();
        example.createCriteria().andNjIdEqualTo(njId).andAuditStatusIn(currentStatus);
        singerVerifyApplyMapper.updateByExample(singerVerifyRecord, example);
    }

    /**
     * 根据厅ID和审核状态查询认证记录
     *
     * @param njId        厅ID
     * @param auditStatus 审核状态
     * @return 认证记录列表
     */
    public List<SingerVerifyRecord> getSingerVerifyRecordListByNjId(Long njId, List<Integer> auditStatus) {
        SingerVerifyRecordExample example = new SingerVerifyRecordExample();
        SingerVerifyRecordExample.Criteria criteria = example.createCriteria();
        criteria.andNjIdEqualTo(njId);
        criteria.andAuditStatusIn(auditStatus);
        return singerVerifyApplyMapper.selectByExample(example);
    }

    /**
     * 根据状态查询认证记录
     *
     * @param appId       应用ID
     * @param singerType  歌手类型
     * @param auditStatus 审核状态
     * @return 结果
     */
    public List<SingerVerifyRecord> getSingerVerifyRecordListByStatus(Integer appId, Long userId, Integer singerType, List<Integer> auditStatus) {
        SingerVerifyRecordExample example = new SingerVerifyRecordExample();
        SingerVerifyRecordExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(appId)
                .andUserIdEqualTo(userId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        if (singerType != null) {
            criteria.andSingerTypeEqualTo(singerType);
        }
        if (CollUtil.isNotEmpty(auditStatus)) {
            criteria.andAuditStatusIn(auditStatus);
        }
        return singerVerifyApplyMapper.selectByExample(example);
    }

    /**
     * 批量查询查询出同证件号下的所有认证用户ID
     *
     * @param appId         应用ID
     * @param idCardNumbers 证件号
     * @return key: 证件号， value：同证件号下的所有认证用户ID
     */
    public Map<String, List<Long>> batchGetSameIdCardNumberUserIds(Integer appId, List<String> idCardNumbers, Integer singerType) {
        //创建一个map
        Map<String, List<Long>> map = new HashMap<>();
        //遍历idCardNumbers
        for (String idCardNumber : idCardNumbers) {
            List<Long> userIds = getSameIdCardUserIdByIdCardNumber(appId, idCardNumber, singerType);
            map.put(idCardNumber, userIds);
        }
        return map;
    }

    public Map<Long, String> getIdCardNumberMapByUserIds(Integer appId, List<Long> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return new HashMap<>();
        }
        SingerVerifyRecordExample example = new SingerVerifyRecordExample();
        example.createCriteria().andAppIdEqualTo(appId).andUserIdIn(userIds).andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        List<SingerVerifyRecord> singerVerifyRecords = singerVerifyApplyMapper.selectByExample(example);
        return singerVerifyRecords.stream().collect(Collectors.toMap(SingerVerifyRecord::getUserId, SingerVerifyRecord::getIdCardNumber, (oldValue, newValue) -> oldValue));
    }

    /**
     * 分页查询歌手认证记录（包含黑名单）
     *
     * @param appId          应用ID
     * @param userId         用户ID
     * @param njId           厅ID
     * @param singerType     歌手类型
     * @param minApplyTime   最小申请时间
     * @param maxApplyTime   最大申请时间
     * @param songStyle      歌曲风格
     * @param auditStatus    审核状态
     * @param originalSinger 是否原创歌手
     * @param pageNo         页码
     * @param pageSize       每页大小
     * @param orderMetrics
     * @param orderType
     * @return 分页结果
     */
    public PageBean<SingerVerifyRecord> pageQuerySingerVerifyRecordWithBlackList(Integer appId, Long userId, Long njId, Integer singerType, Date minApplyTime, Date maxApplyTime, List<String> songStyle, List<Integer> auditStatus, Boolean originalSinger, Integer pageNo, Integer pageSize, String orderMetrics, OrderType orderType) {
        // 计算偏移量
        int offset = (pageNo - 1) * pageSize;

        // 获取环境配置
        String deployEnv = ConfigUtils.getEnvRequired().name();

        if (orderType == null) {
            orderType = OrderType.DESC;
        }

        orderMetrics = SingerOrderConvert.getOrderMetricsByPageQuerySingerVerifyRecordWithBlackList(orderMetrics);


        // 查询分页数据
        List<SingerVerifyRecord> records = singerVerifyRecordExtraMapper.pageQuerySingerVerifyRecordWithBlackList(appId, userId, njId, singerType, minApplyTime, maxApplyTime, songStyle, auditStatus, originalSinger, deployEnv, offset, pageSize, orderMetrics, orderType.getValue());

        // 查询总数
        Long total = singerVerifyRecordExtraMapper.countSingerVerifyRecordWithBlackList(appId, userId, njId, singerType, minApplyTime, maxApplyTime, songStyle, auditStatus, originalSinger, deployEnv);

        // 构建分页结果
        return PageBean.of(total.intValue(), records);
    }

    /**
     * 修改非终态的认证记录家族长和厅主ID
     *
     * @param appId    应用ID
     * @param singerId 歌手ID
     * @param familyId 家族ID
     * @param njId     厅主ID
     * @return 是否修改成功
     */
    public boolean updateVerifyRecordSignInfo(Integer appId, Long singerId, Long familyId, Long njId) {
        SingerVerifyRecordExample example = new SingerVerifyRecordExample();
        SingerVerifyRecordExample.Criteria criteria = example.createCriteria();
        criteria.andUserIdEqualTo(singerId).andAppIdEqualTo(appId).andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name()).andAuditStatusIn(Arrays.asList(SingerAuditStatusEnum.WAIT_AUDIT.getStatus(), SingerAuditStatusEnum.WAIT_DECIDE.getStatus(), SingerAuditStatusEnum.SELECTED.getStatus()));

        SingerVerifyRecord singerVerifyRecord = new SingerVerifyRecord();
        singerVerifyRecord.setFamilyId(familyId);
        singerVerifyRecord.setNjId(njId);
        return singerVerifyApplyMapper.updateByExample(singerVerifyRecord, example) > 0;
    }

    /**
     * 修改家族ID和厅主ID
     *
     * @param appId             应用ID
     * @param singerId          歌手ID
     * @param familyId          家族ID
     * @param njId              厅主ID
     * @param existVerifyRecord 是否存在认证记录
     * @param isSinger          是否是歌手
     * @return 是否修改成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateFamilyIdAndNjId(Integer appId, Long singerId, Long familyId, Long njId, boolean existVerifyRecord, boolean isSinger) {
        if (existVerifyRecord) {
            boolean res = updateVerifyRecordSignInfo(appId, singerId, familyId, njId);
            AssertUtil.assertState(res, "修改认证记录签约信息失败");
        }

        if (isSinger) {
            boolean singerRes = singerInfoDao.updateSingerSignInfo(appId, singerId, familyId, njId);
            AssertUtil.assertState(singerRes, "修改歌手签约信息失败");
        }
        return true;
    }

    /**
     * 根据认证申请ID列表查询歌曲信息
     *
     * @param applyIds 认证申请ID列表
     * @return 歌曲信息列表
     */
    public List<SingerVerifyApplySongInfo> getSingerVerifyApplySongInfoListByApplyId(List<Long> applyIds) {
        SingerVerifyApplySongInfoExample example = new SingerVerifyApplySongInfoExample();
        example.createCriteria().andApplyIdIn(applyIds);
        return singerVerifyApplySongInfoMapper.selectByExample(example);
    }

    /**
     * 批量查询认证记录
     *
     * @param appId       应用ID
     * @param singerType  歌手类型
     * @param auditStatus 审核状态
     * @return 结果
     */
    public List<SingerVerifyRecord> batchGetSingerVerifyRecordListByStatus(Integer appId, List<Long> userIds,
                                                                           List<Integer> singerType, List<Integer> auditStatus) {
        SingerVerifyRecordExample example = new SingerVerifyRecordExample();
        SingerVerifyRecordExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(appId)
                .andUserIdIn(userIds)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        if (CollUtil.isNotEmpty(singerType)) {
            criteria.andSingerTypeIn(singerType);
        }
        if (CollUtil.isNotEmpty(auditStatus)) {
            criteria.andAuditStatusIn(auditStatus);
        }
        return singerVerifyApplyMapper.selectByExample(example);
    }

    /**
     * 根据ID和原始状态修改认证记录状态
     *
     * @param auditStatus  目标状态
     * @param operator     操作人
     * @param rejectReason 拒绝原因
     * @return 是否修改成功
     */
    public boolean batchUpdateSingerVerifyRecordStatus(Integer auditStatus, String operator, String rejectReason, List<SingerVerifyRecord> verifyRecords) {
        if (CollectionUtils.isEmpty(verifyRecords)) {
            return true;
        }
        for (SingerVerifyRecord record : verifyRecords) {
            boolean res = updateSingerVerifyRecordStatus(record.getId(), CollUtil.newArrayList(record.getAuditStatus()), auditStatus, operator, rejectReason, null);
            if (!res) {
                return false;
            }
        }
        return true;
    }
}
