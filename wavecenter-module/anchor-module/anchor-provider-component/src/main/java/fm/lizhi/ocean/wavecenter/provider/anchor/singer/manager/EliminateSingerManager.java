package fm.lizhi.ocean.wavecenter.provider.anchor.singer.manager;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.base.processor.ProcessorFactory;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerDataDay;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerDataDayExample;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerWhiteListConfig;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.mapper.SingerDataDayMapper;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.config.SingerAnchorConfig;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.constant.SingerEliminationReasonConstant;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.constant.SingerOperatorConstant;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerInfoDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerVerifyRecordDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.UpdateSingerVerifyStatusParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerInfoManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerRedisManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerVerifyApplyManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.processor.ISingerHallApplyProcessor;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.processor.ISingerProcessor;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 淘汰管理类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class EliminateSingerManager {

    @Autowired
    private SingerInfoManager singerInfoManager;

    @Autowired
    private SingerRedisManager singerRedisManager;

    @Autowired
    //TODO 需要api
    private FamilyManager familyManager;

    @Autowired
    private ProcessorFactory factory;

    @Autowired
    private SingerVerifyApplyManager singerVerifyApplyManager;

    @Autowired
    private SingerWhiteListManager singerWhiteListManager;

    @Autowired
    private SingerAnchorConfig singerConfig;

    @Resource
    private SingerDataDayMapper singerDataDayMapper;

    /**
     * 处理解约主播
     *
     * @param evnEnum 业务枚举
     */
    public void autoHandelUnSignPlayers(BusinessEvnEnum evnEnum) {
        Date lastDay = DateUtil.getMinuteBefore(new Date(), singerConfig.getEliminateSingerAfterMinutes());
        // 查询 24小时之前解约的主播吗
        List<Long> unSignUserIds = singerRedisManager.getEliminateSingerTag(evnEnum.getAppId(), 0, lastDay.getTime());
        if (unSignUserIds.isEmpty()) {
            return;
        }
        Set<Long> deleteUserTagIds = new HashSet<>();
        Set<Long> eliminateSingerIds = new HashSet<>();

        ISingerHallApplyProcessor processor = factory.getProcessor(ISingerHallApplyProcessor.class);
        ISingerProcessor singerProcessor = factory.getProcessor(ISingerProcessor.class);
        Map<Long, SingerVerifyRecordDTO> waitAuditVerifyRecordMap = getWaitAuditVerifyRecordMap(evnEnum.getAppId(), unSignUserIds);
        for (Long userId : unSignUserIds) {
            List<SingerInfoDTO> singerInfoList = singerProcessor.getWaitAutoEliminateSingerInfo(evnEnum.getAppId(), userId);
            SingerVerifyRecordDTO waitAuditVerifyRecord = waitAuditVerifyRecordMap.get(userId);
            UserInFamilyBean familyBean = familyManager.getUserInFamily(userId);

            //处理待审核的认证记录
            if (waitAuditVerifyRecord != null) {
                boolean autoHandlerWaitAuditVerifyRes = autoHandlerWaitAuditVerify(evnEnum.getAppId(), waitAuditVerifyRecord, familyBean);
                if (!autoHandlerWaitAuditVerifyRes) {
                    log.info("autoHandlerWaitAuditVerify failed, userId:{}", userId);
                    continue;
                }
            }

            // 没有歌手记录，删除淘汰标记
            if (singerInfoList.isEmpty()) {
                log.info("notfound singer info, skip. userId:{}", userId);
                deleteUserTagIds.add(userId);
                continue;
            }


            //查询白名单信息
            List<SingerWhiteListConfig> whiteList = singerWhiteListManager.getWhiteList(evnEnum.getAppId(), userId);
            //去掉在白名单中的歌手信息
            removeWhiteListSinger(singerInfoList, whiteList);
            if (singerInfoList.isEmpty()) {
                //都在白名单中，跳过，记录下用户ID
                log.info("all singer in white list, skip. userId:{}", userId);
                continue;
            }

            if (isNonSign(familyBean)) {
                // 没有家族信息、没有签约厅信息，淘汰
                log.info(" user not in family or nj, skip. userId:{}, family:{}", userId, familyBean);
                eliminateSingerIds.addAll(singerInfoList.stream().map(SingerInfoDTO::getId).collect(Collectors.toList()));
                deleteUserTagIds.add(userId);
                continue;
            }

            //差异化处理：点唱厅签约状态是否正常
            processor.handleWaitEliminateSingerByHallStatus(singerInfoList, familyBean, eliminateSingerIds, deleteUserTagIds);

            // 更新一下最新的家族信息
            singerVerifyApplyManager.updateSignInfo(evnEnum.getAppId(), userId, familyBean.getFamilyId(), familyBean.getNjId());
            log.info("update singer info success, userId:{}, familyId:{}, njId:{}", userId, familyBean.getFamilyId(), familyBean.getNjId());

            // 如果什么都没发生，也加一下淘汰标记, 保底，理论上不会走到这里
            deleteUserTagIds.add(userId);
        }

        // 淘汰歌手
        if (CollectionUtils.isNotEmpty(eliminateSingerIds)) {
            Boolean success = singerInfoManager.eliminateSinger(evnEnum.appId(), new ArrayList<>(eliminateSingerIds), SingerOperatorConstant.SYSTEM, SingerEliminationReasonConstant.SINGER_UNSIGNED, false);
            log.info("eliminate singer, ids:{}", eliminateSingerIds);
            // 删除淘汰标记
            if (success && CollUtil.isNotEmpty(deleteUserTagIds)) {
                singerRedisManager.removeEliminateSingerTag(evnEnum.getAppId(), new ArrayList<>(deleteUserTagIds));
                log.info("remove eliminate singer tag, userIds:{}", deleteUserTagIds);
            }
        } else {
            log.info("not found singer to eliminate, delete eliminate tag. userIds:{}", unSignUserIds);
            singerRedisManager.removeEliminateSingerTag(evnEnum.getAppId(), new ArrayList<>(deleteUserTagIds));
        }

    }

    /**
     * 处理X天无营收，无上麦主播，直接淘汰
     *
     * @param evnEnum 业务枚举
     */
    public void autoHandelNoRevenuePlayers(BusinessEvnEnum evnEnum) {
        log.info("autoHandelNoRevenuePlayers, appId:{}", evnEnum.getAppId());
        int pageNo = 1;
        int pageSize = 50;
        Integer lastDayValue = MyDateUtil.getDateDayValue(new Date());
        boolean hasNextPage;
        AtomicInteger allCount = new AtomicInteger(0);
        AtomicInteger eliminateCount = new AtomicInteger(0);

        SingerDataDayExample example = new SingerDataDayExample();
        example.createCriteria()
                .andAppIdEqualTo(evnEnum.getAppId())
                .andStatDateValueEqualTo(lastDayValue)
                .andLastXDayIncomeEqualTo(0L);
        // 按 njId 排序一下，避免锁竞争太激烈
        example.setOrderByClause("nj_id asc");

        do {
            PageList<SingerDataDay> pageList = singerDataDayMapper.pageByExample(example, pageNo, pageSize);
            if (CollUtil.isEmpty(pageList)) {
                return;
            }

            Set<Long> singerIds = new HashSet<>();
            for (SingerDataDay singer : pageList) {
                List<SingerInfoDTO> list = singerInfoManager.getSingerInfoByUserId(evnEnum.getAppId(), singer.getUserId(),
                        Lists.newArrayList(SingerStatusEnum.EFFECTIVE, SingerStatusEnum.AUTHENTICATING)
                );
                if (CollUtil.isEmpty(list)) {
                    continue;
                }
                singerIds.addAll(list.stream().map(SingerInfoDTO::getId).collect(Collectors.toList()));
            }

            if (CollUtil.isNotEmpty(singerIds)) {
                log.info("eliminate singer, singerIds:{}", singerIds);
                singerInfoManager.eliminateSinger(evnEnum.appId(),
                        new ArrayList<>(singerIds), SingerOperatorConstant.SYSTEM,
                        SingerEliminationReasonConstant.NO_REVENUE, false);
            }
            allCount.addAndGet(pageList.size());
            eliminateCount.addAndGet(singerIds.size());
            pageNo++;
            hasNextPage = pageList.isHasNextPage();

        } while (hasNextPage);
        log.info("eliminate singer, allCount:{}, eliminateCount:{}, appId:{}", allCount.get(), eliminateCount.get(), evnEnum.getAppId());
    }

    /**
     * 自动处理待审核的认证记录(只处理认证歌手的申请记录，高级歌手在淘汰歌手时事务处理)
     * 因为认证记录待审核时，还没有歌手信息，无法在淘汰歌手时关联处理，所以需要单独处理
     *
     * @param appId                 应用ID
     * @param waitAuditVerifyRecord 待审核的认证记录
     * @param familyBean            用户家族信息
     * @return 结果
     */
    public boolean autoHandlerWaitAuditVerify(int appId, SingerVerifyRecordDTO waitAuditVerifyRecord, UserInFamilyBean familyBean) {
        ISingerHallApplyProcessor processor = factory.getProcessor(ISingerHallApplyProcessor.class);
        boolean rejectWaitAuditVerify = processor.isRejectWaitAuditVerify(familyBean);
        if (rejectWaitAuditVerify) {
            //拒绝认证申请
            UpdateSingerVerifyStatusParamDTO param = new UpdateSingerVerifyStatusParamDTO()
                    .setId(waitAuditVerifyRecord.getId())
                    .setCurrentAuditStatus(waitAuditVerifyRecord.getAuditStatus())
                    .setTargetAuditStatus(SingerAuditStatusEnum.REJECTED.getStatus())
                    .setRejectReason(SingerEliminationReasonConstant.SINGER_UNSIGNED)
                    .setOperator(SingerOperatorConstant.SYSTEM);
            boolean res = singerVerifyApplyManager.updateSingerVerifyRecordStatus(param);
            log.info("autoHandlerWaitAuditVerify, reject wait audit verify, recordId:{}, userId:{}, res:{}", waitAuditVerifyRecord.getId(), waitAuditVerifyRecord.getUserId(), res);
            return res;
        }
        return true;
    }

    /**
     * 是否未签约
     *
     * @param familyBean 用户家族信息
     * @return 结果，true未签约，false已签约
     */
    private boolean isNonSign(UserInFamilyBean familyBean) {
        return familyBean == null || familyBean.getFamilyId() == null || familyBean.getFamilyId() <= 0 || familyBean.getNjId() == null || familyBean.getNjId() <= 0;
    }

    /**
     * 去掉白名单中的歌手信息
     *
     * @param singerInfoList 歌手信息列表
     * @param whiteList      白名单列表
     */
    private void removeWhiteListSinger(List<SingerInfoDTO> singerInfoList, List<SingerWhiteListConfig> whiteList) {
        if (CollUtil.isEmpty(whiteList)) {
            log.info("not found white list, skip. userId:{}", singerInfoList.size());
            return;
        }
        List<Integer> whiteListSingerType = whiteList.stream().map(SingerWhiteListConfig::getSingerType).collect(Collectors.toList());
        log.info("removeWhiteListSinger.whiteList:{}", whiteList);
        singerInfoList.removeIf(singer -> whiteListSingerType.contains(singer.getSingerType()));
    }

    /**
     * 获取待审核的认证记录map
     *
     * @param appId   应用ID
     * @param userIds 用户ID列表
     * @return 待审核的认证记录map
     */
    private Map<Long, SingerVerifyRecordDTO> getWaitAuditVerifyRecordMap(int appId, List<Long> userIds) {
        List<Integer> statusList = Lists.newArrayList(SingerAuditStatusEnum.WAIT_AUDIT.getStatus(), SingerAuditStatusEnum.WAIT_DECIDE.getStatus());
        List<SingerVerifyRecordDTO> singerVerifyRecords = singerVerifyApplyManager.batchGetSingerVerifyRecordList(appId, userIds, Lists.newArrayList(SingerTypeEnum.NEW.getType()), statusList);
        return singerVerifyRecords.stream().collect(Collectors.toMap(SingerVerifyRecordDTO::getUserId, Function.identity()));
    }
}
