package fm.lizhi.ocean.wavecenter.provider.anchor.singer.impl;

import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerChatSceneEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.processor.ProcessorFactory;
import fm.lizhi.ocean.wavecenter.common.utils.RedisLock;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerInfo;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.bean.ImportSingerInfoBean;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.bean.ImportSingerInfoResultBean;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.request.RequestBatchImportSinger;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.response.ResponseBatchImportSinger;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.service.BatchImportSingerService;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.manager.SingerImportManager;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.model.convert.SingerInfoConvert;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.processor.ISingerDecorateProcessor;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerChatManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerRedisManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerVerifyApplyManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.processor.ISingerProcessor;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserVerifyResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Slf4j
@ServiceProvider
public class BatchImportSingerServiceImpl implements BatchImportSingerService {

    @Autowired
    private SingerImportManager singerImportManager;
    @Autowired
    private ProcessorFactory processorFactory;
    @Autowired
    private SingerRedisManager singerRedisManager;
    @Autowired
    private SingerVerifyApplyManager singerVerifyApplyManager;
    @Autowired
    private SingerChatManager singerChatManager;
    @Autowired
    private ProcessorFactory factory;



    @Override
    public Result<ResponseBatchImportSinger> batchImportSinger(RequestBatchImportSinger request) {
        List<ImportSingerInfoResultBean> failImportSingerIdList = Lists.newArrayList();
        Date now = new Date();
        Integer appId = request.getAppId();
        String operator = request.getOperator();
        List<ImportSingerInfoBean> infos = request.getSingerInfoList();
        for (ImportSingerInfoBean bean : infos) {
            Result<String> result = tryImportSinger(appId, bean, operator, now);
            if(RpcResult.isFail(result)) {
                log.error("tryImportSinger fail.bean={}, msg={}", bean, result.getMessage());
                failImportSingerIdList.add(SingerInfoConvert.INSTANCE.toImportSingerResultBean(bean, result.getMessage()));
                continue;
            }
            singerChatManager.sendAuditResultChat(appId, bean.getSingerId(), bean.getSingerType().getType(), bean.getSongStyle(), SingerChatSceneEnum.SINGER_AUDIT_PASS);
            log.info("tryImportSinger success.bean={}", bean);
        }
        return RpcResult.success(new ResponseBatchImportSinger().setFailImportSingerIdList(failImportSingerIdList));
    }

    /**
     * 尝试导入歌手
     */
    public Result<String> tryImportSinger(int appId, ImportSingerInfoBean bean, String operator, Date importTime) {
        int singerType = bean.getSingerType().getType();
        Long singerId = bean.getSingerId();

        ISingerProcessor processor = processorFactory.getProcessor(ISingerProcessor.class);
        boolean invalidSingerType = processor.isInvalidSingerType(bean.getSingerType().getType(), BusinessEvnEnum.from(appId));
        if(invalidSingerType){
            log.error("tryImportSinger fail;歌手类型错误;bean={}", bean);
            return RpcResult.fail(1, "不支持歌手类型");
        }
        Optional<UserVerifyResultDTO> verifyResultDTO = singerImportManager.getVerifyResult(bean.getSingerId(), appId);
        if (!verifyResultDTO.isPresent()) {
            log.warn("tryImportSinger fail;user is not verify;bean={}", bean);
            return RpcResult.fail(1, "未实名");
        }
        UserVerifyResultDTO verifyResult = verifyResultDTO.get();
        try (RedisLock lock = singerRedisManager.tryGetSingerVerifyApplyLock(appId, verifyResult.getIdCardNumber())) {
            if (!lock.tryLock()) {
                return RpcResult.fail(1, "导入频繁,重新尝试");
            }
            //校验是否存在审核中的记录
            boolean canApply = singerVerifyApplyManager.checkAuditEffectiveRecordCanApply(appId, singerId, singerType);
            if (!canApply) {
                log.warn("tryImportSinger fail;exist auditing record;bean={}", bean);
                return RpcResult.fail(1, "存在待审核的歌手认证");
            }
            // 校验用户的实名信息关联账号是否存在歌手库信息或者认证信息
            Boolean existUserVerify = singerVerifyApplyManager.checkUserVerify(appId, verifyResult.getIdCardNumber(), singerId, singerType);
            if (existUserVerify) {
                log.warn("tryImportSinger fail;singerVerifyApply exist user verify;bean={}", bean);
                return RpcResult.fail(1, "已存在同个实名身份的歌手");
            }
            List<SingerInfo> userId = singerImportManager.getSingerInfoByUserId(appId, bean.getSingerId(), bean.getSingerType().getType(), Lists.newArrayList(SingerStatusEnum.AUTHENTICATING, SingerStatusEnum.EFFECTIVE));
            if (!userId.isEmpty()) {
                log.warn("tryImportSinger fail;exist singer info;bean={}", bean);
                return RpcResult.fail(1, "已存在歌手信息");
            }
            boolean success = singerImportManager.importSinger(appId, operator, bean, importTime);
            if(!success) {
                log.warn("tryImportSinger fail;导入歌手信息失败;bean={}", bean);
                return RpcResult.fail(1, "导入失败");
            }
            //回收装扮
            //如果通过的是高级歌手，部分业务需要回收认证歌手的奖励
            if (singerType != SingerTypeEnum.NEW.getType()) {
                ISingerDecorateProcessor decorateProcessor = factory.getProcessor(ISingerDecorateProcessor.class);
                decorateProcessor.recoverSingerAward(appId, bean.getSingerId(), SingerTypeEnum.NEW.getType(), operator);
                log.info("tryImportSinger;recover singer award operate, singerId:{}, singerType:{}", bean.getSingerId(), singerType);
            }
            return RpcResult.success();
        } catch (Exception e) {
            log.error("tryImportSinger fail;导入异常;appId={};bean={};operator={};importTime={}", appId, bean, operator, importTime, e);
            return RpcResult.fail(1, "导入异常");
        }
    }

}
