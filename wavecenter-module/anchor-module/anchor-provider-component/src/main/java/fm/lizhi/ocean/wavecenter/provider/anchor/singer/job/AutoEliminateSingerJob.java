package fm.lizhi.ocean.wavecenter.provider.anchor.singer.job;

import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.common.job.dispatcher.executor.logger.ExecutorLogger;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.manager.EliminateSingerManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 自动淘汰歌手定时任务
 *
 * <AUTHOR>
 */
@Component
public class AutoEliminateSingerJob implements JobHandler {

    @Autowired
    private EliminateSingerManager eliminateSingerManager;

    @Override
    public void execute(JobExecuteContext jobExecuteContext) throws Exception {
        for (BusinessEvnEnum evnEnum : BusinessEvnEnum.values()) {
            if (evnEnum.getOnline() != 1) {
                continue;
            }
            try {
                ExecutorLogger.getLogger().append("EliminateSingerJob start, appId:{}", evnEnum.getAppId());
                ContextUtils.setBusinessEvnEnum(evnEnum);

                // 处理解约主播
                try {
                    eliminateSingerManager.autoHandelUnSignPlayers(evnEnum);
                } catch (Exception e) {
                    ExecutorLogger.getLogger().append("handelUnSignPlayers error, appId:{}", evnEnum.getAppId(), e);
                }

                // 处理无营收主播
                try {
                    eliminateSingerManager.autoHandelNoRevenuePlayers(evnEnum);
                } catch (Exception e) {
                    ExecutorLogger.getLogger().append("handelNoRevenuePlayers error, appId:{}", evnEnum.getAppId(), e);
                }

            } finally {
                // 无论是否发生异常，最终执行清理
                ExecutorLogger.getLogger().append("EliminateSingerJob end, appId:{}", evnEnum.getAppId());
                ContextUtils.clearContext();
            }
        }

    }
}
