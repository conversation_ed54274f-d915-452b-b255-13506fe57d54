package fm.lizhi.ocean.wavecenter.module.api.anchor.singer.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 歌手菜单可见规则枚举
 */
@AllArgsConstructor
@Getter
public enum SingerVisibleConditionEnum {
    /**
     * 权限(1)：签约厅 = 点唱厅 or 待审核点唱厅
     */
    CONTRACT_SIGN_VOCAL_ROOM("CONTRACT_SIGN_VOCAL_ROOM", "签约厅=点唱厅 or 待审点唱厅"),
    /**
     * 权限(2)：签约主播【周流水】
     */
    CONTRACT_ANCHOR_WEEKLY_FLOW("CONTRACT_ANCHOR_WEEKLY_FLOW", "签约主播【周流水】"),
    /**
     * 权限(3)：签约主播 签约厅【厅周流水】
     */
    CONTRACT_ROOM_WEEKLY_FLOW("CONTRACT_ROOM_WEEKLY_FLOW", "签约主播 签约厅【厅周流水】"),
    /**
     * 权限(4)：所有签约主播可见
     */
    ALL_CONTRACT_ANCHOR_VISIBLE("ALL_CONTRACT_ANCHOR_VISIBLE", "所有签约主播可见");

    private final String value;
    private final String desc;


    public static SingerVisibleConditionEnum getByCode(String code) {
        for (SingerVisibleConditionEnum value : values()) {
            if (value.value.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
