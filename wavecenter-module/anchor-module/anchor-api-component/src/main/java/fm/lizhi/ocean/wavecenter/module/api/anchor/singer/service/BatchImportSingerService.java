package fm.lizhi.ocean.wavecenter.module.api.anchor.singer.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.request.RequestBatchImportSinger;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.response.ResponseBatchImportSinger;

public interface BatchImportSingerService {

    /**
     * 批量导入歌手

     * @param request
     * @return
     */
    Result<ResponseBatchImportSinger> batchImportSinger(RequestBatchImportSinger request);
}
