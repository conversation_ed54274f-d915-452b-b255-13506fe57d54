package fm.lizhi.ocean.wavecenter.module.api.anchor.singer.response;

import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.bean.ImportSingerInfoBean;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.bean.ImportSingerInfoResultBean;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class ResponseBatchImportSinger {

    private List<ImportSingerInfoResultBean> failImportSingerIdList;
}
