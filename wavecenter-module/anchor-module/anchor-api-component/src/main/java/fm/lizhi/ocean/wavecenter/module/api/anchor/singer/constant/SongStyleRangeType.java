package fm.lizhi.ocean.wavecenter.module.api.anchor.singer.constant;


import com.alibaba.dubbo.common.utils.CollectionUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

@AllArgsConstructor
@Getter
public enum SongStyleRangeType {
    //0:无需曲风校验 1:固定曲风，2：任一曲风，3：全能曲风
    NONE(0, "无需曲风校验"),
    FIXED(1,  "固定曲风"),
    ALL(2, "任一曲风"),
    VERSATILE(3, "全能曲风"),
    ;
    private final Integer value;
    private final String desc;

    public static SongStyleRangeType get(int value) {
        for (SongStyleRangeType item : values()) {
            if (item.value == value) {
                return item;
            }
        }
        return null;
    }

    public static boolean isVersatile(List<String> playerSongStyleList) {
        return CollectionUtils.isNotEmpty(playerSongStyleList) && playerSongStyleList.size() == 3;
    }
}
