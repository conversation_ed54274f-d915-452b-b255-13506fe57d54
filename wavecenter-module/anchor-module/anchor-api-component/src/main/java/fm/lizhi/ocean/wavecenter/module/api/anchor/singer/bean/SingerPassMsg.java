package fm.lizhi.ocean.wavecenter.module.api.anchor.singer.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 歌手通过信息消息结构
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SingerPassMsg {

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 歌手ID列表
     */
    private List<Long> singerIds;

    /**
     * 歌手类型
     */
    private Integer singerType;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 原因
     */
    private String reason;

    /**
     * 曲风
     */
    private String songStyle;

    /**
     * 事务ID
     */
    private String transactionId;
}
