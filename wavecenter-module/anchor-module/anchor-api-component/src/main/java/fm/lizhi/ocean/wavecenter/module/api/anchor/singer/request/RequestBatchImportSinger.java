package fm.lizhi.ocean.wavecenter.module.api.anchor.singer.request;


import fm.lizhi.ocean.wave.server.common.request.RequestAppIdAware;
import fm.lizhi.ocean.wave.server.common.validator.AppEnumId;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.bean.ImportSingerInfoBean;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ToString
public class RequestBatchImportSinger  implements RequestAppIdAware {

    private List<ImportSingerInfoBean> singerInfoList;

    private String operator;

    @NotNull(message = "appId不能为空")
    @AppEnumId
    private Integer appId;

    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}
