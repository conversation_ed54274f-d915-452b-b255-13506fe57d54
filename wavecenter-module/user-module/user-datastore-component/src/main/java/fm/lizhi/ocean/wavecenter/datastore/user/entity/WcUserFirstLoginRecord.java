package fm.lizhi.ocean.wavecenter.datastore.user.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 *
 * @date 2025-06-11 03:11:19
 */
@Table(name = "`wavecenter_user_first_login_record`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcUserFirstLoginRecord {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    @Column(name= "`app_id`")
    private Integer appId;

    @Column(name= "`user_id`")
    private Long userId;

    /**
     * 环境
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", userId=").append(userId);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", createTime=").append(createTime);
        sb.append("]");
        return sb.toString();
    }
}