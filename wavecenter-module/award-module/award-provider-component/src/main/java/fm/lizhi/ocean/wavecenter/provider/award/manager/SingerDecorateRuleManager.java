package fm.lizhi.ocean.wavecenter.provider.award.manager;

import com.google.common.collect.Lists;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.ocean.wavecenter.api.award.singer.bean.SingerDecorateRuleBean;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.entity.SingerDecorateCondition;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.entity.SingerDecorateRule;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.mapper.SingerDecorateConditionMapper;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.mapper.SingerDecorateRuleMapper;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.constant.SongStyleRangeType;
import fm.lizhi.ocean.wavecenter.module.api.award.singer.constants.SingerDecorateConditionTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class SingerDecorateRuleManager {

    @Autowired
    private SingerDecorateRuleMapper singerDecorateRuleMapper;

    @Autowired
    private SingerDecorateConditionMapper singerDecorateConditionMapper;

    @Autowired
    private SingerDecorateRuleManager self;

    public void allResetDecorateRule() throws Exception {
        SingerDecorateRule rule = new SingerDecorateRule();
        rule.setDeployEnv(ConfigUtils.getEnvRequired().name());
        List<SingerDecorateRule> singerDecorateRules = singerDecorateRuleMapper.selectMany(rule);
        for (SingerDecorateRule singerDecorateRule : singerDecorateRules) {
            SingerDecorateCondition condition = new SingerDecorateCondition();
            condition.setDecorateRuleId(singerDecorateRule.getId());
            List<SingerDecorateCondition> conditions = singerDecorateConditionMapper.selectMany(condition);
            log.info("exist condition singerDecorateRuleId:{};conditions={}", singerDecorateRule.getId(), JsonUtils.toJsonString(conditions));
            if(CollectionUtils.isNotEmpty(conditions)) {
                continue;
            }
            //洗数据
            self.resetDecorateRule(singerDecorateRule);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void resetDecorateRule(SingerDecorateRule singerDecorateRule) {
        SingerDecorateCondition songStyleCondition = ready(singerDecorateRule);
        songStyleCondition.setConditionType(SingerDecorateConditionTypeEnum.MUSIC_STYLE.getValue());

        Boolean originalSinger = "原创".equals(singerDecorateRule.getSongStyle());
        List<String> songStyle = Collections.emptyList();
        SongStyleRangeType songStyleRangeType;
        if(originalSinger) {
            songStyleRangeType = SongStyleRangeType.NONE;
        } else if("ALL".equals(singerDecorateRule.getSongStyle())) {
            songStyleRangeType = SongStyleRangeType.ALL;
        } else {
            songStyleRangeType = SongStyleRangeType.FIXED;
            songStyle = Lists.newArrayList(singerDecorateRule.getSongStyle().trim());
        }
        //原创条件
        SingerDecorateCondition originalCondition = ready(singerDecorateRule);
        originalCondition.setConditionType(SingerDecorateConditionTypeEnum.ORIGINAL_SINGER.getValue());
        originalCondition.setConditionJson(originalSinger.toString());

        //曲风条件
        songStyleCondition.setConditionType(SingerDecorateConditionTypeEnum.MUSIC_STYLE.getValue());
        songStyleCondition.setSongStyleType(songStyleRangeType.getValue());
        songStyleCondition.setConditionJson(JsonUtils.toJsonString(songStyle));

        int insert = singerDecorateConditionMapper.insert(originalCondition);
        if(insert != 1) {
            throw new RuntimeException("insert originalCondition error;ruleId=" + singerDecorateRule.getId());
        }
        int insert1 = singerDecorateConditionMapper.insert(songStyleCondition);
        if(insert1 != 1) {
            throw new RuntimeException("insert songStyleCondition error;ruleId=" + singerDecorateRule.getId());
        }

        String index = SingerDecorateConditionTypeEnum.ORIGINAL_SINGER.buildConditionIndex(originalSinger);
        String songStyleIndex = SingerDecorateConditionTypeEnum.MUSIC_STYLE.buildConditionIndex(songStyleRangeType, songStyle);

        SingerDecorateRule updateIndex = new SingerDecorateRule();
        updateIndex.setId(singerDecorateRule.getId());
        updateIndex.setCombineConditionIndex(SingerDecorateConditionTypeEnum.combineMultipleCondition(songStyleIndex, index));
        int i = singerDecorateRuleMapper.updateByPrimaryKey(updateIndex);
        if(i != 1) {
            throw new RuntimeException("update singerDecorateRule error;ruleId=" + singerDecorateRule.getId());
        }

    }


    private SingerDecorateCondition ready(SingerDecorateRule singerDecorateRule) {
        SingerDecorateCondition condition = new SingerDecorateCondition();
        condition.setDecorateRuleId(singerDecorateRule.getId());
        condition.setAppId(singerDecorateRule.getAppId());
        condition.setCreateTime(new Date());
        condition.setModifyTime(new Date());
        return condition;
    }

    public SingerDecorateRule getSingerDecorateByRuleId(Long singerDecorateRuleId) {
        if(singerDecorateRuleId == null) {
            return null;
        }
        SingerDecorateRule rule = new SingerDecorateRule();
        rule.setId(singerDecorateRuleId);
        return singerDecorateRuleMapper.selectByPrimaryKey(rule);
    }
}
