package fm.lizhi.ocean.wavecenter.provider.award.job;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateFlowOperateEnum;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.common.utils.RedisLock;
import fm.lizhi.ocean.wavecenter.module.api.award.singer.constants.SingerDecorateOperateReasonConstant;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.PageSingerInfoParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerInfoDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerInfoManager;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerRedisManager;
import fm.lizhi.ocean.wavecenter.service.award.singer.config.SingerAwardConfig;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowDTO;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowGenerateDTO;
import fm.lizhi.ocean.wavecenter.service.award.singer.dto.SingerDecorateFlowInitParamDTO;
import fm.lizhi.ocean.wavecenter.service.award.singer.manager.SingerDecorateFlowManager;
import fm.lizhi.ocean.wavecenter.service.award.singer.manager.SingerDecorateManager;
import fm.lizhi.ocean.wavecenter.service.user.constants.SearchType;
import fm.lizhi.ocean.wavecenter.service.user.constants.VerifyStatusConstant;
import fm.lizhi.ocean.wavecenter.service.user.dto.SearchUserVerifyResultDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.SearchUserVerifyResultParamDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserVerifyResultDTO;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserVerifyManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * PP补偿待发放的歌手认证装饰 一次性job
 */
@Component
@Slf4j
public class PpSingerCompensateDecorateJob implements JobHandler {

    @Autowired
    private SingerInfoManager singerInfoManager;
    @Autowired
    private SingerAwardConfig singerAwardConfig;
    @Autowired
    private SingerDecorateFlowManager singerDecorateFlowManager;
    @Autowired
    private SingerDecorateManager singerDecorateManager;
    @Autowired
    private UserVerifyManager userVerifyManager;
    @Autowired
    private SingerRedisManager singerRedisManager;

    @Override
    public void execute(JobExecuteContext context) throws Exception {
        log.info("PpSingerCompensateDecorateJob start");
        int i = 1;
        int appId = BusinessEvnEnum.PP.getAppId();
        ContextUtils.reSetBusinessEvnEnum(BusinessEvnEnum.PP);
        PageBean<SingerInfoDTO> pageBeans = pageList(appId, i);
        AtomicInteger count = new AtomicInteger(0);
        while(pageBeans != null && CollectionUtils.isNotEmpty(pageBeans.getList())) {
            for (SingerInfoDTO singer : pageBeans.getList()) {
                if(!singerAwardConfig.isCompensateSingerDecorateEnable()) {
                    log.info("PpSingerCompensateDecorateJob interrupt;appId:{}, pageNo:{}", appId, i);
                    break;
                }
                //如果存在高级歌手，那初级歌手就不用发装扮
                if(appId != BusinessEvnEnum.XIMI.getAppId() && singer.getSingerType() == SingerTypeEnum.NEW.getType()) {
                    List<SingerInfoDTO> singerInfoByUserId = singerInfoManager.getSingerInfoByUserId(appId, singer.getUserId(), SingerStatusEnum.EFFECTIVE);
                    Optional<SingerInfoDTO> any = singerInfoByUserId.stream().filter(singerInfoDTO -> singerInfoDTO.getSingerType() > SingerTypeEnum.NEW.getType()).findAny();
                    if (any.isPresent()) {
                        log.info("PpSingerCompensateDecorateJob;已存在高级歌手,认证歌手不发装扮, appId: {}, singerType: {}, userId:{}", appId, singer.getSingerType(), singer.getUserId());
                        continue;
                    }
                }

                List<SingerDecorateFlowDTO> existFlowList = singerDecorateFlowManager.getDecorateFlowByUserIdAndSingerType(
                        singer.getUserId(), singer.getSingerType(), SingerDecorateFlowOperateEnum.GRANT);
                // 只判断是否存在发放流水
                if (!singerAwardConfig.isCanRepeatCompensation() && CollUtil.isNotEmpty(existFlowList)) {
                    log.info("PpSingerCompensateDecorateJob;已存在的装饰流水, appId: {}, singerType: {}, userId:{}", appId, singer.getSingerType(), singer.getUserId());
                    continue;
                }
                if(doCompensateWithLock(singer)) {
                    log.info("PpSingerCompensateDecorateJob;compensate singer decorate success;appId:{}, singerPrimaryKey:{}", appId, singer.getId());
                    count.incrementAndGet();
                    sleep(20);
                } else {
                    log.error("PpSingerCompensateDecorateJob;compensate singer decorate fail;appId:{}, singer:{}", appId, singer);
                }
            }
            pageBeans = pageList(appId, ++i);
        }
        log.info("PpSingerCompensateDecorateJob end;appId:{}, count:{}", appId, count.get());
    }

    private void sleep(int millSeconds) {
        try {
            Thread.sleep(millSeconds);
        } catch (InterruptedException e) {
            log.error("PpSingerCompensateDecorateJob;sleep error;millSeconds:{}", millSeconds, e);
        }
    }

    private boolean doCompensateWithLock(SingerInfoDTO singer) {
        Integer appId = singer.getAppId();
        Optional<UserVerifyResultDTO> verifyOptional = getVerifyResult(singer.getUserId(), appId);
        if (!verifyOptional.isPresent()) {
            log.error("doCompensateWithLock;singerVerifyApply.userId={} is not verify", singer.getUserId());
            return false;
        }
        try (RedisLock lock = singerRedisManager.tryGetSingerVerifyApplyLock(appId, verifyOptional.get().getIdCardNumber())) {
            if (!lock.tryLock()) {
                log.error("doCompensateWithLock.userId={} is in lock", singer.getUserId());
                return false;
            }
            // 构建发放装扮流水
            Optional<SingerDecorateFlowGenerateDTO> optional = singerDecorateManager.generateAndInsertSingerDecorateFlowAndSendEvent(new SingerDecorateFlowInitParamDTO()
                    .setAppId(appId)
                    .setReason(SingerDecorateOperateReasonConstant.COMPENSATION)
                    .setOperator("系统")
                    .setSingerInfoList(Lists.newArrayList(singer))
                    .setOperateType(SingerDecorateFlowOperateEnum.GRANT)
                    .setTransactionId(null)
            );
            optional.ifPresent(flow -> {
                log.info("doCompensateWithLock success;transactionId={};flow={}", flow.getTransactionId(), JsonUtils.toJsonString(flow.getDecorateFlowList()));
            });
            return optional.isPresent() && CollectionUtils.isNotEmpty(optional.get().getDecorateFlowList());
        } catch (Exception e) {
            log.error("doCompensateWithLock error;appId:{}, singer:{}", appId, singer, e);
            return false;
        }



    }

    private PageBean<SingerInfoDTO> pageList(int appId, int pageNo) {
        PageSingerInfoParamDTO paramDTO = new PageSingerInfoParamDTO();
        paramDTO.setAppId(appId);
        paramDTO.setSingerStatus(Collections.singletonList(SingerStatusEnum.EFFECTIVE));
        paramDTO.setPageNo(pageNo);
        paramDTO.setPageSize(100);
        paramDTO.setOrderMetrics("createTime");
        return singerInfoManager.pageSingerInfo(paramDTO);
    }


    /**
     * 获取用户的实名认证结果
     *
     * @param userId 用户ID
     * @param appId  应用ID
     * @return 实名认证结果
     */
    public Optional<UserVerifyResultDTO> getVerifyResult(Long userId, Integer appId) {
        // 先查询这个用户的认证结果
        SearchUserVerifyResultParamDTO param = new SearchUserVerifyResultParamDTO()
                .setUserId(userId).setAppId(appId)
                .setAppId(appId)
                .setSearchType(SearchType.RESULT.getValue())
                .setVerifyStatus(VerifyStatusConstant.VERIFY_PASS);
        Result<SearchUserVerifyResultDTO> result = userVerifyManager.searchUserVerifyResult(param);
        if (RpcResult.noBusinessData(result) || CollectionUtils.isEmpty(result.target().getUserVerifyResultList())) {
            // 如果查询实名认证结果失败，则直接返回false
            log.warn("searchUserVerifyResult failed, rCode: {}, req: {}", result.rCode(), JsonUtil.dumps(param));
            return Optional.empty();
        }

        // 一般一个用户只有一条认证 通过的
        return Optional.of(result.target().getUserVerifyResultList().get(0));
    }



}
