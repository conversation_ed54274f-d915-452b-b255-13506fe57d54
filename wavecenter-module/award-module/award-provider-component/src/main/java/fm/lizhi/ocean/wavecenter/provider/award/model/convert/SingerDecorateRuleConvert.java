package fm.lizhi.ocean.wavecenter.provider.award.model.convert;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.api.award.singer.bean.SingerDecorateRuleBean;
import fm.lizhi.ocean.wavecenter.api.award.singer.constants.SingerDecorateOperateStatusEnum;
import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import fm.lizhi.ocean.wavecenter.base.convert.CommonConvert;
import fm.lizhi.ocean.wavecenter.datastore.award.singer.entity.SingerDecorateRule;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR,
        imports = {
                ConfigUtils.class, Date.class, SingerDecorateOperateStatusEnum.class, PlatformDecorateTypeEnum.class
        },
        uses = {CommonConvert.class}
)
public interface SingerDecorateRuleConvert {

    SingerDecorateRuleConvert I = Mappers.getMapper(SingerDecorateRuleConvert.class);

    SingerDecorateRuleBean toSingerDecorateRuleBean(SingerDecorateRule rule);

}
