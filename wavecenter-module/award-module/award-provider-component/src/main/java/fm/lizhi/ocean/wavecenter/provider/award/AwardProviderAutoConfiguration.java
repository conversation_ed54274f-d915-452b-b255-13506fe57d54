package fm.lizhi.ocean.wavecenter.provider.award;

import fm.lizhi.common.kafka.ioc.spring.EnableKafkaClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

@Configuration
@ComponentScan(basePackages = "fm.lizhi.ocean.wavecenter.provider.award")
@EnableKafkaClients(basePackages = "fm.lizhi.ocean.wavecenter.provider.award.singer.kafka")
public class AwardProviderAutoConfiguration {
}