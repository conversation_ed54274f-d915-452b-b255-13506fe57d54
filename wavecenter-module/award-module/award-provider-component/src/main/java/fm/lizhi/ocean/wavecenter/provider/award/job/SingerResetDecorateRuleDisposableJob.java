package fm.lizhi.ocean.wavecenter.provider.award.job;

import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.ocean.wavecenter.provider.award.manager.SingerDecorateRuleManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class SingerResetDecorateRuleDisposableJob  implements JobHandler {

    @Autowired
    private SingerDecorateRuleManager singerDecorateRuleManager;

    @Override
    public void execute(JobExecuteContext context) throws Exception {
        log.info("SingerResetDecorateRuleDisposableJob execute start");
        singerDecorateRuleManager.allResetDecorateRule();
        log.info("SingerResetDecorateRuleDisposableJob execute end");
    }
}
