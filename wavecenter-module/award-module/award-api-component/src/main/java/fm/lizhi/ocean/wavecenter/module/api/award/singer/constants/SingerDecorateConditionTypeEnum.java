package fm.lizhi.ocean.wavecenter.module.api.award.singer.constants;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.google.common.collect.Sets;
import fm.lizhi.ocean.wavecenter.module.api.anchor.singer.constant.SongStyleRangeType;
import fm.lizhi.ocean.wavecenter.module.api.award.singer.bean.SingerDecorateConditionIndexBean;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 歌手装扮条件类型枚举
 */
@AllArgsConstructor
@Getter
public enum SingerDecorateConditionTypeEnum {

    MUSIC_STYLE(1,"曲风条件"),
    ORIGINAL_SINGER(2,"是否校验原唱")
    ;


    private final Integer value;
    private final String desc;


    public static SingerDecorateConditionTypeEnum getByCode(int code) {
        for (SingerDecorateConditionTypeEnum value : values()) {
            if (value.value == code) {
                return value;
            }
        }
        return null;
    }


    public static Set<String> combineMultipleConditions(List<String> songStyleConditions, List<String> originalConditions) {
        Set<String> combineConditionIndex = Sets.newHashSetWithExpectedSize(songStyleConditions.size());
        for (String condition : songStyleConditions) {
            for (String originalCondition : originalConditions) {
                combineConditionIndex.add(combineMultipleCondition(condition, originalCondition));
            }
        }
        return combineConditionIndex;
    }

    public static String combineMultipleCondition(String songStyleCondition, String originalCondition) {
        return songStyleCondition + SingerDecorateRuleConstant.CONDITION_COMBINE_SEPARATOR + originalCondition;
    }

    public static String combineMultipleConditions(List<SingerDecorateConditionIndexBean> originalConditions) {
        return originalConditions.stream()
                .sorted(Comparator.comparingInt(condition -> condition.getConditionType().getValue()))
                .map(SingerDecorateConditionIndexBean::getValue)
                .collect(Collectors.joining(SingerDecorateRuleConstant.CONDITION_COMBINE_SEPARATOR));
    }



    public String buildConditionIndex(Boolean originalSinger) {
        return this.value + SingerDecorateRuleConstant.INDEX_SEPARATOR + originalSinger;
    }

    public String buildConditionIndex(SongStyleRangeType songStyleRangeType, List<String> songStyles) {
        if(CollectionUtils.isEmpty(songStyles)) {
            return this.value + SingerDecorateRuleConstant.INDEX_SEPARATOR + songStyleRangeType.getValue();
        }
        String songStyleStr = String.join(SingerDecorateRuleConstant.INDEX_SEPARATOR, songStyles);
        return this.value + SingerDecorateRuleConstant.INDEX_SEPARATOR + songStyleRangeType.getValue() + SingerDecorateRuleConstant.INDEX_SEPARATOR + songStyleStr;
    }
}
