package fm.lizhi.ocean.wavecenter.module.api.award.singer.bean;

import fm.lizhi.ocean.wavecenter.module.api.award.singer.constants.SingerDecorateConditionTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SingerDecorateConditionIndexBean {

    private SingerDecorateConditionTypeEnum conditionType;

    private String value;
}
