package fm.lizhi.ocean.wavecenter.module.api.award.singer.constants;

/**
 * 歌手装扮 操作原因常量
 * <AUTHOR>
 */
public interface SingerDecorateOperateReasonConstant {


    // region 发放常量 ==========
    String AUTH_PASS = "认证通过";
    String HALL_AUTH_PASS = "厅认证通过";
    String UPGRADE = "晋升";
    String MANUAL_GRANT = "人工发放";

    String COMPENSATION = "系统补发";


    // endregion

    // region 回收常量 ==========

    String SINGER_UNSIGNED = "解约回收";
    String HALL_QUALIFICATION_CANCEL = "厅资格取消";
    String SINGER_ELIMINATED = "歌手淘汰";
    String HALL_CATEGORY_CANCEL = "厅分类取消";
    String MANUAL_RECOVER = "人工回收";
    String SINGER_TYPE_CHANGE = "歌手等级变化";

    // endregion


}
