package fm.lizhi.ocean.wavecenter.provider.sign;

import fm.lizhi.common.dubbo.adapter.springboot.EnableServiceProvider;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/7/11 14:25
 */
@Configuration
@ComponentScan(basePackages = "fm.lizhi.ocean.wavecenter.provider.sign")
public class WavecenterProviderSignAutoConfiguration {
} 