package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.service;

import cn.hutool.core.util.StrUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestPlayerHandleAgreement;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestSubmitAgreement;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service.OfflineZoneProtectionService;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.convert.OfflineZoneProtectionConvert;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.OfflineZoneProtectionAgreementValidityDTO;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.manager.OfflineZoneProtectionManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.Optional;

/**
 * 线下专区主播跳槽保护协议服务实现
 * <AUTHOR>
 */
@ServiceProvider
@Slf4j
public class OfflineZoneProtectionServiceImpl implements OfflineZoneProtectionService {

    @Autowired
    private OfflineZoneProtectionManager offlineZoneProtectionManager;


    @Override
    public Result<Void> submitAgreement(RequestSubmitAgreement request) {

        if (StrUtil.isEmpty(request.getAgreementFileJson())){
            return new Result<>(SUBMIT_AGREEMENT_PARAM_ERROR,  null);
        }

        // 校验是否在时效内
        Date validDate =
                offlineZoneProtectionManager.getProtectionValidityDate(request.getAppId(), request.getFamilyId(), request.getNjId(), request.getPlayerId());
        if (validDate == null || validDate.before(new Date())){
            log.info("协议已过期, familyId={}, njId={}, playerId={}, expiredTime={}",
                    request.getFamilyId(), request.getNjId(), request.getPlayerId(), validDate);
            return new Result<>(SUBMIT_AGREEMENT_EXPIRED,  null);
        }

        if (request.getId() == null){
            return offlineZoneProtectionManager.createProtection(request);
        }else {
            return offlineZoneProtectionManager.updateProtection(request);
        }
    }

    @Override
    public Result<Void> playerHandleAgreement(RequestPlayerHandleAgreement request) {
        return offlineZoneProtectionManager.playerHandleProtection(request);
    }
}
