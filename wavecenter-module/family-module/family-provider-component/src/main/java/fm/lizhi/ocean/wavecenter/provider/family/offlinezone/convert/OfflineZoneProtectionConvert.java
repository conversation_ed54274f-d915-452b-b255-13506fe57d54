package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.convert;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneProtection;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneProtectionAgreementValidity;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneProtectionHistory;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestSubmitAgreement;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.OfflineZoneProtectionAgreementValidityDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

/**
 * 线下专区主播跳槽保护协议转换器
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        imports = {
                ConfigUtils.class
        }
)
public interface OfflineZoneProtectionConvert {

    OfflineZoneProtectionConvert INSTANCE = Mappers.getMapper(OfflineZoneProtectionConvert.class);



    @Mapping(target = "playerAgree", expression = "java(fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants.ProtectionAgreeStatusEnums.NOT_PROCESSED.getCode())")
    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "archived", expression = "java(false)")
    @Mapping(target = "agreementUpdateTime", expression = "java(new java.util.Date())")
    @Mapping(target = "createTime", expression = "java(new java.util.Date())")
    @Mapping(target = "modifyTime", expression = "java(new java.util.Date())")
    @Mapping(target = "operator", source = "request.uploadUserId")
    @Mapping(target = "id", source = "id")
    OfflineZoneProtection buildProtection(RequestSubmitAgreement request, Long id);


    @Mapping(target = "createTime", expression = "java(new java.util.Date())")
    @Mapping(target = "modifyTime", expression = "java(new java.util.Date())")
    @Mapping(target = "operator", source = "request.uploadUserId")
    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "agreementUpdateTime", expression = "java(new java.util.Date())")
    @Mapping(target = "id", ignore = true)
     OfflineZoneProtectionHistory buildProtectionHistory(OfflineZoneProtection request, Long protectedId);

    OfflineZoneProtectionAgreementValidityDTO convertOfflineZoneProtectionAgreementValidityDto(OfflineZoneProtectionAgreementValidity validity);

    @Mapping(target = "agreementStartTime", source = "request.agreementStartTime")
    @Mapping(target = "agreementEndTime", source = "request.agreementEndTime")
    @Mapping(target = "uploadUserId", source = "request.uploadUserId")
    @Mapping(target = "agreementFileJson", source = "request.agreementFileJson")
    @Mapping(target = "agreementUpdateTime", expression = "java(new java.util.Date())")
    @Mapping(target = "modifyTime", expression = "java(new java.util.Date())")
    @Mapping(target = "operator", source = "request.uploadUserId")
    @Mapping(target = "stampSign", source = "request.stampSign")
    @Mapping(target = "id", source = "protection.id")
    @Mapping(target = "appId", source = "protection.appId")
    @Mapping(target = "familyId", source = "protection.familyId")
    @Mapping(target = "njId", source = "protection.njId")
    @Mapping(target = "playerId", source = "protection.playerId")
    OfflineZoneProtection buildUpdateProtection(OfflineZoneProtection protection, RequestSubmitAgreement request);
}

