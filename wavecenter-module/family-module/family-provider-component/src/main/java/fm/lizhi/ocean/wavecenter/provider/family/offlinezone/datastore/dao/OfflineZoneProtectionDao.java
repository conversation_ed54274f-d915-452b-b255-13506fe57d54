package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao;

import com.alibaba.csp.sentinel.util.AssertUtil;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.*;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.mapper.OfflineZoneProtectionAgreementValidityMapper;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.mapper.OfflineZoneProtectionHistoryMapper;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.mapper.OfflineZoneProtectionMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 线下专区-主播跳槽保护协议 Dao
 * <AUTHOR>
 */
@Repository
@Slf4j
public class OfflineZoneProtectionDao {

    @Autowired
    private OfflineZoneProtectionMapper offlineZoneProtectionMapper;

    @Autowired
    private OfflineZoneProtectionHistoryMapper offlineZoneProtectionHistoryMapper;

    @Autowired
    private OfflineZoneProtectionAgreementValidityMapper offlineZoneProtectionAgreementValidityMapper;

    /**
     * 批量查询主播的保护协议映射
     *
     * @param appId     应用ID
     * @param playerIds 主播ID集合
     * @return 主播ID -> 保护协议的映射
     */
    public Map<Long, OfflineZoneProtection> getProtectionMapByPlayerIds(Integer appId, Long familyId, Set<Long> playerIds) {
        if (CollectionUtils.isEmpty(playerIds)) {
            return Collections.emptyMap();
        }

        OfflineZoneProtectionExample example = new OfflineZoneProtectionExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andFamilyIdEqualTo(familyId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andPlayerIdIn(new ArrayList<>(playerIds));
        example.setOrderByClause("agreement_update_time DESC");

        List<OfflineZoneProtection> list = offlineZoneProtectionMapper.selectByExample(example);

        // 按主播ID分组，取最新的协议
        return list.stream()
                .collect(Collectors.toMap(
                        OfflineZoneProtection::getPlayerId,
                        protection -> protection,
                        (existing, replacement) -> {
                            // 如果有多个协议，取创建时间最新的
                            if (replacement.getCreateTime() != null && existing.getCreateTime() != null) {
                                return replacement.getAgreementUpdateTime().after(existing.getAgreementUpdateTime()) 
                                        ? replacement : existing;
                            }
                            return replacement;
                        }
                ));
    }

    /**
     * 获取协议
     */
    public OfflineZoneProtection getProtectionById(Long id) {

        OfflineZoneProtection param = new OfflineZoneProtection();
        param.setId(id);

        return offlineZoneProtectionMapper.selectOne(param);
    }


    /**
     * 查询主播的保护协议是否存在
     */
    public Boolean existProtection(int appId, Long familyId,  Long njId, Long playerId) {
        OfflineZoneProtectionExample example = new OfflineZoneProtectionExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andFamilyIdEqualTo(familyId)
                .andNjIdEqualTo(njId)
                .andPlayerIdEqualTo(playerId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
        ;

        return offlineZoneProtectionMapper.countByExample(example) > 0;
    }

    /**
     * 保存协议和历史记录
     */
    @Transactional
    public void saveProtection(OfflineZoneProtection protection, OfflineZoneProtectionHistory protectionHistory) {

        boolean success = offlineZoneProtectionMapper.insert(protection) > 0;
        AssertUtil.assertState(success, "保存协议失败");

        boolean historySuccess = offlineZoneProtectionHistoryMapper.insert(protectionHistory) > 0;
        AssertUtil.assertState(historySuccess, "保存协议历史失败");
    }

    @Transactional
    public void updateProtection(OfflineZoneProtection protection, OfflineZoneProtectionHistory protectionHistory) {
        boolean success = offlineZoneProtectionMapper.updateByPrimaryKey(protection) > 0;
        AssertUtil.assertState(success, "更新协议失败");
        boolean historySuccess = offlineZoneProtectionHistoryMapper.insert(protectionHistory) > 0;
        AssertUtil.assertState(historySuccess, "保存协议历史失败");

    }

    /**
     * 查询是否在时效期内
     */
    public OfflineZoneProtectionAgreementValidity getProtectionAgreementValidity(int appId, Long familyId, Long njId, Long playerId) {

        OfflineZoneProtectionAgreementValidity param = new OfflineZoneProtectionAgreementValidity();
        param.setAppId(appId);
        param.setFamilyId(familyId);
        param.setNjId(njId);
        param.setPlayerId(playerId);
        param.setDeployEnv(ConfigUtils.getEnvRequired().name());


        return offlineZoneProtectionAgreementValidityMapper.selectOne(param);
    }

    /**
     * 更新协议的主播同意状态
     */
    @Transactional
    public void updateProtectionPlayerAgree(Long protectionId, Integer playerAgree) {
        OfflineZoneProtection protection = new OfflineZoneProtection();
        protection.setId(protectionId);
        protection.setPlayerAgree(playerAgree);
        protection.setArchived(true);
        protection.setModifyTime(new Date());

        int updateCount = offlineZoneProtectionMapper.updateByPrimaryKey(protection);
        AssertUtil.assertState(updateCount > 0, "更新协议主播同意状态失败");
    }


}



