package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.convert;

import fm.lizhi.ocean.wavecenter.base.util.CalculateUtil;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataFamilyWeek;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataHallWeek;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.MapDataBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.MapDataRoomBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.OfflineZoneMetricsDataBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants.OfflineZoneRoomCategoryEnums;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.response.ResponseDataMonitorFamilySummary;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.response.ResponseDataMonitorRoomSummary;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 线下数据监控转换器
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS
)
public interface OfflineZoneDataMonitorConvert {

    OfflineZoneDataMonitorConvert INSTANCE = Mappers.getMapper(OfflineZoneDataMonitorConvert.class);

    /**
     * 构建指标数据Bean
     *
     * @param current 当前值
     * @param pre     上期值
     * @return 指标数据Bean
     */
    default OfflineZoneMetricsDataBean buildMetricsDataBean(Double current, Double pre) {
        OfflineZoneMetricsDataBean bean = new OfflineZoneMetricsDataBean();
        bean.setCurrent(current);
        bean.setPre(pre);

        // 使用CalculateUtil计算环比
        String ratio = CalculateUtil.relativeRatio(String.valueOf(current), String.valueOf(pre));
        bean.setRatio(ratio);

        return bean;
    }

    /**
     * 构建指标数据Bean（BigDecimal版本）
     */
    default OfflineZoneMetricsDataBean buildMetricsDataBean(BigDecimal current, BigDecimal pre) {
        Double currentValue = current != null ? current.doubleValue() : null;
        Double preValue = pre != null ? pre.doubleValue() : null;
        return buildMetricsDataBean(currentValue, preValue);
    }

    /**
     * 构建指标数据Bean（Integer版本）
     */
    default OfflineZoneMetricsDataBean buildMetricsDataBean(Integer current, Integer pre) {
        Double currentValue = current != null ? current.doubleValue() : null;
        Double preValue = pre != null ? pre.doubleValue() : null;
        return buildMetricsDataBean(currentValue, preValue);
    }

    /**
     * 构建指标数据Bean（Long版本）
     */
    default OfflineZoneMetricsDataBean buildMetricsDataBean(Long current, Long pre) {
        Double currentValue = current != null ? current.doubleValue() : null;
        Double preValue = pre != null ? pre.doubleValue() : null;
        return buildMetricsDataBean(currentValue, preValue);
    }

    /**
     * 构建家族汇总响应
     */
    default ResponseDataMonitorFamilySummary buildFamilySummaryResponse(OfflineZoneDataFamilyWeek currentWeek, OfflineZoneDataFamilyWeek previousWeek) {
        ResponseDataMonitorFamilySummary response = new ResponseDataMonitorFamilySummary();
        // 基地数
        response.setBasicCnt(OfflineZoneDataMonitorConvert.INSTANCE.buildMetricsDataBean(
                currentWeek.getBasicCnt(),
                previousWeek != null ? previousWeek.getBasicCnt() : null
        ));

        // 线下厅数
        response.setOfflineHallCnt(OfflineZoneDataMonitorConvert.INSTANCE.buildMetricsDataBean(
                currentWeek.getOfflineHallCnt(),
                previousWeek != null ? previousWeek.getOfflineHallCnt() : null
        ));

        // 线下主播数
        response.setOfflinePlayerCnt(OfflineZoneDataMonitorConvert.INSTANCE.buildMetricsDataBean(
                currentWeek.getOfflinePlayerCnt(),
                previousWeek != null ? previousWeek.getOfflinePlayerCnt() : null
        ));

        // 线下厅数占比
        response.setOfflineHallCntRate(OfflineZoneDataMonitorConvert.INSTANCE.buildMetricsDataBean(
                currentWeek.getOfflineHallCntRate(),
                previousWeek != null ? previousWeek.getOfflineHallCntRate() : null
        ));

        // 线下主播数占比
        response.setOfflinePlayerCntRate(OfflineZoneDataMonitorConvert.INSTANCE.buildMetricsDataBean(
                currentWeek.getOfflinePlayerCntRate(),
                previousWeek != null ? previousWeek.getOfflinePlayerCntRate() : null
        ));

        // 线下厅收入
        response.setOfflineHallIncome(OfflineZoneDataMonitorConvert.INSTANCE.buildMetricsDataBean(
                currentWeek.getOfflineHallIncome(),
                previousWeek != null ? previousWeek.getOfflineHallIncome() : null
        ));

        // 线下厅收入占比
        response.setOfflineHallIncomeRate(OfflineZoneDataMonitorConvert.INSTANCE.buildMetricsDataBean(
                currentWeek.getOfflineHallIncomeRate(),
                previousWeek != null ? previousWeek.getOfflineHallIncomeRate() : null
        ));

        // 受保护主播数
        response.setProtectedPlayerCnt(OfflineZoneDataMonitorConvert.INSTANCE.buildMetricsDataBean(
                currentWeek.getProtectedPlayerCnt(),
                previousWeek != null ? previousWeek.getProtectedPlayerCnt() : null
        ));

        // 受保护主播数占比
        response.setProtectedPlayerCntRate(OfflineZoneDataMonitorConvert.INSTANCE.buildMetricsDataBean(
                currentWeek.getProtectedPlayerCntRate(),
                previousWeek != null ? previousWeek.getProtectedPlayerCntRate() : null
        ));

        return response;
    }

    /**
     * 构建厅汇总响应
     */
    default ResponseDataMonitorRoomSummary buildRoomSummaryResponse(OfflineZoneDataHallWeek currentWeek, OfflineZoneDataHallWeek previousWeek) {
        ResponseDataMonitorRoomSummary response = new ResponseDataMonitorRoomSummary();
        if (OfflineZoneRoomCategoryEnums.OFFLINE_HALL.getCategory() == currentWeek.getCategory()){
            response.setCity(currentWeek.getCity());
            response.setProvince(currentWeek.getProvince());
        }

        // 线下主播数
        response.setOfflinePlayerCnt(OfflineZoneDataMonitorConvert.INSTANCE.buildMetricsDataBean(
                currentWeek.getOfflinePlayerCnt(),
                previousWeek != null ? previousWeek.getOfflinePlayerCnt() : null
        ));

        // 线下主播数占比
        response.setOfflinePlayerCntRate(OfflineZoneDataMonitorConvert.INSTANCE.buildMetricsDataBean(
                currentWeek.getOfflinePlayerCntRate(),
                previousWeek != null ? previousWeek.getOfflinePlayerCntRate() : null
        ));

        // 线下厅收入
        response.setIncome(OfflineZoneDataMonitorConvert.INSTANCE.buildMetricsDataBean(
                currentWeek.getIncome(),
                previousWeek != null ? previousWeek.getIncome() : null
        ));

        // 受保护主播数
        response.setProtectedPlayerCnt(OfflineZoneDataMonitorConvert.INSTANCE.buildMetricsDataBean(
                currentWeek.getProtectedPlayerCnt(),
                previousWeek != null ? previousWeek.getProtectedPlayerCnt() : null
        ));

        // 受保护主播数占比
        response.setProtectedPlayerCntRate(OfflineZoneDataMonitorConvert.INSTANCE.buildMetricsDataBean(
                currentWeek.getProtectedPlayerCntRate(),
                previousWeek != null ? previousWeek.getProtectedPlayerCntRate() : null
        ));

        // 线下主播收入
        response.setOfflinePlayerIncome(OfflineZoneDataMonitorConvert.INSTANCE.buildMetricsDataBean(
                currentWeek.getOfflinePlayerIncome(),
                previousWeek != null ? previousWeek.getOfflinePlayerIncome() : null
        ));

        // 线下主播收入占比
        response.setOfflinePlayerIncomeRate(OfflineZoneDataMonitorConvert.INSTANCE.buildMetricsDataBean(
                currentWeek.getOfflinePlayerIncomeRate(),
                previousWeek != null ? previousWeek.getOfflinePlayerIncomeRate() : null
        ));

        return response;
    }

    /**
     * 构建地图数据Bean
     */
    default MapDataBean buildMapDataBean(String province, String city, List<OfflineZoneDataHallWeek> cityHalls) {
        MapDataBean bean = new MapDataBean();
        bean.setProvince(province);
        bean.setCity(city);
        // 构建厅列表
        List<MapDataRoomBean> roomList = cityHalls.stream()
                .map(hall -> OfflineZoneDataMonitorConvert.INSTANCE.buildMapDataRoomBean(
                        hall.getNjId(),
                        hall.getNjName(),
                        hall.getOfflinePlayerCnt()
                ))
                .collect(Collectors.toList());

        bean.setRoomList(roomList);
        return bean;
    }

    /**
     * 构建地图厅数据Bean
     */
    default MapDataRoomBean buildMapDataRoomBean(Long njId, String njName, Integer offlinePlayerCnt) {
        MapDataRoomBean bean = new MapDataRoomBean();
        bean.setNjId(njId);
        bean.setNjName(njName);
        bean.setOfflinePlayerCnt(offlinePlayerCnt != null ? offlinePlayerCnt.longValue() : 0L);
        // njBand 和 photo 需要从其他地方获取，这里暂时设置为空
        bean.setNjBand("");
        bean.setPhoto("");
        return bean;
    }

    /**
     * 构建空的家族汇总响应
     */
    default ResponseDataMonitorFamilySummary buildEmptyFamilySummary() {
        ResponseDataMonitorFamilySummary response = new ResponseDataMonitorFamilySummary();
        OfflineZoneMetricsDataBean emptyMetrics = new OfflineZoneMetricsDataBean()
                .setCurrent(0.0)
                .setPre(0.0)
                .setRatio("--");

        response.setBasicCnt(emptyMetrics);
        response.setOfflineHallCnt(emptyMetrics);
        response.setOfflinePlayerCnt(emptyMetrics);
        response.setOfflineHallCntRate(emptyMetrics);
        response.setOfflinePlayerCntRate(emptyMetrics);
        response.setOfflineHallIncome(emptyMetrics);
        response.setOfflineHallIncomeRate(emptyMetrics);
        response.setProtectedPlayerCnt(emptyMetrics);
        response.setProtectedPlayerCntRate(emptyMetrics);

        return response;
    }

    /**
     * 构建空的厅汇总响应
     */
    default ResponseDataMonitorRoomSummary buildEmptyRoomSummary() {
        ResponseDataMonitorRoomSummary response = new ResponseDataMonitorRoomSummary();
        OfflineZoneMetricsDataBean emptyMetrics = new OfflineZoneMetricsDataBean()
                .setCurrent(0.0)
                .setPre(0.0)
                .setRatio("--");

        response.setOfflinePlayerCnt(emptyMetrics);
        response.setOfflinePlayerCntRate(emptyMetrics);
        response.setIncome(emptyMetrics);
        response.setProtectedPlayerCnt(emptyMetrics);
        response.setProtectedPlayerCntRate(emptyMetrics);
        response.setOfflinePlayerIncome(emptyMetrics);
        response.setOfflinePlayerIncomeRate(emptyMetrics);

        return response;
    }
}
