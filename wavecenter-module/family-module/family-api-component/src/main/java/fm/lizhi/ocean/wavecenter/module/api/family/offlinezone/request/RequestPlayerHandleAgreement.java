package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 主播处理跳槽保护协议请求
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RequestPlayerHandleAgreement {

    /**
     * 协议ID
     */
    @NotNull(message = "协议ID不能为空")
    private Long protectionId;

    /**
     * 应用ID
     */
    @NotNull(message = "应用ID不能为空")
    private Integer appId;

    /**
     * 公会ID
     */
    @NotNull(message = "公会ID不能为空")
    private Long familyId;

    /**
     * 厅ID
     */
    @NotNull(message = "厅ID不能为空")
    private Long njId;

    /**
     * 主播ID
     */
    @NotNull(message = "主播ID不能为空")
    private Long playerId;

    /**
     * 同意状态：1-同意，0-拒绝
     */
    @NotNull(message = "处理状态不能为空")
    private Integer agreeStatus;

    /**
     * 处理原因（可选）
     */
    private String handleReason;

    /**
     * 操作人（主播ID的字符串形式，用于记录操作）
     */
    private String operator;
}
