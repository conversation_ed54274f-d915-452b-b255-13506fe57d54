package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestPlayerHandleAgreement;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestSubmitAgreement;

import javax.validation.Valid;

/**
 * 线下专区-主播跳槽保护协议服务
 * <AUTHOR>
 */
public interface OfflineZoneProtectionService {


    /**
     * 提交跳槽保护协议
     */
    Result<Void> submitAgreement(@Valid RequestSubmitAgreement request);

    /**
     * 主播处理跳槽保护协议（同意或拒绝）
     */
    Result<Void> playerHandleAgreement(@Valid RequestPlayerHandleAgreement request);


    /**
     * 提交跳槽保护协议失败,参数错误
     */
    int SUBMIT_AGREEMENT_PARAM_ERROR = 2520001;

    /**
     * 提交跳槽保护协议失败,协议已存在
     */
    int SUBMIT_AGREEMENT_EXIST = 2520002;

    /**
     * 提交跳槽保护协议失败,保存协议失败
     */
    int SUBMIT_AGREEMENT_FAIL = 2520003;

    /**
     * 提交跳槽保护协议失败,协议已过期
     */
    int SUBMIT_AGREEMENT_EXPIRED = 2520004;

    /**
     * 提交跳槽保护协议失败,协议不存在
     */
    int SUBMIT_AGREEMENT_NOT_EXIST = 2520005;

    /**
     * 主播处理协议失败,参数错误
     */
    int PLAYER_HANDLE_PARAM_ERROR = 2520101;

    /**
     * 主播处理协议失败,协议不存在
     */
    int PLAYER_HANDLE_PROTECTION_NOT_FOUND = 2520102;

    /**
     * 主播处理协议失败,协议已处理
     */
    int PLAYER_HANDLE_ALREADY_PROCESSED = 2520103;

    /**
     * 主播处理协议失败,处理失败
     */
    int PLAYER_HANDLE_FAIL = 2520104;

    /**
     * 主播处理协议失败,协议已过期
     */
    int PLAYER_HANDLE_EXPIRED = 2520105;


}
