package fm.lizhi.ocean.wavecenter.datastore.platform.message.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 *
 * <AUTHOR>
 * @date 2025-06-11 02:08:25
 */
@Table(name = "`wavecenter_notice_config`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcNoticeConfig {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 标题
     */
    @Column(name= "`title`")
    private String title;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 公告类型，1： 新功能上线，2：活动公告，3：签约申请，4：其他
     */
    @Column(name= "`type`")
    private Integer type;

    /**
     * 1：上架，2：下架，默认上架
     */
    @Column(name= "`status`")
    private Integer status;

    /**
     * 环境
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 创建人
     */
    @Column(name= "`operator`")
    private String operator;

    /**
     * 是否删除，0：未删除，1：已删除
     */
    @Column(name= "`deleted`")
    private Boolean deleted;

    /**
     * 生效时间
     */
    @Column(name= "`effect_time`")
    private Date effectTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 文本内容
     */
    @Column(name= "`content`")
    private String content;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", title=").append(title);
        sb.append(", appId=").append(appId);
        sb.append(", type=").append(type);
        sb.append(", status=").append(status);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", operator=").append(operator);
        sb.append(", deleted=").append(deleted);
        sb.append(", effectTime=").append(effectTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", createTime=").append(createTime);
        sb.append(", content=").append(content);
        sb.append("]");
        return sb.toString();
    }
}