package fm.lizhi.ocean.wavecenter.datastore.platform.premission.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 角色表
 *
 * @date 2024-04-15 05:38:26
 */
@Table(name = "`wavecenter_role`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcRole {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 角色名称
     */
    @Column(name= "`role_name`")
    private String roleName;

    /**
     * 角色编码
     */
    @Column(name= "`role_code`")
    private String roleCode;

    /**
     * 角色创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 角色修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 优先级
     */
    @Column(name= "`level`")
    private Integer level;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", roleName=").append(roleName);
        sb.append(", roleCode=").append(roleCode);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", level=").append(level);
        sb.append("]");
        return sb.toString();
    }
}