package fm.lizhi.ocean.wavecenter.datastore.platform.premission.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 组件表
 *
 * @date 2024-03-27 05:09:52
 */
@Table(name = "`wavecenter_component`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcComponent {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 组件名称
     */
    @Column(name= "`component_name`")
    private String componentName;

    /**
     * 组件编码
     */
    @Column(name= "`component_code`")
    private String componentCode;

    /**
     * 角色创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 角色修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", componentName=").append(componentName);
        sb.append(", componentCode=").append(componentCode);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}