package fm.lizhi.ocean.wavecenter.datastore.platform.message.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 消息中心-内容表
 *
 * @date 2025-08-08 02:41:09
 */
@Table(name = "`wavecenter_message_content`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcMessageContent {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 消息记录ID
     */
    @Column(name= "`message_id`")
    private Long messageId;

    /**
     * 内容类型，可用于区分内容类型，0 普通内容
     */
    @Column(name= "`content_type`")
    private Integer contentType;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`update_time`")
    private Date updateTime;

    /**
     * 内容
     */
    @Column(name= "`content`")
    private String content;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", messageId=").append(messageId);
        sb.append(", contentType=").append(contentType);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", content=").append(content);
        sb.append("]");
        return sb.toString();
    }
}