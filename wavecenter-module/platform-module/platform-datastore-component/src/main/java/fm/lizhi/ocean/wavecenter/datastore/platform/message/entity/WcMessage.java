package fm.lizhi.ocean.wavecenter.datastore.platform.message.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 消息中心-基本信息表
 *
 * @date 2024-10-31 04:41:07
 */
@Table(name = "`wavecenter_message`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcMessage {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 标题
     */
    @Column(name= "`title`")
    private String title;

    /**
     * 消息类型
     */
    @Column(name= "`type`")
    private Integer type;

    /**
     * 目标用户 ID
     */
    @Column(name= "`target_user_id`")
    private Long targetUserId;

    /**
     * 发送用户 ID, 1=系统发送
     */
    @Column(name= "`send_user_id`")
    private Long sendUserId;

    /**
     * 可见角色
     */
    @Column(name= "`visible_role_code`")
    private String visibleRoleCode;

    /**
     * 跳转链接
     */
    @Column(name= "`target_link`")
    private String targetLink;

    /**
     * 跳转类型
     */
    @Column(name= "`link_type`")
    private String linkType;

    /**
     * 业务 ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 关联场景业务ID，比如合同id,签约记录ID
     */
    @Column(name= "`biz_id`")
    private Long bizId;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`update_time`")
    private Date updateTime;

    /**
     * 0=未删除，1=已删除
     */
    @Column(name= "`deleted`")
    private Boolean deleted;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", title=").append(title);
        sb.append(", type=").append(type);
        sb.append(", targetUserId=").append(targetUserId);
        sb.append(", sendUserId=").append(sendUserId);
        sb.append(", visibleRoleCode=").append(visibleRoleCode);
        sb.append(", targetLink=").append(targetLink);
        sb.append(", linkType=").append(linkType);
        sb.append(", appId=").append(appId);
        sb.append(", bizId=").append(bizId);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", deleted=").append(deleted);
        sb.append("]");
        return sb.toString();
    }
}