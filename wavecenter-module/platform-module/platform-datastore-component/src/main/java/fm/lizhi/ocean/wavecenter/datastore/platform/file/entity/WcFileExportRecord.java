package fm.lizhi.ocean.wavecenter.datastore.platform.file.entity;

import java.util.Date;
import javax.persistence.*;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 文件导出记录
 *
 * @date 2024-04-22 10:57:24
 */
@Table(name = "`wavecenter_file_export_record`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcFileExportRecord {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 业务
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 文件状态 1: 导出中, 2: 完成导出, 3=导出失败
     */
    @Column(name= "`file_status`")
    private Integer fileStatus;

    /**
     * 文件路径
     */
    @Column(name= "`file_path`")
    private String filePath;

    /**
     * 文件名
     */
    @Column(name= "`file_name`")
    private String fileName;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 文件所属用户ID
     */
    @Column(name= "`user_id`")
    private Long userId;

    /**
     * 0=未删除, 1=已删除
     */
    @Column(name= "`deleted`")
    private Integer deleted;

    public WcFileExportRecord(Integer appId, Long userId, String fileName) {
        this.appId = appId;
        this.fileName = fileName;
        this.userId = userId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", fileStatus=").append(fileStatus);
        sb.append(", filePath=").append(filePath);
        sb.append(", fileName=").append(fileName);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", userId=").append(userId);
        sb.append(", deleted=").append(deleted);
        sb.append("]");
        return sb.toString();
    }
}