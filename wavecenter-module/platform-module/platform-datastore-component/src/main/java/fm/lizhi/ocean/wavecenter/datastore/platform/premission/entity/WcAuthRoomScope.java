package fm.lizhi.ocean.wavecenter.datastore.platform.premission.entity;

import lombok.*;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 权限表-厅数据范围权限
 *
 * @date 2024-12-03 02:32:35
 */
@Table(name = "`wavecenter_auth_room_scope`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcAuthRoomScope {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 授权记录ID,wavecenter_role_auth_ref表的id
     */
    @Column(name= "`auth_id`")
    private Long authId;

    /**
     * 业务id
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 厅关联的家族
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 厅id
     */
    @Column(name= "`room_id`")
    private Long roomId;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`update_time`")
    private Date updateTime;

    /**
     * 1=删除,0=未删除
     */
    @Column(name= "`deleted`")
    private Integer deleted;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", authId=").append(authId);
        sb.append(", appId=").append(appId);
        sb.append(", familyId=").append(familyId);
        sb.append(", roomId=").append(roomId);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", deleted=").append(deleted);
        sb.append("]");
        return sb.toString();
    }
}