<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>fm.lizhi.ocean.wavecenter</groupId>
        <artifactId>platform-module</artifactId>
        <version>2.0.3-SNAPSHOT</version>
    </parent>

    <artifactId>platform-api-component</artifactId>

    <dependencies>
        <!--region =================基础架构的依赖=================-->
        <dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>lz-commons-serviceclient</artifactId>
            <scope>provided</scope>
        </dependency>
        <!--endregion-->

        <!--region ====================        第二方框架依赖        ==================== -->
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>wave-server-common-context</artifactId>
        </dependency>
        <!--endregion-->

        <!--region ====================        第三方框架依赖        ==================== -->
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <!--endregion-->
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>fm.lizhi.commons</groupId>
                <artifactId>autoapi-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>