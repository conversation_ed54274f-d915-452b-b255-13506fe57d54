package fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 活动申请基础信息
 *
 * @date 2025-05-21 09:37:28
 */
@Table(name = "`activity_apply_info`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityApplyInfo {
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 业务定义不超过10个字，稍微冗余
     */
    @Column(name= "`name`")
    private String name;

    /**
     * 提报厅厅主ID
     */
    @Column(name= "`nj_id`")
    private Long njId;

    /**
     * 家族ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 活动分类ID
     */
    @Column(name= "`class_id`")
    private Long classId;

    /**
     * 活动开始时间
     */
    @Column(name= "`start_time`")
    private Date startTime;

    /**
     * 活动结束时间
     */
    @Column(name= "`end_time`")
    private Date endTime;

    /**
     * 活动状态，1：待审批，2：审批通过，3：审批不通过，4：用户取消，5：官方取消
     */
    @Column(name= "`audit_status`")
    private Integer auditStatus;

    /**
     * 版本号
     */
    @Column(name= "`version`")
    private Integer version;

    /**
     * 申请类型，1：自主提报，2: 官方活动
     */
    @Column(name= "`apply_type`")
    private Integer applyType;

    /**
     * 审核原因
     */
    @Column(name= "`audit_reason`")
    private String auditReason;

    /**
     * 联系人
     */
    @Column(name= "`contact`")
    private String contact;

    /**
     * 申请者uid
     */
    @Column(name= "`applicant_uid`")
    private Long applicantUid;

    /**
     * 联系方式
     */
    @Column(name= "`contact_number`")
    private String contactNumber;

    @Column(name= "`host_id`")
    private Long hostId;

    /**
     * 陪档主播ID列表
     */
    @Column(name= "`accompany_nj_ids`")
    private String accompanyNjIds;

    /**
     * 活动目标，不超过100字
     */
    @Column(name= "`goal`")
    private String goal;

    /**
     * 活动介绍，不超过100字
     */
    @Column(name= "`introduction`")
    private String introduction;

    /**
     * 活动道具图片，多个逗号分隔，斜杠开头
     */
    @Column(name= "`auxiliary_prop_url`")
    private String auxiliaryPropUrl;

    /**
     * 活动海报图片地址
     */
    @Column(name= "`poster_url`")
    private String posterUrl;

    /**
     * 玩法工具，多个逗号分隔
     */
    @Column(name= "`activity_tool`")
    private String activityTool;

    /**
     * 房间公告，不超过500字
     */
    @Column(name= "`room_announcement`")
    private String roomAnnouncement;

    /**
     * 房间公告图片，不超过3个
     */
    @Column(name= "`room_announcement_img_url`")
    private String roomAnnouncementImgUrl;

    /**
     * 房间背景ID
     */
    @Column(name= "`room_background_id`")
    private Long roomBackgroundId;

    /**
     * 头像框ID
     */
    @Column(name= "`avatar_widget_id`")
    private Long avatarWidgetId;

    /**
     * 可选值  TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 是否删除，默认不删除
     */
    @Column(name= "`deleted`")
    private Integer deleted;

    /**
     * 审批操作人
     */
    @Column(name= "`audit_operator`")
    private String auditOperator;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 提报模式 1=简易模式, 2=详细模式
     */
    @Column(name= "`model`")
    private Integer model;

    /**
     * 推送数据消息通知状态;0-未通知，1-已通知
     */
    @Column(name= "`notify_report_status`")
    private Boolean notifyReportStatus;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", name=").append(name);
        sb.append(", njId=").append(njId);
        sb.append(", familyId=").append(familyId);
        sb.append(", classId=").append(classId);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", auditStatus=").append(auditStatus);
        sb.append(", version=").append(version);
        sb.append(", applyType=").append(applyType);
        sb.append(", auditReason=").append(auditReason);
        sb.append(", contact=").append(contact);
        sb.append(", applicantUid=").append(applicantUid);
        sb.append(", contactNumber=").append(contactNumber);
        sb.append(", hostId=").append(hostId);
        sb.append(", accompanyNjIds=").append(accompanyNjIds);
        sb.append(", goal=").append(goal);
        sb.append(", introduction=").append(introduction);
        sb.append(", auxiliaryPropUrl=").append(auxiliaryPropUrl);
        sb.append(", posterUrl=").append(posterUrl);
        sb.append(", activityTool=").append(activityTool);
        sb.append(", roomAnnouncement=").append(roomAnnouncement);
        sb.append(", roomAnnouncementImgUrl=").append(roomAnnouncementImgUrl);
        sb.append(", roomBackgroundId=").append(roomBackgroundId);
        sb.append(", avatarWidgetId=").append(avatarWidgetId);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", deleted=").append(deleted);
        sb.append(", auditOperator=").append(auditOperator);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", createTime=").append(createTime);
        sb.append(", model=").append(model);
        sb.append(", notifyReportStatus=").append(notifyReportStatus);
        sb.append("]");
        return sb.toString();
    }
}