package fm.lizhi.ocean.wavecenter.provider.grow.capability.model.convert;

import fm.lizhi.ocean.wavecenter.datastore.grow.capability.entity.WcGrowCapability;
import fm.lizhi.ocean.wavecenter.provider.grow.capability.model.dto.GrowDemoCapabilityDTO;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2025/7/9 16:50
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface GrowCapabilityConvert {

    GrowCapabilityConvert I = Mappers.getMapper(GrowCapabilityConvert.class);

    GrowDemoCapabilityDTO toDTO(WcGrowCapability wcGrowCapability);

}
