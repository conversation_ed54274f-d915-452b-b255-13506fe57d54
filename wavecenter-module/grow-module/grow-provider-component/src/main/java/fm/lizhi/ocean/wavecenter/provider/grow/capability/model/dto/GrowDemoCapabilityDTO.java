package fm.lizhi.ocean.wavecenter.provider.grow.capability.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/7/9 16:49
 */
@Data
@Accessors(chain = true)
public class GrowDemoCapabilityDTO {

    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 能力项code
     */
    private String capabilityCode;

    /**
     * 状态:0=停用 1=启用 2=下周
     */
    private Integer status;

    /**
     * 业务 ID
     */
    private Integer appId;

    /**
     * 服务部署环境, TEST/PRE/PRO
     */
    private String deployEnv;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 最近操作人
     */
    private String modifyUser;

}
