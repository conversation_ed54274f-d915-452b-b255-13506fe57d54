package fm.lizhi.ocean.wavecenter.provider.grow.capability.impl;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.domain.grow.ability.service.GrowDomeDomainService;
import fm.lizhi.ocean.wavecenter.module.api.grow.capability.bean.GrowDemoBean;
import fm.lizhi.ocean.wavecenter.module.api.grow.capability.request.GrowDemoRequest;
import fm.lizhi.ocean.wavecenter.module.api.grow.capability.service.GrowDemoService;
import fm.lizhi.ocean.wavecenter.provider.grow.capability.manager.GrowDemoManager;
import fm.lizhi.ocean.wavecenter.provider.grow.capability.model.dto.GrowDemoCapabilityDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/7/9 16:46
 */
@Slf4j
@ServiceProvider
public class GrowDemoServiceImpl implements GrowDemoService {

    @Autowired
    private GrowDemoManager growDemoManager;
    @Autowired
    private GrowDomeDomainService growDomeDomainService;

    @Override
    public Result<GrowDemoBean> queryGrowDemo(GrowDemoRequest request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        GrowDemoCapabilityDTO demo = growDemoManager.getDemo();
        log.info("demo={}", demo);
        growDomeDomainService.test();
        return RpcResult.success(new GrowDemoBean());
    }
}
