package fm.lizhi.ocean.wavecenter.provider.grow.capability.manager;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.datastore.grow.capability.entity.WcGrowCapability;
import fm.lizhi.ocean.wavecenter.datastore.grow.capability.entity.WcGrowCapabilityExample;
import fm.lizhi.ocean.wavecenter.datastore.grow.capability.mapper.WcGrowCapabilityMapper;
import fm.lizhi.ocean.wavecenter.provider.grow.capability.model.convert.GrowCapabilityConvert;
import fm.lizhi.ocean.wavecenter.provider.grow.capability.model.dto.GrowDemoCapabilityDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/9 16:47
 */
@Component
public class GrowDemoManager {

    @Autowired
    private WcGrowCapabilityMapper wcGrowCapabilityMapper;

    public GrowDemoCapabilityDTO getDemo(){
        WcGrowCapabilityExample example = new WcGrowCapabilityExample();
        example.createCriteria()
                .andAppIdEqualTo(ContextUtils.getBusinessEvnEnum().getAppId())
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        List<WcGrowCapability> list = wcGrowCapabilityMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return GrowCapabilityConvert.I.toDTO(list.get(0));
    }

}
