<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>fm.lizhi.ocean.wavecenter</groupId>
        <artifactId>grow-module</artifactId>
        <version>2.0.3-SNAPSHOT</version>
    </parent>

    <artifactId>grow-provider-component</artifactId>

    <properties>
        <!-- 跳过部署 -->
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <!--region =================本项目模块依赖=================-->
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>grow-api-component</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>grow-datastore-component</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>grow-domain-component</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>wavecenter-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>wavecenter-base</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>wavecenter-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!--endregion-->

        <!--region =================基础架构的依赖=================-->
        <dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>lz-commons-serviceclient</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>lz-commons-util</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.common</groupId>
            <artifactId>lz-common-romefs-javasdk</artifactId>
        </dependency>
        <!-- Okhttp是罗马上传要求的4.x版本 -->
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <!-- kotlin是罗马上传要求的版本 -->
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.common</groupId>
            <artifactId>datastore-redis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.common</groupId>
            <artifactId>dispatcher-executor</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.common</groupId>
            <artifactId>kafka-client-spring-config</artifactId>
        </dependency>
        <!--endregion-->

        <!--region =================第二方框架依赖=================-->
        <dependency>
            <groupId>fm.lizhi.ocean.lamp</groupId>
            <artifactId>lamp-common-logging</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.lamp</groupId>
            <artifactId>lamp-common-generic</artifactId>
        </dependency>
        <!-- apollo配置中心客户端 -->
        <dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>lz-commons-config</artifactId>
        </dependency>
        <!-- 日记打印, 解决logback配置文件不生效问题 -->
        <dependency>
            <groupId>fm.lizhi.common</groupId>
            <artifactId>lz-common-logging-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.lamp</groupId>
            <artifactId>lamp-common-config</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>wave-server-common-context</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--endregion-->

        <!--region =================第三方框架依赖=================-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <!--        熔断限流-->
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-all</artifactId>
        </dependency>
        <!-- cat监控 -->
        <dependency>
            <groupId>com.dianping.cat</groupId>
            <artifactId>cat-client</artifactId>
        </dependency>
        <!-- 灯塔支持跨线程路由 -->
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-trace</artifactId>
        </dependency>

        <!-- apache集合类新版, 推荐 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>
        <!-- apache集合类, 不推荐 -->
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
        </dependency>
        <!-- Java Bean运行时实体映射 -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <!--endregion-->

        <!--region =================PP依赖=================-->
        <!--endregion-->
        <!--region =================西米依赖=================-->
        <!--endregion-->
        <!--region =================黑叶依赖=================-->
        <!--endregion-->
    </dependencies>

</project>