package fm.lizhi.ocean.wavecenter.module.api.grow.capability.request;

import fm.lizhi.ocean.wave.server.common.request.RequestAppIdAware;
import fm.lizhi.ocean.wave.server.common.validator.AppEnumId;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/7/9 16:39
 */
@Data
public class GrowDemoRequest implements RequestAppIdAware {

    @NotNull(message = "appId is null")
    @AppEnumId
    private Integer appId;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
