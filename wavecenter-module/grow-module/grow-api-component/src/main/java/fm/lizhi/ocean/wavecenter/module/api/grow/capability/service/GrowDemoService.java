package fm.lizhi.ocean.wavecenter.module.api.grow.capability.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.module.api.grow.capability.bean.GrowDemoBean;
import fm.lizhi.ocean.wavecenter.module.api.grow.capability.request.GrowDemoRequest;

/**
 * 测试服务
 * <AUTHOR>
 * @date 2025/7/9 16:25
 */
public interface GrowDemoService {

    /**
     * 测试方法
     * @param request
     * @return
     */
    Result<GrowDemoBean> queryGrowDemo(GrowDemoRequest request);

}
