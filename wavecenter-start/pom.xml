<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>fm.lizhi.ocean.wavecenter</groupId>
        <artifactId>lz-ocean-wavecenter</artifactId>
        <version>2.0.3-SNAPSHOT</version>
    </parent>

    <artifactId>wavecenter-start</artifactId>

    <properties>
        <!-- 跳过安装 -->
<!--        <maven.install.skip>true</maven.install.skip>-->
        <!-- 跳过部署 -->
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>

        <!--region =================本项目模块依赖=================-->
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>grow-provider-component</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>datacenter-provider-component</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>user-provider-component</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>activitycenter-provider-component</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>anchor-provider-component</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>background-provider-component</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>sign-provider-component</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>platform-provider-component</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>award-provider-component</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>resource-provider-component</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>family-datastore-component</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>family-provider-component</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>wavecenter-infrastructure</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!--endregion-->

        <!--region ====================        基础架构的依赖        ==================== -->
        <dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>lz-commons-serviceclient</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>lz-unit</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <!--endregion-->

    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>