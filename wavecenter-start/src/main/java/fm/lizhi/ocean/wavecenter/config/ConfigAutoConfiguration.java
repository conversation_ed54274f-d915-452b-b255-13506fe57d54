package fm.lizhi.ocean.wavecenter.config;

import fm.lizhi.ocean.lamp.common.config.annotation.ApolloNamespaceInclude;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.config.OfflineZoneFamilyConfig;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.ActivityConfig;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.config.SingerAnchorConfig;
import fm.lizhi.ocean.wavecenter.service.award.family.config.FamilyAwardConfig;
import fm.lizhi.ocean.wavecenter.service.award.singer.config.SingerAwardConfig;
import fm.lizhi.ocean.wavecenter.service.datacenter.config.DataCenterConfig;
import fm.lizhi.ocean.wavecenter.service.grow.config.GrowConfig;
import fm.lizhi.ocean.wavecenter.service.income.config.IncomeConfig;
import fm.lizhi.ocean.wavecenter.service.live.config.LiveConfig;
import fm.lizhi.ocean.wavecenter.service.permissions.config.PermissionsConfig;
import fm.lizhi.ocean.wavecenter.service.resource.config.ResourceConfig;
import fm.lizhi.ocean.wavecenter.service.sign.config.SignConfig;
import fm.lizhi.ocean.wavecenter.service.user.config.UserConfig;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/4/10 19:02
 */
@Configuration
@ApolloNamespaceInclude({
        "wavecenter-user",
        "wavecenter-common",
        "wavecenter-permission",
        "wavecenter-datacenter",
        "wavecenter-income",
        "wavecenter-activity-center",
        "wavecenter-live",
        "wavecenter-sign",
        "wavecenter-grow",
        "wavecenter-resource",
        "wavecenter-award",
        "wavecenter-anchor",
        "wavecenter-family"

})
@EnableConfigurationProperties({
        CommonConfig.class,
        UserConfig.class,
        PermissionsConfig.class,
        DataCenterConfig.class,
        IncomeConfig.class,
        ActivityConfig.class,
        LiveConfig.class,
        SignConfig.class,
        GrowConfig.class,
        ResourceConfig.class,
        FamilyAwardConfig.class,
        SingerAnchorConfig.class,
        SingerAwardConfig.class,
        OfflineZoneFamilyConfig.class
})
public class ConfigAutoConfiguration {
}
