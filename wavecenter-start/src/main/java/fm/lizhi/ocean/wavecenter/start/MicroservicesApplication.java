package fm.lizhi.ocean.wavecenter.start;

import fm.lizhi.common.datastore.mysql.spring.boot.annotation.EnableDatastoreMysqlConfig;
import fm.lizhi.common.dubbo.adapter.springboot.EnableServiceProvider;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.ActivitycenterPackagePath;
import fm.lizhi.ocean.wavecenter.datastore.anchor.AnchorPackagePath;
import fm.lizhi.ocean.wavecenter.datastore.award.AwardPackagePath;
import fm.lizhi.ocean.wavecenter.datastore.background.BackgroundPackagePath;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.DatacenterPackagePath;
import fm.lizhi.ocean.wavecenter.datastore.family.FamilyPackagePath;
import fm.lizhi.ocean.wavecenter.datastore.grow.GrowPackagePath;
import fm.lizhi.ocean.wavecenter.datastore.platform.PlatformPackagePath;
import fm.lizhi.ocean.wavecenter.datastore.resource.ResourcePackagePath;
import fm.lizhi.ocean.wavecenter.datastore.sign.SignPackagePath;
import fm.lizhi.ocean.wavecenter.datastore.user.UserPackagePath;
import fm.lizhi.ocean.wavecenter.provider.activitycenter.ActivityCenterProviderPath;
import fm.lizhi.ocean.wavecenter.provider.anchor.AnchorProviderPath;
import fm.lizhi.ocean.wavecenter.provider.award.AwardProviderPath;
import fm.lizhi.ocean.wavecenter.provider.background.BackgroundProviderPath;
import fm.lizhi.ocean.wavecenter.provider.datacenter.DatacenterProviderPath;
import fm.lizhi.ocean.wavecenter.provider.grow.GrowProviderPath;
import fm.lizhi.ocean.wavecenter.provider.platform.PlatformProviderPath;
import fm.lizhi.ocean.wavecenter.provider.resource.ResourceProviderPath;
import fm.lizhi.ocean.wavecenter.provider.sign.SignProviderPath;
import fm.lizhi.ocean.wavecenter.provider.user.UserProviderPath;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

/**
 * 微服务应用启动类
 *
 * <AUTHOR> 由脚手架生成
 */
@EnableServiceProvider(basePackages = {"fm.lizhi.ocean.wavecenter.service"
        , ActivityCenterProviderPath.PROVIDER
        , AnchorProviderPath.PROVIDER
        , BackgroundProviderPath.PROVIDER
        , DatacenterProviderPath.PROVIDER
        , GrowProviderPath.PROVIDER
        , SignProviderPath.PROVIDER
        , UserProviderPath.PROVIDER
        , PlatformProviderPath.PROVIDER
        , AwardProviderPath.PROVIDER
        , ResourceProviderPath.PROVIDER
    }
)
@SpringBootApplication(exclude={DataSourceAutoConfiguration.class},
        scanBasePackages = {"fm.lizhi.ocean.wavecenter.config"
                , "fm.lizhi.ocean.wavecenter.service"
                , "fm.lizhi.ocean.wavecenter.infrastructure"
                , "fm.lizhi.ocean.wavecenter.domain"
        }
)
@EnableDatastoreMysqlConfig(basePackages = {"fm.lizhi.ocean.wavecenter.infrastructure"
        , ActivitycenterPackagePath.DATASTORE
        , AnchorPackagePath.DATASTORE
        , BackgroundPackagePath.DATASTORE
        , DatacenterPackagePath.DATASTORE
        , GrowPackagePath.DATASTORE
        , SignPackagePath.DATASTORE
        , UserPackagePath.DATASTORE
        , PlatformPackagePath.DATASTORE
        , AwardPackagePath.DATASTORE
        , ResourcePackagePath.DATASTORE
        , FamilyPackagePath.DATASTORE
        , AnchorProviderPath.DATASTORE
    }
)
public class MicroservicesApplication {

    // ⚠️⚠️⚠️本地DEBUG启动，请记得先配置启动参数，见READNE.MD
    public static void main(String[] args) {
        SpringApplication.run(MicroservicesApplication.class, args);
    }
}
