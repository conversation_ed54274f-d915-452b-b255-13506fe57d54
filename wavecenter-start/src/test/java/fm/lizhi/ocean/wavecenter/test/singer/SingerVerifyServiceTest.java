package fm.lizhi.ocean.wavecenter.test.singer;

import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.OriginalSingerInfo;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SongInfo;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerPreAuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestSingerVerifyApply;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestSingerVerifyApplyV2;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestVerifyAudit;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseGetSingerEntranceInfo;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseGetSingerStatus;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseVerifyAudit;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.service.SingerVerifyAdminService;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.service.SingerVerifyService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import pp.fm.lizhi.live.pp.player.api.SingerAuthService;
import pp.fm.lizhi.live.pp.player.protocol.SingerAuthProto;
import xm.fm.lizhi.live.pp.vocal.enums.SingerVerifyStatusEnum;

import java.util.ArrayList;
import java.util.Date;

@Slf4j
public class SingerVerifyServiceTest extends AbstractDataCenterTest {

    @Autowired
    private SingerVerifyService singerVerifyService;

    @Autowired
    private SingerVerifyAdminService singerVerifyAdminService;

    @Autowired
    private SingerAuthService singerAuthService;

    @Test
    public void testApply() {
        //1476249657716791170L,  1407428746738163330L, 1363233728683696898L, 1492020494159844482L
        ArrayList<Long> list = Lists.newArrayList( 1377315212755443586L, 5450825355705216044L, 5450825355705205804L);
        RequestSingerVerifyApply request = new RequestSingerVerifyApply();
        OriginalSingerInfo originalSingerInfo = new OriginalSingerInfo();
        originalSingerInfo.setOriginalSinger(true);
        originalSingerInfo.setSocialVerifyImageList(Lists.newArrayList("https://lizhi-live-pp-vocal.oss-cn-hangzhou.aliyuncs.com/test/test.jpg", "https://lizhi-live-pp-vocal.oss-cn-hangzhou.aliyuncs.com/test/test.png"));
        originalSingerInfo.setOriginalSongUrl("https://lizhi-live-pp-vocal.oss-cn-hangzhou.aliyuncs.com/test/test.mp3");
        request.setAppId(BusinessEvnEnum.PP.getAppId());
        request.setSingerType(SingerTypeEnum.QUALITY.getType());
        request.setSongName("义勇军进行曲QUALITY");
        request.setAudioPath("https://lizhi-live-pp-vocal.oss-cn-hangzhou.aliyuncs.com/test/test.mp3");
        request.setSongStyle("国风");
        request.setOriginalSingerInfo(originalSingerInfo);
        request.setPreAuditStatus(SingerPreAuditStatusEnum.PASSED.getStatus());
        for (Long id : list) {
            request.setUserId(id);
            Result<Void> result = singerVerifyService.singerVerifyApply(request);
            log.info("testApply.result: {}, msg={}", result.target(), result.getMessage());
        }
    }

    @Test
    public void testApply2() {
        RequestSingerVerifyApplyV2 request = new RequestSingerVerifyApplyV2();
        OriginalSingerInfo originalSingerInfo = new OriginalSingerInfo();
        originalSingerInfo.setOriginalSinger(true);
        originalSingerInfo.setSocialVerifyImageList(Lists.newArrayList("https://lizhi-live-pp-vocal.oss-cn-hangzhou.aliyuncs.com/test/test.jpg", "https://lizhi-live-pp-vocal.oss-cn-hangzhou.aliyuncs.com/test/test.png"));
        originalSingerInfo.setOriginalSongUrl("https://lizhi-live-pp-vocal.oss-cn-hangzhou.aliyuncs.com/test/test.mp3");
        SongInfo songInfo = new SongInfo();
        songInfo.setSongName("义勇军进行曲QUALITY");
        songInfo.setAudioPath("https://lizhi-live-pp-vocal.oss-cn-hangzhou.aliyuncs.com/test/test.mp3");
        songInfo.setSongStyle("国风");
        songInfo.setPreAuditStatus(SingerPreAuditStatusEnum.REJECT.getStatus());
        request.setSongInfos(Lists.newArrayList(songInfo));
        request.setAppId(BusinessEvnEnum.PP.getAppId());
        request.setSingerType(SingerTypeEnum.NEW.getType());
        request.setOriginalSingerInfo(originalSingerInfo);
        request.setSongInfos(Lists.newArrayList(songInfo));
        request.setUserId(1344526773139834370L);
        Result<Void> result = singerVerifyService.singerVerifyApplyV2(request);
        log.info("testApply.result: {}, msg={}", result.target(), result.getMessage());
    }

    @Test
    public void testAudit() {
        RequestVerifyAudit verifyAudit = new RequestVerifyAudit();
        verifyAudit.setAppId(BusinessEvnEnum.HEI_YE.getAppId());
        verifyAudit.setAuditStatus(SingerAuditStatusEnum.PASS.getStatus());
        verifyAudit.setIds(Lists.newArrayList(5445834017408552575L));
        verifyAudit.setOperator("caibingyan");
        verifyAudit.setSingerType(SingerTypeEnum.STAR.getType());
        Result<ResponseVerifyAudit> result = singerVerifyAdminService.verifyAudit(verifyAudit);
        log.info("testApply.result: {}, msg={}", result.target(), result.getMessage());
    }

    @Test
    public void testGetStatus() {
        Result<ResponseGetSingerEntranceInfo> result = singerVerifyService.getSingerEntranceInfo(BusinessEvnEnum.HEI_YE.getAppId(), 1383861584727676162L);
        log.info("testApply.result: {}, msg={}", result.target(), result.getMessage());

    }

    @Test
    public void testGetSingerAuthRecord() {
//        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.PP);
//        long lastQueryTime = DateUtil.getMonthBefore(new Date(), 24).getTime();
//        int pageSize = 100;
//        Result<SingerAuthProto.ResponseBatchGetSingerAuthRecord> result = singerAuthService.batchGetSingerAuthRecord((int) lastQueryTime, pageSize);
//        if (RpcResult.isFail(result)) {
//            log.info("testApply.result: {}, msg={}", result.target(), result.rCode());
//            return;
//        }
//        log.info("testApply.result: {}, msg={}", result.target(), result.rCode());

    }
}
