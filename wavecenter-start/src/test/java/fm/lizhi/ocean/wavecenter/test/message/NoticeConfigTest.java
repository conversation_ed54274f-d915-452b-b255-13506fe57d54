package fm.lizhi.ocean.wavecenter.test.message;

import java.util.Date;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.message.constant.WcNoticeConfigEnum;
import fm.lizhi.ocean.wavecenter.api.message.request.RequestWcNoticeConfigPage;
import fm.lizhi.ocean.wavecenter.api.message.request.RequestWcNoticeConfigSave;
import fm.lizhi.ocean.wavecenter.api.message.response.ResponseWcNoticeConfigPage;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import fm.lizhi.ocean.wavecenter.api.message.service.WcNoticeConfigService;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class NoticeConfigTest extends AbstractDataCenterTest {

    @Autowired
    private WcNoticeConfigService wcNoticeConfigService;

    /**
     * 保存公告配置
     */
    @Test
    public void testSaveWcNoticeConfig() {
        RequestWcNoticeConfigSave request = new RequestWcNoticeConfigSave();
        request.setAppId(BusinessEvnEnum.PP.getAppId());
        request.setType(WcNoticeConfigEnum.NEW_FUNCTION.getCode());
        request.setTitle("测试公告");
        request.setContent("测试公告内容");
        request.setOperator("admin");
        request.setEffectTime(new Date().getTime());
        Result<Void> result = wcNoticeConfigService.saveWcNoticeConfig(request);
        log.info("result: {}", result.rCode());
    }

    /**
     * 查询公告配置
     */
    @Test
    public void testQueryWcNoticeConfig() {
        RequestWcNoticeConfigPage request = new RequestWcNoticeConfigPage();
        request.setAppId(BusinessEvnEnum.PP.getAppId());
        request.setType(WcNoticeConfigEnum.NEW_FUNCTION.getCode());
        request.setPageSize(20);
        request.setPageNo(1);
        request.setStatus(1);
        request.setMinEffectTime(new Date().getTime() - 1000 * 60 * 60 * 24 * 7);
        request.setMaxEffectTime(new Date().getTime());
        request.setTitle("测试公告");

        Result<ResponseWcNoticeConfigPage> result = wcNoticeConfigService.queryWcNoticeConfigPage(request);
        if (result.rCode() == 0) {
            ResponseWcNoticeConfigPage response = result.target();
            log.info("response: {}", JsonUtil.dumps(response));
        }
    }

    /**
     * 删除公告配置
     */
    @Test
    public void testDeleteWcNoticeConfigById() {
        Result<Void> result = wcNoticeConfigService.deleteWcNoticeConfigById(5455098678498820223L);
        log.info("result: {}", result.rCode());
    }
}
