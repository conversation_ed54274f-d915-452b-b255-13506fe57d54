package fm.lizhi.ocean.wavecenter.test.grow.ability;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.module.api.grow.capability.bean.GrowDemoBean;
import fm.lizhi.ocean.wavecenter.module.api.grow.capability.request.GrowDemoRequest;
import fm.lizhi.ocean.wavecenter.module.api.grow.capability.service.GrowDemoService;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2025/7/9 19:49
 */
@Slf4j
public class DemoTest extends AbstractDataCenterTest {

    @Autowired
    private GrowDemoService growDemoService;

    @Test
    public void test(){
        try {
            GrowDemoRequest request = new GrowDemoRequest();
            request.setAppId(BusinessEvnEnum.PP.getAppId());
            Result<GrowDemoBean> result = growDemoService.queryGrowDemo(request);
            System.out.println("result = " + result);
        } catch (Exception e) {
            log.error("error", e);
        }

    }

}
