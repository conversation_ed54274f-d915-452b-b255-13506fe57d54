package fm.lizhi.ocean.wavecenter.test.demo;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.constants.DateType;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.GetRoomPlayerPerformanceBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.IndicatorBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomGetKeyIndicatorsParamBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomPlayerPerformanceResBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.PlayerSignBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.RoomSignBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.dto.PageDto;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcDataRoomDay;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.pp.PpContractRemote;
import fm.lizhi.ocean.wavecenter.service.datacenter.handler.RoomDataHandler;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/19 21:26
 */
public class RoomDataTest extends AbstractDataCenterTest {

    @Autowired
    private RoomDataHandler roomDataHandler;
    @Autowired
    private PpContractRemote ppContractRemote;

    @Test
    public void testPlayer(){
        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.HEI_YE);
        GetRoomPlayerPerformanceBean param = GetRoomPlayerPerformanceBean.builder().roomId(1391498504731581570L)
                .familyId(5318540755940148863L)
                .appId(BusinessEvnEnum.HEI_YE.appId())
                .build();
        RoomPlayerPerformanceResBean playerPerformance = roomDataHandler.getPlayerPerformance(param);
        System.out.println("playerPerformance = " + playerPerformance);
    }

    @Test
    public void testR(){
        PageBean<RoomSignBean> list = ppContractRemote.getAllGuildRooms(5120155821400925311L, null,1, 5000);
        System.out.println("list = " + list);
        PageBean<PlayerSignBean> list2 = ppContractRemote.getAllRoomPlayers(2646620839973229612L, 1, 5000);
        System.out.println("list2 = " + list2);
        PageDto<PlayerSignBean> list3 = ppContractRemote.getAllGuildPlayer(5120155821400925311L, null, 1, 1, 5000);
        System.out.println("list = " + list3);
    }

    @Test
    public void test(){

        List<IndicatorBean> keyIndicators = roomDataHandler.getKeyIndicators(RoomGetKeyIndicatorsParamBean.builder()
                .appId(10919088)
                .roomId(1240698917318997506L)
                .dateType(DateType.MONTH)
                .startDate("2024-04-08")
                .endDate("2024-04-14")
                .valueMetrics(Lists.newArrayList(WcDataRoomDay.Fields.upGuestRate))
                .ratioMetrics(Lists.newArrayList(WcDataRoomDay.Fields.upGuestPlayerCnt))
                .build());

        System.out.println("keyIndicators = " + keyIndicators);

    }

}
