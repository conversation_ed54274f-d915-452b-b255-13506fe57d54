package fm.lizhi.ocean.wavecenter.test.grow.ability;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.service.grow.ability.dto.SettlePeriodDTO;
import fm.lizhi.ocean.wavecenter.service.grow.ability.handler.PlayerAbilitySettleHandler;
import fm.lizhi.ocean.wavecenter.service.grow.ability.handler.RoomAbilitySettleHandler;
import fm.lizhi.ocean.wavecenter.service.grow.ability.handler.RoomInFamilyRankHandler;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/11 18:02
 */
public class PlayerSettleTest extends AbstractDataCenterTest {

    @Autowired
    private PlayerAbilitySettleHandler playerAbilitySettleHandler;
    @Autowired
    private RoomAbilitySettleHandler roomAbilitySettleHandler;
    @Autowired
    private RoomInFamilyRankHandler roomInFamilyRankHandler;

    @Test
    public void testAll(){
        test(MyDateUtil.getDayValueDate(20250428), MyDateUtil.getDayValueDate(20250504));
        test(MyDateUtil.getDayValueDate(20250505), MyDateUtil.getDayValueDate(20250511));
        test(MyDateUtil.getDayValueDate(20250512), MyDateUtil.getDayValueDate(20250518));
        test(MyDateUtil.getDayValueDate(20250519), MyDateUtil.getDayValueDate(20250525));
        test(MyDateUtil.getDayValueDate(20250526), MyDateUtil.getDayValueDate(20250601));
        test(MyDateUtil.getDayValueDate(20250602), MyDateUtil.getDayValueDate(20250608));
    }

    public void test(Date start, Date end){
        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.PP);
        playerAbilitySettleHandler.settleByPeriod(new SettlePeriodDTO(start, end));
//        testRoom(start, end);
//        testRank(start, end);
    }

    @Test
    public void testHy(){
        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.HEI_YE);
        Date start = MyDateUtil.getDayValueDate(20250602);
        Date end = MyDateUtil.getDayValueDate(20250608);
        playerAbilitySettleHandler.settleByPeriod(new SettlePeriodDTO(start, end));
        testRoomHy();
        testRankHy();
    }

    @Test
    public void testXm(){
        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.XIMI);
        Date start = MyDateUtil.getDayValueDate(20250602);
        Date end = MyDateUtil.getDayValueDate(20250608);
        playerAbilitySettleHandler.settleByPeriod(new SettlePeriodDTO(start, end));
    }

    @Test
    public void testRoom(){
        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.PP);
        roomAbilitySettleHandler.settleByPeriod(new SettlePeriodDTO(MyDateUtil.getDayValueDate(20250602)
                , MyDateUtil.getDayValueDate(20250608)));
    }

    @Test
    public void testRoomHy(){
        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.HEI_YE);
        Date start = MyDateUtil.getDayValueDate(20250602);
        Date end = MyDateUtil.getDayValueDate(20250608);
        roomAbilitySettleHandler.settleByPeriod(new SettlePeriodDTO(start, end));
    }

    @Test
    public void testRoomXm(){
        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.XIMI);
        Date start = MyDateUtil.getDayValueDate(20250602);
        Date end = MyDateUtil.getDayValueDate(20250608);
        roomAbilitySettleHandler.settleByPeriod(new SettlePeriodDTO(start, end));
    }

    @Test
    public void testRank(){
        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.PP);
        roomInFamilyRankHandler.settle(new SettlePeriodDTO(MyDateUtil.getDayValueDate(20250609), MyDateUtil.getDayValueDate(20250615))
                , 5156152187132315775L);
    }

    @Test
    public void testRankHy(){
        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.HEI_YE);
        Date start = MyDateUtil.getDayValueDate(20250602);
        Date end = MyDateUtil.getDayValueDate(20250608);
        roomInFamilyRankHandler.settle(new SettlePeriodDTO(start, end));
    }

    @Test
    public void testRankXm(){
        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.XIMI);
        Date start = MyDateUtil.getDayValueDate(20250602);
        Date end = MyDateUtil.getDayValueDate(20250608);
        roomInFamilyRankHandler.settle(new SettlePeriodDTO(start, end));
    }

}
