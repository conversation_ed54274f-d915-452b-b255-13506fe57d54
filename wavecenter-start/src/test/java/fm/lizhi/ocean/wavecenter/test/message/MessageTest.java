package fm.lizhi.ocean.wavecenter.test.message;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.message.constant.MessageTargetLinkEnum;
import fm.lizhi.ocean.wavecenter.api.message.request.*;
import fm.lizhi.ocean.wavecenter.api.message.response.ResponseGetMessageList;
import fm.lizhi.ocean.wavecenter.api.message.response.ResponseUnReadMessageCount;
import fm.lizhi.ocean.wavecenter.api.message.service.WaveCenterMessageService;
import fm.lizhi.ocean.wavecenter.api.message.service.WcNoticeConfigService;
import fm.lizhi.ocean.wavecenter.api.message.response.ResponseQueryRecentMessages;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/10 18:16
 */
@Slf4j
public class MessageTest extends AbstractDataCenterTest {

    @Autowired
    private WaveCenterMessageService waveCenterMessageService;

    @Autowired
    private WcNoticeConfigService wcNoticeConfigService;


    private static final Long bizId = 5411716809409692287L;

    @Test
    public void sendMessage() throws Exception {

        Result<Long> result = waveCenterMessageService.sendMessage(new RequestSendMessage()
                .setTargetUserId(11111L)
                .setBizId(222222L)
                .setContent("测试一下签约内容")
                .setAppId(BusinessEvnEnum.XIMI.getAppId())
                .setType(1)
                .setSendUserId(1L)
                .setLinkType(MessageTargetLinkEnum.SIGN_MANAGEMENT.name())
        );

//        RequestSendMessage param = new RequestSendMessage()
//                .setAppId(BusinessEvnEnum.PP.getAppId())
//                .setType(1)
//                .setContent("测试消息")
//                .setBizId(bizId)
//                .setSendUserId(1L)
//                .setTargetUserId(111L)
//                ;

//        Result<Long> result = waveCenterMessageService.sendMessage(param);
        log.info(JsonUtil.dumps(result));
    }

    @Test
    public void sendMessageBatch() throws Exception {

        RequestSendMessageBatch param = new RequestSendMessageBatch()
                .setAppId(BusinessEvnEnum.PP.getAppId())
                .setType(1)
                .setContent("测试消息" + RandomUtil.randomNumbers(3))
                .setBizId(bizId)
                .setLinkType(MessageTargetLinkEnum.ROOM_INCOME.name())
                .setSendUserId(1L)
                .setTargetUserIds(CollUtil.newArrayList(2677455881410503212L, 1386652551113644802L,
                        1386790754571414914L,
                        1386652918333353474L,
                        1386652950545609346L));

        Result<List<Long>> result = waveCenterMessageService.sendMessageBatch(param);
        log.info(JsonUtil.dumps(result));
    }

    @Test
    public void sendMessage2Role() throws Exception {

        RequestSendMessageToRole param = new RequestSendMessageToRole()
                .setAppId(BusinessEvnEnum.PP.getAppId())
                .setType(1)
                .setContent("测试消息")
                .setBizId(bizId)
                .setSendUserId(1L)
                .setRoleCodes(CollUtil.newArrayList("family", "player"));

        Result<List<Long>> result = waveCenterMessageService.sendMessage2Role(param);
        log.info(JsonUtil.dumps(result));
    }


    @Test
    public void getMessageList() throws Exception {
        RequestGetMessageList param = new RequestGetMessageList();
        param.setAppId(BusinessEvnEnum.PP.getAppId());
        param.setType(1);
        param.setSize(20);
        param.setUserId(111L);
        param.setRoleCode("family");
        param.setPerformanceId(0L);


        Result<ResponseGetMessageList> result = waveCenterMessageService.getMessageList(param);
        log.info(JsonUtil.dumps(result));
    }

    @Test
    public void getUnReadMessageCount() throws Exception {
        RequestUnReadMessageCount param = new RequestUnReadMessageCount();
        param.setAppId(BusinessEvnEnum.PP.getAppId());
        param.setTargetUserId(123L);

        Result<ResponseUnReadMessageCount> result = wcNoticeConfigService.getUnReadMessageCount(param);
        log.info(JsonUtil.dumps(result));
    }

    /**
     * 查询最新5条消息
     */
    @Test
    public void queryRecentMessages() throws Exception {
        Result<ResponseQueryRecentMessages> result = waveCenterMessageService.queryRecentMessages(BusinessEvnEnum.PP.getAppId(), 1432062959013754370L, 5, null);
        if (result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.info(JsonUtil.dumps(result));
        }
    }


}
