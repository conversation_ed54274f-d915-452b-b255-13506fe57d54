package fm.lizhi.ocean.wavecenter.test.datacenter;

import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.*;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.mapper.*;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.entity.WcAuditRecordFull;
import fm.lizhi.ocean.wavecenter.datastore.datacenter.mapper.WcAuditRecordFullMapper;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 造数据单元测试
 * <AUTHOR>
 * @date 2024/12/24 14:48
 */
public class GenDataTest extends AbstractDataCenterTest {

    //日期
    Date day = DateUtil.formatStrToDate("2024-12-12", DateUtil.date_2);

    Date weekStart = DateUtil.formatStrToDate(DateUtil.formatDateToString(cn.hutool.core.date.DateUtil.beginOfWeek(day), DateUtil.date_2), DateUtil.date_2);
    Date weekEnd = DateUtil.formatStrToDate(DateUtil.formatDateToString(cn.hutool.core.date.DateUtil.endOfWeek(day), DateUtil.date_2), DateUtil.date_2);

    Integer year = Integer.valueOf(DateUtil.formatDateToString(day, "yyyy"));
    Integer monthValue = MyDateUtil.getDateMonthValue(day);

    //用户信息
    String playerIds = "5415570784464668716";
    String roomIds = "5415570784464667180";
    Long familyId = 5120155821400925311L;
    int appId = BusinessEvnEnum.PP.getAppId();

    @Autowired
    private WcDataPlayerDayMapper playerDayMapper;
    @Autowired
    private WcDataPlayerWeekMapper playerWeekMapper;
    @Autowired
    private WcDataPlayerMonthMapper playerMonthMapper;
    @Autowired
    private WcDataPlayerRoomDayMapper playerRoomDayMapper;
    @Autowired
    private WcDataPlayerRoomWeekMapper playerRoomWeekMapper;
    @Autowired
    private WcDataPlayerRoomMonthMapper playerRoomMonthMapper;

    @Autowired
    private WcDataRoomDayMapper roomDayMapper;
    @Autowired
    private WcDataRoomWeekMapper roomWeekMapper;
    @Autowired
    private WcDataRoomMonthMapper roomMonthMapper;
    @Autowired
    private WcDataRoomFamilyDayMapper roomFamilyDayMapper;
    @Autowired
    private WcDataRoomFamilyWeekMapper roomFamilyWeekMapper;
    @Autowired
    private WcDataRoomFamilyMonthMapper roomFamilyMonthMapper;

    @Autowired
    private WcDataFamilyDayMapper familyDayMapper;
    @Autowired
    private WcDataFamilyWeekMapper familyWeekMapper;
    @Autowired
    private WcDataFamilyMonthMapper familyMonthMapper;

    @Autowired
    private WcAuditRecordFullMapper auditRecordFullMapper;

    @Test
    public void genDayData(){
        String[] playerArr = playerIds.split(",");
        String[] roomArr = roomIds.split(",");

        for (int i = 0; i < playerArr.length; i++) {
            Long playerId = Long.valueOf(playerArr[i]);
            Long roomId = Long.valueOf(roomArr[i]);

            WcDataPlayerDay playerDay = new WcDataPlayerDay();
            playerDay.setId(System.currentTimeMillis() * -1L); //负数 方便筛选
            playerDay.setPlayerId(playerId);
            playerDay.setAppId(appId);
            playerDay.setStatDateValue(MyDateUtil.getDateDayValue(day));
            playerDay.setStatDate(day);
            playerDay.setIncome(BigDecimal.valueOf(60+20+20));
            playerDay.setCharm(60+20+20);
            playerDay.setSignHallIncome(BigDecimal.valueOf(60));
            playerDay.setOfficialHallIncome(BigDecimal.valueOf(20));
            playerDay.setPersonalHallIncome(BigDecimal.valueOf(20));
            playerDay.setUpGuestDur(BigDecimal.valueOf(30));
            playerDay.setGiftUserCnt(5);
            playerDayMapper.insert(playerDay);

            WcDataPlayerRoomDay playerRoomDay = new WcDataPlayerRoomDay();
            playerRoomDay.setId(System.currentTimeMillis() * -1L); //负数 方便筛选
            playerRoomDay.setPlayerId(playerId);
            playerRoomDay.setRoomId(roomId);
            playerRoomDay.setFamilyId(familyId);
            playerRoomDay.setAppId(appId);
            playerRoomDay.setStatDateValue(MyDateUtil.getDateDayValue(day));
            playerRoomDay.setStatDate(day);
            playerRoomDay.setIncome(playerDay.getIncome());
            playerRoomDay.setCharm(playerDay.getCharm());
            playerRoomDay.setSignHallIncome(playerDay.getSignHallIncome());
            playerRoomDay.setOfficialHallIncome(playerDay.getOfficialHallIncome());
            playerRoomDay.setPersonalHallIncome(playerDay.getPersonalHallIncome());
            playerRoomDay.setUpGuestDur(playerDay.getUpGuestDur());
            playerRoomDay.setGiftUserCnt(playerDay.getGiftUserCnt());
            playerRoomDayMapper.insert(playerRoomDay);

            WcDataPlayerWeek playerWeek = new WcDataPlayerWeek();
            playerWeek.setId(System.currentTimeMillis() * -1L); //负数 方便筛选
            playerWeek.setPlayerId(playerId);
            playerWeek.setAppId(appId);
            playerWeek.setStartWeekDate(weekStart);
            playerWeek.setEndWeekDate(weekEnd);
            playerWeek.setIncome(playerDay.getIncome());
            playerWeek.setCharm(playerDay.getCharm());
            playerWeek.setSignHallIncome(playerDay.getSignHallIncome());
            playerWeek.setOfficialHallIncome(playerDay.getOfficialHallIncome());
            playerWeek.setPersonalHallIncome(playerDay.getPersonalHallIncome());
            playerWeek.setUpGuestDur(playerDay.getUpGuestDur());
            playerWeek.setGiftUserCnt(playerDay.getGiftUserCnt());
            playerWeekMapper.insert(playerWeek);

            WcDataPlayerRoomWeek playerRoomWeek = new WcDataPlayerRoomWeek();
            playerRoomWeek.setId(System.currentTimeMillis() * -1L); //负数 方便筛选
            playerRoomWeek.setPlayerId(playerId);
            playerRoomWeek.setRoomId(roomId);
            playerRoomWeek.setAppId(appId);
            playerRoomWeek.setFamilyId(familyId);
            playerRoomWeek.setStartWeekDate(weekStart);
            playerRoomWeek.setEndWeekDate(weekEnd);
            playerRoomWeek.setIncome(playerDay.getIncome());
            playerRoomWeek.setCharm(playerDay.getCharm());
            playerRoomWeek.setSignHallIncome(playerDay.getSignHallIncome());
            playerRoomWeek.setOfficialHallIncome(playerDay.getOfficialHallIncome());
            playerRoomWeek.setPersonalHallIncome(playerDay.getPersonalHallIncome());
            playerRoomWeek.setUpGuestDur(playerDay.getUpGuestDur());
            playerRoomWeek.setGiftUserCnt(playerDay.getGiftUserCnt());
            playerRoomWeekMapper.insert(playerRoomWeek);

            WcDataPlayerMonth playerMonth = new WcDataPlayerMonth();
            playerMonth.setId(System.currentTimeMillis() * -1L); //负数 方便筛选
            playerMonth.setPlayerId(playerId);
            playerMonth.setAppId(appId);
            playerMonth.setStatYear(year);
            playerMonth.setStatMonth(monthValue);
            playerMonth.setIncome(playerDay.getIncome());
            playerMonth.setCharm(playerDay.getCharm());
            playerMonth.setSignHallIncome(playerDay.getSignHallIncome());
            playerMonth.setOfficialHallIncome(playerDay.getOfficialHallIncome());
            playerMonth.setPersonalHallIncome(playerDay.getPersonalHallIncome());
            playerMonth.setUpGuestDur(playerDay.getUpGuestDur());
            playerMonth.setGiftUserCnt(playerDay.getGiftUserCnt());
            playerMonthMapper.insert(playerMonth);

            WcDataPlayerRoomMonth playerRoomMonth = new WcDataPlayerRoomMonth();
            playerRoomMonth.setId(System.currentTimeMillis() * -1L); //负数 方便筛选
            playerRoomMonth.setPlayerId(playerId);
            playerRoomMonth.setRoomId(roomId);
            playerRoomMonth.setAppId(appId);
            playerRoomMonth.setFamilyId(familyId);
            playerRoomMonth.setStatYear(year);
            playerRoomMonth.setStatMonth(monthValue);
            playerRoomMonth.setIncome(playerDay.getIncome());
            playerRoomMonth.setCharm(playerDay.getCharm());
            playerRoomMonth.setSignHallIncome(playerDay.getSignHallIncome());
            playerRoomMonth.setOfficialHallIncome(playerDay.getOfficialHallIncome());
            playerRoomMonth.setPersonalHallIncome(playerDay.getPersonalHallIncome());
            playerRoomMonth.setUpGuestDur(playerDay.getUpGuestDur());
            playerRoomMonth.setGiftUserCnt(playerDay.getGiftUserCnt());
            playerRoomMonthMapper.insert(playerRoomMonth);

            WcDataRoomDay roomDay = new WcDataRoomDay();
            roomDay.setId(System.currentTimeMillis() * -1L);
            roomDay.setRoomId(roomId);
            roomDay.setAppId(appId);
            roomDay.setStatDateValue(MyDateUtil.getDateDayValue(day));
            roomDay.setStatDate(day);
            roomDay.setIncome(playerDay.getIncome());
            roomDay.setAllIncome(playerDay.getIncome());
            roomDay.setCharm(playerDay.getCharm());
            roomDay.setSignHallIncome(playerDay.getSignHallIncome());
            roomDay.setOfficialHallIncome(playerDay.getOfficialHallIncome());
            roomDay.setPersonalHallIncome(playerDay.getPersonalHallIncome());
            roomDay.setGiftUserCnt(playerDay.getGiftUserCnt());
            roomDay.setOpenDuration(playerDay.getUpGuestDur());
            roomDayMapper.insert(roomDay);

            WcDataRoomFamilyDay roomFamilyDay = new WcDataRoomFamilyDay();
            roomFamilyDay.setId(System.currentTimeMillis() * -1L);
            roomFamilyDay.setRoomId(roomId);
            roomFamilyDay.setFamilyId(familyId);
            roomFamilyDay.setAppId(appId);
            roomFamilyDay.setStatDateValue(MyDateUtil.getDateDayValue(day));
            roomFamilyDay.setStatDate(day);
            roomFamilyDay.setIncome(playerDay.getIncome());
            roomFamilyDay.setAllIncome(playerDay.getIncome());
            roomFamilyDay.setCharm(playerDay.getCharm());
            roomFamilyDay.setSignHallIncome(playerDay.getSignHallIncome());
            roomFamilyDay.setOfficialHallIncome(playerDay.getOfficialHallIncome());
            roomFamilyDay.setPersonalHallIncome(playerDay.getPersonalHallIncome());
            roomFamilyDay.setGiftUserCnt(playerDay.getGiftUserCnt());
            roomFamilyDay.setOpenDuration(playerDay.getUpGuestDur());
            roomFamilyDayMapper.insert(roomFamilyDay);

            WcDataRoomWeek roomWeek = new WcDataRoomWeek();
            roomWeek.setId(System.currentTimeMillis() * -1L);
            roomWeek.setRoomId(roomId);
            roomWeek.setAppId(appId);
            roomWeek.setStartWeekDate(weekStart);
            roomWeek.setEndWeekDate(weekEnd);
            roomWeek.setIncome(playerDay.getIncome());
            roomWeek.setAllIncome(playerDay.getIncome());
            roomWeek.setCharm(playerDay.getCharm());
            roomWeek.setSignHallIncome(playerDay.getSignHallIncome());
            roomWeek.setOfficialHallIncome(playerDay.getOfficialHallIncome());
            roomWeek.setPersonalHallIncome(playerDay.getPersonalHallIncome());
            roomWeek.setGiftUserCnt(playerDay.getGiftUserCnt());
            roomWeek.setOpenDuration(playerDay.getUpGuestDur());
            roomWeekMapper.insert(roomWeek);

            WcDataRoomFamilyWeek roomFamilyWeek = new WcDataRoomFamilyWeek();
            roomFamilyWeek.setId(System.currentTimeMillis() * -1L);
            roomFamilyWeek.setRoomId(roomId);
            roomFamilyWeek.setFamilyId(familyId);
            roomFamilyWeek.setAppId(appId);
            roomFamilyWeek.setStartWeekDate(weekStart);
            roomFamilyWeek.setEndWeekDate(weekEnd);
            roomFamilyWeek.setIncome(playerDay.getIncome());
            roomFamilyWeek.setAllIncome(playerDay.getIncome());
            roomFamilyWeek.setCharm(playerDay.getCharm());
            roomFamilyWeek.setSignHallIncome(playerDay.getSignHallIncome());
            roomFamilyWeek.setOfficialHallIncome(playerDay.getOfficialHallIncome());
            roomFamilyWeek.setPersonalHallIncome(playerDay.getPersonalHallIncome());
            roomFamilyWeek.setGiftUserCnt(playerDay.getGiftUserCnt());
            roomFamilyWeek.setOpenDuration(playerDay.getUpGuestDur());
            roomFamilyWeekMapper.insert(roomFamilyWeek);

            WcDataRoomMonth roomMonth = new WcDataRoomMonth();
            roomMonth.setId(System.currentTimeMillis() * -1L);
            roomMonth.setRoomId(roomId);
            roomMonth.setAppId(appId);
            roomMonth.setStatYear(year);
            roomMonth.setStatMonth(monthValue);
            roomMonth.setIncome(playerDay.getIncome());
            roomMonth.setAllIncome(playerDay.getIncome());
            roomMonth.setCharm(playerDay.getCharm());
            roomMonth.setSignHallIncome(playerDay.getSignHallIncome());
            roomMonth.setOfficialHallIncome(playerDay.getOfficialHallIncome());
            roomMonth.setPersonalHallIncome(playerDay.getPersonalHallIncome());
            roomMonth.setGiftUserCnt(playerDay.getGiftUserCnt());
            roomMonth.setOpenDuration(playerDay.getUpGuestDur());
            roomMonthMapper.insert(roomMonth);

            WcDataRoomFamilyMonth roomFamilyMonth = new WcDataRoomFamilyMonth();
            roomFamilyMonth.setId(System.currentTimeMillis() * -1L);
            roomFamilyMonth.setRoomId(roomId);
            roomFamilyMonth.setFamilyId(familyId);
            roomFamilyMonth.setAppId(appId);
            roomFamilyMonth.setStatYear(year);
            roomFamilyMonth.setStatMonth(monthValue);
            roomFamilyMonth.setIncome(playerDay.getIncome());
            roomFamilyMonth.setAllIncome(playerDay.getIncome());
            roomFamilyMonth.setCharm(playerDay.getCharm());
            roomFamilyMonth.setSignHallIncome(playerDay.getSignHallIncome());
            roomFamilyMonth.setOfficialHallIncome(playerDay.getOfficialHallIncome());
            roomFamilyMonth.setPersonalHallIncome(playerDay.getPersonalHallIncome());
            roomFamilyMonth.setGiftUserCnt(playerDay.getGiftUserCnt());
            roomFamilyMonth.setOpenDuration(playerDay.getUpGuestDur());
            roomFamilyMonthMapper.insert(roomFamilyMonth);
        }

        genFamilyData();
    }

    @Test
    public void genFamilyData(){

        //公会数据需要查询出所有厅的数据进行累加
        WcDataRoomFamilyDay param = new WcDataRoomFamilyDay();
        param.setFamilyId(familyId);
        param.setAppId(appId);
        param.setStatDateValue(MyDateUtil.getDateDayValue(day));

        List<WcDataRoomFamilyDay> dayList = roomFamilyDayMapper.selectMany(param);
        WcDataFamilyDay familyDay = new WcDataFamilyDay();
        familyDay.setId(System.currentTimeMillis() * -1L);
        familyDay.setFamilyId(familyId);
        familyDay.setAppId(appId);
        familyDay.setStatDateValue(MyDateUtil.getDateDayValue(day));
        familyDay.setStatDate(day);
        familyDay.setIncome(dayList.stream().map(WcDataRoomFamilyDay::getIncome).reduce(BigDecimal.valueOf(0L), BigDecimal::add));
        familyDay.setAllIncome(dayList.stream().map(WcDataRoomFamilyDay::getAllIncome).reduce(BigDecimal.valueOf(0L), BigDecimal::add));
        familyDay.setCharm(dayList.stream().map(WcDataRoomFamilyDay::getCharm).reduce(0, Integer::sum));
        familyDay.setSignHallIncome(dayList.stream().map(WcDataRoomFamilyDay::getSignHallIncome).reduce(BigDecimal.valueOf(0L), BigDecimal::add));
        familyDay.setOfficialHallIncome(dayList.stream().map(WcDataRoomFamilyDay::getOfficialHallIncome).reduce(BigDecimal.valueOf(0L), BigDecimal::add));
        familyDay.setPersonalHallIncome(dayList.stream().map(WcDataRoomFamilyDay::getPersonalHallIncome).reduce(BigDecimal.valueOf(0L), BigDecimal::add));
        familyDayMapper.insert(familyDay);

        WcDataRoomFamilyWeek weekParma = new WcDataRoomFamilyWeek();
        weekParma.setFamilyId(familyId);
        weekParma.setAppId(appId);
        weekParma.setStartWeekDate(weekStart);
        weekParma.setEndWeekDate(weekEnd);
        List<WcDataRoomFamilyWeek> weekList = roomFamilyWeekMapper.selectMany(weekParma);
        WcDataFamilyWeek familyWeek = new WcDataFamilyWeek();
        familyWeek.setId(System.currentTimeMillis() * -1L);
        familyWeek.setFamilyId(familyId);
        familyWeek.setAppId(appId);
        familyWeek.setStartWeekDate(weekStart);
        familyWeek.setEndWeekDate(weekEnd);
        familyWeek.setIncome(weekList.stream().map(WcDataRoomFamilyWeek::getIncome).reduce(BigDecimal.valueOf(0L), BigDecimal::add));
        familyWeek.setAllIncome(weekList.stream().map(WcDataRoomFamilyWeek::getAllIncome).reduce(BigDecimal.valueOf(0L), BigDecimal::add));
        familyWeek.setCharm(weekList.stream().map(WcDataRoomFamilyWeek::getCharm).reduce(0, Integer::sum));
        familyWeek.setSignHallIncome(weekList.stream().map(WcDataRoomFamilyWeek::getSignHallIncome).reduce(BigDecimal.valueOf(0L), BigDecimal::add));
        familyWeek.setOfficialHallIncome(weekList.stream().map(WcDataRoomFamilyWeek::getOfficialHallIncome).reduce(BigDecimal.valueOf(0L), BigDecimal::add));
        familyWeek.setPersonalHallIncome(weekList.stream().map(WcDataRoomFamilyWeek::getPersonalHallIncome).reduce(BigDecimal.valueOf(0L), BigDecimal::add));
        familyWeekMapper.insert(familyWeek);

        WcDataRoomFamilyMonth monthParma = new WcDataRoomFamilyMonth();
        monthParma.setFamilyId(familyId);
        monthParma.setAppId(appId);
        monthParma.setStatYear(year);
        monthParma.setStatMonth(monthValue);
        List<WcDataRoomFamilyMonth> monthList = roomFamilyMonthMapper.selectMany(monthParma);
        WcDataFamilyMonth familyMonth = new WcDataFamilyMonth();
        familyMonth.setId(System.currentTimeMillis() * -1L);
        familyMonth.setFamilyId(familyId);
        familyMonth.setAppId(appId);
        familyMonth.setStatYear(year);
        familyMonth.setStatMonth(monthValue);
        familyMonth.setIncome(monthList.stream().map(WcDataRoomFamilyMonth::getIncome).reduce(BigDecimal.valueOf(0L), BigDecimal::add));
        familyMonth.setAllIncome(monthList.stream().map(WcDataRoomFamilyMonth::getAllIncome).reduce(BigDecimal.valueOf(0L), BigDecimal::add));
        familyMonth.setCharm(monthList.stream().map(WcDataRoomFamilyMonth::getCharm).reduce(0, Integer::sum));
        familyMonth.setSignHallIncome(monthList.stream().map(WcDataRoomFamilyMonth::getSignHallIncome).reduce(BigDecimal.valueOf(0L), BigDecimal::add));
        familyMonth.setOfficialHallIncome(monthList.stream().map(WcDataRoomFamilyMonth::getOfficialHallIncome).reduce(BigDecimal.valueOf(0L), BigDecimal::add));
        familyMonth.setPersonalHallIncome(monthList.stream().map(WcDataRoomFamilyMonth::getPersonalHallIncome).reduce(BigDecimal.valueOf(0L), BigDecimal::add));
        familyMonthMapper.insert(familyMonth);

    }

    @Test
    public void genAuditData(){
        String[] playerArr = playerIds.split(",");
        String[] roomArr = roomIds.split(",");

        for (int i = 0; i < playerArr.length; i++) {
            Long playerId = Long.valueOf(playerArr[i]);
            Long roomId = Long.valueOf(roomArr[i]);

            WcAuditRecordFull wcAuditRecordFull = new WcAuditRecordFull();
            wcAuditRecordFull.setId(System.currentTimeMillis() * -1L);
            wcAuditRecordFull.setAppId(appId);
            wcAuditRecordFull.setSignNjId(roomId);
            wcAuditRecordFull.setSourceContentUrl("");
            wcAuditRecordFull.setPublicContentUrl("");
            wcAuditRecordFull.setSignFamilyId(familyId);
            wcAuditRecordFull.setUserId(playerId);
            wcAuditRecordFull.setOp(10);
            wcAuditRecordFull.setReason("");
            wcAuditRecordFull.setAuditEndTime(day);
            wcAuditRecordFull.setAuditStartTime(day);
            wcAuditRecordFull.setAuditId(String.valueOf(UUID.randomUUID()));
            wcAuditRecordFull.setRecordId(String.valueOf(UUID.randomUUID()));
            wcAuditRecordFull.setSceneType(1);
            wcAuditRecordFull.setSceneName("1");

            auditRecordFullMapper.insert(wcAuditRecordFull);
        }

    }


}
