package fm.lizhi.ocean.wavecenter.test.grow.ability;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.grow.ability.request.RequestGetPlayerPerformance;
import fm.lizhi.ocean.wavecenter.api.grow.ability.response.ResponseGetPlayerPerformance;
import fm.lizhi.ocean.wavecenter.service.grow.ability.manager.AbilityPerformanceManager;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2025/6/16 19:22
 */
public class AbilityPerformanceManagerTest extends AbstractDataCenterTest {

    @Autowired
    private AbilityPerformanceManager abilityPerformanceManager;

    @Test
    public void testGetRoomPerformance() {
        while (true) {
            try {
                ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.PP);
                RequestGetPlayerPerformance request = new RequestGetPlayerPerformance();
                request.setAppId(BusinessEvnEnum.PP.getAppId());
                request.setRoomId(1386653158851527810L);
                request.setPlayerId(5450825355705204780L);
                request.setStartWeekDate("2025-06-02");


                ResponseGetPlayerPerformance resp = abilityPerformanceManager.getPlayerPerformance(request);
                System.out.println("resp = " + resp);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

}
