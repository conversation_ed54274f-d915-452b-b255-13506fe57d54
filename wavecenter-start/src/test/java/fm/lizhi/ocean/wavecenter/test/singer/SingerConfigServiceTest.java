package fm.lizhi.ocean.wavecenter.test.singer;

import cn.hutool.core.date.DateUtil;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerAuditConfigBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestSaveApplyMenuConfig;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseApplyMenuConfig;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseGetSingerPreAuditConfig;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseSingerEnumerateConfig;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.service.SingerConfigService;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.constant.SingerOperatorConstant;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

@Slf4j
public class SingerConfigServiceTest extends AbstractDataCenterTest {

    @Autowired
    private SingerConfigService singerConfigService;

    @Test
    public void testGetEnumerateConfig() {
        log.info("testGetEnumerateConfig");
        Result<ResponseSingerEnumerateConfig> result = singerConfigService.getEnumerateConfig(BusinessEvnEnum.PP.getAppId());
        log.info("result:{}", result.target());
    }

    @Test
    public void testGetSingerPreAuditConfig() {
        log.info("testGetSingerPreAuditConfig");
        Result<ResponseGetSingerPreAuditConfig> result = singerConfigService.getSingerAudioAuditConfig(BusinessEvnEnum.PP.getAppId(), 1);
        log.info("result:{}", result.target());
    }


    @Test
    public void testSaveApplyMenuConfig() {
        Result<Void> result = singerConfigService.saveApplyMenuConfig(
                new RequestSaveApplyMenuConfig()
                        .setAppId(BusinessEvnEnum.PP.getAppId())
                        .setSingerType(SingerTypeEnum.NEW.getType())
                        .setEnabled(true)
                        .setOperator(SingerOperatorConstant.SYSTEM)
                        .setStartTime(new Date().getTime())
                        .setEndTime(DateUtil.nextMonth().getTime())
        );
        log.info("result:{}", JsonUtil.dumps(result));
    }


    @Test
    public void testSaveSingerAuditConfig2() {

        Result<ResponseApplyMenuConfig> result = singerConfigService.getApplyMenuConfig(BusinessEvnEnum.PP.getAppId(), SingerTypeEnum.NEW.getType());
        log.info("result:{}", JsonUtil.dumps(result));
    }



    public static void main(String[] args) {
        SingerAuditConfigBean singerAuditConfigBean = new SingerAuditConfigBean();
        singerAuditConfigBean.setDurationSec(10);
        singerAuditConfigBean.setThresholdSpeechProportion(50);
        singerAuditConfigBean.setThresholdMusicProportion(94);
        singerAuditConfigBean.setThresholdNoiseDB(-45);
        singerAuditConfigBean.setEnabledVerifyProportion(true);
        System.out.println(JsonUtil.dumps(singerAuditConfigBean));
    }




}
