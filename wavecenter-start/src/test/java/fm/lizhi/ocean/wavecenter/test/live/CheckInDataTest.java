package fm.lizhi.ocean.wavecenter.test.live;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.live.constants.CheckInDateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.live.request.RequestGetCheckInPlayerStatistic;
import fm.lizhi.ocean.wavecenter.api.live.request.RequestGetCheckInRoomStatistic;
import fm.lizhi.ocean.wavecenter.api.live.response.ResponseGetCheckInRoomStatistic;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.GetRoomInfoByNjIdDTO;
import fm.lizhi.ocean.wavecenter.service.live.handler.CheckInNotifyHandler;
import fm.lizhi.ocean.wavecenter.service.live.manager.LiveManager;
import fm.lizhi.ocean.wavecenter.service.live.manager.WaveCheckInDataManager;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/7 15:01
 */
public class CheckInDataTest extends AbstractDataCenterTest {

    @Autowired
    private WaveCheckInDataManager waveCheckInDataManager;
    @Autowired
    private CheckInNotifyHandler checkInNotifyHandler;

    @Autowired
    private LiveManager liveManager;

    @Test
    public void testRoom(){
        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.XIMI);
        RequestGetCheckInRoomStatistic request = new RequestGetCheckInRoomStatistic();
        request.setAppId(BusinessEvnEnum.XIMI.getAppId());
        request.setRoomId(5420059733990142518L);
        request.setDateType(CheckInDateTypeEnum.DAY);
        request.setStartDate(DateUtil.formatStrToNormalDate("2025-03-07 00:00:00").getTime());
        request.setEndDate(DateUtil.formatStrToNormalDate("2025-03-07 23:59:59").getTime());
        request.setFamilyId(5246373908659044991L);

        ResponseGetCheckInRoomStatistic result = waveCheckInDataManager.getCheckInRoomStatistic(request);
        System.out.println("result = " + result);
    }

    @Test
    public void testRoom2(){
        Result<GetRoomInfoByNjIdDTO> roomInfoByNjId = liveManager.getRoomInfoByNjId(1298319494187327874L);
        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.XIMI);
        RequestGetCheckInRoomStatistic request = new RequestGetCheckInRoomStatistic();
        request.setAppId(BusinessEvnEnum.XIMI.getAppId());
        request.setRoomId(roomInfoByNjId.target().getId());
        request.setDateType(CheckInDateTypeEnum.DAY);
        request.setStartDate(DateUtil.formatStrToNormalDate("2025-03-07 00:00:00").getTime());
        request.setEndDate(DateUtil.formatStrToNormalDate("2025-03-07 23:59:59").getTime());

        ResponseGetCheckInRoomStatistic result = waveCheckInDataManager.getCheckInRoomStatistic(request);
        System.out.println("result = " + result);
    }

    @Test
    public void testPlayer(){
        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.XIMI);

        //家族长
        RequestGetCheckInPlayerStatistic request = new RequestGetCheckInPlayerStatistic();
        request.setAppId(BusinessEvnEnum.XIMI.getAppId());
        request.setStartDate(DateUtil.formatStrToNormalDate("2025-03-07 00:00:00").getTime());
        request.setEndDate(DateUtil.formatStrToNormalDate("2025-03-07 23:59:59").getTime());
        request.setFamilyId(5246373908659044991L);
        request.setPlayerId(1359741258846886146L);
        waveCheckInDataManager.getCheckInPlayerStatistic(request);

        //厅主
        RequestGetCheckInPlayerStatistic request2 = new RequestGetCheckInPlayerStatistic();
        request2.setAppId(BusinessEvnEnum.XIMI.getAppId());
        request2.setRoomId(5420059733990142518L);
        request2.setStartDate(DateUtil.formatStrToNormalDate("2025-03-07 00:00:00").getTime());
        request2.setEndDate(DateUtil.formatStrToNormalDate("2025-03-07 23:59:59").getTime());
        request2.setPlayerId(1359741258846886146L);
        waveCheckInDataManager.getCheckInPlayerStatistic(request2);

        //主播
        RequestGetCheckInPlayerStatistic request3 = new RequestGetCheckInPlayerStatistic();
        request3.setAppId(BusinessEvnEnum.XIMI.getAppId());
        request3.setStartDate(DateUtil.formatStrToNormalDate("2025-03-07 00:00:00").getTime());
        request3.setEndDate(DateUtil.formatStrToNormalDate("2025-03-07 23:59:59").getTime());
        request3.setPlayerId(1359741258846886146L);
        waveCheckInDataManager.getCheckInPlayerStatistic(request3);
    }


    @Test
    public void testNotify() {
        checkInNotifyHandler.executeNotify(DateUtil.formatStrToNormalDate("2025-06-12 17:02:00"), BusinessEvnEnum.XIMI.appId());
    }

}
