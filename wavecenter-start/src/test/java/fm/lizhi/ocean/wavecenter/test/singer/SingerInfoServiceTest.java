package fm.lizhi.ocean.wavecenter.test.singer;

import cn.hutool.core.collection.CollUtil;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.*;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseGetAllSingerStatics;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseSingerCountInHall;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.service.SingerInfoAdminService;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.service.SingerInfoService;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.service.SingerPermissionService;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerInfo;
import fm.lizhi.ocean.wavecenter.provider.anchor.singer.datastore.mapper.ext.SingerInfoExtraMapper;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.constant.SingerEliminationReasonConstant;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.PageSingerInfoParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SaveSingerInfoParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerInfoManager;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class SingerInfoServiceTest extends AbstractDataCenterTest {

    @Autowired
    private SingerInfoManager singerInfoManager;

    @Autowired
    private SingerInfoAdminService singerInfoAdminService;

    @Autowired
    private SingerPermissionService singerPermissionService;

    @Autowired
    private SingerInfoService singerInfoService;

    @Autowired
    private SingerInfoExtraMapper singerInfoExtraMapper;
//
//
//    @Test
//    public void pageSingerInfo() {
//        PageSingerInfoParamDTO param = new PageSingerInfoParamDTO();
//        param.setAppId(BusinessEvnEnum.PP.getAppId());
//        param.setPageNo(1);
//        param.setPageSize(10);
//        param.setWhiteListSinger(true);
//        PageList<SingerInfo> test =
//                singerInfoExtraMapper.pageSingerInfo(param, "TEST", null, null, null, null, null);
//        System.out.println(JsonUtils.toJsonString(test));
//    }
    @Test
    public void saveSingerInfo() {
        SaveSingerInfoParamDTO param = new SaveSingerInfoParamDTO();
        param.setAppId(BusinessEvnEnum.PP.getAppId());
        param.setUserId(5415570784464668716L);
        param.setNjId(5416153068240634412L);
        param.setFamilyId(111L);
        param.setSingerVerifyId(111L);
        param.setSingerStatus(SingerStatusEnum.EFFECTIVE.getStatus());
        param.setSongStyle("TEST");
        param.setOriginalSinger(false);
        param.setSingerType(SingerTypeEnum.NEW.getType());
        param.setOperator("ADMIN");

        Boolean success = singerInfoManager.saveSingerInfo(param);
        log.info("success:{}", success);
    }

    @Test
    public void upgradeSinger() {
        RequestUpgradeSinger request = new RequestUpgradeSinger();
        request.setAppId(BusinessEvnEnum.PP.getAppId());
        request.setIds(CollUtil.newArrayList(5443081523192398975L));
        request.setSingerType(SingerTypeEnum.QUALITY);
        request.setOperator("ADMIN");

        Result<Void> result = singerInfoAdminService.upgradeSinger(request);
        log.info("result:{}", JsonUtil.dumps(result));
    }

    @Test
    public void eliminateSinger() {
        RequestEliminateSinger request = new RequestEliminateSinger();
        request.setAppId(BusinessEvnEnum.PP.getAppId());
        request.setIds(CollUtil.newArrayList(5443081523192398975L));
        request.setEliminateReason(SingerEliminationReasonConstant.OPERATOR_ELIMINATION);
        request.setOperator("adminxx");

        Result<Void> result = singerInfoAdminService.eliminateSinger(request);
        log.info("result:{}", JsonUtil.dumps(result));
    }

    @Test
    public void checkSingerPermission() {
        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.HEI_YE);
        RequestCheckSingerPermission request = new RequestCheckSingerPermission();
        request.setNjId(1384006604298455682L);
        request.setLiveId(111L);
        request.setUserId(1383861584727676162L);
        request.setAppId(BusinessEvnEnum.HEI_YE.getAppId());

        Result<Boolean> result = singerPermissionService.checkSingerPermission(request);
        log.info("result:{}", JsonUtil.dumps(result));


    }

    @Test
    public void isSinger() {
        RequestUserIsSinger singer = new RequestUserIsSinger();
        singer.setAppId(BusinessEvnEnum.PP.getAppId());
        singer.setUserId(5440243675105885740L);
        Result<Boolean> result = singerInfoService.userIsSinger(singer);
        log.info("isSinger:{}", result.target());
    }

    @Test
    public void getSingerHallCount(){
        RequestSingerTotalCountInHall request = new RequestSingerTotalCountInHall();
        request.setNjId(5426000972988023340L);
        Result<ResponseSingerCountInHall> result = singerInfoService.singerTotalCountInHall(request);
        log.info("result:{}", JsonUtil.dumps(result.target()));
    }

    @Test
    public void getAllSingerStatics(){
        RequestGetAllSingerStatics request = new RequestGetAllSingerStatics();
        request.setAppId(BusinessEvnEnum.PP.getAppId());

        Result<ResponseGetAllSingerStatics> result = singerInfoAdminService.getAllSingerStatics(request);
        log.info("result:{}", JsonUtil.dumps(result.target()));
    }

}
