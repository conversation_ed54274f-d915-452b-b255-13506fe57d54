package fm.lizhi.ocean.wavecenter.test.activitycenter;

import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestUserCancelActivity;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityOperateService;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class ActivityOperateTest extends AbstractDataCenterTest {

    @Autowired
    private ActivityOperateService activityOperateService;


    /**
     * 用户取消活动
     */
    @Test
    public void testUserCancelActivity() {
        RequestUserCancelActivity request = new RequestUserCancelActivity();
        request.setActivityId(5423754309944783487L);
        request.setAppId(10919088);
        request.setOperateUserId(1386653158851527810L);
        request.setReason("测试取消活动");
        //Result<Void> result = activityOperateService.userCancelActivity(request);
        //log.info("result:{}, code:{}, message:{}", result, result.rCode(), result.getMessage());
    }

}
