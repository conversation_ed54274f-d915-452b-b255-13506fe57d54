package fm.lizhi.ocean.wavecenter.test.util;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.utils.BizClusterRouter;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2025/7/2 19:19
 */
public class BizClusterRouterTest {

    /**
     * 4个节点，3个定时任务分片广播
     */
    @Test
    public void test4Node3Job(){
        boolean node1HyJob = new BizClusterRouter(4, 0).route(BusinessEvnEnum.HEI_YE);
        boolean node2HyJob = new BizClusterRouter(4, 1).route(BusinessEvnEnum.HEI_YE);
        boolean node3HyJob = new BizClusterRouter(4, 2).route(BusinessEvnEnum.HEI_YE);
        boolean node4HyJob = new BizClusterRouter(4, 3).route(BusinessEvnEnum.HEI_YE);
        Assert.assertTrue(node1HyJob || node2HyJob || node3HyJob || node4HyJob);
        Assert.assertTrue(node3HyJob);
        Assert.assertFalse(node1HyJob);
        Assert.assertFalse(node2HyJob);
        Assert.assertFalse(node4HyJob);

        boolean node1PpJob = new BizClusterRouter(4, 0).route(BusinessEvnEnum.PP);
        boolean node2PpJob = new BizClusterRouter(4, 1).route(BusinessEvnEnum.PP);
        boolean node3PpJob = new BizClusterRouter(4, 2).route(BusinessEvnEnum.PP);
        boolean node4PpJob = new BizClusterRouter(4, 3).route(BusinessEvnEnum.PP);
        Assert.assertTrue(node1PpJob || node2PpJob || node3PpJob || node4PpJob);
        Assert.assertTrue(node1PpJob);
        Assert.assertFalse(node2PpJob);
        Assert.assertFalse(node3PpJob);
        Assert.assertFalse(node4PpJob);

        boolean node1XmJob = new BizClusterRouter(4, 0).route(BusinessEvnEnum.XIMI);
        boolean node2XmJob = new BizClusterRouter(4, 1).route(BusinessEvnEnum.XIMI);
        boolean node3XmJob = new BizClusterRouter(4, 2).route(BusinessEvnEnum.XIMI);
        boolean node4XmJob = new BizClusterRouter(4, 3).route(BusinessEvnEnum.XIMI);
        Assert.assertTrue(node1XmJob || node2XmJob || node3XmJob || node4XmJob);
        Assert.assertTrue(node2XmJob);
        Assert.assertFalse(node1XmJob);
        Assert.assertFalse(node3PpJob);
        Assert.assertFalse(node4XmJob);
    }

    /**
     * 1个节点，3个定时任务
     */
    @Test
    public void test1Node3Job(){
        boolean node1HyJob = new BizClusterRouter(1, 0).route(BusinessEvnEnum.HEI_YE);
        Assert.assertTrue(node1HyJob);

        boolean node1PpJob = new BizClusterRouter(1, 0).route(BusinessEvnEnum.PP);
        Assert.assertTrue(node1PpJob);

        boolean node1XmJob = new BizClusterRouter(1, 0).route(BusinessEvnEnum.XIMI);
        Assert.assertTrue(node1XmJob);
    }

    /**
     * 2个节点，3个定时任务
     */
    @Test
    public void test2Node3Job(){
        boolean node1HyJob = new BizClusterRouter(2, 0).route(BusinessEvnEnum.HEI_YE);
        boolean node2HyJob = new BizClusterRouter(2, 1).route(BusinessEvnEnum.HEI_YE);
        Assert.assertTrue(node1HyJob || node2HyJob);
        Assert.assertTrue(node1HyJob);
        Assert.assertFalse(node2HyJob);

        boolean node1PpJob = new BizClusterRouter(2, 0).route(BusinessEvnEnum.PP);
        boolean node2PpJob = new BizClusterRouter(2, 1).route(BusinessEvnEnum.PP);
        Assert.assertTrue(node1PpJob || node2PpJob);
        Assert.assertTrue(node1PpJob);
        Assert.assertFalse(node2PpJob);

        boolean node1XmJob = new BizClusterRouter(2, 0).route(BusinessEvnEnum.XIMI);
        boolean node2XmJob = new BizClusterRouter(2, 1).route(BusinessEvnEnum.XIMI);
        Assert.assertTrue(node1XmJob || node2XmJob);
        Assert.assertTrue(node2XmJob);
        Assert.assertFalse(node1XmJob);
    }

    /**
     * 3个节点，2个定时任务
     */
    @Test
    public void test3Node2Job(){
        boolean node1HyJob = new BizClusterRouter(3, 0).route(BusinessEvnEnum.HEI_YE);
        boolean node2HyJob = new BizClusterRouter(3, 1).route(BusinessEvnEnum.HEI_YE);
        boolean node3HyJob = new BizClusterRouter(3, 2).route(BusinessEvnEnum.HEI_YE);
        Assert.assertTrue(node1HyJob || node2HyJob || node3HyJob);
        Assert.assertTrue(node3HyJob);
        Assert.assertFalse(node1HyJob);
        Assert.assertFalse(node2HyJob);

        boolean node1PpJob = new BizClusterRouter(3, 0).route(BusinessEvnEnum.PP);
        boolean node2PpJob = new BizClusterRouter(3, 1).route(BusinessEvnEnum.PP);
        boolean node3PpJob = new BizClusterRouter(3, 2).route(BusinessEvnEnum.PP);
        Assert.assertTrue(node1PpJob || node2PpJob || node3PpJob);
        Assert.assertTrue(node1PpJob);
        Assert.assertFalse(node2PpJob);
        Assert.assertFalse(node3PpJob);
    }

    /**
     * 3个节点，3个定时任务分片广播
     */
    @Test
    public void test3Node3Job(){
        boolean node1HyJob = new BizClusterRouter(3, 0).route(BusinessEvnEnum.HEI_YE);
        boolean node2HyJob = new BizClusterRouter(3, 1).route(BusinessEvnEnum.HEI_YE);
        boolean node3HyJob = new BizClusterRouter(3, 2).route(BusinessEvnEnum.HEI_YE);
        Assert.assertTrue(node1HyJob || node2HyJob || node3HyJob);
        Assert.assertTrue(node3HyJob);
        Assert.assertFalse(node1HyJob);
        Assert.assertFalse(node2HyJob);

        boolean node1PpJob = new BizClusterRouter(3, 0).route(BusinessEvnEnum.PP);
        boolean node2PpJob = new BizClusterRouter(3, 1).route(BusinessEvnEnum.PP);
        boolean node3PpJob = new BizClusterRouter(3, 2).route(BusinessEvnEnum.PP);
        Assert.assertTrue(node1PpJob || node2PpJob || node3PpJob);
        Assert.assertTrue(node1PpJob);
        Assert.assertFalse(node2PpJob);
        Assert.assertFalse(node3PpJob);

        boolean node1XmJob = new BizClusterRouter(3, 0).route(BusinessEvnEnum.XIMI);
        boolean node2XmJob = new BizClusterRouter(3, 1).route(BusinessEvnEnum.XIMI);
        boolean node3XmJob = new BizClusterRouter(3, 2).route(BusinessEvnEnum.XIMI);
        Assert.assertTrue(node1XmJob || node2XmJob || node3XmJob);
        Assert.assertTrue(node2XmJob);
        Assert.assertFalse(node1XmJob);
        Assert.assertFalse(node3PpJob);
    }

}
